<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-api-auth</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>api-auth-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--jwt工具-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>jwt-sdk</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--api授权api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>api-auth-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--system基础数据-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--system业务api-->
        <!--调用用户相关业务-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库sdk-->
        <!--数据库dao框架-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--缓存的依赖-->
        <!--sso的session可以用redis，可以用内存的-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-api</artifactId>
            <version>${roses.version}</version>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- aop -->
        <!-- 用于本模块api认证拦截 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

    </dependencies>

</project>
