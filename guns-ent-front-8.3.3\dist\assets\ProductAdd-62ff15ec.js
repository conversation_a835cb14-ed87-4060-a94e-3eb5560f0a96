import{_ as R,s as T,r as A,X as S,k as V,a as f,f as p,w as l,d as e,g as c,h as _,m as N,l as k,u as O,v as x,G as M,W as L,J as q,y as B,$ as G,H,M as W}from"./index-18a1ea24.js";/* empty css              */import{_ as E}from"./SupplierSelector-e8033f79.js";import{P as F}from"./ProductApi-52d42f8e.js";import j from"./CategorySelector-5d61ae06.js";/* empty css              */import"./SupplierApi-6b9315dd.js";/* empty css              */import"./productCategoryApi-39e417fd.js";const D={name:"ProductAdd",components:{SupplierSelector:E,CategorySelector:j},props:{visible:Boolean},emits:["update:visible","done"],setup(C,{emit:a}){const u=T({pricingType:"NORMAL",status:"ACTIVE",retailPrice:null,unitPrice:null,piecePrice:null,referencePrice:null}),r=A(null),g=A(!1),m=T({productCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5546\u54C1\u7F16\u7801",trigger:"blur"},{max:50,message:"\u5546\u54C1\u7F16\u7801\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26",trigger:"blur"}],productName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",trigger:"blur"},{max:200,message:"\u5546\u54C1\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26",trigger:"blur"}],categoryId:[{required:!0,message:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B",trigger:"change"}],supplierId:[{required:!0,message:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",trigger:"change"}],pricingType:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BA1\u4EF7\u7C7B\u578B",trigger:"change"}]}),s=t=>{a("update:visible",t)},n=(t,v)=>{console.log("\u4F9B\u5E94\u5546\u53D8\u66F4:",t,v)},i=t=>{console.log("\u8BA1\u4EF7\u7C7B\u578B\u53D8\u66F4:",t),u.retailPrice=null,u.unitPrice=null,u.piecePrice=null,u.referencePrice=null,d(t)},d=t=>{switch(delete m.retailPrice,delete m.unitPrice,delete m.piecePrice,delete m.referencePrice,t){case"NORMAL":m.retailPrice=[{required:!0,message:"\u8BF7\u8F93\u5165\u96F6\u552E\u4EF7\u683C",trigger:"blur",type:"number"}];break;case"WEIGHT":m.unitPrice=[{required:!0,message:"\u8BF7\u8F93\u5165\u5355\u4F4D\u4EF7\u683C",trigger:"blur",type:"number"}];break;case"PIECE":m.piecePrice=[{required:!0,message:"\u8BF7\u8F93\u5165\u5355\u4EFD\u4EF7\u683C",trigger:"blur",type:"number"}];break}},P=async()=>{try{await r.value.validate(),g.value=!0;const t={...u};await F.add(t),N.success("\u65B0\u589E\u5546\u54C1\u6210\u529F"),s(!1),a("done")}catch(t){console.error("\u65B0\u589E\u5546\u54C1\u5931\u8D25:",t),N.error(t.message||"\u65B0\u589E\u5931\u8D25")}finally{g.value=!1}},y=()=>{Object.keys(u).forEach(t=>{t==="pricingType"?u[t]="NORMAL":t==="status"?u[t]="ACTIVE":t.includes("Price")?u[t]=null:u[t]=void 0}),r.value&&r.value.resetFields()};return S(()=>C.visible,t=>{t&&(y(),d("NORMAL"))},{immediate:!0}),{form:u,formRef:r,loading:g,rules:m,updateVisible:s,handleSupplierChange:n,handlePricingTypeChange:i,save:P}}};function J(C,a,u,r,g,m){const s=k,n=O,i=x,d=M,P=V("category-selector"),y=E,t=L,v=q,b=B,I=G,U=H,w=W;return f(),p(w,{visible:u.visible,title:"\u65B0\u589E\u5546\u54C1",width:800,"confirm-loading":r.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":r.updateVisible,onOk:r.save},{default:l(()=>[e(U,{ref:"formRef",model:r.form,rules:r.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:l(()=>[e(d,{gutter:16},{default:l(()=>[e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u5546\u54C1\u7F16\u7801",name:"productCode"},{default:l(()=>[e(s,{value:r.form.productCode,"onUpdate:value":a[0]||(a[0]=o=>r.form.productCode=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u5546\u54C1\u540D\u79F0",name:"productName"},{default:l(()=>[e(s,{value:r.form.productName,"onUpdate:value":a[1]||(a[1]=o=>r.form.productName=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(d,{gutter:16},{default:l(()=>[e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u5546\u54C1\u7B80\u79F0",name:"productShortName"},{default:l(()=>[e(s,{value:r.form.productShortName,"onUpdate:value":a[2]||(a[2]=o=>r.form.productShortName=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u7B80\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u5546\u54C1\u5206\u7C7B",name:"categoryId"},{default:l(()=>[e(P,{value:r.form.categoryId,"onUpdate:value":a[3]||(a[3]=o=>r.form.categoryId=o)},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(d,{gutter:16},{default:l(()=>[e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u4F9B\u5E94\u5546",name:"supplierId"},{default:l(()=>[e(y,{value:r.form.supplierId,"onUpdate:value":a[4]||(a[4]=o=>r.form.supplierId=o),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},onChange:r.handleSupplierChange},null,8,["value","onChange"])]),_:1})]),_:1}),e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u8BA1\u4EF7\u7C7B\u578B",name:"pricingType"},{default:l(()=>[e(v,{value:r.form.pricingType,"onUpdate:value":a[5]||(a[5]=o=>r.form.pricingType=o),placeholder:"\u8BF7\u9009\u62E9\u8BA1\u4EF7\u7C7B\u578B",onChange:r.handlePricingTypeChange},{default:l(()=>[e(t,{value:"NORMAL"},{default:l(()=>a[16]||(a[16]=[c("\u666E\u901A\u5546\u54C1")])),_:1,__:[16]}),e(t,{value:"WEIGHT"},{default:l(()=>a[17]||(a[17]=[c("\u8BA1\u91CD\u5546\u54C1")])),_:1,__:[17]}),e(t,{value:"PIECE"},{default:l(()=>a[18]||(a[18]=[c("\u8BA1\u4EF6\u5546\u54C1")])),_:1,__:[18]}),e(t,{value:"VARIABLE"},{default:l(()=>a[19]||(a[19]=[c("\u4E0D\u5B9A\u4EF7\u5546\u54C1")])),_:1,__:[19]})]),_:1},8,["value","onChange"])]),_:1})]),_:1})]),_:1}),r.form.pricingType?(f(),p(d,{key:0,gutter:16},{default:l(()=>[r.form.pricingType==="NORMAL"?(f(),p(i,{key:0,md:12,sm:24},{default:l(()=>[e(n,{label:"\u96F6\u552E\u4EF7\u683C",name:"retailPrice"},{default:l(()=>[e(b,{value:r.form.retailPrice,"onUpdate:value":a[6]||(a[6]=o=>r.form.retailPrice=o),placeholder:"\u8BF7\u8F93\u5165\u96F6\u552E\u4EF7\u683C",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):_("",!0),r.form.pricingType==="WEIGHT"?(f(),p(i,{key:1,md:12,sm:24},{default:l(()=>[e(n,{label:"\u5355\u4F4D\u4EF7\u683C",name:"unitPrice"},{default:l(()=>[e(b,{value:r.form.unitPrice,"onUpdate:value":a[7]||(a[7]=o=>r.form.unitPrice=o),placeholder:"\u8BF7\u8F93\u5165\u5355\u4F4D\u4EF7\u683C(\u6BCF\u516C\u65A4)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):_("",!0),r.form.pricingType==="PIECE"?(f(),p(i,{key:2,md:12,sm:24},{default:l(()=>[e(n,{label:"\u5355\u4EFD\u4EF7\u683C",name:"piecePrice"},{default:l(()=>[e(b,{value:r.form.piecePrice,"onUpdate:value":a[8]||(a[8]=o=>r.form.piecePrice=o),placeholder:"\u8BF7\u8F93\u5165\u5355\u4EFD\u4EF7\u683C",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):_("",!0),r.form.pricingType==="VARIABLE"?(f(),p(i,{key:3,md:12,sm:24},{default:l(()=>[e(n,{label:"\u53C2\u8003\u4EF7\u683C",name:"referencePrice"},{default:l(()=>[e(b,{value:r.form.referencePrice,"onUpdate:value":a[9]||(a[9]=o=>r.form.referencePrice=o),placeholder:"\u8BF7\u8F93\u5165\u53C2\u8003\u4EF7\u683C(\u53EF\u9009)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):_("",!0)]),_:1})):_("",!0),e(d,{gutter:16},{default:l(()=>[e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u89C4\u683C",name:"specification"},{default:l(()=>[e(s,{value:r.form.specification,"onUpdate:value":a[10]||(a[10]=o=>r.form.specification=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u89C4\u683C"},null,8,["value"])]),_:1})]),_:1}),e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u5355\u4F4D",name:"unit"},{default:l(()=>[e(s,{value:r.form.unit,"onUpdate:value":a[11]||(a[11]=o=>r.form.unit=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u5355\u4F4D"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(d,{gutter:16},{default:l(()=>[e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u54C1\u724C",name:"brand"},{default:l(()=>[e(s,{value:r.form.brand,"onUpdate:value":a[12]||(a[12]=o=>r.form.brand=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u54C1\u724C"},null,8,["value"])]),_:1})]),_:1}),e(i,{md:12,sm:24},{default:l(()=>[e(n,{label:"\u72B6\u6001",name:"status"},{default:l(()=>[e(v,{value:r.form.status,"onUpdate:value":a[13]||(a[13]=o=>r.form.status=o),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:l(()=>[e(t,{value:"ACTIVE"},{default:l(()=>a[20]||(a[20]=[c("\u542F\u7528")])),_:1,__:[20]}),e(t,{value:"INACTIVE"},{default:l(()=>a[21]||(a[21]=[c("\u7981\u7528")])),_:1,__:[21]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(d,{gutter:16},{default:l(()=>[e(i,{span:24},{default:l(()=>[e(n,{label:"\u5546\u54C1\u63CF\u8FF0",name:"description","label-col":{span:3},"wrapper-col":{span:21}},{default:l(()=>[e(I,{value:r.form.description,"onUpdate:value":a[14]||(a[14]=o=>r.form.description=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u63CF\u8FF0",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(d,{gutter:16},{default:l(()=>[e(i,{span:24},{default:l(()=>[e(n,{label:"\u5907\u6CE8",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:l(()=>[e(I,{value:r.form.remark,"onUpdate:value":a[15]||(a[15]=o=>r.form.remark=o),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:2},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["visible","confirm-loading","onUpdate:visible","onOk"])}const le=R(D,[["render",J],["__scopeId","data-v-88d1f7f7"]]);export{le as default};
