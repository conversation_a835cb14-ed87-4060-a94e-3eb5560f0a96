import{s as d,a as f,f as c,w as o,d as e,l as g,u as v,v as b,y as w,$ as x,G as y,H as C}from"./index-18a1ea24.js";/* empty css              */const U={__name:"position-form",props:{form:Object},setup(t){const m=d({positionName:[{required:!0,message:"\u8BF7\u8F93\u5165\u804C\u4F4D\u540D\u79F0",type:"string",trigger:"blur"}],positionCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u804C\u4F4D\u7F16\u7801",type:"string",trigger:"blur"}],positionSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u6392\u5E8F",type:"number",trigger:"blur"}]});return(N,n)=>{const u=g,l=v,r=b,s=w,i=x,p=y,_=C;return f(),c(_,{ref:"formRef",model:t.form,rules:m,layout:"vertical"},{default:o(()=>[e(p,{gutter:20},{default:o(()=>[e(r,{xs:24,sm:24,md:12},{default:o(()=>[e(l,{label:"\u804C\u4F4D\u540D\u79F0:",name:"positionName"},{default:o(()=>[e(u,{value:t.form.positionName,"onUpdate:value":n[0]||(n[0]=a=>t.form.positionName=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u804C\u4F4D\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:o(()=>[e(l,{label:"\u804C\u4F4D\u7F16\u7801:",name:"positionCode"},{default:o(()=>[e(u,{value:t.form.positionCode,"onUpdate:value":n[1]||(n[1]=a=>t.form.positionCode=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u804C\u4F4D\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:o(()=>[e(l,{label:"\u6392\u5E8F:",name:"positionSort"},{default:o(()=>[e(s,{value:t.form.positionSort,"onUpdate:value":n[2]||(n[2]=a=>t.form.positionSort=a),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(r,{span:24},{default:o(()=>[e(l,{label:"\u5907\u6CE8"},{default:o(()=>[e(i,{value:t.form.remark,"onUpdate:value":n[3]||(n[3]=a=>t.form.remark=a),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:4},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{U as default};
