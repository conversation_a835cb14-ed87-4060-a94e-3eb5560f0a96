/* 主框架 */
export default {
  home: '主頁',
  header: {
    profile: '個人中心',
    userinfo: '個人信息',
    password: '修改密碼',
    logout: '安全登出'
  },
  footer: {
    website: '官網',
    document: '檔案',
    authorization: '授權',
    copyright: 'Copyright © 2022 javaguns.com'
  },
  logout: {
    title: '詢問',
    message: '確定要登出嗎?'
  },
  setting: {
    title: '整體風格設定',
    sideStyles: {
      dark: '暗色側邊欄',
      light: '亮色側邊欄'
    },
    headStyles: {
      light: '亮色頂欄',
      dark: '暗色頂欄',
      primary: '主色頂欄'
    },
    layoutStyles: {
      side: '左側選單佈局',
      top: '頂部選單佈局',
      mix: '混合選單佈局'
    },
    colors: {
      default: '拂曉藍',
      dust: '薄暮',
      sunset: '日暮',
      volcano: '火山',
      purple: '醬紫',
      cyan: '明青',
      green: '極光綠',
      geekblue: '極客藍'
    },
    darkMode: '開啟暗黑模式',
    layoutStyle: '導航模式',
    sideMenuStyle: '側欄雙排選單',
    bodyFull: '內容區域定寬',
    other: '其它配寘',
    fixedHeader: '固定頂欄區域',
    fixedSidebar: '固定側欄區域',
    fixedBody: '固定主體區域',
    logoAutoSize: 'Logo置於頂欄',
    styleResponsive: '移動端響應式',
    colorfulIcon: '側欄彩色圖標',
    sideUniqueOpen: '側欄排他展開',
    sideInitOpenAll: '默認展開所有側欄',
    weakMode: '開啟色弱模式',
    showFooter: '開啟全域頁腳',
    showTabs: '開啟多頁簽欄',
    tabsConfig: '多頁簽配寘',
    tabStyle: '頁簽顯示風格',
    tabStyles: {
      default: '默認',
      dot: '圓點',
      card: '卡片'
    },
    transitionName: '路由切換動畫',
    transitions: {
      slideRight: '滑動消退',
      slideBottom: '底部消退',
      zoomIn: '放大漸變',
      zoomOut: '縮小漸變',
      fade: '淡入淡出'
    },
    reset: '重置',
    tips: '該功能可實时預覽各種佈局效果，修改後會緩存在本地，下次打開會記憶主題配寘.'
  }
};
