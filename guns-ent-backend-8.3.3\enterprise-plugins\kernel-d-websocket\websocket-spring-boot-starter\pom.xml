<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-websocket</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>websocket-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--WebSocket通信业务模块-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>websocket-sdk-memory</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--WebSocket通信业务模块-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>websocket-sdk-redis</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--缓存接口，根据具体的依赖进行抉择是memory还是redis-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-api</artifactId>
            <version>${roses.version}</version>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
