package cn.stylefeng.roses.kernel.erp.modular.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.InventoryConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.InventoryExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryOperationRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryMapper;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryService;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryHistoryService;
import cn.stylefeng.roses.kernel.erp.modular.product.service.ErpProductService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存管理Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@Slf4j
@Service
public class InventoryServiceImpl extends ServiceImpl<InventoryMapper, Inventory> implements InventoryService {

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private InventoryHistoryService inventoryHistoryService;

    @Resource
    private ErpProductService erpProductService;

    @Resource
    private ErpSupplierService erpSupplierService;

    @Override
    public PageResult<InventoryResponse> findPage(InventoryQueryRequest inventoryQueryRequest) {
        // 使用自定义Mapper查询，支持关联查询和复杂筛选条件
        Page<InventoryResponse> page = new Page<>(
                inventoryQueryRequest.getPageNo() != null ? inventoryQueryRequest.getPageNo() : 1,
                inventoryQueryRequest.getPageSize() != null ? inventoryQueryRequest.getPageSize() : 10
        );

        Page<InventoryResponse> result = inventoryMapper.selectInventoryPage(page, inventoryQueryRequest);

        return PageResultFactory.createPageResult(result.getRecords(), result.getTotal(),
                (int) result.getSize(), (int) result.getCurrent());
    }

    @Override
    public List<InventoryResponse> findList(InventoryQueryRequest inventoryQueryRequest) {
        LambdaQueryWrapper<Inventory> wrapper = createQueryWrapper(inventoryQueryRequest);
        List<Inventory> inventoryList = this.list(wrapper);
        return inventoryList.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public InventoryResponse detail(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Inventory::getProductId, productId);
        Inventory inventory = this.getOne(wrapper);
        
        if (ObjectUtil.isEmpty(inventory)) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_NOT_EXIST);
        }

        return convertToResponse(inventory);
    }

    @Override
    public List<InventoryResponse> findWarningList(InventoryQueryRequest inventoryQueryRequest) {
        LambdaQueryWrapper<Inventory> wrapper = createQueryWrapper(inventoryQueryRequest);
        // 添加预警条件：当前库存 <= 最小库存
        wrapper.apply("current_stock <= min_stock");
        List<Inventory> inventoryList = this.list(wrapper);
        return inventoryList.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public List<InventoryResponse> findOutOfStockList(InventoryQueryRequest inventoryQueryRequest) {
        LambdaQueryWrapper<Inventory> wrapper = createQueryWrapper(inventoryQueryRequest);
        // 添加缺货条件：当前库存 = 0
        wrapper.eq(Inventory::getCurrentStock, BigDecimal.ZERO);
        List<Inventory> inventoryList = this.list(wrapper);
        return inventoryList.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public InventoryValueResponse getInventoryValue(InventoryQueryRequest inventoryQueryRequest) {
        InventoryValueResponse response = new InventoryValueResponse();

        // 使用专门的统计方法，支持复杂筛选条件
        BigDecimal totalValue = inventoryMapper.getTotalInventoryValue(inventoryQueryRequest);
        BigDecimal totalQuantity = inventoryMapper.getTotalInventoryQuantity(inventoryQueryRequest);
        Integer productCount = inventoryMapper.getProductCount(inventoryQueryRequest);
        Integer warningCount = inventoryMapper.getWarningProductCount(inventoryQueryRequest);
        Integer outOfStockCount = inventoryMapper.getOutOfStockProductCount(inventoryQueryRequest);

        response.setTotalInventoryValue(totalValue != null ? totalValue : BigDecimal.ZERO);
        response.setTotalInventoryQuantity(totalQuantity != null ? totalQuantity : BigDecimal.ZERO);
        response.setProductCount(productCount != null ? productCount : 0);
        response.setWarningProductCount(warningCount != null ? warningCount : 0);
        response.setOutOfStockProductCount(outOfStockCount != null ? outOfStockCount : 0);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setMinStock(InventoryRequest inventoryRequest) {
        if (ObjectUtil.isEmpty(inventoryRequest.getProductId())) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        if (ObjectUtil.isEmpty(inventoryRequest.getMinStock()) ||
            inventoryRequest.getMinStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(InventoryExceptionEnum.MIN_STOCK_MUST_GREATER_THAN_OR_EQUAL_ZERO);
        }

        // 验证最大库存（如果提供）
        if (ObjectUtil.isNotEmpty(inventoryRequest.getMaxStock())) {
            if (inventoryRequest.getMaxStock().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(InventoryExceptionEnum.MAX_STOCK_MUST_GREATER_THAN_OR_EQUAL_ZERO);
            }
            if (inventoryRequest.getMaxStock().compareTo(inventoryRequest.getMinStock()) <= 0) {
                throw new ServiceException(InventoryExceptionEnum.MAX_STOCK_MUST_GREATER_THAN_MIN_STOCK);
            }
        }

        LambdaUpdateWrapper<Inventory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Inventory::getProductId, inventoryRequest.getProductId())
               .set(Inventory::getMinStock, inventoryRequest.getMinStock())
               .set(Inventory::getUpdateTime, LocalDateTime.now());

        // 如果提供了最大库存，也一起更新
        if (ObjectUtil.isNotEmpty(inventoryRequest.getMaxStock())) {
            wrapper.set(Inventory::getMaxStock, inventoryRequest.getMaxStock());
        }

        boolean updated = this.update(wrapper);
        if (!updated) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_NOT_EXIST);
        }

        // 记录操作日志
        inventoryHistoryService.recordInventoryHistory(
                inventoryRequest.getProductId(),
                InventoryConstants.OperationType.SET_ALERT,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                null,
                InventoryConstants.ReferenceType.MANUAL,
                "设置库存预警值：最小库存=" + inventoryRequest.getMinStock() +
                (ObjectUtil.isNotEmpty(inventoryRequest.getMaxStock()) ?
                 "，最大库存=" + inventoryRequest.getMaxStock() : ""));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustInventory(InventoryOperationRequest inventoryOperationRequest) {
        validateOperationRequest(inventoryOperationRequest);
        
        // 设置操作类型为调整
        inventoryOperationRequest.setOperationType(InventoryConstants.OperationType.ADJUST);
        inventoryOperationRequest.setReferenceType(InventoryConstants.ReferenceType.ADJUST_ORDER);
        
        operateInventory(inventoryOperationRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initInventory(InventoryRequest inventoryRequest) {
        if (ObjectUtil.isEmpty(inventoryRequest.getProductId())) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        // 检查商品是否存在
        ErpProductRequest productRequest = new ErpProductRequest();
        productRequest.setProductId(inventoryRequest.getProductId());
        ErpProductResponse product = erpProductService.detail(productRequest);
        if (ObjectUtil.isEmpty(product)) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        // 检查是否已存在库存记录
        if (hasInventoryRecord(inventoryRequest.getProductId())) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_INVENTORY_ALREADY_EXIST);
        }

        Inventory inventory = new Inventory();
        BeanUtil.copyProperties(inventoryRequest, inventory);
        
        // 设置默认值
        if (ObjectUtil.isEmpty(inventory.getCurrentStock())) {
            inventory.setCurrentStock(new BigDecimal(InventoryConstants.DefaultValue.DEFAULT_CURRENT_STOCK));
        }
        if (ObjectUtil.isEmpty(inventory.getMinStock())) {
            inventory.setMinStock(new BigDecimal(InventoryConstants.DefaultValue.DEFAULT_MIN_STOCK));
        }
        if (ObjectUtil.isEmpty(inventory.getAvgCost())) {
            inventory.setAvgCost(new BigDecimal(InventoryConstants.DefaultValue.DEFAULT_AVG_COST));
        }
        if (ObjectUtil.isEmpty(inventory.getTotalValue())) {
            inventory.setTotalValue(new BigDecimal(InventoryConstants.DefaultValue.DEFAULT_TOTAL_VALUE));
        }
        

        this.save(inventory);
        
        // 记录库存历史
        if (inventory.getCurrentStock().compareTo(BigDecimal.ZERO) > 0) {
            inventoryHistoryService.recordInventoryHistory(
                inventory.getProductId(),
                InventoryConstants.OperationType.IN,
                inventory.getCurrentStock(),
                BigDecimal.ZERO,
                inventory.getCurrentStock(),
                null,
                InventoryConstants.ReferenceType.ADJUST_ORDER,
                "初始化库存"
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInitInventory(List<InventoryRequest> inventoryRequestList) {
        if (CollectionUtil.isEmpty(inventoryRequestList)) {
            return;
        }

        for (InventoryRequest inventoryRequest : inventoryRequestList) {
            try {
                initInventory(inventoryRequest);
            } catch (Exception e) {
                // 记录错误但继续处理其他商品
                log.error("初始化商品库存失败，商品ID：{}，错误信息：{}", 
                         inventoryRequest.getProductId(), e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateInventory(InventoryOperationRequest inventoryOperationRequest) {
        validateOperationRequest(inventoryOperationRequest);

        Long productId = inventoryOperationRequest.getProductId();
        BigDecimal quantityChange = inventoryOperationRequest.getQuantity();
        String operationType = inventoryOperationRequest.getOperationType();

        // 获取当前库存记录
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Inventory::getProductId, productId);
        Inventory inventory = this.getOne(wrapper);

        if (ObjectUtil.isEmpty(inventory)) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_NOT_EXIST);
        }

        BigDecimal stockBefore = inventory.getCurrentStock();
        BigDecimal stockAfter;

        // 根据操作类型计算新库存
        switch (operationType) {
            case InventoryConstants.OperationType.IN:
            case InventoryConstants.OperationType.ADJUST:
                stockAfter = stockBefore.add(quantityChange);
                break;
            case InventoryConstants.OperationType.OUT:
            case InventoryConstants.OperationType.SALE:
                stockAfter = stockBefore.subtract(quantityChange.abs());
                // 检查库存是否充足
                if (stockAfter.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ServiceException(InventoryExceptionEnum.INSUFFICIENT_INVENTORY);
                }
                break;
            default:
                throw new ServiceException(InventoryExceptionEnum.INVENTORY_OPERATION_TYPE_ERROR);
        }

        // 更新库存
        inventory.setCurrentStock(stockAfter);

        // 如果提供了单价，更新平均成本和总价值
        if (ObjectUtil.isNotEmpty(inventoryOperationRequest.getUnitCost()) &&
            inventoryOperationRequest.getUnitCost().compareTo(BigDecimal.ZERO) > 0) {
            
            // 简单的加权平均成本计算
            if (InventoryConstants.OperationType.IN.equals(operationType)) {
                BigDecimal totalCost = inventory.getAvgCost().multiply(stockBefore)
                    .add(inventoryOperationRequest.getUnitCost().multiply(quantityChange));
                if (stockAfter.compareTo(BigDecimal.ZERO) > 0) {
                    inventory.setAvgCost(totalCost.divide(stockAfter, 2, RoundingMode.HALF_UP));
                }
            }
            
            inventory.setTotalValue(inventory.getAvgCost().multiply(stockAfter));
        }

        this.updateById(inventory);

        // 记录库存历史
        inventoryHistoryService.recordInventoryHistory(
            productId,
            operationType,
            quantityChange,
            stockBefore,
            stockAfter,
            inventoryOperationRequest.getReferenceId(),
            inventoryOperationRequest.getReferenceType(),
            inventoryOperationRequest.getRemark()
        );
    }

    @Override
    public boolean hasInventoryRecord(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            return false;
        }

        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Inventory::getProductId, productId);
        return this.count(wrapper) > 0;
    }

    @Override
    public BigDecimal getCurrentStock(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            return BigDecimal.ZERO;
        }

        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Inventory::getProductId, productId);
        Inventory inventory = this.getOne(wrapper);

        return ObjectUtil.isNotEmpty(inventory) ? inventory.getCurrentStock() : BigDecimal.ZERO;
    }

    @Override
    public boolean checkStockSufficient(Long productId, BigDecimal requiredQuantity) {
        if (ObjectUtil.isEmpty(productId) || ObjectUtil.isEmpty(requiredQuantity)) {
            return false;
        }

        BigDecimal currentStock = getCurrentStock(productId);
        return currentStock.compareTo(requiredQuantity) >= 0;
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<Inventory> createQueryWrapper(InventoryQueryRequest inventoryQueryRequest) {
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getProductId())) {
            wrapper.eq(Inventory::getProductId, inventoryQueryRequest.getProductId());
        }

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getMinCurrentStock())) {
            wrapper.ge(Inventory::getCurrentStock, inventoryQueryRequest.getMinCurrentStock());
        }

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getMaxCurrentStock())) {
            wrapper.le(Inventory::getCurrentStock, inventoryQueryRequest.getMaxCurrentStock());
        }

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getMinTotalValue())) {
            wrapper.ge(Inventory::getTotalValue, inventoryQueryRequest.getMinTotalValue());
        }

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getMaxTotalValue())) {
            wrapper.le(Inventory::getTotalValue, inventoryQueryRequest.getMaxTotalValue());
        }

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getWarningOnly()) && inventoryQueryRequest.getWarningOnly()) {
            wrapper.apply("current_stock <= min_stock");
        }

        if (ObjectUtil.isNotEmpty(inventoryQueryRequest.getOutOfStockOnly()) && inventoryQueryRequest.getOutOfStockOnly()) {
            wrapper.eq(Inventory::getCurrentStock, BigDecimal.ZERO);
        }

        wrapper.orderByDesc(Inventory::getUpdateTime);
        return wrapper;
    }

    /**
     * 验证操作请求参数
     */
    private void validateOperationRequest(InventoryOperationRequest inventoryOperationRequest) {
        if (ObjectUtil.isEmpty(inventoryOperationRequest.getProductId())) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        if (StrUtil.isBlank(inventoryOperationRequest.getOperationType())) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_OPERATION_TYPE_ERROR);
        }

        if (ObjectUtil.isEmpty(inventoryOperationRequest.getQuantity()) ||
            inventoryOperationRequest.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_ADJUST_QUANTITY_CANNOT_BE_ZERO);
        }

        // 检查商品是否存在
        ErpProductRequest productRequest = new ErpProductRequest();
        productRequest.setProductId(inventoryOperationRequest.getProductId());
        ErpProductResponse product = erpProductService.detail(productRequest);
        if (ObjectUtil.isEmpty(product)) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }
    }

    /**
     * 转换为响应对象
     */
    private InventoryResponse convertToResponse(Inventory inventory) {
        InventoryResponse response = new InventoryResponse();
        BeanUtil.copyProperties(inventory, response);

        // 获取商品信息
        if (ObjectUtil.isNotEmpty(inventory.getProductId())) {
            ErpProductRequest productRequest = new ErpProductRequest();
            productRequest.setProductId(inventory.getProductId());
            ErpProductResponse product = erpProductService.detail(productRequest);
            if (ObjectUtil.isNotEmpty(product)) {
                response.setProductCode(product.getProductCode());
                response.setProductName(product.getProductName());
                response.setProductShortName(product.getProductShortName());
                response.setBarcode(product.getBarcode());
                response.setSpecification(product.getSpecification());
                response.setUnit(product.getUnit());
                response.setPricingType(product.getPricingType());
                response.setCategoryId(product.getCategoryId());
                response.setProductStatus(product.getStatus());

                // 获取供应商信息
                if (ObjectUtil.isNotEmpty(product.getSupplierId())) {
                    ErpSupplierRequest supplierRequest = new ErpSupplierRequest();
                    supplierRequest.setSupplierId(product.getSupplierId());
                    ErpSupplierResponse supplier = erpSupplierService.detail(supplierRequest);
                    if (ObjectUtil.isNotEmpty(supplier)) {
                        response.setSupplierId(supplier.getSupplierId());
                        response.setSupplierCode(supplier.getSupplierCode());
                        response.setSupplierName(supplier.getSupplierName());
                        response.setBusinessMode(supplier.getBusinessMode());
                    }
                }
            }
        }

        // 判断是否预警
        response.setIsWarning(inventory.getCurrentStock().compareTo(inventory.getMinStock()) <= 0);

        // 设置库存状态
        if (inventory.getCurrentStock().compareTo(BigDecimal.ZERO) == 0) {
            response.setInventoryStatus(InventoryConstants.Status.OUT_OF_STOCK);
            response.setInventoryStatusName("缺货");
        } else if (inventory.getCurrentStock().compareTo(inventory.getMinStock()) <= 0) {
            response.setInventoryStatus(InventoryConstants.Status.WARNING);
            response.setInventoryStatusName("预警");
        } else {
            response.setInventoryStatus(InventoryConstants.Status.NORMAL);
            response.setInventoryStatusName("正常");
        }

        return response;
    }
}