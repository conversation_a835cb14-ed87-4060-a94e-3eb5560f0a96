<template>
  <div class="guns-body guns-body-card">
    <a-card title="markwodn编辑器-编辑" :bordered="false">
      <v-md-editor
        v-model="dataValue"
        :autofocus="true"
        height="400px"
        width="100%"
        ref="editor"
        :left-toolbar="leftBar"
        :right-toolbar="rightBar"
        :disabled-menus="[]"
        mode="edit"
        @upload-image="handleUploadImage"
      />
    </a-card>
    <a-card title="markwodn编辑器-预览" :bordered="false">
      <v-md-preview :text="dataValue" height="500px" />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { API_BASE_PREFIX } from '@/config/setting';
import { FileApi } from '@/views/system/backend/file/api/FileApi';

const leftBar = ref('undo redo clear h bold italic strikethrough quote ul ol table hr link image code');

const rightBar = ref('preview sync-scroll fullscreen');

const dataValue = ref('');

const filePreviewUrl = ref(`${API_BASE_PREFIX}/sysFileInfo/public/preview?fileId=`);

/**
 * markdown图片上传
 *
 * <AUTHOR>
 * @date 2021/11/05 14:55
 */
const handleUploadImage = async (event, insertImage, files) => {
  // 拿到 files 之后上传到文件服务器，然后向编辑框中插入对应的内容
  const formData = new FormData();
  formData.append('file', files[0]);
  let res = await FileApi.commonUpload('N', formData);
  insertImage({
    url: filePreviewUrl.value + res.data.fileId,
    desc: res.data.fileOriginName
  });
};
</script>

<style></style>
