import{_ as w,br as S,a as g,c as p,b as u,a2 as h,r as y,d as m,w as V,at as j,a0 as $}from"./index-18a1ea24.js";/* empty css              */const E={editor:null,name:"json-editor-vue",internalChange:!1,props:{modelValue:Object|Number|Array,options:Object,currentMode:{type:String,default:"code"},modeList:{type:Array,default:()=>["tree","code","form","text","view"]},language:{type:String,default:"en"}},data(){return{json:this.modelValue,expandedModes:["tree","view","form"],isFullScreen:!1,hasLogo:!0,showFullScreen:!1}},watch:{modelValue:{immediate:!0,deep:!0,handler(e){this.internalChange||(this.setEditorContent(e),this.$nextTick(()=>{this.expandAll()}))}}},mounted(){this.init();const e=document.querySelector(".jsoneditor-menu .jsoneditor-poweredBy");this.hasLogo=e&&window.getComputedStyle(e).display!=="none",this.showFullScreen=!0},unmounted(){var e;(e=this.editor)==null||e.destroy(),this.editor=null},methods:{toggleFullScreen(){this.isFullScreen=!this.isFullScreen,this.$nextTick(()=>{const e=new Event("resize");window.dispatchEvent(e)})},init(){const{currentMode:e,modeList:t,options:a}=this,l=()=>{const o=n=>{this.json=n,this.$emit("update:modelValue",n),this.$emit("change",n),this.internalChange=!0,this.$nextTick(()=>{this.internalChange=!1})};if(!this.editor.getText()){o("");return}try{const n=this.editor.get();o(n)}catch(n){}},s=()=>{this.expandAll()},i=(o,r,n)=>{this.$emit("textSelectionChange",this.editor,o,r,n)},c=(o,r)=>{this.$emit("selectionChange",this.editor,o,r)},f=(o,r,n)=>{this.$emit("colorPicker",this.editor,o,r,n)},_=({target:o})=>{this.$emit("focus",this.editor,o)},x=({target:o})=>{this.$emit("blur",this.editor,o)},v=o=>{this.$emit("validationError",this.editor,o)},C={...a,indentation:2,language:this.language,mode:e,modes:t,onChange:l,onModeChange:s,onTextSelectionChange:i,onSelectionChange:c,onColorPicker:f,onFocus:_,onBlur:x,onValidationError:v};this.editor=new S(this.$refs.jsonEditorVue,C,this.json)},expandAll(){var e,t;this.expandedModes.includes((e=this.editor)==null?void 0:e.getMode())&&((t=this.editor)==null||t.expandAll())},setEditorContent(e){var t;(t=this.editor)==null||t.set(e)}}},b={ref:"jsonEditorVue",class:"json-editor-vue"};function F(e,t,a,l,s,i){return g(),p("div",{class:h(["container",{"full-screen-container":s.isFullScreen}])},[u("div",b,null,512),u("div",{class:h(["full-screen",{show:s.showFullScreen,right:!s.hasLogo}]),onClick:t[0]||(t[0]=(...c)=>i.toggleFullScreen&&i.toggleFullScreen(...c))},null,2)],2)}const d=w(E,[["render",F],["__scopeId","data-v-71f05bd7"]]);d.install=function(t){t.component(d.name,d)};const k={class:"guns-body guns-body-card"},B={__name:"index",setup(e){const t=y("");return(a,l)=>{const s=$;return g(),p("div",k,[m(s,{title:"json\u7F16\u8F91\u5668",bordered:!1},{default:V(()=>[m(j(d),{"mode-list":["tree","code","form","text","view"],style:{height:"400px"},modelValue:t.value,"onUpdate:modelValue":l[0]||(l[0]=i=>t.value=i)},null,8,["modelValue"])]),_:1})])}}};export{B as default};
