<template>
  <div class="guns-body guns-body-card">
    <a-card title="自定义选择-自定义填充数据" :bordered="false">
      <CustomSelectComponent
        v-model:value="selectValue"
        :disabled="disabled"
        :readonly="readonly"
        :dataList="dataList"
        :dataSources="2"
        :multiple="false"
        :placeholder="placeholder"
        style="width: 300px"
      />
    </a-card>
    <a-card title="自定义选择-动态接口（只支持get）" :bordered="false">
      <CustomSelectComponent
        v-model:value="selectValue1"
        :disabled="disabled"
        :readonly="readonly"
        :dataSources="1"
        backendUrl="/my/user/selectList"
        :multiple="false"
        :placeholder="placeholder"
        style="width: 300px"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 选中的值
const selectValue = ref(null);

const selectValue1 = ref(null);
// 是否禁用
const disabled = ref(false);

// 是否只读
const readonly = ref(false);

// 选择提示
const placeholder = ref('请选择');

const dataList = ref([
  {
    id: '1',
    name: '张三'
  },
  {
    id: '2',
    name: '李四'
  }
]);
</script>

<style></style>
