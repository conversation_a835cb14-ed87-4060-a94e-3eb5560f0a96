package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 区域请求参数
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpRegionRequest extends BaseRequest {

    /**
     * 区域ID
     */
    @NotNull(message = "区域ID不能为空", groups = {edit.class, delete.class, detail.class, updateStatus.class})
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空", groups = {add.class, edit.class})
    @ChineseDescription("区域编码")
    private String regionCode;

    /**
     * 区域名称
     */
    @NotBlank(message = "区域名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("区域名称")
    private String regionName;

    /**
     * 父级区域ID
     */
    @ChineseDescription("父级区域ID")
    private Long parentId;

    /**
     * 区域层级（1-国家，2-省，3-市，4-区县，5-商圈）
     */
    @ChineseDescription("区域层级")
    private Integer regionLevel;

    /**
     * 区域路径（用/分隔，如：1/2/3）
     */
    @ChineseDescription("区域路径")
    private String regionPath;

    /**
     * 排序号
     */
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 区域ID列表（批量操作用）
     */
    @NotNull(message = "区域ID列表不能为空", groups = {batchDelete.class})
    @ChineseDescription("区域ID列表")
    private List<Long> regionIdList;

    /**
     * 搜索文本（用于搜索区域名称或编码）
     */
    @ChineseDescription("搜索文本")
    private String searchText;

    /**
     * 是否只查询启用状态的区域
     */
    @ChineseDescription("是否只查询启用状态")
    private Boolean onlyEnabled;

    /**
     * 展开的节点ID列表（用于树形结构展开状态）
     */
    @ChineseDescription("展开的节点ID列表")
    private List<Long> expandedKeys;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：更新状态
     */
    public @interface updateStatus {
    }

    /**
     * 参数校验分组：批量删除
     */
    public @interface batchDelete {
    }

}
