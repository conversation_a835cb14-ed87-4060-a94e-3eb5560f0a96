function h(){var a=new Date,o=a.getFullYear(),e=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,r=a.getDate()<10?"0"+a.getDate():a.getDate();return o+"-"+e+"-"+r}function i(a,o){var e=a.split("-"),r=e[0],g=e[1],p=e[2],D=new Date(r,g,0);D=D.getDate();var n=r,t=parseInt(g)+parseInt(o);t>12&&(n=parseInt(n)+parseInt(parseInt(t)/12==0?1:parseInt(t)/12),t=parseInt(t)%12);var s=p,v=new Date(n,t,0);v=v.getDate(),s>v&&(s=v),t<10&&(t="0"+t);var y=n+"-"+t+"-"+s;return y}export{i as G,h as g};
