package cn.stylefeng.roses.seata.demo.storage.modular.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 库存
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
@TableName("storage_tbl")
@Data
public class StorageTbl {

    /**
     * 库存id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Integer id;

    /**
     * 库存商品编码
     */
    @TableField("commodity_code")
    private String commodityCode;

    /**
     * 库存数量
     */
    @TableField("count")
    private Integer count;

}