System.register(["./index-legacy-ee1db0c7.js","./interface-form-legacy-85db84ba.js"],(function(e,a){"use strict";var t,l,i,s,n,u,o,d,r,c;return{setters:[e=>{t=e.r,l=e.o,i=e.a,s=e.f,n=e.w,u=e.d,o=e.m,d=e.M},e=>{r=e.I,c=e.a}],execute:function(){e("default",{__name:"interface-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const v=e,f=a,m=t(!1),p=t(!1),b=t({}),g=t(null);l((()=>{v.data?(p.value=!0,y()):p.value=!1}));const y=()=>{r.detail({apiClientResourceId:v.data.apiClientResourceId}).then((e=>{b.value=Object.assign({},e),g.value.setResourceData(b.value)}))},h=e=>{f("update:visible",e)},R=async()=>{g.value.$refs.formRef.validate().then((async e=>{if(e){m.value=!0;let e=null;e=p.value?r.edit(b.value):r.add(b.value),e.then((async e=>{m.value=!1,o.success(e.message),h(!1),f("done")})).catch((()=>{m.value=!1}))}}))};return(e,a)=>{const t=d;return i(),s(t,{width:700,maskClosable:!1,visible:v.visible,"confirm-loading":m.value,forceRender:!0,title:p.value?"编辑接口":"新建接口","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":h,onOk:R,onClose:a[1]||(a[1]=e=>h(!1))},{default:n((()=>[u(c,{form:b.value,"onUpdate:form":a[0]||(a[0]=e=>b.value=e),ref_key:"InterfaceRef",ref:g,isUpdate:p.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
