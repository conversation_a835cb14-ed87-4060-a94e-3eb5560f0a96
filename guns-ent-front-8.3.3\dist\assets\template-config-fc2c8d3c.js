import{r as l,o as p,a as m,f as c,w as u,d as b,M as v}from"./index-18a1ea24.js";import g from"./config-data-9ea15c30.js";/* empty css              */import"./ThemeTemplateFieldApi-b2a7ece4.js";const B={__name:"template-config",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(n,{emit:s}){const e=n,r=s,f=l(!1),o=l(null);p(()=>{e.data&&o.value.openConfig(e.data.templateId)});const t=a=>{r("update:visible",a)};return(a,i)=>{const d=v;return m(),c(d,{width:700,maskClosable:!1,visible:e.visible,"confirm-loading":f.value,forceRender:!0,title:"\u6A21\u677F\u914D\u7F6E","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":t,footer:null,onClose:i[0]||(i[0]=_=>t(!1))},{default:u(()=>[b(g,{ref_key:"ConfigRef",ref:o},null,512)]),_:1},8,["visible","confirm-loading"])}}};export{B as default};
