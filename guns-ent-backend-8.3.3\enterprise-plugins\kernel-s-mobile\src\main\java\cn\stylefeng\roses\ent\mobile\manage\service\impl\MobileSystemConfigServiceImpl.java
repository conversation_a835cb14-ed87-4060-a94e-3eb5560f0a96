package cn.stylefeng.roses.ent.mobile.manage.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.ent.mobile.manage.exception.MobileException;
import cn.stylefeng.roses.ent.mobile.manage.exception.enums.MobileExceptionEnum;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.ChangePhoneRequest;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.SendPhoneCodeRequest;
import cn.stylefeng.roses.ent.mobile.manage.prop.MobileSmsProperties;
import cn.stylefeng.roses.ent.mobile.manage.service.MobileSystemConfigService;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.auth.api.exception.AuthException;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.security.api.DragCaptchaApi;
import cn.stylefeng.roses.kernel.sms.api.SmsSenderApi;
import cn.stylefeng.roses.kernel.sms.modular.enums.SmsSendSourceEnum;
import cn.stylefeng.roses.kernel.sms.modular.enums.SmsSendStatusEnum;
import cn.stylefeng.roses.kernel.sms.modular.param.SysSmsSendParam;
import cn.stylefeng.roses.kernel.sms.modular.param.SysSmsVerifyParam;
import cn.stylefeng.roses.kernel.sms.modular.service.SysSmsInfoService;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import cn.stylefeng.roses.kernel.validator.api.exception.enums.ValidatorExceptionEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 移动端系统配置的业务
 *
 * <AUTHOR>
 * @since 2024/3/24 23:03
 */
@Service
public class MobileSystemConfigServiceImpl implements MobileSystemConfigService {

    @Resource
    private DragCaptchaApi dragCaptchaApi;

    @Resource
    private SmsSenderApi smsSenderApi;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysSmsInfoService sysSmsInfoService;

    @Resource
    private MobileSmsProperties mobileSmsProperties;

    @Override
    public void sendChangePhoneCode(SendPhoneCodeRequest sendPhoneCodeRequest) {

        // 1. 先校验验证码是否正确
        this.validateDragCaptcha(sendPhoneCodeRequest);

        // 2. 判断新手机是否有用户注册过
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getPhone, sendPhoneCodeRequest.getPhone());
        long count = sysUserService.count(queryWrapper);
        if (count > 0) {
            throw new MobileException(MobileExceptionEnum.USER_PHONE_HAVE);
        }

        // 3. 生成随机验证码
        String validateCode = RandomUtil.randomNumbers(4);
        HashMap<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put(mobileSmsProperties.getChangePhoneSmsCodeFieldName(), validateCode);

        // 4. 存储短信发送验证码
        SysSmsSendParam sysSmsSendParam = new SysSmsSendParam();
        sysSmsSendParam.setPhone(sendPhoneCodeRequest.getPhone());
        sysSmsSendParam.setSmsSendSourceEnum(SmsSendSourceEnum.APP);
        sysSmsSendParam.setTemplateCode(mobileSmsProperties.getChangePhoneSmsTemplateCode());
        Long smsId = sysSmsInfoService.saveSmsInfo(sysSmsSendParam, validateCode);

        // 5. 发送短信验证码
        smsSenderApi.sendSms(sendPhoneCodeRequest.getPhone(), mobileSmsProperties.getChangePhoneSmsTemplateCode(), paramMap);

        // 6. 更新发送状态
        sysSmsInfoService.updateSmsInfo(smsId, SmsSendStatusEnum.SUCCESS);
    }

    @Override
    public void ensureChangePhone(ChangePhoneRequest changePhoneRequest) {

        // 1. 获取当前用户id
        LoginUser loginUser = LoginContext.me().getLoginUser();
        Long userId = loginUser.getUserId();

        // 2. 验证短信验证码
        SysSmsVerifyParam sysSmsVerifyParam = new SysSmsVerifyParam();
        sysSmsVerifyParam.setPhone(changePhoneRequest.getPhone());
        sysSmsVerifyParam.setCode(changePhoneRequest.getPhoneValidateCode());
        sysSmsVerifyParam.setSmsSendSourceEnum(SmsSendSourceEnum.APP);
        sysSmsVerifyParam.setTemplateCode(mobileSmsProperties.getChangePhoneSmsTemplateCode());
        sysSmsInfoService.validateSmsInfo(sysSmsVerifyParam);

        // 3. 更新用户的手机号
        LambdaUpdateWrapper<SysUser> sysUserLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysUserLambdaUpdateWrapper.set(SysUser::getPhone, changePhoneRequest.getPhone());
        sysUserLambdaUpdateWrapper.eq(SysUser::getUserId, userId);
        this.sysUserService.update(sysUserLambdaUpdateWrapper);
    }

    /**
     * 校验验证码是否正确
     *
     * <AUTHOR>
     * @since 2024/3/24 23:11
     */
    @Override
    public void validateDragCaptcha(SendPhoneCodeRequest changePhoneRequest) {
        String verKey = changePhoneRequest.getVerKey();
        String verXLocationValue = changePhoneRequest.getVerCode();

        if (StrUtil.isEmpty(verKey) || StrUtil.isEmpty(verXLocationValue)) {
            throw new AuthException(ValidatorExceptionEnum.CAPTCHA_EMPTY);
        }
        if (!dragCaptchaApi.validateCaptcha(verKey, Convert.toInt(verXLocationValue))) {
            throw new AuthException(ValidatorExceptionEnum.DRAG_CAPTCHA_ERROR);
        }
    }

}
