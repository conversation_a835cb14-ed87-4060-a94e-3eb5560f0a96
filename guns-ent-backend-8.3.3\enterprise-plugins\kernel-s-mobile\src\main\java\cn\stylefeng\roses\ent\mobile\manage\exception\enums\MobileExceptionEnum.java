package cn.stylefeng.roses.ent.mobile.manage.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 移动端异常
 *
 * <AUTHOR>
 * @since 2024/3/24 23:43
 */
@Getter
public enum MobileExceptionEnum implements AbstractExceptionEnum {

    /**
     * 用户手机号已存在，无法更换
     */
    USER_PHONE_HAVE(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "0001", "用户手机号已存在，无法更换"),

    /**
     * 用户手机号已存在，无法申请加入
     */
    USER_PHONE_CANT_APPLY(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "0002", "用户手机号已存在，无法申请加入");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    MobileExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
