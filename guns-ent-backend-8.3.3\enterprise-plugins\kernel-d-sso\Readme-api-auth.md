# API认证

## 1. 适用场景

第三方系统需要调用Guns系统中的接口获取数据或发送数据

Guns系统提供一套接口的认证机制给第三方系统

第三方系统在调用本系统的时候需要传递相关的Token参数才可以正常进行调用。

## 2. API应用创建和API授权

### 2.1 秘钥开通

在后台管理系统，创建一个API应用

![](.Readme-api-auth_images/9df7519b.png)

点击新增，输入API应用名称，应用编号，token过期时间，进行创建API秘钥

![](.Readme-api-auth_images/7e1a45c9.png)

### 2.2 API授权

给第三方应用进行接口权限分配，在右侧接口列表中选中接口，即可给API应用授权

![](.Readme-api-auth_images/de30208e.png)

## 3. API应用请求过程

Dev环境服务器后台地址为：http://192.168.31.191:8081/

### 3.1 token携带位置

请求后端接口时，每次请求都要带上token标识，token标识所在的位置为Http Header中带上Authorization属性，如下所示：

![](.Readme-api-auth_images/3106f1dc.png)

### 3.2 token生成规则

#### 3.2.1 组装参数

组装参数的过程，需要用到API后台的相关数据

```java
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

import java.util.Date;

public class TestTokenGenerate {

    public static void main(String[] args) {

        // 公钥（API应用后台管理界面生成）
        String publicSecret = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDatI4d7mKvqzbKU4hveGoddEsb6M0697IuRlV9c2D01cHPvWMKHRmJcEZZv0g9hnPxi23E6LKnOoZdrBVkAHMDz3fTsT3R6nA68jmV1zPZPt3VRVfPzr2o6g16sBzDZJkUwyFxcoHul6YyAincd2NFWNGkt6aRC6+ilLH3t/aO2wIDAQAB";

        // API应用秘钥（API应用后台管理界面生成）
        String appSecret = "2ze2o13F7JkuEwySXRbb4AZhJb7S4zXY";

        // 获取当前时间
        String dateTime = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        System.out.println("当前时间为：" + dateTime);

        // 将app秘钥和时间拼接并进行md5加密
        String md5 = SecureUtil.md5(appSecret + dateTime);

        // 生成加密字符串，用来进行生成token
        RSA rsa = new RSA(null, publicSecret);
        String base64 = rsa.encryptBase64(md5, KeyType.PublicKey);
        System.out.println("生成的秘钥参数如下：" + base64);
    }

}
```

main方法执行后打印出日志如下：

```md
当前时间为：20230406104636
生成的秘钥参数如下：U9yaFuJh6wXd6AUvL+fSLYORyUxYG1T+mBysxkqAieTJCFyynB/4dRYPNJhCxg11jN3UFtgOYPs2B2LWNdUbxDJhPlRiwjl0tE7Q7NmfSdpJomCFxSmCDSlsDiPOCIQ2ESXH1uEhVjCB31ZsiBuJ36GpJ+kfLZyHMWTwLF87bWE=
```

#### 3.2.2 生成Token

生成token需要调用Guns后台接口，接口地址为：`/apiAuth/login`

将第1步生成的时间和秘钥参数作为请求参数，进行接口调用，接口调用过程如下：

![](.Readme-api-auth_images/2cd3c660.png)

得到token之后，即可用token去请求其他Guns中的接口了。

### 3.3 接口请求示例

将生成的token传入到需要请求的接口的Header中，具体格式如下：

![](.Readme-api-auth_images/f02ead7a.png)