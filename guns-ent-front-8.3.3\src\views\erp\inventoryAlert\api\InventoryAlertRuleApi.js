import Request from '@/utils/request/request-util';

/**
 * 库存预警规则管理API
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
export class InventoryAlertRuleApi {
  
  /**
   * 新增预警规则
   */
  static add(params) {
    return Request.post('/erp/inventoryAlert/rule/add', params);
  }

  /**
   * 编辑预警规则
   */
  static edit(params) {
    return Request.post('/erp/inventoryAlert/rule/edit', params);
  }

  /**
   * 删除预警规则
   */
  static delete(params) {
    return Request.post('/erp/inventoryAlert/rule/delete', params);
  }

  /**
   * 批量删除预警规则
   */
  static batchDelete(params) {
    return Request.post('/erp/inventoryAlert/rule/batchDelete', params);
  }

  /**
   * 查询预警规则详情
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/rule/detail', params);
  }

  /**
   * 分页查询预警规则
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/rule/page', params);
  }

  /**
   * 查询预警规则列表（不分页）
   */
  static findList(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/rule/list', params);
  }

  /**
   * 更新预警规则状态
   */
  static updateStatus(params) {
    return Request.post('/erp/inventoryAlert/rule/updateStatus', params);
  }

  /**
   * 测试预警规则
   */
  static testRule(params) {
    return Request.post('/erp/inventoryAlert/rule/test', params);
  }

  /**
   * 手动执行预警检查
   */
  static executeCheck() {
    return Request.post('/erp/inventoryAlert/rule/executeCheck');
  }
}
