package cn.stylefeng.roses.kernel.sync.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.sync.pojo.OrganizationSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织机构信息创建工厂
 *
 * <AUTHOR>
 * @since 2023/10/30 10:47
 */
public class SyncOrganizationFactory {

    /**
     * 创建组织机构信息的同步数据
     *
     * <AUTHOR>
     * @since 2023/10/30 10:48
     */
    public static List<OrganizationSyncVo> createOrganizationVo(List<HrOrganization> originList) {

        ArrayList<OrganizationSyncVo> organizationSyncVos = new ArrayList<>();

        if (ObjectUtil.isEmpty(originList)) {
            return organizationSyncVos;
        }

        for (HrOrganization hrOrganization : originList) {
            OrganizationSyncVo organizationSyncVo = new OrganizationSyncVo();
            BeanUtil.copyProperties(hrOrganization, organizationSyncVo, CopyOptions.create().ignoreError());

            if (hrOrganization.getOrgId() != null) {
                organizationSyncVo.setOrgId(String.valueOf(hrOrganization.getOrgId()));
            }

            if (hrOrganization.getOrgParentId() != null) {
                organizationSyncVo.setOrgParentId(String.valueOf(hrOrganization.getOrgParentId()));
            }

            organizationSyncVos.add(organizationSyncVo);
        }

        return organizationSyncVos;
    }

}
