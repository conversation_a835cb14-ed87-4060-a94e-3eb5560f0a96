package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商请求参数
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpSupplierRequest extends BaseRequest {

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空", groups = {edit.class, delete.class, detail.class, updateStatus.class})
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @NotBlank(message = "供应商编码不能为空", groups = {add.class, edit.class})
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 供应商简称
     */
    @ChineseDescription("供应商简称")
    private String supplierShortName;

    /**
     * 供应商类型（ENTERPRISE-企业，INDIVIDUAL-个体）
     */
    @NotBlank(message = "供应商类型不能为空", groups = {add.class, edit.class})
    @ChineseDescription("供应商类型")
    private String supplierType;

    /**
     * 所属区域ID
     */
    @ChineseDescription("所属区域ID")
    private Long regionId;

    /**
     * 联系人
     */
    @ChineseDescription("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @ChineseDescription("联系电话")
    private String contactPhone;

    /**
     * 手机号码
     */
    @ChineseDescription("手机号码")
    private String contactMobile;

    /**
     * 邮箱地址
     */
    @ChineseDescription("邮箱地址")
    private String contactEmail;

    /**
     * 联系地址
     */
    @ChineseDescription("联系地址")
    private String contactAddress;

    /**
     * 营业执照号
     */
    @ChineseDescription("营业执照号")
    private String businessLicenseNo;

    /**
     * 税务登记号
     */
    @ChineseDescription("税务登记号")
    private String taxNo;

    /**
     * 开户银行
     */
    @ChineseDescription("开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @ChineseDescription("银行账号")
    private String bankAccount;

    /**
     * 信用等级（A-优秀，B-良好，C-一般，D-较差）
     */
    @ChineseDescription("信用等级")
    private String creditLevel;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，BLACKLIST-黑名单）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 经营方式：PURCHASE_SALE(购销)、JOINT_VENTURE(联营)、CONSIGNMENT(代销)
     */
    @NotBlank(message = "经营方式不能为空", groups = {add.class, edit.class})
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 销售扣点（联营和代销使用）
     */
    @ChineseDescription("销售扣点")
    private java.math.BigDecimal salesDeduction;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 供应商ID列表（批量操作用）
     */
    @ChineseDescription("供应商ID列表")
    private List<Long> supplierIdList;

    /**
     * 关联的区域ID列表（多选区域）
     */
    @ChineseDescription("关联的区域ID列表")
    private List<Long> regionIds;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：更新状态
     */
    public @interface updateStatus {
    }

}
