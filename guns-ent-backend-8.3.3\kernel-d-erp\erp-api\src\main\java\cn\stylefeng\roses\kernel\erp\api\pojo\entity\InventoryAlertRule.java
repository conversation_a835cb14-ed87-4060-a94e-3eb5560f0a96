package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存预警规则实体类
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@TableName("erp_inventory_alert_rule")
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertRule extends BaseEntity {

    /**
     * 预警规则ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)
     */
    @TableField("rule_type")
    private String ruleType;

    /**
     * 目标类型：PRODUCT(单个商品)、CATEGORY(商品分类)、ALL(全部商品)
     */
    @TableField("target_type")
    private String targetType;

    /**
     * 目标ID（商品ID或分类ID，ALL类型时为NULL）
     */
    @TableField("target_id")
    private Long targetId;

    /**
     * 预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)
     */
    @TableField("alert_level")
    private String alertLevel;

    /**
     * 阈值类型：QUANTITY(数量)、PERCENTAGE(百分比)、DAYS(天数)
     */
    @TableField("threshold_type")
    private String thresholdType;

    /**
     * 阈值（根据阈值类型确定含义）
     */
    @TableField("threshold_value")
    private BigDecimal thresholdValue;

    /**
     * 比较操作符：LTE(小于等于)、LT(小于)、GTE(大于等于)、GT(大于)、EQ(等于)
     */
    @TableField("comparison_operator")
    private String comparisonOperator;

    /**
     * 是否启用（Y-启用，N-停用）
     */
    @TableField("is_enabled")
    private String isEnabled;

    /**
     * 通知方式：SYSTEM(系统通知)、EMAIL(邮件)、SMS(短信)、WECHAT(微信)，多个用逗号分隔
     */
    @TableField("notification_methods")
    private String notificationMethods;

    /**
     * 通知用户ID列表，用逗号分隔
     */
    @TableField("notification_users")
    private String notificationUsers;

    /**
     * 检查频率（分钟）
     */
    @TableField("check_frequency")
    private Integer checkFrequency;

    /**
     * 最后检查时间
     */
    @TableField("last_check_time")
    private Date lastCheckTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标记（Y-已删除，N-未删除）
     */
    @TableField("del_flag")
    private String delFlag;
}
