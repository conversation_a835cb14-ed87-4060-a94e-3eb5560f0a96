import{r as l,o as _,cb as I,a as T,f as S,w as h,d as x,m as B,M as k}from"./index-18a1ea24.js";import w from"./dict-form-fb9339f4.js";import{S as o}from"./SysDictApi-f53c1202.js";/* empty css              */const D={__name:"dict-add-edit",props:{visible:Boolean,data:Object,dictTypeId:String,dictTypeName:String},emits:["update:visible","done"],setup(m,{emit:v}){const a=m,r=v,d=l(!1),i=l(!1),s=l({statusFlag:1,dictParentId:"-1",dictTypeId:a.dictTypeId,dictTypeName:a.dictTypeName}),u=l(null),f=l([]);_(async()=>{y(),a.data?(i.value=!0,g()):(s.value.dictSort=await I("SYSTEM_BASE_DICT"),i.value=!1)});const y=()=>{o.tree({dictTypeId:a.dictTypeId}).then(e=>{f.value=[{dictId:"-1",dictName:"\u6839\u8282\u70B9",children:e}]})},g=()=>{o.detail({dictId:a.data.dictId}).then(e=>{s.value=Object.assign({},e)})},n=e=>{r("update:visible",e)},b=async()=>{u.value.$refs.formRef.validate().then(async e=>{if(e){d.value=!0;let t=null;i.value?t=o.edit(s.value):t=o.add(s.value),t.then(async c=>{d.value=!1,B.success(c.message),n(!1),r("done")}).catch(()=>{d.value=!1})}})};return(e,t)=>{const c=k;return T(),S(c,{width:800,maskClosable:!1,visible:a.visible,"confirm-loading":d.value,forceRender:!0,style:{top:"40px"},title:i.value?"\u7F16\u8F91\u5B57\u5178":"\u65B0\u5EFA\u5B57\u5178","body-style":{paddingBottom:"8px",height:"600px",overflowY:"auto"},"onUpdate:visible":n,onOk:b,onClose:t[1]||(t[1]=p=>n(!1))},{default:h(()=>[x(w,{form:s.value,"onUpdate:form":t[0]||(t[0]=p=>s.value=p),ref_key:"dictFormRef",ref:u,isUpdate:i.value,dictList:f.value},null,8,["form","isUpdate","dictList"])]),_:1},8,["visible","confirm-loading","title"])}}};export{D as default};
