# 报表集成

报表组件使用积木报表

## 报表集成步骤

* 1. 启动类加上包扫描`org.jeecg.modules.jmreport`，例如如下：
    
```java
@SpringBootApplication(scanBasePackages = {"cn.stylefeng", "org.jeecg.modules.jmreport"}, exclude = {FlywayAutoConfiguration.class})
```

* 2. `application.yml`中加入配置如下：
    
```yml
# minidao配置，用在报表
minidao:
  base-package: org.jeecg.modules.jmreport.desreport.dao*
```
    
* 3. pom.xml中加入报表的依赖
    
```xml
<!--报表集成-->
<dependency>
    <groupId>com.javaguns.ent</groupId>
    <artifactId>report-spring-boot-starter</artifactId>
    <version>${roses.kernel.version}</version>
</dependency>
```

* 4. 启动项目即可看到报表管理菜单

## 静态资源路径访问不到的问题

```yml
#demo自定义路径名称
jeecg:  
  jmreport:
    customPrePath: /api
```