package cn.stylefeng.roses.ent.saas.modular.reg.service;

import cn.stylefeng.roses.ent.saas.modular.reg.pojo.TenantRegRequest;

/**
 * 租户注册业务
 *
 * <AUTHOR>
 * @since 2024-02-22 17:39
 */
public interface TenantRegService {

    /**
     * 发送验证码邮箱
     *
     * <AUTHOR>
     * @since 2024-02-22 18:43
     */
    void sendVerifyCodeEmail(String verKey, String verCode, String email);

    /**
     * 提交租户注册的申请
     *
     * <AUTHOR>
     * @since 2024-02-22 17:43
     */
    void submitTenantReg(TenantRegRequest tenantRegRequest);

}