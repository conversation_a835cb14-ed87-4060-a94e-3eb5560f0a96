package cn.stylefeng.roses.kernel.sync.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.sync.pojo.UserOrgSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户和组织机构关联的创建工厂
 *
 * <AUTHOR>
 * @since 2023/10/30 14:01
 */
public class SyncUserOrgFactory {

    /**
     * 用户和组织机构关联的信息创建
     *
     * <AUTHOR>
     * @since 2023/10/30 14:01
     */
    public static List<UserOrgSyncVo> createUserOrgVo(List<SysUserOrg> sysUserOrgList) {

        List<UserOrgSyncVo> userOrgSyncVos = new ArrayList<>();

        if (ObjectUtil.isEmpty(sysUserOrgList)) {
            return userOrgSyncVos;
        }

        for (SysUserOrg sysUserOrg : sysUserOrgList) {
            UserOrgSyncVo userOrgSyncVo = new UserOrgSyncVo();

            if (ObjectUtil.isNotEmpty(sysUserOrg.getUserOrgId())) {
                userOrgSyncVo.setUserOrgId(String.valueOf(sysUserOrg.getUserOrgId()));
            }

            if (sysUserOrg.getUserId() != null) {
                userOrgSyncVo.setUserId(String.valueOf(sysUserOrg.getUserId()));
            }

            if (sysUserOrg.getOrgId() != null) {
                userOrgSyncVo.setOrgId(String.valueOf(sysUserOrg.getOrgId()));
            }

            if (sysUserOrg.getPositionId() != null) {
                userOrgSyncVo.setPositionId(String.valueOf(sysUserOrg.getPositionId()));
            }

            if (sysUserOrg.getMainFlag() != null) {
                userOrgSyncVo.setMainFlag(sysUserOrg.getMainFlag());
            }

            userOrgSyncVos.add(userOrgSyncVo);
        }

        return userOrgSyncVos;
    }

}
