<template>
  <universal-tree
    ref="universalTreeRef"
    :data-source="dataSourceConfig"
    :field-mapping="fieldMappingConfig"
    :display-config="displayConfig"
    :interaction-config="interactionConfig"
    :action-config="actionConfig"
    @select="handleSelect"
    @expand="handleExpand"
    @search="handleSearch"
    @load="handleLoad"
    @loadError="handleLoadError"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import UniversalTree from '@/components/UniversalTree/UniversalTree.vue'
import { RegionApi } from '@/views/erp/region/api/regionApi'

// 定义组件名称
defineOptions({
  name: 'RegionTree'
})

// 定义Props
const props = defineProps({
  showBadge: {
    type: Boolean,
    default: false
  },
  isShowEditIcon: {
    type: Boolean,
    default: false
  },
  isSetWidth: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['tree-select', 'tree-data-loaded'])

// 组件引用
const universalTreeRef = ref()

// 数据源配置
const dataSourceConfig = {
  api: RegionApi.findTree,
  lazyLoadApi: RegionApi.findTreeWithLazy,
  searchParam: 'searchText',
  parentIdParam: 'parentId'
}

// 字段映射配置
const fieldMappingConfig = {
  key: 'regionId',
  title: 'regionName',
  children: 'children',
  hasChildren: 'hasChildren',
  level: 'regionLevel'
}

// 显示配置（只读模式，用于客户管理页面）
const displayConfig = computed(() => ({
  title: '区域筛选',
  showHeader: props.isSetWidth,
  showSearch: true,
  searchPlaceholder: '请输入区域名称搜索',
  showAddButton: false,
  showEditIcons: false,
  showIcon: false,
  isSetWidth: props.isSetWidth
}))

// 交互配置
const interactionConfig = {
  selectable: true,
  expandable: true,
  lazyLoad: true,
  defaultExpandLevel: 3,
  allowMultiSelect: false
}

// 操作配置（只读模式）
const actionConfig = {
  allowAdd: false,
  allowEdit: false,
  allowDelete: false
}

// 事件处理方法
const handleSelect = (selectInfo) => {
  const { keys, nodes } = selectInfo
  emit('tree-select', keys, { selectedNodes: nodes })
}

const handleExpand = (expandedKeys) => {
  // 可以在这里处理展开事件，如果需要的话
}

const handleSearch = (searchText) => {
  // 搜索事件已经在UniversalTree内部处理
}

const handleLoad = (data) => {
  emit('tree-data-loaded', data)
}

const handleLoadError = (error) => {
  console.error('区域树数据加载失败:', error)
}

// 暴露给父组件的方法
const reload = () => {
  universalTreeRef.value?.reload()
}

const getSelectedNodes = () => {
  return universalTreeRef.value?.getSelectedNodes()
}

const setSelectedKeys = (keys) => {
  universalTreeRef.value?.setSelectedKeys(keys)
}

// 为了向后兼容，保留原有的属性名
const selectedKeys = computed(() => universalTreeRef.value?.getSelectedNodes() || [])

// 暴露方法给父组件
defineExpose({
  reload,
  getSelectedNodes,
  setSelectedKeys,
  selectedKeys
})
</script>

<style scoped lang="less">
// 样式已经在UniversalTree组件中统一处理
</style>