package cn.stylefeng.roses.ent.mobile.manage.controller;

import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserItem;
import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserRequest;
import cn.stylefeng.roses.ent.mobile.manage.service.CommonSelectBusinessService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 移动端通用选择结构和人员的接口
 *
 * <AUTHOR>
 * @since 2024-04-02 13:49
 */
@RestController
@ApiResource(name = "移动端通用选择结构和人员的接口")
public class CommonSelectBusinessController {

    @Resource
    private CommonSelectBusinessService commonSelectBusinessService;

    /**
     * 通用获取机构和人员列表
     *
     * <AUTHOR>
     * @since 2024-04-02 14:51
     */
    @GetResource(name = "通用获取机构和人员列表", path = "/common/getOrgUserItemList")
    public ResponseData<List<OrgUserItem>> getOrgUserItemList(@Validated OrgUserRequest orgUserRequest) {
        List<OrgUserItem> orgUserItems = commonSelectBusinessService.commonGetOrgUserList(orgUserRequest);
        return new SuccessResponseData<>(orgUserItems);
    }

}
