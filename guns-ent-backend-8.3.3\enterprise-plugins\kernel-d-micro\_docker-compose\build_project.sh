#!/bin/bash

# 这里设置整个项目所在服务器的路径
basePath="/home/<USER>"

# 构建maven  jar包
docker run -it --rm --name guns-cloud-maven -v $basePath:/apps -v $basePath/_docker-compose/maven_settings.xml:/maven_settings.xml -w /apps maven:3.6-jdk-8 mvn -B -s /maven_settings.xml package -Dmaven.test.skip -P docker

# 拷贝jar到各个dockerfile的路径，这里需要手动将system和gateway打包，放到basePath目录中
cp $basePath/sys-app.jar $basePath/_docker-compose/3.guns-cloud-system/
cp $basePath/gateway.jar $basePath/_docker-compose/4.guns-cloud-gateway/

# wait脚本变为可执行
/bin/chmod +x ./wait-for-it.sh

# 启动项目
docker-compose up -d
