package cn.stylefeng.roses.kernel.impexp.user.controller;

import cn.stylefeng.roses.kernel.impexp.user.pojo.UserExportRequest;
import cn.stylefeng.roses.kernel.impexp.user.service.UserExportService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 导出用户的控制器
 *
 * <AUTHOR>
 * @since 2024/2/13 21:35
 */
@RestController
@ApiResource(name = "导出用户的控制器")
@Slf4j
public class UserExportController {

    @Resource
    private UserExportService userExportService;

    /**
     * 用户导出业务
     *
     * <AUTHOR>
     * @since 2024/2/13 21:37
     */
    @GetResource(name = "用户导出业务", path = "/user/ExportUser")
    public ResponseData<?> ExportUser(UserExportRequest userExportRequest) {
        userExportService.exportUser(userExportRequest);
        return new SuccessResponseData<>();
    }

}
