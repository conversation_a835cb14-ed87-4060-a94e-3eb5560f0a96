package cn.stylefeng.roses.kernel.websocket.redis;


import cn.stylefeng.roses.kernel.websocket.api.constants.WebSocketActionConstants;
import cn.stylefeng.roses.kernel.websocket.api.constants.WebSocketEventConstants;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketTransferDTO;
import cn.stylefeng.roses.kernel.websocket.memory.MemoryWebSocketManager;
import cn.stylefeng.roses.kernel.websocket.redis.action.Action;
import com.alibaba.fastjson.JSON;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * Redis的websocket管理器
 *
 * <AUTHOR>
 * @since 2024-01-15 15:22
 */
public class RedisWebSocketManager extends MemoryWebSocketManager {

    /**
     * Redis订阅通道的名称
     */
    public static final String CHANNEL = "websocket";

    /**
     * Redis缓存操作
     */
    protected RedisTemplate<String, Serializable> redisTemplate;

    public RedisWebSocketManager(RedisTemplate<String, Serializable> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void put(String identifier, WebSocketDTO webSocket) {
        super.put(identifier, webSocket);
    }

    @Override
    public void remove(String identifier) {
        super.remove(identifier);
    }

    @Override
    public void sendMessage(String userId, String message) {
        this.sendMessageObject(userId, message, null);
    }

    @Override
    public void sendMessageObject(String userId, String message, Object messageObject) {
        Map<String, Object> map = new HashMap<>(3);

        // 构造发送消息的对象
        map.put(Action.ACTION, WebSocketActionConstants.SEND_MESSAGE_ACTION_BEAN_NAME);

        // 填充用户账号
        map.put(Action.USER_ID, userId);

        // 填充具体消息内容
        WebSocketTransferDTO responseData = new WebSocketTransferDTO(WebSocketEventConstants.EVENT_TYPE_MESSAGE, message, messageObject);
        map.put(Action.MESSAGE, responseData);

        // 发布消息到redis频道上 redis转发到订阅的各个socket实例上 收到信息 根据标识 获取到session 发给自己对应的客户端
        redisTemplate.convertAndSend(getChannel(), JSON.toJSONString(map));
    }

    protected String getChannel() {
        return CHANNEL;
    }

}
