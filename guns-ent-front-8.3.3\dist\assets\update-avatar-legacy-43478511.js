System.register(["./index-legacy-ee1db0c7.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js"],(function(t,e){"use strict";var i,a,o,r,n,s,h,p,c,l,d,u,m,g,f,v,b,w,y,x,C,M,k,D,B,A,W,N,O,R,H,T,S,z,E,L,X,j,Y;return{setters:[t=>{i=t.r,a=t.a,o=t.c,r=t.b,n=t.ah,s=t.F,h=t.h,p=t.L,c=t.k,l=t.e,d=t.f,u=t.w,m=t.d,g=t.t,f=t.B,v=t.bk,b=t.bN,w=t.bO,y=t.at,x=t.X,C=t.aK,M=t.o,k=t.bs,D=t.a2,B=t.aH,A=t.M,W=t._,N=t.b3,O=t.s,R=t.bh,H=t.bi,T=t.g,S=t.bJ,z=t.m,E=t.v,L=t.bK,X=t.G},null,t=>{j=t.F,Y=t.a}],execute:function(){var e=document.createElement("style");
/*!
       * Cropper.js v1.6.2
       * https://fengyuanchen.github.io/cropperjs
       *
       * Copyright 2015-present Chen Fengyuan
       * Released under the MIT license
       *
       * Date: 2024-04-21T07:43:05.335Z
       */
function _(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function U(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?_(Object(i),!0).forEach((function(e){var a,o,r;a=t,o=e,r=i[e],(o=I(o))in a?Object.defineProperty(a,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):a[o]=r})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):_(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function I(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function P(t){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function F(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,I(a.key),a)}}function q(t){return function(t){if(Array.isArray(t))return $(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return $(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?$(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}e.textContent='/*!\n * Cropper.js v1.6.2\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present Chen Fengyuan\n * Released under the MIT license\n *\n * Date: 2024-04-21T07:43:02.731Z\n */.cropper-container{direction:ltr;font-size:0;line-height:0;position:relative;-ms-touch-action:none;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.cropper-container img{backface-visibility:hidden;display:block;height:100%;image-orientation:0deg;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}.cropper-wrap-box,.cropper-canvas,.cropper-drag-box,.cropper-crop-box,.cropper-modal{bottom:0;left:0;position:absolute;right:0;top:0}.cropper-wrap-box,.cropper-canvas{overflow:hidden}.cropper-drag-box{background-color:#fff;opacity:0}.cropper-modal{background-color:#000;opacity:.5}.cropper-view-box{display:block;height:100%;outline:1px solid #39f;outline-color:rgba(51,153,255,.75);overflow:hidden;width:100%}.cropper-dashed{border:0 dashed #eee;display:block;opacity:.5;position:absolute}.cropper-dashed.dashed-h{border-bottom-width:1px;border-top-width:1px;height:calc(100% / 3);left:0;top:calc(100% / 3);width:100%}.cropper-dashed.dashed-v{border-left-width:1px;border-right-width:1px;height:100%;left:calc(100% / 3);top:0;width:calc(100% / 3)}.cropper-center{display:block;height:0;left:50%;opacity:.75;position:absolute;top:50%;width:0}.cropper-center:before,.cropper-center:after{background-color:#eee;content:" ";display:block;position:absolute}.cropper-center:before{height:1px;left:-3px;top:0;width:7px}.cropper-center:after{height:7px;left:0;top:-3px;width:1px}.cropper-face,.cropper-line,.cropper-point{display:block;height:100%;opacity:.1;position:absolute;width:100%}.cropper-face{background-color:#fff;left:0;top:0}.cropper-line{background-color:#39f}.cropper-line.line-e{cursor:ew-resize;right:-3px;top:0;width:5px}.cropper-line.line-n{cursor:ns-resize;height:5px;left:0;top:-3px}.cropper-line.line-w{cursor:ew-resize;left:-3px;top:0;width:5px}.cropper-line.line-s{bottom:-3px;cursor:ns-resize;height:5px;left:0}.cropper-point{background-color:#39f;height:5px;opacity:.75;width:5px}.cropper-point.point-e{cursor:ew-resize;margin-top:-3px;right:-3px;top:50%}.cropper-point.point-n{cursor:ns-resize;left:50%;margin-left:-3px;top:-3px}.cropper-point.point-w{cursor:ew-resize;left:-3px;margin-top:-3px;top:50%}.cropper-point.point-s{bottom:-3px;cursor:s-resize;left:50%;margin-left:-3px}.cropper-point.point-ne{cursor:nesw-resize;right:-3px;top:-3px}.cropper-point.point-nw{cursor:nwse-resize;left:-3px;top:-3px}.cropper-point.point-sw{bottom:-3px;cursor:nesw-resize;left:-3px}.cropper-point.point-se{bottom:-3px;cursor:nwse-resize;height:20px;opacity:1;right:-3px;width:20px}@media (min-width: 768px){.cropper-point.point-se{height:15px;width:15px}}@media (min-width: 992px){.cropper-point.point-se{height:10px;width:10px}}@media (min-width: 1200px){.cropper-point.point-se{height:5px;opacity:.75;width:5px}}.cropper-point.point-se:before{background-color:#39f;bottom:-50%;content:" ";display:block;height:200%;opacity:0;position:absolute;right:-50%;width:200%}.cropper-invisible{opacity:0}.cropper-bg{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC)}.cropper-hide{display:block;height:0;position:absolute;width:0}.cropper-hidden{display:none!important}.cropper-move{cursor:move}.cropper-crop{cursor:crosshair}.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point{cursor:not-allowed}.guns-cropper .guns-cropper-group{display:flex}.guns-cropper .guns-cropper-img-group{flex:1}.guns-cropper .guns-cropper-preview-group{font-size:0;text-align:right}.guns-cropper .guns-cropper-preview{margin-top:16px;display:inline-block;border:1px solid hsla(0,0%,80%,.6);vertical-align:top;overflow:hidden}.guns-cropper .guns-cropper-preview-circle{margin-top:16px;border-radius:50%}@media screen and (max-width: 768px){.guns-cropper-responsive.guns-cropper .guns-cropper-preview-group{display:none}}.guns-cropper-tool{margin-top:6px}.guns-cropper-tool-item{margin:10px 14px 0 0;vertical-align:top}.guns-cropper-tool-item:last-child{margin-right:0!important}.guns-cropper-tool-item>.ant-btn+span{vertical-align:-1px}.guns-cropper-tool-btn{width:auto;padding:0 10px!important}.guns-cropper-tool-btn-ok{padding-left:12px;padding-right:12px}@media screen and (max-width: 768px){.guns-cropper-responsive .guns-cropper-tool .guns-cropper-tool-item{margin-right:6px}}.form[data-v-05dad288]{width:50%}.label[data-v-05dad288]{text-align:right}.avatar[data-v-05dad288]{position:relative;margin:0 20px}.user-info-avatar-group[data-v-05dad288]{margin:16px 0;display:inline-block;position:relative;cursor:pointer}.user-info-avatar-group .user-info-avatar-icon[data-v-05dad288]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:30px;display:none;z-index:2}.user-info-avatar-group:hover .user-info-avatar-icon[data-v-05dad288]{display:block}.user-info-avatar-group[data-v-05dad288]:hover:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;border-radius:50%;background-color:rgba(0,0,0,.3)}@media screen and (max-width: 768px){.form[data-v-05dad288]{width:100%}}\n',document.head.appendChild(e);var Q="undefined"!=typeof window&&void 0!==window.document,Z=Q?window:{},G=!(!Q||!Z.document.documentElement)&&"ontouchstart"in Z.document.documentElement,K=!!Q&&"PointerEvent"in Z,V="cropper",J="all",tt="crop",et="move",it="zoom",at="e",ot="w",rt="s",nt="n",st="ne",ht="nw",pt="se",ct="sw",lt="".concat(V,"-crop"),dt="".concat(V,"-disabled"),ut="".concat(V,"-hidden"),mt="".concat(V,"-hide"),gt="".concat(V,"-invisible"),ft="".concat(V,"-modal"),vt="".concat(V,"-move"),bt="".concat(V,"Action"),wt="".concat(V,"Preview"),yt="crop",xt="move",Ct="none",Mt="crop",kt="cropend",Dt="cropmove",Bt="cropstart",At="dblclick",Wt=K?"pointerdown":G?"touchstart":"mousedown",Nt=K?"pointermove":G?"touchmove":"mousemove",Ot=K?"pointerup pointercancel":G?"touchend touchcancel":"mouseup",Rt="ready",Ht="resize",Tt="wheel",St="zoom",zt="image/jpeg",Et=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Lt=/^data:/,Xt=/^data:image\/jpeg;base64,/,jt=/^img|canvas$/i,Yt={viewMode:0,dragMode:yt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},_t=Number.isNaN||Z.isNaN;function Ut(t){return"number"==typeof t&&!_t(t)}var It=function(t){return t>0&&t<1/0};function Pt(t){return void 0===t}function Ft(t){return"object"===P(t)&&null!==t}var qt=Object.prototype.hasOwnProperty;function $t(t){if(!Ft(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&qt.call(i,"isPrototypeOf")}catch(a){return!1}}function Qt(t){return"function"==typeof t}var Zt=Array.prototype.slice;function Gt(t){return Array.from?Array.from(t):Zt.call(t)}function Kt(t,e){return t&&Qt(e)&&(Array.isArray(t)||Ut(t.length)?Gt(t).forEach((function(i,a){e.call(t,i,a,t)})):Ft(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}))),t}var Vt=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return Ft(t)&&i.length>0&&i.forEach((function(e){Ft(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},Jt=/\.\d*(?:0|9){12}\d*$/;function te(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return Jt.test(t)?Math.round(t*e)/e:t}var ee=/^width|height|left|top|marginLeft|marginTop$/;function ie(t,e){var i=t.style;Kt(e,(function(t,e){ee.test(e)&&Ut(t)&&(t="".concat(t,"px")),i[e]=t}))}function ae(t,e){if(e)if(Ut(t.length))Kt(t,(function(t){ae(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function oe(t,e){e&&(Ut(t.length)?Kt(t,(function(t){oe(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function re(t,e,i){e&&(Ut(t.length)?Kt(t,(function(t){re(t,e,i)})):i?ae(t,e):oe(t,e))}var ne=/([a-z\d])([A-Z])/g;function se(t){return t.replace(ne,"$1-$2").toLowerCase()}function he(t,e){return Ft(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(se(e)))}function pe(t,e,i){Ft(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(se(e)),i)}var ce=/\s\s*/,le=function(){var t=!1;if(Q){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});Z.addEventListener("test",i,a),Z.removeEventListener("test",i,a)}return t}();function de(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i;e.trim().split(ce).forEach((function(e){if(!le){var r=t.listeners;r&&r[e]&&r[e][i]&&(o=r[e][i],delete r[e][i],0===Object.keys(r[e]).length&&delete r[e],0===Object.keys(r).length&&delete t.listeners)}t.removeEventListener(e,o,a)}))}function ue(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i;e.trim().split(ce).forEach((function(e){if(a.once&&!le){var r=t.listeners,n=void 0===r?{}:r;o=function(){delete n[e][i],t.removeEventListener(e,o,a);for(var r=arguments.length,s=new Array(r),h=0;h<r;h++)s[h]=arguments[h];i.apply(t,s)},n[e]||(n[e]={}),n[e][i]&&t.removeEventListener(e,n[e][i],a),n[e][i]=o,t.listeners=n}t.addEventListener(e,o,a)}))}function me(t,e,i){var a;return Qt(Event)&&Qt(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function ge(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var fe=Z.location,ve=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function be(t){var e=t.match(ve);return null!==e&&(e[1]!==fe.protocol||e[2]!==fe.hostname||e[3]!==fe.port)}function we(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function ye(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,o=t.translateX,r=t.translateY,n=[];Ut(o)&&0!==o&&n.push("translateX(".concat(o,"px)")),Ut(r)&&0!==r&&n.push("translateY(".concat(r,"px)")),Ut(e)&&0!==e&&n.push("rotate(".concat(e,"deg)")),Ut(i)&&1!==i&&n.push("scaleX(".concat(i,")")),Ut(a)&&1!==a&&n.push("scaleY(".concat(a,")"));var s=n.length?n.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function xe(t,e){var i=t.pageX,a=t.pageY,o={endX:i,endY:a};return e?o:U({startX:i,startY:a},o)}function Ce(t){var e=t.aspectRatio,i=t.height,a=t.width,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",r=It(a),n=It(i);if(r&&n){var s=i*e;"contain"===o&&s>a||"cover"===o&&s<a?i=a/e:a=i*e}else r?i=a/e:n&&(a=i*e);return{width:a,height:i}}var Me=String.fromCharCode,ke=/^data:.*,/;function De(t){var e,i=new DataView(t);try{var a,o,r;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var n=i.byteLength,s=2;s+1<n;){if(255===i.getUint8(s)&&225===i.getUint8(s+1)){o=s;break}s+=1}if(o){var h=o+10;if("Exif"===function(t,e,i){var a="";i+=e;for(var o=e;o<i;o+=1)a+=Me(t.getUint8(o));return a}(i,o+4,4)){var p=i.getUint16(h);if(((a=18761===p)||19789===p)&&42===i.getUint16(h+2,a)){var c=i.getUint32(h+4,a);c>=8&&(r=h+c)}}}if(r){var l,d,u=i.getUint16(r,a);for(d=0;d<u;d+=1)if(l=r+12*d+2,274===i.getUint16(l,a)){l+=8,e=i.getUint16(l,a),i.setUint16(l,1,a);break}}}catch(m){e=1}return e}var Be={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,o=Number(e.minContainerWidth),r=Number(e.minContainerHeight);ae(a,ut),oe(t,ut);var n={width:Math.max(i.offsetWidth,o>=0?o:200),height:Math.max(i.offsetHeight,r>=0?r:100)};this.containerData=n,ie(a,{width:n.width,height:n.height}),ae(t,ut),oe(a,ut)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180==90,o=a?e.naturalHeight:e.naturalWidth,r=a?e.naturalWidth:e.naturalHeight,n=o/r,s=t.width,h=t.height;t.height*n>t.width?3===i?s=t.height*n:h=t.width/n:3===i?h=t.width/n:s=t.height*n;var p={aspectRatio:n,naturalWidth:o,naturalHeight:r,width:s,height:h};this.canvasData=p,this.limited=1===i||2===i,this.limitCanvas(!0,!0),p.width=Math.min(Math.max(p.width,p.minWidth),p.maxWidth),p.height=Math.min(Math.max(p.height,p.minHeight),p.maxHeight),p.left=(t.width-p.width)/2,p.top=(t.height-p.height)/2,p.oldLeft=p.left,p.oldTop=p.top,this.initialCanvasData=Vt({},p)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,o=this.canvasData,r=this.cropBoxData,n=i.viewMode,s=o.aspectRatio,h=this.cropped&&r;if(t){var p=Number(i.minCanvasWidth)||0,c=Number(i.minCanvasHeight)||0;n>1?(p=Math.max(p,a.width),c=Math.max(c,a.height),3===n&&(c*s>p?p=c*s:c=p/s)):n>0&&(p?p=Math.max(p,h?r.width:0):c?c=Math.max(c,h?r.height:0):h&&(p=r.width,(c=r.height)*s>p?p=c*s:c=p/s));var l=Ce({aspectRatio:s,width:p,height:c});p=l.width,c=l.height,o.minWidth=p,o.minHeight=c,o.maxWidth=1/0,o.maxHeight=1/0}if(e)if(n>(h?0:1)){var d=a.width-o.width,u=a.height-o.height;o.minLeft=Math.min(0,d),o.minTop=Math.min(0,u),o.maxLeft=Math.max(0,d),o.maxTop=Math.max(0,u),h&&this.limited&&(o.minLeft=Math.min(r.left,r.left+(r.width-o.width)),o.minTop=Math.min(r.top,r.top+(r.height-o.height)),o.maxLeft=r.left,o.maxTop=r.top,2===n&&(o.width>=a.width&&(o.minLeft=Math.min(0,d),o.maxLeft=Math.max(0,d)),o.height>=a.height&&(o.minTop=Math.min(0,u),o.maxTop=Math.max(0,u))))}else o.minLeft=-o.width,o.minTop=-o.height,o.maxLeft=a.width,o.maxTop=a.height},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var o=function(t){var e=t.width,i=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:i,height:e};var o=a%90*Math.PI/180,r=Math.sin(o),n=Math.cos(o),s=e*n+i*r,h=e*r+i*n;return a>90?{width:h,height:s}:{width:s,height:h}}({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),r=o.width,n=o.height,s=i.width*(r/i.naturalWidth),h=i.height*(n/i.naturalHeight);i.left-=(s-i.width)/2,i.top-=(h-i.height)/2,i.width=s,i.height=h,i.aspectRatio=r/n,i.naturalWidth=r,i.naturalHeight=n,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,ie(this.canvas,Vt({width:i.width,height:i.height},ye({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),o=i.naturalHeight*(e.height/e.naturalHeight);Vt(i,{width:a,height:o,left:(e.width-a)/2,top:(e.height-o)/2}),ie(this.image,Vt({width:i.width,height:i.height},ye(Vt({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,o={width:e.width,height:e.height};i&&(e.height*i>e.width?o.height=o.width/i:o.width=o.height*i),this.cropBoxData=o,this.limitCropBox(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.width=Math.max(o.minWidth,o.width*a),o.height=Math.max(o.minHeight,o.height*a),o.left=e.left+(e.width-o.width)/2,o.top=e.top+(e.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCropBoxData=Vt({},o)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,o=this.canvasData,r=this.cropBoxData,n=this.limited,s=i.aspectRatio;if(t){var h=Number(i.minCropBoxWidth)||0,p=Number(i.minCropBoxHeight)||0,c=n?Math.min(a.width,o.width,o.width+o.left,a.width-o.left):a.width,l=n?Math.min(a.height,o.height,o.height+o.top,a.height-o.top):a.height;h=Math.min(h,a.width),p=Math.min(p,a.height),s&&(h&&p?p*s>h?p=h/s:h=p*s:h?p=h/s:p&&(h=p*s),l*s>c?l=c/s:c=l*s),r.minWidth=Math.min(h,c),r.minHeight=Math.min(p,l),r.maxWidth=c,r.maxHeight=l}e&&(n?(r.minLeft=Math.max(0,o.left),r.minTop=Math.max(0,o.top),r.maxLeft=Math.min(a.width,o.left+o.width)-r.width,r.maxTop=Math.min(a.height,o.top+o.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=a.width-r.width,r.maxTop=a.height-r.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&pe(this.face,bt,i.width>=e.width&&i.height>=e.height?et:J),ie(this.cropBox,Vt({width:i.width,height:i.height},ye({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),me(this.element,Mt,this.getData())}},Ae={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,o=t.alt||"The image to preview",r=document.createElement("img");if(e&&(r.crossOrigin=e),r.src=a,r.alt=o,this.viewBox.appendChild(r),this.viewBoxImage=r,i){var n=i;"string"==typeof i?n=t.ownerDocument.querySelectorAll(i):i.querySelector&&(n=[i]),this.previews=n,Kt(n,(function(t){var i=document.createElement("img");pe(t,wt,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=o,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)}))}},resetPreview:function(){Kt(this.previews,(function(t){var e=he(t,wt);ie(t,{width:e.width,height:e.height}),t.innerHTML=e.html,function(t,e){if(Ft(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(se(e)))}(t,wt)}))},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,o=i.height,r=t.width,n=t.height,s=i.left-e.left-t.left,h=i.top-e.top-t.top;this.cropped&&!this.disabled&&(ie(this.viewBoxImage,Vt({width:r,height:n},ye(Vt({translateX:-s,translateY:-h},t)))),Kt(this.previews,(function(e){var i=he(e,wt),p=i.width,c=i.height,l=p,d=c,u=1;a&&(d=o*(u=p/a)),o&&d>c&&(l=a*(u=c/o),d=c),ie(e,{width:l,height:d}),ie(e.getElementsByTagName("img")[0],Vt({width:r*u,height:n*u},ye(Vt({translateX:-s*u,translateY:-h*u},t))))})))}},We={bind:function(){var t=this.element,e=this.options,i=this.cropper;Qt(e.cropstart)&&ue(t,Bt,e.cropstart),Qt(e.cropmove)&&ue(t,Dt,e.cropmove),Qt(e.cropend)&&ue(t,kt,e.cropend),Qt(e.crop)&&ue(t,Mt,e.crop),Qt(e.zoom)&&ue(t,St,e.zoom),ue(i,Wt,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&ue(i,Tt,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&ue(i,At,this.onDblclick=this.dblclick.bind(this)),ue(t.ownerDocument,Nt,this.onCropMove=this.cropMove.bind(this)),ue(t.ownerDocument,Ot,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&ue(window,Ht,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;Qt(e.cropstart)&&de(t,Bt,e.cropstart),Qt(e.cropmove)&&de(t,Dt,e.cropmove),Qt(e.cropend)&&de(t,kt,e.cropend),Qt(e.crop)&&de(t,Mt,e.crop),Qt(e.zoom)&&de(t,St,e.zoom),de(i,Wt,this.onCropStart),e.zoomable&&e.zoomOnWheel&&de(i,Tt,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&de(i,At,this.onDblclick),de(t.ownerDocument,Nt,this.onCropMove),de(t.ownerDocument,Ot,this.onCropEnd),e.responsive&&de(window,Ht,this.onResize)}},Ne={resize:function(){if(!this.disabled){var t,e,i=this.options,a=this.container,o=this.containerData,r=a.offsetWidth/o.width,n=a.offsetHeight/o.height,s=Math.abs(r-1)>Math.abs(n-1)?r:n;1!==s&&(i.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(Kt(t,(function(e,i){t[i]=e*s}))),this.setCropBoxData(Kt(e,(function(t,i){e[i]=t*s})))))}},dblclick:function(){var t,e;this.disabled||this.options.dragMode===Ct||this.setDragMode((t=this.dragBox,e=lt,(t.classList?t.classList.contains(e):t.className.indexOf(e)>-1)?xt:yt))},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(Ut(e)&&1!==e||Ut(i)&&0!==i||t.ctrlKey))){var a,o=this.options,r=this.pointers;t.changedTouches?Kt(t.changedTouches,(function(t){r[t.identifier]=xe(t)})):r[t.pointerId||0]=xe(t),a=Object.keys(r).length>1&&o.zoomable&&o.zoomOnTouch?it:he(t.target,bt),Et.test(a)&&!1!==me(this.element,Bt,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===tt&&(this.cropping=!0,ae(this.dragBox,ft)))}},cropMove:function(t){var e=this.action;if(!this.disabled&&e){var i=this.pointers;t.preventDefault(),!1!==me(this.element,Dt,{originalEvent:t,action:e})&&(t.changedTouches?Kt(t.changedTouches,(function(t){Vt(i[t.identifier]||{},xe(t,!0))})):Vt(i[t.pointerId||0]||{},xe(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?Kt(t.changedTouches,(function(t){delete i[t.identifier]})):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,re(this.dragBox,ft,this.cropped&&this.options.modal)),me(this.element,kt,{originalEvent:t,action:e}))}}},Oe={change:function(t){var e,i=this.options,a=this.canvasData,o=this.containerData,r=this.cropBoxData,n=this.pointers,s=this.action,h=i.aspectRatio,p=r.left,c=r.top,l=r.width,d=r.height,u=p+l,m=c+d,g=0,f=0,v=o.width,b=o.height,w=!0;!h&&t.shiftKey&&(h=l&&d?l/d:1),this.limited&&(g=r.minLeft,f=r.minTop,v=g+Math.min(o.width,a.width,a.left+a.width),b=f+Math.min(o.height,a.height,a.top+a.height));var y=n[Object.keys(n)[0]],x={x:y.endX-y.startX,y:y.endY-y.startY},C=function(t){switch(t){case at:u+x.x>v&&(x.x=v-u);break;case ot:p+x.x<g&&(x.x=g-p);break;case nt:c+x.y<f&&(x.y=f-c);break;case rt:m+x.y>b&&(x.y=b-m)}};switch(s){case J:p+=x.x,c+=x.y;break;case at:if(x.x>=0&&(u>=v||h&&(c<=f||m>=b))){w=!1;break}C(at),(l+=x.x)<0&&(s=ot,p-=l=-l),h&&(d=l/h,c+=(r.height-d)/2);break;case nt:if(x.y<=0&&(c<=f||h&&(p<=g||u>=v))){w=!1;break}C(nt),d-=x.y,c+=x.y,d<0&&(s=rt,c-=d=-d),h&&(l=d*h,p+=(r.width-l)/2);break;case ot:if(x.x<=0&&(p<=g||h&&(c<=f||m>=b))){w=!1;break}C(ot),l-=x.x,p+=x.x,l<0&&(s=at,p-=l=-l),h&&(d=l/h,c+=(r.height-d)/2);break;case rt:if(x.y>=0&&(m>=b||h&&(p<=g||u>=v))){w=!1;break}C(rt),(d+=x.y)<0&&(s=nt,c-=d=-d),h&&(l=d*h,p+=(r.width-l)/2);break;case st:if(h){if(x.y<=0&&(c<=f||u>=v)){w=!1;break}C(nt),d-=x.y,c+=x.y,l=d*h}else C(nt),C(at),x.x>=0?u<v?l+=x.x:x.y<=0&&c<=f&&(w=!1):l+=x.x,x.y<=0?c>f&&(d-=x.y,c+=x.y):(d-=x.y,c+=x.y);l<0&&d<0?(s=ct,c-=d=-d,p-=l=-l):l<0?(s=ht,p-=l=-l):d<0&&(s=pt,c-=d=-d);break;case ht:if(h){if(x.y<=0&&(c<=f||p<=g)){w=!1;break}C(nt),d-=x.y,c+=x.y,l=d*h,p+=r.width-l}else C(nt),C(ot),x.x<=0?p>g?(l-=x.x,p+=x.x):x.y<=0&&c<=f&&(w=!1):(l-=x.x,p+=x.x),x.y<=0?c>f&&(d-=x.y,c+=x.y):(d-=x.y,c+=x.y);l<0&&d<0?(s=pt,c-=d=-d,p-=l=-l):l<0?(s=st,p-=l=-l):d<0&&(s=ct,c-=d=-d);break;case ct:if(h){if(x.x<=0&&(p<=g||m>=b)){w=!1;break}C(ot),l-=x.x,p+=x.x,d=l/h}else C(rt),C(ot),x.x<=0?p>g?(l-=x.x,p+=x.x):x.y>=0&&m>=b&&(w=!1):(l-=x.x,p+=x.x),x.y>=0?m<b&&(d+=x.y):d+=x.y;l<0&&d<0?(s=st,c-=d=-d,p-=l=-l):l<0?(s=pt,p-=l=-l):d<0&&(s=ht,c-=d=-d);break;case pt:if(h){if(x.x>=0&&(u>=v||m>=b)){w=!1;break}C(at),d=(l+=x.x)/h}else C(rt),C(at),x.x>=0?u<v?l+=x.x:x.y>=0&&m>=b&&(w=!1):l+=x.x,x.y>=0?m<b&&(d+=x.y):d+=x.y;l<0&&d<0?(s=ht,c-=d=-d,p-=l=-l):l<0?(s=ct,p-=l=-l):d<0&&(s=st,c-=d=-d);break;case et:this.move(x.x,x.y),w=!1;break;case it:this.zoom(function(t){var e=U({},t),i=0;return Kt(t,(function(t,a){delete e[a],Kt(e,(function(e){var a=Math.abs(t.startX-e.startX),o=Math.abs(t.startY-e.startY),r=Math.abs(t.endX-e.endX),n=Math.abs(t.endY-e.endY),s=Math.sqrt(a*a+o*o),h=(Math.sqrt(r*r+n*n)-s)/s;Math.abs(h)>Math.abs(i)&&(i=h)}))})),i}(n),t),w=!1;break;case tt:if(!x.x||!x.y){w=!1;break}e=ge(this.cropper),p=y.startX-e.left,c=y.startY-e.top,l=r.minWidth,d=r.minHeight,x.x>0?s=x.y>0?pt:st:x.x<0&&(p-=l,s=x.y>0?ct:ht),x.y<0&&(c-=d),this.cropped||(oe(this.cropBox,ut),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}w&&(r.width=l,r.height=d,r.left=p,r.top=c,this.action=s,this.renderCropBox()),Kt(n,(function(t){t.startX=t.endX,t.startY=t.endY}))}},Re={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&ae(this.dragBox,ft),oe(this.cropBox,ut),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=Vt({},this.initialImageData),this.canvasData=Vt({},this.initialCanvasData),this.cropBoxData=Vt({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(Vt(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),oe(this.dragBox,ft),ae(this.cropBox,ut)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,Kt(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,oe(this.cropper,dt)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,ae(this.cropper,dt)),this},destroy:function(){var t=this.element;return t[V]?(t[V]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=i.left,o=i.top;return this.moveTo(Pt(t)?t:a+Number(t),Pt(e)?e:o+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(Ut(t)&&(i.left=t,a=!0),Ut(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,o=this.canvasData,r=o.width,n=o.height,s=o.naturalWidth,h=o.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&a.zoomable){var p=s*t,c=h*t;if(!1===me(this.element,St,{ratio:t,oldRatio:r/s,originalEvent:i}))return this;if(i){var l=this.pointers,d=ge(this.cropper),u=l&&Object.keys(l).length?function(t){var e=0,i=0,a=0;return Kt(t,(function(t){var o=t.startX,r=t.startY;e+=o,i+=r,a+=1})),{pageX:e/=a,pageY:i/=a}}(l):{pageX:i.pageX,pageY:i.pageY};o.left-=(p-r)*((u.pageX-d.left-o.left)/r),o.top-=(c-n)*((u.pageY-d.top-o.top)/n)}else $t(e)&&Ut(e.x)&&Ut(e.y)?(o.left-=(p-r)*((e.x-o.left)/r),o.top-=(c-n)*((e.y-o.top)/n)):(o.left-=(p-r)/2,o.top-=(c-n)/2);o.width=p,o.height=c,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return Ut(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,Ut(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(Ut(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(Ut(t)&&(i.scaleX=t,a=!0),Ut(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,a=this.imageData,o=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-o.left,y:r.top-o.top,width:r.width,height:r.height};var n=a.width/a.naturalWidth;if(Kt(t,(function(e,i){t[i]=e/n})),e){var s=Math.round(t.y+t.height),h=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=h-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return i.rotatable&&(t.rotate=a.rotate||0),i.scalable&&(t.scaleX=a.scaleX||1,t.scaleY=a.scaleY||1),t},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,o={};if(this.ready&&!this.disabled&&$t(t)){var r=!1;e.rotatable&&Ut(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,r=!0),e.scalable&&(Ut(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,r=!0),Ut(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var n=i.width/i.naturalWidth;Ut(t.x)&&(o.left=t.x*n+a.left),Ut(t.y)&&(o.top=t.y*n+a.top),Ut(t.width)&&(o.width=t.width*n),Ut(t.height)&&(o.height=t.height*n),this.setCropBoxData(o)}return this},getContainerData:function(){return this.ready?Vt({},this.containerData):{}},getImageData:function(){return this.sized?Vt({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&Kt(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){e[i]=t[i]})),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&$t(t)&&(Ut(t.left)&&(e.left=t.left),Ut(t.top)&&(e.top=t.top),Ut(t.width)?(e.width=t.width,e.height=t.width/i):Ut(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,o=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&$t(t)&&(Ut(t.left)&&(a.left=t.left),Ut(t.top)&&(a.top=t.top),Ut(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),Ut(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),o&&(e?a.height=a.width/o:i&&(a.width=a.height*o)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=function(t,e,i,a){var o=e.aspectRatio,r=e.naturalWidth,n=e.naturalHeight,s=e.rotate,h=void 0===s?0:s,p=e.scaleX,c=void 0===p?1:p,l=e.scaleY,d=void 0===l?1:l,u=i.aspectRatio,m=i.naturalWidth,g=i.naturalHeight,f=a.fillColor,v=void 0===f?"transparent":f,b=a.imageSmoothingEnabled,w=void 0===b||b,y=a.imageSmoothingQuality,x=void 0===y?"low":y,C=a.maxWidth,M=void 0===C?1/0:C,k=a.maxHeight,D=void 0===k?1/0:k,B=a.minWidth,A=void 0===B?0:B,W=a.minHeight,N=void 0===W?0:W,O=document.createElement("canvas"),R=O.getContext("2d"),H=Ce({aspectRatio:u,width:M,height:D}),T=Ce({aspectRatio:u,width:A,height:N},"cover"),S=Math.min(H.width,Math.max(T.width,m)),z=Math.min(H.height,Math.max(T.height,g)),E=Ce({aspectRatio:o,width:M,height:D}),L=Ce({aspectRatio:o,width:A,height:N},"cover"),X=Math.min(E.width,Math.max(L.width,r)),j=Math.min(E.height,Math.max(L.height,n)),Y=[-X/2,-j/2,X,j];return O.width=te(S),O.height=te(z),R.fillStyle=v,R.fillRect(0,0,S,z),R.save(),R.translate(S/2,z/2),R.rotate(h*Math.PI/180),R.scale(c,d),R.imageSmoothingEnabled=w,R.imageSmoothingQuality=x,R.drawImage.apply(R,[t].concat(q(Y.map((function(t){return Math.floor(te(t))}))))),R.restore(),O}(this.image,this.imageData,e,t);if(!this.cropped)return i;var a=this.getData(t.rounded),o=a.x,r=a.y,n=a.width,s=a.height,h=i.width/Math.floor(e.naturalWidth);1!==h&&(o*=h,r*=h,n*=h,s*=h);var p=n/s,c=Ce({aspectRatio:p,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),l=Ce({aspectRatio:p,width:t.minWidth||0,height:t.minHeight||0},"cover"),d=Ce({aspectRatio:p,width:t.width||(1!==h?i.width:n),height:t.height||(1!==h?i.height:s)}),u=d.width,m=d.height;u=Math.min(c.width,Math.max(l.width,u)),m=Math.min(c.height,Math.max(l.height,m));var g=document.createElement("canvas"),f=g.getContext("2d");g.width=te(u),g.height=te(m),f.fillStyle=t.fillColor||"transparent",f.fillRect(0,0,u,m);var v=t.imageSmoothingEnabled,b=void 0===v||v,w=t.imageSmoothingQuality;f.imageSmoothingEnabled=b,w&&(f.imageSmoothingQuality=w);var y,x,C,M,k,D,B=i.width,A=i.height,W=o,N=r;W<=-n||W>B?(W=0,y=0,C=0,k=0):W<=0?(C=-W,W=0,k=y=Math.min(B,n+W)):W<=B&&(C=0,k=y=Math.min(n,B-W)),y<=0||N<=-s||N>A?(N=0,x=0,M=0,D=0):N<=0?(M=-N,N=0,D=x=Math.min(A,s+N)):N<=A&&(M=0,D=x=Math.min(s,A-N));var O=[W,N,y,x];if(k>0&&D>0){var R=u/n;O.push(C*R,M*R,k*R,D*R)}return f.drawImage.apply(f,[i].concat(q(O.map((function(t){return Math.floor(te(t))}))))),g},setAspectRatio:function(t){var e=this.options;return this.disabled||Pt(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var o=t===yt,r=e.movable&&t===xt;t=o||r?t:Ct,e.dragMode=t,pe(i,bt,t),re(i,lt,o),re(i,vt,r),e.cropBoxMovable||(pe(a,bt,t),re(a,lt,o),re(a,vt,r))}return this}},He=Z.Cropper,Te=function(){function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!e||!jt.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=Vt({},Yt,$t(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return e=t,i=[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e[V]){if(e[V]=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e=this;if(t){this.url=t,this.imageData={};var i=this.element,a=this.options;if(a.rotatable||a.scalable||(a.checkOrientation=!1),a.checkOrientation&&window.ArrayBuffer)if(Lt.test(t))Xt.test(t)?this.read((o=t.replace(ke,""),r=atob(o),n=new ArrayBuffer(r.length),Kt(s=new Uint8Array(n),(function(t,e){s[e]=r.charCodeAt(e)})),n)):this.clone();else{var o,r,n,s,h=new XMLHttpRequest,p=this.clone.bind(this);this.reloading=!0,this.xhr=h,h.onabort=p,h.onerror=p,h.ontimeout=p,h.onprogress=function(){h.getResponseHeader("content-type")!==zt&&h.abort()},h.onload=function(){e.read(h.response)},h.onloadend=function(){e.reloading=!1,e.xhr=null},a.checkCrossOrigin&&be(t)&&i.crossOrigin&&(t=we(t)),h.open("GET",t,!0),h.responseType="arraybuffer",h.withCredentials="use-credentials"===i.crossOrigin,h.send()}else this.clone()}}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=De(t),o=0,r=1,n=1;if(a>1){this.url=function(t,e){for(var i=[],a=new Uint8Array(t);a.length>0;)i.push(Me.apply(null,Gt(a.subarray(0,8192)))),a=a.subarray(8192);return"data:".concat(e,";base64,").concat(btoa(i.join("")))}(t,zt);var s=function(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}(a);o=s.rotate,r=s.scaleX,n=s.scaleY}e.rotatable&&(i.rotate=o),e.scalable&&(i.scaleX=r,i.scaleY=n),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&be(e)&&(i||(i="anonymous"),a=we(e)),this.crossOrigin=i,this.crossOriginUrl=a;var o=document.createElement("img");i&&(o.crossOrigin=i),o.src=a||e,o.alt=t.alt||"The image to crop",this.image=o,o.onload=this.start.bind(this),o.onerror=this.stop.bind(this),ae(o,mt),t.parentNode.insertBefore(o,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var i=Z.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(Z.navigator.userAgent),a=function(e,i){Vt(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=Vt({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!e.naturalWidth||i){var o=document.createElement("img"),r=document.body||document.documentElement;this.sizingImage=o,o.onload=function(){a(o.width,o.height),i||r.removeChild(o)},o.src=e.src,i||(o.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",r.appendChild(o))}else a(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,e=this.options,i=this.image,a=t.parentNode,o=document.createElement("div");o.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var r=o.querySelector(".".concat(V,"-container")),n=r.querySelector(".".concat(V,"-canvas")),s=r.querySelector(".".concat(V,"-drag-box")),h=r.querySelector(".".concat(V,"-crop-box")),p=h.querySelector(".".concat(V,"-face"));this.container=a,this.cropper=r,this.canvas=n,this.dragBox=s,this.cropBox=h,this.viewBox=r.querySelector(".".concat(V,"-view-box")),this.face=p,n.appendChild(i),ae(t,ut),a.insertBefore(r,t.nextSibling),oe(i,mt),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,ae(h,ut),e.guides||ae(h.getElementsByClassName("".concat(V,"-dashed")),ut),e.center||ae(h.getElementsByClassName("".concat(V,"-center")),ut),e.background&&ae(r,"".concat(V,"-bg")),e.highlight||ae(p,gt),e.cropBoxMovable&&(ae(p,vt),pe(p,bt,J)),e.cropBoxResizable||(ae(h.getElementsByClassName("".concat(V,"-line")),ut),ae(h.getElementsByClassName("".concat(V,"-point")),ut)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),Qt(e.ready)&&ue(t,Rt,e.ready,{once:!0}),me(t,Rt)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),oe(this.element,ut)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],a=[{key:"noConflict",value:function(){return window.Cropper=He,t}},{key:"setDefaults",value:function(t){Vt(Yt,$t(t)&&t)}}],i&&F(e.prototype,i),a&&F(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,i,a}();Vt(Te.prototype,Be,Ae,We,Ne,Oe,Re);const Se={__name:"cropper-preview",props:{previewWidth:{type:Number,required:!0},aspectRatio:{type:Number,required:!0}},setup(t,{expose:e}){const p=i(null);return e({getPreviews:()=>{var t;return null===(t=p.value)||void 0===t?void 0:t.querySelectorAll(".guns-cropper-preview")}}),(e,i)=>(a(),o("div",{ref_key:"rootRef",ref:p,class:"guns-cropper-preview-group",style:n({width:`${t.previewWidth+14}px`})},[r("div",{class:"guns-cropper-preview",style:n({width:`${t.previewWidth}px`,height:t.previewWidth/(t.aspectRatio||1)+"px",marginTop:"0px"})},null,4),1===t.aspectRatio?(a(),o("div",{key:0,class:"guns-cropper-preview guns-cropper-preview-circle",style:n({width:`${t.previewWidth}px`,height:t.previewWidth/t.aspectRatio+"px"})},null,4)):t.aspectRatio?(a(),o(s,{key:1},[r("div",{class:"guns-cropper-preview",style:n({width:`${t.previewWidth}px`,height:(t.previewWidth/3*2-10)/t.aspectRatio+"px"})},null,4),r("div",{class:"guns-cropper-preview",style:n({width:`${t.previewWidth}px`,height:t.previewWidth/3/t.aspectRatio+"px",marginLeft:"10px"})},null,4)],64)):h("",!0)],4))}},ze={class:"guns-cropper-tool"},Ee={__name:"cropper-tools",props:{tools:String,accept:String,okText:String},emits:["crop","move-b","move-l","move-r","move-t","reset","rotate-l","rotate-r","scale-x","scale-y","replace","zoom-in","zoom-out"],setup(t,{emit:e}){const i=t,n=e,w=p((()=>i.tools?i.tools.split("|").map((t=>t.split(",").map((t=>t.trim())))):[])),y=()=>{n("zoom-in")},x=()=>{n("zoom-out")},C=()=>{n("rotate-l")},M=()=>{n("rotate-r")},k=()=>{n("move-l")},D=()=>{n("move-r")},B=()=>{n("move-t")},A=()=>{n("move-b")},W=()=>{n("scale-x")},N=()=>{n("scale-y")},O=()=>{n("reset")},R=()=>{n("crop")},H=({file:t})=>{const e=new FileReader;return e.onload=e=>{var i;n("replace",{data:null===(i=e.target)||void 0===i?void 0:i.result,type:t.type})},e.readAsDataURL(t),!1};return(e,n)=>{const p=c("ZoomInOutlined"),T=f,S=c("ZoomOutOutlined"),z=c("RotateLeftOutlined"),E=c("RotateRightOutlined"),L=c("ArrowLeftOutlined"),X=c("ArrowRightOutlined"),j=c("ArrowUpOutlined"),Y=c("ArrowDownOutlined"),_=c("SwapOutlined"),U=c("SyncOutlined"),I=c("UploadOutlined"),P=v,F=c("CheckOutlined"),q=b;return a(),o("div",ze,[(a(!0),o(s,null,l(w.value,((e,n)=>(a(),d(q,{key:n+e.join(","),class:"guns-cropper-tool-item"},{default:u((()=>[(a(!0),o(s,null,l(e,((e,c)=>(a(),o(s,null,["zoomIn"===e?(a(),d(T,{key:n+"-"+c+"zoomIn",type:"primary",class:"guns-cropper-tool-btn",title:"放大",onClick:y},{icon:u((()=>[m(p)])),_:2},1024)):"zoomOut"===e?(a(),d(T,{key:n+"-"+c+"zoomOut",type:"primary",class:"guns-cropper-tool-btn",title:"缩小",onClick:x},{icon:u((()=>[m(S)])),_:2},1024)):"rotateL"===e?(a(),d(T,{key:n+"-"+c+"rotateL",type:"primary",class:"guns-cropper-tool-btn",title:"向左旋转",onClick:C},{icon:u((()=>[m(z)])),_:2},1024)):"rotateR"===e?(a(),d(T,{key:n+"-"+c+"rotateR",type:"primary",class:"guns-cropper-tool-btn",title:"向右旋转",onClick:M},{icon:u((()=>[m(E)])),_:2},1024)):"moveL"===e?(a(),d(T,{key:n+"-"+c+"moveL",type:"primary",class:"guns-cropper-tool-btn",title:"左移",onClick:k},{icon:u((()=>[m(L)])),_:2},1024)):"moveR"===e?(a(),d(T,{key:n+"-"+c+"moveR",type:"primary",class:"guns-cropper-tool-btn",title:"右移",onClick:D},{icon:u((()=>[m(X)])),_:2},1024)):"moveT"===e?(a(),d(T,{key:n+"-"+c+"moveT",type:"primary",class:"guns-cropper-tool-btn",title:"上移",onClick:B},{icon:u((()=>[m(j)])),_:2},1024)):"moveB"===e?(a(),d(T,{key:n+"-"+c+"moveB",type:"primary",class:"guns-cropper-tool-btn",title:"下移",onClick:A},{icon:u((()=>[m(Y)])),_:2},1024)):"scaleX"===e?(a(),d(T,{key:n+"-"+c+"scaleX",type:"primary",class:"guns-cropper-tool-btn",title:"左右翻转",onClick:W},{icon:u((()=>[m(_)])),_:2},1024)):"scaleY"===e?(a(),d(T,{key:n+"-"+c+"scaleY",type:"primary",class:"guns-cropper-tool-btn",title:"上下翻转",onClick:N},{icon:u((()=>[m(_,{style:{transform:"rotate(90deg)"}})])),_:2},1024)):"reset"===e?(a(),d(T,{key:n+"-"+c+"reset",type:"primary",class:"guns-cropper-tool-btn",title:"重新开始",onClick:O},{icon:u((()=>[m(U)])),_:2},1024)):"upload"===e?(a(),d(P,{key:n+"-"+c+"upload",accept:t.accept,"custom-request":H,"show-upload-list":!1},{default:u((()=>[m(T,{type:"primary",class:"guns-cropper-tool-btn",title:"选择图片",style:{"border-top-right-radius":"2px","border-bottom-right-radius":"2px"}},{icon:u((()=>[m(I)])),_:1})])),_:2},1032,["accept"])):"crop"===e?(a(),d(T,{key:"crop"+c,type:"primary",class:"guns-cropper-tool-btn-ok",onClick:R},{icon:u((()=>[m(F)])),default:u((()=>[r("span",null,g(i.okText||"完成"),1)])),_:2},1024)):h("",!0)],64)))),256))])),_:2},1024)))),128))])}}},Le={class:"guns-cropper-group"},Xe={class:"guns-cropper-img-group"},je=["src"],Ye={__name:"cropper",props:{src:String,imageType:{type:String,default:"image/png"},accept:{type:String,default:"image/*"},tools:{type:String},showPreview:{type:Boolean,default:!0},previewWidth:{type:Number,default:120},okText:String,toBlob:Boolean,options:Object,croppedOptions:Object,aspectRatio:{type:Number,default:1},viewMode:{type:Number,default:0},dragMode:{type:String,default:"crop"},initialAspectRatio:Number,minContainerWidth:{type:Number,default:200},minContainerHeight:{type:Number,default:100},minCanvasWidth:{type:Number,default:0},minCanvasHeight:{type:Number,default:0},minCropBoxWidth:{type:Number,default:0},minCropBoxHeight:{type:Number,default:0},croppedWidth:Number,croppedHeight:Number,croppedMinWidth:{type:Number,default:0},croppedMinHeight:{type:Number,default:0},croppedMaxWidth:Number,croppedMaxHeight:Number,croppedFillColor:{type:String,default:"transparent"},imageSmoothingEnabled:Boolean,imageSmoothingQuality:String},emits:["done"],setup(t,{emit:e}){const s=t,c=e,l=w("GunsLayoutState"),u=i(null),g=i(null),f=i(s.imageType);let v=null,b=-1,B=-1;const A=p((()=>{var t,e;return null===(t=null===(e=y(l))||void 0===e?void 0:e.styleResponsive)||void 0===t||t})),W=()=>{C((()=>{U();const t={aspectRatio:s.aspectRatio,viewMode:s.viewMode,dragMode:s.dragMode,initialAspectRatio:s.initialAspectRatio,minContainerWidth:s.minContainerWidth,minContainerHeight:s.minContainerHeight,minCanvasWidth:s.minCanvasWidth,minCanvasHeight:s.minCanvasHeight,minCropBoxWidth:s.minCropBoxWidth,minCropBoxHeight:s.minCropBoxHeight,...s.options};var e;s.showPreview&&(t.preview=null===(e=y(g.value))||void 0===e?void 0:e.getPreviews());const i=y(u.value);i&&(v=new Te(i,t))}))},N=()=>{v&&v.zoom(.1)},O=()=>{v&&v.zoom(-.1)},R=()=>{v&&v.move(-10,0)},H=()=>{v&&v.move(10,0)},T=()=>{v&&v.move(0,-10)},S=()=>{v&&v.move(0,10)},z=()=>{v&&v.rotate(-45)},E=()=>{v&&v.rotate(45)},L=()=>{v&&v.scaleX(b),b=-b},X=()=>{v&&v.scaleY(B),B=-B},j=()=>{v&&v.reset()},Y=()=>{var t;const e=null===(t=v)||void 0===t?void 0:t.getCroppedCanvas({width:s.croppedWidth,height:s.croppedHeight,minWidth:s.croppedMinWidth,minHeight:s.croppedMinHeight,maxWidth:s.croppedMaxWidth,maxHeight:s.croppedMaxHeight,fillColor:s.croppedFillColor,imageSmoothingEnabled:s.imageSmoothingEnabled,imageSmoothingQuality:s.imageSmoothingQuality,...s.croppedOptions});e?s.toBlob?e.toBlob((t=>{c("done",t)}),f.value):c("done",e.toDataURL(f.value)):c("done")},_=({data:t,type:e})=>{if(f.value=e,v)v.replace(t);else{const e=y(u.value);e&&(e.src=t,e.style.display="block"),W()}},U=()=>{v&&v.destroy(),v=null};return x((()=>s.src),(t=>{t?v?v.replace(t):C((()=>{W()})):U()})),x((()=>s.imageType),(t=>{t&&(f.value=t)})),M((()=>{s.src&&W()})),k((()=>{U()})),(e,i)=>(a(),o("div",{class:D(["guns-cropper",{"guns-cropper-responsive":A.value}])},[r("div",Le,[r("div",Xe,[r("img",{src:t.src,alt:"cropper",ref_key:"imageRef",ref:u,style:n({maxWidth:"100%",display:t.src?"block":"none"})},null,12,je)]),s.showPreview?(a(),d(Se,{key:0,ref_key:"previewRef",ref:g,"aspect-ratio":s.aspectRatio,"preview-width":s.previewWidth},null,8,["aspect-ratio","preview-width"])):h("",!0)]),m(Ee,{tools:t.tools,"ok-text":s.okText,onCrop:Y,onMoveB:S,onMoveL:R,onMoveR:H,onMoveT:T,onReset:j,onRotateL:z,onRotateR:E,onScaleX:L,onScaleY:X,onReplace:_,onZoomIn:N,onZoomOut:O},null,8,["tools","ok-text"])],2))}},_e={__name:"index",props:{visible:Boolean,title:{type:String,default:"裁剪图片"},width:{type:String,default:"660px"},src:String,imageType:{type:String,default:"image/png"},accept:{type:String,default:"image/*"},tools:{type:String,default:["zoomIn,zoomOut","moveL,moveR,moveT,moveB","rotateL,rotateR","scaleX,scaleY","reset,upload","crop"].join(" | ")},showPreview:{type:Boolean,default:!0},previewWidth:{type:Number,default:120},okText:{type:String,defalut:"完成"},toBlob:{type:Boolean,defalut:!1},options:{type:Object,defalut:{}},croppedOptions:{type:Object,defalut:{}},aspectRatio:{type:Number,default:1},viewMode:{type:Number,default:0},dragMode:{type:String,default:"crop"},initialAspectRatio:Number,minContainerWidth:{type:Number,default:200},minContainerHeight:{type:Number,default:100},minCanvasWidth:{type:Number,default:0},minCanvasHeight:{type:Number,default:0},minCropBoxWidth:{type:Number,default:0},minCropBoxHeight:{type:Number,default:0},croppedWidth:Number,croppedHeight:Number,croppedMinWidth:{type:Number,default:0},croppedMinHeight:{type:Number,default:0},croppedMaxWidth:Number,croppedMaxHeight:Number,croppedFillColor:{type:String,default:"transparent"},imageSmoothingEnabled:Boolean,imageSmoothingQuality:String},emits:["update:visible","done"],setup(t,{emit:e}){const o=t,r=e,n=i(!1),s=t=>{r("update:visible",t)},p=t=>{r("done",t)};return(t,e)=>{const i=A;return a(),d(i,{width:o.width,maskClosable:!1,visible:o.visible,"confirm-loading":n.value,forceRender:!0,title:o.title,"body-style":{padding:"16px 18px 18px 18px"},"onUpdate:visible":s,footer:"",onClose:e[0]||(e[0]=t=>s(!1))},{default:u((()=>[o.visible?(a(),d(Ye,B({key:0},o,{onDone:p,"ok-text":o.okText}),null,16,["ok-text"])):h("",!0)])),_:1},8,["width","visible","confirm-loading","title"])}}},Ue={class:"form"},Ie={class:"avatar"},Pe={class:"upload-btn"};t("default",W({__name:"update-avatar",setup(t){const e=N(),n=i(!1),s=i([]),h=i(e.info.avatarUrl),p=O({fileUploadUrl:`${R}${j}?secretFlag=N`,filePreviewUrl:`${R}/sysFileInfo/public/preview?fileId=`,headers:{Authorization:H()}}),l=async t=>{if("done"===t.file.status){let e=t.file.response.data;h.value=e.fileUrl,s.value=[t.file];const i=await S.updateAvatar({avatar:e.fileId});z.success(i.message,.5).then((()=>{window.location.reload()}))}else"error"===t.file.status&&z.error("头像上传失败")},d=async t=>{n.value=!1;const e=new FormData;e.append("file",t,"avatar.jpg");const i=await Y.commonUpload("N",e),a=await S.updateAvatar({avatar:i.data.fileId});z.success(a.message,.5).then((()=>{window.location.reload()}))};return(t,e)=>{const i=E,g=L,b=c("upload-outlined"),w=_e,y=c("cloud-upload-outlined"),x=f,C=v,M=X;return a(),o("div",Ue,[m(M,null,{default:u((()=>[m(i,{md:6,xs:24,sm:24,style:{display:"flex","align-items":"center"}},{default:u((()=>e[3]||(e[3]=[r("div",{class:"label"},[r("span",null,"头像：")],-1)]))),_:1,__:[3]}),m(i,{md:18,xs:24,sm:24,style:{display:"flex","align-items":"center"}},{default:u((()=>[r("div",Ie,[r("div",{class:"user-info-avatar-group",onClick:e[0]||(e[0]=t=>n.value=!0)},[m(g,{size:110,src:h.value},null,8,["src"]),m(b,{class:"user-info-avatar-icon"})]),m(w,{"mask-closable":!1,visible:n.value,"onUpdate:visible":e[1]||(e[1]=t=>n.value=t),src:h.value,"to-blob":!0,onDone:d},null,8,["visible","src"])]),r("div",Pe,[m(C,{"file-list":s.value,"onUpdate:fileList":e[2]||(e[2]=t=>s.value=t),name:"file","show-upload-list":!1,action:p.fileUploadUrl,headers:p.headers,onChange:l,"max-count":1},{default:u((()=>[m(x,{type:"primary"},{default:u((()=>[m(y),e[4]||(e[4]=T(" 上传头像 "))])),_:1,__:[4]})])),_:1},8,["file-list","action","headers"])])])),_:1})])),_:1})])}}},[["__scopeId","data-v-05dad288"]]))}}}));
