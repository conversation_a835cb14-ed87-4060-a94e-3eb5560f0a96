package cn.stylefeng.roses.kernel.sync.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.sync.factory.SyncOrganizationFactory;
import cn.stylefeng.roses.kernel.sync.pojo.OrganizationSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步业务-人力组织机构数据
 *
 * <AUTHOR>
 * @since 2023/10/29 22:57
 */
@Service
public class SyncOrganizationService {

    @Resource
    private HrOrganizationService hrOrganizationService;

    /**
     * 获取所有的组织机构信息
     *
     * <AUTHOR>
     * @since 2023/10/30 10:36
     */
    public List<OrganizationSyncVo> getTotalOrganization() {
        try {
            // 关闭多租户开关
            TenantSwitchHolder.set(false);

            List<HrOrganization> hrOrganizations = hrOrganizationService.list(createWrapper());

            // 组织机构信息转化为返回的VO信息
            return SyncOrganizationFactory.createOrganizationVo(hrOrganizations);

        } finally {
            TenantSwitchHolder.remove();
        }
    }

    /**
     * 分页获取组织机构信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:02
     */
    public PageResult<OrganizationSyncVo> getPageOrganization(BaseRequest baseRequest) {

        Page<HrOrganization> hrOrganizationPage = PageFactory.defaultPage(baseRequest);

        LambdaQueryWrapper<HrOrganization> wrapper = this.createWrapper();

        Page<HrOrganization> page;
        try {
            TenantSwitchHolder.set(false);
            page = this.hrOrganizationService.page(hrOrganizationPage, wrapper);
        } finally {
            TenantSwitchHolder.remove();
        }

        if (ObjectUtil.isEmpty(page.getRecords())) {
            return PageResultFactory.createPageResult(new ArrayList<>(), page.getTotal(), Convert.toInt(page.getSize()),
                    Convert.toInt(page.getCurrent()));
        }

        List<HrOrganization> records = page.getRecords();
        List<OrganizationSyncVo> organizationVo = SyncOrganizationFactory.createOrganizationVo(records);
        return PageResultFactory.createPageResult(organizationVo, page.getTotal(), Convert.toInt(page.getSize()),
                Convert.toInt(page.getCurrent()));
    }

    /**
     * 创建wrapper信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:03
     */
    private LambdaQueryWrapper<HrOrganization> createWrapper() {

        LambdaQueryWrapper<HrOrganization> hrOrganizationLambdaQueryWrapper = new LambdaQueryWrapper<>();

        // 只查询必要字段
        hrOrganizationLambdaQueryWrapper.select(HrOrganization::getOrgId, HrOrganization::getOrgParentId, HrOrganization::getOrgPids,
                HrOrganization::getOrgName, HrOrganization::getOrgShortName, HrOrganization::getOrgCode, HrOrganization::getOrgSort,
                HrOrganization::getStatusFlag, HrOrganization::getOrgType, HrOrganization::getTaxNo, HrOrganization::getRemark,
                HrOrganization::getOrgLevel, HrOrganization::getTenantId);

        // 只查询启用的组织机构
        hrOrganizationLambdaQueryWrapper.eq(HrOrganization::getStatusFlag, StatusEnum.ENABLE.getCode());

        return hrOrganizationLambdaQueryWrapper;
    }

}
