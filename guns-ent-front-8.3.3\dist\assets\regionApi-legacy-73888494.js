System.register(["./index-legacy-ee1db0c7.js"],(function(t,a){"use strict";var e;return{setters:[t=>{e=t.R}],execute:function(){t("R",class{static async findPage(t){return await e.getAndLoadData("/erp/region/page",t)}static async findList(t){return await e.getAndLoadData("/erp/region/list",t)}static async findTree(t){return await e.getAndLoadData("/erp/region/tree",t)}static async findByParentId(t){return await e.getAndLoadData("/erp/region/listByParent",t)}static async findTreeWithLazy(t){return await e.getAndLoadData("/erp/region/treeWithLazy",t)}static async batchDelete(t){return await e.post("/erp/region/batchDelete",t)}static async findSelector(t){return await e.getAndLoadData("/erp/region/selector",t)}static async add(t){return await e.post("/erp/region/add",t)}static async edit(t){return await e.post("/erp/region/edit",t)}static async delete(t){return await e.post("/erp/region/delete",t)}static async detail(t){return await e.getAndLoadData("/erp/region/detail",t)}static async updateStatus(t){return await e.post("/erp/region/updateStatus",t)}static async validateCode(t){return await e.getAndLoadData("/erp/region/validateCode",t)}})}}}));
