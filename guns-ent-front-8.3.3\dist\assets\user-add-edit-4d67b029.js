import{b3 as h,L,r as o,o as U,cb as I,a as y,f as N,w as _,d as w,m as d,M as A}from"./index-18a1ea24.js";import S from"./user-form-5204a582.js";import{U as m}from"./UsersApi-ec2041f8.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              *//* empty css              *//* empty css              */import"./FileApi-418f4d35.js";import"./SysDictTypeApi-1ce2cbe7.js";const z={__name:"user-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(c,{emit:g}){const i=c,p=g,v=h(),b=L(()=>v.info.superAdminFlag),r=o(!1),s=o(!1),a=o({userOrgList:[],userCertificateList:[],sex:"M",superAdminFlag:"N",statusFlag:1}),l=o(null);U(async()=>{i.data?(s.value=!0,O()):(a.value.userSort=await I("SYSTEM_HR_USER"),s.value=!1)});const O=()=>{m.detail({userId:i.data.userId}).then(t=>{a.value=Object.assign({},t),t.userOrgDTOList&&t.userOrgDTOList.length>0?a.value.userOrgList=t.userOrgDTOList.map(e=>({mainFlag:e.mainFlag,positionId:e.positionId,statusFlag:e.statusFlag,positionName:e.positionName,orgId:e.deptId?e.deptId:e.companyId,orgName:e.deptName?e.deptName:e.companyName})):a.value.userOrgList=[],t.userCertificateList.length>0&&a.value.userCertificateList.forEach(e=>{e.attachmentId&&(e.attachmentName=e.attachmentIdWrapper.name,e.attachmentUrl=e.attachmentIdWrapper.thumbUrl)})})},n=t=>{p("update:visible",t)},F=async()=>{l.value.$refs.formRef.validate().then(async t=>{if(t){if(a.value.userOrgList&&a.value.userOrgList.length==0)return d.warning("\u7EC4\u7EC7\u673A\u6784\u4E0D\u80FD\u4E3A\u7A7A");if(!a.value.userOrgList.find(e=>e.mainFlag=="Y"))return d.warning("\u5FC5\u987B\u6709\u4E00\u4E2A\u4E3B\u8981\u90E8\u95E8");if(await l.value.validAllEvent()){r.value=!0;let e=null;s.value?e=m.edit(a.value):e=m.add(a.value),e.then(async u=>{r.value=!1,d.success(u.message),n(!1),p("done")}).catch(()=>{r.value=!1})}}})};return(t,e)=>{const u=A;return y(),N(u,{width:1e3,maskClosable:!1,visible:i.visible,"confirm-loading":r.value,forceRender:!0,title:s.value?"\u7F16\u8F91\u7528\u6237":"\u65B0\u5EFA\u7528\u6237","body-style":{paddingBottom:"8px"},"onUpdate:visible":n,onOk:F,class:"common-modal",onClose:e[1]||(e[1]=f=>n(!1))},{default:_(()=>[w(S,{form:a.value,"onUpdate:form":e[0]||(e[0]=f=>a.value=f),ref_key:"userFormRef",ref:l,isUpdate:s.value,superAdminFlag:b.value},null,8,["form","isUpdate","superAdminFlag"])]),_:1},8,["visible","confirm-loading","title"])}}};export{z as default};
