package cn.stylefeng.roses.kernel.manage.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.validator.api.validators.status.StatusValue;
import cn.stylefeng.roses.kernel.validator.api.validators.unique.TableUniqueValue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * API客户端封装类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiClientRequest extends BaseRequest {

    /**
     * API客户端ID
     */
    @NotNull(message = "API客户端ID不能为空", groups = {edit.class, delete.class, updateStatus.class})
    @ChineseDescription("API客户端ID")
    private Long apiClientId;

    /**
     * API客户端名称
     */
    @NotBlank(message = "API客户端名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("API客户端名称")
    @TableUniqueValue(message = "API客户端名称存在重复", groups = {add.class, edit.class},
            tableName = "ent_api_client",
            columnName = "api_client_name",
            idFieldName = "api_client_id",
            excludeLogicDeleteItems = true)
    private String apiClientName;

    /**
     * API客户端编号
     */
    @NotBlank(message = "API客户端编号不能为空", groups = {add.class, edit.class})
    @ChineseDescription("API客户端编号")
    @TableUniqueValue(message = "API客户端编码存在重复", groups = {add.class, edit.class},
            tableName = "ent_api_client",
            columnName = "api_client_code",
            idFieldName = "api_client_id",
            excludeLogicDeleteItems = true)
    private String apiClientCode;

    /**
     * API客户端秘钥明文，用来获取API调用的token
     */
    @NotBlank(message = "API客户端秘钥明文，用来获取API调用的token不能为空", groups = {add.class, edit.class})
    @ChineseDescription("API客户端秘钥明文，用来获取API调用的token")
    private String apiClientSecret;

    /**
     * 公钥，加密数据用
     */
    @ChineseDescription("公钥，加密数据用")
    private String apiPublicKey;

    /**
     * 私钥，解密数据用
     */
    @ChineseDescription("私钥，解密数据用")
    private String apiPrivateKey;

    /**
     * token过期时间，单位：秒
     */
    @NotNull(message = "token过期时间，单位：秒不能为空", groups = {add.class, edit.class})
    @ChineseDescription("token过期时间，单位：秒")
    private Integer apiClientTokenExpiration;

    /**
     * 排序
     */
    @ChineseDescription("排序")
    @NotNull(message = "排序不能为空", groups = {add.class, edit.class})
    private BigDecimal apiClientSort;

    /**
     * 状态：1-启用，2-禁用
     */
    @NotNull(message = "状态：1-启用，2-禁用不能为空", groups = {add.class, edit.class, updateStatus.class})
    @ChineseDescription("状态：1-启用，2-禁用")
    @StatusValue(message = "状态值不正确", groups = {add.class, edit.class, updateStatus.class})
    private Integer apiClientStatus;

    /**
     * 批量删除API客户端的id集合
     */
    @NotNull(message = "API客户端ID集合不能为空", groups = batchDelete.class)
    @ChineseDescription("API客户端ID集合")
    private List<Long> apiClientIdList;

}
