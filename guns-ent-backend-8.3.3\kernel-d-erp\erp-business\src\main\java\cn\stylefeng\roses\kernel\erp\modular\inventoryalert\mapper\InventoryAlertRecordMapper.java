package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRecord;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRecordRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRecordResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 库存预警记录Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
public interface InventoryAlertRecordMapper extends BaseMapper<InventoryAlertRecord> {

    /**
     * 分页查询预警记录
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<InventoryAlertRecordResponse> selectRecordPage(Page<InventoryAlertRecordResponse> page,
                                                       @Param("request") InventoryAlertRecordRequest request);

    /**
     * 查询预警统计数据
     *
     * @param tenantId 租户ID
     * @return 统计数据
     */
    Map<String, Object> selectStatistics(@Param("tenantId") Long tenantId);

    /**
     * 检查是否存在未处理的相同预警
     *
     * @param ruleId    规则ID
     * @param productId 商品ID
     * @return 未处理预警数量
     */
    @Select("SELECT COUNT(*) FROM erp_inventory_alert_record " +
            "WHERE rule_id = #{ruleId} AND product_id = #{productId} " +
            "AND status IN ('PENDING', 'PROCESSING')")
    int countUnresolvedAlert(@Param("ruleId") Long ruleId, @Param("productId") Long productId);

    /**
     * 根据状态查询预警记录数量
     *
     * @param status   状态
     * @param tenantId 租户ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM erp_inventory_alert_record " +
            "WHERE status = #{status} " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if>")
    int countByStatus(@Param("status") String status, @Param("tenantId") Long tenantId);

    /**
     * 根据预警级别查询预警记录数量
     *
     * @param alertLevel 预警级别
     * @param tenantId   租户ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM erp_inventory_alert_record " +
            "WHERE alert_level = #{alertLevel} " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if>")
    int countByAlertLevel(@Param("alertLevel") String alertLevel, @Param("tenantId") Long tenantId);

    /**
     * 查询指定商品的预警记录
     *
     * @param productId 商品ID
     * @param status    状态（可选）
     * @return 预警记录列表
     */
    List<InventoryAlertRecord> selectByProductId(@Param("productId") Long productId,
                                               @Param("status") String status);

    /**
     * 查询指定规则的预警记录
     *
     * @param ruleId 规则ID
     * @param status 状态（可选）
     * @return 预警记录列表
     */
    List<InventoryAlertRecord> selectByRuleId(@Param("ruleId") Long ruleId,
                                            @Param("status") String status);

    /**
     * 批量更新预警记录状态
     *
     * @param idList      记录ID列表
     * @param status      新状态
     * @param handlerUser 处理人
     * @param handleRemark 处理备注
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("idList") List<Long> idList,
                         @Param("status") String status,
                         @Param("handlerUser") Long handlerUser,
                         @Param("handleRemark") String handleRemark);

    /**
     * 查询需要清理的预警记录
     *
     * @param days 保留天数
     * @return 需要清理的记录ID列表
     */
    @Select("SELECT id FROM erp_inventory_alert_record " +
            "WHERE status IN ('RESOLVED', 'IGNORED') " +
            "AND handle_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    List<Long> selectRecordsToCleanup(@Param("days") int days);

    /**
     * 查询最近的预警记录
     *
     * @param limit    限制数量
     * @param tenantId 租户ID
     * @return 预警记录列表
     */
    List<InventoryAlertRecordResponse> selectRecentRecords(@Param("limit") int limit,
                                                          @Param("tenantId") Long tenantId);
}
