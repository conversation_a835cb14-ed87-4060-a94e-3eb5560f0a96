package cn.stylefeng.roses.kernel.ca.server.modular.sso.pojo;

import lombok.Data;

/**
 * 用在加密传输的用户信息对象
 *
 * <AUTHOR>
 * @date 2022/3/28 17:51
 */
@Data
public class TransferEncryptUser {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 账号
     */
    private String account;

    /**
     * 用户身份证
     */
    private String idCard;

    /**
     * 其他标识
     */
    private String otherFlags;

}
