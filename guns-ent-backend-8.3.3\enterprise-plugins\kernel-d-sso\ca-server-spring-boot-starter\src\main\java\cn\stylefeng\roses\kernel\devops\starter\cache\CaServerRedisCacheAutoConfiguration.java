package cn.stylefeng.roses.kernel.devops.starter.cache;

import cn.stylefeng.roses.kernel.ca.api.pojo.CaClientInfo;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import cn.stylefeng.roses.kernel.ca.server.core.loginuser.logincode.RedisLoginCodeCache;
import cn.stylefeng.roses.kernel.ca.server.core.session.cache.clienttoken.RedisClientTokenCache;
import cn.stylefeng.roses.kernel.ca.server.core.session.cache.logintoken.RedisCaLoginTokenCache;
import cn.stylefeng.roses.kernel.ca.server.core.session.cache.loginuser.RedisCaLoginUserCache;
import cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi;
import cn.stylefeng.roses.kernel.cache.redis.util.CreateRedisTemplateUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Set;

/**
 * 统一认证服务端的配置（基于Redis缓存）
 *
 * <AUTHOR>
 * @date 2021/1/20 16:43
 */
@Configuration
@ConditionalOnClass(name = "org.springframework.data.redis.connection.RedisConnectionFactory")
public class CaServerRedisCacheAutoConfiguration {

    /**
     * loginCode缓存
     * <p>
     * key：    SSO LoginCode，是一个临时的密码校验成功的一个凭证
     * value：  CaLoginUser，单点登录用户信息的一个标识
     *
     * <AUTHOR>
     * @date 2021/1/29 10:10
     */
    @Bean("loginCodeCache")
    public CacheOperatorApi<CaLoginUser> redisLoginCodeCache(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, CaLoginUser> redisTemplate = CreateRedisTemplateUtil.createObject(redisConnectionFactory);
        return new RedisLoginCodeCache(redisTemplate);
    }

    /**
     * 存放token的缓存
     * <p>
     * key：    用户id
     * value：  用户的所有CAID，用户在一个机器登录一次单点就会有一个CAID
     *
     * <AUTHOR>
     * @date 2021/1/29 10:10
     */
    @Bean("caLoginTokenCache")
    public CacheOperatorApi<Set<String>> redisCaLoginTokenCache(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Set<String>> redisTemplate = CreateRedisTemplateUtil.createObject(redisConnectionFactory);
        return new RedisCaLoginTokenCache(redisTemplate);
    }

    /**
     * 存放当前登录用户的缓存
     * <p>
     * key：    CAID
     * value：  CAID对用的用户登录信息
     *
     * <AUTHOR>
     * @date 2021/1/29 10:10
     */
    @Bean("caLoginUserCache")
    public CacheOperatorApi<CaLoginUser> redisCaLoginUserCache(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, CaLoginUser> redisTemplate = CreateRedisTemplateUtil.createObject(redisConnectionFactory);
        return new RedisCaLoginUserCache(redisTemplate);
    }

    /**
     * 本缓存为了存用户一次登录后，单点过的所有业务系统的token
     * <p>
     * key：   CAID，用户单点成功的后在单点服务器的唯一标识
     * value： 用户所有在业务端单点的token，一般这个缓存是用在单点退出时候清除业务缓存
     *
     * <AUTHOR>
     * @date 2022/5/20 10:17
     */
    @Bean("clientTokenCache")
    public CacheOperatorApi<Set<CaClientInfo>> redisClientTokenCache(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Set<CaClientInfo>> redisTemplate = CreateRedisTemplateUtil.createObject(redisConnectionFactory);
        return new RedisClientTokenCache(redisTemplate);
    }

}
