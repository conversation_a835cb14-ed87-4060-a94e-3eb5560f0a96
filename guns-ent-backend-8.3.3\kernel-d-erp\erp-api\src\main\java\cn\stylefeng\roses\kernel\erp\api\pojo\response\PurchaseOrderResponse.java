package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单响应参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderResponse extends BaseResponse {

    /**
     * 入库单ID
     */
    @ChineseDescription("入库单ID")
    private Long id;

    /**
     * 入库单号
     */
    @ChineseDescription("入库单号")
    private String orderNo;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 供应商经营方式
     */
    @ChineseDescription("供应商经营方式")
    private String businessMode;

    /**
     * 供应商经营方式名称
     */
    @ChineseDescription("供应商经营方式名称")
    private String businessModeName;

    /**
     * 状态：DRAFT(草稿)、CONFIRMED(已确认)、COMPLETED(已完成)
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 状态名称
     */
    @ChineseDescription("状态名称")
    private String statusName;

    /**
     * 总金额
     */
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

    /**
     * 付款方式
     */
    @ChineseDescription("付款方式")
    private String paymentMethod;

    /**
     * 付款方式名称
     */
    @ChineseDescription("付款方式名称")
    private String paymentMethodName;

    /**
     * 付款账户
     */
    @ChineseDescription("付款账户")
    private String paymentAccount;

    /**
     * 订单日期
     */
    @ChineseDescription("订单日期")
    private LocalDate orderDate;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 采购入库单明细列表
     */
    @ChineseDescription("采购入库单明细列表")
    private List<PurchaseOrderDetailResponse> detailList;

    /**
     * 明细数量统计
     */
    @ChineseDescription("明细数量统计")
    private Integer detailCount;

    /**
     * 商品种类数量
     */
    @ChineseDescription("商品种类数量")
    private Integer productCount;

    /**
     * 总数量
     */
    @ChineseDescription("总数量")
    private BigDecimal totalQuantity;

}