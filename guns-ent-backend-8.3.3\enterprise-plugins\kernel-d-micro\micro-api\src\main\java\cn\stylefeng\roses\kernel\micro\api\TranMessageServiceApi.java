package cn.stylefeng.roses.kernel.micro.api;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.micro.api.pojo.TranMessage;
import cn.stylefeng.roses.kernel.micro.api.pojo.request.TranMessageRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 消息服务的接口
 *
 * <AUTHOR>
 * @date 2018/4/16 21:47
 */
@RequestMapping("/api/messageService")
public interface TranMessageServiceApi {

    /**
     * 预存储消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:34
     */
    @RequestMapping(value = "/preSaveMessage", method = RequestMethod.POST)
    TranMessage preSaveMessage(@RequestBody TranMessageRequest reliableMessage);

   /**
    * 确认并发送消息
    *
    * <AUTHOR>
    * @date 2021/5/18 13:34
    */
    @RequestMapping("/confirmAndSendMessage")
    void confirmAndSendMessage(@RequestParam("messageId") String messageId);

    /**
     * 存储并发送消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:34
     */
    @RequestMapping("/saveAndSendMessage")
    void saveAndSendMessage(@RequestBody TranMessageRequest reliableMessage);

    /**
     * 直接发送消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:34
     */
    @RequestMapping("/directSendMessage")
    void directSendMessage(@RequestBody TranMessageRequest reliableMessage);

    /**
     * 重发消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/reSendMessage")
    void reSendMessage(@RequestBody TranMessageRequest reliableMessage);

    /**
     * 根据messageId重发某条消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/reSendMessageByMessageId")
    void reSendMessageByMessageId(@RequestParam("messageId") String messageId);

    /**
     * 将消息标记为死亡消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/setMessageToAreadlyDead")
    void setMessageToAlreadlyDead(@RequestParam("messageId") String messageId);

    /**
     * 根据消息ID获取消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/getMessageByMessageId")
    TranMessage getMessageByMessageId(@RequestParam("messageId") String messageId);

    /**
     * 根据消息ID删除消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/deleteMessageByMessageId")
    void deleteMessageByMessageId(@RequestParam("messageId") String messageId);

    /**
     * 根据业务id删除消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/deleteMessageByBizId")
    void deleteMessageByBizId(@RequestParam("bizId") Long bizId);

    /**
     * 重发某个消息队列中的全部已死亡的消息
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/reSendAllDeadMessageByQueueName")
    void reSendAllDeadMessageByQueueName(@RequestParam("queueName") String queueName);

    /**
     * 分页获取待发送超时的数据
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/listPageWaitConfirmTimeOutMessages")
    PageResult<TranMessage> listPageWaitConfirmTimeOutMessages(@RequestBody TranMessageRequest pageParam);

    /**
     * 分页获取发送中超时的数据
     *
     * <AUTHOR>
     * @date 2021/5/18 13:35
     */
    @RequestMapping("/listPageSendingTimeOutMessages")
    PageResult<TranMessage> listPageSendingTimeOutMessages(@RequestBody TranMessageRequest pageParam);

}
