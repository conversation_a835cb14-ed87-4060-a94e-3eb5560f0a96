@echo off
echo ========================================
echo 区域整合功能集成测试执行脚本
echo ========================================
echo.

echo 正在执行区域整合功能集成测试...
echo.

REM 切换到项目目录
cd /d "%~dp0"

REM 执行Maven测试命令
mvn clean test -Dtest=RegionIntegrationTestSuite -Dspring.profiles.active=test

echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 集成测试执行成功！
    echo ========================================
    echo.
    echo 测试覆盖的功能：
    echo - 区域管理功能（需求1.5, 2.5）
    echo - 供应商-区域关联功能（需求1.1-1.4, 3.3, 4.3）
    echo - 客户-区域关联功能（需求2.1-2.4, 3.3, 4.3）
    echo - 区域筛选功能（需求3.1-3.5）
    echo - 数据完整性验证（需求4.5）
    echo.
) else (
    echo ========================================
    echo 集成测试执行失败！
    echo ========================================
    echo 请检查测试日志以获取详细错误信息。
    echo.
)

pause