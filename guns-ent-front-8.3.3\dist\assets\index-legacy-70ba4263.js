System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-16a1b89e.js","./index-legacy-b540c599.js","./index-legacy-16f295ac.js","./SysDictTypeApi-legacy-1047ef23.js"],(function(e,a){"use strict";var t,l,d,i,n,s,c,u,o,r,v,p,f,y,h,m,g;return{setters:[e=>{t=e._,l=e.r,d=e.o,i=e.X,n=e.k,s=e.a,c=e.c,u=e.d,o=e.w,r=e.b,v=e.g,p=e.t,f=e.C,y=e.aO,h=e.be,m=e.a0},null,null,null,null,e=>{g=e.S}],execute:function(){var a=document.createElement("style");a.textContent=".popover[data-v-d11fa738]{margin-right:20px;display:inline-block}.box[data-v-d11fa738]{width:100%;display:flex;position:relative}.title[data-v-d11fa738]{position:absolute;right:10px}.all[data-v-d11fa738]{margin-left:8px}.name[data-v-d11fa738]{cursor:pointer}.down-title[data-v-d11fa738]{display:inline-block;padding:4px 10px;border-radius:5px;color:#000;font-size:14px}.down-title[data-v-d11fa738]:hover{background:#e9f3f8}\n",document.head.appendChild(a);const x={class:"popover"},b={class:"box"},k={class:"all"},_={style:{"min-width":"150px","max-height":"150px",overflow:"auto","margin-left":"-16px"}},w={class:"name down-title"},N=t({__name:"index",props:{fieldNames:{type:Object,default:{children:"children",title:"dictName",key:"dictId"}},value:{type:Array,default:[]},name:{type:String,default:""},dictTypeId:{type:String,default:""}},emits:["update:value","change"],setup(e,{emit:a}){const t=e,m=a,N=l(0),j=l(!1),I=l(!1),C=l([]),T=l([]);d((()=>{t.dictTypeId&&S()}));const S=async()=>{T.value=await g.getDictListByParams({dictTypeId:t.dictTypeId})},U=()=>{m("update:value",C.value),m("change"),I.value=!1},E=()=>{if(j.value)C.value=[];else{let e=K();C.value=e}},K=()=>{let e=[];return T.value.forEach((a=>{e.push(a[t.fieldNames.key]),a[t.fieldNames.children]&&a[t.fieldNames.children].length>0&&a[t.fieldNames.children].forEach((a=>{e.push(a[t.fieldNames.key])}))})),e},z=e=>{};return i(C,(()=>{let e=K();N.value=C.value.length,C.value.length>0&&C.value.length==e.length?j.value=!0:j.value=!1})),(a,t)=>{const l=f,d=y,i=n("down-outlined"),m=h;return s(),c("div",x,[u(m,{placement:"bottomLeft",visible:I.value,"onUpdate:visible":t[2]||(t[2]=e=>I.value=e),onVisibleChange:z},{title:o((()=>[r("div",b,[r("div",k,[u(l,{checked:j.value,"onUpdate:checked":t[0]||(t[0]=e=>j.value=e),onClick:E},{default:o((()=>t[3]||(t[3]=[v("全选")]))),_:1,__:[3]},8,["checked"])]),r("div",{class:"title"},[r("a",{onClick:U},"确定")])])])),content:o((()=>[r("div",_,[u(d,{checkedKeys:C.value,"onUpdate:checkedKeys":t[1]||(t[1]=e=>C.value=e),checkable:"","tree-data":T.value,"field-names":e.fieldNames,class:"tree"},null,8,["checkedKeys","tree-data","field-names"])])])),default:o((()=>[r("div",w,[v(p(e.name)+" "+p(N.value>0?"("+N.value+")":"")+" ",1),u(i,{style:{"font-size":"10px"}})])])),_:1},8,["visible"])])}}},[["__scopeId","data-v-d11fa738"]]),j={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const a=l([]);return(e,t)=>{const l=N,d=m;return s(),c("div",j,[u(d,{title:"字典popover",bordered:!1},{default:o((()=>[u(l,{name:"性别选择",value:a.value,"onUpdate:value":t[0]||(t[0]=e=>a.value=e),dictTypeId:"1348235720908619811"},null,8,["value"])])),_:1})])}}})}}}));
