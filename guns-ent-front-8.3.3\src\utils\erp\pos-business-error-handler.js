/**
 * POS业务错误处理器
 * 
 * 专门处理POS系统的业务逻辑错误，提供针对性的解决方案
 *
 * <AUTHOR>
 * @since 2025/08/01 22:00
 */
import { PosFeedback } from './pos-feedback';
import { Modal } from 'ant-design-vue';
import { h } from 'vue';

export class PosBusinessErrorHandler {

  // 业务错误类型
  static BUSINESS_ERROR_TYPES = {
    PRODUCT_STOCK: 'product_stock',
    PRODUCT_PRICE: 'product_price',
    ORDER_STATUS: 'order_status',
    PAYMENT_PROCESS: 'payment_process',
    MEMBER_INFO: 'member_info',
    DISCOUNT_INVALID: 'discount_invalid',
    INVENTORY_LOCK: 'inventory_lock'
  };

  // 错误处理策略
  static ERROR_STRATEGIES = {
    USER_CONFIRM: 'user_confirm',
    AUTO_ADJUST: 'auto_adjust',
    ALTERNATIVE_SUGGEST: 'alternative_suggest',
    MANUAL_INTERVENTION: 'manual_intervention',
    RETRY_WITH_DELAY: 'retry_with_delay'
  };

  /**
   * 处理业务错误
   * @param {Object} error 错误对象
   * @param {Object} context 上下文信息
   * @returns {Promise<Object>} 处理结果
   */
  static async handleBusinessError(error, context = {}) {
    const { code, message, data } = error;
    
    // 根据错误码确定处理策略
    const strategy = this.determineStrategy(code, data, context);
    
    // 执行处理策略
    return await this.executeStrategy(strategy, error, context);
  }

  /**
   * 确定处理策略
   */
  static determineStrategy(errorCode, errorData, context) {
    switch (errorCode) {
      case 'POS_PRODUCT_STOCK_001': // 库存不足
        return this.ERROR_STRATEGIES.USER_CONFIRM;
      
      case 'POS_PRODUCT_PRICE_002': // 价格变更
        return this.ERROR_STRATEGIES.USER_CONFIRM;
      
      case 'POS_PAYMENT_PROCESS_001': // 支付失败
        return this.ERROR_STRATEGIES.RETRY_WITH_DELAY;
      
      case 'POS_MEMBER_QUERY_001': // 会员不存在
        return this.ERROR_STRATEGIES.ALTERNATIVE_SUGGEST;
      
      case 'POS_ORDER_STATUS_001': // 订单状态错误
        return this.ERROR_STRATEGIES.MANUAL_INTERVENTION;
      
      case 'POS_STOCK_LOCKED_001': // 库存锁定
        return this.ERROR_STRATEGIES.RETRY_WITH_DELAY;
      
      default:
        return this.ERROR_STRATEGIES.USER_CONFIRM;
    }
  }

  /**
   * 执行处理策略
   */
  static async executeStrategy(strategy, error, context) {
    switch (strategy) {
      case this.ERROR_STRATEGIES.USER_CONFIRM:
        return await this.handleUserConfirm(error, context);
      
      case this.ERROR_STRATEGIES.AUTO_ADJUST:
        return await this.handleAutoAdjust(error, context);
      
      case this.ERROR_STRATEGIES.ALTERNATIVE_SUGGEST:
        return await this.handleAlternativeSuggest(error, context);
      
      case this.ERROR_STRATEGIES.MANUAL_INTERVENTION:
        return await this.handleManualIntervention(error, context);
      
      case this.ERROR_STRATEGIES.RETRY_WITH_DELAY:
        return await this.handleRetryWithDelay(error, context);
      
      default:
        return { success: false, action: 'unknown' };
    }
  }

  /**
   * 处理用户确认策略
   */
  static async handleUserConfirm(error, context) {
    const { code, message, data } = error;
    
    switch (code) {
      case 'POS_PRODUCT_STOCK_001':
        return await this.handleStockInsufficient(data, context);
      
      case 'POS_PRODUCT_PRICE_002':
        return await this.handlePriceChanged(data, context);
      
      default:
        return await this.showGenericConfirmDialog(message, context);
    }
  }

  /**
   * 处理库存不足
   */
  static async handleStockInsufficient(data, context) {
    const { productName, currentStock, requiredQuantity } = data;
    
    return new Promise((resolve) => {
      Modal.confirm({
        title: '库存不足',
        content: h('div', [
          h('p', `商品 "${productName}" 库存不足`),
          h('p', `当前库存：${currentStock}`),
          h('p', `需要数量：${requiredQuantity}`),
          h('p', '请选择处理方式：')
        ]),
        okText: `调整为 ${currentStock}`,
        cancelText: '移除商品',
        onOk() {
          resolve({
            success: true,
            action: 'adjust_quantity',
            data: { newQuantity: currentStock }
          });
        },
        onCancel() {
          resolve({
            success: true,
            action: 'remove_product',
            data: { productName }
          });
        }
      });
    });
  }

  /**
   * 处理价格变更
   */
  static async handlePriceChanged(data, context) {
    const { productName, oldPrice, newPrice } = data;
    
    return new Promise((resolve) => {
      Modal.confirm({
        title: '商品价格已变更',
        content: h('div', [
          h('p', `商品 "${productName}" 价格已发生变更`),
          h('p', `原价格：¥${oldPrice}`),
          h('p', `新价格：¥${newPrice}`),
          h('p', '是否按新价格继续？')
        ]),
        okText: '按新价格继续',
        cancelText: '移除商品',
        onOk() {
          resolve({
            success: true,
            action: 'accept_new_price',
            data: { newPrice }
          });
        },
        onCancel() {
          resolve({
            success: true,
            action: 'remove_product',
            data: { productName }
          });
        }
      });
    });
  }

  /**
   * 处理自动调整策略
   */
  static async handleAutoAdjust(error, context) {
    const { code, data } = error;
    
    switch (code) {
      case 'POS_PRODUCT_STOCK_001':
        // 自动调整数量为可用库存
        PosFeedback.info(`已自动调整 ${data.productName} 数量为 ${data.currentStock}`);
        return {
          success: true,
          action: 'auto_adjust_quantity',
          data: { newQuantity: data.currentStock }
        };
      
      default:
        return { success: false, action: 'auto_adjust_failed' };
    }
  }

  /**
   * 处理替代建议策略
   */
  static async handleAlternativeSuggest(error, context) {
    const { code, data } = error;
    
    switch (code) {
      case 'POS_MEMBER_QUERY_001':
        return await this.handleMemberNotFound(data, context);
      
      default:
        return { success: false, action: 'no_alternative' };
    }
  }

  /**
   * 处理会员不存在
   */
  static async handleMemberNotFound(data, context) {
    const { memberInfo } = data;
    
    return new Promise((resolve) => {
      Modal.confirm({
        title: '会员不存在',
        content: h('div', [
          h('p', `未找到会员信息：${memberInfo}`),
          h('p', '请选择处理方式：')
        ]),
        okText: '创建新会员',
        cancelText: '继续非会员结算',
        onOk() {
          resolve({
            success: true,
            action: 'create_new_member',
            data: { memberInfo }
          });
        },
        onCancel() {
          resolve({
            success: true,
            action: 'continue_without_member',
            data: {}
          });
        }
      });
    });
  }

  /**
   * 处理手动干预策略
   */
  static async handleManualIntervention(error, context) {
    const { message, data } = error;
    
    PosFeedback.notifyError(
      '需要手动处理',
      message,
      {
        duration: 0,
        btn: h('a-button', {
          type: 'primary',
          size: 'small',
          onClick: () => {
            // 可以打开详细处理页面或联系管理员
            this.openManualHandlingDialog(error, context);
          }
        }, '处理')
      }
    );
    
    return {
      success: false,
      action: 'manual_intervention_required',
      data: { error, context }
    };
  }

  /**
   * 处理延迟重试策略
   */
  static async handleRetryWithDelay(error, context) {
    const { code, data } = error;
    const { retryCount = 0, maxRetries = 3 } = context;
    
    if (retryCount >= maxRetries) {
      PosFeedback.error('重试次数已达上限，请手动处理');
      return { success: false, action: 'retry_exhausted' };
    }
    
    // 计算延迟时间
    const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
    
    PosFeedback.info(`${delay / 1000}秒后自动重试...`);
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return {
      success: true,
      action: 'retry',
      data: { 
        retryCount: retryCount + 1,
        delay 
      }
    };
  }

  /**
   * 显示通用确认对话框
   */
  static async showGenericConfirmDialog(message, context) {
    return new Promise((resolve) => {
      Modal.confirm({
        title: '业务错误',
        content: message,
        okText: '确定',
        cancelText: '取消',
        onOk() {
          resolve({ success: true, action: 'confirmed' });
        },
        onCancel() {
          resolve({ success: false, action: 'cancelled' });
        }
      });
    });
  }

  /**
   * 打开手动处理对话框
   */
  static openManualHandlingDialog(error, context) {
    Modal.info({
      title: '错误详情',
      width: 600,
      content: h('div', [
        h('h4', '错误信息：'),
        h('p', error.message),
        h('h4', '错误代码：'),
        h('p', error.code),
        h('h4', '上下文信息：'),
        h('pre', { style: 'background: #f5f5f5; padding: 10px; border-radius: 4px;' }, 
          JSON.stringify(context, null, 2)
        ),
        h('h4', '建议处理方式：'),
        h('ul', [
          h('li', '检查相关数据是否正确'),
          h('li', '联系系统管理员'),
          h('li', '查看系统日志获取更多信息')
        ])
      ]),
      okText: '知道了'
    });
  }

  /**
   * 处理支付相关错误
   */
  static async handlePaymentError(error, context) {
    const { code, data } = error;
    
    switch (code) {
      case 'POS_PAYMENT_PROCESS_001':
        return await this.handlePaymentFailed(data, context);
      
      case 'POS_PAYMENT_BALANCE_001':
        return await this.handleInsufficientBalance(data, context);
      
      case 'POS_PAYMENT_TIMEOUT_001':
        return await this.handlePaymentTimeout(data, context);
      
      default:
        return await this.handleGenericPaymentError(error, context);
    }
  }

  /**
   * 处理支付失败
   */
  static async handlePaymentFailed(data, context) {
    const { orderNo, reason } = data;
    
    return new Promise((resolve) => {
      Modal.confirm({
        title: '支付失败',
        content: h('div', [
          h('p', `订单 ${orderNo} 支付失败`),
          h('p', `失败原因：${reason}`),
          h('p', '请选择处理方式：')
        ]),
        okText: '重试支付',
        cancelText: '取消订单',
        onOk() {
          resolve({
            success: true,
            action: 'retry_payment',
            data: { orderNo }
          });
        },
        onCancel() {
          resolve({
            success: true,
            action: 'cancel_order',
            data: { orderNo }
          });
        }
      });
    });
  }

  /**
   * 处理余额不足
   */
  static async handleInsufficientBalance(data, context) {
    const { memberName, currentBalance, requiredAmount } = data;
    const shortfall = requiredAmount - currentBalance;
    
    return new Promise((resolve) => {
      Modal.confirm({
        title: '余额不足',
        content: h('div', [
          h('p', `会员 ${memberName} 余额不足`),
          h('p', `当前余额：¥${currentBalance}`),
          h('p', `需要金额：¥${requiredAmount}`),
          h('p', `差额：¥${shortfall}`),
          h('p', '请选择处理方式：')
        ]),
        okText: '充值后支付',
        cancelText: '更换支付方式',
        onOk() {
          resolve({
            success: true,
            action: 'recharge_and_pay',
            data: { shortfall }
          });
        },
        onCancel() {
          resolve({
            success: true,
            action: 'change_payment_method',
            data: {}
          });
        }
      });
    });
  }

  /**
   * 处理支付超时
   */
  static async handlePaymentTimeout(data, context) {
    const { orderNo, timeoutSeconds } = data;
    
    return new Promise((resolve) => {
      Modal.confirm({
        title: '支付超时',
        content: h('div', [
          h('p', `订单 ${orderNo} 支付超时`),
          h('p', `超时时间：${timeoutSeconds} 秒`),
          h('p', '请选择处理方式：')
        ]),
        okText: '重新支付',
        cancelText: '取消支付',
        onOk() {
          resolve({
            success: true,
            action: 'retry_payment',
            data: { orderNo }
          });
        },
        onCancel() {
          resolve({
            success: true,
            action: 'cancel_payment',
            data: { orderNo }
          });
        }
      });
    });
  }

  /**
   * 处理通用支付错误
   */
  static async handleGenericPaymentError(error, context) {
    PosFeedback.notifyError(
      '支付错误',
      error.message,
      {
        duration: 0,
        btn: h('a-button', {
          type: 'primary',
          size: 'small',
          onClick: () => {
            // 重试支付或联系客服
            this.openPaymentErrorDialog(error, context);
          }
        }, '处理')
      }
    );
    
    return {
      success: false,
      action: 'payment_error_notification',
      data: { error }
    };
  }

  /**
   * 打开支付错误处理对话框
   */
  static openPaymentErrorDialog(error, context) {
    Modal.confirm({
      title: '支付错误处理',
      content: h('div', [
        h('p', '支付过程中发生错误，请选择处理方式：'),
        h('ul', [
          h('li', '检查网络连接'),
          h('li', '确认支付信息'),
          h('li', '联系客服处理')
        ])
      ]),
      okText: '重试支付',
      cancelText: '联系客服',
      onOk() {
        // 重试支付逻辑
        window.location.reload();
      },
      onCancel() {
        // 联系客服逻辑
        PosFeedback.info('请联系客服：400-123-4567');
      }
    });
  }
}
