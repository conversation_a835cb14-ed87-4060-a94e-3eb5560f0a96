<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryMapper">

    <!-- 分页查询库存列表 -->
    <select id="selectInventoryPage" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse">
        SELECT
            i.id,
            i.product_id,
            i.current_stock,
            i.min_stock,
            i.avg_cost,
            i.total_value,
            i.update_time,
            p.product_code,
            p.product_name,
            p.product_short_name,
            p.barcode,
            p.specification,
            p.unit,
            p.pricing_type,
            p.status AS product_status,
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            s.business_mode,
            pc.category_id,
            pc.category_name,
            CASE 
                WHEN p.pricing_type = 'NORMAL' THEN '普通商品'
                WHEN p.pricing_type = 'WEIGHT' THEN '计重商品'
                WHEN p.pricing_type = 'PIECE' THEN '计件商品'
                WHEN p.pricing_type = 'VARIABLE' THEN '不定价商品'
                ELSE p.pricing_type
            END as pricing_type_name,
            CASE 
                WHEN s.business_mode = 'PURCHASE_SALE' THEN '购销'
                WHEN s.business_mode = 'JOINT_VENTURE' THEN '联营'
                WHEN s.business_mode = 'CONSIGNMENT' THEN '代销'
                ELSE s.business_mode
            END as business_mode_name,
            CASE
                WHEN p.status = 'ACTIVE' THEN '启用'
                WHEN p.status = 'INACTIVE' THEN '停用'
                ELSE p.status
            END as product_status_name,
            CASE 
                WHEN i.current_stock &lt;= 0 THEN 'OUT_OF_STOCK'
                WHEN i.current_stock &lt;= i.min_stock THEN 'WARNING'
                ELSE 'NORMAL'
            END as inventory_status,
            CASE 
                WHEN i.current_stock &lt;= 0 THEN '缺货'
                WHEN i.current_stock &lt;= i.min_stock THEN '预警'
                ELSE '正常'
            END as inventory_status_name,
            CASE 
                WHEN i.current_stock &lt;= i.min_stock THEN true
                ELSE false
            END as is_warning
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        <where>
            <if test="request.productId != null">
                AND i.product_id = #{request.productId}
            </if>
            <if test="request.productCode != null and request.productCode != ''">
                AND p.product_code LIKE CONCAT('%', #{request.productCode}, '%')
            </if>
            <if test="request.productName != null and request.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{request.productName}, '%')
            </if>
            <if test="request.barcode != null and request.barcode != ''">
                AND p.barcode LIKE CONCAT('%', #{request.barcode}, '%')
            </if>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.supplierCode != null and request.supplierCode != ''">
                AND s.supplier_code LIKE CONCAT('%', #{request.supplierCode}, '%')
            </if>
            <if test="request.supplierName != null and request.supplierName != ''">
                AND s.supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
            <if test="request.businessModeList != null and request.businessModeList.size() > 0">
                AND s.business_mode IN
                <foreach collection="request.businessModeList" item="mode" open="(" separator="," close=")">
                    #{mode}
                </foreach>
            </if>
            <if test="request.pricingType != null and request.pricingType != ''">
                AND p.pricing_type = #{request.pricingType}
            </if>
            <if test="request.pricingTypeList != null and request.pricingTypeList.size() > 0">
                AND p.pricing_type IN
                <foreach collection="request.pricingTypeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <if test="request.minCurrentStock != null">
                AND i.current_stock >= #{request.minCurrentStock}
            </if>
            <if test="request.maxCurrentStock != null">
                AND i.current_stock &lt;= #{request.maxCurrentStock}
            </if>
            <if test="request.minTotalValue != null">
                AND i.total_value >= #{request.minTotalValue}
            </if>
            <if test="request.maxTotalValue != null">
                AND i.total_value &lt;= #{request.maxTotalValue}
            </if>
            <if test="request.warningOnly != null and request.warningOnly == true">
                AND i.current_stock &lt;= i.min_stock
            </if>
            <if test="request.outOfStockOnly != null and request.outOfStockOnly == true">
                AND i.current_stock &lt;= 0
            </if>
            <if test="request.productStatus != null and request.productStatus != ''">
                AND p.status = #{request.productStatus}
            </if>
            <if test="request.productStatusList != null and request.productStatusList.size() > 0">
                AND p.status IN
                <foreach collection="request.productStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 排除联营商品，因为联营商品不进行库存管理 -->
            AND s.business_mode != 'JOINT_VENTURE'
        </where>
        ORDER BY i.update_time DESC
    </select>

    <!-- 查询预警库存列表 -->
    <select id="selectWarningInventory" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse">
        SELECT
            i.id,
            i.product_id,
            i.current_stock,
            i.min_stock,
            i.avg_cost,
            i.total_value,
            i.update_time,
            p.product_code,
            p.product_name,
            p.product_short_name,
            p.barcode,
            p.specification,
            p.unit,
            p.pricing_type,
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            s.business_mode,
            pc.category_id,
            pc.category_name,
            true as is_warning,
            'WARNING' as inventory_status,
            '预警' as inventory_status_name
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        WHERE
            i.current_stock &lt;= i.min_stock
            AND i.current_stock > 0
            AND s.business_mode != 'JOINT_VENTURE'
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
        ORDER BY (i.current_stock / i.min_stock) ASC, i.update_time DESC
    </select>

    <!-- 查询缺货商品列表 -->
    <select id="selectOutOfStockInventory" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse">
        SELECT
            i.id,
            i.product_id,
            i.current_stock,
            i.min_stock,
            i.avg_cost,
            i.total_value,
            i.update_time,
            p.product_code,
            p.product_name,
            p.product_short_name,
            p.barcode,
            p.specification,
            p.unit,
            p.pricing_type,
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            s.business_mode,
            pc.category_id,
            pc.category_name,
            true as is_warning,
            'OUT_OF_STOCK' as inventory_status,
            '缺货' as inventory_status_name
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        WHERE
            i.current_stock &lt;= 0
            AND s.business_mode != 'JOINT_VENTURE'
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
        ORDER BY i.update_time DESC
    </select>

    <!-- 统计库存总价值 -->
    <select id="getTotalInventoryValue" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(i.total_value), 0)
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        <where>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
            <if test="request.businessModeList != null and request.businessModeList.size() > 0">
                AND s.business_mode IN
                <foreach collection="request.businessModeList" item="mode" open="(" separator="," close=")">
                    #{mode}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <!-- 排除联营商品（如果没有明确指定businessModeList包含联营） -->
            <if test="request.businessModeList == null or request.businessModeList.size() == 0">
                AND s.business_mode != 'JOINT_VENTURE'
            </if>
        </where>
    </select>

    <!-- 统计库存总数量 -->
    <select id="getTotalInventoryQuantity" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(i.current_stock), 0)
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        <where>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
            <if test="request.businessModeList != null and request.businessModeList.size() > 0">
                AND s.business_mode IN
                <foreach collection="request.businessModeList" item="mode" open="(" separator="," close=")">
                    #{mode}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <!-- 排除联营商品（如果没有明确指定businessModeList包含联营） -->
            <if test="request.businessModeList == null or request.businessModeList.size() == 0">
                AND s.business_mode != 'JOINT_VENTURE'
            </if>
        </where>
    </select>

    <!-- 统计商品种类数量 -->
    <select id="getProductCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT i.product_id)
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        <where>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
            <if test="request.businessModeList != null and request.businessModeList.size() > 0">
                AND s.business_mode IN
                <foreach collection="request.businessModeList" item="mode" open="(" separator="," close=")">
                    #{mode}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <!-- 排除联营商品（如果没有明确指定businessModeList包含联营） -->
            <if test="request.businessModeList == null or request.businessModeList.size() == 0">
                AND s.business_mode != 'JOINT_VENTURE'
            </if>
        </where>
    </select>

    <!-- 统计预警商品数量 -->
    <select id="getWarningProductCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT i.product_id)
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        WHERE
            i.current_stock &lt;= i.min_stock
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
            <if test="request.businessModeList != null and request.businessModeList.size() > 0">
                AND s.business_mode IN
                <foreach collection="request.businessModeList" item="mode" open="(" separator="," close=")">
                    #{mode}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <!-- 排除联营商品（如果没有明确指定businessModeList包含联营） -->
            <if test="request.businessModeList == null or request.businessModeList.size() == 0">
                AND s.business_mode != 'JOINT_VENTURE'
            </if>
    </select>

    <!-- 统计缺货商品数量 -->
    <select id="getOutOfStockProductCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT i.product_id)
        FROM
            erp_inventory i
        LEFT JOIN
            erp_product p ON i.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product_category_relation pcr ON p.product_id = pcr.product_id
        LEFT JOIN
            erp_product_category pc ON pcr.category_id = pc.category_id
        WHERE
            i.current_stock &lt;= 0
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
            <if test="request.businessModeList != null and request.businessModeList.size() > 0">
                AND s.business_mode IN
                <foreach collection="request.businessModeList" item="mode" open="(" separator="," close=")">
                    #{mode}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <!-- 排除联营商品（如果没有明确指定businessModeList包含联营） -->
            <if test="request.businessModeList == null or request.businessModeList.size() == 0">
                AND s.business_mode != 'JOINT_VENTURE'
            </if>
    </select>

    <!-- 按供应商统计库存价值 -->
    <select id="getSupplierInventoryValues" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse$SupplierInventoryValue">
        SELECT
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            s.business_mode,
            COALESCE(SUM(i.total_value), 0) as inventory_value,
            COALESCE(SUM(i.current_stock), 0) as inventory_quantity,
            COUNT(DISTINCT i.product_id) as product_count
        FROM
            erp_supplier s
        LEFT JOIN
            erp_product p ON s.supplier_id = p.supplier_id
        LEFT JOIN
            erp_inventory i ON p.product_id = i.product_id
        WHERE
            s.business_mode != 'JOINT_VENTURE'
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
        GROUP BY s.supplier_id, s.supplier_code, s.supplier_name, s.business_mode
        HAVING inventory_value > 0
        ORDER BY inventory_value DESC
    </select>

    <!-- 按商品分类统计库存价值 -->
    <select id="getCategoryInventoryValues" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse$CategoryInventoryValue">
        SELECT
            pc.category_id,
            pc.category_name,
            COALESCE(SUM(i.total_value), 0) as inventory_value,
            COALESCE(SUM(i.current_stock), 0) as inventory_quantity,
            COUNT(DISTINCT i.product_id) as product_count
        FROM
            erp_product_category pc
        LEFT JOIN
            erp_product_category_relation pcr ON pc.category_id = pcr.category_id
        LEFT JOIN
            erp_product p ON pcr.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_inventory i ON p.product_id = i.product_id
        WHERE
            s.business_mode != 'JOINT_VENTURE'
            <if test="request.categoryId != null">
                AND pc.category_id = #{request.categoryId}
            </if>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
        GROUP BY pc.category_id, pc.category_name
        HAVING inventory_value > 0
        ORDER BY inventory_value DESC
    </select>

    <!-- 按经营方式统计库存价值 -->
    <select id="getBusinessModeInventoryValues" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse$BusinessModeInventoryValue">
        SELECT
            s.business_mode,
            CASE 
                WHEN s.business_mode = 'PURCHASE_SALE' THEN '购销'
                WHEN s.business_mode = 'CONSIGNMENT' THEN '代销'
                ELSE s.business_mode
            END as business_mode_name,
            COALESCE(SUM(i.total_value), 0) as inventory_value,
            COALESCE(SUM(i.current_stock), 0) as inventory_quantity,
            COUNT(DISTINCT i.product_id) as product_count
        FROM
            erp_supplier s
        LEFT JOIN
            erp_product p ON s.supplier_id = p.supplier_id
        LEFT JOIN
            erp_inventory i ON p.product_id = i.product_id
        WHERE
            s.business_mode != 'JOINT_VENTURE'
            <if test="request.businessMode != null and request.businessMode != ''">
                AND s.business_mode = #{request.businessMode}
            </if>
        GROUP BY s.business_mode
        HAVING inventory_value > 0
        ORDER BY inventory_value DESC
    </select>

    <!-- 批量更新库存 -->
    <update id="batchUpdateInventory">
        <foreach collection="inventoryList" item="inventory" separator=";">
            UPDATE erp_inventory 
            SET 
                current_stock = #{inventory.currentStock},
                avg_cost = #{inventory.avgCost},
                total_value = #{inventory.totalValue},
                update_time = #{inventory.updateTime}
            WHERE 
                product_id = #{inventory.productId}
        </foreach>
    </update>

    <!-- 根据商品ID列表查询库存信息 -->
    <select id="selectByProductIds" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory">
        SELECT
            i.*
        FROM
            erp_inventory i
        WHERE
            i.product_id IN
            <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
    </select>

</mapper>