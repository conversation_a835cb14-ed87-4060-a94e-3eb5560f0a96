import{U as T}from"./UniversalTree-6547889b.js";import{R as s}from"./regionApi-2c103d88.js";import{_ as B,r as C,L as l,a as I,f as b}from"./index-18a1ea24.js";const k=Object.assign({name:"RegionTree"},{__name:"index",props:{showBadge:{type:Boolean,default:!1},isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["tree-select","tree-data-loaded"],setup(d,{expose:c,emit:r}){const o=d,n=r,t=C(),i={api:s.findTree,lazyLoadApi:s.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},p={key:"regionId",title:"regionName",children:"children",hasChildren:"hasChildren",level:"regionLevel"},f=l(()=>({title:"\u533A\u57DF\u7B5B\u9009",showHeader:o.isSetWidth,showSearch:!0,searchPlaceholder:"\u8BF7\u8F93\u5165\u533A\u57DF\u540D\u79F0\u641C\u7D22",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:o.isSetWidth})),h={selectable:!0,expandable:!0,lazyLoad:!0,defaultExpandLevel:3,allowMultiSelect:!1},u={allowAdd:!1,allowEdit:!1,allowDelete:!1},_=e=>{const{keys:a,nodes:E}=e;n("tree-select",a,{selectedNodes:E})},g=e=>{},m=e=>{},y=e=>{n("tree-data-loaded",e)},S=e=>{console.error("\u533A\u57DF\u6811\u6570\u636E\u52A0\u8F7D\u5931\u8D25:",e)},v=()=>{var e;(e=t.value)==null||e.reload()},x=()=>{var e;return(e=t.value)==null?void 0:e.getSelectedNodes()},w=e=>{var a;(a=t.value)==null||a.setSelectedKeys(e)},L=l(()=>{var e;return((e=t.value)==null?void 0:e.getSelectedNodes())||[]});return c({reload:v,getSelectedNodes:x,setSelectedKeys:w,selectedKeys:L}),(e,a)=>(I(),b(T,{ref_key:"universalTreeRef",ref:t,"data-source":i,"field-mapping":p,"display-config":f.value,"interaction-config":h,"action-config":u,onSelect:_,onExpand:g,onSearch:m,onLoad:y,onLoadError:S},null,8,["display-config"]))}}),A=B(k,[["__scopeId","data-v-7c17cb78"]]);export{A as _};
