# 库存预警功能菜单权限配置说明

## 概述

本文档说明了JavaGuns Enterprise项目中库存预警功能的菜单和权限配置。配置文件为 `inventory_alert_menu.sql`，包含了完整的菜单结构、功能权限和资源权限配置。

## 菜单结构

### 主菜单层级

```
ERP基础资料管理 (1815000000000000010)
└── 库存预警管理 (1815000000000000200)
    ├── 预警规则管理 (1815000000000000210)
    ├── 预警记录管理 (1815000000000000220)
    └── 预警配置管理 (1815000000000000230)
```

### 菜单详情

#### 1. 库存预警管理 (主菜单)
- **菜单ID**: 1815000000000000200
- **菜单编码**: ERP_INVENTORY_ALERT
- **路由路径**: /erp/inventoryAlert
- **图标**: icon-menu-kucunyujing
- **描述**: 库存预警规则配置、预警记录查看、预警处理等功能

#### 2. 预警规则管理 (子菜单)
- **菜单ID**: 1815000000000000210
- **菜单编码**: ERP_ALERT_RULE
- **路由路径**: /erp/inventoryAlert/rule
- **组件路径**: /erp/inventoryAlert/rule/index
- **图标**: icon-menu-yujingguize
- **描述**: 库存预警规则的新增、编辑、删除、启用禁用等管理功能

#### 3. 预警记录管理 (子菜单)
- **菜单ID**: 1815000000000000220
- **菜单编码**: ERP_ALERT_RECORD
- **路由路径**: /erp/inventoryAlert/record
- **组件路径**: /erp/inventoryAlert/record/index
- **图标**: icon-menu-yujingjilu
- **描述**: 库存预警记录的查看、处理、统计分析等功能

#### 4. 预警配置管理 (子菜单)
- **菜单ID**: 1815000000000000230
- **菜单编码**: ERP_ALERT_CONFIG
- **路由路径**: /erp/inventoryAlert/config
- **组件路径**: /erp/inventoryAlert/config/index
- **图标**: icon-menu-yujingpeizhi
- **描述**: 库存预警系统配置、通知设置、检查频率等配置功能

## 功能权限配置

### 预警规则管理权限
| 权限编码 | 权限名称 | 描述 |
|---------|---------|------|
| ERP_ALERT_RULE_VIEW | 查看预警规则 | 查看预警规则列表和详情 |
| ERP_ALERT_RULE_ADD | 新增预警规则 | 创建新的预警规则 |
| ERP_ALERT_RULE_EDIT | 编辑预警规则 | 修改现有预警规则 |
| ERP_ALERT_RULE_DELETE | 删除预警规则 | 删除单个预警规则 |
| ERP_ALERT_RULE_BATCH_DELETE | 批量删除预警规则 | 批量删除多个预警规则 |
| ERP_ALERT_RULE_UPDATE_STATUS | 启用禁用预警规则 | 更新预警规则的启用状态 |
| ERP_ALERT_RULE_MANUAL_CHECK | 手动执行预警检查 | 手动触发预警检查 |
| ERP_ALERT_RULE_EXPORT | 导出预警规则 | 导出预警规则数据 |

### 预警记录管理权限
| 权限编码 | 权限名称 | 描述 |
|---------|---------|------|
| ERP_ALERT_RECORD_VIEW | 查看预警记录 | 查看预警记录列表 |
| ERP_ALERT_RECORD_DETAIL | 查看预警详情 | 查看预警记录详细信息 |
| ERP_ALERT_RECORD_HANDLE | 处理预警记录 | 处理单个预警记录 |
| ERP_ALERT_RECORD_BATCH_HANDLE | 批量处理预警记录 | 批量处理多个预警记录 |
| ERP_ALERT_RECORD_DELETE | 删除预警记录 | 删除预警记录 |
| ERP_ALERT_RECORD_STATISTICS | 预警统计分析 | 查看预警统计数据 |
| ERP_ALERT_RECORD_EXPORT | 导出预警记录 | 导出预警记录数据 |

### 预警配置管理权限
| 权限编码 | 权限名称 | 描述 |
|---------|---------|------|
| ERP_ALERT_CONFIG_VIEW | 查看预警配置 | 查看系统预警配置 |
| ERP_ALERT_CONFIG_EDIT | 修改预警配置 | 修改系统预警配置 |
| ERP_ALERT_CONFIG_RESET | 重置预警配置 | 重置为默认配置 |
| ERP_ALERT_CONFIG_TEST_NOTIFY | 测试预警通知 | 测试预警通知功能 |

## 资源权限配置

### Controller资源权限
配置了对应Controller方法的访问权限，包括：
- InventoryAlertRuleController 的所有方法
- InventoryAlertRecordController 的所有方法
- InventoryAlertConfigController 的所有方法

### Service资源权限
配置了核心服务方法的访问权限：
- InventoryAlertCheckService 的预警检查方法

## 使用说明

### 1. 执行SQL脚本
```sql
-- 在数据库中执行配置脚本
source sql/inventory_alert_menu.sql;
```

### 2. 角色权限分配
脚本默认为超级管理员角色（ID: 1339550467939639299）分配了所有权限。

如需为其他角色分配权限，可参考以下SQL模板：
```sql
-- 为角色分配菜单权限
INSERT INTO `sys_role_menu` VALUES (新ID, 角色ID, 应用ID, 菜单ID, 创建时间, 创建人, 更新时间, 更新人);

-- 为角色分配功能权限
INSERT INTO `sys_role_menu_options` VALUES (新ID, 角色ID, 应用ID, 菜单ID, 功能权限ID, 创建时间, 创建人, 更新时间, 更新人);

-- 为角色分配资源权限
INSERT INTO `sys_role_resource` VALUES (新ID, 角色ID, 资源ID, 创建时间, 创建人, 更新时间, 更新人);
```

### 3. 前端路由配置
确保前端路由配置与菜单路径一致：
- `/erp/inventoryAlert/rule/index` - 预警规则管理页面
- `/erp/inventoryAlert/record/index` - 预警记录管理页面
- `/erp/inventoryAlert/config/index` - 预警配置管理页面

### 4. 图标资源
确保以下图标资源存在：
- `icon-menu-kucunyujing` - 库存预警主菜单图标
- `icon-menu-yujingguize` - 预警规则管理图标
- `icon-menu-yujingjilu` - 预警记录管理图标
- `icon-menu-yujingpeizhi` - 预警配置管理图标

## 注意事项

1. **ID规范**: 所有ID都使用1815000000000000xxx格式，避免与现有数据冲突
2. **应用ID**: 使用ERP应用ID (1815000000000000001)
3. **排序**: 菜单排序号按照层级递增，确保显示顺序正确
4. **权限继承**: 子菜单权限需要父菜单权限支持
5. **资源映射**: 资源权限需要与实际Controller方法名称一致

## 扩展说明

如需添加新的预警功能权限：
1. 在 `sys_menu_options` 表中添加新的功能权限
2. 在 `sys_resource` 表中添加对应的资源权限
3. 为相关角色分配新权限
4. 更新前端权限控制逻辑

## 测试验证

执行脚本后，可通过以下方式验证：
1. 登录系统，检查菜单是否正确显示
2. 测试各功能权限是否生效
3. 验证资源访问权限是否正常
4. 检查角色权限分配是否正确
