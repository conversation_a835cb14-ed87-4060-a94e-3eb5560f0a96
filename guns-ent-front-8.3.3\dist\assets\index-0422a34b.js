import{_ as K,r as o,o as S,X as V,k as B,a as k,c as x,d as c,w as p,b as l,g as y,t as g,C as I,aO as U,be as E,a0 as A}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{S as O}from"./SysDictTypeApi-1ce2cbe7.js";const P={class:"popover"},$={class:"box"},j={class:"all"},z={style:{"min-width":"150px","max-height":"150px",overflow:"auto","margin-left":"-16px"}},G={class:"name down-title"},H={__name:"index",props:{fieldNames:{type:Object,default:{children:"children",title:"dictName",key:"dictId"}},value:{type:Array,default:[]},name:{type:String,default:""},dictTypeId:{type:String,default:""}},emits:["update:value","change"],setup(_,{emit:v}){const s=_,i=v,d=o(0),n=o(!1),u=o(!1),t=o([]),m=o([]);S(()=>{s.dictTypeId&&b()});const b=async()=>{m.value=await O.getDictListByParams({dictTypeId:s.dictTypeId})},N=()=>{i("update:value",t.value),i("change"),u.value=!1},C=()=>{if(n.value)t.value=[];else{let a=h();t.value=a}},h=()=>{let a=[];return m.value.forEach(e=>{a.push(e[s.fieldNames.key]),e[s.fieldNames.children]&&e[s.fieldNames.children].length>0&&e[s.fieldNames.children].forEach(f=>{a.push(f[s.fieldNames.key])})}),a},w=a=>{};return V(t,()=>{let a=h();d.value=t.value.length,t.value.length>0&&t.value.length==a.length?n.value=!0:n.value=!1}),(a,e)=>{const f=I,T=U,L=B("down-outlined"),D=E;return k(),x("div",P,[c(D,{placement:"bottomLeft",visible:u.value,"onUpdate:visible":e[2]||(e[2]=r=>u.value=r),onVisibleChange:w},{title:p(()=>[l("div",$,[l("div",j,[c(f,{checked:n.value,"onUpdate:checked":e[0]||(e[0]=r=>n.value=r),onClick:C},{default:p(()=>e[3]||(e[3]=[y("\u5168\u9009")])),_:1,__:[3]},8,["checked"])]),l("div",{class:"title"},[l("a",{onClick:N},"\u786E\u5B9A")])])]),content:p(()=>[l("div",z,[c(T,{checkedKeys:t.value,"onUpdate:checkedKeys":e[1]||(e[1]=r=>t.value=r),checkable:"","tree-data":m.value,"field-names":_.fieldNames,class:"tree"},null,8,["checkedKeys","tree-data","field-names"])])]),default:p(()=>[l("div",G,[y(g(_.name)+" "+g(d.value>0?"("+d.value+")":"")+" ",1),c(L,{style:{"font-size":"10px"}})])]),_:1},8,["visible"])])}}},M=K(H,[["__scopeId","data-v-d11fa738"]]),X={class:"guns-body guns-body-card"},Y={__name:"index",setup(_){const v=o([]);return(s,i)=>{const d=M,n=A;return k(),x("div",X,[c(n,{title:"\u5B57\u5178popover",bordered:!1},{default:p(()=>[c(d,{name:"\u6027\u522B\u9009\u62E9",value:v.value,"onUpdate:value":i[0]||(i[0]=u=>v.value=u),dictTypeId:"1348235720908619811"},null,8,["value"])]),_:1})])}}};export{Y as default};
