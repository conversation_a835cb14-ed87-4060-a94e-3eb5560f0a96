package cn.stylefeng.roses.kernel.pay.api.exception.enums;

import cn.stylefeng.roses.kernel.pay.api.constants.PayConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 商品信息异常相关枚举
 *
 * <AUTHOR>
 * @date 2021/06/23 22:28
 */
@Getter
public enum GoodsExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    GOODS_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "01", "查询结果不存在");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    GoodsExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
