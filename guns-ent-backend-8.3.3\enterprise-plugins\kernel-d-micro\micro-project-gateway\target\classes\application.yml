spring:
  application:
    name: guns-cloud-gateway
  main:
    allow-circular-references: true
  profiles:
    active: local
  cloud:
    nacos:
      discovery:
        enabled: true
        register-enabled: true
        watch-delay: 1000
    gateway:
      routes:
        # 系统管理的路由
        - id: cloud-system
          uri: lb://guns
          predicates:
            - Path=/guns/**
          filters:
            - ApiAuth
            - RewritePath=/guns/(?<segment>.*), /$\{segment}

        # demo-consumer
        - id: example-consumer
          uri: lb://example-consumer
          predicates:
            - Path=/example-consumer/**
          filters:
            - ApiAuth
            - RewritePath=/example-consumer/(?<segment>.*), /$\{segment}

# feign远程调用配置
feign:
  sentinel:
    enabled: true
  client:
    config:
      # 全局配置
      default:
        # NONE不记录任何日志--BASIC仅请求方法URL,状态码执行时间等--HEADERS在BASIC基础上记录header等--FULL记录所有
        loggerLevel: full
  httpclient:
    # 让feign使用apache httpclient做请求；而不是默认的urlConnection
    enabled: true
    # feign的最大连接数
    max-connections: 200
    # feign单个路径的最大连接数
    max-connections-per-route: 50

# actuator配置，给spring boot admin监控用
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always