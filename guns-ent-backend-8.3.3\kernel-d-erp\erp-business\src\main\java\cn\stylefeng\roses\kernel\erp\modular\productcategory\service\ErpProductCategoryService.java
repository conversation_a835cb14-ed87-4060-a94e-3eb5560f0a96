package cn.stylefeng.roses.kernel.erp.modular.productcategory.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProductCategory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductCategoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductCategoryResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 产品分类管理服务接口
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
public interface ErpProductCategoryService extends IService<ErpProductCategory> {

    /**
     * 新增产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     */
    void add(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 编辑产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     */
    void edit(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 删除产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     */
    void delete(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 批量删除产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     */
    void batchDelete(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 查看产品分类详情
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 产品分类详情
     */
    ErpProductCategoryResponse detail(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 分页查询产品分类列表
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 分页结果
     */
    PageResult<ErpProductCategoryResponse> findPage(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 查询产品分类列表
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 产品分类列表
     */
    List<ErpProductCategoryResponse> findList(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 查询产品分类树形结构
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 树形结构列表
     */
    List<ErpProductCategoryResponse> findTree(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 查询产品分类树形结构（懒加载）
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 树形结构列表
     */
    List<ErpProductCategoryResponse> findTreeWithLazy(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 查询产品分类树形结构（用于选择器）
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 树形结构列表
     */
    List<ErpProductCategoryResponse> findTreeForSelector(ErpProductCategoryRequest erpProductCategoryRequest);

    /**
     * 检查分类是否有关联商品
     *
     * @param categoryId 分类ID
     * @return 是否有关联商品
     */
    boolean hasRelatedProducts(Long categoryId);

    /**
     * 获取分类关联的商品数量
     *
     * @param categoryId 分类ID
     * @return 商品数量
     */
    int getProductCount(Long categoryId);
}
