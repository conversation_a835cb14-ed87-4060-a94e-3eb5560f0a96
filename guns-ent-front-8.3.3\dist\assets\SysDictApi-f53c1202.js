import{R as e}from"./index-18a1ea24.js";class d{static add(t){return e.post("/dict/add",t)}static edit(t){return e.post("/dict/edit",t)}static delete(t){return e.post("/dict/delete",t)}static batchDelete(t){return e.post("/dict/batchDelete",t)}static detail(t){return e.getAndLoadData("/dict/detail",t)}static tree(t){return e.getAndLoadData("/dict/getDictTreeList",t)}static list(t){return e.getAndLoadData("/dict/list",t)}static updateDictTree(t){return e.post("/dict/updateDictTree",t)}}export{d as S};
