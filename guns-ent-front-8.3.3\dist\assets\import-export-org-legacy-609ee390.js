System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-8a7fc0f5.js","./index-legacy-198191c1.js","./OrgApi-legacy-c15eac58.js","./index-legacy-53580278.js","./index-legacy-efb51034.js","./index-legacy-b540c599.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js"],(function(e,a){"use strict";var t,l,i,o,r,d,n,s,p,c,u,m,g,v,b,x,y,f,h,w,k,I,_,S,O,N,C,z,j,T,R,F,L,E,U;return{setters:[e=>{t=e._,l=e.r,i=e.s,o=e.k,r=e.a,d=e.f,n=e.w,s=e.aR,p=e.d,c=e.b,u=e.c,m=e.F,g=e.e,v=e.a2,b=e.t,x=e.g,y=e.aS,f=e.h,h=e.m,w=e.bj,k=e.B,I=e.I,_=e.bk,S=e.S,O=e.l,N=e.u,C=e.a9,z=e.H,j=e.v,T=e.n,R=e.a7,F=e.G},e=>{L=e._},e=>{E=e._},null,e=>{U=e.O},null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".import-box[data-v-4ed9e0db]{display:flex;flex-direction:column}.import-header[data-v-4ed9e0db]{width:100%;height:80px;display:flex;align-items:center}.import-body[data-v-4ed9e0db]{flex:auto}.import-back[data-v-4ed9e0db]{border:#a6a6a6 solid 1px}.import-title[data-v-4ed9e0db]{color:#505050;font-size:20px;margin-left:10px}.import-tabs[data-v-4ed9e0db]{width:100%;display:flex}.import-tab-item[data-v-4ed9e0db]{width:215px;height:59px;display:flex;align-items:center;justify-content:center;background-color:#fff;border-radius:8px;border:#a6a6a6 solid 1px;box-shadow:0 0 10px rgba(0,0,0,.5);margin-right:40px;color:#505050;font-size:16px;font-weight:700;cursor:pointer}.import-tab-item[data-v-4ed9e0db]:hover{box-shadow:0 0 9px 2px rgba(42,130,228,.65)}.import-tab-item .icon-font-span[data-v-4ed9e0db]{margin-right:10px}.import-active[data-v-4ed9e0db]{box-shadow:0 0 9px 2px rgba(42,130,228,.65)}.import-download-template[data-v-4ed9e0db]{margin:39px 0}.template-title[data-v-4ed9e0db]{color:#505050;font-size:20px;margin-bottom:24px}.loading[data-v-4ed9e0db]{margin-left:10px}.export-content[data-v-4ed9e0db]{margin:39px 0}.import-body-toop[data-v-4ed9e0db]{color:#a6a6a6;font-size:14px;margin-bottom:20px}.import-table[data-v-4ed9e0db]{width:100%;height:calc(100% - 210px);margin-top:10px}[data-v-4ed9e0db] .ant-table-bordered div.ant-table-body:before{width:0px!important}\n",document.head.appendChild(a);const V={class:"import-header"},q={class:"import-body"},D={class:"import-tabs"},A=["onClick"],B={class:"import-content"},G={class:"import-download-template"},P={class:"import-download-template"},H={class:"export-content"},M={class:"import-header"},J={class:"import-body"},K={class:"import-table"},Q={key:0,style:{color:"#43cf7c"}},W={key:1,style:{color:"#ff8d1a"}},X={key:2,style:{color:"red"}},Y={key:0},Z={key:0,style:{color:"red"}};e("default",t({__name:"import-export-org",emits:["back","backReload"],setup(e,{emit:a}){const t=a,$=l([{name:"导入机构",type:"import"},{name:"导出机构",type:"export"}]),ee=l("import"),ae=l(!1),te=l(!1),le=l({containSubOrg:!1}),ie=i({orgName:[{required:!0,message:"请选择机构",type:"string",trigger:"change"}],containSubOrg:[{required:!0,message:"包含子公司",type:"boolean",trigger:"change"}]}),oe=i({belongOrgName:[{required:!0,message:"请选择所属机构",type:"string",trigger:"change"}]}),re=l(null),de=l(null),ne=l(null),se=l({}),pe=l("export"),ce=l(!1),ue=l({selectOrgList:[]}),me=l([]),ge=l(!1),ve=l([{key:"index",title:"序号",width:60,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>e+1},{title:"操作类型",isShow:!0,width:120,align:"center",dataIndex:"operateType"},{title:"机构名称",isShow:!0,width:150,dataIndex:"orgName"},{title:"机构简称",isShow:!0,width:150,dataIndex:"orgShortName"},{title:"父级机构",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"parentOrgName"},{title:"机构编码",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"orgCode"},{title:"排序",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"orgSort"},{title:"状态",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"statusFlag"},{title:"类型",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"orgType"},{title:"税号",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"taxNo"},{title:"描述",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"remark"},{title:"当前系统机构id",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"orgId"},{title:"主数据系统机构id",isShow:!0,width:150,align:"center",ellipsis:!0,fixed:"right",dataIndex:"masterOrgId"}]),be=()=>{t("back")},xe=async()=>{await U.getExcelTemplate()},ye=e=>{ae.value=!0;let a=new FormData;a.append("file",e.file),U.uploadAndGetPreviewData(a).then((e=>{"00000"==e.code&&(me.value=e.data.previewData,ge.value=e.data.totalSuccess,h.success(e.message),te.value=!0)})).finally((()=>{ae.value=!1}))},fe=e=>{if(pe.value=e,ue.value.selectOrgList=[],"export"==e){const{orgName:e,orgId:a}=le.value;e&&a&&(ue.value.selectOrgList=[{bizId:a,name:e}])}else if("import"==e){const{belongOrgName:e,belongOrgId:a}=se.value;e&&a&&(ue.value.selectOrgList=[{bizId:a,name:e}])}ce.value=!0},he=e=>{const{bizId:a,name:t}=e.selectOrgList[0]||{bizId:"",name:""};"export"==pe.value?(le.value.orgId=a,le.value.orgName=t):"import"==pe.value&&(se.value.belongOrgId=a,se.value.belongOrgName=t)},we=async()=>{await re.value.validate(),await U.exportOrg(le.value)},ke=()=>{te.value=!1},Ie=()=>{ue.value.selectOrgList=[],se.value.belongOrgId=-1,se.value.belongOrgName="无父级",ne.value.clearValidate()},_e=async()=>{-1!==se.value.belongOrgId&&await ne.value.validate();let e=w(me.value);e.forEach((e=>{Object.keys(e).forEach((a=>{var t;e[a]=null===(t=e[a])||void 0===t?void 0:t.submitValue}))}));let a={...se.value,importOrgItemList:e};U.ensureImport(a).then((e=>{h.success(e.message),te.value=!1,t("backReload")}))};return(e,a)=>{const t=o("left-outlined"),l=k,i=I,h=_,w=S,U=O,pe=N,Se=C,Oe=z,Ne=j,Ce=T,ze=o("exclamation-circle-outlined"),je=R,Te=E,Re=L,Fe=F;return r(),d(Fe,{class:"guns-layout-content"},{default:n((()=>[s(p(Ne,{span:24,class:"bg-white padding10 import-box"},{default:n((()=>[c("div",V,[p(l,{shape:"circle",class:"import-back",onClick:be},{icon:n((()=>[p(t)])),_:1}),a[6]||(a[6]=c("span",{class:"import-title"},"导入导出机构",-1))]),c("div",q,[c("div",D,[(r(!0),u(m,null,g($.value,(e=>(r(),u("div",{class:v(["import-tab-item",{"import-active":ee.value==e.type}]),key:e.type,onClick:a=>{var t;(t=e).type!=ee.value&&(ee.value=t.type)}},["import"==e.type?(r(),d(i,{key:0,iconClass:"icon-opt-daoru",title:"导入",fontSize:"33px",color:"var(--primary-color)"})):(r(),d(i,{key:1,iconClass:"icon-opt-daochu",title:"导出",fontSize:"33px",color:"var(--primary-color)"})),c("span",null,b(e.name),1)],10,A)))),128))]),s(c("div",B,[c("div",G,[a[8]||(a[8]=c("div",{class:"template-title"},"Excel模板下载",-1)),p(l,{type:"primary",class:"border-radius flex",onClick:xe},{default:n((()=>[p(i,{iconClass:"icon-opt-xiazai",title:"下载",fontSize:"20px",color:"#fffff"}),a[7]||(a[7]=x(" 下载模板 "))])),_:1,__:[7]})]),c("div",P,[a[10]||(a[10]=c("div",{class:"template-title"},"上传Excel",-1)),p(h,{name:"file",customRequest:ye,showUploadList:!1},{default:n((()=>[p(l,{type:"primary",class:"border-radius flex",disabled:ae.value},{default:n((()=>[p(i,{fontSize:"20px",iconClass:"icon-opt-shangchuan",color:"#fffff"}),a[9]||(a[9]=x(" 上传文件 "))])),_:1,__:[9]},8,["disabled"])])),_:1}),p(w,{spinning:ae.value,delay:100,class:"loading"},null,8,["spinning"])])],512),[[y,"import"==ee.value]]),s(c("div",H,[a[12]||(a[12]=c("div",{class:"template-title"},"导出机构筛选",-1)),p(Oe,{model:le.value,rules:ie,layout:"inline",ref_key:"formRef",ref:re},{default:n((()=>[p(pe,{label:"机构筛选：",name:"orgName"},{default:n((()=>[p(U,{value:le.value.orgName,"onUpdate:value":a[0]||(a[0]=e=>le.value.orgName=e),placeholder:"请选择机构",onFocus:a[1]||(a[1]=e=>fe("export"))},null,8,["value"])])),_:1}),p(pe,{label:"包含子公司：",name:"containSubOrg"},{default:n((()=>[p(Se,{checked:le.value.containSubOrg,"onUpdate:checked":a[2]||(a[2]=e=>le.value.containSubOrg=e)},null,8,["checked"])])),_:1})])),_:1},8,["model","rules"]),p(l,{type:"primary",class:"border-radius flex",style:{"margin-top":"24px"},onClick:we},{default:n((()=>[p(i,{iconClass:"icon-opt-daochu",fontSize:"20px",color:"#fff"}),a[11]||(a[11]=x(" 立即导出 "))])),_:1,__:[11]})],512),[[y,"export"==ee.value]])])])),_:1},512),[[y,!te.value]]),s(p(Ne,{span:24,class:"bg-white padding10 import-box"},{default:n((()=>[c("div",M,[p(l,{shape:"circle",class:"import-back",onClick:ke},{icon:n((()=>[p(t)])),_:1}),a[13]||(a[13]=c("span",{class:"import-title"},"导入结果预览",-1))]),c("div",J,[a[15]||(a[15]=c("pre",{class:"import-body-toop"},"导入成功后，您需要选择这批导入机构的所属机构。\n如果是新增的机构，并且父级为空，则会将这批机构自动挂载到指定机构上。\n如果是修改的机构，导入程序只能修改组织机构名称、简称、编码、排序、状态、类型、税号、备注、对外主数据id。\n如确认数据没问题，请点击确认导入。",-1)),p(Oe,{model:se.value,rules:oe,layout:"inline",ref_key:"importFormRef",ref:ne,style:{height:"60px"}},{default:n((()=>[p(pe,{label:"设置所属机构：",name:"belongOrgName"},{default:n((()=>[p(U,{value:se.value.belongOrgName,"onUpdate:value":a[3]||(a[3]=e=>se.value.belongOrgName=e),placeholder:"请选择机构",onFocus:a[4]||(a[4]=e=>fe("import"))},null,8,["value"])])),_:1}),p(pe,{label:""},{default:n((()=>[p(Ce,null,{default:n((()=>[ge.value?(r(),u("span",{key:0,class:"border-radius",style:{cursor:"pointer",color:"var(--primary-color)"},onClick:Ie},"设置为无父级")):f("",!0),ge.value?(r(),d(l,{key:1,type:"primary",class:"border-radius",onClick:_e},{default:n((()=>a[14]||(a[14]=[x("确认导入")]))),_:1,__:[14]})):f("",!0)])),_:1})])),_:1})])),_:1},8,["model","rules"]),c("div",K,[p(Te,{columns:ve.value,bordered:"",ref_key:"tableRef",ref:de,rowSelection:!1,isPage:!1,dataSource:me.value},{bodyCell:n((({column:e,record:a})=>{var t;return["operateType"==e.dataIndex?(r(),u(m,{key:0},[1==a.operateType.submitValue?(r(),u("span",Q,b(a.operateType.value),1)):f("",!0),2==a.operateType.submitValue?(r(),u("span",W,b(a.operateType.value),1)):f("",!0),3==a.operateType.submitValue?(r(),u("span",X,b(a.operateType.value),1)):f("",!0)],64)):f("",!0),["orgName","orgShortName","parentOrgName","orgCode","orgSort","statusFlag","orgType","taxNo","remark","orgId","masterOrgId"].includes(e.dataIndex)?(r(),u(m,{key:1},[null!==(t=a[e.dataIndex])&&void 0!==t&&t.validateResult?(r(),u("span",Y,b(a[e.dataIndex].value),1)):(r(),d(je,{key:1},{title:n((()=>[x(b(a[e.dataIndex].errorMessage),1)])),default:n((()=>[a[e.dataIndex].value?(r(),u("span",Z,b(a[e.dataIndex].value),1)):(r(),d(ze,{key:1,style:{color:"red"}}))])),_:2},1024))],64)):f("",!0)]})),_:1},8,["columns","dataSource"])])])])),_:1},512),[[y,te.value]]),ce.value?(r(),d(Re,{key:0,visible:ce.value,"onUpdate:visible":a[5]||(a[5]=e=>ce.value=e),title:"选择机构",data:ue.value,showTab:["dept"],onDone:he},null,8,["visible","data"])):f("",!0)])),_:1})}}},[["__scopeId","data-v-4ed9e0db"]]))}}}));
