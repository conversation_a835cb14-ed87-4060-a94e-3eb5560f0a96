import Request from '@/utils/request/request-util';

/**
 * 库存预警记录管理API
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
export class InventoryAlertRecordApi {
  
  /**
   * 分页查询预警记录
   */
  static page(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/page', params);
  }

  /**
   * 分页查询预警记录（别名）
   */
  static findPage(params) {
    return this.page(params);
  }

  /**
   * 查询预警记录详情
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/detail', params);
  }

  /**
   * 处理预警记录
   */
  static handle(params) {
    return Request.post('/erp/inventoryAlert/record/handle', params);
  }

  /**
   * 批量处理预警记录
   */
  static batchHandle(params) {
    return Request.post('/erp/inventoryAlert/record/batchHandle', params);
  }

  /**
   * 忽略预警记录
   */
  static ignore(params) {
    return Request.post('/erp/inventoryAlert/record/ignore', params);
  }

  /**
   * 获取预警统计数据
   */
  static getStatistics(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/statistics', params);
  }

  /**
   * 导出预警记录
   */
  static exportRecords(params) {
    return Request.downLoad('/erp/inventoryAlert/record/export', params);
  }

  /**
   * 查询最近的预警记录
   */
  static getRecentRecords(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/recent', params);
  }

  /**
   * 根据商品ID查询预警记录
   */
  static getRecordsByProduct(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/byProduct', params);
  }

  /**
   * 获取预警记录概览
   */
  static getOverview(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/overview', params);
  }

  /**
   * 删除预警记录
   */
  static delete(params) {
    return Request.post('/erp/inventoryAlert/record/delete', params);
  }

  /**
   * 获取历史记录
   */
  static getHistory(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/history', params);
  }
}
