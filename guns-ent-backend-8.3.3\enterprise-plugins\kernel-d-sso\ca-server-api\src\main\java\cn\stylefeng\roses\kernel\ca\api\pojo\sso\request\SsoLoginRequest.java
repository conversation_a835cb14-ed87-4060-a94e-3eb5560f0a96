package cn.stylefeng.roses.kernel.ca.api.pojo.sso.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * sso认证检测请求参数封装
 *
 * <AUTHOR>
 * @date 2021/1/21 13:51
 */
@Data
public class SsoLoginRequest {

    /**
     * sso单点的客户端
     */
    @NotNull(message = "clientId不能为空")
    private Long clientId;

    /**
     * 单点成功后redirect到业务端的url，这个url是经过urlEncode编码的
     */
    private String ssoCallback;

    /**
     * 登录验证成功后颁发的登录码
     */
    @NotBlank(message = "ssoLoginCode不能为空")
    private String ssoLoginCode;

}
