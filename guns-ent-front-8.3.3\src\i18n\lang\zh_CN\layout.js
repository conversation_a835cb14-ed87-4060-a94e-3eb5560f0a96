/* 主框架 */
export default {
  home: '主页',
  header: {
    profile: '个人中心',
    userinfo: '个人信息',
    password: '修改密码',
    logout: '退出登录'
  },
  footer: {
    website: '官网',
    document: '文档',
    authorization: '授权',
    copyright: 'Copyright © 2022 javaguns.com'
  },
  logout: {
    title: '提示',
    message: '确定要退出登录吗?'
  },
  setting: {
    title: '整体风格设置',
    sideStyles: {
      dark: '暗色侧边栏',
      light: '亮色侧边栏'
    },
    headStyles: {
      light: '亮色顶栏',
      dark: '暗色顶栏',
      primary: '主色顶栏'
    },
    layoutStyles: {
      side: '左侧菜单布局',
      top: '顶部菜单布局',
      mix: '混合菜单布局'
    },
    colors: {
      default: '拂晓蓝',
      dust: '薄暮',
      sunset: '日暮',
      volcano: '火山',
      purple: '酱紫',
      cyan: '明青',
      green: '极光绿',
      geekblue: '极客蓝'
    },
    darkMode: '开启暗黑模式',
    layoutStyle: '导航模式',
    sideMenuStyle: '侧栏双排菜单',
    bodyFull: '内容区域定宽',
    other: '其它配置',
    fixedHeader: '固定顶栏区域',
    fixedSidebar: '固定侧栏区域',
    fixedBody: '固定主体区域',
    logoAutoSize: 'Logo置于顶栏',
    styleResponsive: '移动端响应式',
    colorfulIcon: '侧栏彩色图标',
    sideUniqueOpen: '侧栏排他展开',
    sideInitOpenAll: '默认展开所有侧栏',
    weakMode: '开启色弱模式',
    showFooter: '开启全局页脚',
    showTabs: '开启多页签栏',
    tabsConfig: '多页签配置',
    tabStyle: '页签显示风格',
    tabStyles: {
      default: '默认',
      dot: '圆点',
      card: '卡片'
    },
    transitionName: '路由切换动画',
    transitions: {
      slideRight: '滑动消退',
      slideBottom: '底部消退',
      zoomIn: '放大渐变',
      zoomOut: '缩小渐变',
      fade: '淡入淡出'
    },
    reset: '重置',
    tips: '该功能可实时预览各种布局效果, 修改后会缓存在本地, 下次打开会记忆主题配置.'
  }
};
