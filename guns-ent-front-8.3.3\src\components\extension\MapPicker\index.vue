<template>
  <!-- 新增编辑 -->
  <a-modal
    :width="props.width"
    :maskClosable="false"
    :visible="props.visible"
    :confirm-loading="loading"
    :forceRender="true"
    title="选择位置"
    :body-style="{ padding: 0 }"
    @update:visible="updateVisible"
    footer=""
    class="common-modal"
    @close="updateVisible(false)"
  >
    <MapView v-bind="props" ref="mapRef" :map-key="aMapKey" @done="done" />
  </a-modal>
</template>

<script setup name="MapPicker">
import { ref, onMounted, computed } from 'vue';
import MapView from './map-view.vue';

const props = defineProps({
  visible: Boolean,
  // 弹窗标题
  title: String,
  // 弹窗宽度
  width: {
    type: String,
    default: '780px'
  },
  // 地图的高度
  height: {
    type: String,
    default: '450px'
  },
  // 地图默认中心点
  center: Array,
  // 地图初始缩放级别
  zoom: {
    type: Number,
    default: 11
  },
  // 地图选中后缩放级别
  chooseZoom: {
    type: Number,
    default: 17
  },
  // POI 检索最大数量
  poiSize: {
    type: Number,
    default: 30
  },
  // POI 检索兴趣点类别
  poiType: {
    type: String,
    default: ''
  },
  // POI 检索关键字
  poiKeywords: {
    type: String,
    default: ''
  },
  // POI 检索半径
  poiRadius: {
    type: Number,
    default: 1000
  },
  // 是否返回行政区
  needCity: Boolean,
  // 是否强制选择
  forceChoose: {
    type: Boolean,
    default: true
  },
  // 输入建议的城市范围
  suggestionCity: {
    type: String,
    default: '全国'
  },
  // 地点检索类型, 0: POI 检索, 1: 关键字检索
  searchType: {
    type: Number,
    default: 0,
    validator: value => {
      return [0, 1].includes(value);
    }
  },
  // 搜索框提示文本
  searchPlaceholder: {
    type: String,
    default: '请输入关键字搜索'
  },
  // 地图中心图标地址
  markerSrc: {
    type: String,
    default: 'https://3gimg.qq.com/lightmap/components/locationPicker2/image/marker.png'
  },
  // 高德地图 key
  mapKey: String,
  // 高德地图版本号
  mapVersion: {
    type: String,
    default: '2.0'
  },
  // 地图风格
  mapStyle: String,
  // 是否暗黑主题
  darkMode: Boolean,
});

const emits = defineEmits(['update:visible', 'done']);
// 弹框加载
const loading = ref(false);

const mapRef = ref(false);

// 地图 key
const aMapKey = computed(() => props.mapKey || '698cdf3e7e17e75732d2659001e21660');
// ref
onMounted(() => {});

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};

// 点击保存
const done = result => {
  emits('done', result);
  updateVisible(false);
};

defineExpose({
  mapRef
});
</script>

<style></style>
