System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-94a6fc23.js","./ProductSelector-legacy-b94adcaf.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./InventoryApi-legacy-319e6456.js","./ProductApi-legacy-33feae42.js"],(function(e,t){"use strict";var a,o,l,n,i,r,c,s,d,u,m,g,p,k,S,v,f,b,_,y,w,h,I,x,C,P,M,F,T,U,L,E;return{setters:[e=>{a=e._,o=e.r,l=e.s,n=e.X,i=e.a,r=e.f,c=e.w,s=e.d,d=e.g,u=e.b,m=e.t,g=e.a2,p=e.h,k=e.m,S=e.B,v=e.a3,f=e.Y,b=e.Z,_=e.a0,y=e.u,w=e.y,h=e.v,I=e.G,x=e.W,C=e.J,P=e.$,M=e.H,F=e.I,T=e.U,U=e.M},null,null,e=>{L=e._},null,null,e=>{E=e.I},null],execute:function(){var t=document.createElement("style");t.textContent=".set-min-stock-content[data-v-bf8b34d5]{max-height:70vh;overflow-y:auto}.product-name[data-v-bf8b34d5]{font-weight:500;color:#1890ff}.stock-normal[data-v-bf8b34d5]{color:#52c41a;font-weight:500}.stock-warning[data-v-bf8b34d5]{color:#faad14;font-weight:500}.stock-danger[data-v-bf8b34d5]{color:#ff4d4f;font-weight:500}\n",document.head.appendChild(t);const j={name:"SetMinStockModal",components:{ProductSelector:L},props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:t}){const a=o(null),i=o(!1),r=o({}),c=l({productId:null,minStock:null,maxStock:null,suggestRestock:null,warningLevel:"MEDIUM",remark:""}),s=l({show:!1,isWarning:!1,statusColor:"green",statusText:"库存正常",description:""}),d={productId:[{required:!0,message:"请选择商品",trigger:"change"}],minStock:[{required:!0,message:"请输入最小库存预警值",trigger:"blur"},{type:"number",min:0,message:"最小库存预警值不能小于0",trigger:"blur"}],maxStock:[{type:"number",min:0,message:"最大库存预警值不能小于0",trigger:"blur"},{validator:(e,t)=>t&&c.minStock&&t<=c.minStock?Promise.reject("最大库存预警值必须大于最小库存预警值"):Promise.resolve(),trigger:"blur"}]};n((()=>e.data),(e=>{console.log("SetMinStockModal 接收到数据:",e),e&&e.productId&&(r.value={...e},c.productId=e.productId,c.minStock=e.minStock||0,c.maxStock=e.maxStock||null,console.log("SetMinStockModal 表单数据更新:",c),m())}),{immediate:!0}),n((()=>e.visible),(e=>{console.log("SetMinStockModal visible 变化:",e)})),n([()=>c.minStock,()=>c.warningLevel],(()=>{m()})),n((()=>e.visible),(t=>{t&&(e.data.productId||u())}));const u=()=>{Object.assign(c,{productId:null,minStock:null,suggestRestock:null,warningLevel:"MEDIUM",remark:""}),r.value={},s.show=!1,a.value&&a.value.clearValidate()},m=()=>{if(!r.value.productId||null===c.minStock)return void(s.show=!1);const e=parseFloat(r.value.currentStock)||0,t=parseFloat(c.minStock)||0;s.show=!0,e<=0?(s.isWarning=!0,s.statusColor="red",s.statusText="库存不足",s.description="当前库存为0，已达到缺货状态"):e<=t?(s.isWarning=!0,s.statusColor=g(c.warningLevel),s.statusText=p(c.warningLevel),s.description=`当前库存 ${e} 低于预警值 ${t}，将触发${p(c.warningLevel)}`):(s.isWarning=!1,s.statusColor="green",s.statusText="库存正常",s.description=`当前库存 ${e} 高于预警值 ${t}，库存充足`)},g=e=>({LOW:"orange",MEDIUM:"gold",HIGH:"red"}[e]||"orange"),p=e=>({LOW:"低级预警",MEDIUM:"中级预警",HIGH:"高级预警"}[e]||"预警");return{formRef:a,loading:i,selectedProduct:r,minStockForm:c,warningPreview:s,rules:d,onProductChange:(e,t)=>{t?(r.value={...t},c.minStock=t.minStock||0,c.maxStock=t.maxStock||null,m()):(r.value={},s.show=!1)},getPrecision:()=>"WEIGHT"===r.value.pricingType?3:0,getStep:()=>"WEIGHT"===r.value.pricingType?.001:1,getStockUnit:(e,t)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"件";default:return t||"个"}},formatStock:(e,t)=>{if(!e)return"0";const a="WEIGHT"===t?3:0;return parseFloat(e).toFixed(a)},getStockClass:(e,t)=>{const a=parseFloat(e)||0,o=parseFloat(t)||0;return a<=0?"stock-danger":a<=o?"stock-warning":"stock-normal"},handleCancel:()=>{a.value&&a.value.resetFields(),Object.assign(c,{productId:null,minStock:null,maxStock:null,suggestRestock:null,warningLevel:"MEDIUM",remark:""}),t("update:visible",!1)},handleSubmit:()=>{a.value.validate().then((async()=>{i.value=!0;try{const e={productId:c.productId,minStock:c.minStock,maxStock:c.maxStock,suggestRestock:c.suggestRestock,warningLevel:c.warningLevel,remark:c.remark};await E.setMinStock(e),k.success("预警值设置成功"),t("ok")}catch(e){k.error("预警值设置失败："+(e.message||"未知错误"))}finally{i.value=!1}}))}}}},R={class:"set-min-stock-content"},W={class:"product-name"};e("default",a(j,[["render",function(e,t,a,o,l,n){const k=S,E=v,j=f,H=b,G=_,A=L,O=y,$=w,D=h,z=I,N=x,q=C,B=P,J=M,V=F,X=T,Y=U;return i(),r(Y,{visible:a.visible,title:"设置库存预警值",width:600,maskClosable:!1,onCancel:o.handleCancel,"onUpdate:visible":t[6]||(t[6]=t=>e.$emit("update:visible",t))},{footer:c((()=>[s(k,{onClick:o.handleCancel},{default:c((()=>t[7]||(t[7]=[d("取消")]))),_:1,__:[7]},8,["onClick"]),s(k,{type:"primary",loading:o.loading,onClick:o.handleSubmit},{default:c((()=>t[8]||(t[8]=[d(" 确认设置 ")]))),_:1,__:[8]},8,["loading","onClick"])])),default:c((()=>[u("div",R,[s(E,{message:"预警值设置说明",description:"当商品库存低于或等于预警值时，系统将在库存列表中标记为预警状态，提醒及时补货。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}}),a.data.productId?(i(),r(G,{key:0,title:"商品信息",size:"small",style:{"margin-bottom":"16px"}},{default:c((()=>[s(H,{column:2,bordered:"",size:"small"},{default:c((()=>[s(j,{label:"商品名称"},{default:c((()=>[u("span",W,m(a.data.productName),1)])),_:1}),s(j,{label:"商品编码"},{default:c((()=>[d(m(a.data.productCode),1)])),_:1}),s(j,{label:"当前库存"},{default:c((()=>[u("span",{class:g(o.getStockClass(a.data.currentStock,a.data.minStock))},m(o.formatStock(a.data.currentStock,a.data.pricingType))+" "+m(o.getStockUnit(a.data.pricingType,a.data.unit)),3)])),_:1}),s(j,{label:"最小库存预警值"},{default:c((()=>[d(m(o.formatStock(a.data.minStock,a.data.pricingType))+" "+m(o.getStockUnit(a.data.pricingType,a.data.unit)),1)])),_:1}),s(j,{label:"最大库存预警值"},{default:c((()=>[d(m(a.data.maxStock?o.formatStock(a.data.maxStock,a.data.pricingType)+" "+o.getStockUnit(a.data.pricingType,a.data.unit):"未设置"),1)])),_:1})])),_:1})])),_:1})):p("",!0),s(G,{title:"预警值设置",size:"small"},{default:c((()=>[s(J,{ref:"formRef",model:o.minStockForm,rules:o.rules,layout:"vertical"},{default:c((()=>[a.data.productId?p("",!0):(i(),r(O,{key:0,label:"选择商品",name:"productId",required:""},{default:c((()=>[s(A,{value:o.minStockForm.productId,"onUpdate:value":t[0]||(t[0]=e=>o.minStockForm.productId=e),filter:{businessModeList:["PURCHASE_SALE","CONSIGNMENT"]},onChange:o.onProductChange},null,8,["value","onChange"])])),_:1})),s(z,{gutter:16},{default:c((()=>[s(D,{span:12},{default:c((()=>[s(O,{label:"最小库存预警值",name:"minStock",required:""},{default:c((()=>[s($,{value:o.minStockForm.minStock,"onUpdate:value":t[1]||(t[1]=e=>o.minStockForm.minStock=e),min:0,precision:o.getPrecision(),step:o.getStep(),style:{width:"100%"},placeholder:"请输入最小库存预警值"},{addonAfter:c((()=>[d(m(o.getStockUnit(o.selectedProduct.pricingType,o.selectedProduct.unit)),1)])),_:1},8,["value","precision","step"])])),_:1})])),_:1}),s(D,{span:12},{default:c((()=>[s(O,{label:"最大库存预警值",name:"maxStock"},{default:c((()=>[s($,{value:o.minStockForm.maxStock,"onUpdate:value":t[2]||(t[2]=e=>o.minStockForm.maxStock=e),min:0,precision:o.getPrecision(),step:o.getStep(),style:{width:"100%"},placeholder:"请输入最大库存预警值（可选）"},{addonAfter:c((()=>[d(m(o.getStockUnit(o.selectedProduct.pricingType,o.selectedProduct.unit)),1)])),_:1},8,["value","precision","step"])])),_:1})])),_:1})])),_:1}),s(z,{gutter:16},{default:c((()=>[s(D,{span:12},{default:c((()=>[s(O,{label:"建议补货量",name:"suggestRestock"},{default:c((()=>[s($,{value:o.minStockForm.suggestRestock,"onUpdate:value":t[3]||(t[3]=e=>o.minStockForm.suggestRestock=e),min:0,precision:o.getPrecision(),step:o.getStep(),style:{width:"100%"},placeholder:"建议补货量（可选）"},{addonAfter:c((()=>[d(m(o.getStockUnit(o.selectedProduct.pricingType,o.selectedProduct.unit)),1)])),_:1},8,["value","precision","step"])])),_:1})])),_:1}),s(D,{span:12},{default:c((()=>[s(O,{label:"预警级别",name:"warningLevel"},{default:c((()=>[s(q,{value:o.minStockForm.warningLevel,"onUpdate:value":t[4]||(t[4]=e=>o.minStockForm.warningLevel=e),placeholder:"请选择预警级别"},{default:c((()=>[s(N,{value:"LOW"},{default:c((()=>t[9]||(t[9]=[d("低级预警")]))),_:1,__:[9]}),s(N,{value:"MEDIUM"},{default:c((()=>t[10]||(t[10]=[d("中级预警")]))),_:1,__:[10]}),s(N,{value:"HIGH"},{default:c((()=>t[11]||(t[11]=[d("高级预警")]))),_:1,__:[11]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),s(O,{label:"备注",name:"remark"},{default:c((()=>[s(B,{value:o.minStockForm.remark,"onUpdate:value":t[5]||(t[5]=e=>o.minStockForm.remark=e),placeholder:"请输入备注信息（可选）",rows:3,maxlength:200,showCount:""},null,8,["value"])])),_:1})])),_:1},8,["model","rules"]),o.warningPreview.show?(i(),r(G,{key:0,title:"预警预览",size:"small",style:{"margin-top":"16px",background:"#fafafa"}},{default:c((()=>[s(H,{column:1,size:"small"},{default:c((()=>[s(j,{label:"预警状态"},{default:c((()=>[s(X,{color:o.warningPreview.statusColor},{default:c((()=>[o.warningPreview.isWarning?(i(),r(V,{key:0,iconClass:"icon-opt-jinggao",style:{"margin-right":"4px"}})):p("",!0),d(" "+m(o.warningPreview.statusText),1)])),_:1},8,["color"])])),_:1}),s(j,{label:"预警说明"},{default:c((()=>[d(m(o.warningPreview.description),1)])),_:1}),o.minStockForm.suggestRestock?(i(),r(j,{key:0,label:"建议补货"},{default:c((()=>[d(" 补货至 "+m(o.formatStock(o.minStockForm.suggestRestock,o.selectedProduct.pricingType))+" "+m(o.getStockUnit(o.selectedProduct.pricingType,o.selectedProduct.unit)),1)])),_:1})):p("",!0)])),_:1})])),_:1})):p("",!0)])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-bf8b34d5"]]))}}}));
