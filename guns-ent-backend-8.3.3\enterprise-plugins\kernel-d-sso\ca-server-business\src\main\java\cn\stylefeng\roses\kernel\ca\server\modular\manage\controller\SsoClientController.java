package cn.stylefeng.roses.kernel.ca.server.modular.manage.controller;

import cn.stylefeng.roses.kernel.ca.server.modular.manage.entity.SsoClient;
import cn.stylefeng.roses.kernel.ca.server.modular.manage.pojo.request.SsoClientRequest;
import cn.stylefeng.roses.kernel.ca.server.modular.manage.service.SsoClientService;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 单点登录客户端控制器
 *
 * <AUTHOR>
 * @since 2023/11/05 09:28
 */
@RestController
@ApiResource(name = "单点登录客户端")
public class SsoClientController {

    @Resource
    private SsoClientService ssoClientService;

    /**
     * 添加单点登录客户端
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @PostResource(name = "添加单点登录客户端", path = "/ssoClient/add")
    public ResponseData<SsoClient> add(@RequestBody @Validated(SsoClientRequest.add.class) SsoClientRequest ssoClientRequest) {
        ssoClientService.add(ssoClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除单点登录客户端
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @PostResource(name = "删除单点登录客户端", path = "/ssoClient/delete")
    public ResponseData<?> delete(@RequestBody @Validated(SsoClientRequest.delete.class) SsoClientRequest ssoClientRequest) {
        ssoClientService.del(ssoClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除单点登录客户端
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @PostResource(name = "批量删除单点登录客户端", path = "/ssoClient/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody @Validated(BaseRequest.batchDelete.class) SsoClientRequest ssoClientRequest) {
        ssoClientService.batchDelete(ssoClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑单点登录客户端
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @PostResource(name = "编辑单点登录客户端", path = "/ssoClient/edit")
    public ResponseData<?> edit(@RequestBody @Validated(SsoClientRequest.edit.class) SsoClientRequest ssoClientRequest) {
        ssoClientService.edit(ssoClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 更新单点状态
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @PostResource(name = "更新单点状态", path = "/ssoClient/updateStatus")
    public ResponseData<?> updateStatus(@RequestBody @Validated(SsoClientRequest.updateStatus.class) SsoClientRequest ssoClientRequest) {
        ssoClientService.updateStatus(ssoClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看单点登录客户端详情
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @GetResource(name = "查看单点登录客户端详情", path = "/ssoClient/detail")
    public ResponseData<SsoClient> detail(@Validated(SsoClientRequest.detail.class) SsoClientRequest ssoClientRequest) {
        return new SuccessResponseData<>(ssoClientService.detail(ssoClientRequest));
    }

    /**
     * 获取单点登录客户端列表（带分页）
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    @GetResource(name = "获取单点登录客户端列表（带分页）", path = "/ssoClient/page")
    public ResponseData<PageResult<SsoClient>> page(SsoClientRequest ssoClientRequest) {
        return new SuccessResponseData<>(ssoClientService.findPage(ssoClientRequest));
    }

}
