/**
 * Copyright 2018-2020 stylefeng & fengshuonan (<EMAIL>)
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.stylefeng.roses.kernel.micro.api.exception.enums;


import cn.stylefeng.roses.kernel.micro.api.constants.MicroConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 签名异常
 *
 * <AUTHOR>
 * @date 2018-01-05 14:48
 */
@Getter
public enum GatewayExceptionEnum implements AbstractExceptionEnum {

    /**
     * 网关未知异常
     */
    GATEWAY_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "91", "网关未知异常，具体信息：{}"),

    /**
     * 请求资源不存在
     */
    CACHE_URL_NULL(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "92", "请求资源不存在，请检查资源缓存是否有相应url的规则"),

    /**
     * 存在黑名单ip，IP地址被禁用
     */
    IP_BLACK_LIST_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "93", "IP地址被禁用"),

    /**
     * 存在白名单ip，当前IP地址不在白名单
     */
    IP_WHITE_LIST_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "94", "IP地址被禁用");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    GatewayExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
