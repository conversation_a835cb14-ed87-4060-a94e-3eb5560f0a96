package cn.stylefeng.roses.ent.saas.modular.manager.pojo.request;

import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.validator.api.validators.unique.TableUniqueValue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 租户信息封装类
 *
 * <AUTHOR>
 * @date 2023/08/30 17:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantRequest extends BaseRequest {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("主键id")
    private Long tenantId;

    /**
     * 租户唯一标识
     */
    @NotBlank(message = "租户唯一标识不能为空", groups = {add.class, checkTenantCode.class})
    @ChineseDescription("租户唯一标识")
    @TableUniqueValue(message = "租户唯一标识已经存在，请更换租户编码",
            groups = {add.class},
            tableName = "ent_tenant",
            columnName = "tenant_code",
            idFieldName = "tenant_id",
            excludeLogicDeleteItems = true)
    private String tenantCode;

    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("租户名称")
    @TableUniqueValue(message = "租户名称已经存在，请更换租户编码",
            groups = {add.class, edit.class},
            tableName = "ent_tenant",
            columnName = "tenant_name",
            idFieldName = "tenant_id",
            excludeLogicDeleteItems = true)
    private String tenantName;

    /**
     * 租户logo，存储文件id
     */
    @ChineseDescription("租户logo，存储文件id")
    private Long tenantLogo;

    /**
     * 公司名称
     */
    @ChineseDescription("公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @ChineseDescription("公司地址")
    private String companyAddress;

    /**
     * 统一社会信用代码
     */
    @ChineseDescription("统一社会信用代码")
    private String companySocialCode;

    /**
     * 注册邮箱
     */
    @NotBlank(message = "注册邮箱不能为空", groups = {add.class, edit.class})
    @ChineseDescription("注册邮箱")
    @TableUniqueValue(message = "注册邮箱已经存在，请更换注册邮箱",
            groups = {add.class, edit.class},
            tableName = "ent_tenant",
            columnName = "email",
            idFieldName = "tenant_id",
            excludeLogicDeleteItems = true)
    private String email;

    /**
     * 安全手机（注册时的手机号）
     */
    @NotBlank(message = "安全手机（注册时的手机号）不能为空", groups = {add.class, edit.class})
    @ChineseDescription("安全手机（注册时的手机号）")
    private String safePhone;

    /**
     * 密码，加密方式为MD5
     */
    @NotBlank(message = "密码，加密方式为MD5，不能为空", groups = {add.class})
    @ChineseDescription("密码，加密方式为MD5")
    private String password;

    /**
     * 状态：1-启用，2-禁用
     */
    @NotNull(message = "状态：1-启用，2-禁用不能为空", groups = updateStatus.class)
    @ChineseDescription("状态：1-启用，2-禁用")
    private Integer statusFlag;

    /**
     * 租户开通时间
     */
    @ChineseDescription("租户开通时间")
    @NotNull(message = "租户开通时间不能为空", groups = {add.class})
    private Date activeDate;

    /**
     * 租户到期时间
     */
    @ChineseDescription("租户到期时间")
    @NotNull(message = "租户开通时间不能为空", groups = {add.class})
    private Date expireDate;

    /**
     * 拓展字段
     */
    @ChineseDescription("拓展字段")
    private String expandField;

    //-------------------------------非实体字段-------------------------------
    //-------------------------------非实体字段-------------------------------
    //-------------------------------非实体字段-------------------------------
    /**
     * 租户和功能的绑定
     */
    @ChineseDescription("租户和功能的绑定")
    private List<TenantLinkRequest> tenantLinkList;

    /**
     * 激活状态：Y-已激活，N-未激活
     */
    @ChineseDescription("激活状态：Y-已激活，N-未激活")
    private String activeFlag;

    /**
     * 参数校验分组：校验租户编码是否存在
     */
    public @interface checkTenantCode {
    }

}
