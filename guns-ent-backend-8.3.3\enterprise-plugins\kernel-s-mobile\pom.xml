<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>enterprise-plugins</artifactId>
        <version>8.3.3</version>
    </parent>

    <artifactId>kernel-s-mobile</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--依赖整个系统管理模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--短信发送器-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>sms-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--二维码生成-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>

    </dependencies>

</project>
