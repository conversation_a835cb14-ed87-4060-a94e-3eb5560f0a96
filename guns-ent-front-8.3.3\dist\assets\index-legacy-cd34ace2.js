System.register(["./index-legacy-ee1db0c7.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-efb51034.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,l){"use strict";var a,t,s,n,i,r,d,o,c,u,g,v,h,y,p,x,_,w,f,I;return{setters:[e=>{a=e.r,t=e.a,s=e.c,n=e.d,i=e.w,r=e.b,d=e.g,o=e.F,c=e.f,u=e.h,g=e.at,v=e.aI,h=e.aJ,y=e.I,p=e.l,x=e.B,_=e.n,w=e.U},e=>{f=e._},e=>{I=e._},null,null,null,null],execute:function(){const m={class:"guns-layout",style:{padding:"0 12px"}},S={class:"guns-layout-content"},k={class:"guns-layout"},T={class:"guns-layout-content-application"},b={class:"content-mian"},j={class:"content-mian-header"},F={class:"header-content"},z={class:"header-content-left"},C={class:"content-mian-body"},B={class:"table-content"},E={key:1,class:"org-type"};e("default",{__name:"index",setup(e){const N=v((()=>h((()=>l.import("./org-tree-legacy-926da3e5.js")),void 0))),R=a(!0),U=a(!0),J=a(!1),O=a(!1),P=a([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"orgName",title:"机构名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"orgCode",title:"机构编码",width:100,isShow:!0},{dataIndex:"statusFlag",title:"机构状态",ellipsis:!0,width:100,isShow:!0},{dataIndex:"orgType",title:"机构类型",ellipsis:!0,width:100,isShow:!0},{dataIndex:"orgSort",title:"排序",width:100,isShow:!0},{dataIndex:"createTime",title:"创建时间",width:150,isShow:!0}]),q=a(null),A=a({orgId:null,statusFlag:"",searchText:""}),D=a(null),G=a(null),H=(e,l)=>{A.value.orgId=e[0],D.value=e[0],G.value=l.node.orgName,K()},K=()=>{q.value.reload()};return(e,l)=>{const a=y,v=p,h=x,D=_,G=w,L=I,M=f;return t(),s("div",m,[n(M,{space:"0px","allow-collapse":R.value,resizable:U.value,vertical:J.value,reverse:O.value,"min-size":40,style:{height:"480px","margin-top":"12px"}},{content:i((()=>[r("div",S,[r("div",k,[r("div",T,[r("div",b,[r("div",j,[r("div",F,[r("div",z,[n(D,{size:16},{default:i((()=>[n(v,{value:A.value.searchText,"onUpdate:value":l[0]||(l[0]=e=>A.value.searchText=e),placeholder:"机构名称、编码（回车搜索）",onPressEnter:K,class:"search-input"},{prefix:i((()=>[n(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"]),n(h,{class:"border-radius",onClick:e.clear},{default:i((()=>l[1]||(l[1]=[d("重置")]))),_:1,__:[1]},8,["onClick"])])),_:1})]),l[2]||(l[2]=r("div",{class:"header-content-right"},null,-1))])]),r("div",C,[r("div",B,[n(L,{columns:P.value,where:A.value,rowId:"orgId",ref_key:"tableRef",ref:q,url:"/hrOrganization/page"},{bodyCell:i((({column:e,record:a})=>["statusFlag"==e.dataIndex?(t(),s(o,{key:0},[1==a.statusFlag?(t(),c(G,{key:0,color:"green"},{default:i((()=>l[3]||(l[3]=[d("启用")]))),_:1,__:[3]})):u("",!0),2==a.statusFlag?(t(),c(G,{key:1,color:"red"},{default:i((()=>l[4]||(l[4]=[d("禁用")]))),_:1,__:[4]})):u("",!0)],64)):u("",!0),"orgType"==e.dataIndex?(t(),s("div",E,[1==a.orgType?(t(),c(G,{key:0,color:"green"},{default:i((()=>l[5]||(l[5]=[d("公司")]))),_:1,__:[5]})):u("",!0),2==a.orgType?(t(),c(G,{key:1,color:"red"},{default:i((()=>l[6]||(l[6]=[d("部门")]))),_:1,__:[6]})):u("",!0)])):u("",!0)])),_:1},8,["columns","where"])])])])])])])])),default:i((()=>[n(g(N),{onTreeSelect:H,ref:"orgTreeRef",style:{padding:"12px"}},null,512)])),_:1},8,["allow-collapse","resizable","vertical","reverse"])])}}})}}}));
