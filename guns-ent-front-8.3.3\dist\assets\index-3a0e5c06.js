import{_ as P,s as fe,a5 as ie,r as u,a as y,c as C,b as c,d as m,w as L,f as D,F as M,e as ue,t as B,h as $,I as H,l as q,X as E,aK as ee,g as X,U as ge,o as re,aR as W,aS as G,bg as ye,S as be,L as we,aL as xe,m as Re,j as Ie,T as Se,aA as Le}from"./index-18a1ea24.js";import{_ as Q}from"./index-d0cfb2ce.js";import{_ as se}from"./index-02bf6f00.js";/* empty css              *//* empty css              */import{O as de}from"./OrgApi-021dd6dd.js";function le(d,T,o,r,I){if(T){let e={bizId:d[r],name:d[I],...d};o.find(t=>t.bizId==d[r])||o.push(e)}else o.splice(o.findIndex(e=>e.bizId===d[r]),1)}function oe(d,T,o,r,I){d?T.forEach(e=>{if(!o.find(t=>t.bizId==e[r])){let t={bizId:e[r],name:e[I],...e};o.push(t)}}):T.forEach(e=>{for(let t=o.length-1;t>=0;t--)e[r]==o[t].bizId&&o.splice(t,1)})}function ne(d,T,o,r){let I=[],e=[];return r?o.forEach(t=>{t.subValue==r&&I.push(t)}):I=o,d&&d.length>0&&d.forEach(t=>{I.find(_=>_.bizId==t[T])&&e.push(t[T])}),e}const Te={class:"selected"},ke={class:"selected-search"},Ce={class:"selected-bottom"},$e={key:0,class:"bottom-list"},Ne={key:1},Oe={class:"selected-name"},Fe={class:"selected-del"},De={__name:"selected-list",props:{list:Array},emits:["delete","deleteAll"],setup(d,{emit:T}){const o=d,r=T,I=fe({simpleImage:ie.PRESENTED_IMAGE_SIMPLE}),e=u(""),t=N=>{r("delete",N)},_=()=>{r("deleteAll")};return(N,k)=>{var g;const x=H,f=q,a=ie;return y(),C("div",Te,[c("div",{class:"selected-top"},[k[1]||(k[1]=c("span",null,"\u5DF2\u9009",-1)),c("span",{class:"selected-del"},[c("a",{onClick:_},"\u6E05\u7A7A")])]),c("div",ke,[m(f,{value:e.value,"onUpdate:value":k[0]||(k[0]=R=>e.value=R),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09","allow-clear":""},{prefix:L(()=>[m(x,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",Ce,[o.list?(y(),C("div",$e,[d.list&&d.list.length==0?(y(),D(a,{key:0,image:I.simpleImage},null,8,["image"])):(y(),C("div",Ne,[(y(!0),C(M,null,ue((g=o.list)==null?void 0:g.filter(R=>R.name.includes(e.value)),(R,p)=>(y(),C("div",{class:"list",key:R.id},[c("div",Oe,B(p+1)+". "+B(R.subValueName?R.subValueName+"#":"")+B(R.name),1),c("div",Fe,[m(x,{iconClass:"icon-opt-shanchu",title:"\u5220\u9664",color:"#000",onClick:s=>t(R)},null,8,["onClick"])])]))),128))]))])):$("",!0)])])}}},Y=P(De,[["__scopeId","data-v-b6ea496b"]]);const ze={class:"user-select"},Ke={class:"user-select-org"},Ue={class:"search"},Ae={class:"user-table"},Me={class:"user-select-right"},Be={class:"user-select-user"},Ee={class:"search"},Pe={class:"user-table"},Ve={class:"user-select-list"},je={__name:"select-dicts",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(d,{expose:T,emit:o}){const r=d,I=o,e=u([]),t=u({dictTypeId:""}),_=u({}),N=u(null),k=u([{title:"\u7C7B\u578B",dataIndex:"dictTypeName",isShow:!0,width:120,ellipsis:!0},{title:"\u7F16\u7801",isShow:!0,ellipsis:!0,width:120,dataIndex:"dictTypeCode"}]),x=u([{title:"\u540D\u79F0",isShow:!0,ellipsis:!0,dataIndex:"dictName"},{title:"\u7F16\u7801",isShow:!0,ellipsis:!0,dataIndex:"dictCode"}]),f=u(null),a=u(null),g=w=>{e.value.splice(e.value.findIndex(i=>i.bizId===w.bizId),1)},R=()=>{e.value=[]},p=(w,i,l)=>{N.value=w,t.value.dictTypeId=w.dictTypeId,O()},s=(w,i,l)=>{r.isRadio&&(e.value=[]),le(w,i,e.value,"dictId","dictName")},h=(w,i,l)=>{oe(w,l,e.value,"dictId","dictName")},b=(w,i)=>{f.value.selectedRowKeys=ne(w,i,e.value)},v=()=>{var w;return!((w=t.value)!=null&&w.dictTypeCode)};E(()=>e.value,w=>{b(w,"bizId"),I("selectedChange")},{deep:!0});const O=()=>{ee(()=>{f.value.reload()})},F=()=>{ee(()=>{var w;(w=a.value)==null||w.reload()})};return T({dictList:e}),(w,i)=>{const l=H,V=q,J=se,z=Q;return y(),C("div",ze,[m(z,{width:r.isMobileFlag?"100%":"340px",allowCollapse:!1},{content:L(()=>[c("div",Me,[c("div",Be,[i[4]||(i[4]=c("div",{class:"user-header"},"\u5B57\u5178\u5217\u8868",-1)),c("div",Ee,[m(V,{value:t.value.searchText,"onUpdate:value":i[1]||(i[1]=A=>t.value.searchText=A),placeholder:"\u5B57\u5178\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:O,style:{width:"100%"}},{prefix:L(()=>[m(l,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",Pe,[m(J,{columns:x.value,where:t.value,isShowRowSelect:"",isLoad:v,isSort:!1,isPage:!1,rowId:"dictId",ref_key:"tableRef",ref:f,isRadio:r.isRadio,showTool:!1,url:"/dict/list",onOnSelect:s,onOnSelectAll:h},null,8,["columns","where","isRadio"])])]),c("div",Ve,[m(Y,{list:e.value,"onUpdate:list":i[2]||(i[2]=A=>e.value=A),onDelete:g,onDeleteAll:R},null,8,["list"])])])]),default:L(()=>[c("div",Ke,[i[3]||(i[3]=c("div",{class:"user-header"},"\u5B57\u5178\u7C7B\u578B\u5217\u8868",-1)),c("div",Ue,[m(V,{value:_.value.searchText,"onUpdate:value":i[0]||(i[0]=A=>_.value.searchText=A),placeholder:"\u5B57\u5178\u7C7B\u578B\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:F,style:{width:"100%"}},{prefix:L(()=>[m(l,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",Ae,[m(J,{columns:k.value,isSort:!1,where:_.value,isPage:!1,rowId:"dictTypeId",isRadio:!0,ref_key:"tableTypeRef",ref:a,showTool:!1,url:"/dictType/list",isShowRowSelect:"",onOnSelect:p},null,8,["columns","where"])])])]),_:1},8,["width"])])}}},We=P(je,[["__scopeId","data-v-7f79180d"]]);const Ge={class:"user-select"},He={class:"user-select-item"},Je={class:"search"},Xe={class:"user-table"},qe={class:"user-select-item"},Qe={__name:"select-position",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(d,{expose:T,emit:o}){const r=d,I=o,e=u([]),t=u({searchText:""}),_=u(null),N=u([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0},{dataIndex:"positionName",title:"\u804C\u4F4D\u540D\u79F0",align:"center",width:100,ellipsis:!0,isShow:!0},{dataIndex:"positionCode",title:"\u804C\u4F4D\u7F16\u7801",width:100,align:"center",isShow:!0},{dataIndex:"statusFlag",title:"\u72B6\u6001",align:"center",width:100,isShow:!0}]),k=()=>{_.value.reload()},x=(p,s,h)=>{r.isRadio&&(e.value=[]),le(p,s,e.value,"positionId","positionName")},f=(p,s,h)=>{oe(p,h,e.value,"positionId","positionName")},a=(p,s)=>{var h;(h=_.value)!=null&&h.selectedRowList&&(_.value.selectedRowList=ne(p,s,e.value))},g=p=>{e.value.splice(e.value.findIndex(s=>s.bizId===p.bizId),1)},R=()=>{e.value=[]};return E(()=>e.value,p=>{_.value&&(a(p,"bizId"),I("selectedChange"))},{deep:!0}),T({positionList:e}),(p,s)=>{const h=H,b=q,v=ge,O=se,F=Y,w=Q;return y(),C("div",Ge,[m(w,{width:r.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:L(()=>[c("div",qe,[m(F,{list:e.value,"onUpdate:list":s[2]||(s[2]=i=>e.value=i),onDelete:g,onDeleteAll:R},null,8,["list"])])]),default:L(()=>[c("div",He,[s[5]||(s[5]=c("div",{class:"user-header"},"\u804C\u4F4D\u5217\u8868",-1)),c("div",Je,[m(b,{value:t.value.searchText,"onUpdate:value":s[0]||(s[0]=i=>t.value.searchText=i),placeholder:"\u804C\u4F4D\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:k},{prefix:L(()=>[m(h,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",Xe,[m(O,{columns:N.value,where:t.value,bordered:"",isShowRowSelect:"",isRadio:r.isRadio,rowId:"positionId",ref_key:"tableRef",ref:_,showTool:!1,url:"/hrPosition/page",onOnSelect:x,onOnSelectAll:f,onTableListChange:s[1]||(s[1]=i=>a(i,"positionId"))},{bodyCell:L(({column:i,record:l})=>[i.dataIndex=="statusFlag"?(y(),C(M,{key:0},[l.statusFlag==1?(y(),D(v,{key:0,color:"blue"},{default:L(()=>s[3]||(s[3]=[X("\u542F\u7528")])),_:1,__:[3]})):$("",!0),l.statusFlag==2?(y(),D(v,{key:1,color:"red"},{default:L(()=>s[4]||(s[4]=[X("\u7981\u7528")])),_:1,__:[4]})):$("",!0)],64)):$("",!0)]),_:1},8,["columns","where","isRadio"])])])]),_:1},8,["width"])])}}},Ye=P(Qe,[["__scopeId","data-v-9a44e4d2"]]);const Ze={class:"user-select"},et={class:"user-select-item"},tt={class:"search"},st={class:"user-table"},lt={class:"user-select-item"},ot={__name:"select-role",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(d,{expose:T,emit:o}){const r=d,I=o,e=u([]),t=u({roleName:""}),_=u(null),N=u([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0},{dataIndex:"roleName",title:"\u89D2\u8272\u540D\u79F0",align:"center",width:100,ellipsis:!0,isShow:!0},{dataIndex:"roleCode",title:"\u89D2\u8272\u7F16\u7801",width:100,align:"center",isShow:!0}]),k=()=>{_.value.reload()},x=(p,s,h)=>{r.isRadio&&(e.value=[]),le(p,s,e.value,"roleId","roleName")},f=(p,s,h)=>{oe(p,h,e.value,"roleId","roleName")},a=(p,s)=>{var h;(h=_.value)!=null&&h.selectedRowList&&(_.value.selectedRowList=ne(p,s,e.value))},g=p=>{e.value.splice(e.value.findIndex(s=>s.bizId===p.bizId),1)},R=()=>{e.value=[]};return E(()=>e.value,p=>{_.value&&(a(p,"bizId"),I("selectedChange"))},{deep:!0}),T({roleList:e}),(p,s)=>{const h=H,b=q,v=se,O=Y,F=Q;return y(),C("div",Ze,[m(F,{width:r.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:L(()=>[c("div",lt,[m(O,{list:e.value,"onUpdate:list":s[2]||(s[2]=w=>e.value=w),onDelete:g,onDeleteAll:R},null,8,["list"])])]),default:L(()=>[c("div",et,[s[3]||(s[3]=c("div",{class:"user-header"},"\u89D2\u8272\u5217\u8868",-1)),c("div",tt,[m(b,{value:t.value.roleName,"onUpdate:value":s[0]||(s[0]=w=>t.value.roleName=w),placeholder:"\u89D2\u8272\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:k},{prefix:L(()=>[m(h,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",st,[m(v,{columns:N.value,where:t.value,bordered:"",isShowRowSelect:"",isRadio:r.isRadio,rowId:"roleId",ref_key:"tableRef",ref:_,showTool:!1,url:"/sysRole/page",onOnSelect:x,onOnSelectAll:f,onTableListChange:s[1]||(s[1]=w=>a(w,"roleId"))},{bodyCell:L(({column:w,index:i})=>[w.key=="index"?(y(),C(M,{key:0},[X(B(i+1),1)],64)):$("",!0)]),_:1},8,["columns","where","isRadio"])])])]),_:1},8,["width"])])}}},nt=P(ot,[["__scopeId","data-v-172d71a6"]]);const at={class:"box bgColor"},it={class:"search"},ct={class:"tree-content"},dt={class:"left-tree"},ut={class:"tree-title"},rt={__name:"org-tree",props:{companySearchFlag:{type:Boolean,default:!1},isRadio:{type:Boolean,default:!0}},emits:["treeSelect","treeData","checkedTree"],setup(d,{expose:T,emit:o}){const r=o,I=d,e=u(""),t=u(!1),_=u([]),N=u([]),k=u([]),x=u([]),f=u([]);re(()=>{a()});const a=()=>{x.value=[],t.value=!0,de.tree({searchText:e.value,companySearchFlag:I.companySearchFlag}).then(b=>{e.value&&(k.value=b.data.expandOrgIdList);const v=g(b.data.orgTreeList);_.value=v}).finally(()=>t.value=!1)},g=b=>(b&&b.length>0&&b.forEach(v=>{v.haveSubOrgFlag?v.isLeaf=!1:v.isLeaf=!0,v.children&&v.children.length>0&&(v.children=g(v.children))}),b),R=(b,v)=>{r("treeSelect",b,v)},p=(b,{checked:v,node:O})=>{r("checkedTree",v,O)},s=()=>{e.value||a()},h=b=>(x.value.push(b.eventKey),new Promise(v=>{de.tree({orgParentId:b.dataRef.orgId,companySearchFlag:I.companySearchFlag}).then(O=>{const F=g(O.data.orgTreeList);b.dataRef.children=F,_.value=[..._.value]}).finally(()=>{v()})}));return T({currentSelectKeys:N,checkedKeyss:f}),(b,v)=>{const O=q,F=ye,w=ie,i=be;return y(),C("div",at,[v[5]||(v[5]=c("div",{class:"tree-header"},"\u5168\u90E8\u673A\u6784",-1)),c("div",it,[m(O,{value:e.value,"onUpdate:value":v[0]||(v[0]=l=>e.value=l),placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u540D\u79F0","allow-clear":"",onPressEnter:a,onChange:s},{prefix:L(()=>[m(H,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",ct,[m(i,{tip:"Loading...",spinning:t.value,delay:100},{default:L(()=>[W(c("div",dt,[m(F,{"show-icon":!0,selectedKeys:N.value,"onUpdate:selectedKeys":v[1]||(v[1]=l=>N.value=l),expandedKeys:k.value,"onUpdate:expandedKeys":v[2]||(v[2]=l=>k.value=l),loadedKeys:x.value,"onUpdate:loadedKeys":v[3]||(v[3]=l=>x.value=l),onSelect:R,"load-data":h,"tree-data":_.value,checkedKeys:f.value,"onUpdate:checkedKeys":v[4]||(v[4]=l=>f.value=l),checkable:!I.isRadio,checkStrictly:"",onCheck:p,fieldNames:{children:"children",title:"orgName",key:"orgId",value:"orgId"}},{icon:L(l=>[l.orgType==1?(y(),D(H,{key:0,"icon-class":"icon-nav-gongsi",color:"#43505e",fontSize:"24px"})):$("",!0),l.orgType==2?(y(),D(H,{key:1,"icon-class":"icon-tree-dept",color:"#43505e",fontSize:"24px"})):$("",!0)]),title:L(l=>[c("span",ut,B(l.orgName),1)]),_:1},8,["selectedKeys","expandedKeys","loadedKeys","tree-data","checkedKeys","checkable"])],512),[[G,_.value&&_.value.length>0]]),W(m(w,{class:"empty"},null,512),[[G,_.value&&_.value.length==0]])]),_:1},8,["spinning"])])])}}},ce=P(rt,[["__scopeId","data-v-9ef34d14"]]);const _t={class:"user-select"},pt={class:"user-select-item"},vt={class:"user-select-item"},ht={__name:"select-dept",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(d,{expose:T,emit:o}){const r=d,I=o,e=u([]),t=u(null),_=(f,{node:a})=>{let g={...a,bizId:a.orgId,name:a.orgName,children:null};const R=e.value.find(p=>p.bizId===a.orgId);r.isRadio?e.value=R?[]:[g]:R?k(g):e.value.push(g)},N=(f,a)=>{a.bizId=a.orgId,a.name=a.orgName,f?e.value.find(g=>g.bizId==a.orgId)||e.value.push(a):k(a)},k=f=>{e.value.splice(e.value.findIndex(a=>a.bizId===f.bizId),1)},x=()=>{e.value=[]};return E(()=>e.value,f=>{I("selectedChange"),ee(()=>{var a;!r.isRadio&&t.value&&(t.value.checkedKeyss=(a=e.value)==null?void 0:a.map(g=>g.bizId))})},{deep:!0}),T({deptList:e}),(f,a)=>{const g=Y,R=Q;return y(),C("div",_t,[m(R,{width:r.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:L(()=>[c("div",vt,[m(g,{list:e.value,"onUpdate:list":a[0]||(a[0]=p=>e.value=p),onDelete:k,onDeleteAll:x},null,8,["list"])])]),default:L(()=>[c("div",pt,[m(ce,{onTreeSelect:_,onCheckedTree:N,isRadio:r.isRadio,ref_key:"selectionOrgTreeRef",ref:t},null,8,["isRadio"])])]),_:1},8,["width"])])}}},mt=P(ht,[["__scopeId","data-v-499d2032"]]);const ft={class:"user-select"},gt={class:"user-select-org"},yt={class:"user-select-right"},bt={class:"user-select-user"},wt={class:"search"},xt={class:"user-table"},Rt={key:0},It={key:1},St={key:0},Lt={key:1},Tt={class:"user-select-list"},kt={__name:"select-user",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(d,{expose:T,emit:o}){const r=d,I=o,e=u([]),t=u({searchText:"",orgIdCondition:""}),_=u(null),N=u([{dataIndex:"realName",title:"\u59D3\u540D",align:"center",width:100,isShow:!0},{dataIndex:"company",title:"\u516C\u53F8",align:"center",width:100,isShow:!0},{dataIndex:"dept",title:"\u90E8\u95E8",align:"center",width:100,isShow:!0},{dataIndex:"positionName",title:"\u804C\u52A1",align:"center",width:100,isShow:!0}]),k=(s,h)=>{t.value.orgIdCondition=s[0],x()},x=()=>{_.value.reload()},f=(s,h,b)=>{r.isRadio&&(e.value=[]),le(s,h,e.value,"userId","realName")},a=(s,h,b)=>{oe(s,b,e.value,"userId","realName")},g=(s,h)=>{var b;(b=_.value)!=null&&b.selectedRowList&&(_.value.selectedRowList=ne(s,h,e.value))},R=s=>{e.value.splice(e.value.findIndex(h=>h.bizId===s.bizId),1)},p=()=>{e.value=[]};return E(()=>e.value,s=>{_.value&&(g(s,"bizId"),I("selectedChange"))},{deep:!0}),T({userList:e}),(s,h)=>{const b=H,v=q,O=se,F=Y,w=Q;return y(),C("div",ft,[m(w,{width:r.isMobileFlag?"100%":"292px",allowCollapse:!1},{content:L(()=>[c("div",yt,[c("div",bt,[h[3]||(h[3]=c("div",{class:"user-header"},"\u4EBA\u5458\u5217\u8868",-1)),c("div",wt,[m(v,{value:t.value.searchText,"onUpdate:value":h[0]||(h[0]=i=>t.value.searchText=i),placeholder:"\u59D3\u540D\u3001\u8D26\u53F7\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:x,style:{width:"100%"}},{prefix:L(()=>[m(b,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),c("div",xt,[m(O,{columns:N.value,where:t.value,isShowRowSelect:"",isRadio:r.isRadio,rowId:"userId",ref_key:"tableRef",ref:_,showTool:!1,url:"/sysUser/page",onOnSelect:f,onOnSelectAll:a,onTableListChange:h[1]||(h[1]=i=>g(i,"userId"))},{bodyCell:L(({column:i,record:l})=>{var V,J,z,A,te,n;return[i.dataIndex=="company"?(y(),C(M,{key:0},[X(B((V=l==null?void 0:l.userOrgDTO)!=null&&V.companyName?(J=l==null?void 0:l.userOrgDTO)==null?void 0:J.companyName:""),1)],64)):$("",!0),i.dataIndex=="dept"?(y(),C(M,{key:1},[X(B((z=l==null?void 0:l.userOrgDTO)!=null&&z.deptName?(A=l==null?void 0:l.userOrgDTO)==null?void 0:A.deptName:""),1)],64)):$("",!0),i.dataIndex=="positionName"?(y(),C(M,{key:2},[X(B((te=l==null?void 0:l.userOrgDTO)!=null&&te.positionName?(n=l==null?void 0:l.userOrgDTO)==null?void 0:n.positionName:""),1)],64)):$("",!0),i.dataIndex=="sex"?(y(),C(M,{key:3},[l.sex=="M"?(y(),C("span",Rt,"\u7537")):$("",!0),l.sex=="F"?(y(),C("span",It,"\u5973")):$("",!0)],64)):$("",!0),i.dataIndex=="statusFlag"?(y(),C(M,{key:4},[l.statusFlag==1?(y(),C("span",St,"\u542F\u7528")):$("",!0),l.statusFlag==2?(y(),C("span",Lt,"\u7981\u7528")):$("",!0)],64)):$("",!0)]}),_:1},8,["columns","where","isRadio"])])]),c("div",Tt,[m(F,{list:e.value,"onUpdate:list":h[2]||(h[2]=i=>e.value=i),onDelete:R,onDeleteAll:p},null,8,["list"])])])]),default:L(()=>[c("div",gt,[m(ce,{onTreeSelect:k})])]),_:1},8,["width"])])}}},Ct=P(kt,[["__scopeId","data-v-7d3b2200"]]);const $t={class:"user-select"},Nt={class:"user-select-item"},Ot={class:"user-select-item"},Ft={__name:"select-company",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(d,{expose:T,emit:o}){const r=d,I=o,e=u([]),t=u(),_=(x,f)=>{let a=f.node.orgId,g={bizId:a,name:f.node.orgName};r.isRadio?e.value=[g]:e.value.filter(R=>R.bizId===a).length===0&&e.value.push(g)},N=x=>{e.value.splice(e.value.findIndex(f=>f.bizId===x.bizId),1)},k=()=>{e.value=[]};return E(()=>e.value,x=>{I("selectedChange"),r.isRadio&&t.value&&(x!=null&&x.length?t.value.currentSelectKeys=[x[0].bizId]:t.value.currentSelectKeys=[])},{deep:!0}),T({companyList:e}),(x,f)=>{const a=Q;return y(),C("div",$t,[m(a,{width:r.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:L(()=>[c("div",Ot,[m(Y,{list:e.value,"onUpdate:list":f[0]||(f[0]=g=>e.value=g),onDelete:N,onDeleteAll:k},null,8,["list"])])]),default:L(()=>[c("div",Nt,[m(ce,{onTreeSelect:_,"company-search-flag":!0,ref_key:"selectionOrgTreeRef",ref:t},null,512)])]),_:1},8,["width"])])}}},Dt=P(Ft,[["__scopeId","data-v-5f981e98"]]);const zt={class:"box"},Kt={class:"box-header"},Ut={key:0,class:"box-tab"},At={__name:"index",props:{width:{type:String,default:"70%"},visible:Boolean,title:{type:String,default:"\u6807\u9898"},orgIdLimit:{type:Array,default:()=>[]},showTab:{type:Array,default:()=>[]},data:{type:Object,default:()=>{}},isRadio:{type:Boolean,default:!0},footer:{type:String,default:void 0},max:{type:Number,default:1e5}},emits:["update:visible","done"],setup(d,{emit:T}){const o=d,r=T,I=u(!1),e=u("user"),t=u([]),_=u([{key:"company",name:"\u516C\u53F8"},{key:"user",name:"\u7528\u6237"},{key:"dept",name:"\u90E8\u95E8"},{key:"role",name:"\u89D2\u8272"},{key:"position",name:"\u804C\u4F4D"},{key:"dict",name:"\u5B57\u5178"}]),N=u(""),k=u(!1),x=u(null),f=u(null),a=u(null),g=u(null),R=u(null),p=u(null),s=u(null),h=u(window.innerWidth),b=we(()=>h.value<=768);re(()=>{O(),F(),window.addEventListener("resize",v)}),xe(()=>{window.removeEventListener("resize",v)});const v=()=>{h.value=window.innerWidth},O=()=>{if(o.showTab){if(o.showTab.length==0)t.value=_.value;else if(o.showTab.length>0){let n=[];_.value.forEach(S=>{o.showTab.find(K=>K==S.key)&&n.push(S)}),t.value=n}A(),e.value=t.value[0].key}},F=()=>{JSON.stringify(o.data)!="{}"&&w(o.data)},w=n=>{p.value&&(p.value.companyList=n!=null&&n.selectCompanyList?n.selectCompanyList:[]),x.value&&(x.value.userList=n!=null&&n.selectUserList?n.selectUserList:[]),f.value&&(f.value.deptList=n!=null&&n.selectOrgList?n.selectOrgList:[]),a.value&&(a.value.roleList=n!=null&&n.selectRoleList?n.selectRoleList:[]),g.value&&(g.value.positionList=n!=null&&n.selectPositionList?n.selectPositionList:[]),s.value&&(s.value.dictList=n!=null&&n.selectDictList?n.selectDictList:[]),i()},i=()=>{let n=p.value?l(p.value.companyList,"company"):"",S=x.value?l(x.value.userList,"user"):"",K=f.value?l(f.value.deptList,"dept"):"",U=a.value?l(a.value.roleList,"role"):"",j=g.value?l(g.value.positionList,"position"):"",ae=s.value?l(s.value.dictList,"dict"):"";N.value=S+K+U+j+n+ae},l=(n,S)=>{let K="";if(n&&n.length>0){n.forEach(j=>{j.subValueName&&(S=="position"||S=="user"||S=="dept")?K+=j.subValueName+"#"+j.name+";":K+=j.name+";"});let U="";S=="user"?U="\u3010\u7528\u6237\u3011":S=="dept"?U="\u3010\u90E8\u95E8\u3011":S=="role"?U="\u3010\u89D2\u8272\u3011":S=="position"?U="\u3010\u804C\u4F4D\u3011":S=="company"?U="\u3010\u516C\u53F8\u3011":S=="dict"&&(U="\u3010\u5B57\u5178\u3011"),K=U+K}return K},V=n=>{r("update:visible",n)},J=()=>{let n={selectCompanyList:p.value?p.value.companyList:[],selectUserList:x.value?x.value.userList:[],selectOrgList:f.value?f.value.deptList:[],selectRoleList:a.value?a.value.roleList:[],selectPositionList:g.value?g.value.positionList:[],selectDictList:s.value?s.value.dictList:[],userGroupDetailName:N.value};if(!o.isRadio){let S=!0;if((t.value[0]="user")?S=z(n,"selectUserList"):(t.value[0]="dept")?S=z(n,"selectOrgList"):(t.value[0]="role")?S=z(n,"selectRoleList"):(t.value[0]="position")?S=z(n,"selectPositionList"):(t.value[0]="dict")?S=z(n,"selectDictList"):(t.value[0]="company")&&(S=z(n,"selectCompanyList")),!S)return}I.value=!0,I.value=!1,V(!1),r("done",n)},z=(n,S)=>n[S].length>o.max?(Re.warning("\u6700\u591A\u9009"+o.max+"\u6761"),!1):!0,A=()=>{ee(()=>{R.value&&(t.value.length>=2?(k.value=!0,R.value.style.height="calc(100% - 140px)"):(R.value.style.height="calc(100% - 80px)",k.value=!1))})},te=n=>{e.value=n};return E(()=>o.showTab,()=>{O()}),E(()=>o.data,()=>{ee(()=>{w(o.data)})},{deep:!0}),E(()=>o.visible,()=>{o.visible&&(N.value="")}),(n,S)=>{const K=Ie,U=Se,j=Dt,ae=Ct,_e=mt,pe=nt,ve=Ye,he=We,me=Le;return d.visible?(y(),D(me,{key:0,width:o.width,visible:o.visible,"confirm-loading":I.value,"body-style":{paddingBottom:"8px"},onOk:J,title:d.title,style:{top:"40px"},onCancel:S[1]||(S[1]=Z=>V(!1)),footer:d.footer,maxable:"",maskClosable:!1,wrapClassName:"project-modal h80 selection-modal"},{default:L(()=>[c("div",zt,[c("div",Kt,"\u5DF2\u9009\uFF1A"+B(N.value),1),t.value.length>=2?(y(),C("div",Ut,[m(U,{activeKey:e.value,"onUpdate:activeKey":S[0]||(S[0]=Z=>e.value=Z),onChange:te},{default:L(()=>[(y(!0),C(M,null,ue(t.value,Z=>(y(),D(K,{key:Z.key,tab:Z.name},null,8,["tab"]))),128))]),_:1},8,["activeKey"])])):$("",!0),c("div",{class:"box-content",ref_key:"contentRef",ref:R},[d.showTab.length===0||d.showTab.indexOf("company")!==-1?W((y(),D(j,{key:0,ref_key:"companyRef",ref:p,isRadio:o.isRadio,isMobileFlag:b.value,onSelectedChange:i},null,8,["isRadio","isMobileFlag"])),[[G,e.value==="company"]]):$("",!0),d.showTab.length===0||d.showTab.indexOf("user")!==-1?W((y(),D(ae,{key:1,ref_key:"userRef",ref:x,isRadio:o.isRadio,isMobileFlag:b.value,onSelectedChange:i},null,8,["isRadio","isMobileFlag"])),[[G,e.value==="user"]]):$("",!0),d.showTab.length===0||d.showTab.indexOf("dept")!==-1?W((y(),D(_e,{key:2,ref_key:"deptRef",ref:f,isRadio:o.isRadio,isMobileFlag:b.value,onSelectedChange:i},null,8,["isRadio","isMobileFlag"])),[[G,e.value==="dept"]]):$("",!0),d.showTab.length===0||d.showTab.indexOf("role")!==-1?W((y(),D(pe,{key:3,ref_key:"roleRef",ref:a,isRadio:o.isRadio,isMobileFlag:b.value,onSelectedChange:i},null,8,["isRadio","isMobileFlag"])),[[G,e.value==="role"]]):$("",!0),d.showTab.length===0||d.showTab.indexOf("position")!==-1?W((y(),D(ve,{key:4,ref_key:"positionRef",ref:g,isRadio:o.isRadio,isMobileFlag:b.value,onSelectedChange:i},null,8,["isRadio","isMobileFlag"])),[[G,e.value==="position"]]):$("",!0),d.showTab.length===0||d.showTab.indexOf("dict")!==-1?W((y(),D(he,{key:5,ref_key:"dictRef",ref:s,isRadio:o.isRadio,isMobileFlag:b.value,onSelectedChange:i},null,8,["isRadio","isMobileFlag"])),[[G,e.value==="dict"]]):$("",!0)],512)])]),_:1},8,["width","visible","confirm-loading","title","footer"])):$("",!0)}}},Wt=P(At,[["__scopeId","data-v-f6151c6e"]]);export{Wt as _};
