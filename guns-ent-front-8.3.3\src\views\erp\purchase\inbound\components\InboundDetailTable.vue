<template>
  <div class="inbound-detail-table">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ x: 1200 }"
    >
      <!-- 商品信息 -->
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'productInfo'">
          <div class="product-info">
            <div class="product-name">{{ record.productName }}</div>
            <div class="product-details">
              <span class="product-code">{{ record.productCode }}</span>
              <span v-if="record.specification" class="product-spec">{{ record.specification }}</span>
            </div>
          </div>
        </template>

        <!-- 数量输入 -->
        <template v-if="column.key === 'quantity'">
          <a-input-number
            v-model:value="record.quantity"
            :min="0"
            :precision="getPrecision(record.pricingType)"
            :step="getStep(record.pricingType)"
            style="width: 100%"
            @change="onQuantityChange(record, index)"
          >
            <template #addonAfter>
              {{ getQuantityUnit(record) }}
            </template>
          </a-input-number>
        </template>

        <!-- 单价输入 -->
        <template v-if="column.key === 'unitPrice'">
          <a-input-number
            v-model:value="record.unitPrice"
            :min="0"
            :precision="2"
            :step="0.01"
            style="width: 100%"
            @change="onUnitPriceChange(record, index)"
          >
            <template #addonBefore>¥</template>
          </a-input-number>
        </template>

        <!-- 总价显示 -->
        <template v-if="column.key === 'totalPrice'">
          <span class="total-price">¥{{ formatAmount(record.totalPrice) }}</span>
        </template>

        <!-- 备注输入 -->
        <template v-if="column.key === 'remark'">
          <a-input
            v-model:value="record.remark"
            placeholder="备注"
            @change="onRemarkChange(record, index)"
          />
        </template>

        <!-- 操作 -->
        <template v-if="column.key === 'action'">
          <a-button type="link" size="small" danger @click="removeItem(index)">
            删除
          </a-button>
        </template>
      </template>
    </a-table>

    <!-- 空状态 -->
    <div v-if="!dataSource || dataSource.length === 0" class="empty-state">
      <a-empty description="暂无商品明细">
        <template #image>
          <icon-font iconClass="icon-opt-shangpin" style="font-size: 48px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';

export default {
  name: 'InboundDetailTable',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    supplierId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    // 计算属性
    const dataSource = computed({
      get: () => props.value || [],
      set: (value) => {
        emit('update:value', value);
        emit('change', value);
      }
    });

    // 表格列定义
    const columns = [
      {
        title: '商品信息',
        key: 'productInfo',
        width: 250,
        fixed: 'left'
      },
      {
        title: '数量',
        key: 'quantity',
        width: 150,
        align: 'center'
      },
      {
        title: '单价',
        key: 'unitPrice',
        width: 120,
        align: 'center'
      },
      {
        title: '总价',
        key: 'totalPrice',
        width: 120,
        align: 'right'
      },
      {
        title: '备注',
        key: 'remark',
        width: 200
      },
      {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right',
        align: 'center'
      }
    ];

    // 根据计价类型获取精度
    const getPrecision = (pricingType) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 3; // 重量计价，保留3位小数
        case 'NORMAL':
        case 'PIECE':
        case 'VARIABLE':
        default:
          return 0; // 其他类型，整数
      }
    };

    // 根据计价类型获取步长
    const getStep = (pricingType) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 0.001;
        case 'NORMAL':
        case 'PIECE':
        case 'VARIABLE':
        default:
          return 1;
      }
    };

    // 获取数量单位
    const getQuantityUnit = (record) => {
      switch (record.pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return record.unit || '个';
      }
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 计算总价
    const calculateTotalPrice = (record) => {
      const quantity = parseFloat(record.quantity) || 0;
      const unitPrice = parseFloat(record.unitPrice) || 0;
      return quantity * unitPrice;
    };

    // 数量变化事件
    const onQuantityChange = (record, index) => {
      record.totalPrice = calculateTotalPrice(record);
      updateDataSource(index, record);
    };

    // 单价变化事件
    const onUnitPriceChange = (record, index) => {
      record.totalPrice = calculateTotalPrice(record);
      updateDataSource(index, record);
    };

    // 备注变化事件
    const onRemarkChange = (record, index) => {
      updateDataSource(index, record);
    };

    // 更新数据源
    const updateDataSource = (index, record) => {
      const newDataSource = [...dataSource.value];
      newDataSource[index] = { ...record };
      dataSource.value = newDataSource;
    };

    // 删除项目
    const removeItem = (index) => {
      const newDataSource = [...dataSource.value];
      newDataSource.splice(index, 1);
      dataSource.value = newDataSource;
    };

    return {
      dataSource,
      columns,
      getPrecision,
      getStep,
      getQuantityUnit,
      formatAmount,
      onQuantityChange,
      onUnitPriceChange,
      onRemarkChange,
      removeItem
    };
  }
};
</script>

<style scoped>
.inbound-detail-table {
  min-height: 200px;
}

.product-info {
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-details {
  font-size: 12px;
  color: #8c8c8c;
}

.product-code {
  margin-right: 8px;
}

.product-spec {
  margin-left: 8px;
}

.total-price {
  font-weight: 500;
  color: #1890ff;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

/* 表格样式优化 */
:deep(.ant-table-tbody > tr > td) {
  padding: 8px;
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-input-number-input) {
  text-align: center;
}
</style>
