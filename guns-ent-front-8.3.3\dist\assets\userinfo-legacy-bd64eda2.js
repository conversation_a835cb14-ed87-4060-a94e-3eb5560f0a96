System.register(["./index-legacy-ee1db0c7.js"],(function(e,a){"use strict";var i,t,l,d,n,s,r,o,c,u,f,p,v,h,g,x,b,m,y;return{setters:[e=>{i=e._,t=e.b2,l=e.b3,d=e.r,n=e.o,s=e.a,r=e.c,o=e.b,c=e.d,u=e.I,f=e.t,p=e.w,v=e.g,h=e.f,g=e.bI,x=e.h,b=e.bJ,m=e.bK,y=e.B}],execute:function(){var a=document.createElement("style");a.textContent=".user-info-card[data-v-724041dd]{width:100%;height:100%;background-color:#fff;box-shadow:0 0 6px rgba(204,204,204,.5);border-radius:4px;padding:10px}.user-info-card-header[data-v-724041dd]{width:100%;height:50px;border-bottom:1px #999999 solid;display:flex;align-items:center;justify-content:space-between}.user-info-card-header-title[data-v-724041dd]{font-size:18px;font-weight:500}.user-info-card-header-icon[data-v-724041dd]{cursor:pointer;font-size:24px;color:#2a82e4}.user-info-card-body[data-v-724041dd]{width:100%;height:calc(100% - 50px);display:flex;flex-direction:row;padding-top:15px}.user-info-profile[data-v-724041dd]{width:100%;height:100%;padding-left:15px}.user-info-profile-info[data-v-724041dd]{width:100%;height:100%;display:flex;flex-direction:column}.title[data-v-724041dd]{color:#505050;font-size:18px;line-height:30px}\n",document.head.appendChild(a);const w={class:"user-info-card"},I={class:"user-info-card-header"},_={class:"user-info-card-body"},k={class:"user-info-profile-photo"},z={class:"user-info-profile"},C={class:"user-info-profile-info"},j={class:"title"},O={class:"title"},N={class:"title"},S={class:"title"},U={class:"title"};e("default",i({__name:"userinfo",setup(e){const a=t(),i=l(),B=d({}),E=d({}),F=d(!1),J=d(""),K=()=>{F.value=!0},L=()=>{a.push({path:"/index/personal"})};return n((()=>{b.getUserInfo().then((e=>{B.value=e}));let e=i.info.userOrgInfoList.filter((e=>e.currentSelectFlag));1===e.length&&(E.value=e[0],J.value=E.value.orgId)})),(e,a)=>{const i=m,t=y;return s(),r("div",w,[o("div",I,[a[1]||(a[1]=o("div",{class:"user-info-card-header-title"},[o("span",null,"个人信息")],-1)),o("div",{class:"user-info-card-header-icon",onClick:L},[c(u,{"font-size":"34px",color:"#2a82e4","icon-class":"icon-opt-bianji",title:"编辑","font-weight":"bold"})])]),o("div",_,[o("div",k,[c(i,{size:110,src:B.value.avatarWrapper},null,8,["src"])]),o("div",z,[o("div",C,[o("div",j,[o("span",null,"姓名："+f(B.value.realName),1)]),o("div",O,[o("span",null,"账号："+f(B.value.account),1)]),o("div",N,[o("span",null,"公司："+f(E.value.companyName),1),c(t,{type:"link",onClick:K},{default:p((()=>a[2]||(a[2]=[v("切换当前公司")]))),_:1,__:[2]})]),o("div",S,[o("span",null,"邮箱："+f(B.value.email),1)]),o("div",U,[o("span",null,"电话："+f(B.value.phone),1)])])])]),F.value?(s(),h(g,{key:0,visible:F.value,"onUpdate:visible":a[0]||(a[0]=e=>F.value=e),selectOrgId:J.value},null,8,["visible","selectOrgId"])):x("",!0)])}}},[["__scopeId","data-v-724041dd"]]))}}}));
