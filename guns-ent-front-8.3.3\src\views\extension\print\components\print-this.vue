<template>
  <a-card title="打印当前页面" :bordered="false">
    <a-space>
      <a-button @click="print">打印</a-button>
    </a-space>
  </a-card>
</template>

<script setup>
import { reactive } from 'vue';
import { printThis } from '@/utils/common/print';

// 打印当前页面参数
const option = reactive({
  horizontal: undefined,
  margin: undefined,
  title: ''
});

/* 打印当前页面 */
const print = () => {
  printThis(option);
};
</script>
