package cn.stylefeng.roses.kernel.conversion.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aspose.pdf.Document;
import com.aspose.pdf.Image;
import com.aspose.pdf.Page;

import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStream;

/**
 * 图片转PDF
 *
 * <AUTHOR>
 * @since 2024-04-02 18:07
 */
public class PictureUtil {

    /**
     * svg图片转化为pdf格式文件
     *
     * <AUTHOR>
     * @since 2024-04-02 18:08
     */
    public static void picToPdf(byte[] inputFileBytes, OutputStream outputStream) {

        Document pdfDocument = null;

        try {
            // 创建Pdf 文档
            pdfDocument = new Document();

            // 创建一个新的页面
            Page page = pdfDocument.getPages().add();

            // 加载图片
            Image image = new Image();
            image.setImageStream(new ByteArrayInputStream(inputFileBytes));

            // 添加图片到页面
            page.getParagraphs().add(image);

            pdfDocument.save(outputStream);
        } finally {
            if (ObjectUtil.isNotEmpty(pdfDocument)) {
                pdfDocument.close();
            }
        }
    }

    /**
     * svg图片转化为pdf格式文件
     *
     * <AUTHOR>
     * @since 2024-04-02 18:08
     */
    public static void svgToPdf(byte[] inputFileBytes, OutputStream outputStream) {

        // 加载svg图片的流
        Document pdfDocument = null;

        try {
            pdfDocument = new Document(new ByteArrayInputStream(inputFileBytes), new com.aspose.pdf.SvgLoadOptions());

            pdfDocument.save(outputStream);
        } finally {
            if (ObjectUtil.isNotEmpty(pdfDocument)) {
                pdfDocument.close();
            }
        }
    }

    public static void main(String[] args) throws FileNotFoundException {
        PictureUtil.picToPdf(FileUtil.readBytes("C:\\Users\\<USER>\\Pictures\\中国航空logo.png"), new FileOutputStream("C:\\Users\\<USER>\\Pictures\\picTopdf1.pdf"));
        PictureUtil.svgToPdf(FileUtil.readBytes("C:\\Users\\<USER>\\Pictures\\arcgis.svg"), new FileOutputStream("C:\\Users\\<USER>\\Pictures\\svgTopdf2.pdf"));
    }

}
