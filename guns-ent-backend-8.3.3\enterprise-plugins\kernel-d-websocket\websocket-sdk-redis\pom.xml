<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-websocket</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>websocket-sdk-redis</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--WebSocket内存缓存实现-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>websocket-sdk-memory</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--redis依赖-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

        <!--springboot redis依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
