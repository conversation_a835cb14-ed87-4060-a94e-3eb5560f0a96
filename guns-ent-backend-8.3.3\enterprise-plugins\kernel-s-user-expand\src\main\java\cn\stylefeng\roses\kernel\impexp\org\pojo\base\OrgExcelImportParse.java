package cn.stylefeng.roses.kernel.impexp.org.pojo.base;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用在excel导入机构的实体
 * <p>
 * 一共12列，每个字段都有对应的映射关系
 *
 * <AUTHOR>
 * @since 2024-02-04 14:46
 */
@Data
public class OrgExcelImportParse {

    /**
     * 主键
     */
    @ExcelProperty(index = 10)
    @ChineseDescription("主键")
    private String orgId;

    /**
     * excel导入的序号
     */
    @ExcelProperty(index = 0)
    @ChineseDescription("excel导入的序号")
    private String number;

    /**
     * 机构名称
     */
    @ExcelProperty(index = 1)
    @ChineseDescription("机构名称")
    private String orgName;

    /**
     * 机构简称
     */
    @ExcelProperty(index = 2)
    @ChineseDescription("机构简称")
    private String orgShortName;

    /**
     * 父级机构
     */
    @ExcelProperty(index = 3)
    @ChineseDescription("父级机构")
    private String parentOrgName;

    /**
     * 机构编码
     */
    @ExcelProperty(index = 4)
    @ChineseDescription("机构编码")
    private String orgCode;

    /**
     * 排序
     */
    @ExcelProperty(index = 5)
    @ChineseDescription("排序")
    private String orgSort;

    /**
     * 状态：启用和禁用
     */
    @ExcelProperty(index = 6)
    @ChineseDescription("状态：启用和禁用")
    private String statusFlag;

    /**
     * 机构类型
     */
    @ExcelProperty(index = 7)
    @ChineseDescription("机构类型")
    private String orgType;

    /**
     * 税号
     */
    @ExcelProperty(index = 8)
    @ChineseDescription("税号")
    private String taxNo;

    /**
     * 描述
     */
    @ExcelProperty(index = 9)
    @ChineseDescription("描述")
    private String remark;

    /**
     * 对外主数据系统的机构id
     */
    @ExcelProperty(index = 11)
    @ChineseDescription("对外主数据系统的机构id")
    private String masterOrgId;

}
