package cn.stylefeng.roses.kernel.erp.modular.productcategory.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductCategoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductCategoryResponse;
import cn.stylefeng.roses.kernel.erp.modular.productcategory.service.ErpProductCategoryService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 产品分类管理控制器
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
@RestController
@ApiResource(name = "产品分类管理")
public class ErpProductCategoryController {

    @Resource
    private ErpProductCategoryService erpProductCategoryService;

    /**
     * 新增产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 响应结果
     */
    @PostResource(name = "新增产品分类", path = "/erp/productCategory/add")
    public ResponseData<?> add(@RequestBody @Validated(ErpProductCategoryRequest.add.class) ErpProductCategoryRequest erpProductCategoryRequest) {
        erpProductCategoryService.add(erpProductCategoryRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 响应结果
     */
    @PostResource(name = "编辑产品分类", path = "/erp/productCategory/edit")
    public ResponseData<?> edit(@RequestBody @Validated(ErpProductCategoryRequest.edit.class) ErpProductCategoryRequest erpProductCategoryRequest) {
        erpProductCategoryService.edit(erpProductCategoryRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除产品分类
     * <p>
     * 删除前会检查分类是否有关联商品，如有则不允许删除
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 响应结果
     */
    @PostResource(name = "删除产品分类", path = "/erp/productCategory/delete")
    public ResponseData<?> delete(@RequestBody @Validated(ErpProductCategoryRequest.delete.class) ErpProductCategoryRequest erpProductCategoryRequest) {
        erpProductCategoryService.delete(erpProductCategoryRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除产品分类
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 响应结果
     */
    @PostResource(name = "批量删除产品分类", path = "/erp/productCategory/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody @Validated(ErpProductCategoryRequest.batchDelete.class) ErpProductCategoryRequest erpProductCategoryRequest) {
        erpProductCategoryService.batchDelete(erpProductCategoryRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看产品分类详情
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 产品分类详情
     */
    @GetResource(name = "查看产品分类详情", path = "/erp/productCategory/detail")
    public ResponseData<ErpProductCategoryResponse> detail(@Validated(ErpProductCategoryRequest.detail.class) ErpProductCategoryRequest erpProductCategoryRequest) {
        ErpProductCategoryResponse categoryResponse = erpProductCategoryService.detail(erpProductCategoryRequest);
        return new SuccessResponseData<>(categoryResponse);
    }

    /**
     * 分页查询产品分类列表
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 分页结果
     */
    @GetResource(name = "分页查询产品分类列表", path = "/erp/productCategory/page")
    public ResponseData<PageResult<ErpProductCategoryResponse>> page(ErpProductCategoryRequest erpProductCategoryRequest) {
        PageResult<ErpProductCategoryResponse> pageResult = erpProductCategoryService.findPage(erpProductCategoryRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询产品分类列表
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 产品分类列表
     */
    @GetResource(name = "查询产品分类列表", path = "/erp/productCategory/list")
    public ResponseData<List<ErpProductCategoryResponse>> list(ErpProductCategoryRequest erpProductCategoryRequest) {
        List<ErpProductCategoryResponse> categoryList = erpProductCategoryService.findList(erpProductCategoryRequest);
        return new SuccessResponseData<>(categoryList);
    }

    /**
     * 查询产品分类树形结构
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 树形结构列表
     */
    @GetResource(name = "查询产品分类树形结构", path = "/erp/productCategory/tree")
    public ResponseData<List<ErpProductCategoryResponse>> tree(ErpProductCategoryRequest erpProductCategoryRequest) {
        List<ErpProductCategoryResponse> treeList = erpProductCategoryService.findTree(erpProductCategoryRequest);
        return new SuccessResponseData<>(treeList);
    }

    /**
     * 查询产品分类树形结构（懒加载）
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 树形结构列表
     */
    @GetResource(name = "查询产品分类树形结构（懒加载）", path = "/erp/productCategory/treeWithLazy")
    public ResponseData<List<ErpProductCategoryResponse>> treeWithLazy(ErpProductCategoryRequest erpProductCategoryRequest) {
        List<ErpProductCategoryResponse> treeList = erpProductCategoryService.findTreeWithLazy(erpProductCategoryRequest);
        return new SuccessResponseData<>(treeList);
    }

    /**
     * 查询产品分类树形结构（用于选择器）
     * <p>
     * 返回适用于前端选择器组件的树形结构数据，包含key、value、title等字段
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 树形结构列表
     */
    @GetResource(name = "查询产品分类树形结构（用于选择器）", path = "/erp/productCategory/treeForSelector")
    public ResponseData<List<ErpProductCategoryResponse>> treeForSelector(ErpProductCategoryRequest erpProductCategoryRequest) {
        List<ErpProductCategoryResponse> treeList = erpProductCategoryService.findTreeForSelector(erpProductCategoryRequest);
        return new SuccessResponseData<>(treeList);
    }

    /**
     * 检查分类是否有关联商品
     * <p>
     * 用于前端在删除分类前进行检查
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 是否有关联商品
     */
    @GetResource(name = "检查分类是否有关联商品", path = "/api/erp/productCategory/checkHasRelatedProducts")
    public ResponseData<Boolean> checkHasRelatedProducts(ErpProductCategoryRequest erpProductCategoryRequest) {
        boolean hasRelatedProducts = erpProductCategoryService.hasRelatedProducts(erpProductCategoryRequest.getCategoryId());
        return new SuccessResponseData<>(hasRelatedProducts);
    }

    /**
     * 获取分类关联的商品数量
     *
     * @param erpProductCategoryRequest 请求参数
     * @return 商品数量
     */
    @GetResource(name = "获取分类关联的商品数量", path = "/api/erp/productCategory/getProductCount")
    public ResponseData<Integer> getProductCount(ErpProductCategoryRequest erpProductCategoryRequest) {
        int count = erpProductCategoryService.getProductCount(erpProductCategoryRequest.getCategoryId());
        return new SuccessResponseData<>(count);
    }
}
