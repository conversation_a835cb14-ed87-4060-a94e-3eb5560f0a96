import{_ as te,r as c,o as ae,X as ne,bv as se,a as n,f as g,w as o,b as r,d as _,g as p,t as d,aR as y,c as l,F as m,e as D,aS as x,h as u,bK as oe,B as le,u as ie,v as ue,G as de,H as ce,i as re,C as _e,ab as pe,a7 as me,ch as ve}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */import{U as O}from"./UsersApi-ec2041f8.js";import ge from"./user-add-edit-4d67b029.js";import fe from"./allocation-role-3dec600b.js";import{S as ye}from"./SysDictTypeApi-1ce2cbe7.js";import"./user-form-5204a582.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              *//* empty css              *//* empty css              */import"./FileApi-418f4d35.js";const he={class:"top"},be={class:"top-left"},Ie={class:"username"},xe={class:"top-right"},ke={class:"content"},Le={class:"content-item"},Ne={key:0},we={key:1},Ce={key:2},Fe={key:3},Te={class:"content-item"},De={key:0},Oe={key:1},Re={key:0},Se={key:1},Ue={class:"content-item"},Ae={class:"content-item"},Be={key:0,style:{width:"100%",display:"flex","align-items":"center"}},Ee={class:"filename"},je=["onClick"],ze={__name:"user-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(R,{emit:S}){const k=R,U=S,A=c(null),v=c("1"),B=c([{key:"1",name:"\u57FA\u7840\u4FE1\u606F",icon:"icon-tab-jichuxinxi"},{key:"2",name:"\u7EC4\u7EC7\u673A\u6784",icon:"icon-tab-zuzhijigou"},{key:"3",name:"\u89D2\u8272\u4FE1\u606F",icon:"icon-tab-jiaosexinxi"},{key:"4",name:"\u7528\u6237\u8BC1\u4E66",icon:"icon-tab-yonghuzhengshu"}]),s=c({userOrgList:[]}),h=c(!1),b=c(!1),E=c([{name:"\u8D85\u7EA7\u7BA1\u7406\u5458",value:"superAdminFlag"},{name:"\u59D3\u540D",value:"realName"},{name:"\u8D26\u53F7",value:"account"},{name:"\u5DE5\u53F7",value:"employeeNumber"},{name:"\u6027\u522B",value:"sex"},{name:"\u751F\u65E5",value:"birthday"},{name:"\u90AE\u7BB1",value:"email"},{name:"\u624B\u673A\u53F7",value:"phone"},{name:"\u4E0A\u6B21\u767B\u5F55IP",value:"lastLoginIp"},{name:"\u4E0A\u6B21\u767B\u5F55\u65F6\u95F4",value:"lastLoginTime"},{name:"\u767B\u5F55\u6B21\u6570",value:"loginCount"},{name:"\u7528\u6237\u72B6\u6001",value:"statusFlag"},{name:"\u521B\u5EFA\u65F6\u95F4",value:"createTime"},{name:"\u4E0A\u6B21\u66F4\u65B0\u65F6\u95F4",value:"updateTime"}]),j=c([{key:"index",title:"\u5E8F\u53F7",align:"center",customRender:({index:a})=>a+1},{title:"\u673A\u6784\u540D\u79F0",align:"center",dataIndex:"orgName"},{title:"\u804C\u4F4D",align:"center",dataIndex:"positionName"},{title:"\u662F\u5426\u662F\u4E3B\u90E8\u95E8",align:"center",dataIndex:"mainFlag"},{title:"\u662F\u5426\u542F\u7528",align:"center",dataIndex:"statusFlag"}]),z=c([{key:"index",title:"\u5E8F\u53F7",width:60,align:"center",customRender:({index:a})=>a+1},{title:"\u8BC1\u4E66\u7C7B\u578B",align:"center",dataIndex:"certificateType"},{title:"\u8BC1\u4E66\u7F16\u53F7",align:"center",dataIndex:"certificateNo"},{title:"\u53D1\u8BC1\u673A\u6784\u540D\u79F0",align:"center",dataIndex:"issuingAuthority"},{title:"\u53D1\u8BC1\u65E5\u671F",align:"center",dataIndex:"dateIssued"},{title:"\u9644\u4EF6",align:"center",width:150,ellipsis:!0,dataIndex:"attachmentId"},{title:"\u5230\u671F\u65E5\u671F",align:"center",dataIndex:"dateExpires"}]),N=c([]),L=c([]);ae(()=>{$(),I(),V()}),ne(()=>k.data,a=>{a&&I()},{deep:!0});const V=async()=>{L.value=await ye.getDictListByParams({dictTypeId:"1722790763315597314"})},K=a=>{let e="";return L.value.find(f=>f.dictId==a)&&(e=L.value.find(f=>f.dictId==a).dictName),e},I=()=>{O.detail({userId:k.data.userId}).then(a=>{s.value=Object.assign({},a),a.userOrgDTOList&&a.userOrgDTOList.length>0&&(s.value.userOrgList=a.userOrgDTOList.map(e=>({mainFlag:e.mainFlag,statusFlag:e.statusFlag,positionId:e.positionId,positionName:e.positionName,orgId:e.deptId?e.deptId:e.companyId,orgName:e.deptName?e.deptName:e.companyName}))),a.userCertificateList.length>0&&s.value.userCertificateList.forEach(e=>{e.attachmentId&&(e.attachmentName=e.attachmentIdWrapper.name,e.attachmentUrl=e.attachmentIdWrapper.thumbUrl)})})},$=async()=>{N.value=await O.roleList()},Y=a=>{U("update:visible",a)},M=a=>{v.value=a},P=a=>{const{href:e}=router.resolve({path:a.attachmentUrl});window.open(e,"_blank")},W=()=>{v.value=="3"?b.value=!0:h.value=!0},G=a=>{let e=a;return a&&a.length>2&&(e=a.substr(0,1)),e};return(a,e)=>{const f=oe,H=le,w=ie,X=ue,C=de,F=ce,T=re,q=_e,J=pe,Q=me,Z=ve,ee=se("permission");return n(),g(Z,{width:800,visible:k.visible,title:"\u7528\u6237\u4FE1\u606F",onClose:e[3]||(e[3]=t=>Y(!1)),isShowTab:!0,activeKey:v.value,tabList:B.value,onTabChange:M},{top:o(()=>[r("div",he,[r("div",be,[_(f,{style:{"background-color":"#6f9ae7"}},{default:o(()=>[p(d(G(s.value.realName)),1)]),_:1}),r("span",Ie,d(s.value.realName),1)]),r("div",xe,[y((n(),g(H,{type:"primary",class:"border-radius",onClick:W},{default:o(()=>e[4]||(e[4]=[p("\u7F16\u8F91")])),_:1,__:[4]})),[[ee,["EDIT_USER"]]])])])]),default:o(()=>[r("div",ke,[y(r("div",Le,[_(F,{ref:"formRef",model:s.value,"label-col":{span:6}},{default:o(()=>[_(C,{gutter:16},{default:o(()=>[(n(!0),l(m,null,D(E.value,(t,i)=>(n(),g(X,{span:12,key:i},{default:o(()=>[_(w,{label:t.name},{default:o(()=>[t.value=="superAdminFlag"?(n(),l("span",Ne,d(s.value[t.value]=="Y"?"\u662F":"\u5426"),1)):t.value=="statusFlag"?(n(),l("span",we,d(s.value[t.value]==1?"\u542F\u7528":"\u7981\u7528"),1)):t.value=="sex"?(n(),l("span",Ce,d(s.value[t.value]=="M"?"\u7537":s.value[t.value]=="Y"?"\u5426":""),1)):(n(),l("span",Fe,d(s.value[t.value]),1))]),_:2},1032,["label"])]),_:2},1024))),128))]),_:1})]),_:1},8,["model"])],512),[[x,v.value=="1"]]),y(r("div",Te,[_(T,{dataSource:s.value.userOrgList,bordered:"",size:"small",scroll:{x:"max-content"},columns:j.value,pagination:!1,ref_key:"tableRef",ref:A},{bodyCell:o(({column:t,record:i})=>[t.dataIndex=="mainFlag"?(n(),l(m,{key:0},[i.mainFlag=="Y"?(n(),l("span",De,"\u662F")):u("",!0),i.mainFlag=="N"?(n(),l("span",Oe,"\u5426")):u("",!0)],64)):u("",!0),t.dataIndex=="statusFlag"?(n(),l(m,{key:1},[i.statusFlag==1?(n(),l("span",Re,"\u542F\u7528")):u("",!0),i.statusFlag==2?(n(),l("span",Se,"\u7981\u7528")):u("",!0)],64)):u("",!0)]),_:1},8,["dataSource","columns"])],512),[[x,v.value=="2"]]),y(r("div",Ue,[_(F,{ref:"formRef",model:s.value,"label-col":{span:3}},{default:o(()=>[_(C,null,{default:o(()=>[_(w,{label:"\u89D2\u8272\u4FE1\u606F",style:{width:"100%"}},{default:o(()=>[_(J,{value:s.value.roleIdList,"onUpdate:value":e[0]||(e[0]=t=>s.value.roleIdList=t),disabled:""},{default:o(()=>[(n(!0),l(m,null,D(N.value,t=>(n(),g(q,{value:t.roleId,name:"type",key:t.roleId},{default:o(()=>[p(d(t.roleName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1},8,["model"])],512),[[x,v.value=="3"]]),y(r("div",Ae,[_(T,{dataSource:s.value.userCertificateList,scroll:{x:"max-content"},size:"small",bordered:"",columns:z.value,pagination:!1},{bodyCell:o(({column:t,record:i})=>[t.dataIndex=="certificateType"?(n(),l(m,{key:0},[p(d(K(i.certificateType)),1)],64)):u("",!0),t.dataIndex=="dateIssued"?(n(),l(m,{key:1},[p(d(i[t.dataIndex]?i[t.dataIndex].substr(0,10):""),1)],64)):u("",!0),t.dataIndex=="dateExpires"?(n(),l(m,{key:2},[p(d(i[t.dataIndex]?i[t.dataIndex].substr(0,10):"\u957F\u671F"),1)],64)):u("",!0),t.dataIndex=="attachmentId"?(n(),l(m,{key:3},[i.attachmentId?(n(),l("div",Be,[r("span",Ee,[r("a",{onClick:Ve=>P(i)},[_(Q,null,{title:o(()=>[p(d(i.attachmentName),1)]),default:o(()=>[p(" "+d(i.attachmentName),1)]),_:2},1024)],8,je)])])):u("",!0)],64)):u("",!0)]),_:1},8,["dataSource","columns"])],512),[[x,v.value=="4"]])]),h.value?(n(),g(ge,{key:0,visible:h.value,"onUpdate:visible":e[1]||(e[1]=t=>h.value=t),data:s.value,onDone:I},null,8,["visible","data"])):u("",!0),b.value?(n(),g(fe,{key:1,visible:b.value,"onUpdate:visible":e[2]||(e[2]=t=>b.value=t),data:s.value,onDone:I},null,8,["visible","data"])):u("",!0)]),_:1},8,["visible","activeKey","tabList"])}}},ot=te(ze,[["__scopeId","data-v-3ac33d54"]]);export{ot as default};
