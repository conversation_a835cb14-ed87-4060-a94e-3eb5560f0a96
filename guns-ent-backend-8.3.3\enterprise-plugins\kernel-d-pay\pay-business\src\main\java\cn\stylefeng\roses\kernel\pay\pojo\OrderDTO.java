package cn.stylefeng.roses.kernel.pay.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单查询结果
 *
 * <AUTHOR>
 * @date 2021/06/23 22:28
 */
@Data
public class OrderDTO {

    /**
     * 订单id
     */
    @ChineseDescription("订单id")
    private Long orderId;

    /**
     * 订单号，时间戳加6位随机字符串
     */
    @ChineseDescription("订单号")
    private String orderNumber;

    /**
     * 商品id
     */
    @ChineseDescription("商品id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String goodsName;

    /**
     * 实付金额
     */
    @ChineseDescription("实付金额")
    private BigDecimal payPrice;

    /**
     * 状态，1待支付、2已完成、3已取消、4已退款
     */
    @ChineseDescription("状态，1待支付、2已完成、3已取消、4已退款")
    private Integer state;

    /**
     * 支付时间
     */
    @ChineseDescription("支付时间")
    private Date payTime;

    /**
     * 支付渠道：alipay，wxpay
     */
    @ChineseDescription("支付渠道：alipay，wxpay")
    private String payChannel;

    /**
     * 是否开过发票
     */
    @ChineseDescription("是否开过发票：Y-开过，N-未开过")
    private String invoiceFlag;

}
