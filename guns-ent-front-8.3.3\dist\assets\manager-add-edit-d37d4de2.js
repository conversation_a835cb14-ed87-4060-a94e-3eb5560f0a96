import{r as s,o as R,cf as v,a as _,f as q,w as O,d as j,m as k,M as w}from"./index-18a1ea24.js";import A from"./manager-form-ed3d923e.js";import{T as L}from"./ThemeTemplateApi-ac0cf8e8.js";/* empty css              */import"./FileApi-418f4d35.js";const M={__name:"manager-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(h,{emit:y}){const r=h,f=y,u=s(!1),o=s(!1),t=s({positionSort:1e3}),c=s(null),g=s({templateId:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u8D26\u53F7",type:"string",trigger:"blur"}],themeName:[{required:!0,message:"\u8BF7\u8F93\u5165\u4E3B\u9898\u540D\u79F0",type:"string",trigger:"blur"}]}),i=s([]),d=s(!1),n=s({});R(()=>{r.data?(o.value=!0,d.value=!0,b(r.data.templateId).then(()=>{T(r.data)})):(o.value=!1,d.value=!1)});const b=async a=>{i.value=await L.detail({templateId:a}),I(),F()},I=()=>{let a={templateId:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u8D26\u53F7",type:"string",trigger:"blur"}],themeName:[{required:!0,message:"\u8BF7\u8F93\u5165\u4E3B\u9898\u540D\u79F0",type:"string",trigger:"blur"}]};for(let e in i.value){const l=i.value[e];l.fieldRequired==="Y"&&(a[l.fieldCode]=[{required:!0,message:"\u8BF7\u8F93\u5165".concat(l.fieldDescription),type:"string",trigger:"blur"}])}g.value=a},F=()=>{for(let a in i.value){const e=i.value[a];e.fieldType==="file"&&(n.value[e.fieldCode]=[])}},T=async a=>{t.value.themeId=a.themeId,t.value.themeName=a.themeName,t.value.templateId=a.templateId;let e=await v.detail({themeId:t.value.themeId});t.value=Object.assign(t.value,e.dynamicForm),n.value=e.tempFileList},N=()=>{let a=t.value.themeName,e=t.value.templateId,l=t.value.themeId,m=JSON.stringify(t.value);t.value={},l&&(t.value.themeId=l),t.value.themeName=a,t.value.templateId=e,t.value.themeValue=m},p=a=>{f("update:visible",a)},C=async()=>{c.value.$refs.formRef.validate().then(async a=>{if(a){u.value=!0;let e=null;N(),o.value?e=v.edit(t.value):e=v.add(t.value),e.then(async l=>{u.value=!1,k.success(l.message),p(!1),f("done")}).catch(()=>{u.value=!1})}})};return(a,e)=>{const l=w;return _(),q(l,{width:900,maskClosable:!1,visible:r.visible,"confirm-loading":u.value,forceRender:!0,title:o.value?"\u7F16\u8F91\u4E3B\u9898":"\u65B0\u5EFA\u4E3B\u9898","body-style":{paddingBottom:"8px"},"onUpdate:visible":p,onOk:C,class:"common-modal",onClose:e[2]||(e[2]=m=>p(!1))},{default:O(()=>[j(A,{form:t.value,"onUpdate:form":e[0]||(e[0]=m=>t.value=m),rules:g.value,disabledChangeTemplate:d.value,tempFileList:n.value,"onUpdate:tempFileList":e[1]||(e[1]=m=>n.value=m),onGetThemeAttributes:b,templateFields:i.value,ref_key:"managerFormRef",ref:c},null,8,["form","rules","disabledChangeTemplate","tempFileList","templateFields"])]),_:1},8,["visible","confirm-loading","title"])}}};export{M as default};
