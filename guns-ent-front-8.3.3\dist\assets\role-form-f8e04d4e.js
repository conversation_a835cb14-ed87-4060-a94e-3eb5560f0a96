import{s as A,r as _,X as I,a as d,f as s,w as l,d as o,g as f,h as y,l as L,u as R,v as z,y as B,z as V,A as D,as as H,$,G as j,H as G}from"./index-18a1ea24.js";import{_ as O}from"./index-3a0e5c06.js";/* empty css              *//* empty css              */import{R as P}from"./RoleTypeApi-abe10d8b.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const ae={__name:"role-form",props:{form:Object,superAdminFlag:Boolean},setup(a){const t=a,T=A({roleName:[{required:!0,message:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0",type:"string",trigger:"blur"}],roleCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u89D2\u8272\u7F16\u7801",type:"string",trigger:"blur"}],roleSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u6392\u5E8F",type:"number",trigger:"blur"}],statusFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",type:"number",trigger:"change"}],roleType:[{required:!0,message:"\u8BF7\u9009\u62E9\u89D2\u8272\u7C7B\u578B",type:"number",trigger:"change"}],roleCategoryId:[{required:!0,message:"\u8BF7\u9009\u62E9\u89D2\u8272\u5206\u7C7B",type:"string",trigger:"change"}],roleCompanyIdWrapper:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u516C\u53F8",type:"string",trigger:"change"}]}),g=_(!1),c=_({selectCompanyList:[]}),v=_([]),w=()=>{c.value.selectCompanyList=[{bizId:t.form.roleCompanyId,name:t.form.roleCompanyIdWrapper}],g.value=!0},x=n=>{const{bizId:e,name:p}=n.selectCompanyList[0];t.form.roleCompanyId=e,t.form.roleCompanyIdWrapper=p,t.form.roleCategoryId=void 0,C()},k=({target:n})=>{t.form.roleCompanyId=null,t.form.roleCompanyIdWrapper=null,t.form.roleCategoryId=void 0},C=async()=>{var e;const n=await P.treeList({categoryType:t.form.roleType,companyId:(e=t.form)==null?void 0:e.roleCompanyId});v.value=n.data};return I(()=>{var n;return(n=t.form)==null?void 0:n.roleType},n=>{[15].includes(n)&&C()},{deep:!0,immediate:!0}),I(()=>{var n,e;return[(n=t.form)==null?void 0:n.roleCompanyId,(e=t.form)==null?void 0:e.roleType]},n=>{n[0]&&n[1]?C():v.value=[]},{deep:!0,immediate:!0}),(n,e)=>{const p=L,m=R,u=z,F=B,i=V,b=D,N=H,S=$,U=j,W=O,q=G;return d(),s(q,{ref:"formRef",model:a.form,rules:T,layout:"vertical"},{default:l(()=>[o(U,{gutter:20},{default:l(()=>[o(u,{xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u89D2\u8272\u540D\u79F0:",name:"roleName"},{default:l(()=>[o(p,{value:a.form.roleName,"onUpdate:value":e[0]||(e[0]=r=>a.form.roleName=r),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),o(u,{xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u89D2\u8272\u7F16\u7801:",name:"roleCode"},{default:l(()=>[o(p,{value:a.form.roleCode,"onUpdate:value":e[1]||(e[1]=r=>a.form.roleCode=r),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),o(u,{xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u6392\u5E8F:",name:"roleSort"},{default:l(()=>[o(F,{value:a.form.roleSort,"onUpdate:value":e[2]||(e[2]=r=>a.form.roleSort=r),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),o(u,{xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u72B6\u6001:",name:"statusFlag"},{default:l(()=>[o(b,{value:a.form.statusFlag,"onUpdate:value":e[3]||(e[3]=r=>a.form.statusFlag=r)},{default:l(()=>[o(i,{value:1},{default:l(()=>e[9]||(e[9]=[f("\u542F\u7528")])),_:1,__:[9]}),o(i,{value:2},{default:l(()=>e[10]||(e[10]=[f("\u7981\u7528")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1})]),_:1}),o(u,{xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u89D2\u8272\u7C7B\u578B:",name:"roleType"},{default:l(()=>[o(b,{value:a.form.roleType,"onUpdate:value":e[4]||(e[4]=r=>a.form.roleType=r),onChange:k},{default:l(()=>[t.superAdminFlag?(d(),s(i,{key:0,value:10},{default:l(()=>e[11]||(e[11]=[f("\u7CFB\u7EDF\u89D2\u8272")])),_:1,__:[11]})):y("",!0),t.superAdminFlag?(d(),s(i,{key:1,value:15},{default:l(()=>e[12]||(e[12]=[f("\u4E1A\u52A1\u89D2\u8272")])),_:1,__:[12]})):y("",!0),o(i,{value:20},{default:l(()=>e[13]||(e[13]=[f("\u516C\u53F8\u89D2\u8272")])),_:1,__:[13]})]),_:1},8,["value"])]),_:1})]),_:1}),a.form.roleType==20?(d(),s(u,{key:0,xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u6240\u5C5E\u516C\u53F8:",name:"roleCompanyIdWrapper"},{default:l(()=>[o(p,{value:a.form.roleCompanyIdWrapper,"onUpdate:value":e[5]||(e[5]=r=>a.form.roleCompanyIdWrapper=r),onFocus:w,disabled:!t.superAdminFlag,placeholder:"\u8BF7\u9009\u62E9\u6240\u5C5E\u516C\u53F8"},null,8,["value","disabled"])]),_:1})]),_:1})):y("",!0),a.form.roleType==15||a.form.roleType==20&&a.form.roleCompanyId?(d(),s(u,{key:1,xs:24,sm:24,md:12},{default:l(()=>[o(m,{label:"\u89D2\u8272\u5206\u7C7B:",name:"roleCategoryId"},{default:l(()=>[o(N,{value:a.form.roleCategoryId,"onUpdate:value":e[6]||(e[6]=r=>a.form.roleCategoryId=r),style:{width:"100%"},showSearch:"","tree-data":v.value,treeNodeFilterProp:"roleCategoryName","dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272\u5206\u7C7B",fieldNames:{children:"children",label:"roleCategoryName",key:"id",value:"id"},"allow-clear":"","tree-default-expand-all":""},null,8,["value","tree-data"])]),_:1})]),_:1})):y("",!0),o(u,{span:24},{default:l(()=>[o(m,{label:"\u5907\u6CE8"},{default:l(()=>[o(S,{value:a.form.remark,"onUpdate:value":e[7]||(e[7]=r=>a.form.remark=r),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:4},null,8,["value"])]),_:1})]),_:1})]),_:1}),g.value?(d(),s(W,{key:0,visible:g.value,"onUpdate:visible":e[8]||(e[8]=r=>g.value=r),title:"\u9009\u62E9\u516C\u53F8",data:c.value,showTab:["company"],onDone:x},null,8,["visible","data"])):y("",!0)]),_:1},8,["model","rules"])}}};export{ae as default};
