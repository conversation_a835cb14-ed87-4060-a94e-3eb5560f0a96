import{_ as O,r as s,bh as $,bi as j,L as z,o as A,bj as E,X as S,k as D,a as _,c as F,d as g,w as y,f as J,h as R,b as T,m as f,bk as B,M as X,a0 as G}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{F as q,a as U}from"./FileApi-418f4d35.js";const H={class:"wh100"},K=["src"],Q={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},normal:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(h,{emit:u}){const a=h,r=u,n=s([]),b=s("".concat($).concat(q,"?secretFlag=N")),c=s({Authorization:j()}),l=s(null),d=s(!1),v=s(null),m=s(!1),i=z(()=>{var e;return!!((e=a.record)!=null&&e.itemMultipleChoiceFlag||a.multiple)});A(()=>{I(),w()});const I=()=>{a.value?i.value?l.value=a.normal?a.value:JSON.parse(a.value):l.value=[a.value]:l.value=[]},w=()=>{var e;m.value||((e=l.value)==null?void 0:e.length)!=0&&(U.getAntdVInfoBatch({fileIdList:l.value}).then(t=>{t.data.forEach((o,p)=>{o.fileId=l.value[p]}),n.value=E(t.data)}),m.value=!1)},V=e=>{const t=e.type==="image/jpeg"||e.type==="image/jpg"||e.type==="image/png"||e.type==="image/tif"||e.type==="image/jfif"||e.type==="image/webp"||e.type==="image/pjp"||e.type==="image/apng"||e.type==="image/pjpeg"||e.type==="image/avif"||e.type==="image/ico"||e.type==="image/tiff"||e.type==="image/bmp"||e.type==="image/xbm"||e.type==="image/jxl"||e.type==="image/svgz"||e.type==="image/gif"||e.type==="image/svg";if(!t)return f.error("\u53EA\u80FD\u4E0A\u4F20\u56FE\u7247!"),B.LIST_IGNORE;const o=e.size/1024/1024<5;return o||f.error("\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC75MB!"),t&&o},k=async e=>{v.value=e.url||e.preview||e.thumbUrl,d.value=!0},L=e=>{e.file.status==="done"?(e.file.fileId=e.file.response.data.fileId,m.value=!0,i.value?l.value.push(e.file.response.data.fileId):l.value=[e.file.response.data.fileId],x(),f.success("".concat(e.file.name," \u56FE\u7247\u4E0A\u4F20\u6210\u529F"))):e.file.status==="error"&&f.error("".concat(e.file.name," \u56FE\u7247\u4E0A\u4F20\u5931\u8D25"))},P=e=>{U.download({token:j(),fileId:e.fileId})},x=()=>{var t;let e;i.value?e=a.normal?l.value:JSON.stringify(l.value):e=(t=l.value[0])!=null?t:"",r("update:value",e),r("onChange",a.record)},M=e=>{let t=e.fileId;if(t){let o=n.value.findIndex(p=>p.fileId===t);o!==-1&&(n.value.splice(o,1),l.value.splice(o,1),m.value=!0,x())}};return S(()=>a.value,e=>{I(),w()},{deep:!0}),(e,t)=>{const o=D("plus-outlined"),p=B,N=X;return _(),F("div",H,[g(p,{name:"file",multiple:i.value,"file-list":n.value,"onUpdate:fileList":t[0]||(t[0]=C=>n.value=C),"default-file-list":n.value,maxCount:i.value?1e7:1,disabled:a.readonly||a.disabled,action:b.value,"list-type":"picture-card",headers:c.value,"before-upload":V,accept:".jpeg,.jpg,.png,.tif,.jfif,.webp,.pjp,.apng,.pjpeg,.avif,.ico,.tiff,.bmp,.xbm,.jxl,.jpeg,.svgz,.gif,.svg",onPreview:k,onDownload:P,onChange:L,onRemove:M,showUploadList:{showDownloadIcon:!0}},{default:y(()=>[(i.value?!(a.readonly||a.disabled):n.value.length==0)?(_(),J(o,{key:0,style:{"font-size":"28px","font-weight":"200"}})):R("",!0)]),_:1},8,["multiple","file-list","default-file-list","maxCount","disabled","action","headers"]),g(N,{visible:d.value,footer:null,onCancel:t[1]||(t[1]=C=>d.value=!1)},{default:y(()=>[T("img",{alt:"example",style:{width:"100%"},src:v.value},null,8,K)]),_:1},8,["visible"])])}}},W=O(Q,[["__scopeId","data-v-7bed3e1d"]]),Y={class:"guns-body guns-body-card"},le={__name:"index",setup(h){const u=s(""),a=s(!1),r=s(!1),n=()=>{console.log(u.value)};return(b,c)=>{const l=W,d=G;return _(),F("div",Y,[g(d,{title:"\u4E0A\u4F20\u56FE\u7247",bordered:!1},{default:y(()=>[g(l,{value:u.value,"onUpdate:value":c[0]||(c[0]=v=>u.value=v),disabled:a.value,readonly:r.value,onOnChange:n},null,8,["value","disabled","readonly"])]),_:1})])}}};export{le as default};
