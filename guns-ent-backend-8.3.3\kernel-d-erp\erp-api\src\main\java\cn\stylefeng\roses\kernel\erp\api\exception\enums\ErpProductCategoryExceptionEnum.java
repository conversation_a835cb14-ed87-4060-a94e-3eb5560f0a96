package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 产品分类异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
@Getter
public enum ErpProductCategoryExceptionEnum implements AbstractExceptionEnum {

    /**
     * 分类不存在
     */
    CATEGORY_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "分类不存在"),

    /**
     * 分类编码重复
     */
    CATEGORY_CODE_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "分类编码重复"),

    /**
     * 父级分类不存在
     */
    PARENT_CATEGORY_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "父级分类不存在"),

    /**
     * 不能选择自己作为父级分类
     */
    CANNOT_SELECT_SELF_AS_PARENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10004", "不能选择自己作为父级分类"),

    /**
     * 分类层级超过最大深度
     */
    CATEGORY_LEVEL_EXCEED_MAX_DEPTH(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10005", "分类层级超过最大深度"),

    /**
     * 分类状态错误
     */
    CATEGORY_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10006", "分类状态错误"),

    /**
     * 分类下存在子分类，不能删除
     */
    CATEGORY_HAS_CHILDREN_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10007", "分类下存在子分类，不能删除"),

    /**
     * 分类下存在商品，不能删除
     */
    CATEGORY_HAS_PRODUCTS_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10008", "分类下存在商品，不能删除"),

    /**
     * 分类已被使用，不能停用
     */
    CATEGORY_IN_USE_CANNOT_DISABLE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10009", "分类已被使用，不能停用");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ErpProductCategoryExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }
}
