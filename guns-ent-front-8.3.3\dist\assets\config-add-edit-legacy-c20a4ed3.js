System.register(["./index-legacy-ee1db0c7.js","./config-form-legacy-939b0d24.js"],(function(e,a){"use strict";var t,l,o,i,n,s,d,u,r,c;return{setters:[e=>{t=e.r,l=e.o,o=e.bX,i=e.a,n=e.f,s=e.w,d=e.d,u=e.m,r=e.M},e=>{c=e.default}],execute:function(){e("default",{__name:"config-add-edit",props:{visible:Boolean,data:Object,groupCode:String},emits:["update:visible","done"],setup(e,{emit:a}){const f=e,v=a,g=t(!1),m=t(!1),p=t({sysFlag:"Y",groupCode:f.groupCode}),b=t(null);l((()=>{f.data?(m.value=!0,y()):m.value=!1}));const y=()=>{o.detail({configId:f.data.configId}).then((e=>{p.value=Object.assign({},e)}))},h=e=>{v("update:visible",e)},C=async()=>{b.value.$refs.formRef.validate().then((async e=>{if(e){g.value=!0;let e=null;e=m.value?o.edit(p.value):o.add(p.value),e.then((async e=>{g.value=!1,u.success(e.message),h(!1),v("done")})).catch((()=>{g.value=!1}))}}))};return(e,a)=>{const t=r;return i(),n(t,{width:700,maskClosable:!1,visible:f.visible,"confirm-loading":g.value,forceRender:!0,title:m.value?"编辑配置":"新建配置","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":h,onOk:C,onClose:a[1]||(a[1]=e=>h(!1))},{default:s((()=>[d(c,{form:p.value,"onUpdate:form":a[0]||(a[0]=e=>p.value=e),ref_key:"configFormRef",ref:b,isUpdate:m.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
