System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./productCategoryApi-legacy-247b2407.js"],(function(e,a){"use strict";var t,r,l,o,d,n,u,s,c,i,m,f,g,v,y,p,h,_,b,w,k,C;return{setters:[e=>{t=e._,r=e.r,l=e.s,o=e.X,d=e.a,n=e.f,u=e.w,s=e.d,c=e.g,i=e.m,m=e.l,f=e.u,g=e.v,v=e.G,y=e.as,p=e.y,h=e.W,_=e.J,b=e.$,w=e.H,k=e.M},null,null,e=>{C=e.P}],execute:function(){var a=document.createElement("style");a.textContent=".ant-form-item[data-v-e8d947ed]{margin-bottom:16px}\n",document.head.appendChild(a);const I={name:"ProductCategoryEditForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:a}){const t=r(),d=r(!1),n=r([]),u=r("1级 - 一级分类"),s=l({categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),c=e=>{a("update:visible",e),e||m()},m=()=>{var e;null===(e=t.value)||void 0===e||e.resetFields(),Object.assign(s,{categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),u.value="1级 - 一级分类"},f=()=>{const e=(e=>{if(!e)return 1;const a=(e,t)=>{for(const r of e){if(r.categoryId===t)return r.categoryLevel||1;if(r.children&&r.children.length>0){const e=a(r.children,t);if(e)return e}}return null},t=a(n.value,e);return t?Math.min(t+1,5):1})(s.parentId);s.categoryLevel=e,u.value=(e=>({1:"1级 - 一级分类",2:"2级 - 二级分类",3:"3级 - 三级分类",4:"4级 - 四级分类",5:"5级 - 五级分类"}[e]||`${e}级 - 未知`))(e)},g=e=>Array.isArray(e)?e.filter((e=>!!(e&&e.categoryId&&e.categoryName)&&(e.title=e.categoryName,e.key=String(e.categoryId),e.value=String(e.categoryId),e.children&&Array.isArray(e.children)&&(e.children=g(e.children)),!0))):[];return o((()=>e.data),(e=>{e&&Object.keys(e).length>0&&(Object.assign(s,e),f())}),{immediate:!0}),o((()=>e.visible),(e=>{e&&(async()=>{try{let e=await C.findTree()||[];e=g(e),n.value=e}catch(e){console.error("加载分类树失败:",e),n.value=[]}})()})),o((()=>s.parentId),(()=>{f()})),{formRef:t,loading:d,form:s,rules:{categoryCode:[{required:!0,message:"请输入分类编码",trigger:"blur"}],categoryName:[{required:!0,message:"请输入分类名称",trigger:"blur"}],categoryLevel:[{required:!0,message:"请选择分类层级",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},categoryTreeData:n,categoryLevelText:u,updateVisible:c,save:async()=>{try{await t.value.validate(),d.value=!0,await C.edit(s),i.success("编辑成功"),a("ok"),c(!1)}catch(e){console.error("编辑分类失败:",e),i.error("编辑失败")}finally{d.value=!1}},edit:async e=>{Object.assign(s,e),f()},filterTreeNode:(e,a)=>a.title&&a.title.toLowerCase().includes(e.toLowerCase())}}};e("default",t(I,[["render",function(e,a,t,r,l,o){const i=m,C=f,I=g,x=v,O=y,L=p,N=h,j=_,T=b,U=w,A=k;return d(),n(A,{title:"编辑产品分类",visible:t.visible,"confirm-loading":r.loading,width:800,onOk:r.save,onCancel:a[7]||(a[7]=e=>r.updateVisible(!1))},{default:u((()=>[s(U,{ref:"formRef",model:r.form,rules:r.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:u((()=>[s(x,{gutter:16},{default:u((()=>[s(I,{md:12,sm:24},{default:u((()=>[s(C,{label:"分类编码",name:"categoryCode"},{default:u((()=>[s(i,{value:r.form.categoryCode,"onUpdate:value":a[0]||(a[0]=e=>r.form.categoryCode=e),placeholder:"请输入分类编码","allow-clear":""},null,8,["value"])])),_:1})])),_:1}),s(I,{md:12,sm:24},{default:u((()=>[s(C,{label:"分类名称",name:"categoryName"},{default:u((()=>[s(i,{value:r.form.categoryName,"onUpdate:value":a[1]||(a[1]=e=>r.form.categoryName=e),placeholder:"请输入分类名称","allow-clear":""},null,8,["value"])])),_:1})])),_:1})])),_:1}),s(x,{gutter:16},{default:u((()=>[s(I,{md:12,sm:24},{default:u((()=>[s(C,{label:"父级分类",name:"parentId"},{default:u((()=>[s(O,{value:r.form.parentId,"onUpdate:value":a[2]||(a[2]=e=>r.form.parentId=e),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":r.categoryTreeData,placeholder:"请选择父级分类","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":r.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])])),_:1})])),_:1}),s(I,{md:12,sm:24},{default:u((()=>[s(C,{label:"分类层级",name:"categoryLevel"},{default:u((()=>[s(i,{value:r.categoryLevelText,"onUpdate:value":a[3]||(a[3]=e=>r.categoryLevelText=e),placeholder:"根据父级分类自动设置",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),s(x,{gutter:16},{default:u((()=>[s(I,{md:12,sm:24},{default:u((()=>[s(C,{label:"排序号",name:"sortOrder"},{default:u((()=>[s(L,{value:r.form.sortOrder,"onUpdate:value":a[4]||(a[4]=e=>r.form.sortOrder=e),placeholder:"请输入排序号",min:0,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),s(I,{md:12,sm:24},{default:u((()=>[s(C,{label:"状态",name:"status"},{default:u((()=>[s(j,{value:r.form.status,"onUpdate:value":a[5]||(a[5]=e=>r.form.status=e),placeholder:"请选择状态"},{default:u((()=>[s(N,{value:"Y"},{default:u((()=>a[8]||(a[8]=[c("启用")]))),_:1,__:[8]}),s(N,{value:"N"},{default:u((()=>a[9]||(a[9]=[c("停用")]))),_:1,__:[9]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),s(x,{gutter:16},{default:u((()=>[s(I,{span:24},{default:u((()=>[s(C,{label:"备注",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:u((()=>[s(T,{value:r.form.remark,"onUpdate:value":a[6]||(a[6]=e=>r.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading","onOk"])}],["__scopeId","data-v-e8d947ed"]]))}}}));
