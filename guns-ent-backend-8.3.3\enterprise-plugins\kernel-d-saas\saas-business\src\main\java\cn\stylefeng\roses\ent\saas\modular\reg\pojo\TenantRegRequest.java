package cn.stylefeng.roses.ent.saas.modular.reg.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.validator.api.validators.unique.TableUniqueValue;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户注册的请求类
 *
 * <AUTHOR>
 * @since 2024-02-22 17:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantRegRequest extends BaseRequest {

    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空")
    @ChineseDescription("租户名称")
    private String tenantName;

    /**
     * 租户唯一标识
     */
    @NotBlank(message = "租户唯一标识不能为空")
    @ChineseDescription("租户唯一标识")
    @TableUniqueValue(message = "租户唯一标识已经存在，请更换租户编码",
            tableName = "ent_tenant",
            columnName = "tenant_code",
            idFieldName = "tenant_id",
            excludeLogicDeleteItems = true)
    private String tenantCode;

    /**
     * 注册邮箱
     */
    @NotBlank(message = "注册邮箱不能为空")
    @ChineseDescription("注册邮箱")
    @TableUniqueValue(message = "注册邮箱已经存在，请更换注册邮箱",
            tableName = "ent_tenant",
            columnName = "email",
            idFieldName = "tenant_id",
            excludeLogicDeleteItems = true)
    private String email;

    /**
     * 安全手机（注册时的手机号）
     */
    @NotBlank(message = "安全手机（注册时的手机号）不能为空")
    @ChineseDescription("安全手机（注册时的手机号）")
    private String safePhone;

    /**
     * 密码，加密方式为MD5
     */
    @NotBlank(message = "密码，加密方式为MD5，不能为空")
    @ChineseDescription("密码，加密方式为MD5")
    private String password;

    /**
     * 租户logo，存储文件id
     */
    @ChineseDescription("租户logo，存储文件id")
    private Long tenantLogo;

    /**
     * 公司名称
     */
    @ChineseDescription("公司名称")
    private String companyName;

    /**
     * 邮箱验证码
     */
    @ChineseDescription("邮箱验证码")
    private String emailValidateCode;

    //-------------------------------图形验证码-------------------------------

    /**
     * 用在图形验证码或者拖拽验证码
     */
    @ChineseDescription("用在图形验证码或者拖拽验证码")
    @NotBlank(message = "图形验证码key不能为空")
    private String verKey;

}
