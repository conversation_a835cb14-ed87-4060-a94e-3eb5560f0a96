package cn.stylefeng.roses.kernel.license;

import cn.hutool.core.util.RandomUtil;

/**
 * xjar打包示例
 *
 * <AUTHOR>
 * @date 2022/4/2 15:23
 */
public class TestEncryptJarUtil {

    public static void main(String[] args) throws Exception {

        String s = RandomUtil.randomString(52);
        System.out.println(s);

        //  from：    被加密的jar包
        //  use：     jar包加密的密码
        //  include： 指定要加密的资源，使用classpath路径的的ANT路径表达式
        //  exclude： 不加密的资源，使用classpath路径的的ANT路径表达式
        //  to:       指定加密后JAR包输出路径, 并执行加密
//        XCryptos.encryption()
//                .from("C:\\Users\\<USER>\\Desktop\\其他\\generator\\guns.jar")
//                .use("9ockmeciztugy0t48e0ogf8hfljvvajymtkio")
//                .include("/cn/stylefeng/**/*.class")
//                .include("/cn/stylefeng/**/*Mapper.xml")
//                .exclude("/assets/**/*")
//                .exclude("/pages/**/*")
//                .to("C:\\Users\\<USER>\\Desktop\\其他\\generator\\generator-encrypt.jar");
    }

}
