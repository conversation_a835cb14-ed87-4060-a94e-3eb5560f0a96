package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.erp.api.constants.ErpConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 客户模块异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
@Getter
public enum ErpCustomerExceptionEnum implements AbstractExceptionEnum {

    /**
     * 客户不存在
     */
    CUSTOMER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "41", "客户不存在"),

    /**
     * 客户编码重复
     */
    CUSTOMER_CODE_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "42", "客户编码重复"),

    /**
     * 客户名称重复
     */
    CUSTOMER_NAME_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "43", "客户名称重复"),

    /**
     * 客户状态不正确
     */
    CUSTOMER_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "44", "客户状态不正确"),

    /**
     * 客户类型不正确
     */
    CUSTOMER_TYPE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "45", "客户类型不正确"),

    /**
     * 客户等级不正确
     */
    CUSTOMER_LEVEL_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "46", "客户等级不正确"),

    /**
     * 客户已停用，无法操作
     */
    CUSTOMER_INACTIVE_CANNOT_OPERATE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "47", "客户已停用，无法操作"),

    /**
     * 客户已冻结，无法操作
     */
    CUSTOMER_FROZEN_CANNOT_OPERATE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "48", "客户已冻结，无法操作"),

    /**
     * 客户存在关联业务数据，无法删除
     */
    CUSTOMER_HAS_BUSINESS_DATA_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "49", "客户存在关联业务数据，无法删除"),

    /**
     * 客户存在关联业务数据，无法停用
     */
    CUSTOMER_HAS_BUSINESS_DATA_CANNOT_INACTIVE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "50", "客户存在关联业务数据，无法停用"),

    /**
     * 客户区域不存在
     */
    CUSTOMER_REGION_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "51", "客户所属区域不存在"),

    /**
     * 客户联系方式格式不正确
     */
    CUSTOMER_CONTACT_FORMAT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "52", "客户联系方式格式不正确"),

    /**
     * 信用额度不能小于已用额度
     */
    CUSTOMER_CREDIT_LIMIT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "53", "信用额度不能小于已用额度"),

    /**
     * 账期天数格式不正确
     */
    CUSTOMER_PAYMENT_TERMS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "54", "账期天数格式不正确");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ErpCustomerExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
