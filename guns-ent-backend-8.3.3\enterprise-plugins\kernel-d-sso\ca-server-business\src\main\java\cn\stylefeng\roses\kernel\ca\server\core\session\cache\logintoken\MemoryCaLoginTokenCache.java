package cn.stylefeng.roses.kernel.ca.server.core.session.cache.logintoken;

import cn.hutool.cache.impl.TimedCache;
import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.cache.memory.AbstractMemoryCacheOperator;

import java.util.Set;

/**
 * 基于内存的token缓存
 * <p>
 * key：    用户id
 * value：  用户的所有CAID，用户在一个机器登录一次单点就会有一个CAID
 *
 * <AUTHOR>
 * @date 2020/12/24 19:16
 */
public class MemoryCaLoginTokenCache extends AbstractMemoryCacheOperator<Set<String>> {

    public MemoryCaLoginTokenCache(TimedCache<String, Set<String>> timedCache) {
        super(timedCache);
    }

    @Override
    public String getCommonKeyPrefix() {
        return CaServerConstants.CA_USER_ID_CACHE_PREFIX;
    }

}
