/**
 * ERP业务状态管理统一导出
 * 
 * 该文件用于统一导出所有ERP业务相关的Pinia store
 * 便于在组件中进行统一引用和管理
 */

// 供应商状态管理
export { useSupplierStore } from './supplier'

// 商品状态管理
export { useProductStore } from './product'

// 采购入库状态管理
export { usePurchaseStore } from './purchase'

// 库存管理状态管理
export { useInventoryStore } from './inventory'

// 所有ERP业务状态管理已创建完成
// 可以通过以下方式在组件中使用：
// import { useSupplierStore, useProductStore, usePurchaseStore, useInventoryStore } from '@/store/modules/erp'