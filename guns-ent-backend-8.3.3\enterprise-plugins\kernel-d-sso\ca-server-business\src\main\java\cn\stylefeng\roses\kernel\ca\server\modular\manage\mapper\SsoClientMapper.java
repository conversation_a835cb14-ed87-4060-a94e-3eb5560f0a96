package cn.stylefeng.roses.kernel.ca.server.modular.manage.mapper;

import cn.stylefeng.roses.kernel.ca.server.modular.manage.entity.SsoClient;
import cn.stylefeng.roses.kernel.ca.server.modular.manage.pojo.request.SsoClientRequest;
import cn.stylefeng.roses.kernel.ca.server.modular.manage.pojo.response.SsoClientVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单点登录客户端 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023/11/05 09:28
 */
public interface SsoClientMapper extends BaseMapper<SsoClient> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    List<SsoClientVo> customFindList(@Param("page") Page page, @Param("param")SsoClientRequest request);

}
