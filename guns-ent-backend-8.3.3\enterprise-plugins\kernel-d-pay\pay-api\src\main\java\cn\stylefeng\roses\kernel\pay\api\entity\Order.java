package cn.stylefeng.roses.kernel.pay.api.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseBusinessEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 订单实例类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@TableName(value = "shop_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class Order extends BaseBusinessEntity {

    /**
     * 订单id
     */
    @TableId(value = "order_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("订单id")
    private Long orderId;

    /**
     * 订单号，时间戳加6位随机字符串
     */
    @TableField("order_number")
    @ChineseDescription("订单号，时间戳加6位随机字符串")
    private String orderNumber;

    /**
     * 订单所属用户id
     */
    @TableField("user_id")
    @ChineseDescription("订单所属用户id")
    private Long userId;

    /**
     * 商品id
     */
    @TableField("goods_id")
    @ChineseDescription("商品id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    @ChineseDescription("商品名称")
    private String goodsName;

    /**
     * 业务的参数
     */
    @TableField(value = "business_params", typeHandler = JacksonTypeHandler.class)
    @ChineseDescription("业务的参数")
    private Map<String, Object> businessParams;

    /**
     * 商品名称
     */
    @TableField("business_process_class")
    @ChineseDescription("商品名称")
    private String businessProcessClass;

    /**
     * 商品原价
     */
    @TableField("original_price")
    @ChineseDescription("商品原价")
    private BigDecimal originalPrice;

    /**
     * 实付金额
     */
    @TableField("pay_price")
    @ChineseDescription("实付金额")
    private BigDecimal payPrice;

    /**
     * 状态，1待支付、2已完成、3已取消、4已退款
     */
    @TableField("state")
    @ChineseDescription("状态，1待支付、2已完成、3已取消、4已退款")
    private Integer state;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    @ChineseDescription("支付时间")
    private Date payTime;

    /**
     * YunGouOS系统单号
     */
    @TableField("order_no")
    @ChineseDescription("YunGouOS系统单号")
    private String orderNo;

    /**
     * 支付单号
     */
    @TableField("pay_no")
    @ChineseDescription("支付单号")
    private String payNo;

    /**
     * 支付渠道：alipay，wxpay
     */
    @TableField("pay_channel")
    @ChineseDescription("支付渠道：alipay，wxpay")
    private String payChannel;

    /**
     * 支付时的签名
     */
    @TableField("sign")
    @ChineseDescription("支付时的签名")
    private String sign;

    /**
     * 用户openId
     */
    @TableField("open_id")
    @ChineseDescription("用户openId")
    private String openId;

    /**
     * 是否开过发票
     */
    @TableField("invoice_flag")
    @ChineseDescription("是否开过发票：Y-开过，N-未开过")
    private String invoiceFlag;

}
