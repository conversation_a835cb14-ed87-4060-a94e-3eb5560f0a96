package cn.stylefeng.roses.ent.saas.modular.auth.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseBusinessEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 租户-功能包实例类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@TableName("ent_tenant_package")
@Data
@EqualsAndHashCode(callSuper = true)
public class TenantPackage extends BaseBusinessEntity {

    /**
     * 主键id
     */
    @TableId(value = "package_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键id")
    private Long packageId;

    /**
     * 功能包名称
     */
    @TableField("package_name")
    @ChineseDescription("功能包名称")
    private String packageName;

    /**
     * 功能包价格
     */
    @TableField("package_price")
    @ChineseDescription("功能包价格")
    private BigDecimal packagePrice;

}
