# 权限管理、菜单管理和角色管理设计架构

## 1. 系统概述

本系统采用基于角色的访问控制（RBAC）模型，实现了用户-角色-权限的三层权限管理架构。系统支持细粒度的权限控制，包括菜单级别和功能级别的权限管理，同时提供了灵活的数据权限控制机制。

## 2. 数据库表结构设计

### 2.1 核心权限相关表

#### 2.1.1 用户表 (sys_user)
```sql
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL COMMENT '主键',
  `real_name` varchar(100) COMMENT '姓名',
  `account` varchar(50) NOT NULL COMMENT '账号',
  `password` varchar(100) NOT NULL COMMENT '密码，加密方式为MD5',
  `super_admin_flag` char(1) COMMENT '是否是超级管理员：Y-是，N-否',
  `status_flag` tinyint(4) COMMENT '状态：1-正常，2-冻结，3-临时冻结',
  PRIMARY KEY (`user_id`)
);
```

**主要字段说明**：
- `user_id`: 用户主键ID
- `account`: 用户登录账号
- `super_admin_flag`: 超级管理员标识，超级管理员拥有所有权限
- `status_flag`: 用户状态控制

#### 2.1.2 角色表 (sys_role)
```sql
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL COMMENT '主键id',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `role_code` varchar(100) NOT NULL COMMENT '角色编码',
  `role_type` tinyint(4) NOT NULL DEFAULT 10 COMMENT '角色类型：10-系统角色，15-业务角色，20-公司角色',
  `data_scope_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '数据范围类型：10-仅本人数据，20-本部门数据，30-本部门及以下数据，40-指定部门数据，50-全部数据',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  PRIMARY KEY (`role_id`)
);
```

**主要字段说明**：
- `role_type`: 角色类型，支持系统角色、业务角色、公司角色
- `data_scope_type`: 数据权限范围类型

#### 2.1.3 菜单表 (sys_menu)
```sql
CREATE TABLE `sys_menu` (
  `menu_id` bigint(20) NOT NULL COMMENT '主键',
  `menu_parent_id` bigint(20) NOT NULL COMMENT '父id，顶级节点的父id是-1',
  `menu_pids` varchar(1000) NOT NULL COMMENT '父id集合，中括号包住，逗号分隔',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单的名称',
  `menu_code` varchar(50) NOT NULL COMMENT '菜单的编码',
  `app_id` bigint(20) NOT NULL COMMENT '所属应用id',
  `menu_type` tinyint(4) COMMENT '菜单类型：10-后台菜单，20-纯前台路由界面，30-内部链接，40-外部链接，50-应用设计',
  `antdv_router` varchar(255) COMMENT '路由地址，浏览器显示的URL',
  `antdv_component` varchar(255) COMMENT '前端组件名',
  PRIMARY KEY (`menu_id`)
);
```

**主要字段说明**：
- `menu_pids`: 存储完整的父级路径，便于树形结构查询
- `menu_type`: 支持多种菜单类型
- `antdv_router`: 前端路由地址
- `antdv_component`: 对应的前端组件

#### 2.1.4 菜单功能表 (sys_menu_options)
```sql
CREATE TABLE `sys_menu_options` (
  `menu_option_id` bigint(20) NOT NULL COMMENT '主键',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单id',
  `option_name` varchar(100) NOT NULL COMMENT '功能或操作的名称',
  `option_code` varchar(100) NOT NULL COMMENT '功能或操作的编码',
  PRIMARY KEY (`menu_option_id`)
);
```

**主要字段说明**：
- `option_code`: 功能编码，对应后端接口的权限编码

### 2.2 关联关系表

#### 2.2.1 用户角色关联表 (sys_user_role)
```sql
CREATE TABLE `sys_user_role` (
  `user_role_id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `role_type` tinyint(4) NOT NULL DEFAULT 10 COMMENT '角色类型：10-系统角色，15-业务角色，20-公司角色',
  `role_org_id` bigint(20) COMMENT '用户所属机构id',
  PRIMARY KEY (`user_role_id`)
);
```

#### 2.2.2 角色菜单关联表 (sys_role_menu)
```sql
CREATE TABLE `sys_role_menu` (
  `role_menu_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单id',
  `app_id` bigint(20) COMMENT '冗余字段，菜单所属的应用id',
  PRIMARY KEY (`role_menu_id`)
);
```

#### 2.2.3 角色菜单功能关联表 (sys_role_menu_options)
```sql
CREATE TABLE `sys_role_menu_options` (
  `role_menu_option_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `menu_option_id` bigint(20) NOT NULL COMMENT '菜单功能id',
  `app_id` bigint(20) COMMENT '冗余字段，菜单所属的应用id',
  `menu_id` bigint(20) COMMENT '冗余字段，功能所属的菜单id',
  PRIMARY KEY (`role_menu_option_id`)
);
```

### 2.3 数据权限相关表

#### 2.3.1 角色数据范围表 (sys_role_data_scope)
```sql
CREATE TABLE `sys_role_data_scope` (
  `role_data_scope_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `data_scope_type` tinyint(4) NOT NULL DEFAULT 20 COMMENT '数据范围类型：10-仅本人数据，20-本部门数据，30-本部门及以下数据，31-本公司及以下数据，32-指定机构层级及以下，40-指定机构集合数据，41-指定机构及以下，50-全部数据',
  `org_level_code` varchar(100) COMMENT '层级的编码',
  `define_org_list` json COMMENT '指定机构集合列表',
  `define_org_id` bigint(20) COMMENT '指定机构的id',
  PRIMARY KEY (`role_data_scope_id`)
);
```

#### 2.3.2 角色权限限制表 (sys_role_limit)
```sql
CREATE TABLE `sys_role_limit` (
  `role_limit_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `limit_type` tinyint(4) NOT NULL COMMENT '角色限制类型：1-角色可分配的菜单，2-角色可分配的功能',
  `business_id` bigint(20) NOT NULL COMMENT '业务id，为菜单id或菜单功能id',
  PRIMARY KEY (`role_limit_id`)
);
```

## 3. 业务逻辑设计

### 3.1 权限控制实现机制

#### 3.1.1 权限验证流程
1. **Token验证**：验证用户登录状态
2. **资源匹配**：根据请求URL获取资源定义
3. **权限编码获取**：从资源定义中获取所需权限编码
4. **用户权限获取**：获取用户拥有的所有权限编码列表
5. **权限校验**：判断用户是否拥有所需权限

#### 3.1.2 用户权限获取逻辑
```
用户权限 = 用户角色 → 角色菜单权限 + 角色功能权限
```

**详细步骤**：
1. 根据用户ID获取用户拥有的角色列表
2. 根据角色列表获取角色绑定的菜单ID集合
3. 根据角色列表获取角色绑定的菜单功能ID集合
4. 获取菜单对应的权限编码集合
5. 获取菜单功能对应的权限编码集合
6. 合并所有权限编码形成用户最终权限列表

### 3.2 菜单显示控制逻辑

#### 3.2.1 前端菜单获取流程
1. **用户登录后**：调用获取用户信息接口
2. **角色菜单获取**：根据用户角色获取可访问的菜单列表
3. **菜单树构建**：将平铺的菜单数据构建成树形结构
4. **路由生成**：将菜单数据转换为前端路由配置

#### 3.2.2 菜单权限过滤
- 只显示用户角色有权限访问的菜单
- 支持按应用分组显示菜单
- 支持菜单的动态显示/隐藏控制

### 3.3 数据权限控制机制

#### 3.3.1 数据范围类型
- **10-仅本人数据**：只能查看自己创建的数据
- **20-本部门数据**：只能查看本部门的数据
- **30-本部门及以下数据**：可以查看本部门及下级部门的数据
- **31-本公司及以下数据**：可以查看本公司及下级机构的数据
- **40-指定机构集合数据**：只能查看指定机构的数据
- **50-全部数据**：可以查看所有数据

#### 3.3.2 数据权限实现
通过MyBatis-Plus的数据权限插件，在SQL执行时自动添加数据权限过滤条件。

## 4. 代码实现架构

### 4.1 核心实体类

#### 4.1.1 用户实体 (SysUser)
```java
@TableName(value = "sys_user", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUser extends BaseExpandFieldEntity {
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;
    
    @TableField("real_name")
    private String realName;
    
    @TableField("account")
    private String account;
    
    @TableField("super_admin_flag")
    private String superAdminFlag;
    
    @TableField("status_flag")
    private Integer statusFlag;
}
```

#### 4.1.2 角色实体 (SysRole)
```java
@TableName(value = "sys_role", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRole extends BaseExpandFieldEntity {
    @TableId(value = "role_id", type = IdType.ASSIGN_ID)
    private Long roleId;
    
    @TableField("role_name")
    private String roleName;
    
    @TableField("role_code")
    private String roleCode;
    
    @TableField("role_type")
    private Integer roleType;
    
    @TableField("data_scope_type")
    private Integer dataScopeType;
}
```

### 4.2 权限验证核心组件

#### 4.2.1 权限验证拦截器 (TokenAndPermissionInterceptor)
```java
@Override
public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
    String requestURI = request.getRequestURI();
    
    // 获取ResourceDefinition
    ResourceDefinition resourceDefinition = resourceServiceApi.getResourceByUrl(resourceUrlParam);
    
    // 执行token校验
    if (resourceDefinition.getRequiredLoginFlag()) {
        authServiceApi.checkAuth(token, requestURI);
    }
    
    // 执行权限校验
    if (resourceDefinition.getRequiredPermissionFlag()) {
        permissionServiceApi.checkPermission(token, requestURI);
    }
    
    return true;
}
```

#### 4.2.2 权限校验服务 (PermissionCheckServiceImpl)
```java
@Override
public void checkPermission(String token, String requestUrl) {
    // 1. 获取token对应的用户信息
    LoginUser loginUser = sessionManagerApi.getSession(token);
    
    // 2. 获取url对应的资源信息
    ResourceDefinition resourceDefinition = resourceServiceApi.getResourceByUrl(new ResourceUrlParam(requestUrl));
    
    // 3. 获取当前资源需要的权限编码
    String permissionCode = resourceDefinition.getPermissionCode();
    
    // 4. 判断当前用户是否有该权限编码
    List<String> userPermissionCodeList = userPermissionService.getUserPermissionCodeList(loginUser);
    if (ObjectUtil.isNotEmpty(userPermissionCodeList) && userPermissionCodeList.contains(permissionCode)) {
        return;
    } else {
        throw new AuthException(PERMISSION_RES_VALIDATE_ERROR);
    }
}
```

### 4.3 权限注解

#### 4.3.1 API资源注解 (@ApiResource)
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@RequestMapping
public @interface ApiResource {
    /**
     * 资源名称(必填项)
     */
    String name() default "";
    
    /**
     * 当前接口是否需要登录
     */
    boolean requiredLogin() default true;
    
    /**
     * 当前接口是否需要鉴权
     */
    boolean requiredPermission() default false;
    
    /**
     * 当前接口需要的权限标识
     */
    String requirePermissionCode() default StrUtil.EMPTY;
}
```

### 4.4 前端权限控制

#### 4.4.1 权限指令
```javascript
export default {
  install(app) {
    // 权限指令
    app.directive('permission', {
      mounted: (el, binding) => {
        if (!hasPermission(binding.value)) {
          el.parentNode?.removeChild(el);
        }
      }
    });
    
    // 角色指令
    app.directive('role', {
      mounted: (el, binding) => {
        if (!hasRole(binding.value)) {
          el.parentNode?.removeChild(el);
        }
      }
    });
  }
};
```

#### 4.4.2 菜单状态管理
```javascript
async fetchUserInfo(path) {
  // 调用获取用户信息接口
  const result = await getUserInfo().catch(() => undefined);
  
  // 当前应用的菜单和首页
  const { menus, homePath } = formatMenus(
    toTreeData({
      data: activeApp.menuList?.map(d => {
        return {
          ...d,
          path: d.menuType == 40 ? 'http://' + location.host + d.path : d.path
        };
      }),
      idField: 'menuId',
      parentIdField: 'menuParentId'
    })
  );
  
  // 当前激活应用的菜单列表
  this.menus = menus;
}
```

## 5. 新功能添加流程

### 5.1 添加新页面/功能的完整步骤

#### 步骤1：数据库操作

**1. 添加菜单记录 (sys_menu表)**
```sql
INSERT INTO sys_menu (
  menu_id, menu_parent_id, menu_pids, menu_name, menu_code,
  app_id, menu_type, antdv_router, antdv_component, menu_sort
) VALUES (
  新菜单ID, 父菜单ID, '父级路径', '菜单名称', '菜单编码',
  应用ID, 10, '/路由地址', '/组件路径', 排序号
);
```

**2. 添加菜单功能记录 (sys_menu_options表)**
```sql
INSERT INTO sys_menu_options (
  menu_option_id, menu_id, option_name, option_code
) VALUES (
  功能ID, 菜单ID, '功能名称', '权限编码'
);
```

**3. 分配角色权限 (sys_role_menu表)**
```sql
INSERT INTO sys_role_menu (
  role_menu_id, role_id, menu_id, app_id
) VALUES (
  关联ID, 角色ID, 菜单ID, 应用ID
);
```

**4. 分配角色功能权限 (sys_role_menu_options表)**
```sql
INSERT INTO sys_role_menu_options (
  role_menu_option_id, role_id, menu_option_id, app_id, menu_id
) VALUES (
  关联ID, 角色ID, 功能ID, 应用ID, 菜单ID
);
```

#### 步骤2：后端代码修改

**1. 创建Controller类**
```java
@RestController
@ApiResource(name = "新功能管理", requiredPermission = true, requirePermissionCode = "NEW_FUNCTION")
public class NewFunctionController {

    @Resource
    private NewFunctionService newFunctionService;

    @PostResource(name = "新增", path = "/newFunction/add")
    @ApiResource(name = "新增功能", requiredPermission = true, requirePermissionCode = "ADD_NEW_FUNCTION")
    public ResponseData add(@RequestBody NewFunctionRequest request) {
        newFunctionService.add(request);
        return new SuccessResponseData();
    }

    @PostResource(name = "修改", path = "/newFunction/edit")
    @ApiResource(name = "修改功能", requiredPermission = true, requirePermissionCode = "EDIT_NEW_FUNCTION")
    public ResponseData edit(@RequestBody NewFunctionRequest request) {
        newFunctionService.edit(request);
        return new SuccessResponseData();
    }

    @PostResource(name = "删除", path = "/newFunction/delete")
    @ApiResource(name = "删除功能", requiredPermission = true, requirePermissionCode = "DELETE_NEW_FUNCTION")
    public ResponseData delete(@RequestBody DeleteRequest request) {
        newFunctionService.delete(request);
        return new SuccessResponseData();
    }
}
```

**2. 添加权限编码常量**
```java
public interface PermissionCodeConstants {
    /**
     * 新功能管理界面
     */
    String NEW_FUNCTION = "NEW_FUNCTION";

    /**
     * 新功能管理界面-新增
     */
    String ADD_NEW_FUNCTION = "ADD_NEW_FUNCTION";

    /**
     * 新功能管理界面-修改
     */
    String EDIT_NEW_FUNCTION = "EDIT_NEW_FUNCTION";

    /**
     * 新功能管理界面-删除
     */
    String DELETE_NEW_FUNCTION = "DELETE_NEW_FUNCTION";
}
```

**3. 创建Service业务层**
```java
@Service
public class NewFunctionServiceImpl implements NewFunctionService {

    @Override
    @DataScope
    public void add(NewFunctionRequest request) {
        // 业务逻辑实现
        // @DataScope注解会自动应用数据权限过滤
    }

    @Override
    public PageResult<NewFunctionResponse> findPage(NewFunctionRequest request) {
        // 分页查询逻辑
        // 会自动应用数据权限过滤
        return new PageResult<>();
    }
}
```

#### 步骤3：前端代码修改

**1. 创建页面组件**
```vue
<template>
  <div class="new-function-container">
    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <a-button
        v-permission="'ADD_NEW_FUNCTION'"
        type="primary"
        @click="showAddModal">
        新增
      </a-button>

      <a-button
        v-permission="'EXPORT_NEW_FUNCTION'"
        @click="exportData">
        导出
      </a-button>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination">

      <template #action="{ record }">
        <a-button
          v-permission="'EDIT_NEW_FUNCTION'"
          type="link"
          @click="showEditModal(record)">
          编辑
        </a-button>

        <a-button
          v-permission="'DELETE_NEW_FUNCTION'"
          type="link"
          danger
          @click="deleteRecord(record)">
          删除
        </a-button>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { NewFunctionApi } from './api/NewFunctionApi';

// 数据和方法定义
const dataSource = ref([]);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

// 页面加载时获取数据
onMounted(() => {
  loadData();
});

const loadData = async () => {
  try {
    const result = await NewFunctionApi.getPage({
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize
    });
    dataSource.value = result.rows;
    pagination.value.total = result.totalRows;
  } catch (error) {
    console.error('加载数据失败:', error);
  }
};
</script>
```

**2. 创建API接口文件**
```javascript
import { request } from '@/utils/request';

export class NewFunctionApi {

  /**
   * 获取分页数据
   */
  static async getPage(params) {
    return request({
      url: '/newFunction/page',
      method: 'get',
      params
    });
  }

  /**
   * 新增
   */
  static async add(data) {
    return request({
      url: '/newFunction/add',
      method: 'post',
      data
    });
  }

  /**
   * 修改
   */
  static async edit(data) {
    return request({
      url: '/newFunction/edit',
      method: 'post',
      data
    });
  }

  /**
   * 删除
   */
  static async delete(data) {
    return request({
      url: '/newFunction/delete',
      method: 'post',
      data
    });
  }
}
```

### 5.2 权限分配界面操作

系统提供了可视化的权限分配界面，管理员可以通过以下界面进行权限配置：

#### 5.2.1 主要管理界面
- **角色管理** (`/system/auth/role`)：管理角色基本信息
- **权限分配** (`/system/auth/permission`)：为角色分配菜单和功能权限
- **菜单管理** (`/system/auth/menu`)：管理菜单结构和功能
- **应用管理** (`/system/auth/app`)：管理应用信息

#### 5.2.2 权限分配操作流程
1. **选择角色**：在权限分配界面选择要配置的角色
2. **选择应用**：选择要分配权限的应用
3. **选择菜单**：勾选角色可以访问的菜单
4. **选择功能**：勾选角色可以使用的具体功能
5. **保存配置**：保存权限配置，系统会自动更新相关表数据

#### 5.2.3 数据权限配置
1. **选择角色**：选择要配置数据权限的角色
2. **选择数据范围类型**：
   - 仅本人数据
   - 本部门数据
   - 本部门及以下数据
   - 本公司及以下数据
   - 指定机构数据
   - 全部数据
3. **配置具体范围**：根据选择的类型配置具体的机构范围
4. **保存配置**：保存数据权限配置

### 5.3 注意事项

#### 5.3.1 开发注意事项
1. **权限编码一致性**：确保数据库中的`option_code`与代码中的权限编码完全一致
2. **菜单层级关系**：正确设置`menu_parent_id`和`menu_pids`，确保菜单树形结构正确
3. **应用归属**：确保菜单归属到正确的应用下，避免权限混乱
4. **角色权限分配**：新功能添加后需要给相应角色分配权限，否则用户无法访问
5. **缓存清理**：权限相关的缓存可能需要清理或重启应用才能生效

#### 5.3.2 权限设计原则
1. **最小权限原则**：用户只应该拥有完成其工作所必需的最小权限
2. **职责分离**：不同的职责应该分配给不同的角色
3. **权限继承**：下级角色可以继承上级角色的部分权限
4. **动态权限**：支持运行时动态调整权限，无需重启系统

#### 5.3.3 安全考虑
1. **前后端双重验证**：前端控制显示，后端控制访问
2. **接口权限校验**：每个接口都应该进行权限校验
3. **数据权限过滤**：查询数据时自动应用数据权限过滤
4. **审计日志**：记录权限变更和敏感操作的审计日志

## 6. 系统特点与优势

### 6.1 核心特点
1. **RBAC模型**：基于角色的访问控制，支持用户-角色-权限的三层模型
2. **细粒度权限**：支持到菜单功能级别的权限控制
3. **数据权限**：支持多种数据范围控制策略
4. **多应用支持**：支持多个应用的权限独立管理
5. **前后端分离**：前端通过指令控制页面元素显示，后端通过拦截器控制接口访问
6. **缓存优化**：权限数据采用缓存机制提高性能
7. **动态配置**：支持通过管理界面动态配置权限，无需重启应用

### 6.2 技术优势
1. **高性能**：采用缓存机制，减少数据库查询
2. **高可用**：支持分布式部署，权限数据可以共享
3. **易扩展**：模块化设计，易于扩展新的权限类型
4. **易维护**：清晰的代码结构和完善的文档
5. **安全性**：多层次的安全防护机制

### 6.3 业务优势
1. **灵活配置**：支持复杂的组织架构和权限需求
2. **用户友好**：直观的权限配置界面
3. **审计支持**：完整的操作日志和审计功能
4. **合规性**：满足企业级应用的合规要求

## 7. 总结

本权限管理系统采用了成熟的RBAC模型，通过精心设计的数据库表结构和业务逻辑，实现了完整的权限管理功能。系统不仅支持传统的菜单权限控制，还提供了细粒度的功能权限和灵活的数据权限控制。

通过前后端分离的架构设计，系统在保证安全性的同时，也提供了良好的用户体验。完善的权限配置界面使得权限管理变得简单直观，而强大的扩展能力则确保了系统能够适应不断变化的业务需求。

这套权限系统设计完整、功能强大，能够满足企业级应用的复杂权限管理需求，是一个值得参考和学习的优秀实践案例。
```
