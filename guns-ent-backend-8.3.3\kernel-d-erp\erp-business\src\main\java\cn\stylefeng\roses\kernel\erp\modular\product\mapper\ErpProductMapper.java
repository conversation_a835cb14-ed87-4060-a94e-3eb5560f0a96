package cn.stylefeng.roses.kernel.erp.modular.product.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ProductStatsResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品主档案Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
public interface ErpProductMapper extends BaseMapper<ErpProduct> {

    /**
     * 根据供应商ID查询商品
     *
     * @param supplierId 供应商ID
     * @param status 状态（可选）
     * @param pricingType 计价类型（可选）
     * @return 商品列表
     */
    List<ErpProduct> findBySupplier(@Param("supplierId") Long supplierId,
                                   @Param("status") String status,
                                   @Param("pricingType") String pricingType);

    /**
     * 根据供应商ID列表查询商品
     *
     * @param supplierIds 供应商ID列表
     * @param status 状态（可选）
     * @param pricingType 计价类型（可选）
     * @return 商品列表
     */
    List<ErpProduct> findBySupplierIds(@Param("supplierIds") List<Long> supplierIds,
                                      @Param("status") String status,
                                      @Param("pricingType") String pricingType);

    /**
     * 根据计价类型查询商品
     *
     * @param pricingType 计价类型
     * @param status 状态（可选）
     * @param supplierId 供应商ID（可选）
     * @return 商品列表
     */
    List<ErpProduct> findByPricingType(@Param("pricingType") String pricingType,
                                      @Param("status") String status,
                                      @Param("supplierId") Long supplierId);

    /**
     * 查询商品及其供应商信息
     *
     * @param supplierId 供应商ID（可选）
     * @param businessMode 经营方式（可选）
     * @param pricingType 计价类型（可选）
     * @param status 状态（可选）
     * @return 商品列表（包含供应商信息）
     */
    List<ErpProduct> findProductsWithSupplier(@Param("supplierId") Long supplierId,
                                             @Param("businessMode") String businessMode,
                                             @Param("pricingType") String pricingType,
                                             @Param("status") String status);

    /**
     * 根据价格范围查询商品
     *
     * @param pricingType 计价类型（可选，为空则查询所有类型）
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @param supplierId 供应商ID（可选）
     * @return 商品列表
     */
    List<ErpProduct> findByPriceRange(@Param("pricingType") String pricingType,
                                     @Param("minPrice") BigDecimal minPrice,
                                     @Param("maxPrice") BigDecimal maxPrice,
                                     @Param("supplierId") Long supplierId);

    /**
     * 模糊搜索商品
     *
     * @param keyword 搜索关键词
     * @param supplierId 供应商ID（可选）
     * @param pricingType 计价类型（可选）
     * @param status 状态（可选）
     * @return 商品列表
     */
    List<ErpProduct> searchProducts(@Param("keyword") String keyword,
                                   @Param("supplierId") Long supplierId,
                                   @Param("pricingType") String pricingType,
                                   @Param("status") String status);

    /**
     * 获取商品统计信息
     *
     * @param productId 商品ID（可选，为空则查询所有）
     * @param supplierId 供应商ID（可选）
     * @param pricingType 计价类型（可选）
     * @return 商品统计信息列表
     */
    List<ProductStatsResponse> getProductStats(@Param("productId") Long productId,
                                              @Param("supplierId") Long supplierId,
                                              @Param("pricingType") String pricingType);

    /**
     * 统计供应商下的商品数量（按计价类型分组）
     *
     * @param supplierId 供应商ID
     * @param status 状态（可选）
     * @return 计价类型和数量的映射
     */
    List<Map<String, Object>> countProductsBySupplierAndPricingType(@Param("supplierId") Long supplierId,
                                                                   @Param("status") String status);

    /**
     * 批量更新商品供应商
     *
     * @param productIds 商品ID列表
     * @param newSupplierId 新的供应商ID
     * @param updateUser 更新用户
     * @return 更新数量
     */
    int batchUpdateSupplier(@Param("productIds") List<Long> productIds,
                           @Param("newSupplierId") Long newSupplierId,
                           @Param("updateUser") Long updateUser);

    /**
     * 批量更新商品计价类型
     *
     * @param productIds 商品ID列表
     * @param pricingType 新的计价类型
     * @param price 新的价格
     * @param updateUser 更新用户
     * @return 更新数量
     */
    int batchUpdatePricingType(@Param("productIds") List<Long> productIds,
                              @Param("pricingType") String pricingType,
                              @Param("price") BigDecimal price,
                              @Param("updateUser") Long updateUser);

    /**
     * 查询没有设置供应商的商品
     *
     * @param status 状态（可选）
     * @param pricingType 计价类型（可选）
     * @return 商品列表
     */
    List<ErpProduct> findProductsWithoutSupplier(@Param("status") String status,
                                                @Param("pricingType") String pricingType);

}
