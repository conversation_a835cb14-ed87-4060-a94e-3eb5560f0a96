package cn.stylefeng.roses.ent.mobile.invite.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.stylefeng.roses.ent.mobile.invite.entity.SysInviteUser;
import cn.stylefeng.roses.ent.mobile.invite.enums.InviteStatusEnum;
import cn.stylefeng.roses.ent.mobile.invite.enums.SysInviteUserExceptionEnum;
import cn.stylefeng.roses.ent.mobile.invite.factory.InviteUserFactory;
import cn.stylefeng.roses.ent.mobile.invite.mapper.SysInviteUserMapper;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteDetailRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteUserRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteDetail;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteUserVo;
import cn.stylefeng.roses.ent.mobile.invite.service.SysInviteUserService;
import cn.stylefeng.roses.ent.mobile.manage.exception.MobileException;
import cn.stylefeng.roses.ent.mobile.manage.exception.enums.MobileExceptionEnum;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.SendPhoneCodeRequest;
import cn.stylefeng.roses.ent.mobile.manage.prop.MobileSmsProperties;
import cn.stylefeng.roses.ent.mobile.manage.service.MobileSystemConfigService;
import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.file.api.FileInfoApi;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.sms.api.SmsSenderApi;
import cn.stylefeng.roses.kernel.sms.modular.enums.SmsSendSourceEnum;
import cn.stylefeng.roses.kernel.sms.modular.enums.SmsSendStatusEnum;
import cn.stylefeng.roses.kernel.sms.modular.param.SysSmsSendParam;
import cn.stylefeng.roses.kernel.sms.modular.param.SysSmsVerifyParam;
import cn.stylefeng.roses.kernel.sms.modular.service.SysSmsInfoService;
import cn.stylefeng.roses.kernel.sys.api.MessagePublishApi;
import cn.stylefeng.roses.kernel.sys.api.pojo.message.MessageSendDTO;
import cn.stylefeng.roses.kernel.sys.api.pojo.org.CompanyDeptDTO;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 邀请用户业务实现层
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
@Service
public class SysInviteUserServiceImpl extends ServiceImpl<SysInviteUserMapper, SysInviteUser> implements SysInviteUserService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private FileInfoApi fileInfoApi;

    @Resource
    private HrOrganizationService hrOrganizationService;

    @Resource
    private MobileSmsProperties mobileSmsProperties;

    @Resource
    private SysSmsInfoService sysSmsInfoService;

    @Resource
    private MobileSystemConfigService mobileSystemConfigService;

    @Resource
    private SmsSenderApi smsSenderApi;

    @Resource
    private MessagePublishApi messagePublishApi;

    @Override
    public void sendPhoneValidateCode(SendPhoneCodeRequest changePhoneRequest) {

        // 1. 先校验验证码是否正确
        this.mobileSystemConfigService.validateDragCaptcha(changePhoneRequest);

        // 2. 判断手机是否在邀请列表里
        LambdaQueryWrapper<SysInviteUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysInviteUser::getPhoneNumber, changePhoneRequest.getPhone());
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new MobileException(MobileExceptionEnum.USER_PHONE_CANT_APPLY);
        }

        // 3. 生成随机验证码
        String validateCode = RandomUtil.randomNumbers(4);
        HashMap<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put(mobileSmsProperties.getInviteSmsCodeFieldName(), validateCode);

        // 4. 存储短信发送验证码
        SysSmsSendParam sysSmsSendParam = new SysSmsSendParam();
        sysSmsSendParam.setPhone(changePhoneRequest.getPhone());
        sysSmsSendParam.setSmsSendSourceEnum(SmsSendSourceEnum.APP);
        sysSmsSendParam.setTemplateCode(mobileSmsProperties.getInviteSmsTemplateCode());
        Long smsId = sysSmsInfoService.saveSmsInfo(sysSmsSendParam, validateCode);

        // 5. 发送短信验证码
        smsSenderApi.sendSms(changePhoneRequest.getPhone(), mobileSmsProperties.getInviteSmsTemplateCode(), paramMap);

        // 6. 更新发送状态
        sysSmsInfoService.updateSmsInfo(smsId, SmsSendStatusEnum.SUCCESS);
    }

    @Override
    public void submitApply(SysInviteUserRequest sysInviteUserRequest) {

        // 1. 验证手机号验证码是否正确
        SysSmsVerifyParam sysSmsVerifyParam = new SysSmsVerifyParam();
        sysSmsVerifyParam.setPhone(sysInviteUserRequest.getPhoneNumber());
        sysSmsVerifyParam.setCode(sysInviteUserRequest.getPhoneValidateNumber());
        sysSmsVerifyParam.setSmsSendSourceEnum(SmsSendSourceEnum.APP);
        sysSmsVerifyParam.setTemplateCode(mobileSmsProperties.getInviteSmsTemplateCode());
        sysSmsInfoService.validateSmsInfo(sysSmsVerifyParam);

        // 2. 插入一条邀请用户的记录
        SysInviteUser inviteUser = InviteUserFactory.createInviteUser(sysInviteUserRequest);
        this.save(inviteUser);

        // 3. 发送给管理员一条消息通知，提示有人申请加入
        // 获取机构id对应的公司和部门名称
        CompanyDeptDTO companyDeptDTO = hrOrganizationService.getCompanyDeptInfo(sysInviteUserRequest.getOrgId());
        MessageSendDTO inviteUserMessage = InviteUserFactory.createInviteUserMessage(inviteUser, companyDeptDTO.getCompanyName(), companyDeptDTO.getDeptName());
        messagePublishApi.batchSendMessage(inviteUserMessage);
    }

    @Override
    public SysInviteUser detail(SysInviteUserRequest sysInviteUserRequest) {
        return this.querySysInviteUser(sysInviteUserRequest);
    }

    @Override
    public SysInviteDetail getInviteDetail(SysInviteDetailRequest sysInviteDetailRequest) {

        SysInviteDetail sysInviteDetail = new SysInviteDetail();

        // 获取邀请人的头像和姓名
        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserLambdaQueryWrapper.select(SysUser::getUserId, SysUser::getRealName, SysUser::getAvatar);
        sysUserLambdaQueryWrapper.eq(SysUser::getUserId, sysInviteDetailRequest.getUserId());
        try {
            TenantSwitchHolder.set(false);
            SysUser sysUser = this.sysUserService.getOne(sysUserLambdaQueryWrapper, false);
            if (ObjectUtil.isNotEmpty(sysUser)) {
                sysInviteDetail.setInviteUserId(sysUser.getUserId());
                sysInviteDetail.setInviteUserName(sysUser.getRealName());
                // 获取头像的url
                String fileUnAuthUrl = fileInfoApi.getFileUnAuthUrl(sysUser.getAvatar());
                sysInviteDetail.setAvatarFileUrl(fileUnAuthUrl);
            }
        } finally {
            TenantSwitchHolder.remove();
        }

        // 获取加入的公司和部门信息
        CompanyDeptDTO orgCompanyInfo = hrOrganizationService.getOrgCompanyInfo(sysInviteDetailRequest.getOrgId());
        if (orgCompanyInfo != null) {
            sysInviteDetail.setCompanyName(orgCompanyInfo.getCompanyName());
            sysInviteDetail.setDeptName(orgCompanyInfo.getDeptName());
        }

        return sysInviteDetail;
    }

    @Override
    public void agreeApply(SysInviteUserRequest sysInviteUserRequest) {
        SysInviteUser sysInviteUser = this.querySysInviteUser(sysInviteUserRequest);
        sysInviteUser.setInviteStatus(InviteStatusEnum.PASS.getCode());
        this.updateById(sysInviteUser);
    }

    @Override
    public void disAgreeApply(SysInviteUserRequest sysInviteUserRequest) {
        SysInviteUser sysInviteUser = this.querySysInviteUser(sysInviteUserRequest);
        sysInviteUser.setInviteStatus(InviteStatusEnum.JUJUE.getCode());
        this.updateById(sysInviteUser);
    }

    @Override
    public List<SysInviteUserVo> findList(SysInviteUserRequest sysInviteUserRequest) {
        LambdaQueryWrapper<SysInviteUser> wrapper = this.createWrapper(sysInviteUserRequest);

        // 查询关键信息
        wrapper.select(SysInviteUser::getInviteUserId, SysInviteUser::getRealName, BaseEntity::getCreateTime, SysInviteUser::getOrgId, SysInviteUser::getPhoneNumber, SysInviteUser::getFromUserId,
                SysInviteUser::getApplyReason, SysInviteUser::getInviteStatus);

        // 获取列表
        List<SysInviteUser> list = this.list(wrapper);
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<SysInviteUserVo> finalList = new ArrayList<>();

        for (SysInviteUser sysInviteUser : list) {

            SysInviteUserVo sysInviteUserVo = new SysInviteUserVo();
            BeanUtil.copyProperties(sysInviteUser, sysInviteUserVo);

            // 根据机构id查询到公司和部门名称
            CompanyDeptDTO orgCompanyInfo = hrOrganizationService.getOrgCompanyInfo(sysInviteUser.getOrgId());
            if (ObjectUtil.isNotEmpty(orgCompanyInfo)) {
                sysInviteUserVo.setCompanyName(orgCompanyInfo.getCompanyName());
                sysInviteUserVo.setDeptName(orgCompanyInfo.getDeptName());
            }

            // 获取邀请人的姓名和电话
            SysUser sysUser = this.sysUserService.getById(sysInviteUser.getFromUserId());
            if (ObjectUtil.isNotEmpty(sysUser)) {
                sysInviteUserVo.setInviteUserName(sysUser.getRealName());
                sysInviteUserVo.setInviteUserPhone(sysUser.getPhone());
            }

            finalList.add(sysInviteUserVo);
        }

        return finalList;
    }

    /**
     * 获取信息
     *
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    private SysInviteUser querySysInviteUser(SysInviteUserRequest sysInviteUserRequest) {
        SysInviteUser sysInviteUser = this.getById(sysInviteUserRequest.getInviteUserId());
        if (ObjectUtil.isEmpty(sysInviteUser)) {
            throw new ServiceException(SysInviteUserExceptionEnum.SYS_INVITE_USER_NOT_EXISTED);
        }
        return sysInviteUser;
    }

    /**
     * 创建查询wrapper
     *
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    private LambdaQueryWrapper<SysInviteUser> createWrapper(SysInviteUserRequest sysInviteUserRequest) {
        LambdaQueryWrapper<SysInviteUser> queryWrapper = new LambdaQueryWrapper<>();

        // 没审核的放在前边
        queryWrapper.orderByAsc(SysInviteUser::getInviteStatus);

        // 按时间倒序排列
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);

        return queryWrapper;
    }

}
