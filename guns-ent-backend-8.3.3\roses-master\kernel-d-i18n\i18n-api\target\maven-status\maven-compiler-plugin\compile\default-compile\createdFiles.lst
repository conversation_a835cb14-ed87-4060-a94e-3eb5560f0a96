cn\stylefeng\roses\kernel\i18n\api\TranslationPersistenceApi.class
cn\stylefeng\roses\kernel\i18n\api\TranslationApi.class
cn\stylefeng\roses\kernel\i18n\api\pojo\request\TranslationRequest.class
cn\stylefeng\roses\kernel\i18n\api\context\TranslationContext.class
cn\stylefeng\roses\kernel\i18n\api\pojo\request\TranslationRequest$changeUserLanguage.class
cn\stylefeng\roses\kernel\i18n\api\constants\TranslationConstants.class
cn\stylefeng\roses\kernel\i18n\api\exception\TranslationException.class
cn\stylefeng\roses\kernel\i18n\api\pojo\request\TranslationRequest$deleteTranLanguage.class
cn\stylefeng\roses\kernel\i18n\api\exception\enums\TranslationExceptionEnum.class
cn\stylefeng\roses\kernel\i18n\api\pojo\TranslationDict.class
