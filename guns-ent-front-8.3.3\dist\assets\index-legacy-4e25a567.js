System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5bd8cc08.js","./index-legacy-45c79de7.js","./index-legacy-510bfbb8.js"],(function(e,n){"use strict";var i,o,t,a,c,l,s,u,d,r,p,g,v,f,h,m,b,x,y,_,I,O,C,k,j,S,w,z,E,M,U,A,P,R,N;return{setters:[e=>{i=e._,o=e.ac,t=e.bl,a=e.aX,c=e.bm,l=e.r,s=e.L,u=e.o,d=e.k,r=e.a,p=e.c,g=e.d,v=e.w,f=e.b,h=e.g,m=e.F,b=e.e,x=e.f,y=e.t,_=e.h,I=e.ar,O=e.m,C=e.l,k=e.v,j=e.W,S=e.J,w=e.B,z=e.G,E=e.a4,M=e.I,U=e.a0,A=e.a3,P=e.M,R=e.bn,N=e.bo},null,null,null],execute:function(){var n=document.createElement("style");n.textContent=".icon-manager[data-v-e805b31d]{padding:24px}.search-section[data-v-e805b31d]{margin-bottom:24px}.stats-section[data-v-e805b31d]{margin-bottom:24px;padding:16px;background:#fafafa;border-radius:6px}.icons-section[data-v-e805b31d]{margin-bottom:24px}.icon-card[data-v-e805b31d]{text-align:center;cursor:pointer;transition:all .3s}.icon-card[data-v-e805b31d]:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,.15)}.icon-display[data-v-e805b31d]{margin-bottom:12px}.icon-info[data-v-e805b31d]{font-size:12px}.icon-class[data-v-e805b31d]{font-weight:700;color:#1890ff;margin-bottom:4px}.icon-description[data-v-e805b31d]{color:#666;margin-bottom:4px}.icon-category[data-v-e805b31d]{color:#999;font-size:11px}.missing-check-section[data-v-e805b31d]{margin-bottom:16px}.suggestion-result[data-v-e805b31d],.alternatives[data-v-e805b31d]{margin-top:16px}.alternative-option[data-v-e805b31d]{margin-bottom:16px}.alternative-display[data-v-e805b31d]{display:flex;align-items:center;gap:12px}.usage-guide[data-v-e805b31d]{margin-top:24px}.guide-content h4[data-v-e805b31d]{margin-top:16px;margin-bottom:8px;color:#1890ff}.guide-content pre[data-v-e805b31d]{background:#f5f5f5;padding:8px;border-radius:4px;overflow-x:auto}.guide-content ul[data-v-e805b31d],.guide-content ol[data-v-e805b31d]{margin-left:20px}.guide-content li[data-v-e805b31d]{margin-bottom:4px}\n",document.head.appendChild(n);const D={OPERATION:{"icon-opt-bianji":"编辑","icon-opt-shanchu":"删除","icon-opt-xiangqing":"详情/查看","icon-opt-shezhi":"设置","icon-opt-search":"搜索","icon-opt-shuaxin":"刷新","icon-opt-tianjia":"添加","icon-opt-daochu":"导出","icon-opt-daoru":"导入","icon-opt-fuzhi":"复制","icon-opt-shoucang":"收藏","icon-opt-yishoucang":"已收藏","icon-opt-fenpeijuese":"分配角色","icon-opt-chongzhimima":"重置密码","icon-opt-tongyi":"同意","icon-opt-jujue":"拒绝","icon-opt-chehui":"撤回","icon-opt-tuihui":"退回","icon-opt-fabu":"发布","icon-opt-yulan":"预览","icon-opt-yidu":"已读","icon-opt-qiehuan":"切换","icon-opt-fangda":"放大","icon-opt-suoxiao":"缩小","icon-opt-zidingyilie":"自定义列"},MENU:{"icon-menu-rizhichakan":"日志查看","icon-nav-zhuye":"主页","icon-nav-gongsi":"公司"},USER:{"icon-opt-export-user":"导出用户","icon-opt-import-user":"导入用户","icon-xiala-xiugaitouxiang":"修改头像","icon-xiala-tuichudenglu":"退出登录"}},F={view:"icon-opt-xiangqing",detail:"icon-opt-xiangqing",edit:"icon-opt-bianji",delete:"icon-opt-shanchu",add:"icon-opt-tianjia",create:"icon-opt-tianjia",setting:"icon-opt-shezhi",config:"icon-opt-shezhi",search:"icon-opt-search",refresh:"icon-opt-shuaxin",export:"icon-opt-daochu",import:"icon-opt-daoru",copy:"icon-opt-fuzhi",confirm:"icon-opt-tongyi",approve:"icon-opt-tongyi",reject:"icon-opt-jujue",cancel:"icon-opt-chehui",publish:"icon-opt-fabu",preview:"icon-opt-yulan",receive:"icon-opt-tianjia",inbound:"icon-opt-tianjia",outbound:"icon-opt-daochu",transfer:"icon-opt-qiehuan",adjust:"icon-opt-shezhi",check:"icon-opt-xiangqing",freeze:"icon-opt-suoxiao",unfreeze:"icon-opt-fangda"},T={view:"EyeOutlined",detail:"FileTextOutlined",edit:"EditOutlined",delete:"DeleteOutlined",add:"PlusOutlined",create:"PlusCircleOutlined",setting:"SettingOutlined",config:"ToolOutlined",search:"SearchOutlined",refresh:"ReloadOutlined",export:"ExportOutlined",import:"ImportOutlined",copy:"CopyOutlined",confirm:"CheckOutlined",approve:"CheckCircleOutlined",reject:"CloseCircleOutlined",cancel:"UndoOutlined",publish:"SendOutlined",preview:"EyeOutlined",receive:"InboxOutlined",inbound:"DownloadOutlined",outbound:"UploadOutlined",transfer:"SwapOutlined",adjust:"AdjustOutlined",check:"AuditOutlined",freeze:"PauseCircleOutlined",unfreeze:"PlayCircleOutlined"};function q(e){for(const n of Object.values(D))if(n[e])return!0;return!1}function L(e){for(const n of Object.values(D))if(n[e])return n[e];return"未知图标"}function $(){const e=[];for(const[n,i]of Object.entries(D))for(const[o,t]of Object.entries(i))e.push({category:n,iconClass:o,description:t});return e}function K(e){const n=$(),i=e.toLowerCase();return n.filter((e=>e.iconClass.toLowerCase().includes(i)||e.description.toLowerCase().includes(i)))}function G(e){let n="view";return e.includes("chakan")||e.includes("view")?n="view":e.includes("bianji")||e.includes("edit")?n="edit":e.includes("shanchu")||e.includes("delete")?n="delete":e.includes("tianjia")||e.includes("add")?n="add":e.includes("queren")||e.includes("confirm")?n="confirm":(e.includes("ruku")||e.includes("inbound"))&&(n="receive"),{missingIcon:e,suggestedAction:n,recommendation:function(e){const n=F[e];return{iconFont:n,antIcon:T[e],exists:!!n&&q(n),description:L(n)}}(n),alternatives:K(n)}}const J={class:"icon-manager"},B={class:"search-section"},W={class:"stats-section"},X={class:"icons-section"},Y={class:"icon-display"},H={class:"icon-info"},Q={class:"icon-class"},V={class:"icon-description"},Z={class:"icon-category"},ee={class:"missing-check-section"},ne={key:0,class:"suggestion-result"},ie={key:0,class:"alternatives"},oe={key:0,class:"alternative-option"},te={class:"alternative-display"},ae={key:1,class:"alternative-option"},ce={class:"alternative-display"},le={class:"usage-guide"};e("default",i({name:"IconManager",components:{SearchOutlined:o,EditOutlined:t,DeleteOutlined:a,EyeOutlined:c},setup(){const e=l(""),n=l(""),i=l(!1),o=l(""),t=l(null),a=l([]),c=s((()=>{let i=a.value;return n.value&&(i=i.filter((e=>e.category===n.value))),e.value&&(i=K(e.value)),i})),d=s((()=>a.value.length)),r=s((()=>Object.keys(D.OPERATION).length)),p=s((()=>Object.keys(D.MENU).length)),g=s((()=>Object.keys(D.USER).length));return u((()=>{a.value=$()})),{searchKeyword:e,selectedCategory:n,showMissingModal:i,missingIconInput:o,iconSuggestion:t,filteredIcons:c,totalIcons:d,operationIcons:r,menuIcons:p,userIcons:g,handleSearch:()=>{},handleCategoryChange:()=>{},checkMissingIcon:()=>{i.value=!0},checkSpecificIcon:()=>{o.value.trim()?t.value=G(o.value.trim()):O.warning("请输入图标类名")},copyIconClass:e=>{navigator.clipboard.writeText(e).then((()=>{O.success(`已复制图标类名: ${e}`)}))},copyAntIcon:e=>{const n=`<${e} style="font-size: 24px; color: #60666b;" />`;navigator.clipboard.writeText(n).then((()=>{O.success("已复制Ant Design图标代码")}))},getCategoryName:e=>({OPERATION:"操作类",MENU:"菜单类",USER:"用户类"}[e]||e),getSuggestionDescription:()=>t.value?t.value.recommendation.exists?`图标 ${t.value.missingIcon} 在系统中存在，可以正常使用。`:`图标 ${t.value.missingIcon} 不存在，建议使用以下替代方案：`:""}}},[["render",function(e,n,i,o,t,a){const c=d("SearchOutlined"),l=C,s=k,u=j,O=S,D=w,F=z,T=E,q=M,L=U,$=A,K=P,G=R,se=N;return r(),p("div",J,[g(L,{title:"JavaGuns Enterprise ERP 图标管理工具",bordered:!1},{default:v((()=>[f("div",B,[g(F,{gutter:16},{default:v((()=>[g(s,{span:8},{default:v((()=>[g(l,{value:o.searchKeyword,"onUpdate:value":n[0]||(n[0]=e=>o.searchKeyword=e),placeholder:"搜索图标名称或描述",onInput:o.handleSearch},{prefix:v((()=>[g(c)])),_:1},8,["value","onInput"])])),_:1}),g(s,{span:8},{default:v((()=>[g(O,{value:o.selectedCategory,"onUpdate:value":n[1]||(n[1]=e=>o.selectedCategory=e),placeholder:"选择图标分类",style:{width:"100%"},onChange:o.handleCategoryChange},{default:v((()=>[g(u,{value:""},{default:v((()=>n[6]||(n[6]=[h("全部分类")]))),_:1,__:[6]}),g(u,{value:"OPERATION"},{default:v((()=>n[7]||(n[7]=[h("操作类图标")]))),_:1,__:[7]}),g(u,{value:"MENU"},{default:v((()=>n[8]||(n[8]=[h("菜单类图标")]))),_:1,__:[8]}),g(u,{value:"USER"},{default:v((()=>n[9]||(n[9]=[h("用户类图标")]))),_:1,__:[9]})])),_:1},8,["value","onChange"])])),_:1}),g(s,{span:8},{default:v((()=>[g(D,{type:"primary",onClick:o.checkMissingIcon},{default:v((()=>n[10]||(n[10]=[h(" 检查缺失图标 ")]))),_:1,__:[10]},8,["onClick"])])),_:1})])),_:1})]),f("div",W,[g(F,{gutter:16},{default:v((()=>[g(s,{span:6},{default:v((()=>[g(T,{title:"总图标数",value:o.totalIcons},null,8,["value"])])),_:1}),g(s,{span:6},{default:v((()=>[g(T,{title:"操作类图标",value:o.operationIcons},null,8,["value"])])),_:1}),g(s,{span:6},{default:v((()=>[g(T,{title:"菜单类图标",value:o.menuIcons},null,8,["value"])])),_:1}),g(s,{span:6},{default:v((()=>[g(T,{title:"用户类图标",value:o.userIcons},null,8,["value"])])),_:1})])),_:1})]),f("div",X,[g(F,{gutter:[16,16]},{default:v((()=>[(r(!0),p(m,null,b(o.filteredIcons,(e=>(r(),x(s,{key:e.iconClass,span:6},{default:v((()=>[g(L,{size:"small",hoverable:!0,class:"icon-card",onClick:n=>o.copyIconClass(e.iconClass)},{default:v((()=>[f("div",Y,[g(q,{iconClass:e.iconClass,"font-size":"32px",color:"#1890ff"},null,8,["iconClass"])]),f("div",H,[f("div",Q,y(e.iconClass),1),f("div",V,y(e.description),1),f("div",Z,y(o.getCategoryName(e.category)),1)])])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})]),g(K,{visible:o.showMissingModal,"onUpdate:visible":n[5]||(n[5]=e=>o.showMissingModal=e),title:"缺失图标检查",width:"800px",footer:null},{default:v((()=>[f("div",ee,[g(l,{value:o.missingIconInput,"onUpdate:value":n[2]||(n[2]=e=>o.missingIconInput=e),placeholder:"输入可能缺失的图标类名，如：icon-opt-chakan",onPressEnter:o.checkSpecificIcon},{suffix:v((()=>[g(D,{type:"link",onClick:o.checkSpecificIcon},{default:v((()=>n[11]||(n[11]=[h(" 检查 ")]))),_:1,__:[11]},8,["onClick"])])),_:1},8,["value","onPressEnter"]),o.iconSuggestion?(r(),p("div",ne,[g($,{type:o.iconSuggestion.recommendation.exists?"success":"warning",message:o.iconSuggestion.recommendation.exists?"图标存在":"图标不存在",description:o.getSuggestionDescription(),"show-icon":""},null,8,["type","message","description"]),o.iconSuggestion.recommendation.exists?_("",!0):(r(),p("div",ie,[n[16]||(n[16]=f("h4",null,"推荐替代方案：",-1)),o.iconSuggestion.recommendation.iconFont?(r(),p("div",oe,[n[13]||(n[13]=f("h5",null,"IconFont 图标：",-1)),g(L,{size:"small"},{default:v((()=>[f("div",te,[g(q,{iconClass:o.iconSuggestion.recommendation.iconFont,"font-size":"24px",color:"#1890ff"},null,8,["iconClass"]),f("span",null,y(o.iconSuggestion.recommendation.iconFont),1),g(D,{type:"link",size:"small",onClick:n[3]||(n[3]=e=>o.copyIconClass(o.iconSuggestion.recommendation.iconFont))},{default:v((()=>n[12]||(n[12]=[h(" 复制 ")]))),_:1,__:[12]})])])),_:1})])):_("",!0),o.iconSuggestion.recommendation.antIcon?(r(),p("div",ae,[n[15]||(n[15]=f("h5",null,"Ant Design 图标：",-1)),g(L,{size:"small"},{default:v((()=>[f("div",ce,[(r(),x(I(o.iconSuggestion.recommendation.antIcon),{style:{"font-size":"24px",color:"#1890ff"}})),f("span",null,y(o.iconSuggestion.recommendation.antIcon),1),g(D,{type:"link",size:"small",onClick:n[4]||(n[4]=e=>o.copyAntIcon(o.iconSuggestion.recommendation.antIcon))},{default:v((()=>n[14]||(n[14]=[h(" 复制代码 ")]))),_:1,__:[14]})])])),_:1})])):_("",!0)]))])):_("",!0)])])),_:1},8,["visible"]),f("div",le,[g(se,null,{default:v((()=>[g(G,{key:"1",header:"图标使用指南"},{default:v((()=>n[17]||(n[17]=[f("div",{class:"guide-content"},[f("h4",null,"1. 图标使用方法"),f("pre",null,[f("code",null,'<icon-font iconClass="icon-opt-bianji" font-size="24px" color="#60666b" />')]),f("h4",null,"2. Ant Design 图标使用方法"),f("pre",null,[f("code",null,'<EditOutlined style="font-size: 24px; color: #60666b;" />')]),f("h4",null,"3. 图标命名规范"),f("ul",null,[f("li",null,[f("code",null,"icon-opt-*"),h(": 操作类图标（编辑、删除、查看等）")]),f("li",null,[f("code",null,"icon-menu-*"),h(": 菜单类图标")]),f("li",null,[f("code",null,"icon-nav-*"),h(": 导航类图标")]),f("li",null,[f("code",null,"icon-tab-*"),h(": 标签页图标")])]),f("h4",null,"4. 图标缺失处理流程"),f("ol",null,[f("li",null,"使用本工具检查图标是否存在"),f("li",null,"如果不存在，选择推荐的替代图标"),f("li",null,"优先使用 IconFont 图标，其次使用 Ant Design 图标"),f("li",null,"如需新增图标，请联系UI设计师添加到图标库")])],-1)]))),_:1,__:[17]})])),_:1})])])),_:1})])}],["__scopeId","data-v-e805b31d"]]))}}}));
