import{R as s,r as n,o as b,a as T,f as _,w as C,d as S,m as R,M as h}from"./index-18a1ea24.js";import k from"./config-type-form-be70389a.js";class c{static add(e){return s.post("/sysConfigType/add",e)}static edit(e){return s.post("/sysConfigType/edit",e)}static delete(e){return s.post("/sysConfigType/delete",e)}static detail(e){return s.getAndLoadData("/sysConfigType/detail",e)}static list(e){return s.getAndLoadData("/sysConfigType/list",e)}}const x={__name:"config-type-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(u,{emit:e}){const d=u,p=e,i=n(!1),l=n(!1),a=n({configTypeSort:100}),y=n(null);b(()=>{d.data?(l.value=!0,v()):l.value=!1});const v=()=>{c.detail({configTypeId:d.data.id}).then(t=>{t&&(a.value.configTypeSort=t.dictSort,a.value.configTypeName=t.dictName,a.value.configTypeCode=t.dictCode,a.value.configTypeId=t.dictId)})},f=t=>{p("update:visible",t)},g=async()=>{y.value.$refs.formRef.validate().then(async t=>{if(t){i.value=!0;let o=null;l.value?o=c.edit(a.value):o=c.add(a.value),o.then(async r=>{i.value=!1,R.success(r.message),f(!1),p("done")}).catch(()=>{i.value=!1})}})};return(t,o)=>{const r=h;return T(),_(r,{width:700,maskClosable:!1,visible:d.visible,"confirm-loading":i.value,forceRender:!0,title:l.value?"\u7F16\u8F91\u914D\u7F6E\u5206\u7C7B":"\u65B0\u5EFA\u914D\u7F6E\u5206\u7C7B","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":f,onOk:g,onClose:o[1]||(o[1]=m=>f(!1))},{default:C(()=>[S(k,{form:a.value,"onUpdate:form":o[0]||(o[0]=m=>a.value=m),ref_key:"typeFormRef",ref:y},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}},B=Object.freeze(Object.defineProperty({__proto__:null,default:x},Symbol.toStringTag,{value:"Module"}));export{c as S,x as _,B as c};
