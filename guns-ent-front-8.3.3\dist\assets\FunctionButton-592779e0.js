import{_ as b,L as m,a,c,b as n,d as y,h as s,t as i,a2 as o,I as g}from"./index-18a1ea24.js";const _=["disabled"],h={class:"card-content"},k={key:0,class:"card-icon"},v={class:"card-title"},C={key:1,class:"card-subtitle"},B={key:0,class:"card-badge"},p=Object.assign({name:"FunctionButton"},{__name:"FunctionButton",props:{title:{type:String,required:!0},subtitle:{type:String,default:""},icon:{type:String,default:""},badge:{type:Number,default:0},type:{type:String,default:"default",validator:t=>["default","primary","success","warning","danger"].includes(t)},size:{type:String,default:"normal",validator:t=>["small","normal","large"].includes(t)},disabled:{type:Boolean,default:!1},isCheckout:{type:Boolean,default:!1}},emits:["click"],setup(t,{emit:d}){const e=t,l=d,u=m(()=>["function-card-".concat(e.type),"function-card-".concat(e.size),{"function-card-disabled":e.disabled,"function-card-checkout":e.isCheckout}]),r=()=>{e.disabled||l("click")};return(S,F)=>{const f=g;return a(),c("div",{class:o(["function-button-item",{"checkout-button":t.isCheckout}])},[n("div",{class:o(["function-card",u.value]),onClick:r,disabled:t.disabled},[n("div",h,[t.icon?(a(),c("div",k,[y(f,{iconClass:t.icon},null,8,["iconClass"])])):s("",!0),n("div",v,i(t.title),1),t.subtitle?(a(),c("div",C,i(t.subtitle),1)):s("",!0)]),t.badge&&t.badge>0?(a(),c("div",B,i(t.badge),1)):s("",!0)],10,_)],2)}}}),N=b(p,[["__scopeId","data-v-276cc3fe"]]);export{N as default};
