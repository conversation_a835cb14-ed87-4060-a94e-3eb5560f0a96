package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

/**
 * 非空的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 22:30
 */
public class NotNullValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(false, originValue, originValue, "必填字段，请填写此值");
        }

        return new ExcelLineParseResult(originValue);
    }

}
