System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-c65a6a4e.js","./PurchaseApi-legacy-77810512.js","./InboundForm-legacy-e79c6259.js","./InboundDetail-legacy-a79f6750.js","./ConfirmInboundModal-legacy-20066270.js","./ReceiveInboundModal-legacy-e48b0079.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-510bfbb8.js","./index-legacy-45c79de7.js","./SupplierSelector-legacy-fba3813b.js","./SupplierApi-legacy-234ddfc1.js","./InboundDetailTable-legacy-c1973558.js","./index-legacy-94a6fc23.js","./ProductSelectorModal-legacy-749b3af7.js","./ProductApi-legacy-33feae42.js","./productCategoryApi-legacy-247b2407.js","./index-legacy-9e3f6b64.js","./index-legacy-e24582b9.js"],(function(e,a){"use strict";var l,o,t,n,r,s,d,i,c,u,p,h,v,m,f,g,y,b,k,x,C,w,_,D,R,I,j,S,O,N,A,M,T,P,U,E,z,F,L,B,Y,V,q,H,Q,W,$,G;return{setters:[e=>{l=e._},e=>{o=e._,t=e.P,n=e.K,r=e.a1,s=e.r,d=e.L,i=e.N,c=e.s,u=e.o,p=e.k,h=e.a,v=e.c,m=e.b,f=e.d,g=e.w,y=e.g,b=e.t,k=e.O,x=e.Q,C=e.F,w=e.e,_=e.f,D=e.h,R=e.M,I=e.E,j=e.m,S=e.n,O=e.B,N=e.I,A=e.p,M=e.q,T=e.D,P=e.l,U=e.V,E=e.u,z=e.v,F=e.W,L=e.J,B=e.a6,Y=e.G,V=e.H,q=e.U},null,null,e=>{H=e.P},e=>{Q=e.default},e=>{W=e.default},e=>{$=e.default},e=>{G=e.default},null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".divider[data-v-aebaad11]{height:20px;margin:0 12px}.supplier-name[data-v-aebaad11]{font-weight:500;color:#262626;margin-bottom:4px;line-height:1.4}.supplier-code[data-v-aebaad11]{font-size:12px;color:#8c8c8c}.amount-text[data-v-aebaad11]{font-weight:500;color:#1890ff}.table-link[data-v-aebaad11]{color:#1890ff;cursor:pointer;text-decoration:none;transition:color .2s ease}.table-link[data-v-aebaad11]:hover{color:#40a9ff;text-decoration:underline}.text-muted[data-v-aebaad11]{color:#8c8c8c;font-size:12px}[data-v-aebaad11] .ant-table-tbody>tr:hover>td{background-color:#fafafa!important}[data-v-aebaad11] .ant-table-thead>tr>th{background-color:#fafafa!important;border-bottom:1px solid #f0f0f0;font-weight:500;color:#262626}[data-v-aebaad11] .ant-table-tbody>tr>td{border-bottom:1px solid #f5f5f5;padding:12px 16px;vertical-align:middle}\n",document.head.appendChild(a);const J={class:"guns-layout"},K={class:"guns-layout-content"},X={class:"guns-layout"},Z={class:"guns-layout-content-application"},ee={class:"content-mian"},ae={class:"content-mian-header"},le={class:"header-content"},oe={class:"header-content-left"},te={class:"header-content-right"},ne={class:"content-mian-body"},re={class:"table-content"},se={key:0,class:"super-search",style:{"margin-top":"8px"}},de=["onClick"],ie={key:1},ce={class:"supplier-name"},ue={class:"supplier-code text-muted"},pe={key:3,class:"amount-text"};e("default",o({name:"PurchaseInboundIndex",components:{PlusOutlined:t,SmallDashOutlined:n,DownOutlined:r,InboundForm:Q,InboundDetail:W,ConfirmInboundModal:$,ReceiveInboundModal:G},setup(){const e=s(!1),a=s(!1),l=s(!1),o=s(!1),t=s(!1),n=s({}),r=s(null),p=s([]),h=H.getPurchaseStatusOptions(),v=d((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),m=d((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),g=d((()=>i()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),y=c({searchText:"",orderNo:"",supplierName:"",status:void 0,startDate:void 0,endDate:void 0}),b=()=>{p.value&&2===p.value.length?(y.startDate=p.value[0],y.endDate=p.value[1]):(y.startDate=void 0,y.endDate=void 0),r.value.reload()},k=()=>{r.value.selectedRowList&&0!==r.value.selectedRowList.length?R.confirm({title:"确认批量删除",content:`确定要删除选中的 ${r.value.selectedRowList.length} 条记录吗？`,icon:f(I),onOk:async()=>{try{j.success("批量删除成功"),b()}catch(e){j.error("批量删除失败："+(e.message||"未知错误"))}}}):j.warning("请选择要删除的记录")},x=()=>{try{H.exportPurchase(y),j.success("导出成功")}catch(e){j.error("导出失败："+(e.message||"未知错误"))}};return u((()=>{})),{superSearch:e,showEdit:a,showDetailModal:l,showConfirmModal:o,showReceiveModal:t,currentRecord:n,tableRef:r,dateRange:p,statusOptions:h,labelCol:v,wrapperCol:m,spanCol:g,where:y,columns:[{title:"入库单号",dataIndex:"orderNo",key:"orderNo",width:160,fixed:"left"},{title:"供应商",key:"supplierInfo",width:200},{title:"状态",key:"status",width:100},{title:"总金额",key:"totalAmount",width:120,align:"right"},{title:"订单日期",dataIndex:"orderDate",key:"orderDate",width:120},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:160},{title:"操作",key:"action",width:200,fixed:"right"}],changeSuperSearch:()=>{e.value=!e.value},reload:b,reset:()=>{y.searchText="",y.orderNo="",y.supplierName="",y.status=void 0,y.startDate=void 0,y.endDate=void 0,p.value=[],b()},getStatusName:e=>H.getPurchaseStatusName(e),getStatusTagColor:e=>H.getStatusTagColor(e),formatAmount:e=>H.formatAmount(e),openAddModal:()=>{n.value={},a.value=!0},editRecord:async e=>{try{console.log("开始加载入库单详情用于编辑，ID:",e.id);const l=await H.detail({id:e.id});console.log("编辑入库单详情API响应:",l),l?(n.value=l,a.value=!0):j.error("获取入库单详情失败，无法编辑")}catch(l){console.error("加载入库单详情失败:",l),j.error("加载入库单详情失败："+(l.message||"未知错误"))}},showDetail:async e=>{try{console.log("开始加载入库单详情，ID:",e.id);const a=await H.detail({id:e.id});console.log("入库单详情API响应:",a),a?(n.value=a,l.value=!0):j.error("获取入库单详情失败")}catch(a){console.error("加载入库单详情失败:",a),j.error("加载入库单详情失败："+(a.message||"未知错误"))}},confirmRecord:async e=>{try{console.log("开始加载入库单详情用于确认，ID:",e.id);const a=await H.detail({id:e.id});console.log("确认入库单详情API响应:",a),a?(n.value=a,o.value=!0):j.error("获取入库单详情失败，无法确认")}catch(a){console.error("加载入库单详情失败:",a),j.error("加载入库单详情失败："+(a.message||"未知错误"))}},receiveRecord:async e=>{try{console.log("开始加载入库单详情用于入库操作，ID:",e.id);const a=await H.detail({id:e.id});console.log("入库操作详情API响应:",a),a?(n.value=a,t.value=!0):j.error("获取入库单详情失败，无法执行入库操作")}catch(a){console.error("加载入库单详情失败:",a),j.error("加载入库单详情失败："+(a.message||"未知错误"))}},deleteRecord:async e=>{R.confirm({title:"删除入库单",content:`确定要删除入库单"${e.orderNo}"吗？删除后无法恢复。`,okText:"确定删除",cancelText:"取消",okType:"danger",icon:f(I),onOk:async()=>{try{await H.delete({id:e.id}),j.success("删除成功"),b()}catch(a){j.error("删除失败："+(a.message||"未知错误"))}}})},moreClick:({key:e})=>{"1"===e?k():"2"===e&&x()},batchDelete:k,exportData:x,handleFormOk:()=>{a.value=!1,b()},handleConfirmOk:()=>{o.value=!1,b()},handleReceiveOk:()=>{t.value=!1,b()}}}},[["render",function(e,a,o,t,n,r){const s=S,d=p("plus-outlined"),i=O,c=N,u=A,R=M,I=p("small-dash-outlined"),j=T,H=P,Q=U,W=E,$=z,G=F,he=L,ve=B,me=Y,fe=V,ge=q,ye=l,be=p("inbound-form"),ke=p("inbound-detail"),xe=p("confirm-inbound-modal"),Ce=p("receive-inbound-modal");return h(),v("div",J,[m("div",K,[m("div",X,[m("div",Z,[m("div",ee,[m("div",ae,[m("div",le,[m("div",oe,[f(s,{size:16})]),m("div",te,[f(s,{size:16},{default:g((()=>[f(i,{type:"primary",class:"border-radius",onClick:t.openAddModal},{default:g((()=>[f(d),a[10]||(a[10]=y(" 新建入库单 "))])),_:1,__:[10]},8,["onClick"]),f(j,null,{overlay:g((()=>[f(R,{onClick:t.moreClick},{default:g((()=>[f(u,{key:"1"},{default:g((()=>[f(c,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[11]||(a[11]=m("span",null,"批量删除",-1))])),_:1,__:[11]}),f(u,{key:"2"},{default:g((()=>[f(c,{iconClass:"icon-opt-daochu",color:"#60666b"}),a[12]||(a[12]=m("span",null,"导出数据",-1))])),_:1,__:[12]})])),_:1},8,["onClick"])])),default:g((()=>[f(i,{class:"border-radius"},{default:g((()=>[a[13]||(a[13]=y(" 更多 ")),f(I)])),_:1,__:[13]})])),_:1})])),_:1})])])]),m("div",ne,[m("div",re,[f(ye,{columns:t.columns,where:t.where,fieldBusinessCode:"ERP_PURCHASE_INBOUND_TABLE",showTableTool:"",showToolTotal:!1,rowId:"id",ref:"tableRef",url:"/purchase/order/page"},{toolLeft:g((()=>[f(H,{value:t.where.searchText,"onUpdate:value":a[0]||(a[0]=e=>t.where.searchText=e),placeholder:"入库单号、供应商名称（回车搜索）",onPressEnter:t.reload,bordered:!1,class:"search-input"},{prefix:g((()=>[f(c,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),f(Q,{type:"vertical",class:"divider"}),m("a",{onClick:a[1]||(a[1]=(...e)=>t.changeSuperSearch&&t.changeSuperSearch(...e))},b(t.superSearch?"收起":"高级筛选"),1)])),toolBottom:g((()=>[t.superSearch?(h(),v("div",se,[f(fe,{model:t.where,labelCol:t.labelCol,"wrapper-col":t.wrapperCol},{default:g((()=>[f(me,{gutter:16},{default:g((()=>[f($,k(x(t.spanCol)),{default:g((()=>[f(W,{label:"入库单号:"},{default:g((()=>[f(H,{value:t.where.orderNo,"onUpdate:value":a[2]||(a[2]=e=>t.where.orderNo=e),placeholder:"请输入入库单号",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),f($,k(x(t.spanCol)),{default:g((()=>[f(W,{label:"供应商:"},{default:g((()=>[f(H,{value:t.where.supplierName,"onUpdate:value":a[3]||(a[3]=e=>t.where.supplierName=e),placeholder:"请输入供应商名称",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),f($,k(x(t.spanCol)),{default:g((()=>[f(W,{label:"状态:"},{default:g((()=>[f(he,{value:t.where.status,"onUpdate:value":a[4]||(a[4]=e=>t.where.status=e),placeholder:"请选择状态",allowClear:""},{default:g((()=>[(h(!0),v(C,null,w(t.statusOptions,(e=>(h(),_(G,{key:e.value,value:e.value},{default:g((()=>[y(b(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),f($,k(x(t.spanCol)),{default:g((()=>[f(W,{label:"订单日期:"},{default:g((()=>[f(ve,{value:t.dateRange,"onUpdate:value":a[5]||(a[5]=e=>t.dateRange=e),style:{width:"100%"},placeholder:["开始日期","结束日期"],format:"YYYY-MM-DD",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),f($,k(x(t.spanCol)),{default:g((()=>[f(W,{label:" ",colon:!1},{default:g((()=>[f(s,null,{default:g((()=>[f(i,{type:"primary",onClick:t.reload},{default:g((()=>a[14]||(a[14]=[y("搜索")]))),_:1,__:[14]},8,["onClick"]),f(i,{onClick:t.reset},{default:g((()=>a[15]||(a[15]=[y("重置")]))),_:1,__:[15]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):D("",!0)])),bodyCell:g((({column:e,record:a})=>["orderNo"===e.key?(h(),v("a",{key:0,onClick:e=>t.showDetail(a),class:"table-link"},b(a.orderNo),9,de)):D("",!0),"supplierInfo"===e.key?(h(),v("div",ie,[m("div",ce,b(a.supplierName),1),m("div",ue,b(a.supplierCode),1)])):D("",!0),"status"===e.key?(h(),_(ge,{key:2,color:t.getStatusTagColor(a.status)},{default:g((()=>[y(b(a.statusName||t.getStatusName(a.status)),1)])),_:2},1032,["color"])):D("",!0),"totalAmount"===e.key?(h(),v("span",pe,b(t.formatAmount(a.totalAmount)),1)):D("",!0),"action"===e.key?(h(),_(s,{key:4,size:16},{default:g((()=>[f(c,{iconClass:"icon-opt-xiangqing","font-size":"24px",title:"查看",color:"#60666b",onClick:e=>t.showDetail(a)},null,8,["onClick"]),"DRAFT"===a.status?(h(),_(c,{key:0,iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>t.editRecord(a)},null,8,["onClick"])):D("",!0),"DRAFT"===a.status?(h(),_(c,{key:1,iconClass:"icon-opt-tongyi","font-size":"24px",title:"确认",color:"#60666b",onClick:e=>t.confirmRecord(a)},null,8,["onClick"])):D("",!0),"CONFIRMED"===a.status?(h(),_(c,{key:2,iconClass:"icon-opt-tianjia","font-size":"24px",title:"入库",color:"#60666b",onClick:e=>t.receiveRecord(a)},null,8,["onClick"])):D("",!0),"DRAFT"===a.status?(h(),_(c,{key:3,iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>t.deleteRecord(a)},null,8,["onClick"])):D("",!0)])),_:2},1024)):D("",!0)])),_:1},8,["columns","where"])])])])])])]),f(be,{visible:t.showEdit,"onUpdate:visible":a[6]||(a[6]=e=>t.showEdit=e),data:t.currentRecord,onOk:t.handleFormOk},null,8,["visible","data","onOk"]),f(ke,{visible:t.showDetailModal,"onUpdate:visible":a[7]||(a[7]=e=>t.showDetailModal=e),data:t.currentRecord},null,8,["visible","data"]),f(xe,{visible:t.showConfirmModal,"onUpdate:visible":a[8]||(a[8]=e=>t.showConfirmModal=e),data:t.currentRecord,onOk:t.handleConfirmOk},null,8,["visible","data","onOk"]),f(Ce,{visible:t.showReceiveModal,"onUpdate:visible":a[9]||(a[9]=e=>t.showReceiveModal=e),data:t.currentRecord,onOk:t.handleReceiveOk},null,8,["visible","data","onOk"])])}],["__scopeId","data-v-aebaad11"]]))}}}));
