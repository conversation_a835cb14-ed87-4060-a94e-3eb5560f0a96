System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./logo-legacy-1fd31909.js"],(function(e,t){"use strict";var n,s,a,l,u,c,d,r,i,o;return{setters:[e=>{n=e.bv,s=e.a,a=e.c,l=e.d,u=e.w,c=e.aR,d=e.b,r=e.at,i=e.a0},null,e=>{o=e._}],execute:function(){const t={class:"guns-body guns-body-card"},g={alt:""};e("default",{__name:"index",setup:e=>(e,y)=>{const b=i,f=n("lazy");return s(),a("div",t,[l(b,{title:"图片懒加载",bordered:!1},{default:u((()=>[c(d("img",g,null,512),[[f,r(o)]])])),_:1})])}})}}}));
