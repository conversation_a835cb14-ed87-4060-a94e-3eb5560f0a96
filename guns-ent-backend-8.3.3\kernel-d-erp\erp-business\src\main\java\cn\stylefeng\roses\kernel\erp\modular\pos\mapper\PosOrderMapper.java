package cn.stylefeng.roses.kernel.erp.modular.pos.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * POS订单Mapper接口
 *
 * <AUTHOR>
 * @since 2025/08/01 10:30
 */
public interface PosOrderMapper extends BaseMapper<PosOrder> {

    /**
     * 根据收银员ID和日期范围查询订单
     *
     * @param cashierId 收银员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态（可选）
     * @param paymentStatus 支付状态（可选）
     * @return 订单列表
     */
    List<PosOrder> findOrdersByCashierAndDateRange(@Param("cashierId") Long cashierId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("orderStatus") String orderStatus,
                                                   @Param("paymentStatus") String paymentStatus);

    /**
     * 统计收银员的销售数据
     *
     * @param cashierId 收银员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 销售统计数据
     */
    Map<String, Object> getCashierSalesStats(@Param("cashierId") Long cashierId,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 根据会员ID查询订单
     *
     * @param memberId 会员ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param orderStatus 订单状态（可选）
     * @return 订单列表
     */
    List<PosOrder> findOrdersByMember(@Param("memberId") Long memberId,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime,
                                     @Param("orderStatus") String orderStatus);

    /**
     * 查询指定时间范围内的销售统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 销售统计数据
     */
    Map<String, Object> getSalesStatsByDateRange(@Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime,
                                                @Param("paymentMethod") String paymentMethod);

    /**
     * 查询热销商品统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 热销商品列表
     */
    List<Map<String, Object>> getHotSellingProducts(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("limit") Integer limit);

    /**
     * 查询支付方式统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 支付方式统计数据
     */
    List<Map<String, Object>> getPaymentMethodStats(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询订单状态统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单状态统计数据
     */
    List<Map<String, Object>> getOrderStatusStats(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询收银员业绩排行
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 收银员业绩排行
     */
    List<Map<String, Object>> getCashierPerformanceRanking(@Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime,
                                                          @Param("limit") Integer limit);

    /**
     * 查询会员消费统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 会员消费统计
     */
    List<Map<String, Object>> getMemberConsumptionStats(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime,
                                                       @Param("limit") Integer limit);

    /**
     * 查询每日销售趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每日销售数据
     */
    List<Map<String, Object>> getDailySalesTrend(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询每小时销售趋势
     *
     * @param date 日期
     * @return 每小时销售数据
     */
    List<Map<String, Object>> getHourlySalesTrend(@Param("date") LocalDateTime date);

}