package cn.stylefeng.roses.kernel.erp.modular.pos.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.PosException;
import cn.stylefeng.roses.kernel.erp.api.exception.ProductException;
import cn.stylefeng.roses.kernel.erp.api.exception.OrderException;
import cn.stylefeng.roses.kernel.erp.api.exception.PaymentException;
import cn.stylefeng.roses.kernel.erp.api.exception.MemberException;
import cn.stylefeng.roses.kernel.erp.api.exception.StockException;
import cn.stylefeng.roses.kernel.erp.api.exception.NetworkException;
import cn.stylefeng.roses.kernel.rule.pojo.response.ErrorResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeoutException;

/**
 * POS系统全局异常处理器
 * 
 * 提供统一的异常处理，包括业务异常、系统异常、网络异常等
 *
 * <AUTHOR>
 * @since 2025/08/01 20:40
 */
@RestControllerAdvice
@Slf4j
public class PosGlobalExceptionHandler {

    /**
     * POS业务异常处理
     */
    @ExceptionHandler(PosException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handlePosException(PosException e) {
        log.warn("POS业务异常: {}", e.getMessage(), e);

        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", e.getErrorCode());
        errorData.put("userMessage", e.getMessage());
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("traceId", MDC.get("traceId"));

        return new ErrorResponseData<>(e.getErrorCode(), e.getMessage(), errorData);
    }

    /**
     * 商品异常处理
     */
    @ExceptionHandler(ProductException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleProductException(ProductException e) {
        log.warn("商品异常: {}", e.getMessage(), e);
        return buildErrorResponse(e, "PRODUCT");
    }

    /**
     * 订单异常处理
     */
    @ExceptionHandler(OrderException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleOrderException(OrderException e) {
        log.warn("订单异常: {}", e.getMessage(), e);
        return buildErrorResponse(e, "ORDER");
    }

    /**
     * 支付异常处理
     */
    @ExceptionHandler(PaymentException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handlePaymentException(PaymentException e) {
        log.error("支付异常: {}", e.getMessage(), e);
        return buildErrorResponse(e, "PAYMENT");
    }

    /**
     * 会员异常处理
     */
    @ExceptionHandler(MemberException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleMemberException(MemberException e) {
        log.warn("会员异常: {}", e.getMessage(), e);
        return buildErrorResponse(e, "MEMBER");
    }

    /**
     * 库存异常处理
     */
    @ExceptionHandler(StockException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleStockException(StockException e) {
        log.error("库存异常: {}", e.getMessage(), e);
        return buildErrorResponse(e, "STOCK");
    }

    /**
     * 网络异常处理
     */
    @ExceptionHandler(NetworkException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleNetworkException(NetworkException e) {
        log.error("网络异常: {}", e.getMessage(), e);
        return buildErrorResponse(e, "NETWORK");
    }

    /**
     * 网络超时异常处理
     */
    @ExceptionHandler({SocketTimeoutException.class, TimeoutException.class, ConnectException.class})
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleTimeoutException(Exception e) {
        log.error("网络超时异常: {}", e.getMessage(), e);
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", "POS_NETWORK_TIMEOUT_001");
        errorData.put("userMessage", "网络连接超时，请检查网络状态");
        errorData.put("severity", "HIGH");
        errorData.put("retryable", true);
        errorData.put("retryDelay", 3000);
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("traceId", MDC.get("traceId"));
        
        return new ErrorResponseData<>("POS_NETWORK_TIMEOUT_001", "网络连接超时，请检查网络状态", errorData);
    }

    /**
     * 数据库异常处理
     */
    @ExceptionHandler({DataAccessException.class, SQLException.class})
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleDataAccessException(Exception e) {
        log.error("数据库访问异常: {}", e.getMessage(), e);
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", "POS_DATABASE_ERROR_001");
        errorData.put("userMessage", "数据访问失败，请稍后重试");
        errorData.put("severity", "HIGH");
        errorData.put("retryable", true);
        errorData.put("retryDelay", 2000);
        errorData.put("needReport", true);
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("traceId", MDC.get("traceId"));
        
        return new ErrorResponseData<>("POS_DATABASE_ERROR_001", "数据访问失败，请稍后重试", errorData);
    }

    /**
     * 参数验证异常处理
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleValidationException(Exception e) {
        log.warn("参数验证异常: {}", e.getMessage());
        
        StringBuilder errorMessage = new StringBuilder("参数验证失败: ");
        
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                errorMessage.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
            }
        } else if (e instanceof BindException) {
            BindException ex = (BindException) e;
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                errorMessage.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
            }
        }
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", "POS_VALIDATION_ERROR_001");
        errorData.put("userMessage", errorMessage.toString());
        errorData.put("severity", "MEDIUM");
        errorData.put("retryable", false);
        errorData.put("timestamp", System.currentTimeMillis());
        
        return new ErrorResponseData<>("POS_VALIDATION_ERROR_001", errorMessage.toString(), errorData);
    }

    /**
     * 约束违反异常处理
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束违反异常: {}", e.getMessage());
        
        StringBuilder errorMessage = new StringBuilder("参数约束违反: ");
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            errorMessage.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
        }
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", "POS_CONSTRAINT_ERROR_001");
        errorData.put("userMessage", errorMessage.toString());
        errorData.put("severity", "MEDIUM");
        errorData.put("retryable", false);
        errorData.put("timestamp", System.currentTimeMillis());
        
        return new ErrorResponseData<>("POS_CONSTRAINT_ERROR_001", errorMessage.toString(), errorData);
    }

    /**
     * 非法参数异常处理
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseData<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", "POS_ILLEGAL_ARGUMENT_001");
        errorData.put("userMessage", "参数错误: " + e.getMessage());
        errorData.put("severity", "MEDIUM");
        errorData.put("retryable", false);
        errorData.put("timestamp", System.currentTimeMillis());
        
        return new ErrorResponseData<>("POS_ILLEGAL_ARGUMENT_001", "参数错误: " + e.getMessage(), errorData);
    }

    /**
     * 通用系统异常处理
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseData<Object> handleGenericException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", "POS_SYSTEM_ERROR_001");
        errorData.put("userMessage", "系统内部错误，请联系管理员");
        errorData.put("severity", "CRITICAL");
        errorData.put("retryable", false);
        errorData.put("needReport", true);
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("traceId", MDC.get("traceId"));
        
        return new ErrorResponseData<>("POS_SYSTEM_ERROR_001", "系统内部错误，请联系管理员", errorData);
    }

    /**
     * 构建错误响应
     */
    private ResponseData<Object> buildErrorResponse(PosException e, String category) {
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", e.getErrorCode());
        errorData.put("userMessage", e.getMessage());
        errorData.put("category", category);
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("traceId", MDC.get("traceId"));

        return new ErrorResponseData<>(e.getErrorCode(), e.getMessage(), errorData);
    }
}
