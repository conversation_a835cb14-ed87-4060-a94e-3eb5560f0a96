package cn.stylefeng.roses.kernel.manage.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.manage.entity.ApiClient;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiClientRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.KeyPair;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * API客户端服务类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
public interface ApiClientService extends IService<ApiClient> {

    /**
     * 新增API客户端
     *
     * @param apiClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    void add(ApiClientRequest apiClientRequest);

    /**
     * 删除API客户端
     *
     * @param apiClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    void del(ApiClientRequest apiClientRequest);

    /**
     * 编辑API客户端
     *
     * @param apiClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    void edit(ApiClientRequest apiClientRequest);

    /**
     * 查询详情API客户端
     *
     * @param apiClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    ApiClient detail(ApiClientRequest apiClientRequest);

    /**
     * 获取API客户端列表
     *
     * @param apiClientRequest 请求参数
     * @return List<ApiClient>  返回结果
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    List<ApiClient> findList(ApiClientRequest apiClientRequest);

    /**
     * 获取API客户端分页列表
     *
     * @param apiClientRequest 请求参数
     * @return PageResult<ApiClient>   返回结果
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    PageResult<ApiClient> findPage(ApiClientRequest apiClientRequest);

    /**
     * 生成随机的公钥私钥对
     *
     * <AUTHOR>
     * @since 2023/10/24 21:07
     */
    KeyPair randomRsaKey();

    /**
     * 更新客户端的状态
     *
     * <AUTHOR>
     * @since 2023/10/24 21:24
     */
    void updateStatus(ApiClientRequest apiClientRequest);

    /**
     * 批量删除API客户端
     *
     * <AUTHOR>
     * @since 2023/10/24 21:28
     */
    void batchDelete(ApiClientRequest apiClientRequest);

    /**
     * 通过客户端编码获取客户端
     *
     * <AUTHOR>
     * @since 2023/10/25 22:44
     */
    ApiClient getByCode(String apiClientCode);

    /**
     * 通过clientId获取到客户端的秘钥字符串，用来解析jwt用
     * <p>
     * 这个接口可以加缓存
     *
     * <AUTHOR>
     * @since 2023/10/26 15:22
     */
    String getApiClientSecret(Long apiClientId);

}
