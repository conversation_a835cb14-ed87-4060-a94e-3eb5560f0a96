package cn.stylefeng.roses.kernel.devops.starter;

import cn.stylefeng.roses.kernel.ca.api.CaClientTokenApi;
import cn.stylefeng.roses.kernel.ca.api.CaLoginUserApi;
import cn.stylefeng.roses.kernel.ca.api.CaSessionManagerApi;
import cn.stylefeng.roses.kernel.ca.api.cookie.CaSessionCookieCreator;
import cn.stylefeng.roses.kernel.ca.api.pojo.CaClientInfo;
import cn.stylefeng.roses.kernel.ca.api.pojo.RemoteSsoProperties;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import cn.stylefeng.roses.kernel.ca.server.core.loginuser.LoginUserServiceImpl;
import cn.stylefeng.roses.kernel.ca.server.core.session.DefaultCaSessionManager;
import cn.stylefeng.roses.kernel.ca.server.core.session.cookie.DefaultCaSessionCookieCreator;
import cn.stylefeng.roses.kernel.ca.server.core.sso.CaClientTokenApiImpl;
import cn.stylefeng.roses.kernel.ca.server.core.timer.ClearInvalidCaUserCacheTimer;
import cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi;
import jakarta.annotation.Resource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 统一认证服务端的配置
 *
 * <AUTHOR>
 * @date 2021/1/20 16:43
 */
@Configuration
public class CaServerAutoConfiguration {

    @Resource(name = "loginCodeCache")
    private CacheOperatorApi<CaLoginUser> loginCodeCache;

    @Resource(name = "caLoginUserCache")
    private CacheOperatorApi<CaLoginUser> caLoginUserCache;

    @Resource(name = "caLoginTokenCache")
    private CacheOperatorApi<Set<String>> caLoginTokenCache;

    @Resource(name = "clientTokenCache")
    private CacheOperatorApi<Set<CaClientInfo>> clientTokenCache;

    /**
     * ca当前登录用户的信息
     *
     * <AUTHOR>
     * @date 2021/1/29 10:01
     */
    @Bean
    public CaLoginUserApi caLoginUserApi(CaSessionManagerApi caSessionManagerApi) {
        return new LoginUserServiceImpl(caSessionManagerApi, loginCodeCache);
    }

    /**
     * 用于ca cookie创建器
     *
     * <AUTHOR>
     * @date 2021/1/29 10:13
     */
    @Bean
    public CaSessionCookieCreator caSessionCookieCreator() {
        return new DefaultCaSessionCookieCreator();
    }

    /**
     * CA会话管理器，管理单点登录的用户
     *
     * <AUTHOR>
     * @date 2021/1/29 10:14
     */
    @Bean
    public CaSessionManagerApi caSessionManagerApi(CaSessionCookieCreator caSessionCookieCreator) {
        return new DefaultCaSessionManager(caLoginUserCache, caLoginTokenCache, caSessionCookieCreator);
    }

    /**
     * 远程sso的配置
     *
     * <AUTHOR>
     * @date 2021/3/4 11:09
     */
    @Bean
    @ConfigurationProperties(prefix = "remote.sso")
    public RemoteSsoProperties remoteSsoProperties() {
        return new RemoteSsoProperties();
    }

    /**
     * 清空无用缓存的定时任务
     *
     * <AUTHOR>
     * @date 2021/3/30 11:32
     */
    @Bean
    public ClearInvalidCaUserCacheTimer clearInvalidCaUserCacheTimer() {
        return new ClearInvalidCaUserCacheTimer(caLoginUserCache, caLoginTokenCache);
    }

    /**
     * 单点客户端token管理
     *
     * <AUTHOR>
     * @date 2022/5/20 11:03
     */
    @Bean
    public CaClientTokenApi caClientTokenApi() {
        return new CaClientTokenApiImpl(clientTokenCache);
    }

}
