package cn.stylefeng.roses.kernel.micro.api;

import cn.hutool.core.lang.Dict;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * feign服务的接口，服务提供者和消费者都实现这个接口
 *
 * <AUTHOR>
 * @date 2021/5/17 14:10
 */
public interface FeignExampleApi {

    /**
     * 示例请求成功
     *
     * <AUTHOR>
     * @date 2021/5/17 14:12
     */
    @RequestMapping(value = "/feignExample/success", method = RequestMethod.POST)
    ResponseData<?> success(@RequestBody Dict dict);

    /**
     * 示例请求失败
     *
     * <AUTHOR>
     * @date 2021/5/17 14:12
     */
    @RequestMapping(value = "/feignExample/error", method = RequestMethod.POST)
    ResponseData<?> error(@RequestBody Dict dict);

}
