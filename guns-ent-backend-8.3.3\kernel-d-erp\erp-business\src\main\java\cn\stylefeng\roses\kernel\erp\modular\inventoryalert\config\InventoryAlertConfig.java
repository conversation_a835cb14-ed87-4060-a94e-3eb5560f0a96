package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 库存预警配置类
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Configuration
@EnableScheduling
@EnableAsync
public class InventoryAlertConfig {
    
    // 配置类暂时为空，主要用于启用定时任务和异步处理
    
}
