<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper.InventoryAlertRecordMapper">

    <!-- 分页查询预警记录 -->
    <select id="selectRecordPage" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRecordResponse">
        SELECT 
            ar.id,
            ar.rule_id,
            ar.product_id,
            ar.alert_type,
            ar.alert_level,
            ar.current_stock,
            ar.threshold_value,
            ar.alert_message,
            ar.status,
            ar.suggested_action,
            ar.suggested_quantity,
            ar.suggested_supplier_id,
            ar.notification_sent,
            ar.notification_methods,
            ar.notification_time,
            ar.handler_user,
            ar.handle_time,
            ar.handle_remark,
            ar.alert_time,
            ar.tenant_id,
            r.rule_name,
            p.product_code,
            p.product_name,
            s.supplier_name AS suggested_supplier_name,
            u.real_name AS handler_user_name
        FROM erp_inventory_alert_record ar
        LEFT JOIN erp_inventory_alert_rule r ON ar.rule_id = r.id
        LEFT JOIN erp_product p ON ar.product_id = p.product_id
        LEFT JOIN erp_supplier s ON ar.suggested_supplier_id = s.supplier_id
        LEFT JOIN sys_user u ON ar.handler_user = u.user_id
        WHERE 1=1
        <if test="request.searchText != null and request.searchText != ''">
            AND (p.product_name LIKE CONCAT('%', #{request.searchText}, '%') 
                 OR p.product_code LIKE CONCAT('%', #{request.searchText}, '%')
                 OR ar.alert_message LIKE CONCAT('%', #{request.searchText}, '%'))
        </if>
        <if test="request.alertTypeFilter != null and request.alertTypeFilter != ''">
            AND ar.alert_type = #{request.alertTypeFilter}
        </if>
        <if test="request.alertLevelFilter != null and request.alertLevelFilter != ''">
            AND ar.alert_level = #{request.alertLevelFilter}
        </if>
        <if test="request.statusFilter != null and request.statusFilter != ''">
            AND ar.status = #{request.statusFilter}
        </if>
        <if test="request.productIdFilter != null">
            AND ar.product_id = #{request.productIdFilter}
        </if>
        <if test="request.alertTimeStart != null">
            AND ar.alert_time >= #{request.alertTimeStart}
        </if>
        <if test="request.alertTimeEnd != null">
            AND ar.alert_time &lt;= #{request.alertTimeEnd}
        </if>
        ORDER BY ar.alert_time DESC
    </select>

    <!-- 查询预警统计数据 -->
    <select id="selectStatistics" resultType="map">
        SELECT 
            COUNT(*) AS total_alerts,
            SUM(CASE WHEN alert_level = 'CRITICAL' THEN 1 ELSE 0 END) AS critical_alerts,
            SUM(CASE WHEN alert_level = 'WARNING' THEN 1 ELSE 0 END) AS warning_alerts,
            SUM(CASE WHEN alert_level = 'INFO' THEN 1 ELSE 0 END) AS info_alerts,
            SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) AS pending_alerts,
            SUM(CASE WHEN status = 'PROCESSING' THEN 1 ELSE 0 END) AS processing_alerts,
            SUM(CASE WHEN status = 'RESOLVED' THEN 1 ELSE 0 END) AS resolved_alerts,
            SUM(CASE WHEN status = 'IGNORED' THEN 1 ELSE 0 END) AS ignored_alerts
        FROM erp_inventory_alert_record
        WHERE 1=1
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        AND alert_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    </select>

    <!-- 查询指定商品的预警记录 -->
    <select id="selectByProductId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRecord">
        SELECT * FROM erp_inventory_alert_record 
        WHERE product_id = #{productId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY alert_time DESC
    </select>

    <!-- 查询指定规则的预警记录 -->
    <select id="selectByRuleId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRecord">
        SELECT * FROM erp_inventory_alert_record 
        WHERE rule_id = #{ruleId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY alert_time DESC
    </select>

    <!-- 批量更新预警记录状态 -->
    <update id="batchUpdateStatus">
        UPDATE erp_inventory_alert_record 
        SET status = #{status},
            handler_user = #{handlerUser},
            handle_time = NOW(),
            handle_remark = #{handleRemark}
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询最近的预警记录 -->
    <select id="selectRecentRecords" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRecordResponse">
        SELECT 
            ar.id,
            ar.rule_id,
            ar.product_id,
            ar.alert_type,
            ar.alert_level,
            ar.current_stock,
            ar.threshold_value,
            ar.alert_message,
            ar.status,
            ar.alert_time,
            r.rule_name,
            p.product_code,
            p.product_name
        FROM erp_inventory_alert_record ar
        LEFT JOIN erp_inventory_alert_rule r ON ar.rule_id = r.id
        LEFT JOIN erp_product p ON ar.product_id = p.product_id
        WHERE ar.status IN ('PENDING', 'PROCESSING')
        <if test="tenantId != null">
            AND ar.tenant_id = #{tenantId}
        </if>
        ORDER BY ar.alert_time DESC
        LIMIT #{limit}
    </select>

</mapper>
