System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js"],(function(e,a){"use strict";var l,t,d,r,u,o,i,s,m,c,n,f,g,p,v,_;return{setters:[e=>{l=e.s,t=e.a,d=e.f,r=e.w,u=e.d,o=e.at,i=e.as,s=e.g,m=e.l,c=e.u,n=e.v,f=e.y,g=e.z,p=e.A,v=e.G,_=e.H},null],execute:function(){e("default",{__name:"dict-form",props:{form:Object,isUpdate:Boolean,dictList:Array},setup(e){const a=e,h=l({dictName:[{required:!0,message:"请输入字典名称",type:"string",trigger:"blur"}],dictCode:[{required:!0,message:"请输入字典编码",type:"string",trigger:"blur"}],dictSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}],statusFlag:[{required:!0,message:"请选择字典状态",type:"number",trigger:"change"}],sysFlag:[{required:!0,message:"请选择是否是系统配置",type:"string",trigger:"change"}],dictParentId:[{required:!0,message:"请选择上级字典",type:"string",trigger:"change"}]});return(l,b)=>{const y=m,x=c,N=n,w=f,S=g,U=p,C=v,q=_;return t(),d(q,{ref:"formRef",model:e.form,rules:h,layout:"vertical"},{default:r((()=>[u(C,{gutter:20},{default:r((()=>[u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"所属字典类型:",name:"dictTypeName"},{default:r((()=>[u(y,{value:e.form.dictTypeName,"onUpdate:value":b[0]||(b[0]=a=>e.form.dictTypeName=a),"allow-clear":"",placeholder:"请输入所属字典类型",disabled:""},null,8,["value"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"字典名称:",name:"dictName"},{default:r((()=>[u(y,{value:e.form.dictName,"onUpdate:value":b[1]||(b[1]=a=>e.form.dictName=a),"allow-clear":"",placeholder:"请输入字典名称"},null,8,["value"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"字典编码(字典值):",name:"dictCode"},{default:r((()=>[u(y,{value:e.form.dictCode,"onUpdate:value":b[2]||(b[2]=a=>e.form.dictCode=a),"allow-clear":"",placeholder:"请输入字典编码",disabled:a.isUpdate},null,8,["value","disabled"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"上级字典:",name:"dictParentId"},{default:r((()=>[u(o(i),{value:e.form.dictParentId,"onUpdate:value":b[3]||(b[3]=a=>e.form.dictParentId=a),style:{width:"100%"},showSearch:"","tree-data":a.dictList,treeNodeFilterProp:"dictName","dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择上级字典",fieldNames:{children:"children",label:"dictName",key:"dictId",value:"dictId"},"allow-clear":"",disabled:a.isUpdate,"tree-default-expand-all":""},null,8,["value","tree-data","disabled"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"排序:",name:"dictSort"},{default:r((()=>[u(w,{value:e.form.dictSort,"onUpdate:value":b[4]||(b[4]=a=>e.form.dictSort=a),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"字典简称:",name:"dictShortName"},{default:r((()=>[u(y,{value:e.form.dictShortName,"onUpdate:value":b[5]||(b[5]=a=>e.form.dictShortName=a),"allow-clear":"",placeholder:"请输入字典简称"},null,8,["value"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"字典简要编码:",name:"dictShortCode"},{default:r((()=>[u(y,{value:e.form.dictShortCode,"onUpdate:value":b[6]||(b[6]=a=>e.form.dictShortCode=a),"allow-clear":"",placeholder:"请输入字典简要编码"},null,8,["value"])])),_:1})])),_:1}),u(N,{xs:24,sm:24,md:12},{default:r((()=>[u(x,{label:"字典状态:",name:"statusFlag"},{default:r((()=>[u(U,{value:e.form.statusFlag,"onUpdate:value":b[7]||(b[7]=a=>e.form.statusFlag=a)},{default:r((()=>[u(S,{value:1},{default:r((()=>b[8]||(b[8]=[s("启用")]))),_:1,__:[8]}),u(S,{value:2},{default:r((()=>b[9]||(b[9]=[s("禁用")]))),_:1,__:[9]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
