<template>
  <div class="popover">
    <a-popover placement="bottomLeft" v-model:visible="visible" @visibleChange="handleHoverChange">
      <!-- title -->
      <template #title>
        <div class="box">
          <div class="all">
            <a-checkbox v-model:checked="checked" @click="checkedClick">全选</a-checkbox>
          </div>
          <div class="title">
            <a @click="save">确定</a>
          </div>
        </div>
      </template>
      <!-- content -->
      <template #content>
        <div style="min-width: 150px; max-height: 150px; overflow: auto; margin-left: -16px">
          <a-tree v-model:checkedKeys="checkedKeys" checkable :tree-data="dictList" :field-names="fieldNames" class="tree" />
        </div>
      </template>
      <div class="name down-title">
        {{ name }} {{ checkedKeysLength > 0 ? '(' + checkedKeysLength + ')' : '' }} <down-outlined style="font-size: 10px" />
      </div>
    </a-popover>
  </div>
</template>

<script setup name="GunsDictPopover">
import { onMounted, ref, watch } from 'vue';
import { SysDictTypeApi } from '@/components/DictSelect/api/SysDictTypeApi';

const props = defineProps({
  fieldNames: {
    type: Object,
    default: {
      children: 'children',
      title: 'dictName',
      key: 'dictId'
    }
  },
  value: {
    type: Array,
    default: []
  },
  //   名称
  name: {
    type: String,
    default: ''
  },
  // 字典类型id
  dictTypeId: {
    type: String,
    default: ''
  }
});

const emits = defineEmits(['update:value', 'change']);

// 选中的长度
const checkedKeysLength = ref(0);

// 是否全选
const checked = ref(false);

// 是否显示
const visible = ref(false);

// tree多选
const checkedKeys = ref([]);

// 字典列表
const dictList = ref([]);

onMounted(() => {
  if (props.dictTypeId) {
    getDictList();
  }
});

// 获取字典列表
const getDictList = async () => {
  dictList.value = await SysDictTypeApi.getDictListByParams({ dictTypeId: props.dictTypeId });
};

// 确定
const save = () => {
  emits('update:value', checkedKeys.value);
  emits('change');
  visible.value = false;
};

// 全选按钮点击
const checkedClick = () => {
  if (!checked.value) {
    let arr = getTreeList();
    checkedKeys.value = arr;
  } else {
    checkedKeys.value = [];
  }
};
// 获取树所有的列表
const getTreeList = () => {
  let arr = [];
  dictList.value.forEach(treeItem => {
    arr.push(treeItem[props.fieldNames.key]);
    if (treeItem[props.fieldNames.children] && treeItem[props.fieldNames.children].length > 0) {
      treeItem[props.fieldNames.children].forEach(childItem => {
        arr.push(childItem[props.fieldNames.key]);
      });
    }
  });
  return arr;
};

// 卡片hover改变事件
const handleHoverChange = val => {
  if (val == false) {
    // context.emit('save', checkedKeys.value);
  }
};

// tree选择监听
watch(checkedKeys, () => {
  let arr = getTreeList();
  checkedKeysLength.value = checkedKeys.value.length;
  if (checkedKeys.value.length > 0 && checkedKeys.value.length == arr.length) {
    checked.value = true;
  } else {
    checked.value = false;
  }
});
</script>

<style lang="less" scoped>
.popover {
  margin-right: 20px;
  display: inline-block;
}

.box {
  width: 100%;
  display: flex;
  position: relative;
}

.title {
  position: absolute;
  right: 10px;
}

.all {
  margin-left: 8px;
}

.name {
  cursor: pointer;
}

.down-title {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 5px;
  color: #000;
  font-size: 14px;

  &:hover {
    background: #e9f3f8;
  }
}
</style>