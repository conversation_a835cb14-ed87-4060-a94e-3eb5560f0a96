package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存预警记录请求参数
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertRecordRequest extends BaseRequest {

    /**
     * 预警记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = {edit.class, delete.class, detail.class, handle.class})
    @ChineseDescription("预警记录ID")
    private Long id;

    /**
     * 预警规则ID
     */
    @NotNull(message = "预警规则ID不能为空", groups = {add.class})
    @ChineseDescription("预警规则ID")
    private Long ruleId;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空", groups = {add.class})
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)
     */
    @NotBlank(message = "预警类型不能为空", groups = {add.class})
    @ChineseDescription("预警类型")
    private String alertType;

    /**
     * 预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)
     */
    @NotBlank(message = "预警级别不能为空", groups = {add.class})
    @ChineseDescription("预警级别")
    private String alertLevel;

    /**
     * 当前库存数量
     */
    @NotNull(message = "当前库存数量不能为空", groups = {add.class})
    @ChineseDescription("当前库存数量")
    private BigDecimal currentStock;

    /**
     * 触发阈值
     */
    @NotNull(message = "触发阈值不能为空", groups = {add.class})
    @ChineseDescription("触发阈值")
    private BigDecimal thresholdValue;

    /**
     * 预警消息
     */
    @NotBlank(message = "预警消息不能为空", groups = {add.class})
    @ChineseDescription("预警消息")
    private String alertMessage;

    /**
     * 处理状态：PENDING(待处理)、PROCESSING(处理中)、RESOLVED(已解决)、IGNORED(已忽略)
     */
    @ChineseDescription("处理状态")
    private String status;

    /**
     * 建议操作
     */
    @ChineseDescription("建议操作")
    private String suggestedAction;

    /**
     * 建议补货数量
     */
    @ChineseDescription("建议补货数量")
    private BigDecimal suggestedQuantity;

    /**
     * 建议供应商ID
     */
    @ChineseDescription("建议供应商ID")
    private Long suggestedSupplierId;

    /**
     * 处理备注
     */
    @ChineseDescription("处理备注")
    private String handleRemark;

    /**
     * 预警时间
     */
    @ChineseDescription("预警时间")
    private Date alertTime;

    // ========== 查询条件 ==========

    /**
     * 搜索文本
     */
    @ChineseDescription("搜索文本")
    private String searchText;

    /**
     * 预警类型过滤
     */
    @ChineseDescription("预警类型过滤")
    private String alertTypeFilter;

    /**
     * 预警级别过滤
     */
    @ChineseDescription("预警级别过滤")
    private String alertLevelFilter;

    /**
     * 处理状态过滤
     */
    @ChineseDescription("处理状态过滤")
    private String statusFilter;

    /**
     * 商品ID过滤
     */
    @ChineseDescription("商品ID过滤")
    private Long productIdFilter;

    /**
     * 预警时间开始
     */
    @ChineseDescription("预警时间开始")
    private Date alertTimeStart;

    /**
     * 预警时间结束
     */
    @ChineseDescription("预警时间结束")
    private Date alertTimeEnd;

    /**
     * 批量处理的ID列表
     */
    @NotEmpty(message = "处理的ID列表不能为空", groups = {batchHandle.class})
    @ChineseDescription("批量处理的ID列表")
    private List<Long> idList;

    /**
     * 处理操作类型：RESOLVE(解决)、IGNORE(忽略)
     */
    @NotBlank(message = "处理操作类型不能为空", groups = {handle.class, batchHandle.class})
    @ChineseDescription("处理操作类型")
    private String handleAction;

    /**
     * 处理分组
     */
    public @interface handle {
    }

    /**
     * 批量处理分组
     */
    public @interface batchHandle {
    }
}
