import{_ as N}from"./index-02bf6f00.js";import{r as l,o as T,k as D,a,c as g,b as t,d as o,w as s,g as r,F as A,f as c,t as F,h as f,M as U,E as j,m as O,n as V,B as z,I as M,l as R,U as $,a7 as L}from"./index-18a1ea24.js";/* empty css              */import{_ as P,D as q}from"./datasource-add-edit-7bf349e3.js";/* empty css              *//* empty css              *//* empty css              */import"./datasource-form-af174b20.js";const G={class:"guns-layout"},H={class:"guns-layout-content"},J={class:"guns-layout"},K={class:"guns-layout-content-application"},Q={class:"content-mian"},W={class:"content-mian-header"},X={class:"header-content"},Y={class:"header-content-left"},Z={class:"header-content-right"},ee={class:"content-mian-body"},te={class:"table-content"},_e=Object.assign({name:"Position"},{__name:"index",setup(oe){const k=l([{key:"index",title:"\u5E8F\u53F7",width:60,align:"center",isShow:!0,fixed:"left"},{dataIndex:"dbName",title:"\u6570\u636E\u5E93\u540D\u79F0",ellipsis:!0,fixed:"left",width:100,isShow:!0},{dataIndex:"jdbcDriver",title:"\u9A71\u52A8\u7C7B\u578B",width:100,ellipsis:!0,isShow:!0},{dataIndex:"username",title:"\u8D26\u53F7",ellipsis:!0,width:100,isShow:!0},{dataIndex:"jdbcUrl",title:"jdbc\u7684url",ellipsis:!0,width:100,isShow:!0},{dataIndex:"statusFlag",title:"\u72B6\u6001",width:60,isShow:!0},{dataIndex:"remarks",title:"\u5907\u6CE8",ellipsis:!0,width:150,isShow:!0},{key:"action",title:"\u64CD\u4F5C",fixed:"right",width:60,isShow:!0}]),h=l(null),u=l({dbName:""}),v=l(null),i=l(!1);T(()=>{});const _=()=>{h.value.reload()},x=d=>{v.value=d,i.value=!0},y=d=>{U.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u591A\u6570\u636E\u6E90\u5417?",icon:o(j),maskClosable:!0,onOk:async()=>{const e=await q.delete({dbId:d.dbId});O.success(e.message),_()}})};return(d,e)=>{const p=V,I=D("plus-outlined"),C=z,b=M,S=R,w=$,B=L,E=N;return a(),g("div",G,[t("div",H,[t("div",J,[t("div",K,[t("div",Q,[t("div",W,[t("div",X,[t("div",Y,[o(p,{size:16})]),t("div",Z,[o(p,{size:16},{default:s(()=>[o(C,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=n=>x())},{default:s(()=>[o(I),e[3]||(e[3]=r("\u65B0\u5EFA"))]),_:1,__:[3]})]),_:1})])])]),t("div",ee,[t("div",te,[o(E,{columns:k.value,where:u.value,rowId:"dbId",ref_key:"tableRef",ref:h,rowSelection:!1,url:"/databaseInfo/page",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"DATASOURCE_TABLE"},{toolLeft:s(()=>[o(S,{value:u.value.dbName,"onUpdate:value":e[1]||(e[1]=n=>u.value.dbName=n),placeholder:"\u6570\u636E\u6E90\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:_,class:"search-input",bordered:!1},{prefix:s(()=>[o(b,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:s(({column:n,record:m})=>[n.dataIndex==="statusFlag"?(a(),g(A,{key:0},[m.statusFlag===1?(a(),c(w,{key:0,color:"success"},{default:s(()=>e[4]||(e[4]=[r("\u6B63\u5E38")])),_:1,__:[4]})):(a(),c(w,{key:1,color:"error"},{default:s(()=>[o(B,null,{title:s(()=>[r(F(m.errorDescription),1)]),default:s(()=>[e[5]||(e[5]=r(" \u8FDE\u63A5\u9519\u8BEF "))]),_:2,__:[5]},1024)]),_:2},1024))],64)):f("",!0),n.key=="action"?(a(),c(p,{key:1},{default:s(()=>[o(b,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:se=>y(m)},null,8,["onClick"])]),_:2},1024)):f("",!0)]),_:1},8,["columns","where"])])])])])])]),i.value?(a(),c(P,{key:0,visible:i.value,"onUpdate:visible":e[2]||(e[2]=n=>i.value=n),data:v.value,onDone:_},null,8,["visible","data"])):f("",!0)])}}});export{_e as default};
