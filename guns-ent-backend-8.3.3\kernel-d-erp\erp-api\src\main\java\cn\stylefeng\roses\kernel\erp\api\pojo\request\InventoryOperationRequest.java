package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 库存操作请求
 *
 * <AUTHOR>
 * @since 2025/07/28 11:30
 */
@Data
public class InventoryOperationRequest {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 操作类型
     */
    @ChineseDescription("操作类型")
    private String operationType;

    /**
     * 操作数量
     */
    @ChineseDescription("操作数量")
    private BigDecimal quantity;

    /**
     * 单位成本（入库时需要）
     */
    @ChineseDescription("单位成本")
    private BigDecimal unitCost;

    /**
     * 关联单据类型
     */
    @ChineseDescription("关联单据类型")
    private String referenceType;

    /**
     * 关联单据ID
     */
    @ChineseDescription("关联单据ID")
    private Long referenceId;

    /**
     * 操作备注
     */
    @ChineseDescription("操作备注")
    private String remark;

}