package cn.stylefeng.roses.kernel.pay.api.enums;

import lombok.Getter;

/**
 * 订单状态
 *
 * <AUTHOR>
 * @date 2021/6/23 23:00
 */
@Getter
public enum OrderStateEnum {

    /**
     * 待支付
     */
    WAIT_PAY(1),

    /**
     * 已完成
     */
    COMPLETE(2),

    /**
     * 已取消
     */
    CANCEL(3),

    /**
     * 已退款
     */
    BACK_MONEY(4);

    Integer code;

    OrderStateEnum(Integer state) {
        this.code = state;
    }

}
