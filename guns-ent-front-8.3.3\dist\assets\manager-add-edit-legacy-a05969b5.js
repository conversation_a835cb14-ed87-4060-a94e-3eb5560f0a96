System.register(["./index-legacy-ee1db0c7.js","./manager-form-legacy-a74e7199.js","./ThemeTemplateApi-legacy-4a2544d6.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js"],(function(e,a){"use strict";var t,l,i,u,s,d,r,m,n,v,o;return{setters:[e=>{t=e.r,l=e.o,i=e.cf,u=e.a,s=e.f,d=e.w,r=e.d,m=e.m,n=e.M},e=>{v=e.default},e=>{o=e.T},null,null],execute:function(){e("default",{__name:"manager-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const g=e,p=a,c=t(!1),f=t(!1),h=t({positionSort:1e3}),y=t(null),b=t({templateId:[{required:!0,message:"请输入用户账号",type:"string",trigger:"blur"}],themeName:[{required:!0,message:"请输入主题名称",type:"string",trigger:"blur"}]}),I=t([]),F=t(!1),j=t({});l((()=>{g.data?(f.value=!0,F.value=!0,N(g.data.templateId).then((()=>{C(g.data)}))):(f.value=!1,F.value=!1)}));const N=async e=>{I.value=await o.detail({templateId:e}),T(),q()},T=()=>{let e={templateId:[{required:!0,message:"请输入用户账号",type:"string",trigger:"blur"}],themeName:[{required:!0,message:"请输入主题名称",type:"string",trigger:"blur"}]};for(let a in I.value){const t=I.value[a];"Y"===t.fieldRequired&&(e[t.fieldCode]=[{required:!0,message:`请输入${t.fieldDescription}`,type:"string",trigger:"blur"}])}b.value=e},q=()=>{for(let e in I.value){const a=I.value[e];"file"===a.fieldType&&(j.value[a.fieldCode]=[])}},C=async e=>{h.value.themeId=e.themeId,h.value.themeName=e.themeName,h.value.templateId=e.templateId;let a=await i.detail({themeId:h.value.themeId});h.value=Object.assign(h.value,a.dynamicForm),j.value=a.tempFileList},L=e=>{p("update:visible",e)},R=async()=>{y.value.$refs.formRef.validate().then((async e=>{if(e){c.value=!0;let e=null;(()=>{let e=h.value.themeName,a=h.value.templateId,t=h.value.themeId,l=JSON.stringify(h.value);h.value={},t&&(h.value.themeId=t),h.value.themeName=e,h.value.templateId=a,h.value.themeValue=l})(),e=f.value?i.edit(h.value):i.add(h.value),e.then((async e=>{c.value=!1,m.success(e.message),L(!1),p("done")})).catch((()=>{c.value=!1}))}}))};return(e,a)=>{const t=n;return u(),s(t,{width:900,maskClosable:!1,visible:g.visible,"confirm-loading":c.value,forceRender:!0,title:f.value?"编辑主题":"新建主题","body-style":{paddingBottom:"8px"},"onUpdate:visible":L,onOk:R,class:"common-modal",onClose:a[2]||(a[2]=e=>L(!1))},{default:d((()=>[r(v,{form:h.value,"onUpdate:form":a[0]||(a[0]=e=>h.value=e),rules:b.value,disabledChangeTemplate:F.value,tempFileList:j.value,"onUpdate:tempFileList":a[1]||(a[1]=e=>j.value=e),onGetThemeAttributes:N,templateFields:I.value,ref_key:"managerFormRef",ref:y},null,8,["form","rules","disabledChangeTemplate","tempFileList","templateFields"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
