<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.purchase.mapper.PurchaseOrderDetailMapper">

    <!-- 根据入库单ID查询明细列表（包含商品信息） -->
    <select id="getDetailsByOrderIdWithProduct" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderDetailResponse">
        SELECT
            pod.id,
            pod.order_id,
            pod.product_id,
            pod.quantity,
            pod.unit_price,
            pod.total_price,
            pod.create_time,
            p.product_code,
            p.product_name,
            p.pricing_type,
            p.unit,
            p.specification
        FROM
            erp_purchase_order_detail pod
        LEFT JOIN
            erp_product p ON pod.product_id = p.product_id
        WHERE
            pod.order_id = #{orderId}
        ORDER BY pod.create_time ASC
    </select>

    <!-- 根据商品ID查询采购历史 -->
    <select id="getPurchaseHistoryByProduct" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.ProductPurchaseHistoryResponse">
        SELECT
            pod.id,
            pod.order_id,
            pod.product_id,
            pod.quantity,
            pod.unit_price,
            pod.total_price,
            pod.create_time,
            po.order_no,
            po.supplier_id,
            po.status,
            po.order_date,
            s.supplier_code,
            s.supplier_name
        FROM
            erp_purchase_order_detail pod
        LEFT JOIN
            erp_purchase_order po ON pod.order_id = po.id
        LEFT JOIN
            erp_supplier s ON po.supplier_id = s.supplier_id
        WHERE
            pod.product_id = #{productId}
            <if test="startDate != null">
                AND po.order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND po.order_date &lt;= #{endDate}
            </if>
        ORDER BY po.order_date DESC, pod.create_time DESC
    </select>

    <!-- 统计商品的采购数量和金额 -->
    <select id="getProductPurchaseStats" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.ProductPurchaseStatsResponse">
        SELECT
            pod.product_id,
            p.product_code,
            p.product_name,
            p.pricing_type,
            COALESCE(SUM(pod.quantity), 0) as totalQuantity,
            COALESCE(SUM(pod.total_price), 0) as totalAmount,
            COUNT(DISTINCT po.supplier_id) as supplierCount,
            COUNT(pod.id) as purchaseCount,
            AVG(pod.unit_price) as avgPrice
        FROM
            erp_purchase_order_detail pod
        LEFT JOIN
            erp_purchase_order po ON pod.order_id = po.id
        LEFT JOIN
            erp_product p ON pod.product_id = p.product_id
        WHERE
            po.status = 'COMPLETED'
            <if test="productId != null">
                AND pod.product_id = #{productId}
            </if>
            <if test="supplierId != null">
                AND po.supplier_id = #{supplierId}
            </if>
            <if test="startDate != null">
                AND po.order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND po.order_date &lt;= #{endDate}
            </if>
        GROUP BY pod.product_id, p.product_code, p.product_name, p.pricing_type
        ORDER BY totalAmount DESC
    </select>

    <!-- 获取供应商的商品采购明细统计 -->
    <select id="getSupplierProductStats" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.SupplierProductStatsResponse">
        SELECT
            po.supplier_id,
            s.supplier_name,
            pod.product_id,
            p.product_code,
            p.product_name,
            COALESCE(SUM(pod.quantity), 0) as totalQuantity,
            COALESCE(SUM(pod.total_price), 0) as totalAmount,
            COUNT(pod.id) as purchaseCount,
            AVG(pod.unit_price) as avgPrice,
            MAX(po.order_date) as lastPurchaseDate
        FROM
            erp_purchase_order_detail pod
        LEFT JOIN
            erp_purchase_order po ON pod.order_id = po.id
        LEFT JOIN
            erp_supplier s ON po.supplier_id = s.supplier_id
        LEFT JOIN
            erp_product p ON pod.product_id = p.product_id
        WHERE
            po.status = 'COMPLETED'
            <if test="supplierId != null">
                AND po.supplier_id = #{supplierId}
            </if>
            <if test="productId != null">
                AND pod.product_id = #{productId}
            </if>
            <if test="startDate != null">
                AND po.order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND po.order_date &lt;= #{endDate}
            </if>
        GROUP BY po.supplier_id, s.supplier_name, pod.product_id, p.product_code, p.product_name
        ORDER BY totalAmount DESC
    </select>

    <!-- 根据入库单ID删除明细 -->
    <delete id="deleteByOrderId">
        DELETE FROM erp_purchase_order_detail WHERE order_id = #{orderId}
    </delete>

    <!-- 批量删除明细 -->
    <delete id="deleteByOrderIds">
        DELETE FROM erp_purchase_order_detail
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

</mapper>