import{ay as ce,az as pe,_ as de,r as S,X as E,o as oe,bs as fe,k as j,a as N,f as ae,w as K,b as C,d as B,g as me,ah as H,c as T,F as X,a2 as Q,h as Y,e as ge,t as ee,m as ve,aK as ye,bt as he,B as Me,S as ne,L as Ae,aH as we,M as _e}from"./index-18a1ea24.js";var te={exports:{}};(function(h,z){(function(s,y){h.exports=y()})(ce,function(){function s(e){var f=[];return e.AMapUI&&f.push(y(e.AMapUI)),e.Loca&&f.push(U(e.Loca)),Promise.all(f)}function y(e){return new Promise(function(f,l){var p=[];if(e.plugins)for(var o=0;o<e.plugins.length;o+=1)t.AMapUI.plugins.indexOf(e.plugins[o])==-1&&p.push(e.plugins[o]);if(u.AMapUI===r.failed)l("\u524D\u6B21\u8BF7\u6C42 AMapUI \u5931\u8D25");else if(u.AMapUI===r.notload){u.AMapUI=r.loading,t.AMapUI.version=e.version||t.AMapUI.version,o=t.AMapUI.version;var v=document.body||document.head,m=document.createElement("script");m.type="text/javascript",m.src="https://webapi.amap.com/ui/"+o+"/main.js",m.onerror=function(c){u.AMapUI=r.failed,l("\u8BF7\u6C42 AMapUI \u5931\u8D25")},m.onload=function(){if(u.AMapUI=r.loaded,p.length)window.AMapUI.loadUI(p,function(){for(var c=0,w=p.length;c<w;c++){var L=p[c].split("/").slice(-1)[0];window.AMapUI[L]=arguments[c]}for(f();d.AMapUI.length;)d.AMapUI.splice(0,1)[0]()});else for(f();d.AMapUI.length;)d.AMapUI.splice(0,1)[0]()},v.appendChild(m)}else u.AMapUI===r.loaded?e.version&&e.version!==t.AMapUI.version?l("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C AMapUI \u6DF7\u7528"):p.length?window.AMapUI.loadUI(p,function(){for(var c=0,w=p.length;c<w;c++){var L=p[c].split("/").slice(-1)[0];window.AMapUI[L]=arguments[c]}f()}):f():e.version&&e.version!==t.AMapUI.version?l("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C AMapUI \u6DF7\u7528"):d.AMapUI.push(function(c){c?l(c):p.length?window.AMapUI.loadUI(p,function(){for(var w=0,L=p.length;w<L;w++){var O=p[w].split("/").slice(-1)[0];window.AMapUI[O]=arguments[w]}f()}):f()})})}function U(e){return new Promise(function(f,l){if(u.Loca===r.failed)l("\u524D\u6B21\u8BF7\u6C42 Loca \u5931\u8D25");else if(u.Loca===r.notload){u.Loca=r.loading,t.Loca.version=e.version||t.Loca.version;var p=t.Loca.version,o=t.AMap.version.startsWith("2"),v=p.startsWith("2");if(o&&!v||!o&&v)l("JSAPI \u4E0E Loca \u7248\u672C\u4E0D\u5BF9\u5E94\uFF01\uFF01");else{o=t.key,v=document.body||document.head;var m=document.createElement("script");m.type="text/javascript",m.src="https://webapi.amap.com/loca?v="+p+"&key="+o,m.onerror=function(c){u.Loca=r.failed,l("\u8BF7\u6C42 AMapUI \u5931\u8D25")},m.onload=function(){for(u.Loca=r.loaded,f();d.Loca.length;)d.Loca.splice(0,1)[0]()},v.appendChild(m)}}else u.Loca===r.loaded?e.version&&e.version!==t.Loca.version?l("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C Loca \u6DF7\u7528"):f():e.version&&e.version!==t.Loca.version?l("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C Loca \u6DF7\u7528"):d.Loca.push(function(c){c?l(c):l()})})}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var r;(function(e){e.notload="notload",e.loading="loading",e.loaded="loaded",e.failed="failed"})(r||(r={}));var t={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},u={AMap:r.notload,AMapUI:r.notload,Loca:r.notload},d={AMap:[],AMapUI:[],Loca:[]},_=[],b=function(e){typeof e=="function"&&(u.AMap===r.loaded?e(window.AMap):_.push(e))};return{load:function(e){return new Promise(function(f,l){if(u.AMap==r.failed)l("");else if(u.AMap==r.notload){var p=e.key,o=e.version,v=e.plugins;p?(window.AMap&&location.host!=="lbs.amap.com"&&l("\u7981\u6B62\u591A\u79CDAPI\u52A0\u8F7D\u65B9\u5F0F\u6DF7\u7528"),t.key=p,t.AMap.version=o||t.AMap.version,t.AMap.plugins=v||t.AMap.plugins,u.AMap=r.loading,o=document.body||document.head,window.___onAPILoaded=function(c){if(delete window.___onAPILoaded,c)u.AMap=r.failed,l(c);else for(u.AMap=r.loaded,s(e).then(function(){f(window.AMap)}).catch(l);_.length;)_.splice(0,1)[0]()},v=document.createElement("script"),v.type="text/javascript",v.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+t.AMap.version+"&key="+p+"&plugin="+t.AMap.plugins.join(","),v.onerror=function(c){u.AMap=r.failed,l(c)},o.appendChild(v)):l("\u8BF7\u586B\u5199key")}else if(u.AMap==r.loaded)if(e.key&&e.key!==t.key)l("\u591A\u4E2A\u4E0D\u4E00\u81F4\u7684 key");else if(e.version&&e.version!==t.AMap.version)l("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C JSAPI \u6DF7\u7528");else{if(p=[],e.plugins)for(o=0;o<e.plugins.length;o+=1)t.AMap.plugins.indexOf(e.plugins[o])==-1&&p.push(e.plugins[o]);p.length?window.AMap.plugin(p,function(){s(e).then(function(){f(window.AMap)}).catch(l)}):s(e).then(function(){f(window.AMap)}).catch(l)}else if(e.key&&e.key!==t.key)l("\u591A\u4E2A\u4E0D\u4E00\u81F4\u7684 key");else if(e.version&&e.version!==t.AMap.version)l("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C JSAPI \u6DF7\u7528");else{var m=[];if(e.plugins)for(o=0;o<e.plugins.length;o+=1)t.AMap.plugins.indexOf(e.plugins[o])==-1&&m.push(e.plugins[o]);b(function(){m.length?window.AMap.plugin(m,function(){s(e).then(function(){f(window.AMap)}).catch(l)}):s(e).then(function(){f(window.AMap)}).catch(l)})}})},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,t={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},u={AMap:r.notload,AMapUI:r.notload,Loca:r.notload},d={AMap:[],AMapUI:[],Loca:[]}}}})})(te);var be=te.exports;const ke=pe(be);const Se={class:"guns-map-picker-header"},Ie={class:"guns-map-picker-header-search"},Ce={class:"guns-map-picker-body"},Ue={class:"guns-map-picker-main"},Le=["src"],Pe=["onClick"],xe={class:"guns-map-picker-poi-item-title"},Be={key:0,class:"guns-map-picker-poi-item-address"},q="guns-map-picker-main-icon",Ne={__name:"map-view",props:{height:{type:String,default:"450px"},center:Array,zoom:{type:Number,default:11},chooseZoom:{type:Number,default:17},poiSize:{type:Number,default:30},poiType:{type:String,default:""},poiKeywords:{type:String,default:""},poiRadius:{type:Number,default:1e3},needCity:Boolean,forceChoose:{type:Boolean,default:!0},suggestionCity:{type:String,default:"\u5168\u56FD"},searchType:{type:Number,default:0,validator:h=>[0,1].includes(h)},searchPlaceholder:String,markerSrc:{type:String,default:"https://3gimg.qq.com/lightmap/components/locationPicker2/image/marker.png"},mapKey:String,mapVersion:{type:String,default:"2.0"},mapStyle:String,darkMode:Boolean},emits:["done","map-done"],setup(h,{emit:z}){const s=h,y=z,U=S(null),r=S(!1),t=S(!1),u=S(!1),d=S([]),_=S([]),b=S([q]),e=S("");let f="",l=null,p=!1,o=null,v=null,m=null,c=null;const w=()=>{!s.mapKey||o||ke.load({key:s.mapKey,version:s.mapVersion,plugins:["AMap.PlaceSearch","AMap.AutoComplete"]}).then(n=>{$(),m=new n.AutoComplete({city:s.suggestionCity}),v=new n.PlaceSearch({type:s.poiType,pageSize:s.poiSize,pageIndex:1});const a=(()=>{if(s.mapStyle)return s.mapStyle;if(s.darkMode)return"amap://styles/dark"})();o=new n.Map(U.value,{zoom:s.zoom,center:s.center,resizeEnable:!0,mapStyle:a}),o.on("complete",()=>{r.value=!1;const{lng:i,lat:g}=o.getCenter();Z(i,g)}),o.on("moveend",()=>{if(p)p=!1;else if(s.searchType===0){J();const{lng:i,lat:g}=o.getCenter();Z(i,g)}}),c=new n.Marker({icon:new n.Icon({image:s.markerSrc,size:new n.Size(26,36.5),imageSize:new n.Size(26,36.5)}),offset:new n.Pixel(-13,-36.5)}),y("map-done",o)}).catch(n=>{console.error(n)})},L=n=>{!n||f===n||(f=n,s.searchType!==0&&(t.value=!0),le(n).then(a=>{s.searchType!==0?(d.value=a,t.value=!1,V()):_.value=a}).catch(a=>{console.error(a),t.value=!1}))},O=(n,a)=>{(!d.value.length||d.value[0].name!==a.name)&&(d.value=[{...a,selected:!0},...d.value.map(i=>({...i,selected:!1}))]),R(a.location.lng,a.location.lat,s.chooseZoom),l=a},ie=n=>{p=!0,d.value=d.value.map(g=>({...g,selected:g===n}));const{lng:a,lat:i}=n.location;R(a,i,s.chooseZoom),s.searchType===0?J():F(a,i)},se=()=>{const n=D();if(!n){if(s.forceChoose){ve.error("\u8BF7\u70B9\u51FB\u5217\u8868\u9009\u4E2D\u4F4D\u7F6E");return}u.value=!0,W(s.needCity).then(i=>{u.value=!1,y("done",i)}).catch(i=>{console.error(i),u.value=!1,y("done",{})});return}const a={...n.location,name:n.name,address:n.address||""};s.needCity?(u.value=!0,R(a.lng,a.lat),W(!0).then(({city:i})=>{u.value=!1,a.city=i,y("done",a)}).catch(i=>{console.error(i),u.value=!1,y("done",a)})):y("done",a)},le=n=>new Promise((a,i)=>{if(!m){i(new Error("AutoComplete instance is null"));return}m.search(n,(g,M)=>{if(!(M!=null&&M.tips)){a([]);return}const I=M.tips.filter(A=>!!A.location).map(A=>{const P="".concat(A.name,"(").concat(A.district,")");return{...A,label:P,value:P,key:A.id||P,address:Array.isArray(A.address)?A.address[0]:A.address}});a(I)})}),Z=(n,a)=>{t.value=!0,re(n,a).then(i=>{l?i.length===0||i[0].name!==l.name?d.value=[{...l,selected:!0},...i]:d.value=i.map((g,M)=>({...g,selected:M===0})):d.value=i,t.value=!1}).catch(i=>{console.error(i),t.value=!1,d.value=[]})},re=(n,a)=>new Promise((i,g)=>{if(!v){g(new Error("PlaceSearch instance is null"));return}v.searchNearBy(s.poiKeywords,[n,a],s.poiRadius,(M,I)=>{var A;if(M==="complete"&&((A=I==null?void 0:I.poiList)!=null&&A.pois)){const P=I.poiList.pois.filter(x=>!!x.location).map((x,k)=>({...x,key:x.id||"".concat(x.name,"-").concat(k)}));i(P)}else M==="no_data"?i([]):g(new Error(M))})}),D=()=>d.value.find(n=>n.selected),J=()=>{b.value=[q],ye(()=>{setTimeout(()=>{b.value=[q,"guns-map-picker-anim-bounce"]},0)})},V=()=>{c&&o&&o.remove(c)},F=(n,a)=>{if(!c){console.error("centerMarker is null");return}if(!o){console.error("map instance is null");return}n!=null&&a!=null?(c.setPosition([n,a]),o.add(c)):V()},R=(n,a,i)=>{o&&n!=null&&a!=null&&(i==null?o.setCenter([n,a]):o.setZoomAndCenter(i,[n,a]))},W=n=>new Promise((a,i)=>{if(!o){i(new Error("map instance is null"));return}const g=o.getCenter();n?o.getCity(M=>{g.city=M,a(g)}):a(g)}),G=n=>{o&&(typeof n=="boolean"?n?o.setMapStyle("amap://styles/dark"):o.setMapStyle("amap://styles/normal"):n&&o.setMapStyle(n))},ue=()=>{o&&o.destroy(),c=null,v=null,m=null,o=null},$=()=>{ue(),d.value=[],_.value=[],e.value="",f="",l=null,p=!1};return E(()=>s.darkMode,n=>{s.mapStyle||G(n)}),E(()=>s.mapStyle,n=>{n&&G(n)}),E(()=>s.searchType,n=>{if(e.value="",_.value=[],l=null,f="",V(),n===1){const a=D();if(a){const{lng:i,lat:g}=a.location;F(i,g)}}}),E(()=>s.mapKey,()=>{$(),w()}),oe(()=>{w()}),fe(()=>{$()}),(n,a)=>{const i=he,g=Me,M=j("PlusOutlined"),I=j("EnvironmentOutlined"),A=j("CheckCircleOutlined"),P=ne,x=ne;return N(),ae(x,{spinning:r.value},{default:K(()=>[C("div",Se,[C("div",Ie,[B(i,{"allow-clear":"",value:e.value,"onUpdate:value":a[0]||(a[0]=k=>e.value=k),options:_.value,placeholder:h.searchPlaceholder,onSelect:O,onSearch:L},null,8,["value","options","placeholder"])]),B(g,{type:"primary",loading:u.value,onClick:se},{default:K(()=>a[1]||(a[1]=[me(" \u786E\u5B9A ")])),_:1,__:[1]},8,["loading"])]),C("div",Ce,[C("div",Ue,[C("div",{ref_key:"mapRef",ref:U,style:H({height:h.height})},null,4),h.searchType===0?(N(),T(X,{key:0},[B(M,{class:"guns-map-picker-main-plus"}),C("img",{class:Q(b.value),src:h.markerSrc,alt:""},null,10,Le)],64)):Y("",!0)]),B(P,{spinning:t.value},{default:K(()=>[C("div",{class:"guns-map-picker-poi-list",style:H({height:h.height})},[(N(!0),T(X,null,ge(d.value,k=>(N(),T("div",{key:k.key,class:Q(["guns-map-picker-poi-item",{active:k.selected}]),onClick:Ee=>ie(k)},[B(I,{class:"guns-map-picker-poi-item-icon"}),C("div",xe,ee(k.name),1),k.address?(N(),T("div",Be,ee(k.address),1)):Y("",!0),B(A,{class:"guns-map-picker-poi-item-check"})],10,Pe))),128))],4)]),_:1},8,["spinning"])])]),_:1},8,["spinning"])}}},ze=de(Ne,[["__scopeId","data-v-e27826be"]]),Ke={__name:"index",props:{visible:Boolean,title:String,width:{type:String,default:"780px"},height:{type:String,default:"450px"},center:Array,zoom:{type:Number,default:11},chooseZoom:{type:Number,default:17},poiSize:{type:Number,default:30},poiType:{type:String,default:""},poiKeywords:{type:String,default:""},poiRadius:{type:Number,default:1e3},needCity:Boolean,forceChoose:{type:Boolean,default:!0},suggestionCity:{type:String,default:"\u5168\u56FD"},searchType:{type:Number,default:0,validator:h=>[0,1].includes(h)},searchPlaceholder:{type:String,default:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57\u641C\u7D22"},markerSrc:{type:String,default:"https://3gimg.qq.com/lightmap/components/locationPicker2/image/marker.png"},mapKey:String,mapVersion:{type:String,default:"2.0"},mapStyle:String,darkMode:Boolean},emits:["update:visible","done"],setup(h,{expose:z,emit:s}){const y=h,U=s,r=S(!1),t=S(!1),u=Ae(()=>y.mapKey||"698cdf3e7e17e75732d2659001e21660");oe(()=>{});const d=b=>{U("update:visible",b)},_=b=>{U("done",b),d(!1)};return z({mapRef:t}),(b,e)=>{const f=_e;return N(),ae(f,{width:y.width,maskClosable:!1,visible:y.visible,"confirm-loading":r.value,forceRender:!0,title:"\u9009\u62E9\u4F4D\u7F6E","body-style":{padding:0},"onUpdate:visible":d,footer:"",class:"common-modal",onClose:e[0]||(e[0]=l=>d(!1))},{default:K(()=>[B(ze,we(y,{ref_key:"mapRef",ref:t,"map-key":u.value,onDone:_}),null,16,["map-key"])]),_:1},8,["width","visible","confirm-loading"])}}};export{Ke as _};
