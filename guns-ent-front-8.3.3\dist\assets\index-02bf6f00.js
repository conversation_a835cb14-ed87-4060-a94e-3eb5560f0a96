import{R as D,aB as se,_ as ie,aC as de,r as y,s as ue,aD as ce,o as re,aE as fe,X as x,a as B,c as A,b as K,d as P,w as U,t as J,h as N,aF as T,f as he,at as W,aG as ge,e as pe,aH as j,F as me,g as ye,a2 as Se,aI as Ce,aJ as ve,aK as we,n as xe,i as Ie}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */class ke{static getUserConfig(o){return D.getAndLoadData("/sysTableWidth/getUserConfig",o)}static setTableWidth(o){return D.post("/sysTableWidth/setTableWidth",o)}}function be(i){return"".concat(i,"Size")}function _e(i){if(i){const o=localStorage.getItem(be(i));if(o)return JSON.parse(o)}}function q(i){const o=[];return i.forEach(u=>{var c;if((c=u.children)!=null&&c.length){const t=q(u.children);t.length&&o.push({...u,checked:!0,children:t})}else o.push({...u,checked:!u.hideInTable})}),o}function ze(i,o,u){const c=H(o);return c?G(i,c.data,c.checkedIds,u):q(i)}function G(i,o,u,c){const t=[];return i&&i.forEach(s=>{var m,C;const l=I(s),f=o.find(g=>g.id===l),h=(m=f==null?void 0:f.fixed)!=null?m:s.fixed,d=(C=f==null?void 0:f.checked)!=null?C:!0;if(s.children){const g=G(s.children,o,u,c);g.length&&t.push({...s,fixed:h,checked:d,children:g})}else t.push({...s,fixed:h,checked:d})}),c&&t.length&&t.sort((s,l)=>{var C,g;const f=I(s),h=I(l);let d=o.findIndex(S=>S.id===f),m=o.findIndex(S=>S.id===h);if(d===-1&&((C=s.children)!=null&&C.length)){const S=I(s.children[0]);d=o.findIndex(k=>k.id===S)}if(m===-1&&((g=l.children)!=null&&g.length)){const S=I(l.children[0]);m=o.findIndex(k=>k.id===S)}return d===-1&&s.hideInSetting&&s.fixed==="right"&&(d=o.length),s.fixed===!0||s.fixed==="left"?d-=o.length:(s.fixed==="right"||s.hideInSetting&&!s.fixed)&&(d+=o.length),m===-1&&l.hideInSetting&&l.fixed==="right"&&(m=o.length),l.fixed===!0||l.fixed==="left"?m-=o.length:(l.fixed==="right"||l.hideInSetting&&!l.fixed)&&(m+=o.length),d-m}),t}function H(i){if(i)try{const o=localStorage.getItem(Be(i));if(o){const u=JSON.parse(o);if(Array.isArray(u)){const c=u.filter(t=>t.checked).map(t=>t.id);return{data:u,checkedIds:c,isAllChecked:u.length>0&&u.length===c.length,isIndeterminate:c.length!==0&&u.length!==c.length}}}}catch(o){}}function Be(i){return"".concat(i,"Cols")}function I(i){const o=i.key||i.dataIndex;return(Array.isArray(o)?o:[o]).join(".")}function $e(i,o,u){const c=H(u);if(c)return c;const t=[];i&&se(i,l=>{var h;const f=I(l);f&&!((h=l.children)!=null&&h.length)&&!l.hideInSetting&&t.push({id:f,key:l.key,width:l.width||120,dataIndex:l.dataIndex,title:l.title||o,checked:l.checked,fixed:l.fixed,...l})});const s=t.filter(l=>l.checked).map(l=>l.id);return{data:t,checkedIds:s,isAllChecked:t.length>0&&t.length===s.length,isIndeterminate:s.length!==0&&t.length!==s.length}}const Te={key:0,class:"table-tool"},Re={class:"table-tool-top"},Ae={class:"table-tool-top-left"},Ke={key:0,class:"total-num"},Ne={class:"table-tool-top-right"},Ee={class:"table-tool-bottom"},Le=Object.assign({name:"GunsTable"},{__name:"index",props:{columns:{type:Array,default:[]},rowId:{type:String,default:"id"},url:{type:String,default:""},methods:{type:String,default:"get"},where:{type:Object,default:{}},isSort:{type:Boolean,default:!1},bordered:{type:Boolean,default:!1},isPage:{type:Boolean,default:!0},isRadio:{type:Boolean,default:!1},pageSize:{type:Number,default:20},scroll:{type:Object,default:{x:"max-content",y:"100%"}},isInit:{type:Boolean,default:!0},rowSelection:{type:Boolean,default:!0},isShowRowSelect:{type:Boolean,default:!1},size:{type:String,default:"small"},checkStrictly:{type:Boolean,default:!1},customData:{type:Function,default:null},dataSource:{type:Array,default:[]},expandIconColumnIndex:{type:Number,default:0},isLoad:{type:[Function,Boolean],default:()=>!0},selection:Array,loading:{type:Boolean,default:!1},defaultExpandedRowKeys:Array,expandedRowKeys:Array,showTool:{type:Boolean,default:!0},showTableTool:{type:Boolean,default:!1},tools:{type:Array,default:()=>["reload","size","columns","fullscreen"]},cacheKey:String,fieldBusinessCode:{type:String,default:""},showToolTotal:{type:Boolean,default:!0},childrenColumnName:{type:String,default:"children"},montageParams:{type:Boolean,default:!1},height100:{type:Boolean,default:!0}},emits:["tableListChange","onSelect","onSelectAll","customRowClick","getTotal","update:selection","expand","size-change","columns-change","fullscreen-change"],setup(i,{expose:o,emit:u}){de(e=>({37496551:t.showTool?0:"1px solid rgba(197, 207, 209, 0.4)"}));const c=Ce(()=>ve(()=>import("./table-tool-d923d620.js"),["assets/table-tool-d923d620.js","assets/index-18a1ea24.js","assets/index-747cb573.css","assets/index-cd3d6e23.js","assets/index-64ac9434.css","assets/table-tool-ea3aa128.css","assets/index-3a72e44e.css","assets/index-7acfe497.css","assets/index-27dc9b3a.css"])),t=i,s=u,l=y([]),f=y(!1),h=ue({current:1,pageSize:t.pageSize,showSizeChanger:!0,showLessItems:!0,pageSizeOptions:["10","20","50","100"],showTotal:e=>"\u5171 ".concat(e," \u6761"),onShowSizeChange:(e,a)=>M(e,a),onChange:(e,a)=>Z(e),total:0}),d=y([]),m=y(""),C=y(""),g=y([]),S=ce(),k=Object.keys(S),b=y([]),R=y("small"),E=y(!1),_=y([]);re(()=>{F(!0),t.isInit&&z()});const F=async(e=!1)=>{let a=t.columns;if(t.fieldBusinessCode&&e){const n=await ke.getUserConfig({fieldBusinessCode:t.fieldBusinessCode});n.tableWidthJson&&(a=JSON.parse(n.tableWidthJson))}a?_.value=ze(a,t.cacheKey,!0):_.value=[]},z=()=>{if(b.value=[],t.url&&t.isLoad){f.value=!0;let e={...t.where};t.isSort&&(e.sortBy=m.value,e.orderBy=fe(C.value)),t.isPage&&(e.pageNo=h.current||1,e.pageSize=h.pageSize||20);let a="";if(t.montageParams){e===void 0&&(e={});let n="?";for(let p in e)e[p]&&(n=n+p+"="+e[p]+"&");n=n.substring(0,n.length-1),a="".concat(t.url).concat(n)}else a=t.url;D[t.methods](a,t.montageParams?{}:e).then(n=>{var p,v;t.isPage?(t.customData&&(n=t.customData(n)),l.value=n.data.rows,h.total=n.data.totalRows,s("getTotal",n.data.totalRows)):(t.customData&&(l.value=t.customData(n)),l.value=n.data,s("getTotal",(v=(p=l.value)==null?void 0:p.length)!=null?v:0))}).finally(()=>f.value=!1)}},M=(e,a)=>{h.pageSize=a,z()},X=(e,a)=>{d.value=e;const n=e.map(p=>{const v=a.find(r=>L(r,t.rowId)===p);return v!=null?v:t.selection==null||!t.selection.length?null:t.selection.find(r=>L(r,t.rowId)===p)}).filter(p=>p!=null);s("update:selection",n)},L=(e,a)=>{if(typeof a=="function")return a(e);if(a){let n=e;return a.split(".").forEach(p=>{n=n?n[p]:null}),n}},Q=(e,a,n)=>{g.value=n,s("onSelect",e,a,n)},Y=(e,a,n)=>{s("onSelectAll",e,a,n)},Z=(e,a)=>{e!=h.current&&(h.current=e,z())},ee=(e,a,n)=>{n.order=="ascend"?m.value="asc":n.order=="descend"&&(m.value="desc"),C.value=n.field,t.isSort&&z()},$=()=>{we(()=>{h.current=1,z()})},te=(e,a)=>({onClick:()=>{t.isShowRowSelect&&(t.isRadio?(d.value=[e[t.rowId]],s("onSelect",e,!0,[e])):g.value.find(n=>n[t.rowId]==e[t.rowId])?(d.value.splice(d.value.findIndex(n=>n===e[t.rowId]),1),g.value.splice(g.value.findIndex(n=>n[t.rowId]===e[t.rowId]),1),s("onSelect",e,!1,g.value)):(d.value.push(e[t.rowId]),g.value.push(e),s("onSelect",e,!0,g.value))),s("customRowClick",e,a)}}),ne=(e,a)=>{s("expand",e,a)},ae=e=>{setTimeout(()=>{l.value=[...e]},500)},le=e=>{R.value=e,s("size-change",e)},O=e=>{_.value=e,s("columns-change",e)},oe=e=>{E.value=e,s("fullscreen-change",e)};return x(()=>t.dataSource,e=>{t.url||(l.value=e)},{deep:!0,immediate:!0}),x(()=>t.columns,e=>{e.some(n=>"checked"in n)?O(e):F()},{deep:!0}),x(()=>t.selection,e=>{if(!t.isRadio)if(e!=null&&e.length){const a=e.map(n=>L(n,t.rowId));if(a.length!==d.value.length)d.value=a;else for(let n=0;n<a.length;n++)if(!d.value.includes(a[n])){d.value=a;return}}else d.value.length&&(d.value=[])},{immediate:!0,deep:!0}),x(()=>t.loading,e=>{f.value=e}),x(()=>t.expandedRowKeys,e=>{b.value=e},{deep:!0}),x(()=>t.size,e=>{var a,n;R.value=(n=(a=_e(t.cacheKey))!=null?a:t.size)!=null?n:"small"},{deep:!0,immediate:!0}),o({reload:$,list:l,setList:ae,expandedRowKeys:b,tableLoading:f,selectedRowList:d}),(e,a)=>{var v;const n=xe,p=Ie;return B(),A("div",{class:Se(["table-top",{"table-fullscreen":E.value},{"table-height-100":t.height100}])},[t.showTool?(B(),A("div",Te,[K("div",Re,[K("div",Ae,[P(n,{size:16,class:"table-tool-top-left-space"},{default:U(()=>{var r;return[i.showToolTotal?(B(),A("span",Ke,"\u5171 "+J(t.isPage?h.total:(r=l.value)==null?void 0:r.length)+" \u4E2A",1)):N("",!0),T(e.$slots,"toolLeft",{},void 0,!0)]}),_:3})]),K("div",Ne,[P(n,{size:16},{default:U(()=>[T(e.$slots,"toolRight",{},void 0,!0),t.showTableTool?(B(),he(W(c),{key:0,tools:t.tools,size:R.value,cacheKey:i.cacheKey,fullscreen:E.value,onReload:$,columns:_.value,fieldBusinessCode:t.fieldBusinessCode,"onUpdate:size":le,"onUpdate:columns":O,"onUpdate:fullscreen":oe},null,8,["tools","size","cacheKey","fullscreen","columns","fieldBusinessCode"])):N("",!0)]),_:3})])]),K("div",Ee,[T(e.$slots,"toolBottom",{},void 0,!0)])])):N("",!0),P(p,{"data-source":l.value,"row-key":i.rowId,size:R.value,class:"table",bordered:i.bordered,columns:_.value.filter((r,w)=>{if(r!=null&&r.checked)return r}),loading:f.value,defaultExpandedRowKeys:t.defaultExpandedRowKeys,expandedRowKeys:b.value,"onUpdate:expandedRowKeys":a[0]||(a[0]=r=>b.value=r),expandIconColumnIndex:t.expandIconColumnIndex,pagination:i.isPage?h:!1,onChange:ee,onExpand:ne,"row-selection":i.rowSelection?{type:i.isRadio?"radio":"checkbox",selectedRowKeys:d.value,onChange:X,onSelect:Q,checkStrictly:t.checkStrictly,onSelectAll:Y}:null,scroll:((v=l.value)==null?void 0:v.length)>0?i.scroll:{y:"100%"},customRow:te,childrenColumnName:t.childrenColumnName},ge({_:2},[pe(W(k),r=>({name:r,fn:U(w=>[r=="bodyCell"?T(e.$slots,r,j({key:0,scope:w},w||{}),()=>{var V;return[((V=w.column)==null?void 0:V.title)=="\u5E8F\u53F7"?(B(),A(me,{key:0},[ye(J(w.index+1),1)],64)):N("",!0)]},!0):T(e.$slots,r,j({key:1,scope:w},w||{}),void 0,!0)])}))]),1032,["data-source","row-key","size","bordered","columns","loading","defaultExpandedRowKeys","expandedRowKeys","expandIconColumnIndex","pagination","row-selection","scroll","childrenColumnName"])],2)}}}),Oe=ie(Le,[["__scopeId","data-v-0d3e11b9"]]);export{ke as C,Oe as _,$e as a,G as b,be as c,Be as d,ze as g};
