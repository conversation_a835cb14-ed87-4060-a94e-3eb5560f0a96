<template>
  <div class="guns-body guns-body-card">
    <a-card title="树形选择-自定义填充数据" :bordered="false">
      <TreeSelectComponent
        v-model:value="selectValue"
        :disabled="disabled"
        :readonly="readonly"
        :dataList="dataList"
        :dataSources="2"
        :multiple="false"
        :placeholder="placeholder"
        style="width: 300px"
      />
    </a-card>
    <a-card title="树形选择-动态接口（只支持get）" :bordered="false">
      <TreeSelectComponent
        v-model:value="selectValue1"
        :disabled="disabled"
        :readonly="readonly"
        :dataSources="1"
        backendUrl="/my/org/treeSelectList"
        :multiple="false"
        :placeholder="placeholder"
        style="width: 300px"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 选中的值
const selectValue = ref(null);

const selectValue1 = ref(null);
// 是否禁用
const disabled = ref(false);

// 是否只读
const readonly = ref(false);

// 选择提示
const placeholder = ref('请选择');

const dataList = ref([
  {
    id: '1798280342488690689',
    name: '北京总部',
    code: 'bj',
    parentId: '-1',
    children: [
      {
        id: '1798280342488690711',
        name: '北京总部1部门',
        code: 'bj1',
        parentId: '1798280342488690689',
        children: null,
        nodeId: '1798280342488690711',
        nodeParentId: '1798280342488690689'
      },
      {
        id: '1798280342488690720',
        name: '北京总部2部门',
        code: 'bj2',
        parentId: '1798280342488690689',
        children: null,
        nodeId: '1798280342488690720',
        nodeParentId: '1798280342488690689'
      }
    ],
    nodeId: '1798280342488690689',
    nodeParentId: '-1'
  },
  {
    id: '1798280342488690696',
    name: '上海分公司',
    code: 'sh',
    parentId: '-1',
    children: [
      {
        id: '1798280342488690731',
        name: '上海分公司1部门',
        code: 'sh1',
        parentId: '1798280342488690696',
        children: null,
        nodeId: '1798280342488690731',
        nodeParentId: '1798280342488690696'
      },
      {
        id: '1798280342488690742',
        name: '上海分公司2部门',
        code: 'sh2',
        parentId: '1798280342488690696',
        children: null,
        nodeId: '1798280342488690742',
        nodeParentId: '1798280342488690696'
      }
    ],
    nodeId: '1798280342488690696',
    nodeParentId: '-1'
  },
  {
    id: '1798280342488690704',
    name: '广州分公司',
    code: 'gz',
    parentId: '-1',
    children: [
      {
        id: '1798280342488690756',
        name: '广州分公司1部门',
        code: 'gz1',
        parentId: '1798280342488690704',
        children: null,
        nodeId: '1798280342488690756',
        nodeParentId: '1798280342488690704'
      },
      {
        id: '1798280342488690769',
        name: '广州分公司2部门',
        code: 'gz2',
        parentId: '1798280342488690704',
        children: null,
        nodeId: '1798280342488690769',
        nodeParentId: '1798280342488690704'
      }
    ],
    nodeId: '1798280342488690704',
    nodeParentId: '-1'
  }
]);
</script>

<style></style>
