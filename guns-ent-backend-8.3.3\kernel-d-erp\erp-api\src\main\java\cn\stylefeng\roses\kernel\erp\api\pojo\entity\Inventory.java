package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;

/**
 * 库存实体类
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@TableName(value = "erp_inventory", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class Inventory extends BaseEntity {

    /**
     * 库存ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ChineseDescription("库存ID")
    private Long id;

    /**
     * 商品ID
     */
    @TableField("product_id")
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 当前库存
     */
    @TableField("current_stock")
    @ChineseDescription("当前库存")
    private BigDecimal currentStock;

    /**
     * 最小库存（预警值）
     */
    @TableField("min_stock")
    @ChineseDescription("最小库存")
    private BigDecimal minStock;

    /**
     * 最大库存
     */
    @TableField("max_stock")
    @ChineseDescription("最大库存")
    private BigDecimal maxStock;

    /**
     * 平均成本
     */
    @TableField("avg_cost")
    @ChineseDescription("平均成本")
    private BigDecimal avgCost;

    /**
     * 库存总价值
     */
    @TableField("total_value")
    @ChineseDescription("库存总价值")
    private BigDecimal totalValue;

    /**
     * 最后更新时间
     */
    @TableField("update_time")
    @ChineseDescription("最后更新时间")
    private Date updateTime;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 关联的商品信息（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的商品信息")
    private ErpProduct product;

    /**
     * 关联的供应商信息（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的供应商信息")
    private ErpSupplier supplier;

    /**
     * 是否库存预警（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("是否库存预警")
    private Boolean isWarning;

}