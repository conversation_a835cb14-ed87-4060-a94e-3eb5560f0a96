package cn.stylefeng.roses.kernel.erp.modular.pos.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosSuspendedOrder;

import java.util.List;

/**
 * POS挂单管理服务接口
 *
 * <AUTHOR>
 * @since 2025/08/01 11:30
 */
public interface PosSuspendService {

    /**
     * 挂起当前订单
     *
     * @param cashierId 收银员ID
     * @param orderData 订单数据（包含购物车信息、会员信息等）
     * @param expireHours 过期小时数（默认24小时）
     * @return 挂单ID
     */
    Long suspendOrder(Long cashierId, SuspendOrderData orderData, Integer expireHours);

    /**
     * 恢复挂起的订单
     *
     * @param suspendId 挂单ID
     * @return 订单数据
     */
    SuspendOrderData resumeOrder(Long suspendId);

    /**
     * 删除挂单
     *
     * @param suspendId 挂单ID
     */
    void deleteSuspendedOrder(Long suspendId);

    /**
     * 根据收银员ID查询挂单列表
     *
     * @param cashierId 收银员ID
     * @return 挂单列表
     */
    List<PosSuspendedOrder> getSuspendedOrdersByCashier(Long cashierId);

    /**
     * 查询所有有效的挂单
     *
     * @return 有效挂单列表
     */
    List<PosSuspendedOrder> getActiveSuspendedOrders();

    /**
     * 分页查询挂单列表
     *
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param cashierId 收银员ID（可选）
     * @param status 状态（可选）
     * @return 挂单分页结果
     */
    PageResult<PosSuspendedOrder> findSuspendedOrderPage(Integer pageNo, Integer pageSize,
                                                        Long cashierId, String status);

    /**
     * 根据挂单ID查询挂单详情
     *
     * @param suspendId 挂单ID
     * @return 挂单详情
     */
    PosSuspendedOrder getSuspendedOrderById(Long suspendId);

    /**
     * 根据挂单号查询挂单详情
     *
     * @param suspendNo 挂单号
     * @return 挂单详情
     */
    PosSuspendedOrder getSuspendedOrderByNo(String suspendNo);

    /**
     * 清理过期的挂单
     *
     * @return 清理的挂单数量
     */
    int cleanExpiredSuspendedOrders();

    /**
     * 生成挂单号
     *
     * @return 挂单号
     */
    String generateSuspendNo();

    /**
     * 校验挂单是否可以恢复
     *
     * @param suspendId 挂单ID
     * @return 是否可以恢复
     */
    boolean validateCanResume(Long suspendId);

    /**
     * 校验挂单是否已过期
     *
     * @param suspendId 挂单ID
     * @return 是否已过期
     */
    boolean isExpired(Long suspendId);

    /**
     * 更新挂单状态
     *
     * @param suspendId 挂单ID
     * @param status 新状态
     */
    void updateSuspendedOrderStatus(Long suspendId, String status);

    /**
     * 统计收银员的挂单数量
     *
     * @param cashierId 收银员ID
     * @return 挂单数量统计
     */
    SuspendOrderStats getSuspendOrderStatsByCashier(Long cashierId);

    /**
     * 挂单数据内部类
     */
    class SuspendOrderData {
        private List<PosOrderItem> orderItems;
        private Long memberId;
        private String memberName;
        private java.math.BigDecimal totalAmount;
        private java.math.BigDecimal discountAmount;
        private java.math.BigDecimal finalAmount;
        private String remark;
        private java.util.Map<String, Object> extraData;

        // 构造函数
        public SuspendOrderData() {
        }

        public SuspendOrderData(List<PosOrderItem> orderItems, java.math.BigDecimal totalAmount) {
            this.orderItems = orderItems;
            this.totalAmount = totalAmount;
        }

        // Getters and Setters
        public List<PosOrderItem> getOrderItems() {
            return orderItems;
        }

        public void setOrderItems(List<PosOrderItem> orderItems) {
            this.orderItems = orderItems;
        }

        public Long getMemberId() {
            return memberId;
        }

        public void setMemberId(Long memberId) {
            this.memberId = memberId;
        }

        public String getMemberName() {
            return memberName;
        }

        public void setMemberName(String memberName) {
            this.memberName = memberName;
        }

        public java.math.BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(java.math.BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

        public java.math.BigDecimal getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(java.math.BigDecimal discountAmount) {
            this.discountAmount = discountAmount;
        }

        public java.math.BigDecimal getFinalAmount() {
            return finalAmount;
        }

        public void setFinalAmount(java.math.BigDecimal finalAmount) {
            this.finalAmount = finalAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public java.util.Map<String, Object> getExtraData() {
            return extraData;
        }

        public void setExtraData(java.util.Map<String, Object> extraData) {
            this.extraData = extraData;
        }
    }

    /**
     * 挂单统计数据内部类
     */
    class SuspendOrderStats {
        private Integer activeCount;
        private Integer resumedCount;
        private Integer expiredCount;
        private Integer totalCount;

        // 构造函数
        public SuspendOrderStats() {
        }

        public SuspendOrderStats(Integer activeCount, Integer resumedCount, Integer expiredCount, Integer totalCount) {
            this.activeCount = activeCount;
            this.resumedCount = resumedCount;
            this.expiredCount = expiredCount;
            this.totalCount = totalCount;
        }

        // Getters and Setters
        public Integer getActiveCount() {
            return activeCount;
        }

        public void setActiveCount(Integer activeCount) {
            this.activeCount = activeCount;
        }

        public Integer getResumedCount() {
            return resumedCount;
        }

        public void setResumedCount(Integer resumedCount) {
            this.resumedCount = resumedCount;
        }

        public Integer getExpiredCount() {
            return expiredCount;
        }

        public void setExpiredCount(Integer expiredCount) {
            this.expiredCount = expiredCount;
        }

        public Integer getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Integer totalCount) {
            this.totalCount = totalCount;
        }
    }

}