package cn.stylefeng.roses.kernel.micro.core.context;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.micro.api.constants.MicroConstants;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 获取当前请求的请求号，没有请求号则生成空串
 *
 * <AUTHOR>
 * @date 2018-05-09-下午6:25
 */
@Slf4j
public class RequestNoContext {

    public static String getRequestNoByHttpHeader() {
        HttpServletRequest request = HttpServletUtil.getRequest();

        if (request == null) {
            if (log.isDebugEnabled()) {
                log.info("获取请求号失败，当前不是http请求环境！");
            }
            return "";
        } else {
            String requestNo = request.getHeader(MicroConstants.REQUEST_NO_HEADER_NAME);
            if (StrUtil.isEmpty(requestNo)) {
                return "";
            } else {
                return requestNo;
            }
        }
    }

    /**
     * 通过请求参数获取requestNo，参数必须是AbstractBaseRequest的子类
     */
    public static String getRequestNoByRequestParam(Object[] params) {

        if (params == null || params.length <= 0) {
            return "";
        } else {
            for (Object paramItem : params) {
                if (paramItem instanceof BaseRequest) {
                    BaseRequest baseRequest = (BaseRequest) paramItem;
                    return baseRequest.getRequestNo();
                }
            }
            return "";
        }
    }

}
