import{_,b2 as i,bR as d,r as t,o as f,a as m,f as p,w as s,b,d as l,bS as g,bT as h,aF as y,bU as x,bV as v}from"./index-18a1ea24.js";const w=["src"],L={__name:"index",setup(T){const{push:n}=i();let r=d();const o=t(""),a=t("Guns Tech."),c=e=>{e||n(v)};return f(async()=>{let e=await r.loadThemeInfo();o.value=e.gunsMgrLogo,a.value=e.gunsMgrName}),(e,S)=>(m(),p(x,{"show-collapse":!1,"show-refresh":!1,"fixed-sidebar":!1,"show-breadcrumb":!1,collapse:!0,onLogoClick:c,"project-name":a.value,"layout-style":"top","tab-context-menu":!1},{logo:s(()=>[b("img",{src:o.value,alt:"logo"},null,8,w)]),right:s(u=>[l(g,{"is-mobile":u.isMobile},null,8,["is-mobile"])]),footer:s(()=>[l(h)]),default:s(()=>[y(e.$slots,"default",{},void 0,!0)]),_:3},8,["project-name"]))}},H=_(L,[["__scopeId","data-v-450e97bf"]]);export{H};
