System.register(["./index-legacy-ee1db0c7.js","./formatter-legacy-97503ee9.js","./constants-legacy-2a31d63c.js"],(function(t,a){"use strict";var e,o,n,l,s,c,i,d,u,r,m,p,f,y;return{setters:[t=>{e=t._,o=t.a,n=t.c,l=t.b,s=t.d,c=t.I,i=t.g,d=t.t,u=t.h,r=t.w,m=t.m,p=t.V,f=t.B},t=>{y=t.A},null],execute:function(){var a=document.createElement("style");a.textContent=".cart-summary[data-v-9e23e82d]{padding:16px;background:#fff}.amount-details[data-v-9e23e82d]{display:flex;flex-direction:column;gap:8px}.amount-row[data-v-9e23e82d]{display:flex;align-items:center;justify-content:space-between;font-size:13px}.amount-label[data-v-9e23e82d]{display:flex;align-items:center;gap:6px;color:#595959}.amount-value[data-v-9e23e82d]{color:#262626;font-weight:500}.discount-row .amount-label[data-v-9e23e82d]{color:#52c41a}.discount-value[data-v-9e23e82d]{color:#52c41a!important}.total-amount[data-v-9e23e82d]{margin-top:8px}.total-row[data-v-9e23e82d]{display:flex;align-items:center;justify-content:space-between;font-size:16px;font-weight:600}.total-label[data-v-9e23e82d]{display:flex;align-items:center;gap:8px;color:#262626}.total-value[data-v-9e23e82d]{color:#ff4d4f;font-size:18px}.action-buttons[data-v-9e23e82d]{display:flex;gap:8px;margin-top:16px}.action-buttons .ant-btn[data-v-9e23e82d]{flex:1;height:44px;font-size:14px;font-weight:500}\n",document.head.appendChild(a);const g={class:"cart-summary"},v={class:"amount-details"},x={class:"amount-row"},b={class:"amount-label"},h={class:"amount-value"},w={key:0,class:"amount-row discount-row"},C={class:"amount-label"},j={class:"amount-value discount-value"},k={class:"total-amount"},_={class:"total-row"},z={class:"total-label"},A={class:"total-value"},B={class:"action-buttons"},O=Object.assign({name:"CartSummary"},{__name:"CartSummary",props:{summary:{type:Object,required:!0},total:{type:Object,required:!0},canCheckout:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["checkout"],setup(t,{emit:a}){const e=t,O=a,S=t=>y.formatCurrency(t||0),q=()=>{e.canCheckout?O("checkout"):m.warning("购物车为空或商品信息有误，无法结账")};return(a,e)=>{const m=p,y=f;return o(),n("div",g,[l("div",v,[l("div",x,[l("span",b,[s(c,{iconClass:"icon-shopping"}),i(" 商品总计（"+d(t.summary.itemCount)+"件） ",1)]),l("span",h,d(S(t.total.totalAmount)),1)]),t.total.discountAmount>0?(o(),n("div",w,[l("span",C,[s(c,{iconClass:"icon-discount"}),e[0]||(e[0]=i(" 优惠折扣 "))]),l("span",j,"-"+d(S(t.total.discountAmount)),1)])):u("",!0)]),s(m,{style:{margin:"12px 0"}}),l("div",k,[l("div",_,[l("span",z,[s(c,{iconClass:"icon-money"}),e[1]||(e[1]=i(" 应付总额 "))]),l("span",A,d(S(t.total.payableAmount)),1)])]),l("div",B,[s(y,{size:"large",type:"primary",loading:t.loading,disabled:!t.canCheckout,onClick:q},{icon:r((()=>[s(c,{iconClass:"icon-pay"})])),default:r((()=>[e[2]||(e[2]=i(" 立即结账 "))])),_:1,__:[2]},8,["loading","disabled"])])])}}});t("default",e(O,[["__scopeId","data-v-9e23e82d"]]))}}}));
