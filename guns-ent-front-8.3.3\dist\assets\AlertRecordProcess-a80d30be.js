import{_ as w,aj as G,ak as H,r as A,s as Y,X as j,k as I,a as u,f as M,w as a,b as _,d as o,g as n,t as v,ah as F,c as D,h as p,F as L,m as N,Y as B,U as K,Z as W,a0 as z,z as X,A as Z,u as q,$ as J,C as Q,ab as $,l as ee,al as oe,W as te,J as ae,H as le,M as re}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{I as ne}from"./InventoryAlertRecordApi-19b95203.js";const se={name:"AlertRecordProcess",components:{CheckCircleOutlined:G,MinusCircleOutlined:H},props:{visible:{type:Boolean,default:!1},recordData:{type:Object,default:()=>({})}},emits:["update:visible","success","cancel"],setup(y,{emit:e}){const m=A(),t=A(!1),r=Y({handleType:"RESOLVE",handleRemark:"",resolveMethod:[],otherMethodDesc:"",expectedCompleteTime:null,ignoreReason:"",otherReasonDesc:"",recheckTime:null,notifySettings:["SYSTEM"]}),S=Y({handleType:[{required:!0,message:"\u8BF7\u9009\u62E9\u5904\u7406\u65B9\u5F0F",trigger:"change"}],handleRemark:[{required:!0,message:"\u8BF7\u8F93\u5165\u5904\u7406\u5907\u6CE8",trigger:"blur"},{min:10,message:"\u5904\u7406\u5907\u6CE8\u81F3\u5C1110\u4E2A\u5B57\u7B26",trigger:"blur"}],resolveMethod:[{validator:(l,s)=>r.handleType==="RESOLVE"&&(!s||s.length===0)?Promise.reject("\u8BF7\u9009\u62E9\u89E3\u51B3\u65B9\u5F0F"):Promise.resolve(),trigger:"change"}],otherMethodDesc:[{validator:(l,s)=>r.handleType==="RESOLVE"&&r.resolveMethod&&r.resolveMethod.includes("OTHER")&&!s?Promise.reject("\u8BF7\u8BF4\u660E\u5176\u4ED6\u89E3\u51B3\u65B9\u5F0F"):Promise.resolve(),trigger:"blur"}],ignoreReason:[{validator:(l,s)=>r.handleType==="IGNORE"&&!s?Promise.reject("\u8BF7\u9009\u62E9\u5FFD\u7565\u539F\u56E0"):Promise.resolve(),trigger:"change"}],otherReasonDesc:[{validator:(l,s)=>r.handleType==="IGNORE"&&r.ignoreReason==="OTHER"&&!s?Promise.reject("\u8BF7\u8BF4\u660E\u5176\u4ED6\u5FFD\u7565\u539F\u56E0"):Promise.resolve(),trigger:"blur"}]}),c=async()=>{var l,s;try{await m.value.validate(),t.value=!0;const d={id:y.recordData.id,handleType:r.handleType,handleRemark:r.handleRemark,notifySettings:r.notifySettings};r.handleType==="RESOLVE"?(d.resolveMethod=r.resolveMethod,d.otherMethodDesc=r.otherMethodDesc,d.expectedCompleteTime=(l=r.expectedCompleteTime)==null?void 0:l.format("YYYY-MM-DD")):r.handleType==="IGNORE"&&(d.ignoreReason=r.ignoreReason,d.otherReasonDesc=r.otherReasonDesc,d.recheckTime=(s=r.recheckTime)==null?void 0:s.format("YYYY-MM-DD")),await ne.handle(d),N.success("\u5904\u7406\u6210\u529F"),e("success")}catch(d){console.error("\u5904\u7406\u5931\u8D25:",d),N.error("\u5904\u7406\u5931\u8D25")}finally{t.value=!1}},g=()=>{e("cancel")},C=()=>{r.resolveMethod=[],r.otherMethodDesc="",r.expectedCompleteTime=null,r.ignoreReason="",r.otherReasonDesc="",r.recheckTime=null},O=l=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"})[l]||"default",R=l=>({LOW_STOCK:"\u5E93\u5B58\u4E0D\u8DB3",ZERO_STOCK:"\u96F6\u5E93\u5B58",OVERSTOCK:"\u5E93\u5B58\u79EF\u538B",EXPIRY:"\u4E34\u671F\u9884\u8B66"})[l]||l,h=l=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"})[l]||"default",T=l=>({CRITICAL:"\u7D27\u6025",WARNING:"\u8B66\u544A",INFO:"\u63D0\u9192"})[l]||l,E=()=>{Object.assign(r,{handleType:"RESOLVE",handleRemark:"",resolveMethod:[],otherMethodDesc:"",expectedCompleteTime:null,ignoreReason:"",otherReasonDesc:"",recheckTime:null,notifySettings:["SYSTEM"]})};return j(()=>y.visible,l=>{l&&E()}),{formRef:m,confirmLoading:t,formData:r,rules:S,handleSubmit:c,handleCancel:g,handleTypeChange:C,getAlertTypeColor:O,getAlertTypeText:R,getAlertLevelColor:h,getAlertLevelText:T}}},de={class:"process-container"},ie={class:"alert-message"},me={style:{color:"#52c41a"}},ce={style:{color:"#faad14"}},ue={class:"handle-type-desc"},_e={key:0,class:"desc-text"},fe={key:1,class:"desc-text"};function ve(y,e,m,t,r,S){const c=B,g=K,C=W,O=z,R=I("CheckCircleOutlined"),h=X,T=I("MinusCircleOutlined"),E=Z,l=q,s=J,d=Q,k=$,b=ee,x=oe,f=te,P=ae,V=le,U=re;return u(),M(U,{title:"\u5904\u7406\u9884\u8B66\u8BB0\u5F55",visible:m.visible,width:600,"confirm-loading":t.confirmLoading,onOk:t.handleSubmit,onCancel:t.handleCancel},{default:a(()=>[_("div",de,[o(O,{title:"\u9884\u8B66\u4FE1\u606F",size:"small",class:"alert-info-card"},{default:a(()=>[o(C,{column:2,size:"small"},{default:a(()=>[o(c,{label:"\u5546\u54C1\u540D\u79F0"},{default:a(()=>[n(v(m.recordData.productName),1)]),_:1}),o(c,{label:"\u9884\u8B66\u7C7B\u578B"},{default:a(()=>[o(g,{color:t.getAlertTypeColor(m.recordData.alertType)},{default:a(()=>[n(v(t.getAlertTypeText(m.recordData.alertType)),1)]),_:1},8,["color"])]),_:1}),o(c,{label:"\u9884\u8B66\u7EA7\u522B"},{default:a(()=>[o(g,{color:t.getAlertLevelColor(m.recordData.alertLevel)},{default:a(()=>[n(v(t.getAlertLevelText(m.recordData.alertLevel)),1)]),_:1},8,["color"])]),_:1}),o(c,{label:"\u5F53\u524D\u5E93\u5B58"},{default:a(()=>[_("span",{style:F({color:m.recordData.currentStock<=m.recordData.thresholdValue?"#f5222d":"#52c41a",fontWeight:"bold"})},v(m.recordData.currentStock),5)]),_:1}),o(c,{label:"\u9884\u8B66\u6D88\u606F",span:2},{default:a(()=>[_("div",ie,v(m.recordData.alertMessage),1)]),_:1})]),_:1})]),_:1}),o(V,{ref:"formRef",model:t.formData,rules:t.rules,"label-col":{span:6},"wrapper-col":{span:16},class:"process-form"},{default:a(()=>[o(l,{label:"\u5904\u7406\u65B9\u5F0F",name:"handleType"},{default:a(()=>[o(E,{value:t.formData.handleType,"onUpdate:value":e[0]||(e[0]=i=>t.formData.handleType=i),onChange:t.handleTypeChange},{default:a(()=>[o(h,{value:"RESOLVE"},{default:a(()=>[_("span",me,[o(R),e[9]||(e[9]=n(" \u89E3\u51B3 "))])]),_:1}),o(h,{value:"IGNORE"},{default:a(()=>[_("span",ce,[o(T),e[10]||(e[10]=n(" \u5FFD\u7565 "))])]),_:1})]),_:1},8,["value","onChange"]),_("div",ue,[t.formData.handleType==="RESOLVE"?(u(),D("div",_e,[o(R,{style:{color:"#52c41a"}}),e[11]||(e[11]=n(" \u6807\u8BB0\u4E3A\u5DF2\u89E3\u51B3\uFF0C\u8868\u793A\u5DF2\u91C7\u53D6\u63AA\u65BD\u89E3\u51B3\u4E86\u5E93\u5B58\u95EE\u9898 "))])):t.formData.handleType==="IGNORE"?(u(),D("div",fe,[o(T,{style:{color:"#faad14"}}),e[12]||(e[12]=n(" \u6807\u8BB0\u4E3A\u5DF2\u5FFD\u7565\uFF0C\u8868\u793A\u6682\u65F6\u4E0D\u5904\u7406\u6B64\u9884\u8B66 "))])):p("",!0)])]),_:1}),o(l,{label:"\u5904\u7406\u5907\u6CE8",name:"handleRemark"},{default:a(()=>[o(s,{value:t.formData.handleRemark,"onUpdate:value":e[1]||(e[1]=i=>t.formData.handleRemark=i),placeholder:"\u8BF7\u8F93\u5165\u5904\u7406\u5907\u6CE8\uFF0C\u8BF4\u660E\u5177\u4F53\u7684\u5904\u7406\u63AA\u65BD\u6216\u5FFD\u7565\u539F\u56E0",rows:4,maxlength:500,"show-count":""},null,8,["value"])]),_:1}),t.formData.handleType==="RESOLVE"?(u(),D(L,{key:0},[o(l,{label:"\u89E3\u51B3\u65B9\u5F0F",name:"resolveMethod"},{default:a(()=>[o(k,{value:t.formData.resolveMethod,"onUpdate:value":e[2]||(e[2]=i=>t.formData.resolveMethod=i)},{default:a(()=>[o(d,{value:"PURCHASE"},{default:a(()=>e[13]||(e[13]=[n("\u91C7\u8D2D\u8865\u8D27")])),_:1,__:[13]}),o(d,{value:"TRANSFER"},{default:a(()=>e[14]||(e[14]=[n("\u8C03\u62E8\u5E93\u5B58")])),_:1,__:[14]}),o(d,{value:"ADJUST"},{default:a(()=>e[15]||(e[15]=[n("\u5E93\u5B58\u8C03\u6574")])),_:1,__:[15]}),o(d,{value:"OTHER"},{default:a(()=>e[16]||(e[16]=[n("\u5176\u4ED6\u65B9\u5F0F")])),_:1,__:[16]})]),_:1},8,["value"])]),_:1}),t.formData.resolveMethod&&t.formData.resolveMethod.includes("OTHER")?(u(),M(l,{key:0,label:"\u5176\u4ED6\u65B9\u5F0F\u8BF4\u660E",name:"otherMethodDesc"},{default:a(()=>[o(b,{value:t.formData.otherMethodDesc,"onUpdate:value":e[3]||(e[3]=i=>t.formData.otherMethodDesc=i),placeholder:"\u8BF7\u8BF4\u660E\u5176\u4ED6\u89E3\u51B3\u65B9\u5F0F",maxlength:200},null,8,["value"])]),_:1})):p("",!0),o(l,{label:"\u9884\u8BA1\u5B8C\u6210\u65F6\u95F4",name:"expectedCompleteTime"},{default:a(()=>[o(x,{value:t.formData.expectedCompleteTime,"onUpdate:value":e[4]||(e[4]=i=>t.formData.expectedCompleteTime=i),format:"YYYY-MM-DD",placeholder:"\u8BF7\u9009\u62E9\u9884\u8BA1\u5B8C\u6210\u65F6\u95F4",style:{width:"100%"}},null,8,["value"])]),_:1})],64)):p("",!0),t.formData.handleType==="IGNORE"?(u(),D(L,{key:1},[o(l,{label:"\u5FFD\u7565\u539F\u56E0",name:"ignoreReason"},{default:a(()=>[o(P,{value:t.formData.ignoreReason,"onUpdate:value":e[5]||(e[5]=i=>t.formData.ignoreReason=i),placeholder:"\u8BF7\u9009\u62E9\u5FFD\u7565\u539F\u56E0"},{default:a(()=>[o(f,{value:"TEMPORARY"},{default:a(()=>e[17]||(e[17]=[n("\u4E34\u65F6\u6027\u95EE\u9898")])),_:1,__:[17]}),o(f,{value:"ACCEPTABLE"},{default:a(()=>e[18]||(e[18]=[n("\u53EF\u63A5\u53D7\u8303\u56F4")])),_:1,__:[18]}),o(f,{value:"PROCESSING"},{default:a(()=>e[19]||(e[19]=[n("\u6B63\u5728\u5904\u7406\u4E2D")])),_:1,__:[19]}),o(f,{value:"SYSTEM_ERROR"},{default:a(()=>e[20]||(e[20]=[n("\u7CFB\u7EDF\u9519\u8BEF")])),_:1,__:[20]}),o(f,{value:"OTHER"},{default:a(()=>e[21]||(e[21]=[n("\u5176\u4ED6\u539F\u56E0")])),_:1,__:[21]})]),_:1},8,["value"])]),_:1}),t.formData.ignoreReason==="OTHER"?(u(),M(l,{key:0,label:"\u5176\u4ED6\u539F\u56E0\u8BF4\u660E",name:"otherReasonDesc"},{default:a(()=>[o(b,{value:t.formData.otherReasonDesc,"onUpdate:value":e[6]||(e[6]=i=>t.formData.otherReasonDesc=i),placeholder:"\u8BF7\u8BF4\u660E\u5176\u4ED6\u5FFD\u7565\u539F\u56E0",maxlength:200},null,8,["value"])]),_:1})):p("",!0),o(l,{label:"\u91CD\u65B0\u68C0\u67E5\u65F6\u95F4",name:"recheckTime"},{default:a(()=>[o(x,{value:t.formData.recheckTime,"onUpdate:value":e[7]||(e[7]=i=>t.formData.recheckTime=i),format:"YYYY-MM-DD",placeholder:"\u8BF7\u9009\u62E9\u91CD\u65B0\u68C0\u67E5\u65F6\u95F4\uFF08\u53EF\u9009\uFF09",style:{width:"100%"}},null,8,["value"])]),_:1})],64)):p("",!0),o(l,{label:"\u901A\u77E5\u8BBE\u7F6E",name:"notifySettings"},{default:a(()=>[o(k,{value:t.formData.notifySettings,"onUpdate:value":e[8]||(e[8]=i=>t.formData.notifySettings=i)},{default:a(()=>[o(d,{value:"EMAIL"},{default:a(()=>e[22]||(e[22]=[n("\u90AE\u4EF6\u901A\u77E5")])),_:1,__:[22]}),o(d,{value:"SMS"},{default:a(()=>e[23]||(e[23]=[n("\u77ED\u4FE1\u901A\u77E5")])),_:1,__:[23]}),o(d,{value:"SYSTEM"},{default:a(()=>e[24]||(e[24]=[n("\u7CFB\u7EDF\u6D88\u606F")])),_:1,__:[24]})]),_:1},8,["value"]),e[25]||(e[25]=_("div",{class:"notify-desc"}," \u9009\u62E9\u5904\u7406\u5B8C\u6210\u540E\u7684\u901A\u77E5\u65B9\u5F0F ",-1))]),_:1,__:[25]})]),_:1},8,["model","rules"])])]),_:1},8,["visible","confirm-loading","onOk","onCancel"])}const Ce=w(se,[["render",ve],["__scopeId","data-v-d96975f3"]]);export{Ce as default};
