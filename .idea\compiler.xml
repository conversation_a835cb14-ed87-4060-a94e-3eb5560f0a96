<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="guns" />
        <module name="wrapper-api" />
        <module name="file-sdk-local" />
        <module name="system-spring-boot-starter" />
        <module name="conversion-api" />
        <module name="business-service" />
        <module name="dict-city-business" />
        <module name="sanyuan-business" />
        <module name="db-sdk-mp" />
        <module name="security-sdk-request-encrypt-and-decode" />
        <module name="license-sdk" />
        <module name="security-api" />
        <module name="ca-server-api" />
        <module name="api-auth-api" />
        <module name="temp-secret-api" />
        <module name="saas-spring-boot-starter" />
        <module name="security-spring-boot-starter" />
        <module name="group-spring-boot-starter" />
        <module name="security-sdk-guomi" />
        <module name="email-sdk-java" />
        <module name="monitor-business-system-info" />
        <module name="micro-project-tran-message" />
        <module name="i18n-sdk" />
        <module name="erp-api" />
        <module name="i18n-spring-boot-starter" />
        <module name="timer-spring-boot-starter" />
        <module name="timer-business" />
        <module name="monitor-api" />
        <module name="license-api" />
        <module name="email-api" />
        <module name="auth-api" />
        <module name="micro-project-gateway" />
        <module name="sanyuan-api" />
        <module name="sms-sdk-aliyun" />
        <module name="config-spring-boot-starter" />
        <module name="websocket-api" />
        <module name="system-api" />
        <module name="demo-business" />
        <module name="demo-spring-boot-starter" />
        <module name="group-api" />
        <module name="sms-business-validation" />
        <module name="conversion-business" />
        <module name="sms-spring-boot-starter" />
        <module name="event-sdk" />
        <module name="groovy-api" />
        <module name="dict-api" />
        <module name="erp-business" />
        <module name="db-api" />
        <module name="timer-sdk-hutool" />
        <module name="stat-api" />
        <module name="config-sdk-redis" />
        <module name="log-api" />
        <module name="cache-spring-boot-starter-redis" />
        <module name="office-api" />
        <module name="file-api" />
        <module name="groovy-spring-boot-starter" />
        <module name="wrapper-spring-boot-starter" />
        <module name="security-sdk-black-white" />
        <module name="oauth2-spring-boot-starter" />
        <module name="websocket-sdk-memory" />
        <module name="erp-spring-boot-starter" />
        <module name="validator-api" />
        <module name="pay-sdk" />
        <module name="websocket-sdk-redis" />
        <module name="storage-service" />
        <module name="groovy-sdk" />
        <module name="temp-secret-spring-boot-starter" />
        <module name="cache-spring-boot-starter-memory" />
        <module name="micro-spring-boot-starter" />
        <module name="sms-api" />
        <module name="config-api" />
        <module name="ca-server-spring-boot-starter" />
        <module name="sanyuan-spring-boot-starter" />
        <module name="log-business" />
        <module name="ds-container-spring-boot-starter" />
        <module name="db-spring-boot-starter" />
        <module name="api-auth-spring-boot-starter" />
        <module name="ds-container-sdk" />
        <module name="log-business-requestapi" />
        <module name="pay-business" />
        <module name="dict-business" />
        <module name="timer-api" />
        <module name="pay-api" />
        <module name="security-sdk-allow-cors" />
        <module name="micro-project-feign-consumer" />
        <module name="log-business-login-log" />
        <module name="file-sdk-minio" />
        <module name="dict-spring-boot-starter" />
        <module name="office-sdk-excel" />
        <module name="i18n-business" />
        <module name="saas-business" />
        <module name="monitor-spring-boot-starter" />
        <module name="system-business-permission" />
        <module name="kernel-a-rule" />
        <module name="sharding-spring-boot-starter" />
        <module name="cache-sdk-redis" />
        <module name="system-business-portal" />
        <module name="auth-spring-boot-starter" />
        <module name="websocket-spring-boot-starter" />
        <module name="oauth2-api" />
        <module name="saas-api" />
        <module name="kernel-s-user-expand" />
        <module name="account-service" />
        <module name="user-favorite-business" />
        <module name="cache-api" />
        <module name="group-business" />
        <module name="ds-container-api" />
        <module name="sms-sdk-tencent" />
        <module name="log-spring-boot-starter" />
        <module name="oauth2-business" />
        <module name="event-api" />
        <module name="file-spring-boot-starter" />
        <module name="log-business-security" />
        <module name="file-sdk-aliyun" />
        <module name="micro-project-feign-provider" />
        <module name="security-sdk-clear-threadlocal" />
        <module name="event-spring-boot-starter" />
        <module name="wrapper-field-sdk" />
        <module name="order-service" />
        <module name="file-sdk-qingyun" />
        <module name="pinyin-spring-boot-starter" />
        <module name="scanner-api" />
        <module name="micro-sdk-loadbalancer" />
        <module name="scanner-sdk-scanner" />
        <module name="ds-container-business" />
        <module name="file-business" />
        <module name="security-sdk-captcha" />
        <module name="kernel-s-mobile" />
        <module name="validator-spring-boot-starter" />
        <module name="ca-server-business" />
        <module name="jwt-api" />
        <module name="system-business-hr" />
        <module name="pay-spring-boot-starter" />
        <module name="i18n-api" />
        <module name="sharding-api" />
        <module name="demo-api" />
        <module name="validator-api-table-unique" />
        <module name="scanner-spring-boot-starter" />
        <module name="micro-api" />
        <module name="db-sdk-flyway" />
        <module name="micro-sdk-core" />
        <module name="auth-sdk" />
        <module name="security-sdk-xss" />
        <module name="pinyin-api" />
        <module name="temp-secret-business" />
        <module name="email-sdk-aliyun" />
        <module name="wrapper-sdk" />
        <module name="jwt-sdk" />
        <module name="user-favorite-api" />
        <module name="stat-business" />
        <module name="config-sdk-map" />
        <module name="api-auth-business" />
        <module name="security-sdk-count" />
        <module name="config-business" />
        <module name="pinyin-sdk-pinyin4j" />
        <module name="cache-sdk-memory" />
        <module name="stat-spring-boot-starter" />
        <module name="micro-project-monitor-server" />
        <module name="kernel-d-tree" />
        <module name="user-favorite-spring-boot-starter" />
        <module name="email-spring-boot-starter" />
        <module name="file-sdk-tencent" />
      </profile>
    </annotationProcessing>
  </component>
</project>