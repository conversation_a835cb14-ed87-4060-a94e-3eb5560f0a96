package cn.stylefeng.roses.kernel.erp.modular.pos.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem;

import java.util.List;

/**
 * POS订单服务接口
 *
 * <AUTHOR>
 * @since 2025/08/01 10:30
 */
public interface PosOrderService {

    /**
     * 创建POS订单
     *
     * @param posOrder 订单信息
     * @param orderItems 订单项列表
     * @return 创建的订单ID
     */
    Long createOrder(PosOrder posOrder, List<PosOrderItem> orderItems);

    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    PosOrder getOrderById(Long orderId);

    /**
     * 根据订单号查询订单详情
     *
     * @param orderNo 订单号
     * @return 订单详情
     */
    PosOrder getOrderByNo(String orderNo);

    /**
     * 查询订单项列表
     *
     * @param orderId 订单ID
     * @return 订单项列表
     */
    List<PosOrderItem> getOrderItems(Long orderId);

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param orderStatus 订单状态
     */
    void updateOrderStatus(Long orderId, String orderStatus);

    /**
     * 更新支付状态
     *
     * @param orderId 订单ID
     * @param paymentStatus 支付状态
     * @param paymentMethod 支付方式
     */
    void updatePaymentStatus(Long orderId, String paymentStatus, String paymentMethod);

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param reason 取消原因
     */
    void cancelOrder(Long orderId, String reason);

    /**
     * 分页查询订单列表
     *
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param orderStatus 订单状态（可选）
     * @param paymentStatus 支付状态（可选）
     * @param cashierId 收银员ID（可选）
     * @return 订单分页结果
     */
    PageResult<PosOrder> findOrderPage(Integer pageNo, Integer pageSize, 
                                       String orderStatus, String paymentStatus, Long cashierId);

    /**
     * 根据收银员ID查询今日订单列表
     *
     * @param cashierId 收银员ID
     * @return 今日订单列表
     */
    List<PosOrder> getTodayOrdersByCashier(Long cashierId);

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 校验订单是否可以取消
     *
     * @param orderId 订单ID
     * @return 是否可以取消
     */
    boolean validateCanCancel(Long orderId);

    /**
     * 校验订单是否可以退款
     *
     * @param orderId 订单ID
     * @return 是否可以退款
     */
    boolean validateCanRefund(Long orderId);

    /**
     * 添加订单项
     *
     * @param orderId 订单ID
     * @param orderItem 订单项
     */
    void addOrderItem(Long orderId, PosOrderItem orderItem);

    /**
     * 更新订单项数量
     *
     * @param itemId 订单项ID
     * @param quantity 新数量
     */
    void updateOrderItemQuantity(Long itemId, java.math.BigDecimal quantity);

    /**
     * 删除订单项
     *
     * @param itemId 订单项ID
     */
    void removeOrderItem(Long itemId);

    /**
     * 重新计算订单金额
     *
     * @param orderId 订单ID
     */
    void recalculateOrderAmount(Long orderId);

}