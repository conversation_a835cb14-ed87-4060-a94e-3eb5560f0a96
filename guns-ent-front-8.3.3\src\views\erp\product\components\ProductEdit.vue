<template>
  <a-modal
    :title="isUpdate ? '编辑商品' : '新增商品'"
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="商品编码" name="productCode">
            <a-input v-model:value="form.productCode" placeholder="请输入商品编码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="商品名称" name="productName">
            <a-input v-model:value="form.productName" placeholder="请输入商品名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="商品简称" name="productShortName">
            <a-input v-model:value="form.productShortName" placeholder="请输入商品简称" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="条形码" name="barcode">
            <a-input v-model:value="form.barcode" placeholder="请输入条形码" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="品牌" name="brand">
            <a-input v-model:value="form.brand" placeholder="请输入品牌" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="规格" name="specification">
            <a-input v-model:value="form.specification" placeholder="请输入规格" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="基本单位" name="unit">
            <a-select v-model:value="form.unit" placeholder="请选择基本单位" show-search>
              <a-select-option v-for="item in unitOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="重量(kg)" name="weight">
            <a-input-number 
              v-model:value="form.weight" 
              placeholder="请输入重量" 
              :min="0" 
              :precision="3"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="体积(m³)" name="volume">
            <a-input-number 
              v-model:value="form.volume" 
              placeholder="请输入体积" 
              :min="0" 
              :precision="3"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="保质期(天)" name="shelfLife">
            <a-input-number
              v-model:value="form.shelfLife"
              placeholder="请输入保质期"
              :min="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="商品分类" name="categoryId">
            <category-selector v-model:value="form.categoryId" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="供应商" name="supplierId">
            <supplier-selector 
              v-model:value="form.supplierId" 
              :filter="{ businessMode: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
              @change="handleSupplierChange" 
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="计价类型" name="pricingType">
            <a-select v-model:value="form.pricingType" placeholder="请选择计价类型" @change="handlePricingTypeChange">
              <a-select-option v-for="item in pricingTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 价格设置 - 计价类型和价格在同一行 -->
      <a-row :gutter="16" v-if="form.pricingType">
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'NORMAL'">
          <a-form-item label="零售价格" name="retailPrice">
            <a-input-number v-model:value="form.retailPrice" placeholder="请输入零售价格" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'WEIGHT'">
          <a-form-item label="单位价格" name="unitPrice">
            <a-input-number v-model:value="form.unitPrice" placeholder="请输入单位价格(每公斤)" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'PIECE'">
          <a-form-item label="单份价格" name="piecePrice">
            <a-input-number v-model:value="form.piecePrice" placeholder="请输入单份价格" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'VARIABLE'">
          <a-form-item label="参考价格" name="referencePrice">
            <a-input-number v-model:value="form.referencePrice" placeholder="请输入参考价格(可选)" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="存储条件" name="storageCondition" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.storageCondition" placeholder="请输入存储条件" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { ProductApi } from '../api/ProductApi';
import CategorySelector from './category/CategorySelector.vue';
import SupplierSelector from '@/components/erp/SupplierSelector.vue';

export default {
  name: 'ProductEdit',
  components: {
    CategorySelector,
    SupplierSelector
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['update:visible', 'done'],
  setup(props, { emit }) {
    // 表单数据
    const form = reactive({});
    const formRef = ref(null);
    const loading = ref(false);

    // 是否是修改
    const isUpdate = ref(false);

    // 选项数据
    const statusOptions = ProductApi.getProductStatusOptions();
    const unitOptions = ProductApi.getCommonUnitOptions();
    const pricingTypeOptions = ProductApi.getPricingTypeOptions();

    // 表单验证规则
    const rules = reactive({
      productCode: [
        { required: true, message: '请输入商品编码', trigger: 'blur' },
        { max: 50, message: '商品编码不能超过50个字符', trigger: 'blur' }
      ],
      productName: [
        { required: true, message: '请输入商品名称', trigger: 'blur' },
        { max: 200, message: '商品名称不能超过200个字符', trigger: 'blur' }
      ],
      unit: [
        { required: true, message: '请选择基本单位', trigger: 'change' }
      ],
      // 商品分类为必填
      categoryId: [
        { required: true, message: '请选择商品分类', trigger: 'change' }
      ],
      weight: [
        { type: 'number', min: 0, message: '重量不能小于0', trigger: 'blur' }
      ],
      volume: [
        { type: 'number', min: 0, message: '体积不能小于0', trigger: 'blur' }
      ],
      shelfLife: [
        { type: 'number', min: 0, message: '保质期不能小于0', trigger: 'blur' }
      ],
      supplierId: [
        { required: true, message: '请选择供应商', trigger: 'change' }
      ],
      pricingType: [
        { required: true, message: '请选择计价类型', trigger: 'change' }
      ]
    });

    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 处理供应商变更
    const handleSupplierChange = (supplierId, supplier) => {
      console.log('供应商变更:', supplierId, supplier);
    };

    // 处理计价类型变更
    const handlePricingTypeChange = (value) => {
      // 清空之前的价格数据
      form.retailPrice = undefined;
      form.unitPrice = undefined;
      form.piecePrice = undefined;
      form.referencePrice = undefined;
      
      // 动态更新验证规则
      updatePriceValidationRules(value);
    };

    // 动态更新价格验证规则
    const updatePriceValidationRules = (pricingType) => {
      // 清除之前的价格验证规则
      delete rules.retailPrice;
      delete rules.unitPrice;
      delete rules.piecePrice;
      delete rules.referencePrice;
      
      // 根据计价类型添加相应的验证规则
      switch (pricingType) {
        case 'NORMAL':
          rules.retailPrice = [
            { required: true, message: '请输入零售价格', trigger: 'blur', type: 'number' }
          ];
          break;
        case 'WEIGHT':
          rules.unitPrice = [
            { required: true, message: '请输入单位价格', trigger: 'blur', type: 'number' }
          ];
          break;
        case 'PIECE':
          rules.piecePrice = [
            { required: true, message: '请输入单份价格', trigger: 'blur', type: 'number' }
          ];
          break;
        case 'VARIABLE':
          // 参考价格为可选，不添加必填验证
          break;
      }
    };

    // 保存
    const save = () => {
      formRef.value
        .validate()
        .then(() => {
          loading.value = true;
          const saveMethod = isUpdate.value ? ProductApi.edit : ProductApi.add;
          return saveMethod(form);
        })
        .then(() => {
          message.success('保存成功');
          updateVisible(false);
          emit('done');
        })
        .catch((e) => {
          message.error(e.message || '保存失败');
        })
        .finally(() => {
          loading.value = false;
        });
    };

    // 回显数据
    watch(
      () => props.visible,
      (visible) => {
        if (visible) {
          if (props.data?.productId) {
            isUpdate.value = true;
            Object.assign(form, props.data);
            // 处理分类数据（单分类）
            if (props.data.categoryId) {
              form.categoryId = props.data.categoryId;
            } else {
              form.categoryId = undefined;
            }
            // 根据现有的计价类型设置验证规则
            if (form.pricingType) {
              updatePriceValidationRules(form.pricingType);
            }
          } else {
            isUpdate.value = false;
            Object.keys(form).forEach((key) => {
              form[key] = undefined;
            });
            // 设置默认值
            form.unit = '个';
            form.status = 'ACTIVE';
            form.pricingType = 'NORMAL';
            form.categoryId = undefined; // 默认不选择分类
            // 设置默认计价类型的验证规则
            updatePriceValidationRules('NORMAL');
          }
        }
      },
      { immediate: true }
    );

    return {
      form,
      formRef,
      loading,
      isUpdate,
      statusOptions,
      unitOptions,
      pricingTypeOptions,
      rules,
      updateVisible,
      save,
      handleSupplierChange,
      handlePricingTypeChange
    };
  }
};
</script>
