package cn.stylefeng.roses.ent.saas.modular.reg.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户注册，发送邮箱验证码的请求
 *
 * <AUTHOR>
 * @since 2024-02-22 18:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantRegSendEmailRequest extends BaseRequest {

    /**
     * 注册邮箱
     */
    @NotBlank(message = "注册邮箱不能为空")
    @ChineseDescription("注册邮箱")
    private String email;

    /**
     * 用在图形验证码或者拖拽验证码
     */
    @ChineseDescription("用在图形验证码或者拖拽验证码")
    @NotBlank(message = "图形验证码key不能为空")
    private String verKey;

    /**
     * 图形验证码值
     */
    @ChineseDescription("图形验证码值")
    @NotBlank(message = "图形验证码值不能为空")
    private String verCode;

}
