package cn.stylefeng.roses.kernel.impexp.user.service;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.factory.UserExportFactory;
import cn.stylefeng.roses.kernel.impexp.user.pojo.UserExportRequest;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.UserExcelImportParse;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import cn.stylefeng.roses.kernel.rule.util.ResponseRenderUtil;
import cn.stylefeng.roses.kernel.sys.api.enums.user.UserStatusEnum;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserOrgService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户导出业务
 *
 * <AUTHOR>
 * @since 2024/2/13 21:26
 */
@Service
public class UserExportService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysUserOrgService sysUserOrgService;

    /**
     * 导出并生成excel
     *
     * <AUTHOR>
     * @since 2024/2/13 21:40
     */
    public void exportUser(UserExportRequest userExportRequest) {

        // 1. 根据请求参数获取导出哪个公司下的人员
        List<SysUser> exportUser = this.getExportUser(userExportRequest);

        // 2. 将这些用户转化为Excel导出的格式，主要为了映射列
        List<UserExcelImportParse> userExportData = UserExportFactory.createUserExportData(exportUser);

        // 3. 将这些用户导出为excel
        InputStream exportExcelTemplate = ResourceUtil.getStream("user-export-template.xlsx");

        // 设置header
        ResponseRenderUtil.setRenderExcelHeader(HttpServletUtil.getResponse(), "用户导出结果");

        // 获取响应流
        ServletOutputStream outputStream = null;
        try {
            outputStream = HttpServletUtil.getResponse().getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 执行导出
        EasyExcel.write(outputStream).withTemplate(exportExcelTemplate).sheet().doFill(userExportData);
    }

    /**
     * 获取导出的用户信息
     *
     * <AUTHOR>
     * @since 2024/2/13 21:28
     */
    public List<SysUser> getExportUser(UserExportRequest userExportRequest) {

        // 查询这些用户的信息
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();

        // 只查询关键的信息
        wrapper.select(SysUser::getUserId, SysUser::getRealName, SysUser::getNickName, SysUser::getAccount, SysUser::getBirthday, SysUser::getSex, SysUser::getEmail,
                SysUser::getPhone, SysUser::getTel, SysUser::getStatusFlag, SysUser::getUserSort, SysUser::getEmployeeNumber, SysUser::getMasterUserId);

        // 如果组织机构为空，则导出所有的用户数据
        if (ObjectUtil.isEmpty(userExportRequest) || ObjectUtil.isEmpty(userExportRequest.getOrgId())) {
            return sysUserService.list(wrapper);
        }

        // 获取需要导出的机构列表
        Long orgId = userExportRequest.getOrgId();
        if (ObjectUtil.isEmpty(userExportRequest.getContainSubOrg())) {
            userExportRequest.setContainSubOrg(false);
        }

        // 查询这个机构下的所有用户id
        List<Long> orgUserIdList = this.sysUserOrgService.getOrgUserIdList(orgId, true);
        if(ObjectUtil.isEmpty(orgUserIdList)){
            return new ArrayList<>();
        }

        // 只查询启用的用户
        wrapper.eq(SysUser::getStatusFlag, UserStatusEnum.ENABLE.getCode());

        // 根据排序查询
        wrapper.orderByAsc(SysUser::getUserSort);

        // 查询机构下的用户
        wrapper.in(SysUser::getUserId, orgUserIdList);

        return this.sysUserService.list(wrapper);
    }

}
