package cn.stylefeng.roses.kernel.pay.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.pay.api.entity.Goods;
import cn.stylefeng.roses.kernel.pay.api.entity.Order;
import cn.stylefeng.roses.kernel.pay.api.enums.OrderStateEnum;
import cn.stylefeng.roses.kernel.pay.api.pojo.BusinessOrder;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import java.util.Date;

/**
 * 订单工厂
 *
 * <AUTHOR>
 * @date 2021/6/23 23:20
 */
public class OrderFactory {

    /**
     * 创建订单对象
     *
     * <AUTHOR>
     * @date 2021/6/23 23:20
     */
    public static Order createOrder(Goods goods) {
        Order order = new Order();

        // 填充创建订单的通用参数
        commonSetOrderParams(order, goods.getGoodsId(), goods.getGoodsName());

        // 商品总价
        order.setOriginalPrice(goods.getPrice());

        // 支付价格
        order.setPayPrice(goods.getPrice());

        // 状态
        order.setState(OrderStateEnum.WAIT_PAY.getCode());

        return order;
    }

    /**
     * 创建业务订单
     *
     * <AUTHOR>
     * @since 2024/6/9 15:04
     */
    public static Order createBusinessOrder(BusinessOrder businessOrder) {
        Order order = new Order();

        // 生成订单id
        order.setOrderId(IdWorker.getId());

        // 填充创建订单的通用参数
        commonSetOrderParams(order, businessOrder.getGoodsId(), businessOrder.getGoodsName());

        // 商品支付成功后的回调处理
        order.setBusinessParams(businessOrder.getBusinessParams());
        order.setBusinessProcessClass(businessOrder.getBusinessProcessClass());

        // 商品总价
        order.setOriginalPrice(businessOrder.getOriginalPrice());

        // 支付价格
        order.setPayPrice(businessOrder.getPayPrice());

        // 状态默认为待支付状态
        order.setState(OrderStateEnum.WAIT_PAY.getCode());

        return order;
    }

    /**
     * 通用订单数据填充
     *
     * <AUTHOR>
     * @since 2024/6/9 15:17
     */
    private static void commonSetOrderParams(Order order, Long goodsId, String goodsName) {

        // 生成订单号
        String orderNumber = DateUtil.format(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomString(10);
        order.setOrderNumber(orderNumber);

        // 订单的购买人
        Long userId = LoginContext.me().getLoginUser().getUserId();
        order.setUserId(userId);

        // 商品id，也可以是业务id
        order.setGoodsId(goodsId);

        // 商品名称
        order.setGoodsName(goodsName);
    }

}
