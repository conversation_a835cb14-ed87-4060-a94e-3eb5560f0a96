package cn.stylefeng.roses.ent.saas.modular.auth.action.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.action.PackageBindPermissionAction;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.kernel.sys.api.entity.SysMenuOptions;
import cn.stylefeng.roses.kernel.sys.api.enums.PermissionNodeTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.menu.service.SysMenuOptionsService;
import cn.stylefeng.roses.kernel.sys.modular.role.enums.RoleLimitTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能包绑定菜单的实现
 *
 * <AUTHOR>
 * @since 2024/1/22 0:44
 */
@Service
public class PackageBindMenuImpl implements PackageBindPermissionAction {

    @Resource
    private SysMenuOptionsService sysMenuOptionsService;

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    @Override
    public PermissionNodeTypeEnum getPackageBindPermissionNodeType() {
        return PermissionNodeTypeEnum.MENU;
    }

    @Override
    public void doPackageBindPermissionAction(PackageBindPermissionRequest packageBindPermissionRequest) {
        Long packageId = packageBindPermissionRequest.getPackageId();
        Long menuId = packageBindPermissionRequest.getNodeId();

        List<TenantPackageAuth> tenantPackageAuths = new ArrayList<>();

        // 1. 先取消绑定，角色对菜单的限制
        LambdaQueryWrapper<TenantPackageAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantPackageAuth::getPackageId, packageId);
        wrapper.eq(TenantPackageAuth::getBusinessId, menuId);
        this.tenantPackageAuthService.remove(wrapper);

        // 2. 如果是选中，则执行角色绑定菜单限制
        if (packageBindPermissionRequest.getChecked()) {
            TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
            tenantPackageAuth.setPackageId(packageId);
            tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU.getCode());
            tenantPackageAuth.setBusinessId(menuId);
            tenantPackageAuths.add(tenantPackageAuth);
        }

        // 2.1. 查询菜单下的所有菜单功能
        List<Long> menuOptionsIds = this.getMenuOptions(menuId);

        // 菜单下没有菜单功能，则直接返回
        if (ObjectUtil.isEmpty(menuOptionsIds)) {
            this.tenantPackageAuthService.saveBatch(tenantPackageAuths);
            return;
        }

        // 2.2. 如果有菜单功能，则执行先删除后添加的逻辑
        // 先删除角色和菜单功能的绑定
        LambdaQueryWrapper<TenantPackageAuth> optionWrapper = new LambdaQueryWrapper<>();
        optionWrapper.eq(TenantPackageAuth::getPackageId, packageId);
        optionWrapper.in(TenantPackageAuth::getBusinessId, menuOptionsIds);
        this.tenantPackageAuthService.remove(optionWrapper);

        // 2.3. 如果是选中，则创建角色对菜单功能的绑定限制
        if (packageBindPermissionRequest.getChecked()) {
            for (Long menuOptionId : menuOptionsIds) {
                TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
                tenantPackageAuth.setPackageId(packageId);
                tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU_OPTIONS.getCode());
                tenantPackageAuth.setBusinessId(menuOptionId);
                tenantPackageAuths.add(tenantPackageAuth);
            }
        }
        this.tenantPackageAuthService.saveBatch(tenantPackageAuths);
    }

    /**
     * 获取菜单下的所有菜单功能
     *
     * <AUTHOR>
     * @since 2023/9/8 16:02
     */
    private List<Long> getMenuOptions(Long menuId) {
        return this.getMenuOptions(menuId, null);
    }

    /**
     * 获取菜单下的所有菜单功能
     *
     * <AUTHOR>
     * @since 2023/9/8 16:02
     */
    private List<Long> getMenuOptions(Long menuId, Set<Long> roleLimitMenuIdsAndOptionIds) {
        LambdaQueryWrapper<SysMenuOptions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenuOptions::getMenuId, menuId);
        if (ObjectUtil.isNotEmpty(roleLimitMenuIdsAndOptionIds)) {
            queryWrapper.in(SysMenuOptions::getMenuOptionId, roleLimitMenuIdsAndOptionIds);
        }
        queryWrapper.select(SysMenuOptions::getMenuOptionId);
        List<SysMenuOptions> list = sysMenuOptionsService.list(queryWrapper);
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(SysMenuOptions::getMenuOptionId).collect(Collectors.toList());
    }

}
