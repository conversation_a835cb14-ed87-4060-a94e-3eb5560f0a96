package cn.stylefeng.guns.gateway.modular.cache;

import cn.stylefeng.roses.kernel.cache.redis.AbstractRedisHashCacheOperator;
import cn.stylefeng.roses.kernel.scanner.api.constants.ScannerConstants;
import cn.stylefeng.roses.kernel.scanner.api.pojo.resource.ResourceDefinition;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 基于redis的资源缓存，key是资源url，value是资源的详情
 *
 * <AUTHOR>
 * @date 2021/5/13 21:52
 */
@Component
public class ResourceRedisCache extends AbstractRedisHashCacheOperator<ResourceDefinition> {

    public ResourceRedisCache(RedisTemplate<String, ResourceDefinition> redisTemplate) {
        super(redisTemplate);
    }

    @Override
    public String getCommonKeyPrefix() {
        return ScannerConstants.RESOURCE_CACHE_KEY;
    }

}
