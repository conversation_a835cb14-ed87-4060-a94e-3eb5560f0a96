import{r as l,o as b,cb as N,a as O,f as y,w as I,d as _,m as R,M as S}from"./index-18a1ea24.js";import k from"./org-form-47675b1b.js";import{O as m}from"./OrgApi-021dd6dd.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */const T={__name:"org-add-edit",props:{visible:Boolean,data:Object,parentId:String,parentName:String,levelNumberList:Array},emits:["update:visible","done"],setup(d,{emit:v}){const o=d,u=v,r=l(!1),s=l(!1),e=l({orgParentId:o.parentId,parentOrgName:o.parentName,orgType:1,statusFlag:1}),p=l(null);b(async()=>{o.data?(s.value=!0,c()):(e.value.orgSort=await N("SYSTEM_HR_ORGANIZATION"),s.value=!1)});const c=()=>{m.detail({orgId:o.data.orgId}).then(t=>{e.value=Object.assign({},t)})},i=t=>{u("update:visible",t)},g=async()=>{p.value.$refs.formRef.validate().then(async t=>{if(t){e.value.orgParentId||(e.value.orgParentId="-1",e.value.parentOrgName="\u6839\u8282\u70B9"),r.value=!0;let a=null;s.value?a=m.edit(e.value):a=m.add(e.value),a.then(async n=>{r.value=!1,R.success(n.message),i(!1),u("done")}).catch(()=>{r.value=!1})}})};return(t,a)=>{const n=S;return O(),y(n,{width:800,maskClosable:!1,visible:o.visible,"confirm-loading":r.value,forceRender:!0,title:s.value?"\u7F16\u8F91\u673A\u6784":"\u65B0\u5EFA\u673A\u6784","body-style":{paddingBottom:"8px"},"onUpdate:visible":i,onOk:g,class:"common-modal",onClose:a[1]||(a[1]=f=>i(!1))},{default:I(()=>[_(k,{form:e.value,"onUpdate:form":a[0]||(a[0]=f=>e.value=f),ref_key:"orgFormRef",ref:p,isUpdate:s.value,levelNumberList:d.levelNumberList},null,8,["form","isUpdate","levelNumberList"])]),_:1},8,["visible","confirm-loading","title"])}}};export{T as default};
