import Request from '@/utils/request/request-util';

/**
 * 商品管理API
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
export class ProductApi {
  
  /**
   * 新增商品
   * @param {Object} params 商品信息
   * @returns {Promise}
   */
  static add(params) {
    return Request.post('/erp/product/add', params);
  }

  /**
   * 删除商品
   * @param {Object} params 包含productId的参数
   * @returns {Promise}
   */
  static delete(params) {
    return Request.post('/erp/product/delete', params);
  }

  /**
   * 批量删除商品
   * @param {Object} params 包含productIdList的参数
   * @returns {Promise}
   */
  static batchDelete(params) {
    return Request.post('/erp/product/batchDelete', params);
  }

  /**
   * 编辑商品
   * @param {Object} params 商品信息
   * @returns {Promise}
   */
  static edit(params) {
    return Request.post('/erp/product/edit', params);
  }

  /**
   * 查询商品详情
   * @param {Object} params 包含productId的参数
   * @returns {Promise}
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/product/detail', params);
  }

  /**
   * 分页查询商品列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/product/page', params);
  }

  /**
   * 查询商品列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findList(params) {
    return Request.getAndLoadData('/erp/product/list', params);
  }

  /**
   * 更新商品状态
   * @param {Object} params 包含productId和status的参数
   * @returns {Promise}
   */
  static updateStatus(params) {
    return Request.post('/erp/product/updateStatus', params);
  }

  /**
   * 校验商品编码是否重复
   * @param {Object} params 包含productCode和productId的参数
   * @returns {Promise}
   */
  static validateCode(params) {
    return Request.getAndLoadData('/erp/product/validateCode', params);
  }

  /**
   * 校验条形码是否重复
   * @param {Object} params 包含barcode和productId的参数
   * @returns {Promise}
   */
  static validateBarcode(params) {
    return Request.getAndLoadData('/erp/product/validateBarcode', params);
  }

  /**
   * 获取商品状态选项
   * @returns {Array}
   */
  static getProductStatusOptions() {
    return [
      { label: '正常', value: 'ACTIVE' },
      { label: '停用', value: 'INACTIVE' },
      { label: '停产', value: 'DISCONTINUED' }
    ];
  }

  /**
   * 获取计价类型选项
   * @returns {Array}
   */
  static getPricingTypeOptions() {
    return [
      { label: '普通', value: 'NORMAL' },
      { label: '计重', value: 'WEIGHT' },
      { label: '计件', value: 'PIECE' },
      { label: '不定价', value: 'VARIABLE' }
    ];
  }

  /**
   * 获取常用单位选项
   * @returns {Array}
   */
  static getCommonUnitOptions() {
    return [
      { label: '个', value: '个' },
      { label: '件', value: '件' },
      { label: '盒', value: '盒' },
      { label: '包', value: '包' },
      { label: '袋', value: '袋' },
      { label: '瓶', value: '瓶' },
      { label: '罐', value: '罐' },
      { label: '桶', value: '桶' },
      { label: '箱', value: '箱' },
      { label: '台', value: '台' },
      { label: '套', value: '套' },
      { label: '副', value: '副' },
      { label: '双', value: '双' },
      { label: '对', value: '对' },
      { label: '张', value: '张' },
      { label: '本', value: '本' },
      { label: '支', value: '支' },
      { label: '根', value: '根' },
      { label: '条', value: '条' },
      { label: '米', value: '米' },
      { label: '千克', value: '千克' },
      { label: '克', value: '克' },
      { label: '升', value: '升' },
      { label: '毫升', value: '毫升' }
    ];
  }

  /**
   * 获取商品状态名称
   * @param {String} status 商品状态
   * @returns {String}
   */
  static getProductStatusName(status) {
    const options = ProductApi.getProductStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? option.label : status;
  }

  /**
   * 获取计价类型名称
   * @param {String} pricingType 计价类型
   * @returns {String}
   */
  static getPricingTypeName(pricingType) {
    const options = ProductApi.getPricingTypeOptions();
    const option = options.find(item => item.value === pricingType);
    return option ? option.label : pricingType;
  }

  /**
   * 获取状态标签颜色
   * @param {String} status 商品状态
   * @returns {String}
   */
  static getStatusTagColor(status) {
    switch (status) {
      case 'ACTIVE':
        return 'green';
      case 'INACTIVE':
        return 'orange';
      case 'DISCONTINUED':
        return 'red';
      default:
        return 'default';
    }
  }

  /**
   * 获取计价类型标签颜色
   * @param {String} pricingType 计价类型
   * @returns {String}
   */
  static getPricingTypeTagColor(pricingType) {
    switch (pricingType) {
      case 'NORMAL':
        return 'blue';
      case 'WEIGHT':
        return 'green';
      case 'PIECE':
        return 'orange';
      case 'VARIABLE':
        return 'purple';
      default:
        return 'default';
    }
  }

  /**
   * 格式化重量显示
   * @param {Number} weight 重量
   * @returns {String}
   */
  static formatWeight(weight) {
    if (!weight) return '-';
    return `${weight}kg`;
  }

  /**
   * 格式化体积显示
   * @param {Number} volume 体积
   * @returns {String}
   */
  static formatVolume(volume) {
    if (!volume) return '-';
    return `${volume}m³`;
  }

  /**
   * 格式化保质期显示
   * @param {Number} shelfLife 保质期
   * @returns {String}
   */
  static formatShelfLife(shelfLife) {
    if (!shelfLife) return '-';
    return `${shelfLife}天`;
  }

  /**
   * 根据供应商获取商品列表
   * @param {Object} params 包含supplierId的参数
   * @returns {Promise}
   */
  static getProductsBySupplier(params) {
    return Request.getAndLoadData('/erp/product/listBySupplier', params);
  }

  /**
   * 校验计价类型变更的影响
   * @param {Object} params 包含productId和pricingType的参数
   * @returns {Promise}
   */
  static validatePricingTypeChange(params) {
    return Request.getAndLoadData('/erp/product/validatePricingTypeChange', params);
  }

  /**
   * 格式化价格显示
   * @param {Number} price 价格
   * @param {String} pricingType 计价类型
   * @returns {String}
   */
  static formatPrice(price, pricingType) {
    if (!price) return '-';
    
    switch (pricingType) {
      case 'NORMAL':
        return `¥${price.toFixed(2)}`;
      case 'WEIGHT':
        return `¥${price.toFixed(2)}/kg`;
      case 'PIECE':
        return `¥${price.toFixed(2)}/份`;
      case 'VARIABLE':
        return `参考价 ¥${price.toFixed(2)}`;
      default:
        return `¥${price.toFixed(2)}`;
    }
  }

  /**
   * 获取商品分类树
   * @returns {Promise}
   */
  static getCategoryTree() {
    return Request.getAndLoadData('/erp/productCategory/tree');
  }

}
