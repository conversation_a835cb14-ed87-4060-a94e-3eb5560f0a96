package cn.stylefeng.roses.seata.demo.order.modular.service;

import cn.stylefeng.roses.seata.demo.order.modular.entity.OrderTbl;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务类
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
public interface OrderTblService extends IService<OrderTbl> {

    /**
     * 生成一个订单
     *
     * <AUTHOR>
     * @date 2021/10/13 22:24
     */
    void placeOrder(String userId, String commodityCode, int orderCount);

}