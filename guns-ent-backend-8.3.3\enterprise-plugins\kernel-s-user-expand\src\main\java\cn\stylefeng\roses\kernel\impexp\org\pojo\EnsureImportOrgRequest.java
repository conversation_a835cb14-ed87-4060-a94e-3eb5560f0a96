package cn.stylefeng.roses.kernel.impexp.org.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.util.List;

/**
 * 确认导入机构的请求参数
 *
 * <AUTHOR>
 * @since 2024/2/16 17:54
 */
@Data
public class EnsureImportOrgRequest {

    /**
     * 所属组织机构id
     */
    @ChineseDescription("所属组织机构id")
    private Long belongOrgId;

    /**
     * 导入的组织机构列表
     */
    @ChineseDescription("导入的组织机构列表")
    private List<EnsureImportOrgItem> importOrgItemList;

}
