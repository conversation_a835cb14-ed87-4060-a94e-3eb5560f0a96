System.register(["./index-legacy-ee1db0c7.js"],(function(e,l){"use strict";var a,u,t,d,i,n,f,v,o,s,b,r,c;return{setters:[e=>{a=e._,u=e.r,t=e.o,d=e.a,i=e.f,n=e.w,f=e.d,v=e.l,o=e.u,s=e.v,b=e.G,r=e.H,c=e.M}],execute:function(){var l=document.createElement("style");l.textContent="[data-v-bdd3a1c0] .ant-input{--disabled-color: black}\n",document.head.appendChild(l);const p={__name:"file-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:l}){const a=e,p=l,m=u(!1),_=u({});t((()=>{a.data&&(_.value=Object.assign({},a.data))}));const g=e=>{p("update:visible",e)},U=()=>{g(!1)};return(e,l)=>{const u=v,t=o,p=s,O=b,x=r,C=c;return d(),i(C,{width:700,maskClosable:!1,visible:a.visible,"confirm-loading":m.value,forceRender:!0,title:"文件详情","body-style":{paddingBottom:"8px",height:"550px",overflowY:"auto"},"onUpdate:visible":g,onOk:U,onClose:l[8]||(l[8]=e=>g(!1))},{default:n((()=>[f(x,{ref:"formRef",model:_.value,layout:"vertical"},{default:n((()=>[f(O,{gutter:20},{default:n((()=>[f(p,{span:12},{default:n((()=>[f(t,{label:"文件编码",name:"fileCode"},{default:n((()=>[f(u,{value:_.value.fileCode,"onUpdate:value":l[0]||(l[0]=e=>_.value.fileCode=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:12},{default:n((()=>[f(t,{label:"文件仓库",name:"fileBucket"},{default:n((()=>[f(u,{value:_.value.fileBucket,"onUpdate:value":l[1]||(l[1]=e=>_.value.fileBucket=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:12},{default:n((()=>[f(t,{label:"文件名称",name:"fileOriginName"},{default:n((()=>[f(u,{value:_.value.fileOriginName,"onUpdate:value":l[2]||(l[2]=e=>_.value.fileOriginName=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:12},{default:n((()=>[f(t,{label:"是否为机密文件",name:"secretFlag"},{default:n((()=>[f(u,{value:_.value.secretFlag,"onUpdate:value":l[3]||(l[3]=e=>_.value.secretFlag=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:12},{default:n((()=>[f(t,{label:"文件后缀",name:"fileSuffix"},{default:n((()=>[f(u,{value:_.value.fileSuffix,"onUpdate:value":l[4]||(l[4]=e=>_.value.fileSuffix=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:12},{default:n((()=>[f(t,{label:"文件大小",name:"fileSizeInfo"},{default:n((()=>[f(u,{value:_.value.fileSizeInfo,"onUpdate:value":l[5]||(l[5]=e=>_.value.fileSizeInfo=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:24},{default:n((()=>[f(t,{label:"唯一标识",name:"fileObjectName"},{default:n((()=>[f(u,{value:_.value.fileObjectName,"onUpdate:value":l[6]||(l[6]=e=>_.value.fileObjectName=e),disabled:""},null,8,["value"])])),_:1})])),_:1}),f(p,{span:24},{default:n((()=>[f(t,{label:"存储路径",name:"fileUrl"},{default:n((()=>[f(u,{value:_.value.fileUrl,"onUpdate:value":l[7]||(l[7]=e=>_.value.fileUrl=e),disabled:""},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["visible","confirm-loading"])}}};e("default",a(p,[["__scopeId","data-v-bdd3a1c0"]]))}}}));
