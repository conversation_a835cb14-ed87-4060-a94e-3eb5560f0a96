package cn.stylefeng.roses.kernel.micro.api.constants;

/**
 * 微服务组件模块常量
 *
 * <AUTHOR>
 * @date 2021/5/10 10:19
 */
public interface MicroConstants {

    /**
     * 微服务模块的名称
     */
    String MICRO_MODULE_NAME = "kernel-d-micro";

    /**
     * 异常枚举的步进值
     */
    String MICRO_EXCEPTION_STEP_CODE = "60";

    /**
     * 请求号header标识
     */
    String REQUEST_NO_HEADER_NAME = "Request-No";

    /**
     * header中的spanId，传递规则：request header中传递本服务的id
     */
    String SPAN_ID_HEADER_NAME = "Span-Id";

    /**
     * 鉴权请求头名称
     */
    String AUTH_HEADER_NAME = "Authorization";

    /**
     * 请求参数的token名
     */
    String TOKEN_PARAM_NAME = "token";

}
