import{r as s,a as n,c as g,d as a,w as t,b as o,g as r,F as V,f as p,h as l,at as E,aI as R,aJ as O,I as P,l as A,B as U,n as D,U as J}from"./index-18a1ea24.js";import{_ as L}from"./index-d0cfb2ce.js";import{_ as j}from"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */const q={class:"guns-layout",style:{padding:"0 12px"}},G={class:"guns-layout-content"},H={class:"guns-layout"},M={class:"guns-layout-content-application"},Q={class:"content-mian"},W={class:"content-mian-header"},X={class:"header-content"},Y={class:"header-content-left"},Z={class:"content-mian-body"},$={class:"table-content"},K={key:1,class:"org-type"},ie={__name:"index",setup(ee){const h=R(()=>O(()=>import("./org-tree-7bb24e1b.js"),["assets/org-tree-7bb24e1b.js","assets/index-18a1ea24.js","assets/index-747cb573.css","assets/OrgApi-021dd6dd.js","assets/org-tree-abcf89c8.css","assets/index-27dc9b3a.css"])),f=s(!0),w=s(!0),y=s(!1),x=s(!1),I=s([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"orgName",title:"\u673A\u6784\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"orgCode",title:"\u673A\u6784\u7F16\u7801",width:100,isShow:!0},{dataIndex:"statusFlag",title:"\u673A\u6784\u72B6\u6001",ellipsis:!0,width:100,isShow:!0},{dataIndex:"orgType",title:"\u673A\u6784\u7C7B\u578B",ellipsis:!0,width:100,isShow:!0},{dataIndex:"orgSort",title:"\u6392\u5E8F",width:100,isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:150,isShow:!0}]),m=s(null),i=s({orgId:null,statusFlag:"",searchText:""}),k=s(null),T=s(null),b=(d,e)=>{i.value.orgId=d[0],k.value=d[0],T.value=e.node.orgName,v()},v=()=>{m.value.reload()};return(d,e)=>{const S=P,C=A,F=U,N=D,u=J,z=j,B=L;return n(),g("div",q,[a(B,{space:"0px","allow-collapse":f.value,resizable:w.value,vertical:y.value,reverse:x.value,"min-size":40,style:{height:"480px","margin-top":"12px"}},{content:t(()=>[o("div",G,[o("div",H,[o("div",M,[o("div",Q,[o("div",W,[o("div",X,[o("div",Y,[a(N,{size:16},{default:t(()=>[a(C,{value:i.value.searchText,"onUpdate:value":e[0]||(e[0]=_=>i.value.searchText=_),placeholder:"\u673A\u6784\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:v,class:"search-input"},{prefix:t(()=>[a(S,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),a(F,{class:"border-radius",onClick:d.clear},{default:t(()=>e[1]||(e[1]=[r("\u91CD\u7F6E")])),_:1,__:[1]},8,["onClick"])]),_:1})]),e[2]||(e[2]=o("div",{class:"header-content-right"},null,-1))])]),o("div",Z,[o("div",$,[a(z,{columns:I.value,where:i.value,rowId:"orgId",ref_key:"tableRef",ref:m,url:"/hrOrganization/page"},{bodyCell:t(({column:_,record:c})=>[_.dataIndex=="statusFlag"?(n(),g(V,{key:0},[c.statusFlag==1?(n(),p(u,{key:0,color:"green"},{default:t(()=>e[3]||(e[3]=[r("\u542F\u7528")])),_:1,__:[3]})):l("",!0),c.statusFlag==2?(n(),p(u,{key:1,color:"red"},{default:t(()=>e[4]||(e[4]=[r("\u7981\u7528")])),_:1,__:[4]})):l("",!0)],64)):l("",!0),_.dataIndex=="orgType"?(n(),g("div",K,[c.orgType==1?(n(),p(u,{key:0,color:"green"},{default:t(()=>e[5]||(e[5]=[r("\u516C\u53F8")])),_:1,__:[5]})):l("",!0),c.orgType==2?(n(),p(u,{key:1,color:"red"},{default:t(()=>e[6]||(e[6]=[r("\u90E8\u95E8")])),_:1,__:[6]})):l("",!0)])):l("",!0)]),_:1},8,["columns","where"])])])])])])])]),default:t(()=>[a(E(h),{onTreeSelect:b,ref:"orgTreeRef",style:{padding:"12px"}},null,512)]),_:1},8,["allow-collapse","resizable","vertical","reverse"])])}}};export{ie as default};
