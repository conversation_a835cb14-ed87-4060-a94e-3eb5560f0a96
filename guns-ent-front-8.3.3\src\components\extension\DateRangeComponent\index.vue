<template>
  <a-range-picker
    v-model:value="dataValue"
    :value-format="valueFormat"
    :disabled="readonly || disabled"
    allowClear
    show-time
    @change="dataValueChange"
    @click="dataValueClick"
    @blur="dataValueBlur"
    class="w-full"
    :placeholder="[firstPlaceholder, secondPlaceholder]"
  />
</template>

<script setup name="DateRangeComponent">
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  value: {
    type: [String, Array],
    default: ''
  },
  //保存的值
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  //是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: {}
  },
  firstPlaceholder: {
    type: String,
    default: '请选择开始日期'
  },
  secondPlaceholder: {
    type: String,
    default: '请选择结束日期'
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  },
  // 是否正常保存，不是转json格式
  normal: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:value', 'onBlur', 'onClick', 'onChange']);

// 选中的值
const dataValue = ref([]);

onMounted(() => {
  setDataValue();
});

watch(
  () => props.value,
  val => {
    setDataValue();
  },
  { deep: true }
);

// 设置值
const setDataValue = () => {
  if (props.value) {
    dataValue.value = props.normal ? props.value : JSON.parse(props.value);
  } else {
    dataValue.value = [];
  }
};

// 更改值
const dataValueChange = () => {
  let data = props.normal ? dataValue.value : JSON.stringify(dataValue.value);
  emits('update:value', data);
  emits('onChange', props.record);
};
// 点击
const dataValueClick = () => {
  emits('onClick', props.record);
};
// 失去焦点
const dataValueBlur = () => {
  emits('onBlur', props.record);
};
</script>

<style></style>
