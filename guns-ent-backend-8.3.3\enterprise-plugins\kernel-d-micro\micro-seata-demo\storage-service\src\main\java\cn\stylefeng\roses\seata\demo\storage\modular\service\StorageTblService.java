package cn.stylefeng.roses.seata.demo.storage.modular.service;

import cn.stylefeng.roses.seata.demo.storage.modular.entity.StorageTbl;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务类
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
public interface StorageTblService extends IService<StorageTbl> {

    /**
     * 扣减库存操作
     *
     * <AUTHOR>
     * @date 2021/10/13 22:16
     */
    void subStorage(String commodityCode, int count);

}