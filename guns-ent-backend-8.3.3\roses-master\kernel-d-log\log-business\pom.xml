<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-log</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>log-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- 数据源容器 -->
        <!-- 用来获取当前的数据源的类型 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>ds-container-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--日志api模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>log-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--aop获取注解属性-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--auth模块-->
        <!--获取当前登录用户-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>auth-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库操作-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- web -->
        <!-- 用于本模块中对controller的拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- aop -->
        <!-- 用于本模块中对controller的拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

    </dependencies>

</project>
