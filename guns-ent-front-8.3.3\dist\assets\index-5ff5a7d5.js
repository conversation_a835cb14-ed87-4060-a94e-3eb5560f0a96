import{_ as M}from"./index-02bf6f00.js";import{r as l,o as A,k as U,a as i,c as x,b as s,d as n,w as u,g as V,t as L,h as d,f as _,M as P,E as R,m as T,n as O,B as Y,I as j,l as H,a9 as q}from"./index-18a1ea24.js";import{T as I}from"./ThemeTemplateApi-ac0cf8e8.js";import G from"./tamplate-detail-01f9a1c7.js";import J from"./template-add-edit-23974592.js";import K from"./template-config-fc2c8d3c.js";/* empty css              *//* empty css              *//* empty css              */import"./config-data-9ea15c30.js";import"./ThemeTemplateFieldApi-b2a7ece4.js";import"./template-form-e6240596.js";const Q={class:"guns-layout"},W={class:"guns-layout-content"},X={class:"guns-layout"},Z={class:"guns-layout-content-application"},ee={class:"content-mian"},te={class:"content-mian-header"},oe={class:"header-content"},ne={class:"header-content-left"},se={class:"header-content-right"},ae={class:"content-mian-body"},le={class:"table-content"},ie=["onClick"],ge={__name:"index",setup(ce){const S=l([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{title:"\u6A21\u677F\u540D\u79F0",dataIndex:"templateName",width:160,isShow:!0},{title:"\u6A21\u677F\u7F16\u7801",isShow:!0,dataIndex:"templateCode",width:160},{title:"\u6A21\u677F\u7C7B\u578B",dataIndex:"templateType",width:160,isShow:!0,customRender:function({text:t}){return t===1?"\u7CFB\u7EDF\u7C7B\u578B":"\u4E1A\u52A1\u7C7B\u578B"}},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createTime",width:160,isShow:!0},{title:"\u542F\u7528\u72B6\u6001",isShow:!0,dataIndex:"statusFlag",width:100},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}]),y=l(null),k=l({templateName:""}),c=l(null),v=l(!1),h=l(!1),f=l(!1);A(()=>{});const r=()=>{y.value.reload()},C=t=>{c.value=t,v.value=!0},N=t=>{c.value=t,f.value=!0},E=t=>{P.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6A21\u677F\u5417?",icon:n(R),maskClosable:!0,onOk:async()=>{const e=await I.del({templateId:t.templateId});T.success(e.message),r()}})},B=async(t,e)=>{const m=e.templateId,b=t?"Y":"N",g=await I.updateTemplateStatus({templateId:m});T.success(g.message),e.statusFlag=b,r()},z=t=>{c.value=t,h.value=!0};return(t,e)=>{const m=O,b=U("plus-outlined"),g=Y,w=j,$=H,D=q,F=M;return i(),x("div",Q,[s("div",W,[s("div",X,[s("div",Z,[s("div",ee,[s("div",te,[s("div",oe,[s("div",ne,[n(m,{size:16})]),s("div",se,[n(m,{size:16},{default:u(()=>[n(g,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=o=>C())},{default:u(()=>[n(b),e[5]||(e[5]=V("\u65B0\u5EFA"))]),_:1,__:[5]})]),_:1})])])]),s("div",ae,[s("div",le,[n(F,{columns:S.value,where:k.value,rowId:"templateId",ref_key:"tableRef",ref:y,rowSelection:!1,url:"/sysThemeTemplate/findPage",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"THMEM_TEMPLATE_TABLE"},{toolLeft:u(()=>[n($,{value:k.value.templateName,"onUpdate:value":e[1]||(e[1]=o=>k.value.templateName=o),placeholder:"\u4E3B\u9898\u6A21\u677F\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:r,class:"search-input",bordered:!1,style:{width:"240px"}},{prefix:u(()=>[n(w,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:u(({column:o,record:a})=>[o.dataIndex=="templateName"?(i(),x("a",{key:0,onClick:p=>N(a)},L(a.templateName),9,ie)):d("",!0),o.dataIndex==="statusFlag"?(i(),_(D,{key:1,checked:a.statusFlag==="Y",onChange:p=>B(p,a)},null,8,["checked","onChange"])):d("",!0),o.key=="action"?(i(),_(m,{key:2,size:16},{default:u(()=>[n(w,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:p=>C(a)},null,8,["onClick"]),n(w,{iconClass:"icon-menu-peizhi",color:"#60666b","font-size":"24px",title:"\u914D\u7F6E",onClick:p=>z(a)},null,8,["onClick"]),n(w,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:p=>E(a)},null,8,["onClick"])]),_:2},1024)):d("",!0)]),_:1},8,["columns","where"])])])])])])]),v.value?(i(),_(J,{key:0,visible:v.value,"onUpdate:visible":e[2]||(e[2]=o=>v.value=o),data:c.value,onDone:r},null,8,["visible","data"])):d("",!0),h.value?(i(),_(K,{key:1,visible:h.value,"onUpdate:visible":e[3]||(e[3]=o=>h.value=o),data:c.value,onDone:r},null,8,["visible","data"])):d("",!0),f.value?(i(),_(G,{key:2,visible:f.value,"onUpdate:visible":e[4]||(e[4]=o=>f.value=o),data:c.value},null,8,["visible","data"])):d("",!0)])}}};export{ge as default};
