<template>
  <a-select
    v-model:value="selectedValue"
    placeholder="请选择供应商"
    :loading="loading"
    :allowClear="allowClear"
    :disabled="disabled"
    show-search
    :filter-option="filterOption"
    @change="handleChange"
    @search="handleSearch"
  >
    <a-select-option
      v-for="supplier in filteredSuppliers"
      :key="supplier.supplierId"
      :value="supplier.supplierId"
      :disabled="supplier.disabled"
    >
      <div class="supplier-option">
        <span class="supplier-name">{{ supplier.supplierName }}</span>
        <a-tag
          :color="getBusinessModeColor(supplier.businessMode)"
          size="small"
          class="business-mode-tag"
        >
          {{ getBusinessModeName(supplier.businessMode) }}
        </a-tag>
      </div>
    </a-select-option>
  </a-select>
</template>

<script>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { SupplierApi } from '@/views/erp/supplier/api/SupplierApi';

// 全局供应商数据缓存
const globalSupplierCache = {
  data: ref([]),
  loading: ref(false),
  lastFetchTime: 0,
  cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
  loadPromise: null,

  async fetchSuppliers(forceRefresh = false) {
    const now = Date.now();

    // 如果有缓存且未过期，直接返回
    if (!forceRefresh && this.data.value.length > 0 && (now - this.lastFetchTime) < this.cacheTimeout) {
      return this.data.value;
    }

    // 如果正在加载，返回现有的Promise
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = (async () => {
      try {
        this.loading.value = true;
        const result = await SupplierApi.findList({ status: 'ACTIVE' });
        this.data.value = result || [];
        this.lastFetchTime = now;
        return this.data.value;
      } catch (error) {
        console.error('加载供应商列表失败:', error);
        throw error;
      } finally {
        this.loading.value = false;
        this.loadPromise = null;
      }
    })();

    return this.loadPromise;
  },

  clearCache() {
    this.data.value = [];
    this.lastFetchTime = 0;
    this.loadPromise = null;
  }
};

export default {
  name: 'SupplierSelector',
  props: {
    // v-model绑定的值
    value: {
      type: [String, Number],
      default: undefined
    },
    // 是否允许清空
    allowClear: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 过滤条件
    filter: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const loading = ref(false);
    const suppliers = ref([]);
    const searchKeyword = ref('');
    const isInitialized = ref(false);

    // 双向绑定的值
    const selectedValue = computed({
      get: () => props.value,
      set: (value) => {
        emit('update:value', value);
      }
    });

    // 过滤后的供应商列表
    const filteredSuppliers = computed(() => {
      let result = suppliers.value;

      // 根据业务模式过滤
      if (props.filter.businessMode) {
        const allowedModes = Array.isArray(props.filter.businessMode) 
          ? props.filter.businessMode 
          : [props.filter.businessMode];
        result = result.filter(supplier => 
          allowedModes.includes(supplier.businessMode)
        );
      }

      // 排除联营供应商（如果没有明确指定要包含）
      if (!props.filter.includeJointVenture) {
        result = result.filter(supplier => 
          supplier.businessMode !== 'JOINT_VENTURE'
        );
      }

      // 根据搜索关键词过滤
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(supplier =>
          supplier.supplierName.toLowerCase().includes(keyword) ||
          supplier.supplierCode.toLowerCase().includes(keyword)
        );
      }

      return result;
    });

    // 获取业务模式名称
    const getBusinessModeName = (businessMode) => {
      const modeMap = {
        'PURCHASE_SALE': '购销',
        'JOINT_VENTURE': '联营',
        'CONSIGNMENT': '代销'
      };
      return modeMap[businessMode] || businessMode;
    };

    // 获取业务模式标签颜色
    const getBusinessModeColor = (businessMode) => {
      const colorMap = {
        'PURCHASE_SALE': 'blue',
        'JOINT_VENTURE': 'orange',
        'CONSIGNMENT': 'green'
      };
      return colorMap[businessMode] || 'default';
    };

    // 选项过滤函数
    const filterOption = (_input, _option) => {
      // 由于我们已经在computed中处理了过滤，这里返回true
      return true;
    };

    // 处理选择变更
    const handleChange = (value, _option) => {
      const selectedSupplier = suppliers.value.find(s => s.supplierId === value);
      emit('change', value, selectedSupplier);
    };

    // 处理搜索
    const handleSearch = (value) => {
      searchKeyword.value = value;
    };

    // 刷新供应商数据
    const refreshSuppliers = async () => {
      try {
        loading.value = true;
        const result = await globalSupplierCache.fetchSuppliers(true); // 强制刷新
        suppliers.value = result || [];
      } catch (error) {
        console.error('刷新供应商列表失败:', error);
      } finally {
        loading.value = false;
      }
    };

    // 加载供应商列表
    const loadSuppliers = async () => {
      // 防止重复请求
      if (loading.value) {
        return;
      }

      try {
        loading.value = true;
        // 使用全局缓存获取供应商数据
        const result = await globalSupplierCache.fetchSuppliers();
        suppliers.value = result || [];
        isInitialized.value = true;
      } catch (error) {
        console.error('加载供应商列表失败:', error);
        suppliers.value = [];
      } finally {
        loading.value = false;
      }
    };

    // 监听过滤条件变化，重新加载数据
    watch(
      () => props.filter,
      (newFilter, oldFilter) => {
        // 只有在组件已初始化且filter真正发生变化时才重新加载
        if (isInitialized.value && JSON.stringify(newFilter) !== JSON.stringify(oldFilter)) {
          loadSuppliers();
        }
      },
      { deep: true }
    );

    // 组件挂载时加载数据
    onMounted(() => {
      // 使用nextTick确保组件完全挂载后再加载数据
      nextTick(() => {
        loadSuppliers();
      });
    });

    return {
      loading,
      selectedValue,
      filteredSuppliers,
      getBusinessModeName,
      getBusinessModeColor,
      filterOption,
      handleChange,
      handleSearch,
      refreshSuppliers
    };
  }
};
</script>

<style scoped>
.supplier-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.supplier-name {
  flex: 1;
  margin-right: 8px;
}

.business-mode-tag {
  margin-left: auto;
}

/* 下拉选项样式优化 */
:deep(.ant-select-item-option-content) {
  width: 100%;
}
</style>