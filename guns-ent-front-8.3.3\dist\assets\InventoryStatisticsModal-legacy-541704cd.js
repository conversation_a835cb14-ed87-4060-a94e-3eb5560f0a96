System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./index-legacy-510bfbb8.js","./index-legacy-45c79de7.js","./SupplierSelector-legacy-fba3813b.js","./index-legacy-c65a6a4e.js","./InventoryApi-legacy-319e6456.js","./productCategoryApi-legacy-247b2407.js","./SupplierApi-legacy-234ddfc1.js"],(function(t,e){"use strict";var a,l,o,n,i,r,s,u,c,d,g,p,y,v,f,m,h,C,k,w,S,b,_,x,N,I,R,M,T,E,V,j,z,A,F,P;return{setters:[t=>{a=t._,l=t.r,o=t.s,n=t.o,i=t.a,r=t.f,s=t.w,u=t.b,c=t.d,d=t.c,g=t.F,p=t.e,y=t.g,v=t.t,f=t.h,m=t.a2,h=t.m,C=t.a6,k=t.u,w=t.W,S=t.J,b=t.B,_=t.n,x=t.H,N=t.a0,I=t.a4,R=t.v,M=t.G,T=t.U,E=t.i,V=t.I,j=t.a5,z=t.M},null,null,null,null,null,null,t=>{A=t._},null,t=>{F=t.I},t=>{P=t.P},null],execute:function(){var e=document.createElement("style");e.textContent=".inventory-statistics-content[data-v-684aba3f]{max-height:70vh;overflow-y:auto}.supplier-name[data-v-684aba3f]{font-weight:500;color:#262626;margin-bottom:4px}.product-info[data-v-684aba3f]{text-align:left}.product-name[data-v-684aba3f]{font-weight:500;color:#262626;margin-bottom:4px}.product-code[data-v-684aba3f]{font-size:12px;color:#8c8c8c}.total-value[data-v-684aba3f]{font-weight:500;color:#1890ff}.warning-count[data-v-684aba3f]{color:#faad14;font-weight:500}.turnover-high[data-v-684aba3f]{color:#52c41a;font-weight:500}.turnover-medium[data-v-684aba3f]{color:#faad14;font-weight:500}.turnover-low[data-v-684aba3f]{color:#ff4d4f;font-weight:500}.trend-chart-container[data-v-684aba3f]{height:300px;display:flex;align-items:center;justify-content:center}.chart-placeholder[data-v-684aba3f]{text-align:center;color:#8c8c8c}.ant-statistic[data-v-684aba3f]{text-align:center}.ant-statistic-title[data-v-684aba3f]{color:#8c8c8c;font-size:14px}.ant-statistic-content[data-v-684aba3f]{color:#262626;font-size:20px;font-weight:500}\n",document.head.appendChild(e);const D={name:"InventoryStatisticsModal",components:{SupplierSelector:A},props:{visible:{type:Boolean,default:!1}},emits:["update:visible"],setup(t,{emit:e}){const a=l(!1),i=l(!1),r=l(!1),s=l([]),u=l([]),c=o({supplierId:void 0,categoryId:void 0,startDate:void 0,endDate:void 0}),d=o({totalProducts:0,totalValue:0,warningProducts:0,outOfStockProducts:0}),g=l([]),p=l([]),y=l([]),v=async()=>{await Promise.all([f(),m(),C(),k()])},f=async()=>{try{const t=await F.inventoryValue(c);t.success&&Object.assign(d,t.data)}catch(t){console.error("加载总体统计失败:",t)}},m=async()=>{a.value=!0;try{g.value=[{categoryName:"食品",productCount:25,totalValue:15e3,warningCount:3},{categoryName:"饮料",productCount:18,totalValue:8500,warningCount:1},{categoryName:"日用品",productCount:32,totalValue:12e3,warningCount:5}]}catch(t){console.error("加载分类统计失败:",t)}finally{a.value=!1}},C=async()=>{i.value=!0;try{p.value=[{supplierName:"供应商A",businessMode:"PURCHASE_SALE",businessModeName:"购销",productCount:20,totalValue:18e3,warningCount:2},{supplierName:"供应商B",businessMode:"CONSIGNMENT",businessModeName:"代销",productCount:15,totalValue:12e3,warningCount:3}]}catch(t){console.error("加载供应商统计失败:",t)}finally{i.value=!1}},k=async()=>{r.value=!0;try{y.value=[{productName:"商品A",productCode:"P001",pricingType:"NORMAL",unit:"个",turnoverRate:12.5,avgStock:100,totalSales:1250,stockDays:30},{productName:"商品B",productCode:"P002",pricingType:"WEIGHT",unit:"kg",turnoverRate:8.2,avgStock:50,totalSales:410,stockDays:45}]}catch(t){console.error("加载周转率统计失败:",t)}finally{r.value=!1}};return n((()=>{(async()=>{try{const t=await P.findList({status:"Y"});t.success&&(u.value=t.data||[])}catch(t){console.error("加载商品分类失败:",t)}})(),t.visible&&v()})),{categoryLoading:a,supplierLoading:i,turnoverLoading:r,dateRange:s,categories:u,statisticsForm:c,overallStats:d,categoryStats:g,categoryColumns:[{title:"商品分类",key:"categoryName",width:120},{title:"商品数量",dataIndex:"productCount",width:80,align:"center"},{title:"库存价值",key:"totalValue",width:100,align:"right"},{title:"预警数量",key:"warningCount",width:80,align:"center"}],supplierStats:p,supplierColumns:[{title:"供应商",key:"supplierName",width:150},{title:"商品数量",dataIndex:"productCount",width:80,align:"center"},{title:"库存价值",key:"totalValue",width:100,align:"right"},{title:"预警数量",key:"warningCount",width:80,align:"center"}],turnoverStats:y,turnoverColumns:[{title:"商品信息",key:"productInfo",width:200},{title:"周转率",key:"turnoverRate",width:100,align:"center"},{title:"平均库存",key:"avgStock",width:100,align:"right"},{title:"销售总量",key:"totalSales",width:100,align:"right"},{title:"库存天数",dataIndex:"stockDays",width:80,align:"center"}],loadStatistics:v,onDateRangeChange:t=>{t&&2===t.length?(c.startDate=t[0],c.endDate=t[1]):(c.startDate=void 0,c.endDate=void 0),v()},exportStatistics:()=>{try{h.success("导出成功")}catch(t){h.error("导出失败："+(t.message||"未知错误"))}},getBusinessModeColor:t=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"}[t]||"default"),getStockUnit:(t,e)=>{switch(t){case"WEIGHT":return"kg";case"PIECE":return"件";default:return e||"个"}},formatStock:(t,e)=>{if(!t)return"0";const a="WEIGHT"===e?3:0;return parseFloat(t).toFixed(a)},formatAmount:t=>t?parseFloat(t).toFixed(2):"0.00",formatTurnoverRate:t=>t?parseFloat(t).toFixed(1):"0.0",getTurnoverRateClass:t=>{const e=parseFloat(t)||0;return e>=10?"turnover-high":e>=5?"turnover-medium":"turnover-low"},handleCancel:()=>{e("update:visible",!1)}}}},L={class:"inventory-statistics-content"},U={key:1,class:"total-value"},O={key:0,class:"warning-count"},G={key:1},H={key:0},B={class:"supplier-name"},Y={key:1,class:"total-value"},W={key:0,class:"warning-count"},J={key:1},Q={class:"trend-chart-container"},q={class:"chart-placeholder"},K={key:0,class:"product-info"},X={class:"product-name"},Z={class:"product-code"};t("default",a(D,[["render",function(t,e,a,l,o,n){const h=C,F=k,P=A,D=w,$=S,tt=b,et=_,at=x,lt=N,ot=I,nt=R,it=M,rt=T,st=E,ut=V,ct=j,dt=z;return i(),r(dt,{visible:a.visible,title:"库存统计分析",width:1200,footer:null,onCancel:l.handleCancel},{default:s((()=>[u("div",L,[c(lt,{title:"统计条件",size:"small",style:{"margin-bottom":"16px"}},{default:s((()=>[c(at,{layout:"inline",model:l.statisticsForm},{default:s((()=>[c(F,{label:"统计时间"},{default:s((()=>[c(h,{value:l.dateRange,"onUpdate:value":e[0]||(e[0]=t=>l.dateRange=t),style:{width:"240px"},placeholder:["开始时间","结束时间"],format:"YYYY-MM-DD",onChange:l.onDateRangeChange},null,8,["value","onChange"])])),_:1}),c(F,{label:"供应商"},{default:s((()=>[c(P,{value:l.statisticsForm.supplierId,"onUpdate:value":e[1]||(e[1]=t=>l.statisticsForm.supplierId=t),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},placeholder:"全部供应商",allowClear:"",onChange:l.loadStatistics},null,8,["value","onChange"])])),_:1}),c(F,{label:"商品分类"},{default:s((()=>[c($,{value:l.statisticsForm.categoryId,"onUpdate:value":e[2]||(e[2]=t=>l.statisticsForm.categoryId=t),placeholder:"全部分类",allowClear:"",style:{width:"150px"},onChange:l.loadStatistics},{default:s((()=>[(i(!0),d(g,null,p(l.categories,(t=>(i(),r(D,{key:t.categoryId,value:t.categoryId},{default:s((()=>[y(v(t.categoryName),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","onChange"])])),_:1}),c(F,null,{default:s((()=>[c(et,null,{default:s((()=>[c(tt,{type:"primary",onClick:l.loadStatistics},{default:s((()=>e[3]||(e[3]=[y("刷新统计")]))),_:1,__:[3]},8,["onClick"]),c(tt,{onClick:l.exportStatistics},{default:s((()=>e[4]||(e[4]=[y("导出报表")]))),_:1,__:[4]},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),c(lt,{title:"总体统计",size:"small",style:{"margin-bottom":"16px"}},{default:s((()=>[c(it,{gutter:16},{default:s((()=>[c(nt,{span:6},{default:s((()=>[c(ot,{title:"商品总数",value:l.overallStats.totalProducts,"value-style":{color:"#1890ff"},suffix:"种"},null,8,["value"])])),_:1}),c(nt,{span:6},{default:s((()=>[c(ot,{title:"库存总价值",value:l.overallStats.totalValue,"value-style":{color:"#52c41a"},prefix:"¥",precision:2},null,8,["value"])])),_:1}),c(nt,{span:6},{default:s((()=>[c(ot,{title:"预警商品",value:l.overallStats.warningProducts,"value-style":{color:"#faad14"},suffix:"种"},null,8,["value"])])),_:1}),c(nt,{span:6},{default:s((()=>[c(ot,{title:"缺货商品",value:l.overallStats.outOfStockProducts,"value-style":{color:"#ff4d4f"},suffix:"种"},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(it,{gutter:16},{default:s((()=>[c(nt,{span:12},{default:s((()=>[c(lt,{title:"按分类统计",size:"small",style:{"margin-bottom":"16px"}},{default:s((()=>[c(st,{columns:l.categoryColumns,"data-source":l.categoryStats,pagination:!1,size:"small",loading:l.categoryLoading,scroll:{y:300}},{bodyCell:s((({column:t,record:e})=>["categoryName"===t.key?(i(),r(rt,{key:0,color:"blue"},{default:s((()=>[y(v(e.categoryName),1)])),_:2},1024)):f("",!0),"totalValue"===t.key?(i(),d("span",U,"¥"+v(l.formatAmount(e.totalValue)),1)):f("",!0),"warningCount"===t.key?(i(),d(g,{key:2},[e.warningCount>0?(i(),d("span",O,v(e.warningCount),1)):(i(),d("span",G,"0"))],64)):f("",!0)])),_:1},8,["columns","data-source","loading"])])),_:1})])),_:1}),c(nt,{span:12},{default:s((()=>[c(lt,{title:"按供应商统计",size:"small",style:{"margin-bottom":"16px"}},{default:s((()=>[c(st,{columns:l.supplierColumns,"data-source":l.supplierStats,pagination:!1,size:"small",loading:l.supplierLoading,scroll:{y:300}},{bodyCell:s((({column:t,record:e})=>["supplierName"===t.key?(i(),d("div",H,[u("div",B,v(e.supplierName),1),e.businessModeName?(i(),r(rt,{key:0,size:"small",color:l.getBusinessModeColor(e.businessMode)},{default:s((()=>[y(v(e.businessModeName),1)])),_:2},1032,["color"])):f("",!0)])):f("",!0),"totalValue"===t.key?(i(),d("span",Y,"¥"+v(l.formatAmount(e.totalValue)),1)):f("",!0),"warningCount"===t.key?(i(),d(g,{key:2},[e.warningCount>0?(i(),d("span",W,v(e.warningCount),1)):(i(),d("span",J,"0"))],64)):f("",!0)])),_:1},8,["columns","data-source","loading"])])),_:1})])),_:1})])),_:1}),c(lt,{title:"库存变动趋势",size:"small",style:{"margin-bottom":"16px"}},{default:s((()=>[u("div",Q,[u("div",q,[c(ct,{description:"图表功能待开发"},{image:s((()=>[c(ut,{iconClass:"icon-opt-tongji",style:{"font-size":"48px",color:"#d9d9d9"}})])),_:1})])])])),_:1}),c(lt,{title:"库存周转分析",size:"small"},{default:s((()=>[c(st,{columns:l.turnoverColumns,"data-source":l.turnoverStats,pagination:{pageSize:10},size:"small",loading:l.turnoverLoading},{bodyCell:s((({column:t,record:e})=>["productInfo"===t.key?(i(),d("div",K,[u("div",X,v(e.productName),1),u("div",Z,v(e.productCode),1)])):f("",!0),"turnoverRate"===t.key?(i(),d("span",{key:1,class:m(l.getTurnoverRateClass(e.turnoverRate))},v(l.formatTurnoverRate(e.turnoverRate)),3)):f("",!0),"avgStock"===t.key?(i(),d(g,{key:2},[y(v(l.formatStock(e.avgStock,e.pricingType))+" "+v(l.getStockUnit(e.pricingType,e.unit)),1)],64)):f("",!0),"totalSales"===t.key?(i(),d(g,{key:3},[y(v(l.formatStock(e.totalSales,e.pricingType))+" "+v(l.getStockUnit(e.pricingType,e.unit)),1)],64)):f("",!0)])),_:1},8,["columns","data-source","loading"])])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-684aba3f"]]))}}}));
