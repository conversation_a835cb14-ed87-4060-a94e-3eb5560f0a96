<template>
  <common-drawer
    :width="800"
    :visible="props.visible"
    title="租户信息"
    @close="updateVisible(false)"
    :isShowTab="true"
    :activeKey="activeKey"
    :tabList="tabList"
    @tabChange="tabChange"
  >
    <template #top>
      <div class="top">
        <a-avatar style="background-color: #6f9ae7">
          {{ setName(form.tenantName) }}
        </a-avatar>
        <span class="username">{{ form.tenantName }}</span>
      </div>
    </template>
    <div class="content">
      <!-- 基本信息 -->
      <div class="content-item" v-show="activeKey == '1'">
        <a-form ref="formRef" :model="form" :label-col="{ span: 8 }">
          <a-row :gutter="16">
            <a-col :span="12" v-for="(item, index) in baseColumn" :key="index">
              <a-form-item :label="item.name">
                <span>{{ form[item.value] }}</span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- 统计信息 -->
      <div class="content-item" v-show="activeKey == '2'">
        <a-form ref="formRef" :model="form" :label-col="{ span: 8 }">
          <a-row :gutter="16">
            <a-col :span="12" v-for="(item, index) in countColumn" :key="index">
              <a-form-item :label="item.name">
                <span>{{ form[item.value] }}</span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>
  </common-drawer>
</template>

<script setup name="TenantDetail">
import { ref, onMounted, watch } from 'vue';
import { TenantApi } from '@/views/tenant/manage/api/TenantApi';

const props = defineProps({
  visible: Boolean,
  data: Object
});

const emits = defineEmits(['update:visible', 'done']);

// 激活tab
const activeKey = ref('1');
// tab栏列表
const tabList = ref([
  {
    key: '1',
    name: '基础信息',
    icon: 'icon-tab-baseinfo'
  },
  {
    key: '2',
    name: '统计信息',
    icon: 'icon-menu-zuzhijiagou'
  }
]);

// 表单数据
const form = ref({});

// 基本信息展示项配置
const baseColumn = ref([
  {
    name: '租户名称',
    value: 'tenantName'
  },
  {
    name: '租户编码',
    value: 'tenantCode'
  },
  {
    name: '注册邮箱',
    value: 'email'
  },
  {
    name: '注册电话',
    value: 'safePhone'
  },
  {
    name: '性别',
    value: 'sex'
  },
  {
    name: '租户开通时间',
    value: 'activeDate'
  },
  {
    name: '租户到期时间',
    value: 'expireDate'
  },
  {
    name: '创建时间',
    value: 'createTime'
  },
  {
    name: '上次更新时间',
    value: 'updateTime'
  }
]);

// 统计信息
const countColumn = ref([
  {
    name: '租户用户数',
    value: 'tenantUserCount'
  },
  {
    name: '租户下公司数量',
    value: 'tenantOrgCount'
  }
]);
onMounted(() => {
  getDetail();
});

watch(
  () => props.data,
  val => {
    if (val) {
      getDetail();
    }
  },
  { deep: true }
);

// 获取租户详情
const getDetail = () => {
  TenantApi.detail({ tenantId: props.data.tenantId }).then(res => {
    form.value = Object.assign({}, res);
  });
};

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};
// tab切换
const tabChange = key => {
  activeKey.value = key;
};
// 设置名称
const setName = realName => {
  let name = realName;
  if (realName && realName.length > 1) {
    name = realName.substr(0, 1);
  }
  return name;
};
</script>

<style scoped lang="less">
:deep(.ant-drawer-title) {
  color: #262626;
  font-size: 18px;
  font-weight: 500;
}
.top {
  display: flex;
  align-items: center;
  height: 40px;
  line-height: 40px;
  margin-bottom: 14px;
  .username {
    margin-left: 8px;
    font-size: 20px;
    font-weight: 500;
    color: #43505e;
  }
}
.content {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .content-item {
    width: 100%;
    height: 100%;
  }
}
:deep(.ant-checkbox-wrapper-checked .ant-checkbox-disabled .ant-checkbox-inner) {
  --disabled-bg: var(--primary-color);
  --disabled-color: #fff;
}
:deep(.ant-checkbox-disabled + span) {
  --disabled-color: black;
}
</style>
