package cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

/**
 * 用来统计机构下的所有人员，包含机构下的机构这种统计
 *
 * <AUTHOR>
 * @since 2024-03-29 14:56
 */
@Data
public class OrgUserStatTotal {

    /**
     * 机构id
     */
    @ChineseDescription("机构id")
    private Long orgId;

    /**
     * 用户id
     */
    @ChineseDescription("用户id")
    private Long userId;

    /**
     * 机构的子机构信息
     */
    @ChineseDescription("机构的子机构信息")
    private String orgPids;

}
