/**
 * POS系统增强错误处理工具类
 *
 * 提供统一的错误处理、智能重试机制、错误恢复策略和用户友好的错误提示
 *
 * <AUTHOR>
 * @since 2025/08/01 20:50
 */
import { message, notification, Modal } from 'ant-design-vue';

export class PosErrorHandler {

  // 错误类型常量
  static ERROR_TYPES = {
    NETWORK: 'NETWORK',           // 网络错误
    BUSINESS: 'BUSINESS',         // 业务错误
    VALIDATION: 'VALIDATION',     // 验证错误
    PERMISSION: 'PERMISSION',     // 权限错误
    SYSTEM: 'SYSTEM'             // 系统错误
  };

  // 错误严重程度
  static SEVERITY_LEVELS = {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
    CRITICAL: 'CRITICAL'
  };

  // 错误恢复策略
  static RECOVERY_STRATEGIES = {
    RETRY: 'retry',               // 重试
    FALLBACK: 'fallback',         // 降级处理
    CACHE: 'cache',               // 使用缓存
    MANUAL: 'manual',             // 手动处理
    IGNORE: 'ignore',             // 忽略错误
    REFRESH: 'refresh',           // 刷新页面
    LOGOUT: 'logout'              // 重新登录
  };

  // 网络状态监控
  static networkStatus = {
    isOnline: navigator.onLine,
    lastOnlineTime: Date.now(),
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectDelay: 3000
  };

  // 错误统计
  static errorStats = {
    totalErrors: 0,
    errorsByType: new Map(),
    errorsByCode: new Map(),
    lastErrorTime: null
  };

  // 缓存数据存储
  static cachedData = new Map();

  // 重试队列
  static retryQueue = new Map();

  // 业务错误码映射
  static BUSINESS_ERROR_MESSAGES = {
    // 商品相关错误
    'PRODUCT_NOT_FOUND': '商品不存在',
    'PRODUCT_INACTIVE': '商品已停用',
    'PRODUCT_OUT_OF_STOCK': '商品库存不足',
    'PRODUCT_PRICE_CHANGED': '商品价格已变更，请刷新后重试',
    
    // 订单相关错误
    'ORDER_NOT_FOUND': '订单不存在',
    'ORDER_ALREADY_PAID': '订单已支付',
    'ORDER_ALREADY_CANCELLED': '订单已取消',
    'ORDER_ITEM_NOT_FOUND': '订单项不存在',
    'ORDER_AMOUNT_MISMATCH': '订单金额不匹配',
    'ORDER_STATUS_INVALID': '订单状态无效',
    
    // 支付相关错误
    'PAYMENT_FAILED': '支付失败',
    'PAYMENT_TIMEOUT': '支付超时',
    'PAYMENT_CANCELLED': '支付已取消',
    'PAYMENT_AMOUNT_INVALID': '支付金额无效',
    'INSUFFICIENT_BALANCE': '余额不足',
    'MEMBER_NOT_FOUND': '会员不存在',
    'MEMBER_CARD_DISABLED': '会员卡已停用',
    'CARD_NUMBER_INVALID': '银行卡号无效',
    
    // 库存相关错误
    'STOCK_INSUFFICIENT': '库存不足',
    'STOCK_LOCKED': '库存已被锁定',
    'STOCK_UPDATE_FAILED': '库存更新失败',
    
    // 系统相关错误
    'SYSTEM_BUSY': '系统繁忙，请稍后重试',
    'DATA_SYNC_FAILED': '数据同步失败',
    'CONCURRENT_UPDATE': '数据已被其他用户修改，请刷新后重试'
  };

  // 可重试的错误类型
  static RETRYABLE_ERRORS = [
    'NETWORK_ERROR',
    'TIMEOUT',
    'SYSTEM_BUSY',
    'DATA_SYNC_FAILED',
    500, // 服务器内部错误
    502, // 网关错误
    503, // 服务不可用
    504  // 网关超时
  ];

  /**
   * 智能错误处理（增强版）
   *
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  static async handleError(error, context = {}, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      autoRetry = true,
      maxRetries = 3,
      retryDelay = 1000
    } = options;

    // 解析错误信息
    const errorInfo = this.parseError(error);

    // 更新错误统计
    this.updateErrorStats(errorInfo);

    // 记录错误日志
    this.logError(error, context, errorInfo);

    // 确定恢复策略
    const strategy = this.determineRecoveryStrategy(errorInfo, context);

    // 执行恢复策略
    const result = await this.executeRecoveryStrategy(strategy, error, context, options);

    // 用户反馈
    if (result.success) {
      if (showMessage && result.message) {
        message.success(result.message);
      }
    } else {
      this.provideUserFeedback(errorInfo, result, options);
    }

    return result;
  }

  /**
   * 处理API错误（保持向后兼容）
   */
  static handleApiError(error, options = {}) {
    return this.handleError(error, { operation: 'api_call' }, options);
  }

  /**
   * 确定错误恢复策略
   */
  static determineRecoveryStrategy(errorInfo, context) {
    const { type, code, severity } = errorInfo;
    const { operation, retryCount = 0 } = context;

    // 网络错误处理策略
    if (type === this.ERROR_TYPES.NETWORK) {
      if (retryCount < 3) return this.RECOVERY_STRATEGIES.RETRY;
      if (this.hasCachedData(operation)) return this.RECOVERY_STRATEGIES.CACHE;
      return this.RECOVERY_STRATEGIES.MANUAL;
    }

    // 业务错误处理策略
    if (type === this.ERROR_TYPES.BUSINESS) {
      switch (code) {
        case 'POS_PRODUCT_STOCK_001':
          return this.RECOVERY_STRATEGIES.MANUAL; // 库存不足需要用户处理
        case 'POS_PAYMENT_PROCESS_001':
          return retryCount < 2 ? this.RECOVERY_STRATEGIES.RETRY : this.RECOVERY_STRATEGIES.MANUAL;
        case 'POS_MEMBER_QUERY_001':
          return this.RECOVERY_STRATEGIES.MANUAL; // 会员不存在需要用户确认
        default:
          return this.RECOVERY_STRATEGIES.MANUAL;
      }
    }

    // 权限错误处理策略
    if (type === this.ERROR_TYPES.PERMISSION) {
      if (code === 'UNAUTHORIZED') {
        return this.RECOVERY_STRATEGIES.LOGOUT; // 需要重新登录
      }
      return this.RECOVERY_STRATEGIES.MANUAL;
    }

    // 系统错误处理策略
    if (type === this.ERROR_TYPES.SYSTEM) {
      if (severity === this.SEVERITY_LEVELS.CRITICAL) {
        return this.RECOVERY_STRATEGIES.REFRESH; // 严重错误刷新页面
      }
      return retryCount < 1 ? this.RECOVERY_STRATEGIES.RETRY : this.RECOVERY_STRATEGIES.MANUAL;
    }

    return this.RECOVERY_STRATEGIES.MANUAL;
  }

  /**
   * 执行恢复策略
   */
  static async executeRecoveryStrategy(strategy, error, context, options) {
    const { operation, originalFunction, retryCount = 0 } = context;

    switch (strategy) {
      case this.RECOVERY_STRATEGIES.RETRY:
        return await this.executeRetry(originalFunction, context, options);

      case this.RECOVERY_STRATEGIES.CACHE:
        return this.useCachedData(operation);

      case this.RECOVERY_STRATEGIES.FALLBACK:
        return this.executeFallback(operation, context);

      case this.RECOVERY_STRATEGIES.REFRESH:
        return this.executeRefresh();

      case this.RECOVERY_STRATEGIES.LOGOUT:
        return this.executeLogout();

      case this.RECOVERY_STRATEGIES.IGNORE:
        return { success: true, message: '错误已忽略' };

      case this.RECOVERY_STRATEGIES.MANUAL:
      default:
        return { success: false, strategy: 'manual', error };
    }
  }

  /**
   * 执行重试
   */
  static async executeRetry(originalFunction, context, options) {
    const { retryCount = 0, maxRetries = 3 } = context;
    const { retryDelay = 1000 } = options;

    if (retryCount >= maxRetries) {
      return { success: false, message: '重试次数已达上限' };
    }

    // 延迟重试
    await this.delay(retryDelay * Math.pow(2, retryCount)); // 指数退避

    try {
      const result = await originalFunction();
      return { success: true, data: result, message: '重试成功' };
    } catch (error) {
      const newContext = { ...context, retryCount: retryCount + 1 };
      return await this.handleError(error, newContext, options);
    }
  }

  /**
   * 使用缓存数据
   */
  static useCachedData(operation) {
    const cachedData = this.cachedData.get(operation);
    if (cachedData && this.isCacheValid(cachedData)) {
      return {
        success: true,
        data: cachedData.data,
        message: '使用缓存数据',
        fromCache: true
      };
    }
    return { success: false, message: '无可用缓存数据' };
  }

  /**
   * 执行降级处理
   */
  static executeFallback(operation, context) {
    // 根据操作类型执行不同的降级策略
    switch (operation) {
      case 'getProducts':
        return { success: true, data: [], message: '商品数据暂时不可用' };
      case 'getCategories':
        return { success: true, data: [], message: '分类数据暂时不可用' };
      default:
        return { success: false, message: '无可用降级方案' };
    }
  }

  /**
   * 执行页面刷新
   */
  static executeRefresh() {
    Modal.confirm({
      title: '系统错误',
      content: '系统遇到严重错误，需要刷新页面。是否立即刷新？',
      okText: '立即刷新',
      cancelText: '稍后处理',
      onOk() {
        window.location.reload();
      }
    });
    return { success: true, message: '准备刷新页面' };
  }

  /**
   * 执行重新登录
   */
  static executeLogout() {
    Modal.confirm({
      title: '登录已过期',
      content: '您的登录已过期，需要重新登录。是否立即跳转到登录页面？',
      okText: '重新登录',
      cancelText: '稍后处理',
      onOk() {
        // 清除本地存储
        localStorage.clear();
        sessionStorage.clear();
        // 跳转到登录页面
        window.location.href = '/login';
      }
    });
    return { success: true, message: '准备重新登录' };
  }

  /**
   * 检查是否有缓存数据
   */
  static hasCachedData(operation) {
    const cachedData = this.cachedData.get(operation);
    return cachedData && this.isCacheValid(cachedData);
  }

  /**
   * 检查缓存是否有效
   */
  static isCacheValid(cachedData) {
    const now = Date.now();
    const cacheAge = now - cachedData.timestamp;
    const maxAge = cachedData.maxAge || 5 * 60 * 1000; // 默认5分钟
    return cacheAge < maxAge;
  }

  /**
   * 设置缓存数据
   */
  static setCachedData(operation, data, maxAge = 5 * 60 * 1000) {
    this.cachedData.set(operation, {
      data,
      timestamp: Date.now(),
      maxAge
    });
  }

  /**
   * 更新错误统计
   */
  static updateErrorStats(errorInfo) {
    this.errorStats.totalErrors++;
    this.errorStats.lastErrorTime = Date.now();

    // 按类型统计
    const typeCount = this.errorStats.errorsByType.get(errorInfo.type) || 0;
    this.errorStats.errorsByType.set(errorInfo.type, typeCount + 1);

    // 按错误码统计
    const codeCount = this.errorStats.errorsByCode.get(errorInfo.code) || 0;
    this.errorStats.errorsByCode.set(errorInfo.code, codeCount + 1);
  }

  /**
   * 延迟函数
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 提供用户反馈
   */
  static provideUserFeedback(errorInfo, result, options) {
    const { showMessage = true, showNotification = false } = options;

    if (showMessage) {
      this.showErrorMessage(errorInfo);
    }

    if (showNotification) {
      this.showErrorNotification(errorInfo);
    }

    // 如果有恢复建议，显示操作提示
    if (result.strategy === 'manual') {
      this.showRecoveryOptions(errorInfo, result);
    }
  }

  /**
   * 显示恢复选项
   */
  static showRecoveryOptions(errorInfo, result) {
    const { type, code } = errorInfo;

    // 根据错误类型显示不同的恢复选项
    if (type === this.ERROR_TYPES.NETWORK) {
      Modal.confirm({
        title: '网络连接错误',
        content: '网络连接失败，您可以：',
        okText: '重试',
        cancelText: '使用离线模式',
        onOk() {
          // 重试操作
          window.location.reload();
        },
        onCancel() {
          // 启用离线模式
          message.info('已切换到离线模式');
        }
      });
    } else if (code === 'POS_PRODUCT_STOCK_001') {
      Modal.info({
        title: '库存不足',
        content: '商品库存不足，请选择其他商品或减少数量。',
        okText: '知道了'
      });
    }
  }

  /**
   * 解析错误信息
   * 
   * @param {Error} error - 错误对象
   * @returns {Object} 解析后的错误信息
   */
  static parseError(error) {
    let errorInfo = {
      type: PosErrorHandler.ERROR_TYPES.SYSTEM,
      code: 'UNKNOWN_ERROR',
      message: '未知错误',
      originalError: error,
      retryable: false
    };

    if (!error) {
      return errorInfo;
    }

    // 网络错误
    if (error.code === 'NETWORK_ERROR' || !error.response) {
      errorInfo.type = PosErrorHandler.ERROR_TYPES.NETWORK;
      errorInfo.code = 'NETWORK_ERROR';
      errorInfo.message = '网络连接失败，请检查网络设置';
      errorInfo.retryable = true;
      return errorInfo;
    }

    // HTTP状态码错误
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          errorInfo.type = PosErrorHandler.ERROR_TYPES.VALIDATION;
          errorInfo.code = 'BAD_REQUEST';
          errorInfo.message = data?.message || '请求参数错误';
          break;
          
        case 401:
          errorInfo.type = PosErrorHandler.ERROR_TYPES.PERMISSION;
          errorInfo.code = 'UNAUTHORIZED';
          errorInfo.message = '登录已过期，请重新登录';
          break;
          
        case 403:
          errorInfo.type = PosErrorHandler.ERROR_TYPES.PERMISSION;
          errorInfo.code = 'FORBIDDEN';
          errorInfo.message = '没有权限执行此操作';
          break;
          
        case 404:
          errorInfo.type = PosErrorHandler.ERROR_TYPES.BUSINESS;
          errorInfo.code = 'NOT_FOUND';
          errorInfo.message = '请求的资源不存在';
          break;
          
        case 500:
          errorInfo.type = PosErrorHandler.ERROR_TYPES.SYSTEM;
          errorInfo.code = 'INTERNAL_SERVER_ERROR';
          errorInfo.message = '服务器内部错误，请稍后重试';
          errorInfo.retryable = true;
          break;
          
        case 502:
        case 503:
        case 504:
          errorInfo.type = PosErrorHandler.ERROR_TYPES.SYSTEM;
          errorInfo.code = 'SERVICE_UNAVAILABLE';
          errorInfo.message = '服务暂时不可用，请稍后重试';
          errorInfo.retryable = true;
          break;
          
        default:
          errorInfo.message = data?.message || `HTTP错误 ${status}`;
      }

      // 处理业务错误码
      if (data?.code && PosErrorHandler.BUSINESS_ERROR_MESSAGES[data.code]) {
        errorInfo.type = PosErrorHandler.ERROR_TYPES.BUSINESS;
        errorInfo.code = data.code;
        errorInfo.message = PosErrorHandler.BUSINESS_ERROR_MESSAGES[data.code];
      }

      // 判断是否可重试
      errorInfo.retryable = PosErrorHandler.RETRYABLE_ERRORS.includes(status) ||
                           PosErrorHandler.RETRYABLE_ERRORS.includes(data?.code);
    }

    return errorInfo;
  }

  /**
   * 显示错误消息
   * 
   * @param {Object} errorInfo - 错误信息
   */
  static showErrorMessage(errorInfo) {
    const { type, message } = errorInfo;
    
    switch (type) {
      case PosErrorHandler.ERROR_TYPES.NETWORK:
        message.error(message, 5);
        break;
        
      case PosErrorHandler.ERROR_TYPES.BUSINESS:
        message.warning(message, 4);
        break;
        
      case PosErrorHandler.ERROR_TYPES.VALIDATION:
        message.warning(message, 3);
        break;
        
      case PosErrorHandler.ERROR_TYPES.PERMISSION:
        message.error(message, 5);
        break;
        
      case PosErrorHandler.ERROR_TYPES.SYSTEM:
      default:
        message.error(message, 5);
        break;
    }
  }

  /**
   * 显示错误通知
   * 
   * @param {Object} errorInfo - 错误信息
   */
  static showErrorNotification(errorInfo) {
    const { type, message, code } = errorInfo;
    
    let notificationConfig = {
      message: '操作失败',
      description: message,
      duration: 4.5
    };

    switch (type) {
      case PosErrorHandler.ERROR_TYPES.NETWORK:
        notificationConfig.message = '网络错误';
        notificationConfig.description = `${message}\n错误代码: ${code}`;
        break;
        
      case PosErrorHandler.ERROR_TYPES.BUSINESS:
        notificationConfig.message = '业务错误';
        break;
        
      case PosErrorHandler.ERROR_TYPES.VALIDATION:
        notificationConfig.message = '参数错误';
        break;
        
      case PosErrorHandler.ERROR_TYPES.PERMISSION:
        notificationConfig.message = '权限错误';
        notificationConfig.duration = 6;
        break;
        
      case PosErrorHandler.ERROR_TYPES.SYSTEM:
        notificationConfig.message = '系统错误';
        notificationConfig.description = `${message}\n错误代码: ${code}`;
        break;
    }

    notification.error(notificationConfig);
  }

  /**
   * 记录错误日志
   * 
   * @param {Error} error - 原始错误对象
   * @param {string} context - 错误上下文
   * @param {Object} errorInfo - 解析后的错误信息
   */
  static logError(error, context, errorInfo) {
    const logData = {
      timestamp: new Date().toISOString(),
      context: context,
      errorType: errorInfo.type,
      errorCode: errorInfo.code,
      errorMessage: errorInfo.message,
      originalError: {
        message: error?.message,
        stack: error?.stack,
        response: error?.response?.data
      },
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // 开发环境下在控制台输出详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 POS Error [${errorInfo.type}]`);
      console.error('Context:', context);
      console.error('Error Info:', errorInfo);
      console.error('Original Error:', error);
      console.error('Log Data:', logData);
      console.groupEnd();
    }

    // 生产环境下可以发送到日志服务
    // TODO: 集成日志服务，如Sentry、LogRocket等
  }

  /**
   * 创建重试函数
   * 
   * @param {Function} apiFunction - 要重试的API函数
   * @param {Object} options - 重试选项
   * @param {number} options.maxRetries - 最大重试次数，默认3
   * @param {number} options.retryDelay - 重试延迟（毫秒），默认1000
   * @param {Function} options.shouldRetry - 自定义重试判断函数
   * @returns {Function} 包装后的函数
   */
  static createRetryWrapper(apiFunction, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      shouldRetry = (errorInfo) => errorInfo.retryable
    } = options;

    return async function retryWrapper(...args) {
      let lastError;
      
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          return await apiFunction.apply(this, args);
        } catch (error) {
          lastError = error;
          const errorInfo = PosErrorHandler.parseError(error);
          
          // 如果不是最后一次尝试且错误可重试
          if (attempt < maxRetries && shouldRetry(errorInfo)) {
            console.warn(`API调用失败，${retryDelay}ms后进行第${attempt + 1}次重试:`, errorInfo.message);
            await PosErrorHandler.delay(retryDelay * Math.pow(2, attempt)); // 指数退避
            continue;
          }
          
          // 最后一次尝试失败或错误不可重试
          throw error;
        }
      }
      
      throw lastError;
    };
  }

  /**
   * 延迟函数
   * 
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建带错误处理的API调用包装器
   * 
   * @param {Function} apiFunction - API函数
   * @param {Object} errorOptions - 错误处理选项
   * @param {Object} retryOptions - 重试选项
   * @returns {Function} 包装后的函数
   */
  static wrapApiCall(apiFunction, errorOptions = {}, retryOptions = {}) {
    // 先包装重试逻辑
    const retryWrapper = PosErrorHandler.createRetryWrapper(apiFunction, retryOptions);
    
    // 再包装错误处理逻辑
    return async function errorHandlerWrapper(...args) {
      try {
        return await retryWrapper.apply(this, args);
      } catch (error) {
        const errorInfo = PosErrorHandler.handleApiError(error, errorOptions);
        throw errorInfo; // 抛出处理后的错误信息
      }
    };
  }

  /**
   * 批量处理API调用错误
   * 
   * @param {Array} apiCalls - API调用数组，每个元素为 {name, promise}
   * @param {Object} options - 处理选项
   * @returns {Promise} 处理结果
   */
  static async handleBatchApiCalls(apiCalls, options = {}) {
    const {
      continueOnError = true,
      showSummary = true
    } = options;

    const results = [];
    const errors = [];

    for (const { name, promise } of apiCalls) {
      try {
        const result = await promise;
        results.push({ name, success: true, data: result });
      } catch (error) {
        const errorInfo = PosErrorHandler.handleApiError(error, {
          showMessage: false, // 批量处理时不立即显示消息
          context: `批量操作: ${name}`
        });
        
        errors.push({ name, error: errorInfo });
        results.push({ name, success: false, error: errorInfo });
        
        if (!continueOnError) {
          break;
        }
      }
    }

    // 显示批量操作摘要
    if (showSummary && errors.length > 0) {
      const successCount = results.filter(r => r.success).length;
      const errorCount = errors.length;
      
      if (errorCount === apiCalls.length) {
        message.error(`批量操作全部失败 (${errorCount}项)`);
      } else if (errorCount > 0) {
        message.warning(`批量操作部分失败 (成功${successCount}项，失败${errorCount}项)`);
      }
    }

    return {
      results,
      errors,
      successCount: results.filter(r => r.success).length,
      errorCount: errors.length
    };
  }

  /**
   * 获取错误类型对应的图标
   * 
   * @param {string} errorType - 错误类型
   * @returns {string} 图标名称
   */
  static getErrorIcon(errorType) {
    switch (errorType) {
      case PosErrorHandler.ERROR_TYPES.NETWORK:
        return 'wifi';
      case PosErrorHandler.ERROR_TYPES.BUSINESS:
        return 'exclamation-circle';
      case PosErrorHandler.ERROR_TYPES.VALIDATION:
        return 'warning';
      case PosErrorHandler.ERROR_TYPES.PERMISSION:
        return 'lock';
      case PosErrorHandler.ERROR_TYPES.SYSTEM:
      default:
        return 'bug';
    }
  }

  /**
   * 获取错误类型对应的颜色
   * 
   * @param {string} errorType - 错误类型
   * @returns {string} 颜色值
   */
  static getErrorColor(errorType) {
    switch (errorType) {
      case PosErrorHandler.ERROR_TYPES.NETWORK:
        return '#1890ff';
      case PosErrorHandler.ERROR_TYPES.BUSINESS:
        return '#faad14';
      case PosErrorHandler.ERROR_TYPES.VALIDATION:
        return '#faad14';
      case PosErrorHandler.ERROR_TYPES.PERMISSION:
        return '#f5222d';
      case PosErrorHandler.ERROR_TYPES.SYSTEM:
      default:
        return '#f5222d';
    }
  }

}