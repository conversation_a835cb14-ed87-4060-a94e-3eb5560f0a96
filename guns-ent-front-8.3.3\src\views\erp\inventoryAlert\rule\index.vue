<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="content-main">
        <div class="content-main-body">
          <!-- 搜索区域 -->
          <div class="search-area">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-input
                  v-model:value="searchForm.searchText"
                  placeholder="规则名称"
                  allow-clear
                  @press-enter="handleSearch"
                >
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="searchForm.ruleTypeFilter"
                  placeholder="预警类型"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option
                    v-for="option in alertTypeOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="searchForm.alertLevelFilter"
                  placeholder="预警级别"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option
                    v-for="option in alertLevelOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    <a-tag :color="option.color">{{ option.label }}</a-tag>
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="searchForm.isEnabledFilter"
                  placeholder="启用状态"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">停用</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="handleSearch">
                    <SearchOutlined />
                    搜索
                  </a-button>
                  <a-button @click="handleReset">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button type="primary" @click="handleAdd">
                    <PlusOutlined />
                    新增规则
                  </a-button>
                  <a-button @click="handleExecuteCheck">
                    <PlayCircleOutlined />
                    执行检查
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>

          <!-- 表格区域 -->
          <div class="table-area">
            <a-table
              :columns="columns"
              :data-source="ruleList"
              :loading="ruleLoading"
              :pagination="pagination"
              row-key="id"
              :row-selection="rowSelection"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'ruleType'">
                  <a-tag :color="getAlertTypeInfo(record.ruleType).color">
                    {{ getAlertTypeInfo(record.ruleType).name }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'alertLevel'">
                  <a-tag :color="getAlertLevelInfo(record.alertLevel).color">
                    <template #icon>
                      <component :is="getAlertLevelIcon(record.alertLevel)" />
                    </template>
                    {{ getAlertLevelInfo(record.alertLevel).name }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'targetType'">
                  {{ getTargetTypeName(record.targetType) }}
                </template>
                <template v-else-if="column.key === 'thresholdValue'">
                  {{ formatThresholdValue(record.thresholdValue, record.thresholdType) }}
                </template>
                <template v-else-if="column.key === 'isEnabled'">
                  <a-switch
                    :checked="record.isEnabled === 'Y'"
                    @change="(checked) => handleStatusChange(record, checked)"
                  />
                </template>
                <template v-else-if="column.key === 'lastCheckTime'">
                  {{ record.lastCheckTime ? dayjs(record.lastCheckTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleEdit(record)">
                      编辑
                    </a-button>
                    <a-button type="link" size="small" @click="handleTest(record)">
                      测试
                    </a-button>
                    <a-button type="link" size="small" danger @click="handleDelete(record)">
                      删除
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 批量操作区域 -->
          <div v-if="selectedRowKeys.length > 0" class="batch-actions">
            <a-space>
              <span>已选择 {{ selectedRowKeys.length }} 项</span>
              <a-button danger @click="handleBatchDelete">
                批量删除
              </a-button>
              <a-button @click="clearSelection">
                取消选择
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <alert-rule-form
      v-model:visible="formVisible"
      :form-data="currentRule"
      @submit="handleFormSubmit"
      @cancel="handleFormCancel"
    />

    <!-- 测试结果弹窗 -->
    <alert-test-result
      v-model:visible="testVisible"
      :data="testResult"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useInventoryAlertStore } from '@/stores/modules/erp/inventoryAlert';
import { InventoryAlertRuleApi } from '../api/InventoryAlertRuleApi';
import { InventoryAlertApi } from '@/api/erp/inventoryAlert';
import AlertRuleForm from '../components/AlertRuleForm.vue';
import AlertTestResult from '../components/AlertTestResult.vue';

export default {
  name: 'InventoryAlertRuleIndex',
  components: {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    PlayCircleOutlined,
    ExclamationCircleOutlined,
    WarningOutlined,
    InfoCircleOutlined,
    QuestionCircleOutlined,
    AlertRuleForm,
    AlertTestResult
  },
  setup() {
    const alertStore = useInventoryAlertStore();

    // 响应式数据
    const searchForm = reactive({
      searchText: '',
      ruleTypeFilter: undefined,
      alertLevelFilter: undefined,
      isEnabledFilter: undefined
    });

    const formVisible = ref(false);
    const testVisible = ref(false);
    const currentRule = ref(null);
    const testResult = ref([]);
    const selectedRowKeys = ref([]);

    // 选项数据
    const alertTypeOptions = InventoryAlertApi.getAlertTypeOptions();
    const alertLevelOptions = InventoryAlertApi.getAlertLevelOptions();
    const targetTypeOptions = InventoryAlertApi.getTargetTypeOptions();

    // 表格列定义
    const columns = [
      {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        width: 200,
        ellipsis: true
      },
      {
        title: '预警类型',
        dataIndex: 'ruleType',
        key: 'ruleType',
        width: 120
      },
      {
        title: '预警级别',
        dataIndex: 'alertLevel',
        key: 'alertLevel',
        width: 100
      },
      {
        title: '目标类型',
        dataIndex: 'targetType',
        key: 'targetType',
        width: 120
      },
      {
        title: '阈值',
        dataIndex: 'thresholdValue',
        key: 'thresholdValue',
        width: 100
      },
      {
        title: '检查频率(分钟)',
        dataIndex: 'checkFrequency',
        key: 'checkFrequency',
        width: 120
      },
      {
        title: '启用状态',
        dataIndex: 'isEnabled',
        key: 'isEnabled',
        width: 100
      },
      {
        title: '最后检查时间',
        dataIndex: 'lastCheckTime',
        key: 'lastCheckTime',
        width: 160
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ];

    // 计算属性
    const ruleList = computed(() => alertStore.ruleList);
    const ruleLoading = computed(() => alertStore.ruleLoading);
    const ruleTotal = computed(() => alertStore.ruleTotal);

    const pagination = computed(() => ({
      current: 1,
      pageSize: 20,
      total: ruleTotal.value,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    }));

    // 行选择配置
    const rowSelection = computed(() => ({
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys) => {
        selectedRowKeys.value = keys;
      }
    }));

    // 方法
    const loadData = (params = {}) => {
      const queryParams = {
        pageNo: 1,
        pageSize: 20,
        ...searchForm,
        ...params
      };
      alertStore.loadRuleList(queryParams);
    };

    const handleSearch = () => {
      loadData();
    };

    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = undefined;
      });
      loadData();
    };

    const handleAdd = () => {
      currentRule.value = null;
      formVisible.value = true;
    };

    const handleEdit = async (record) => {
      try {
        // 获取详细信息
        const response = await InventoryAlertRuleApi.detail({ id: record.id });
        currentRule.value = response.data;
        formVisible.value = true;
      } catch (error) {
        console.error('获取预警规则详情失败:', error);
        message.error('获取详情失败，请重试');
      }
    };

    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除预警规则"${record.ruleName}"吗？`,
        icon: ExclamationCircleOutlined,
        onOk: async () => {
          try {
            await InventoryAlertRuleApi.delete({ id: record.id });
            message.success('删除成功');
            loadData();
          } catch (error) {
            message.error('删除失败');
          }
        }
      });
    };

    const handleBatchDelete = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要删除的记录');
        return;
      }

      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 条预警规则吗？`,
        icon: ExclamationCircleOutlined,
        onOk: async () => {
          try {
            await InventoryAlertRuleApi.batchDelete({ idList: selectedRowKeys.value });
            message.success('批量删除成功');
            clearSelection();
            loadData();
          } catch (error) {
            message.error('批量删除失败');
          }
        }
      });
    };

    const clearSelection = () => {
      selectedRowKeys.value = [];
    };

    const handleStatusChange = async (record, checked) => {
      try {
        await InventoryAlertRuleApi.updateStatus({
          id: record.id,
          isEnabled: checked ? 'Y' : 'N'
        });
        message.success('状态更新成功');
        alertStore.updateRuleStatus(record.id, checked ? 'Y' : 'N');
      } catch (error) {
        message.error('状态更新失败');
      }
    };

    const handleTest = async (record) => {
      try {
        const result = await InventoryAlertRuleApi.testRule({ id: record.id });
        testResult.value = result;
        testVisible.value = true;
      } catch (error) {
        message.error('测试失败');
      }
    };

    const handleExecuteCheck = async () => {
      try {
        await InventoryAlertRuleApi.executeCheck();
        message.success('预警检查已开始执行');
      } catch (error) {
        message.error('执行预警检查失败');
      }
    };

    const handleTableChange = (pagination) => {
      loadData({
        pageNo: pagination.current,
        pageSize: pagination.pageSize
      });
    };

    const handleFormSubmit = async (formData) => {
      try {
        if (formData.id) {
          // 编辑
          await InventoryAlertRuleApi.edit(formData);
          message.success('编辑预警规则成功');
        } else {
          // 新增
          await InventoryAlertRuleApi.add(formData);
          message.success('新增预警规则成功');
        }
        formVisible.value = false;
        loadData();
      } catch (error) {
        console.error('保存预警规则失败:', error);
        message.error('保存失败，请重试');
      }
    };

    const handleFormCancel = () => {
      formVisible.value = false;
    };

    // 工具方法
    const getAlertTypeInfo = (type) => {
      return InventoryAlertApi.getAlertTypeInfo(type);
    };

    const getAlertLevelInfo = (level) => {
      return InventoryAlertApi.getAlertLevelInfo(level);
    };

    const getTargetTypeName = (type) => {
      return InventoryAlertApi.getTargetTypeName(type);
    };

    const formatThresholdValue = (value, type) => {
      return InventoryAlertApi.formatThresholdValue(value, type);
    };

    const getAlertLevelIcon = (level) => {
      const iconName = InventoryAlertApi.getAlertLevelIcon(level);
      const iconMap = {
        'exclamation-circle': ExclamationCircleOutlined,
        'warning': WarningOutlined,
        'info-circle': InfoCircleOutlined,
        'question-circle': QuestionCircleOutlined
      };
      return iconMap[iconName] || QuestionCircleOutlined;
    };

    // 生命周期
    onMounted(() => {
      loadData();
    });

    return {
      // 响应式数据
      searchForm,
      formVisible,
      testVisible,
      currentRule,
      testResult,
      selectedRowKeys,
      
      // 选项数据
      alertTypeOptions,
      alertLevelOptions,
      
      // 表格相关
      columns,
      ruleList,
      ruleLoading,
      pagination,
      rowSelection,
      
      // 方法
      handleSearch,
      handleReset,
      handleAdd,
      handleEdit,
      handleDelete,
      handleBatchDelete,
      clearSelection,
      handleStatusChange,
      handleTest,
      handleExecuteCheck,
      handleTableChange,
      handleFormSubmit,
      handleFormCancel,
      getAlertTypeInfo,
      getAlertLevelInfo,
      getTargetTypeName,
      formatThresholdValue,
      getAlertLevelIcon,
      
      // 工具
      dayjs
    };
  }
};
</script>

<style scoped>
.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
}

.table-area {
  background: #fff;
  border-radius: 6px;
}

.batch-actions {
  margin-top: 16px;
  padding: 12px 16px;
  background: #f0f2f5;
  border-radius: 6px;
}
</style>
