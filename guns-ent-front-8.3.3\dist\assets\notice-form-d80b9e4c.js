import{_ as G,s as P,r as s,a as v,f,w as o,d as t,aR as g,g as m,aS as b,b as J,h as w,j as Q,T as W,G as X,l as Z,u as h,v as ee,z as te,A as oe,al as ne,$ as ae,B as le,n as ie,I as se,i as re,H as ue}from"./index-18a1ea24.js";import{_ as ce}from"./index-3a0e5c06.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";import"./OrgApi-021dd6dd.js";const me={class:"notice-button"},de={__name:"notice-form",props:{form:Object},setup(l){const i=l,z=P({noticeTitle:[{required:!0,message:"\u8BF7\u8F93\u5165\u901A\u77E5\u6807\u9898",type:"string",trigger:"blur"}],priorityLevel:[{required:!0,message:"\u8BF7\u9009\u62E9\u4F18\u5148\u7EA7",type:"string",trigger:"blur"}],noticeContent:[{required:!0,message:"\u8BF7\u8F93\u5165\u901A\u77E5\u5185\u5BB9",type:"string",trigger:"blur"}]}),d=s("1"),r=s("1"),B=s([{title:"\u7528\u6237\u540D\u79F0",dataIndex:"name"},{title:"\u7528\u6237id",dataIndex:"id"},{title:"\u64CD\u4F5C",dataIndex:"action",width:50,align:"center"}]),D=s([{title:"\u90E8\u95E8\u540D\u79F0",dataIndex:"name"},{title:"\u90E8\u95E8id",dataIndex:"id"},{title:"\u64CD\u4F5C",dataIndex:"action",width:50,align:"center"}]),_=s(!1),y=s(""),U=s({selectOrgList:[],selectUserList:[]}),x=s(["user"]),Y=a=>{i.form.noticeUserScope.pointUserList.splice(a,1)},$=a=>{i.form.noticeUserScope.pointOrgList.splice(a,1)},H=()=>{r.value=="1"?(y.value="\u9009\u62E9\u7528\u6237",x.value=["user"],U.value.selectUserList=i.form.noticeUserScope.pointUserList.map(a=>({bizId:a.id,name:a.name}))):(y.value="\u9009\u62E9\u90E8\u95E8",x.value=["dept"],U.value.selectOrgList=i.form.noticeUserScope.pointOrgList.map(a=>({bizId:a.id,name:a.name}))),_.value=!0},N=a=>{r.value=="1"?i.form.noticeUserScope.pointUserList=a.selectUserList.map(e=>({id:e.bizId,name:e.name})):i.form.noticeUserScope.pointOrgList=a.selectOrgList.map(e=>({id:e.bizId,name:e.name}))},F=()=>{r.value=="1"?i.form.noticeUserScope.pointUserList=[]:i.form.noticeUserScope.pointOrgList=[]};return(a,e)=>{const p=Q,C=W,L=X,M=Z,u=h,c=ee,S=te,R=oe,I=ne,V=ae,T=le,q=ie,K=se,O=re,E=ce,j=ue;return v(),f(j,{ref:"formRef",model:l.form,rules:z,layout:"vertical"},{default:o(()=>[t(L,null,{default:o(()=>[t(C,{activeKey:d.value,"onUpdate:activeKey":e[0]||(e[0]=n=>d.value=n),animated:"",class:"right-tab"},{default:o(()=>[t(p,{key:"1",tab:"\u901A\u77E5\u5185\u5BB9"}),t(p,{key:"2",tab:"\u901A\u77E5\u8303\u56F4"})]),_:1},8,["activeKey"])]),_:1}),g(t(L,{gutter:20},{default:o(()=>[t(c,{xs:24,sm:24,md:12},{default:o(()=>[t(u,{label:"\u901A\u77E5\u6807\u9898:",name:"noticeTitle"},{default:o(()=>[t(M,{value:l.form.noticeTitle,"onUpdate:value":e[1]||(e[1]=n=>l.form.noticeTitle=n),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u901A\u77E5\u6807\u9898"},null,8,["value"])]),_:1})]),_:1}),t(c,{xs:24,sm:24,md:12},{default:o(()=>[t(u,{label:"\u4F18\u5148\u7EA7",name:"priorityLevel"},{default:o(()=>[t(R,{value:l.form.priorityLevel,"onUpdate:value":e[2]||(e[2]=n=>l.form.priorityLevel=n),name:"sex"},{default:o(()=>[t(S,{value:"high"},{default:o(()=>e[8]||(e[8]=[m("\u9AD8")])),_:1,__:[8]}),t(S,{value:"middle"},{default:o(()=>e[9]||(e[9]=[m("\u4E2D")])),_:1,__:[9]}),t(S,{value:"low"},{default:o(()=>e[10]||(e[10]=[m("\u4F4E")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1})]),_:1}),t(c,{xs:24,sm:24,md:12},{default:o(()=>[t(u,{label:"\u5F00\u59CB\u65E5\u671F:",name:"noticeBeginTime"},{default:o(()=>[t(I,{value:l.form.noticeBeginTime,"onUpdate:value":e[3]||(e[3]=n=>l.form.noticeBeginTime=n),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u8BF7\u9009\u62E9\u5F00\u59CB\u65E5\u671F",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),t(c,{xs:24,sm:24,md:12},{default:o(()=>[t(u,{label:"\u7ED3\u675F\u65E5\u671F:",name:"noticeEndTime"},{default:o(()=>[t(I,{value:l.form.noticeEndTime,"onUpdate:value":e[4]||(e[4]=n=>l.form.noticeEndTime=n),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u675F\u65E5\u671F",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),t(c,{span:24},{default:o(()=>[t(u,{label:"\u901A\u77E5\u5185\u5BB9",name:"noticeContent"},{default:o(()=>[t(V,{value:l.form.noticeContent,"onUpdate:value":e[5]||(e[5]=n=>l.form.noticeContent=n),rows:4},null,8,["value"])]),_:1})]),_:1})]),_:1},512),[[b,d.value=="1"]]),g(t(L,{gutter:20},{default:o(()=>[t(C,{activeKey:r.value,"onUpdate:activeKey":e[6]||(e[6]=n=>r.value=n),animated:"",class:"right-tab"},{default:o(()=>[t(p,{key:"1",tab:"\u7528\u6237"}),t(p,{key:"2",tab:"\u90E8\u95E8"})]),_:1},8,["activeKey"]),J("div",me,[t(q,null,{default:o(()=>[t(T,{class:"border-radius",type:"primary",onClick:H},{default:o(()=>e[11]||(e[11]=[m("+ \u65B0\u589E")])),_:1,__:[11]}),t(T,{class:"border-radius",danger:"",onClick:F},{default:o(()=>e[12]||(e[12]=[m("\u6E05\u7A7A")])),_:1,__:[12]})]),_:1})]),g(t(O,{dataSource:i.form.noticeUserScope.pointUserList,columns:B.value,pagination:!1,rowKey:"id",bordered:"",size:"small",scroll:{y:"400"}},{bodyCell:o(({column:n,index:k})=>[n.dataIndex=="action"?(v(),f(K,{key:0,iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:A=>Y(k)},null,8,["onClick"])):w("",!0)]),_:1},8,["dataSource","columns"]),[[b,r.value=="1"]]),g(t(O,{dataSource:i.form.noticeUserScope.pointOrgList,columns:D.value,pagination:!1,rowKey:"id",bordered:"",size:"small",scroll:{y:"400"}},{bodyCell:o(({column:n,index:k})=>[n.dataIndex=="action"?(v(),f(K,{key:0,iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:A=>$(k)},null,8,["onClick"])):w("",!0)]),_:1},8,["dataSource","columns"]),[[b,r.value=="2"]])]),_:1},512),[[b,d.value=="2"]]),_.value?(v(),f(E,{key:0,visible:_.value,"onUpdate:visible":e[7]||(e[7]=n=>_.value=n),title:y.value,data:U.value,isRadio:!1,showTab:x.value,onDone:N},null,8,["visible","title","data","showTab"])):w("",!0)]),_:1},8,["model","rules"])}}},Se=G(de,[["__scopeId","data-v-447f9d19"]]);export{Se as default};
