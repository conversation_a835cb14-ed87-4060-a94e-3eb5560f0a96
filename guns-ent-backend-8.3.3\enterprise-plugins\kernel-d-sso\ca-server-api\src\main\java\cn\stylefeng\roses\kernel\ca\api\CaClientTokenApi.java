package cn.stylefeng.roses.kernel.ca.api;

import cn.stylefeng.roses.kernel.ca.api.pojo.CaClientInfo;

/**
 * 统一认证中心，客户端token管理的接口
 *
 * <AUTHOR>
 * @date 2022/5/20 10:22
 */
public interface CaClientTokenApi {

    /**
     * 添加一个客户端token
     *
     * @param userCaToken  统一认证中心用户的token
     * @param caClientInfo 客户端token，单点跳转到客户端系统时生成
     * <AUTHOR>
     * @date 2022/5/20 10:23
     */
    void addClientToken(String userCaToken, CaClientInfo caClientInfo);

    /**
     * 退出所有客户端token
     *
     * @param userCaToken 单点登录唯一id，用户单点成功后生成
     * <AUTHOR>
     * @date 2022/5/20 10:24
     */
    void logoutClientTokens(String userCaToken);

}
