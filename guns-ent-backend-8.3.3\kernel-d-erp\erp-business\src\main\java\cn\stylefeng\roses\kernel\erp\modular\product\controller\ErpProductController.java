package cn.stylefeng.roses.kernel.erp.modular.product.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.modular.product.service.ErpProductService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 商品主档案控制器
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
@RestController
@ApiResource(name = "商品主档案管理", requiredPermission = true, requirePermissionCode = "ERP_PRODUCT")
public class ErpProductController {

    @Resource
    private ErpProductService erpProductService;

    /**
     * 新增商品
     * <p>
     * 支持关联产品分类，通过categoryIds参数指定关联的分类ID列表
     */
    @PostResource(name = "新增商品", path = "/erp/product/add")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_PRODUCT_MANAGE)
    public ResponseData<?> add(@RequestBody @Validated(ErpProductRequest.add.class) ErpProductRequest erpProductRequest) {
        erpProductService.add(erpProductRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除商品
     */
    @PostResource(name = "删除商品", path = "/erp/product/delete")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_PRODUCT_MANAGE)
    public ResponseData<?> delete(@RequestBody @Validated(ErpProductRequest.delete.class) ErpProductRequest erpProductRequest) {
        erpProductService.del(erpProductRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除商品
     */
    @PostResource(name = "批量删除商品", path = "/erp/product/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody ErpProductRequest erpProductRequest) {
        erpProductService.batchDelete(erpProductRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑商品
     * <p>
     * 支持更新关联产品分类，通过categoryIds参数指定关联的分类ID列表
     */
    @PostResource(name = "编辑商品", path = "/erp/product/edit")
    public ResponseData<?> edit(@RequestBody @Validated(ErpProductRequest.edit.class) ErpProductRequest erpProductRequest) {
        erpProductService.edit(erpProductRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查询商品详情
     * <p>
     * 返回结果中包含关联的产品分类信息
     */
    @GetResource(name = "查询商品详情", path = "/erp/product/detail")
    public ResponseData<ErpProductResponse> detail(@Validated(ErpProductRequest.detail.class) ErpProductRequest erpProductRequest) {
        ErpProductResponse response = erpProductService.detail(erpProductRequest);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询商品列表
     */
    @GetResource(name = "分页查询商品列表", path = "/erp/product/page", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.PAGE_PRODUCT)
    public ResponseData<PageResult<ErpProductResponse>> page(ErpProductRequest erpProductRequest) {
        PageResult<ErpProductResponse> pageResult = erpProductService.findPage(erpProductRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询商品列表
     */
    @GetResource(name = "查询商品列表", path = "/erp/product/list", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.LIST_PRODUCT)
    public ResponseData<List<ErpProductResponse>> list(ErpProductRequest erpProductRequest) {
        List<ErpProductResponse> responseList = erpProductService.findList(erpProductRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 更新商品状态
     */
    @PostResource(name = "更新商品状态", path = "/erp/product/updateStatus")
    public ResponseData<?> updateStatus(@RequestBody @Validated(ErpProductRequest.updateStatus.class) ErpProductRequest erpProductRequest) {
        erpProductService.updateStatus(erpProductRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 校验商品编码是否重复
     */
    @GetResource(name = "校验商品编码", path = "/erp/product/validateCode")
    public ResponseData<Boolean> validateCode(ErpProductRequest erpProductRequest) {
        boolean isRepeat = erpProductService.validateProductCodeRepeat(
                erpProductRequest.getProductCode(), 
                erpProductRequest.getProductId()
        );
        return new SuccessResponseData<>(!isRepeat);
    }

    /**
     * 校验条形码是否重复
     */
    @GetResource(name = "校验条形码", path = "/erp/product/validateBarcode")
    public ResponseData<Boolean> validateBarcode(ErpProductRequest erpProductRequest) {
        boolean isRepeat = erpProductService.validateBarcodeRepeat(
                erpProductRequest.getBarcode(), 
                erpProductRequest.getProductId()
        );
        return new SuccessResponseData<>(!isRepeat);
    }



    /**
     * 根据分类ID查询商品列表
     * <p>
     * 查询指定分类及其子分类下的所有商品
     */
    @GetResource(name = "根据分类ID查询商品列表", path = "/erp/product/listByCategoryId")
    public ResponseData<List<ErpProductResponse>> listByCategoryId(@Validated ErpProductRequest erpProductRequest) {
        List<ErpProductResponse> responseList = erpProductService.findListByCategoryId(erpProductRequest.getCategoryId());
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 根据供应商查询商品列表
     * <p>
     * 查询指定供应商下的所有商品
     */
    @GetResource(name = "根据供应商查询商品列表", path = "/erp/product/listBySupplier")
    public ResponseData<List<ErpProductResponse>> listBySupplier(@Validated(ErpProductRequest.bySupplier.class) ErpProductRequest erpProductRequest) {
        List<ErpProductResponse> responseList = erpProductService.findListBySupplierId(erpProductRequest.getSupplierId());
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 校验计价类型变更的影响
     * <p>
     * 检查变更计价类型对现有销售记录和库存的影响
     */
    @GetResource(name = "校验计价类型变更", path = "/erp/product/validatePricingTypeChange")
    public ResponseData<cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse> validatePricingTypeChange(
            @Validated(ErpProductRequest.validatePricingTypeChange.class) ErpProductRequest erpProductRequest) {
        cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse response = 
                erpProductService.validatePricingTypeChange(erpProductRequest.getProductId(), erpProductRequest.getPricingType());
        return new SuccessResponseData<>(response);
    }

}
