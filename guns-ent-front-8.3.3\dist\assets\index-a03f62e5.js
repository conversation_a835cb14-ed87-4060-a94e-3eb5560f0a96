import{R as P,r as S,L as w,s as be,m as s,M as ue,b2 as Oe,b3 as Se,o as de,aL as me,_ as Ae,a as Z,c as ce,b as l,d as f,I as V,t as k,at as o,w as O,aq as we,g as M,f as le,b4 as De,b5 as _e,ad as Me,b6 as xe,h as Re,b7 as re,a2 as Ne,B as Ee,a7 as ke,n as Te}from"./index-18a1ea24.js";import Ue from"./ProductDisplayArea-5b66b134.js";import{C as ie,P as se,u as Fe,S as We}from"./ShoppingCart-3699cb13.js";import Le from"./ToolbarPanel-6eb7f826.js";import qe from"./OrderSuspend-3ebe2459.js";import Be from"./PaymentPanel-5a383353.js";import{u as ee}from"./pos-8a64eaa6.js";import{a as He,P as $e}from"./performance-monitor-659a8228.js";import{a as C,b as p}from"./constants-2fa70699.js";import{A as je}from"./formatter-5a06da9d.js";/* empty css               */import"./CartHeader-ed27259f.js";import"./CartItem-343bbd45.js";/* empty css              */import"./CartSummary-686b7994.js";import"./FunctionButton-592779e0.js";import"./OrderSummary-ec4b928f.js";import"./SuspendedOrderItem-54ab7d24.js";import"./CashPayment-9bc7318e.js";class x{static createApiWrapper(e,r={}){const{context:y="Payment API\u8C03\u7528",showMessage:I=!0,showNotification:b=!0,retryOptions:u={maxRetries:0}}=r,c=He.measureApiCall(y,e);return $e.wrapApiCall(c,{showMessage:I,showNotification:b,context:y,retryOptions:u})}static async processCashPayment(e){const r=()=>P.post("/erp/pos/payment/cash",{...e,paymentMethod:C.CASH,paymentTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u5904\u7406\u73B0\u91D1\u652F\u4ED8",retryOptions:{maxRetries:0}})()}static async processQrCodePayment(e){const r=()=>P.post("/erp/pos/payment/qrcode",{...e,paymentTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u5904\u7406\u626B\u7801\u652F\u4ED8",retryOptions:{maxRetries:0}})()}static async queryQrCodePaymentStatus(e){const r=()=>P.get("/erp/pos/payment/qrcode/status",e);return this.createApiWrapper(r,{context:"\u67E5\u8BE2\u626B\u7801\u652F\u4ED8\u72B6\u6001",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:3,retryDelay:2e3}})()}static async processMemberCardPayment(e){const r=()=>P.post("/erp/pos/payment/member",{...e,paymentMethod:C.MEMBER,paymentTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u5904\u7406\u4F1A\u5458\u5361\u652F\u4ED8",retryOptions:{maxRetries:0}})()}static async processPointsPayment(e){const r=()=>P.post("/erp/pos/payment/points",{...e,paymentMethod:C.POINTS,paymentTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u5904\u7406\u79EF\u5206\u652F\u4ED8",retryOptions:{maxRetries:0}})()}static async processBankCardPayment(e){const r=()=>P.post("/erp/pos/payment/bankcard",{...e,paymentMethod:C.CARD,paymentTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u5904\u7406\u94F6\u884C\u5361\u652F\u4ED8",retryOptions:{maxRetries:0}})()}static async processComboPayment(e){const r=()=>P.post("/erp/pos/payment/combo",{...e,paymentTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u5904\u7406\u7EC4\u5408\u652F\u4ED8",retryOptions:{maxRetries:0}})()}static async confirmPaymentSuccess(e){const r=()=>P.post("/erp/pos/payment/confirm",{...e,confirmTime:e.confirmTime||new Date().toISOString()});return this.createApiWrapper(r,{context:"\u786E\u8BA4\u652F\u4ED8\u6210\u529F",retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async cancelPayment(e){const r=()=>P.post("/erp/pos/payment/cancel",{...e,cancelTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u53D6\u6D88\u652F\u4ED8",retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async requestRefund(e){const r=()=>P.post("/erp/pos/payment/refund",{...e,refundTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u7533\u8BF7\u9000\u6B3E",retryOptions:{maxRetries:1,retryDelay:2e3}})()}static async queryRefundStatus(e){const r=()=>P.get("/erp/pos/payment/refund/status",e);return this.createApiWrapper(r,{context:"\u67E5\u8BE2\u9000\u6B3E\u72B6\u6001",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:3,retryDelay:2e3}})()}static async getPaymentRecords(e={}){const r=()=>P.get("/erp/pos/payment/records",e);return this.createApiWrapper(r,{context:"\u83B7\u53D6\u652F\u4ED8\u8BB0\u5F55",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async getPaymentStatistics(e={}){const r=()=>P.get("/erp/pos/payment/statistics",e);return this.createApiWrapper(r,{context:"\u83B7\u53D6\u652F\u4ED8\u7EDF\u8BA1",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async validatePaymentPassword(e){const r=()=>P.post("/erp/pos/payment/validatePassword",e);return this.createApiWrapper(r,{context:"\u9A8C\u8BC1\u652F\u4ED8\u5BC6\u7801",showMessage:!1,retryOptions:{maxRetries:0}})()}static async getPaymentConfig(e={}){const r=()=>P.get("/erp/pos/payment/config",e);return this.createApiWrapper(r,{context:"\u83B7\u53D6\u652F\u4ED8\u914D\u7F6E",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async testPaymentConnection(e){const r=()=>P.post("/erp/pos/payment/test",e);return this.createApiWrapper(r,{context:"\u6D4B\u8BD5\u652F\u4ED8\u8FDE\u63A5",retryOptions:{maxRetries:1,retryDelay:3e3}})()}static async syncPaymentStatus(e){const r=()=>P.post("/erp/pos/payment/sync",e);return this.createApiWrapper(r,{context:"\u540C\u6B65\u652F\u4ED8\u72B6\u6001",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:2e3}})()}static async batchProcessPayments(e){const r=()=>P.post("/erp/pos/payment/batch",{...e,processTime:new Date().toISOString()});return this.createApiWrapper(r,{context:"\u6279\u91CF\u5904\u7406\u652F\u4ED8",retryOptions:{maxRetries:1,retryDelay:3e3}})()}}function Qe(){const i=ee(),e=S(p.UNPAID),r=S(""),y=S(0),I=S(0),b=w(()=>r.value===C.CASH?ie.calculateChange(I.value,y.value):0),u=be({success:!1,paymentId:"",transactionId:"",message:"",timestamp:null}),c=S(!1),A=S({enabledMethods:[],limits:{},settings:{}}),T=w(()=>A.value.enabledMethods.map(t=>({value:t,label:_(t),icon:Q(t),enabled:!0,limit:A.value.limits[t]}))),q=w(()=>y.value>0&&r.value&&e.value===p.UNPAID&&!c.value),B=async(t,a={})=>{try{const n=se.validatePaymentAmount(t);return n.isValid?(y.value=t,e.value=p.UNPAID,r.value="",I.value=0,Object.assign(u,{success:!1,paymentId:"",transactionId:"",message:"",timestamp:null}),await z(),!0):(s.error(n.message),!1)}catch(n){return console.error("\u521D\u59CB\u5316\u652F\u4ED8\u5931\u8D25:",n),s.error("\u521D\u59CB\u5316\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}},K=t=>{if(!A.value.enabledMethods.includes(t))return s.error("\u8BE5\u652F\u4ED8\u65B9\u5F0F\u4E0D\u53EF\u7528"),!1;const a=A.value.limits[t];return a&&a.max&&y.value>a.max?(s.error("".concat(_(t),"\u5355\u7B14\u9650\u989D\u4E3A").concat(a.max,"\u5143")),!1):(r.value=t,t===C.CASH&&(I.value=y.value),!0)},R=async(t={})=>{var a;try{c.value=!0,e.value=p.PROCESSING;const n={orderId:t.orderId||i.currentOrderId,paymentAmount:y.value,receivedAmount:I.value,changeAmount:b.value,cashierId:t.cashierId||((a=i.currentUser)==null?void 0:a.id)},m=se.validateCashPayment(n);if(!m.isValid)return s.error(m.message),e.value=p.UNPAID,!1;const v=await x.processCashPayment(n);return v.success?(Object.assign(u,{success:!0,paymentId:v.paymentId,transactionId:v.transactionId,message:"\u73B0\u91D1\u652F\u4ED8\u6210\u529F",timestamp:new Date().toISOString()}),e.value=p.PAID,s.success("\u73B0\u91D1\u652F\u4ED8\u6210\u529F"),!0):(e.value=p.UNPAID,s.error(v.message||"\u73B0\u91D1\u652F\u4ED8\u5931\u8D25"),!1)}catch(n){return console.error("\u73B0\u91D1\u652F\u4ED8\u5931\u8D25:",n),e.value=p.UNPAID,s.error("\u73B0\u91D1\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{c.value=!1}},H=async(t={})=>{var a;try{c.value=!0,e.value=p.PROCESSING;const n={orderId:t.orderId||i.currentOrderId,paymentAmount:y.value,paymentMethod:r.value,qrCode:t.qrCode||"",cashierId:t.cashierId||((a=i.currentUser)==null?void 0:a.id)},m=se.validateQrCodePayment(n);if(!m.isValid)return s.error(m.message),e.value=p.UNPAID,!1;const v=await x.processQrCodePayment(n);return v.success?await $(v.paymentId)?(Object.assign(u,{success:!0,paymentId:v.paymentId,transactionId:v.transactionId,message:"".concat(_(r.value),"\u652F\u4ED8\u6210\u529F"),timestamp:new Date().toISOString()}),e.value=p.PAID,s.success("".concat(_(r.value),"\u652F\u4ED8\u6210\u529F")),!0):(e.value=p.UNPAID,s.error("\u652F\u4ED8\u5931\u8D25\u6216\u5DF2\u53D6\u6D88"),!1):(e.value=p.UNPAID,s.error(v.message||"\u53D1\u8D77\u652F\u4ED8\u5931\u8D25"),!1)}catch(n){return console.error("\u626B\u7801\u652F\u4ED8\u5931\u8D25:",n),e.value=p.UNPAID,s.error("\u626B\u7801\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{c.value=!1}},N=async(t={})=>{var a;try{c.value=!0,e.value=p.PROCESSING;const n={orderId:t.orderId||i.currentOrderId,paymentAmount:y.value,memberId:t.memberId,memberCardNo:t.memberCardNo,cashierId:t.cashierId||((a=i.currentUser)==null?void 0:a.id)};if(!n.memberId||!n.memberCardNo)return s.error("\u8BF7\u5148\u9009\u62E9\u4F1A\u5458"),e.value=p.UNPAID,!1;const m=await x.processMemberCardPayment(n);return m.success?(Object.assign(u,{success:!0,paymentId:m.paymentId,transactionId:m.transactionId,message:"\u4F1A\u5458\u5361\u652F\u4ED8\u6210\u529F",timestamp:new Date().toISOString()}),e.value=p.PAID,s.success("\u4F1A\u5458\u5361\u652F\u4ED8\u6210\u529F\uFF0C\u4F59\u989D\uFF1A".concat(m.remainingBalance,"\u5143")),!0):(e.value=p.UNPAID,s.error(m.message||"\u4F1A\u5458\u5361\u652F\u4ED8\u5931\u8D25"),!1)}catch(n){return console.error("\u4F1A\u5458\u5361\u652F\u4ED8\u5931\u8D25:",n),e.value=p.UNPAID,s.error("\u4F1A\u5458\u5361\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{c.value=!1}},U=async(t,a={})=>{var n;try{c.value=!0,e.value=p.PROCESSING;const m=t.reduce((E,W)=>E+W.amount,0);if(Math.abs(m-y.value)>.01)return s.error("\u7EC4\u5408\u652F\u4ED8\u91D1\u989D\u4E0D\u5339\u914D"),e.value=p.UNPAID,!1;const v={orderId:a.orderId||i.currentOrderId,totalAmount:y.value,paymentMethods:t,cashierId:a.cashierId||((n=i.currentUser)==null?void 0:n.id)},d=await x.processComboPayment(v);return d.success?(Object.assign(u,{success:!0,paymentId:d.paymentId,transactionId:d.transactionId,message:"\u7EC4\u5408\u652F\u4ED8\u6210\u529F",timestamp:new Date().toISOString()}),e.value=p.PAID,s.success("\u7EC4\u5408\u652F\u4ED8\u6210\u529F"),!0):(e.value=p.UNPAID,s.error(d.message||"\u7EC4\u5408\u652F\u4ED8\u5931\u8D25"),!1)}catch(m){return console.error("\u7EC4\u5408\u652F\u4ED8\u5931\u8D25:",m),e.value=p.UNPAID,s.error("\u7EC4\u5408\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{c.value=!1}},$=async(t,a=30)=>{let n=0;return new Promise(m=>{const v=async()=>{try{n++;const d=await x.queryQrCodePaymentStatus({paymentId:t,outTradeNo:"".concat(i.currentOrderId,"_").concat(t)});if(d.status==="SUCCESS"){m(!0);return}if(d.status==="FAILED"||d.status==="CANCELLED"){m(!1);return}if(n>=a){s.warning("\u652F\u4ED8\u8D85\u65F6\uFF0C\u8BF7\u68C0\u67E5\u652F\u4ED8\u72B6\u6001"),m(!1);return}setTimeout(v,2e3)}catch(d){console.error("\u67E5\u8BE2\u652F\u4ED8\u72B6\u6001\u5931\u8D25:",d),n>=a?m(!1):setTimeout(v,2e3)}};v()})},j=async(t="\u7528\u6237\u53D6\u6D88")=>{var a;try{return u.paymentId&&await x.cancelPayment({paymentId:u.paymentId,cancelReason:t,cancelBy:(a=i.currentUser)==null?void 0:a.id}),e.value=p.UNPAID,r.value="",I.value=0,Object.assign(u,{success:!1,paymentId:"",transactionId:"",message:"",timestamp:null}),s.info("\u652F\u4ED8\u5DF2\u53D6\u6D88"),!0}catch(n){return console.error("\u53D6\u6D88\u652F\u4ED8\u5931\u8D25:",n),s.error("\u53D6\u6D88\u652F\u4ED8\u5931\u8D25"),!1}},F=async t=>{var a;try{c.value=!0;const n={paymentId:t.paymentId||u.paymentId,refundAmount:t.refundAmount||y.value,refundReason:t.refundReason||"\u5BA2\u6237\u8981\u6C42\u9000\u6B3E",refundBy:t.refundBy||((a=i.currentUser)==null?void 0:a.id)},m=await x.requestRefund(n);return m.success?(s.success("\u9000\u6B3E\u7533\u8BF7\u5DF2\u63D0\u4EA4"),!0):(s.error(m.message||"\u9000\u6B3E\u7533\u8BF7\u5931\u8D25"),!1)}catch(n){return console.error("\u7533\u8BF7\u9000\u6B3E\u5931\u8D25:",n),s.error("\u7533\u8BF7\u9000\u6B3E\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{c.value=!1}},z=async()=>{var t;try{const a=await x.getPaymentConfig({storeId:(t=i.currentStore)==null?void 0:t.id});A.value={enabledMethods:a.enabledMethods||Object.values(C),limits:a.limits||{},settings:a.settings||{}}}catch(a){console.error("\u52A0\u8F7D\u652F\u4ED8\u914D\u7F6E\u5931\u8D25:",a),A.value={enabledMethods:Object.values(C),limits:{},settings:{}}}},D=async t=>{var a;try{return(await x.validatePaymentPassword({password:t,cashierId:(a=i.currentUser)==null?void 0:a.id})).valid}catch(n){return console.error("\u9A8C\u8BC1\u652F\u4ED8\u5BC6\u7801\u5931\u8D25:",n),!1}},G=(t={})=>new Promise(a=>{ue.confirm({title:t.title||"\u786E\u8BA4\u652F\u4ED8",content:t.content||"\u786E\u8BA4\u652F\u4ED8 ".concat(y.value," \u5143\uFF1F"),okText:"\u786E\u8BA4\u652F\u4ED8",cancelText:"\u53D6\u6D88",onOk:()=>a(!0),onCancel:()=>a(!1)})}),_=t=>({[C.CASH]:"\u73B0\u91D1",[C.WECHAT]:"\u5FAE\u4FE1\u652F\u4ED8",[C.ALIPAY]:"\u652F\u4ED8\u5B9D",[C.MEMBER]:"\u4F1A\u5458\u5361",[C.CARD]:"\u94F6\u884C\u5361",[C.POINTS]:"\u79EF\u5206\u652F\u4ED8"})[t]||t,Q=t=>({[C.CASH]:"money-collect",[C.WECHAT]:"wechat",[C.ALIPAY]:"alipay",[C.MEMBER]:"credit-card",[C.CARD]:"bank",[C.POINTS]:"gift"})[t]||"pay-circle";return{paymentStatus:e,selectedPaymentMethod:r,paymentAmount:y,receivedAmount:I,changeAmount:b,paymentResult:u,loading:c,paymentConfig:A,availablePaymentMethods:T,canPay:q,initializePayment:B,selectPaymentMethod:K,processCashPayment:R,processQrCodePayment:H,processMemberCardPayment:N,processComboPayment:U,cancelPayment:j,requestRefund:F,validatePaymentPassword:D,showPaymentConfirmDialog:G,getPaymentMethodLabel:_,getPaymentMethodIcon:Q,formatChangeDenominations:t=>ie.calculateChangeDenominations(t)}}function Ve(){const i=ee();return{selectMember:b=>{i.setCurrentMember(b)},clearMember:()=>{i.clearCurrentMember()},applyMemberDiscount:b=>{i.applyMemberDiscount(b)},clearMemberDiscount:()=>{i.clearMemberDiscount()}}}function Ke(){const i=ee();return{suspendOrder:async u=>{try{return await i.suspendCurrentOrder(u)}catch(c){throw console.error("\u6302\u8D77\u8BA2\u5355\u5931\u8D25:",c),c}},resumeOrder:async u=>{try{return await i.resumeSuspendedOrder(u)}catch(c){throw console.error("\u6062\u590D\u8BA2\u5355\u5931\u8D25:",c),c}},deleteOrder:async u=>{try{return await i.deleteSuspendedOrder(u),!0}catch(c){throw console.error("\u5220\u9664\u8BA2\u5355\u5931\u8D25:",c),c}},createOrder:async u=>{try{return await i.createOrder(u)}catch(c){throw console.error("\u521B\u5EFA\u8BA2\u5355\u5931\u8D25:",c),c}},getOrderList:async(u={})=>{try{return await i.getOrderList(u)}catch(c){throw console.error("\u83B7\u53D6\u8BA2\u5355\u5217\u8868\u5931\u8D25:",c),c}}}}function ze(){const i=Oe(),e=ee(),r=Se(),{addItem:y,updateQuantity:I,removeItem:b,clearCart:u,recalculateTotal:c}=Fe(),{processPayment:A,resetPaymentStatus:T}=Qe();Ve();const{suspendOrder:q,resumeOrder:B,deleteOrder:K}=Ke(),R=S(!1),H=S(!1),N=S(!1),U=S(!1),$=S(""),j=S(""),F=S(null),z=w(()=>r.userInfo||{}),D=w(()=>e.getCartSummary()),G=w(()=>e.hasMember),_=w(()=>e.currentMember),Q=w(()=>e.suspendedOrders.length),Y=w(()=>e.hasCartItems),t=w(()=>e.canCheckout),a=w(()=>({itemCount:D.value.itemCount,totalAmount:D.value.totalAmount,discountAmount:D.value.discountAmount,pointsDeductionAmount:D.value.pointsDeductionAmount,finalAmount:D.value.finalAmount,items:e.cartItems,member:_.value})),n=()=>{const h=new Date;$.value=h.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),j.value=h.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},m=()=>{document.fullscreenElement?(document.exitFullscreen(),R.value=!1):(document.documentElement.requestFullscreen(),R.value=!0)},v=()=>{H.value=!0},d=()=>{e.resetAllState(),T(),s.success("\u5DF2\u91CD\u7F6E\u6240\u6709\u72B6\u6001")},E=()=>{r.logout(),i.push("/login")},W=h=>je.formatCurrency(h||0),X=async h=>{try{await y(h,1),s.success("\u5DF2\u6DFB\u52A0 ".concat(h.name))}catch(g){console.error("\u6DFB\u52A0\u5546\u54C1\u5931\u8D25:",g),s.error("\u6DFB\u52A0\u5546\u54C1\u5931\u8D25: "+g.message)}},te=h=>{X(h)},L=h=>{const{type:g,itemId:ae,quantity:ne,item:J}=h;try{switch(g){case"update":I(ae,ne);break;case"remove":b(ae),s.success("\u5DF2\u79FB\u9664 ".concat((J==null?void 0:J.name)||"\u5546\u54C1"));break;case"add":y(J,ne);break}}catch(oe){console.error("\u8D2D\u7269\u8F66\u64CD\u4F5C\u5931\u8D25:",oe),s.error("\u64CD\u4F5C\u5931\u8D25: "+oe.message)}},pe=()=>{u(),s.success("\u5DF2\u6E05\u7A7A\u8D2D\u7269\u8F66")},ye=()=>{if(!t.value){s.warning("\u8D2D\u7269\u8F66\u4E3A\u7A7A\u6216\u6B63\u5728\u5904\u7406\u4E2D\uFF0C\u65E0\u6CD5\u7ED3\u8D26");return}if(!Y.value){s.warning("\u8D2D\u7269\u8F66\u4E3A\u7A7A\uFF0C\u8BF7\u5148\u6DFB\u52A0\u5546\u54C1");return}N.value=!0},fe=()=>{U.value=!0},he=()=>{s.info("\u4F1A\u5458\u7BA1\u7406\u529F\u80FD")},Ce=async h=>{try{await q(h),s.success("\u8BA2\u5355\u5DF2\u6302\u8D77"),u()}catch(g){console.error("\u6302\u8D77\u8BA2\u5355\u5931\u8D25:",g),s.error("\u6302\u8D77\u8BA2\u5355\u5931\u8D25: "+g.message)}},ve=async h=>{try{const g=await B(h);s.success("\u8BA2\u5355\u5DF2\u6062\u590D"),U.value=!1,g&&g.cartItems&&e.restoreCartFromData(g)}catch(g){console.error("\u6062\u590D\u8BA2\u5355\u5931\u8D25:",g),s.error("\u6062\u590D\u8BA2\u5355\u5931\u8D25: "+g.message)}},ge=async h=>{try{await K(h),s.success("\u8BA2\u5355\u5DF2\u5220\u9664")}catch(g){console.error("\u5220\u9664\u8BA2\u5355\u5931\u8D25:",g),s.error("\u5220\u9664\u8BA2\u5355\u5931\u8D25: "+g.message)}},Pe=async h=>{try{s.success("\u652F\u4ED8\u6210\u529F\uFF01"),u(),N.value=!1,T(),console.log("\u652F\u4ED8\u6210\u529F:",h)}catch(g){console.error("\u5904\u7406\u652F\u4ED8\u6210\u529F\u5931\u8D25:",g),s.error("\u5904\u7406\u652F\u4ED8\u7ED3\u679C\u5931\u8D25")}},Ie=()=>{N.value=!1,T(),s.info("\u5DF2\u53D6\u6D88\u652F\u4ED8")};return de(()=>{n(),F.value=setInterval(n,1e3);const h=()=>{R.value=!!document.fullscreenElement};return document.addEventListener("fullscreenchange",h),()=>{document.removeEventListener("fullscreenchange",h)}}),me(()=>{F.value&&clearInterval(F.value)}),{currentUser:z,currentTime:$,currentDate:j,isFullscreen:R,showHelpModal:H,showPaymentPanel:N,showSuspendedOrdersDrawer:U,cartSummary:D,hasMember:G,currentMember:_,suspendedOrdersCount:Q,hasCartItems:Y,canCheckout:t,orderInfo:a,toggleFullscreen:m,showHelp:v,resetAll:d,logout:E,formatAmount:W,handleProductAdd:X,handleProductSelect:te,handleCartChange:L,handleCartClear:pe,handleCheckout:ye,handleShowSuspendedOrders:fe,handleMemberManagement:he,handleOrderSuspended:Ce,handleOrderResumed:ve,handleOrderDeleted:ge,handlePaymentSuccess:Pe,handlePaymentCancel:Ie}}function Ge(){const i=e=>{if(!(e.target.tagName==="INPUT"||e.target.tagName==="TEXTAREA")){if(e.key==="F1"){e.preventDefault(),document.dispatchEvent(new CustomEvent("pos:showHelp"));return}if(e.key==="F11"){e.preventDefault(),document.dispatchEvent(new CustomEvent("pos:toggleFullscreen"));return}if(e.ctrlKey&&e.key==="r"){e.preventDefault(),document.dispatchEvent(new CustomEvent("pos:resetAll"));return}if(e.key==="Escape"){document.dispatchEvent(new CustomEvent("pos:cancel"));return}if(e.key==="Enter"&&e.ctrlKey){e.preventDefault(),document.dispatchEvent(new CustomEvent("pos:confirm"));return}if(e.key==="Delete"){document.dispatchEvent(new CustomEvent("pos:delete"));return}if(e.key>="0"&&e.key<="9"&&e.altKey){e.preventDefault(),document.dispatchEvent(new CustomEvent("pos:numberInput",{detail:{number:e.key}}));return}}};return de(()=>{document.addEventListener("keydown",i)}),me(()=>{document.removeEventListener("keydown",i)}),{}}const Ye={class:"pos-toolbar"},Xe={class:"toolbar-left"},Je={class:"pos-logo"},Ze={class:"cashier-info"},et={class:"cashier-name"},tt={class:"work-time"},rt={class:"toolbar-right"},st={class:"pos-content"},at={class:"pos-left pos-card"},nt={class:"pos-center"},ot={class:"cart-section pos-card"},ct={class:"pos-right"},lt={class:"pos-statusbar"},it={class:"status-left"},ut={class:"status-item"},dt={class:"status-item"},mt={key:0,class:"status-item"},pt={class:"status-right"},yt={class:"status-item"},ft={class:"status-item"},ht=Object.assign({name:"PosIndex"},{__name:"index",setup(i){const{currentUser:e,currentTime:r,currentDate:y,isFullscreen:I,showHelpModal:b,showPaymentPanel:u,showSuspendedOrdersDrawer:c,cartSummary:A,hasMember:T,currentMember:q,suspendedOrdersCount:B,orderInfo:K,toggleFullscreen:R,showHelp:H,resetAll:N,logout:U,formatAmount:$,handleProductAdd:j,handleProductSelect:F,handleCartChange:z,handleCartClear:D,handleCheckout:G,handleShowSuspendedOrders:_,handleMemberManagement:Q,handleOrderSuspended:Y,handleOrderResumed:t,handleOrderDeleted:a,handlePaymentSuccess:n,handlePaymentCancel:m}=ze();return Ge(),(v,d)=>{const E=Ee,W=ke,X=Te,te=ue;return Z(),ce("div",{class:Ne(["pos-main",{fullscreen:o(I)}])},[l("div",Ye,[l("div",Xe,[l("div",Je,[f(V,{iconClass:"icon-pos"}),d[3]||(d[3]=l("span",{class:"logo-text"},"POS\u6536\u94F6\u7CFB\u7EDF",-1))]),l("div",Ze,[l("span",et,"\u6536\u94F6\u5458: "+k(o(e).realName),1),l("span",tt,k(o(r)),1)])]),l("div",rt,[f(X,null,{default:O(()=>[f(W,{title:"\u5FEB\u6377\u952E: F1"},{default:O(()=>[f(E,{onClick:o(H),class:"toolbar-btn"},{icon:O(()=>[f(o(we))]),default:O(()=>[d[4]||(d[4]=M(" \u5E2E\u52A9 "))]),_:1,__:[4]},8,["onClick"])]),_:1}),f(W,{title:"\u5FEB\u6377\u952E: F11"},{default:O(()=>[f(E,{onClick:o(R),class:"toolbar-btn"},{icon:O(()=>[o(I)?(Z(),le(o(_e),{key:1})):(Z(),le(o(De),{key:0}))]),default:O(()=>[M(" "+k(o(I)?"\u9000\u51FA\u5168\u5C4F":"\u5168\u5C4F"),1)]),_:1},8,["onClick"])]),_:1}),f(W,{title:"\u5FEB\u6377\u952E: Ctrl+R"},{default:O(()=>[f(E,{onClick:o(N),class:"toolbar-btn"},{icon:O(()=>[f(o(Me))]),default:O(()=>[d[5]||(d[5]=M(" \u91CD\u7F6E "))]),_:1,__:[5]},8,["onClick"])]),_:1}),f(E,{onClick:o(U),type:"primary",danger:"",class:"logout-btn"},{icon:O(()=>[f(o(xe))]),default:O(()=>[d[6]||(d[6]=M(" \u9000\u51FA\u767B\u5F55 "))]),_:1,__:[6]},8,["onClick"])]),_:1})])]),l("div",st,[l("div",at,[f(Ue,{onProductAdd:o(j),onProductSelect:o(F)},null,8,["onProductAdd","onProductSelect"])]),l("div",nt,[l("div",ot,[f(We,{onCartChange:o(z),onCartClear:o(D),onCheckout:o(G)},null,8,["onCartChange","onCartClear","onCheckout"])])]),l("div",ct,[f(Le,{"suspended-orders-count":o(B),onShowSuspendedOrders:o(_),onMemberManagement:o(Q)},null,8,["suspended-orders-count","onShowSuspendedOrders","onMemberManagement"])])]),l("div",lt,[l("div",it,[l("span",ut,[f(V,{iconClass:"icon-cart"}),M(" \u8D2D\u7269\u8F66: "+k(o(A).itemCount)+"\u4EF6 ",1)]),l("span",dt,[f(V,{iconClass:"icon-money"}),M(" \u91D1\u989D: "+k(o($)(o(A).finalAmount)),1)]),o(T)?(Z(),ce("span",mt,[f(V,{iconClass:"icon-member"}),M(" \u4F1A\u5458: "+k(o(q).memberName),1)])):Re("",!0)]),l("div",pt,[l("span",yt,[f(V,{iconClass:"icon-suspend"}),M(" \u6302\u5355: "+k(o(B))+"\u4E2A ",1)]),l("span",ft,[f(V,{iconClass:"icon-time"}),M(" "+k(o(y)),1)])])]),f(qe,{visible:o(c),"onUpdate:visible":d[0]||(d[0]=L=>re(c)?c.value=L:null),onOrderSuspended:o(Y),onOrderResumed:o(t),onOrderDeleted:o(a)},null,8,["visible","onOrderSuspended","onOrderResumed","onOrderDeleted"]),f(Be,{visible:o(u),"onUpdate:visible":d[1]||(d[1]=L=>re(u)?u.value=L:null),"order-info":o(K),"member-info":o(q),onPaymentSuccess:o(n),onPaymentCancel:o(m)},null,8,["visible","order-info","member-info","onPaymentSuccess","onPaymentCancel"]),f(te,{open:o(b),"onUpdate:open":d[2]||(d[2]=L=>re(b)?b.value=L:null),title:"\u5FEB\u6377\u952E\u5E2E\u52A9",footer:null,width:"600px",class:"help-modal"},{default:O(()=>d[7]||(d[7]=[l("div",{class:"help-content"},[l("div",{class:"help-section"},[l("h4",null,"\u57FA\u672C\u64CD\u4F5C"),l("div",{class:"help-item"},[l("span",{class:"key"},"F1"),l("span",{class:"desc"},"\u663E\u793A\u5E2E\u52A9")]),l("div",{class:"help-item"},[l("span",{class:"key"},"F11"),l("span",{class:"desc"},"\u5207\u6362\u5168\u5C4F")]),l("div",{class:"help-item"},[l("span",{class:"key"},"Ctrl+R"),l("span",{class:"desc"},"\u91CD\u7F6E\u6240\u6709\u72B6\u6001")])])],-1)])),_:1,__:[7]},8,["open"])],2)}}}),Ut=Ae(ht,[["__scopeId","data-v-3ccb510b"]]);export{Ut as default};
