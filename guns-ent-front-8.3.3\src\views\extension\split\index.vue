<template>
  <div class="guns-layout" style="padding: 0 12px">
    <guns-split-layout
      space="0px"
      :allow-collapse="allowCollapse"
      :resizable="resizable"
      :vertical="vertical"
      :reverse="reverse"
      :min-size="40"
      style="height: 480px; margin-top: 12px"
    >
      <org-tree @treeSelect="treeSelect" ref="orgTreeRef" style="padding: 12px" />
      <template #content>
        <div class="guns-layout-content">
          <div class="guns-layout">
            <div class="guns-layout-content-application">
              <div class="content-mian">
                <div class="content-mian-header">
                  <div class="header-content">
                    <div class="header-content-left">
                      <a-space :size="16">
                        <a-input
                          v-model:value="where.searchText"
                          placeholder="机构名称、编码（回车搜索）"
                          @pressEnter="reload"
                          class="search-input"
                        >
                          <template #prefix>
                            <icon-font iconClass="icon-opt-search" />
                          </template>
                        </a-input>
                        <a-button class="border-radius" @click="clear">重置</a-button>
                      </a-space>
                    </div>
                    <div class="header-content-right"></div>
                  </div>
                </div>
                <div class="content-mian-body">
                  <div class="table-content">
                    <common-table :columns="columns" :where="where" rowId="orgId" ref="tableRef" url="/hrOrganization/page">
                      <template #bodyCell="{ column, record }">
                        <!-- 机构状态 -->
                        <template v-if="column.dataIndex == 'statusFlag'">
                          <a-tag color="green" v-if="record.statusFlag == 1">启用</a-tag>
                          <a-tag color="red" v-if="record.statusFlag == 2">禁用</a-tag>
                        </template>
                        <template v-if="column.dataIndex == 'orgType'">
                          <div class="org-type">
                            <a-tag color="green" v-if="record.orgType == 1">公司</a-tag>
                            <a-tag color="red" v-if="record.orgType == 2">部门</a-tag>
                          </div>
                        </template>
                      </template>
                    </common-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </guns-split-layout>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent } from 'vue';

const OrgTree = defineAsyncComponent(() => import('@/views/system/structure/user/org-tree.vue'));

const allowCollapse = ref(true);

const resizable = ref(true);

const vertical = ref(false);

const reverse = ref(false);

// 表格配置
const columns = ref([
  {
    key: 'index',
    title: '序号',
    width: 48,
    align: 'center',
    isShow: true,
    hideInSetting: true
  },
  {
    dataIndex: 'orgName',
    title: '机构名称',
    ellipsis: true,
    width: 200,
    isShow: true
  },
  {
    dataIndex: 'orgCode',
    title: '机构编码',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'statusFlag',
    title: '机构状态',
    ellipsis: true,
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'orgType',
    title: '机构类型',
    ellipsis: true,
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'orgSort',
    title: '排序',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'createTime',
    title: '创建时间',
    width: 150,
    isShow: true
  },
]);
// ref
const tableRef = ref(null);

const where = ref({
  orgId: null,
  statusFlag: '',
  searchText: ''
});

// 当前选中的组织机构id
const parentId = ref(null);

const parentName = ref(null);

// 左侧树选中
const treeSelect = (selectedKeys, metadata) => {
  where.value.orgId = selectedKeys[0];
  parentId.value = selectedKeys[0];
  parentName.value = metadata.node.orgName;
  reload();
};

// 点击搜索
const reload = () => {
  tableRef.value.reload();
};
</script>

<style lang="less" scoped></style>
