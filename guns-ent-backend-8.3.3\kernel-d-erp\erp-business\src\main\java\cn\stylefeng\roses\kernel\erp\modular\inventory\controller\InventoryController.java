package cn.stylefeng.roses.kernel.erp.modular.inventory.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryOperationRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import lombok.extern.slf4j.Slf4j;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 库存管理控制器
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@Slf4j
@RestController
@ApiResource(name = "库存管理", requiredPermission = true, requirePermissionCode = "ERP_INVENTORY")
public class InventoryController {

    @Resource
    private InventoryService inventoryService;

    /**
     * 分页查询库存列表
     */
    @PostResource(name = "分页查询库存列表", path = "/erp/inventory/page")
    public ResponseData<PageResult<InventoryResponse>> page(@RequestBody InventoryQueryRequest inventoryQueryRequest) {
        PageResult<InventoryResponse> pageResult = inventoryService.findPage(inventoryQueryRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询库存列表
     */
    @GetResource(name = "查询库存列表", path = "/erp/inventory/list")
    public ResponseData<List<InventoryResponse>> list(InventoryQueryRequest inventoryQueryRequest) {
        List<InventoryResponse> responseList = inventoryService.findList(inventoryQueryRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 查询商品库存详情
     */
    @GetResource(name = "查询商品库存详情", path = "/erp/inventory/detail")
    public ResponseData<InventoryResponse> detail(InventoryQueryRequest inventoryQueryRequest) {
        InventoryResponse response = inventoryService.detail(inventoryQueryRequest.getProductId());
        return new SuccessResponseData<>(response);
    }

    /**
     * 查询预警库存列表
     */
    @GetResource(name = "查询预警库存列表", path = "/erp/inventory/warning")
    public ResponseData<List<InventoryResponse>> warningList(InventoryQueryRequest inventoryQueryRequest) {
        List<InventoryResponse> responseList = inventoryService.findWarningList(inventoryQueryRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 查询缺货商品列表
     */
    @GetResource(name = "查询缺货商品列表", path = "/erp/inventory/outOfStock")
    public ResponseData<List<InventoryResponse>> outOfStockList(InventoryQueryRequest inventoryQueryRequest) {
        List<InventoryResponse> responseList = inventoryService.findOutOfStockList(inventoryQueryRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 查询库存价值统计
     */
    @GetResource(name = "查询库存价值统计", path = "/erp/inventory/value")
    public ResponseData<InventoryValueResponse> inventoryValue(InventoryQueryRequest inventoryQueryRequest) {
        InventoryValueResponse response = inventoryService.getInventoryValue(inventoryQueryRequest);
        return new SuccessResponseData<>(response);
    }

    /**
     * 设置库存预警值
     */
    @PostResource(name = "设置库存预警值", path = "/erp/inventory/setMinStock")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_MANAGE)
    public ResponseData<?> setMinStock(@RequestBody @Validated InventoryRequest inventoryRequest) {
        inventoryService.setMinStock(inventoryRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 库存调整
     */
    @PostResource(name = "库存调整", path = "/erp/inventory/adjust")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_MANAGE)
    public ResponseData<?> adjustInventory(@RequestBody @Validated InventoryOperationRequest inventoryOperationRequest) {
        inventoryService.adjustInventory(inventoryOperationRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 初始化商品库存
     */
    @PostResource(name = "初始化商品库存", path = "/erp/inventory/init")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_MANAGE)
    public ResponseData<?> initInventory(@RequestBody @Validated InventoryRequest inventoryRequest) {
        inventoryService.initInventory(inventoryRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量初始化商品库存
     */
    @PostResource(name = "批量初始化商品库存", path = "/erp/inventory/batchInit")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_MANAGE)
    public ResponseData<?> batchInitInventory(@RequestBody List<InventoryRequest> inventoryRequestList) {
        inventoryService.batchInitInventory(inventoryRequestList);
        return new SuccessResponseData<>();
    }


}