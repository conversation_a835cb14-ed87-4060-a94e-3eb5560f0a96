import{_ as K,r as b,s as E,o as h,a as U,c as T,d as l,w as t,g as s,b as d,F as I,h as M,m as f,a8 as j,V as O,a9 as q,u as L,y as V,l as B,aa as z,J,W as Y,B as H,n as W,H as G,a0 as Q,C as X,ab as Z,$,M as ee}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */import{I as R}from"./inventoryAlert-17fb61ca.js";const ae={name:"InventoryAlertConfigIndex",setup(){const N=b(),e=b(),x=b(!1),a=b(!1),P=b(["SYSTEM"]),w=b("\u8FD9\u662F\u4E00\u6761\u6D4B\u8BD5\u9884\u8B66\u901A\u77E5\u6D88\u606F"),m=E({enableAlert:!0,globalCheckFrequency:30,maxCheckProducts:1e3,alertRecordRetentionDays:90,enableEmailNotify:!1,smtpServer:"",smtpPort:587,senderEmail:"",emailPassword:"",defaultRecipients:[],enableSmsNotify:!1,smsProvider:"",smsAccessKey:"",smsSecretKey:"",smsTemplateId:"",defaultPhones:[],alertDeduplicationTime:60,batchProcessSize:100,enableDebugMode:!1}),v=E({globalCheckFrequency:[{required:!0,message:"\u8BF7\u8F93\u5165\u5168\u5C40\u68C0\u67E5\u9891\u7387",trigger:"blur"},{type:"number",min:1,max:1440,message:"\u68C0\u67E5\u9891\u7387\u5FC5\u987B\u57281-1440\u5206\u949F\u4E4B\u95F4",trigger:"blur"}],maxCheckProducts:[{required:!0,message:"\u8BF7\u8F93\u5165\u6700\u5927\u68C0\u67E5\u5546\u54C1\u6570",trigger:"blur"},{type:"number",min:100,max:1e4,message:"\u6700\u5927\u68C0\u67E5\u5546\u54C1\u6570\u5FC5\u987B\u5728100-10000\u4E4B\u95F4",trigger:"blur"}],alertRecordRetentionDays:[{required:!0,message:"\u8BF7\u8F93\u5165\u9884\u8B66\u8BB0\u5F55\u4FDD\u7559\u5929\u6570",trigger:"blur"},{type:"number",min:7,max:365,message:"\u4FDD\u7559\u5929\u6570\u5FC5\u987B\u57287-365\u5929\u4E4B\u95F4",trigger:"blur"}],smtpServer:[{validator:(r,i)=>m.enableEmailNotify&&!i?Promise.reject("\u8BF7\u8F93\u5165SMTP\u670D\u52A1\u5668\u5730\u5740"):Promise.resolve(),trigger:"blur"}],senderEmail:[{validator:(r,i)=>m.enableEmailNotify&&!i?Promise.reject("\u8BF7\u8F93\u5165\u53D1\u4EF6\u4EBA\u90AE\u7BB1"):i&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i)?Promise.reject("\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u683C\u5F0F"):Promise.resolve(),trigger:"blur"}]}),g=async()=>{try{const r=await R.getConfig();Object.assign(m,r.data)}catch(r){console.error("\u83B7\u53D6\u914D\u7F6E\u5931\u8D25:",r),f.error("\u83B7\u53D6\u914D\u7F6E\u5931\u8D25")}},o=async()=>{try{await N.value.validate(),x.value=!0,await R.updateConfig(m),f.success("\u914D\u7F6E\u4FDD\u5B58\u6210\u529F")}catch(r){console.error("\u4FDD\u5B58\u914D\u7F6E\u5931\u8D25:",r),f.error("\u4FDD\u5B58\u914D\u7F6E\u5931\u8D25")}finally{x.value=!1}},u=()=>{g()},p=()=>{a.value=!0},S=async()=>{try{await R.testNotify({types:P.value,message:w.value}),f.success("\u6D4B\u8BD5\u901A\u77E5\u53D1\u9001\u6210\u529F"),a.value=!1}catch(r){console.error("\u6D4B\u8BD5\u901A\u77E5\u5931\u8D25:",r),f.error("\u6D4B\u8BD5\u901A\u77E5\u5931\u8D25")}},y=()=>{try{const r=new Blob([JSON.stringify(m,null,2)],{type:"application/json"}),i=URL.createObjectURL(r),c=document.createElement("a");c.href=i,c.download="\u5E93\u5B58\u9884\u8B66\u914D\u7F6E_".concat(new Date().getTime(),".json"),c.click(),URL.revokeObjectURL(i),f.success("\u914D\u7F6E\u5BFC\u51FA\u6210\u529F")}catch(r){console.error("\u5BFC\u51FA\u914D\u7F6E\u5931\u8D25:",r),f.error("\u5BFC\u51FA\u914D\u7F6E\u5931\u8D25")}},D=()=>{e.value.click()},_=r=>{const i=r.target.files[0];if(!i)return;const c=new FileReader;c.onload=k=>{try{const C=JSON.parse(k.target.result);Object.assign(m,C),f.success("\u914D\u7F6E\u5BFC\u5165\u6210\u529F")}catch(C){console.error("\u5BFC\u5165\u914D\u7F6E\u5931\u8D25:",C),f.error("\u914D\u7F6E\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF")}},c.readAsText(i),r.target.value=""};return h(()=>{g()}),{formRef:N,importFileRef:e,submitLoading:x,testNotifyVisible:a,testNotifyTypes:P,testMessage:w,configData:m,rules:v,handleSubmit:o,handleReset:u,handleTestNotify:p,handleTestNotifyConfirm:S,handleExportConfig:y,handleImportConfig:D,handleImportFileChange:_}}},le={class:"inventory-alert-config-container"};function ne(N,e,x,a,P,w){const m=j,v=O,g=q,o=L,u=V,p=B,S=z,y=J,D=Y,_=H,r=W,i=G,c=Q,k=X,C=Z,A=$,F=ee;return U(),T("div",le,[l(m,{title:"\u5E93\u5B58\u9884\u8B66\u914D\u7F6E","sub-title":"\u914D\u7F6E\u5E93\u5B58\u9884\u8B66\u7CFB\u7EDF\u7684\u5168\u5C40\u53C2\u6570\u548C\u901A\u77E5\u8BBE\u7F6E"}),l(c,{bordered:!1,class:"config-card"},{default:t(()=>[l(i,{ref:"formRef",model:a.configData,rules:a.rules,"label-col":{span:6},"wrapper-col":{span:12},onFinish:a.handleSubmit},{default:t(()=>[l(v,{orientation:"left"},{default:t(()=>e[23]||(e[23]=[s("\u57FA\u7840\u914D\u7F6E")])),_:1,__:[23]}),l(o,{label:"\u542F\u7528\u9884\u8B66\u68C0\u67E5",name:"enableAlert"},{default:t(()=>[l(g,{checked:a.configData.enableAlert,"onUpdate:checked":e[0]||(e[0]=n=>a.configData.enableAlert=n),"checked-children":"\u542F\u7528","un-checked-children":"\u7981\u7528"},null,8,["checked"]),e[24]||(e[24]=d("div",{class:"form-item-desc"}," \u63A7\u5236\u662F\u5426\u542F\u7528\u81EA\u52A8\u9884\u8B66\u68C0\u67E5\u529F\u80FD ",-1))]),_:1,__:[24]}),l(o,{label:"\u5168\u5C40\u68C0\u67E5\u9891\u7387",name:"globalCheckFrequency"},{default:t(()=>[l(u,{value:a.configData.globalCheckFrequency,"onUpdate:value":e[1]||(e[1]=n=>a.configData.globalCheckFrequency=n),min:1,max:1440,style:{width:"200px"},"addon-after":"\u5206\u949F"},null,8,["value"]),e[25]||(e[25]=d("div",{class:"form-item-desc"}," \u5168\u5C40\u9884\u8B66\u68C0\u67E5\u9891\u7387\uFF0C\u5355\u4F4D\uFF1A\u5206\u949F\uFF081-1440\u5206\u949F\uFF09 ",-1))]),_:1,__:[25]}),l(o,{label:"\u6700\u5927\u68C0\u67E5\u5546\u54C1\u6570",name:"maxCheckProducts"},{default:t(()=>[l(u,{value:a.configData.maxCheckProducts,"onUpdate:value":e[2]||(e[2]=n=>a.configData.maxCheckProducts=n),min:100,max:1e4,style:{width:"200px"},"addon-after":"\u4E2A"},null,8,["value"]),e[26]||(e[26]=d("div",{class:"form-item-desc"}," \u5355\u6B21\u68C0\u67E5\u7684\u6700\u5927\u5546\u54C1\u6570\u91CF\uFF0C\u907F\u514D\u7CFB\u7EDF\u8D1F\u8F7D\u8FC7\u9AD8 ",-1))]),_:1,__:[26]}),l(o,{label:"\u9884\u8B66\u8BB0\u5F55\u4FDD\u7559\u5929\u6570",name:"alertRecordRetentionDays"},{default:t(()=>[l(u,{value:a.configData.alertRecordRetentionDays,"onUpdate:value":e[3]||(e[3]=n=>a.configData.alertRecordRetentionDays=n),min:7,max:365,style:{width:"200px"},"addon-after":"\u5929"},null,8,["value"]),e[27]||(e[27]=d("div",{class:"form-item-desc"}," \u9884\u8B66\u8BB0\u5F55\u5728\u7CFB\u7EDF\u4E2D\u7684\u4FDD\u7559\u5929\u6570\uFF0C\u8D85\u671F\u81EA\u52A8\u6E05\u7406 ",-1))]),_:1,__:[27]}),l(v,{orientation:"left"},{default:t(()=>e[28]||(e[28]=[s("\u901A\u77E5\u914D\u7F6E")])),_:1,__:[28]}),l(o,{label:"\u542F\u7528\u90AE\u4EF6\u901A\u77E5",name:"enableEmailNotify"},{default:t(()=>[l(g,{checked:a.configData.enableEmailNotify,"onUpdate:checked":e[4]||(e[4]=n=>a.configData.enableEmailNotify=n),"checked-children":"\u542F\u7528","un-checked-children":"\u7981\u7528"},null,8,["checked"])]),_:1}),a.configData.enableEmailNotify?(U(),T(I,{key:0},[l(o,{label:"SMTP\u670D\u52A1\u5668",name:"smtpServer"},{default:t(()=>[l(p,{value:a.configData.smtpServer,"onUpdate:value":e[5]||(e[5]=n=>a.configData.smtpServer=n),placeholder:"\u8BF7\u8F93\u5165SMTP\u670D\u52A1\u5668\u5730\u5740"},null,8,["value"])]),_:1}),l(o,{label:"SMTP\u7AEF\u53E3",name:"smtpPort"},{default:t(()=>[l(u,{value:a.configData.smtpPort,"onUpdate:value":e[6]||(e[6]=n=>a.configData.smtpPort=n),min:1,max:65535,style:{width:"200px"}},null,8,["value"])]),_:1}),l(o,{label:"\u53D1\u4EF6\u4EBA\u90AE\u7BB1",name:"senderEmail"},{default:t(()=>[l(p,{value:a.configData.senderEmail,"onUpdate:value":e[7]||(e[7]=n=>a.configData.senderEmail=n),placeholder:"\u8BF7\u8F93\u5165\u53D1\u4EF6\u4EBA\u90AE\u7BB1"},null,8,["value"])]),_:1}),l(o,{label:"\u90AE\u7BB1\u5BC6\u7801",name:"emailPassword"},{default:t(()=>[l(S,{value:a.configData.emailPassword,"onUpdate:value":e[8]||(e[8]=n=>a.configData.emailPassword=n),placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1\u5BC6\u7801\u6216\u6388\u6743\u7801"},null,8,["value"])]),_:1}),l(o,{label:"\u9ED8\u8BA4\u6536\u4EF6\u4EBA",name:"defaultRecipients"},{default:t(()=>[l(y,{value:a.configData.defaultRecipients,"onUpdate:value":e[9]||(e[9]=n=>a.configData.defaultRecipients=n),mode:"tags",placeholder:"\u8BF7\u8F93\u5165\u6536\u4EF6\u4EBA\u90AE\u7BB1\uFF0C\u652F\u6301\u591A\u4E2A",style:{width:"100%"}},null,8,["value"]),e[29]||(e[29]=d("div",{class:"form-item-desc"}," \u9ED8\u8BA4\u7684\u9884\u8B66\u901A\u77E5\u6536\u4EF6\u4EBA\uFF0C\u53EF\u8F93\u5165\u591A\u4E2A\u90AE\u7BB1\u5730\u5740 ",-1))]),_:1,__:[29]})],64)):M("",!0),l(o,{label:"\u542F\u7528\u77ED\u4FE1\u901A\u77E5",name:"enableSmsNotify"},{default:t(()=>[l(g,{checked:a.configData.enableSmsNotify,"onUpdate:checked":e[10]||(e[10]=n=>a.configData.enableSmsNotify=n),"checked-children":"\u542F\u7528","un-checked-children":"\u7981\u7528"},null,8,["checked"])]),_:1}),a.configData.enableSmsNotify?(U(),T(I,{key:1},[l(o,{label:"\u77ED\u4FE1\u670D\u52A1\u5546",name:"smsProvider"},{default:t(()=>[l(y,{value:a.configData.smsProvider,"onUpdate:value":e[11]||(e[11]=n=>a.configData.smsProvider=n),placeholder:"\u8BF7\u9009\u62E9\u77ED\u4FE1\u670D\u52A1\u5546"},{default:t(()=>[l(D,{value:"ALIYUN"},{default:t(()=>e[30]||(e[30]=[s("\u963F\u91CC\u4E91")])),_:1,__:[30]}),l(D,{value:"TENCENT"},{default:t(()=>e[31]||(e[31]=[s("\u817E\u8BAF\u4E91")])),_:1,__:[31]}),l(D,{value:"HUAWEI"},{default:t(()=>e[32]||(e[32]=[s("\u534E\u4E3A\u4E91")])),_:1,__:[32]})]),_:1},8,["value"])]),_:1}),l(o,{label:"AccessKey",name:"smsAccessKey"},{default:t(()=>[l(p,{value:a.configData.smsAccessKey,"onUpdate:value":e[12]||(e[12]=n=>a.configData.smsAccessKey=n),placeholder:"\u8BF7\u8F93\u5165AccessKey"},null,8,["value"])]),_:1}),l(o,{label:"SecretKey",name:"smsSecretKey"},{default:t(()=>[l(S,{value:a.configData.smsSecretKey,"onUpdate:value":e[13]||(e[13]=n=>a.configData.smsSecretKey=n),placeholder:"\u8BF7\u8F93\u5165SecretKey"},null,8,["value"])]),_:1}),l(o,{label:"\u77ED\u4FE1\u6A21\u677FID",name:"smsTemplateId"},{default:t(()=>[l(p,{value:a.configData.smsTemplateId,"onUpdate:value":e[14]||(e[14]=n=>a.configData.smsTemplateId=n),placeholder:"\u8BF7\u8F93\u5165\u77ED\u4FE1\u6A21\u677FID"},null,8,["value"])]),_:1}),l(o,{label:"\u9ED8\u8BA4\u63A5\u6536\u624B\u673A\u53F7",name:"defaultPhones"},{default:t(()=>[l(y,{value:a.configData.defaultPhones,"onUpdate:value":e[15]||(e[15]=n=>a.configData.defaultPhones=n),mode:"tags",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\uFF0C\u652F\u6301\u591A\u4E2A",style:{width:"100%"}},null,8,["value"]),e[33]||(e[33]=d("div",{class:"form-item-desc"}," \u9ED8\u8BA4\u7684\u9884\u8B66\u901A\u77E5\u63A5\u6536\u624B\u673A\u53F7\uFF0C\u53EF\u8F93\u5165\u591A\u4E2A ",-1))]),_:1,__:[33]})],64)):M("",!0),l(v,{orientation:"left"},{default:t(()=>e[34]||(e[34]=[s("\u9AD8\u7EA7\u914D\u7F6E")])),_:1,__:[34]}),l(o,{label:"\u9884\u8B66\u53BB\u91CD\u65F6\u95F4",name:"alertDeduplicationTime"},{default:t(()=>[l(u,{value:a.configData.alertDeduplicationTime,"onUpdate:value":e[16]||(e[16]=n=>a.configData.alertDeduplicationTime=n),min:1,max:1440,style:{width:"200px"},"addon-after":"\u5206\u949F"},null,8,["value"]),e[35]||(e[35]=d("div",{class:"form-item-desc"}," \u76F8\u540C\u5546\u54C1\u7684\u9884\u8B66\u53BB\u91CD\u65F6\u95F4\uFF0C\u907F\u514D\u9891\u7E41\u53D1\u9001\u91CD\u590D\u9884\u8B66 ",-1))]),_:1,__:[35]}),l(o,{label:"\u6279\u91CF\u5904\u7406\u5927\u5C0F",name:"batchProcessSize"},{default:t(()=>[l(u,{value:a.configData.batchProcessSize,"onUpdate:value":e[17]||(e[17]=n=>a.configData.batchProcessSize=n),min:10,max:1e3,style:{width:"200px"},"addon-after":"\u6761"},null,8,["value"]),e[36]||(e[36]=d("div",{class:"form-item-desc"}," \u6279\u91CF\u5904\u7406\u9884\u8B66\u8BB0\u5F55\u7684\u5355\u6B21\u5904\u7406\u6570\u91CF ",-1))]),_:1,__:[36]}),l(o,{label:"\u542F\u7528\u8C03\u8BD5\u6A21\u5F0F",name:"enableDebugMode"},{default:t(()=>[l(g,{checked:a.configData.enableDebugMode,"onUpdate:checked":e[18]||(e[18]=n=>a.configData.enableDebugMode=n),"checked-children":"\u542F\u7528","un-checked-children":"\u7981\u7528"},null,8,["checked"]),e[37]||(e[37]=d("div",{class:"form-item-desc"}," \u542F\u7528\u540E\u4F1A\u8BB0\u5F55\u8BE6\u7EC6\u7684\u8C03\u8BD5\u65E5\u5FD7\uFF0C\u4FBF\u4E8E\u95EE\u9898\u6392\u67E5 ",-1))]),_:1,__:[37]}),l(o,{"wrapper-col":{offset:6,span:12}},{default:t(()=>[l(r,null,{default:t(()=>[l(_,{type:"primary","html-type":"submit",loading:a.submitLoading},{default:t(()=>e[38]||(e[38]=[s(" \u4FDD\u5B58\u914D\u7F6E ")])),_:1,__:[38]},8,["loading"]),l(_,{onClick:a.handleReset},{default:t(()=>e[39]||(e[39]=[s(" \u91CD\u7F6E ")])),_:1,__:[39]},8,["onClick"]),l(_,{onClick:a.handleTestNotify},{default:t(()=>e[40]||(e[40]=[s(" \u6D4B\u8BD5\u901A\u77E5 ")])),_:1,__:[40]},8,["onClick"]),l(_,{onClick:a.handleExportConfig},{default:t(()=>e[41]||(e[41]=[s(" \u5BFC\u51FA\u914D\u7F6E ")])),_:1,__:[41]},8,["onClick"]),l(_,{onClick:a.handleImportConfig},{default:t(()=>e[42]||(e[42]=[s(" \u5BFC\u5165\u914D\u7F6E ")])),_:1,__:[42]},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model","rules","onFinish"])]),_:1}),l(F,{visible:a.testNotifyVisible,"onUpdate:visible":e[21]||(e[21]=n=>a.testNotifyVisible=n),title:"\u6D4B\u8BD5\u901A\u77E5",onOk:a.handleTestNotifyConfirm},{default:t(()=>[l(i,{"label-col":{span:6},"wrapper-col":{span:16}},{default:t(()=>[l(o,{label:"\u901A\u77E5\u7C7B\u578B"},{default:t(()=>[l(C,{value:a.testNotifyTypes,"onUpdate:value":e[19]||(e[19]=n=>a.testNotifyTypes=n)},{default:t(()=>[l(k,{value:"EMAIL",disabled:!a.configData.enableEmailNotify},{default:t(()=>e[43]||(e[43]=[s(" \u90AE\u4EF6\u901A\u77E5 ")])),_:1,__:[43]},8,["disabled"]),l(k,{value:"SMS",disabled:!a.configData.enableSmsNotify},{default:t(()=>e[44]||(e[44]=[s(" \u77ED\u4FE1\u901A\u77E5 ")])),_:1,__:[44]},8,["disabled"]),l(k,{value:"SYSTEM"},{default:t(()=>e[45]||(e[45]=[s("\u7CFB\u7EDF\u6D88\u606F")])),_:1,__:[45]})]),_:1},8,["value"])]),_:1}),l(o,{label:"\u6D4B\u8BD5\u6D88\u606F"},{default:t(()=>[l(A,{value:a.testMessage,"onUpdate:value":e[20]||(e[20]=n=>a.testMessage=n),placeholder:"\u8BF7\u8F93\u5165\u6D4B\u8BD5\u6D88\u606F\u5185\u5BB9",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["visible","onOk"]),d("input",{ref:"importFileRef",type:"file",accept:".json",style:{display:"none"},onChange:e[22]||(e[22]=(...n)=>a.handleImportFileChange&&a.handleImportFileChange(...n))},null,544)])}const de=K(ae,[["render",ne],["__scopeId","data-v-64475a7c"]]);export{de as default};
