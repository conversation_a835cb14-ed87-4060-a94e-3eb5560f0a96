package cn.stylefeng.roses.kernel.micro.api.example.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-05-05
 */
@TableName("goods_order")
@Data
public class GoodsOrder extends Model<GoodsOrder> {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 商品名称
     */
    @TableField("goods_name")
    private String goodsName;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 总金额
     */
    private BigDecimal sum;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 下单人id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 订单状态：1.未完成   2.已完成
     */
    @TableField("status")
    private Integer status;

}
