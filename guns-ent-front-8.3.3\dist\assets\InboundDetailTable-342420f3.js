import{_ as A,L as E,a as i,c as u,d as f,w as r,b as C,t as p,h as s,f as m,g as I,y as S,l as T,B,i as U,I as N,a5 as x}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */const R={name:"InboundDetailTable",props:{value:{type:Array,default:()=>[]},supplierId:{type:[String,Number],default:null}},emits:["update:value","change"],setup(P,{emit:c}){const l=E({get:()=>P.value||[],set:t=>{c("update:value",t),c("change",t)}}),a=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:250,fixed:"left"},{title:"\u6570\u91CF",key:"quantity",width:150,align:"center"},{title:"\u5355\u4EF7",key:"unitPrice",width:120,align:"center"},{title:"\u603B\u4EF7",key:"totalPrice",width:120,align:"right"},{title:"\u5907\u6CE8",key:"remark",width:200},{title:"\u64CD\u4F5C",key:"action",width:80,fixed:"right",align:"center"}],b=t=>{switch(t){case"WEIGHT":return 3;case"NORMAL":case"PIECE":case"VARIABLE":default:return 0}},w=t=>{switch(t){case"WEIGHT":return .001;case"NORMAL":case"PIECE":case"VARIABLE":default:return 1}},_=t=>{switch(t.pricingType){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return t.unit||"\u4E2A"}},g=t=>t?parseFloat(t).toFixed(2):"0.00",d=t=>{const e=parseFloat(t.quantity)||0,y=parseFloat(t.unitPrice)||0;return e*y},h=(t,e)=>{t.totalPrice=d(t),o(e,t)},v=(t,e)=>{t.totalPrice=d(t),o(e,t)},k=(t,e)=>{o(e,t)},o=(t,e)=>{const y=[...l.value];y[t]={...e},l.value=y};return{dataSource:l,columns:a,getPrecision:b,getStep:w,getQuantityUnit:_,formatAmount:g,onQuantityChange:h,onUnitPriceChange:v,onRemarkChange:k,removeItem:t=>{const e=[...l.value];e.splice(t,1),l.value=e}}}},D={class:"inbound-detail-table"},L={key:0,class:"product-info"},V={class:"product-name"},q={class:"product-details"},F={class:"product-code"},Q={key:0,class:"product-spec"},z={key:3,class:"total-price"},G={key:0,class:"empty-state"};function H(P,c,l,a,b,w){const _=S,g=T,d=B,h=U,v=N,k=x;return i(),u("div",D,[f(h,{columns:a.columns,"data-source":a.dataSource,pagination:!1,size:"small",bordered:"",scroll:{x:1200}},{bodyCell:r(({column:o,record:n,index:t})=>[o.key==="productInfo"?(i(),u("div",L,[C("div",V,p(n.productName),1),C("div",q,[C("span",F,p(n.productCode),1),n.specification?(i(),u("span",Q,p(n.specification),1)):s("",!0)])])):s("",!0),o.key==="quantity"?(i(),m(_,{key:1,value:n.quantity,"onUpdate:value":e=>n.quantity=e,min:0,precision:a.getPrecision(n.pricingType),step:a.getStep(n.pricingType),style:{width:"100%"},onChange:e=>a.onQuantityChange(n,t)},{addonAfter:r(()=>[I(p(a.getQuantityUnit(n)),1)]),_:2},1032,["value","onUpdate:value","precision","step","onChange"])):s("",!0),o.key==="unitPrice"?(i(),m(_,{key:2,value:n.unitPrice,"onUpdate:value":e=>n.unitPrice=e,min:0,precision:2,step:.01,style:{width:"100%"},onChange:e=>a.onUnitPriceChange(n,t)},{addonBefore:r(()=>c[0]||(c[0]=[I("\xA5")])),_:2},1032,["value","onUpdate:value","onChange"])):s("",!0),o.key==="totalPrice"?(i(),u("span",z,"\xA5"+p(a.formatAmount(n.totalPrice)),1)):s("",!0),o.key==="remark"?(i(),m(g,{key:4,value:n.remark,"onUpdate:value":e=>n.remark=e,placeholder:"\u5907\u6CE8",onChange:e=>a.onRemarkChange(n,t)},null,8,["value","onUpdate:value","onChange"])):s("",!0),o.key==="action"?(i(),m(d,{key:5,type:"link",size:"small",danger:"",onClick:e=>a.removeItem(t)},{default:r(()=>c[1]||(c[1]=[I(" \u5220\u9664 ")])),_:2,__:[1]},1032,["onClick"])):s("",!0)]),_:1},8,["columns","data-source"]),!a.dataSource||a.dataSource.length===0?(i(),u("div",G,[f(k,{description:"\u6682\u65E0\u5546\u54C1\u660E\u7EC6"},{image:r(()=>[f(v,{iconClass:"icon-opt-shangpin",style:{"font-size":"48px",color:"#d9d9d9"}})]),_:1})])):s("",!0)])}const K=A(R,[["render",H],["__scopeId","data-v-3caa35a0"]]);export{K as default};
