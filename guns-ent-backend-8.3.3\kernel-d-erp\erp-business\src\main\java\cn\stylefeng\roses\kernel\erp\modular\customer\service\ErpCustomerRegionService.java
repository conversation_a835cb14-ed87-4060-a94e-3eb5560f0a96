package cn.stylefeng.roses.kernel.erp.modular.customer.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpCustomerRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;

import java.util.List;

/**
 * 客户-区域关联Service接口
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
public interface ErpCustomerRegionService {

    /**
     * 获取客户关联的区域列表
     *
     * @param erpCustomerRegionRequest 请求参数
     * @return 区域列表
     */
    List<ErpRegionResponse> getCustomerRegions(ErpCustomerRegionRequest erpCustomerRegionRequest);

    /**
     * 更新客户关联的区域
     *
     * @param erpCustomerRegionRequest 请求参数
     */
    void updateCustomerRegions(ErpCustomerRegionRequest erpCustomerRegionRequest);

    /**
     * 根据区域ID查询关联的客户
     *
     * @param erpCustomerRegionRequest 请求参数
     * @return 客户分页列表
     */
    PageResult<ErpCustomerResponse> getCustomersByRegion(ErpCustomerRegionRequest erpCustomerRegionRequest);

    /**
     * 统计区域关联的客户数量
     *
     * @param erpCustomerRegionRequest 请求参数
     * @return 客户数量
     */
    Long countCustomersByRegion(ErpCustomerRegionRequest erpCustomerRegionRequest);
}