<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.rule.pidset.mapper.CommonUpdatePidMapper">

    <update id="updateSubParentIdListString">
        UPDATE ${tableName}
        SET ${pidsFieldName} = replace(${pidsFieldName}, #{oldParentIdListString}, #{newParentIdListString}),
            update_time    = #{updateTime},
            update_user    = #{updateUser}
        WHERE ${pidsFieldName} LIKE CONCAT('%', #{oldParentIdListString}, '%')
    </update>

</mapper>