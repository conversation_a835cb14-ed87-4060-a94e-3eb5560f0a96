package cn.stylefeng.roses.kernel.impexp.org.pojo;

import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

/**
 * 机构导入过程中预览数据正确性
 *
 * <AUTHOR>
 * @since 2024/2/16 17:47
 */
@Data
public class OrgExcelImportPreview {

    /**
     * 主键
     */
    @ChineseDescription("主键")
    private ExcelLineParseResult orgId;

    /**
     * excel导入的序号
     */
    @ChineseDescription("excel导入的序号")
    private ExcelLineParseResult number;

    /**
     * 机构名称
     */
    @ChineseDescription("机构名称")
    private ExcelLineParseResult orgName;

    /**
     * 机构简称
     */
    @ChineseDescription("机构简称")
    private ExcelLineParseResult orgShortName;

    /**
     * 父级机构
     */
    @ChineseDescription("父级机构")
    private ExcelLineParseResult parentOrgName;

    /**
     * 机构编码
     */
    @ChineseDescription("机构编码")
    private ExcelLineParseResult orgCode;

    /**
     * 排序
     */
    @ChineseDescription("排序")
    private ExcelLineParseResult orgSort;

    /**
     * 状态：启用和禁用
     */
    @ChineseDescription("状态：启用和禁用")
    private ExcelLineParseResult statusFlag;

    /**
     * 机构类型
     */
    @ChineseDescription("机构类型")
    private ExcelLineParseResult orgType;

    /**
     * 税号
     */
    @ChineseDescription("税号")
    private ExcelLineParseResult taxNo;

    /**
     * 描述
     */
    @ChineseDescription("描述")
    private ExcelLineParseResult remark;

    /**
     * 对外主数据系统的机构id
     */
    @ChineseDescription("对外主数据系统的机构id")
    private ExcelLineParseResult masterOrgId;

    /**
     * 这条记录将会以什么类型录入到数据库，包括：新增、修改、删除
     */
    @ChineseDescription("这条记录将会以什么类型录入到数据库，包括：新增、修改、删除")
    private ExcelLineParseResult operateType;

}
