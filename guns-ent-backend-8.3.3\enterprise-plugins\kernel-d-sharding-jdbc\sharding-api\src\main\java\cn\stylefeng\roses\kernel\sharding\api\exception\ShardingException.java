package cn.stylefeng.roses.kernel.sharding.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.sharding.api.constants.ShardingConstants;

/**
 * sharding模块的异常
 *
 * <AUTHOR>
 * @date 2021/7/16 16:47
 */
public class ShardingException extends ServiceException {

    public ShardingException(AbstractExceptionEnum exception, Object... params) {
        super(ShardingConstants.SHARDING_SERVER_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public ShardingException(AbstractExceptionEnum exception) {
        super(ShardingConstants.SHARDING_SERVER_MODULE_NAME, exception);
    }

}
