package cn.stylefeng.roses.kernel.saas.api.expander;

import cn.stylefeng.roses.kernel.config.api.context.ConfigContext;

/**
 * 租户的配置
 *
 * <AUTHOR>
 * @since 2024-02-23 14:11
 */
public class SaasConfigExpander {

    /**
     * 获取租户的下拉选项配置
     *
     * <AUTHOR>
     * @since 2024-02-23 14:11
     */
    public static Boolean getTenantCodeSelectFlag() {
        return ConfigContext.me().getSysConfigValueWithDefault("SAAS_TENANT_CODE_SELECT_FLAG", Boolean.class, true);
    }

}
