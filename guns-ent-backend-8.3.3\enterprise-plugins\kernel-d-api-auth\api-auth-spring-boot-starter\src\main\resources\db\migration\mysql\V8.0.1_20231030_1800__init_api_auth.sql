CREATE TABLE `ent_api_client`  (
  `api_client_id` bigint NOT NULL COMMENT 'API客户端ID',
  `api_client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API客户端名称',
  `api_client_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API客户端编号',
  `api_client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API客户端秘钥明文，用来获取API调用的token',
  `api_public_key` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公钥，加密数据用',
  `api_private_key` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '私钥，解密数据用',
  `api_client_token_expiration` int NOT NULL COMMENT 'token过期时间，单位：秒',
  `api_client_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '排序',
  `api_client_status` tinyint NOT NULL COMMENT '状态：1-启用，2-禁用',
  `version_flag` bigint DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '更新人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '删除标记：Y-已删除，N-未删除',
  PRIMARY KEY (`api_client_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API客户端' ROW_FORMAT = Dynamic;

CREATE TABLE `ent_api_client_auth`  (
  `api_client_resource_id` bigint NOT NULL COMMENT '主键id',
  `api_client_id` bigint NOT NULL COMMENT 'api客户端id',
  `resource_code` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源编码，与sys_resource表编码对应',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`api_client_resource_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API客户端和资源绑定关系' ROW_FORMAT = Dynamic;

CREATE TABLE `ent_api_endpoint`  (
  `api_client_resource_id` bigint NOT NULL COMMENT '主键id',
  `resource_code` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源编码，与sys_resource表编码对应',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`api_client_resource_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API资源接口列表' ROW_FORMAT = Dynamic;

INSERT INTO `sys_app`(`app_id`, `app_name`, `app_code`, `app_icon`, `status_flag`, `app_sort`, `remark`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1717100945571057665, 'API认证', 'api_certify', 1718932070425694209, 1, 1000.00, '针对外部系统访问本系统的接口对接打通', NULL, 0, 'N', '2023-10-25 16:49:02', 1339550467939639299, NULL, NULL);

INSERT INTO `sys_file_storage`(`file_id`, `file_bytes`) VALUES (1718932070425694209, 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
INSERT INTO `sys_file_info`(`file_id`, `file_code`, `file_version`, `file_status`, `file_location`, `file_bucket`, `file_origin_name`, `file_suffix`, `file_size_kb`, `file_size_info`, `file_object_name`, `file_path`, `secret_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1718932070425694209, 1718932070425694210, 1, '1', 5, 'defaultBucket', 'API服务备份.png', 'png', 4, '4.3 KB', '1718932070425694209.png', NULL, 'N', 'N', '2023-10-30 18:05:16', 1339550467939639299, NULL, NULL);

INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1718824432245411841, -1, '[-1],', 'API认证', 'ApiRenZheng', 1717100945571057665, 100.00, 1, NULL, 10, '/apiAuth', NULL, 'icon-menu-apirenzheng', NULL, NULL, 'Y', NULL, 1, 'N', '2023-10-30 10:57:34', 1339550467939639299, '2023-10-30 11:21:58', 1339550467939639299);
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1718826323209621506, 1718824432245411841, '[-1],[1718824432245411841],', '外部应用', 'waibuyingyong', 1717100945571057665, 100.00, 1, NULL, 10, '/apiAuth/externalApp', '/api-auth/external-app/index', 'icon-menu-waibuyingyong', NULL, NULL, 'Y', NULL, 1, 'N', '2023-10-30 11:05:04', 1339550467939639299, '2023-10-30 11:22:28', 1339550467939639299);
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1718826580437897217, 1718824432245411841, '[-1],[1718824432245411841],', '接口', 'jiekou', 1717100945571057665, 110.00, 1, NULL, 10, '/apiAuth/interface', '/api-auth/interface/index', 'icon-menu-jiekou', NULL, NULL, 'Y', NULL, 1, 'N', '2023-10-30 11:06:06', 1339550467939639299, '2023-10-30 11:22:38', 1339550467939639299);
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1718826709295304705, 1718824432245411841, '[-1],[1718824432245411841],', '接口授权', 'jiekoushouquan', 1717100945571057665, 120.00, 1, NULL, 10, '/apiAuth/interface/authorization', '/api-auth/authorization/index', 'icon-menu-apishouquan', NULL, NULL, 'Y', NULL, 1, 'N', '2023-10-30 11:06:36', 1339550467939639299, '2023-10-30 11:22:48', 1339550467939639299);
