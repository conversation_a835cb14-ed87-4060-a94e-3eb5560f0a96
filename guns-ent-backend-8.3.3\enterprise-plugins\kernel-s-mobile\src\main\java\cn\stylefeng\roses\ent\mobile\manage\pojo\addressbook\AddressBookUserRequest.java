package cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通讯录人员详情查询请求
 *
 * <AUTHOR>
 * @since 2024/3/23 19:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddressBookUserRequest extends BaseRequest {

    /**
     * 当前用户所在的机构id
     */
    @ChineseDescription("当前用户所在的机构id")
    @NotNull(message = "当前用户所在的机构id不能为空")
    private Long orgId;

    /**
     * 人员id
     */
    @ChineseDescription("人员id")
    @NotNull(message = "人员id不能为空")
    private Long userId;

}
