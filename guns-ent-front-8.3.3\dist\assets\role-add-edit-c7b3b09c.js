import{R as o,b3 as S,r as d,L as y,o as O,cb as A,a as F,f as L,w as T,d as h,m as B,M}from"./index-18a1ea24.js";import j from"./role-form-f8e04d4e.js";class c{static findPage(t){return o.getAndLoadData("/sysRole/page",t)}static add(t){return o.post("/sysRole/add",t)}static edit(t){return o.post("/sysRole/edit",t)}static delete(t){return o.post("/sysRole/delete",t)}static batchDelete(t){return o.post("/sysRole/batchDelete",t)}static detail(t){return o.getAndLoadData("/sysRole/detail",t)}}const k={__name:"role-add-edit",props:{visible:Boolean,data:Object,roleType:[String,Number],roleCategoryId:String,companyData:Object},emits:["update:visible","done"],setup(f,{emit:t}){const p=S(),l=f,m=t,n=d(!1),i=d(!1),s=d({roleType:void 0,statusFlag:1,roleCategoryId:l.roleCategoryId}),v=d(null),b=y(()=>p.info.superAdminFlag),_=y(()=>{let a=p.info.userOrgInfoList.filter(r=>r.currentSelectFlag);if(a.length>0)return a[0]});O(async()=>{if(l.data)i.value=!0,R();else{s.value.roleSort=await A("SYSTEM_BASE_ROLE"),i.value=!1,s.value.roleType=l.roleType||20;let e=l.companyData?l.companyData:_.value;e!=null&&e.companyId&&s.value.roleType==20&&(s.value.roleCompanyId=e==null?void 0:e.companyId,s.value.roleCompanyIdWrapper=e==null?void 0:e.companyName)}});const R=()=>{c.detail({roleId:l.data.roleId}).then(e=>{s.value=Object.assign({},e)})},u=e=>{m("update:visible",e)},I=async()=>{v.value.$refs.formRef.validate().then(async e=>{if(e){n.value=!0;let a=null;i.value?a=c.edit(s.value):a=c.add(s.value),a.then(async r=>{n.value=!1,B.success(r.message),u(!1),m("done")}).catch(()=>{n.value=!1})}})};return(e,a)=>{const r=M;return F(),L(r,{width:700,maskClosable:!1,visible:l.visible,"confirm-loading":n.value,forceRender:!0,title:i.value?"\u7F16\u8F91\u89D2\u8272":"\u65B0\u5EFA\u89D2\u8272","body-style":{paddingBottom:"8px"},"onUpdate:visible":u,onOk:I,onClose:a[1]||(a[1]=g=>u(!1))},{default:T(()=>[h(j,{form:s.value,"onUpdate:form":a[0]||(a[0]=g=>s.value=g),ref_key:"roleFormRef",ref:v,superAdminFlag:b.value},null,8,["form","superAdminFlag"])]),_:1},8,["visible","confirm-loading","title"])}}},C=Object.freeze(Object.defineProperty({__proto__:null,default:k},Symbol.toStringTag,{value:"Module"}));export{c as R,k as _,C as r};
