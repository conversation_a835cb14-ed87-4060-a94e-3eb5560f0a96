<template>
  <a-modal
    title="处理预警记录"
    :visible="visible"
    :width="600"
    :confirm-loading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="process-container">
      <!-- 预警信息概要 -->
      <a-card title="预警信息" size="small" class="alert-info-card">
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="商品名称">
            {{ recordData.productName }}
          </a-descriptions-item>
          <a-descriptions-item label="预警类型">
            <a-tag :color="getAlertTypeColor(recordData.alertType)">
              {{ getAlertTypeText(recordData.alertType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预警级别">
            <a-tag :color="getAlertLevelColor(recordData.alertLevel)">
              {{ getAlertLevelText(recordData.alertLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            <span
              :style="{ 
                color: recordData.currentStock <= recordData.thresholdValue ? '#f5222d' : '#52c41a',
                fontWeight: 'bold'
              }">
              {{ recordData.currentStock }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="预警消息" :span="2">
            <div class="alert-message">
              {{ recordData.alertMessage }}
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 处理表单 -->
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        class="process-form"
      >
        <a-form-item label="处理方式" name="handleType">
          <a-radio-group v-model:value="formData.handleType" @change="handleTypeChange">
            <a-radio value="RESOLVE">
              <span style="color: #52c41a;">
                <CheckCircleOutlined />
                解决
              </span>
            </a-radio>
            <a-radio value="IGNORE">
              <span style="color: #faad14;">
                <MinusCircleOutlined />
                忽略
              </span>
            </a-radio>
          </a-radio-group>
          <div class="handle-type-desc">
            <div v-if="formData.handleType === 'RESOLVE'" class="desc-text">
              <CheckCircleOutlined style="color: #52c41a;" />
              标记为已解决，表示已采取措施解决了库存问题
            </div>
            <div v-else-if="formData.handleType === 'IGNORE'" class="desc-text">
              <MinusCircleOutlined style="color: #faad14;" />
              标记为已忽略，表示暂时不处理此预警
            </div>
          </div>
        </a-form-item>

        <a-form-item label="处理备注" name="handleRemark">
          <a-textarea
            v-model:value="formData.handleRemark"
            placeholder="请输入处理备注，说明具体的处理措施或忽略原因"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <!-- 解决方式的额外选项 -->
        <template v-if="formData.handleType === 'RESOLVE'">
          <a-form-item label="解决方式" name="resolveMethod">
            <a-checkbox-group v-model:value="formData.resolveMethod">
              <a-checkbox value="PURCHASE">采购补货</a-checkbox>
              <a-checkbox value="TRANSFER">调拨库存</a-checkbox>
              <a-checkbox value="ADJUST">库存调整</a-checkbox>
              <a-checkbox value="OTHER">其他方式</a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item 
            v-if="formData.resolveMethod && formData.resolveMethod.includes('OTHER')"
            label="其他方式说明" 
            name="otherMethodDesc"
          >
            <a-input
              v-model:value="formData.otherMethodDesc"
              placeholder="请说明其他解决方式"
              :maxlength="200"
            />
          </a-form-item>

          <a-form-item label="预计完成时间" name="expectedCompleteTime">
            <a-date-picker
              v-model:value="formData.expectedCompleteTime"
              format="YYYY-MM-DD"
              placeholder="请选择预计完成时间"
              style="width: 100%"
            />
          </a-form-item>
        </template>

        <!-- 忽略方式的额外选项 -->
        <template v-if="formData.handleType === 'IGNORE'">
          <a-form-item label="忽略原因" name="ignoreReason">
            <a-select
              v-model:value="formData.ignoreReason"
              placeholder="请选择忽略原因"
            >
              <a-select-option value="TEMPORARY">临时性问题</a-select-option>
              <a-select-option value="ACCEPTABLE">可接受范围</a-select-option>
              <a-select-option value="PROCESSING">正在处理中</a-select-option>
              <a-select-option value="SYSTEM_ERROR">系统错误</a-select-option>
              <a-select-option value="OTHER">其他原因</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item 
            v-if="formData.ignoreReason === 'OTHER'"
            label="其他原因说明" 
            name="otherReasonDesc"
          >
            <a-input
              v-model:value="formData.otherReasonDesc"
              placeholder="请说明其他忽略原因"
              :maxlength="200"
            />
          </a-form-item>

          <a-form-item label="重新检查时间" name="recheckTime">
            <a-date-picker
              v-model:value="formData.recheckTime"
              format="YYYY-MM-DD"
              placeholder="请选择重新检查时间（可选）"
              style="width: 100%"
            />
          </a-form-item>
        </template>

        <!-- 通知设置 -->
        <a-form-item label="通知设置" name="notifySettings">
          <a-checkbox-group v-model:value="formData.notifySettings">
            <a-checkbox value="EMAIL">邮件通知</a-checkbox>
            <a-checkbox value="SMS">短信通知</a-checkbox>
            <a-checkbox value="SYSTEM">系统消息</a-checkbox>
          </a-checkbox-group>
          <div class="notify-desc">
            选择处理完成后的通知方式
          </div>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CheckCircleOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import { InventoryAlertRecordApi } from '../api/InventoryAlertRecordApi';

export default {
  name: 'AlertRecordProcess',
  components: {
    CheckCircleOutlined,
    MinusCircleOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'success', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref();
    const confirmLoading = ref(false);

    const formData = reactive({
      handleType: 'RESOLVE',
      handleRemark: '',
      resolveMethod: [],
      otherMethodDesc: '',
      expectedCompleteTime: null,
      ignoreReason: '',
      otherReasonDesc: '',
      recheckTime: null,
      notifySettings: ['SYSTEM']
    });

    const rules = reactive({
      handleType: [
        { required: true, message: '请选择处理方式', trigger: 'change' }
      ],
      handleRemark: [
        { required: true, message: '请输入处理备注', trigger: 'blur' },
        { min: 10, message: '处理备注至少10个字符', trigger: 'blur' }
      ],
      resolveMethod: [
        {
          validator: (rule, value) => {
            if (formData.handleType === 'RESOLVE' && (!value || value.length === 0)) {
              return Promise.reject('请选择解决方式');
            }
            return Promise.resolve();
          },
          trigger: 'change'
        }
      ],
      otherMethodDesc: [
        {
          validator: (rule, value) => {
            if (formData.handleType === 'RESOLVE' && 
                formData.resolveMethod && 
                formData.resolveMethod.includes('OTHER') && 
                !value) {
              return Promise.reject('请说明其他解决方式');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ],
      ignoreReason: [
        {
          validator: (rule, value) => {
            if (formData.handleType === 'IGNORE' && !value) {
              return Promise.reject('请选择忽略原因');
            }
            return Promise.resolve();
          },
          trigger: 'change'
        }
      ],
      otherReasonDesc: [
        {
          validator: (rule, value) => {
            if (formData.handleType === 'IGNORE' && 
                formData.ignoreReason === 'OTHER' && 
                !value) {
              return Promise.reject('请说明其他忽略原因');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ]
    });

    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        confirmLoading.value = true;

        const params = {
          id: props.recordData.id,
          handleType: formData.handleType,
          handleRemark: formData.handleRemark,
          notifySettings: formData.notifySettings
        };

        if (formData.handleType === 'RESOLVE') {
          params.resolveMethod = formData.resolveMethod;
          params.otherMethodDesc = formData.otherMethodDesc;
          params.expectedCompleteTime = formData.expectedCompleteTime?.format('YYYY-MM-DD');
        } else if (formData.handleType === 'IGNORE') {
          params.ignoreReason = formData.ignoreReason;
          params.otherReasonDesc = formData.otherReasonDesc;
          params.recheckTime = formData.recheckTime?.format('YYYY-MM-DD');
        }

        await InventoryAlertRecordApi.handle(params);
        message.success('处理成功');
        emit('success');
      } catch (error) {
        console.error('处理失败:', error);
        message.error('处理失败');
      } finally {
        confirmLoading.value = false;
      }
    };

    const handleCancel = () => {
      emit('cancel');
    };

    const handleTypeChange = () => {
      // 清空相关字段
      formData.resolveMethod = [];
      formData.otherMethodDesc = '';
      formData.expectedCompleteTime = null;
      formData.ignoreReason = '';
      formData.otherReasonDesc = '';
      formData.recheckTime = null;
    };

    const getAlertTypeColor = (type) => {
      const colors = {
        'LOW_STOCK': 'orange',
        'ZERO_STOCK': 'red',
        'OVERSTOCK': 'purple',
        'EXPIRY': 'volcano'
      };
      return colors[type] || 'default';
    };

    const getAlertTypeText = (type) => {
      const texts = {
        'LOW_STOCK': '库存不足',
        'ZERO_STOCK': '零库存',
        'OVERSTOCK': '库存积压',
        'EXPIRY': '临期预警'
      };
      return texts[type] || type;
    };

    const getAlertLevelColor = (level) => {
      const colors = {
        'CRITICAL': 'red',
        'WARNING': 'orange',
        'INFO': 'blue'
      };
      return colors[level] || 'default';
    };

    const getAlertLevelText = (level) => {
      const texts = {
        'CRITICAL': '紧急',
        'WARNING': '警告',
        'INFO': '提醒'
      };
      return texts[level] || level;
    };

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        handleType: 'RESOLVE',
        handleRemark: '',
        resolveMethod: [],
        otherMethodDesc: '',
        expectedCompleteTime: null,
        ignoreReason: '',
        otherReasonDesc: '',
        recheckTime: null,
        notifySettings: ['SYSTEM']
      });
    };

    watch(() => props.visible, (newVal) => {
      if (newVal) {
        resetForm();
      }
    });

    return {
      formRef,
      confirmLoading,
      formData,
      rules,
      handleSubmit,
      handleCancel,
      handleTypeChange,
      getAlertTypeColor,
      getAlertTypeText,
      getAlertLevelColor,
      getAlertLevelText
    };
  }
};
</script>

<style scoped>
.process-container {
  max-height: 70vh;
  overflow-y: auto;
}

.alert-info-card {
  margin-bottom: 16px;
}

.alert-message {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  word-break: break-word;
}

.process-form {
  margin-top: 16px;
}

.handle-type-desc {
  margin-top: 8px;
}

.desc-text {
  font-size: 12px;
  color: #666;
  padding: 4px 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.notify-desc {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
