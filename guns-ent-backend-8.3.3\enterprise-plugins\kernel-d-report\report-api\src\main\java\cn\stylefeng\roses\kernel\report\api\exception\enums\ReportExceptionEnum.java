package cn.stylefeng.roses.kernel.report.api.exception.enums;

import cn.stylefeng.roses.kernel.report.api.constants.ReportConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 报表模块异常枚举
 *
 * <AUTHOR>
 * @date 2021/11/1 9:46
 */
@Getter
public enum ReportExceptionEnum implements AbstractExceptionEnum {

    /**
     * 获取租户编码错误
     */
    CALC_TENANT_CODE_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ReportConstants.REPORT_SERVER_EXCEPTION_STEP_CODE + "01", "报表模块异常，具体信息:{}");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ReportExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
