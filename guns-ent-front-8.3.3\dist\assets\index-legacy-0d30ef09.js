System.register(["./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js","./regionApi-legacy-73888494.js"],(function(e,t){"use strict";var a,r,n,o,l,i,c,d,s,u,p,g,v,h,f,m,y,x,b,w,k,A,S,I,V,N,_,C,L;return{setters:[e=>{a=e._,r=e.r,n=e.L,o=e.X,l=e.o,i=e.aL,c=e.m,d=e.a,s=e.c,u=e.d,p=e.w,g=e.b,v=e.f,h=e.aM,f=e.at,m=e.aN,y=e.h,x=e.a1,b=e.a2,w=e.ac,k=e.t,A=e.g,S=e.B,I=e.l,V=e.aO,N=e.a5,_=e.S,C=e.n},null,e=>{L=e.R}],execute:function(){var t=document.createElement("style");t.textContent=".region-selector[data-v-8764992c]{width:100%;position:relative}.region-selector .region-selector-input[data-v-8764992c]{cursor:pointer;transition:all .2s}.region-selector .region-selector-input[data-v-8764992c]:hover:not(.ant-input-disabled){border-color:#40a9ff}.region-selector .region-selector-input[data-v-8764992c]:focus,.region-selector .region-selector-input.ant-input-focused[data-v-8764992c]{border-color:#40a9ff;box-shadow:0 0 0 2px rgba(24,144,255,.2)}.region-selector .region-selector-input.ant-input-disabled[data-v-8764992c]{cursor:not-allowed;background-color:#f5f5f5}.region-selector .region-selector-input[readonly][data-v-8764992c]{cursor:pointer;background-color:#fafafa}.region-selector .region-selector-suffix[data-v-8764992c]{display:flex;align-items:center;gap:4px}.region-selector .region-selector-suffix .clear-btn[data-v-8764992c]{padding:0;width:16px;height:16px;min-width:16px;border:none;background:transparent;color:#bfbfbf}.region-selector .region-selector-suffix .clear-btn[data-v-8764992c]:hover{color:#8c8c8c}.region-selector .region-selector-suffix .dropdown-icon[data-v-8764992c]{color:#bfbfbf;transition:transform .3s}.region-selector .region-selector-suffix .dropdown-icon.dropdown-icon-open[data-v-8764992c]{transform:rotate(180deg)}.region-selector-dropdown[data-v-8764992c]{position:absolute;top:100%;left:0;right:0;z-index:1050;background:#fff;border-radius:6px;box-shadow:0 6px 16px rgba(0,0,0,.08),0 3px 6px -4px rgba(0,0,0,.12),0 9px 28px 8px rgba(0,0,0,.05);min-width:300px;max-width:500px;margin-top:4px;border:1px solid #d9d9d9;animation:slideDown-8764992c .2s ease-out}.region-selector-dropdown .region-selector-content[data-v-8764992c]{padding:8px}.region-selector-dropdown .region-selector-content .search-box[data-v-8764992c]{margin-bottom:8px}.region-selector-dropdown .region-selector-content .tree-content[data-v-8764992c]{max-height:300px;overflow-y:auto;border:1px solid #f0f0f0;border-radius:4px;padding:8px}.region-selector-dropdown .region-selector-content .tree-content .tree-wrapper .tree-node-title[data-v-8764992c]{user-select:none}.region-selector-dropdown .region-selector-content .tree-content .empty-content[data-v-8764992c]{padding:20px;text-align:center}.region-selector-dropdown .region-selector-content .action-buttons[data-v-8764992c]{margin-top:8px;text-align:right;border-top:1px solid #f0f0f0;padding-top:8px}[data-v-8764992c] .ant-tree .ant-tree-node-content-wrapper{width:calc(100% - 24px);border-radius:4px;transition:all .2s}[data-v-8764992c] .ant-tree .ant-tree-node-content-wrapper:hover{background-color:#f5f5f5}[data-v-8764992c] .ant-tree .ant-tree-title{width:100%;padding:2px 4px}[data-v-8764992c] .ant-tree .ant-tree-treenode{padding:2px 0}[data-v-8764992c] .ant-tree .ant-tree-treenode.ant-tree-treenode-selected .ant-tree-node-content-wrapper{background-color:#e6f7ff;border-color:#91d5ff}[data-v-8764992c] .ant-tree .ant-tree-checkbox{margin-right:8px}[data-v-8764992c] .ant-tree .ant-tree-switcher{width:20px;height:20px;line-height:20px}[data-v-8764992c] .ant-tree .ant-tree-switcher .ant-tree-switcher-icon{font-size:12px}[data-v-8764992c] .ant-tree .ant-tree-indent-unit{width:16px}.tree-content[data-v-8764992c]::-webkit-scrollbar{width:6px}.tree-content[data-v-8764992c]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.tree-content[data-v-8764992c]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.tree-content[data-v-8764992c]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}@keyframes slideDown-8764992c{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@media (max-width: 768px){.region-selector-dropdown[data-v-8764992c]{min-width:280px;max-width:100%}.region-selector-dropdown .region-selector-content .tree-content[data-v-8764992c]{max-height:250px}}\n",document.head.appendChild(t);const B={class:"region-selector"},K={class:"region-selector-suffix"},j={class:"region-selector-content"},z={key:0,class:"search-box"},E={class:"tree-content"},P={key:0,class:"tree-wrapper"},R={class:"tree-node-title"},T={key:1,class:"empty-content"},O={key:1,class:"action-buttons"},U={__name:"index",props:{modelValue:{type:[Array,String,Number],default:()=>[]},multiple:{type:Boolean,default:!0},placeholder:{type:String,default:"请选择区域"},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},showSearch:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:"搜索区域名称"},emptyText:{type:String,default:"暂无区域数据"},showActionButtons:{type:Boolean,default:!1},checkStrictly:{type:Boolean,default:!0},customApi:{type:Function,default:null},customLazyApi:{type:Function,default:null},extraParams:{type:Object,default:()=>({})},fieldNames:{type:Object,default:()=>({children:"children",title:"regionName",key:"regionId",value:"regionId"})}},emits:["update:modelValue","change","search","clear"],setup(e,{expose:t,emit:a}){const U=e,D=a,F=r(!1),Y=r(!1),J=r(""),M=r([]),W=r([]),X=r([]),q=r([]),G=r(!1),H=n((()=>{return 0===q.value.length?"":U.multiple?q.value.map((e=>e.regionName)).join(", "):(null===(e=q.value[0])||void 0===e?void 0:e.regionName)||"";var e}));o((()=>U.modelValue),((e,t)=>{!e||Array.isArray(e)&&0===e.length?(q.value=[],W.value=[]):M.value.length>0&&Z(e)}),{immediate:!0}),o(M,(e=>{e.length>0&&U.modelValue&&(Array.isArray(U.modelValue)?U.modelValue.length>0:U.modelValue)&&Z(U.modelValue)}),{immediate:!1}),l((()=>{ee(),document.addEventListener("click",Q)})),i((()=>{document.removeEventListener("click",Q),q.value=[],W.value=[],X.value=[],M.value=[]}));const Q=e=>{!e.target.closest(".region-selector")&&F.value&&(F.value=!1)},Z=e=>{if(q.value=[],W.value=[],e&&(!Array.isArray(e)||0!==e.length))try{if(U.multiple){const t=Array.isArray(e)?e:[e];if(t.length>0){const e=t.map((e=>String(e))).filter((e=>e&&"undefined"!==e&&"null"!==e));if(e.length>0){const t=$(e);q.value=t,W.value=e}}}else if(e){const t=String(e);if(t&&"undefined"!==t&&"null"!==t){const e=$([t]);q.value=e}}}catch(t){console.error("初始化选中区域失败:",t),q.value=[],W.value=[]}},$=e=>{if(!e||0===e.length)return[];const t=[],a=(e,r)=>{for(const n of e)r.includes(String(n.regionId))&&t.push({regionId:n.regionId,regionName:n.regionName,regionCode:n.regionCode,regionLevel:n.regionLevel}),n.children&&n.children.length>0&&a(n.children,r)};return M.value.length>0&&a(M.value,e),t},ee=async()=>{Y.value=!0;try{const e=U.customApi||L.findTree,t={searchText:J.value,...U.extraParams},a=await e(t);M.value=te(a),J.value?X.value=ae(a):X.value=re(a,3),U.modelValue&&(Array.isArray(U.modelValue)?U.modelValue.length>0:U.modelValue)&&Z(U.modelValue)}catch(e){console.error("加载区域数据失败:",e),c.error("加载区域数据失败")}finally{Y.value=!1}},te=e=>e&&Array.isArray(e)?e.map((e=>{const t={...e,isLeaf:!e.children||0===e.children.length,regionId:String(e.regionId||e.id||""),regionName:e.regionName||e.name||e.title||"",regionCode:e.regionCode||e.code||"",regionLevel:e.regionLevel||e.level||0,key:String(e.regionId||e.id||""),title:e.regionName||e.name||e.title||"",value:String(e.regionId||e.id||"")};return e.children&&e.children.length>0&&(t.children=te(e.children),t.isLeaf=!1),t})):[],ae=e=>{const t=[],a=e=>{e.forEach((e=>{t.push(e.regionId),e.children&&e.children.length>0&&a(e.children)}))};return a(e),t},re=(e,t)=>{const a=[],r=(e,n=1)=>{e.forEach((e=>{n<=t&&a.push(e.regionId),e.children&&e.children.length>0&&n<t&&r(e.children,n+1)}))};return r(e),a},ne=e=>{if(!U.multiple)return;const t=[],a=(e,r)=>{for(const n of e){const e=String(n.regionId);r.includes(e)&&t.push({regionId:n.regionId,regionName:n.regionName,regionCode:n.regionCode,regionLevel:n.regionLevel}),n.children&&n.children.length>0&&a(n.children,r)}};a(M.value,e),q.value=t},oe=()=>{U.disabled||U.readonly||(F.value=!0)},le=()=>{U.disabled||U.readonly||(F.value=!0)},ie=()=>{q.value=[],W.value=[],D("update:modelValue",U.multiple?[]:null),D("clear"),D("change",U.multiple?[]:null,[])},ce=()=>{ee(),D("search",J.value)},de=()=>{J.value||ee()},se=(e,t)=>{if(G.value=!0,U.multiple){let t=[];t=Array.isArray(e)?e:e&&"object"==typeof e?e.checked&&Array.isArray(e.checked)?e.checked:e.checkedKeys&&Array.isArray(e.checkedKeys)?e.checkedKeys:Object.keys(e):[];const a=t.map((e=>String(e)));W.value=a,ne(a),D("update:modelValue",a),D("change",a,q.value)}setTimeout((()=>{G.value=!1}),0)},ue=(e,t)=>{if(!G.value)if(U.multiple){if(t.node){const e=String(t.node.key||t.node.regionId),a=[...W.value];if(t.selected){if(!a.includes(e)){const t=[...a,e];W.value=t,ne(t),D("update:modelValue",t),D("change",t,q.value)}}else if(a.includes(e)){const t=a.filter((t=>t!==e));W.value=t,ne(t),D("update:modelValue",t),D("change",t,q.value)}}}else if(!U.multiple&&e.length>0){const a=t.selectedNodes[0];q.value=[a],D("update:modelValue",e[0]),D("change",e[0],a),F.value=!1}},pe=()=>{F.value=!1};t({resetState:()=>{q.value=[],W.value=[],X.value=[],F.value=!1,J.value=""},clearSelection:ie,getSelectedRegions:()=>q.value,reloadData:ee});const ge=()=>{var e;const t=U.multiple?W.value:(null===(e=q.value[0])||void 0===e?void 0:e.regionId)||null;D("update:modelValue",t),D("change",t,q.value),F.value=!1},ve=async e=>{const t=U.customLazyApi||L.findTreeWithLazy,a={parentId:e.dataRef.regionId,...U.extraParams};try{const r=await t(a);e.dataRef.children=te(r),M.value=[...M.value]}catch(r){console.error("懒加载区域数据失败:",r),c.error("加载子区域失败")}};return(t,a)=>{const r=S,n=I,o=V,l=N,i=_,c=C;return d(),s("div",B,[u(n,{value:H.value,"onUpdate:value":a[0]||(a[0]=e=>H.value=e),placeholder:e.placeholder,disabled:e.disabled,readonly:e.readonly,class:"region-selector-input",onFocus:oe,onClick:le},{suffix:p((()=>[g("div",K,[e.clearable&&q.value.length>0&&!e.disabled&&!e.readonly?(d(),v(r,{key:0,type:"text",size:"small",class:"clear-btn",onClick:h(ie,["stop"])},{icon:p((()=>[u(f(m))])),_:1})):y("",!0),u(f(x),{class:b(["dropdown-icon",{"dropdown-icon-open":F.value}])},null,8,["class"])])])),_:1},8,["value","placeholder","disabled","readonly"]),F.value?(d(),s("div",{key:0,class:"region-selector-dropdown",onClick:a[4]||(a[4]=h((()=>{}),["stop"]))},[g("div",j,[e.showSearch?(d(),s("div",z,[u(n,{value:J.value,"onUpdate:value":a[1]||(a[1]=e=>J.value=e),placeholder:e.searchPlaceholder,allowClear:"",onPressEnter:ce,onChange:de},{prefix:p((()=>[u(f(w))])),_:1},8,["value","placeholder"])])):y("",!0),g("div",E,[u(i,{spinning:Y.value},{default:p((()=>[M.value&&M.value.length>0?(d(),s("div",P,[u(o,{checkedKeys:W.value,"onUpdate:checkedKeys":a[2]||(a[2]=e=>W.value=e),expandedKeys:X.value,"onUpdate:expandedKeys":a[3]||(a[3]=e=>X.value=e),"tree-data":M.value,checkable:e.multiple,selectable:!0,checkStrictly:e.checkStrictly,"load-data":ve,fieldNames:e.fieldNames,onCheck:se,onSelect:ue},{title:p((({title:e,key:t,dataRef:a})=>[g("span",R,k(e),1)])),_:1},8,["checkedKeys","expandedKeys","tree-data","checkable","checkStrictly","fieldNames"])])):(d(),s("div",T,[u(l,{description:e.emptyText},null,8,["description"])]))])),_:1},8,["spinning"])]),e.showActionButtons?(d(),s("div",O,[u(c,null,{default:p((()=>[u(r,{size:"small",onClick:pe},{default:p((()=>a[5]||(a[5]=[A("取消")]))),_:1,__:[5]}),u(r,{type:"primary",size:"small",onClick:ge},{default:p((()=>a[6]||(a[6]=[A("确定")]))),_:1,__:[6]})])),_:1})])):y("",!0)])])):y("",!0)])}}};e("R",a(U,[["__scopeId","data-v-8764992c"]]))}}}));
