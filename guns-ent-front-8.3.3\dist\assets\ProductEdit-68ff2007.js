import{_ as M,s as S,r as k,X as q,k as x,a as m,f,w as a,d as e,c as I,F as T,e as O,g as N,t as E,h as P,m as L,l as R,u as V,v as B,G,W as H,J as j,y as F,$ as W,H as D,M as J}from"./index-18a1ea24.js";import{_ as A}from"./SupplierSelector-e8033f79.js";/* empty css              */import{P as h}from"./ProductApi-52d42f8e.js";import X from"./CategorySelector-5d61ae06.js";/* empty css              */import"./SupplierApi-6b9315dd.js";/* empty css              */import"./productCategoryApi-39e417fd.js";const z={name:"ProductEdit",components:{CategorySelector:X,SupplierSelector:A},props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(c,{emit:t}){const i=S({}),l=k(null),U=k(!1),_=k(!1),p=h.getProductStatusOptions(),n=h.getCommonUnitOptions(),o=h.getPricingTypeOptions(),u=S({productCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5546\u54C1\u7F16\u7801",trigger:"blur"},{max:50,message:"\u5546\u54C1\u7F16\u7801\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26",trigger:"blur"}],productName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",trigger:"blur"},{max:200,message:"\u5546\u54C1\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26",trigger:"blur"}],unit:[{required:!0,message:"\u8BF7\u9009\u62E9\u57FA\u672C\u5355\u4F4D",trigger:"change"}],categoryId:[{required:!0,message:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B",trigger:"change"}],weight:[{type:"number",min:0,message:"\u91CD\u91CF\u4E0D\u80FD\u5C0F\u4E8E0",trigger:"blur"}],volume:[{type:"number",min:0,message:"\u4F53\u79EF\u4E0D\u80FD\u5C0F\u4E8E0",trigger:"blur"}],shelfLife:[{type:"number",min:0,message:"\u4FDD\u8D28\u671F\u4E0D\u80FD\u5C0F\u4E8E0",trigger:"blur"}],supplierId:[{required:!0,message:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",trigger:"change"}],pricingType:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BA1\u4EF7\u7C7B\u578B",trigger:"change"}]}),g=d=>{t("update:visible",d)},b=(d,v)=>{console.log("\u4F9B\u5E94\u5546\u53D8\u66F4:",d,v)},s=d=>{i.retailPrice=void 0,i.unitPrice=void 0,i.piecePrice=void 0,i.referencePrice=void 0,y(d)},y=d=>{switch(delete u.retailPrice,delete u.unitPrice,delete u.piecePrice,delete u.referencePrice,d){case"NORMAL":u.retailPrice=[{required:!0,message:"\u8BF7\u8F93\u5165\u96F6\u552E\u4EF7\u683C",trigger:"blur",type:"number"}];break;case"WEIGHT":u.unitPrice=[{required:!0,message:"\u8BF7\u8F93\u5165\u5355\u4F4D\u4EF7\u683C",trigger:"blur",type:"number"}];break;case"PIECE":u.piecePrice=[{required:!0,message:"\u8BF7\u8F93\u5165\u5355\u4EFD\u4EF7\u683C",trigger:"blur",type:"number"}];break}},C=()=>{l.value.validate().then(()=>(U.value=!0,(_.value?h.edit:h.add)(i))).then(()=>{L.success("\u4FDD\u5B58\u6210\u529F"),g(!1),t("done")}).catch(d=>{L.error(d.message||"\u4FDD\u5B58\u5931\u8D25")}).finally(()=>{U.value=!1})};return q(()=>c.visible,d=>{var v;d&&((v=c.data)!=null&&v.productId?(_.value=!0,Object.assign(i,c.data),c.data.categoryId?i.categoryId=c.data.categoryId:i.categoryId=void 0,i.pricingType&&y(i.pricingType)):(_.value=!1,Object.keys(i).forEach(w=>{i[w]=void 0}),i.unit="\u4E2A",i.status="ACTIVE",i.pricingType="NORMAL",i.categoryId=void 0,y("NORMAL")))},{immediate:!0}),{form:i,formRef:l,loading:U,isUpdate:_,statusOptions:p,unitOptions:n,pricingTypeOptions:o,rules:u,updateVisible:g,save:C,handleSupplierChange:b,handlePricingTypeChange:s}}};function K(c,t,i,l,U,_){const p=R,n=V,o=B,u=G,g=H,b=j,s=F,y=x("category-selector"),C=A,d=W,v=D,w=J;return m(),f(w,{title:l.isUpdate?"\u7F16\u8F91\u5546\u54C1":"\u65B0\u589E\u5546\u54C1",width:900,visible:i.visible,"confirm-loading":l.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":l.updateVisible,onOk:l.save},{default:a(()=>[e(v,{ref:"formRef",model:l.form,rules:l.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:a(()=>[e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5546\u54C1\u7F16\u7801",name:"productCode"},{default:a(()=>[e(p,{value:l.form.productCode,"onUpdate:value":t[0]||(t[0]=r=>l.form.productCode=r),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5546\u54C1\u540D\u79F0",name:"productName"},{default:a(()=>[e(p,{value:l.form.productName,"onUpdate:value":t[1]||(t[1]=r=>l.form.productName=r),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5546\u54C1\u7B80\u79F0",name:"productShortName"},{default:a(()=>[e(p,{value:l.form.productShortName,"onUpdate:value":t[2]||(t[2]=r=>l.form.productShortName=r),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u7B80\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u6761\u5F62\u7801",name:"barcode"},{default:a(()=>[e(p,{value:l.form.barcode,"onUpdate:value":t[3]||(t[3]=r=>l.form.barcode=r),placeholder:"\u8BF7\u8F93\u5165\u6761\u5F62\u7801"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u54C1\u724C",name:"brand"},{default:a(()=>[e(p,{value:l.form.brand,"onUpdate:value":t[4]||(t[4]=r=>l.form.brand=r),placeholder:"\u8BF7\u8F93\u5165\u54C1\u724C"},null,8,["value"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u89C4\u683C",name:"specification"},{default:a(()=>[e(p,{value:l.form.specification,"onUpdate:value":t[5]||(t[5]=r=>l.form.specification=r),placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u57FA\u672C\u5355\u4F4D",name:"unit"},{default:a(()=>[e(b,{value:l.form.unit,"onUpdate:value":t[6]||(t[6]=r=>l.form.unit=r),placeholder:"\u8BF7\u9009\u62E9\u57FA\u672C\u5355\u4F4D","show-search":""},{default:a(()=>[(m(!0),I(T,null,O(l.unitOptions,r=>(m(),f(g,{key:r.value,value:r.value},{default:a(()=>[N(E(r.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u72B6\u6001",name:"status"},{default:a(()=>[e(b,{value:l.form.status,"onUpdate:value":t[7]||(t[7]=r=>l.form.status=r),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:a(()=>[(m(!0),I(T,null,O(l.statusOptions,r=>(m(),f(g,{key:r.value,value:r.value},{default:a(()=>[N(E(r.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u91CD\u91CF(kg)",name:"weight"},{default:a(()=>[e(s,{value:l.form.weight,"onUpdate:value":t[8]||(t[8]=r=>l.form.weight=r),placeholder:"\u8BF7\u8F93\u5165\u91CD\u91CF",min:0,precision:3,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u4F53\u79EF(m\xB3)",name:"volume"},{default:a(()=>[e(s,{value:l.form.volume,"onUpdate:value":t[9]||(t[9]=r=>l.form.volume=r),placeholder:"\u8BF7\u8F93\u5165\u4F53\u79EF",min:0,precision:3,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u4FDD\u8D28\u671F(\u5929)",name:"shelfLife"},{default:a(()=>[e(s,{value:l.form.shelfLife,"onUpdate:value":t[10]||(t[10]=r=>l.form.shelfLife=r),placeholder:"\u8BF7\u8F93\u5165\u4FDD\u8D28\u671F",min:0,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5546\u54C1\u5206\u7C7B",name:"categoryId"},{default:a(()=>[e(y,{value:l.form.categoryId,"onUpdate:value":t[11]||(t[11]=r=>l.form.categoryId=r)},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u4F9B\u5E94\u5546",name:"supplierId"},{default:a(()=>[e(C,{value:l.form.supplierId,"onUpdate:value":t[12]||(t[12]=r=>l.form.supplierId=r),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},onChange:l.handleSupplierChange},null,8,["value","onChange"])]),_:1})]),_:1}),e(o,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u8BA1\u4EF7\u7C7B\u578B",name:"pricingType"},{default:a(()=>[e(b,{value:l.form.pricingType,"onUpdate:value":t[13]||(t[13]=r=>l.form.pricingType=r),placeholder:"\u8BF7\u9009\u62E9\u8BA1\u4EF7\u7C7B\u578B",onChange:l.handlePricingTypeChange},{default:a(()=>[(m(!0),I(T,null,O(l.pricingTypeOptions,r=>(m(),f(g,{key:r.value,value:r.value},{default:a(()=>[N(E(r.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange"])]),_:1})]),_:1})]),_:1}),l.form.pricingType?(m(),f(u,{key:0,gutter:16},{default:a(()=>[l.form.pricingType==="NORMAL"?(m(),f(o,{key:0,md:12,sm:24},{default:a(()=>[e(n,{label:"\u96F6\u552E\u4EF7\u683C",name:"retailPrice"},{default:a(()=>[e(s,{value:l.form.retailPrice,"onUpdate:value":t[14]||(t[14]=r=>l.form.retailPrice=r),placeholder:"\u8BF7\u8F93\u5165\u96F6\u552E\u4EF7\u683C",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):P("",!0),l.form.pricingType==="WEIGHT"?(m(),f(o,{key:1,md:12,sm:24},{default:a(()=>[e(n,{label:"\u5355\u4F4D\u4EF7\u683C",name:"unitPrice"},{default:a(()=>[e(s,{value:l.form.unitPrice,"onUpdate:value":t[15]||(t[15]=r=>l.form.unitPrice=r),placeholder:"\u8BF7\u8F93\u5165\u5355\u4F4D\u4EF7\u683C(\u6BCF\u516C\u65A4)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):P("",!0),l.form.pricingType==="PIECE"?(m(),f(o,{key:2,md:12,sm:24},{default:a(()=>[e(n,{label:"\u5355\u4EFD\u4EF7\u683C",name:"piecePrice"},{default:a(()=>[e(s,{value:l.form.piecePrice,"onUpdate:value":t[16]||(t[16]=r=>l.form.piecePrice=r),placeholder:"\u8BF7\u8F93\u5165\u5355\u4EFD\u4EF7\u683C",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):P("",!0),l.form.pricingType==="VARIABLE"?(m(),f(o,{key:3,md:12,sm:24},{default:a(()=>[e(n,{label:"\u53C2\u8003\u4EF7\u683C",name:"referencePrice"},{default:a(()=>[e(s,{value:l.form.referencePrice,"onUpdate:value":t[17]||(t[17]=r=>l.form.referencePrice=r),placeholder:"\u8BF7\u8F93\u5165\u53C2\u8003\u4EF7\u683C(\u53EF\u9009)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})):P("",!0)]),_:1})):P("",!0),e(u,{gutter:16},{default:a(()=>[e(o,{span:24},{default:a(()=>[e(n,{label:"\u5B58\u50A8\u6761\u4EF6",name:"storageCondition","label-col":{span:3},"wrapper-col":{span:21}},{default:a(()=>[e(d,{value:l.form.storageCondition,"onUpdate:value":t[18]||(t[18]=r=>l.form.storageCondition=r),placeholder:"\u8BF7\u8F93\u5165\u5B58\u50A8\u6761\u4EF6",rows:2},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:16},{default:a(()=>[e(o,{span:24},{default:a(()=>[e(n,{label:"\u5907\u6CE8",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:a(()=>[e(d,{value:l.form.remark,"onUpdate:value":t[19]||(t[19]=r=>l.form.remark=r),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","visible","confirm-loading","onUpdate:visible","onOk"])}const ne=M(z,[["render",K]]);export{ne as default};
