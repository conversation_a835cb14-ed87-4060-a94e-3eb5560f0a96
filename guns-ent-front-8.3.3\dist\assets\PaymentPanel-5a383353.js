import{_ as L,r as g,L as P,X as j,a as i,f as w,w as f,b as e,t as l,c as v,h as k,F as z,e as D,a2 as E,d as p,I,g as x,m as d,B as O,M as R}from"./index-18a1ea24.js";import W from"./CashPayment-9bc7318e.js";/* empty css              */const Y={class:"payment-panel"},U={class:"order-summary"},X={class:"summary-header"},$={class:"order-items-count"},q={class:"amount-details"},G={class:"amount-row"},J={class:"value"},K={key:0,class:"amount-row"},Q={class:"value discount"},Z={class:"amount-row total"},ee={class:"value"},te={class:"payment-methods"},ae={class:"method-grid"},ne=["onClick"],se={class:"method-icon"},oe={class:"method-name"},le={class:"payment-content"},ie={key:1,class:"other-payment"},ce={class:"payment-placeholder"},ue={class:"placeholder-icon"},me={class:"placeholder-text"},de={key:0,class:"payment-actions"},re=Object.assign({name:"PaymentPanel"},{__name:"PaymentPanel",props:{visible:{type:Boolean,default:!1},orderInfo:{type:Object,default:()=>({})},memberInfo:{type:Object,default:null}},emits:["update:visible","payment-success","payment-cancel"],setup(h,{emit:H}){const c=h,u=H,n=g("CASH"),o=g(!1),A=[{value:"CASH",label:"\u73B0\u91D1\u652F\u4ED8",icon:"icon-cash"},{value:"WECHAT",label:"\u5FAE\u4FE1\u652F\u4ED8",icon:"icon-wechat"},{value:"ALIPAY",label:"\u652F\u4ED8\u5B9D",icon:"icon-alipay"},{value:"CARD",label:"\u94F6\u884C\u5361",icon:"icon-card"}],s=P(()=>({itemCount:c.orderInfo.itemCount||0,totalAmount:c.orderInfo.totalAmount||0,discountAmount:c.orderInfo.discountAmount||0,finalAmount:c.orderInfo.finalAmount||0})),C=P(()=>n.value&&s.value.finalAmount>0),_=a=>(a||0).toFixed(2),M=a=>{n.value=a},S=a=>{const t=A.find(r=>r.value===a);return t?t.label:"\u672A\u77E5\u652F\u4ED8\u65B9\u5F0F"},B=()=>{if(o.value)return"\u5904\u7406\u4E2D...";switch(n.value){case"CASH":return"\u786E\u8BA4\u6536\u6B3E";case"WECHAT":case"ALIPAY":return"\u786E\u8BA4\u652F\u4ED8";case"CARD":return"\u786E\u8BA4\u5237\u5361";default:return"\u786E\u8BA4\u652F\u4ED8"}},T=async a=>{o.value=!0;try{await new Promise(t=>setTimeout(t,1e3)),u("payment-success",{paymentMethod:"CASH",paymentAmount:s.value.finalAmount,receivedAmount:a.receivedAmount,changeAmount:a.changeAmount}),d.success("\u73B0\u91D1\u652F\u4ED8\u6210\u529F"),y()}catch(t){d.error("\u652F\u4ED8\u5931\u8D25: "+t.message)}finally{o.value=!1}},N=async()=>{if(!C.value){d.warning("\u8BF7\u5B8C\u5584\u652F\u4ED8\u4FE1\u606F");return}o.value=!0;try{await new Promise(a=>setTimeout(a,2e3)),u("payment-success",{paymentMethod:n.value,paymentAmount:s.value.finalAmount}),d.success("\u652F\u4ED8\u6210\u529F"),y()}catch(a){d.error("\u652F\u4ED8\u5931\u8D25: "+a.message)}finally{o.value=!1}},b=()=>{u("payment-cancel"),y()},V=a=>{u("update:visible",a)},y=()=>{u("update:visible",!1),setTimeout(()=>{n.value="CASH",o.value=!1},300)};return j(()=>c.visible,a=>{a&&(n.value="CASH",o.value=!1)}),(a,t)=>{const r=O,F=R;return i(),w(F,{open:h.visible,"onUpdate:open":V,title:"\u6536\u94F6\u7ED3\u7B97",width:"600px",closable:!1,maskClosable:!1,footer:null,class:"payment-modal"},{default:f(()=>[e("div",Y,[e("div",U,[e("div",X,[t[0]||(t[0]=e("h4",null,"\u8BA2\u5355\u4FE1\u606F",-1)),e("div",$,"\u5171 "+l(s.value.itemCount)+" \u4EF6\u5546\u54C1",1)]),e("div",q,[e("div",G,[t[1]||(t[1]=e("span",{class:"label"},"\u5546\u54C1\u603B\u989D:",-1)),e("span",J,"\xA5"+l(_(s.value.totalAmount)),1)]),s.value.discountAmount>0?(i(),v("div",K,[t[2]||(t[2]=e("span",{class:"label"},"\u4F18\u60E0\u91D1\u989D:",-1)),e("span",Q,"-\xA5"+l(_(s.value.discountAmount)),1)])):k("",!0),e("div",Z,[t[3]||(t[3]=e("span",{class:"label"},"\u5E94\u4ED8\u91D1\u989D:",-1)),e("span",ee,"\xA5"+l(_(s.value.finalAmount)),1)])])]),e("div",te,[t[4]||(t[4]=e("h4",null,"\u9009\u62E9\u652F\u4ED8\u65B9\u5F0F",-1)),e("div",ae,[(i(),v(z,null,D(A,m=>e("div",{key:m.value,class:E(["method-card",{active:n.value===m.value}]),onClick:ve=>M(m.value)},[e("div",se,[p(I,{iconClass:m.icon},null,8,["iconClass"])]),e("div",oe,l(m.label),1)],10,ne)),64))])]),e("div",le,[n.value==="CASH"?(i(),w(W,{key:0,"payment-amount":s.value.finalAmount,"initial-amount":s.value.finalAmount,loading:o.value,onConfirmPayment:T,onCancelPayment:b},null,8,["payment-amount","initial-amount","loading"])):(i(),v("div",ie,[e("div",ce,[e("div",ue,[p(I,{iconClass:"icon-payment"})]),e("div",me,l(S(n.value))+" \u652F\u4ED8\u529F\u80FD\u5F00\u53D1\u4E2D... ",1)])]))]),n.value!=="CASH"?(i(),v("div",de,[p(r,{size:"large",onClick:b,class:"cancel-btn"},{default:f(()=>t[5]||(t[5]=[x(" \u53D6\u6D88 ")])),_:1,__:[5]}),p(r,{type:"primary",size:"large",onClick:N,loading:o.value,disabled:!C.value,class:"confirm-btn"},{default:f(()=>[x(l(B()),1)]),_:1},8,["loading","disabled"])])):k("",!0)])]),_:1},8,["open"])}}}),fe=L(re,[["__scopeId","data-v-b5570666"]]);export{fe as default};
