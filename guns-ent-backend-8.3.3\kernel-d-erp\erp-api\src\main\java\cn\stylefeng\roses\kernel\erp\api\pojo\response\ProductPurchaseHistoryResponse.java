package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商品采购历史响应类
 *
 * <AUTHOR>
 * @since 2025/07/28 10:00
 */
@Data
public class ProductPurchaseHistoryResponse {

    /**
     * 明细ID
     */
    @ChineseDescription("明细ID")
    private Long id;

    /**
     * 入库单ID
     */
    @ChineseDescription("入库单ID")
    private Long orderId;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 数量
     */
    @ChineseDescription("数量")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @ChineseDescription("单价")
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @ChineseDescription("总价")
    private BigDecimal totalPrice;

    /**
     * 创建时间
     */
    @ChineseDescription("创建时间")
    private LocalDateTime createTime;

    /**
     * 入库单号
     */
    @ChineseDescription("入库单号")
    private String orderNo;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 入库单状态
     */
    @ChineseDescription("入库单状态")
    private String status;

    /**
     * 订单日期
     */
    @ChineseDescription("订单日期")
    private LocalDate orderDate;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

}