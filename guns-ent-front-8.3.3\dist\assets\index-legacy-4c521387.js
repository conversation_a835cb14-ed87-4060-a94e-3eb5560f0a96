System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./tenant-detail-legacy-cd0e295d.js","./tenant-approver-legacy-57cacee7.js","./TenantApi-legacy-40853d2f.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./time-util-legacy-296598c1.js"],(function(e,t){"use strict";var a,n,l,i,s,o,c,d,u,r,v,p,h,y,g,x,m,w,f,I,k,b,C;return{setters:[e=>{a=e._},e=>{n=e._,l=e.r,i=e.o,s=e.a,o=e.c,c=e.b,d=e.d,u=e.w,r=e.g,v=e.t,p=e.h,h=e.f,y=e.M,g=e.E,x=e.m,m=e.I,w=e.l,f=e.n,I=e.B},e=>{k=e.default},e=>{b=e.default},e=>{C=e.T},null,null,null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".appIconWrapper[data-v-60537850]{width:22px;height:22px}\n",document.head.appendChild(t);const _={class:"guns-layout"},j={class:"guns-layout-content"},S={class:"guns-layout"},L={class:"guns-layout-content-application"},T={class:"content-mian"},z={class:"content-mian-header"},N={class:"header-content"},R={class:"header-content-left"},W={class:"header-content-right"},E={class:"content-mian-body"},O={class:"table-content"},U=["onClick"],B=["src"],D=Object.assign({name:"TenantExamine"},{__name:"index",setup(e){const t=l([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>n.value.tableIndex+e},{dataIndex:"tenantName",title:"租户名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"tenantCode",title:"租户编码",width:100,isShow:!0},{dataIndex:"tenantLogoWrapper",title:"租户logo",width:100,isShow:!0},{dataIndex:"companyName",title:"公司",width:100,isShow:!0},{dataIndex:"email",title:"申请人邮箱",width:100,isShow:!0},{dataIndex:"safePhone",title:"申请人电话",width:100,isShow:!0},{dataIndex:"createTime",title:"申请时间",width:100,isShow:!0},{key:"action",title:"操作",width:80,isShow:!0}]),n=l(null),D=l({searchText:"",activeFlag:"N"}),F=l(null),P=l(!1),A=l(!1),M=l([]);i((()=>{}));const q=()=>{n.value.reload()},G=()=>{if(n.value.selectedRowList&&0==n.value.selectedRowList.length)return x.warning("请选择需要审批的租户");M.value=n.value.selectedRowList,A.value=!0};return(e,l)=>{const i=m,H=w,J=f,K=I,Q=a;return s(),o("div",_,[c("div",j,[c("div",S,[c("div",L,[c("div",T,[c("div",z,[c("div",N,[c("div",R,[d(J,{size:16},{default:u((()=>[d(H,{value:D.value.searchText,"onUpdate:value":l[0]||(l[0]=e=>D.value.searchText=e),placeholder:"租户名称、租户编码（回车搜索）",onPressEnter:q,class:"search-input"},{prefix:u((()=>[d(i,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),_:1})]),c("div",W,[d(J,{size:16},{default:u((()=>[d(K,{type:"primary",class:"border-radius",onClick:G},{default:u((()=>l[3]||(l[3]=[r("批量审批")]))),_:1,__:[3]})])),_:1})])])]),c("div",E,[c("div",O,[d(Q,{columns:t.value,where:D.value,rowId:"tenantId",ref_key:"tableRef",ref:n,url:"/tenant/page"},{bodyCell:u((({column:e,record:t})=>["tenantName"==e.dataIndex?(s(),o("a",{key:0,onClick:e=>(e=>{F.value=e,P.value=!0})(t)},v(t.tenantName),9,U)):p("",!0),"tenantLogoWrapper"==e.dataIndex?(s(),o("img",{key:1,src:t.tenantLogoWrapper,alt:"",class:"appIconWrapper"},null,8,B)):p("",!0),"action"==e.key?(s(),h(J,{key:2,size:16},{default:u((()=>[d(i,{iconClass:"icon-opt-shenpirenshezhi","font-size":"24px",title:"审批",color:"#60666b",onClick:e=>(e=>{M.value=[e.tenantId],A.value=!0})(t)},null,8,["onClick"]),d(i,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{y.confirm({title:"提示",content:"确定要删除选中的租户吗?",icon:d(g),maskClosable:!0,onOk:async()=>{const t=await C.delete({tenantId:e.tenantId});x.success(t.message),q()}})})(t)},null,8,["onClick"])])),_:2},1024)):p("",!0)])),_:1},8,["columns","where"])])])])])])]),A.value?(s(),h(b,{key:0,visible:A.value,"onUpdate:visible":l[1]||(l[1]=e=>A.value=e),approverList:M.value,onDone:q},null,8,["visible","approverList"])):p("",!0),P.value?(s(),h(k,{key:1,visible:P.value,"onUpdate:visible":l[2]||(l[2]=e=>P.value=e),data:F.value,onDone:q},null,8,["visible","data"])):p("",!0)])}}});e("default",n(D,[["__scopeId","data-v-60537850"]]))}}}));
