<template>
  <a-modal
    title="预警检查结果"
    :visible="visible"
    :width="900"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="test-result-container">
      <!-- 检查概要 -->
      <a-card title="检查概要" size="small" class="summary-card">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="检查规则数"
              :value="summary.totalRules"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="触发预警数"
              :value="summary.alertCount"
              :value-style="{ color: summary.alertCount > 0 ? '#f5222d' : '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="检查商品数"
              :value="summary.checkedProducts"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="检查耗时"
              :value="summary.duration"
              suffix="ms"
              :value-style="{ color: '#13c2c2' }"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 预警结果列表 -->
      <a-card title="预警结果" size="small" class="result-card">
        <a-table
          :columns="alertColumns"
          :data-source="alertResults"
          :pagination="false"
          :scroll="{ y: 300 }"
          size="small"
        >
          <template #alertLevel="{ record }">
            <a-tag :color="getAlertLevelColor(record.alertLevel)">
              {{ getAlertLevelText(record.alertLevel) }}
            </a-tag>
          </template>
          
          <template #alertType="{ record }">
            <a-tag :color="getAlertTypeColor(record.alertType)">
              {{ getAlertTypeText(record.alertType) }}
            </a-tag>
          </template>

          <template #currentStock="{ record }">
            <span :style="{ color: record.currentStock <= record.thresholdValue ? '#f5222d' : '#52c41a' }">
              {{ record.currentStock }}
            </span>
          </template>

          <template #thresholdValue="{ record }">
            <span>{{ record.thresholdValue }}</span>
          </template>
        </a-table>

        <div v-if="alertResults.length === 0" class="no-alert">
          <a-empty description="未发现预警情况">
            <template #image>
              <a-icon type="check-circle" style="font-size: 48px; color: #52c41a;" />
            </template>
          </a-empty>
        </div>
      </a-card>

      <!-- 检查详情 -->
      <a-card title="检查详情" size="small" class="detail-card">
        <a-table
          :columns="detailColumns"
          :data-source="checkDetails"
          :pagination="false"
          :scroll="{ y: 200 }"
          size="small"
        >
          <template #status="{ record }">
            <a-tag :color="record.hasAlert ? 'red' : 'green'">
              {{ record.hasAlert ? '触发预警' : '正常' }}
            </a-tag>
          </template>

          <template #ruleName="{ record }">
            <a-tooltip :title="record.ruleDescription">
              {{ record.ruleName }}
            </a-tooltip>
          </template>
        </a-table>
      </a-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button @click="handleExport">
            <template #icon>
              <DownloadOutlined />
            </template>
            导出结果
          </a-button>
          <a-button type="primary" @click="handleCancel">
            关闭
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { ref, computed } from 'vue';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

export default {
  name: 'AlertTestResult',
  components: {
    DownloadOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    testResult: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'cancel'],
  setup(props, { emit }) {
    
    const summary = computed(() => {
      return props.testResult.summary || {
        totalRules: 0,
        alertCount: 0,
        checkedProducts: 0,
        duration: 0
      };
    });

    const alertResults = computed(() => {
      return props.testResult.alerts || [];
    });

    const checkDetails = computed(() => {
      return props.testResult.details || [];
    });

    const alertColumns = [
      {
        title: '商品名称',
        dataIndex: 'productName',
        key: 'productName',
        width: 150
      },
      {
        title: '预警类型',
        dataIndex: 'alertType',
        key: 'alertType',
        width: 100,
        slots: { customRender: 'alertType' }
      },
      {
        title: '预警级别',
        dataIndex: 'alertLevel',
        key: 'alertLevel',
        width: 100,
        slots: { customRender: 'alertLevel' }
      },
      {
        title: '当前库存',
        dataIndex: 'currentStock',
        key: 'currentStock',
        width: 100,
        slots: { customRender: 'currentStock' }
      },
      {
        title: '阈值',
        dataIndex: 'thresholdValue',
        key: 'thresholdValue',
        width: 80,
        slots: { customRender: 'thresholdValue' }
      },
      {
        title: '预警消息',
        dataIndex: 'alertMessage',
        key: 'alertMessage'
      }
    ];

    const detailColumns = [
      {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        width: 150,
        slots: { customRender: 'ruleName' }
      },
      {
        title: '检查状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        slots: { customRender: 'status' }
      },
      {
        title: '检查商品数',
        dataIndex: 'checkedCount',
        key: 'checkedCount',
        width: 100
      },
      {
        title: '触发预警数',
        dataIndex: 'alertCount',
        key: 'alertCount',
        width: 100
      },
      {
        title: '检查耗时',
        dataIndex: 'duration',
        key: 'duration',
        width: 100,
        customRender: ({ text }) => `${text}ms`
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark'
      }
    ];

    const getAlertLevelColor = (level) => {
      const colors = {
        'CRITICAL': 'red',
        'WARNING': 'orange',
        'INFO': 'blue'
      };
      return colors[level] || 'default';
    };

    const getAlertLevelText = (level) => {
      const texts = {
        'CRITICAL': '紧急',
        'WARNING': '警告',
        'INFO': '提醒'
      };
      return texts[level] || level;
    };

    const getAlertTypeColor = (type) => {
      const colors = {
        'LOW_STOCK': 'orange',
        'ZERO_STOCK': 'red',
        'OVERSTOCK': 'purple',
        'EXPIRY': 'volcano'
      };
      return colors[type] || 'default';
    };

    const getAlertTypeText = (type) => {
      const texts = {
        'LOW_STOCK': '库存不足',
        'ZERO_STOCK': '零库存',
        'OVERSTOCK': '库存积压',
        'EXPIRY': '临期预警'
      };
      return texts[type] || type;
    };

    const handleCancel = () => {
      emit('cancel');
    };

    const handleExport = () => {
      // 导出检查结果
      try {
        const data = {
          summary: summary.value,
          alerts: alertResults.value,
          details: checkDetails.value,
          exportTime: new Date().toLocaleString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
          type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `预警检查结果_${new Date().getTime()}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
      }
    };

    return {
      summary,
      alertResults,
      checkDetails,
      alertColumns,
      detailColumns,
      getAlertLevelColor,
      getAlertLevelText,
      getAlertTypeColor,
      getAlertTypeText,
      handleCancel,
      handleExport
    };
  }
};
</script>

<style scoped>
.test-result-container {
  max-height: 70vh;
  overflow-y: auto;
}

.summary-card,
.result-card,
.detail-card {
  margin-bottom: 16px;
}

.no-alert {
  text-align: center;
  padding: 40px 0;
}

.action-buttons {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
}
</style>
