package cn.stylefeng.roses.kernel.erp.modular.supplier.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.SupplierStatsResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 供应商Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/22 16:30
 */
public interface ErpSupplierMapper extends BaseMapper<ErpSupplier> {

    /**
     * 根据供应商编码查询供应商
     *
     * @param supplierCode 供应商编码
     * @return 供应商信息
     */
    @Select("SELECT * FROM erp_supplier WHERE supplier_code = #{supplierCode} AND del_flag = 'N'")
    ErpSupplier getBySupplierCode(@Param("supplierCode") String supplierCode);

    /**
     * 统计区域关联的供应商数量（主表关联）
     *
     * @param regionId 区域ID
     * @return 供应商数量
     */
    @Select("SELECT COUNT(1) FROM erp_supplier WHERE region_id = #{regionId} AND del_flag = 'N'")
    Long countByRegionId(@Param("regionId") Long regionId);

    /**
     * 根据经营方式查询供应商
     *
     * @param businessMode 经营方式
     * @return 供应商列表
     */
    List<ErpSupplier> findByBusinessMode(@Param("businessMode") String businessMode);

    /**
     * 根据经营方式和销售扣点范围查询供应商
     *
     * @param businessMode 经营方式
     * @param minDeduction 最小销售扣点
     * @param maxDeduction 最大销售扣点
     * @return 供应商列表
     */
    List<ErpSupplier> findByBusinessModeAndDeductionRange(@Param("businessMode") String businessMode,
                                                          @Param("minDeduction") BigDecimal minDeduction,
                                                          @Param("maxDeduction") BigDecimal maxDeduction);

    /**
     * 查询有关联商品的供应商
     *
     * @param businessMode 经营方式（可选）
     * @param status 状态（可选）
     * @return 供应商列表
     */
    List<ErpSupplier> findSuppliersWithProducts(@Param("businessMode") String businessMode,
                                                @Param("status") String status);

    /**
     * 查询没有关联商品的供应商
     *
     * @param businessMode 经营方式（可选）
     * @param status 状态（可选）
     * @return 供应商列表
     */
    List<ErpSupplier> findSuppliersWithoutProducts(@Param("businessMode") String businessMode,
                                                   @Param("status") String status);

    /**
     * 统计供应商关联的商品数量
     *
     * @param supplierId 供应商ID
     * @return 商品数量
     */
    Long countProductsBySupplier(@Param("supplierId") Long supplierId);

    /**
     * 获取供应商统计信息
     *
     * @param supplierId 供应商ID（可选，为空则查询所有）
     * @param businessMode 经营方式（可选）
     * @return 供应商统计信息列表
     */
    List<SupplierStatsResponse> getSupplierStats(@Param("supplierId") Long supplierId,
                                                @Param("businessMode") String businessMode);

    /**
     * 根据区域ID列表查询供应商
     *
     * @param regionIds 区域ID列表
     * @param businessMode 经营方式（可选）
     * @param status 状态（可选）
     * @return 供应商列表
     */
    List<ErpSupplier> findByRegionIds(@Param("regionIds") List<Long> regionIds,
                                     @Param("businessMode") String businessMode,
                                     @Param("status") String status);

    /**
     * 模糊搜索供应商
     *
     * @param keyword 搜索关键词
     * @param businessMode 经营方式（可选）
     * @param status 状态（可选）
     * @return 供应商列表
     */
    List<ErpSupplier> searchSuppliers(@Param("keyword") String keyword,
                                     @Param("businessMode") String businessMode,
                                     @Param("status") String status);

    /**
     * 批量更新供应商经营方式
     *
     * @param supplierIds 供应商ID列表
     * @param businessMode 新的经营方式
     * @param salesDeduction 新的销售扣点
     * @param updateUser 更新用户
     * @return 更新数量
     */
    int batchUpdateBusinessMode(@Param("supplierIds") List<Long> supplierIds,
                               @Param("businessMode") String businessMode,
                               @Param("salesDeduction") BigDecimal salesDeduction,
                               @Param("updateUser") Long updateUser);
}