package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRuleRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRuleResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 库存预警规则Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
public interface InventoryAlertRuleMapper extends BaseMapper<InventoryAlertRule> {

    /**
     * 分页查询预警规则
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<InventoryAlertRuleResponse> selectRulePage(Page<InventoryAlertRuleResponse> page,
                                                   @Param("request") InventoryAlertRuleRequest request);

    /**
     * 查询启用的预警规则
     *
     * @return 启用的预警规则列表
     */
    @Select("SELECT * FROM erp_inventory_alert_rule WHERE is_enabled = 'Y' AND del_flag = 'N' " +
            "AND (last_check_time IS NULL OR TIMESTAMPDIFF(MINUTE, last_check_time, NOW()) >= check_frequency)")
    List<InventoryAlertRule> selectEnabledRules();

    /**
     * 根据目标类型和目标ID查询规则
     *
     * @param targetType 目标类型
     * @param targetId   目标ID
     * @return 规则列表
     */
    List<InventoryAlertRule> selectByTarget(@Param("targetType") String targetType,
                                          @Param("targetId") Long targetId);

    /**
     * 检查规则名称是否重复
     *
     * @param ruleName 规则名称
     * @param excludeId 排除的ID（编辑时使用）
     * @return 重复数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM erp_inventory_alert_rule " +
            "WHERE rule_name = #{ruleName} AND del_flag = 'N' " +
            "<if test='excludeId != null'> AND id != #{excludeId} </if>" +
            "</script>")
    int countByRuleName(@Param("ruleName") String ruleName, @Param("excludeId") Long excludeId);

    /**
     * 根据规则类型查询规则数量
     *
     * @param ruleType 规则类型
     * @return 规则数量
     */
    @Select("SELECT COUNT(*) FROM erp_inventory_alert_rule WHERE rule_type = #{ruleType} AND del_flag = 'N'")
    int countByRuleType(@Param("ruleType") String ruleType);

    /**
     * 查询指定商品的预警规则
     *
     * @param productId 商品ID
     * @return 预警规则列表
     */
    List<InventoryAlertRule> selectByProductId(@Param("productId") Long productId);

    /**
     * 查询指定分类的预警规则
     *
     * @param categoryId 分类ID
     * @return 预警规则列表
     */
    List<InventoryAlertRule> selectByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 更新规则的最后检查时间
     *
     * @param ruleId 规则ID
     */
    @Update("UPDATE erp_inventory_alert_rule SET last_check_time = NOW() WHERE id = #{ruleId}")
    void updateLastCheckTime(@Param("ruleId") Long ruleId);
}
