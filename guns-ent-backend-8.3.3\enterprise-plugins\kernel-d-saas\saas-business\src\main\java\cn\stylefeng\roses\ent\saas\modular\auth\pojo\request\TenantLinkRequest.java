package cn.stylefeng.roses.ent.saas.modular.auth.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 租户和功能包的关联
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantLinkRequest extends BaseRequest {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("主键id")
    private Long tenantLinkId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 功能包id
     */
    @NotNull(message = "功能包id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("功能包id")
    private Long packageId;

    /**
     * 服务结束时间，空则是长期
     */
    @ChineseDescription("服务结束时间，空则是长期")
    private Date serviceEndTime;

    /**
     * 是否为试用：Y-试用，N-非试用
     */
    @NotBlank(message = "是否为试用：Y-试用，N-非试用不能为空", groups = {add.class, edit.class})
    @ChineseDescription("是否为试用：Y-试用，N-非试用")
    private String trialFlag;

    /**
     * 批量删除用的id集合
     */
    @NotNull(message = "批量删除id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("批量删除用的id集合")
    private List<Long> batchDeleteIdList;

}
