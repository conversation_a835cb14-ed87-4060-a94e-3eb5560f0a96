/**
 * 通用树结构组件工具函数
 * 
 * <AUTHOR>
 * @since 2025/01/24
 */

import { TreeErrorType } from './types.js'

/**
 * 树数据适配器
 */
export class TreeDataAdapter {
  /**
   * 格式化树形数据
   * @param {any[]} data 原始数据
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @returns {TreeNode[]} 格式化后的树形数据
   */
  static formatTreeData(data, fieldMapping) {
    if (!data || !data.length) return []

    return data.map(item => ({
      ...item,
      // 统一字段映射
      key: String(item[fieldMapping.key]),
      title: item[fieldMapping.title],
      children: item[fieldMapping.children] ? 
        this.formatTreeData(item[fieldMapping.children], fieldMapping) : [],
      // 设置叶子节点标识
      isLeaf: fieldMapping.hasChildren ? 
        !item[fieldMapping.hasChildren] : 
        (!item[fieldMapping.children] || item[fieldMapping.children].length === 0)
    }))
  }

  /**
   * 提取所有节点ID
   * @param {TreeNode[]} data 树形数据
   * @returns {string[]} 所有节点ID数组
   */
  static extractAllIds(data) {
    const ids = []
    const extract = (nodes) => {
      nodes.forEach(node => {
        ids.push(node.key)
        if (node.children && node.children.length > 0) {
          extract(node.children)
        }
      })
    }
    extract(data)
    return ids
  }

  /**
   * 按层级提取节点ID
   * @param {TreeNode[]} data 树形数据
   * @param {number} maxLevel 最大层级
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @returns {string[]} 指定层级内的节点ID数组
   */
  static extractNodeIdsByLevel(data, maxLevel, fieldMapping) {
    const ids = []
    const extract = (nodes) => {
      nodes.forEach(node => {
        const level = node[fieldMapping.level || 'level'] || 1
        if (level <= maxLevel) {
          ids.push(node.key)
        }
        if (node.children && node.children.length > 0 && level < maxLevel) {
          extract(node.children)
        }
      })
    }
    extract(data)
    return ids
  }

  /**
   * 设置叶子节点标识
   * @param {any[]} data 原始数据
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @param {number} [maxLevel] 最大层级（可选）
   * @returns {any[]} 处理后的数据
   */
  static setIsLeaf(data, fieldMapping, maxLevel) {
    if (!data || !data.length) return []

    return data.map(item => {
      // 确保key是字符串类型
      item[fieldMapping.key] = String(item[fieldMapping.key])
      
      // 优先使用后端返回的hasChildren字段判断
      if (fieldMapping.hasChildren && item[fieldMapping.hasChildren] !== undefined) {
        item.isLeaf = !item[fieldMapping.hasChildren]
      } else {
        // 如果没有hasChildren字段，根据是否有子节点和层级判断
        const hasChildrenData = item[fieldMapping.children] && item[fieldMapping.children].length > 0
        const currentLevel = item[fieldMapping.level || 'level'] || 1
        const isMaxLevel = maxLevel ? currentLevel >= maxLevel : false
        item.isLeaf = !hasChildrenData && isMaxLevel
      }

      // 递归处理子节点
      if (item[fieldMapping.children] && item[fieldMapping.children].length > 0) {
        item[fieldMapping.children] = this.setIsLeaf(item[fieldMapping.children], fieldMapping, maxLevel)
      }

      return item
    })
  }

  /**
   * 搜索树节点
   * @param {TreeNode[]} data 树形数据
   * @param {string} searchText 搜索文本
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @returns {TreeNode[]} 搜索结果
   */
  static searchTreeNodes(data, searchText, fieldMapping) {
    if (!searchText || !searchText.trim()) return data

    const searchLower = searchText.toLowerCase().trim()
    const result = []

    const searchInNode = (node) => {
      const title = node[fieldMapping.title] || ''
      const titleMatch = title.toLowerCase().includes(searchLower)
      
      let childrenMatch = []
      if (node.children && node.children.length > 0) {
        childrenMatch = node.children.map(searchInNode).filter(Boolean)
      }

      if (titleMatch || childrenMatch.length > 0) {
        return {
          ...node,
          children: childrenMatch
        }
      }

      return null
    }

    data.forEach(node => {
      const matchedNode = searchInNode(node)
      if (matchedNode) {
        result.push(matchedNode)
      }
    })

    return result
  }

  /**
   * 获取节点路径
   * @param {TreeNode[]} data 树形数据
   * @param {string} targetKey 目标节点key
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @returns {TreeNode[]} 节点路径数组
   */
  static getNodePath(data, targetKey, fieldMapping) {
    const path = []

    const findPath = (nodes, target, currentPath) => {
      for (const node of nodes) {
        const newPath = [...currentPath, node]
        
        if (String(node[fieldMapping.key]) === String(target)) {
          path.push(...newPath)
          return true
        }

        if (node.children && node.children.length > 0) {
          if (findPath(node.children, target, newPath)) {
            return true
          }
        }
      }
      return false
    }

    findPath(data, targetKey, [])
    return path
  }

  /**
   * 获取所有父节点keys
   * @param {TreeNode[]} data 树形数据
   * @param {string} targetKey 目标节点key
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @returns {string[]} 父节点keys数组
   */
  static getParentKeys(data, targetKey, fieldMapping) {
    const path = this.getNodePath(data, targetKey, fieldMapping)
    return path.slice(0, -1).map(node => String(node[fieldMapping.key]))
  }

  /**
   * 展开到指定节点
   * @param {TreeNode[]} data 树形数据
   * @param {string} targetKey 目标节点key
   * @param {FieldMapping} fieldMapping 字段映射配置
   * @returns {string[]} 需要展开的节点keys
   */
  static expandToNode(data, targetKey, fieldMapping) {
    return this.getParentKeys(data, targetKey, fieldMapping)
  }

  /**
   * 验证配置完整性
   * @param {TreeConfig} config 树配置
   * @returns {Object} 验证结果
   */
  static validateConfig(config) {
    const errors = []
    const warnings = []

    // 检查必需的配置
    if (!config.dataSource || !config.dataSource.api) {
      errors.push('dataSource.api 是必需的')
    }

    if (!config.fieldMapping) {
      errors.push('fieldMapping 是必需的')
    } else {
      if (!config.fieldMapping.key) {
        errors.push('fieldMapping.key 是必需的')
      }
      if (!config.fieldMapping.title) {
        errors.push('fieldMapping.title 是必需的')
      }
      if (!config.fieldMapping.children) {
        errors.push('fieldMapping.children 是必需的')
      }
    }

    // 检查可选但推荐的配置
    if (!config.displayConfig) {
      warnings.push('建议提供 displayConfig 配置')
    }

    if (!config.interactionConfig) {
      warnings.push('建议提供 interactionConfig 配置')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

/**
 * 树错误处理器
 */
export class TreeErrorHandler {
  /**
   * 处理错误
   * @param {TreeError} error 错误对象
   * @param {any} context 上下文信息
   * @returns {Object} 错误处理结果
   */
  static handleError(error, context) {
    switch (error.type) {
      case TreeErrorType.DATA_LOAD_ERROR:
        return this.handleDataLoadError(error, context)
      case TreeErrorType.LAZY_LOAD_ERROR:
        return this.handleLazyLoadError(error, context)
      case TreeErrorType.SEARCH_ERROR:
        return this.handleSearchError(error, context)
      case TreeErrorType.ACTION_ERROR:
        return this.handleActionError(error, context)
      case TreeErrorType.CONFIG_ERROR:
        return this.handleConfigError(error, context)
      default:
        return this.handleUnknownError(error, context)
    }
  }

  /**
   * 处理数据加载错误
   */
  static handleDataLoadError(error, _context) {
    console.error('树数据加载失败:', error)
    return {
      showRetry: true,
      message: '数据加载失败，请重试'
    }
  }

  /**
   * 处理懒加载错误
   */
  static handleLazyLoadError(error, _context) {
    console.error('懒加载失败:', error)
    return {
      showNodeRetry: true,
      message: '子节点加载失败'
    }
  }

  /**
   * 处理搜索错误
   */
  static handleSearchError(error, _context) {
    console.error('搜索失败:', error)
    return {
      clearSearch: true,
      message: '搜索失败，请重试'
    }
  }

  /**
   * 处理操作错误
   */
  static handleActionError(error, _context) {
    console.error('操作失败:', error)
    return {
      message: error.message || '操作失败'
    }
  }

  /**
   * 处理配置错误
   */
  static handleConfigError(error, _context) {
    console.error('配置错误:', error)
    return {
      message: '组件配置错误'
    }
  }

  /**
   * 处理未知错误
   */
  static handleUnknownError(error, _context) {
    console.error('未知错误:', error)
    return {
      message: '发生未知错误'
    }
  }
}

/**
 * 合并配置对象
 * @param {Object} defaultConfig 默认配置
 * @param {Object} userConfig 用户配置
 * @returns {Object} 合并后的配置
 */
export function mergeConfig(defaultConfig, userConfig) {
  return {
    ...defaultConfig,
    ...userConfig
  }
}

/**
 * 深度合并配置对象
 * @param {Object} defaultConfig 默认配置
 * @param {Object} userConfig 用户配置
 * @returns {Object} 深度合并后的配置
 */
export function deepMergeConfig(defaultConfig, userConfig) {
  const result = { ...defaultConfig }
  
  for (const key in userConfig) {
    if (Object.prototype.hasOwnProperty.call(userConfig, key)) {
      const userValue = userConfig[key]
      const defaultValue = defaultConfig[key]
      
      if (userValue !== undefined) {
        if (typeof userValue === 'object' && userValue !== null && !Array.isArray(userValue) &&
            typeof defaultValue === 'object' && defaultValue !== null && !Array.isArray(defaultValue)) {
          result[key] = deepMergeConfig(defaultValue, userValue)
        } else {
          result[key] = userValue
        }
      }
    }
  }
  
  return result
}

/**
 * 创建默认配置
 * @returns {TreeConfig} 默认配置对象
 */
export function createDefaultConfig() {
  return {
    dataSource: {
      api: null,
      lazyLoadApi: null,
      searchParam: 'searchText',
      parentIdParam: 'parentId'
    },
    fieldMapping: {
      key: 'id',
      title: 'name',
      children: 'children',
      hasChildren: 'hasChildren',
      level: 'level'
    },
    displayConfig: {
      title: '',
      showHeader: true,
      showSearch: true,
      searchPlaceholder: '请输入关键字搜索',
      showAddButton: false,
      showEditIcons: false,
      showIcon: false,
      isSetWidth: true
    },
    interactionConfig: {
      selectable: true,
      expandable: true,
      lazyLoad: false,
      defaultExpandLevel: 1,
      allowMultiSelect: false
    },
    actionConfig: {
      allowAdd: false,
      allowEdit: false,
      allowDelete: false,
      customActions: []
    }
  }
}

/**
 * 配置构建器类
 */
export class TreeConfigBuilder {
  constructor() {
    this.config = createDefaultConfig()
  }

  /**
   * 设置数据源
   */
  setDataSource(api, lazyLoadApi = null, searchParam = 'searchText', parentIdParam = 'parentId') {
    this.config.dataSource = {
      api,
      lazyLoadApi,
      searchParam,
      parentIdParam
    }
    return this
  }

  /**
   * 设置字段映射
   */
  setFieldMapping(key, title, children, hasChildren = null, level = null) {
    this.config.fieldMapping = {
      key,
      title,
      children,
      hasChildren,
      level
    }
    return this
  }

  /**
   * 设置显示配置
   */
  setDisplayConfig(options) {
    this.config.displayConfig = {
      ...this.config.displayConfig,
      ...options
    }
    return this
  }

  /**
   * 设置交互配置
   */
  setInteractionConfig(options) {
    this.config.interactionConfig = {
      ...this.config.interactionConfig,
      ...options
    }
    return this
  }

  /**
   * 设置操作配置
   */
  setActionConfig(options) {
    this.config.actionConfig = {
      ...this.config.actionConfig,
      ...options
    }
    return this
  }

  /**
   * 启用只读模式
   */
  enableReadOnlyMode() {
    this.config.displayConfig.showAddButton = false
    this.config.displayConfig.showEditIcons = false
    this.config.actionConfig.allowAdd = false
    this.config.actionConfig.allowEdit = false
    this.config.actionConfig.allowDelete = false
    return this
  }

  /**
   * 启用编辑模式
   */
  enableEditMode() {
    this.config.displayConfig.showAddButton = true
    this.config.displayConfig.showEditIcons = true
    this.config.actionConfig.allowAdd = true
    this.config.actionConfig.allowEdit = true
    this.config.actionConfig.allowDelete = true
    return this
  }

  /**
   * 构建配置
   */
  build() {
    const validation = TreeDataAdapter.validateConfig(this.config)
    if (!validation.isValid) {
      console.warn('配置验证失败:', validation.errors)
    }
    if (validation.warnings.length > 0) {
      console.warn('配置警告:', validation.warnings)
    }
    return { ...this.config }
  }
}