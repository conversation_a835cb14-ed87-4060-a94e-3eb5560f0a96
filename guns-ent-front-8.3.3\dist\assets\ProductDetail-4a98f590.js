import{_ as p,a as u,f as g,w as a,d as i,g as o,t as n,c as b,b as N,h,Y as C,U as S,Z as V,V as v,M as E}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{P as r}from"./ProductApi-52d42f8e.js";const L={name:"ProductDetail",components:{},props:{visible:Boolean,data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(P,{emit:c}){return{updateVisible:e=>{c("update:visible",e)},getProductStatusName:e=>r.getProductStatusName(e),getStatusTagColor:e=>r.getStatusTagColor(e),getPricingTypeName:e=>r.getPricingTypeName(e),getPricingTypeTagColor:e=>r.getPricingTypeTagColor(e),formatWeight:e=>r.formatWeight(e),formatVolume:e=>r.formatVolume(e),formatShelfLife:e=>r.formatShelfLife(e),formatPrice:e=>{if(!e.pricingType)return"-";switch(e.pricingType){case"NORMAL":return e.retailPrice?r.formatPrice(e.retailPrice,"NORMAL"):"-";case"WEIGHT":return e.unitPrice?r.formatPrice(e.unitPrice,"WEIGHT"):"-";case"PIECE":return e.piecePrice?r.formatPrice(e.piecePrice,"PIECE"):"-";case"VARIABLE":return e.referencePrice?r.formatPrice(e.referencePrice,"VARIABLE"):"-";default:return"-"}}}}},k={key:1},I={key:0,style:{padding:"16px",background:"#f5f5f5","border-radius":"6px"}};function x(P,c,t,d,T,y){const l=C,f=S,m=V,s=v,_=E;return u(),g(_,{title:"\u5546\u54C1\u8BE6\u60C5",width:800,visible:t.visible,footer:null,"onUpdate:visible":d.updateVisible},{default:a(()=>[i(m,{column:2,bordered:""},{default:a(()=>[i(l,{label:"\u5546\u54C1\u7F16\u7801"},{default:a(()=>[o(n(t.data.productCode),1)]),_:1}),i(l,{label:"\u5546\u54C1\u540D\u79F0"},{default:a(()=>[o(n(t.data.productName),1)]),_:1}),i(l,{label:"\u5546\u54C1\u7B80\u79F0"},{default:a(()=>[o(n(t.data.productShortName||"-"),1)]),_:1}),i(l,{label:"\u6761\u5F62\u7801"},{default:a(()=>[o(n(t.data.barcode||"-"),1)]),_:1}),i(l,{label:"\u54C1\u724C"},{default:a(()=>[o(n(t.data.brand||"-"),1)]),_:1}),i(l,{label:"\u89C4\u683C"},{default:a(()=>[o(n(t.data.specification||"-"),1)]),_:1}),i(l,{label:"\u57FA\u672C\u5355\u4F4D"},{default:a(()=>[o(n(t.data.unit),1)]),_:1}),i(l,{label:"\u72B6\u6001"},{default:a(()=>[i(f,{color:d.getStatusTagColor(t.data.status)},{default:a(()=>[o(n(d.getProductStatusName(t.data.status)),1)]),_:1},8,["color"])]),_:1}),i(l,{label:"\u91CD\u91CF"},{default:a(()=>[o(n(d.formatWeight(t.data.weight)),1)]),_:1}),i(l,{label:"\u4F53\u79EF"},{default:a(()=>[o(n(d.formatVolume(t.data.volume)),1)]),_:1}),i(l,{label:"\u4FDD\u8D28\u671F"},{default:a(()=>[o(n(d.formatShelfLife(t.data.shelfLife)),1)]),_:1}),i(l,{label:"\u5546\u54C1\u5206\u7C7B"},{default:a(()=>[o(n(t.data.categoryName||"-"),1)]),_:1}),i(l,{label:"\u4F9B\u5E94\u5546"},{default:a(()=>[o(n(t.data.supplierName||"-"),1)]),_:1}),i(l,{label:"\u8BA1\u4EF7\u7C7B\u578B"},{default:a(()=>[t.data.pricingType?(u(),g(f,{key:0,color:d.getPricingTypeTagColor(t.data.pricingType)},{default:a(()=>[o(n(d.getPricingTypeName(t.data.pricingType)),1)]),_:1},8,["color"])):(u(),b("span",k,"-"))]),_:1}),i(l,{label:"\u4EF7\u683C\u4FE1\u606F"},{default:a(()=>[o(n(d.formatPrice(t.data)),1)]),_:1}),i(l,{label:"\u5B58\u50A8\u6761\u4EF6",span:2},{default:a(()=>[o(n(t.data.storageCondition||"-"),1)]),_:1}),i(l,{label:"\u5907\u6CE8",span:2},{default:a(()=>[o(n(t.data.remark||"-"),1)]),_:1}),i(l,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:a(()=>[o(n(t.data.createTime),1)]),_:1}),i(l,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:a(()=>[o(n(t.data.updateTime),1)]),_:1})]),_:1}),i(s,{orientation:"left"},{default:a(()=>c[0]||(c[0]=[o("\u5E93\u5B58\u4FE1\u606F")])),_:1,__:[0]}),t.data.productId?(u(),b("div",I,c[1]||(c[1]=[N("p",null,"\u5E93\u5B58\u4FE1\u606F\u529F\u80FD\u5F00\u53D1\u4E2D...",-1)]))):h("",!0)]),_:1},8,["visible","onUpdate:visible"])}const D=p(L,[["render",x]]);export{D as default};
