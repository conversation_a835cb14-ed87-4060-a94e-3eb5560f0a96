package cn.stylefeng.roses.kernel.sync.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 职位同步信息
 *
 * <AUTHOR>
 * @since 2023/10/30 13:38
 */
@Data
public class PositionSyncVo {

    /**
     * 主键
     */
    @ChineseDescription("主键")
    private String positionId;

    /**
     * 职位名称
     */
    @ChineseDescription("职位名称")
    private String positionName;

    /**
     * 职位编码
     */
    @ChineseDescription("职位编码")
    private String positionCode;

    /**
     * 排序
     */
    @ChineseDescription("排序")
    private BigDecimal positionSort;

    /**
     * 状态：1-启用，2-禁用
     */
    @ChineseDescription("状态：1-启用，2-禁用")
    private Integer statusFlag;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

}
