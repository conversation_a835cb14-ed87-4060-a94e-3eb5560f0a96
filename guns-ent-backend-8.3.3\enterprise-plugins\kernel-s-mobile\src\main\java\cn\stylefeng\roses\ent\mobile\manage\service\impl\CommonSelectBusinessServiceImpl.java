package cn.stylefeng.roses.ent.mobile.manage.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.ent.mobile.manage.enums.CommonSelectTypeEnum;
import cn.stylefeng.roses.ent.mobile.manage.factory.CommonOrgUserFactory;
import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserItem;
import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserRequest;
import cn.stylefeng.roses.ent.mobile.manage.service.CommonSelectBusinessService;
import cn.stylefeng.roses.kernel.db.mp.datascope.UserRoleDataScopeApi;
import cn.stylefeng.roses.kernel.db.mp.datascope.config.DataScopeConfig;
import cn.stylefeng.roses.kernel.db.mp.datascope.holder.DataScopeHolder;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserOrgService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用选择机构选择人员组件的接口
 *
 * <AUTHOR>
 * @since 2024-04-02 13:51
 */
@Service
public class CommonSelectBusinessServiceImpl implements CommonSelectBusinessService {

    @Resource
    private UserRoleDataScopeApi userRoleDataScopeApi;

    @Resource
    private HrOrganizationService hrOrganizationService;

    @Resource
    private SysUserOrgService sysUserOrgService;

    @Resource
    private SysUserService sysUserService;

    @Override
    public List<OrgUserItem> commonGetOrgUserList(OrgUserRequest orgUserRequest) {

        // 获取用户数据范围配置
        DataScopeConfig userRoleDataScopeConfig = userRoleDataScopeApi.getUserRoleDataScopeConfig();
        List<HrOrganization> orgList;
        List<SysUserOrg> sysUserOrgList;

        try {
            // 设置数据范围限制
            userRoleDataScopeConfig.setUserIdFieldName("create_user");
            DataScopeHolder.set(userRoleDataScopeConfig);

            // 1. 查询指定机构下子一级的所有公司和部门
            orgList = this.getOrgList(orgUserRequest.getOrgId());

            // 2. 查询这个机构下的所有人员
            sysUserOrgList = this.getPersonAndPositionList(orgUserRequest.getOrgId());

        } finally {
            DataScopeHolder.remove();
        }

        // 人员信息为空，则直接返回机构列表
        if (ObjectUtil.isEmpty(sysUserOrgList) || CommonSelectTypeEnum.ORG.getCode().equals(orgUserRequest.getSelectType())) {
            return CommonOrgUserFactory.createOrgUserList(orgList, null);
        }

        // 3. 根据人员id，查询到所有的人员信息，包含头像，姓名，是否是管理员，用户状态
        List<SysUser> sysUserList = this.getUserDetailList(orgUserRequest.getSearchText(), sysUserOrgList);

        return CommonOrgUserFactory.createOrgUserList(orgList, sysUserList);
    }

    @Override
    public List<HrOrganization> getOrgList(Long orgId) {
        List<HrOrganization> orgList;
        LambdaQueryWrapper<HrOrganization> orgWrapper = new LambdaQueryWrapper<>();
        orgWrapper.eq(HrOrganization::getOrgParentId, orgId);
        orgWrapper.eq(HrOrganization::getStatusFlag, StatusEnum.ENABLE.getCode());
        orgWrapper.select(HrOrganization::getOrgId, HrOrganization::getOrgName, HrOrganization::getOrgType);
        orgWrapper.orderByAsc(HrOrganization::getOrgSort);
        orgList = hrOrganizationService.list(orgWrapper);
        return orgList;
    }

    @Override
    public List<SysUserOrg> getPersonAndPositionList(Long orgId) {
        List<SysUserOrg> sysUserOrgList;
        LambdaQueryWrapper<SysUserOrg> userOrgWrapper = new LambdaQueryWrapper<>();
        userOrgWrapper.eq(SysUserOrg::getOrgId, orgId);
        userOrgWrapper.select(SysUserOrg::getUserId, SysUserOrg::getPositionId);
        sysUserOrgList = sysUserOrgService.list(userOrgWrapper);
        return sysUserOrgList;
    }

    @Override
    public List<SysUser> getUserDetailList(String searchText, List<SysUserOrg> sysUserOrgList) {
        List<Long> userIdList = sysUserOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(SysUser::getUserId, userIdList);

        // 根据人员的姓名和手机号查询
        if (StrUtil.isNotBlank(searchText)) {
            userWrapper.nested(i -> i.like(SysUser::getRealName, searchText).or().like(SysUser::getPhone, searchText));
        }

        userWrapper.orderByAsc(SysUser::getUserSort);
        userWrapper.select(SysUser::getUserId, SysUser::getAvatar, SysUser::getRealName, SysUser::getSuperAdminFlag, SysUser::getStatusFlag);
        return sysUserService.list(userWrapper);
    }

}
