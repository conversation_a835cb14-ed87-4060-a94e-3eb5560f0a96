System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-0d30ef09.js","./index-legacy-b540c599.js","./regionApi-legacy-73888494.js"],(function(l,e){"use strict";var a,n,o,t,u,d,i,r,s,p,v,g,c,m,h,f,y,x,_;return{setters:[l=>{a=l._,n=l.r,o=l.s,t=l.a,u=l.c,d=l.d,i=l.w,r=l.b,s=l.g,p=l.t,v=l.F,g=l.e,c=l.m,m=l.B,h=l.n,f=l.u,y=l.H,x=l.a0},null,l=>{_=l.R},null,null],execute:function(){var e=document.createElement("style");e.textContent="h3[data-v-b676df9b]{color:#1890ff;margin-bottom:16px}h4[data-v-b676df9b]{margin-bottom:8px;font-size:14px}\n",document.head.appendChild(e);const V={class:"guns-body guns-body-card"},b={style:{"margin-top":"8px"}},w={style:{"margin-top":"4px"}},C={style:{"margin-top":"8px"}},S={style:{"margin-top":"4px"}},R={style:{"margin-top":"8px"}},k={style:{"margin-top":"8px"}},U={style:{"margin-top":"4px"}},$={style:{"margin-top":"8px"}},j={style:{"margin-top":"8px"}},N={style:{"max-height":"200px","overflow-y":"auto",border:"1px solid #d9d9d9",padding:"8px",background:"#fafafa"}},A={style:{color:"#666"}},J={style:{color:"#1890ff"}},z={__name:"index",setup(l){const e=n([]),a=n([]),z=n(null),O=n(null),B=n([]),D=n([]),H=n([]),M=n([]),E=n([]),F=n([]),I=n([]),K=n([]),q=o({supplierRegions:[],customerRegions:[]}),G=n([]),L=n(),P=(l,e)=>{a.value=e,console.log("多选变化:",l,e)},Q=(l,e)=>{O.value=Array.isArray(e)?e[0]:e,console.log("单选变化:",l,e)},T=(l,e)=>{console.log("带按钮选择变化:",l,e),c.success(`选择了 ${e.length} 个区域`)},W=(l,e)=>{Z("change",`值: ${JSON.stringify(l)}, 区域数量: ${e.length}`)},X=l=>{Z("search",`搜索文本: ${l}`)},Y=()=>{Z("clear","清空选择")},Z=(l,e)=>{const a=new Date,n=`${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}:${a.getSeconds().toString().padStart(2,"0")}`;G.value.unshift({time:n,event:l,data:e}),G.value.length>50&&(G.value=G.value.slice(0,50))},ll=()=>{var l;null===(l=L.value)||void 0===l||l.clearSelection(),c.info("已清空选择")},el=()=>{var l;const e=null===(l=L.value)||void 0===l?void 0:l.getSelectedRegions();console.log("当前选中的区域:",e),c.info(`当前选中 ${(null==e?void 0:e.length)||0} 个区域`)},al=()=>{var l;null===(l=L.value)||void 0===l||l.reloadData(),c.info("正在重新加载数据...")},nl=()=>{D.value=["1","2"],c.info("已设置只读值")},ol=()=>{console.log("表单数据:",q),c.success("表单提交成功")},tl=()=>{q.supplierRegions=[],q.customerRegions=[],c.info("表单已重置")},ul=(l,e)=>{K.value=e,console.log("修复测试变化:",l,e),Z("fix-test-change",`选中 ${e.length} 个区域`)},dl=()=>{I.value=["1947278275361439745","1947278275361439746"],c.info("已设置测试数据，验证数据回显是否正常")},il=()=>{I.value=[],K.value=[],c.info("已清空测试数据")};return(l,n)=>{const o=m,c=h,Z=f,rl=y,sl=x;return t(),u("div",V,[d(sl,{title:"区域选择组件测试",bordered:!1},{default:i((()=>[d(c,{direction:"vertical",size:"large",style:{width:"100%"}},{default:i((()=>{var l;return[r("div",null,[n[13]||(n[13]=r("h3",null,"基本多选模式（修复后测试）",-1)),d(_,{modelValue:e.value,"onUpdate:modelValue":n[0]||(n[0]=l=>e.value=l),placeholder:"请选择多个区域",onChange:P,style:{width:"400px"}},null,8,["modelValue"]),r("div",b,[n[11]||(n[11]=r("strong",null,"选中值:",-1)),s(" "+p(JSON.stringify(e.value)),1)]),r("div",w,[n[12]||(n[12]=r("strong",null,"选中区域:",-1)),s(" "+p(a.value.map((l=>l.regionName)).join(", ")),1)]),n[14]||(n[14]=r("div",{style:{"margin-top":"4px",color:"#666","font-size":"12px"}}," 测试要点：1. 树节点名称是否正确显示 2. 勾选后是否保持选中状态 3. 数据是否正确更新 ",-1))]),r("div",null,[n[17]||(n[17]=r("h3",null,"单选模式",-1)),d(_,{modelValue:z.value,"onUpdate:modelValue":n[1]||(n[1]=l=>z.value=l),multiple:!1,placeholder:"请选择一个区域",onChange:Q,style:{width:"400px"}},null,8,["modelValue"]),r("div",C,[n[15]||(n[15]=r("strong",null,"选中值:",-1)),s(" "+p(z.value),1)]),r("div",S,[n[16]||(n[16]=r("strong",null,"选中区域:",-1)),s(" "+p((null===(l=O.value)||void 0===l?void 0:l.regionName)||"无"),1)])]),r("div",null,[n[18]||(n[18]=r("h3",null,"禁用搜索功能",-1)),d(_,{modelValue:B.value,"onUpdate:modelValue":n[2]||(n[2]=l=>B.value=l),showSearch:!1,placeholder:"无搜索功能的区域选择",style:{width:"400px"}},null,8,["modelValue"])]),r("div",null,[n[20]||(n[20]=r("h3",null,"只读状态",-1)),d(_,{modelValue:D.value,"onUpdate:modelValue":n[3]||(n[3]=l=>D.value=l),readonly:"",placeholder:"只读状态",style:{width:"400px"}},null,8,["modelValue"]),d(o,{onClick:nl,style:{"margin-left":"8px"}},{default:i((()=>n[19]||(n[19]=[s("设置值")]))),_:1,__:[19]})]),r("div",null,[n[21]||(n[21]=r("h3",null,"禁用状态",-1)),d(_,{modelValue:H.value,"onUpdate:modelValue":n[4]||(n[4]=l=>H.value=l),disabled:"",placeholder:"禁用状态",style:{width:"400px"}},null,8,["modelValue"])]),r("div",null,[n[22]||(n[22]=r("h3",null,"带操作按钮",-1)),d(_,{modelValue:M.value,"onUpdate:modelValue":n[5]||(n[5]=l=>M.value=l),showActionButtons:!0,placeholder:"带确定/取消按钮",onChange:T,style:{width:"400px"}},null,8,["modelValue"])]),r("div",null,[n[26]||(n[26]=r("h3",null,"组件方法测试",-1)),d(_,{ref_key:"regionSelectorRef",ref:L,modelValue:E.value,"onUpdate:modelValue":n[6]||(n[6]=l=>E.value=l),placeholder:"方法测试",style:{width:"400px"}},null,8,["modelValue"]),r("div",R,[d(c,null,{default:i((()=>[d(o,{onClick:ll},{default:i((()=>n[23]||(n[23]=[s("清空选择")]))),_:1,__:[23]}),d(o,{onClick:el},{default:i((()=>n[24]||(n[24]=[s("获取选中区域")]))),_:1,__:[24]}),d(o,{onClick:al},{default:i((()=>n[25]||(n[25]=[s("重新加载数据")]))),_:1,__:[25]})])),_:1})])]),r("div",null,[n[29]||(n[29]=r("h3",null,"表单集成测试",-1)),d(rl,{model:q,layout:"vertical",style:{width:"400px"}},{default:i((()=>[d(Z,{label:"供应商关联区域",name:"supplierRegions"},{default:i((()=>[d(_,{modelValue:q.supplierRegions,"onUpdate:modelValue":n[7]||(n[7]=l=>q.supplierRegions=l),placeholder:"请选择供应商关联的区域"},null,8,["modelValue"])])),_:1}),d(Z,{label:"客户关联区域",name:"customerRegions"},{default:i((()=>[d(_,{modelValue:q.customerRegions,"onUpdate:modelValue":n[8]||(n[8]=l=>q.customerRegions=l),placeholder:"请选择客户关联的区域"},null,8,["modelValue"])])),_:1}),d(Z,null,{default:i((()=>[d(c,null,{default:i((()=>[d(o,{type:"primary",onClick:ol},{default:i((()=>n[27]||(n[27]=[s("提交表单")]))),_:1,__:[27]}),d(o,{onClick:tl},{default:i((()=>n[28]||(n[28]=[s("重置表单")]))),_:1,__:[28]})])),_:1})])),_:1})])),_:1},8,["model"])]),r("div",null,[n[34]||(n[34]=r("h3",null,"问题修复验证测试",-1)),d(_,{modelValue:I.value,"onUpdate:modelValue":n[9]||(n[9]=l=>I.value=l),placeholder:"验证修复效果",onChange:ul,style:{width:"400px"}},null,8,["modelValue"]),r("div",k,[n[30]||(n[30]=r("strong",null,"当前选中:",-1)),s(" "+p(JSON.stringify(I.value)),1)]),r("div",U,[n[31]||(n[31]=r("strong",null,"选中区域:",-1)),s(" "+p(K.value.map((l=>l.regionName)).join(", ")),1)]),r("div",$,[d(c,null,{default:i((()=>[d(o,{onClick:dl},{default:i((()=>n[32]||(n[32]=[s("设置测试数据")]))),_:1,__:[32]}),d(o,{onClick:il},{default:i((()=>n[33]||(n[33]=[s("清空数据")]))),_:1,__:[33]})])),_:1})]),n[35]||(n[35]=r("div",{style:{"margin-top":"8px",padding:"8px",background:"#f0f9ff",border:"1px solid #bae6fd","border-radius":"4px"}},[r("h4",{style:{margin:"0 0 8px 0",color:"#0369a1"}},"修复验证要点："),r("ul",{style:{margin:"0","padding-left":"20px",color:"#0369a1"}},[r("li",null,"树节点名称是否正确显示（不再是空白）"),r("li",null,"勾选节点后是否保持选中状态（不会自动取消）"),r("li",null,"数据回显是否正常工作"),r("li",null,"多选操作是否流畅")])],-1))]),r("div",null,[n[38]||(n[38]=r("h3",null,"事件监听测试",-1)),d(_,{modelValue:F.value,"onUpdate:modelValue":n[10]||(n[10]=l=>F.value=l),placeholder:"事件测试",onChange:W,onSearch:X,onClear:Y,style:{width:"400px"}},null,8,["modelValue"]),r("div",j,[n[37]||(n[37]=r("h4",null,"事件日志:",-1)),r("div",N,[(t(!0),u(v,null,g(G.value,((l,e)=>(t(),u("div",{key:e,style:{"margin-bottom":"4px"}},[r("span",A,p(l.time),1),n[36]||(n[36]=s(" - ")),r("span",J,p(l.event),1),s(": "+p(l.data),1)])))),128))])])])]})),_:1})])),_:1})])}}};l("default",a(z,[["__scopeId","data-v-b676df9b"]]))}}}));
