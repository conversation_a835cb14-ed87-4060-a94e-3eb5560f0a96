package cn.stylefeng.roses.kernel.saas.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.saas.api.constants.SaasConstants;

/**
 * SaaS的异常
 *
 * <AUTHOR>
 * @date 2021/2/18 13:57
 */
public class SaasException extends ServiceException {

    public SaasException(AbstractExceptionEnum exception, Object... params) {
        super(SaasConstants.SAAS_SERVER_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public SaasException(AbstractExceptionEnum exception) {
        super(SaasConstants.SAAS_SERVER_MODULE_NAME, exception);
    }

}
