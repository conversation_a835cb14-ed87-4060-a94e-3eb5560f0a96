package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

/**
 * 生日的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 22:37
 */
public class BirthdayValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(originValue);
        }

        if (originValue.length() != 10) {
            return new ExcelLineParseResult(false, originValue, originValue, "生日格式不对请检查格式是否为：YYYY-MM-DD");
        }

        try {
            DateTime dateTime = DateUtil.parseDate(originValue);
            return new ExcelLineParseResult(originValue);
        } catch (Exception e) {
            return new ExcelLineParseResult(false, originValue, originValue, "生日格式不对请检查格式是否为：YYYY-MM-DD");
        }
    }

}
