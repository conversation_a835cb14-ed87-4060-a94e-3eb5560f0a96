package cn.stylefeng.roses.kernel.erp.modular.purchase.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseConfirmRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseReceiveRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderResponse;
import cn.stylefeng.roses.kernel.erp.modular.purchase.service.PurchaseOrderService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 采购入库单控制器
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@RestController
@ApiResource(name = "采购入库单管理")
public class PurchaseOrderController {

    @Resource
    private PurchaseOrderService purchaseOrderService;

    /**
     * 新增采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @PostResource(name = "新增采购入库单", path = "/purchase/order/add")
    public ResponseData<Long> add(@RequestBody @Validated(PurchaseOrderRequest.add.class) PurchaseOrderRequest purchaseOrderRequest) {
        Long orderId = purchaseOrderService.add(purchaseOrderRequest);
        return new SuccessResponseData<>(orderId);
    }

    /**
     * 删除采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @PostResource(name = "删除采购入库单", path = "/purchase/order/delete")
    public ResponseData<?> delete(@RequestBody @Validated(PurchaseOrderRequest.delete.class) PurchaseOrderRequest purchaseOrderRequest) {
        purchaseOrderService.del(purchaseOrderRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @PostResource(name = "批量删除采购入库单", path = "/purchase/order/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody PurchaseOrderRequest purchaseOrderRequest) {
        purchaseOrderService.batchDelete(purchaseOrderRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @PostResource(name = "编辑采购入库单", path = "/purchase/order/edit")
    public ResponseData<?> edit(@RequestBody @Validated(PurchaseOrderRequest.edit.class) PurchaseOrderRequest purchaseOrderRequest) {
        purchaseOrderService.edit(purchaseOrderRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查询采购入库单详情
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 采购入库单详情
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @GetResource(name = "查询采购入库单详情", path = "/purchase/order/detail")
    public ResponseData<PurchaseOrderResponse> detail(@Validated(PurchaseOrderRequest.detail.class) PurchaseOrderRequest purchaseOrderRequest) {
        PurchaseOrderResponse response = purchaseOrderService.detail(purchaseOrderRequest);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询采购入库单列表
     *
     * @param purchaseOrderQueryRequest 采购入库单查询请求参数
     * @return 采购入库单分页列表
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @GetResource(name = "分页查询采购入库单列表", path = "/purchase/order/page")
    public ResponseData<PageResult<PurchaseOrderResponse>> page(PurchaseOrderQueryRequest purchaseOrderQueryRequest) {
        PageResult<PurchaseOrderResponse> pageResult = purchaseOrderService.findPage(purchaseOrderQueryRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询采购入库单列表
     *
     * @param purchaseOrderQueryRequest 采购入库单查询请求参数
     * @return 采购入库单列表
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @GetResource(name = "查询采购入库单列表", path = "/purchase/order/list")
    public ResponseData<List<PurchaseOrderResponse>> list(PurchaseOrderQueryRequest purchaseOrderQueryRequest) {
        List<PurchaseOrderResponse> responseList = purchaseOrderService.findList(purchaseOrderQueryRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 确认采购入库单
     *
     * @param purchaseConfirmRequest 采购确认请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @PostResource(name = "确认采购入库单", path = "/purchase/order/confirm")
    public ResponseData<?> confirm(@RequestBody @Validated PurchaseConfirmRequest purchaseConfirmRequest) {
        purchaseOrderService.confirm(purchaseConfirmRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 采购入库操作
     *
     * @param purchaseReceiveRequest 采购入库请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @PostResource(name = "采购入库操作", path = "/purchase/order/receive")
    public ResponseData<?> receive(@RequestBody @Validated PurchaseReceiveRequest purchaseReceiveRequest) {
        purchaseOrderService.receive(purchaseReceiveRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 校验供应商经营方式是否允许采购
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 校验结果
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @GetResource(name = "校验供应商经营方式", path = "/purchase/order/validateSupplierBusinessMode")
    public ResponseData<Boolean> validateSupplierBusinessMode(PurchaseOrderRequest purchaseOrderRequest) {
        boolean canPurchase = purchaseOrderService.validateSupplierBusinessMode(purchaseOrderRequest.getSupplierId());
        return new SuccessResponseData<>(canPurchase);
    }

    /**
     * 生成采购入库单号
     *
     * @return 采购入库单号
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    @GetResource(name = "生成采购入库单号", path = "/purchase/order/generateOrderNo")
    public ResponseData<String> generateOrderNo() {
        String orderNo = purchaseOrderService.generateOrderNo();
        return new SuccessResponseData<>(orderNo);
    }

}