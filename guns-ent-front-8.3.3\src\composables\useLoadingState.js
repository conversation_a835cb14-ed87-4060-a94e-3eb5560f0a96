/**
 * 加载状态管理组合式函数
 * 
 * 提供统一的加载状态管理，支持多个并发操作的加载状态跟踪
 *
 * <AUTHOR>
 * @since 2025/08/01 21:00
 */
import { ref, computed, readonly } from 'vue';

/**
 * 加载状态管理
 */
export function useLoadingState() {
  // 加载状态映射表
  const loadingStates = ref(new Map());
  
  // 全局加载计数器
  const globalLoadingCount = ref(0);

  /**
   * 设置加载状态
   * @param {string} key 操作标识
   * @param {boolean} loading 是否加载中
   * @param {string} message 加载消息
   * @param {Object} options 选项
   */
  const setLoading = (key, loading, message = '', options = {}) => {
    const {
      progress = 0,
      total = 100,
      showProgress = false,
      cancelable = false,
      timeout = 0
    } = options;

    if (loading) {
      // 开始加载
      const loadingInfo = {
        loading: true,
        message,
        progress,
        total,
        showProgress,
        cancelable,
        startTime: Date.now(),
        timeout,
        timeoutId: null
      };

      // 设置超时
      if (timeout > 0) {
        loadingInfo.timeoutId = setTimeout(() => {
          setLoading(key, false, '操作超时');
        }, timeout);
      }

      loadingStates.value.set(key, loadingInfo);
      globalLoadingCount.value++;
    } else {
      // 结束加载
      const existingState = loadingStates.value.get(key);
      if (existingState) {
        // 清除超时定时器
        if (existingState.timeoutId) {
          clearTimeout(existingState.timeoutId);
        }
        
        loadingStates.value.delete(key);
        globalLoadingCount.value = Math.max(0, globalLoadingCount.value - 1);
      }
    }
  };

  /**
   * 更新加载进度
   * @param {string} key 操作标识
   * @param {number} progress 进度值
   * @param {string} message 进度消息
   */
  const updateProgress = (key, progress, message = '') => {
    const state = loadingStates.value.get(key);
    if (state) {
      state.progress = progress;
      if (message) {
        state.message = message;
      }
      loadingStates.value.set(key, state);
    }
  };

  /**
   * 检查是否正在加载
   * @param {string} key 操作标识
   * @returns {boolean}
   */
  const isLoading = (key) => {
    const state = loadingStates.value.get(key);
    return state?.loading || false;
  };

  /**
   * 获取加载消息
   * @param {string} key 操作标识
   * @returns {string}
   */
  const getLoadingMessage = (key) => {
    const state = loadingStates.value.get(key);
    return state?.message || '';
  };

  /**
   * 获取加载进度
   * @param {string} key 操作标识
   * @returns {number}
   */
  const getProgress = (key) => {
    const state = loadingStates.value.get(key);
    return state?.progress || 0;
  };

  /**
   * 获取加载状态详情
   * @param {string} key 操作标识
   * @returns {Object}
   */
  const getLoadingState = (key) => {
    return loadingStates.value.get(key) || null;
  };

  /**
   * 取消加载操作
   * @param {string} key 操作标识
   */
  const cancelLoading = (key) => {
    const state = loadingStates.value.get(key);
    if (state && state.cancelable) {
      setLoading(key, false, '操作已取消');
      return true;
    }
    return false;
  };

  /**
   * 清除所有加载状态
   */
  const clearAllLoading = () => {
    // 清除所有超时定时器
    loadingStates.value.forEach(state => {
      if (state.timeoutId) {
        clearTimeout(state.timeoutId);
      }
    });
    
    loadingStates.value.clear();
    globalLoadingCount.value = 0;
  };

  /**
   * 获取所有加载中的操作
   * @returns {Array}
   */
  const getActiveLoadings = () => {
    const activeLoadings = [];
    loadingStates.value.forEach((state, key) => {
      if (state.loading) {
        activeLoadings.push({ key, ...state });
      }
    });
    return activeLoadings;
  };

  // 计算属性
  const hasAnyLoading = computed(() => globalLoadingCount.value > 0);
  
  const loadingCount = computed(() => globalLoadingCount.value);

  const longestRunningOperation = computed(() => {
    let longest = null;
    let maxDuration = 0;
    
    loadingStates.value.forEach((state, key) => {
      if (state.loading) {
        const duration = Date.now() - state.startTime;
        if (duration > maxDuration) {
          maxDuration = duration;
          longest = { key, duration, ...state };
        }
      }
    });
    
    return longest;
  });

  return {
    // 状态
    loadingStates: readonly(loadingStates),
    hasAnyLoading,
    loadingCount,
    longestRunningOperation,
    
    // 方法
    setLoading,
    updateProgress,
    isLoading,
    getLoadingMessage,
    getProgress,
    getLoadingState,
    cancelLoading,
    clearAllLoading,
    getActiveLoadings
  };
}

/**
 * POS专用加载状态管理
 */
export function usePosLoadingState() {
  const loadingState = useLoadingState();

  // POS系统常用的加载操作
  const POS_LOADING_KEYS = {
    INIT_DATA: 'pos_init_data',
    LOAD_CATEGORIES: 'pos_load_categories',
    LOAD_PRODUCTS: 'pos_load_products',
    SEARCH_PRODUCTS: 'pos_search_products',
    CREATE_ORDER: 'pos_create_order',
    PROCESS_PAYMENT: 'pos_process_payment',
    LOAD_MEMBER: 'pos_load_member',
    SUSPEND_ORDER: 'pos_suspend_order',
    RESUME_ORDER: 'pos_resume_order'
  };

  // POS专用方法
  const startInitData = () => {
    loadingState.setLoading(POS_LOADING_KEYS.INIT_DATA, true, '正在初始化POS数据...', {
      showProgress: true,
      timeout: 30000
    });
  };

  const finishInitData = () => {
    loadingState.setLoading(POS_LOADING_KEYS.INIT_DATA, false);
  };

  const startLoadProducts = (categoryName = '') => {
    const message = categoryName ? `正在加载${categoryName}商品...` : '正在加载商品...';
    loadingState.setLoading(POS_LOADING_KEYS.LOAD_PRODUCTS, true, message, {
      timeout: 15000
    });
  };

  const finishLoadProducts = () => {
    loadingState.setLoading(POS_LOADING_KEYS.LOAD_PRODUCTS, false);
  };

  const startCreateOrder = () => {
    loadingState.setLoading(POS_LOADING_KEYS.CREATE_ORDER, true, '正在创建订单...', {
      showProgress: true,
      timeout: 10000
    });
  };

  const finishCreateOrder = () => {
    loadingState.setLoading(POS_LOADING_KEYS.CREATE_ORDER, false);
  };

  const startProcessPayment = (paymentMethod = '') => {
    const message = paymentMethod ? `正在处理${paymentMethod}支付...` : '正在处理支付...';
    loadingState.setLoading(POS_LOADING_KEYS.PROCESS_PAYMENT, true, message, {
      showProgress: true,
      cancelable: true,
      timeout: 60000
    });
  };

  const updatePaymentProgress = (progress, step = '') => {
    const message = step ? `正在处理支付 - ${step}` : '正在处理支付...';
    loadingState.updateProgress(POS_LOADING_KEYS.PROCESS_PAYMENT, progress, message);
  };

  const finishProcessPayment = () => {
    loadingState.setLoading(POS_LOADING_KEYS.PROCESS_PAYMENT, false);
  };

  // 检查POS特定操作的加载状态
  const isInitializing = () => loadingState.isLoading(POS_LOADING_KEYS.INIT_DATA);
  const isLoadingProducts = () => loadingState.isLoading(POS_LOADING_KEYS.LOAD_PRODUCTS);
  const isCreatingOrder = () => loadingState.isLoading(POS_LOADING_KEYS.CREATE_ORDER);
  const isProcessingPayment = () => loadingState.isLoading(POS_LOADING_KEYS.PROCESS_PAYMENT);

  return {
    ...loadingState,
    
    // POS专用常量
    POS_LOADING_KEYS,
    
    // POS专用方法
    startInitData,
    finishInitData,
    startLoadProducts,
    finishLoadProducts,
    startCreateOrder,
    finishCreateOrder,
    startProcessPayment,
    updatePaymentProgress,
    finishProcessPayment,
    
    // POS专用状态检查
    isInitializing,
    isLoadingProducts,
    isCreatingOrder,
    isProcessingPayment
  };
}
