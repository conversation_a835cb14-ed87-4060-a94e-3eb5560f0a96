package cn.stylefeng.roses.seata.demo.storage.modular.service.impl;

import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.seata.demo.storage.modular.entity.StorageTbl;
import cn.stylefeng.roses.seata.demo.storage.modular.mapper.StorageTblMapper;
import cn.stylefeng.roses.seata.demo.storage.modular.service.StorageTblService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 业务实现层
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
@Service
public class StorageTblServiceImpl extends ServiceImpl<StorageTblMapper, StorageTbl> implements StorageTblService {


    @Override
    public void subStorage(String commodityCode, int count) {

        // 扣减库存
        LambdaQueryWrapper<StorageTbl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StorageTbl::getCommodityCode, commodityCode);
        StorageTbl storageTbl = this.getOne(queryWrapper, false);

        // 判断商品库存是否为空
        if (storageTbl == null) {
            throw new ServiceException("account", "B0011", "商品不存在，commodityCode不正确");
        }

        // 扣减库存
        storageTbl.setCount(storageTbl.getCount() - count);
        this.updateById(storageTbl);
    }

}