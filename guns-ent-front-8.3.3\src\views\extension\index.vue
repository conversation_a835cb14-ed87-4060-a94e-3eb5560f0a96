<template>
  <div class="guns-layout" :class="[{ 'guns-collapse': !isCollapse }]">
    <div class="guns-layout-sidebar">
      <div class="sidebar-content">
        <div class="sidebar-content">
          <a-menu
            v-model:selectedKeys="selectedKeys"
            class="sidebar-menu"
            mode="inline"
            :open-keys="openKeys"
            @select="selectChange"
            @openChange="openChange"
          >
            <a-sub-menu key="1">
              <template #title>扩展组件</template>
              <a-menu-item :key="item.key" v-for="item in baseMenuList" :title="item.label"> {{ item.label }} </a-menu-item>
            </a-sub-menu>
          </a-menu>
        </div>
      </div>
      <!-- 折叠按钮 -->
      <div class="collapse-btn" @click="toggleCollapse()">
        <CaretRightOutlined v-if="isCollapse" />
        <CaretLeftOutlined v-else />
      </div>
    </div>
    <div class="collapse-mask" @click="toggleCollapse()"></div>
    <div class="guns-layout-content" style="padding: 0">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <div class="content-mian-body">
              <component :is="componentName" v-if="componentName" :title="title" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="BaseComponents">
import { ref, onMounted, shallowRef, defineAsyncComponent } from 'vue';

// 左侧菜单展开列表
const openKeys = ref(['1']);

// 左侧菜单选中列表
const selectedKeys = ref(['city-select']);

// 当前菜单选中
const currentMenuSelect = ref('city-select');

// 是否显示折叠按钮
const isCollapse = ref(false);

const baseMenuList = ref([
  {
    key: 'city-select',
    label: '城市选择器'
  },
  {
    key: 'custom-select',
    label: '自定义选择'
  },
  {
    key: 'date-range-select',
    label: '日期范围'
  },
  {
    key: 'map-select',
    label: '地图选择(input)'
  },
  {
    key: 'org-user-select',
    label: '机构人员选择(input)'
  },
  {
    key: 'dict-select',
    label: '字典选择'
  },
  {
    key: 'org-select',
    label: '机构选择'
  },
  {
    key: 'user-select',
    label: '人员选择'
  },
  {
    key: 'position-select',
    label: '职位选择'
  },
  {
    key: 'role-select',
    label: '角色选择'
  },
  {
    key: 'tree-select',
    label: '树形选择'
  },
  {
    key: 'region-selector-test',
    label: '区域选择组件测试'
  },
  {
    key: 'file-upload',
    label: '上传文件'
  },
  {
    key: 'pic-upload',
    label: '上传图片'
  },
  {
    key: 'json-editor',
    label: 'json编辑器'
  },
  {
    key: 'progress',
    label: '进度条'
  },
  {
    key: 'map',
    label: '地图选择'
  },
  {
    key: 'org-user',
    label: '机构人员选择器'
  },
  {
    key: 'split',
    label: '分割面板'
  },
  {
    key: 'code-preview',
    label: '代码预览'
  },
  {
    key: 'dict-popover',
    label: '字典popover'
  },
  {
    key: 'monacocode',
    label: '代码编辑器'
  },
  {
    key: 'tinymce-editor',
    label: '富文本编辑器'
  },
  {
    key: 'markdown',
    label: 'markdown编辑器'
  },
  {
    key: 'copy',
    label: '一键复制'
  },
  {
    key: 'print',
    label: '打印'
  },
  {
    key: 'pic-preview',
    label: '图片预览组件'
  },
  {
    key: 'toPic',
    label: '页面转换成图片'
  },
  {
    key: 'pic-lazy',
    label: '图片懒加载'
  },
  {
    key: 'icon-manager',
    label: '图标管理工具'
  },
  
]);

const componentName = shallowRef('');

const title = ref('城市选择器');

// 左侧菜单选中
const selectChange = ({ key, item }) => {
  currentMenuSelect.value = key;
  title.value = item.title;
  buildComponentName();
};

// 设置展开
const openChange = () => {
  openKeys.value = ['1'];
};

onMounted(() => {
  buildComponentName();
});

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
};

const buildComponentName = async () => {
  try {
    const pathUrl = await import(`./${currentMenuSelect.value}/index.vue`);
    componentName.value = defineAsyncComponent(() => Promise.resolve(pathUrl));
  } catch (error) {
    componentName.value = null;
  }
};
</script>

<style scoped lang="less">
.guns-layout-sidebar {
  width: v-bind('isCollapse ? 0 : "252px"');
  padding: v-bind('isCollapse ? 0 : "12px"');
}
.sidebar-content {
  scrollbar-width: none;
}
.guns-layout-content-header {
  height: 40px !important;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
</style>
