<template>
  <div class="region-selector-example">
    <a-card title="区域选择组件使用示例" :bordered="false">
      
      <!-- 供应商表单示例 -->
      <div class="example-section">
        <h3>供应商表单中的区域选择</h3>
        <a-form :model="supplierForm" layout="vertical" style="max-width: 600px">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="供应商名称" name="supplierName">
                <a-input v-model:value="supplierForm.supplierName" placeholder="请输入供应商名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="供应商编码" name="supplierCode">
                <a-input v-model:value="supplierForm.supplierCode" placeholder="请输入供应商编码" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="关联区域" name="regionIds">
            <region-selector 
              v-model="supplierForm.regionIds" 
              placeholder="请选择供应商服务的区域"
              @change="handleSupplierRegionChange"
            />
          </a-form-item>
          
          <a-form-item label="联系人" name="contactPerson">
            <a-input v-model:value="supplierForm.contactPerson" placeholder="请输入联系人姓名" />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="saveSupplier">保存供应商</a-button>
              <a-button @click="resetSupplierForm">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
        
        <div class="form-data-display">
          <h4>表单数据:</h4>
          <pre>{{ JSON.stringify(supplierForm, null, 2) }}</pre>
        </div>
      </div>

      <!-- 客户表单示例 -->
      <div class="example-section">
        <h3>客户表单中的区域选择</h3>
        <a-form :model="customerForm" layout="vertical" style="max-width: 600px">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户名称" name="customerName">
                <a-input v-model:value="customerForm.customerName" placeholder="请输入客户名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="客户类型" name="customerType">
                <a-select v-model:value="customerForm.customerType" placeholder="请选择客户类型">
                  <a-select-option value="enterprise">企业客户</a-select-option>
                  <a-select-option value="individual">个人客户</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="服务区域" name="regionIds">
            <region-selector 
              v-model="customerForm.regionIds" 
              placeholder="请选择客户所在的区域"
              @change="handleCustomerRegionChange"
            />
          </a-form-item>
          
          <a-form-item label="客户地址" name="address">
            <a-textarea v-model:value="customerForm.address" placeholder="请输入详细地址" :rows="3" />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="saveCustomer">保存客户</a-button>
              <a-button @click="resetCustomerForm">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
        
        <div class="form-data-display">
          <h4>表单数据:</h4>
          <pre>{{ JSON.stringify(customerForm, null, 2) }}</pre>
        </div>
      </div>

      <!-- 单选模式示例 -->
      <div class="example-section">
        <h3>单选模式示例（选择主要服务区域）</h3>
        <a-form layout="vertical" style="max-width: 400px">
          <a-form-item label="主要服务区域">
            <region-selector 
              v-model="primaryRegion" 
              :multiple="false"
              placeholder="请选择主要服务区域"
              @change="handlePrimaryRegionChange"
            />
          </a-form-item>
        </a-form>
        
        <div class="form-data-display">
          <h4>选中的主要区域:</h4>
          <p>区域ID: {{ primaryRegion }}</p>
          <p>区域信息: {{ primaryRegionInfo ? primaryRegionInfo.regionName : '未选择' }}</p>
        </div>
      </div>

    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import RegionSelector from './index.vue'

// 供应商表单数据
const supplierForm = reactive({
  supplierName: '',
  supplierCode: '',
  regionIds: [],
  contactPerson: ''
})

// 客户表单数据
const customerForm = reactive({
  customerName: '',
  customerType: '',
  regionIds: [],
  address: ''
})

// 单选区域
const primaryRegion = ref(null)
const primaryRegionInfo = ref(null)

// 供应商区域选择变化
const handleSupplierRegionChange = (value, regions) => {
  console.log('供应商区域选择变化:', value, regions)
  message.info(`供应商选择了 ${regions.length} 个服务区域`)
}

// 客户区域选择变化
const handleCustomerRegionChange = (value, regions) => {
  console.log('客户区域选择变化:', value, regions)
  message.info(`客户选择了 ${regions.length} 个服务区域`)
}

// 主要区域选择变化
const handlePrimaryRegionChange = (value, region) => {
  console.log('主要区域选择变化:', value, region)
  primaryRegionInfo.value = Array.isArray(region) ? region[0] : region
  if (region) {
    message.success(`选择了主要服务区域: ${primaryRegionInfo.value.regionName}`)
  }
}

// 保存供应商
const saveSupplier = () => {
  console.log('保存供应商数据:', supplierForm)
  message.success('供应商信息保存成功')
}

// 重置供应商表单
const resetSupplierForm = () => {
  Object.assign(supplierForm, {
    supplierName: '',
    supplierCode: '',
    regionIds: [],
    contactPerson: ''
  })
  message.info('供应商表单已重置')
}

// 保存客户
const saveCustomer = () => {
  console.log('保存客户数据:', customerForm)
  message.success('客户信息保存成功')
}

// 重置客户表单
const resetCustomerForm = () => {
  Object.assign(customerForm, {
    customerName: '',
    customerType: '',
    regionIds: [],
    address: ''
  })
  message.info('客户表单已重置')
}
</script>

<style scoped>
.region-selector-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.example-section:last-child {
  border-bottom: none;
}

h3 {
  color: #1890ff;
  margin-bottom: 20px;
  font-size: 18px;
}

h4 {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.form-data-display {
  margin-top: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.form-data-display pre {
  margin: 0;
  font-size: 12px;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}

.form-data-display p {
  margin: 4px 0;
  font-size: 14px;
  color: #333;
}
</style>
