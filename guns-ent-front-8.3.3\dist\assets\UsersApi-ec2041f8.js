import{R as e}from"./index-18a1ea24.js";class a{static findPage(t){return e.getAndLoadData("/sysUser/page",t)}static getResetPassword(t){return e.getAndLoadData("/sysUser/getResetPassword",t)}static add(t){return e.post("/sysUser/add",t)}static edit(t){return e.post("/sysUser/edit",t)}static delete(t){return e.post("/sysUser/delete",t)}static batchDelete(t){return e.post("/sysUser/batchDelete",t)}static detail(t){return e.getAndLoadData("/sysUser/detail",t)}static list(t){return e.getAndLoadData("/sysUser/list",t)}static resetPassword(t){return e.post("/sysUser/resetPassword",t)}static updateStatus(t){return e.post("/sysUser/updateStatus",t)}static bindRoles(t){return e.post("/sysUser/bindRoles",t)}static roleList(t){return e.getAndLoadData("/sysRole/list",t)}static ExportUser(t){return e.downLoad("/user/ExportUser",t)}static getExcelTemplate(t){return e.downLoad("/userImport/getExcelTemplate",t)}static uploadAndGetPreviewData(t){return e.post("/userImport/uploadAndGetPreviewData",t)}static ensureImport(t){return e.post("/userImport/ensureImport",t)}static getUserOrgList(t){return e.getAndLoadData("/sysRoleAssign/v2/getUserOrgList",t)}static getCompanyBusinessRoleTree(t){return e.get("/sysRoleAssign/v2/getCompanyBusinessRoleTree",t)}static getCompanyRoleTree(t){return e.get("/sysRoleAssign/v2/getCompanyRoleTree",t)}static changeRoleSelect(t){return e.post("/sysRoleAssign/changeRoleSelect",t)}}export{a as U};
