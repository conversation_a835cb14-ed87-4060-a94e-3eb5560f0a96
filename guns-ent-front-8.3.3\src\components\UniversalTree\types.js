/**
 * 通用树结构组件类型定义
 * 
 * <AUTHOR>
 * @since 2025/01/24
 */

// 错误类型枚举
export const TreeErrorType = {
  DATA_LOAD_ERROR: 'DATA_LOAD_ERROR',
  LAZY_LOAD_ERROR: 'LAZY_LOAD_ERROR',
  SEARCH_ERROR: 'SEARCH_ERROR',
  ACTION_ERROR: 'ACTION_ERROR',
  CONFIG_ERROR: 'CONFIG_ERROR'
}

/**
 * 树节点数据模型
 * @typedef {Object} TreeNode
 * @property {string} key - 节点唯一标识
 * @property {string} title - 节点显示文本
 * @property {TreeNode[]} [children] - 子节点
 * @property {boolean} [isLeaf] - 是否为叶子节点
 * @property {number} [level] - 节点层级
 * @property {boolean} [hasChildren] - 是否有子节点
 * @property {boolean} [disabled] - 是否禁用
 * @property {boolean} [selectable] - 是否可选择
 */

/**
 * 数据源配置
 * @typedef {Object} DataSourceConfig
 * @property {Function} api - 主要API函数
 * @property {Function} [lazyLoadApi] - 懒加载API函数
 * @property {string} [searchParam] - 搜索参数名
 * @property {string} [parentIdParam] - 父级ID参数名
 */

/**
 * 字段映射配置
 * @typedef {Object} FieldMapping
 * @property {string} key - 节点唯一标识字段
 * @property {string} title - 节点显示文本字段
 * @property {string} children - 子节点字段
 * @property {string} [hasChildren] - 是否有子节点字段
 * @property {string} [level] - 层级字段
 */

/**
 * 显示配置
 * @typedef {Object} DisplayConfig
 * @property {string} [title] - 树结构标题
 * @property {boolean} [showHeader] - 是否显示头部
 * @property {boolean} [showSearch] - 是否显示搜索框
 * @property {string} [searchPlaceholder] - 搜索框占位符
 * @property {boolean} [showAddButton] - 是否显示新增按钮
 * @property {boolean} [showEditIcons] - 是否显示编辑图标
 * @property {boolean} [showIcon] - 是否显示节点图标
 * @property {boolean} [isSetWidth] - 是否设置固定宽度
 */

/**
 * 交互配置
 * @typedef {Object} InteractionConfig
 * @property {boolean} [selectable] - 是否可选择
 * @property {boolean} [expandable] - 是否可展开
 * @property {boolean} [lazyLoad] - 是否懒加载
 * @property {number} [defaultExpandLevel] - 默认展开层级
 * @property {boolean} [allowMultiSelect] - 是否允许多选
 */

/**
 * 自定义操作
 * @typedef {Object} CustomAction
 * @property {string} key - 操作键值
 * @property {string} icon - 图标类名
 * @property {string} [color] - 图标颜色
 * @property {string} [title] - 操作提示
 */

/**
 * 操作配置
 * @typedef {Object} ActionConfig
 * @property {boolean} [allowAdd] - 是否允许新增
 * @property {boolean} [allowEdit] - 是否允许编辑
 * @property {boolean} [allowDelete] - 是否允许删除
 * @property {CustomAction[]} [customActions] - 自定义操作
 */

/**
 * 完整的树配置
 * @typedef {Object} TreeConfig
 * @property {DataSourceConfig} dataSource - 数据源配置
 * @property {FieldMapping} fieldMapping - 字段映射配置
 * @property {DisplayConfig} displayConfig - 显示配置
 * @property {InteractionConfig} interactionConfig - 交互配置
 * @property {ActionConfig} [actionConfig] - 操作配置
 */

/**
 * 错误接口
 * @typedef {Object} TreeError
 * @property {string} type - 错误类型
 * @property {string} message - 错误消息
 * @property {Error} [originalError] - 原始错误
 * @property {any} [context] - 上下文信息
 */

/**
 * 配置验证结果
 * @typedef {Object} ConfigValidationResult
 * @property {boolean} isValid - 配置是否有效
 * @property {string[]} errors - 错误信息数组
 * @property {string[]} warnings - 警告信息数组
 */

/**
 * 树组件事件
 * @typedef {Object} TreeEvents
 * @property {Function} onSelect - 节点选择事件
 * @property {Function} onExpand - 节点展开事件
 * @property {Function} onSearch - 搜索事件
 * @property {Function} onAdd - 新增事件
 * @property {Function} onEdit - 编辑事件
 * @property {Function} onDelete - 删除事件
 * @property {Function} onCustomAction - 自定义操作事件
 * @property {Function} onLoad - 数据加载事件
 * @property {Function} onLoadError - 加载错误事件
 */

/**
 * 搜索配置
 * @typedef {Object} SearchConfig
 * @property {boolean} enabled - 是否启用搜索
 * @property {string} placeholder - 搜索框占位符
 * @property {boolean} realtime - 是否实时搜索
 * @property {number} debounceTime - 防抖时间（毫秒）
 * @property {boolean} caseSensitive - 是否区分大小写
 */

/**
 * 加载状态
 * @typedef {Object} LoadingState
 * @property {boolean} loading - 是否正在加载
 * @property {boolean} lazyLoading - 是否正在懒加载
 * @property {string} loadingNodeKey - 正在加载的节点key
 * @property {string} message - 加载消息
 */

/**
 * 树组件状态
 * @typedef {Object} TreeState
 * @property {TreeNode[]} data - 树数据
 * @property {string[]} selectedKeys - 选中的节点keys
 * @property {string[]} expandedKeys - 展开的节点keys
 * @property {string} searchText - 搜索文本
 * @property {LoadingState} loading - 加载状态
 * @property {TreeError} error - 错误信息
 */