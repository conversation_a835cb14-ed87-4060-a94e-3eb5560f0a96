import{i as f}from"./performance-monitor-659a8228.js";import"./index-18a1ea24.js";import"./constants-2fa70699.js";class C{static async withRetry(t,e={}){const{maxRetries:r=3,retryDelay:u=1e3,maxDelay:l=3e4,backoffFactor:o=2,retryCondition:c=n=>this.shouldRetry(n),onRetry:a}=e;let i,s=u;for(let n=0;n<=r;n++)try{return await t()}catch(y){if(i=y,n===r||!c(y))throw y;typeof a=="function"&&a(y,n+1,r),await this.delay(s),s=Math.min(s*o,l)}throw i}static shouldRetry(t){if(t&&typeof t.retryable=="boolean")return t.retryable;if(t&&t.type)return f(t.type);if(t&&t.response&&t.response.status){const e=t.response.status;if(e>=500||e===408||e===429)return!0;if(e>=400&&e<500)return!1}if(t&&t.message){const e=t.message.toLowerCase();if(e.includes("network")||e.includes("timeout")||e.includes("connection")||e.includes("\u7F51\u7EDC")||e.includes("\u8D85\u65F6")||e.includes("\u8FDE\u63A5")||e.includes("service unavailable")||e.includes("\u670D\u52A1\u4E0D\u53EF\u7528"))return!0}return!1}static delay(t){return new Promise(e=>setTimeout(e,t))}static delayWithJitter(t,e=.1){const r=t*e*Math.random(),u=t+r;return this.delay(u)}static async withLinearBackoff(t,e={}){const{maxRetries:r=3,retryDelay:u=1e3,retryCondition:l=a=>this.shouldRetry(a),onRetry:o}=e;let c;for(let a=0;a<=r;a++)try{return await t()}catch(i){if(c=i,a===r||!l(i))throw i;typeof o=="function"&&o(i,a+1,r);const s=u*(a+1);await this.delay(s)}throw c}static async withFixedInterval(t,e={}){const{maxRetries:r=3,retryDelay:u=1e3,retryCondition:l=a=>this.shouldRetry(a),onRetry:o}=e;let c;for(let a=0;a<=r;a++)try{return await t()}catch(i){if(c=i,a===r||!l(i))throw i;typeof o=="function"&&o(i,a+1,r),await this.delay(u)}throw c}static async withCircuitBreaker(t,e={}){const{maxRetries:r=3,retryDelay:u=1e3,failureThreshold:l=5,recoveryTimeout:o=6e4,retryCondition:c=n=>this.shouldRetry(n),onRetry:a}=e,i=t.name||"anonymous",s=this.getCircuitState(i);if(s.state==="OPEN"){if(Date.now()-s.lastFailureTime<o)throw new Error("\u7194\u65AD\u5668\u5F00\u542F\uFF0C\u670D\u52A1\u6682\u65F6\u4E0D\u53EF\u7528");s.state="HALF_OPEN"}try{const n=await this.withRetry(t,{maxRetries:r,retryDelay:u,retryCondition:c,onRetry:a});return this.resetCircuit(i),n}catch(n){throw this.recordFailure(i,l),n}}static getCircuitState(t){return this.circuitStates||(this.circuitStates=new Map),this.circuitStates.has(t)||this.circuitStates.set(t,{state:"CLOSED",failureCount:0,lastFailureTime:0}),this.circuitStates.get(t)}static recordFailure(t,e){const r=this.getCircuitState(t);r.failureCount++,r.lastFailureTime=Date.now(),r.failureCount>=e&&(r.state="OPEN")}static resetCircuit(t){const e=this.getCircuitState(t);e.state="CLOSED",e.failureCount=0,e.lastFailureTime=0}static async batchRetry(t,e={}){const{maxRetries:r=3,retryDelay:u=1e3,retryCondition:l=i=>this.shouldRetry(i),onRetry:o,failFast:c=!1}=e,a=t.map(async(i,s)=>{try{return await this.withRetry(i,{maxRetries:r,retryDelay:u,retryCondition:l,onRetry:o?(n,y,h)=>o(n,y,h,s):void 0})}catch(n){if(c)throw n;return{error:n,index:s}}});return c?await Promise.all(a):await Promise.allSettled(a)}static async retryIf(t,e,r={}){return this.withRetry(t,{...r,retryCondition:e})}static async retryUntilTimeout(t,e={}){const{timeout:r=3e4,retryDelay:u=1e3,retryCondition:l=s=>this.shouldRetry(s),onRetry:o}=e,c=Date.now();let a=0,i;for(;Date.now()-c<r;)try{return await t()}catch(s){if(i=s,!l(s))throw s;typeof o=="function"&&o(s,++a,1/0),await this.delay(u)}throw new Error("\u64CD\u4F5C\u8D85\u65F6\uFF08".concat(r,"ms\uFF09\uFF0C\u6700\u540E\u4E00\u6B21\u9519\u8BEF: ").concat((i==null?void 0:i.message)||"\u672A\u77E5\u9519\u8BEF"))}}export{C as RetryHandler};
