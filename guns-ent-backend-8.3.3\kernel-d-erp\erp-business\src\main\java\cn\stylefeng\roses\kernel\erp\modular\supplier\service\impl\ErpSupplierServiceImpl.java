package cn.stylefeng.roses.kernel.erp.modular.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpSupplierConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpSupplierExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.modular.supplier.mapper.ErpSupplierMapper;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierRegionService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.SupplierBusinessModeRuleService;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.BusinessModeChangeValidationResponse;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.collection.CollUtil;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商主档案Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
@Slf4j
@Service
public class ErpSupplierServiceImpl extends ServiceImpl<ErpSupplierMapper, ErpSupplier> implements ErpSupplierService {

    @Resource
    private ErpSupplierMapper erpSupplierMapper;

    @Resource
    private ErpSupplierRegionService erpSupplierRegionService;

    @Resource
    private SupplierBusinessModeRuleService supplierBusinessModeRuleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ErpSupplierRequest erpSupplierRequest) {
        ErpSupplier erpSupplier = new ErpSupplier();
        BeanUtil.copyProperties(erpSupplierRequest, erpSupplier);

        // 校验供应商编码是否重复
        if (this.validateSupplierCodeRepeat(erpSupplier.getSupplierCode(), null)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_CODE_REPEAT);
        }

        // 设置默认值
        if (StrUtil.isBlank(erpSupplier.getSupplierType())) {
            erpSupplier.setSupplierType(ErpSupplierConstants.DEFAULT_SUPPLIER_TYPE);
        }
        if (StrUtil.isBlank(erpSupplier.getCreditLevel())) {
            erpSupplier.setCreditLevel(ErpSupplierConstants.DEFAULT_CREDIT_LEVEL);
        }
        if (StrUtil.isBlank(erpSupplier.getStatus())) {
            erpSupplier.setStatus(ErpSupplierConstants.DEFAULT_SUPPLIER_STATUS);
        }
        if (StrUtil.isBlank(erpSupplier.getBusinessMode())) {
            erpSupplier.setBusinessMode(ErpSupplierConstants.DEFAULT_BUSINESS_MODE);
        }

        // 校验参数
        this.validateSupplierParams(erpSupplier);

        // 校验经营方式相关参数
        supplierBusinessModeRuleService.validateSupplierBusinessModeParams(erpSupplier);

        this.save(erpSupplier);

        // 处理区域关联
        if (CollUtil.isNotEmpty(erpSupplierRequest.getRegionIds())) {
            ErpSupplierRegionRequest regionRequest = new ErpSupplierRegionRequest();
            regionRequest.setSupplierId(erpSupplier.getSupplierId());
            regionRequest.setRegionIds(erpSupplierRequest.getRegionIds());
            erpSupplierRegionService.updateSupplierRegions(regionRequest);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(ErpSupplierRequest erpSupplierRequest) {
        ErpSupplier erpSupplier = this.querySupplier(erpSupplierRequest);

        // 校验是否可以删除
        if (!this.validateCanDelete(erpSupplier.getSupplierId())) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_HAS_BUSINESS_DATA_CANNOT_DELETE);
        }

        this.removeById(erpSupplier.getSupplierId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ErpSupplierRequest erpSupplierRequest) {
        List<Long> supplierIdList = erpSupplierRequest.getSupplierIdList();
        if (ObjectUtil.isEmpty(supplierIdList)) {
            return;
        }

        // 校验每个供应商是否可以删除
        for (Long supplierId : supplierIdList) {
            if (!this.validateCanDelete(supplierId)) {
                throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_HAS_BUSINESS_DATA_CANNOT_DELETE);
            }
        }

        this.removeBatchByIds(supplierIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ErpSupplierRequest erpSupplierRequest) {
        ErpSupplier erpSupplier = this.querySupplier(erpSupplierRequest);

        // 校验供应商编码是否重复（排除自己）
        if (this.validateSupplierCodeRepeat(erpSupplierRequest.getSupplierCode(), erpSupplier.getSupplierId())) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_CODE_REPEAT);
        }

        BeanUtil.copyProperties(erpSupplierRequest, erpSupplier);

        // 校验参数
        this.validateSupplierParams(erpSupplier);

        // 校验经营方式相关参数
        supplierBusinessModeRuleService.validateSupplierBusinessModeParams(erpSupplier);

        this.updateById(erpSupplier);

        // 处理区域关联
        ErpSupplierRegionRequest regionRequest = new ErpSupplierRegionRequest();
        regionRequest.setSupplierId(erpSupplier.getSupplierId());
        regionRequest.setRegionIds(erpSupplierRequest.getRegionIds());
        erpSupplierRegionService.updateSupplierRegions(regionRequest);
    }

    @Override
    public ErpSupplierResponse detail(ErpSupplierRequest erpSupplierRequest) {
        ErpSupplier erpSupplier = this.querySupplier(erpSupplierRequest);
        ErpSupplierResponse response = new ErpSupplierResponse();
        BeanUtil.copyProperties(erpSupplier, response);

        // 填充扩展信息
        this.fillSupplierExtInfo(response);

        return response;
    }

    @Override
    public PageResult<ErpSupplierResponse> findPage(ErpSupplierRequest erpSupplierRequest) {
        LambdaQueryWrapper<ErpSupplier> wrapper = this.createWrapper(erpSupplierRequest);
        Page<ErpSupplier> page = this.page(PageFactory.defaultPage(), wrapper);

        List<ErpSupplierResponse> responseList = page.getRecords().stream().map(supplier -> {
            ErpSupplierResponse response = new ErpSupplierResponse();
            BeanUtil.copyProperties(supplier, response);
            this.fillSupplierExtInfo(response);
            return response;
        }).collect(Collectors.toList());

        return PageResultFactory.createPageResult(responseList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public List<ErpSupplierResponse> findList(ErpSupplierRequest erpSupplierRequest) {
        LambdaQueryWrapper<ErpSupplier> wrapper = this.createWrapper(erpSupplierRequest);
        List<ErpSupplier> supplierList = this.list(wrapper);

        return supplierList.stream().map(supplier -> {
            ErpSupplierResponse response = new ErpSupplierResponse();
            BeanUtil.copyProperties(supplier, response);
            this.fillSupplierExtInfo(response);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(ErpSupplierRequest erpSupplierRequest) {
        ErpSupplier erpSupplier = this.querySupplier(erpSupplierRequest);

        // 如果要停用，校验是否可以停用
        if (ErpSupplierConstants.SUPPLIER_STATUS_INACTIVE.equals(erpSupplierRequest.getStatus())) {
            if (!this.validateCanInactive(erpSupplier.getSupplierId())) {
                throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_HAS_BUSINESS_DATA_CANNOT_INACTIVE);
            }
        }

        // 校验状态参数
        this.validateStatus(erpSupplierRequest.getStatus());

        LambdaUpdateWrapper<ErpSupplier> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ErpSupplier::getSupplierId, erpSupplier.getSupplierId())
                .set(ErpSupplier::getStatus, erpSupplierRequest.getStatus());

        this.update(updateWrapper);
    }

    @Override
    public boolean validateSupplierCodeRepeat(String supplierCode, Long supplierId) {
        LambdaQueryWrapper<ErpSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpSupplier::getSupplierCode, supplierCode);
        if (ObjectUtil.isNotNull(supplierId)) {
            wrapper.ne(ErpSupplier::getSupplierId, supplierId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean validateCanDelete(Long supplierId) {
        // TODO: 这里需要检查供应商是否有关联的业务数据
        // 例如：采购订单、入库单等
        // 暂时返回true，后续根据业务需要完善
        return true;
    }

    @Override
    public boolean validateCanInactive(Long supplierId) {
        // TODO: 这里需要检查供应商是否有未完成的业务数据
        // 例如：未完成的采购订单等
        // 暂时返回true，后续根据业务需要完善
        return true;
    }

    /**
     * 根据主键查询供应商
     *
     * @param erpSupplierRequest 请求参数
     * @return 供应商实体
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private ErpSupplier querySupplier(ErpSupplierRequest erpSupplierRequest) {
        ErpSupplier erpSupplier = this.getById(erpSupplierRequest.getSupplierId());
        if (ObjectUtil.isNull(erpSupplier)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_NOT_EXIST);
        }
        return erpSupplier;
    }

    /**
     * 创建查询条件
     *
     * @param erpSupplierRequest 请求参数
     * @return 查询条件
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private LambdaQueryWrapper<ErpSupplier> createWrapper(ErpSupplierRequest erpSupplierRequest) {
        LambdaQueryWrapper<ErpSupplier> wrapper = new LambdaQueryWrapper<>();

        // 供应商编码
        if (StrUtil.isNotBlank(erpSupplierRequest.getSupplierCode())) {
            wrapper.like(ErpSupplier::getSupplierCode, erpSupplierRequest.getSupplierCode());
        }

        // 供应商名称
        if (StrUtil.isNotBlank(erpSupplierRequest.getSupplierName())) {
            wrapper.like(ErpSupplier::getSupplierName, erpSupplierRequest.getSupplierName());
        }

        // 供应商类型
        if (StrUtil.isNotBlank(erpSupplierRequest.getSupplierType())) {
            wrapper.eq(ErpSupplier::getSupplierType, erpSupplierRequest.getSupplierType());
        }

        // 所属区域
        if (ObjectUtil.isNotNull(erpSupplierRequest.getRegionId())) {
            wrapper.eq(ErpSupplier::getRegionId, erpSupplierRequest.getRegionId());
        }

        // 状态
        if (StrUtil.isNotBlank(erpSupplierRequest.getStatus())) {
            wrapper.eq(ErpSupplier::getStatus, erpSupplierRequest.getStatus());
        }

        // 信用等级
        if (StrUtil.isNotBlank(erpSupplierRequest.getCreditLevel())) {
            wrapper.eq(ErpSupplier::getCreditLevel, erpSupplierRequest.getCreditLevel());
        }

        // 联系人
        if (StrUtil.isNotBlank(erpSupplierRequest.getContactPerson())) {
            wrapper.like(ErpSupplier::getContactPerson, erpSupplierRequest.getContactPerson());
        }

        // 联系电话
        if (StrUtil.isNotBlank(erpSupplierRequest.getContactPhone())) {
            wrapper.like(ErpSupplier::getContactPhone, erpSupplierRequest.getContactPhone());
        }

        // 手机号码
        if (StrUtil.isNotBlank(erpSupplierRequest.getContactMobile())) {
            wrapper.like(ErpSupplier::getContactMobile, erpSupplierRequest.getContactMobile());
        }

        // 经营方式
        if (StrUtil.isNotBlank(erpSupplierRequest.getBusinessMode())) {
            wrapper.eq(ErpSupplier::getBusinessMode, erpSupplierRequest.getBusinessMode());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(ErpSupplier::getCreateTime);

        return wrapper;
    }

    /**
     * 校验供应商参数
     *
     * @param erpSupplier 供应商实体
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private void validateSupplierParams(ErpSupplier erpSupplier) {
        // 校验供应商类型
        this.validateSupplierType(erpSupplier.getSupplierType());

        // 校验状态
        this.validateStatus(erpSupplier.getStatus());

        // 校验信用等级
        this.validateCreditLevel(erpSupplier.getCreditLevel());

        // 校验经营方式
        this.validateBusinessMode(erpSupplier.getBusinessMode());

        // TODO: 校验区域是否存在
        // TODO: 校验联系方式格式
    }

    /**
     * 校验供应商类型
     *
     * @param supplierType 供应商类型
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private void validateSupplierType(String supplierType) {
        if (!ErpSupplierConstants.SUPPLIER_TYPE_ENTERPRISE.equals(supplierType) &&
                !ErpSupplierConstants.SUPPLIER_TYPE_INDIVIDUAL.equals(supplierType)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_TYPE_ERROR);
        }
    }

    /**
     * 校验状态
     *
     * @param status 状态
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private void validateStatus(String status) {
        if (!ErpSupplierConstants.SUPPLIER_STATUS_ACTIVE.equals(status) &&
                !ErpSupplierConstants.SUPPLIER_STATUS_INACTIVE.equals(status) &&
                !ErpSupplierConstants.SUPPLIER_STATUS_BLACKLIST.equals(status)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_STATUS_ERROR);
        }
    }

    /**
     * 校验信用等级
     *
     * @param creditLevel 信用等级
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private void validateCreditLevel(String creditLevel) {
        if (!ErpSupplierConstants.CREDIT_LEVEL_A.equals(creditLevel) &&
                !ErpSupplierConstants.CREDIT_LEVEL_B.equals(creditLevel) &&
                !ErpSupplierConstants.CREDIT_LEVEL_C.equals(creditLevel) &&
                !ErpSupplierConstants.CREDIT_LEVEL_D.equals(creditLevel)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_CREDIT_LEVEL_ERROR);
        }
    }

    /**
     * 填充供应商扩展信息
     *
     * @param response 响应对象
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    private void fillSupplierExtInfo(ErpSupplierResponse response) {
        // 填充供应商类型名称
        if (ErpSupplierConstants.SUPPLIER_TYPE_ENTERPRISE.equals(response.getSupplierType())) {
            response.setSupplierTypeName("企业");
        } else if (ErpSupplierConstants.SUPPLIER_TYPE_INDIVIDUAL.equals(response.getSupplierType())) {
            response.setSupplierTypeName("个体");
        }

        // 填充状态名称
        if (ErpSupplierConstants.SUPPLIER_STATUS_ACTIVE.equals(response.getStatus())) {
            response.setStatusName("正常");
        } else if (ErpSupplierConstants.SUPPLIER_STATUS_INACTIVE.equals(response.getStatus())) {
            response.setStatusName("停用");
        } else if (ErpSupplierConstants.SUPPLIER_STATUS_BLACKLIST.equals(response.getStatus())) {
            response.setStatusName("黑名单");
        }

        // 填充信用等级名称
        if (ErpSupplierConstants.CREDIT_LEVEL_A.equals(response.getCreditLevel())) {
            response.setCreditLevelName("优秀");
        } else if (ErpSupplierConstants.CREDIT_LEVEL_B.equals(response.getCreditLevel())) {
            response.setCreditLevelName("良好");
        } else if (ErpSupplierConstants.CREDIT_LEVEL_C.equals(response.getCreditLevel())) {
            response.setCreditLevelName("一般");
        } else if (ErpSupplierConstants.CREDIT_LEVEL_D.equals(response.getCreditLevel())) {
            response.setCreditLevelName("较差");
        }

        // 填充经营方式名称
        if (ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE.equals(response.getBusinessMode())) {
            response.setBusinessModeName("购销");
        } else if (ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE.equals(response.getBusinessMode())) {
            response.setBusinessModeName("联营");
        } else if (ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(response.getBusinessMode())) {
            response.setBusinessModeName("代销");
        }

        // 填充区域信息
        erpSupplierRegionService.fillSupplierRegionInfo(response);
    }

    /**
     * 校验经营方式
     *
     * @param businessMode 经营方式
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    private void validateBusinessMode(String businessMode) {
        if (!supplierBusinessModeRuleService.validateBusinessMode(businessMode)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_BUSINESS_MODE_ERROR);
        }
    }

    @Override
    public List<ErpProductResponse> getSupplierProducts(ErpSupplierRequest erpSupplierRequest) {
        // TODO: 实现获取供应商关联商品列表的逻辑
        // 这里需要查询商品表中supplier_id等于当前供应商ID的商品
        // 暂时返回空列表，等商品模块扩展完成后再实现
        return CollUtil.newArrayList();
    }

    @Override
    public BusinessModeChangeValidationResponse validateBusinessModeChange(ErpSupplierRequest erpSupplierRequest) {
        // 获取当前供应商信息
        ErpSupplier currentSupplier = this.querySupplier(erpSupplierRequest);
        String newBusinessMode = erpSupplierRequest.getBusinessMode();
        
        // 使用业务规则服务进行验证
        return supplierBusinessModeRuleService.validateBusinessModeChange(currentSupplier, newBusinessMode);
    }

}
