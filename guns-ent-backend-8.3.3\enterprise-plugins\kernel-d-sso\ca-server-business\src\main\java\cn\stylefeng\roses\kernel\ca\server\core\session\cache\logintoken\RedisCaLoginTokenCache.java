package cn.stylefeng.roses.kernel.ca.server.core.session.cache.logintoken;

import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.cache.redis.AbstractRedisCacheOperator;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Set;

/**
 * 基于redis的token的缓存
 * <p>
 * key：    用户id
 * value：  用户的所有CAID，用户在一个机器登录一次单点就会有一个CAID
 *
 * <AUTHOR>
 * @date 2020/12/24 19:16
 */
public class RedisCaLoginTokenCache extends AbstractRedisCacheOperator<Set<String>> {

    public RedisCaLoginTokenCache(RedisTemplate<String, Set<String>> redisTemplate) {
        super(redisTemplate);
    }

    @Override
    public String getCommonKeyPrefix() {
        return CaServerConstants.CA_USER_ID_CACHE_PREFIX;
    }

}
