import{R as t}from"./index-18a1ea24.js";class u{static add(e){return t.post("/erp/product/add",e)}static delete(e){return t.post("/erp/product/delete",e)}static batchDelete(e){return t.post("/erp/product/batchDelete",e)}static edit(e){return t.post("/erp/product/edit",e)}static detail(e){return t.getAndLoadData("/erp/product/detail",e)}static findPage(e){return t.getAndLoadData("/erp/product/page",e)}static findList(e){return t.getAndLoadData("/erp/product/list",e)}static updateStatus(e){return t.post("/erp/product/updateStatus",e)}static validateCode(e){return t.getAndLoadData("/erp/product/validateCode",e)}static validateBarcode(e){return t.getAndLoadData("/erp/product/validateBarcode",e)}static getProductStatusOptions(){return[{label:"\u6B63\u5E38",value:"ACTIVE"},{label:"\u505C\u7528",value:"INACTIVE"},{label:"\u505C\u4EA7",value:"DISCONTINUED"}]}static getPricingTypeOptions(){return[{label:"\u666E\u901A",value:"NORMAL"},{label:"\u8BA1\u91CD",value:"WEIGHT"},{label:"\u8BA1\u4EF6",value:"PIECE"},{label:"\u4E0D\u5B9A\u4EF7",value:"VARIABLE"}]}static getCommonUnitOptions(){return[{label:"\u4E2A",value:"\u4E2A"},{label:"\u4EF6",value:"\u4EF6"},{label:"\u76D2",value:"\u76D2"},{label:"\u5305",value:"\u5305"},{label:"\u888B",value:"\u888B"},{label:"\u74F6",value:"\u74F6"},{label:"\u7F50",value:"\u7F50"},{label:"\u6876",value:"\u6876"},{label:"\u7BB1",value:"\u7BB1"},{label:"\u53F0",value:"\u53F0"},{label:"\u5957",value:"\u5957"},{label:"\u526F",value:"\u526F"},{label:"\u53CC",value:"\u53CC"},{label:"\u5BF9",value:"\u5BF9"},{label:"\u5F20",value:"\u5F20"},{label:"\u672C",value:"\u672C"},{label:"\u652F",value:"\u652F"},{label:"\u6839",value:"\u6839"},{label:"\u6761",value:"\u6761"},{label:"\u7C73",value:"\u7C73"},{label:"\u5343\u514B",value:"\u5343\u514B"},{label:"\u514B",value:"\u514B"},{label:"\u5347",value:"\u5347"},{label:"\u6BEB\u5347",value:"\u6BEB\u5347"}]}static getProductStatusName(e){const a=u.getProductStatusOptions().find(l=>l.value===e);return a?a.label:e}static getPricingTypeName(e){const a=u.getPricingTypeOptions().find(l=>l.value===e);return a?a.label:e}static getStatusTagColor(e){switch(e){case"ACTIVE":return"green";case"INACTIVE":return"orange";case"DISCONTINUED":return"red";default:return"default"}}static getPricingTypeTagColor(e){switch(e){case"NORMAL":return"blue";case"WEIGHT":return"green";case"PIECE":return"orange";case"VARIABLE":return"purple";default:return"default"}}static formatWeight(e){return e?"".concat(e,"kg"):"-"}static formatVolume(e){return e?"".concat(e,"m\xB3"):"-"}static formatShelfLife(e){return e?"".concat(e,"\u5929"):"-"}static getProductsBySupplier(e){return t.getAndLoadData("/erp/product/listBySupplier",e)}static validatePricingTypeChange(e){return t.getAndLoadData("/erp/product/validatePricingTypeChange",e)}static formatPrice(e,r){if(!e)return"-";switch(r){case"NORMAL":return"\xA5".concat(e.toFixed(2));case"WEIGHT":return"\xA5".concat(e.toFixed(2),"/kg");case"PIECE":return"\xA5".concat(e.toFixed(2),"/\u4EFD");case"VARIABLE":return"\u53C2\u8003\u4EF7 \xA5".concat(e.toFixed(2));default:return"\xA5".concat(e.toFixed(2))}}static getCategoryTree(){return t.getAndLoadData("/erp/productCategory/tree")}}export{u as P};
