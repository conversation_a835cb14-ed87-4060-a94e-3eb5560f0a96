package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 库存管理模块异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@Getter
public enum InventoryExceptionEnum implements AbstractExceptionEnum {

    /**
     * 库存记录不存在
     */
    INVENTORY_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30001", "库存记录不存在"),

    /**
     * 库存历史记录不存在
     */
    INVENTORY_HISTORY_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30002", "库存历史记录不存在"),

    /**
     * 库存不足
     */
    INSUFFICIENT_INVENTORY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30003", "库存不足"),

    /**
     * 库存操作类型错误
     */
    INVENTORY_OPERATION_TYPE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30004", "库存操作类型错误，只能为：入库、出库、调整、销售"),

    /**
     * 库存数量必须大于0
     */
    INVENTORY_QUANTITY_MUST_GREATER_THAN_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30005", "库存数量必须大于0"),

    /**
     * 最小库存必须大于等于0
     */
    MIN_STOCK_MUST_GREATER_THAN_OR_EQUAL_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30006", "最小库存必须大于等于0"),

    /**
     * 平均成本必须大于等于0
     */
    AVG_COST_MUST_GREATER_THAN_OR_EQUAL_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30007", "平均成本必须大于等于0"),

    /**
     * 库存总价值必须大于等于0
     */
    TOTAL_VALUE_MUST_GREATER_THAN_OR_EQUAL_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30008", "库存总价值必须大于等于0"),

    /**
     * 商品不存在
     */
    PRODUCT_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30009", "商品不存在"),

    /**
     * 商品已存在库存记录
     */
    PRODUCT_INVENTORY_ALREADY_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30010", "商品已存在库存记录"),

    /**
     * 联营商品不进行库存管理
     */
    JOINT_VENTURE_PRODUCT_NO_INVENTORY_MANAGEMENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30011", "联营商品不进行库存管理"),

    /**
     * 库存操作失败
     */
    INVENTORY_OPERATION_FAILED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30012", "库存操作失败"),

    /**
     * 关联单据类型错误
     */
    REFERENCE_TYPE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30013", "关联单据类型错误"),

    /**
     * 关联单据不存在
     */
    REFERENCE_ORDER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30014", "关联单据不存在"),

    /**
     * 库存预警阈值设置错误
     */
    INVENTORY_WARNING_THRESHOLD_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30015", "库存预警阈值设置错误"),

    /**
     * 库存调整数量不能为0
     */
    INVENTORY_ADJUST_QUANTITY_CANNOT_BE_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30016", "库存调整数量不能为0"),

    /**
     * 出库数量不能大于当前库存
     */
    OUT_QUANTITY_CANNOT_GREATER_THAN_CURRENT_STOCK(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30017", "出库数量不能大于当前库存"),

    /**
     * 最大库存必须大于等于0
     */
    MAX_STOCK_MUST_GREATER_THAN_OR_EQUAL_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30018", "最大库存必须大于等于0"),

    /**
     * 最大库存必须大于最小库存
     */
    MAX_STOCK_MUST_GREATER_THAN_MIN_STOCK(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "30019", "最大库存必须大于最小库存");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    InventoryExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }
}