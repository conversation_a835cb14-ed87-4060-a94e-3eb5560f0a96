<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.ca.server.modular.manage.mapper.SsoClientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.ca.server.modular.manage.entity.SsoClient">
		<id column="client_id" property="clientId" />
		<result column="client_name" property="clientName" />
		<result column="client_logo_file_id" property="clientLogoFileId" />
		<result column="login_page_type" property="loginPageType" />
		<result column="unified_logout_flag" property="unifiedLogoutFlag" />
		<result column="sso_callback_url" property="ssoCallbackUrl" />
		<result column="sso_logout_url" property="ssoLogoutUrl" />
		<result column="custom_login_url" property="customLoginUrl" />
		<result column="ca_token_secret" property="caTokenSecret" />
		<result column="client_status" property="clientStatus" />
		<result column="client_sort" property="clientSort" />
		<result column="client_description" property="clientDescription" />
		<result column="version_flag" property="versionFlag" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		client_id,client_name,client_logo_file_id,login_page_type,unified_logout_flag,sso_callback_url,sso_logout_url,custom_login_url,ca_token_secret,client_status,client_sort,client_description,version_flag,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.ca.server.modular.manage.pojo.response.SsoClientVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		ent_sso_client tbl
		WHERE
		<where>
        <if test="param.clientId != null and param.clientId != ''">
            and tbl.client_id like concat('%',#{param.clientId},'%')
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            and tbl.client_name like concat('%',#{param.clientName},'%')
        </if>
        <if test="param.clientLogoFileId != null and param.clientLogoFileId != ''">
            and tbl.client_logo_file_id like concat('%',#{param.clientLogoFileId},'%')
        </if>
        <if test="param.loginPageType != null and param.loginPageType != ''">
            and tbl.login_page_type like concat('%',#{param.loginPageType},'%')
        </if>
        <if test="param.unifiedLogoutFlag != null and param.unifiedLogoutFlag != ''">
            and tbl.unified_logout_flag like concat('%',#{param.unifiedLogoutFlag},'%')
        </if>
        <if test="param.ssoCallbackUrl != null and param.ssoCallbackUrl != ''">
            and tbl.sso_callback_url like concat('%',#{param.ssoCallbackUrl},'%')
        </if>
        <if test="param.ssoLogoutUrl != null and param.ssoLogoutUrl != ''">
            and tbl.sso_logout_url like concat('%',#{param.ssoLogoutUrl},'%')
        </if>
        <if test="param.customLoginUrl != null and param.customLoginUrl != ''">
            and tbl.custom_login_url like concat('%',#{param.customLoginUrl},'%')
        </if>
        <if test="param.caTokenSecret != null and param.caTokenSecret != ''">
            and tbl.ca_token_secret like concat('%',#{param.caTokenSecret},'%')
        </if>
        <if test="param.clientStatus != null and param.clientStatus != ''">
            and tbl.client_status like concat('%',#{param.clientStatus},'%')
        </if>
        <if test="param.clientSort != null and param.clientSort != ''">
            and tbl.client_sort like concat('%',#{param.clientSort},'%')
        </if>
        <if test="param.clientDescription != null and param.clientDescription != ''">
            and tbl.client_description like concat('%',#{param.clientDescription},'%')
        </if>
        <if test="param.versionFlag != null and param.versionFlag != ''">
            and tbl.version_flag like concat('%',#{param.versionFlag},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
