package cn.stylefeng.roses.kernel.manage.mapper;

import cn.stylefeng.roses.kernel.manage.entity.ApiClientAuth;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiClientAuthRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.ApiClientAuthVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API客户端和资源绑定关系 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
public interface ApiClientAuthMapper extends BaseMapper<ApiClientAuth> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    List<ApiClientAuthVo> customFindList(@Param("page") Page page, @Param("param") ApiClientAuthRequest request);

}
