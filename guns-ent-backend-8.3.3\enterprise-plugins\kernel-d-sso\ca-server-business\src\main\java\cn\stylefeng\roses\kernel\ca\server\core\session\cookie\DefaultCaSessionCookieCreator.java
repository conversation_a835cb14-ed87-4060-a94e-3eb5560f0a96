package cn.stylefeng.roses.kernel.ca.server.core.session.cookie;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.ca.api.cookie.CaSessionCookieCreator;
import cn.stylefeng.roses.kernel.ca.api.expander.CaServerConfigExpander;
import jakarta.servlet.http.Cookie;


/**
 * 默认的cookie创建
 * <p>
 * 这里预留了expandCookieProp的接口可以拓展cookie的属性
 *
 * <AUTHOR>
 * @date 2020/12/27 13:29
 */
public class DefaultCaSessionCookieCreator extends CaSessionCookieCreator {

    @Override
    public void expandCookieProp(Cookie cookie) {
        cookie.setHttpOnly(true);
        cookie.setPath("/");
        if (StrUtil.isNotEmpty(CaServerConfigExpander.getCookieDomain())) {
            cookie.setDomain(CaServerConfigExpander.getCookieDomain());
        }
    }

}
