System.register(["./index-legacy-ee1db0c7.js","./config-type-form-legacy-e04eb611.js"],(function(e,t){"use strict";var a,i,o,l,n,s,d,r,u,c;return{setters:[e=>{a=e.R,i=e.r,o=e.o,l=e.a,n=e.f,s=e.w,d=e.d,r=e.m,u=e.M},e=>{c=e.default}],execute:function(){class t{static add(e){return a.post("/sysConfigType/add",e)}static edit(e){return a.post("/sysConfigType/edit",e)}static delete(e){return a.post("/sysConfigType/delete",e)}static detail(e){return a.getAndLoadData("/sysConfigType/detail",e)}static list(e){return a.getAndLoadData("/sysConfigType/list",e)}}e("S",t);const f=e("_",{__name:"config-type-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const f=e,y=a,p=i(!1),v=i(!1),g=i({configTypeSort:100}),m=i(null);o((()=>{f.data?(v.value=!0,b()):v.value=!1}));const b=()=>{t.detail({configTypeId:f.data.id}).then((e=>{e&&(g.value.configTypeSort=e.dictSort,g.value.configTypeName=e.dictName,g.value.configTypeCode=e.dictCode,g.value.configTypeId=e.dictId)}))},T=e=>{y("update:visible",e)},C=async()=>{m.value.$refs.formRef.validate().then((async e=>{if(e){p.value=!0;let e=null;e=v.value?t.edit(g.value):t.add(g.value),e.then((async e=>{p.value=!1,r.success(e.message),T(!1),y("done")})).catch((()=>{p.value=!1}))}}))};return(e,t)=>{const a=u;return l(),n(a,{width:700,maskClosable:!1,visible:f.visible,"confirm-loading":p.value,forceRender:!0,title:v.value?"编辑配置分类":"新建配置分类","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":T,onOk:C,onClose:t[1]||(t[1]=e=>T(!1))},{default:s((()=>[d(c,{form:g.value,"onUpdate:form":t[0]||(t[0]=e=>g.value=e),ref_key:"typeFormRef",ref:m},null,8,["form"])])),_:1},8,["visible","confirm-loading","title"])}}}),y=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));e("c",y)}}}));
