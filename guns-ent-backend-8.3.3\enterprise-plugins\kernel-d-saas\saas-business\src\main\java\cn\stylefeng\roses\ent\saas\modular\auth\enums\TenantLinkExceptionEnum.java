package cn.stylefeng.roses.ent.saas.modular.auth.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 租户-功能包异常相关枚举
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@Getter
public enum TenantLinkExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    TENANT_LINK_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE +  "10001", "查询结果不存在");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    TenantLinkExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
