# JavaGuns Enterprise 库存预警功能实现设计文档

## 一、项目架构概述

基于JavaGuns Enterprise的分层架构设计，库存预警功能将按照以下模块化结构实现：

### 后端架构
```
kernel-d-erp/erp-business/src/main/java/cn/stylefeng/roses/kernel/erp/modular/
├── inventoryalert/                    # 库存预警模块
│   ├── controller/                    # 控制器层
│   │   ├── InventoryAlertRuleController.java
│   │   ├── InventoryAlertRecordController.java
│   │   └── InventoryAlertConfigController.java
│   ├── service/                       # 服务层
│   │   ├── InventoryAlertRuleService.java
│   │   ├── InventoryAlertRecordService.java
│   │   ├── InventoryAlertConfigService.java
│   │   └── InventoryAlertCheckService.java
│   ├── mapper/                        # 数据访问层
│   │   ├── InventoryAlertRuleMapper.java
│   │   ├── InventoryAlertRecordMapper.java
│   │   └── InventoryAlertConfigMapper.java
│   └── task/                          # 定时任务
│       └── InventoryAlertScheduleTask.java
```

### 前端架构
```
guns-ent-front-8.3.3/src/views/erp/
├── inventoryAlert/                    # 库存预警模块
│   ├── api/                          # API接口
│   │   ├── InventoryAlertRuleApi.js
│   │   ├── InventoryAlertRecordApi.js
│   │   └── InventoryAlertConfigApi.js
│   ├── components/                   # 专用组件
│   │   ├── AlertRuleForm.vue
│   │   ├── AlertRecordList.vue
│   │   ├── AlertDashboard.vue
│   │   └── AlertConfigForm.vue
│   ├── rule/                         # 预警规则管理
│   │   └── index.vue
│   ├── record/                       # 预警记录管理
│   │   └── index.vue
│   ├── dashboard/                    # 库存监控看板
│   │   └── index.vue
│   └── config/                       # 系统配置
│       └── index.vue
```

## 二、后端实现步骤

### 2.1 实体类和请求响应对象

#### 2.1.1 创建实体类
在 `kernel-d-erp/erp-api/src/main/java/cn/stylefeng/roses/kernel/erp/api/pojo/entity/` 目录下创建：

**InventoryAlertRule.java**
```java
@TableName("erp_inventory_alert_rule")
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertRule extends BaseEntity {
    
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    
    @TableField("rule_name")
    private String ruleName;
    
    @TableField("rule_type")
    private String ruleType;
    
    @TableField("target_type")
    private String targetType;
    
    @TableField("target_id")
    private Long targetId;
    
    @TableField("alert_level")
    private String alertLevel;
    
    @TableField("threshold_type")
    private String thresholdType;
    
    @TableField("threshold_value")
    private BigDecimal thresholdValue;
    
    @TableField("comparison_operator")
    private String comparisonOperator;
    
    @TableField("is_enabled")
    private String isEnabled;
    
    @TableField("notification_methods")
    private String notificationMethods;
    
    @TableField("notification_users")
    private String notificationUsers;
    
    @TableField("check_frequency")
    private Integer checkFrequency;
    
    @TableField("last_check_time")
    private Date lastCheckTime;
    
    @TableField("remark")
    private String remark;
}
```

#### 2.1.2 创建请求对象
在 `kernel-d-erp/erp-api/src/main/java/cn/stylefeng/roses/kernel/erp/api/pojo/request/` 目录下创建：

**InventoryAlertRuleRequest.java**
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertRuleRequest extends BaseRequest {
    
    @NotNull(message = "规则ID不能为空", groups = {edit.class, delete.class, detail.class})
    private Long id;
    
    @NotBlank(message = "规则名称不能为空", groups = {add.class, edit.class})
    @Length(max = 100, message = "规则名称长度不能超过100个字符", groups = {add.class, edit.class})
    private String ruleName;
    
    @NotBlank(message = "预警类型不能为空", groups = {add.class, edit.class})
    private String ruleType;
    
    @NotBlank(message = "目标类型不能为空", groups = {add.class, edit.class})
    private String targetType;
    
    private Long targetId;
    
    @NotBlank(message = "预警级别不能为空", groups = {add.class, edit.class})
    private String alertLevel;
    
    @NotBlank(message = "阈值类型不能为空", groups = {add.class, edit.class})
    private String thresholdType;
    
    @NotNull(message = "阈值不能为空", groups = {add.class, edit.class})
    @DecimalMin(value = "0", message = "阈值不能小于0", groups = {add.class, edit.class})
    private BigDecimal thresholdValue;
    
    @NotBlank(message = "比较操作符不能为空", groups = {add.class, edit.class})
    private String comparisonOperator;
    
    private String isEnabled;
    private String notificationMethods;
    private String notificationUsers;
    
    @Min(value = 1, message = "检查频率不能小于1分钟", groups = {add.class, edit.class})
    private Integer checkFrequency;
    
    private String remark;
    
    // 查询条件
    private String searchText;
    private String ruleTypeFilter;
    private String alertLevelFilter;
    private String isEnabledFilter;
}
```

### 2.2 数据访问层（Mapper）

#### 2.2.1 InventoryAlertRuleMapper.java
```java
public interface InventoryAlertRuleMapper extends BaseMapper<InventoryAlertRule> {
    
    /**
     * 分页查询预警规则
     */
    Page<InventoryAlertRuleResponse> selectRulePage(Page<InventoryAlertRuleResponse> page, 
                                                   @Param("request") InventoryAlertRuleRequest request);
    
    /**
     * 查询启用的预警规则
     */
    @Select("SELECT * FROM erp_inventory_alert_rule WHERE is_enabled = 'Y' AND del_flag = 'N' " +
            "AND (last_check_time IS NULL OR TIMESTAMPDIFF(MINUTE, last_check_time, NOW()) >= check_frequency)")
    List<InventoryAlertRule> selectEnabledRules();
    
    /**
     * 根据目标类型和目标ID查询规则
     */
    List<InventoryAlertRule> selectByTarget(@Param("targetType") String targetType, 
                                          @Param("targetId") Long targetId);
}
```

#### 2.2.2 创建Mapper XML文件
在 `kernel-d-erp/erp-business/src/main/resources/mapping/inventoryalert/` 目录下创建：

**InventoryAlertRuleMapper.xml**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper.InventoryAlertRuleMapper">

    <select id="selectRulePage" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRuleResponse">
        SELECT 
            r.id,
            r.rule_name,
            r.rule_type,
            r.target_type,
            r.target_id,
            r.alert_level,
            r.threshold_type,
            r.threshold_value,
            r.comparison_operator,
            r.is_enabled,
            r.notification_methods,
            r.check_frequency,
            r.last_check_time,
            r.remark,
            r.create_time,
            r.update_time,
            CASE r.target_type
                WHEN 'PRODUCT' THEN p.product_name
                WHEN 'CATEGORY' THEN pc.category_name
                ELSE '全部商品'
            END AS target_name
        FROM erp_inventory_alert_rule r
        LEFT JOIN erp_product p ON r.target_type = 'PRODUCT' AND r.target_id = p.product_id
        LEFT JOIN erp_product_category pc ON r.target_type = 'CATEGORY' AND r.target_id = pc.category_id
        WHERE r.del_flag = 'N'
        <if test="request.searchText != null and request.searchText != ''">
            AND r.rule_name LIKE CONCAT('%', #{request.searchText}, '%')
        </if>
        <if test="request.ruleTypeFilter != null and request.ruleTypeFilter != ''">
            AND r.rule_type = #{request.ruleTypeFilter}
        </if>
        <if test="request.alertLevelFilter != null and request.alertLevelFilter != ''">
            AND r.alert_level = #{request.alertLevelFilter}
        </if>
        <if test="request.isEnabledFilter != null and request.isEnabledFilter != ''">
            AND r.is_enabled = #{request.isEnabledFilter}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <select id="selectByTarget" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule">
        SELECT * FROM erp_inventory_alert_rule 
        WHERE target_type = #{targetType} 
        AND (target_id = #{targetId} OR target_type = 'ALL')
        AND is_enabled = 'Y' 
        AND del_flag = 'N'
    </select>

</mapper>
```

### 2.3 服务层（Service）

#### 2.3.1 InventoryAlertRuleService.java
```java
public interface InventoryAlertRuleService extends IService<InventoryAlertRule> {
    
    /**
     * 新增预警规则
     */
    void add(InventoryAlertRuleRequest request);
    
    /**
     * 编辑预警规则
     */
    void edit(InventoryAlertRuleRequest request);
    
    /**
     * 删除预警规则
     */
    void del(InventoryAlertRuleRequest request);
    
    /**
     * 批量删除预警规则
     */
    void batchDelete(InventoryAlertRuleRequest request);
    
    /**
     * 查询预警规则详情
     */
    InventoryAlertRuleResponse detail(InventoryAlertRuleRequest request);
    
    /**
     * 分页查询预警规则
     */
    PageResult<InventoryAlertRuleResponse> findPage(InventoryAlertRuleRequest request);
    
    /**
     * 更新规则状态
     */
    void updateStatus(InventoryAlertRuleRequest request);
    
    /**
     * 测试预警规则
     */
    List<InventoryAlertTestResult> testRule(InventoryAlertRuleRequest request);
}
```

#### 2.3.2 InventoryAlertRuleServiceImpl.java
```java
@Slf4j
@Service
public class InventoryAlertRuleServiceImpl extends ServiceImpl<InventoryAlertRuleMapper, InventoryAlertRule>
        implements InventoryAlertRuleService {

    @Resource
    private InventoryAlertRuleMapper inventoryAlertRuleMapper;

    @Override
    public void add(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = new InventoryAlertRule();
        BeanUtil.copyProperties(request, entity);
        entity.setId(IdWorker.getId());
        entity.setIsEnabled("Y");
        entity.setDelFlag("N");
        this.save(entity);
    }

    @Override
    public void edit(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        BeanUtil.copyProperties(request, entity);
        this.updateById(entity);
    }

    @Override
    public void del(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        entity.setDelFlag("Y");
        this.updateById(entity);
    }

    @Override
    public void batchDelete(InventoryAlertRuleRequest request) {
        List<Long> idList = request.getIdList();
        if (CollUtil.isNotEmpty(idList)) {
            List<InventoryAlertRule> entities = this.listByIds(idList);
            entities.forEach(entity -> entity.setDelFlag("Y"));
            this.updateBatchById(entities);
        }
    }

    @Override
    public InventoryAlertRuleResponse detail(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        InventoryAlertRuleResponse response = new InventoryAlertRuleResponse();
        BeanUtil.copyProperties(entity, response);
        return response;
    }

    @Override
    public PageResult<InventoryAlertRuleResponse> findPage(InventoryAlertRuleRequest request) {
        LambdaQueryWrapper<InventoryAlertRule> wrapper = createWrapper(request);
        Page<InventoryAlertRuleResponse> page = new Page<>(request.getPageNo(), request.getPageSize());
        Page<InventoryAlertRuleResponse> result = inventoryAlertRuleMapper.selectRulePage(page, request);
        return PageResultUtil.createPageResult(result);
    }

    @Override
    public void updateStatus(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        entity.setIsEnabled(request.getIsEnabled());
        this.updateById(entity);
    }

    @Override
    public List<InventoryAlertTestResult> testRule(InventoryAlertRuleRequest request) {
        // 实现预警规则测试逻辑
        // 根据规则条件查询符合条件的库存数据
        // 返回测试结果
        return new ArrayList<>();
    }

    /**
     * 查询预警规则
     */
    private InventoryAlertRule queryInventoryAlertRule(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.getById(request.getId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new ServiceException(InventoryAlertExceptionEnum.RULE_NOT_FOUND);
        }
        return entity;
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<InventoryAlertRule> createWrapper(InventoryAlertRuleRequest request) {
        LambdaQueryWrapper<InventoryAlertRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InventoryAlertRule::getDelFlag, "N");

        if (ObjectUtil.isNotEmpty(request.getSearchText())) {
            queryWrapper.like(InventoryAlertRule::getRuleName, request.getSearchText());
        }

        if (ObjectUtil.isNotEmpty(request.getRuleTypeFilter())) {
            queryWrapper.eq(InventoryAlertRule::getRuleType, request.getRuleTypeFilter());
        }

        if (ObjectUtil.isNotEmpty(request.getAlertLevelFilter())) {
            queryWrapper.eq(InventoryAlertRule::getAlertLevel, request.getAlertLevelFilter());
        }

        if (ObjectUtil.isNotEmpty(request.getIsEnabledFilter())) {
            queryWrapper.eq(InventoryAlertRule::getIsEnabled, request.getIsEnabledFilter());
        }

        queryWrapper.orderByDesc(InventoryAlertRule::getCreateTime);
        return queryWrapper;
    }
}
```

#### 2.3.3 InventoryAlertCheckService.java（核心预警检查服务）
```java
@Slf4j
@Service
public class InventoryAlertCheckService {

    @Resource
    private InventoryAlertRuleMapper ruleMapper;

    @Resource
    private InventoryAlertRecordService recordService;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private ErpProductMapper productMapper;

    /**
     * 执行库存预警检查
     */
    @Async
    public void executeAlertCheck() {
        log.info("开始执行库存预警检查");

        try {
            // 获取所有启用的预警规则
            List<InventoryAlertRule> rules = ruleMapper.selectEnabledRules();

            for (InventoryAlertRule rule : rules) {
                checkRuleAlert(rule);
                // 更新规则的最后检查时间
                rule.setLastCheckTime(new Date());
                ruleMapper.updateById(rule);
            }

            log.info("库存预警检查完成，共检查{}条规则", rules.size());
        } catch (Exception e) {
            log.error("库存预警检查异常", e);
        }
    }

    /**
     * 检查单个规则的预警
     */
    private void checkRuleAlert(InventoryAlertRule rule) {
        switch (rule.getRuleType()) {
            case "LOW_STOCK":
                checkLowStockAlert(rule);
                break;
            case "ZERO_STOCK":
                checkZeroStockAlert(rule);
                break;
            case "OVERSTOCK":
                checkOverstockAlert(rule);
                break;
            case "EXPIRY":
                checkExpiryAlert(rule);
                break;
            default:
                log.warn("未知的预警类型: {}", rule.getRuleType());
        }
    }

    /**
     * 检查低库存预警
     */
    private void checkLowStockAlert(InventoryAlertRule rule) {
        // 根据规则的目标类型查询库存数据
        List<Inventory> inventories = getInventoriesByRule(rule);

        for (Inventory inventory : inventories) {
            if (isLowStock(inventory, rule)) {
                createAlertRecord(rule, inventory, "库存不足预警");
            }
        }
    }

    /**
     * 检查零库存预警
     */
    private void checkZeroStockAlert(InventoryAlertRule rule) {
        List<Inventory> inventories = getInventoriesByRule(rule);

        for (Inventory inventory : inventories) {
            if (inventory.getCurrentStock().compareTo(BigDecimal.ZERO) == 0) {
                createAlertRecord(rule, inventory, "零库存紧急预警");
            }
        }
    }

    /**
     * 根据规则获取库存数据
     */
    private List<Inventory> getInventoriesByRule(InventoryAlertRule rule) {
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();

        if ("PRODUCT".equals(rule.getTargetType())) {
            wrapper.eq(Inventory::getProductId, rule.getTargetId());
        } else if ("CATEGORY".equals(rule.getTargetType())) {
            // 查询指定分类下的所有商品库存
            List<Long> productIds = getProductIdsByCategory(rule.getTargetId());
            if (CollUtil.isNotEmpty(productIds)) {
                wrapper.in(Inventory::getProductId, productIds);
            }
        }
        // ALL类型不需要额外条件

        return inventoryMapper.selectList(wrapper);
    }

    /**
     * 判断是否为低库存
     */
    private boolean isLowStock(Inventory inventory, InventoryAlertRule rule) {
        BigDecimal currentStock = inventory.getCurrentStock();
        BigDecimal threshold = rule.getThresholdValue();

        if ("QUANTITY".equals(rule.getThresholdType())) {
            return currentStock.compareTo(threshold) <= 0;
        } else if ("PERCENTAGE".equals(rule.getThresholdType())) {
            BigDecimal minStock = inventory.getMinStock();
            if (minStock != null && minStock.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal percentage = currentStock.divide(minStock, 4, RoundingMode.HALF_UP)
                                                  .multiply(new BigDecimal("100"));
                return percentage.compareTo(threshold) <= 0;
            }
        }

        return false;
    }

    /**
     * 创建预警记录
     */
    private void createAlertRecord(InventoryAlertRule rule, Inventory inventory, String message) {
        // 检查是否已存在未处理的相同预警
        if (hasUnresolvedAlert(rule.getId(), inventory.getProductId())) {
            return;
        }

        InventoryAlertRecordRequest request = new InventoryAlertRecordRequest();
        request.setRuleId(rule.getId());
        request.setProductId(inventory.getProductId());
        request.setAlertType(rule.getRuleType());
        request.setAlertLevel(rule.getAlertLevel());
        request.setCurrentStock(inventory.getCurrentStock());
        request.setThresholdValue(rule.getThresholdValue());
        request.setAlertMessage(message);
        request.setStatus("PENDING");

        // 生成建议操作
        generateSuggestedAction(request, inventory, rule);

        recordService.add(request);

        // 发送通知
        sendNotification(rule, request);
    }
}
```

### 2.4 控制器层（Controller）

#### 2.4.1 InventoryAlertRuleController.java
```java
@RestController
@ApiResource(name = "库存预警规则管理", requiredPermission = true, requirePermissionCode = "ERP_INVENTORY_ALERT")
public class InventoryAlertRuleController {

    @Resource
    private InventoryAlertRuleService inventoryAlertRuleService;

    /**
     * 新增预警规则
     */
    @PostResource(name = "新增预警规则", path = "/erp/inventoryAlert/rule/add")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
    public ResponseData<?> add(@RequestBody @Validated(InventoryAlertRuleRequest.add.class)
                              InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.add(request);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑预警规则
     */
    @PostResource(name = "编辑预警规则", path = "/erp/inventoryAlert/rule/edit")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
    public ResponseData<?> edit(@RequestBody @Validated(InventoryAlertRuleRequest.edit.class)
                               InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.edit(request);
        return new SuccessResponseData<>();
    }

    /**
     * 删除预警规则
     */
    @PostResource(name = "删除预警规则", path = "/erp/inventoryAlert/rule/delete")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
    public ResponseData<?> delete(@RequestBody @Validated(InventoryAlertRuleRequest.delete.class)
                                 InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.del(request);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除预警规则
     */
    @PostResource(name = "批量删除预警规则", path = "/erp/inventoryAlert/rule/batchDelete")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
    public ResponseData<?> batchDelete(@RequestBody @Validated(InventoryAlertRuleRequest.batchDelete.class)
                                      InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.batchDelete(request);
        return new SuccessResponseData<>();
    }

    /**
     * 查询预警规则详情
     */
    @GetResource(name = "查询预警规则详情", path = "/erp/inventoryAlert/rule/detail")
    public ResponseData<InventoryAlertRuleResponse> detail(@Validated(InventoryAlertRuleRequest.detail.class)
                                                          InventoryAlertRuleRequest request) {
        InventoryAlertRuleResponse response = inventoryAlertRuleService.detail(request);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询预警规则
     */
    @GetResource(name = "分页查询预警规则", path = "/erp/inventoryAlert/rule/page")
    public ResponseData<PageResult<InventoryAlertRuleResponse>> page(InventoryAlertRuleRequest request) {
        PageResult<InventoryAlertRuleResponse> result = inventoryAlertRuleService.findPage(request);
        return new SuccessResponseData<>(result);
    }

    /**
     * 更新预警规则状态
     */
    @PostResource(name = "更新预警规则状态", path = "/erp/inventoryAlert/rule/updateStatus")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
    public ResponseData<?> updateStatus(@RequestBody @Validated(InventoryAlertRuleRequest.updateStatus.class)
                                       InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.updateStatus(request);
        return new SuccessResponseData<>();
    }

    /**
     * 测试预警规则
     */
    @PostResource(name = "测试预警规则", path = "/erp/inventoryAlert/rule/test")
    public ResponseData<List<InventoryAlertTestResult>> testRule(@RequestBody @Validated(InventoryAlertRuleRequest.detail.class)
                                                                InventoryAlertRuleRequest request) {
        List<InventoryAlertTestResult> result = inventoryAlertRuleService.testRule(request);
        return new SuccessResponseData<>(result);
    }

    /**
     * 手动执行预警检查
     */
    @PostResource(name = "手动执行预警检查", path = "/erp/inventoryAlert/rule/executeCheck")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
    public ResponseData<?> executeCheck() {
        // 调用预警检查服务
        return new SuccessResponseData<>();
    }
}
```

### 2.5 定时任务

#### 2.5.1 InventoryAlertScheduleTask.java
```java
@Component
@Slf4j
public class InventoryAlertScheduleTask {

    @Resource
    private InventoryAlertCheckService alertCheckService;

    @Resource
    private InventoryAlertConfigService configService;

    /**
     * 库存预警检查定时任务
     * 默认每30分钟执行一次
     */
    @Scheduled(fixedRate = 30 * 60 * 1000)
    public void executeInventoryAlertCheck() {
        // 检查是否启用预警功能
        if (!isAlertEnabled()) {
            return;
        }

        log.info("开始执行定时库存预警检查");
        alertCheckService.executeAlertCheck();
    }

    /**
     * 预警记录清理任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupAlertRecords() {
        log.info("开始执行预警记录清理任务");
        // 清理超过30天的已解决预警记录
        // 实现清理逻辑
    }

    /**
     * 检查预警功能是否启用
     */
    private boolean isAlertEnabled() {
        String enabled = configService.getConfigValue("alert.enabled", "true");
        return "true".equalsIgnoreCase(enabled);
    }
}
```

## 三、前端实现步骤

### 3.1 API接口层

#### 3.1.1 创建全局ERP API接口
在 `guns-ent-front-8.3.3/src/api/erp/` 目录下创建：

**inventoryAlert.js**
```javascript
import Request from '@/utils/request/request-util';

/**
 * 库存预警API接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
export class InventoryAlertApi {

  /**
   * 获取预警类型选项
   */
  static getAlertTypeOptions() {
    return [
      { label: '库存不足', value: 'LOW_STOCK' },
      { label: '零库存', value: 'ZERO_STOCK' },
      { label: '库存积压', value: 'OVERSTOCK' },
      { label: '临期预警', value: 'EXPIRY' }
    ];
  }

  /**
   * 获取预警级别选项
   */
  static getAlertLevelOptions() {
    return [
      { label: '紧急', value: 'CRITICAL', color: 'red' },
      { label: '警告', value: 'WARNING', color: 'orange' },
      { label: '提醒', value: 'INFO', color: 'blue' }
    ];
  }

  /**
   * 获取目标类型选项
   */
  static getTargetTypeOptions() {
    return [
      { label: '单个商品', value: 'PRODUCT' },
      { label: '商品分类', value: 'CATEGORY' },
      { label: '全部商品', value: 'ALL' }
    ];
  }

  /**
   * 获取阈值类型选项
   */
  static getThresholdTypeOptions() {
    return [
      { label: '数量', value: 'QUANTITY' },
      { label: '百分比', value: 'PERCENTAGE' },
      { label: '天数', value: 'DAYS' }
    ];
  }

  /**
   * 获取比较操作符选项
   */
  static getComparisonOperatorOptions() {
    return [
      { label: '小于等于', value: 'LTE' },
      { label: '小于', value: 'LT' },
      { label: '大于等于', value: 'GTE' },
      { label: '大于', value: 'GT' },
      { label: '等于', value: 'EQ' }
    ];
  }

  /**
   * 获取通知方式选项
   */
  static getNotificationMethodOptions() {
    return [
      { label: '系统通知', value: 'SYSTEM' },
      { label: '邮件通知', value: 'EMAIL' },
      { label: '短信通知', value: 'SMS' },
      { label: '微信通知', value: 'WECHAT' }
    ];
  }

  /**
   * 获取处理状态选项
   */
  static getStatusOptions() {
    return [
      { label: '待处理', value: 'PENDING', color: 'orange' },
      { label: '处理中', value: 'PROCESSING', color: 'blue' },
      { label: '已解决', value: 'RESOLVED', color: 'green' },
      { label: '已忽略', value: 'IGNORED', color: 'gray' }
    ];
  }

  /**
   * 获取预警类型名称
   */
  static getAlertTypeName(type) {
    const options = InventoryAlertApi.getAlertTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取预警级别名称和颜色
   */
  static getAlertLevelInfo(level) {
    const options = InventoryAlertApi.getAlertLevelOptions();
    const option = options.find(item => item.value === level);
    return option ? { name: option.label, color: option.color } : { name: level, color: 'default' };
  }

  /**
   * 获取状态信息
   */
  static getStatusInfo(status) {
    const options = InventoryAlertApi.getStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? { name: option.label, color: option.color } : { name: status, color: 'default' };
  }
}
```

#### 3.1.2 创建模块级API接口
在 `guns-ent-front-8.3.3/src/views/erp/inventoryAlert/api/` 目录下创建：

**InventoryAlertRuleApi.js**
```javascript
import Request from '@/utils/request/request-util';

/**
 * 库存预警规则管理API
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
export class InventoryAlertRuleApi {

  /**
   * 新增预警规则
   */
  static add(params) {
    return Request.post('/erp/inventoryAlert/rule/add', params);
  }

  /**
   * 编辑预警规则
   */
  static edit(params) {
    return Request.post('/erp/inventoryAlert/rule/edit', params);
  }

  /**
   * 删除预警规则
   */
  static delete(params) {
    return Request.post('/erp/inventoryAlert/rule/delete', params);
  }

  /**
   * 批量删除预警规则
   */
  static batchDelete(params) {
    return Request.post('/erp/inventoryAlert/rule/batchDelete', params);
  }

  /**
   * 查询预警规则详情
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/rule/detail', params);
  }

  /**
   * 分页查询预警规则
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/rule/page', params);
  }

  /**
   * 更新预警规则状态
   */
  static updateStatus(params) {
    return Request.post('/erp/inventoryAlert/rule/updateStatus', params);
  }

  /**
   * 测试预警规则
   */
  static testRule(params) {
    return Request.post('/erp/inventoryAlert/rule/test', params);
  }

  /**
   * 手动执行预警检查
   */
  static executeCheck() {
    return Request.post('/erp/inventoryAlert/rule/executeCheck');
  }
}
```

**InventoryAlertRecordApi.js**
```javascript
import Request from '@/utils/request/request-util';

/**
 * 库存预警记录管理API
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
export class InventoryAlertRecordApi {

  /**
   * 分页查询预警记录
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/page', params);
  }

  /**
   * 查询预警记录详情
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/detail', params);
  }

  /**
   * 处理预警记录
   */
  static handle(params) {
    return Request.post('/erp/inventoryAlert/record/handle', params);
  }

  /**
   * 批量处理预警记录
   */
  static batchHandle(params) {
    return Request.post('/erp/inventoryAlert/record/batchHandle', params);
  }

  /**
   * 忽略预警记录
   */
  static ignore(params) {
    return Request.post('/erp/inventoryAlert/record/ignore', params);
  }

  /**
   * 获取预警统计数据
   */
  static getStatistics(params) {
    return Request.getAndLoadData('/erp/inventoryAlert/record/statistics', params);
  }

  /**
   * 导出预警记录
   */
  static exportRecords(params) {
    return Request.download('/erp/inventoryAlert/record/export', params);
  }
}
```

### 3.2 状态管理（Pinia Store）

#### 3.2.1 创建预警状态管理
在 `guns-ent-front-8.3.3/src/store/modules/erp/` 目录下创建：

**inventoryAlert.js**
```javascript
import { defineStore } from 'pinia';
import { InventoryAlertRuleApi } from '@/views/erp/inventoryAlert/api/InventoryAlertRuleApi';
import { InventoryAlertRecordApi } from '@/views/erp/inventoryAlert/api/InventoryAlertRecordApi';

export const useInventoryAlertStore = defineStore('inventoryAlert', {
  state: () => ({
    // 预警规则列表
    ruleList: [],
    ruleTotal: 0,
    ruleLoading: false,

    // 预警记录列表
    recordList: [],
    recordTotal: 0,
    recordLoading: false,

    // 预警统计数据
    statistics: {
      totalAlerts: 0,
      criticalAlerts: 0,
      warningAlerts: 0,
      infoAlerts: 0,
      pendingAlerts: 0,
      resolvedAlerts: 0
    },

    // 当前选中的规则或记录
    currentRule: null,
    currentRecord: null,

    // 缓存的选项数据
    productOptions: [],
    categoryOptions: [],
    userOptions: []
  }),

  getters: {
    // 获取启用的规则数量
    enabledRulesCount: (state) => {
      return state.ruleList.filter(rule => rule.isEnabled === 'Y').length;
    },

    // 获取待处理预警数量
    pendingAlertsCount: (state) => {
      return state.statistics.pendingAlerts;
    },

    // 获取紧急预警数量
    criticalAlertsCount: (state) => {
      return state.statistics.criticalAlerts;
    }
  },

  actions: {
    /**
     * 加载预警规则列表
     */
    async loadRuleList(params = {}) {
      this.ruleLoading = true;
      try {
        const response = await InventoryAlertRuleApi.findPage(params);
        this.ruleList = response.rows || [];
        this.ruleTotal = response.totalRows || 0;
      } catch (error) {
        console.error('加载预警规则列表失败:', error);
        this.ruleList = [];
        this.ruleTotal = 0;
      } finally {
        this.ruleLoading = false;
      }
    },

    /**
     * 加载预警记录列表
     */
    async loadRecordList(params = {}) {
      this.recordLoading = true;
      try {
        const response = await InventoryAlertRecordApi.findPage(params);
        this.recordList = response.rows || [];
        this.recordTotal = response.totalRows || 0;
      } catch (error) {
        console.error('加载预警记录列表失败:', error);
        this.recordList = [];
        this.recordTotal = 0;
      } finally {
        this.recordLoading = false;
      }
    },

    /**
     * 加载预警统计数据
     */
    async loadStatistics(params = {}) {
      try {
        const response = await InventoryAlertRecordApi.getStatistics(params);
        this.statistics = response || this.statistics;
      } catch (error) {
        console.error('加载预警统计数据失败:', error);
      }
    },

    /**
     * 设置当前规则
     */
    setCurrentRule(rule) {
      this.currentRule = rule;
    },

    /**
     * 设置当前记录
     */
    setCurrentRecord(record) {
      this.currentRecord = record;
    },

    /**
     * 清空数据
     */
    clearData() {
      this.ruleList = [];
      this.ruleTotal = 0;
      this.recordList = [];
      this.recordTotal = 0;
      this.currentRule = null;
      this.currentRecord = null;
    },

    /**
     * 刷新预警数据
     */
    async refreshData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadRuleList({ pageNo: 1, pageSize: 10 }),
        this.loadRecordList({ pageNo: 1, pageSize: 10, status: 'PENDING' })
      ]);
    }
  }
});
```

### 3.3 Vue组件实现

#### 3.3.1 预警规则管理主页面
在 `guns-ent-front-8.3.3/src/views/erp/inventoryAlert/rule/` 目录下创建：

**index.vue**
```vue
<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="content-main">
        <div class="content-main-body">
          <!-- 搜索区域 -->
          <div class="search-area">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-input
                  v-model:value="searchForm.searchText"
                  placeholder="规则名称"
                  allow-clear
                  @press-enter="handleSearch"
                >
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="searchForm.ruleTypeFilter"
                  placeholder="预警类型"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option
                    v-for="option in alertTypeOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="searchForm.alertLevelFilter"
                  placeholder="预警级别"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option
                    v-for="option in alertLevelOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    <a-tag :color="option.color">{{ option.label }}</a-tag>
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="searchForm.isEnabledFilter"
                  placeholder="启用状态"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option value="Y">启用</a-select-option>
                  <a-select-option value="N">停用</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="handleSearch">
                    <SearchOutlined />
                    搜索
                  </a-button>
                  <a-button @click="handleReset">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button type="primary" @click="handleAdd">
                    <PlusOutlined />
                    新增规则
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>

          <!-- 表格区域 -->
          <div class="table-area">
            <a-table
              :columns="columns"
              :data-source="ruleList"
              :loading="ruleLoading"
              :pagination="pagination"
              row-key="id"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'ruleType'">
                  {{ getAlertTypeName(record.ruleType) }}
                </template>
                <template v-else-if="column.key === 'alertLevel'">
                  <a-tag :color="getAlertLevelInfo(record.alertLevel).color">
                    {{ getAlertLevelInfo(record.alertLevel).name }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'targetType'">
                  {{ getTargetTypeName(record.targetType) }}
                </template>
                <template v-else-if="column.key === 'isEnabled'">
                  <a-switch
                    :checked="record.isEnabled === 'Y'"
                    @change="(checked) => handleStatusChange(record, checked)"
                  />
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleEdit(record)">
                      编辑
                    </a-button>
                    <a-button type="link" size="small" @click="handleTest(record)">
                      测试
                    </a-button>
                    <a-button type="link" size="small" danger @click="handleDelete(record)">
                      删除
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <alert-rule-form
      v-model:visible="formVisible"
      :data="currentRule"
      @ok="handleFormOk"
    />

    <!-- 测试结果弹窗 -->
    <alert-test-result
      v-model:visible="testVisible"
      :data="testResult"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { useInventoryAlertStore } from '@/store/modules/erp/inventoryAlert';
import { InventoryAlertRuleApi } from '../api/InventoryAlertRuleApi';
import { InventoryAlertApi } from '@/api/erp/inventoryAlert';
import AlertRuleForm from '../components/AlertRuleForm.vue';
import AlertTestResult from '../components/AlertTestResult.vue';

export default {
  name: 'InventoryAlertRuleIndex',
  components: {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    AlertRuleForm,
    AlertTestResult
  },
  setup() {
    const alertStore = useInventoryAlertStore();

    // 响应式数据
    const searchForm = reactive({
      searchText: '',
      ruleTypeFilter: undefined,
      alertLevelFilter: undefined,
      isEnabledFilter: undefined
    });

    const formVisible = ref(false);
    const testVisible = ref(false);
    const currentRule = ref(null);
    const testResult = ref([]);

    // 选项数据
    const alertTypeOptions = InventoryAlertApi.getAlertTypeOptions();
    const alertLevelOptions = InventoryAlertApi.getAlertLevelOptions();
    const targetTypeOptions = InventoryAlertApi.getTargetTypeOptions();

    // 表格列定义
    const columns = [
      {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        width: 200
      },
      {
        title: '预警类型',
        dataIndex: 'ruleType',
        key: 'ruleType',
        width: 120
      },
      {
        title: '预警级别',
        dataIndex: 'alertLevel',
        key: 'alertLevel',
        width: 100
      },
      {
        title: '目标类型',
        dataIndex: 'targetType',
        key: 'targetType',
        width: 120
      },
      {
        title: '阈值',
        dataIndex: 'thresholdValue',
        key: 'thresholdValue',
        width: 100
      },
      {
        title: '检查频率(分钟)',
        dataIndex: 'checkFrequency',
        key: 'checkFrequency',
        width: 120
      },
      {
        title: '启用状态',
        dataIndex: 'isEnabled',
        key: 'isEnabled',
        width: 100
      },
      {
        title: '最后检查时间',
        dataIndex: 'lastCheckTime',
        key: 'lastCheckTime',
        width: 160
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ];

    // 计算属性
    const ruleList = computed(() => alertStore.ruleList);
    const ruleLoading = computed(() => alertStore.ruleLoading);
    const ruleTotal = computed(() => alertStore.ruleTotal);

    const pagination = computed(() => ({
      current: 1,
      pageSize: 20,
      total: ruleTotal.value,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    }));

    // 方法
    const loadData = (params = {}) => {
      const queryParams = {
        pageNo: 1,
        pageSize: 20,
        ...searchForm,
        ...params
      };
      alertStore.loadRuleList(queryParams);
    };

    const handleSearch = () => {
      loadData();
    };

    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = undefined;
      });
      loadData();
    };

    const handleAdd = () => {
      currentRule.value = null;
      formVisible.value = true;
    };

    const handleEdit = (record) => {
      currentRule.value = { ...record };
      formVisible.value = true;
    };

    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除预警规则"${record.ruleName}"吗？`,
        icon: ExclamationCircleOutlined,
        onOk: async () => {
          try {
            await InventoryAlertRuleApi.delete({ id: record.id });
            message.success('删除成功');
            loadData();
          } catch (error) {
            message.error('删除失败');
          }
        }
      });
    };

    const handleStatusChange = async (record, checked) => {
      try {
        await InventoryAlertRuleApi.updateStatus({
          id: record.id,
          isEnabled: checked ? 'Y' : 'N'
        });
        message.success('状态更新成功');
        loadData();
      } catch (error) {
        message.error('状态更新失败');
      }
    };

    const handleTest = async (record) => {
      try {
        const result = await InventoryAlertRuleApi.testRule({ id: record.id });
        testResult.value = result;
        testVisible.value = true;
      } catch (error) {
        message.error('测试失败');
      }
    };

    const handleTableChange = (pagination) => {
      loadData({
        pageNo: pagination.current,
        pageSize: pagination.pageSize
      });
    };

    const handleFormOk = () => {
      formVisible.value = false;
      loadData();
    };

    // 工具方法
    const getAlertTypeName = (type) => {
      return InventoryAlertApi.getAlertTypeName(type);
    };

    const getAlertLevelInfo = (level) => {
      return InventoryAlertApi.getAlertLevelInfo(level);
    };

    const getTargetTypeName = (type) => {
      const option = targetTypeOptions.find(item => item.value === type);
      return option ? option.label : type;
    };

    // 生命周期
    onMounted(() => {
      loadData();
    });

    return {
      // 响应式数据
      searchForm,
      formVisible,
      testVisible,
      currentRule,
      testResult,

      // 选项数据
      alertTypeOptions,
      alertLevelOptions,

      // 表格相关
      columns,
      ruleList,
      ruleLoading,
      pagination,

      // 方法
      handleSearch,
      handleReset,
      handleAdd,
      handleEdit,
      handleDelete,
      handleStatusChange,
      handleTest,
      handleTableChange,
      handleFormOk,
      getAlertTypeName,
      getAlertLevelInfo,
      getTargetTypeName
    };
  }
};
</script>

<style scoped>
.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
}

.table-area {
  background: #fff;
  border-radius: 6px;
}
</style>
```

#### 3.3.2 预警规则表单组件
在 `guns-ent-front-8.3.3/src/views/erp/inventoryAlert/components/` 目录下创建：

**AlertRuleForm.vue**
```vue
<template>
  <a-modal
    v-model:visible="visible"
    :title="isEdit ? '编辑预警规则' : '新增预警规则'"
    width="800px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="规则名称" name="ruleName">
            <a-input v-model:value="formData.ruleName" placeholder="请输入规则名称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预警类型" name="ruleType">
            <a-select v-model:value="formData.ruleType" placeholder="请选择预警类型">
              <a-select-option
                v-for="option in alertTypeOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="预警级别" name="alertLevel">
            <a-select v-model:value="formData.alertLevel" placeholder="请选择预警级别">
              <a-select-option
                v-for="option in alertLevelOptions"
                :key="option.value"
                :value="option.value"
              >
                <a-tag :color="option.color">{{ option.label }}</a-tag>
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="目标类型" name="targetType">
            <a-select
              v-model:value="formData.targetType"
              placeholder="请选择目标类型"
              @change="handleTargetTypeChange"
            >
              <a-select-option
                v-for="option in targetTypeOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16" v-if="formData.targetType && formData.targetType !== 'ALL'">
        <a-col :span="12">
          <a-form-item label="目标对象" name="targetId">
            <a-select
              v-model:value="formData.targetId"
              placeholder="请选择目标对象"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option
                v-for="option in targetOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="阈值类型" name="thresholdType">
            <a-select v-model:value="formData.thresholdType" placeholder="请选择阈值类型">
              <a-select-option
                v-for="option in thresholdTypeOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="比较操作符" name="comparisonOperator">
            <a-select v-model:value="formData.comparisonOperator" placeholder="请选择操作符">
              <a-select-option
                v-for="option in comparisonOperatorOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="阈值" name="thresholdValue">
            <a-input-number
              v-model:value="formData.thresholdValue"
              placeholder="请输入阈值"
              :min="0"
              :precision="3"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="检查频率(分钟)" name="checkFrequency">
            <a-input-number
              v-model:value="formData.checkFrequency"
              placeholder="请输入检查频率"
              :min="1"
              :max="1440"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="通知方式" name="notificationMethods">
            <a-select
              v-model:value="formData.notificationMethods"
              mode="multiple"
              placeholder="请选择通知方式"
            >
              <a-select-option
                v-for="option in notificationMethodOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="通知用户" name="notificationUsers">
        <a-select
          v-model:value="formData.notificationUsers"
          mode="multiple"
          placeholder="请选择通知用户"
          show-search
          :filter-option="filterOption"
        >
          <a-select-option
            v-for="user in userOptions"
            :key="user.value"
            :value="user.value"
          >
            {{ user.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注"
          :rows="3"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryAlertRuleApi } from '../api/InventoryAlertRuleApi';
import { InventoryAlertApi } from '@/api/erp/inventoryAlert';

export default {
  name: 'AlertRuleForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref();

    // 表单数据
    const formData = reactive({
      id: null,
      ruleName: '',
      ruleType: undefined,
      targetType: undefined,
      targetId: undefined,
      alertLevel: undefined,
      thresholdType: undefined,
      thresholdValue: null,
      comparisonOperator: undefined,
      checkFrequency: 30,
      notificationMethods: ['SYSTEM'],
      notificationUsers: [],
      remark: ''
    });

    // 选项数据
    const alertTypeOptions = InventoryAlertApi.getAlertTypeOptions();
    const alertLevelOptions = InventoryAlertApi.getAlertLevelOptions();
    const targetTypeOptions = InventoryAlertApi.getTargetTypeOptions();
    const thresholdTypeOptions = InventoryAlertApi.getThresholdTypeOptions();
    const comparisonOperatorOptions = InventoryAlertApi.getComparisonOperatorOptions();
    const notificationMethodOptions = InventoryAlertApi.getNotificationMethodOptions();

    const targetOptions = ref([]);
    const userOptions = ref([]);

    // 表单验证规则
    const rules = {
      ruleName: [
        { required: true, message: '请输入规则名称', trigger: 'blur' },
        { max: 100, message: '规则名称不能超过100个字符', trigger: 'blur' }
      ],
      ruleType: [
        { required: true, message: '请选择预警类型', trigger: 'change' }
      ],
      targetType: [
        { required: true, message: '请选择目标类型', trigger: 'change' }
      ],
      targetId: [
        {
          required: computed(() => formData.targetType && formData.targetType !== 'ALL'),
          message: '请选择目标对象',
          trigger: 'change'
        }
      ],
      alertLevel: [
        { required: true, message: '请选择预警级别', trigger: 'change' }
      ],
      thresholdType: [
        { required: true, message: '请选择阈值类型', trigger: 'change' }
      ],
      thresholdValue: [
        { required: true, message: '请输入阈值', trigger: 'blur' },
        { type: 'number', min: 0, message: '阈值不能小于0', trigger: 'blur' }
      ],
      comparisonOperator: [
        { required: true, message: '请选择比较操作符', trigger: 'change' }
      ],
      checkFrequency: [
        { required: true, message: '请输入检查频率', trigger: 'blur' },
        { type: 'number', min: 1, max: 1440, message: '检查频率必须在1-1440分钟之间', trigger: 'blur' }
      ]
    };

    // 计算属性
    const isEdit = computed(() => !!formData.id);

    // 监听器
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        initForm();
      }
    });

    watch(() => props.data, (newVal) => {
      if (newVal) {
        Object.assign(formData, newVal);
        if (formData.notificationMethods && typeof formData.notificationMethods === 'string') {
          formData.notificationMethods = formData.notificationMethods.split(',');
        }
        if (formData.notificationUsers && typeof formData.notificationUsers === 'string') {
          formData.notificationUsers = formData.notificationUsers.split(',');
        }
      }
    });

    // 方法
    const initForm = () => {
      if (!props.data) {
        resetForm();
      }
      loadUserOptions();
    };

    const resetForm = () => {
      Object.assign(formData, {
        id: null,
        ruleName: '',
        ruleType: undefined,
        targetType: undefined,
        targetId: undefined,
        alertLevel: undefined,
        thresholdType: undefined,
        thresholdValue: null,
        comparisonOperator: undefined,
        checkFrequency: 30,
        notificationMethods: ['SYSTEM'],
        notificationUsers: [],
        remark: ''
      });
      formRef.value?.resetFields();
    };

    const handleTargetTypeChange = (value) => {
      formData.targetId = undefined;
      loadTargetOptions(value);
    };

    const loadTargetOptions = async (targetType) => {
      if (targetType === 'PRODUCT') {
        // 加载商品选项
        // const response = await ProductApi.findList();
        // targetOptions.value = response.map(item => ({
        //   value: item.productId,
        //   label: `${item.productCode} - ${item.productName}`
        // }));
      } else if (targetType === 'CATEGORY') {
        // 加载分类选项
        // const response = await ProductCategoryApi.findList();
        // targetOptions.value = response.map(item => ({
        //   value: item.categoryId,
        //   label: item.categoryName
        // }));
      }
    };

    const loadUserOptions = async () => {
      // 加载用户选项
      // const response = await UserApi.findList();
      // userOptions.value = response.map(item => ({
      //   value: item.userId,
      //   label: item.realName
      // }));
    };

    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    const handleOk = async () => {
      try {
        await formRef.value.validate();

        const submitData = { ...formData };
        if (Array.isArray(submitData.notificationMethods)) {
          submitData.notificationMethods = submitData.notificationMethods.join(',');
        }
        if (Array.isArray(submitData.notificationUsers)) {
          submitData.notificationUsers = submitData.notificationUsers.join(',');
        }

        if (isEdit.value) {
          await InventoryAlertRuleApi.edit(submitData);
          message.success('编辑成功');
        } else {
          await InventoryAlertRuleApi.add(submitData);
          message.success('新增成功');
        }

        emit('ok');
      } catch (error) {
        if (error.errorFields) {
          message.error('请检查表单输入');
        } else {
          message.error(isEdit.value ? '编辑失败' : '新增失败');
        }
      }
    };

    const handleCancel = () => {
      emit('update:visible', false);
    };

    return {
      formRef,
      formData,
      rules,
      isEdit,
      alertTypeOptions,
      alertLevelOptions,
      targetTypeOptions,
      thresholdTypeOptions,
      comparisonOperatorOptions,
      notificationMethodOptions,
      targetOptions,
      userOptions,
      handleTargetTypeChange,
      filterOption,
      handleOk,
      handleCancel
    };
  }
};
</script>
```

### 3.4 路由配置

#### 3.4.1 添加ERP路由配置
在 `guns-ent-front-8.3.3/src/router/` 相关文件中添加库存预警路由：

```javascript
// 在ERP模块路由中添加
{
  path: '/erp/inventoryAlert',
  name: 'InventoryAlert',
  component: () => import('@/layouts/base-layout/index.vue'),
  meta: {
    title: '库存预警',
    icon: 'AlertOutlined'
  },
  children: [
    {
      path: 'rule',
      name: 'InventoryAlertRule',
      component: () => import('@/views/erp/inventoryAlert/rule/index.vue'),
      meta: {
        title: '预警规则',
        keepAlive: true
      }
    },
    {
      path: 'record',
      name: 'InventoryAlertRecord',
      component: () => import('@/views/erp/inventoryAlert/record/index.vue'),
      meta: {
        title: '预警记录',
        keepAlive: true
      }
    },
    {
      path: 'dashboard',
      name: 'InventoryAlertDashboard',
      component: () => import('@/views/erp/inventoryAlert/dashboard/index.vue'),
      meta: {
        title: '监控看板',
        keepAlive: true
      }
    },
    {
      path: 'config',
      name: 'InventoryAlertConfig',
      component: () => import('@/views/erp/inventoryAlert/config/index.vue'),
      meta: {
        title: '系统配置',
        keepAlive: true
      }
    }
  ]
}
```

## 四、技术实现细节

### 4.1 定时任务实现

#### 4.1.1 Spring Boot @Scheduled配置
```java
@Configuration
@EnableScheduling
public class ScheduleConfig {

    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10);
        scheduler.setThreadNamePrefix("inventory-alert-");
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        return scheduler;
    }
}
```

#### 4.1.2 分布式任务调度
```java
@Component
@Slf4j
public class DistributedInventoryAlertTask {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟执行一次
    public void executeAlertCheck() {
        String lockKey = "inventory:alert:lock";
        String lockValue = UUID.randomUUID().toString();

        try {
            // 分布式锁，确保只有一个实例执行
            Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, Duration.ofMinutes(35));

            if (Boolean.TRUE.equals(acquired)) {
                log.info("获取分布式锁成功，开始执行库存预警检查");
                alertCheckService.executeAlertCheck();
            } else {
                log.debug("未获取到分布式锁，跳过本次预警检查");
            }
        } catch (Exception e) {
            log.error("执行库存预警检查异常", e);
        } finally {
            // 释放锁
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                           "return redis.call('del', KEYS[1]) else return 0 end";
            redisTemplate.execute(new DefaultRedisScript<>(script, Long.class),
                Collections.singletonList(lockKey), lockValue);
        }
    }
}
```

### 4.2 异步处理和消息通知

#### 4.2.1 异步配置
```java
@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean("alertTaskExecutor")
    public TaskExecutor alertTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("alert-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

#### 4.2.2 消息通知服务
```java
@Service
@Slf4j
public class AlertNotificationService {

    @Resource
    private JavaMailSender mailSender;

    @Resource
    private SmsService smsService;

    @Async("alertTaskExecutor")
    public void sendNotification(InventoryAlertRule rule, InventoryAlertRecordRequest record) {
        String[] methods = rule.getNotificationMethods().split(",");
        String[] users = rule.getNotificationUsers().split(",");

        for (String method : methods) {
            switch (method.trim()) {
                case "SYSTEM":
                    sendSystemNotification(users, record);
                    break;
                case "EMAIL":
                    sendEmailNotification(users, record);
                    break;
                case "SMS":
                    sendSmsNotification(users, record);
                    break;
                case "WECHAT":
                    sendWechatNotification(users, record);
                    break;
            }
        }
    }

    private void sendEmailNotification(String[] users, InventoryAlertRecordRequest record) {
        try {
            // 实现邮件发送逻辑
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            // 设置邮件内容
            helper.setSubject("库存预警通知");
            helper.setText(buildEmailContent(record), true);

            // 发送给指定用户
            for (String userId : users) {
                String email = getUserEmail(userId);
                if (StringUtils.hasText(email)) {
                    helper.setTo(email);
                    mailSender.send(message);
                }
            }
        } catch (Exception e) {
            log.error("发送邮件通知失败", e);
        }
    }
}
```

### 4.3 数据库查询优化

#### 4.3.1 索引优化建议
```sql
-- 库存预警规则表索引
CREATE INDEX idx_rule_enabled_check ON erp_inventory_alert_rule(is_enabled, check_frequency, last_check_time);
CREATE INDEX idx_rule_target ON erp_inventory_alert_rule(target_type, target_id);

-- 库存预警记录表索引
CREATE INDEX idx_record_status_time ON erp_inventory_alert_record(status, alert_time);
CREATE INDEX idx_record_product_status ON erp_inventory_alert_record(product_id, status);
CREATE INDEX idx_record_level_time ON erp_inventory_alert_record(alert_level, alert_time);

-- 库存表查询优化索引
CREATE INDEX idx_inventory_alert_check ON erp_inventory(current_stock, min_stock, max_stock);
CREATE INDEX idx_inventory_product_stock ON erp_inventory(product_id, current_stock);
```

#### 4.3.2 缓存策略
```java
@Service
@Slf4j
public class InventoryAlertCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String RULE_CACHE_KEY = "inventory:alert:rules";
    private static final String STATISTICS_CACHE_KEY = "inventory:alert:statistics";
    private static final long CACHE_EXPIRE_SECONDS = 300; // 5分钟

    @Cacheable(value = "alertRules", key = "'enabled'")
    public List<InventoryAlertRule> getEnabledRules() {
        // 从数据库查询启用的规则
        return ruleMapper.selectEnabledRules();
    }

    @CacheEvict(value = "alertRules", allEntries = true)
    public void clearRuleCache() {
        log.info("清除预警规则缓存");
    }

    public void cacheStatistics(String tenantId, Object statistics) {
        String key = STATISTICS_CACHE_KEY + ":" + tenantId;
        redisTemplate.opsForValue().set(key, statistics, Duration.ofSeconds(CACHE_EXPIRE_SECONDS));
    }

    public Object getStatistics(String tenantId) {
        String key = STATISTICS_CACHE_KEY + ":" + tenantId;
        return redisTemplate.opsForValue().get(key);
    }
}
```

## 五、开发顺序建议

### 5.1 第一阶段：基础架构搭建（预估3-4天）

#### 5.1.1 数据库层（1天）
1. **执行数据库脚本**
   - 执行已设计的库存预警相关表结构SQL
   - 验证表结构和索引是否正确创建
   - 插入基础配置数据

2. **验证数据库设计**
   - 测试表关联关系
   - 验证索引性能
   - 确认数据完整性约束

#### 5.1.2 后端基础结构（2-3天）
1. **创建实体类和请求响应对象**（0.5天）
   - InventoryAlertRule.java
   - InventoryAlertRecord.java
   - InventoryAlertConfig.java
   - 对应的Request和Response类

2. **创建Mapper接口和XML**（0.5天）
   - InventoryAlertRuleMapper
   - InventoryAlertRecordMapper
   - InventoryAlertConfigMapper
   - 编写基础CRUD和查询方法

3. **创建Service接口和实现**（1天）
   - InventoryAlertRuleService及实现
   - InventoryAlertRecordService及实现
   - InventoryAlertConfigService及实现
   - InventoryAlertCheckService（核心预警逻辑）

4. **创建Controller**（0.5天）
   - InventoryAlertRuleController
   - InventoryAlertRecordController
   - InventoryAlertConfigController

5. **配置和异常处理**（0.5天）
   - 添加权限常量
   - 创建异常枚举
   - 配置自动扫描

### 5.2 第二阶段：核心功能实现（预估4-5天）

#### 5.2.1 预警规则管理（1.5天）
1. **完善规则CRUD功能**
   - 实现规则的增删改查
   - 添加规则状态管理
   - 实现规则测试功能

2. **规则验证逻辑**
   - 添加规则参数验证
   - 实现规则冲突检查
   - 添加规则生效时间控制

#### 5.2.2 预警检查核心逻辑（2天）
1. **实现预警检查算法**
   - 低库存预警检查
   - 零库存预警检查
   - 库存积压预警检查
   - 临期预警检查

2. **优化查询性能**
   - 实现批量查询
   - 添加查询缓存
   - 优化SQL语句

#### 5.2.3 预警记录管理（1天）
1. **记录生成和管理**
   - 实现预警记录创建
   - 添加记录状态管理
   - 实现记录处理流程

2. **统计和分析功能**
   - 实现预警统计
   - 添加趋势分析
   - 生成预警报表

#### 5.2.4 定时任务和通知（0.5天）
1. **定时任务实现**
   - 配置定时任务
   - 实现分布式锁
   - 添加任务监控

2. **通知功能实现**
   - 系统内通知
   - 邮件通知（可选）
   - 短信通知（可选）

### 5.3 第三阶段：前端界面开发（预估4-5天）

#### 5.3.1 API接口层（0.5天）
1. **创建API接口文件**
   - 全局ERP API接口
   - 模块级API接口
   - 工具函数和常量

#### 5.3.2 状态管理（0.5天）
1. **创建Pinia Store**
   - inventoryAlert store
   - 状态管理逻辑
   - 数据缓存策略

#### 5.3.3 核心页面开发（2.5天）
1. **预警规则管理页面**（1天）
   - 规则列表页面
   - 规则表单组件
   - 规则测试功能

2. **预警记录管理页面**（1天）
   - 记录列表页面
   - 记录详情组件
   - 记录处理功能

3. **监控看板页面**（0.5天）
   - 统计图表展示
   - 实时数据更新
   - 快速操作入口

#### 5.3.4 辅助功能（1天）
1. **系统配置页面**
   - 配置参数管理
   - 通知设置
   - 系统参数调整

2. **路由和权限配置**
   - 添加路由配置
   - 配置菜单权限
   - 测试页面访问

### 5.4 第四阶段：集成测试和优化（预估2-3天）

#### 5.4.1 功能测试（1天）
1. **单元测试**
   - 后端Service层测试
   - 前端组件测试
   - API接口测试

2. **集成测试**
   - 前后端联调测试
   - 预警流程测试
   - 通知功能测试

#### 5.4.2 性能优化（1天）
1. **后端优化**
   - SQL查询优化
   - 缓存策略调整
   - 异步处理优化

2. **前端优化**
   - 组件性能优化
   - 数据加载优化
   - 用户体验优化

#### 5.4.3 部署和文档（1天）
1. **部署配置**
   - 生产环境配置
   - 数据库迁移
   - 权限配置

2. **文档编写**
   - 用户操作手册
   - 技术文档
   - 部署说明

### 5.5 关键里程碑

| 里程碑 | 时间节点 | 验收标准 |
|--------|----------|----------|
| 数据库设计完成 | 第1天 | 所有表结构创建成功，基础数据插入完成 |
| 后端基础架构完成 | 第4天 | 所有API接口可正常调用，基础CRUD功能正常 |
| 核心预警逻辑完成 | 第8天 | 预警检查功能正常，能够正确识别各类预警情况 |
| 前端界面开发完成 | 第12天 | 所有页面功能正常，前后端数据交互正常 |
| 集成测试完成 | 第15天 | 完整预警流程测试通过，性能满足要求 |

### 5.6 测试验证方案

#### 5.6.1 功能测试用例
1. **预警规则测试**
   - 创建各种类型的预警规则
   - 测试规则参数验证
   - 验证规则启用/停用功能

2. **预警检查测试**
   - 模拟各种库存情况
   - 验证预警触发条件
   - 测试预警记录生成

3. **通知功能测试**
   - 测试系统内通知
   - 验证邮件通知（如果启用）
   - 测试通知用户配置

#### 5.6.2 性能测试
1. **并发测试**
   - 模拟多用户同时操作
   - 测试定时任务性能
   - 验证数据库查询性能

2. **压力测试**
   - 大量数据情况下的性能
   - 长时间运行稳定性
   - 内存和CPU使用情况

#### 5.6.3 兼容性测试
1. **浏览器兼容性**
   - Chrome、Firefox、Safari、Edge
   - 移动端浏览器适配

2. **数据库兼容性**
   - MySQL不同版本兼容性
   - 数据迁移测试

## 六、总结

本设计文档详细描述了JavaGuns Enterprise库存预警功能的完整实现方案，包括：

1. **完整的技术架构**：基于项目现有的分层架构，确保与现有系统的无缝集成
2. **详细的实现步骤**：从数据库设计到前后端实现，提供了完整的代码示例
3. **实用的技术方案**：包含定时任务、异步处理、缓存优化等关键技术实现
4. **清晰的开发计划**：按照依赖关系和优先级制定了详细的开发顺序
5. **完善的测试方案**：确保功能质量和系统稳定性

该方案严格遵循JavaGuns Enterprise项目的开发规范，采用成熟的技术栈，具有良好的可扩展性和维护性。开发团队可以按照本文档的指导，高效地完成库存预警功能的开发和部署。

**预估总工作量**：13-17个工作日
**建议团队配置**：1名后端开发工程师 + 1名前端开发工程师
**技术难点**：预警算法优化、大数据量查询性能、实时通知机制
```
```
```
