package cn.stylefeng.roses.seata.demo.storage.modular.controller;

import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.seata.demo.storage.modular.service.StorageTblService;
import cn.stylefeng.roses.seata.demo.storage.modular.service.TccSubStorageService;
import io.seata.core.context.RootContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 库存控制器
 *
 * <AUTHOR>
 * @date 2021/8/29 11:01
 */
@RestController
@Slf4j
public class StorageController {

    @Resource
    private StorageTblService storageTblService;

    @Resource
    private TccSubStorageService tccSubStorageService;

    /**
     * 扣减库存（AT模式）
     *
     * @param commodityCode 商品编码
     * @param count         扣减的数量
     * <AUTHOR>
     * @date 2021/8/29 11:01
     */
    @GetMapping(value = "/storage/{commodityCode}/{count}")
    public SuccessResponseData subStorage(@PathVariable String commodityCode, @PathVariable int count) {

        // 打印分布式事务id
        log.info("Storage Service Begin ... xid: " + RootContext.getXID());
        storageTblService.subStorage(commodityCode, count);

        return new SuccessResponseData();
    }

    /**
     * 扣减库存（TCC方式）
     *
     * @param commodityCode 商品编码
     * @param count         扣减的数量
     * <AUTHOR>
     * @date 2021/8/29 11:01
     */
    @GetMapping(value = "/tcc/storage/{commodityCode}/{count}")
    public SuccessResponseData tccSubStorage(@PathVariable String commodityCode, @PathVariable int count) {
        tccSubStorageService.subStorageTry(commodityCode, count);
        return new SuccessResponseData();
    }

}
