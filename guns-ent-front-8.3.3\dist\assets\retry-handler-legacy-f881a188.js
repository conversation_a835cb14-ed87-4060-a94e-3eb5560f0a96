System.register(["./performance-monitor-legacy-4ff0ac5f.js","./index-legacy-ee1db0c7.js","./constants-legacy-2a31d63c.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.i},null,null],execute:function(){t("RetryHandler",class{static async withRetry(t,e={}){const{maxRetries:r=3,retryDelay:i=1e3,maxDelay:a=3e4,backoffFactor:s=2,retryCondition:n=(t=>this.shouldRetry(t)),onRetry:o}=e;let c,y=i;for(let l=0;l<=r;l++)try{return await t()}catch(u){if(c=u,l===r||!n(u))throw u;"function"==typeof o&&o(u,l+1,r),await this.delay(y),y=Math.min(y*s,a)}throw c}static shouldRetry(t){if(t&&"boolean"==typeof t.retryable)return t.retryable;if(t&&t.type)return r(t.type);if(t&&t.response&&t.response.status){const e=t.response.status;if(e>=500)return!0;if(408===e)return!0;if(429===e)return!0;if(e>=400&&e<500)return!1}if(t&&t.message){const e=t.message.toLowerCase();if(e.includes("network")||e.includes("timeout")||e.includes("connection")||e.includes("网络")||e.includes("超时")||e.includes("连接"))return!0;if(e.includes("service unavailable")||e.includes("服务不可用"))return!0}return!1}static delay(t){return new Promise((e=>setTimeout(e,t)))}static delayWithJitter(t,e=.1){const r=t+t*e*Math.random();return this.delay(r)}static async withLinearBackoff(t,e={}){const{maxRetries:r=3,retryDelay:i=1e3,retryCondition:a=(t=>this.shouldRetry(t)),onRetry:s}=e;let n;for(let c=0;c<=r;c++)try{return await t()}catch(o){if(n=o,c===r||!a(o))throw o;"function"==typeof s&&s(o,c+1,r);const t=i*(c+1);await this.delay(t)}throw n}static async withFixedInterval(t,e={}){const{maxRetries:r=3,retryDelay:i=1e3,retryCondition:a=(t=>this.shouldRetry(t)),onRetry:s}=e;let n;for(let c=0;c<=r;c++)try{return await t()}catch(o){if(n=o,c===r||!a(o))throw o;"function"==typeof s&&s(o,c+1,r),await this.delay(i)}throw n}static async withCircuitBreaker(t,e={}){const{maxRetries:r=3,retryDelay:i=1e3,failureThreshold:a=5,recoveryTimeout:s=6e4,retryCondition:n=(t=>this.shouldRetry(t)),onRetry:o}=e,c=t.name||"anonymous",y=this.getCircuitState(c);if("OPEN"===y.state){if(Date.now()-y.lastFailureTime<s)throw new Error("熔断器开启，服务暂时不可用");y.state="HALF_OPEN"}try{const e=await this.withRetry(t,{maxRetries:r,retryDelay:i,retryCondition:n,onRetry:o});return this.resetCircuit(c),e}catch(u){throw this.recordFailure(c,a),u}}static getCircuitState(t){return this.circuitStates||(this.circuitStates=new Map),this.circuitStates.has(t)||this.circuitStates.set(t,{state:"CLOSED",failureCount:0,lastFailureTime:0}),this.circuitStates.get(t)}static recordFailure(t,e){const r=this.getCircuitState(t);r.failureCount++,r.lastFailureTime=Date.now(),r.failureCount>=e&&(r.state="OPEN")}static resetCircuit(t){const e=this.getCircuitState(t);e.state="CLOSED",e.failureCount=0,e.lastFailureTime=0}static async batchRetry(t,e={}){const{maxRetries:r=3,retryDelay:i=1e3,retryCondition:a=(t=>this.shouldRetry(t)),onRetry:s,failFast:n=!1}=e,o=t.map((async(t,e)=>{try{return await this.withRetry(t,{maxRetries:r,retryDelay:i,retryCondition:a,onRetry:s?(t,r,i)=>s(t,r,i,e):void 0})}catch(o){if(n)throw o;return{error:o,index:e}}}));return n?await Promise.all(o):await Promise.allSettled(o)}static async retryIf(t,e,r={}){return this.withRetry(t,{...r,retryCondition:e})}static async retryUntilTimeout(t,e={}){var r;const{timeout:i=3e4,retryDelay:a=1e3,retryCondition:s=(t=>this.shouldRetry(t)),onRetry:n}=e,o=Date.now();let c,y=0;for(;Date.now()-o<i;)try{return await t()}catch(u){if(c=u,!s(u))throw u;"function"==typeof n&&n(u,++y,1/0),await this.delay(a)}throw new Error(`操作超时（${i}ms），最后一次错误: ${(null===(r=c)||void 0===r?void 0:r.message)||"未知错误"}`)}})}}}));
