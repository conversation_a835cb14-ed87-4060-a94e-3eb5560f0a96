import{r as n,o as B,cb as k,a as w,f as F,w as M,d as S,m as R,M as C}from"./index-18a1ea24.js";import O from"./app-form-ae67ea2d.js";import{A as r}from"./AppApi-4e70edf8.js";import{a as U}from"./FileApi-418f4d35.js";/* empty css              *//* empty css              */const D={__name:"app-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(L,{emit:h}){const p=L,f=h,i=n(!1),l=n(!1),e=n({iconList:[],statusFlag:1}),c=n(null);B(async()=>{p.data?(l.value=!0,y()):(e.value.appSort=await k("SYSTEM_BASE_APP"),l.value=!1)});const y=()=>{r.detail({appId:p.data.appId}).then(t=>{e.value=Object.assign({},t),e.value.iconList=[],A("iconList",t.appIcon)})},A=(t,a)=>{U.getAntdVInfo({fileId:a}).then(s=>{s.uid=a,e.value[t]=[s]})},u=t=>{f("update:visible",t)},I=async()=>{c.value.$refs.formRef.validate().then(async t=>{var a,s,o,v,m,b,g,_;if(t){(a=e.value.iconList)!=null&&a.length&&(e.value.appIcon=(v=(o=(s=e.value.iconList[0])==null?void 0:s.response)==null?void 0:o.data)!=null&&v.fileId?(g=(b=(m=e.value.iconList[0])==null?void 0:m.response)==null?void 0:b.data)==null?void 0:g.fileId:(_=e.value.iconList[0])==null?void 0:_.uid),i.value=!0;let d=null;l.value?d=r.edit(e.value):d=r.add(e.value),d.then(async x=>{i.value=!1,R.success(x.message),u(!1),f("done")}).catch(()=>{i.value=!1})}})};return(t,a)=>{const s=C;return w(),F(s,{width:700,maskClosable:!1,visible:p.visible,"confirm-loading":i.value,forceRender:!0,title:l.value?"\u7F16\u8F91\u5E94\u7528":"\u65B0\u5EFA\u5E94\u7528","body-style":{paddingBottom:"8px",height:"550px",overflowY:"auto"},"onUpdate:visible":u,onOk:I,onClose:a[1]||(a[1]=o=>u(!1))},{default:M(()=>[S(O,{form:e.value,"onUpdate:form":a[0]||(a[0]=o=>e.value=o),ref_key:"appFormRef",ref:c},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}};export{D as default};
