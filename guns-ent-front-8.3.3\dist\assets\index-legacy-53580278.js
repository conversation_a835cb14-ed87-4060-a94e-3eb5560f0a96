System.register(["./index-legacy-ee1db0c7.js"],(function(s,e){"use strict";var l,i,t,n,p,a,o,r,u,c,g,v,d,h,b;return{setters:[s=>{l=s._,i=s.aP,t=s.aQ,n=s.r,p=s.o,a=s.X,o=s.k,r=s.a,u=s.c,c=s.b,g=s.ah,v=s.aF,d=s.h,h=s.f,b=s.a2}],execute:function(){var e=document.createElement("style");e.textContent='.guns-split-panel{width:100%;display:flex;flex-direction:row;flex:auto;height:100%;min-height:0px;position:relative;--guns-split-size: 252px;--guns-split-space: 16px}.guns-split-panel>.guns-split-panel-wrap{flex-shrink:0;box-sizing:border-box;width:calc(var(--guns-split-size) + var(--guns-split-space));display:flex;justify-content:flex-end;transition:all .3s cubic-bezier(.4,0,.2,1);opacity:1}.guns-split-panel>.guns-split-panel-wrap>.guns-split-panel-side{flex-shrink:0;display:flex;width:100%;box-sizing:border-box;position:relative}.guns-split-panel>.guns-split-panel-wrap>.guns-split-panel-space{flex-shrink:0;width:var(--guns-split-space);box-sizing:border-box;position:relative}.guns-split-panel>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line{width:12px;height:100%;position:absolute;left:-6px;z-index:4;cursor:e-resize}.guns-split-panel>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line:after{content:"";width:3px;height:100%;display:block;margin:0 auto}.guns-split-panel>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line:hover:after{background:var(--primary-color)}.guns-split-panel>.guns-split-panel-body{flex:1;display:flex;overflow:auto;box-sizing:border-box;position:relative}.guns-split-panel>.guns-split-collapse-button{width:12px;height:40px;line-height:40px;text-align:center;position:absolute;left:var(--guns-split-size);top:50%;margin-top:-24px;border-radius:0 4px 4px 0;background:var(--border-color-split);transition:all .3s cubic-bezier(.4,0,.2,1);cursor:pointer;z-index:3}.guns-split-panel>.guns-split-collapse-button .guns-split-collapse-icon{font-size:12px;color:var(--text-color-secondary);transition:all .3s cubic-bezier(.4,0,.2,1);transform:scaleX(1)}.guns-split-panel>.guns-split-collapse-button:hover{background:var(--border-color-base)}.guns-split-panel.is-collapse>.guns-split-panel-wrap{width:0!important;pointer-events:none;opacity:0}.guns-split-panel.is-collapse>.guns-split-collapse-button{left:0}.guns-split-panel.is-collapse>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleX(-1)}.guns-split-panel.is-vertical{flex-direction:column}.guns-split-panel.is-vertical>.guns-split-panel-wrap{flex-direction:column;height:calc(var(--guns-split-size) + var(--guns-split-space));width:auto}.guns-split-panel.is-vertical>.guns-split-panel-wrap>.guns-split-panel-side{height:var(--guns-split-size);width:auto}.guns-split-panel.is-vertical>.guns-split-panel-wrap>.guns-split-panel-space{height:var(--guns-split-space);width:auto}.guns-split-panel.is-vertical>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line{width:100%;height:12px;left:auto;top:-6px;cursor:n-resize}.guns-split-panel.is-vertical>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line:after{width:100%;height:3px;margin:4px 0 0}.guns-split-panel.is-vertical>.guns-split-collapse-button{width:48px;height:14px;line-height:14px;top:var(--guns-split-size);left:50%;margin-left:-24px;margin-top:0;border-radius:0 0 4px 4px}.guns-split-panel.is-vertical>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleY(1)}.guns-split-panel.is-vertical.is-collapse>.guns-split-panel-wrap{width:auto!important;height:0!important}.guns-split-panel.is-vertical.is-collapse>.guns-split-collapse-button{top:0}.guns-split-panel.is-vertical.is-collapse>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleY(-1)}.guns-split-panel.is-reverse{flex-direction:row-reverse}.guns-split-panel.is-reverse>.guns-split-panel-wrap{flex-direction:row-reverse}.guns-split-panel.is-reverse>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line{left:auto;right:-6px}.guns-split-panel.is-reverse>.guns-split-collapse-button{left:auto;right:var(--guns-split-size);border-radius:4px 0 0 4px}.guns-split-panel.is-reverse>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleX(-1)}.guns-split-panel.is-reverse.is-collapse>.guns-split-collapse-button{right:0}.guns-split-panel.is-reverse.is-collapse>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleX(1)}.guns-split-panel.is-reverse.is-vertical{flex-direction:column-reverse}.guns-split-panel.is-reverse.is-vertical>.guns-split-panel-wrap{flex-direction:column-reverse}.guns-split-panel.is-reverse.is-vertical>.guns-split-panel-wrap>.guns-split-panel-space .guns-split-resize-line{top:auto;right:auto;bottom:-6px}.guns-split-panel.is-reverse.is-vertical>.guns-split-collapse-button{left:50%;top:auto;bottom:var(--guns-split-size);border-radius:4px 4px 0 0}.guns-split-panel.is-reverse.is-vertical>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleY(-1)}.guns-split-panel.is-reverse.is-vertical.is-collapse>.guns-split-collapse-button{bottom:0}.guns-split-panel.is-reverse.is-vertical.is-collapse>.guns-split-collapse-button .guns-split-collapse-icon{transform:scaleY(1)}.guns-split-panel.is-resizing{user-select:none}.guns-split-panel.is-resizing>.guns-split-panel-wrap,.guns-split-panel.is-resizing>.guns-split-collapse-button{transition:none}.guns-split-panel .guns-split-panel-mask{position:absolute;top:0;right:0;bottom:0;left:0;background:transparent;transition:all .3s cubic-bezier(.4,0,.2,1);pointer-events:none;display:none;z-index:2}@media screen and (max-width: 768px){.guns-split-panel.is-responsive:not(.is-vertical):not(.is-collapse){overflow:hidden!important}.guns-split-panel.is-responsive:not(.is-vertical)>.guns-split-panel-wrap{position:absolute;top:0;left:0;bottom:0}.guns-split-panel.is-responsive:not(.is-vertical)>.guns-split-panel-wrap>.guns-split-panel-side{background:var(--component-background);z-index:3}.guns-split-panel.is-responsive:not(.is-vertical)>.guns-split-panel-body{transition:all .3s cubic-bezier(.4,0,.2,1)}.guns-split-panel.is-responsive:not(.is-vertical)>.guns-split-panel-mask{display:block}.guns-split-panel.is-responsive:not(.is-vertical):not(.is-collapse)>.guns-split-panel-mask{left:var(--guns-split-size);background:rgba(0,0,0,.45);pointer-events:all}.guns-split-panel.is-responsive:not(.is-vertical):not(.is-collapse)>.guns-split-panel-body{transform:translate(calc(var(--guns-split-size) + var(--guns-split-space)));z-index:1}.guns-split-panel.is-responsive:not(.is-vertical).is-reverse>.guns-split-panel-wrap{right:0;left:auto}.guns-split-panel.is-responsive:not(.is-vertical).is-reverse:not(.is-collapse)>.guns-split-panel-mask{left:0;right:var(--guns-split-size)}.guns-split-panel.is-responsive:not(.is-vertical).is-reverse:not(.is-collapse)>.guns-split-panel-body{transform:translate(calc(0px - var(--guns-split-size) - var(--guns-split-space)))}}\n',document.head.appendChild(e);const x=i({name:"GunsSplitLayout",props:{width:{type:String,default:"252px"},minSize:Number,maxSize:Number,space:{type:String,default:"0px"},collapse:Boolean,allowCollapse:{type:Boolean,default:!0},leftStyle:{type:Object,default:{height:"100%"}},rightStyle:{type:Object,default:{height:"100%"}},collapseBtnStyle:Object,vertical:Boolean,reverse:Boolean,resizable:{type:Boolean,default:!0},responsive:{type:Boolean,default:null},cacheKey:String},emits:{"update:collapse":s=>!0},setup(s,{emit:e}){const l=t(s),i=n(null),o=n(null),r=n(null),u=n(!1),c=n(null),g=n(!1);p((()=>{if(s.cacheKey){const e=localStorage.getItem(v(s.cacheKey));c.value=e}}));const v=s=>`${s}Coll-Width`;return a([()=>s.collapse,()=>s.allowCollapse],(()=>{s.allowCollapse?u.value=s.collapse:u.value=!1}),{immediate:!0}),{rootRef:i,wrapRef:o,sideRef:r,isResponsive:l,isCollapse:u,resizedSize:c,resizing:g,toggleCollapse:s=>{u.value="boolean"==typeof s?s:!u.value,e("update:collapse",u.value)},onResize:e=>{const l=i.value,t=r.value;if(!l||!t)return;g.value=!0;const n=e.clientX,p=e.clientY,a=t.clientWidth,o=t.clientHeight,u=s.minSize||0,d=(e=>{const l=s.vertical?e.clientHeight:e.clientWidth;return s.maxSize?s.maxSize<0?l+s.maxSize:s.maxSize<1?Math.floor(l*s.maxSize):Math.min(s.maxSize,l):l})(l),h=e=>{const l=s.vertical?(s.reverse?p-e.clientY:e.clientY-p)+o:(s.reverse?n-e.clientX:e.clientX-n)+a;c.value=(l<u?u:l>d?d:l)+"px",s.cacheKey&&localStorage.setItem(v(s.cacheKey),c.value)},b=()=>{g.value=!1,document.removeEventListener("mousemove",h),document.removeEventListener("mouseup",b)};document.addEventListener("mousemove",h),document.addEventListener("mouseup",b)},getCollWidthCacheKey:v}}}),f={ref:"wrapRef",class:"guns-split-panel-wrap"},m={class:"guns-split-panel-space"};s("_",l(x,[["render",function(s,e,l,i,t,n){var p;const a=o("CaretUpOutlined"),x=o("CaretLeftOutlined");return r(),u("div",{ref:"rootRef",class:b(["guns-split-panel",{"is-reverse":s.reverse},{"is-vertical":s.vertical},{"is-collapse":s.isCollapse},{"is-resizing":s.resizing},{"is-responsive":s.isResponsive}]),style:g({"--guns-split-size":null!==(p=s.resizedSize)&&void 0!==p?p:s.width,"--guns-split-space":s.space})},[c("div",f,[c("div",{ref:"sideRef",class:"guns-split-panel-side",style:g(s.leftStyle)},[v(s.$slots,"default")],4),c("div",m,[s.resizable?(r(),u("div",{key:0,class:"guns-split-resize-line",onMousedown:e[0]||(e[0]=(...e)=>s.onResize&&s.onResize(...e))},null,32)):d("",!0)])],512),c("div",{class:"guns-split-panel-body",style:g(s.rightStyle)},[v(s.$slots,"content",{collapse:s.isCollapse})],4),s.allowCollapse?(r(),u("div",{key:0,style:g(s.collapseBtnStyle),class:"guns-split-collapse-button",onClick:e[1]||(e[1]=e=>s.toggleCollapse())},[v(s.$slots,"collapse",{collapse:s.isCollapse},(()=>[s.vertical?(r(),h(a,{key:0,class:"guns-split-collapse-icon"})):(r(),h(x,{key:1,class:"guns-split-collapse-icon"}))]))],4)):d("",!0),c("div",{class:"guns-split-panel-mask",onClick:e[2]||(e[2]=e=>s.toggleCollapse())})],6)}]]))}}}));
