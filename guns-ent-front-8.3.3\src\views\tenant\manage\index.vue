<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <div class="content-mian-header">
              <div class="header-content">
                <div class="header-content-left">
                  <a-space :size="16">
                    <a-input
                      v-model:value="where.searchText"
                      placeholder="租户名称、租户编码（回车搜索）"
                      @pressEnter="reload"
                      class="search-input"
                    >
                      <template #prefix>
                        <icon-font iconClass="icon-opt-search" />
                      </template>
                    </a-input>
                  </a-space>
                </div>
                <div class="header-content-right">
                  <a-space :size="16">
                    <a-button type="primary" class="border-radius" @click="openAddEdit()"><plus-outlined />新建</a-button>
                    <a-dropdown>
                      <template #overlay>
                        <a-menu @click="moreClick">
                          <a-menu-item key="1">
                            <icon-font iconClass="icon-opt-zidingyilie" color="#60666b" />
                            <span>自定义列</span>
                          </a-menu-item>
                          <div>
                            <a-menu-item key="2">
                              <icon-font iconClass="icon-opt-shanchu" color="#60666b" />
                              <span>批量删除</span>
                            </a-menu-item>
                          </div>
                        </a-menu>
                      </template>
                      <a-button class="border-radius">
                        更多
                        <small-dash-outlined />
                      </a-button>
                    </a-dropdown>
                  </a-space>
                </div>
              </div>
            </div>
            <div class="content-mian-body">
              <div class="table-content">
                <common-table :columns="columns" :where="where" rowId="tenantId" ref="tableRef" url="/tenant/page">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex == 'tenantName'">
                      <a @click="openDetail(record)">{{ record.tenantName }}</a>
                    </template>
                    <!-- 图标 -->
                    <template v-if="column.dataIndex == 'tenantLogoWrapper'">
                      <img :src="record.tenantLogoWrapper" alt="" class="appIconWrapper" />
                    </template>
                    <!-- 状态 -->
                    <template v-if="column.dataIndex == 'statusFlag'">
                      <vxe-switch
                        v-model="record.statusFlag"
                        :open-value="1"
                        :close-value="2"
                        @change="statusFlagChange(record)"
                      />
                    </template>
                    <template v-if="column.dataIndex == 'activeDate' || column.dataIndex == 'expireDate'">
                      {{ setTime(record[column.dataIndex]) }}
                    </template>
                    <!-- 操作 -->
                    <template v-if="column.key == 'action'">
                      <a-space :size="16">
                        <icon-font
                          iconClass="icon-opt-bianji"
                          font-size="24px"
                          title="编辑"
                          color="#60666b"
                          @click="openAddEdit(record)"
                        />
                        <icon-font
                          iconClass="icon-opt-shanchu"
                          font-size="24px"
                          title="删除"
                          color="#60666b"
                          @click="remove(record)"
                        />
                      </a-space>
                    </template>
                  </template>
                </common-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义列 -->
    <Custom
      v-model:visible="isShowCustom"
      v-if="isShowCustom"
      :data="columns"
      @done="val => (columns = val)"
      :fieldBusinessCode="fieldBusinessCode"
    />

    <!-- 新增编辑弹框 -->
    <TenantAddEdit v-model:visible="showEdit" v-if="showEdit" :data="current" @done="reload" />
    <!-- 租户详情 -->
    <TenantDetail v-model:visible="showDetail" v-if="showDetail" :data="current" @done="reload" />
  </div>
</template>

<script setup name="TenantManage">
import { TenantApi } from './api/TenantApi';
import { ref, createVNode, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue/es';
import TenantDetail from './components/tenant-detail.vue';
import TenantAddEdit from './components/tenant-add-edit.vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { CustomApi } from '@/components/common/Custom/api/CustomApi';

defineOptions({
  name: 'TenantManage',
})

// 表格配置
const columns = ref([
  {
    key: 'index',
    title: '序号',
    width: 48,
    align: 'center',
    isShow: true,
    hideInSetting: true,
    customRender: ({ index }) => tableRef.value.tableIndex + index
  },
  {
    dataIndex: 'tenantName',
    title: '租户名称',
    ellipsis: true,
    width: 200,
    isShow: true
  },
  {
    dataIndex: 'tenantCode',
    title: '租户编码',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'tenantLogoWrapper',
    title: '租户logo',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'companyName',
    title: '公司',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'email',
    title: '申请人邮箱',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'safePhone',
    title: '申请人电话',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'statusFlag',
    title: '状态',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'activeDate',
    title: '租户生效时间',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'expireDate',
    title: '租户到期时间',
    width: 100,
    isShow: true
  },
  {
    key: 'action',
    title: '操作',
    width: 80,
    isShow: true
  }
]);
// ref
const tableRef = ref(null);

// 搜索条件
const where = ref({
  searchText: ''
});
// 是否显示自定义列
const isShowCustom = ref(false);
// 当前行数据
const current = ref(null);
// 是否显示新增编辑弹框
const showEdit = ref(false);
// 是否显示详情
const showDetail = ref(false);
// 业务标识的编码
const fieldBusinessCode = ref('TENANT_MANAGEMENT');

onMounted(() => {
  getColumnData();
});

// 获取表格配置
const getColumnData = () => {
  CustomApi.getUserConfig({ fieldBusinessCode: fieldBusinessCode.value }).then(res => {
    if (res.tableWidthJson) {
      columns.value = JSON.parse(res.tableWidthJson);
    }
  });
};

// 更多点击
const moreClick = ({ key }) => {
  if (key == '1') {
    isShowCustom.value = true;
  } else if (key == '2') {
    batchDelete();
  }
};

// 点击搜索
const reload = () => {
  tableRef.value.reload();
};

// 新增编辑点击
const openAddEdit = record => {
  current.value = record;
  showEdit.value = true;
};

// 详情点击
const openDetail = record => {
  current.value = record;
  showDetail.value = true;
};

// 删除单个
const remove = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选中的租户吗?',
    icon: createVNode(ExclamationCircleOutlined),
    maskClosable: true,
    onOk: async () => {
      const res = await TenantApi.delete({ tenantId: record.tenantId });
      message.success(res.message);
      reload();
    }
  });
};

// 批量删除
const batchDelete = () => {
  if (tableRef.value.selectedRowList && tableRef.value.selectedRowList.length == 0) {
    return message.warning('请选择需要删除的租户');
  }
  Modal.confirm({
    title: '提示',
    content: '确定要删除选中的租户吗?',
    icon: createVNode(ExclamationCircleOutlined),
    maskClosable: true,
    onOk: async () => {
      const res = await TenantApi.batchDelete({ tenantIdList: tableRef.value.selectedRowList });
      message.success(res.message);
      reload();
    }
  });
};

// 切换租户状态
const statusFlagChange = record => {
  TenantApi.edit(record).then(res => {
    message.success(res.message);
    reload();
  });
};

// 设置时间
const setTime = value => {
  let time = '';
  if (value) {
    time = value.substr(0, 10);
  }
  return time;
};
</script>

<style scoped lang="less">
.appIconWrapper {
  width: 22px;
  height: 22px;
}
</style>
