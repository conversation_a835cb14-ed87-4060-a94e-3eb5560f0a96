package cn.stylefeng.roses.ent.mobile.manage.pojo.config;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 更换手机，发送验证码的请求
 *
 * <AUTHOR>
 * @since 2024/3/24 22:59
 */
@Data
public class SendPhoneCodeRequest {

    /**
     * 手机号
     */
    @ChineseDescription("手机号")
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    /**
     * 验证码图形对应的缓存key
     */
    @ChineseDescription("验证码图形对应的缓存key")
    @NotBlank(message = "验证码key不能为空")
    private String verKey;

    /**
     * 用户输入的验证码的值
     */
    @ChineseDescription("用户输入的验证码的值")
    @NotBlank(message = "验证码不能为空")
    private String verCode;

}
