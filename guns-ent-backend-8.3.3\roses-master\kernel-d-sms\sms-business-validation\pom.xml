<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-sms</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>sms-business-validation</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--系统管理的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--短信发送模块api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>sms-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--参数校验模块-->
        <!--用在控制器，参数校验-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>validator-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--安全模块模块-->
        <!--发送短信前的验证码校验，防止暴力发送短信-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库sdk-->
        <!--数据库dao框架-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

</project>
