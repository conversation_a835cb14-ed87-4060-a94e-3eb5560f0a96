import{_ as y,r,o as b,a as t,c as i,b as e,d as h,w as s,f as c,F as k,e as x,t as p,a5 as B,b8 as L,b9 as R,S as N}from"./index-18a1ea24.js";/* empty css              */import{H as S}from"./HomeApi-75ad5066.js";const w={class:"card"},C={class:"card-body"},F={__name:"operation-records",setup(H){const n=r([]),o=r(!1),d=()=>{o.value=!0,S.getRecentLogs().then(_=>{n.value=_}).finally(()=>{o.value=!1})};return b(()=>{d()}),(_,l)=>{const u=B,m=L,g=R,v=N;return t(),i("div",w,[l[0]||(l[0]=e("div",{class:"card-header"},[e("div",{class:"card-header-title"},[e("span",null,"\u6700\u8FD1\u64CD\u4F5C\u8BB0\u5F55")])],-1)),e("div",C,[h(v,{spinning:o.value},{default:s(()=>[n.value.length===0?(t(),c(u,{key:0})):(t(),c(g,{key:1},{default:s(()=>[(t(!0),i(k,null,x(n.value,(a,f)=>(t(),c(m,{key:f,color:a.color},{default:s(()=>[e("em",null,p(a.createTime),1),e("em",null,p(a.logName+"-"+a.logContent),1)]),_:2},1032,["color"]))),128))]),_:1}))]),_:1},8,["spinning"])])])}}},D=y(F,[["__scopeId","data-v-2ac71b09"]]);export{D as default};
