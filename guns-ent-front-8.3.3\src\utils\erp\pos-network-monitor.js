/**
 * POS系统网络状态监控工具
 * 
 * 提供网络连接状态监控、离线模式管理、网络恢复处理
 *
 * <AUTHOR>
 * @since 2025/08/01 21:45
 */
import { ref, reactive, computed } from 'vue';
import { PosFeedback } from './pos-feedback';

export class PosNetworkMonitor {
  
  // 网络状态
  static networkState = reactive({
    isOnline: navigator.onLine,
    lastOnlineTime: Date.now(),
    lastOfflineTime: null,
    connectionType: 'unknown',
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0,
    saveData: false
  });

  // 离线模式状态
  static offlineState = reactive({
    enabled: false,
    pendingRequests: new Map(),
    cachedData: new Map(),
    syncQueue: [],
    lastSyncTime: null
  });

  // 重连状态
  static reconnectState = reactive({
    isReconnecting: false,
    attempts: 0,
    maxAttempts: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2
  });

  // 事件监听器
  static listeners = new Set();

  /**
   * 初始化网络监控
   */
  static init() {
    this.setupEventListeners();
    this.detectConnectionInfo();
    this.startPeriodicCheck();
    
    // 初始状态检查
    if (!navigator.onLine) {
      this.handleOffline();
    }
  }

  /**
   * 设置事件监听器
   */
  static setupEventListeners() {
    // 监听在线/离线事件
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

    // 监听网络信息变化（如果支持）
    if ('connection' in navigator) {
      navigator.connection.addEventListener('change', this.handleConnectionChange.bind(this));
    }
  }

  /**
   * 处理网络连接
   */
  static handleOnline() {
    console.log('网络已连接');
    
    this.networkState.isOnline = true;
    this.networkState.lastOnlineTime = Date.now();
    this.reconnectState.attempts = 0;
    this.reconnectState.isReconnecting = false;

    // 通知用户
    PosFeedback.success('网络连接已恢复');

    // 同步离线数据
    this.syncOfflineData();

    // 重试待处理的请求
    this.retryPendingRequests();

    // 通知监听器
    this.notifyListeners('online');
  }

  /**
   * 处理网络断开
   */
  static handleOffline() {
    console.log('网络已断开');
    
    this.networkState.isOnline = false;
    this.networkState.lastOfflineTime = Date.now();

    // 启用离线模式
    this.enableOfflineMode();

    // 通知用户
    PosFeedback.notifyWarning(
      '网络连接断开',
      '已切换到离线模式，部分功能可能受限',
      { duration: 0 }
    );

    // 开始重连尝试
    this.startReconnectAttempts();

    // 通知监听器
    this.notifyListeners('offline');
  }

  /**
   * 处理页面可见性变化
   */
  static handleVisibilityChange() {
    if (!document.hidden && !this.networkState.isOnline) {
      // 页面重新可见时检查网络状态
      this.checkNetworkStatus();
    }
  }

  /**
   * 处理网络信息变化
   */
  static handleConnectionChange() {
    this.detectConnectionInfo();
    this.notifyListeners('connectionchange');
  }

  /**
   * 检测网络连接信息
   */
  static detectConnectionInfo() {
    if ('connection' in navigator) {
      const connection = navigator.connection;
      this.networkState.connectionType = connection.type || 'unknown';
      this.networkState.effectiveType = connection.effectiveType || 'unknown';
      this.networkState.downlink = connection.downlink || 0;
      this.networkState.rtt = connection.rtt || 0;
      this.networkState.saveData = connection.saveData || false;
    }
  }

  /**
   * 启用离线模式
   */
  static enableOfflineMode() {
    this.offlineState.enabled = true;
    console.log('离线模式已启用');
  }

  /**
   * 禁用离线模式
   */
  static disableOfflineMode() {
    this.offlineState.enabled = false;
    console.log('离线模式已禁用');
  }

  /**
   * 开始重连尝试
   */
  static startReconnectAttempts() {
    if (this.reconnectState.isReconnecting) return;
    
    this.reconnectState.isReconnecting = true;
    this.attemptReconnect();
  }

  /**
   * 尝试重连
   */
  static async attemptReconnect() {
    if (this.networkState.isOnline || this.reconnectState.attempts >= this.reconnectState.maxAttempts) {
      this.reconnectState.isReconnecting = false;
      return;
    }

    this.reconnectState.attempts++;
    console.log(`重连尝试 ${this.reconnectState.attempts}/${this.reconnectState.maxAttempts}`);

    try {
      // 尝试发送测试请求
      const isConnected = await this.testConnection();
      
      if (isConnected) {
        this.handleOnline();
        return;
      }
    } catch (error) {
      console.log('重连测试失败:', error.message);
    }

    // 计算下次重连延迟（指数退避）
    const delay = Math.min(
      this.reconnectState.baseDelay * Math.pow(this.reconnectState.backoffFactor, this.reconnectState.attempts - 1),
      this.reconnectState.maxDelay
    );

    setTimeout(() => {
      this.attemptReconnect();
    }, delay);
  }

  /**
   * 测试网络连接
   */
  static async testConnection() {
    try {
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache',
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * 定期检查网络状态
   */
  static startPeriodicCheck() {
    setInterval(() => {
      this.checkNetworkStatus();
    }, 30000); // 每30秒检查一次
  }

  /**
   * 检查网络状态
   */
  static async checkNetworkStatus() {
    const wasOnline = this.networkState.isOnline;
    const isCurrentlyOnline = navigator.onLine;

    if (wasOnline !== isCurrentlyOnline) {
      if (isCurrentlyOnline) {
        // 验证真实连接
        const isReallyOnline = await this.testConnection();
        if (isReallyOnline) {
          this.handleOnline();
        }
      } else {
        this.handleOffline();
      }
    }
  }

  /**
   * 同步离线数据
   */
  static async syncOfflineData() {
    if (this.offlineState.syncQueue.length === 0) return;

    console.log(`开始同步 ${this.offlineState.syncQueue.length} 条离线数据`);

    const syncPromises = this.offlineState.syncQueue.map(async (item) => {
      try {
        await this.syncSingleItem(item);
        return { success: true, item };
      } catch (error) {
        console.error('同步失败:', item, error);
        return { success: false, item, error };
      }
    });

    const results = await Promise.allSettled(syncPromises);
    const successCount = results.filter(r => r.value?.success).length;
    const failCount = results.length - successCount;

    if (successCount > 0) {
      PosFeedback.success(`成功同步 ${successCount} 条数据`);
      // 移除已成功同步的数据
      this.offlineState.syncQueue = this.offlineState.syncQueue.filter((item, index) => 
        !results[index].value?.success
      );
    }

    if (failCount > 0) {
      PosFeedback.warning(`${failCount} 条数据同步失败，将在下次连接时重试`);
    }

    this.offlineState.lastSyncTime = Date.now();
  }

  /**
   * 同步单个数据项
   */
  static async syncSingleItem(item) {
    const { type, data, timestamp } = item;
    
    switch (type) {
      case 'order':
        return await this.syncOrder(data);
      case 'payment':
        return await this.syncPayment(data);
      case 'inventory':
        return await this.syncInventory(data);
      default:
        throw new Error(`未知的同步类型: ${type}`);
    }
  }

  /**
   * 重试待处理的请求
   */
  static async retryPendingRequests() {
    if (this.offlineState.pendingRequests.size === 0) return;

    console.log(`重试 ${this.offlineState.pendingRequests.size} 个待处理请求`);

    for (const [requestId, request] of this.offlineState.pendingRequests) {
      try {
        const result = await request.retry();
        request.resolve(result);
        this.offlineState.pendingRequests.delete(requestId);
      } catch (error) {
        // 如果重试失败，保留请求以便下次重试
        console.error('请求重试失败:', requestId, error);
      }
    }
  }

  /**
   * 添加离线数据到同步队列
   */
  static addToSyncQueue(type, data) {
    this.offlineState.syncQueue.push({
      id: Date.now() + Math.random(),
      type,
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 添加待处理请求
   */
  static addPendingRequest(requestId, request) {
    this.offlineState.pendingRequests.set(requestId, request);
  }

  /**
   * 获取缓存数据
   */
  static getCachedData(key) {
    return this.offlineState.cachedData.get(key);
  }

  /**
   * 设置缓存数据
   */
  static setCachedData(key, data, ttl = 5 * 60 * 1000) {
    this.offlineState.cachedData.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 检查缓存是否有效
   */
  static isCacheValid(key) {
    const cached = this.offlineState.cachedData.get(key);
    if (!cached) return false;
    
    return (Date.now() - cached.timestamp) < cached.ttl;
  }

  /**
   * 添加网络状态监听器
   */
  static addListener(callback) {
    this.listeners.add(callback);
  }

  /**
   * 移除网络状态监听器
   */
  static removeListener(callback) {
    this.listeners.delete(callback);
  }

  /**
   * 通知监听器
   */
  static notifyListeners(event) {
    this.listeners.forEach(callback => {
      try {
        callback(event, this.networkState);
      } catch (error) {
        console.error('网络状态监听器错误:', error);
      }
    });
  }

  /**
   * 获取网络状态
   */
  static getNetworkState() {
    return { ...this.networkState };
  }

  /**
   * 获取离线状态
   */
  static getOfflineState() {
    return { ...this.offlineState };
  }

  /**
   * 是否为慢速网络
   */
  static isSlowConnection() {
    return this.networkState.effectiveType === 'slow-2g' || 
           this.networkState.effectiveType === '2g' ||
           this.networkState.downlink < 0.5;
  }

  /**
   * 是否启用数据节省模式
   */
  static isDataSaverEnabled() {
    return this.networkState.saveData;
  }

  /**
   * 销毁监控器
   */
  static destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    
    if ('connection' in navigator) {
      navigator.connection.removeEventListener('change', this.handleConnectionChange);
    }
    
    this.listeners.clear();
  }
}
