<template>
  <a-modal
    :visible="visible"
    title="新增商品"
    :width="800"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <!-- 基本信息 -->
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="商品编码" name="productCode">
            <a-input v-model:value="form.productCode" placeholder="请输入商品编码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="商品名称" name="productName">
            <a-input v-model:value="form.productName" placeholder="请输入商品名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="商品简称" name="productShortName">
            <a-input v-model:value="form.productShortName" placeholder="请输入商品简称" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="商品分类" name="categoryId">
            <category-selector v-model:value="form.categoryId" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="供应商" name="supplierId">
            <supplier-selector 
              v-model:value="form.supplierId" 
              :filter="{ businessMode: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
              @change="handleSupplierChange" 
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="计价类型" name="pricingType">
            <a-select v-model:value="form.pricingType" placeholder="请选择计价类型" @change="handlePricingTypeChange">
              <a-select-option value="NORMAL">普通商品</a-select-option>
              <a-select-option value="WEIGHT">计重商品</a-select-option>
              <a-select-option value="PIECE">计件商品</a-select-option>
              <a-select-option value="VARIABLE">不定价商品</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 价格信息 - 计价类型和价格在同一行 -->
      <a-row :gutter="16" v-if="form.pricingType">
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'NORMAL'">
          <a-form-item label="零售价格" name="retailPrice">
            <a-input-number v-model:value="form.retailPrice" placeholder="请输入零售价格" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'WEIGHT'">
          <a-form-item label="单位价格" name="unitPrice">
            <a-input-number v-model:value="form.unitPrice" placeholder="请输入单位价格(每公斤)" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'PIECE'">
          <a-form-item label="单份价格" name="piecePrice">
            <a-input-number v-model:value="form.piecePrice" placeholder="请输入单份价格" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.pricingType === 'VARIABLE'">
          <a-form-item label="参考价格" name="referencePrice">
            <a-input-number v-model:value="form.referencePrice" placeholder="请输入参考价格(可选)" :min="0" :precision="2" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="规格" name="specification">
            <a-input v-model:value="form.specification" placeholder="请输入商品规格" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="单位" name="unit">
            <a-input v-model:value="form.unit" placeholder="请输入商品单位" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="品牌" name="brand">
            <a-input v-model:value="form.brand" placeholder="请输入商品品牌" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option value="ACTIVE">启用</a-select-option>
              <a-select-option value="INACTIVE">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="商品描述" name="description" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.description" placeholder="请输入商品描述" :rows="3" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { reactive, ref, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { ProductApi } from '../api/ProductApi';
import SupplierSelector from '@/components/erp/SupplierSelector.vue';
import CategorySelector from './category/CategorySelector.vue';

export default {
  name: 'ProductAdd',
  components: {
    SupplierSelector,
    CategorySelector
  },
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  setup(props, { emit }) {
    // 表单数据
    const form = reactive({
      pricingType: 'NORMAL',
      status: 'ACTIVE',
      retailPrice: null,
      unitPrice: null,
      piecePrice: null,
      referencePrice: null
    });
    
    const formRef = ref(null);
    const loading = ref(false);

    // 表单验证规则
    const rules = reactive({
      productCode: [
        { required: true, message: '请输入商品编码', trigger: 'blur' },
        { max: 50, message: '商品编码不能超过50个字符', trigger: 'blur' }
      ],
      productName: [
        { required: true, message: '请输入商品名称', trigger: 'blur' },
        { max: 200, message: '商品名称不能超过200个字符', trigger: 'blur' }
      ],
      categoryId: [
        { required: true, message: '请选择商品分类', trigger: 'change' }
      ],
      supplierId: [
        { required: true, message: '请选择供应商', trigger: 'change' }
      ],
      pricingType: [
        { required: true, message: '请选择计价类型', trigger: 'change' }
      ]
    });

    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 供应商变更处理
    const handleSupplierChange = (supplierId, supplier) => {
      console.log('供应商变更:', supplierId, supplier);
    };

    // 计价类型变更处理
    const handlePricingTypeChange = (pricingType) => {
      console.log('计价类型变更:', pricingType);
      // 清空价格字段
      form.retailPrice = null;
      form.unitPrice = null;
      form.piecePrice = null;
      form.referencePrice = null;
      
      // 动态更新验证规则
      updatePriceValidationRules(pricingType);
    };

    // 动态更新价格验证规则
    const updatePriceValidationRules = (pricingType) => {
      // 清除之前的价格验证规则
      delete rules.retailPrice;
      delete rules.unitPrice;
      delete rules.piecePrice;
      delete rules.referencePrice;
      
      // 根据计价类型添加相应的验证规则
      switch (pricingType) {
        case 'NORMAL':
          rules.retailPrice = [
            { required: true, message: '请输入零售价格', trigger: 'blur', type: 'number' }
          ];
          break;
        case 'WEIGHT':
          rules.unitPrice = [
            { required: true, message: '请输入单位价格', trigger: 'blur', type: 'number' }
          ];
          break;
        case 'PIECE':
          rules.piecePrice = [
            { required: true, message: '请输入单份价格', trigger: 'blur', type: 'number' }
          ];
          break;
        case 'VARIABLE':
          // 参考价格为可选，不添加必填验证
          break;
      }
    };

    // 保存
    const save = async () => {
      try {
        await formRef.value.validate();
        loading.value = true;
        
        // 准备提交的数据
        const submitData = { ...form };
        
        // 保存商品信息
        await ProductApi.add(submitData);
        
        message.success('新增商品成功');
        updateVisible(false);
        emit('done');
      } catch (e) {
        console.error('新增商品失败:', e);
        message.error(e.message || '新增失败');
      } finally {
        loading.value = false;
      }
    };

    // 重置表单
    const resetForm = () => {
      Object.keys(form).forEach((key) => {
        if (key === 'pricingType') {
          form[key] = 'NORMAL';
        } else if (key === 'status') {
          form[key] = 'ACTIVE';
        } else if (key.includes('Price')) {
          form[key] = null;
        } else {
          form[key] = undefined;
        }
      });

      // 重置表单验证状态
      if (formRef.value) {
        formRef.value.resetFields();
      }
    };



    // 监听弹窗显示状态
    watch(
      () => props.visible,
      (visible) => {
        if (visible) {
          resetForm();
          // 设置默认计价类型的验证规则
          updatePriceValidationRules('NORMAL');
        }
      },
      { immediate: true }
    );



    return {
      form,
      formRef,
      loading,

      rules,
      updateVisible,
      handleSupplierChange,
      handlePricingTypeChange,
      save
    };
  }
};
</script>

<style scoped>
/* 表单样式优化 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label {
  font-weight: 500;
}

/* 文本域样式 */
.ant-input {
  border-radius: 4px;
}

.ant-select {
  border-radius: 4px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  :deep(.ant-modal) {
    width: 95% !important;
    margin: 10px auto;
  }
  
  :deep(.ant-form-item-label) {
    text-align: left !important;
  }
}
</style>