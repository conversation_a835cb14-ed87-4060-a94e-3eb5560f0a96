package cn.stylefeng.roses.kernel.erp.modular.inventory.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryHistoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryHistoryService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 库存历史控制器
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@RestController
@ApiResource(name = "库存历史管理", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.ERP_INVENTORY_MANAGE)
public class InventoryHistoryController {

    @Resource
    private InventoryHistoryService inventoryHistoryService;

    /**
     * 分页查询库存历史列表
     */
    @GetResource(name = "分页查询库存历史列表", path = "/erp/inventory/history/page")
    public ResponseData<PageResult<InventoryHistoryResponse>> page(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        PageResult<InventoryHistoryResponse> pageResult = inventoryHistoryService.findPage(inventoryHistoryQueryRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询库存历史列表
     */
    @GetResource(name = "查询库存历史列表", path = "/erp/inventory/history/list")
    public ResponseData<List<InventoryHistoryResponse>> list(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        List<InventoryHistoryResponse> responseList = inventoryHistoryService.findList(inventoryHistoryQueryRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 查询商品库存历史
     */
    @GetResource(name = "查询商品库存历史", path = "/erp/inventory/history/product")
    public ResponseData<List<InventoryHistoryResponse>> productHistory(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        List<InventoryHistoryResponse> responseList = inventoryHistoryService.findProductHistory(inventoryHistoryQueryRequest.getProductId());
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 查询库存历史详情
     */
    @GetResource(name = "查询库存历史详情", path = "/erp/inventory/history/detail")
    public ResponseData<InventoryHistoryResponse> detail(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        InventoryHistoryResponse response = inventoryHistoryService.detail(inventoryHistoryQueryRequest.getProductId());
        return new SuccessResponseData<>(response);
    }

    /**
     * 按操作类型统计库存变动
     */
    @GetResource(name = "按操作类型统计库存变动", path = "/erp/inventory/history/statistics")
    public ResponseData<List<InventoryHistoryResponse>> statistics(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        List<InventoryHistoryResponse> responseList = inventoryHistoryService.findStatistics(inventoryHistoryQueryRequest);
        return new SuccessResponseData<>(responseList);
    }

}