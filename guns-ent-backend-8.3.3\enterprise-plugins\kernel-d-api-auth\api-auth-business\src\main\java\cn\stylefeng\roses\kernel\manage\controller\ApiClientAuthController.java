package cn.stylefeng.roses.kernel.manage.controller;

import cn.stylefeng.roses.kernel.manage.pojo.request.ApiClientAuthRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.ApiAuthBindResult;
import cn.stylefeng.roses.kernel.manage.service.ApiClientAuthService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * API客户端和资源绑定关系控制器
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@RestController
@ApiResource(name = "API客户端和资源绑定关系")
public class ApiClientAuthController {

    @Resource
    private ApiClientAuthService apiClientAuthService;

    /**
     * 获取API客户端和资源绑定关系列表
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @GetResource(name = "获取API客户端和资源绑定关系列表", path = "/apiClientAuth/getBindResult")
    public ResponseData<ApiAuthBindResult> getBindResult(@Validated(ApiClientAuthRequest.getBindResult.class) ApiClientAuthRequest apiClientAuthRequest) {
        return new SuccessResponseData<>(apiClientAuthService.getBindResult(apiClientAuthRequest));
    }

    /**
     * api客户端和资源绑定
     *
     * <AUTHOR>
     * @since 2023/10/25 18:10
     */
    @PostResource(name = "api客户端和资源绑定", path = "/apiClientAuth/bind")
    public ResponseData<?> bind(@RequestBody @Validated(ApiClientAuthRequest.selectBind.class) ApiClientAuthRequest apiClientAuthRequest) {
        apiClientAuthService.bind(apiClientAuthRequest);
        return new SuccessResponseData<>();
    }

}
