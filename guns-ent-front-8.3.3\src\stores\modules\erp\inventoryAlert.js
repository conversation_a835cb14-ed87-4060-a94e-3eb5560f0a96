import { defineStore } from 'pinia';
import { InventoryAlertRuleApi } from '@/views/erp/inventoryAlert/api/InventoryAlertRuleApi';
import { InventoryAlertRecordApi } from '@/views/erp/inventoryAlert/api/InventoryAlertRecordApi';

export const useInventoryAlertStore = defineStore('inventoryAlert', {
  state: () => ({
    // 预警规则列表
    ruleList: [],
    ruleTotal: 0,
    ruleLoading: false,
    
    // 预警记录列表
    recordList: [],
    recordTotal: 0,
    recordLoading: false,
    
    // 预警统计数据
    statistics: {
      totalAlerts: 0,
      criticalAlerts: 0,
      warningAlerts: 0,
      infoAlerts: 0,
      pendingAlerts: 0,
      processingAlerts: 0,
      resolvedAlerts: 0,
      ignoredAlerts: 0
    },
    
    // 当前选中的规则或记录
    currentRule: null,
    currentRecord: null,
    
    // 缓存的选项数据
    productOptions: [],
    categoryOptions: [],
    userOptions: [],
    
    // 最近的预警记录
    recentRecords: [],
    
    // 概览数据
    overview: null
  }),

  getters: {
    // 获取启用的规则数量
    enabledRulesCount: (state) => {
      return state.ruleList.filter(rule => rule.isEnabled === 'Y').length;
    },
    
    // 获取待处理预警数量
    pendingAlertsCount: (state) => {
      return state.statistics.pendingAlerts || 0;
    },
    
    // 获取紧急预警数量
    criticalAlertsCount: (state) => {
      return state.statistics.criticalAlerts || 0;
    },
    
    // 获取总预警数量
    totalAlertsCount: (state) => {
      return state.statistics.totalAlerts || 0;
    },
    
    // 获取处理率
    resolveRate: (state) => {
      const total = state.statistics.totalAlerts || 0;
      const resolved = state.statistics.resolvedAlerts || 0;
      return total > 0 ? Math.round((resolved / total) * 100) : 0;
    }
  },

  actions: {
    /**
     * 加载预警规则列表
     */
    async loadRuleList(params = {}) {
      this.ruleLoading = true;
      try {
        const response = await InventoryAlertRuleApi.findPage(params);
        this.ruleList = response.rows || [];
        this.ruleTotal = response.totalRows || 0;
      } catch (error) {
        console.error('加载预警规则列表失败:', error);
        this.ruleList = [];
        this.ruleTotal = 0;
      } finally {
        this.ruleLoading = false;
      }
    },

    /**
     * 加载预警记录列表
     */
    async loadRecordList(params = {}) {
      this.recordLoading = true;
      try {
        const response = await InventoryAlertRecordApi.findPage(params);
        this.recordList = response.rows || [];
        this.recordTotal = response.totalRows || 0;
      } catch (error) {
        console.error('加载预警记录列表失败:', error);
        this.recordList = [];
        this.recordTotal = 0;
      } finally {
        this.recordLoading = false;
      }
    },

    /**
     * 加载预警统计数据
     */
    async loadStatistics(params = {}) {
      try {
        const response = await InventoryAlertRecordApi.getStatistics(params);
        this.statistics = {
          totalAlerts: response.total_alerts || 0,
          criticalAlerts: response.critical_alerts || 0,
          warningAlerts: response.warning_alerts || 0,
          infoAlerts: response.info_alerts || 0,
          pendingAlerts: response.pending_alerts || 0,
          processingAlerts: response.processing_alerts || 0,
          resolvedAlerts: response.resolved_alerts || 0,
          ignoredAlerts: response.ignored_alerts || 0
        };
      } catch (error) {
        console.error('加载预警统计数据失败:', error);
      }
    },

    /**
     * 加载最近的预警记录
     */
    async loadRecentRecords(params = {}) {
      try {
        const response = await InventoryAlertRecordApi.getRecentRecords(params);
        this.recentRecords = response || [];
      } catch (error) {
        console.error('加载最近预警记录失败:', error);
        this.recentRecords = [];
      }
    },

    /**
     * 加载概览数据
     */
    async loadOverview(params = {}) {
      try {
        const response = await InventoryAlertRecordApi.getOverview(params);
        this.overview = response;
        
        // 更新统计数据和最近记录
        if (response.statistics) {
          this.statistics = {
            totalAlerts: response.statistics.total_alerts || 0,
            criticalAlerts: response.statistics.critical_alerts || 0,
            warningAlerts: response.statistics.warning_alerts || 0,
            infoAlerts: response.statistics.info_alerts || 0,
            pendingAlerts: response.statistics.pending_alerts || 0,
            processingAlerts: response.statistics.processing_alerts || 0,
            resolvedAlerts: response.statistics.resolved_alerts || 0,
            ignoredAlerts: response.statistics.ignored_alerts || 0
          };
        }
        
        if (response.recentRecords) {
          this.recentRecords = response.recentRecords;
        }
      } catch (error) {
        console.error('加载概览数据失败:', error);
      }
    },

    /**
     * 设置当前规则
     */
    setCurrentRule(rule) {
      this.currentRule = rule;
    },

    /**
     * 设置当前记录
     */
    setCurrentRecord(record) {
      this.currentRecord = record;
    },

    /**
     * 更新规则状态
     */
    updateRuleStatus(ruleId, isEnabled) {
      const rule = this.ruleList.find(r => r.id === ruleId);
      if (rule) {
        rule.isEnabled = isEnabled;
      }
    },

    /**
     * 更新记录状态
     */
    updateRecordStatus(recordId, status) {
      const record = this.recordList.find(r => r.id === recordId);
      if (record) {
        record.status = status;
        record.handleTime = new Date();
      }
      
      // 同时更新最近记录中的状态
      const recentRecord = this.recentRecords.find(r => r.id === recordId);
      if (recentRecord) {
        recentRecord.status = status;
        recentRecord.handleTime = new Date();
      }
    },

    /**
     * 清空数据
     */
    clearData() {
      this.ruleList = [];
      this.ruleTotal = 0;
      this.recordList = [];
      this.recordTotal = 0;
      this.currentRule = null;
      this.currentRecord = null;
      this.recentRecords = [];
      this.overview = null;
    },

    /**
     * 刷新预警数据
     */
    async refreshData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadRecentRecords(),
        this.loadRuleList({ pageNo: 1, pageSize: 10 }),
        this.loadRecordList({ pageNo: 1, pageSize: 10, statusFilter: 'PENDING' })
      ]);
    },

    /**
     * 刷新概览数据
     */
    async refreshOverview() {
      await this.loadOverview();
    }
  }
});
