import{R as r,_ as b,r as d,s as k,o as T,a as p,c as B,b as s,d as l,w as t,g as i,f as P,h as R,m as N,z as V,A as h,l as U,u as q,H as A,B as I}from"./index-18a1ea24.js";class v{static getFileConfig(){return r.get("/new/sysConfig/getFileConfig")}static updateFileConfig(a){return r.post("/new/sysConfig/updateFileConfig",a)}}const j={class:"file-config"},z={class:"file-config-type"},E={__name:"file-config",setup(c){const a=d({fileSaveType:null,localFileSavePath:null}),g=k({localFileSavePath:[{required:!0,type:"string",message:"\u8BF7\u8F93\u5165\u5B58\u50A8\u76EE\u5F55\u8DEF\u5F84",trigger:"blur"}]}),u=d();T(()=>{f()});const f=()=>{v.getFileConfig().then(o=>{a.value=o.data})},m=()=>{a.value.fileSaveType==10&&(a.value.localFileSavePath=null)},y=async()=>{await u.value.validate(),v.updateFileConfig(a.value).then(o=>{N.success(o.message),f()})};return(o,e)=>{const _=V,C=h,F=U,S=q,w=A,x=I;return p(),B("div",j,[e[5]||(e[5]=s("div",{class:"file-config-title"},"\u6587\u4EF6\u5B58\u50A8\u65B9\u5F0F",-1)),e[6]||(e[6]=s("div",{class:"file-config-remark"},"\u914D\u7F6E\u4E0A\u4F20\u7684\u6587\u6863\u5B58\u50A8\u5728\u4EC0\u4E48\u5730\u65B9",-1)),s("div",z,[l(C,{value:a.value.fileSaveType,"onUpdate:value":e[0]||(e[0]=n=>a.value.fileSaveType=n),name:"sex",onChange:m},{default:t(()=>[l(_,{value:10},{default:t(()=>e[2]||(e[2]=[i("\u672C\u5730\uFF0C\u5B58\u50A8\u5230\u9ED8\u8BA4\u8DEF\u5F84\uFF08jar\u6240\u5728\u76EE\u5F55\uFF09")])),_:1,__:[2]}),l(_,{value:11},{default:t(()=>e[3]||(e[3]=[i("\u5B58\u50A8\u5230\u6307\u5B9A\u8DEF\u5F84\u4E0B\uFF08\u9700\u8981\u914D\u7F6Elinux\u548Cwindows\u7684\u8DEF\u5F84\uFF09")])),_:1,__:[3]})]),_:1},8,["value"])]),l(w,{model:a.value,rules:g,ref_key:"formRef",ref:u},{default:t(()=>[a.value.fileSaveType!=10?(p(),P(S,{key:0,label:"\u5B58\u50A8\u76EE\u5F55\u8DEF\u5F84",name:"localFileSavePath"},{default:t(()=>[l(F,{value:a.value.localFileSavePath,"onUpdate:value":e[1]||(e[1]=n=>a.value.localFileSavePath=n),placeholder:"\u8F93\u5165\u5E94\u7528\u670D\u52A1\u5668\u5B8C\u6574\u76EE\u5F55\u7ED3\u6784\uFF0C\u5FC5\u987B\u76EE\u5F55\u5DF2\u5B58\u5728",style:{width:"80%"}},null,8,["value"])]),_:1})):R("",!0)]),_:1},8,["model","rules"]),l(x,{type:"primary",class:"border-radius",onClick:y},{default:t(()=>e[4]||(e[4]=[i("\u4FDD\u5B58")])),_:1,__:[4]})])}}},M=b(E,[["__scopeId","data-v-4115efb5"]]);export{M as default};
