/**
 * 通用树结构组件预设配置
 * 
 * <AUTHOR>
 * @since 2025/01/24
 */

import { RegionApi } from '@/views/erp/region/api/regionApi'
import { ProductCategoryApi } from '@/views/erp/productCategory/api/productCategoryApi'

/**
 * 区域管理配置模板
 */
export const RegionTreeConfig = {
  dataSource: {
    api: RegionApi.findTree,
    lazyLoadApi: RegionApi.findTreeWithLazy,
    searchParam: 'searchText',
    parentIdParam: 'parentId'
  },
  fieldMapping: {
    key: 'regionId',
    title: 'regionName',
    children: 'children',
    hasChildren: 'hasChildren',
    level: 'regionLevel'
  },
  displayConfig: {
    title: '区域管理',
    showHeader: true,
    showSearch: true,
    searchPlaceholder: '请输入区域名称，回车搜索',
    showAddButton: true,
    showEditIcons: true,
    showIcon: false,
    isSetWidth: true
  },
  interactionConfig: {
    selectable: true,
    expandable: true,
    lazyLoad: true,
    defaultExpandLevel: 3,
    allowMultiSelect: false
  },
  actionConfig: {
    allowAdd: true,
    allowEdit: true,
    allowDelete: true
  }
}

/**
 * 产品分类管理配置模板
 */
export const ProductCategoryTreeConfig = {
  dataSource: {
    api: ProductCategoryApi.findTree,
    lazyLoadApi: ProductCategoryApi.findTreeWithLazy,
    searchParam: 'searchText',
    parentIdParam: 'parentId'
  },
  fieldMapping: {
    key: 'categoryId',
    title: 'categoryName',
    children: 'children',
    hasChildren: 'hasChildren',
    level: 'categoryLevel'
  },
  displayConfig: {
    title: '产品分类',
    showHeader: true,
    showSearch: true,
    searchPlaceholder: '请输入分类名称，回车搜索',
    showAddButton: true,
    showEditIcons: true,
    showIcon: false,
    isSetWidth: true
  },
  interactionConfig: {
    selectable: true,
    expandable: true,
    lazyLoad: true,
    defaultExpandLevel: 2,
    allowMultiSelect: false
  },
  actionConfig: {
    allowAdd: true,
    allowEdit: true,
    allowDelete: true
  }
}

/**
 * 只读模式配置模板
 */
export const ReadOnlyTreeConfig = {
  displayConfig: {
    showHeader: false,
    showSearch: true,
    showAddButton: false,
    showEditIcons: false,
    showIcon: false,
    isSetWidth: true
  },
  interactionConfig: {
    selectable: true,
    expandable: true,
    lazyLoad: true,
    allowMultiSelect: false
  },
  actionConfig: {
    allowAdd: false,
    allowEdit: false,
    allowDelete: false
  }
}

/**
 * 创建区域树只读配置
 */
export const createRegionReadOnlyConfig = () => {
  return {
    ...RegionTreeConfig,
    displayConfig: {
      ...RegionTreeConfig.displayConfig,
      ...ReadOnlyTreeConfig.displayConfig
    },
    interactionConfig: {
      ...RegionTreeConfig.interactionConfig,
      ...ReadOnlyTreeConfig.interactionConfig
    },
    actionConfig: {
      ...ReadOnlyTreeConfig.actionConfig
    }
  }
}

/**
 * 创建产品分类树只读配置
 */
export const createProductCategoryReadOnlyConfig = () => {
  return {
    ...ProductCategoryTreeConfig,
    displayConfig: {
      ...ProductCategoryTreeConfig.displayConfig,
      ...ReadOnlyTreeConfig.displayConfig
    },
    interactionConfig: {
      ...ProductCategoryTreeConfig.interactionConfig,
      ...ReadOnlyTreeConfig.interactionConfig
    },
    actionConfig: {
      ...ReadOnlyTreeConfig.actionConfig
    }
  }
}

/**
 * 配置预设类型枚举
 */
export const ConfigPresetType = {
  REGION_TREE: 'REGION_TREE',
  PRODUCT_CATEGORY_TREE: 'PRODUCT_CATEGORY_TREE',
  REGION_READONLY: 'REGION_READONLY',
  PRODUCT_CATEGORY_READONLY: 'PRODUCT_CATEGORY_READONLY',
  CUSTOM: 'CUSTOM'
}

/**
 * 获取预设配置
 * @param {string} presetType 预设类型
 * @param {Object} customOptions 自定义选项
 * @returns {TreeConfig} 配置对象
 */
export function getPresetConfig(presetType, customOptions = {}) {
  let baseConfig

  switch (presetType) {
    case ConfigPresetType.REGION_TREE:
      baseConfig = RegionTreeConfig
      break
    case ConfigPresetType.PRODUCT_CATEGORY_TREE:
      baseConfig = ProductCategoryTreeConfig
      break
    case ConfigPresetType.REGION_READONLY:
      baseConfig = createRegionReadOnlyConfig()
      break
    case ConfigPresetType.PRODUCT_CATEGORY_READONLY:
      baseConfig = createProductCategoryReadOnlyConfig()
      break
    default:
      throw new Error(`未知的预设类型: ${presetType}`)
  }

  // 深度合并自定义选项
  return deepMergeConfig(baseConfig, customOptions)
}

/**
 * 创建自定义配置
 * @param {Object} options 配置选项
 * @returns {TreeConfig} 配置对象
 */
export function createCustomConfig(options) {
  const defaultConfig = {
    dataSource: {
      api: null,
      lazyLoadApi: null,
      searchParam: 'searchText',
      parentIdParam: 'parentId'
    },
    fieldMapping: {
      key: 'id',
      title: 'name',
      children: 'children',
      hasChildren: 'hasChildren',
      level: 'level'
    },
    displayConfig: {
      title: '',
      showHeader: true,
      showSearch: true,
      searchPlaceholder: '请输入关键字搜索',
      showAddButton: false,
      showEditIcons: false,
      showIcon: false,
      isSetWidth: true
    },
    interactionConfig: {
      selectable: true,
      expandable: true,
      lazyLoad: false,
      defaultExpandLevel: 1,
      allowMultiSelect: false
    },
    actionConfig: {
      allowAdd: false,
      allowEdit: false,
      allowDelete: false,
      customActions: []
    }
  }

  return deepMergeConfig(defaultConfig, options)
}

/**
 * 深度合并配置对象（从utils导入）
 */
function deepMergeConfig(defaultConfig, userConfig) {
  const result = { ...defaultConfig }
  
  for (const key in userConfig) {
    if (Object.prototype.hasOwnProperty.call(userConfig, key)) {
      const userValue = userConfig[key]
      const defaultValue = defaultConfig[key]
      
      if (userValue !== undefined) {
        if (typeof userValue === 'object' && userValue !== null && !Array.isArray(userValue) &&
            typeof defaultValue === 'object' && defaultValue !== null && !Array.isArray(defaultValue)) {
          result[key] = deepMergeConfig(defaultValue, userValue)
        } else {
          result[key] = userValue
        }
      }
    }
  }
  
  return result
}

/**
 * 配置模板映射
 */
export const ConfigTemplates = {
  [ConfigPresetType.REGION_TREE]: RegionTreeConfig,
  [ConfigPresetType.PRODUCT_CATEGORY_TREE]: ProductCategoryTreeConfig,
  [ConfigPresetType.REGION_READONLY]: createRegionReadOnlyConfig,
  [ConfigPresetType.PRODUCT_CATEGORY_READONLY]: createProductCategoryReadOnlyConfig
}

/**
 * 获取所有可用的预设类型
 * @returns {string[]} 预设类型数组
 */
export function getAvailablePresets() {
  return Object.values(ConfigPresetType)
}

/**
 * 验证预设类型是否有效
 * @param {string} presetType 预设类型
 * @returns {boolean} 是否有效
 */
export function isValidPresetType(presetType) {
  return Object.values(ConfigPresetType).includes(presetType)
}