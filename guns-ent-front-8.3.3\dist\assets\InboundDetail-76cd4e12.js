import{_ as O,L as E,a as s,f as C,w as t,b as r,d as a,t as o,g as d,c as _,h as m,F as N,e as S,Y as F,U as w,Z as z,a0 as B,i as H,a4 as V,v as U,G as q,I as Q,b8 as Y,b9 as K,M as G}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{P as x}from"./PurchaseApi-38cc3d1a.js";const W={name:"InboundDetail",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(u,{emit:T}){const e=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:250},{title:"\u6570\u91CF",key:"quantity",width:120,align:"center"},{title:"\u5355\u4EF7",key:"unitPrice",width:100,align:"right"},{title:"\u603B\u4EF7",key:"totalPrice",width:120,align:"right"},{title:"\u5907\u6CE8",key:"remark",width:200}],l=E(()=>u.data.detailList?u.data.detailList.length:0),M=E(()=>!u.data.detailList||u.data.detailList.length===0?0:u.data.detailList.reduce((n,y)=>n+(parseFloat(y.quantity)||0),0)),k=E(()=>!u.data.detailList||u.data.detailList.length===0?0:u.data.detailList.reduce((n,y)=>n+(parseFloat(y.totalPrice)||0),0)),i=E(()=>M.value===0?0:k.value/M.value),p=n=>x.getPurchaseStatusName(n),A=n=>x.getStatusTagColor(n),g=n=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"})[n]||"default",P=E(()=>u.data.paymentMethod||u.data.paymentAccount||u.data.status==="COMPLETED");return{detailColumns:e,productCount:l,totalQuantity:M,totalAmount:k,averagePrice:i,showPaymentInfo:P,getStatusName:p,getStatusTagColor:A,getBusinessModeColor:g,getPaymentMethodName:n=>({CASH:"\u73B0\u91D1",BANK_TRANSFER:"\u94F6\u884C\u8F6C\u8D26",CHECK:"\u652F\u7968",CREDIT_CARD:"\u4FE1\u7528\u5361",MONTHLY:"\u6708\u7ED3",ALIPAY:"\u652F\u4ED8\u5B9D",WECHAT:"\u5FAE\u4FE1\u652F\u4ED8",CREDIT:"\u8D4A\u8D26"})[n]||n,getPaymentMethodColor:n=>({CASH:"green",BANK_TRANSFER:"blue",CHECK:"orange",CREDIT_CARD:"purple",MONTHLY:"cyan",ALIPAY:"blue",WECHAT:"green",CREDIT:"red"})[n]||"default",getPaymentStatusName:n=>({DRAFT:"\u5F85\u4ED8\u6B3E",CONFIRMED:"\u5F85\u4ED8\u6B3E",COMPLETED:"\u5DF2\u4ED8\u6B3E"})[n]||"\u672A\u77E5",getPaymentStatusColor:n=>({DRAFT:"orange",CONFIRMED:"orange",COMPLETED:"green"})[n]||"default",getQuantityUnit:n=>{switch(n.pricingType){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return n.unit||"\u4E2A"}},formatAmount:n=>n?parseFloat(n).toFixed(2):"0.00",getOperationColor:n=>({CREATE:"blue",EDIT:"orange",CONFIRM:"green",RECEIVE:"purple",CANCEL:"red"})[n]||"default",getOperationIcon:n=>({CREATE:"icon-opt-xinzeng",EDIT:"icon-opt-bianji",CONFIRM:"icon-opt-queren",RECEIVE:"icon-opt-rukudan",CANCEL:"icon-opt-quxiao"})[n]||"icon-opt-caozuo",handleCancel:()=>{T("update:visible",!1)}}}},j={class:"inbound-detail-content"},J={class:"order-no"},Z={key:1,class:"text-muted"},X={key:0,class:"payment-account"},$={key:1,class:"text-muted"},tt={class:"amount-text"},et={class:"amount-text"},at={key:0,class:"product-info"},nt={class:"product-name"},ot={class:"product-details"},lt={class:"product-code"},it={key:0,class:"product-spec"},st={key:3,class:"total-price"},ct={class:"operation-log"},dt={class:"operation-title"},ut={class:"operation-name"},rt={class:"operation-time"},_t={class:"operation-user"},mt={key:0,class:"operation-remark"};function ft(u,T,e,l,M,k){const i=F,p=w,A=z,g=B,P=H,h=V,b=U,I=q,v=Q,L=Y,R=K,D=G;return s(),C(D,{visible:e.visible,title:"\u5165\u5E93\u5355\u8BE6\u60C5",width:1200,footer:null,onCancel:l.handleCancel},{default:t(()=>[r("div",j,[a(g,{title:"\u57FA\u672C\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:t(()=>[a(A,{column:3,bordered:"",size:"small"},{default:t(()=>[a(i,{label:"\u5165\u5E93\u5355\u53F7"},{default:t(()=>[r("span",J,o(e.data.orderNo),1)]),_:1}),a(i,{label:"\u72B6\u6001"},{default:t(()=>[a(p,{color:l.getStatusTagColor(e.data.status)},{default:t(()=>[d(o(e.data.statusName||l.getStatusName(e.data.status)),1)]),_:1},8,["color"])]),_:1}),a(i,{label:"\u8BA2\u5355\u65E5\u671F"},{default:t(()=>[d(o(e.data.orderDate),1)]),_:1}),a(i,{label:"\u4F9B\u5E94\u5546\u540D\u79F0"},{default:t(()=>[d(o(e.data.supplierName),1)]),_:1}),a(i,{label:"\u4F9B\u5E94\u5546\u7F16\u7801"},{default:t(()=>[d(o(e.data.supplierCode),1)]),_:1}),a(i,{label:"\u7ECF\u8425\u65B9\u5F0F"},{default:t(()=>[a(p,{color:l.getBusinessModeColor(e.data.businessMode)},{default:t(()=>[d(o(e.data.businessModeName),1)]),_:1},8,["color"])]),_:1}),a(i,{label:"\u521B\u5EFA\u4EBA"},{default:t(()=>[d(o(e.data.createUserName),1)]),_:1}),a(i,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:t(()=>[d(o(e.data.createTime),1)]),_:1}),a(i,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:t(()=>[d(o(e.data.updateTime),1)]),_:1}),a(i,{label:"\u5907\u6CE8",span:3},{default:t(()=>[d(o(e.data.remark||"\u65E0"),1)]),_:1})]),_:1})]),_:1}),l.showPaymentInfo?(s(),C(g,{key:0,title:"\u4ED8\u6B3E\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:t(()=>[a(A,{column:3,bordered:"",size:"small"},{default:t(()=>[a(i,{label:"\u4ED8\u6B3E\u65B9\u5F0F"},{default:t(()=>[e.data.paymentMethod?(s(),C(p,{key:0,color:l.getPaymentMethodColor(e.data.paymentMethod)},{default:t(()=>[d(o(e.data.paymentMethodName||l.getPaymentMethodName(e.data.paymentMethod)),1)]),_:1},8,["color"])):(s(),_("span",Z,"\u672A\u8BBE\u7F6E"))]),_:1}),a(i,{label:"\u4ED8\u6B3E\u8D26\u6237"},{default:t(()=>[e.data.paymentAccount?(s(),_("span",X,o(e.data.paymentAccount),1)):(s(),_("span",$,"\u672A\u8BBE\u7F6E"))]),_:1}),a(i,{label:"\u4ED8\u6B3E\u72B6\u6001"},{default:t(()=>[a(p,{color:l.getPaymentStatusColor(e.data.status)},{default:t(()=>[d(o(l.getPaymentStatusName(e.data.status)),1)]),_:1},8,["color"])]),_:1}),a(i,{label:"\u5E94\u4ED8\u91D1\u989D"},{default:t(()=>[r("span",tt,"\xA5"+o(l.formatAmount(e.data.totalAmount)),1)]),_:1}),e.data.status==="COMPLETED"?(s(),C(i,{key:0,label:"\u5B9E\u4ED8\u91D1\u989D"},{default:t(()=>[r("span",et,"\xA5"+o(l.formatAmount(e.data.actualPaymentAmount||e.data.totalAmount)),1)]),_:1})):m("",!0),e.data.status==="COMPLETED"&&e.data.paymentTime?(s(),C(i,{key:1,label:"\u4ED8\u6B3E\u65F6\u95F4"},{default:t(()=>[d(o(e.data.paymentTime),1)]),_:1})):m("",!0)]),_:1})]),_:1})):m("",!0),a(g,{title:"\u5546\u54C1\u660E\u7EC6",size:"small",style:{"margin-bottom":"16px"}},{default:t(()=>[a(P,{columns:l.detailColumns,"data-source":e.data.detailList||[],pagination:!1,size:"small",bordered:"",scroll:{x:1e3}},{bodyCell:t(({column:c,record:f})=>[c.key==="productInfo"?(s(),_("div",at,[r("div",nt,o(f.productName),1),r("div",ot,[r("span",lt,o(f.productCode),1),f.specification?(s(),_("span",it,o(f.specification),1)):m("",!0)])])):m("",!0),c.key==="quantity"?(s(),_(N,{key:1},[d(o(f.quantity)+" "+o(l.getQuantityUnit(f)),1)],64)):m("",!0),c.key==="unitPrice"?(s(),_(N,{key:2},[d(" \xA5"+o(l.formatAmount(f.unitPrice)),1)],64)):m("",!0),c.key==="totalPrice"?(s(),_("span",st,"\xA5"+o(l.formatAmount(f.totalPrice)),1)):m("",!0),c.key==="remark"?(s(),_(N,{key:4},[d(o(f.remark||"-"),1)],64)):m("",!0)]),_:1},8,["columns","data-source"])]),_:1}),a(g,{title:"\u6C47\u603B\u4FE1\u606F",size:"small"},{default:t(()=>[a(I,{gutter:16},{default:t(()=>[a(b,{span:6},{default:t(()=>[a(h,{title:"\u5546\u54C1\u79CD\u7C7B",value:l.productCount,suffix:"\u79CD"},null,8,["value"])]),_:1}),a(b,{span:6},{default:t(()=>[a(h,{title:"\u603B\u6570\u91CF",value:l.totalQuantity,suffix:"\u4EF6"},null,8,["value"])]),_:1}),a(b,{span:6},{default:t(()=>[a(h,{title:"\u603B\u91D1\u989D",value:l.totalAmount,prefix:"\xA5",precision:2},null,8,["value"])]),_:1}),a(b,{span:6},{default:t(()=>[a(h,{title:"\u5E73\u5747\u5355\u4EF7",value:l.averagePrice,prefix:"\xA5",precision:2},null,8,["value"])]),_:1})]),_:1})]),_:1}),e.data.operationLogs&&e.data.operationLogs.length>0?(s(),C(g,{key:1,title:"\u64CD\u4F5C\u5386\u53F2",size:"small",style:{"margin-top":"16px"}},{default:t(()=>[a(R,null,{default:t(()=>[(s(!0),_(N,null,S(e.data.operationLogs,c=>(s(),C(L,{key:c.id,color:l.getOperationColor(c.operation)},{dot:t(()=>[a(v,{iconClass:l.getOperationIcon(c.operation)},null,8,["iconClass"])]),default:t(()=>[r("div",ct,[r("div",dt,[r("span",ut,o(c.operationName),1),r("span",rt,o(c.operationTime),1)]),r("div",_t,"\u64CD\u4F5C\u4EBA\uFF1A"+o(c.operatorName),1),c.remark?(s(),_("div",mt,"\u5907\u6CE8\uFF1A"+o(c.remark),1)):m("",!0)])]),_:2},1032,["color"]))),128))]),_:1})]),_:1})):m("",!0)])]),_:1},8,["visible","onCancel"])}const kt=O(W,[["render",ft],["__scopeId","data-v-987baa8b"]]);export{kt as default};
