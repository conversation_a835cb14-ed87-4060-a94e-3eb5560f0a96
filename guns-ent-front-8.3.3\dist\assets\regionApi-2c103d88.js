import{R as a}from"./index-18a1ea24.js";class i{static async findPage(t){return await a.getAndLoadData("/erp/region/page",t)}static async findList(t){return await a.getAndLoadData("/erp/region/list",t)}static async findTree(t){return await a.getAndLoadData("/erp/region/tree",t)}static async findByParentId(t){return await a.getAndLoadData("/erp/region/listByParent",t)}static async findTreeWithLazy(t){return await a.getAndLoadData("/erp/region/treeWithLazy",t)}static async batchDelete(t){return await a.post("/erp/region/batchDelete",t)}static async findSelector(t){return await a.getAndLoadData("/erp/region/selector",t)}static async add(t){return await a.post("/erp/region/add",t)}static async edit(t){return await a.post("/erp/region/edit",t)}static async delete(t){return await a.post("/erp/region/delete",t)}static async detail(t){return await a.getAndLoadData("/erp/region/detail",t)}static async updateStatus(t){return await a.post("/erp/region/updateStatus",t)}static async validateCode(t){return await a.getAndLoadData("/erp/region/validateCode",t)}}export{i as R};
