package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * POS订单请求参数
 *
 * <AUTHOR>
 * @since 2025/08/01 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PosOrderRequest extends BaseRequest {

    /**
     * 订单ID
     */
    @ChineseDescription("订单ID")
    @NotNull(message = "订单ID不能为空", groups = {detail.class, updateStatus.class})
    private Long orderId;

    /**
     * 订单号
     */
    @ChineseDescription("订单号")
    private String orderNo;

    /**
     * 会员ID
     */
    @ChineseDescription("会员ID")
    private Long memberId;

    /**
     * 订单总金额
     */
    @ChineseDescription("订单总金额")
    @NotNull(message = "订单总金额不能为空", groups = {add.class})
    private BigDecimal totalAmount;

    /**
     * 折扣金额
     */
    @ChineseDescription("折扣金额")
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    @ChineseDescription("实付金额")
    @NotNull(message = "实付金额不能为空", groups = {add.class})
    private BigDecimal finalAmount;

    /**
     * 订单状态
     */
    @ChineseDescription("订单状态")
    @NotNull(message = "订单状态不能为空", groups = {updateStatus.class})
    private String orderStatus;

    /**
     * 支付状态
     */
    @ChineseDescription("支付状态")
    private String paymentStatus;

    /**
     * 支付方式
     */
    @ChineseDescription("支付方式")
    private String paymentMethod;

    /**
     * 收银员ID
     */
    @ChineseDescription("收银员ID")
    private Long cashierId;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 订单项列表
     */
    @ChineseDescription("订单项列表")
    @NotEmpty(message = "订单项不能为空", groups = {add.class})
    @Valid
    private List<PosOrderItemRequest> orderItems;

    /**
     * 参数校验分组：新增订单
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑订单
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：查询订单详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：更新订单状态
     */
    public @interface updateStatus {
    }

}