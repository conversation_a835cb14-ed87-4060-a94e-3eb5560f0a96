package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商-区域关联请求参数
 *
 * <AUTHOR>
 * @since 2025/07/22 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpSupplierRegionRequest extends BaseRequest {

    /**
     * 主键ID
     */
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空", groups = {add.class, edit.class, delete.class, detail.class, getSupplierRegions.class, updateSupplierRegions.class})
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 区域ID
     */
    @NotNull(message = "区域ID不能为空", groups = {getSuppliersByRegion.class, countSuppliersByRegion.class})
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 区域ID列表
     */
    @NotEmpty(message = "区域ID列表不能为空", groups = {updateSupplierRegions.class})
    @ChineseDescription("区域ID列表")
    private List<Long> regionIds;

    /**
     * 是否包含子区域
     */
    @ChineseDescription("是否包含子区域")
    private Boolean includeChildRegions = true;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 供应商类型
     */
    @ChineseDescription("供应商类型")
    private String supplierType;

    /**
     * 供应商状态
     */
    @ChineseDescription("供应商状态")
    private String status;

    /**
     * 信用等级
     */
    @ChineseDescription("信用等级")
    private String creditLevel;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：获取供应商关联区域
     */
    public @interface getSupplierRegions {
    }

    /**
     * 参数校验分组：更新供应商关联区域
     */
    public @interface updateSupplierRegions {
    }

    /**
     * 参数校验分组：根据区域ID查询供应商
     */
    public @interface getSuppliersByRegion {
    }

    /**
     * 参数校验分组：统计区域关联供应商数量
     */
    public @interface countSuppliersByRegion {
    }
}