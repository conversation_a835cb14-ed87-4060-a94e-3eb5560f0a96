## 项目jar包加密

### 介绍

jar包加密是将编译好的jar包通过字节码加密，运行加载时候再解密的方式对源代码起到保护作用，被加密后，字节码将不可被反编译

使用插件为：https://gitee.com/core-lib/xjar

### 使用方法

1.任意一个项目，pom中添加依赖如下

```xml
<dependency>
    <groupId>com.github.core-lib</groupId>
    <artifactId>xjar</artifactId>
    <version>4.0.1</version>
</dependency>
```

注意事项：因为xjar不在maven中央仓库，所以在配置maven的settings.xml文件时，要配置`!jitpack.io`，如下：

```xml
<mirror>
    <id>alimaven</id>
    <mirrorOf>*,!jitpack.io</mirrorOf>
    <name>alimaven</name>
    <url>https://maven.aliyun.com/repository/public</url>
</mirror>   
```


2.pom中project标签，增加指定仓库

```xml
<project>
    <!-- 设置 jitpack.io 仓库 -->
    <repositories>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
    </repositories>
</project>
```

3.main方法中运行方法，加密jar包

```java

//  from：    被加密的jar包
//  use：     jar包加密的密码
//  include： 指定要加密的资源，使用classpath路径的的ANT路径表达式
//  exclude： 不加密的资源，使用classpath路径的的ANT路径表达式
//  to:       指定加密后JAR包输出路径, 并执行加密
XCryptos.encryption()
    .from("C:\\Users\\<USER>\\Desktop\\guns-vip-main.jar")
    .use("cn.stylefeng")
    .include("/cn/stylefeng/**/*.class")
    .include("/cn/stylefeng/**/*Mapper.xml")
    .exclude("/assets/**/*")
    .exclude("/pages/**/*")
    .to("C:\\Users\\<USER>\\Desktop\\guns-vip-main-encrypt.jar");
```

4.编译go脚本

这里需要安装下go的环境

没有的话可以从这里下载安装包：https://studygolang.com/dl

第三步生成加密jar后会在同目录下生成一个xjar.go的go语言源码

```shell script
go build xjar.go
```

如果是需要打linux环境的脚本，需要如下命令：

```shell script
SET CGO_ENABLED=0
SET GOOS=linux
SET GOARCH=amd64
go build xjar.go
```

如果是打mac环境的脚本，使用如下命令:

```shell
SET CGO_ENABLED=0
SET GOOS=darwin
SET GOARCH=amd64
go build xjar.go
```

5.启动加密的jar包

```shell script
/path/to/xjar /path/to/java [OPTIONS] -jar /path/to/encrypted.jar [ARGS]
```

例如windows上可以用：
```
C:\Users\<USER>\Desktop\xjar.exe "C:\Program Files\Java\jdk1.8.0_221\bin\java.exe" -jar "C:\Users\<USER>\Desktop\guns-vip-main-encrypt.jar"
```

## license机制

### 介绍

项目交付时候，如果没有发布方授权的license（一串字符），则项目无法启动。
license机制的作用是防止项目被采购方非法使用。license颁发后，只能运行在一台服务器之上，更换服务器需要更换license。

### 生成license

加密和解密license用的是AES非对称加密，所以生成授权license之前要先创建公钥和私钥

1.创建公钥和私钥

```java
RSA rsa = new RSA();

//获得私钥
String privateKeyBase64 = rsa.getPrivateKeyBase64();
System.out.println(privateKeyBase64);

//获得公钥
String publicKeyBase64 = rsa.getPublicKeyBase64();
System.out.println(publicKeyBase64);
```

2.创建license

```java
// 1. 获取被加密机器的mac地址，可以是一个可以多个，所以是一个list
List<String> macList = new ArrayList();
macList.add("9E-B6-D0-C6-D2-43");

// 2. mac地址转为json
String macJson = JSON.toJSONString(macList);

// 3. json字符串加密
RSA rsa = new RSA("这里填写你创建的私钥", "这里填写你创建的公钥");

// 4. 获取加密后的字符串即为license
String encryptBase64 = rsa.encryptBase64(macJson, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);
System.out.println(encryptBase64);
```

3.配置license

创建好的license要配置到项目里，配置到project.license属性里：

例如如下：

```yml
# application.yml中
project:
  license: MoikWfyaCKQEs3NjCJkkijcy631JLktKVRZUHAVooPFE6610Ae22c4jYkHQ4IUnDqsl37bciIgVvHDmZdh4CT1Z06g9+hIVYmHaVIHvue40Zj5BFpItBJnd7al5kk5MxCy71S4wYiVkwJWujrE5yfQDYKncGScMesLLoWkxIChc=
```

4.启动项目成功后，会提示：“项目license校验成功！项目正在启动！”，如果启动失败的话，会提示失败原因





