package cn.stylefeng.roses.kernel.websocket.memory;

import cn.hutool.core.date.DateUtil;
import cn.stylefeng.roses.kernel.sys.api.MessageWebsocketApi;
import cn.stylefeng.roses.kernel.sys.api.pojo.message.MessageSendToSocketDTO;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实现实时发送系统通知消息
 *
 * <AUTHOR>
 * @since 2024-01-15 18:57
 */
@Service
public class MessageSendService implements MessageWebsocketApi {

    @Resource
    private WebSocketManagerApi webSocketManagerApi;

    @Override
    public void wsSendMessage(List<MessageSendToSocketDTO> messageSendToSocketDTOList) {
        for (MessageSendToSocketDTO messageSendToSocketDTO : messageSendToSocketDTOList) {
            Map<String, Object> messageBody = createMessageBody(messageSendToSocketDTO);
            webSocketManagerApi.sendMessageObject(String.valueOf(messageSendToSocketDTO.getReceiveUserId()), "收到系统通知消息", messageBody);
        }
    }

    /**
     * 创建消息发送的body
     *
     * <AUTHOR>
     * @since 2024-01-15 19:12
     */
    private Map<String, Object> createMessageBody(MessageSendToSocketDTO messageSendDTO) {

        Map<String, Object> map = new HashMap<>();
        map.put("messageId", String.valueOf(messageSendDTO.getMessageId()));
        map.put("messageTitle", messageSendDTO.getMessageTitle());
        map.put("messageContent", messageSendDTO.getMessageContent());
        map.put("messageType", messageSendDTO.getMessageType());
        map.put("messageUrl", messageSendDTO.getMessageUrl());
        map.put("priorityLevel", messageSendDTO.getPriorityLevel());
        map.put("businessId", messageSendDTO.getBusinessId());
        map.put("businessType", messageSendDTO.getBusinessType());
        map.put("messageSendTime", DateUtil.formatDateTime(messageSendDTO.getMessageSendTime()));

        return map;
    }

}
