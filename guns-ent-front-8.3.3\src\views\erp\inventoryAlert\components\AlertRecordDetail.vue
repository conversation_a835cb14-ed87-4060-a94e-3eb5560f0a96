<template>
  <a-modal
    title="预警记录详情"
    :visible="visible"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="record-detail-container">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="预警ID">
            {{ recordData.id }}
          </a-descriptions-item>
          <a-descriptions-item label="规则名称">
            {{ recordData.ruleName }}
          </a-descriptions-item>
          <a-descriptions-item label="商品名称">
            {{ recordData.productName }}
          </a-descriptions-item>
          <a-descriptions-item label="商品编码">
            {{ recordData.productCode }}
          </a-descriptions-item>
          <a-descriptions-item label="预警类型">
            <a-tag :color="getAlertTypeColor(recordData.alertType)">
              {{ getAlertTypeText(recordData.alertType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预警级别">
            <a-tag :color="getAlertLevelColor(recordData.alertLevel)">
              {{ getAlertLevelText(recordData.alertLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预警时间">
            {{ formatDateTime(recordData.alertTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="处理状态">
            <a-tag :color="getHandleStatusColor(recordData.handleStatus)">
              {{ getHandleStatusText(recordData.handleStatus) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 库存信息 -->
      <a-card title="库存信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="当前库存">
            <span
              :style="{ 
                color: recordData.currentStock <= recordData.thresholdValue ? '#f5222d' : '#52c41a',
                fontWeight: 'bold'
              }">
              {{ recordData.currentStock }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="最小库存">
            {{ recordData.minStock || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="最大库存">
            {{ recordData.maxStock || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="阈值">
            {{ recordData.thresholdValue }}
          </a-descriptions-item>
          <a-descriptions-item label="阈值类型">
            {{ getThresholdTypeText(recordData.thresholdType) }}
          </a-descriptions-item>
          <a-descriptions-item label="比较操作符">
            {{ getComparisonOperatorText(recordData.comparisonOperator) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 预警详情 -->
      <a-card title="预警详情" size="small" class="detail-card">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="预警消息">
            <div class="alert-message">
              {{ recordData.alertMessage }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="触发条件">
            <div class="trigger-condition">
              当前库存 ({{ recordData.currentStock }}) 
              {{ getComparisonOperatorSymbol(recordData.comparisonOperator) }} 
              阈值 ({{ recordData.thresholdValue }})
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 处理信息 -->
      <a-card 
        v-if="recordData.handleStatus !== 'PENDING'" 
        title="处理信息" 
        size="small" 
        class="detail-card"
      >
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="处理人">
            {{ recordData.handleUserName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="处理时间">
            {{ formatDateTime(recordData.handleTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="处理方式" :span="2">
            <a-tag :color="getHandleTypeColor(recordData.handleType)">
              {{ getHandleTypeText(recordData.handleType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="处理备注" :span="2">
            <div class="handle-remark">
              {{ recordData.handleRemark || '无' }}
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 历史记录 -->
      <a-card title="相关历史记录" size="small" class="detail-card">
        <a-table
          :columns="historyColumns"
          :data-source="historyData"
          :pagination="false"
          :loading="historyLoading"
          size="small"
        >
          <template #alertLevel="{ record }">
            <a-tag :color="getAlertLevelColor(record.alertLevel)">
              {{ getAlertLevelText(record.alertLevel) }}
            </a-tag>
          </template>
          
          <template #handleStatus="{ record }">
            <a-tag :color="getHandleStatusColor(record.handleStatus)">
              {{ getHandleStatusText(record.handleStatus) }}
            </a-tag>
          </template>

          <template #alertTime="{ record }">
            {{ formatDateTime(record.alertTime) }}
          </template>
        </a-table>
      </a-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button
            v-if="recordData.handleStatus === 'PENDING'"
            type="primary"
            @click="handleProcess"
          >
            处理预警
          </a-button>
          <a-button @click="handleCancel">
            关闭
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { ref, watch, onMounted } from 'vue';
import dayjs from 'dayjs';
import { InventoryAlertRecordApi } from '../api/InventoryAlertRecordApi';

export default {
  name: 'AlertRecordDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'process'],
  setup(props, { emit }) {
    const historyData = ref([]);
    const historyLoading = ref(false);

    const historyColumns = [
      {
        title: '预警时间',
        dataIndex: 'alertTime',
        key: 'alertTime',
        width: 150,
        slots: { customRender: 'alertTime' }
      },
      {
        title: '预警级别',
        dataIndex: 'alertLevel',
        key: 'alertLevel',
        width: 100,
        slots: { customRender: 'alertLevel' }
      },
      {
        title: '当前库存',
        dataIndex: 'currentStock',
        key: 'currentStock',
        width: 100
      },
      {
        title: '处理状态',
        dataIndex: 'handleStatus',
        key: 'handleStatus',
        width: 100,
        slots: { customRender: 'handleStatus' }
      },
      {
        title: '预警消息',
        dataIndex: 'alertMessage',
        key: 'alertMessage',
        ellipsis: true
      }
    ];

    const handleCancel = () => {
      emit('update:visible', false);
    };

    const handleProcess = () => {
      emit('process', props.recordData);
    };

    const formatDateTime = (dateTime) => {
      return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '-';
    };

    const getAlertTypeColor = (type) => {
      const colors = {
        'LOW_STOCK': 'orange',
        'ZERO_STOCK': 'red',
        'OVERSTOCK': 'purple',
        'EXPIRY': 'volcano'
      };
      return colors[type] || 'default';
    };

    const getAlertTypeText = (type) => {
      const texts = {
        'LOW_STOCK': '库存不足',
        'ZERO_STOCK': '零库存',
        'OVERSTOCK': '库存积压',
        'EXPIRY': '临期预警'
      };
      return texts[type] || type;
    };

    const getAlertLevelColor = (level) => {
      const colors = {
        'CRITICAL': 'red',
        'WARNING': 'orange',
        'INFO': 'blue'
      };
      return colors[level] || 'default';
    };

    const getAlertLevelText = (level) => {
      const texts = {
        'CRITICAL': '紧急',
        'WARNING': '警告',
        'INFO': '提醒'
      };
      return texts[level] || level;
    };

    const getHandleStatusColor = (status) => {
      const colors = {
        'PENDING': 'orange',
        'RESOLVED': 'green',
        'IGNORED': 'gray'
      };
      return colors[status] || 'default';
    };

    const getHandleStatusText = (status) => {
      const texts = {
        'PENDING': '待处理',
        'RESOLVED': '已解决',
        'IGNORED': '已忽略'
      };
      return texts[status] || status;
    };

    const getHandleTypeColor = (type) => {
      const colors = {
        'RESOLVE': 'green',
        'IGNORE': 'gray'
      };
      return colors[type] || 'default';
    };

    const getHandleTypeText = (type) => {
      const texts = {
        'RESOLVE': '解决',
        'IGNORE': '忽略'
      };
      return texts[type] || type;
    };

    const getThresholdTypeText = (type) => {
      const texts = {
        'QUANTITY': '数量',
        'PERCENTAGE': '百分比',
        'DAYS': '天数'
      };
      return texts[type] || type;
    };

    const getComparisonOperatorText = (operator) => {
      const texts = {
        'LTE': '小于等于',
        'LT': '小于',
        'GTE': '大于等于',
        'GT': '大于',
        'EQ': '等于'
      };
      return texts[operator] || operator;
    };

    const getComparisonOperatorSymbol = (operator) => {
      const symbols = {
        'LTE': '≤',
        'LT': '<',
        'GTE': '≥',
        'GT': '>',
        'EQ': '='
      };
      return symbols[operator] || operator;
    };

    const fetchHistoryData = async () => {
      if (!props.recordData.productId) return;
      
      try {
        historyLoading.value = true;
        const response = await InventoryAlertRecordApi.getHistory({
          productId: props.recordData.productId,
          limit: 10
        });
        historyData.value = response.data || [];
      } catch (error) {
        console.error('获取历史记录失败:', error);
      } finally {
        historyLoading.value = false;
      }
    };

    watch(() => props.visible, (newVal) => {
      if (newVal && props.recordData.id) {
        fetchHistoryData();
      }
    });

    return {
      historyData,
      historyLoading,
      historyColumns,
      handleCancel,
      handleProcess,
      formatDateTime,
      getAlertTypeColor,
      getAlertTypeText,
      getAlertLevelColor,
      getAlertLevelText,
      getHandleStatusColor,
      getHandleStatusText,
      getHandleTypeColor,
      getHandleTypeText,
      getThresholdTypeText,
      getComparisonOperatorText,
      getComparisonOperatorSymbol
    };
  }
};
</script>

<style scoped>
.record-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.alert-message,
.handle-remark {
  white-space: pre-wrap;
  word-break: break-word;
}

.trigger-condition {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.action-buttons {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>
