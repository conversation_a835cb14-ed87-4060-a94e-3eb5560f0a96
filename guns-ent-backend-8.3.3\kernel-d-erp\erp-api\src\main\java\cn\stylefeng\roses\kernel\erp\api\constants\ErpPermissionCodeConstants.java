package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * ERP模块权限编码常量
 * <p>
 * 用于ERP模块各个Controller的@ApiResource注解权限控制标识
 *
 * <AUTHOR>
 * @since 2025/07/24 16:30
 */
public interface ErpPermissionCodeConstants {

    // =============================================
    // 模块级别权限编码（用于Controller类级别）
    // =============================================

    /**
     * 供应商管理界面整体权限
     */
    String ERP_SUPPLIER_MANAGE = "ERP_SUPPLIER_MANAGE";

    /**
     * 客户管理界面整体权限
     */
    String ERP_CUSTOMER_MANAGE = "ERP_CUSTOMER_MANAGE";

    /**
     * 商品管理界面整体权限
     */
    String ERP_PRODUCT_MANAGE = "ERP_PRODUCT_MANAGE";

    /**
     * 区域管理界面整体权限
     */
    String ERP_REGION_MANAGE = "ERP_REGION_MANAGE";

    /**
     * 产品分类管理界面整体权限
     */
    String ERP_PRODUCT_CATEGORY_MANAGE = "ERP_PRODUCT_CATEGORY_MANAGE";

    /**
     * 库存管理界面整体权限
     */
    String ERP_INVENTORY_MANAGE = "ERP_INVENTORY_MANAGE";

    /**
     * 采购入库管理界面整体权限
     */
    String ERP_PURCHASE_ORDER_MANAGE = "ERP_PURCHASE_ORDER_MANAGE";

    /**
     * 库存预警管理界面整体权限
     */
    String ERP_INVENTORY_ALERT_MANAGE = "ERP_INVENTORY_ALERT_MANAGE";

    // =============================================
    // 供应商管理权限编码
    // =============================================

    /**
     * 新增供应商
     */
    String ADD_SUPPLIER = "ADD_SUPPLIER";

    /**
     * 修改供应商
     */
    String EDIT_SUPPLIER = "EDIT_SUPPLIER";

    /**
     * 删除供应商
     */
    String DELETE_SUPPLIER = "DELETE_SUPPLIER";

    /**
     * 批量删除供应商
     */
    String BATCH_DELETE_SUPPLIER = "BATCH_DELETE_SUPPLIER";

    /**
     * 查看供应商详情
     */
    String VIEW_SUPPLIER_DETAIL = "VIEW_SUPPLIER_DETAIL";

    /**
     * 分页查询供应商
     */
    String PAGE_SUPPLIER = "PAGE_SUPPLIER";

    /**
     * 查询供应商列表
     */
    String LIST_SUPPLIER = "LIST_SUPPLIER";

    /**
     * 更新供应商状态
     */
    String UPDATE_SUPPLIER_STATUS = "UPDATE_SUPPLIER_STATUS";

    /**
     * 导出供应商
     */
    String EXPORT_SUPPLIER = "EXPORT_SUPPLIER";

    // =============================================
    // 客户管理权限编码
    // =============================================

    /**
     * 新增客户
     */
    String ADD_CUSTOMER = "ADD_CUSTOMER";

    /**
     * 修改客户
     */
    String EDIT_CUSTOMER = "EDIT_CUSTOMER";

    /**
     * 删除客户
     */
    String DELETE_CUSTOMER = "DELETE_CUSTOMER";

    /**
     * 批量删除客户
     */
    String BATCH_DELETE_CUSTOMER = "BATCH_DELETE_CUSTOMER";

    /**
     * 查看客户详情
     */
    String VIEW_CUSTOMER_DETAIL = "VIEW_CUSTOMER_DETAIL";

    /**
     * 分页查询客户
     */
    String PAGE_CUSTOMER = "PAGE_CUSTOMER";

    /**
     * 查询客户列表
     */
    String LIST_CUSTOMER = "LIST_CUSTOMER";

    /**
     * 更新客户状态
     */
    String UPDATE_CUSTOMER_STATUS = "UPDATE_CUSTOMER_STATUS";

    /**
     * 导出客户
     */
    String EXPORT_CUSTOMER = "EXPORT_CUSTOMER";

    // =============================================
    // 商品管理权限编码
    // =============================================

    /**
     * 新增商品
     */
    String ADD_PRODUCT = "ADD_PRODUCT";

    /**
     * 修改商品
     */
    String EDIT_PRODUCT = "EDIT_PRODUCT";

    /**
     * 删除商品
     */
    String DELETE_PRODUCT = "DELETE_PRODUCT";

    /**
     * 批量删除商品
     */
    String BATCH_DELETE_PRODUCT = "BATCH_DELETE_PRODUCT";

    /**
     * 查看商品详情
     */
    String VIEW_PRODUCT_DETAIL = "VIEW_PRODUCT_DETAIL";

    /**
     * 分页查询商品
     */
    String PAGE_PRODUCT = "PAGE_PRODUCT";

    /**
     * 查询商品列表
     */
    String LIST_PRODUCT = "LIST_PRODUCT";

    /**
     * 更新商品状态
     */
    String UPDATE_PRODUCT_STATUS = "UPDATE_PRODUCT_STATUS";

    /**
     * 导出商品
     */
    String EXPORT_PRODUCT = "EXPORT_PRODUCT";

    // =============================================
    // 区域管理权限编码
    // =============================================

    /**
     * 新增区域
     */
    String ADD_REGION = "ADD_REGION";

    /**
     * 修改区域
     */
    String EDIT_REGION = "EDIT_REGION";

    /**
     * 删除区域
     */
    String DELETE_REGION = "DELETE_REGION";

    /**
     * 查看区域详情
     */
    String VIEW_REGION_DETAIL = "VIEW_REGION_DETAIL";

    /**
     * 分页查询区域
     */
    String PAGE_REGION = "PAGE_REGION";

    /**
     * 查询区域列表
     */
    String LIST_REGION = "LIST_REGION";

    /**
     * 查询区域树形结构
     */
    String TREE_REGION = "TREE_REGION";

    /**
     * 更新区域状态
     */
    String UPDATE_REGION_STATUS = "UPDATE_REGION_STATUS";

    /**
     * 导出区域
     */
    String EXPORT_REGION = "EXPORT_REGION";

    // =============================================
    // 产品分类管理权限编码
    // =============================================

    /**
     * 新增产品分类
     */
    String ADD_PRODUCT_CATEGORY = "ADD_PRODUCT_CATEGORY";

    /**
     * 编辑产品分类
     */
    String EDIT_PRODUCT_CATEGORY = "EDIT_PRODUCT_CATEGORY";

    /**
     * 删除产品分类
     */
    String DELETE_PRODUCT_CATEGORY = "DELETE_PRODUCT_CATEGORY";

    /**
     * 批量删除产品分类
     */
    String BATCH_DELETE_PRODUCT_CATEGORY = "BATCH_DELETE_PRODUCT_CATEGORY";

    /**
     * 查看产品分类详情
     */
    String VIEW_PRODUCT_CATEGORY_DETAIL = "VIEW_PRODUCT_CATEGORY_DETAIL";

    /**
     * 查询产品分类列表
     */
    String LIST_PRODUCT_CATEGORY = "LIST_PRODUCT_CATEGORY";

    /**
     * 查询产品分类树形结构
     */
    String TREE_PRODUCT_CATEGORY = "TREE_PRODUCT_CATEGORY";

    /**
     * 导出产品分类
     */
    String EXPORT_PRODUCT_CATEGORY = "EXPORT_PRODUCT_CATEGORY";

    // =============================================
    // 库存管理权限编码
    // =============================================

    /**
     * 查看库存详情
     */
    String VIEW_INVENTORY_DETAIL = "VIEW_INVENTORY_DETAIL";

    /**
     * 分页查询库存
     */
    String PAGE_INVENTORY = "PAGE_INVENTORY";

    /**
     * 查询库存列表
     */
    String LIST_INVENTORY = "LIST_INVENTORY";

    /**
     * 查询预警库存
     */
    String LIST_WARNING_INVENTORY = "LIST_WARNING_INVENTORY";

    /**
     * 查询缺货商品
     */
    String LIST_OUT_OF_STOCK = "LIST_OUT_OF_STOCK";

    /**
     * 查询库存价值统计
     */
    String VIEW_INVENTORY_VALUE = "VIEW_INVENTORY_VALUE";

    /**
     * 设置库存预警值
     */
    String SET_MIN_STOCK = "SET_MIN_STOCK";

    /**
     * 库存调整
     */
    String ADJUST_INVENTORY = "ADJUST_INVENTORY";

    /**
     * 初始化库存
     */
    String INIT_INVENTORY = "INIT_INVENTORY";

    /**
     * 查询库存历史
     */
    String VIEW_INVENTORY_HISTORY = "VIEW_INVENTORY_HISTORY";

    /**
     * 导出库存
     */
    String EXPORT_INVENTORY = "EXPORT_INVENTORY";

    // =============================================
    // 采购入库管理权限编码
    // =============================================

    /**
     * 新增采购入库单
     */
    String ADD_PURCHASE_ORDER = "ADD_PURCHASE_ORDER";

    /**
     * 修改采购入库单
     */
    String EDIT_PURCHASE_ORDER = "EDIT_PURCHASE_ORDER";

    /**
     * 删除采购入库单
     */
    String DELETE_PURCHASE_ORDER = "DELETE_PURCHASE_ORDER";

    /**
     * 查看采购入库单详情
     */
    String VIEW_PURCHASE_ORDER_DETAIL = "VIEW_PURCHASE_ORDER_DETAIL";

    /**
     * 分页查询采购入库单
     */
    String PAGE_PURCHASE_ORDER = "PAGE_PURCHASE_ORDER";

    /**
     * 查询采购入库单列表
     */
    String LIST_PURCHASE_ORDER = "LIST_PURCHASE_ORDER";

    /**
     * 确认采购入库单
     */
    String CONFIRM_PURCHASE_ORDER = "CONFIRM_PURCHASE_ORDER";

    /**
     * 采购入库操作
     */
    String RECEIVE_PURCHASE_ORDER = "RECEIVE_PURCHASE_ORDER";

    /**
     * 导出采购入库单
     */
    String EXPORT_PURCHASE_ORDER = "EXPORT_PURCHASE_ORDER";

    // =============================================
    // 库存预警管理权限编码
    // =============================================

    /**
     * 新增预警规则
     */
    String ADD_INVENTORY_ALERT_RULE = "ADD_INVENTORY_ALERT_RULE";

    /**
     * 修改预警规则
     */
    String EDIT_INVENTORY_ALERT_RULE = "EDIT_INVENTORY_ALERT_RULE";

    /**
     * 删除预警规则
     */
    String DELETE_INVENTORY_ALERT_RULE = "DELETE_INVENTORY_ALERT_RULE";

    /**
     * 批量删除预警规则
     */
    String BATCH_DELETE_INVENTORY_ALERT_RULE = "BATCH_DELETE_INVENTORY_ALERT_RULE";

    /**
     * 查看预警规则详情
     */
    String VIEW_INVENTORY_ALERT_RULE_DETAIL = "VIEW_INVENTORY_ALERT_RULE_DETAIL";

    /**
     * 分页查询预警规则
     */
    String PAGE_INVENTORY_ALERT_RULE = "PAGE_INVENTORY_ALERT_RULE";

    /**
     * 更新预警规则状态
     */
    String UPDATE_INVENTORY_ALERT_RULE_STATUS = "UPDATE_INVENTORY_ALERT_RULE_STATUS";

    /**
     * 测试预警规则
     */
    String TEST_INVENTORY_ALERT_RULE = "TEST_INVENTORY_ALERT_RULE";

    /**
     * 手动执行预警检查
     */
    String EXECUTE_INVENTORY_ALERT_CHECK = "EXECUTE_INVENTORY_ALERT_CHECK";

    /**
     * 查看预警记录详情
     */
    String VIEW_INVENTORY_ALERT_RECORD_DETAIL = "VIEW_INVENTORY_ALERT_RECORD_DETAIL";

    /**
     * 分页查询预警记录
     */
    String PAGE_INVENTORY_ALERT_RECORD = "PAGE_INVENTORY_ALERT_RECORD";

    /**
     * 处理预警记录
     */
    String HANDLE_INVENTORY_ALERT_RECORD = "HANDLE_INVENTORY_ALERT_RECORD";

    /**
     * 批量处理预警记录
     */
    String BATCH_HANDLE_INVENTORY_ALERT_RECORD = "BATCH_HANDLE_INVENTORY_ALERT_RECORD";

    /**
     * 忽略预警记录
     */
    String IGNORE_INVENTORY_ALERT_RECORD = "IGNORE_INVENTORY_ALERT_RECORD";

    /**
     * 查看预警统计
     */
    String VIEW_INVENTORY_ALERT_STATISTICS = "VIEW_INVENTORY_ALERT_STATISTICS";

    /**
     * 导出预警记录
     */
    String EXPORT_INVENTORY_ALERT_RECORD = "EXPORT_INVENTORY_ALERT_RECORD";

    /**
     * 管理预警配置
     */
    String MANAGE_INVENTORY_ALERT_CONFIG = "MANAGE_INVENTORY_ALERT_CONFIG";

}
