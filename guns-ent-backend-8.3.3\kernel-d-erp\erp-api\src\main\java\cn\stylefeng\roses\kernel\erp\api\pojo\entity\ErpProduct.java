package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品主档案实体类
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
@TableName(value = "erp_product", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ErpProduct extends BaseEntity {

    /**
     * 商品ID
     */
    @TableId(value = "product_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @TableField("product_code")
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @TableField("product_name")
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品简称
     */
    @TableField("product_short_name")
    @ChineseDescription("商品简称")
    private String productShortName;

    /**
     * 条形码
     */
    @TableField("barcode")
    @ChineseDescription("条形码")
    private String barcode;

    /**
     * 商品分类ID
     */
    @TableField("category_id")
    @ChineseDescription("商品分类ID")
    private Long categoryId;

    /**
     * 品牌
     */
    @TableField("brand")
    @ChineseDescription("品牌")
    private String brand;

    /**
     * 规格
     */
    @TableField("specification")
    @ChineseDescription("规格")
    private String specification;

    /**
     * 基本单位
     */
    @TableField("unit")
    @ChineseDescription("基本单位")
    private String unit;

    /**
     * 重量（kg）
     */
    @TableField("weight")
    @ChineseDescription("重量")
    private BigDecimal weight;

    /**
     * 体积（立方米）
     */
    @TableField("volume")
    @ChineseDescription("体积")
    private BigDecimal volume;

    /**
     * 保质期（天）
     */
    @TableField("shelf_life")
    @ChineseDescription("保质期")
    private Integer shelfLife;

    /**
     * 存储条件
     */
    @TableField("storage_condition")
    @ChineseDescription("存储条件")
    private String storageCondition;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，DISCONTINUED-停产）
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 供应商ID
     */
    @TableField("supplier_id")
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 计价类型：NORMAL(普通)、WEIGHT(计重)、PIECE(计件)、VARIABLE(不定价)
     */
    @TableField("pricing_type")
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 零售价格
     */
    @TableField("retail_price")
    @ChineseDescription("零售价格")
    private BigDecimal retailPrice;

    /**
     * 单位价格（计重商品用）
     */
    @TableField("unit_price")
    @ChineseDescription("单位价格")
    private BigDecimal unitPrice;

    /**
     * 单份价格（计件商品用）
     */
    @TableField("piece_price")
    @ChineseDescription("单份价格")
    private BigDecimal piecePrice;

    /**
     * 参考价格（不定价商品用）
     */
    @TableField("reference_price")
    @ChineseDescription("参考价格")
    private BigDecimal referencePrice;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;

}
