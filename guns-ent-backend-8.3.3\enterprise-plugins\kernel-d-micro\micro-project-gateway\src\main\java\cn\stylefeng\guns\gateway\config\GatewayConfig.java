package cn.stylefeng.guns.gateway.config;

import cn.stylefeng.guns.gateway.core.balancer.CustomBlockingLoadBalancerClient;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * 网关配置
 *
 * <AUTHOR>
 * @since 2023/8/8 23:27
 */
@Configuration
public class GatewayConfig {

    @Resource
    private LoadBalancerClientFactory loadBalancerClientFactory;

    /**
     * 修复调用问题
     *
     * <AUTHOR>
     * @since 2023/8/8 23:27
     */
    @Bean
    public LoadBalancerClient blockingLoadBalancerClient() {
        return new CustomBlockingLoadBalancerClient(loadBalancerClientFactory);
    }

}
