import{_ as q}from"./index-02bf6f00.js";import{r as h,o as P,k as S,a as i,c as k,b as a,d as t,w as o,g as r,t as W,h as l,F as T,f as c,M as L,E as N,m as v,n as G,B as H,I as J,p as K,q as Q,D as X,l as Y,U as Z}from"./index-18a1ea24.js";/* empty css              */import{_ as ee,N as w}from"./notice-add-edit-a73eac6d.js";/* empty css              *//* empty css              *//* empty css              */import"./notice-form-d80b9e4c.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./OrgApi-021dd6dd.js";/* empty css              */const te={class:"guns-layout"},oe={class:"guns-layout-content"},ne={class:"guns-layout"},se={class:"guns-layout-content-application"},ie={class:"content-mian"},le={class:"content-mian-header"},ae={class:"header-content"},ue={class:"header-content-left"},ce={class:"header-content-right"},de={class:"content-mian-body"},re={class:"table-content"},_e=["onClick"],Se=Object.assign({name:"BackendNotice"},{__name:"index",setup(pe){const B=h([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:n})=>u.value.tableIndex+n},{dataIndex:"noticeTitle",title:"\u901A\u77E5\u6807\u9898",ellipsis:!0,width:200,isShow:!0},{dataIndex:"priorityLevel",title:"\u4F18\u5148\u7EA7",width:100,isShow:!0},{dataIndex:"publishStatus",title:"\u53D1\u5E03\u72B6\u6001",ellipsis:!0,width:100,isShow:!0},{dataIndex:"noticeBeginTime",title:"\u5F00\u59CB\u65F6\u95F4",width:100,isShow:!0},{dataIndex:"noticeEndTime",title:"\u7ED3\u675F\u65F6\u95F4",width:150,isShow:!0},{dataIndex:"createUserWrapper",title:"\u521B\u5EFA\u4EBA",width:150,isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:150,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}]),u=h(null),y=h({searchText:""}),x=h(null),f=h(!1);P(()=>{});const E=({key:n})=>{n=="1"&&D()},_=()=>{u.value.reload()},g=n=>{x.value=n,f.value=!0},z=n=>{L.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6570\u636E\u5417?",icon:t(N),maskClosable:!0,onOk:async()=>{const e=await w.delete({noticeId:n.noticeId});v.success(e.message),_()}})},D=()=>{if(u.value.selectedRowList&&u.value.selectedRowList.length==0)return v.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u6570\u636E");L.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6570\u636E\u5417?",icon:t(N),maskClosable:!0,onOk:async()=>{const n=await w.batchDelete({batchDeleteIdList:u.value.selectedRowList});v.success(n.message),_()}})},$=n=>{u.value.tableLoading=!0,w.publishNotice({noticeId:n.noticeId}).then(e=>{v.success(e.message),_()}).finally(()=>u.value.tableLoading=!1)},R=n=>{u.value.tableLoading=!0,w.retractNotice({noticeId:n.noticeId}).then(e=>{v.success(e.message),_()}).finally(()=>u.value.tableLoading=!1)};return(n,e)=>{const C=G,O=S("plus-outlined"),I=H,p=J,M=K,U=Q,V=S("small-dash-outlined"),A=X,F=Y,m=Z,j=q;return i(),k("div",te,[a("div",oe,[a("div",ne,[a("div",se,[a("div",ie,[a("div",le,[a("div",ae,[a("div",ue,[t(C,{size:16})]),a("div",ce,[t(C,{size:16},{default:o(()=>[t(I,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=d=>g())},{default:o(()=>[t(O),e[3]||(e[3]=r("\u65B0\u5EFA"))]),_:1,__:[3]}),t(A,null,{overlay:o(()=>[t(U,{onClick:E},{default:o(()=>[a("div",null,[t(M,{key:"1"},{default:o(()=>[t(p,{iconClass:"icon-opt-shanchu",color:"#60666b"}),e[4]||(e[4]=a("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[4]})])]),_:1})]),default:o(()=>[t(I,{class:"border-radius"},{default:o(()=>[e[5]||(e[5]=r(" \u66F4\u591A ")),t(V)]),_:1,__:[5]})]),_:1})]),_:1})])])]),a("div",de,[a("div",re,[t(j,{columns:B.value,where:y.value,rowId:"noticeId",ref_key:"tableRef",ref:u,url:"/sysNotice/page",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"NOTICE_TABLE"},{toolLeft:o(()=>[t(F,{value:y.value.searchText,"onUpdate:value":e[1]||(e[1]=d=>y.value.searchText=d),placeholder:"\u901A\u77E5\u6807\u9898\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:_,class:"search-input",bordered:!1},{prefix:o(()=>[t(p,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:o(({column:d,record:s})=>[d.dataIndex=="noticeTitle"?(i(),k("a",{key:0,onClick:b=>g(s)},W(s.noticeTitle),9,_e)):l("",!0),d.dataIndex=="priorityLevel"?(i(),k(T,{key:1},[s.priorityLevel=="high"?(i(),c(m,{key:0,color:"red"},{default:o(()=>e[6]||(e[6]=[r("\u9AD8")])),_:1,__:[6]})):l("",!0),s.priorityLevel=="middle"?(i(),c(m,{key:1,color:"orange"},{default:o(()=>e[7]||(e[7]=[r("\u4E2D")])),_:1,__:[7]})):l("",!0),s.priorityLevel=="low"?(i(),c(m,{key:2,color:"blue"},{default:o(()=>e[8]||(e[8]=[r("\u4F4E")])),_:1,__:[8]})):l("",!0)],64)):l("",!0),d.dataIndex=="publishStatus"?(i(),k(T,{key:2},[s.publishStatus==1?(i(),c(m,{key:0,color:"green"},{default:o(()=>e[9]||(e[9]=[r("\u5DF2\u53D1\u5E03")])),_:1,__:[9]})):l("",!0),s.publishStatus==2?(i(),c(m,{key:1,color:"orange"},{default:o(()=>e[10]||(e[10]=[r("\u672A\u53D1\u5E03")])),_:1,__:[10]})):l("",!0)],64)):l("",!0),d.key=="action"?(i(),c(C,{key:3,size:16},{default:o(()=>[s.publishStatus==2?(i(),c(p,{key:0,iconClass:"icon-opt-bianji","font-size":"24px",color:"#60666b",title:"\u7F16\u8F91",onClick:b=>g(s)},null,8,["onClick"])):l("",!0),s.publishStatus==2?(i(),c(p,{key:1,iconClass:"icon-opt-fabu","font-size":"24px",color:"#60666b",title:"\u53D1\u5E03",onClick:b=>$(s)},null,8,["onClick"])):l("",!0),s.publishStatus==1?(i(),c(p,{key:2,iconClass:"icon-opt-chehui","font-size":"24px",color:"#60666b",title:"\u64A4\u56DE",onClick:b=>R(s)},null,8,["onClick"])):l("",!0),t(p,{iconClass:"icon-opt-shanchu","font-size":"24px",color:"#60666b",title:"\u5220\u9664",onClick:b=>z(s)},null,8,["onClick"])]),_:2},1024)):l("",!0)]),_:1},8,["columns","where"])])])])])])]),f.value?(i(),c(ee,{key:0,visible:f.value,"onUpdate:visible":e[2]||(e[2]=d=>f.value=d),data:x.value,onDone:_},null,8,["visible","data"])):l("",!0)])}}});export{Se as default};
