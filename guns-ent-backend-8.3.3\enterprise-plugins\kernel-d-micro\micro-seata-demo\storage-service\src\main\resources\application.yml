server:
  port: 18082
  max-http-header-size: 10240

spring:
  application:
    name: storage-service
  profiles:
    active: local
  servlet:
    multipart:
      max-request-size: 200MB
      max-file-size: 200MB
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    locale: zh_CN
    serialization:
      indent_output: false
  flyway:
    enabled: true
    locations: classpath:db/migration/mysql
    # 当迁移时发现目标schema非空，而且带有没有元数据的表时，是否自动执行基准迁移
    baseline-on-migrate: true
    # 是否允许无序的迁移 开发环境最好开启, 生产环境关闭
    out-of-order: true
    # 关闭占位符替换，因为插入的sql里边可能包含${}关键字
    placeholder-replacement: false
  main:
    allow-circular-references: true

# 资源扫描
scanner:
  open: false

# 日志记录
sys-log:
  # db-数据库，file-文件
  type: db
  file-save-path: _sys_logs

mybatis-plus:
  mapper-locations: classpath*:cn/stylefeng/**/mapping/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
  global-config:
    banner: false
    enable-sql-runner: true
    db-config:
      id-type: assign_id
      table-underline: true
