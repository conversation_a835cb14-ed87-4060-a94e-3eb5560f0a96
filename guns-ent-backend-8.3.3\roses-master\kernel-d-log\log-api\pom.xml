<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-log</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>log-api</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--sys系统模块-->
        <!--用来获取userId的包装转化-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--参数校验模块-->
        <!--包含带参数校验注解的类-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>validator-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库模块 api-->
        <!--分页查询需要用到PageResult相关类-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web-->
        <!--LogRecordFactory快速创建http类的日志参数会用到-->
        <!--如果不要记录当前请求的http接口信息，就不用本模块，所以optional=true-->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

    </dependencies>

</project>
