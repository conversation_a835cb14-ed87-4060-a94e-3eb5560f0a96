package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.erp.api.exception.InventoryAlertExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRuleRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRuleResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper.InventoryAlertRuleMapper;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.InventoryAlertRuleService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存预警规则服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Slf4j
@Service
public class InventoryAlertRuleServiceImpl extends ServiceImpl<InventoryAlertRuleMapper, InventoryAlertRule>
        implements InventoryAlertRuleService {

    @Resource
    private InventoryAlertRuleMapper inventoryAlertRuleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(InventoryAlertRuleRequest request) {
        // 验证规则参数
        validateRuleParams(request);
        
        // 检查规则名称是否重复
        if (isRuleNameDuplicate(request.getRuleName(), null)) {
            throw new ServiceException(InventoryAlertExceptionEnum.RULE_NAME_DUPLICATE);
        }

        InventoryAlertRule entity = new InventoryAlertRule();
        BeanUtil.copyProperties(request, entity);
        entity.setIsEnabled("Y");
        entity.setDelFlag("N");
        
        // 设置默认值
        if (entity.getCheckFrequency() == null) {
            entity.setCheckFrequency(30); // 默认30分钟检查一次
        }
        if (entity.getNotificationMethods() == null) {
            entity.setNotificationMethods("SYSTEM");
        }
        
        this.save(entity);
        log.info("新增预警规则成功，规则名称：{}", request.getRuleName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        
        // 验证规则参数
        validateRuleParams(request);
        
        // 检查规则名称是否重复
        if (isRuleNameDuplicate(request.getRuleName(), request.getId())) {
            throw new ServiceException(InventoryAlertExceptionEnum.RULE_NAME_DUPLICATE);
        }

        BeanUtil.copyProperties(request, entity);
        this.updateById(entity);
        log.info("编辑预警规则成功，规则ID：{}", request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        
        // 检查规则是否正在使用中
        // TODO: 检查是否有未处理的预警记录
        
        entity.setDelFlag("Y");
        this.updateById(entity);
        log.info("删除预警规则成功，规则ID：{}", request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(InventoryAlertRuleRequest request) {
        List<Long> idList = request.getIdList();
        if (CollUtil.isNotEmpty(idList)) {
            List<InventoryAlertRule> entities = this.listByIds(idList);
            entities.forEach(entity -> entity.setDelFlag("Y"));
            this.updateBatchById(entities);
            log.info("批量删除预警规则成功，删除数量：{}", idList.size());
        }
    }

    @Override
    public InventoryAlertRuleResponse detail(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        InventoryAlertRuleResponse response = new InventoryAlertRuleResponse();
        BeanUtil.copyProperties(entity, response);
        return response;
    }

    @Override
    public PageResult<InventoryAlertRuleResponse> findPage(InventoryAlertRuleRequest request) {
        // 设置默认分页参数，避免空指针异常
        Integer pageNo = request.getPageNo() != null ? request.getPageNo() : 1;
        Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 10;

        Page<InventoryAlertRuleResponse> page = new Page<>(pageNo, pageSize);
        Page<InventoryAlertRuleResponse> result = inventoryAlertRuleMapper.selectRulePage(page, request);
        return PageResultFactory.createPageResult(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.queryInventoryAlertRule(request);
        entity.setIsEnabled(request.getIsEnabled());
        this.updateById(entity);
        log.info("更新预警规则状态成功，规则ID：{}，状态：{}", request.getId(), request.getIsEnabled());
    }

    @Override
    public List<Object> testRule(InventoryAlertRuleRequest request) {
        InventoryAlertRule rule = this.queryInventoryAlertRule(request);
        
        // TODO: 实现预警规则测试逻辑
        // 根据规则条件查询符合条件的库存数据
        // 返回测试结果
        
        List<Object> testResults = new ArrayList<>();
        log.info("测试预警规则，规则ID：{}", request.getId());
        return testResults;
    }

    @Override
    public List<InventoryAlertRule> findEnabledRules() {
        return inventoryAlertRuleMapper.selectEnabledRules();
    }

    @Override
    public List<InventoryAlertRule> findByProductId(Long productId) {
        return inventoryAlertRuleMapper.selectByProductId(productId);
    }

    @Override
    public List<InventoryAlertRule> findByCategoryId(Long categoryId) {
        return inventoryAlertRuleMapper.selectByCategoryId(categoryId);
    }

    @Override
    public void validateRuleParams(InventoryAlertRuleRequest request) {
        // 验证目标类型和目标ID的匹配性
        if ("PRODUCT".equals(request.getTargetType()) || "CATEGORY".equals(request.getTargetType())) {
            if (request.getTargetId() == null) {
                throw new ServiceException(InventoryAlertExceptionEnum.TARGET_NOT_FOUND);
            }
        }
        
        // 验证阈值配置
        if (request.getThresholdValue() == null || request.getThresholdValue().compareTo(java.math.BigDecimal.ZERO) < 0) {
            throw new ServiceException(InventoryAlertExceptionEnum.THRESHOLD_CONFIG_ERROR);
        }
        
        // 验证预警类型和阈值类型的匹配性
        if ("EXPIRY".equals(request.getRuleType()) && !"DAYS".equals(request.getThresholdType())) {
            throw new ServiceException(InventoryAlertExceptionEnum.THRESHOLD_CONFIG_ERROR);
        }
    }

    @Override
    public boolean isRuleNameDuplicate(String ruleName, Long excludeId) {
        int count = inventoryAlertRuleMapper.countByRuleName(ruleName, excludeId);
        return count > 0;
    }

    /**
     * 查询预警规则
     */
    private InventoryAlertRule queryInventoryAlertRule(InventoryAlertRuleRequest request) {
        InventoryAlertRule entity = this.getById(request.getId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new ServiceException(InventoryAlertExceptionEnum.RULE_NOT_FOUND);
        }
        return entity;
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<InventoryAlertRule> createWrapper(InventoryAlertRuleRequest request) {
        LambdaQueryWrapper<InventoryAlertRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InventoryAlertRule::getDelFlag, "N");
        
        if (ObjectUtil.isNotEmpty(request.getSearchText())) {
            queryWrapper.like(InventoryAlertRule::getRuleName, request.getSearchText());
        }
        
        if (ObjectUtil.isNotEmpty(request.getRuleTypeFilter())) {
            queryWrapper.eq(InventoryAlertRule::getRuleType, request.getRuleTypeFilter());
        }
        
        if (ObjectUtil.isNotEmpty(request.getAlertLevelFilter())) {
            queryWrapper.eq(InventoryAlertRule::getAlertLevel, request.getAlertLevelFilter());
        }
        
        if (ObjectUtil.isNotEmpty(request.getIsEnabledFilter())) {
            queryWrapper.eq(InventoryAlertRule::getIsEnabled, request.getIsEnabledFilter());
        }
        
        queryWrapper.orderByDesc(InventoryAlertRule::getCreateTime);
        return queryWrapper;
    }
}
