import n from"./list-51145fbc.js";import u from"./detail-d04228d8.js";import{r,a as o,c as l,f as a,h as p}from"./index-18a1ea24.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              */import"./SysDictTypeApi-1ce2cbe7.js";const m={class:"guns-layout"},U={__name:"index",setup(c){const e=r("list"),t=r(""),i=s=>{e.value=s.type,t.value=s.businessLogId};return(s,d)=>(o(),l("div",m,[e.value==="list"?(o(),a(n,{key:0,onUpdateType:i})):p("",!0),e.value==="detail"&&t.value?(o(),a(u,{key:1,"business-log-id":t.value,onUpdateType:i},null,8,["business-log-id"])):p("",!0)]))}};export{U as default};
