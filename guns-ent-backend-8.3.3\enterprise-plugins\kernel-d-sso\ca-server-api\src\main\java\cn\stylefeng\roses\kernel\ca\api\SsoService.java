package cn.stylefeng.roses.kernel.ca.api;

import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoDetectionRequest;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoLoginRequest;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoLogoutRequest;

/**
 * 单点登录的业务api
 *
 * <AUTHOR>
 * @date 2021/1/21 14:52
 */
public interface SsoService {

    /**
     * SSO认证检测
     * <p>
     * 判断请求过来的用户在统一认证中心是否有会话如果全局会话存在，redirect到客户端应用
     * <p>
     * 如果会话不存在，则跳转登录界面
     *
     * @param ssoDetectionRequest 单点的请求
     * @return 单点之后的url
     * <AUTHOR>
     * @date 2021/1/21 14:53
     */
    String detection(SsoDetectionRequest ssoDetectionRequest);

    /**
     * 通过ssoLoginCode进行单点登录
     *
     * @param ssoLoginRequest 参数封装
     * @return redirect给客户端应用的url
     * <AUTHOR>
     * @date 2021/1/28 13:56
     */
    String activateByLoginCode(SsoLoginRequest ssoLoginRequest);

    /**
     * 单点登录退出
     *
     * @param ssoLogoutRequest 参数封装
     * @return redirect给客户端应用的url
     * <AUTHOR>
     * @date 2021/1/28 13:56
     */
    String ssoLogout(SsoLogoutRequest ssoLogoutRequest);

}
