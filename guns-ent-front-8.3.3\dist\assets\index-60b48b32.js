import{_ as ee,r as v,L as le,X as D,o as ne,aL as te,m as M,a as _,c as k,d as g,w as m,b as C,f as ae,aM as E,at as L,aN as oe,h as A,a1 as re,a2 as se,ac as ie,t as ce,g as P,B as de,l as ue,aO as ge,a5 as fe,S as he,n as pe}from"./index-18a1ea24.js";/* empty css              */import{R as j}from"./regionApi-2c103d88.js";const ve={class:"region-selector"},me={class:"region-selector-suffix"},ye={class:"region-selector-content"},_e={key:0,class:"search-box"},Se={class:"tree-content"},ke={key:0,class:"tree-wrapper"},Ie={class:"tree-node-title"},Ce={key:1,class:"empty-content"},Ae={key:1,class:"action-buttons"},xe={__name:"index",props:{modelValue:{type:[Array,String,Number],default:()=>[]},multiple:{type:Boolean,default:!0},placeholder:{type:String,default:"\u8BF7\u9009\u62E9\u533A\u57DF"},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},showSearch:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:"\u641C\u7D22\u533A\u57DF\u540D\u79F0"},emptyText:{type:String,default:"\u6682\u65E0\u533A\u57DF\u6570\u636E"},showActionButtons:{type:Boolean,default:!1},checkStrictly:{type:Boolean,default:!0},customApi:{type:Function,default:null},customLazyApi:{type:Function,default:null},extraParams:{type:Object,default:()=>({})},fieldNames:{type:Object,default:()=>({children:"children",title:"regionName",key:"regionId",value:"regionId"})}},emits:["update:modelValue","change","search","clear"],setup(i,{expose:U,emit:F}){const a=i,c=F,f=v(!1),x=v(!1),y=v(""),u=v([]),d=v([]),S=v([]),s=v([]),b=v(!1),R=le(()=>{var l;return s.value.length===0?"":a.multiple?s.value.map(e=>e.regionName).join(", "):((l=s.value[0])==null?void 0:l.regionName)||""});D(()=>a.modelValue,(l,e)=>{!l||Array.isArray(l)&&l.length===0?(s.value=[],d.value=[]):u.value.length>0&&N(l)},{immediate:!0}),D(u,l=>{l.length>0&&a.modelValue&&(Array.isArray(a.modelValue)?a.modelValue.length>0:a.modelValue)&&N(a.modelValue)},{immediate:!1}),ne(()=>{I(),document.addEventListener("click",V)}),te(()=>{document.removeEventListener("click",V),s.value=[],d.value=[],S.value=[],u.value=[]});const V=l=>{!l.target.closest(".region-selector")&&f.value&&(f.value=!1)},N=l=>{if(s.value=[],d.value=[],!(!l||Array.isArray(l)&&l.length===0))try{if(a.multiple){const e=Array.isArray(l)?l:[l];if(e.length>0){const n=e.map(t=>String(t)).filter(t=>t&&t!=="undefined"&&t!=="null");if(n.length>0){const t=T(n);s.value=t,d.value=n}}}else if(l){const e=String(l);if(e&&e!=="undefined"&&e!=="null"){const n=T([e]);s.value=n}}}catch(e){console.error("\u521D\u59CB\u5316\u9009\u4E2D\u533A\u57DF\u5931\u8D25:",e),s.value=[],d.value=[]}},T=l=>{if(!l||l.length===0)return[];const e=[],n=(t,o)=>{for(const r of t)o.includes(String(r.regionId))&&e.push({regionId:r.regionId,regionName:r.regionName,regionCode:r.regionCode,regionLevel:r.regionLevel}),r.children&&r.children.length>0&&n(r.children,o)};return u.value.length>0&&n(u.value,l),e},I=async()=>{x.value=!0;try{const l=a.customApi||j.findTree,e={searchText:y.value,...a.extraParams},n=await l(e);u.value=B(n),y.value?S.value=H(n):S.value=K(n,3),a.modelValue&&(Array.isArray(a.modelValue)?a.modelValue.length>0:a.modelValue)&&N(a.modelValue)}catch(l){console.error("\u52A0\u8F7D\u533A\u57DF\u6570\u636E\u5931\u8D25:",l),M.error("\u52A0\u8F7D\u533A\u57DF\u6570\u636E\u5931\u8D25")}finally{x.value=!1}},B=l=>!l||!Array.isArray(l)?[]:l.map(e=>{const n={...e,isLeaf:!e.children||e.children.length===0,regionId:String(e.regionId||e.id||""),regionName:e.regionName||e.name||e.title||"",regionCode:e.regionCode||e.code||"",regionLevel:e.regionLevel||e.level||0,key:String(e.regionId||e.id||""),title:e.regionName||e.name||e.title||"",value:String(e.regionId||e.id||"")};return e.children&&e.children.length>0&&(n.children=B(e.children),n.isLeaf=!1),n}),H=l=>{const e=[],n=t=>{t.forEach(o=>{e.push(o.regionId),o.children&&o.children.length>0&&n(o.children)})};return n(l),e},K=(l,e)=>{const n=[],t=(o,r=1)=>{o.forEach(p=>{r<=e&&n.push(p.regionId),p.children&&p.children.length>0&&r<e&&t(p.children,r+1)})};return t(l),n},w=l=>{if(!a.multiple)return;const e=[],n=(t,o)=>{for(const r of t){const p=String(r.regionId);o.includes(p)&&e.push({regionId:r.regionId,regionName:r.regionName,regionCode:r.regionCode,regionLevel:r.regionLevel}),r.children&&r.children.length>0&&n(r.children,o)}};n(u.value,l),s.value=e},W=()=>{!a.disabled&&!a.readonly&&(f.value=!0)},X=()=>{!a.disabled&&!a.readonly&&(f.value=!0)},O=()=>{s.value=[],d.value=[],c("update:modelValue",a.multiple?[]:null),c("clear"),c("change",a.multiple?[]:null,[])},q=()=>{I(),c("search",y.value)},G=()=>{y.value||I()},J=(l,e)=>{if(b.value=!0,a.multiple){let n=[];Array.isArray(l)?n=l:l&&typeof l=="object"?l.checked&&Array.isArray(l.checked)?n=l.checked:l.checkedKeys&&Array.isArray(l.checkedKeys)?n=l.checkedKeys:n=Object.keys(l):n=[];const t=n.map(o=>String(o));d.value=t,w(t),c("update:modelValue",t),c("change",t,s.value)}setTimeout(()=>{b.value=!1},0)},Q=(l,e)=>{if(!b.value){if(a.multiple){if(e.node){const n=String(e.node.key||e.node.regionId),t=[...d.value];if(e.selected){if(!t.includes(n)){const o=[...t,n];d.value=o,w(o),c("update:modelValue",o),c("change",o,s.value)}}else if(t.includes(n)){const o=t.filter(r=>r!==n);d.value=o,w(o),c("update:modelValue",o),c("change",o,s.value)}}}else if(!a.multiple&&l.length>0){const n=e.selectedNodes[0];s.value=[n],c("update:modelValue",l[0]),c("change",l[0],n),f.value=!1}}},Y=()=>{f.value=!1};U({resetState:()=>{s.value=[],d.value=[],S.value=[],f.value=!1,y.value=""},clearSelection:O,getSelectedRegions:()=>s.value,reloadData:I});const Z=()=>{var e;const l=a.multiple?d.value:((e=s.value[0])==null?void 0:e.regionId)||null;c("update:modelValue",l),c("change",l,s.value),f.value=!1},$=async l=>{const e=a.customLazyApi||j.findTreeWithLazy,n={parentId:l.dataRef.regionId,...a.extraParams};try{const t=await e(n);l.dataRef.children=B(t),u.value=[...u.value]}catch(t){console.error("\u61D2\u52A0\u8F7D\u533A\u57DF\u6570\u636E\u5931\u8D25:",t),M.error("\u52A0\u8F7D\u5B50\u533A\u57DF\u5931\u8D25")}};return(l,e)=>{const n=de,t=ue,o=ge,r=fe,p=he,z=pe;return _(),k("div",ve,[g(t,{value:R.value,"onUpdate:value":e[0]||(e[0]=h=>R.value=h),placeholder:i.placeholder,disabled:i.disabled,readonly:i.readonly,class:"region-selector-input",onFocus:W,onClick:X},{suffix:m(()=>[C("div",me,[i.clearable&&s.value.length>0&&!i.disabled&&!i.readonly?(_(),ae(n,{key:0,type:"text",size:"small",class:"clear-btn",onClick:E(O,["stop"])},{icon:m(()=>[g(L(oe))]),_:1})):A("",!0),g(L(re),{class:se(["dropdown-icon",{"dropdown-icon-open":f.value}])},null,8,["class"])])]),_:1},8,["value","placeholder","disabled","readonly"]),f.value?(_(),k("div",{key:0,class:"region-selector-dropdown",onClick:e[4]||(e[4]=E(()=>{},["stop"]))},[C("div",ye,[i.showSearch?(_(),k("div",_e,[g(t,{value:y.value,"onUpdate:value":e[1]||(e[1]=h=>y.value=h),placeholder:i.searchPlaceholder,allowClear:"",onPressEnter:q,onChange:G},{prefix:m(()=>[g(L(ie))]),_:1},8,["value","placeholder"])])):A("",!0),C("div",Se,[g(p,{spinning:x.value},{default:m(()=>[u.value&&u.value.length>0?(_(),k("div",ke,[g(o,{checkedKeys:d.value,"onUpdate:checkedKeys":e[2]||(e[2]=h=>d.value=h),expandedKeys:S.value,"onUpdate:expandedKeys":e[3]||(e[3]=h=>S.value=h),"tree-data":u.value,checkable:i.multiple,selectable:!0,checkStrictly:i.checkStrictly,"load-data":$,fieldNames:i.fieldNames,onCheck:J,onSelect:Q},{title:m(({title:h,key:Ne,dataRef:Be})=>[C("span",Ie,ce(h),1)]),_:1},8,["checkedKeys","expandedKeys","tree-data","checkable","checkStrictly","fieldNames"])])):(_(),k("div",Ce,[g(r,{description:i.emptyText},null,8,["description"])]))]),_:1},8,["spinning"])]),i.showActionButtons?(_(),k("div",Ae,[g(z,null,{default:m(()=>[g(n,{size:"small",onClick:Y},{default:m(()=>e[5]||(e[5]=[P("\u53D6\u6D88")])),_:1,__:[5]}),g(n,{type:"primary",size:"small",onClick:Z},{default:m(()=>e[6]||(e[6]=[P("\u786E\u5B9A")])),_:1,__:[6]})]),_:1})])):A("",!0)])])):A("",!0)])}}},Ve=ee(xe,[["__scopeId","data-v-8764992c"]]);export{Ve as R};
