System.register(["./index-legacy-ee1db0c7.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./TenantApi-legacy-40853d2f.js","./time-util-legacy-296598c1.js"],(function(e,a){"use strict";var t,l,n,i,r,d,u,o,s,c,v,p,m,g,f,y,b,h,_,x,k,w,D,Y,L,I,T,U,C,H;return{setters:[e=>{t=e._,l=e.r,n=e.s,i=e.o,r=e.k,d=e.a,u=e.f,o=e.w,s=e.d,c=e.b,v=e.g,p=e.c,m=e.F,g=e.e,f=e.t,y=e.m,b=e.cj,h=e.al,_=e.u,x=e.v,k=e.B,w=e.W,D=e.J,Y=e.I,L=e.G,I=e.H,T=e.M},null,null,e=>{U=e.T},e=>{C=e.g,H=e.G}],execute:function(){var a=document.createElement("style");a.textContent=".company[data-v-ebb55fa9]{border-left:4px solid var(--primary-color);padding-left:10px;margin-bottom:20px}\n",document.head.appendChild(a);const M={style:{"margin-bottom":"10px"}},j={__name:"tenant-approver",props:{visible:Boolean,approverList:Array},emits:["update:visible","done"],setup(e,{emit:a}){const t=e,j=a,F=l(!1),E=l({tenantIdList:t.approverList,activeDate:C()+" 00:00:00",expireDate:H(C(),1)+" 00:00:00",tenantLinkList:[]}),N=n({activeDate:[{required:!0,message:"请选择租户生效时间",type:"string",trigger:"change"}],expireDate:[{required:!0,message:"请选择到期时间",type:"string",trigger:"change"}]}),V=l(null),q=l(null),B=l([]);i((()=>{R()}));const R=()=>{U.tenantPackageList().then((e=>{B.value=e.data}))},z=e=>{j("update:visible",e)},A=async()=>{V.value.validate().then((async e=>{e&&(F.value=!0,U.auditTenant(E.value).then((async e=>{F.value=!1,y.success(e.message),z(!1),j("done")})).catch((()=>{F.value=!1})))}))},G=()=>{E.value.tenantLinkList.push({trialFlag:"N",packageId:null,serviceEndTime:""})};return(e,a)=>{const l=h,n=_,i=x,y=k,U=r("vxe-column"),C=w,H=D,j=r("vxe-switch"),R=Y,O=r("vxe-table"),S=L,J=I,K=T;return d(),u(K,{width:900,maskClosable:!1,visible:t.visible,"confirm-loading":F.value,forceRender:!0,title:"租户审批","body-style":{paddingBottom:"8px"},"onUpdate:visible":z,onOk:A,onClose:a[2]||(a[2]=e=>z(!1))},{default:o((()=>[s(J,{ref_key:"formRef",ref:V,model:E.value,rules:N,layout:"vertical"},{default:o((()=>[s(S,{gutter:20},{default:o((()=>[s(i,{span:12},{default:o((()=>[s(n,{label:"租户生效时间:",name:"activeDate"},{default:o((()=>[s(l,{value:E.value.activeDate,"onUpdate:value":a[0]||(a[0]=e=>E.value.activeDate=e),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择租户生效时间",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),s(i,{span:12},{default:o((()=>[s(n,{label:"到期时间:",name:"expireDate"},{default:o((()=>[s(l,{value:E.value.expireDate,"onUpdate:value":a[1]||(a[1]=e=>E.value.expireDate=e),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择到期时间",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),s(i,{span:24},{default:o((()=>a[3]||(a[3]=[c("div",{class:"company"},"开通功能",-1)]))),_:1,__:[3]}),s(i,{span:24},{default:o((()=>[c("div",M,[s(y,{type:"primary",class:"border-radius",onClick:G},{default:o((()=>a[4]||(a[4]=[v("+ 添加功能")]))),_:1,__:[4]})]),s(O,{border:"",style:{"margin-bottom":"20px"},"show-overflow":"",data:E.value.tenantLinkList,"row-config":{useKey:!0},"column-config":{resizable:!0},height:"300",ref_key:"xTableRef",ref:q},{default:o((()=>[s(U,{type:"seq",width:"60",title:"序号",align:"center"}),s(U,{field:"packageId",title:"功能包",align:"center"},{default:o((({row:e})=>[s(H,{value:e.packageId,"onUpdate:value":a=>e.packageId=a,style:{width:"100%","border-radius":"4px","text-align":"left"},placeholder:"请选择功能包"},{default:o((()=>[(d(!0),p(m,null,g(B.value,(e=>(d(),u(C,{value:e.packageId,key:e.packageId},{default:o((()=>[v(f(e.packageName),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value"])])),_:1}),s(U,{field:"serviceEndTime",title:"到期时间",width:"200",align:"center"},{default:o((({row:e})=>[s(l,{value:e.serviceEndTime,"onUpdate:value":a=>e.serviceEndTime=a,"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择到期时间",style:{width:"100%","border-radius":"4px"}},null,8,["value","onUpdate:value"])])),_:1}),s(U,{field:"trialFlag",title:"是否试用",width:"100",align:"center"},{default:o((({row:e})=>[s(j,{modelValue:e.trialFlag,"onUpdate:modelValue":a=>e.trialFlag=a,"open-value":"Y","close-value":"N"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(U,{title:"操作",width:"100",align:"center"},{default:o((({row:e})=>[s(R,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:a=>(async e=>{"confirm"===await b.modal.confirm("您确定要删除该数据?")&&q.value.remove(e);const a=q.value.getTableData().tableData;E.value.tenantLinkList=a})(e)},null,8,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading"])}}};e("default",t(j,[["__scopeId","data-v-ebb55fa9"]]))}}}));
