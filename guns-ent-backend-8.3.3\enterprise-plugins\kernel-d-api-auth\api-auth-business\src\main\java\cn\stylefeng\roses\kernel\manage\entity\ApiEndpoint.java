package cn.stylefeng.roses.kernel.manage.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API资源接口列表实例类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@TableName("ent_api_endpoint")
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiEndpoint extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "api_client_resource_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键id")
    private Long apiClientResourceId;

    /**
     * 资源编码，与sys_resource表编码对应
     */
    @TableField("resource_code")
    @ChineseDescription("资源编码，与sys_resource表编码对应")
    private String resourceCode;

    /**
     * 接口名称
     */
    @TableField(exist = false)
    @ChineseDescription("接口名称")
    private String resourceName;

    /**
     * 资源url
     */
    @TableField(exist = false)
    @ChineseDescription("资源url")
    private String url;

    /**
     * http请求方法
     */
    @TableField(exist = false)
    @ChineseDescription("http请求方法")
    private String httpMethod;

}
