<template>
  <div class="icon-manager">
    <a-card title="JavaGuns Enterprise ERP 图标管理工具" :bordered="false">
      
      <!-- 搜索区域 -->
      <div class="search-section">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input
              v-model:value="searchKeyword"
              placeholder="搜索图标名称或描述"
              @input="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </a-col>
          <a-col :span="8">
            <a-select
              v-model:value="selectedCategory"
              placeholder="选择图标分类"
              style="width: 100%"
              @change="handleCategoryChange"
            >
              <a-select-option value="">全部分类</a-select-option>
              <a-select-option value="OPERATION">操作类图标</a-select-option>
              <a-select-option value="MENU">菜单类图标</a-select-option>
              <a-select-option value="USER">用户类图标</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="8">
            <a-button type="primary" @click="checkMissingIcon">
              检查缺失图标
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总图标数" :value="totalIcons" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="操作类图标" :value="operationIcons" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="菜单类图标" :value="menuIcons" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="用户类图标" :value="userIcons" />
          </a-col>
        </a-row>
      </div>

      <!-- 图标展示区域 -->
      <div class="icons-section">
        <a-row :gutter="[16, 16]">
          <a-col 
            v-for="icon in filteredIcons" 
            :key="icon.iconClass"
            :span="6"
          >
            <a-card 
              size="small" 
              :hoverable="true"
              class="icon-card"
              @click="copyIconClass(icon.iconClass)"
            >
              <div class="icon-display">
                <icon-font 
                  :iconClass="icon.iconClass" 
                  font-size="32px" 
                  color="#1890ff"
                />
              </div>
              <div class="icon-info">
                <div class="icon-class">{{ icon.iconClass }}</div>
                <div class="icon-description">{{ icon.description }}</div>
                <div class="icon-category">{{ getCategoryName(icon.category) }}</div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 缺失图标检查弹窗 -->
      <a-modal
        v-model:visible="showMissingModal"
        title="缺失图标检查"
        width="800px"
        :footer="null"
      >
        <div class="missing-check-section">
          <a-input
            v-model:value="missingIconInput"
            placeholder="输入可能缺失的图标类名，如：icon-opt-chakan"
            @pressEnter="checkSpecificIcon"
          >
            <template #suffix>
              <a-button type="link" @click="checkSpecificIcon">
                检查
              </a-button>
            </template>
          </a-input>
          
          <div v-if="iconSuggestion" class="suggestion-result">
            <a-alert
              :type="iconSuggestion.recommendation.exists ? 'success' : 'warning'"
              :message="iconSuggestion.recommendation.exists ? '图标存在' : '图标不存在'"
              :description="getSuggestionDescription()"
              show-icon
            />
            
            <div v-if="!iconSuggestion.recommendation.exists" class="alternatives">
              <h4>推荐替代方案：</h4>
              
              <!-- IconFont 替代方案 -->
              <div v-if="iconSuggestion.recommendation.iconFont" class="alternative-option">
                <h5>IconFont 图标：</h5>
                <a-card size="small">
                  <div class="alternative-display">
                    <icon-font 
                      :iconClass="iconSuggestion.recommendation.iconFont" 
                      font-size="24px" 
                      color="#1890ff"
                    />
                    <span>{{ iconSuggestion.recommendation.iconFont }}</span>
                    <a-button 
                      type="link" 
                      size="small"
                      @click="copyIconClass(iconSuggestion.recommendation.iconFont)"
                    >
                      复制
                    </a-button>
                  </div>
                </a-card>
              </div>
              
              <!-- Ant Design 替代方案 -->
              <div v-if="iconSuggestion.recommendation.antIcon" class="alternative-option">
                <h5>Ant Design 图标：</h5>
                <a-card size="small">
                  <div class="alternative-display">
                    <component 
                      :is="iconSuggestion.recommendation.antIcon" 
                      style="font-size: 24px; color: #1890ff;"
                    />
                    <span>{{ iconSuggestion.recommendation.antIcon }}</span>
                    <a-button 
                      type="link" 
                      size="small"
                      @click="copyAntIcon(iconSuggestion.recommendation.antIcon)"
                    >
                      复制代码
                    </a-button>
                  </div>
                </a-card>
              </div>
            </div>
          </div>
        </div>
      </a-modal>

      <!-- 使用说明 -->
      <div class="usage-guide">
        <a-collapse>
          <a-collapse-panel key="1" header="图标使用指南">
            <div class="guide-content">
              <h4>1. 图标使用方法</h4>
              <pre><code>&lt;icon-font iconClass="icon-opt-bianji" font-size="24px" color="#60666b" /&gt;</code></pre>
              
              <h4>2. Ant Design 图标使用方法</h4>
              <pre><code>&lt;EditOutlined style="font-size: 24px; color: #60666b;" /&gt;</code></pre>
              
              <h4>3. 图标命名规范</h4>
              <ul>
                <li><code>icon-opt-*</code>: 操作类图标（编辑、删除、查看等）</li>
                <li><code>icon-menu-*</code>: 菜单类图标</li>
                <li><code>icon-nav-*</code>: 导航类图标</li>
                <li><code>icon-tab-*</code>: 标签页图标</li>
              </ul>
              
              <h4>4. 图标缺失处理流程</h4>
              <ol>
                <li>使用本工具检查图标是否存在</li>
                <li>如果不存在，选择推荐的替代图标</li>
                <li>优先使用 IconFont 图标，其次使用 Ant Design 图标</li>
                <li>如需新增图标，请联系UI设计师添加到图标库</li>
              </ol>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </a-card>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons-vue';
import { 
  getAllAvailableIcons, 
  searchIcons, 
  generateIconSuggestion,
  AVAILABLE_ICONS 
} from '@/views/extension/icon-manager/iconManager';

export default {
  name: 'IconManager',
  components: {
    SearchOutlined,
    EditOutlined,
    DeleteOutlined,
    EyeOutlined
  },
  setup() {
    const searchKeyword = ref('');
    const selectedCategory = ref('');
    const showMissingModal = ref(false);
    const missingIconInput = ref('');
    const iconSuggestion = ref(null);
    
    const allIcons = ref([]);
    
    // 计算属性
    const filteredIcons = computed(() => {
      let icons = allIcons.value;
      
      // 按分类过滤
      if (selectedCategory.value) {
        icons = icons.filter(icon => icon.category === selectedCategory.value);
      }
      
      // 按关键词过滤
      if (searchKeyword.value) {
        icons = searchIcons(searchKeyword.value);
      }
      
      return icons;
    });
    
    const totalIcons = computed(() => allIcons.value.length);
    const operationIcons = computed(() => Object.keys(AVAILABLE_ICONS.OPERATION).length);
    const menuIcons = computed(() => Object.keys(AVAILABLE_ICONS.MENU).length);
    const userIcons = computed(() => Object.keys(AVAILABLE_ICONS.USER).length);
    
    // 方法
    const handleSearch = () => {
      // 搜索逻辑已在计算属性中处理
    };
    
    const handleCategoryChange = () => {
      // 分类变更逻辑已在计算属性中处理
    };
    
    const checkMissingIcon = () => {
      showMissingModal.value = true;
    };
    
    const checkSpecificIcon = () => {
      if (!missingIconInput.value.trim()) {
        message.warning('请输入图标类名');
        return;
      }
      
      iconSuggestion.value = generateIconSuggestion(missingIconInput.value.trim());
    };
    
    const copyIconClass = (iconClass) => {
      navigator.clipboard.writeText(iconClass).then(() => {
        message.success(`已复制图标类名: ${iconClass}`);
      });
    };
    
    const copyAntIcon = (antIcon) => {
      const code = `<${antIcon} style="font-size: 24px; color: #60666b;" />`;
      navigator.clipboard.writeText(code).then(() => {
        message.success(`已复制Ant Design图标代码`);
      });
    };
    
    const getCategoryName = (category) => {
      const names = {
        'OPERATION': '操作类',
        'MENU': '菜单类',
        'USER': '用户类'
      };
      return names[category] || category;
    };
    
    const getSuggestionDescription = () => {
      if (!iconSuggestion.value) return '';
      
      if (iconSuggestion.value.recommendation.exists) {
        return `图标 ${iconSuggestion.value.missingIcon} 在系统中存在，可以正常使用。`;
      } else {
        return `图标 ${iconSuggestion.value.missingIcon} 不存在，建议使用以下替代方案：`;
      }
    };
    
    // 生命周期
    onMounted(() => {
      allIcons.value = getAllAvailableIcons();
    });
    
    return {
      searchKeyword,
      selectedCategory,
      showMissingModal,
      missingIconInput,
      iconSuggestion,
      filteredIcons,
      totalIcons,
      operationIcons,
      menuIcons,
      userIcons,
      handleSearch,
      handleCategoryChange,
      checkMissingIcon,
      checkSpecificIcon,
      copyIconClass,
      copyAntIcon,
      getCategoryName,
      getSuggestionDescription
    };
  }
};
</script>

<style scoped>
.icon-manager {
  padding: 24px;
}

.search-section {
  margin-bottom: 24px;
}

.stats-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.icons-section {
  margin-bottom: 24px;
}

.icon-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon-display {
  margin-bottom: 12px;
}

.icon-info {
  font-size: 12px;
}

.icon-class {
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 4px;
}

.icon-description {
  color: #666;
  margin-bottom: 4px;
}

.icon-category {
  color: #999;
  font-size: 11px;
}

.missing-check-section {
  margin-bottom: 16px;
}

.suggestion-result {
  margin-top: 16px;
}

.alternatives {
  margin-top: 16px;
}

.alternative-option {
  margin-bottom: 16px;
}

.alternative-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.usage-guide {
  margin-top: 24px;
}

.guide-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  color: #1890ff;
}

.guide-content pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.guide-content ul, .guide-content ol {
  margin-left: 20px;
}

.guide-content li {
  margin-bottom: 4px;
}
</style>
