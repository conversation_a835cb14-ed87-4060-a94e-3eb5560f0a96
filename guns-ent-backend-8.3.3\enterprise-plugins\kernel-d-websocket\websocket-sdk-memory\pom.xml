<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-websocket</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>websocket-sdk-memory</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--WebSocket通信api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>websocket-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--系统管理的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
