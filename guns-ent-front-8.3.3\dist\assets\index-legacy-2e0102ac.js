System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-dba03026.js","./CommonApi-legacy-1cfbfce8.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var a,u,n,t,d,i,s,o,v,r,c,p,y,f,g;return{setters:[e=>{a=e.r,u=e.o,n=e.X,t=e.a,d=e.c,i=e.d,s=e.ah,o=e.w,v=e.f,r=e.h,c=e.l,p=e.bf,y=e.a0},null,e=>{f=e._},e=>{g=e.C},null,null,null,null,null,null,null],execute:function(){const l={class:"wh100"},m={__name:"index",props:{value:{type:[String,Array],default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"请选择"},isJson:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},width:{type:String,default:"100%"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null}},emits:["update:value","onChange"],setup(e,{emit:y}){const m=e,h=y,b=a(!0),j=a(!1),x=a([]),w=a(""),U=a(!1),C=a({selectUserList:[]});u((()=>{var e;null!==(e=m.record)&&void 0!==e&&e.itemMultipleChoiceFlag||m.multiple?b.value=!1:b.value=!0,_()}));const _=async()=>{if(m.isJson)if(m.value)if(b.value){if(j.value)return;const e=await g.getUserName({userId:m.value});e.data&&(x.value=[{id:m.value,name:e.data}])}else x.value=m.normal?x.value:JSON.parse(m.value);else x.value=[];else x.value=m.value?m.value:[];j.value=!1,w.value=x.value.map((e=>e.name)).join("；")},B=()=>{var e,l;null!==(e=x.value)&&void 0!==e&&e.length?C.value.selectUserList=null===(l=x.value)||void 0===l?void 0:l.map((e=>({bizId:e.id,name:e.name}))):C.value.selectUserList=[],U.value=!0},O=e=>{var l;x.value=null===(l=e.selectUserList)||void 0===l?void 0:l.map((e=>({id:e.bizId,name:e.name}))),w.value=e.selectUserList.map((e=>e.name)).join("；"),j.value=!0,S()},S=()=>{if(m.isJson){let e=x.value.length>0?b.value?x.value[0].id:m.normal?x.value:JSON.stringify(x.value):"";h("update:value",e),h("onChange",m.record),F()}else h("update:value",x.value)},F=async()=>{var e;!m.normal&&null!==(e=m.formRef)&&void 0!==e&&e.validateFields&&await m.formRef.validateFields([m.record.fieldCode])};return n((()=>m.value),(e=>{_()}),{deep:!0}),(a,u)=>{const n=c,y=f,g=p;return t(),d("div",l,[i(n,{value:w.value,"onUpdate:value":u[0]||(u[0]=e=>w.value=e),disabled:m.readonly||m.disabled,class:"w-full",style:s({width:m.width}),placeholder:e.placeholder,onFocus:B},null,8,["value","disabled","style","placeholder"]),i(g,null,{default:o((()=>[U.value?(t(),v(y,{key:0,visible:U.value,"onUpdate:visible":u[1]||(u[1]=e=>U.value=e),data:C.value,showTab:["user"],changeHeight:!0,title:"人员选择",isRadio:b.value,onDone:O},null,8,["visible","data","isRadio"])):r("",!0)])),_:1})])}}},h={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const l=a(""),u=a(!1),n=a(!1),s=a("请选择"),v=()=>{console.log(l.value)};return(e,a)=>{const r=m,c=y;return t(),d("div",h,[i(c,{title:"人员选择",bordered:!1},{default:o((()=>[i(r,{value:l.value,"onUpdate:value":a[0]||(a[0]=e=>l.value=e),disabled:u.value,readonly:n.value,onOnChange:v,placeholder:s.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])])),_:1})])}}})}}}));
