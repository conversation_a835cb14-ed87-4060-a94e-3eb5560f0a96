package cn.stylefeng.roses.ent.mobile.manage.service;

import cn.stylefeng.roses.ent.mobile.manage.pojo.config.ChangePhoneRequest;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.SendPhoneCodeRequest;

/**
 * 移动端系统配置的业务
 *
 * <AUTHOR>
 * @since 2024/3/24 23:02
 */
public interface MobileSystemConfigService {

    /**
     * 发送修改手机号的短信验证码
     *
     * <AUTHOR>
     * @since 2024/3/24 23:02
     */
    void sendChangePhoneCode(SendPhoneCodeRequest sendPhoneCodeRequest);

    /**
     * 确认更换手机号
     *
     * <AUTHOR>
     * @since 2024/3/24 23:46
     */
    void ensureChangePhone(ChangePhoneRequest changePhoneRequest);

    /**
     * 校验拖拽验证码是否正确
     *
     * <AUTHOR>
     * @since 2024-04-08 19:47
     */
    void validateDragCaptcha(SendPhoneCodeRequest changePhoneRequest);

}
