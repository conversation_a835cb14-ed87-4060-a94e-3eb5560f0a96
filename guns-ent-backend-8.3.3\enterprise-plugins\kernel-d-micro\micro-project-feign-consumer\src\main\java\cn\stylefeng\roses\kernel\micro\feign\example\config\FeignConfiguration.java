package cn.stylefeng.roses.kernel.micro.feign.example.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.text.SimpleDateFormat;

/**
 * Feign响应解析
 *
 * <AUTHOR>
 * @since 2023/8/9 23:36
 */
@Configuration
public class FeignConfiguration {

    /**
     * feign请求响应的解析器
     *
     * <AUTHOR>
     * @since 2023/8/9 0:21
     */
    @Bean
    public HttpMessageConverters messageConverters() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        return new HttpMessageConverters(mappingJackson2HttpMessageConverter);
    }

}
