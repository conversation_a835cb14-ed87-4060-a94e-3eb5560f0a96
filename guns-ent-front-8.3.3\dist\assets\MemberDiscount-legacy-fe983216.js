System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./formatter-legacy-97503ee9.js","./constants-legacy-2a31d63c.js"],(function(e,t){"use strict";var a,n,i,d,o,s,u,c,l,r,p,m,v,f;return{setters:[e=>{a=e._,n=e.r,i=e.L,d=e.X,o=e.a,s=e.c,u=e.b,c=e.d,l=e.t,r=e.h,p=e.I,m=e.a9,v=e.y},null,e=>{f=e.A},null],execute:function(){var t=document.createElement("style");t.textContent=".member-discount-panel[data-v-d46a55e8]{padding:16px 0}.member-discount[data-v-d46a55e8]{background:rgba(255,255,255,.15);border-radius:8px;padding:12px;margin-bottom:16px}.discount-info[data-v-d46a55e8]{display:flex;align-items:center;gap:8px;font-size:14px;color:#fff}.points-deduction[data-v-d46a55e8]{background:rgba(255,255,255,.15);border-radius:8px;padding:12px;color:#fff}.deduction-header[data-v-d46a55e8]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}.deduction-label[data-v-d46a55e8]{font-size:14px;font-weight:500}.deduction-details[data-v-d46a55e8]{display:flex;flex-direction:column;gap:8px}.deduction-input[data-v-d46a55e8]{display:flex;align-items:center;gap:8px}.points-input[data-v-d46a55e8]{flex:1;background:rgba(255,255,255,.9)}.points-unit[data-v-d46a55e8]{font-size:12px;opacity:.8}.deduction-amount[data-v-d46a55e8]{font-size:13px;font-weight:500;color:#52c41a}.deduction-rate[data-v-d46a55e8]{font-size:11px;opacity:.7}\n",document.head.appendChild(t);const g={class:"member-discount-panel"},x={key:0,class:"member-discount"},b={class:"discount-info"},h={class:"discount-text"},y={key:1,class:"points-deduction"},C={class:"deduction-header"},k={key:0,class:"deduction-details"},j={class:"deduction-input"},z={class:"deduction-amount"},D={class:"deduction-rate"};e("default",a(Object.assign({name:"MemberDiscount"},{__name:"MemberDiscount",props:{member:{type:Object,required:!0},discountRate:{type:Number,default:0},finalAmount:{type:Number,default:0},pointsExchangeRate:{type:Number,default:100}},emits:["pointsDeductionChange"],setup(e,{expose:t,emit:a}){const R=e,E=a,M=n(!1),_=n(0),w=n(0),A=i((()=>R.member&&R.member.points>0&&R.finalAmount>0)),N=i((()=>{if(!R.member)return 0;const e=R.member.points||0,t=Math.floor(R.finalAmount*R.pointsExchangeRate);return Math.min(e,t)})),I=e=>{e?(_.value=Math.min(N.value,1e3),O()):(_.value=0,w.value=0,E("pointsDeductionChange",{points:0,amount:0}))},O=()=>{if(!M.value||_.value<=0)return w.value=0,void E("pointsDeductionChange",{points:0,amount:0});const e=_.value/R.pointsExchangeRate;w.value=e,E("pointsDeductionChange",{points:_.value,amount:e})},S=()=>{M.value=!1,_.value=0,w.value=0};return d((()=>R.member),(()=>{S()})),t({resetPointsDeduction:S}),(t,a)=>{const n=p,i=m,d=v;return o(),s("div",g,[e.discountRate>0?(o(),s("div",x,[u("div",b,[c(n,{iconClass:"icon-discount"}),u("span",h," 享受 "+l((100*e.discountRate).toFixed(1))+"% 会员折扣 ",1)])])):r("",!0),A.value?(o(),s("div",y,[u("div",C,[a[2]||(a[2]=u("span",{class:"deduction-label"},"积分抵扣",-1)),c(i,{checked:M.value,"onUpdate:checked":a[0]||(a[0]=e=>M.value=e),onChange:I,size:"small"},null,8,["checked"])]),M.value?(o(),s("div",k,[u("div",j,[c(d,{value:_.value,"onUpdate:value":a[1]||(a[1]=e=>_.value=e),min:0,max:N.value,step:100,size:"small",onChange:O,class:"points-input"},null,8,["value","max"]),a[3]||(a[3]=u("span",{class:"points-unit"},"积分",-1))]),u("div",z," 可抵扣: ￥"+l((R=w.value,f.formatCurrency(R,{showSymbol:!1}))),1),u("div",D," ("+l(e.pointsExchangeRate)+"积分 = ￥1) ",1)])):r("",!0)])):r("",!0)]);var R}}}),[["__scopeId","data-v-d46a55e8"]]))}}}));
