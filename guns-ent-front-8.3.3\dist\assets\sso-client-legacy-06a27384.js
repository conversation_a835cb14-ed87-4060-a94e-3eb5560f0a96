System.register(["./index-legacy-cbae9bf3.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./sso-client-add-edit-legacy-2391938b.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./sso-client-form-legacy-b76cb089.js","./index-legacy-94a6fc23.js","./index-legacy-198191c1.js","./print-legacy-bf2789b6.js","./FileApi-legacy-f85a3060.js"],(function(e,l){"use strict";var t,a,n,i,s,o,d,c,u,r,p,g,v,h,y,_,f,x,k,m,C,b,w,I,S,j,L,F,T,U;return{setters:[e=>{t=e._},e=>{a=e.C,n=e._},e=>{i=e._,s=e.r,o=e.o,d=e.k,c=e.a,u=e.c,r=e.b,p=e.d,g=e.w,v=e.g,h=e.t,y=e.h,_=e.F,f=e.f,x=e.M,k=e.E,m=e.m,C=e.I,b=e.l,w=e.B,I=e.n,S=e.p,j=e.q,L=e.D,F=e.U},null,e=>{T=e._,U=e.S},null,null,null,null,null,null,null,null],execute:function(){var l=document.createElement("style");l.textContent=".appIconWrapper[data-v-5a431360]{width:22px;height:22px}\n",document.head.appendChild(l);const W={class:"guns-layout"},z={class:"guns-layout-content"},D={class:"guns-layout"},N={class:"guns-layout-content-application"},B={class:"content-mian"},O={class:"content-mian-header"},P={class:"header-content"},R={class:"header-content-left"},V={class:"header-content-right"},E={class:"content-mian-body"},A={class:"table-content"},J=["onClick"],q=["src"];e("default",i(Object.assign({name:"SsoClient"},{__name:"sso-client",setup(e){const l=s([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>i.value.tableIndex+e},{dataIndex:"clientName",title:"第三方业务系统名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"clientLogoFileIdWrapper",title:"应用图标",ellipsis:!0,width:200,isShow:!0},{dataIndex:"loginPageType",title:"登录页面类型",ellipsis:!0,width:200,isShow:!0},{dataIndex:"unifiedLogoutFlag",title:"是否统一退出",ellipsis:!0,width:200,isShow:!0},{dataIndex:"clientStatus",title:"状态",ellipsis:!0,width:200,isShow:!0},{dataIndex:"createTime",title:"创建时间",ellipsis:!0,width:200,isShow:!0},{key:"action",title:"操作",width:100,fixed:"right",isShow:!0}]),i=s(null),M=s({searchText:""}),Y=s(!1),G=s(null),H=s(!1),K=s("SSO_CLIENT");o((()=>{Q()}));const Q=()=>{a.getUserConfig({fieldBusinessCode:K.value}).then((e=>{e.tableWidthJson&&(l.value=JSON.parse(e.tableWidthJson))}))},X=({key:e})=>{"1"==e?Y.value=!0:"2"==e&&le()},Z=()=>{i.value.reload()},$=()=>{M.value.searchText="",Z()},ee=e=>{G.value=e,H.value=!0},le=()=>{if(i.value.selectedRowList&&0==i.value.selectedRowList.length)return m.warning("请选择需要删除的单带登录配置");x.confirm({title:"提示",content:"确定要删除选中的单带登录配置吗?",icon:p(k),maskClosable:!0,onOk:async()=>{const e=await U.batchDelete({batchDeleteIdList:i.value.selectedRowList});m.success(e.message),Z()}})};return(e,a)=>{const s=C,o=b,Q=w,le=I,te=d("plus-outlined"),ae=S,ne=j,ie=d("small-dash-outlined"),se=L,oe=F,de=d("vxe-switch"),ce=n,ue=t;return c(),u("div",W,[r("div",z,[r("div",D,[r("div",N,[r("div",B,[r("div",O,[r("div",P,[r("div",R,[p(le,{size:16},{default:g((()=>[p(o,{value:M.value.searchText,"onUpdate:value":a[0]||(a[0]=e=>M.value.searchText=e),placeholder:"输入应用名称或编码（回车搜索）",onPressEnter:Z,class:"search-input"},{prefix:g((()=>[p(s,{iconClass:"icon-opt-search"})])),_:1},8,["value"]),p(Q,{class:"border-radius",onClick:$},{default:g((()=>a[5]||(a[5]=[v("重置")]))),_:1,__:[5]})])),_:1})]),r("div",V,[p(le,{size:16},{default:g((()=>[p(Q,{type:"primary",class:"border-radius",onClick:a[1]||(a[1]=e=>ee())},{default:g((()=>[p(te),a[6]||(a[6]=v("新建"))])),_:1,__:[6]}),p(se,null,{overlay:g((()=>[p(ne,{onClick:X},{default:g((()=>[p(ae,{key:"1"},{default:g((()=>[p(s,{iconClass:"icon-opt-zidingyilie",color:"#60666b"}),a[7]||(a[7]=r("span",null,"自定义列",-1))])),_:1,__:[7]}),r("div",null,[p(ae,{key:"2"},{default:g((()=>[p(s,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[8]||(a[8]=r("span",null,"批量删除",-1))])),_:1,__:[8]})])])),_:1})])),default:g((()=>[p(Q,{class:"border-radius"},{default:g((()=>[a[9]||(a[9]=v(" 更多 ")),p(ie)])),_:1,__:[9]})])),_:1})])),_:1})])])]),r("div",E,[r("div",A,[p(ce,{columns:l.value,where:M.value,rowId:"clientId",ref_key:"tableRef",ref:i,url:"/ssoClient/page"},{bodyCell:g((({column:e,record:l})=>["clientName"==e.dataIndex?(c(),u("a",{key:0,onClick:e=>ee(l)},h(l.clientName),9,J)):y("",!0),"clientLogoFileIdWrapper"==e.dataIndex?(c(),u("img",{key:1,src:l.clientLogoFileIdWrapper,alt:"",class:"appIconWrapper"},null,8,q)):y("",!0),"loginPageType"==e.dataIndex?(c(),u(_,{key:2},[1==l.loginPageType?(c(),f(oe,{key:0,color:"blue"},{default:g((()=>a[10]||(a[10]=[v("应用自定义登录界面")]))),_:1,__:[10]})):y("",!0),2==l.loginPageType?(c(),f(oe,{key:1,color:"red"},{default:g((()=>a[11]||(a[11]=[v("使用CA服务统一登录界面")]))),_:1,__:[11]})):y("",!0)],64)):y("",!0),"unifiedLogoutFlag"==e.dataIndex?(c(),u(_,{key:3},["Y"==l.unifiedLogoutFlag?(c(),f(oe,{key:0,color:"blue"},{default:g((()=>a[12]||(a[12]=[v("是")]))),_:1,__:[12]})):y("",!0),"N"==l.unifiedLogoutFlag?(c(),f(oe,{key:1,color:"red"},{default:g((()=>a[13]||(a[13]=[v("否")]))),_:1,__:[13]})):y("",!0)],64)):y("",!0),"clientStatus"==e.dataIndex?(c(),f(de,{key:4,modelValue:l.clientStatus,"onUpdate:modelValue":e=>l.clientStatus=e,"open-value":1,"close-value":2,onChange:e=>(e=>{U.updateStatus({clientId:e.clientId,clientStatus:e.clientStatus}).then((e=>{m.success(e.message)}))})(l)},null,8,["modelValue","onUpdate:modelValue","onChange"])):y("",!0),"action"==e.key?(c(),f(le,{key:5,size:16},{default:g((()=>[p(s,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>ee(l)},null,8,["onClick"]),p(s,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{x.confirm({title:"提示",content:"确定要删除选中的单带登录配置吗?",icon:p(k),maskClosable:!0,onOk:async()=>{const l=await U.delete({clientId:e.clientId});m.success(l.message),Z()}})})(l)},null,8,["onClick"])])),_:2},1024)):y("",!0)])),_:1},8,["columns","where"])])])])])])]),Y.value?(c(),f(ue,{key:0,visible:Y.value,"onUpdate:visible":a[2]||(a[2]=e=>Y.value=e),data:l.value,onDone:a[3]||(a[3]=e=>l.value=e),fieldBusinessCode:K.value},null,8,["visible","data","fieldBusinessCode"])):y("",!0),H.value?(c(),f(T,{key:1,visible:H.value,"onUpdate:visible":a[4]||(a[4]=e=>H.value=e),data:G.value,onDone:Z},null,8,["visible","data"])):y("",!0)])}}}),[["__scopeId","data-v-5a431360"]]))}}}));
