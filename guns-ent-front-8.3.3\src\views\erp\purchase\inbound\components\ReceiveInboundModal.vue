<template>
  <a-modal
    :visible="visible"
    title="执行入库操作"
    :width="1000"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleReceive">
        确认入库
      </a-button>
    </template>

    <div class="receive-inbound-content">
      <!-- 入库提示 -->
      <a-alert
        message="入库操作提醒"
        description="执行入库操作后，商品库存将会增加，入库单状态变为已完成，请仔细核对入库数量。"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <!-- 入库单基本信息 -->
      <a-card title="入库单信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="入库单号">
            <span class="order-no">{{ data.orderNo }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="供应商">
            {{ data.supplierName }}
          </a-descriptions-item>
          <a-descriptions-item label="订单日期">
            {{ data.orderDate }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <!-- 入库信息 -->
      <a-card title="入库信息" size="small" style="margin-bottom: 16px">
        <a-form ref="receiveFormRef" :model="receiveData" :rules="receiveRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="付款方式" name="paymentMethod">
                <a-select v-model:value="receiveData.paymentMethod" placeholder="请选择付款方式">
                  <a-select-option value="CASH">现金</a-select-option>
                  <a-select-option value="BANK_TRANSFER">银行转账</a-select-option>
                  <a-select-option value="ALIPAY">支付宝</a-select-option>
                  <a-select-option value="WECHAT">微信支付</a-select-option>
                  <a-select-option value="CREDIT">赊账</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="付款账户" name="paymentAccount">
                <a-input v-model:value="receiveData.paymentAccount" placeholder="请输入付款账户" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="实际总金额" name="actualTotalAmount">
                <a-input-number
                  v-model:value="receiveData.actualTotalAmount"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  placeholder="请输入实际总金额"
                >
                  <template #addonBefore>¥</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="入库备注" name="receiveRemark">
                <a-input v-model:value="receiveData.receiveRemark" placeholder="请输入入库备注" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 入库明细 -->
      <a-card title="入库明细" size="small" style="margin-bottom: 16px">
        <a-form ref="formRef" :model="receiveData" :rules="rules">
          <a-table
            :columns="columns"
            :data-source="receiveData.detailList"
            :pagination="false"
            size="small"
            bordered
            :scroll="{ y: 400 }"
          >
            <template #bodyCell="{ column, record, index }">
              <!-- 商品信息 -->
              <template v-if="column.key === 'productInfo'">
                <div class="product-info">
                  <div class="product-name">{{ record.productName }}</div>
                  <div class="product-details">
                    <span class="product-code">{{ record.productCode }}</span>
                    <span v-if="record.specification" class="product-spec">{{ record.specification }}</span>
                  </div>
                </div>
              </template>

              <!-- 订单数量 -->
              <template v-if="column.key === 'orderQuantity'">
                {{ record.orderQuantity }} {{ getQuantityUnit(record) }}
              </template>

              <!-- 实际入库数量 -->
              <template v-if="column.key === 'receiveQuantity'">
                <a-form-item
                  :name="['detailList', index, 'receiveQuantity']"
                  :rules="[
                    { required: true, message: '请输入入库数量' },
                    { type: 'number', min: 0, message: '入库数量不能小于0' }
                  ]"
                  style="margin-bottom: 0"
                >
                  <a-input-number
                    v-model:value="record.receiveQuantity"
                    :min="0"
                    :precision="getPrecision(record.pricingType)"
                    :step="getStep(record.pricingType)"
                    style="width: 100%"
                    @change="onReceiveQuantityChange(record, index)"
                  >
                    <template #addonAfter>
                      {{ getQuantityUnit(record) }}
                    </template>
                  </a-input-number>
                </a-form-item>
              </template>

              <!-- 差异数量 -->
              <template v-if="column.key === 'diffQuantity'">
                <span :class="getDiffQuantityClass(record.diffQuantity)">
                  {{ formatDiffQuantity(record.diffQuantity) }} {{ getQuantityUnit(record) }}
                </span>
              </template>

              <!-- 单价 -->
              <template v-if="column.key === 'unitPrice'">
                ¥{{ formatAmount(record.unitPrice) }}
              </template>

              <!-- 实际金额 -->
              <template v-if="column.key === 'receiveAmount'">
                <span class="receive-amount">¥{{ formatAmount(record.receiveAmount) }}</span>
              </template>

              <!-- 备注 -->
              <template v-if="column.key === 'remark'">
                <a-input
                  v-model:value="record.remark"
                  placeholder="入库备注"
                  size="small"
                />
              </template>
            </template>
          </a-table>
        </a-form>
      </a-card>


      <!-- 汇总信息 -->
      <a-card title="入库汇总" size="small">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="商品种类" :value="productCount" suffix="种" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="订单总量" :value="orderTotalQuantity" suffix="件" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="入库总量" :value="receiveTotalQuantity" suffix="件" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="入库金额" :value="receiveTotalAmount" prefix="¥" :precision="2" />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PurchaseApi } from '../../api/PurchaseApi';

export default {
  name: 'ReceiveInboundModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const receiveFormRef = ref(null);
    const loading = ref(false);

    // 入库数据
    const receiveData = reactive({
      id: null,
      paymentMethod: '',
      paymentAccount: '',
      actualTotalAmount: null,
      receiveRemark: '',
      detailList: []
    });

    // 表单验证规则
    const rules = {
      // 验证规则在模板中定义
    };

    // 入库信息表单验证规则
    const receiveRules = {
      paymentMethod: [
        { required: true, message: '请选择付款方式', trigger: 'change' }
      ]
    };

    // 表格列定义
    const columns = [
      {
        title: '商品信息',
        key: 'productInfo',
        width: 200,
        fixed: 'left'
      },
      {
        title: '订单数量',
        key: 'orderQuantity',
        width: 100,
        align: 'center'
      },
      {
        title: '入库数量',
        key: 'receiveQuantity',
        width: 120,
        align: 'center'
      },
      {
        title: '差异数量',
        key: 'diffQuantity',
        width: 100,
        align: 'center'
      },
      {
        title: '单价',
        key: 'unitPrice',
        width: 100,
        align: 'right'
      },
      {
        title: '入库金额',
        key: 'receiveAmount',
        width: 120,
        align: 'right'
      },
      {
        title: '备注',
        key: 'remark',
        width: 150
      }
    ];

    // 监听props.data变化
    watch(() => props.data, (newData) => {
      if (newData && newData.id) {
        receiveData.id = newData.id;
        receiveData.paymentMethod = '';
        receiveData.paymentAccount = '';
        receiveData.actualTotalAmount = newData.totalAmount || null;
        receiveData.receiveRemark = '';
        receiveData.detailList = (newData.detailList || []).map(item => ({
          ...item,
          orderQuantity: item.quantity,
          receiveQuantity: item.quantity, // 默认入库数量等于订单数量
          diffQuantity: 0,
          receiveAmount: item.totalPrice,
          remark: ''
        }));
      }
    }, { immediate: true });

    // 计算属性
    const productCount = computed(() => {
      return receiveData.detailList ? receiveData.detailList.length : 0;
    });

    const orderTotalQuantity = computed(() => {
      if (!receiveData.detailList || receiveData.detailList.length === 0) return 0;
      return receiveData.detailList.reduce((total, item) => {
        return total + (parseFloat(item.orderQuantity) || 0);
      }, 0);
    });

    const receiveTotalQuantity = computed(() => {
      if (!receiveData.detailList || receiveData.detailList.length === 0) return 0;
      return receiveData.detailList.reduce((total, item) => {
        return total + (parseFloat(item.receiveQuantity) || 0);
      }, 0);
    });

    const receiveTotalAmount = computed(() => {
      if (!receiveData.detailList || receiveData.detailList.length === 0) return 0;
      return receiveData.detailList.reduce((total, item) => {
        return total + (parseFloat(item.receiveAmount) || 0);
      }, 0);
    });

    // 根据计价类型获取精度
    const getPrecision = (pricingType) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 3;
        case 'NORMAL':
        case 'PIECE':
        case 'VARIABLE':
        default:
          return 0;
      }
    };

    // 根据计价类型获取步长
    const getStep = (pricingType) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 0.001;
        case 'NORMAL':
        case 'PIECE':
        case 'VARIABLE':
        default:
          return 1;
      }
    };

    // 获取数量单位
    const getQuantityUnit = (record) => {
      switch (record.pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return record.unit || '个';
      }
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 格式化差异数量
    const formatDiffQuantity = (diffQuantity) => {
      const diff = parseFloat(diffQuantity) || 0;
      return diff >= 0 ? `+${diff}` : `${diff}`;
    };

    // 获取差异数量样式类
    const getDiffQuantityClass = (diffQuantity) => {
      const diff = parseFloat(diffQuantity) || 0;
      if (diff > 0) return 'diff-positive';
      if (diff < 0) return 'diff-negative';
      return 'diff-zero';
    };

    // 入库数量变化事件
    const onReceiveQuantityChange = (record, index) => {
      const orderQuantity = parseFloat(record.orderQuantity) || 0;
      const receiveQuantity = parseFloat(record.receiveQuantity) || 0;
      const unitPrice = parseFloat(record.unitPrice) || 0;

      // 计算差异数量
      record.diffQuantity = receiveQuantity - orderQuantity;
      
      // 计算入库金额
      record.receiveAmount = receiveQuantity * unitPrice;
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 执行入库
    const handleReceive = () => {
      // 先验证入库信息表单
      receiveFormRef.value.validate().then(() => {
        // 再验证明细表单
        return formRef.value.validate();
      }).then(() => {
        if (!receiveData.id) {
          message.error('入库单信息不完整');
          return;
        }

        loading.value = true;
        PurchaseApi.receive({
          id: receiveData.id,
          paymentMethod: receiveData.paymentMethod,
          paymentAccount: receiveData.paymentAccount,
          actualTotalAmount: receiveData.actualTotalAmount,
          receiveRemark: receiveData.receiveRemark
        })
          .then(() => {
            message.success('入库成功');
            emit('ok');
          })
          .catch(error => {
            message.error('入库失败：' + (error.message || '未知错误'));
          })
          .finally(() => {
            loading.value = false;
          });
      }).catch(() => {
        message.error('请完善入库信息');
      });
    };

    return {
      formRef,
      receiveFormRef,
      loading,
      receiveData,
      rules,
      receiveRules,
      columns,
      productCount,
      orderTotalQuantity,
      receiveTotalQuantity,
      receiveTotalAmount,
      getPrecision,
      getStep,
      getQuantityUnit,
      formatAmount,
      formatDiffQuantity,
      getDiffQuantityClass,
      onReceiveQuantityChange,
      handleCancel,
      handleReceive
    };
  }
};
</script>

<style scoped>
.receive-inbound-content {
  max-height: 70vh;
  overflow-y: auto;
}

.order-no {
  font-weight: 500;
  color: #1890ff;
}

.product-info {
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-details {
  font-size: 12px;
  color: #8c8c8c;
}

.product-code {
  margin-right: 8px;
}

.product-spec {
  margin-left: 8px;
}

.receive-amount {
  font-weight: 500;
  color: #1890ff;
}

.diff-positive {
  color: #52c41a;
  font-weight: 500;
}

.diff-negative {
  color: #ff4d4f;
  font-weight: 500;
}

.diff-zero {
  color: #8c8c8c;
}

.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
}

.ant-statistic-content {
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}
</style>
