System.register(["./typescript-legacy-2b5cb163.js","./index-legacy-db773591.js","./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js"],(function(e,t){"use strict";var s,i;return{setters:[e=>{s=e.conf,i=e.language},null,null,null],execute:function(){
/*!-----------------------------------------------------------------------------
       * Copyright (c) Microsoft Corporation. All rights reserved.
       * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
       * Released under the MIT license
       * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
       *-----------------------------------------------------------------------------*/
e("conf",s),e("language",{defaultToken:"invalid",tokenPostfix:".js",keywords:["break","case","catch","class","continue","const","constructor","debugger","default","delete","do","else","export","extends","false","finally","for","from","function","get","if","import","in","instanceof","let","new","null","return","set","static","super","switch","symbol","this","throw","true","try","typeof","undefined","var","void","while","with","yield","async","await","of"],typeKeywords:[],operators:i.operators,symbols:i.symbols,escapes:i.escapes,digits:i.digits,octaldigits:i.octaldigits,binarydigits:i.binarydigits,hexdigits:i.hexdigits,regexpctl:i.regexpctl,regexpesc:i.regexpesc,tokenizer:i.tokenizer})}}}));
