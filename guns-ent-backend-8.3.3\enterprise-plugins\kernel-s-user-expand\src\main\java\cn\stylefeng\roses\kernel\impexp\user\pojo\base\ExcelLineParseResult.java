package cn.stylefeng.roses.kernel.impexp.user.pojo.base;

import lombok.Data;

/**
 * 针对一条数据某个字段，导入结果属性格式的判断
 *
 * <AUTHOR>
 * @since 2024-02-04 15:38
 */
@Data
public class ExcelLineParseResult {

    /**
     * 字段格式校验是否正确，默认是正确
     */
    private Boolean validateResult = true;

    /**
     * 字段的展示值
     */
    private String value;

    /**
     * 直接能提交给后端的值
     */
    private Object submitValue;

    /**
     * 字段的错误提示，如果校验不通过，这里要给出错误提示
     */
    private String errorMessage;

    public ExcelLineParseResult(String originValue) {
        this.validateResult = true;
        this.value = originValue;
        this.submitValue = originValue;
    }

    public ExcelLineParseResult(Boolean validateResult, String value, Object submitValue) {
        this.validateResult = validateResult;
        this.value = value;
        this.submitValue = submitValue;
    }

    public ExcelLineParseResult(Boolean validateResult, String value, Object submitValue, String errorMessage) {
        this.validateResult = validateResult;
        this.value = value;
        this.submitValue = submitValue;
        this.errorMessage = errorMessage;
    }

}
