package cn.stylefeng.roses.kernel.ca.api.pojo.sso.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单点登录，获取ssoLoginCode的请求参数封装
 *
 * <AUTHOR>
 * @since 2021/1/27 16:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SsoLoginCodeRequest extends BaseRequest {

    /**
     * 用户账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 用户密码
     */
    @NotBlank(message = "用户密码不能为空")
    private String password;

    /**
     * 租户编码
     */
    @ChineseDescription("租户编码")
    private String tenantCode;

}
