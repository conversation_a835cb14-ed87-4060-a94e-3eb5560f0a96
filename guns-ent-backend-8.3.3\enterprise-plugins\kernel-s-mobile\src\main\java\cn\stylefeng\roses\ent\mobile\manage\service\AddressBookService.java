package cn.stylefeng.roses.ent.mobile.manage.service;

import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.*;

import java.util.List;

/**
 * 通讯录接口
 *
 * <AUTHOR>
 * @since 2024/3/21 22:11
 */
public interface AddressBookService {

    /**
     * 获取通讯录信息列表
     *
     * <AUTHOR>
     * @since 2024/3/21 22:26
     */
    List<AddressBookItem> getAddressBookItemList(AddressBookRequest addressBookRequest);

    /**
     * 获取通讯录，用户详情
     *
     * <AUTHOR>
     * @since 2024/3/24 22:16
     */
    AddressBookUserDetail getUserInfo(AddressBookUserRequest addressBookUserRequest);

    /**
     * 获取邀请成员的二维码的图片
     *
     * <AUTHOR>
     * @since 2024-04-08 16:28
     */
    void getInviteQrCodePicture(QrCodeCreateRequest qrCodeCreateRequest);

    /**
     * 获取邀请成员的url
     *
     * <AUTHOR>
     * @since 2024-04-08 17:00
     */
    String getInviteUrl(QrCodeCreateRequest qrCodeCreateRequest);

}
