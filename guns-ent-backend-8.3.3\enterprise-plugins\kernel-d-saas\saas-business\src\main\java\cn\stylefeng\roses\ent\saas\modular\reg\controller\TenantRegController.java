package cn.stylefeng.roses.ent.saas.modular.reg.controller;

import cn.stylefeng.roses.ent.saas.modular.reg.pojo.TenantRegRequest;
import cn.stylefeng.roses.ent.saas.modular.reg.pojo.TenantRegSendEmailRequest;
import cn.stylefeng.roses.ent.saas.modular.reg.service.TenantRegService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租户注册控制器
 *
 * <AUTHOR>
 * @since 2024-02-22 16:59
 */
@RestController
@ApiResource(name = "租户注册控制器")
public class TenantRegController {

    @Resource
    private TenantRegService tenantRegService;

    /**
     * 发送邮箱验证码
     *
     * <AUTHOR>
     * @since 2024-02-22 17:00
     */
    @PostResource(name = "发送邮箱验证码", path = "/tenant/sendEmail", requiredLogin = false)
    public ResponseData<?> sendEmail(@RequestBody TenantRegSendEmailRequest tenantRegSendEmailRequest) {
        tenantRegService.sendVerifyCodeEmail(tenantRegSendEmailRequest.getVerKey(), tenantRegSendEmailRequest.getVerCode(), tenantRegSendEmailRequest.getEmail());
        return new SuccessResponseData<>();
    }

    /**
     * 提交租户注册信息
     *
     * <AUTHOR>
     * @since 2024-02-22 17:00
     */
    @PostResource(name = "提交租户注册信息", path = "/tenant/submitTenantReg", requiredLogin = false)
    public ResponseData<?> submitTenantReg(@RequestBody TenantRegRequest tenantRegRequest) {
        tenantRegService.submitTenantReg(tenantRegRequest);
        return new SuccessResponseData<>();
    }

}
