System.register(["./index-legacy-ee1db0c7.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./role-add-edit-legacy-1d1a31d7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./role-form-legacy-d39b6ac5.js","./index-legacy-dba03026.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js","./index-legacy-9a185ac3.js","./index-legacy-94a6fc23.js","./RoleTypeApi-legacy-6008d05b.js"],(function(e,l){"use strict";var a,o,t,n,s,i,d,r,c,u,y,p,v,h,g,T,b,m,C,w,_,I,f,k,x,j,E,R,D,L,S,O,z,A,B,K;return{setters:[e=>{a=e._,o=e.r,t=e.o,n=e.k,s=e.bv,i=e.a,d=e.c,r=e.b,c=e.d,u=e.w,y=e.f,p=e.aR,v=e.g,h=e.a2,g=e.t,T=e.h,b=e.F,m=e.at,C=e.aI,w=e.aJ,_=e.M,I=e.E,f=e.m,k=e.j,x=e.T,j=e.n,E=e.B,R=e.I,D=e.p,L=e.q,S=e.D,O=e.l},e=>{z=e._},e=>{A=e._},e=>{B=e._,K=e.R},null,null,null,null,null,null,null,null,null,null],execute:function(){var N=document.createElement("style");N.textContent=".role-table[data-v-48741f7b]{width:100%;height:100%}[data-v-48741f7b] .no-radius .table-tool{border-top-left-radius:0}\n",document.head.appendChild(N);const U={class:"guns-layout"},F={class:"guns-layout-content"},q={class:"guns-layout"},G={class:"guns-layout-content-application"},J={class:"content-mian"},M={class:"content-mian-header"},P={class:"header-content"},W={class:"header-content-left"},H={class:"header-content-right"},Q={class:"content-mian-body"},V={class:"table-content"},X=["onClick"],Y={key:0},Z={key:1},$={key:2};e("default",a(Object.assign({name:"AuthRole"},{__name:"index",setup(e){const a=C((()=>w((()=>l.import("./role-tree-legacy-7c23ecc5.js")),void 0))),N=o([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"roleName",title:"角色名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"roleType",title:"角色类型",width:100,isShow:!0},{dataIndex:"roleCompanyIdWrapper",title:"角色所属公司",width:100,isShow:!0},{dataIndex:"roleCode",title:"角色编码",width:100,isShow:!0},{dataIndex:"roleSort",title:"排序",width:100,isShow:!0},{dataIndex:"createTime",title:"创建时间",width:150,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}]),ee=o(null),le=o({searchText:"",roleType:"",roleCategoryId:void 0}),ae=o(null),oe=o(!1),te=o(null);t((()=>{}));const ne=({key:e})=>{"1"==e&&ye()},se=e=>{le.value.roleCategoryId=e.id,re()},ie=e=>{20==le.value.roleType&&(te.value=e,le.value.roleCompanyId=null==e?void 0:e.companyId,re())},de=e=>{le.value.roleCategoryId==e.id&&(le.value.roleCategoryId=void 0,re())},re=()=>{ee.value.reload()},ce=()=>{le.value.roleCategoryId=void 0,le.value.roleCompanyId=void 0,20!=le.value.roleType&&re()},ue=e=>{ae.value=e,oe.value=!0},ye=()=>{if(ee.value.selectedRowList&&0==ee.value.selectedRowList.length)return f.warning("请选择需要删除的角色");_.confirm({title:"提示",content:"确定要删除选中的角色吗?",icon:c(I),maskClosable:!0,onOk:async()=>{const e=await K.batchDelete({roleIdList:ee.value.selectedRowList});f.success(e.message),re()}})};return(e,l)=>{const o=k,t=x,C=j,w=n("plus-outlined"),ye=E,pe=R,ve=D,he=L,ge=n("small-dash-outlined"),Te=S,be=O,me=A,Ce=z,we=s("permission");return i(),d("div",U,[r("div",F,[r("div",q,[r("div",G,[r("div",J,[r("div",M,[r("div",P,[r("div",W,[c(C,{size:16},{default:u((()=>[c(t,{activeKey:le.value.roleType,"onUpdate:activeKey":l[0]||(l[0]=e=>le.value.roleType=e),class:"devops-tabs",onChange:ce},{default:u((()=>[c(o,{key:"",tab:"全部"}),(i(),y(o,{key:10,tab:"系统角色"})),(i(),y(o,{key:15,tab:"业务角色"})),(i(),y(o,{key:20,tab:"公司角色"}))])),_:1},8,["activeKey"])])),_:1})]),r("div",H,[c(C,{size:16},{default:u((()=>[p((i(),y(ye,{type:"primary",class:"border-radius",onClick:l[1]||(l[1]=e=>ue())},{default:u((()=>[c(w),l[4]||(l[4]=v("新建"))])),_:1,__:[4]})),[[we,["ADD_ROLE"]]]),c(Te,null,{overlay:u((()=>[c(he,{onClick:ne},{default:u((()=>[p((i(),d("div",null,[c(ve,{key:"1"},{default:u((()=>[c(pe,{iconClass:"icon-opt-shanchu",color:"#60666b"}),l[5]||(l[5]=r("span",null,"批量删除",-1))])),_:1,__:[5]})])),[[we,["EDIT_ROLE"]]])])),_:1})])),default:u((()=>[c(ye,{class:"border-radius"},{default:u((()=>[l[6]||(l[6]=v(" 更多 ")),c(ge)])),_:1,__:[6]})])),_:1})])),_:1})])])]),r("div",Q,[r("div",V,[c(Ce,{width:["",10].includes(le.value.roleType)?"0px":"292px",allowCollapse:!["",10].includes(le.value.roleType),resizable:!["",10].includes(le.value.roleType)},{content:u((()=>[r("div",{class:h(["role-table",{"no-radius":[15,20].includes(le.value.roleType)}])},[c(me,{columns:N.value,where:le.value,showToolTotal:!1,showTableTool:"",rowId:"roleId",ref_key:"tableRef",ref:ee,url:"/sysRole/page",fieldBusinessCode:"ROLE_TABLE"},{toolLeft:u((()=>[c(be,{value:le.value.searchText,"onUpdate:value":l[2]||(l[2]=e=>le.value.searchText=e),placeholder:"名称、编码（回车搜索）",onPressEnter:re,class:"search-input",bordered:!1},{prefix:u((()=>[c(pe,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:u((({column:e,record:l})=>["roleName"==e.dataIndex?(i(),d("a",{key:0,onClick:e=>ue(l)},g(l.roleName),9,X)):T("",!0),"roleType"==e.dataIndex?(i(),d(b,{key:1},[10==l.roleType?(i(),d("span",Y,"系统角色")):T("",!0),15==l.roleType?(i(),d("span",Z,"业务角色")):T("",!0),20==l.roleType?(i(),d("span",$,"公司角色")):T("",!0)],64)):T("",!0),"action"==e.key?(i(),y(C,{key:2,size:16},{default:u((()=>[p(c(pe,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>ue(l)},null,8,["onClick"]),[[we,["DELETE_ROLE"]]]),p(c(pe,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{_.confirm({title:"提示",content:"确定要删除选中的角色吗?",icon:c(I),maskClosable:!0,onOk:async()=>{const l=await K.delete({roleId:e.roleId});f.success(l.message),re()}})})(l)},null,8,["onClick"]),[[we,["EDIT_ROLE"]]])])),_:2},1024)):T("",!0)])),_:1},8,["columns","where"])],2)])),default:u((()=>[[15,20].includes(le.value.roleType)?(i(),y(m(a),{key:0,roleType:le.value.roleType,onTreeSelect:se,onDelRoleType:de,onGetCompanyData:ie},null,8,["roleType"])):T("",!0)])),_:1},8,["width","allowCollapse","resizable"])])])])])])]),oe.value?(i(),y(B,{key:0,visible:oe.value,"onUpdate:visible":l[3]||(l[3]=e=>oe.value=e),roleType:le.value.roleType,roleCategoryId:le.value.roleCategoryId,data:ae.value,onDone:re,companyData:te.value},null,8,["visible","roleType","roleCategoryId","data","companyData"])):T("",!0)])}}}),[["__scopeId","data-v-48741f7b"]]))}}}));
