System.register(["./index-legacy-ee1db0c7.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./index-legacy-16f295ac.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./InventoryAlertRecordApi-legacy-ebfe5eed.js"],(function(e,a){"use strict";var l,t,o,r,d,n,s,c,i,u,f,m,h,v,p,g,_,R,T,y,D,E,O,C,M,S,b,x,k,Y,A,L,I,N,P,j;return{setters:[e=>{l=e._,t=e.aj,o=e.ak,r=e.r,d=e.s,n=e.X,s=e.k,c=e.a,i=e.f,u=e.w,f=e.b,m=e.d,h=e.g,v=e.t,p=e.ah,g=e.c,_=e.h,R=e.F,T=e.m,y=e.Y,D=e.U,E=e.Z,O=e.a0,C=e.z,M=e.A,S=e.u,b=e.$,x=e.C,k=e.ab,Y=e.l,A=e.al,L=e.W,I=e.J,N=e.H,P=e.M},null,null,null,null,null,e=>{j=e.I}],execute:function(){var a=document.createElement("style");a.textContent=".process-container[data-v-d96975f3]{max-height:70vh;overflow-y:auto}.alert-info-card[data-v-d96975f3]{margin-bottom:16px}.alert-message[data-v-d96975f3]{background-color:#f5f5f5;padding:8px;border-radius:4px;word-break:break-word}.process-form[data-v-d96975f3]{margin-top:16px}.handle-type-desc[data-v-d96975f3]{margin-top:8px}.desc-text[data-v-d96975f3]{font-size:12px;color:#666;padding:4px 8px;background-color:#f9f9f9;border-radius:4px}.notify-desc[data-v-d96975f3]{font-size:12px;color:#999;margin-top:4px}\n",document.head.appendChild(a);const w={name:"AlertRecordProcess",components:{CheckCircleOutlined:t,MinusCircleOutlined:o},props:{visible:{type:Boolean,default:!1},recordData:{type:Object,default:()=>({})}},emits:["update:visible","success","cancel"],setup(e,{emit:a}){const l=r(),t=r(!1),o=d({handleType:"RESOLVE",handleRemark:"",resolveMethod:[],otherMethodDesc:"",expectedCompleteTime:null,ignoreReason:"",otherReasonDesc:"",recheckTime:null,notifySettings:["SYSTEM"]}),s=d({handleType:[{required:!0,message:"请选择处理方式",trigger:"change"}],handleRemark:[{required:!0,message:"请输入处理备注",trigger:"blur"},{min:10,message:"处理备注至少10个字符",trigger:"blur"}],resolveMethod:[{validator:(e,a)=>"RESOLVE"!==o.handleType||a&&0!==a.length?Promise.resolve():Promise.reject("请选择解决方式"),trigger:"change"}],otherMethodDesc:[{validator:(e,a)=>"RESOLVE"===o.handleType&&o.resolveMethod&&o.resolveMethod.includes("OTHER")&&!a?Promise.reject("请说明其他解决方式"):Promise.resolve(),trigger:"blur"}],ignoreReason:[{validator:(e,a)=>"IGNORE"!==o.handleType||a?Promise.resolve():Promise.reject("请选择忽略原因"),trigger:"change"}],otherReasonDesc:[{validator:(e,a)=>"IGNORE"!==o.handleType||"OTHER"!==o.ignoreReason||a?Promise.resolve():Promise.reject("请说明其他忽略原因"),trigger:"blur"}]});return n((()=>e.visible),(e=>{e&&Object.assign(o,{handleType:"RESOLVE",handleRemark:"",resolveMethod:[],otherMethodDesc:"",expectedCompleteTime:null,ignoreReason:"",otherReasonDesc:"",recheckTime:null,notifySettings:["SYSTEM"]})})),{formRef:l,confirmLoading:t,formData:o,rules:s,handleSubmit:async()=>{try{await l.value.validate(),t.value=!0;const n={id:e.recordData.id,handleType:o.handleType,handleRemark:o.handleRemark,notifySettings:o.notifySettings};var r;if("RESOLVE"===o.handleType)n.resolveMethod=o.resolveMethod,n.otherMethodDesc=o.otherMethodDesc,n.expectedCompleteTime=null===(r=o.expectedCompleteTime)||void 0===r?void 0:r.format("YYYY-MM-DD");else if("IGNORE"===o.handleType){var d;n.ignoreReason=o.ignoreReason,n.otherReasonDesc=o.otherReasonDesc,n.recheckTime=null===(d=o.recheckTime)||void 0===d?void 0:d.format("YYYY-MM-DD")}await j.handle(n),T.success("处理成功"),a("success")}catch(n){console.error("处理失败:",n),T.error("处理失败")}finally{t.value=!1}},handleCancel:()=>{a("cancel")},handleTypeChange:()=>{o.resolveMethod=[],o.otherMethodDesc="",o.expectedCompleteTime=null,o.ignoreReason="",o.otherReasonDesc="",o.recheckTime=null},getAlertTypeColor:e=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"}[e]||"default"),getAlertTypeText:e=>({LOW_STOCK:"库存不足",ZERO_STOCK:"零库存",OVERSTOCK:"库存积压",EXPIRY:"临期预警"}[e]||e),getAlertLevelColor:e=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"}[e]||"default"),getAlertLevelText:e=>({CRITICAL:"紧急",WARNING:"警告",INFO:"提醒"}[e]||e)}}},U={class:"process-container"},V={class:"alert-message"},G={style:{color:"#52c41a"}},H={style:{color:"#faad14"}},K={class:"handle-type-desc"},W={key:0,class:"desc-text"},z={key:1,class:"desc-text"};e("default",l(w,[["render",function(e,a,l,t,o,r){const d=y,n=D,T=E,j=O,w=s("CheckCircleOutlined"),F=C,Z=s("MinusCircleOutlined"),X=M,q=S,B=b,J=x,$=k,Q=Y,ee=A,ae=L,le=I,te=N,oe=P;return c(),i(oe,{title:"处理预警记录",visible:l.visible,width:600,"confirm-loading":t.confirmLoading,onOk:t.handleSubmit,onCancel:t.handleCancel},{default:u((()=>[f("div",U,[m(j,{title:"预警信息",size:"small",class:"alert-info-card"},{default:u((()=>[m(T,{column:2,size:"small"},{default:u((()=>[m(d,{label:"商品名称"},{default:u((()=>[h(v(l.recordData.productName),1)])),_:1}),m(d,{label:"预警类型"},{default:u((()=>[m(n,{color:t.getAlertTypeColor(l.recordData.alertType)},{default:u((()=>[h(v(t.getAlertTypeText(l.recordData.alertType)),1)])),_:1},8,["color"])])),_:1}),m(d,{label:"预警级别"},{default:u((()=>[m(n,{color:t.getAlertLevelColor(l.recordData.alertLevel)},{default:u((()=>[h(v(t.getAlertLevelText(l.recordData.alertLevel)),1)])),_:1},8,["color"])])),_:1}),m(d,{label:"当前库存"},{default:u((()=>[f("span",{style:p({color:l.recordData.currentStock<=l.recordData.thresholdValue?"#f5222d":"#52c41a",fontWeight:"bold"})},v(l.recordData.currentStock),5)])),_:1}),m(d,{label:"预警消息",span:2},{default:u((()=>[f("div",V,v(l.recordData.alertMessage),1)])),_:1})])),_:1})])),_:1}),m(te,{ref:"formRef",model:t.formData,rules:t.rules,"label-col":{span:6},"wrapper-col":{span:16},class:"process-form"},{default:u((()=>[m(q,{label:"处理方式",name:"handleType"},{default:u((()=>[m(X,{value:t.formData.handleType,"onUpdate:value":a[0]||(a[0]=e=>t.formData.handleType=e),onChange:t.handleTypeChange},{default:u((()=>[m(F,{value:"RESOLVE"},{default:u((()=>[f("span",G,[m(w),a[9]||(a[9]=h(" 解决 "))])])),_:1}),m(F,{value:"IGNORE"},{default:u((()=>[f("span",H,[m(Z),a[10]||(a[10]=h(" 忽略 "))])])),_:1})])),_:1},8,["value","onChange"]),f("div",K,["RESOLVE"===t.formData.handleType?(c(),g("div",W,[m(w,{style:{color:"#52c41a"}}),a[11]||(a[11]=h(" 标记为已解决，表示已采取措施解决了库存问题 "))])):"IGNORE"===t.formData.handleType?(c(),g("div",z,[m(Z,{style:{color:"#faad14"}}),a[12]||(a[12]=h(" 标记为已忽略，表示暂时不处理此预警 "))])):_("",!0)])])),_:1}),m(q,{label:"处理备注",name:"handleRemark"},{default:u((()=>[m(B,{value:t.formData.handleRemark,"onUpdate:value":a[1]||(a[1]=e=>t.formData.handleRemark=e),placeholder:"请输入处理备注，说明具体的处理措施或忽略原因",rows:4,maxlength:500,"show-count":""},null,8,["value"])])),_:1}),"RESOLVE"===t.formData.handleType?(c(),g(R,{key:0},[m(q,{label:"解决方式",name:"resolveMethod"},{default:u((()=>[m($,{value:t.formData.resolveMethod,"onUpdate:value":a[2]||(a[2]=e=>t.formData.resolveMethod=e)},{default:u((()=>[m(J,{value:"PURCHASE"},{default:u((()=>a[13]||(a[13]=[h("采购补货")]))),_:1,__:[13]}),m(J,{value:"TRANSFER"},{default:u((()=>a[14]||(a[14]=[h("调拨库存")]))),_:1,__:[14]}),m(J,{value:"ADJUST"},{default:u((()=>a[15]||(a[15]=[h("库存调整")]))),_:1,__:[15]}),m(J,{value:"OTHER"},{default:u((()=>a[16]||(a[16]=[h("其他方式")]))),_:1,__:[16]})])),_:1},8,["value"])])),_:1}),t.formData.resolveMethod&&t.formData.resolveMethod.includes("OTHER")?(c(),i(q,{key:0,label:"其他方式说明",name:"otherMethodDesc"},{default:u((()=>[m(Q,{value:t.formData.otherMethodDesc,"onUpdate:value":a[3]||(a[3]=e=>t.formData.otherMethodDesc=e),placeholder:"请说明其他解决方式",maxlength:200},null,8,["value"])])),_:1})):_("",!0),m(q,{label:"预计完成时间",name:"expectedCompleteTime"},{default:u((()=>[m(ee,{value:t.formData.expectedCompleteTime,"onUpdate:value":a[4]||(a[4]=e=>t.formData.expectedCompleteTime=e),format:"YYYY-MM-DD",placeholder:"请选择预计完成时间",style:{width:"100%"}},null,8,["value"])])),_:1})],64)):_("",!0),"IGNORE"===t.formData.handleType?(c(),g(R,{key:1},[m(q,{label:"忽略原因",name:"ignoreReason"},{default:u((()=>[m(le,{value:t.formData.ignoreReason,"onUpdate:value":a[5]||(a[5]=e=>t.formData.ignoreReason=e),placeholder:"请选择忽略原因"},{default:u((()=>[m(ae,{value:"TEMPORARY"},{default:u((()=>a[17]||(a[17]=[h("临时性问题")]))),_:1,__:[17]}),m(ae,{value:"ACCEPTABLE"},{default:u((()=>a[18]||(a[18]=[h("可接受范围")]))),_:1,__:[18]}),m(ae,{value:"PROCESSING"},{default:u((()=>a[19]||(a[19]=[h("正在处理中")]))),_:1,__:[19]}),m(ae,{value:"SYSTEM_ERROR"},{default:u((()=>a[20]||(a[20]=[h("系统错误")]))),_:1,__:[20]}),m(ae,{value:"OTHER"},{default:u((()=>a[21]||(a[21]=[h("其他原因")]))),_:1,__:[21]})])),_:1},8,["value"])])),_:1}),"OTHER"===t.formData.ignoreReason?(c(),i(q,{key:0,label:"其他原因说明",name:"otherReasonDesc"},{default:u((()=>[m(Q,{value:t.formData.otherReasonDesc,"onUpdate:value":a[6]||(a[6]=e=>t.formData.otherReasonDesc=e),placeholder:"请说明其他忽略原因",maxlength:200},null,8,["value"])])),_:1})):_("",!0),m(q,{label:"重新检查时间",name:"recheckTime"},{default:u((()=>[m(ee,{value:t.formData.recheckTime,"onUpdate:value":a[7]||(a[7]=e=>t.formData.recheckTime=e),format:"YYYY-MM-DD",placeholder:"请选择重新检查时间（可选）",style:{width:"100%"}},null,8,["value"])])),_:1})],64)):_("",!0),m(q,{label:"通知设置",name:"notifySettings"},{default:u((()=>[m($,{value:t.formData.notifySettings,"onUpdate:value":a[8]||(a[8]=e=>t.formData.notifySettings=e)},{default:u((()=>[m(J,{value:"EMAIL"},{default:u((()=>a[22]||(a[22]=[h("邮件通知")]))),_:1,__:[22]}),m(J,{value:"SMS"},{default:u((()=>a[23]||(a[23]=[h("短信通知")]))),_:1,__:[23]}),m(J,{value:"SYSTEM"},{default:u((()=>a[24]||(a[24]=[h("系统消息")]))),_:1,__:[24]})])),_:1},8,["value"]),a[25]||(a[25]=f("div",{class:"notify-desc"}," 选择处理完成后的通知方式 ",-1))])),_:1,__:[25]})])),_:1},8,["model","rules"])])])),_:1},8,["visible","confirm-loading","onOk","onCancel"])}],["__scopeId","data-v-d96975f3"]]))}}}));
