package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 采购入库单明细实体类
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@TableName(value = "erp_purchase_order_detail", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderDetail extends BaseEntity {

    /**
     * 明细ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ChineseDescription("明细ID")
    private Long id;

    /**
     * 入库单ID
     */
    @TableField("order_id")
    @ChineseDescription("入库单ID")
    private Long orderId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 数量
     */
    @TableField("quantity")
    @ChineseDescription("数量")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @TableField("unit_price")
    @ChineseDescription("单价")
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @TableField("total_price")
    @ChineseDescription("总价")
    private BigDecimal totalPrice;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 关联的商品信息（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的商品信息")
    private ErpProduct product;

}