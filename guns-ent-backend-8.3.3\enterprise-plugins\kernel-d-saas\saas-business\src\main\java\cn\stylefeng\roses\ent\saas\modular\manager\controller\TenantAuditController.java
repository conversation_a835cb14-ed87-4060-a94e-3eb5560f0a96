package cn.stylefeng.roses.ent.saas.modular.manager.controller;

import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantAuditRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantAuditService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.saas.api.constants.SaasConstants;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租户审核接口
 *
 * <AUTHOR>
 * @since 2024-02-22 19:19
 */
@RestController
@ApiResource(name = "租户审核接口")
public class TenantAuditController {

    @Resource
    private TenantAuditService tenantAuditService;

    /**
     * 提交租户审核（开通租户）
     *
     * <AUTHOR>
     * @since 2024-02-22 19:22
     */
    @PostResource(name = "提交租户审核（开通租户）", path = "/tenant/auditTenant", requiredPermission = true, requirePermissionCode = SaasConstants.TENANT_MENU_CODE)
    public ResponseData<Void> auditTenant(@RequestBody TenantAuditRequest tenantAuditRequest) {
        tenantAuditService.auditTenant(tenantAuditRequest);
        return new SuccessResponseData<>();
    }

}
