package cn.stylefeng.roses.kernel.conversion.controller;

import cn.stylefeng.roses.kernel.conversion.service.DocumentPreviewService;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文档预览
 *
 * <AUTHOR>
 * @since 2023/10/20 22:12
 */
@RestController
@ApiResource(name = "文档预览")
public class DocPreviewController {

    @Resource
    private DocumentPreviewService documentPreviewService;

    /**
     * 通用文件预览接口，可以预览图片和office文档
     *
     * <AUTHOR>
     * @since 2023/10/20 22:14
     */
    @GetResource(name = "多文件预览", path = "/documentPreview")
    public void documentPreview(@RequestParam("fileId") Long fileId) {
        documentPreviewService.documentPreview(fileId, false);
    }

    /**
     * 将图片、pdf、office通通以pdf预览
     *
     * <AUTHOR>
     * @since 2024-04-02 18:05
     */
    @GetResource(name = "多文件预览", path = "/documentPreviewByPdf")
    public void documentPreviewByPdf(@RequestParam("fileId") Long fileId) {
        documentPreviewService.documentPreview(fileId, true);
    }

}
