<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-scanner</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>scanner-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--资源扫描-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-sdk-scanner</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--properties自动提示装载-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

    </dependencies>

</project>
