package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.util.List;

/**
 * 库存验证响应
 *
 * <AUTHOR>
 * @since 2025/07/28 11:30
 */
@Data
public class InventoryValidationResponse {

    /**
     * 是否可以操作
     */
    @ChineseDescription("是否可以操作")
    private Boolean canOperate;

    /**
     * 错误信息列表
     */
    @ChineseDescription("错误信息列表")
    private List<String> errors;

    /**
     * 警告信息列表
     */
    @ChineseDescription("警告信息列表")
    private List<String> warnings;

    /**
     * 详细说明
     */
    @ChineseDescription("详细说明")
    private String description;

    /**
     * 建议信息
     */
    @ChineseDescription("建议信息")
    private String suggestion;

}