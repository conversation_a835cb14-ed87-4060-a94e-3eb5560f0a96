package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存预警记录响应参数
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Data
public class InventoryAlertRecordResponse {

    /**
     * 预警记录ID
     */
    @ChineseDescription("预警记录ID")
    private Long id;

    /**
     * 预警规则ID
     */
    @ChineseDescription("预警规则ID")
    private Long ruleId;

    /**
     * 规则名称
     */
    @ChineseDescription("规则名称")
    private String ruleName;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)
     */
    @ChineseDescription("预警类型")
    private String alertType;

    /**
     * 预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)
     */
    @ChineseDescription("预警级别")
    private String alertLevel;

    /**
     * 当前库存数量
     */
    @ChineseDescription("当前库存数量")
    private BigDecimal currentStock;

    /**
     * 触发阈值
     */
    @ChineseDescription("触发阈值")
    private BigDecimal thresholdValue;

    /**
     * 预警消息
     */
    @ChineseDescription("预警消息")
    private String alertMessage;

    /**
     * 处理状态：PENDING(待处理)、PROCESSING(处理中)、RESOLVED(已解决)、IGNORED(已忽略)
     */
    @ChineseDescription("处理状态")
    private String status;

    /**
     * 建议操作
     */
    @ChineseDescription("建议操作")
    private String suggestedAction;

    /**
     * 建议补货数量
     */
    @ChineseDescription("建议补货数量")
    private BigDecimal suggestedQuantity;

    /**
     * 建议供应商ID
     */
    @ChineseDescription("建议供应商ID")
    private Long suggestedSupplierId;

    /**
     * 建议供应商名称
     */
    @ChineseDescription("建议供应商名称")
    private String suggestedSupplierName;

    /**
     * 是否已发送通知（Y-已发送，N-未发送）
     */
    @ChineseDescription("是否已发送通知")
    private String notificationSent;

    /**
     * 已发送的通知方式
     */
    @ChineseDescription("已发送的通知方式")
    private String notificationMethods;

    /**
     * 通知发送时间
     */
    @ChineseDescription("通知发送时间")
    private Date notificationTime;

    /**
     * 处理人ID
     */
    @ChineseDescription("处理人ID")
    private Long handlerUser;

    /**
     * 处理人姓名
     */
    @ChineseDescription("处理人姓名")
    private String handlerUserName;

    /**
     * 处理时间
     */
    @ChineseDescription("处理时间")
    private Date handleTime;

    /**
     * 处理备注
     */
    @ChineseDescription("处理备注")
    private String handleRemark;

    /**
     * 预警时间
     */
    @ChineseDescription("预警时间")
    private Date alertTime;

    /**
     * 租户ID
     */
    @ChineseDescription("租户ID")
    private Long tenantId;
}
