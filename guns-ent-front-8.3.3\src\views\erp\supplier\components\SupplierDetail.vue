<template>
  <a-modal
    title="供应商详情"
    :width="1000"
    :visible="visible"
    :footer="null"
    @update:visible="updateVisible"
  >
    <a-tabs default-active-key="basic" type="card">
      <!-- 基本信息标签页 -->
      <a-tab-pane key="basic" tab="基本信息">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="供应商编码">
            {{ data.supplierCode }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商名称">
            {{ data.supplierName }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商简称">
            {{ data.supplierShortName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商类型">
            <a-tag>{{ getSupplierTypeName(data.supplierType) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="经营方式">
            <a-tag :color="getBusinessModeTagColor(data.businessMode)">
              {{ getBusinessModeName(data.businessMode) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="销售扣点">
            <span v-if="data.salesDeduction !== null && data.salesDeduction !== undefined">
              {{ data.salesDeduction }}%
            </span>
            <span v-else class="text-muted">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="联系人">
            {{ data.contactPerson || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ data.contactPhone || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="手机号码">
            {{ data.contactMobile || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱地址">
            {{ data.contactEmail || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="联系地址" :span="2">
            {{ data.contactAddress || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="营业执照号">
            {{ data.businessLicenseNo || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="税务登记号">
            {{ data.taxNo || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="开户银行">
            {{ data.bankName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="银行账号">
            {{ data.bankAccount || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="信用等级">
            <a-tag :color="getCreditLevelTagColor(data.creditLevel)">
              {{ getCreditLevelName(data.creditLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusTagColor(data.status)">
              {{ getSupplierStatusName(data.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="关联区域" :span="2">
            <div v-if="loading">
              <a-spin size="small" />
            </div>
            <div v-else-if="regionList.length > 0" class="region-tags">
              <a-tag v-for="region in regionList" :key="region.regionId" color="blue">
                {{ region.regionName }}
              </a-tag>
            </div>
            <div v-else class="empty-regions">
              <span class="empty-text">暂无关联区域</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ data.remark || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ data.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ data.updateTime }}
          </a-descriptions-item>
        </a-descriptions>
      </a-tab-pane>

      <!-- 关联商品标签页 -->
      <a-tab-pane key="products" tab="关联商品">
        <div class="products-section">
          <div class="products-header">
            <a-space>
              <span class="products-title">关联商品列表</span>
              <a-tag color="blue" v-if="supplierProducts.length > 0">
                共 {{ supplierProducts.length }} 个商品
              </a-tag>
            </a-space>
          </div>

          <div class="products-content">
            <a-spin :spinning="productsLoading">
              <div v-if="supplierProducts.length > 0">
                <a-table
                  :columns="productColumns"
                  :data-source="supplierProducts"
                  :pagination="false"
                  size="small"
                  :scroll="{ y: 300 }"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'pricingType'">
                      <a-tag :color="getPricingTypeColor(record.pricingType)">
                        {{ getPricingTypeName(record.pricingType) }}
                      </a-tag>
                    </template>
                    <template v-else-if="column.dataIndex === 'retailPrice'">
                      <span v-if="record.retailPrice">￥{{ record.retailPrice }}</span>
                      <span v-else class="text-muted">-</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'status'">
                      <a-tag :color="record.status === 'ACTIVE' ? 'green' : 'red'">
                        {{ record.status === 'ACTIVE' ? '正常' : '停用' }}
                      </a-tag>
                    </template>
                  </template>
                </a-table>
              </div>
              <div v-else class="empty-products">
                <a-empty description="暂无关联商品" />
              </div>
            </a-spin>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script>
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { SupplierApi } from '../api/SupplierApi';

export default {
  name: 'SupplierDetail',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 详情数据
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    // 区域相关状态
    const loading = ref(false);
    const regionList = ref([]);

    // 关联商品相关状态
    const supplierProducts = ref([]);
    const productsLoading = ref(false);

    // 商品表格列定义
    const productColumns = [
      {
        title: '商品编码',
        dataIndex: 'productCode',
        width: 120,
        ellipsis: true
      },
      {
        title: '商品名称',
        dataIndex: 'productName',
        width: 200,
        ellipsis: true
      },
      {
        title: '计价类型',
        dataIndex: 'pricingType',
        width: 100,
        align: 'center'
      },
      {
        title: '零售价格',
        dataIndex: 'retailPrice',
        width: 100,
        align: 'right'
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 80,
        align: 'center'
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 140,
        ellipsis: true
      }
    ];

    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 获取名称和颜色的方法（使用箭头函数保持this上下文）
    const getSupplierTypeName = (type) => SupplierApi.getSupplierTypeName(type);
    const getBusinessModeName = (businessMode) => SupplierApi.getBusinessModeName(businessMode);
    const getSupplierStatusName = (status) => SupplierApi.getSupplierStatusName(status);
    const getCreditLevelName = (level) => SupplierApi.getCreditLevelName(level);
    const getStatusTagColor = (status) => SupplierApi.getStatusTagColor(status);
    const getBusinessModeTagColor = (businessMode) => SupplierApi.getBusinessModeTagColor(businessMode);
    const getCreditLevelTagColor = (level) => SupplierApi.getCreditLevelTagColor(level);

    // 商品相关的方法（这里需要导入ProductApi，暂时使用简单实现）
    const getPricingTypeName = (pricingType) => {
      const typeMap = {
        'NORMAL': '普通商品',
        'WEIGHT': '计重商品',
        'PIECE': '计件商品',
        'VARIABLE': '不定价商品'
      };
      return typeMap[pricingType] || pricingType;
    };

    const getPricingTypeColor = (pricingType) => {
      const colorMap = {
        'NORMAL': 'blue',
        'WEIGHT': 'green',
        'PIECE': 'orange',
        'VARIABLE': 'purple'
      };
      return colorMap[pricingType] || 'default';
    };

    // 监听弹窗打开，加载区域数据和关联商品数据
    watch(() => props.visible, async (visible) => {
      if (visible && props.data?.supplierId) {
        await Promise.all([
          loadRegionData(),
          loadSupplierProducts()
        ]);
      } else {
        // 重置状态
        regionList.value = [];
        supplierProducts.value = [];
      }
    });

    // 加载供应商关联的区域数据
    const loadRegionData = async () => {
      if (!props.data?.supplierId) return;

      loading.value = true;
      try {
        const result = await SupplierApi.getSupplierRegions({ supplierId: props.data.supplierId });
        if (result && Array.isArray(result)) {
          regionList.value = result;
        } else {
          regionList.value = [];
        }
      } catch (error) {
        console.error('获取供应商关联区域失败:', error);
        message.error('获取供应商关联区域失败');
        regionList.value = [];
      } finally {
        loading.value = false;
      }
    };

    // 加载供应商关联的商品数据
    const loadSupplierProducts = async () => {
      if (!props.data?.supplierId) return;

      productsLoading.value = true;
      try {
        const result = await SupplierApi.getSupplierProducts({ supplierId: props.data.supplierId });
        if (result && Array.isArray(result)) {
          supplierProducts.value = result;
        } else {
          supplierProducts.value = [];
        }
      } catch (error) {
        console.error('获取供应商关联商品失败:', error);
        message.error('获取供应商关联商品失败');
        supplierProducts.value = [];
      } finally {
        productsLoading.value = false;
      }
    };

    return {
      loading,
      regionList,
      supplierProducts,
      productsLoading,
      productColumns,
      updateVisible,
      getSupplierTypeName,
      getBusinessModeName,
      getSupplierStatusName,
      getCreditLevelName,
      getStatusTagColor,
      getBusinessModeTagColor,
      getCreditLevelTagColor,
      getPricingTypeName,
      getPricingTypeColor
    };
  }
};
</script>

<style scoped>
.region-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.empty-regions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.empty-text {
  color: #999;
  font-style: italic;
}

.text-muted {
  color: #999;
  font-style: italic;
}

.products-section {
  margin-top: 16px;
}

.products-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.products-title {
  font-weight: 500;
  font-size: 16px;
  color: #262626;
}

.products-content {
  min-height: 200px;
}

.empty-products {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

:deep(.ant-table-thead > tr > th) {
  padding: 8px 12px;
  background-color: #fafafa;
}

:deep(.ant-tabs-content-holder) {
  padding: 16px 0;
}

:deep(.ant-tabs-tab) {
  padding: 8px 16px;
}
</style>
