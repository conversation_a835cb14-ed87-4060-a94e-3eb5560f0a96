import{_ as x,r as v,o as O,a as k,f as B,w as l,d as e,l as N,u as C,v as S,G as j,H as w,M as y}from"./index-18a1ea24.js";const F={__name:"file-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(_,{emit:p}){const s=_,m=p,r=v(!1),a=v({});O(()=>{s.data&&(a.value=Object.assign({},s.data))});const d=f=>{m("update:visible",f)},b=()=>{d(!1)};return(f,t)=>{const u=N,o=C,i=S,c=j,g=w,U=y;return k(),B(U,{width:700,maskClosable:!1,visible:s.visible,"confirm-loading":r.value,forceRender:!0,title:"\u6587\u4EF6\u8BE6\u60C5","body-style":{paddingBottom:"8px",height:"550px",overflowY:"auto"},"onUpdate:visible":d,onOk:b,onClose:t[8]||(t[8]=n=>d(!1))},{default:l(()=>[e(g,{ref:"formRef",model:a.value,layout:"vertical"},{default:l(()=>[e(c,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(o,{label:"\u6587\u4EF6\u7F16\u7801",name:"fileCode"},{default:l(()=>[e(u,{value:a.value.fileCode,"onUpdate:value":t[0]||(t[0]=n=>a.value.fileCode=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(o,{label:"\u6587\u4EF6\u4ED3\u5E93",name:"fileBucket"},{default:l(()=>[e(u,{value:a.value.fileBucket,"onUpdate:value":t[1]||(t[1]=n=>a.value.fileBucket=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(o,{label:"\u6587\u4EF6\u540D\u79F0",name:"fileOriginName"},{default:l(()=>[e(u,{value:a.value.fileOriginName,"onUpdate:value":t[2]||(t[2]=n=>a.value.fileOriginName=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(o,{label:"\u662F\u5426\u4E3A\u673A\u5BC6\u6587\u4EF6",name:"secretFlag"},{default:l(()=>[e(u,{value:a.value.secretFlag,"onUpdate:value":t[3]||(t[3]=n=>a.value.secretFlag=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(o,{label:"\u6587\u4EF6\u540E\u7F00",name:"fileSuffix"},{default:l(()=>[e(u,{value:a.value.fileSuffix,"onUpdate:value":t[4]||(t[4]=n=>a.value.fileSuffix=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(o,{label:"\u6587\u4EF6\u5927\u5C0F",name:"fileSizeInfo"},{default:l(()=>[e(u,{value:a.value.fileSizeInfo,"onUpdate:value":t[5]||(t[5]=n=>a.value.fileSizeInfo=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:24},{default:l(()=>[e(o,{label:"\u552F\u4E00\u6807\u8BC6",name:"fileObjectName"},{default:l(()=>[e(u,{value:a.value.fileObjectName,"onUpdate:value":t[6]||(t[6]=n=>a.value.fileObjectName=n),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(i,{span:24},{default:l(()=>[e(o,{label:"\u5B58\u50A8\u8DEF\u5F84",name:"fileUrl"},{default:l(()=>[e(u,{value:a.value.fileUrl,"onUpdate:value":t[7]||(t[7]=n=>a.value.fileUrl=n),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])}}},z=x(F,[["__scopeId","data-v-bdd3a1c0"]]);export{z as default};
