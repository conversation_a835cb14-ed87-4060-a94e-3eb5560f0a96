package cn.stylefeng.roses.ent.saas.modular.auth.service;

import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.PackageAuthInfo;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.kernel.sys.api.pojo.role.response.RoleBindPermissionResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * 租户-功能包授权范围服务类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
public interface TenantPackageAuthService extends IService<TenantPackageAuth> {

    /**
     * 删除功能包绑定的权限范围
     *
     * <AUTHOR>
     * @since 2024-01-21 16:26
     */
    void removeByPackageId(Long packageId);

    /**
     * 获取功能包包含的所有菜单和所有的菜单功能id
     *
     * <AUTHOR>
     * @since 2024-01-21 18:44
     */
    PackageAuthInfo getPackageAuthInfo(List<Long> packageIdList);

    /**
     * 获取功能包绑定的权限列表
     *
     * <AUTHOR>
     * @since 2024/1/21 23:44
     */
    RoleBindPermissionResponse getPackageAuth(PackageBindPermissionRequest packageBindPermissionRequest);

    /**
     * 获取功能包绑定的菜单和菜单功能id集合
     *
     * <AUTHOR>
     * @since 2024/1/21 23:51
     */
    Set<Long> getPackageAuthBusinessList(Long packageId);

    /**
     * 更新功能包绑定的权限
     *
     * <AUTHOR>
     * @since 2024/1/21 23:58
     */
    void updatePackageBindPermission(PackageBindPermissionRequest packageBindPermissionRequest);

    /**
     * 刷新功能包对应的租户的角色的权限和权限范围
     *
     * <AUTHOR>
     * @since 2024-01-23 11:54
     */
    void refreshPackageTenantRoles(PackageBindPermissionRequest packageBindPermissionRequest);

    /**
     * 修改租户绑定的功能包对应的权限
     *
     * <AUTHOR>
     * @since 2024-01-23 15:00
     */
    void updateTenantPackageRole(Long tenantId, List<Long> tenantPackageIdList);

}
