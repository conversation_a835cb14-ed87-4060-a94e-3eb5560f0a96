<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns</groupId>
        <artifactId>kernel-d-erp</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>erp-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--ERP的api-->
        <dependency>
            <groupId>com.javaguns</groupId>
            <artifactId>erp-api</artifactId>
            <version>8.3.3</version>
        </dependency>

        <!--系统管理模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库操作模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源扫描模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- H2数据库用于测试 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- JUnit 5 测试套件支持 -->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
