package cn.stylefeng.roses.kernel.erp.modular.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpCustomerExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpCustomer;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpCustomerRegion;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpCustomerRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.modular.customer.mapper.ErpCustomerMapper;
import cn.stylefeng.roses.kernel.erp.modular.customer.mapper.ErpCustomerRegionMapper;
import cn.stylefeng.roses.kernel.erp.modular.customer.service.ErpCustomerRegionService;
import cn.stylefeng.roses.kernel.erp.modular.region.service.ErpRegionService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 客户-区域关联Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
@Service
public class ErpCustomerRegionServiceImpl extends ServiceImpl<ErpCustomerRegionMapper, ErpCustomerRegion> implements ErpCustomerRegionService {

    @Resource
    private ErpCustomerRegionMapper erpCustomerRegionMapper;

    @Resource
    private ErpCustomerMapper erpCustomerMapper;

    @Resource
    private ErpRegionService erpRegionService;

    @Override
    public List<ErpRegionResponse> getCustomerRegions(ErpCustomerRegionRequest erpCustomerRegionRequest) {
        // 校验客户是否存在
        this.checkCustomerExists(erpCustomerRegionRequest.getCustomerId());

        // 查询客户关联的区域ID列表
        List<Long> regionIds = erpCustomerRegionMapper.getRegionIdsByCustomerId(erpCustomerRegionRequest.getCustomerId());

        if (CollUtil.isEmpty(regionIds)) {
            return new ArrayList<>();
        }

        // 查询区域详细信息
        List<ErpRegionResponse> regionList = new ArrayList<>();
        for (Long regionId : regionIds) {
            ErpRegionRequest regionRequest = new ErpRegionRequest();
            regionRequest.setRegionId(regionId);
            ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);
            regionList.add(regionResponse);
        }

        return regionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomerRegions(ErpCustomerRegionRequest erpCustomerRegionRequest) {
        Long customerId = erpCustomerRegionRequest.getCustomerId();
        List<Long> regionIds = erpCustomerRegionRequest.getRegionIds();

        // 校验客户是否存在
        this.checkCustomerExists(customerId);

        // 校验区域是否存在
        this.checkRegionsExist(regionIds);

        // 删除原有关联
        LambdaQueryWrapper<ErpCustomerRegion> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(ErpCustomerRegion::getCustomerId, customerId);
        this.remove(deleteWrapper);

        // 添加新的关联
        if (CollUtil.isNotEmpty(regionIds)) {
            List<ErpCustomerRegion> customerRegionList = new ArrayList<>();
            Long currentUserId = LoginContext.me().getLoginUser().getUserId();
            Date now = new Date();

            for (Long regionId : regionIds) {
                ErpCustomerRegion customerRegion = new ErpCustomerRegion();
                customerRegion.setCustomerId(customerId);
                customerRegion.setRegionId(regionId);
                customerRegion.setCreateUser(currentUserId);
                customerRegion.setCreateTime(now);
                customerRegionList.add(customerRegion);
            }

            this.saveBatch(customerRegionList);
        }
    }

    @Override
    public PageResult<ErpCustomerResponse> getCustomersByRegion(ErpCustomerRegionRequest erpCustomerRegionRequest) {
        Long regionId = erpCustomerRegionRequest.getRegionId();

        // 校验区域是否存在
        this.checkRegionExists(regionId);

        // 查询区域信息，获取区域路径
        ErpRegionRequest regionRequest = new ErpRegionRequest();
        regionRequest.setRegionId(regionId);
        ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);

        // 是否包含子区域
        Boolean includeChildRegions = erpCustomerRegionRequest.getIncludeChildRegions() != null ? 
                erpCustomerRegionRequest.getIncludeChildRegions() : true;
        List<Long> childRegionIds = new ArrayList<>();
        childRegionIds.add(regionId);

        // 如果包含子区域，查询所有子区域ID
        if (includeChildRegions) {
            // 查询所有区域
            ErpRegionRequest allRegionRequest = new ErpRegionRequest();
            List<ErpRegionResponse> allRegions = erpRegionService.findList(allRegionRequest);

            // 找出所有子区域
            for (ErpRegionResponse region : allRegions) {
                if (region.getRegionPath() != null && region.getRegionPath().startsWith(regionResponse.getRegionPath() + "/")) {
                    childRegionIds.add(region.getRegionId());
                }
            }
        }

        // 分页查询
        Page<ErpCustomerResponse> page = PageFactory.defaultPage();
        List<ErpCustomerResponse> customerList = erpCustomerRegionMapper.getCustomersByRegionId(
                page, regionId, includeChildRegions, childRegionIds);

        // 填充客户扩展信息
        for (ErpCustomerResponse customer : customerList) {
            this.fillCustomerExtInfo(customer);
        }

        return PageResultFactory.createPageResult(customerList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public Long countCustomersByRegion(ErpCustomerRegionRequest erpCustomerRegionRequest) {
        Long regionId = erpCustomerRegionRequest.getRegionId();

        // 校验区域是否存在
        this.checkRegionExists(regionId);

        // 查询区域信息，获取区域路径
        ErpRegionRequest regionRequest = new ErpRegionRequest();
        regionRequest.setRegionId(regionId);
        ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);

        // 是否包含子区域
        Boolean includeChildRegions = erpCustomerRegionRequest.getIncludeChildRegions() != null ? 
                erpCustomerRegionRequest.getIncludeChildRegions() : true;
        List<Long> childRegionIds = new ArrayList<>();
        childRegionIds.add(regionId);

        // 如果包含子区域，查询所有子区域ID
        if (includeChildRegions) {
            // 查询所有区域
            ErpRegionRequest allRegionRequest = new ErpRegionRequest();
            List<ErpRegionResponse> allRegions = erpRegionService.findList(allRegionRequest);

            // 找出所有子区域
            for (ErpRegionResponse region : allRegions) {
                if (region.getRegionPath() != null && region.getRegionPath().startsWith(regionResponse.getRegionPath() + "/")) {
                    childRegionIds.add(region.getRegionId());
                }
            }
        }

        // 统计客户数量
        return erpCustomerRegionMapper.countCustomersByRegionId(regionId, includeChildRegions, childRegionIds);
    }

    /**
     * 校验客户是否存在
     */
    private void checkCustomerExists(Long customerId) {
        ErpCustomer customer = erpCustomerMapper.selectById(customerId);
        if (ObjectUtil.isNull(customer)) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_NOT_EXIST);
        }
    }

    /**
     * 校验区域是否存在
     */
    private void checkRegionExists(Long regionId) {
        ErpRegionRequest regionRequest = new ErpRegionRequest();
        regionRequest.setRegionId(regionId);
        try {
            erpRegionService.detail(regionRequest);
        } catch (Exception e) {
            throw new ServiceException("erp-region-module", "区域不存在", "区域ID: " + regionId + " 不存在");
        }
    }

    /**
     * 校验多个区域是否存在
     */
    private void checkRegionsExist(List<Long> regionIds) {
        if (CollUtil.isEmpty(regionIds)) {
            return;
        }

        for (Long regionId : regionIds) {
            this.checkRegionExists(regionId);
        }
    }

    /**
     * 填充客户扩展信息
     */
    private void fillCustomerExtInfo(ErpCustomerResponse customer) {
        // 填充客户类型名称
        switch (customer.getCustomerType()) {
            case "ENTERPRISE":
                customer.setCustomerTypeName("企业");
                break;
            case "INDIVIDUAL":
                customer.setCustomerTypeName("个人");
                break;
            case "RETAIL":
                customer.setCustomerTypeName("零售");
                break;
            default:
                customer.setCustomerTypeName("未知");
                break;
        }

        // 填充客户等级名称
        switch (customer.getCustomerLevel()) {
            case "DIAMOND":
                customer.setCustomerLevelName("钻石");
                break;
            case "GOLD":
                customer.setCustomerLevelName("黄金");
                break;
            case "SILVER":
                customer.setCustomerLevelName("白银");
                break;
            case "BRONZE":
                customer.setCustomerLevelName("青铜");
                break;
            default:
                customer.setCustomerLevelName("未知");
                break;
        }

        // 填充状态名称
        switch (customer.getStatus()) {
            case "ACTIVE":
                customer.setStatusName("正常");
                break;
            case "INACTIVE":
                customer.setStatusName("停用");
                break;
            case "FROZEN":
                customer.setStatusName("冻结");
                break;
            default:
                customer.setStatusName("未知");
                break;
        }

        // 填充区域信息
        if (ObjectUtil.isNotNull(customer.getRegionId())) {
            ErpRegionRequest regionRequest = new ErpRegionRequest();
            regionRequest.setRegionId(customer.getRegionId());
            try {
                ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);
                customer.setRegionName(regionResponse.getRegionName());
            } catch (Exception e) {
                // 忽略异常，区域可能已被删除
                customer.setRegionName("未知区域");
            }
        }

        // 查询客户关联的区域
        List<Long> regionIds = erpCustomerRegionMapper.getRegionIdsByCustomerId(customer.getCustomerId());
        customer.setRegionIds(regionIds);

        if (CollUtil.isNotEmpty(regionIds)) {
            List<ErpRegionResponse> regionList = new ArrayList<>();
            for (Long regionId : regionIds) {
                ErpRegionRequest regionRequest = new ErpRegionRequest();
                regionRequest.setRegionId(regionId);
                try {
                    ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);
                    regionList.add(regionResponse);
                } catch (Exception e) {
                    // 忽略异常，区域可能已被删除
                }
            }
            customer.setRegionList(regionList);
        }
    }
}