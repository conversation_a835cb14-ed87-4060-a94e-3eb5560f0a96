<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-micro</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>micro-project-gateway</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- 微服务api -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>micro-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 客户端负载均衡 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>micro-sdk-loadbalancer</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--微服务客户端负载均衡-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>micro-sdk-loadbalancer</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 资源扫描api -->
        <!-- 获取资源缓存的缓存前缀 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- jwt模块，解析请求token -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>jwt-sdk</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 鉴权sdk -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>auth-sdk</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 缓存服务 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-spring-boot-starter-redis</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 网关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>

        <!--open feign远程调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <!-- 服务降级 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 服务监控 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>guns-gateway</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 如果没有该项配置，则devtools不会起作用，即应用不会restart -->
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/webapp</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>


</project>
