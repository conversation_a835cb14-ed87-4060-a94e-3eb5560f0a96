System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-da493ce1.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var n,a,s,t,i,u,c,d,r,o,g,v,y;return{setters:[e=>{n=e.r,a=e.a,s=e.c,t=e.d,i=e.w,u=e.g,c=e.b,d=e.t,r=e.f,o=e.h,g=e.B,v=e.a0},null,e=>{y=e._},null,null,null,null,null],execute:function(){const l={class:"guns-body guns-body-card"},x={style:{"margin-top":"12px"}};e("default",{__name:"index",setup(e){const b=n(!1),j=n([]),_=()=>{b.value=!0},f=e=>{j.value=e,b.value=!1};return(e,n)=>{const p=g,m=y,k=v;return a(),s("div",l,[t(k,{title:"机构人员选择",bordered:!1},{default:i((()=>[t(p,{class:"ele-btn-icon",onClick:_},{default:i((()=>n[1]||(n[1]=[u(" 选择机构人员 ")]))),_:1,__:[1]}),c("div",x,"选择数据: "+d(j.value.map((e=>e.name)).join(",")),1),b.value?(a(),r(m,{key:0,visible:b.value,"onUpdate:visible":n[0]||(n[0]=e=>b.value=e),list:j.value,title:"机构人员选择",onDone:f},null,8,["visible","list"])):o("",!0)])),_:1})])}}})}}}));
