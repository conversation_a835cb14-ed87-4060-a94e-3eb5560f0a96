<template>
  <!-- 新增编辑弹框 -->
  <a-modal
    :width="700"
    :maskClosable="false"
    :visible="props.visible"
    :confirm-loading="loading"
    :forceRender="true"
    title="新建临时秘钥"
    :body-style="{ paddingBottom: '8px', height: '500px', overflowY: 'auto' }"
    @update:visible="updateVisible"
    @ok="save"
    @close="updateVisible(false)"
  >
    <sys-user-secret-key-form v-model:form="form" ref="sysUserSecretKeyFormRef" />
  </a-modal>
</template>

<script setup name="SysUserSecretKeyAddEdit">
import { ref } from 'vue';
import SysUserSecretKeyForm from './sys-user-secret-key-form.vue';
import { message } from 'ant-design-vue';
import { SysUserSecretKeyApi } from '../api/SysUserSecretKeyApi';

const props = defineProps({
  visible: Boolean,
  data: Object
});

const emits = defineEmits(['update:visible', 'done']);

// 弹框加载
const loading = ref(false);

// 表单数据
const form = ref({
  sysUserSecretKeySort: 1000,
  secretOnceFlag: 'N'
});

// ref
const sysUserSecretKeyFormRef = ref(null);

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};

// 点击保存
const save = async () => {
  sysUserSecretKeyFormRef.value.$refs.formRef.validate().then(async valid => {
    if (valid) {
      // 修改加载框为正在加载
      loading.value = true;

      SysUserSecretKeyApi.add(form.value)
        .then(async result => {
          // 移除加载框
          loading.value = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          emits('done');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<style></style>
