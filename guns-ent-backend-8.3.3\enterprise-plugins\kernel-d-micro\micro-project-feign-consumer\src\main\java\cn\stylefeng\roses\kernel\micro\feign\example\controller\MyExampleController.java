package cn.stylefeng.roses.kernel.micro.feign.example.controller;

import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import org.springframework.web.bind.annotation.RestController;

/**
 * 一个demo控制器，用来采集资源
 *
 * <AUTHOR>
 * @since 2023/8/9 23:34
 */
@RestController
@ApiResource(name = "我的控制器")
public class MyExampleController {

    /**
     * 测试一个接口
     *
     * <AUTHOR>
     * @since 2023/8/9 23:34
     */
    @GetResource(name = "我的接口", path = "/myApi")
    public ResponseData<LoginUser> test() {

        // 测试获取当前登录用户
        LoginUser loginUser = LoginContext.me().getLoginUser();

        return new SuccessResponseData<>(loginUser);
    }

}
