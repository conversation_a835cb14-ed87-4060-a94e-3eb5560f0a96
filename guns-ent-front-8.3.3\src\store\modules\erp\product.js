/**
 * 商品状态管理
 * 
 * 管理商品相关的状态和操作，包括：
 * - 商品列表管理
 * - 当前商品信息
 * - 搜索和筛选状态
 * - CRUD操作
 * - 计价类型验证
 * - 供应商关联查询
 * - 价格管理
 */
import { defineStore } from 'pinia';
import { ProductApi } from '@/views/erp/product/api/ProductApi';
import { message } from 'ant-design-vue';

export const useProductStore = defineStore({
  id: 'product',
  state: () => ({
    // 商品列表数据
    products: [],
    // 当前选中的商品
    currentProduct: null,
    // 加载状态
    loading: false,
    // 搜索查询条件
    searchQuery: '',
    // 供应商筛选
    supplierFilter: '',
    // 计价类型筛选
    pricingTypeFilter: '',
    // 商品状态筛选
    statusFilter: '',
    // 商品分类筛选
    categoryFilter: '',
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    // 按供应商查询的商品列表
    supplierProducts: [],
    // 供应商商品加载状态
    supplierProductsLoading: false
  }),
  
  getters: {
    /**
     * 获取筛选后的商品列表
     */
    filteredProducts: (state) => {
      let filtered = [...state.products];
      
      // 按搜索条件筛选
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(product => 
          product.productName?.toLowerCase().includes(query) ||
          product.productCode?.toLowerCase().includes(query) ||
          product.barcode?.toLowerCase().includes(query) ||
          product.specification?.toLowerCase().includes(query)
        );
      }
      
      // 按供应商筛选
      if (state.supplierFilter) {
        filtered = filtered.filter(product => 
          product.supplierId === state.supplierFilter
        );
      }
      
      // 按计价类型筛选
      if (state.pricingTypeFilter) {
        filtered = filtered.filter(product => 
          product.pricingType === state.pricingTypeFilter
        );
      }
      
      // 按状态筛选
      if (state.statusFilter) {
        filtered = filtered.filter(product => 
          product.status === state.statusFilter
        );
      }
      
      // 按分类筛选
      if (state.categoryFilter) {
        filtered = filtered.filter(product => 
          product.categoryId === state.categoryFilter
        );
      }
      
      return filtered;
    },
    
    /**
     * 获取活跃的商品列表（状态为ACTIVE）
     */
    activeProducts: (state) => {
      return state.products.filter(product => product.status === 'ACTIVE');
    },
    
    /**
     * 获取计价类型统计
     */
    pricingTypeStats: (state) => {
      const stats = {
        NORMAL: 0,
        WEIGHT: 0,
        PIECE: 0,
        VARIABLE: 0
      };
      
      state.products.forEach(product => {
        if (stats.hasOwnProperty(product.pricingType)) {
          stats[product.pricingType]++;
        }
      });
      
      return stats;
    },
    
    /**
     * 获取商品状态统计
     */
    statusStats: (state) => {
      const stats = {
        ACTIVE: 0,
        INACTIVE: 0,
        DISCONTINUED: 0
      };
      
      state.products.forEach(product => {
        if (stats.hasOwnProperty(product.status)) {
          stats[product.status]++;
        }
      });
      
      return stats;
    },
    
    /**
     * 获取低库存商品列表
     */
    lowStockProducts: (state) => {
      return state.products.filter(product => 
        product.currentStock !== undefined && 
        product.minStock !== undefined && 
        product.currentStock <= product.minStock
      );
    }
  },
  
  actions: {
    /**
     * 获取商品列表（分页）
     */
    async fetchProducts(params = {}) {
      this.loading = true;
      try {
        const queryParams = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          productName: this.searchQuery || undefined,
          supplierId: this.supplierFilter || undefined,
          pricingType: this.pricingTypeFilter || undefined,
          status: this.statusFilter || undefined,
          categoryId: this.categoryFilter || undefined,
          ...params
        };
        
        const result = await ProductApi.findPage(queryParams);
        
        if (result && result.rows) {
          this.products = result.rows;
          this.pagination.total = result.totalRows;
          this.pagination.current = result.pageNo;
          this.pagination.pageSize = result.pageSize;
        }
        
        return result;
      } catch (error) {
        console.error('获取商品列表失败:', error);
        message.error('获取商品列表失败');
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 获取所有商品列表（不分页）
     */
    async fetchAllProducts(params = {}) {
      try {
        const result = await ProductApi.findList(params);
        
        if (result) {
          return result;
        }
        
        return [];
      } catch (error) {
        console.error('获取所有商品列表失败:', error);
        message.error('获取所有商品列表失败');
        throw error;
      }
    },
    
    /**
     * 根据供应商获取商品列表
     */
    async fetchProductsBySupplier(supplierId) {
      this.supplierProductsLoading = true;
      try {
        const result = await ProductApi.getProductsBySupplier({ supplierId });
        
        if (result) {
          this.supplierProducts = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取供应商商品列表失败:', error);
        message.error('获取供应商商品列表失败');
        throw error;
      } finally {
        this.supplierProductsLoading = false;
      }
    },
    
    /**
     * 创建商品
     */
    async createProduct(productData) {
      try {
        const result = await ProductApi.add(productData);
        
        if (result) {
          message.success('商品创建成功');
          // 重新获取商品列表
          await this.fetchProducts();
        }
        
        return result;
      } catch (error) {
        console.error('创建商品失败:', error);
        message.error('创建商品失败');
        throw error;
      }
    },
    
    /**
     * 更新商品
     */
    async updateProduct(productData) {
      try {
        const result = await ProductApi.edit(productData);
        
        if (result) {
          message.success('商品更新成功');
          // 重新获取商品列表
          await this.fetchProducts();
          
          // 如果更新的是当前商品，更新当前商品信息
          if (this.currentProduct && this.currentProduct.productId === productData.productId) {
            await this.fetchProductDetail(productData.productId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('更新商品失败:', error);
        message.error('更新商品失败');
        throw error;
      }
    },
    
    /**
     * 删除商品
     */
    async deleteProduct(productId) {
      try {
        const result = await ProductApi.delete({ productId });
        
        if (result) {
          message.success('商品删除成功');
          // 重新获取商品列表
          await this.fetchProducts();
          
          // 如果删除的是当前商品，清空当前商品
          if (this.currentProduct && this.currentProduct.productId === productId) {
            this.currentProduct = null;
          }
        }
        
        return result;
      } catch (error) {
        console.error('删除商品失败:', error);
        message.error('删除商品失败');
        throw error;
      }
    },
    
    /**
     * 批量删除商品
     */
    async batchDeleteProducts(productIds) {
      try {
        const result = await ProductApi.batchDelete({ productIdList: productIds });
        
        if (result) {
          message.success(`成功删除 ${productIds.length} 个商品`);
          // 重新获取商品列表
          await this.fetchProducts();
          
          // 如果删除的包含当前商品，清空当前商品
          if (this.currentProduct && productIds.includes(this.currentProduct.productId)) {
            this.currentProduct = null;
          }
        }
        
        return result;
      } catch (error) {
        console.error('批量删除商品失败:', error);
        message.error('批量删除商品失败');
        throw error;
      }
    },
    
    /**
     * 获取商品详情
     */
    async fetchProductDetail(productId) {
      try {
        const result = await ProductApi.detail({ productId });
        
        if (result) {
          this.currentProduct = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取商品详情失败:', error);
        message.error('获取商品详情失败');
        throw error;
      }
    },
    
    /**
     * 更新商品状态
     */
    async updateProductStatus(productId, status) {
      try {
        const result = await ProductApi.updateStatus({ productId, status });
        
        if (result) {
          message.success('商品状态更新成功');
          // 重新获取商品列表
          await this.fetchProducts();
          
          // 如果更新的是当前商品，更新当前商品信息
          if (this.currentProduct && this.currentProduct.productId === productId) {
            this.currentProduct.status = status;
          }
        }
        
        return result;
      } catch (error) {
        console.error('更新商品状态失败:', error);
        message.error('更新商品状态失败');
        throw error;
      }
    },
    
    /**
     * 校验商品编码
     */
    async validateProductCode(productCode, productId = null) {
      try {
        const result = await ProductApi.validateCode({ 
          productCode, 
          productId 
        });
        
        return result;
      } catch (error) {
        console.error('校验商品编码失败:', error);
        throw error;
      }
    },
    
    /**
     * 校验条形码
     */
    async validateBarcode(barcode, productId = null) {
      try {
        const result = await ProductApi.validateBarcode({ 
          barcode, 
          productId 
        });
        
        return result;
      } catch (error) {
        console.error('校验条形码失败:', error);
        throw error;
      }
    },
    
    /**
     * 校验计价类型变更的影响
     */
    async validatePricingTypeChange(productId, pricingType) {
      try {
        const result = await ProductApi.validatePricingTypeChange({ 
          productId, 
          pricingType 
        });
        
        return result;
      } catch (error) {
        console.error('校验计价类型变更失败:', error);
        message.error('校验计价类型变更失败');
        throw error;
      }
    },
    
    /**
     * 设置搜索条件
     */
    setSearchQuery(query) {
      this.searchQuery = query;
    },
    
    /**
     * 设置供应商筛选
     */
    setSupplierFilter(supplierId) {
      this.supplierFilter = supplierId;
    },
    
    /**
     * 设置计价类型筛选
     */
    setPricingTypeFilter(pricingType) {
      this.pricingTypeFilter = pricingType;
    },
    
    /**
     * 设置状态筛选
     */
    setStatusFilter(status) {
      this.statusFilter = status;
    },
    
    /**
     * 设置分类筛选
     */
    setCategoryFilter(categoryId) {
      this.categoryFilter = categoryId;
    },
    
    /**
     * 设置分页信息
     */
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    
    /**
     * 设置当前商品
     */
    setCurrentProduct(product) {
      this.currentProduct = product;
    },
    
    /**
     * 清空当前商品
     */
    clearCurrentProduct() {
      this.currentProduct = null;
    },
    
    /**
     * 清空搜索和筛选条件
     */
    clearFilters() {
      this.searchQuery = '';
      this.supplierFilter = '';
      this.pricingTypeFilter = '';
      this.statusFilter = '';
      this.categoryFilter = '';
    },
    
    /**
     * 重置分页到第一页
     */
    resetPagination() {
      this.pagination.current = 1;
    },
    
    /**
     * 刷新商品列表
     */
    async refreshProducts() {
      await this.fetchProducts();
    },
    
    /**
     * 清空供应商商品列表
     */
    clearSupplierProducts() {
      this.supplierProducts = [];
    },
    
    /**
     * 根据计价类型获取价格字段名
     */
    getPriceFieldByType(pricingType) {
      switch (pricingType) {
        case 'NORMAL':
          return 'retailPrice';
        case 'WEIGHT':
          return 'unitPrice';
        case 'PIECE':
          return 'piecePrice';
        case 'VARIABLE':
          return 'referencePrice';
        default:
          return 'retailPrice';
      }
    },
    
    /**
     * 根据计价类型获取价格值
     */
    getPriceByType(product, pricingType) {
      const priceField = this.getPriceFieldByType(pricingType || product.pricingType);
      return product[priceField];
    },
    
    /**
     * 格式化商品价格显示
     */
    formatProductPrice(product) {
      if (!product) return '-';
      
      const price = this.getPriceByType(product, product.pricingType);
      return ProductApi.formatPrice(price, product.pricingType);
    }
  }
});