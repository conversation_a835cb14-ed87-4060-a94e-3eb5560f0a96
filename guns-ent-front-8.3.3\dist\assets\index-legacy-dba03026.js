System.register(["./index-legacy-ee1db0c7.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-efb51034.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js"],(function(e,t){"use strict";var a,l,i,d,s,o,r,n,c,u,p,v,h,g,f,x,b,m,w,y,I,k,R,_,L,S,T,C,z,F,O,N,M,D;return{setters:[e=>{a=e._,l=e.s,i=e.a5,d=e.r,s=e.a,o=e.c,r=e.b,n=e.d,c=e.w,u=e.f,p=e.F,v=e.e,h=e.t,g=e.h,f=e.I,x=e.l,b=e.X,m=e.aK,w=e.g,y=e.U,I=e.o,k=e.aR,R=e.aS,_=e.bg,L=e.S,S=e.L,T=e.aL,C=e.m,z=e.j,F=e.T,O=e.aA},e=>{N=e._},e=>{M=e._},null,null,e=>{D=e.O}],execute:function(){var t=document.createElement("style");function E(e,t,a,l,i){if(t){let t={bizId:e[l],name:e[i],...e};a.find((t=>t.bizId==e[l]))||a.push(t)}else a.splice(a.findIndex((t=>t.bizId===e[l])),1)}function U(e,t,a,l,i){e?t.forEach((e=>{if(!a.find((t=>t.bizId==e[l]))){let t={bizId:e[l],name:e[i],...e};a.push(t)}})):t.forEach((e=>{for(let t=a.length-1;t>=0;t--)e[l]==a[t].bizId&&a.splice(t,1)}))}function K(e,t,a,l){let i=[],d=[];return l?a.forEach((e=>{e.subValue==l&&i.push(e)})):i=a,e&&e.length>0&&e.forEach((e=>{i.find((a=>a.bizId==e[t]))&&d.push(e[t])})),d}t.textContent=".selected[data-v-b6ea496b]{width:100%;height:100%;overflow-y:auto}.selected .selected-top[data-v-b6ea496b]{width:100%;padding:8px 16px;height:48px;line-height:32px;position:relative;font-size:16px;background:#f7f7f9;color:#43505e}.selected .selected-top .selected-del[data-v-b6ea496b]{position:absolute;right:12px;cursor:pointer}.selected .selected-top .selected-del[data-v-b6ea496b]:hover{color:red}.selected .selected-search[data-v-b6ea496b]{margin-top:10px;padding:0 16px;height:36px;border-radius:5px;margin-bottom:16px}.selected .selected-search .ant-input-affix-wrapper[data-v-b6ea496b]{height:100%}.selected .selected-bottom[data-v-b6ea496b]{padding:0 16px 16px;height:calc(100% - 110px)}.selected .selected-bottom .bottom-list[data-v-b6ea496b]{width:100%;height:100%;overflow-y:auto;position:relative}.selected .selected-bottom .bottom-list .ant-empty[data-v-b6ea496b]{position:absolute;left:50%;top:50%;margin-left:-32px;margin-top:-42px}.selected .selected-bottom .list[data-v-b6ea496b]{display:flex;width:100%;padding:0 12px;height:100%}.selected .selected-bottom .list .selected-name[data-v-b6ea496b]{width:80%;padding:12px 0}.selected .selected-bottom .list .selected-del[data-v-b6ea496b]{width:20%;cursor:pointer;padding:12px 0;display:flex;align-items:center;justify-content:right}.selected .selected-bottom .list .selected-del[data-v-b6ea496b]:hover{color:red}.user-select[data-v-7f79180d]{width:100%;height:100%;display:flex;overflow:hidden;border-radius:8px}.user-select .user-select-org[data-v-7f79180d]{width:100%;height:100%;border:1px solid rgba(197,207,209,.4)}.user-select .user-header[data-v-7f79180d]{height:48px;padding:8px 16px;line-height:32px;background:#f7f7f9;color:#43505e;font-size:16px}.user-select .search[data-v-7f79180d]{margin-top:10px;padding:0 16px;height:36px;border-radius:5px;margin-bottom:16px}.user-select .search .ant-input-affix-wrapper[data-v-7f79180d]{height:100%}.user-select .user-table[data-v-7f79180d]{margin-top:10px;height:calc(100% - 120px);padding:0 16px}.user-select .user-select-right[data-v-7f79180d]{width:100%;height:100%;display:flex;flex-direction:row}.user-select .user-select-right .user-select-user[data-v-7f79180d]{width:calc(100% - 308px);height:100%;border:1px solid rgba(197,207,209,.4)}.user-select .user-select-right .user-select-list[data-v-7f79180d]{margin-left:16px;border-radius:8px;border:1px solid rgba(197,207,209,.4);width:292px;height:100%}@media screen and (max-width: 768px){.user-select-right[data-v-7f79180d]{display:block!important}.user-select-user[data-v-7f79180d]{width:100%!important}.user-select-list[data-v-7f79180d]{margin-top:16px;width:100%!important;margin-left:0!important}}.user-select[data-v-9a44e4d2]{width:100%;height:100%;overflow:hidden}.user-select .user-select-item[data-v-9a44e4d2]{width:100%;height:100%;border:1px solid rgba(197,207,209,.4)}.user-header[data-v-9a44e4d2]{height:48px;padding:8px 16px;line-height:32px;background:#f7f7f9;color:#43505e;font-size:16px}.search[data-v-9a44e4d2]{margin-top:10px;padding:0 16px;height:36px;border-radius:5px;margin-bottom:16px}.search .ant-input-affix-wrapper[data-v-9a44e4d2]{height:100%}.user-table[data-v-9a44e4d2]{height:calc(100% - 110px);padding:0 16px}.user-select[data-v-172d71a6]{width:100%;height:100%;overflow:hidden}.user-select .user-select-item[data-v-172d71a6]{width:100%;height:100%;border:1px solid rgba(197,207,209,.4)}.user-header[data-v-172d71a6]{height:48px;padding:8px 16px;line-height:32px;background:#f7f7f9;color:#43505e;font-size:16px}.search[data-v-172d71a6]{margin-top:10px;padding:0 16px;height:36px;border-radius:5px;margin-bottom:16px}.search .ant-input-affix-wrapper[data-v-172d71a6]{height:100%}.user-table[data-v-172d71a6]{height:calc(100% - 110px);padding:0 16px}.box[data-v-9ef34d14]{height:calc(100% - 30px)!important;border-radius:0}.tree-content[data-v-9ef34d14]{padding:0 16px}.search[data-v-9ef34d14]{margin-top:10px;padding:0 16px}.tree-header[data-v-9ef34d14]{height:48px;padding:8px 16px;line-height:32px;background:#f7f7f9;color:#43505e;font-size:16px}[data-v-9ef34d14] .ant-tree{background-color:#fff!important}[data-v-9ef34d14] .ant-tree-checkbox{margin:10px 8px 0 0!important}.left-header[data-v-9ef34d14]{height:30px;line-height:30px;display:flex;justify-content:space-between;align-items:center;color:#505050;font-size:14px;font-weight:400;margin-bottom:16px}.left-header .left-header-title[data-v-9ef34d14]{color:#60666b;font-size:14px;font-weight:400}.left-header .header-add[data-v-9ef34d14]{font-size:14px;cursor:pointer;padding:5px}.left-header .header-add[data-v-9ef34d14]:hover{background:#e9f3f8}.search[data-v-9ef34d14]{height:36px;border-radius:5px;margin-bottom:16px}.search-input[data-v-9ef34d14]{border-radius:4px}.tree-content[data-v-9ef34d14]{width:100%;height:calc(100% - 90px);overflow:hidden}[data-v-9ef34d14] .ant-spin-container{height:100%}.left-tree[data-v-9ef34d14]{height:calc(100% - 10px)!important;overflow-y:auto!important;overflow-x:hidden!important}[data-v-9ef34d14]::-webkit-scrollbar{width:12px!important}.tree-edit[data-v-9ef34d14],.not-tree-edit[data-v-9ef34d14]{width:100%;display:inline-block;position:relative}.tree-edit .edit-title[data-v-9ef34d14],.not-tree-edit .edit-title[data-v-9ef34d14]{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tree-edit .edit-icon[data-v-9ef34d14],.not-tree-edit .edit-icon[data-v-9ef34d14]{display:none;width:40px;position:absolute;right:10px}.tree-edit:hover .edit-icon[data-v-9ef34d14],.not-tree-edit:hover .edit-icon[data-v-9ef34d14]{display:inline-block}.tree-edit:hover .edit-title[data-v-9ef34d14],.not-tree-edit:hover .edit-title[data-v-9ef34d14]{width:calc(100% - 50px)}.not-tree-edit:hover .edit-title[data-v-9ef34d14]{width:100%}[data-v-9ef34d14] .ant-tree .ant-tree-node-content-wrapper{height:38px!important;line-height:38px!important;display:inherit!important}[data-v-9ef34d14] .ant-tree-switcher{line-height:38px!important}[data-v-9ef34d14] .ant-tree-switcher .ant-tree-switcher-icon{font-size:14px!important}[data-v-9ef34d14] .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle{height:38px!important;line-height:38px!important;margin-right:8px}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before{border-radius:4px;background:rgba(207,221,247,.35)!important}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{color:#0f56d7;font-weight:500}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tree-iconEle .iconfont{color:#0f56d7!important}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before{background:rgba(207,221,247,.35)!important;border-radius:4px}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{color:#000;font-weight:500}[data-v-9ef34d14] .ant-tree-treenode:not(:last-child){margin-bottom:8px}[data-v-9ef34d14] .ant-tree-indent-unit{width:10px!important}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode:before{bottom:0!important}[data-v-9ef34d14] .ant-tree .ant-tree-treenode{padding:0 12px}[data-v-9ef34d14] .guns-table-tool .guns-tool{display:none}.img[data-v-9ef34d14]{width:24px;height:22px;margin-top:-4px}.svg-img[data-v-9ef34d14]{width:24px;height:22px;margin-top:8px}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode{height:38px!important;line-height:38px!important}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button{display:inline;display:flex;top:0}[data-v-9ef34d14] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button-first{display:inline;display:flex;top:0;margin-right:150px}[data-v-9ef34d14] .ant-tree-node-content-wrapper{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 0 0 4px}[data-v-9ef34d14] .ant-tree-title{width:calc(100% - 32px)}.empty[data-v-9ef34d14]{margin-top:50%}[data-v-9ef34d14] .ant-card-body,[data-v-9ef34d14] .ant-spin-nested-loading,[data-v-9ef34d14] .ant-spin-container{height:100%}.user-select[data-v-499d2032]{width:100%;height:100%;overflow:hidden}.user-select .user-select-item[data-v-499d2032]{width:100%;height:100%;border:1px solid rgba(197,207,209,.4)}.user-select[data-v-7d3b2200]{width:100%;height:100%;display:flex;overflow:hidden;border-radius:8px}.user-select .user-select-org[data-v-7d3b2200]{width:100%;height:100%;border:1px solid rgba(197,207,209,.4)}.user-select .user-select-right[data-v-7d3b2200]{width:100%;height:100%;display:flex;flex-direction:row}.user-select .user-select-right .user-select-user[data-v-7d3b2200]{width:calc(100% - 308px);height:100%;border:1px solid rgba(197,207,209,.4)}.user-select .user-select-right .user-select-user .user-header[data-v-7d3b2200]{height:48px;padding:8px 16px;line-height:32px;background:#f7f7f9;color:#43505e;font-size:16px}.user-select .user-select-right .user-select-user .search[data-v-7d3b2200]{margin-top:10px;padding:0 16px;height:36px;border-radius:5px;margin-bottom:16px}.user-select .user-select-right .user-select-user .search .ant-input-affix-wrapper[data-v-7d3b2200]{height:100%}.user-select .user-select-right .user-select-user .user-table[data-v-7d3b2200]{height:calc(100% - 110px);padding:0 16px}.user-select .user-select-right .user-select-list[data-v-7d3b2200]{margin-left:16px;border-radius:8px;border:1px solid rgba(197,207,209,.4);width:292px;height:100%}@media screen and (max-width: 768px){.user-select-right[data-v-7d3b2200]{display:block!important}.user-select-user[data-v-7d3b2200]{width:100%!important}.user-select-list[data-v-7d3b2200]{margin-top:16px;width:100%!important;margin-left:0!important}}.user-select[data-v-5f981e98]{width:100%;height:100%;display:flex;overflow:hidden;border-radius:8px}.user-select .user-select-item[data-v-5f981e98]{width:100%;height:100%;border:1px solid rgba(197,207,209,.4)}.box[data-v-f6151c6e]{width:100%;height:100%}.box .box-header[data-v-f6151c6e]{border:1px solid #e8e8e8;color:rgba(0,0,0,.65);padding:12px;height:60px;overflow-y:auto;margin-bottom:10px}.box .box-tab[data-v-f6151c6e] .ant-tabs-tab{padding:12px 16px!important}.box .box-content[data-v-f6151c6e]{width:100%;height:calc(100% - 121px)}@media screen and (max-width: 768px){.selection-modal .ant-modal{width:100%!important;top:20px}.selection-modal .user-select{overflow-y:auto!important}.selection-modal .guns-split-panel{flex-direction:column}.selection-modal .guns-split-panel-body{transform:translate(0)!important;height:100%}.selection-modal .guns-split-panel>.guns-split-panel-body{flex:none;border-top:1px solid rgba(197,207,209,.4);padding-top:16px}.selection-modal .guns-split-panel.is-responsive:not(.is-vertical)>.guns-split-panel-wrap{position:relative;height:100%}.selection-modal .guns-split-panel.is-responsive:not(.is-vertical):not(.is-collapse){overflow-y:auto!important;overflow-x:hidden!important}}\n",document.head.appendChild(t);const A={class:"selected"},B={class:"selected-search"},P={class:"selected-bottom"},j={key:0,class:"bottom-list"},V={key:1},G={class:"selected-name"},W={class:"selected-del"},J=a({__name:"selected-list",props:{list:Array},emits:["delete","deleteAll"],setup(e,{emit:t}){const a=e,b=t,m=l({simpleImage:i.PRESENTED_IMAGE_SIMPLE}),w=d(""),y=()=>{b("deleteAll")};return(t,l)=>{var d;const I=f,k=x,R=i;return s(),o("div",A,[r("div",{class:"selected-top"},[l[1]||(l[1]=r("span",null,"已选",-1)),r("span",{class:"selected-del"},[r("a",{onClick:y},"清空")])]),r("div",B,[n(k,{value:w.value,"onUpdate:value":l[0]||(l[0]=e=>w.value=e),placeholder:"请输入名称（回车搜索）","allow-clear":""},{prefix:c((()=>[n(I,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",P,[a.list?(s(),o("div",j,[e.list&&0==e.list.length?(s(),u(R,{key:0,image:m.simpleImage},null,8,["image"])):(s(),o("div",V,[(s(!0),o(p,null,v(null===(d=a.list)||void 0===d?void 0:d.filter((e=>e.name.includes(w.value))),((e,t)=>(s(),o("div",{class:"list",key:e.id},[r("div",G,h(t+1)+". "+h(e.subValueName?e.subValueName+"#":"")+h(e.name),1),r("div",W,[n(I,{iconClass:"icon-opt-shanchu",title:"删除",color:"#000",onClick:t=>(e=>{b("delete",e)})(e)},null,8,["onClick"])])])))),128))]))])):g("",!0)])])}}},[["__scopeId","data-v-b6ea496b"]]),X={class:"user-select"},q={class:"user-select-org"},H={class:"search"},Q={class:"user-table"},Y={class:"user-select-right"},Z={class:"user-select-user"},$={class:"search"},ee={class:"user-table"},te={class:"user-select-list"},ae={__name:"select-dicts",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=d([]),p=d({dictTypeId:""}),v=d({}),h=d(null),g=d([{title:"类型",dataIndex:"dictTypeName",isShow:!0,width:120,ellipsis:!0},{title:"编码",isShow:!0,ellipsis:!0,width:120,dataIndex:"dictTypeCode"}]),w=d([{title:"名称",isShow:!0,ellipsis:!0,dataIndex:"dictName"},{title:"编码",isShow:!0,ellipsis:!0,dataIndex:"dictCode"}]),y=d(null),I=d(null),k=e=>{u.value.splice(u.value.findIndex((t=>t.bizId===e.bizId)),1)},R=()=>{u.value=[]},_=(e,t,a)=>{h.value=e,p.value.dictTypeId=e.dictTypeId,C()},L=(e,t,a)=>{l.isRadio&&(u.value=[]),E(e,t,u.value,"dictId","dictName")},S=(e,t,a)=>{U(e,a,u.value,"dictId","dictName")},T=()=>{var e;return null===(e=p.value)||void 0===e||!e.dictTypeCode};b((()=>u.value),(e=>{var t,a;t=e,a="bizId",y.value.selectedRowKeys=K(t,a,u.value),i("selectedChange")}),{deep:!0});const C=()=>{m((()=>{y.value.reload()}))},z=()=>{m((()=>{var e;null===(e=I.value)||void 0===e||e.reload()}))};return t({dictList:u}),(e,t)=>{const a=f,i=x,d=M,h=N;return s(),o("div",X,[n(h,{width:l.isMobileFlag?"100%":"340px",allowCollapse:!1},{content:c((()=>[r("div",Y,[r("div",Z,[t[4]||(t[4]=r("div",{class:"user-header"},"字典列表",-1)),r("div",$,[n(i,{value:p.value.searchText,"onUpdate:value":t[1]||(t[1]=e=>p.value.searchText=e),placeholder:"字典名称（回车搜索）",onPressEnter:C,style:{width:"100%"}},{prefix:c((()=>[n(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",ee,[n(d,{columns:w.value,where:p.value,isShowRowSelect:"",isLoad:T,isSort:!1,isPage:!1,rowId:"dictId",ref_key:"tableRef",ref:y,isRadio:l.isRadio,showTool:!1,url:"/dict/list",onOnSelect:L,onOnSelectAll:S},null,8,["columns","where","isRadio"])])]),r("div",te,[n(J,{list:u.value,"onUpdate:list":t[2]||(t[2]=e=>u.value=e),onDelete:k,onDeleteAll:R},null,8,["list"])])])])),default:c((()=>[r("div",q,[t[3]||(t[3]=r("div",{class:"user-header"},"字典类型列表",-1)),r("div",H,[n(i,{value:v.value.searchText,"onUpdate:value":t[0]||(t[0]=e=>v.value.searchText=e),placeholder:"字典类型名称（回车搜索）",onPressEnter:z,style:{width:"100%"}},{prefix:c((()=>[n(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",Q,[n(d,{columns:g.value,isSort:!1,where:v.value,isPage:!1,rowId:"dictTypeId",isRadio:!0,ref_key:"tableTypeRef",ref:I,showTool:!1,url:"/dictType/list",isShowRowSelect:"",onOnSelect:_},null,8,["columns","where"])])])])),_:1},8,["width"])])}}},le=a(ae,[["__scopeId","data-v-7f79180d"]]),ie={class:"user-select"},de={class:"user-select-item"},se={class:"search"},oe={class:"user-table"},re={class:"user-select-item"},ne={__name:"select-position",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(e,{expose:t,emit:a}){const l=e,i=a,v=d([]),h=d({searchText:""}),m=d(null),I=d([{key:"index",title:"序号",width:48,align:"center",isShow:!0},{dataIndex:"positionName",title:"职位名称",align:"center",width:100,ellipsis:!0,isShow:!0},{dataIndex:"positionCode",title:"职位编码",width:100,align:"center",isShow:!0},{dataIndex:"statusFlag",title:"状态",align:"center",width:100,isShow:!0}]),k=()=>{m.value.reload()},R=(e,t,a)=>{l.isRadio&&(v.value=[]),E(e,t,v.value,"positionId","positionName")},_=(e,t,a)=>{U(e,a,v.value,"positionId","positionName")},L=(e,t)=>{var a;null!==(a=m.value)&&void 0!==a&&a.selectedRowList&&(m.value.selectedRowList=K(e,t,v.value))},S=e=>{v.value.splice(v.value.findIndex((t=>t.bizId===e.bizId)),1)},T=()=>{v.value=[]};return b((()=>v.value),(e=>{m.value&&(L(e,"bizId"),i("selectedChange"))}),{deep:!0}),t({positionList:v}),(e,t)=>{const a=f,i=x,d=y,b=M,C=J,z=N;return s(),o("div",ie,[n(z,{width:l.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:c((()=>[r("div",re,[n(C,{list:v.value,"onUpdate:list":t[2]||(t[2]=e=>v.value=e),onDelete:S,onDeleteAll:T},null,8,["list"])])])),default:c((()=>[r("div",de,[t[5]||(t[5]=r("div",{class:"user-header"},"职位列表",-1)),r("div",se,[n(i,{value:h.value.searchText,"onUpdate:value":t[0]||(t[0]=e=>h.value.searchText=e),placeholder:"职位名称、编码（回车搜索）",onPressEnter:k},{prefix:c((()=>[n(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",oe,[n(b,{columns:I.value,where:h.value,bordered:"",isShowRowSelect:"",isRadio:l.isRadio,rowId:"positionId",ref_key:"tableRef",ref:m,showTool:!1,url:"/hrPosition/page",onOnSelect:R,onOnSelectAll:_,onTableListChange:t[1]||(t[1]=e=>L(e,"positionId"))},{bodyCell:c((({column:e,record:a})=>["statusFlag"==e.dataIndex?(s(),o(p,{key:0},[1==a.statusFlag?(s(),u(d,{key:0,color:"blue"},{default:c((()=>t[3]||(t[3]=[w("启用")]))),_:1,__:[3]})):g("",!0),2==a.statusFlag?(s(),u(d,{key:1,color:"red"},{default:c((()=>t[4]||(t[4]=[w("禁用")]))),_:1,__:[4]})):g("",!0)],64)):g("",!0)])),_:1},8,["columns","where","isRadio"])])])])),_:1},8,["width"])])}}},ce=a(ne,[["__scopeId","data-v-9a44e4d2"]]),ue={class:"user-select"},pe={class:"user-select-item"},ve={class:"search"},he={class:"user-table"},ge={class:"user-select-item"},fe={__name:"select-role",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=d([]),v=d({roleName:""}),m=d(null),y=d([{key:"index",title:"序号",width:48,align:"center",isShow:!0},{dataIndex:"roleName",title:"角色名称",align:"center",width:100,ellipsis:!0,isShow:!0},{dataIndex:"roleCode",title:"角色编码",width:100,align:"center",isShow:!0}]),I=()=>{m.value.reload()},k=(e,t,a)=>{l.isRadio&&(u.value=[]),E(e,t,u.value,"roleId","roleName")},R=(e,t,a)=>{U(e,a,u.value,"roleId","roleName")},_=(e,t)=>{var a;null!==(a=m.value)&&void 0!==a&&a.selectedRowList&&(m.value.selectedRowList=K(e,t,u.value))},L=e=>{u.value.splice(u.value.findIndex((t=>t.bizId===e.bizId)),1)},S=()=>{u.value=[]};return b((()=>u.value),(e=>{m.value&&(_(e,"bizId"),i("selectedChange"))}),{deep:!0}),t({roleList:u}),(e,t)=>{const a=f,i=x,d=M,b=J,T=N;return s(),o("div",ue,[n(T,{width:l.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:c((()=>[r("div",ge,[n(b,{list:u.value,"onUpdate:list":t[2]||(t[2]=e=>u.value=e),onDelete:L,onDeleteAll:S},null,8,["list"])])])),default:c((()=>[r("div",pe,[t[3]||(t[3]=r("div",{class:"user-header"},"角色列表",-1)),r("div",ve,[n(i,{value:v.value.roleName,"onUpdate:value":t[0]||(t[0]=e=>v.value.roleName=e),placeholder:"角色名称（回车搜索）",onPressEnter:I},{prefix:c((()=>[n(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",he,[n(d,{columns:y.value,where:v.value,bordered:"",isShowRowSelect:"",isRadio:l.isRadio,rowId:"roleId",ref_key:"tableRef",ref:m,showTool:!1,url:"/sysRole/page",onOnSelect:k,onOnSelectAll:R,onTableListChange:t[1]||(t[1]=e=>_(e,"roleId"))},{bodyCell:c((({column:e,index:t})=>["index"==e.key?(s(),o(p,{key:0},[w(h(t+1),1)],64)):g("",!0)])),_:1},8,["columns","where","isRadio"])])])])),_:1},8,["width"])])}}},xe=a(fe,[["__scopeId","data-v-172d71a6"]]),be={class:"box bgColor"},me={class:"search"},we={class:"tree-content"},ye={class:"left-tree"},Ie={class:"tree-title"},ke={__name:"org-tree",props:{companySearchFlag:{type:Boolean,default:!1},isRadio:{type:Boolean,default:!0}},emits:["treeSelect","treeData","checkedTree"],setup(e,{expose:t,emit:a}){const l=a,p=e,v=d(""),b=d(!1),m=d([]),w=d([]),y=d([]),S=d([]),T=d([]);I((()=>{C()}));const C=()=>{S.value=[],b.value=!0,D.tree({searchText:v.value,companySearchFlag:p.companySearchFlag}).then((e=>{v.value&&(y.value=e.data.expandOrgIdList);const t=z(e.data.orgTreeList);m.value=t})).finally((()=>b.value=!1))},z=e=>(e&&e.length>0&&e.forEach((e=>{e.haveSubOrgFlag?e.isLeaf=!1:e.isLeaf=!0,e.children&&e.children.length>0&&(e.children=z(e.children))})),e),F=(e,t)=>{l("treeSelect",e,t)},O=(e,{checked:t,node:a})=>{l("checkedTree",t,a)},N=()=>{v.value||C()},M=e=>(S.value.push(e.eventKey),new Promise((t=>{D.tree({orgParentId:e.dataRef.orgId,companySearchFlag:p.companySearchFlag}).then((t=>{const a=z(t.data.orgTreeList);e.dataRef.children=a,m.value=[...m.value]})).finally((()=>{t()}))})));return t({currentSelectKeys:w,checkedKeyss:T}),(e,t)=>{const a=x,l=_,d=i,I=L;return s(),o("div",be,[t[5]||(t[5]=r("div",{class:"tree-header"},"全部机构",-1)),r("div",me,[n(a,{value:v.value,"onUpdate:value":t[0]||(t[0]=e=>v.value=e),placeholder:"请输入机构名称","allow-clear":"",onPressEnter:C,onChange:N},{prefix:c((()=>[n(f,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",we,[n(I,{tip:"Loading...",spinning:b.value,delay:100},{default:c((()=>[k(r("div",ye,[n(l,{"show-icon":!0,selectedKeys:w.value,"onUpdate:selectedKeys":t[1]||(t[1]=e=>w.value=e),expandedKeys:y.value,"onUpdate:expandedKeys":t[2]||(t[2]=e=>y.value=e),loadedKeys:S.value,"onUpdate:loadedKeys":t[3]||(t[3]=e=>S.value=e),onSelect:F,"load-data":M,"tree-data":m.value,checkedKeys:T.value,"onUpdate:checkedKeys":t[4]||(t[4]=e=>T.value=e),checkable:!p.isRadio,checkStrictly:"",onCheck:O,fieldNames:{children:"children",title:"orgName",key:"orgId",value:"orgId"}},{icon:c((e=>[1==e.orgType?(s(),u(f,{key:0,"icon-class":"icon-nav-gongsi",color:"#43505e",fontSize:"24px"})):g("",!0),2==e.orgType?(s(),u(f,{key:1,"icon-class":"icon-tree-dept",color:"#43505e",fontSize:"24px"})):g("",!0)])),title:c((e=>[r("span",Ie,h(e.orgName),1)])),_:1},8,["selectedKeys","expandedKeys","loadedKeys","tree-data","checkedKeys","checkable"])],512),[[R,m.value&&m.value.length>0]]),k(n(d,{class:"empty"},null,512),[[R,m.value&&0==m.value.length]])])),_:1},8,["spinning"])])])}}},Re=a(ke,[["__scopeId","data-v-9ef34d14"]]),_e={class:"user-select"},Le={class:"user-select-item"},Se={class:"user-select-item"},Te={__name:"select-dept",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=d([]),p=d(null),v=(e,{node:t})=>{let a={...t,bizId:t.orgId,name:t.orgName,children:null};const i=u.value.find((e=>e.bizId===t.orgId));l.isRadio?u.value=i?[]:[a]:i?g(a):u.value.push(a)},h=(e,t)=>{t.bizId=t.orgId,t.name=t.orgName,e?u.value.find((e=>e.bizId==t.orgId))||u.value.push(t):g(t)},g=e=>{u.value.splice(u.value.findIndex((t=>t.bizId===e.bizId)),1)},f=()=>{u.value=[]};return b((()=>u.value),(e=>{i("selectedChange"),m((()=>{var e;!l.isRadio&&p.value&&(p.value.checkedKeyss=null===(e=u.value)||void 0===e?void 0:e.map((e=>e.bizId)))}))}),{deep:!0}),t({deptList:u}),(e,t)=>{const a=J,i=N;return s(),o("div",_e,[n(i,{width:l.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:c((()=>[r("div",Se,[n(a,{list:u.value,"onUpdate:list":t[0]||(t[0]=e=>u.value=e),onDelete:g,onDeleteAll:f},null,8,["list"])])])),default:c((()=>[r("div",Le,[n(Re,{onTreeSelect:v,onCheckedTree:h,isRadio:l.isRadio,ref_key:"selectionOrgTreeRef",ref:p},null,8,["isRadio"])])])),_:1},8,["width"])])}}},Ce=a(Te,[["__scopeId","data-v-499d2032"]]),ze={class:"user-select"},Fe={class:"user-select-org"},Oe={class:"user-select-right"},Ne={class:"user-select-user"},Me={class:"search"},De={class:"user-table"},Ee={key:0},Ue={key:1},Ke={key:0},Ae={key:1},Be={class:"user-select-list"},Pe={__name:"select-user",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=d([]),v=d({searchText:"",orgIdCondition:""}),m=d(null),y=d([{dataIndex:"realName",title:"姓名",align:"center",width:100,isShow:!0},{dataIndex:"company",title:"公司",align:"center",width:100,isShow:!0},{dataIndex:"dept",title:"部门",align:"center",width:100,isShow:!0},{dataIndex:"positionName",title:"职务",align:"center",width:100,isShow:!0}]),I=(e,t)=>{v.value.orgIdCondition=e[0],k()},k=()=>{m.value.reload()},R=(e,t,a)=>{l.isRadio&&(u.value=[]),E(e,t,u.value,"userId","realName")},_=(e,t,a)=>{U(e,a,u.value,"userId","realName")},L=(e,t)=>{var a;null!==(a=m.value)&&void 0!==a&&a.selectedRowList&&(m.value.selectedRowList=K(e,t,u.value))},S=e=>{u.value.splice(u.value.findIndex((t=>t.bizId===e.bizId)),1)},T=()=>{u.value=[]};return b((()=>u.value),(e=>{m.value&&(L(e,"bizId"),i("selectedChange"))}),{deep:!0}),t({userList:u}),(e,t)=>{const a=f,i=x,d=M,b=J,C=N;return s(),o("div",ze,[n(C,{width:l.isMobileFlag?"100%":"292px",allowCollapse:!1},{content:c((()=>[r("div",Oe,[r("div",Ne,[t[3]||(t[3]=r("div",{class:"user-header"},"人员列表",-1)),r("div",Me,[n(i,{value:v.value.searchText,"onUpdate:value":t[0]||(t[0]=e=>v.value.searchText=e),placeholder:"姓名、账号（回车搜索）",onPressEnter:k,style:{width:"100%"}},{prefix:c((()=>[n(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),r("div",De,[n(d,{columns:y.value,where:v.value,isShowRowSelect:"",isRadio:l.isRadio,rowId:"userId",ref_key:"tableRef",ref:m,showTool:!1,url:"/sysUser/page",onOnSelect:R,onOnSelectAll:_,onTableListChange:t[1]||(t[1]=e=>L(e,"userId"))},{bodyCell:c((({column:e,record:t})=>{var a,l,i,d,r,n;return["company"==e.dataIndex?(s(),o(p,{key:0},[w(h(null!=t&&null!==(a=t.userOrgDTO)&&void 0!==a&&a.companyName?null==t||null===(l=t.userOrgDTO)||void 0===l?void 0:l.companyName:""),1)],64)):g("",!0),"dept"==e.dataIndex?(s(),o(p,{key:1},[w(h(null!=t&&null!==(i=t.userOrgDTO)&&void 0!==i&&i.deptName?null==t||null===(d=t.userOrgDTO)||void 0===d?void 0:d.deptName:""),1)],64)):g("",!0),"positionName"==e.dataIndex?(s(),o(p,{key:2},[w(h(null!=t&&null!==(r=t.userOrgDTO)&&void 0!==r&&r.positionName?null==t||null===(n=t.userOrgDTO)||void 0===n?void 0:n.positionName:""),1)],64)):g("",!0),"sex"==e.dataIndex?(s(),o(p,{key:3},["M"==t.sex?(s(),o("span",Ee,"男")):g("",!0),"F"==t.sex?(s(),o("span",Ue,"女")):g("",!0)],64)):g("",!0),"statusFlag"==e.dataIndex?(s(),o(p,{key:4},[1==t.statusFlag?(s(),o("span",Ke,"启用")):g("",!0),2==t.statusFlag?(s(),o("span",Ae,"禁用")):g("",!0)],64)):g("",!0)]})),_:1},8,["columns","where","isRadio"])])]),r("div",Be,[n(b,{list:u.value,"onUpdate:list":t[2]||(t[2]=e=>u.value=e),onDelete:S,onDeleteAll:T},null,8,["list"])])])])),default:c((()=>[r("div",Fe,[n(Re,{onTreeSelect:I})])])),_:1},8,["width"])])}}},je=a(Pe,[["__scopeId","data-v-7d3b2200"]]),Ve={class:"user-select"},Ge={class:"user-select-item"},We={class:"user-select-item"},Je={__name:"select-company",props:{isRadio:{type:Boolean,default:!0},isMobileFlag:Boolean},emits:["selectedChange"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=d([]),p=d(),v=(e,t)=>{let a=t.node.orgId,i={bizId:a,name:t.node.orgName};l.isRadio?u.value=[i]:0===u.value.filter((e=>e.bizId===a)).length&&u.value.push(i)},h=e=>{u.value.splice(u.value.findIndex((t=>t.bizId===e.bizId)),1)},g=()=>{u.value=[]};return b((()=>u.value),(e=>{i("selectedChange"),l.isRadio&&p.value&&(null!=e&&e.length?p.value.currentSelectKeys=[e[0].bizId]:p.value.currentSelectKeys=[])}),{deep:!0}),t({companyList:u}),(e,t)=>{const a=N;return s(),o("div",Ve,[n(a,{width:l.isMobileFlag?"100%":"50%",allowCollapse:!1},{content:c((()=>[r("div",We,[n(J,{list:u.value,"onUpdate:list":t[0]||(t[0]=e=>u.value=e),onDelete:h,onDeleteAll:g},null,8,["list"])])])),default:c((()=>[r("div",Ge,[n(Re,{onTreeSelect:v,"company-search-flag":!0,ref_key:"selectionOrgTreeRef",ref:p},null,512)])])),_:1},8,["width"])])}}},Xe=a(Je,[["__scopeId","data-v-5f981e98"]]),qe={class:"box"},He={class:"box-header"},Qe={key:0,class:"box-tab"},Ye={__name:"index",props:{width:{type:String,default:"70%"},visible:Boolean,title:{type:String,default:"标题"},orgIdLimit:{type:Array,default:()=>[]},showTab:{type:Array,default:()=>[]},data:{type:Object,default:()=>{}},isRadio:{type:Boolean,default:!0},footer:{type:String,default:void 0},max:{type:Number,default:1e5}},emits:["update:visible","done"],setup(e,{emit:t}){const a=e,l=t,i=d(!1),f=d("user"),x=d([]),w=d([{key:"company",name:"公司"},{key:"user",name:"用户"},{key:"dept",name:"部门"},{key:"role",name:"角色"},{key:"position",name:"职位"},{key:"dict",name:"字典"}]),y=d(""),_=d(!1),L=d(null),N=d(null),M=d(null),D=d(null),E=d(null),U=d(null),K=d(null),A=d(window.innerWidth),B=S((()=>A.value<=768));I((()=>{j(),V(),window.addEventListener("resize",P)})),T((()=>{window.removeEventListener("resize",P)}));const P=()=>{A.value=window.innerWidth},j=()=>{if(a.showTab){if(0==a.showTab.length)x.value=w.value;else if(a.showTab.length>0){let e=[];w.value.forEach((t=>{a.showTab.find((e=>e==t.key))&&e.push(t)})),x.value=e}Q(),f.value=x.value[0].key}},V=()=>{"{}"!=JSON.stringify(a.data)&&G(a.data)},G=e=>{U.value&&(U.value.companyList=null!=e&&e.selectCompanyList?e.selectCompanyList:[]),L.value&&(L.value.userList=null!=e&&e.selectUserList?e.selectUserList:[]),N.value&&(N.value.deptList=null!=e&&e.selectOrgList?e.selectOrgList:[]),M.value&&(M.value.roleList=null!=e&&e.selectRoleList?e.selectRoleList:[]),D.value&&(D.value.positionList=null!=e&&e.selectPositionList?e.selectPositionList:[]),K.value&&(K.value.dictList=null!=e&&e.selectDictList?e.selectDictList:[]),W()},W=()=>{let e=U.value?J(U.value.companyList,"company"):"",t=L.value?J(L.value.userList,"user"):"",a=N.value?J(N.value.deptList,"dept"):"",l=M.value?J(M.value.roleList,"role"):"",i=D.value?J(D.value.positionList,"position"):"",d=K.value?J(K.value.dictList,"dict"):"";y.value=t+a+l+i+e+d},J=(e,t)=>{let a="";if(e&&e.length>0){e.forEach((e=>{!e.subValueName||"position"!=t&&"user"!=t&&"dept"!=t?a+=e.name+";":a+=e.subValueName+"#"+e.name+";"}));let l="";"user"==t?l="【用户】":"dept"==t?l="【部门】":"role"==t?l="【角色】":"position"==t?l="【职位】":"company"==t?l="【公司】":"dict"==t&&(l="【字典】"),a=l+a}return a},X=e=>{l("update:visible",e)},q=()=>{let e={selectCompanyList:U.value?U.value.companyList:[],selectUserList:L.value?L.value.userList:[],selectOrgList:N.value?N.value.deptList:[],selectRoleList:M.value?M.value.roleList:[],selectPositionList:D.value?D.value.positionList:[],selectDictList:K.value?K.value.dictList:[],userGroupDetailName:y.value};if(!a.isRadio){let t=!0;if((x.value[0]="user")?t=H(e,"selectUserList"):(x.value[0]="dept")?t=H(e,"selectOrgList"):(x.value[0]="role")?t=H(e,"selectRoleList"):(x.value[0]="position")?t=H(e,"selectPositionList"):(x.value[0]="dict")?t=H(e,"selectDictList"):(x.value[0]="company")&&(t=H(e,"selectCompanyList")),!t)return}i.value=!0,i.value=!1,X(!1),l("done",e)},H=(e,t)=>!(e[t].length>a.max&&(C.warning("最多选"+a.max+"条"),1)),Q=()=>{m((()=>{E.value&&(x.value.length>=2?(_.value=!0,E.value.style.height="calc(100% - 140px)"):(E.value.style.height="calc(100% - 80px)",_.value=!1))}))},Y=e=>{f.value=e};return b((()=>a.showTab),(()=>{j()})),b((()=>a.data),(()=>{m((()=>{G(a.data)}))}),{deep:!0}),b((()=>a.visible),(()=>{a.visible&&(y.value="")})),(t,l)=>{const d=z,b=F,m=Xe,w=je,I=Ce,_=xe,S=ce,T=le,C=O;return e.visible?(s(),u(C,{key:0,width:a.width,visible:a.visible,"confirm-loading":i.value,"body-style":{paddingBottom:"8px"},onOk:q,title:e.title,style:{top:"40px"},onCancel:l[1]||(l[1]=e=>X(!1)),footer:e.footer,maxable:"",maskClosable:!1,wrapClassName:"project-modal h80 selection-modal"},{default:c((()=>[r("div",qe,[r("div",He,"已选："+h(y.value),1),x.value.length>=2?(s(),o("div",Qe,[n(b,{activeKey:f.value,"onUpdate:activeKey":l[0]||(l[0]=e=>f.value=e),onChange:Y},{default:c((()=>[(s(!0),o(p,null,v(x.value,(e=>(s(),u(d,{key:e.key,tab:e.name},null,8,["tab"])))),128))])),_:1},8,["activeKey"])])):g("",!0),r("div",{class:"box-content",ref_key:"contentRef",ref:E},[0===e.showTab.length||-1!==e.showTab.indexOf("company")?k((s(),u(m,{key:0,ref_key:"companyRef",ref:U,isRadio:a.isRadio,isMobileFlag:B.value,onSelectedChange:W},null,8,["isRadio","isMobileFlag"])),[[R,"company"===f.value]]):g("",!0),0===e.showTab.length||-1!==e.showTab.indexOf("user")?k((s(),u(w,{key:1,ref_key:"userRef",ref:L,isRadio:a.isRadio,isMobileFlag:B.value,onSelectedChange:W},null,8,["isRadio","isMobileFlag"])),[[R,"user"===f.value]]):g("",!0),0===e.showTab.length||-1!==e.showTab.indexOf("dept")?k((s(),u(I,{key:2,ref_key:"deptRef",ref:N,isRadio:a.isRadio,isMobileFlag:B.value,onSelectedChange:W},null,8,["isRadio","isMobileFlag"])),[[R,"dept"===f.value]]):g("",!0),0===e.showTab.length||-1!==e.showTab.indexOf("role")?k((s(),u(_,{key:3,ref_key:"roleRef",ref:M,isRadio:a.isRadio,isMobileFlag:B.value,onSelectedChange:W},null,8,["isRadio","isMobileFlag"])),[[R,"role"===f.value]]):g("",!0),0===e.showTab.length||-1!==e.showTab.indexOf("position")?k((s(),u(S,{key:4,ref_key:"positionRef",ref:D,isRadio:a.isRadio,isMobileFlag:B.value,onSelectedChange:W},null,8,["isRadio","isMobileFlag"])),[[R,"position"===f.value]]):g("",!0),0===e.showTab.length||-1!==e.showTab.indexOf("dict")?k((s(),u(T,{key:5,ref_key:"dictRef",ref:K,isRadio:a.isRadio,isMobileFlag:B.value,onSelectedChange:W},null,8,["isRadio","isMobileFlag"])),[[R,"dict"===f.value]]):g("",!0)],512)])])),_:1},8,["width","visible","confirm-loading","title","footer"])):g("",!0)}}};e("_",a(Ye,[["__scopeId","data-v-f6151c6e"]]))}}}));
