package cn.stylefeng.roses.kernel.websocket.redis;

/**
 * 订阅Redis的消息，用于收到消息之后的业务的处理
 *
 * <AUTHOR>
 * @since 2024-01-15 15:13
 */
public interface RedisReceiver {

    /**
     * 消息接受的方法，跟本类中的接受消息方法名一致
     */
    String RECEIVER_METHOD_NAME = "receiveMessage";

    /**
     * 消息接收器Spring Bean的名称
     */
    String REDIS_RECEIVER_NAME = "redisReceiver";

    /**
     * 接收到消息后的业务处理
     *
     * @param message 接收到的redis消息的字符串
     * <AUTHOR>
     * @since 2024-01-15 15:14
     */
    void receiveMessage(String message);

}
