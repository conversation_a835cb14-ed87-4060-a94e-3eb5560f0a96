package cn.stylefeng.roses.kernel.manage.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API客户端和资源绑定关系封装类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiClientAuthRequest extends BaseRequest {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("主键id")
    private Long apiClientResourceId;

    /**
     * api客户端id
     */
    @NotNull(message = "api客户端id不能为空", groups = {add.class, edit.class, getBindResult.class})
    @ChineseDescription("api客户端id")
    private Long apiClientId;

    /**
     * 资源编码，与sys_resource表编码对应
     */
    @NotBlank(message = "资源编码，与sys_resource表编码对应不能为空", groups = {add.class, edit.class})
    @ChineseDescription("资源编码，与sys_resource表编码对应")
    private String resourceCode;

    /**
     * 选项类型：1-全选，2-选择单个资源
     */
    @ChineseDescription("选项类型：1-全选，2-选择单个资源")
    @NotNull(message = "选项类型不能为空", groups = selectBind.class)
    private Integer selectType;

    /**
     * 是否选中：true-选中，false-取消选中
     */
    @ChineseDescription("是否选中：true-选中，false-取消选中")
    @NotNull(message = "是否选中不能为空", groups = selectBind.class)
    private Boolean checkedFlag;

    /**
     * 获取权限绑定的结果
     */
    public @interface getBindResult {
    }

    /**
     * 选中资源，选中全选或单个资源
     */
    public @interface selectBind {
    }


}
