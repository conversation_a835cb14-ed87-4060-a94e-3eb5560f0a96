package cn.stylefeng.roses.kernel.report.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.report.api.constants.ReportConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * 报表的异常
 *
 * <AUTHOR>
 * @date 2021/2/18 13:57
 */
public class ReportException extends ServiceException {

    public ReportException(AbstractExceptionEnum exception, Object... params) {
        super(ReportConstants.REPORT_SERVER_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public ReportException(AbstractExceptionEnum exception) {
        super(ReportConstants.REPORT_SERVER_MODULE_NAME, exception);
    }

}
