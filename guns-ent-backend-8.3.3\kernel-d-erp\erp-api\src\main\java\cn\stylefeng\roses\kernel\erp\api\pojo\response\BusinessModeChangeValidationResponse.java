package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.util.List;

/**
 * 经营方式变更验证响应
 *
 * <AUTHOR>
 * @since 2025/07/27 21:50
 */
@Data
public class BusinessModeChangeValidationResponse {

    /**
     * 是否可以变更
     */
    @ChineseDescription("是否可以变更")
    private Boolean canChange;

    /**
     * 影响的商品数量
     */
    @ChineseDescription("影响的商品数量")
    private Integer affectedProductCount;

    /**
     * 影响的商品列表
     */
    @ChineseDescription("影响的商品列表")
    private List<ErpProductResponse> affectedProducts;

    /**
     * 警告信息
     */
    @ChineseDescription("警告信息")
    private String warningMessage;

    /**
     * 详细说明
     */
    @ChineseDescription("详细说明")
    private String description;

    /**
     * 错误信息
     */
    @ChineseDescription("错误信息")
    private String errorMessage;

}