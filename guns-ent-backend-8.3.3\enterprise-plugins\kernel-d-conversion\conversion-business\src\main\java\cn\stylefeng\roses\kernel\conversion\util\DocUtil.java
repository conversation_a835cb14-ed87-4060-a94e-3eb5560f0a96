package cn.stylefeng.roses.kernel.conversion.util;

import com.aspose.words.*;

import java.io.ByteArrayInputStream;
import java.io.OutputStream;

/**
 * doc文件操作工具类
 *
 * <AUTHOR>
 * @date 2021/8/26 14:20
 */
public class DocUtil {

    /**
     * doc转化为pdf
     *
     * @param inputFile  原有doc或docx文件路径
     * @param outputFile 输出的pdf文件路径
     * <AUTHOR>
     * @date 2021/8/26 14:35
     */
    public static void toPdf(String inputFile, String outputFile) throws Exception {
        Document document = new Document(inputFile);
        document.save(outputFile, SaveFormat.PDF);
    }

    /**
     * doc转化为pdf，读入字节，输出到流中
     *
     * @param inputFileBytes 原有doc或docx文件字节数组
     * <AUTHOR>
     * @since 2023/10/20 22:48
     */
    public static void toPdfByBytes(byte[] inputFileBytes, String suffix, OutputStream outputStream) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(inputFileBytes);
        Document document = new Document(inputStream);
        document.save(outputStream, SaveFormat.PDF);
    }

    /**
     * doc转化为pdf（禁用超链接）
     *
     * @param inputFile  原有doc或docx文件路径
     * @param outputFile 输出的pdf文件路径
     * <AUTHOR>
     * @date 2021/8/26 14:37
     */
    public static void toPdfDisableHyperlinks(String inputFile, String outputFile) throws Exception {
        Document document = new Document(inputFile);
        PdfSaveOptions options = new PdfSaveOptions();
        options.setCreateNoteHyperlinks(true);
        document.save(outputFile, options);
    }

    /**
     * doc转化为pdf（嵌入式字体）
     *
     * @param inputFile  原有doc或docx文件路径
     * @param outputFile 输出的pdf文件路径
     * <AUTHOR>
     * @date 2021/8/26 14:37
     */
    public static void toPdfEmbedAllFontsInPDF(String inputFile, String outputFile) throws Exception {
        Document document = new Document(inputFile);
        PdfSaveOptions options = new PdfSaveOptions();
        options.setEmbedFullFonts(true);
        document.save(outputFile, options);
    }

    /**
     * doc转化为pdf（嵌入式非安装字体）
     *
     * @param inputFile  原有doc或docx文件路径
     * @param outputFile 输出的pdf文件路径
     * <AUTHOR>
     * @date 2021/8/26 14:37
     */
    public static void toPdfEmbedNonInstalledFonts(String inputFile, String outputFile, String fontFile) throws Exception {
        Document document = new Document(inputFile);
        FontSettings fontSettings = new FontSettings();
        fontSettings.setFontsFolders(new String[]{fontFile}, true);
        document.setFontSettings(fontSettings);
        document.save(outputFile, SaveFormat.PDF);
    }

    /**
     * doc转化为pdf（设置图片的质量）
     *
     * @param inputFile  原有doc或docx文件路径
     * @param outputFile 输出的pdf文件路径
     * @param quality    默认是80%的图片质量，这里可以输入图片质量百分比
     * <AUTHOR>
     * @date 2021/8/26 14:37
     */
    public static void toPdfSetImgQuality(String inputFile, String outputFile, int quality) throws Exception {
        Document document = new Document(inputFile);
        PdfSaveOptions options = new PdfSaveOptions();
        options.setJpegQuality(quality);
        document.save(outputFile, options);
    }

    /**
     * word转图片
     *
     * <AUTHOR>
     * @date 2021/8/26 14:47
     */
    public static void toImage(String inputFile, String outputFile) throws Exception {
        Document doc = new Document(inputFile);

        ImageSaveOptions options = new ImageSaveOptions(SaveFormat.PNG);

        for (int i = 0; i < doc.getPageCount(); i++) {
            options.setPageSet(new PageSet(i));
            String imagePath = outputFile + "." + (i + 1) + ".png";
            doc.save(imagePath, options);
        }
    }

    /**
     * 往docx文件中写一段文字
     *
     * <AUTHOR>
     * @date 2021/8/26 14:22
     */
    public static void writeText(String content, String filePath) throws Exception {
        Document document = new Document();
        DocumentBuilder builder = new DocumentBuilder(document);
        builder.writeln(content);
        document.save(filePath, SaveFormat.DOCX);
    }

}
