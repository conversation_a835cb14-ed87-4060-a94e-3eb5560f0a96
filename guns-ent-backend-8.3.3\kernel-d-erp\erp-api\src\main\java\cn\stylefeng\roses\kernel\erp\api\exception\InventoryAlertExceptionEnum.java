package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 库存预警异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Getter
public enum InventoryAlertExceptionEnum implements AbstractExceptionEnum {

    /**
     * 预警规则不存在
     */
    RULE_NOT_FOUND(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "预警规则不存在"),

    /**
     * 预警规则名称重复
     */
    RULE_NAME_DUPLICATE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "预警规则名称重复"),

    /**
     * 预警规则正在使用中，无法删除
     */
    RULE_IN_USE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "预警规则正在使用中，无法删除"),

    /**
     * 预警记录不存在
     */
    RECORD_NOT_FOUND(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10004", "预警记录不存在"),

    /**
     * 预警记录已处理，无法重复处理
     */
    RECORD_ALREADY_HANDLED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10005", "预警记录已处理，无法重复处理"),

    /**
     * 预警配置不存在
     */
    CONFIG_NOT_FOUND(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10006", "预警配置不存在"),

    /**
     * 预警配置键重复
     */
    CONFIG_KEY_DUPLICATE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10007", "预警配置键重复"),

    /**
     * 系统配置不允许修改
     */
    SYSTEM_CONFIG_NOT_EDITABLE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10008", "系统配置不允许修改"),

    /**
     * 预警类型不支持
     */
    ALERT_TYPE_NOT_SUPPORTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10009", "预警类型不支持"),

    /**
     * 阈值配置错误
     */
    THRESHOLD_CONFIG_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10010", "阈值配置错误"),

    /**
     * 目标对象不存在
     */
    TARGET_NOT_FOUND(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10011", "目标对象不存在"),

    /**
     * 通知方式配置错误
     */
    NOTIFICATION_CONFIG_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10012", "通知方式配置错误"),

    /**
     * 预警检查失败
     */
    ALERT_CHECK_FAILED(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "10001", "预警检查失败"),

    /**
     * 通知发送失败
     */
    NOTIFICATION_SEND_FAILED(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "10002", "通知发送失败");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String errorMessage;

    InventoryAlertExceptionEnum(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    @Override
    public String getUserTip() {
        return this.errorMessage;
    }
}
