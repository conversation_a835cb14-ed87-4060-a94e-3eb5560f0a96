package cn.stylefeng.roses.kernel.sync.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.sync.factory.SyncPositionFactory;
import cn.stylefeng.roses.kernel.sync.pojo.PositionSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.position.entity.HrPosition;
import cn.stylefeng.roses.kernel.sys.modular.position.service.HrPositionService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步业务-职位信息
 *
 * <AUTHOR>
 * @since 2023/10/29 22:57
 */
@Service
public class SyncPositionService {

    @Resource
    private HrPositionService hrPositionService;

    /**
     * 获取所有的职务信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:38
     */
    public List<PositionSyncVo> getTotalPosition() {
        try {
            // 关闭多租户开关
            TenantSwitchHolder.set(false);

            List<HrPosition> hrPositions = hrPositionService.list(createWrapper());

            // 组织机构信息转化为返回的VO信息
            return SyncPositionFactory.createPositionVo(hrPositions);

        } finally {
            TenantSwitchHolder.remove();
        }
    }

    /**
     * 分页获取职务信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:38
     */
    public PageResult<PositionSyncVo> getPagePosition(BaseRequest baseRequest) {

        Page<HrPosition> positionSyncVoPage = PageFactory.defaultPage(baseRequest);

        LambdaQueryWrapper<HrPosition> wrapper = this.createWrapper();

        Page<HrPosition> page;
        try {
            TenantSwitchHolder.set(false);
            page = this.hrPositionService.page(positionSyncVoPage, wrapper);
        } finally {
            TenantSwitchHolder.remove();
        }

        if (ObjectUtil.isEmpty(page.getRecords())) {
            return PageResultFactory.createPageResult(new ArrayList<>(), page.getTotal(), Convert.toInt(page.getSize()),
                    Convert.toInt(page.getCurrent()));
        }

        List<HrPosition> records = page.getRecords();
        List<PositionSyncVo> positionVo = SyncPositionFactory.createPositionVo(records);
        return PageResultFactory.createPageResult(positionVo, page.getTotal(), Convert.toInt(page.getSize()),
                Convert.toInt(page.getCurrent()));
    }

    /**
     * 创建wrapper信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:03
     */
    private LambdaQueryWrapper<HrPosition> createWrapper() {

        LambdaQueryWrapper<HrPosition> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询必要字段
        queryWrapper.select(HrPosition::getPositionId, HrPosition::getPositionCode, HrPosition::getPositionName,
                HrPosition::getPositionSort, HrPosition::getRemark, HrPosition::getStatusFlag);

        // 只查询启用的
        queryWrapper.eq(HrPosition::getStatusFlag, StatusEnum.ENABLE.getCode());

        // 根据id排序
        queryWrapper.orderByAsc(HrPosition::getPositionId);

        return queryWrapper;
    }

}
