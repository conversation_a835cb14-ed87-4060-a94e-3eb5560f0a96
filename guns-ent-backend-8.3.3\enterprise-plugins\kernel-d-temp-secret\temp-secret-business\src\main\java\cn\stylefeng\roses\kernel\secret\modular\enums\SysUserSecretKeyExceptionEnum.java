package cn.stylefeng.roses.kernel.secret.modular.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 用户临时秘钥异常相关枚举
 *
 * <AUTHOR>
 * @date 2022/03/22 11:33
 */
@Getter
public enum SysUserSecretKeyExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    SYS_USER_SECRET_KEY_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "查询结果不存在"),

    /**
     * 每个用户只能创建一个密钥
     */
    USER_ONLY_ONE_KEY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "每个用户只能创建一个密钥"),

    /**
     * 无法操作，权限不够
     */
    AUTH_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "无法操作，权限不够");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    SysUserSecretKeyExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
