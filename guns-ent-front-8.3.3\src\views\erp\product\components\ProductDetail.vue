<template>
  <a-modal
    title="商品详情"
    :width="800"
    :visible="visible"
    :footer="null"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="商品编码">
        {{ data.productCode }}
      </a-descriptions-item>
      <a-descriptions-item label="商品名称">
        {{ data.productName }}
      </a-descriptions-item>
      <a-descriptions-item label="商品简称">
        {{ data.productShortName || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="条形码">
        {{ data.barcode || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="品牌">
        {{ data.brand || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="规格">
        {{ data.specification || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="基本单位">
        {{ data.unit }}
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag :color="getStatusTagColor(data.status)">
          {{ getProductStatusName(data.status) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="重量">
        {{ formatWeight(data.weight) }}
      </a-descriptions-item>
      <a-descriptions-item label="体积">
        {{ formatVolume(data.volume) }}
      </a-descriptions-item>
      <a-descriptions-item label="保质期">
        {{ formatShelfLife(data.shelfLife) }}
      </a-descriptions-item>
      <a-descriptions-item label="商品分类">
        {{ data.categoryName || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="供应商">
        {{ data.supplierName || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="计价类型">
        <a-tag v-if="data.pricingType" :color="getPricingTypeTagColor(data.pricingType)">
          {{ getPricingTypeName(data.pricingType) }}
        </a-tag>
        <span v-else>-</span>
      </a-descriptions-item>
      <a-descriptions-item label="价格信息">
        {{ formatPrice(data) }}
      </a-descriptions-item>
      <a-descriptions-item label="存储条件" :span="2">
        {{ data.storageCondition || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" :span="2">
        {{ data.remark || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">
        {{ data.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="更新时间">
        {{ data.updateTime }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 库存信息区域 -->
    <a-divider orientation="left">库存信息</a-divider>
    <!-- 暂时注释库存状态组件，因为InventoryStatus组件不存在 -->
    <!-- <inventory-status
      v-if="data.productId"
      :productId="data.productId"
      :pricingType="data.pricingType"
      :unit="data.unit"
    /> -->
    <div v-if="data.productId" style="padding: 16px; background: #f5f5f5; border-radius: 6px;">
      <p>库存信息功能开发中...</p>
    </div>
  </a-modal>
</template>

<script>
import { ProductApi } from '../api/ProductApi';
// import InventoryStatus from '@/components/erp/InventoryStatus.vue';

export default {
  name: 'ProductDetail',
  components: {
    // InventoryStatus
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 详情数据
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 获取名称和颜色的方法（使用箭头函数保持this上下文）
    const getProductStatusName = (status) => ProductApi.getProductStatusName(status);
    const getStatusTagColor = (status) => ProductApi.getStatusTagColor(status);
    const getPricingTypeName = (pricingType) => ProductApi.getPricingTypeName(pricingType);
    const getPricingTypeTagColor = (pricingType) => ProductApi.getPricingTypeTagColor(pricingType);
    const formatWeight = (weight) => ProductApi.formatWeight(weight);
    const formatVolume = (volume) => ProductApi.formatVolume(volume);
    const formatShelfLife = (shelfLife) => ProductApi.formatShelfLife(shelfLife);

    // 格式化价格显示
    const formatPrice = (record) => {
      if (!record.pricingType) return '-'

      switch (record.pricingType) {
        case 'NORMAL':
          return record.retailPrice ? ProductApi.formatPrice(record.retailPrice, 'NORMAL') : '-'
        case 'WEIGHT':
          return record.unitPrice ? ProductApi.formatPrice(record.unitPrice, 'WEIGHT') : '-'
        case 'PIECE':
          return record.piecePrice ? ProductApi.formatPrice(record.piecePrice, 'PIECE') : '-'
        case 'VARIABLE':
          return record.referencePrice ? ProductApi.formatPrice(record.referencePrice, 'VARIABLE') : '-'
        default:
          return '-'
      }
    };

    return {
      updateVisible,
      getProductStatusName,
      getStatusTagColor,
      getPricingTypeName,
      getPricingTypeTagColor,
      formatWeight,
      formatVolume,
      formatShelfLife,
      formatPrice
    };
  }
};
</script>
