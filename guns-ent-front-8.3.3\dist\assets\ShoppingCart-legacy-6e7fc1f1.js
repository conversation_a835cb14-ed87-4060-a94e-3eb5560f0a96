System.register(["./index-legacy-ee1db0c7.js","./pos-legacy-fe4fee5f.js","./performance-monitor-legacy-4ff0ac5f.js","./constants-legacy-2a31d63c.js","./CartHeader-legacy-1c7632ad.js","./CartItem-legacy-8641f838.js","./CartSummary-legacy-fa651ed6.js"],(function(e,t){"use strict";var a,r,s,n,o,i,c,l,u,m,d,p,y,g,h,v,f,b,w,A,S,C,I,k,x,M,V,T;return{setters:[e=>{a=e.R,r=e.r,s=e.L,n=e.o,o=e.aL,i=e.M,c=e.m,l=e._,u=e.a,m=e.c,d=e.at,p=e.f,y=e.h,g=e.b,h=e.F,v=e.e,f=e.d,b=e.w,w=e.a5},e=>{A=e.u},e=>{S=e.P,C=e.a},e=>{I=e.N,k=e.P,x=e.S},e=>{M=e.default},e=>{V=e.default},e=>{T=e.default}],execute:function(){var t=document.createElement("style");t.textContent=".shopping-cart[data-v-9dcb7d34]{display:flex;flex-direction:column;height:100%;background:#fff;border-radius:8px;overflow:hidden}.cart-content[data-v-9dcb7d34]{flex:1;overflow:hidden;display:flex;flex-direction:column}.cart-items[data-v-9dcb7d34]{flex:1;overflow-y:auto;padding:8px}.empty-cart[data-v-9dcb7d34]{flex:1;display:flex;align-items:center;justify-content:center;padding:40px 20px}.empty-tip[data-v-9dcb7d34]{color:#8c8c8c;font-size:14px;margin-top:8px}.cart-items[data-v-9dcb7d34]::-webkit-scrollbar{width:6px}.cart-items[data-v-9dcb7d34]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.cart-items[data-v-9dcb7d34]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.cart-items[data-v-9dcb7d34]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}\n",document.head.appendChild(t),e("u",B);class R{static createApiWrapper(e,t={}){const{context:a="Cart API调用",showMessage:r=!0,showNotification:s=!1,retryOptions:n={maxRetries:2,retryDelay:1e3}}=t,o=C.measureApiCall(a,e);return S.wrapApiCall(o,{showMessage:r,showNotification:s,context:a,retryOptions:n})}static async checkInventory(e,t){return this.createApiWrapper((()=>a.post("/erp/pos/product/checkStock",{productId:e,quantity:t})),{context:"检查商品库存",showMessage:!0,retryOptions:{maxRetries:3,retryDelay:500}})()}static async batchCheckInventory(e){return this.createApiWrapper((()=>a.post("/erp/pos/product/batchCheckStock",{items:e})),{context:"批量检查商品库存",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async getProductDetail(e){return this.createApiWrapper((()=>a.get("/erp/pos/product/detail",e)),{context:"获取商品详情",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getProductByBarcode(e){return this.createApiWrapper((()=>a.get("/erp/pos/product/barcode",{barcode:e})),{context:"根据条形码获取商品",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async getProductsBatch(e){return this.createApiWrapper((()=>a.post("/erp/pos/product/batch",{productIds:e})),{context:"批量获取商品信息",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async searchProducts(e={}){return this.createApiWrapper((()=>a.get("/erp/pos/products/search",{...e,limit:e.limit||20})),{context:"搜索商品",showMessage:!1,retryOptions:{maxRetries:1,retryDelay:300}})()}static async saveCartState(e){return this.createApiWrapper((()=>a.post("/erp/pos/cart/suspend",{...e,suspendTime:(new Date).toISOString()})),{context:"挂单保存",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async restoreCartState(e){return this.createApiWrapper((()=>a.get(`/erp/pos/cart/restore/${e}`)),{context:"恢复挂单",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getSuspendedCarts(e={}){return this.createApiWrapper((()=>a.get("/erp/pos/cart/suspended",{...e,limit:e.limit||50})),{context:"获取挂单列表",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async deleteSuspendedCart(e){return this.createApiWrapper((()=>a.delete(`/erp/pos/cart/suspended/${e}`)),{context:"删除挂单",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async clearExpiredCarts(e=24){return this.createApiWrapper((()=>a.post("/erp/pos/cart/clearExpired",{expireHours:e})),{context:"清理过期挂单",showMessage:!1,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async validateCart(e){return this.createApiWrapper((()=>a.post("/erp/pos/cart/validate",e)),{context:"验证购物车",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async calculateCartTotal(e){return this.createApiWrapper((()=>a.post("/erp/pos/cart/calculate",e)),{context:"计算购物车金额",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:300}})()}static async applyCoupon(e){return this.createApiWrapper((()=>a.post("/erp/pos/cart/applyCoupon",e)),{context:"应用优惠券",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async removeCoupon(e){return this.createApiWrapper((()=>a.post("/erp/pos/cart/removeCoupon",e)),{context:"移除优惠券",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}}class ${static add(e,t){const a=Math.pow(10,I.AMOUNT_PRECISION);return Math.round((e+t)*a)/a}static subtract(e,t){const a=Math.pow(10,I.AMOUNT_PRECISION);return Math.round((e-t)*a)/a}static multiply(e,t){const a=Math.pow(10,I.AMOUNT_PRECISION);return Math.round(e*t*a)/a}static divide(e,t){if(0===t)throw new Error("除数不能为零");const a=Math.pow(10,I.AMOUNT_PRECISION);return Math.round(e/t*a)/a}static round(e,t=I.AMOUNT_PRECISION){const a=Math.pow(10,t);return Math.round(e*a)/a}}class O{static calculateSubtotal(e,t){if(e<0||t<0)throw new Error("价格和数量不能为负数");return $.multiply(e,t)}static calculateTotal(e){if(!Array.isArray(e))throw new Error("购物车商品列表必须是数组");return e.reduce(((e,t)=>{const a=this.calculateSubtotal(t.price||0,t.quantity||0);return $.add(e,a)}),0)}static calculateTotalQuantity(e){if(!Array.isArray(e))throw new Error("购物车商品列表必须是数组");return e.reduce(((e,t)=>$.add(e,t.quantity||0)),0)}static calculateItemCount(e){return Array.isArray(e)?e.length:0}static calculateAveragePrice(e){if(!Array.isArray(e)||0===e.length)return 0;const t=this.calculateTotal(e),a=this.calculateTotalQuantity(e);return 0===a?0:$.divide(t,a)}}e("C",class{static calculateChange(e,t){if(e<0||t<0)throw new Error("金额不能为负数");const a=$.subtract(e,t);return Math.max(0,a)}static calculateChangeDenominations(e,t=[100,50,20,10,5,1,.5,.1]){if(e<0)throw new Error("找零金额不能为负数");const a={};let r=e;for(const s of t)if(r>=s){const e=Math.floor(r/s);a[s]=e,r=$.subtract(r,$.multiply(s,e))}return a}});class E{static validateNumber(e,t={}){const{allowZero:a=!0,allowNegative:r=!1,min:s,max:n}=t;return"number"!=typeof e||isNaN(e)||!isFinite(e)?{isValid:!1,message:"必须是有效的数字"}:a||0!==e?!r&&e<0?{isValid:!1,message:"不能为负数"}:"number"==typeof s&&e<s?{isValid:!1,message:`不能小于${s}`}:"number"==typeof n&&e>n?{isValid:!1,message:`不能大于${n}`}:{isValid:!0,message:""}:{isValid:!1,message:"不能为0"}}static validateString(e,t={}){const{required:a=!0,minLength:r,maxLength:s,pattern:n,patternMessage:o="格式不正确"}=t;if("string"!=typeof e){if(a)return{isValid:!1,message:"必须是字符串"};if(null==e)return{isValid:!0,message:""}}const i=String(e||"");return a&&0===i.trim().length?{isValid:!1,message:"不能为空"}:"number"==typeof r&&i.length<r?{isValid:!1,message:`长度不能少于${r}个字符`}:"number"==typeof s&&i.length>s?{isValid:!1,message:`长度不能超过${s}个字符`}:n instanceof RegExp&&i.length>0&&!n.test(i)?{isValid:!1,message:o}:{isValid:!0,message:""}}static validateArray(e,t={}){const{required:a=!0,minLength:r,maxLength:s}=t;if(!Array.isArray(e)){if(a)return{isValid:!1,message:"必须是数组"};if(null==e)return{isValid:!0,message:""}}const n=Array.isArray(e)?e:[];return a&&0===n.length?{isValid:!1,message:"不能为空"}:"number"==typeof r&&n.length<r?{isValid:!1,message:`至少需要${r}个元素`}:"number"==typeof s&&n.length>s?{isValid:!1,message:`最多只能有${s}个元素`}:{isValid:!0,message:""}}}class N{static validateProduct(e){if(!e||"object"!=typeof e)return{isValid:!1,message:"商品信息不能为空"};const t=E.validateString(e.id,{required:!0,minLength:1,maxLength:50});if(!t.isValid)return{isValid:!1,message:`商品ID${t.message}`};const a=E.validateString(e.name,{required:!0,minLength:1,maxLength:100});if(!a.isValid)return{isValid:!1,message:`商品名称${a.message}`};const r=E.validateNumber(e.price,{allowZero:!1,allowNegative:!1,min:.01,max:999999.99});return r.isValid?e.pricingType&&!Object.values(k).includes(e.pricingType)?{isValid:!1,message:"无效的计价类型"}:{isValid:!0,message:""}:{isValid:!1,message:`商品价格${r.message}`}}static validateQuantity(e,t={}){const{maxQuantity:a=1e3,precision:r=I.QUANTITY_PRECISION}=t,s=E.validateNumber(e,{allowZero:!1,allowNegative:!1,min:.001,max:a});return s.isValid?(e.toString().split(".")[1]||"").length>r?{isValid:!1,message:`数量精度不能超过${r}位小数`}:{isValid:!0,message:""}:{isValid:!1,message:`商品数量${s.message}`}}static validateCartItem(e){if(!e||"object"!=typeof e)return{isValid:!1,message:"购物车项不能为空"};const t=this.validateProduct(e);if(!t.isValid)return t;const a=this.validateQuantity(e.quantity);if(!a.isValid)return a;if("number"==typeof e.subtotal){const t=e.price*e.quantity,a=.01;if(Math.abs(e.subtotal-t)>a)return{isValid:!1,message:"小计金额计算错误"}}return{isValid:!0,message:""}}static validateCart(e){const t=E.validateArray(e,{required:!0,minLength:1,maxLength:I.MAX_CART_ITEMS});if(!t.isValid)return{isValid:!1,message:`购物车${t.message}`};for(let s=0;s<e.length;s++){const t=this.validateCartItem(e[s]);if(!t.isValid)return{isValid:!1,message:`第${s+1}个商品${t.message}`}}const a=e.map((e=>e.id)),r=[...new Set(a)];return a.length!==r.length?{isValid:!1,message:"购物车中存在重复商品"}:{isValid:!0,message:""}}}function D(e={}){const t=r({enableAutoBackup:!0,backupInterval:3e4,maxBackupCount:10,enableCrashDetection:!0,enableRecoveryPrompt:!0,compressionEnabled:!0,encryptionEnabled:!1,...e}),a=r({isRecovering:!1,hasRecoverableData:!1,lastBackupTime:null,backupCount:0,recoveryHistory:[]}),l={CART:"cart",ORDER:"order",MEMBER:"member",PAYMENT:"payment",SETTINGS:"settings",SESSION:"session"};let u=null,m=null;const d=s((()=>a.value.hasRecoverableData&&!a.value.isRecovering)),p=s((()=>{const{lastBackupTime:e,backupCount:r}=a.value,s=e?Date.now()-e:null;return{lastBackupTime:e,backupCount:r,timeSinceBackup:s,isStale:s&&s>2*t.value.backupInterval}})),y=(e,t=0)=>`${x.DATA_BACKUP_PREFIX}_${e}_${t}`,g=e=>{try{return JSON.parse(e)}catch(t){return console.warn("数据解压失败:",t),null}},h=e=>{if(!t.value.encryptionEnabled)return e;try{return atob(e)}catch(a){return console.warn("数据解密失败:",a),e}},v=async(e,r,s={})=>{try{const{skipValidation:n=!1,metadata:o={}}=s;if(!n&&!w(e,r))throw new Error(`数据验证失败: ${e}`);const i=(e=>{if(!t.value.compressionEnabled)return JSON.stringify(e);try{return JSON.stringify(e).replace(/\\s+/g," ").trim()}catch(a){return console.warn("数据压缩失败:",a),JSON.stringify(e)}})({type:e,data:r,timestamp:Date.now(),version:"2.0",checksum:A(r),metadata:{userAgent:navigator.userAgent,url:window.location.href,sessionId:S(),...o}}),c=(e=>{if(!t.value.encryptionEnabled)return e;try{return btoa(e)}catch(a){return console.warn("数据加密失败:",a),e}})(i);await b(e);const l=y(e,0);return localStorage.setItem(l,c),a.value.lastBackupTime=Date.now(),a.value.backupCount++,a.value.hasRecoverableData=!0,console.log(`✅ 数据备份成功: ${e}`),!0}catch(n){return console.error(`❌ 数据备份失败: ${e}`,n),!1}},f=async(e,t={})=>{try{const{backupIndex:r=0,skipValidation:s=!1,maxAge:n=null}=t,o=y(e,r),i=localStorage.getItem(o);if(!i)return console.warn(`没有找到备份数据: ${e}`),null;const c=h(i),l=g(c);if(!l)throw new Error("数据解析失败");if(n&&Date.now()-l.timestamp>n)return console.warn(`备份数据已过期: ${e}`),null;if(!s&&A(l.data)!==l.checksum)throw new Error("数据校验失败");return a.value.recoveryHistory.unshift({type:e,timestamp:Date.now(),backupTimestamp:l.timestamp,success:!0}),a.value.recoveryHistory.length>50&&(a.value.recoveryHistory=a.value.recoveryHistory.slice(0,50)),console.log(`✅ 数据恢复成功: ${e}`),l.data}catch(r){return console.error(`❌ 数据恢复失败: ${e}`,r),a.value.recoveryHistory.unshift({type:e,timestamp:Date.now(),success:!1,error:r.message}),null}},b=async e=>{try{const a=t.value.maxBackupCount;for(let t=a-1;t>0;t--){const a=y(e,t-1),r=y(e,t),s=localStorage.getItem(a);s&&localStorage.setItem(r,s)}const r=y(e,a);localStorage.removeItem(r)}catch(a){console.warn("备份轮转失败:",a)}},w=(e,t)=>{if(!t||"object"!=typeof t)return!1;switch(e){case l.CART:return Array.isArray(t.items)&&"number"==typeof t.total;case l.ORDER:return t.orderId&&Array.isArray(t.items);case l.MEMBER:return t.memberId||t.memberInfo;case l.PAYMENT:return t.method&&"number"==typeof t.amount;case l.SETTINGS:return"object"==typeof t;case l.SESSION:return t.sessionId&&t.timestamp;default:return!0}},A=e=>{const t=JSON.stringify(e);let a=0;for(let r=0;r<t.length;r++)a=(a<<5)-a+t.charCodeAt(r),a|=0;return a.toString(36)},S=()=>{let e=sessionStorage.getItem("pos_session_id");return e||(e=`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,sessionStorage.setItem("pos_session_id",e)),e},C=async()=>{const e={};for(const a of Object.values(l)){const r=y(a,0),s=localStorage.getItem(r);if(s)try{const t=h(s),r=g(t);r&&r.timestamp&&(e[a]={timestamp:r.timestamp,age:Date.now()-r.timestamp,size:s.length})}catch(t){console.warn(`检查备份数据失败: ${a}`,t)}}return a.value.hasRecoverableData=Object.keys(e).length>0,e},I=e=>{if(!t.value.enableRecoveryPrompt)return;const a=Object.keys(e);if(0===a.length)return;const r={[l.CART]:"购物车",[l.ORDER]:"订单",[l.MEMBER]:"会员信息",[l.PAYMENT]:"支付信息",[l.SETTINGS]:"设置",[l.SESSION]:"会话"},s=a.map((t=>{const a=e[t];return`${r[t]||t} (${new Date(a.timestamp).toLocaleString()})`})).join("\\n");i.confirm({title:"发现可恢复的数据",content:`检测到以下数据可以恢复：\\n\\n${s}\\n\\n是否要恢复这些数据？`,okText:"恢复",cancelText:"忽略",onOk:async()=>{await k(a)},onCancel:()=>{c.info("已忽略数据恢复")}})},k=async e=>{a.value.isRecovering=!0;try{const t=[];for(const s of e){const e=await f(s);t.push({type:s,data:e,success:!!e})}const a=t.filter((e=>e.success)).length,r=t.length-a;return a>0&&c.success(`成功恢复 ${a} 项数据`),r>0&&c.warning(`${r} 项数据恢复失败`),t}catch(t){return console.error("批量恢复失败:",t),c.error("数据恢复失败"),[]}finally{a.value.isRecovering=!1}},M=()=>{u&&(clearInterval(u),u=null,console.log("⏹️ 自动备份已停止"))},V=(e=null)=>{try{if(e){for(let a=0;a<t.value.maxBackupCount;a++){const t=y(e,a);localStorage.removeItem(t)}console.log(`🧹 已清除 ${e} 类型的所有备份`)}else{for(const e of Object.values(l))for(let a=0;a<t.value.maxBackupCount;a++){const t=y(e,a);localStorage.removeItem(t)}a.value.hasRecoverableData=!1,a.value.backupCount=0,a.value.lastBackupTime=null,console.log("🧹 已清除所有备份数据")}}catch(r){console.error("清除备份数据失败:",r)}},T=()=>{const e={totalBackups:0,totalSize:0,typeStats:{},oldestBackup:null,newestBackup:null};for(const r of Object.values(l)){const s={count:0,size:0,timestamps:[]};for(let e=0;e<t.value.maxBackupCount;e++){const t=y(r,e),n=localStorage.getItem(t);if(n){s.count++,s.size+=n.length;try{const e=h(n),t=g(e);t&&t.timestamp&&s.timestamps.push(t.timestamp)}catch(a){}}}if(s.count>0){e.typeStats[r]=s,e.totalBackups+=s.count,e.totalSize+=s.size;const t=Math.min(...s.timestamps),a=Math.max(...s.timestamps);(!e.oldestBackup||t<e.oldestBackup)&&(e.oldestBackup=t),(!e.newestBackup||a>e.newestBackup)&&(e.newestBackup=a)}}return e};return n((async()=>{await C(),(()=>{if(!t.value.enableCrashDetection)return;const e="pos_heartbeat",a=()=>{localStorage.setItem(e,Date.now().toString())};a(),m=setInterval(a,5e3);const r=localStorage.getItem(e);r&&Date.now()-parseInt(r)>3e4&&(console.warn("检测到异常退出，检查可恢复数据"),setTimeout((async()=>{const e=await C();Object.keys(e).length>0&&I(e)}),1e3)),window.addEventListener("beforeunload",(()=>{localStorage.removeItem(e)}))})()})),o((()=>{M(),m&&(clearInterval(m),m=null)})),{recoveryState:a,config:t,canRecover:d,backupStatus:p,backupTypes:l,saveData:v,restoreData:f,checkRecoverableData:C,showRecoveryPrompt:I,performBatchRecovery:k,setupAutoBackup:e=>{t.value.enableAutoBackup&&!u&&(u=setInterval((async()=>{try{const t=await e();t&&(t.cart&&await v(l.CART,t.cart),t.order&&await v(l.ORDER,t.order),t.member&&await v(l.MEMBER,t.member),t.payment&&await v(l.PAYMENT,t.payment),t.settings&&await v(l.SETTINGS,t.settings))}catch(t){console.warn("自动备份失败:",t)}}),t.value.backupInterval),console.log("🔄 自动备份已启用"))},stopAutoBackup:M,clearAllBackups:V,getBackupStatistics:T,exportBackupData:(e="json")=>{const a={};for(const s of Object.values(l)){const e=[];for(let a=0;a<t.value.maxBackupCount;a++){const t=y(s,a),n=localStorage.getItem(t);if(n)try{const t=h(n),a=g(t);a&&e.push(a)}catch(r){console.warn(`导出备份数据失败: ${t}`,r)}}e.length>0&&(a[s]=e)}if("csv"===e){const e=[];e.push("Type,Timestamp,Size,Checksum");for(const[t,r]of Object.entries(a))r.forEach((a=>{const r=JSON.stringify(a.data).length;e.push(`${t},${new Date(a.timestamp).toISOString()},${r},${a.checksum}`)}));return e.join("\\n")}return JSON.stringify({exportTime:(new Date).toISOString(),backups:a,statistics:T()},null,2)},saveCartState:e=>v(l.CART,e),restoreCartState:()=>f(l.CART),clearBackup:()=>V(l.CART)}}function B(){const e=A();D();const t=s((()=>e.cartItems||[])),a=s((()=>{try{const a=O.calculateTotal(t.value),r=e.discountAmount||0,s=Math.max(0,a-r);return{totalAmount:Number(a.toFixed(2)),discountAmount:Number(r.toFixed(2)),finalAmount:Number(s.toFixed(2)),itemCount:O.calculateItemCount(t.value),totalQuantity:O.calculateTotalQuantity(t.value)}}catch(a){return console.error("计算购物车总金额失败:",a),{totalAmount:0,discountAmount:0,finalAmount:0,itemCount:0,totalQuantity:0}}})),n=s((()=>0===t.value.length)),o=s((()=>!n.value&&a.value.finalAmount>0)),i=s((()=>({itemCount:a.value.itemCount,totalQuantity:a.value.totalQuantity,totalAmount:a.value.totalAmount,discountAmount:a.value.discountAmount,finalAmount:a.value.finalAmount,averagePrice:t.value.length>0?O.calculateAveragePrice(t.value):0}))),l=r(!1),u=async(a,r=1)=>{try{l.value=!0;const s={...a,id:String(a.id||""),name:a.name||a.productName||"未知商品"},n=N.validateProduct(s);if(!n.isValid)return c.error(n.message),!1;const o=N.validateQuantity(r);if(!o.isValid)return c.error(o.message),!1;if(t.value.length>=I.MAX_CART_ITEMS)return c.error(`购物车商品数量不能超过${I.MAX_CART_ITEMS}件`),!1;const i=await R.checkInventory(s.id,r);if(!i.available)return c.error(`商品"${s.name}"库存不足，当前库存：${i.stock}`),!1;const u=t.value.findIndex((e=>e.id===s.id));if(-1!==u){const e=t.value[u],a=e.quantity+r,n=await R.checkInventory(s.id,a);return n.available?await m(s.id,a):(c.error(`商品"${s.name}"库存不足，当前库存：${n.stock}，已添加：${e.quantity}`),!1)}{const t={id:s.id,name:s.name,barcode:s.barcode||"",price:s.price,quantity:r,subtotal:O.calculateSubtotal(s.price,r),unit:s.unit||"件",categoryId:s.categoryId||"",categoryName:s.categoryName||"",specifications:s.specifications||"",image:s.image||"",pricingType:s.pricingType||"NORMAL",addedAt:(new Date).toISOString()},a=N.validateCartItem(t);return a.isValid?(e.addCartItem(t),await p(),c.success(`已添加"${s.name}"到购物车`),!0):(c.error(a.message),!1)}}catch(s){return console.error("添加商品到购物车失败:",s),c.error("添加商品失败，请重试"),!1}finally{l.value=!1}},m=async(a,r)=>{try{l.value=!0;const s=String(a||""),n=N.validateQuantity(r);if(!n.isValid)return c.error(n.message),!1;const o=t.value.find((e=>e.id===s));if(!o)return c.error("商品不存在"),!1;if(0===r)return await d(s);const i=await R.checkInventory(s,r);if(!i.available)return c.error(`商品"${o.name}"库存不足，当前库存：${i.stock}`),!1;const u={...o,quantity:r,subtotal:O.calculateSubtotal(o.price,r),updatedAt:(new Date).toISOString()},m=N.validateCartItem(u);return m.isValid?(e.updateCartItem(s,u),await p(),!0):(c.error(m.message),!1)}catch(s){return console.error("更新商品数量失败:",s),c.error("更新数量失败，请重试"),!1}finally{l.value=!1}},d=async a=>{try{const r=String(a||""),s=t.value.find((e=>e.id===r));return s?(e.removeCartItem(r),await p(),c.success(`已移除"${s.name}"`),!0):(c.error("商品不存在"),!1)}catch(r){return console.error("移除商品失败:",r),c.error("移除商品失败，请重试"),!1}},p=async()=>(console.log("数据备份功能已禁用"),!0);return{items:t,cartTotal:a,cartSummary:i,isEmpty:n,canCheckout:o,loading:l,addItem:u,updateQuantity:m,removeItem:d,clearCart:async()=>{try{return n.value?(c.info("购物车已经是空的"),!0):(e.clearCart(),await p(),c.success("购物车已清空"),!0)}catch(t){return console.error("清空购物车失败:",t),c.error("清空购物车失败，请重试"),!1}},batchAddItems:async e=>{const t={success:0,failed:0,errors:[]};try{l.value=!0;const r=e.map((({product:e,quantity:t})=>({productId:String(e.id||""),quantity:t||1}))),s=await R.batchCheckInventory(r),n=new Map(s.map((e=>[String(e.productId),e])));for(const{product:o,quantity:i=1}of e)try{const e=String(o.id||""),a=n.get(e);if(!a||!a.available){const e=o.name||o.productName||"未知商品";t.failed++,t.errors.push(`商品"${e}"库存不足`);continue}if(await u(o,i))t.success++;else{const e=o.name||o.productName||"未知商品";t.failed++,t.errors.push(`添加商品"${e}"失败`)}}catch(a){const e=o.name||o.productName||"未知商品";t.failed++,t.errors.push(`添加商品"${e}"失败: ${a.message}`)}return t.success>0&&c.success(`成功添加${t.success}件商品`),t.failed>0&&c.warning(`${t.failed}件商品添加失败`),t}catch(a){return console.error("批量添加商品失败:",a),c.error("批量添加商品失败，请重试"),{success:0,failed:e.length,errors:[a.message]}}finally{l.value=!1}},validateCart:async()=>{try{const e=N.validateCart(t.value);return e.isValid?await R.validateCart({items:t.value}):e}catch(e){return console.error("验证购物车失败:",e),{isValid:!1,message:"验证购物车失败，请重试"}}},applyCoupon:async(a,r=null)=>{try{if(l.value=!0,n.value)return c.error("购物车为空，无法使用优惠券"),!1;const s=await R.applyCoupon({items:t.value,couponCode:a,member:r});return s.success?(e.updateDiscountAmount(s.discountAmount),c.success(`优惠券应用成功，优惠${s.discountAmount}元`),!0):(c.error(s.message||"优惠券应用失败"),!1)}catch(s){return console.error("应用优惠券失败:",s),c.error("应用优惠券失败，请重试"),!1}finally{l.value=!1}},removeCoupon:async a=>{try{l.value=!0;const r=await R.removeCoupon({items:t.value,couponId:a});if(r.success){const t=Math.max(0,e.discountAmount-r.removedDiscount);return e.updateDiscountAmount(t),c.success("优惠券已移除"),!0}return c.error("移除优惠券失败"),!1}catch(r){return console.error("移除优惠券失败:",r),c.error("移除优惠券失败，请重试"),!1}finally{l.value=!1}},addItemByBarcode:async(e,t=1)=>{try{if(l.value=!0,!e||""===e.trim())return c.error("请输入商品条码"),!1;const a=await R.getProductByBarcode(e.trim());return a?await u(a,t):(c.error("未找到对应的商品"),!1)}catch(a){return console.error("根据条码添加商品失败:",a),c.error("商品条码无效或商品不存在"),!1}finally{l.value=!1}},saveCartBackup:p,restoreCartBackup:async()=>(console.log("数据恢复功能已禁用"),!1)}}e("P",class{static validatePaymentAmount(e,t={}){const{maxAmount:a=999999.99}=t;return E.validateNumber(e,{allowZero:!1,allowNegative:!1,min:.01,max:a})}static validateCashPayment(e){if(!e||"object"!=typeof e)return{isValid:!1,message:"支付数据不能为空"};const t=this.validatePaymentAmount(e.payableAmount);if(!t.isValid)return{isValid:!1,message:`应付金额${t.message}`};const a=this.validatePaymentAmount(e.receivedAmount);return a.isValid?e.receivedAmount<e.payableAmount?{isValid:!1,message:"实收金额不能少于应付金额"}:{isValid:!0,message:""}:{isValid:!1,message:`实收金额${a.message}`}}static validateQrCodePayment(e){if(!e||"object"!=typeof e)return{isValid:!1,message:"支付数据不能为空"};const t=this.validatePaymentAmount(e.payableAmount);return t.isValid?["WECHAT","ALIPAY"].includes(e.paymentMethod)?{isValid:!0,message:""}:{isValid:!1,message:"无效的扫码支付方式"}:{isValid:!1,message:`应付金额${t.message}`}}static validateBankCardPayment(e){if(!e||"object"!=typeof e)return{isValid:!1,message:"支付数据不能为空"};const t=this.validatePaymentAmount(e.payableAmount);if(!t.isValid)return{isValid:!1,message:`应付金额${t.message}`};const a=E.validateString(e.cardNo,{required:!0,minLength:16,maxLength:19,pattern:/^\d+$/,patternMessage:"银行卡号只能包含数字"});return a.isValid?this.validateLuhn(e.cardNo)?{isValid:!0,message:""}:{isValid:!1,message:"银行卡号格式不正确"}:{isValid:!1,message:`银行卡号${a.message}`}}static validateLuhn(e){if("string"!=typeof e||!/^\d+$/.test(e))return!1;let t=0,a=!1;for(let r=e.length-1;r>=0;r--){let s=parseInt(e.charAt(r),10);a&&(s*=2,s>9&&(s=s%10+1)),t+=s,a=!a}return t%10==0}});const P={class:"shopping-cart"},_={class:"cart-content"},j={key:0,class:"cart-items"},q={key:1,class:"empty-cart"},L=e("S",l(Object.assign({name:"ShoppingCart"},{__name:"ShoppingCart",props:{currentMember:{type:Object,default:null}},emits:["checkout","suspend-order","item-change"],setup(e,{emit:t}){const a=e,r=t;A();const{items:s,cartTotal:n,cartSummary:o,isEmpty:c,canCheckout:l,loading:S,updateQuantity:C,removeItem:I,clearCart:k}=B(),x=async(e,t)=>{await C(e,t)&&r("item-change",{type:"update",itemId:e,quantity:t})},R=async e=>{await I(e)&&r("item-change",{type:"remove",itemId:e})},$=()=>{i.confirm({title:"确认清空购物车",content:"确定要清空购物车中的所有商品吗？",okText:"确认清空",cancelText:"取消",okType:"danger",onOk:async()=>{await k()&&r("item-change",{type:"clear"})}})},O=()=>{l.value&&r("checkout",{items:s.value,total:n.value,member:a.currentMember})},E=()=>{c.value||r("suspend-order",{items:s.value,total:n.value,member:a.currentMember})};return(t,a)=>{const r=w;return u(),m("div",P,[d(c)?y("",!0):(u(),p(M,{key:0,"item-count":d(o).itemCount,"total-quantity":d(o).totalQuantity,onClearCart:$},null,8,["item-count","total-quantity"])),g("div",_,[d(c)?(u(),m("div",q,[f(r,{description:"购物车为空",image:d(w).PRESENTED_IMAGE_SIMPLE},{default:b((()=>a[0]||(a[0]=[g("p",{class:"empty-tip"},"请选择商品添加到购物车",-1)]))),_:1,__:[0]},8,["image"])])):(u(),m("div",j,[(u(!0),m(h,null,v(d(s),(e=>(u(),p(V,{key:e.id,item:e,loading:d(S),onUpdateQuantity:x,onRemoveItem:R},null,8,["item","loading"])))),128))]))]),d(c)?y("",!0):(u(),p(T,{key:1,"cart-total":d(n),member:e.currentMember,"can-checkout":d(l),loading:d(S),onCheckout:O,onSuspendOrder:E},null,8,["cart-total","member","can-checkout","loading"]))])}}}),[["__scopeId","data-v-9dcb7d34"]])),Q=Object.freeze(Object.defineProperty({__proto__:null,default:L},Symbol.toStringTag,{value:"Module"}));e("a",Q)}}}));
