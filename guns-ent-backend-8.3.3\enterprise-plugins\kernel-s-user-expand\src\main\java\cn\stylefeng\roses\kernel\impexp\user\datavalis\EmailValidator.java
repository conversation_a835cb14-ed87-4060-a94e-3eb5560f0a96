package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

import java.util.regex.Pattern;

/**
 * 更新邮箱的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 22:51
 */
public class EmailValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(originValue);
        }

        String regex = "\\w+@\\w+\\.[a-z]+(\\.[a-z]+)?";
        boolean matches = Pattern.matches(regex, originValue);

        if (matches) {
            return new ExcelLineParseResult(originValue);
        } else {
            return new ExcelLineParseResult(false, originValue, originValue, "邮箱格式不正确");
        }
    }

}
