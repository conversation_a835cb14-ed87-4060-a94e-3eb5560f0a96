import{r as l,o as g,a as I,f as _,w as y,d as M,m as P,M as k}from"./index-18a1ea24.js";import U from"./menu-form-cc583187.js";import{M as u}from"./MenuApi-47485f5a.js";/* empty css              *//* empty css              */import"./AppApi-4e70edf8.js";const w={__name:"menu-add-edit",props:{visible:Boolean,data:Object,appId:String,menuParentId:String,menuParentName:String},emits:["update:visible","done"],setup(p,{emit:v}){const a=p,d=v,o=l(!1),s=l(!1),t=l({antdvVisible:"Y",menuSort:100,menuType:10,appId:a.appId,menuParentId:a.menuParentId,menuParentName:a.menuParentName}),r=l(null);g(()=>{a.data?(s.value=!0,c()):s.value=!1});const c=()=>{u.detail({menuId:a.data.menuId}).then(n=>{t.value=Object.assign({},n),t.value.menuId=a.data.menuId})},i=n=>{d("update:visible",n)},b=()=>{r.value.$refs.formRef.validate().then(async n=>{if(n){o.value=!0;let e=null;s.value?e=u.edit(t.value):e=u.add(t.value),e.then(async m=>{o.value=!1,P.success(m.message),i(!1),d("done")}).catch(()=>{o.value=!1})}})};return(n,e)=>{const m=k;return I(),_(m,{width:800,maskClosable:!1,visible:a.visible,"confirm-loading":o.value,forceRender:!0,title:s.value?"\u7F16\u8F91\u83DC\u5355":"\u65B0\u5EFA\u83DC\u5355","body-style":{paddingBottom:"8px"},"onUpdate:visible":i,onOk:b,class:"common-modal",onClose:e[1]||(e[1]=f=>i(!1))},{default:y(()=>[M(U,{form:t.value,"onUpdate:form":e[0]||(e[0]=f=>t.value=f),ref_key:"menuFormRef",ref:r,isUpdate:s.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}};export{w as default};
