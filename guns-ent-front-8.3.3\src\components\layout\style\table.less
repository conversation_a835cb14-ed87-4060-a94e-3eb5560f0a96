.cell-hover:hover {
  background-color: #eef3fc !important;
  position: relative;
}
.cell-hoverr:hover {
  background-color: #eef3fc !important;
  position: relative;
}

.cell-hover:not(.ant-table-cell-fix-left):not(.ant-table-cell-fix-right):hover {
  &::after {
    content: '';
    right: 10px;
    top: 50%;
    margin-top: -8px;
    position: absolute;
    width: 16px;
    height: 16px;
    background-image: url('@/assets/table/down.svg');
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.ant-table-cell-fix-left.cell-hover:hover,
.ant-table-cell-fix-right.cell-hover:hover {
  &:before {
    content: '';
    right: 10px;
    top: 50%;
    margin-top: -8px;
    position: absolute;
    width: 16px;
    height: 16px;
    background-image: url('@/assets/table/down.svg');
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.cell-not-ima-hover:hover {
  background-color: #eef3fc !important;
  position: relative;
}

.add-placeholder {
  display: none;
}

.cell-add-hover:hover {
  background-color: #eef3fc !important;
  position: relative;
  cursor: pointer;

  .add-placeholder {
    display: inline-block;
  }
}

.cell-add-hover:not(.ant-table-cell-fix-left):not(.ant-table-cell-fix-right):hover {
  &::after {
    content: '';
    right: 10px;
    top: 50%;
    margin-top: -8px;
    position: absolute;
    width: 16px;
    height: 16px;
    background-image: url('@/assets/table/add.svg');
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.ant-table-cell-fix-left.cell-add-hover:hover,
.ant-table-cell-fix-right.cell-add-hover:hover {
  &:before {
    content: '';
    right: 10px;
    top: 50%;
    margin-top: -8px;
    position: absolute;
    width: 16px;
    height: 16px;
    background-image: url('@/assets/table/add.svg');
    background-size: contain;
    background-repeat: no-repeat;
  }
}
