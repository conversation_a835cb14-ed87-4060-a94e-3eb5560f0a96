<p align="center">
    <img src="https://images.gitee.com/uploads/images/2019/0112/222304_562302ff_551203.png" width="300">
    <br>      
    <br>      
    <p align="center">
        Roses基于Spring Boot 2，是<a target="_blank" href="https://gitee.com/stylefeng/guns">开源项目Guns</a>的核心支撑层，亦可用于任何java项目支撑内核。
        <br>      
        <br>      
        <span>
            <span>
                 Guns官方交流群：254550081(满)   684163663(满)   207434260(满)(三个群已满，扫下方二维码加官方微信群)
            </span>
        </span>
        <br>
        <br>
        <a href="http://spring.io/projects/spring-boot">
            <img src="https://img.shields.io/badge/spring--boot-2.3.5-green.svg" alt="spring-boot">
        </a>
        <a href="http://mp.baomidou.com">
            <img src="https://img.shields.io/badge/mybatis--plus-3.4.0-blue.svg" alt="mybatis-plus">
        </a>  
        <a href="https://www.hutool.cn/">
            <img src="https://img.shields.io/badge/hutool-5.4.4-blue.svg" alt="hutool">
        </a>  
    </p>
</p>

-----------------------------------------------------------------------------------------------

### 更新说明

目前Roses已全面升级，整体模块化重构，整体功能分为4类，`规则类模块(R)`、`开发工具模块(D)`、`运维类模块(O)`、`业务类模块(S)`

### 配套手册

[https://javaguns.com/gunsDoc?categoryId=1504358929897992193&artId=1504372009277173762](https://javaguns.com/gunsDoc?categoryId=1504358929897992193&artId=1504372009277173762)

### 使用方式

```
# 安装到本地maven仓库
mvn clean install -Dmaven.test.skip=true
```

### 扫码关注官方公众号和官方微信群
<table>
    <tr>
        <td>官方公众号</td>
        <td><img src="https://images.gitee.com/uploads/images/2019/0415/104911_9bc924a5_551203.png" width="120"/></td>
        <td>扫码邀请入群</td>
        <td><img src="https://images.gitee.com/uploads/images/2019/0419/103622_d6e9fa5d_551203.png" width="120"/></td>
    </tr>
</table>
