server:
  port: 8100

spring:
  application:
    name: guns-cloud-tx-service
  profiles:
    active: local
  flyway:
    enabled: false
  cloud:
    nacos:
      discovery:
        enabled: true
        register-enabled: true
        watch-delay: 1000
  main:
    allow-circular-references: true

roses:
  message:
    checkIntervalSeconds: 10

scanner:
  open: true
  urlWithAppCode: true

# feign远程调用配置
feign:
  sentinel:
    enabled: true
  client:
    config:
      # 全局配置
      default:
        # NONE不记录任何日志--BASIC仅请求方法URL,状态码执行时间等--HEADERS在BASIC基础上记录header等--FULL记录所有
        loggerLevel: full
        connectTimeout: 500  #连接超时时间
        readTimeout: 5000    #连接超时时间
        errorDecoder: cn.stylefeng.roses.kernel.micro.core.feign.GunsFeignErrorDecoder
        requestInterceptors:
          - cn.stylefeng.roses.kernel.micro.core.feign.GunsFeignHeaderProcessInterceptor
  httpclient:
    # 让feign使用apache httpclient做请求；而不是默认的urlConnection
    enabled: true
    # feign的最大连接数
    max-connections: 200
    # feign单个路径的最大连接数
    max-connections-per-route: 50

# actuator配置，给spring boot admin监控用
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

mybatis-plus:
  mapper-locations: classpath*:cn/stylefeng/roses/kernel/micro/tran/message/modular/mapper/mapping/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
  global-config:
    banner: false
    enable-sql-runner: true
    db-config:
      id-type: assign_id
      table-underline: true