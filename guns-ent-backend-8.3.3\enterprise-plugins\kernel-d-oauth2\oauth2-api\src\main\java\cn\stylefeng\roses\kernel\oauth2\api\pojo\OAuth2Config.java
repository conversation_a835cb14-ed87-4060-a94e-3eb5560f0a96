package cn.stylefeng.roses.kernel.oauth2.api.pojo;

import lombok.Data;

/**
 * 支付相关的配置
 *
 * <AUTHOR>
 * @date 2022/4/19 12:02
 */
@Data
public class OAuth2Config {

    /**
     * 前端应用的地址，默认是:http://localhost:8000
     * <p>
     * 用来做oauth2回调时候跳转到前端应用并登录
     */
    private String frontAppHost = "http://localhost:8000";

    /**
     * 本后台部署地址，用来请求第三方oauth2时，拼接回调地址
     */
    private String backendCallbackHost = "http://localhost:8080";

}
