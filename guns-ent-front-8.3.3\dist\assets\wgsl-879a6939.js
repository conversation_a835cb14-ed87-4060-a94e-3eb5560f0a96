/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var g={comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"[",close:"]"},{open:"{",close:"}"},{open:"(",close:")"}],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"}]};function e(i){let o=[];const a=i.split(/\t+|\r+|\n+| +/);for(let r=0;r<a.length;++r)a[r].length>0&&o.push(a[r]);return o}var s=e("true false"),c=e("\n			  alias\n			  break\n			  case\n			  const\n			  const_assert\n			  continue\n			  continuing\n			  default\n			  diagnostic\n			  discard\n			  else\n			  enable\n			  fn\n			  for\n			  if\n			  let\n			  loop\n			  override\n			  requires\n			  return\n			  struct\n			  switch\n			  var\n			  while\n			  "),m=e("\n			  NULL\n			  Self\n			  abstract\n			  active\n			  alignas\n			  alignof\n			  as\n			  asm\n			  asm_fragment\n			  async\n			  attribute\n			  auto\n			  await\n			  become\n			  binding_array\n			  cast\n			  catch\n			  class\n			  co_await\n			  co_return\n			  co_yield\n			  coherent\n			  column_major\n			  common\n			  compile\n			  compile_fragment\n			  concept\n			  const_cast\n			  consteval\n			  constexpr\n			  constinit\n			  crate\n			  debugger\n			  decltype\n			  delete\n			  demote\n			  demote_to_helper\n			  do\n			  dynamic_cast\n			  enum\n			  explicit\n			  export\n			  extends\n			  extern\n			  external\n			  fallthrough\n			  filter\n			  final\n			  finally\n			  friend\n			  from\n			  fxgroup\n			  get\n			  goto\n			  groupshared\n			  highp\n			  impl\n			  implements\n			  import\n			  inline\n			  instanceof\n			  interface\n			  layout\n			  lowp\n			  macro\n			  macro_rules\n			  match\n			  mediump\n			  meta\n			  mod\n			  module\n			  move\n			  mut\n			  mutable\n			  namespace\n			  new\n			  nil\n			  noexcept\n			  noinline\n			  nointerpolation\n			  noperspective\n			  null\n			  nullptr\n			  of\n			  operator\n			  package\n			  packoffset\n			  partition\n			  pass\n			  patch\n			  pixelfragment\n			  precise\n			  precision\n			  premerge\n			  priv\n			  protected\n			  pub\n			  public\n			  readonly\n			  ref\n			  regardless\n			  register\n			  reinterpret_cast\n			  require\n			  resource\n			  restrict\n			  self\n			  set\n			  shared\n			  sizeof\n			  smooth\n			  snorm\n			  static\n			  static_assert\n			  static_cast\n			  std\n			  subroutine\n			  super\n			  target\n			  template\n			  this\n			  thread_local\n			  throw\n			  trait\n			  try\n			  type\n			  typedef\n			  typeid\n			  typename\n			  typeof\n			  union\n			  unless\n			  unorm\n			  unsafe\n			  unsized\n			  use\n			  using\n			  varying\n			  virtual\n			  volatile\n			  wgsl\n			  where\n			  with\n			  writeonly\n			  yield\n			  "),l=e("\n		read write read_write\n		function private workgroup uniform storage\n		perspective linear flat\n		center centroid sample\n		vertex_index instance_index position front_facing frag_depth\n			local_invocation_id local_invocation_index\n			global_invocation_id workgroup_id num_workgroups\n			sample_index sample_mask\n		rgba8unorm\n		rgba8snorm\n		rgba8uint\n		rgba8sint\n		rgba16uint\n		rgba16sint\n		rgba16float\n		r32uint\n		r32sint\n		r32float\n		rg32uint\n		rg32sint\n		rg32float\n		rgba32uint\n		rgba32sint\n		rgba32float\n		bgra8unorm\n"),u=e("\n		bool\n		f16\n		f32\n		i32\n		sampler sampler_comparison\n		texture_depth_2d\n		texture_depth_2d_array\n		texture_depth_cube\n		texture_depth_cube_array\n		texture_depth_multisampled_2d\n		texture_external\n		texture_external\n		u32\n		"),p=e("\n		array\n		atomic\n		mat2x2\n		mat2x3\n		mat2x4\n		mat3x2\n		mat3x3\n		mat3x4\n		mat4x2\n		mat4x3\n		mat4x4\n		ptr\n		texture_1d\n		texture_2d\n		texture_2d_array\n		texture_3d\n		texture_cube\n		texture_cube_array\n		texture_multisampled_2d\n		texture_storage_1d\n		texture_storage_2d\n		texture_storage_2d_array\n		texture_storage_3d\n		vec2\n		vec3\n		vec4\n		"),d=e("\n		vec2i vec3i vec4i\n		vec2u vec3u vec4u\n		vec2f vec3f vec4f\n		vec2h vec3h vec4h\n		mat2x2f mat2x3f mat2x4f\n		mat3x2f mat3x3f mat3x4f\n		mat4x2f mat4x3f mat4x4f\n		mat2x2h mat2x3h mat2x4h\n		mat3x2h mat3x3h mat3x4h\n		mat4x2h mat4x3h mat4x4h\n		"),x=e("\n  bitcast all any select arrayLength abs acos acosh asin asinh atan atanh atan2\n  ceil clamp cos cosh countLeadingZeros countOneBits countTrailingZeros cross\n  degrees determinant distance dot exp exp2 extractBits faceForward firstLeadingBit\n  firstTrailingBit floor fma fract frexp inverseBits inverseSqrt ldexp length\n  log log2 max min mix modf normalize pow quantizeToF16 radians reflect refract\n  reverseBits round saturate sign sin sinh smoothstep sqrt step tan tanh transpose\n  trunc dpdx dpdxCoarse dpdxFine dpdy dpdyCoarse dpdyFine fwidth fwidthCoarse fwidthFine\n  textureDimensions textureGather textureGatherCompare textureLoad textureNumLayers\n  textureNumLevels textureNumSamples textureSample textureSampleBias textureSampleCompare\n  textureSampleCompareLevel textureSampleGrad textureSampleLevel textureSampleBaseClampToEdge\n  textureStore atomicLoad atomicStore atomicAdd atomicSub atomicMax atomicMin\n  atomicAnd atomicOr atomicXor atomicExchange atomicCompareExchangeWeak pack4x8snorm\n  pack4x8unorm pack2x16snorm pack2x16unorm pack2x16float unpack4x8snorm unpack4x8unorm\n  unpack2x16snorm unpack2x16unorm unpack2x16float storageBarrier workgroupBarrier\n  workgroupUniformLoad\n"),f=e("\n					 &\n					 &&\n					 ->\n					 /\n					 =\n					 ==\n					 !=\n					 >\n					 >=\n					 <\n					 <=\n					 %\n					 -\n					 --\n					 +\n					 ++\n					 |\n					 ||\n					 *\n					 <<\n					 >>\n					 +=\n					 -=\n					 *=\n					 /=\n					 %=\n					 &=\n					 |=\n					 ^=\n					 >>=\n					 <<=\n					 "),_=/enable|requires|diagnostic/,n=new RegExp("[_\\p{XID_Start}]\\p{XID_Continue}*","u"),t="variable.predefined",h={tokenPostfix:".wgsl",defaultToken:"invalid",unicode:!0,atoms:s,keywords:c,reserved:m,predeclared_enums:l,predeclared_types:u,predeclared_type_generators:p,predeclared_type_aliases:d,predeclared_intrinsics:x,operators:f,symbols:/[!%&*+\-\.\/:;<=>^|_~,]+/,tokenizer:{root:[[_,"keyword","@directive"],[n,{cases:{"@atoms":t,"@keywords":"keyword","@reserved":"invalid","@predeclared_enums":t,"@predeclared_types":t,"@predeclared_type_generators":t,"@predeclared_type_aliases":t,"@predeclared_intrinsics":t,"@default":"identifier"}}],{include:"@commentOrSpace"},{include:"@numbers"},[/[{}()\[\]]/,"@brackets"],["@","annotation","@attribute"],[/@symbols/,{cases:{"@operators":"operator","@default":"delimiter"}}],[/./,"invalid"]],commentOrSpace:[[/\s+/,"white"],[/\/\*/,"comment","@blockComment"],[/\/\/.*$/,"comment"]],blockComment:[[/[^\/*]+/,"comment"],[/\/\*/,"comment","@push"],[/\*\//,"comment","@pop"],[/[\/*]/,"comment"]],attribute:[{include:"@commentOrSpace"},[/\w+/,"annotation","@pop"]],directive:[{include:"@commentOrSpace"},[/[()]/,"@brackets"],[/,/,"delimiter"],[n,"meta.content"],[/;/,"delimiter","@pop"]],numbers:[[/0[fh]/,"number.float"],[/[1-9][0-9]*[fh]/,"number.float"],[/[0-9]*\.[0-9]+([eE][+-]?[0-9]+)?[fh]?/,"number.float"],[/[0-9]+\.[0-9]*([eE][+-]?[0-9]+)?[fh]?/,"number.float"],[/[0-9]+[eE][+-]?[0-9]+[fh]?/,"number.float"],[/0[xX][0-9a-fA-F]*\.[0-9a-fA-F]+(?:[pP][+-]?[0-9]+[fh]?)?/,"number.hex"],[/0[xX][0-9a-fA-F]+\.[0-9a-fA-F]*(?:[pP][+-]?[0-9]+[fh]?)?/,"number.hex"],[/0[xX][0-9a-fA-F]+[pP][+-]?[0-9]+[fh]?/,"number.hex"],[/0[xX][0-9a-fA-F]+[iu]?/,"number.hex"],[/[1-9][0-9]*[iu]?/,"number"],[/0[iu]?/,"number"]]}};export{g as conf,h as language};
