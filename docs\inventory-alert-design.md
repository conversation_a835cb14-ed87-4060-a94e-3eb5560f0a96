# JavaGuns Enterprise 库存预警功能设计方案

## 一、现有库存相关表结构分析

### 核心表结构
1. **erp_product（商品主档案表）**
   - 包含商品基本信息、保质期（shelf_life）、供应商ID等
   - 支持临期预警和供应商建议

2. **erp_inventory（库存表）**
   - 当前库存（current_stock）、最小库存（min_stock）、最大库存（max_stock）
   - 已有基础的库存预警索引：`idx_stock_warning`

3. **erp_inventory_history（库存历史表）**
   - 记录库存变动历史，支持趋势分析

4. **erp_supplier（供应商表）**
   - 供应商信息，用于预警时的补货建议

5. **erp_product_category（商品分类表）**
   - 商品分类，支持分类级别的预警规则

## 二、新增数据库表设计

### 1. erp_inventory_alert_rule（库存预警规则表）
**功能**：配置各种预警规则
**核心字段**：
- `rule_type`：预警类型（LOW_STOCK、EXPIRY、OVERSTOCK、ZERO_STOCK）
- `target_type`：目标类型（PRODUCT、CATEGORY、ALL）
- `alert_level`：预警级别（CRITICAL、WARNING、INFO）
- `threshold_type`：阈值类型（QUANTITY、PERCENTAGE、DAYS）
- `threshold_value`：阈值
- `notification_methods`：通知方式（SYSTEM、EMAIL、SMS、WECHAT）
- `check_frequency`：检查频率（分钟）

### 2. erp_inventory_alert_record（库存预警记录表）
**功能**：记录预警历史和处理状态
**核心字段**：
- `alert_type`：预警类型
- `alert_level`：预警级别
- `current_stock`：当前库存
- `status`：处理状态（PENDING、PROCESSING、RESOLVED、IGNORED）
- `suggested_action`：建议操作
- `suggested_quantity`：建议补货数量
- `suggested_supplier_id`：建议供应商

### 3. erp_inventory_alert_config（库存预警配置表）
**功能**：系统级预警配置
**核心字段**：
- `config_key`：配置键
- `config_value`：配置值
- `config_type`：配置类型（STRING、NUMBER、BOOLEAN、JSON）

## 三、库存预警业务逻辑设计

### 1. 预警触发条件

#### 低库存预警（LOW_STOCK）
- **触发条件**：当前库存 ≤ 最小库存阈值
- **预警级别**：
  - CRITICAL：当前库存 ≤ 最小库存 × 0.5
  - WARNING：当前库存 ≤ 最小库存
- **建议操作**：补货到最大库存的80%

#### 零库存预警（ZERO_STOCK）
- **触发条件**：当前库存 = 0
- **预警级别**：CRITICAL
- **建议操作**：紧急补货

#### 库存积压预警（OVERSTOCK）
- **触发条件**：当前库存 ≥ 最大库存 × 阈值百分比
- **预警级别**：INFO
- **建议操作**：促销或调配

#### 临期预警（EXPIRY）
- **触发条件**：距离过期天数 ≤ 设定天数
- **计算方式**：基于商品保质期和入库时间
- **预警级别**：WARNING
- **建议操作**：优先销售或处理

### 2. 预警级别分类

| 级别 | 英文 | 颜色 | 处理优先级 | 通知方式 |
|------|------|------|------------|----------|
| 紧急 | CRITICAL | 红色 | 最高 | 系统+邮件+短信 |
| 警告 | WARNING | 橙色 | 高 | 系统+邮件 |
| 提醒 | INFO | 蓝色 | 中 | 系统通知 |

### 3. 预警通知机制

#### 通知方式
- **SYSTEM**：系统内通知（消息中心）
- **EMAIL**：邮件通知
- **SMS**：短信通知
- **WECHAT**：微信通知

#### 通知策略
- 支持多种通知方式组合
- 可配置通知用户列表
- 支持通知频率控制（避免重复通知）

## 四、数据库索引优化

### 新增索引
1. **库存表优化索引**：
   ```sql
   INDEX `idx_stock_alert_check`(`current_stock`, `min_stock`, `max_stock`)
   INDEX `idx_last_in_out_time`(`last_in_time`, `last_out_time`)
   ```

2. **商品表保质期索引**：
   ```sql
   INDEX `idx_shelf_life_status`(`shelf_life`, `status`, `del_flag`)
   ```

3. **预警规则表索引**：
   ```sql
   INDEX `idx_check_frequency`(`check_frequency`, `last_check_time`)
   INDEX `idx_target_type_id`(`target_type`, `target_id`)
   ```

## 五、与现有ERP模块的集成方案

### 1. 库存模块集成
- **入库操作**：自动更新库存，触发预警检查
- **出库操作**：实时检查库存状态，生成预警
- **库存调整**：手动调整后重新评估预警状态

### 2. 采购模块集成
- **采购建议**：基于预警记录生成采购计划
- **供应商推荐**：根据商品供应商关系推荐补货供应商
- **采购单生成**：一键生成采购单

### 3. 销售模块集成
- **库存检查**：销售下单时检查库存状态
- **临期商品**：优先推荐临期商品销售
- **库存预留**：支持库存预留机制

### 4. 系统通知集成
- **消息中心**：集成到现有消息通知系统
- **工作台**：在工作台显示预警统计
- **报表模块**：生成预警分析报表

## 六、技术实现要点

### 1. 定时任务
- 使用Spring Boot的@Scheduled注解实现定时检查
- 支持分布式环境下的任务调度
- 可配置检查频率和批处理大小

### 2. 异步处理
- 预警检查采用异步处理，避免影响业务操作
- 通知发送采用消息队列异步处理
- 支持批量处理提高性能

### 3. 缓存策略
- 预警规则缓存，减少数据库查询
- 商品基础信息缓存
- 预警统计结果缓存

### 4. 扩展性设计
- 支持自定义预警规则
- 支持插件化通知方式
- 支持多租户隔离

## 七、前端界面设计

### 1. 预警规则管理
- 规则列表：展示所有预警规则
- 规则配置：创建和编辑预警规则
- 规则测试：测试规则是否正确

### 2. 预警记录管理
- 预警列表：展示所有预警记录
- 预警处理：标记预警处理状态
- 预警统计：预警数据统计分析

### 3. 库存监控看板
- 实时库存状态
- 预警统计图表
- 快速操作入口

### 4. 系统配置
- 通知配置：邮件、短信等通知参数
- 系统参数：预警相关系统参数
- 用户权限：预警功能权限管理
