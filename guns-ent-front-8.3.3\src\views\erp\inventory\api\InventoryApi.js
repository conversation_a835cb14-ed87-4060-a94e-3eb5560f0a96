import Request from '@/utils/request/request-util';

/**
 * 库存管理API
 *
 * <AUTHOR>
 * @since 2025/01/28 16:00
 */
export class InventoryApi {
  
  /**
   * 分页查询库存列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findPage(params) {
    return Request.postAndLoadData('/erp/inventory/page', params);
  }

  /**
   * 查询库存列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findList(params) {
    return Request.getAndLoadData('/erp/inventory/list', params);
  }

  /**
   * 查询库存详情
   * @param {Object} params 包含productId的参数
   * @returns {Promise}
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/inventory/detail', params);
  }

  /**
   * 查询预警库存列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static warningList(params) {
    return Request.getAndLoadData('/erp/inventory/warning', params);
  }

  /**
   * 查询缺货商品列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static outOfStockList(params) {
    return Request.getAndLoadData('/erp/inventory/outOfStock', params);
  }

  /**
   * 查询库存价值统计
   * @param {Object} params 统计参数
   * @returns {Promise}
   */
  static inventoryValue(params) {
    return Request.getAndLoadData('/erp/inventory/value', params);
  }

  /**
   * 设置库存预警值
   * @param {Object} params 设置参数
   * @returns {Promise}
   */
  static setMinStock(params) {
    return Request.post('/erp/inventory/setMinStock', params);
  }

  /**
   * 初始化商品库存
   * @param {Object} params 初始化参数
   * @returns {Promise}
   */
  static initInventory(params) {
    return Request.post('/erp/inventory/init', params);
  }

  /**
   * 手动调整库存
   * @param {Object} params 调整参数
   * @returns {Promise}
   */
  static adjustInventory(params) {
    return Request.post('/erp/inventory/adjust', params);
  }

  /**
   * 批量调整库存
   * @param {Object} params 批量调整参数
   * @returns {Promise}
   */
  static batchAdjustInventory(params) {
    return Request.post('/erp/inventory/batchAdjust', params);
  }

  /**
   * 设置库存预警阈值
   * @param {Object} params 预警设置参数
   * @returns {Promise}
   */
  static setInventoryAlert(params) {
    return Request.post('/erp/inventory/setAlert', params);
  }

  /**
   * 获取库存变动类型选项
   * @returns {Array}
   */
  static getChangeTypeOptions() {
    return [
      { label: '入库', value: 'INBOUND' },
      { label: '出库', value: 'OUTBOUND' },
      { label: '调整', value: 'ADJUSTMENT' },
      { label: '盘点', value: 'STOCKTAKING' },
      { label: '损耗', value: 'LOSS' },
      { label: '退货', value: 'RETURN' }
    ];
  }

  /**
   * 获取库存状态选项
   * @returns {Array}
   */
  static getInventoryStatusOptions() {
    return [
      { label: '正常', value: 'NORMAL' },
      { label: '预警', value: 'WARNING' },
      { label: '缺货', value: 'OUT_OF_STOCK' },
      { label: '滞销', value: 'SLOW_MOVING' },
      { label: '过期', value: 'EXPIRED' }
    ];
  }

  /**
   * 获取调整原因选项
   * @returns {Array}
   */
  static getAdjustReasonOptions() {
    return [
      { label: '盘点差异', value: 'STOCKTAKING_DIFF' },
      { label: '系统错误', value: 'SYSTEM_ERROR' },
      { label: '商品损坏', value: 'DAMAGED' },
      { label: '商品过期', value: 'EXPIRED' },
      { label: '其他原因', value: 'OTHER' }
    ];
  }

  /**
   * 获取库存变动类型名称
   * @param {String} changeType 变动类型
   * @returns {String}
   */
  static getChangeTypeName(changeType) {
    const options = InventoryApi.getChangeTypeOptions();
    const option = options.find(item => item.value === changeType);
    return option ? option.label : changeType;
  }

  /**
   * 获取库存状态名称
   * @param {String} status 库存状态
   * @returns {String}
   */
  static getInventoryStatusName(status) {
    const options = InventoryApi.getInventoryStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? option.label : status;
  }

  /**
   * 获取调整原因名称
   * @param {String} reason 调整原因
   * @returns {String}
   */
  static getAdjustReasonName(reason) {
    const options = InventoryApi.getAdjustReasonOptions();
    const option = options.find(item => item.value === reason);
    return option ? option.label : reason;
  }

  /**
   * 获取库存状态标签颜色
   * @param {String} status 库存状态
   * @returns {String}
   */
  static getInventoryStatusTagColor(status) {
    switch (status) {
      case 'NORMAL':
        return 'green';
      case 'WARNING':
        return 'orange';
      case 'OUT_OF_STOCK':
        return 'red';
      case 'SLOW_MOVING':
        return 'blue';
      case 'EXPIRED':
        return 'red';
      default:
        return 'default';
    }
  }

  /**
   * 获取变动类型标签颜色
   * @param {String} changeType 变动类型
   * @returns {String}
   */
  static getChangeTypeTagColor(changeType) {
    switch (changeType) {
      case 'INBOUND':
        return 'green';
      case 'OUTBOUND':
        return 'blue';
      case 'ADJUSTMENT':
        return 'orange';
      case 'STOCKTAKING':
        return 'purple';
      case 'LOSS':
        return 'red';
      case 'RETURN':
        return 'cyan';
      default:
        return 'default';
    }
  }

  /**
   * 格式化库存数量显示
   * @param {Number} quantity 数量
   * @param {String} unit 单位
   * @returns {String}
   */
  static formatQuantity(quantity, unit) {
    if (quantity === null || quantity === undefined) return '-';
    return `${quantity}${unit || ''}`;
  }

  /**
   * 格式化库存价值显示
   * @param {Number} value 价值
   * @returns {String}
   */
  static formatValue(value) {
    if (!value) return '¥0.00';
    return `¥${value.toFixed(2)}`;
  }

  /**
   * 判断库存是否预警
   * @param {Number} currentStock 当前库存
   * @param {Number} minStock 最小库存
   * @returns {Boolean}
   */
  static isStockWarning(currentStock, minStock) {
    return currentStock <= minStock;
  }

  /**
   * 判断库存是否缺货
   * @param {Number} currentStock 当前库存
   * @returns {Boolean}
   */
  static isOutOfStock(currentStock) {
    return currentStock <= 0;
  }

  /**
   * 计算库存周转率
   * @param {Number} salesQuantity 销售数量
   * @param {Number} avgStock 平均库存
   * @returns {Number}
   */
  static calculateTurnoverRate(salesQuantity, avgStock) {
    if (!avgStock || avgStock === 0) return 0;
    return (salesQuantity / avgStock).toFixed(2);
  }

  /**
   * 导出库存报表
   * @param {Object} params 导出参数
   * @returns {void}
   */
  static exportInventory(params) {
    return Request.downLoad('/erp/inventory/export', params);
  }

  /**
   * 导出库存预警报表
   * @param {Object} params 导出参数
   * @returns {void}
   */
  static exportInventoryAlerts(params) {
    return Request.downLoad('/erp/inventory/exportAlerts', params);
  }

  /**
   * 导出库存变动报表
   * @param {Object} params 导出参数
   * @returns {void}
   */
  static exportInventoryChanges(params) {
    return Request.downLoad('/erp/inventory/exportChanges', params);
  }

}