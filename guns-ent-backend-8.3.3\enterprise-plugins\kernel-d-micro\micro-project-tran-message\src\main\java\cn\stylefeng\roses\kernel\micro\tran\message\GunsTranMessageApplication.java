package cn.stylefeng.roses.kernel.micro.tran.message;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * feign示例服务提供者
 *
 * <AUTHOR>
 * @date 2021/5/12 19:42
 */
@SpringBootApplication(scanBasePackages = "cn.stylefeng")
@EnableFeignClients(basePackages = {"cn.stylefeng.roses.kernel.micro.tran.message.modular.consumer", "cn.stylefeng.roses.kernel.micro.core.auth.consumer"})
@EnableDiscoveryClient
@EnableScheduling
public class GunsTranMessageApplication {

    public static void main(String[] args) {
        SpringApplication.run(GunsTranMessageApplication.class, args);
    }

}

