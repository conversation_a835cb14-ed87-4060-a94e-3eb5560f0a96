package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.erp.api.constants.ErpConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 区域模块异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
@Getter
public enum ErpRegionExceptionEnum implements AbstractExceptionEnum {

    /**
     * 区域不存在
     */
    REGION_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "61", "区域不存在"),

    /**
     * 区域编码重复
     */
    REGION_CODE_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "62", "区域编码重复"),

    /**
     * 区域名称重复
     */
    REGION_NAME_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "63", "区域名称重复"),

    /**
     * 区域状态不正确
     */
    REGION_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "64", "区域状态不正确"),

    /**
     * 区域层级不正确
     */
    REGION_LEVEL_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "65", "区域层级不正确"),

    /**
     * 父级区域不存在
     */
    PARENT_REGION_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "66", "父级区域不存在"),

    /**
     * 不能选择自己作为父级区域
     */
    CANNOT_SELECT_SELF_AS_PARENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "67", "不能选择自己作为父级区域"),

    /**
     * 不能选择子级区域作为父级区域
     */
    CANNOT_SELECT_CHILD_AS_PARENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "68", "不能选择子级区域作为父级区域"),

    /**
     * 区域层级超过最大深度
     */
    REGION_LEVEL_EXCEED_MAX_DEPTH(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "69", "区域层级超过最大深度"),

    /**
     * 区域存在子级，无法删除
     */
    REGION_HAS_CHILDREN_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "70", "区域存在子级，无法删除"),

    /**
     * 区域存在关联业务数据，无法删除
     */
    REGION_HAS_BUSINESS_DATA_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "71", "区域存在关联业务数据，无法删除"),

    /**
     * 区域存在关联业务数据，无法停用
     */
    REGION_HAS_BUSINESS_DATA_CANNOT_DISABLE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "72", "区域存在关联业务数据，无法停用"),

    /**
     * 区域ID列表为空
     */
    REGION_ID_LIST_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "73", "区域ID列表不能为空"),

    /**
     * 区域存在子区域，不能删除
     */
    REGION_HAS_CHILDREN(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "74", "区域\"{}\"存在子区域，不能删除");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ErpRegionExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
