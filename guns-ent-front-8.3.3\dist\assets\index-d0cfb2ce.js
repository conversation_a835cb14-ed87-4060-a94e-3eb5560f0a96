import{_ as N,aP as Y,aQ as j,r as u,o as F,X as H,k as $,a as c,c as y,b as r,ah as p,aF as z,h as E,f as L,a2 as I}from"./index-18a1ea24.js";const U=Y({name:"GunsSplitLayout",props:{width:{type:String,default:"252px"},minSize:Number,maxSize:Number,space:{type:String,default:"0px"},collapse:Boolean,allowCollapse:{type:Boolean,default:!0},leftStyle:{type:Object,default:{height:"100%"}},rightStyle:{type:Object,default:{height:"100%"}},collapseBtnStyle:Object,vertical:Boolean,reverse:Boolean,resizable:{type:Boolean,default:!0},responsive:{type:Boolean,default:null},cacheKey:String},emits:{"update:collapse":e=>!0},setup(e,{emit:s}){const S=j(e),m=u(null),C=u(null),v=u(null),t=u(!1),o=u(null),i=u(!1);F(()=>{if(e.cacheKey){const l=localStorage.getItem(n(e.cacheKey));o.value=l}});const n=l=>"".concat(l,"Coll-Width"),M=l=>{t.value=typeof l=="boolean"?l:!t.value,s("update:collapse",t.value)},O=l=>{const a=e.vertical?l.clientHeight:l.clientWidth;return e.maxSize?e.maxSize<0?a+e.maxSize:e.maxSize<1?Math.floor(a*e.maxSize):Math.min(e.maxSize,a):a},K=l=>{const a=m.value,f=v.value;if(!a||!f)return;i.value=!0;const h=l.clientX,w=l.clientY,W=f.clientWidth,X=f.clientHeight,B=e.minSize||0,R=O(a),b=d=>{const g=e.vertical?(e.reverse?w-d.clientY:d.clientY-w)+X:(e.reverse?h-d.clientX:d.clientX-h)+W;o.value=(g<B?B:g>R?R:g)+"px",e.cacheKey&&localStorage.setItem(n(e.cacheKey),o.value)},k=()=>{i.value=!1,document.removeEventListener("mousemove",b),document.removeEventListener("mouseup",k)};document.addEventListener("mousemove",b),document.addEventListener("mouseup",k)};return H([()=>e.collapse,()=>e.allowCollapse],()=>{e.allowCollapse?t.value=e.collapse:t.value=!1},{immediate:!0}),{rootRef:m,wrapRef:C,sideRef:v,isResponsive:S,isCollapse:t,resizedSize:o,resizing:i,toggleCollapse:M,onResize:K,getCollWidthCacheKey:n}}}),V={ref:"wrapRef",class:"guns-split-panel-wrap"},G={class:"guns-split-panel-space"};function P(e,s,S,m,C,v){var i;const t=$("CaretUpOutlined"),o=$("CaretLeftOutlined");return c(),y("div",{ref:"rootRef",class:I(["guns-split-panel",{"is-reverse":e.reverse},{"is-vertical":e.vertical},{"is-collapse":e.isCollapse},{"is-resizing":e.resizing},{"is-responsive":e.isResponsive}]),style:p({"--guns-split-size":(i=e.resizedSize)!=null?i:e.width,"--guns-split-space":e.space})},[r("div",V,[r("div",{ref:"sideRef",class:"guns-split-panel-side",style:p(e.leftStyle)},[z(e.$slots,"default")],4),r("div",G,[e.resizable?(c(),y("div",{key:0,class:"guns-split-resize-line",onMousedown:s[0]||(s[0]=(...n)=>e.onResize&&e.onResize(...n))},null,32)):E("",!0)])],512),r("div",{class:"guns-split-panel-body",style:p(e.rightStyle)},[z(e.$slots,"content",{collapse:e.isCollapse})],4),e.allowCollapse?(c(),y("div",{key:0,style:p(e.collapseBtnStyle),class:"guns-split-collapse-button",onClick:s[1]||(s[1]=n=>e.toggleCollapse())},[z(e.$slots,"collapse",{collapse:e.isCollapse},()=>[e.vertical?(c(),L(t,{key:0,class:"guns-split-collapse-icon"})):(c(),L(o,{key:1,class:"guns-split-collapse-icon"}))])],4)):E("",!0),r("div",{class:"guns-split-panel-mask",onClick:s[2]||(s[2]=n=>e.toggleCollapse())})],6)}const _=N(U,[["render",P]]);export{_};
