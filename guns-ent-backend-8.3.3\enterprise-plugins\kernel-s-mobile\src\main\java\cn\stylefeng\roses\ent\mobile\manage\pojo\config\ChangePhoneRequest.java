package cn.stylefeng.roses.ent.mobile.manage.pojo.config;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 更换手机的请求
 *
 * <AUTHOR>
 * @since 2024/3/24 23:34
 */
@Data
public class ChangePhoneRequest {

    /**
     * 新手机号
     */
    @ChineseDescription("新手机号")
    @NotBlank(message = "新手机号不能为空")
    private String phone;

    /**
     * 手机验证码
     */
    @ChineseDescription("手机验证码")
    @NotBlank(message = "手机验证码不能为空")
    private String phoneValidateCode;

}
