# 基于seata解决分布式事务

项目架构图如下：

![](./.README_images/4300e21d.png)

business微服务会调用storage和order服务，order服务会调用account

在调用business接口时，order和account会随机产生异常，从而模拟分布式事务storage回滚的情景。

## Demo运行

### 1.下载seata中间件启动

从seata官网下载seata，推荐使用1.3.0版本：

[https://seata.io/zh-cn/blog/download.html](https://seata.io/zh-cn/blog/download.html)

启动脚本如下：

```shell
# 启动seata
seata-server.bat -p 8091 -m file
```

### 2.在数据库中新建三个库

分别建立cloud_account，cloud_order，cloud_storage这三个库，不用运行初始化sql

### 3.修改account，order，storage的数据库连接信息

在application-local.yml中修改datasource配置

### 4.分别启动account，business，order和storage4个服务

程序启动时会自动连接数据库初始化相关sql

### 5.调用business服务接口，测试分布式事务

```shell
# 访问如下接口
http://localhost:18081/seata/rest
```

将会看到最终结果，当请求失败时，之前执行的事务会回滚，数据库的库存和订单和用户的余额数据会保持准确性。

