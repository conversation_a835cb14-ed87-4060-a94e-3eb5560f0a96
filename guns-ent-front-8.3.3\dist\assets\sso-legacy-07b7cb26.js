System.register(["./index-legacy-ee1db0c7.js","./sso-util-legacy-cfa203a0.js"],(function(e,t){"use strict";var r,s,n,c,a,u,o,l,m,f;return{setters:[e=>{r=e._,s=e.aP,n=e.m,c=e.bZ,a=e.b_,u=e.a,o=e.f,l=e.bY,m=e.S},e=>{f=e.S}],execute:function(){const t=s({name:"Sso",setup(){const e=f.getUrlParam("token");f.getUrlParam("callback");const t=f.getUrlParam("from"),r=()=>{l.query&&l.query.from?l.push(String(l.query.from)):l.push("/").catch((()=>{}))};t&&"oauth2"===t?(n.success("登录成功"),c(e,!0),a(),r()):f.tokenExchange(e).then((e=>{n.success("登录成功"),c(e.token,!0),a(),r()}))}});e("default",r(t,[["render",function(e,t,r,s,n,c){const a=m;return u(),o(a)}]]))}}}));
