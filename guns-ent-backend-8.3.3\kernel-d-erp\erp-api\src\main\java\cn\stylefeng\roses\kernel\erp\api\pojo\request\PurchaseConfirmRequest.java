package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * 采购入库单确认请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseConfirmRequest extends BaseRequest {

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空")
    @ChineseDescription("入库单ID")
    private Long id;

    /**
     * 确认备注
     */
    @ChineseDescription("确认备注")
    private String confirmRemark;

}