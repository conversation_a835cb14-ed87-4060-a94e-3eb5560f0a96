package cn.stylefeng.roses.kernel.pay.mapper;

import cn.stylefeng.roses.kernel.pay.api.entity.UserExpiry;
import cn.stylefeng.roses.kernel.pay.pojo.request.UserExpiryRequest;
import cn.stylefeng.roses.kernel.pay.pojo.response.UserExpiryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户商品到期时间 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
public interface UserExpiryMapper extends BaseMapper<UserExpiry> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    List<UserExpiryVo> customFindList(@Param("page") Page page, @Param("param")UserExpiryRequest request);

}
