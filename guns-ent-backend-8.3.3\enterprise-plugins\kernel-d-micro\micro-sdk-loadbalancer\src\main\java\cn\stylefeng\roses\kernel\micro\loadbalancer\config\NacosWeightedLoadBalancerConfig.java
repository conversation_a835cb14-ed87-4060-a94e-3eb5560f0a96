package cn.stylefeng.roses.kernel.micro.loadbalancer.config;

import cn.stylefeng.roses.kernel.micro.loadbalancer.NacosWeightedLoadBalancer;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.core.ReactorLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;

/**
 * 通过nacos权重进行路由的配置
 *
 * <AUTHOR>
 * @date 2021/5/15 14:38
 */
@LoadBalancerClients(defaultConfiguration = NacosWeightedLoadBalancerConfig.class)
public class NacosWeightedLoadBalancerConfig {

    /**
     * 基于nacos权重的客户端负载均衡路由
     *
     * <AUTHOR>
     * @date 2021/5/15 14:49
     */
    @Bean
    public ReactorLoadBalancer<ServiceInstance> reactorServiceInstanceLoadBalancer(Environment environment, LoadBalancerClientFactory loadBalancerClientFactory) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        return new NacosWeightedLoadBalancer(loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
    }

}