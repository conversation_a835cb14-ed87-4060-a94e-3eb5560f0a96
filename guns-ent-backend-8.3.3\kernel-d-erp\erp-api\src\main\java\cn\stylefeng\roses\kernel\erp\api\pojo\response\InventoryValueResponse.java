package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存价值统计响应参数
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryValueResponse extends BaseResponse {

    /**
     * 总库存价值
     */
    @ChineseDescription("总库存价值")
    private BigDecimal totalInventoryValue;

    /**
     * 总库存数量
     */
    @ChineseDescription("总库存数量")
    private BigDecimal totalInventoryQuantity;

    /**
     * 商品种类数量
     */
    @ChineseDescription("商品种类数量")
    private Integer productCount;

    /**
     * 预警商品数量
     */
    @ChineseDescription("预警商品数量")
    private Integer warningProductCount;

    /**
     * 缺货商品数量
     */
    @ChineseDescription("缺货商品数量")
    private Integer outOfStockProductCount;

    /**
     * 按供应商统计的库存价值
     */
    @ChineseDescription("按供应商统计的库存价值")
    private List<SupplierInventoryValue> supplierInventoryValues;

    /**
     * 按商品分类统计的库存价值
     */
    @ChineseDescription("按商品分类统计的库存价值")
    private List<CategoryInventoryValue> categoryInventoryValues;

    /**
     * 按经营方式统计的库存价值
     */
    @ChineseDescription("按经营方式统计的库存价值")
    private List<BusinessModeInventoryValue> businessModeInventoryValues;

    /**
     * 供应商库存价值统计
     */
    @Data
    public static class SupplierInventoryValue {
        /**
         * 供应商ID
         */
        @ChineseDescription("供应商ID")
        private Long supplierId;

        /**
         * 供应商编码
         */
        @ChineseDescription("供应商编码")
        private String supplierCode;

        /**
         * 供应商名称
         */
        @ChineseDescription("供应商名称")
        private String supplierName;

        /**
         * 经营方式
         */
        @ChineseDescription("经营方式")
        private String businessMode;

        /**
         * 库存价值
         */
        @ChineseDescription("库存价值")
        private BigDecimal inventoryValue;

        /**
         * 库存数量
         */
        @ChineseDescription("库存数量")
        private BigDecimal inventoryQuantity;

        /**
         * 商品种类数量
         */
        @ChineseDescription("商品种类数量")
        private Integer productCount;
    }

    /**
     * 商品分类库存价值统计
     */
    @Data
    public static class CategoryInventoryValue {
        /**
         * 分类ID
         */
        @ChineseDescription("分类ID")
        private Long categoryId;

        /**
         * 分类名称
         */
        @ChineseDescription("分类名称")
        private String categoryName;

        /**
         * 库存价值
         */
        @ChineseDescription("库存价值")
        private BigDecimal inventoryValue;

        /**
         * 库存数量
         */
        @ChineseDescription("库存数量")
        private BigDecimal inventoryQuantity;

        /**
         * 商品种类数量
         */
        @ChineseDescription("商品种类数量")
        private Integer productCount;
    }

    /**
     * 经营方式库存价值统计
     */
    @Data
    public static class BusinessModeInventoryValue {
        /**
         * 经营方式
         */
        @ChineseDescription("经营方式")
        private String businessMode;

        /**
         * 经营方式名称
         */
        @ChineseDescription("经营方式名称")
        private String businessModeName;

        /**
         * 库存价值
         */
        @ChineseDescription("库存价值")
        private BigDecimal inventoryValue;

        /**
         * 库存数量
         */
        @ChineseDescription("库存数量")
        private BigDecimal inventoryQuantity;

        /**
         * 商品种类数量
         */
        @ChineseDescription("商品种类数量")
        private Integer productCount;
    }

}