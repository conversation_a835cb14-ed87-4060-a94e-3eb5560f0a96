package cn.stylefeng.roses.kernel.sys.modular.theme.constants;

/**
 * 主题相关的常量
 *
 * <AUTHOR>
 * @since 2023/6/18 9:29
 */
public interface ThemeConstants {

    /**
     * 系统主题的缓存
     */
    String SYSTEM_THEME_CACHE_PREFIX = "system_cache";

    /**
     * 系统内置主题模板的编码
     */
    String THEME_GUNS_PLATFORM = "GUNS_PLATFORM";

    /**
     * 主题编码相关的系统变量前缀
     */
    String THEME_CODE_SYSTEM_PREFIX = "GUNS";

    /**
     * 主题管理界面的权限标识
     */
    String THEME_MANAGER = "THEME_MANAGER";

}
