package cn.stylefeng.roses.kernel.erp.modular.sequence.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.SequenceGeneratorExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.SequenceGenerator;
import cn.stylefeng.roses.kernel.erp.modular.sequence.mapper.SequenceGeneratorMapper;
import cn.stylefeng.roses.kernel.erp.modular.sequence.service.SequenceGeneratorService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 序列号生成器Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
@Slf4j
@Service
public class SequenceGeneratorServiceImpl extends ServiceImpl<SequenceGeneratorMapper, SequenceGenerator> implements SequenceGeneratorService {

    @Resource
    private SequenceGeneratorMapper sequenceGeneratorMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateNextSequence(String sequenceName) {
        if (StrUtil.isBlank(sequenceName)) {
            throw new ServiceException(SequenceGeneratorExceptionEnum.SEQUENCE_NAME_EMPTY);
        }

        // 获取序列配置
        SequenceGenerator sequenceGenerator = sequenceGeneratorMapper.getBySequenceName(sequenceName);
        if (ObjectUtil.isEmpty(sequenceGenerator)) {
            throw new ServiceException(SequenceGeneratorExceptionEnum.SEQUENCE_NOT_EXIST);
        }

        // 检查是否需要重置
        checkAndResetSequence(sequenceName);

        // 获取下一个值
        sequenceGeneratorMapper.getNextValue(sequenceName);

        // 重新查询获取最新值
        sequenceGenerator = sequenceGeneratorMapper.getBySequenceName(sequenceName);

        // 生成序列号
        String prefix = StrUtil.isBlank(sequenceGenerator.getPrefix()) ? "" : sequenceGenerator.getPrefix();
        String sequence = String.format("%04d", sequenceGenerator.getCurrentValue());

        return prefix + sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateDateSequence(String sequenceName) {
        if (StrUtil.isBlank(sequenceName)) {
            throw new ServiceException(SequenceGeneratorExceptionEnum.SEQUENCE_NAME_EMPTY);
        }

        // 获取序列配置
        SequenceGenerator sequenceGenerator = sequenceGeneratorMapper.getBySequenceName(sequenceName);
        if (ObjectUtil.isEmpty(sequenceGenerator)) {
            throw new ServiceException(SequenceGeneratorExceptionEnum.SEQUENCE_NOT_EXIST);
        }

        // 检查是否需要重置
        checkAndResetSequence(sequenceName);

        // 获取下一个值
        sequenceGeneratorMapper.getNextValue(sequenceName);

        // 重新查询获取最新值
        sequenceGenerator = sequenceGeneratorMapper.getBySequenceName(sequenceName);

        // 生成序列号
        String prefix = StrUtil.isBlank(sequenceGenerator.getPrefix()) ? "" : sequenceGenerator.getPrefix();
        String dateStr = "";
        if (StrUtil.isNotBlank(sequenceGenerator.getDateFormat())) {
            dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern(sequenceGenerator.getDateFormat()));
        }
        String sequence = String.format("%04d", sequenceGenerator.getCurrentValue());

        return prefix + dateStr + sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndResetSequence(String sequenceName) {
        SequenceGenerator sequenceGenerator = sequenceGeneratorMapper.getBySequenceName(sequenceName);
        if (ObjectUtil.isEmpty(sequenceGenerator)) {
            return;
        }

        String resetType = sequenceGenerator.getResetType();
        LocalDate lastResetDate = sequenceGenerator.getLastResetDate();
        LocalDate currentDate = LocalDate.now();

        boolean needReset = false;

        switch (resetType) {
            case "DAILY":
                needReset = lastResetDate == null || !lastResetDate.equals(currentDate);
                break;
            case "MONTHLY":
                needReset = lastResetDate == null || 
                           lastResetDate.getYear() != currentDate.getYear() || 
                           lastResetDate.getMonthValue() != currentDate.getMonthValue();
                break;
            case "YEARLY":
                needReset = lastResetDate == null || lastResetDate.getYear() != currentDate.getYear();
                break;
            default:
                // NONE 不重置
                break;
        }

        if (needReset) {
            resetSequence(sequenceName, 0L);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetSequence(String sequenceName, Long resetValue) {
        String currentDate = LocalDate.now().toString();
        sequenceGeneratorMapper.resetSequence(sequenceName, resetValue, currentDate);
    }

    @Override
    public Long getCurrentValue(String sequenceName) {
        SequenceGenerator sequenceGenerator = sequenceGeneratorMapper.getBySequenceName(sequenceName);
        if (ObjectUtil.isEmpty(sequenceGenerator)) {
            throw new ServiceException(SequenceGeneratorExceptionEnum.SEQUENCE_NOT_EXIST);
        }
        return sequenceGenerator.getCurrentValue();
    }

}
