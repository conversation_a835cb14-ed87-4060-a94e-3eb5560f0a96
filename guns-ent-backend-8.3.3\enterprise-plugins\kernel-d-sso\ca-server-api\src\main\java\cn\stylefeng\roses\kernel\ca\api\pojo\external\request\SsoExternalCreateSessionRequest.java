package cn.stylefeng.roses.kernel.ca.api.pojo.external.request;

import cn.stylefeng.roses.kernel.ca.api.pojo.external.BaseSsoExternalApiRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 远程创建sso会话的请求
 *
 * <AUTHOR>
 * @date 2021/2/20 17:17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SsoExternalCreateSessionRequest extends BaseSsoExternalApiRequest {

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;

}
