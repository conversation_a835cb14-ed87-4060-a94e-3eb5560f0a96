package cn.stylefeng.roses.kernel.erp.modular.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.PurchaseOrderConstants;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpSupplierConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.PurchaseOrderExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PurchaseOrder;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PurchaseOrderDetail;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseConfirmRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseReceiveRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderDetailRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderDetailResponse;
import cn.stylefeng.roses.kernel.erp.modular.purchase.mapper.PurchaseOrderMapper;
import cn.stylefeng.roses.kernel.erp.modular.purchase.mapper.PurchaseOrderDetailMapper;
import cn.stylefeng.roses.kernel.erp.modular.purchase.service.PurchaseOrderService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.mapper.ErpSupplierMapper;
import cn.stylefeng.roses.kernel.erp.modular.product.mapper.ErpProductMapper;
import cn.stylefeng.roses.kernel.erp.modular.sequence.service.SequenceGeneratorService;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryService;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryOperationRequest;
import cn.stylefeng.roses.kernel.erp.api.constants.InventoryConstants;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购入库单Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@Slf4j
@Service
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> implements PurchaseOrderService {

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    private ErpSupplierMapper erpSupplierMapper;

    @Resource
    private ErpProductMapper erpProductMapper;

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Resource
    private InventoryService inventoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(PurchaseOrderRequest purchaseOrderRequest) {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        BeanUtil.copyProperties(purchaseOrderRequest, purchaseOrder);

        // 校验供应商是否存在
        ErpSupplier supplier = erpSupplierMapper.selectById(purchaseOrder.getSupplierId());
        if (ObjectUtil.isEmpty(supplier)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.SUPPLIER_NOT_EXIST);
        }

        // 校验供应商经营方式是否允许采购
        if (!this.validateSupplierBusinessMode(purchaseOrder.getSupplierId())) {
            throw new ServiceException(PurchaseOrderExceptionEnum.JOINT_VENTURE_SUPPLIER_CANNOT_PURCHASE);
        }

        // 生成入库单号
        if (StrUtil.isBlank(purchaseOrder.getOrderNo())) {
            purchaseOrder.setOrderNo(this.generateOrderNo());
        }

        // 设置默认状态
        if (StrUtil.isBlank(purchaseOrder.getStatus())) {
            purchaseOrder.setStatus(PurchaseOrderConstants.DEFAULT_PURCHASE_ORDER_STATUS);
        }

        // 设置默认付款方式
        if (StrUtil.isBlank(purchaseOrder.getPaymentMethod())) {
            purchaseOrder.setPaymentMethod(PurchaseOrderConstants.DEFAULT_PAYMENT_METHOD);
        }

        // 校验并计算总金额
        BigDecimal totalAmount = this.calculateTotalAmount(purchaseOrderRequest.getDetailList());
        purchaseOrder.setTotalAmount(totalAmount);

        // 保存主表
        this.save(purchaseOrder);

        // 保存明细
        this.saveOrderDetails(purchaseOrder.getId(), purchaseOrderRequest.getDetailList());

        return purchaseOrder.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(PurchaseOrderRequest purchaseOrderRequest) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderRequest.getId());
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_NOT_EXIST);
        }

        // 校验状态，只有草稿状态才能删除
        if (!PurchaseOrderConstants.PURCHASE_ORDER_STATUS_DRAFT.equals(purchaseOrder.getStatus())) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_CONFIRMED_CANNOT_MODIFY);
        }

        // 删除明细
        purchaseOrderDetailMapper.deleteByOrderId(purchaseOrder.getId());

        // 删除主表
        this.removeById(purchaseOrder.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(PurchaseOrderRequest purchaseOrderRequest) {
        if (CollUtil.isEmpty(purchaseOrderRequest.getIdList())) {
            return;
        }

        for (Long id : purchaseOrderRequest.getIdList()) {
            PurchaseOrderRequest deleteRequest = new PurchaseOrderRequest();
            deleteRequest.setId(id);
            this.del(deleteRequest);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(PurchaseOrderRequest purchaseOrderRequest) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderRequest.getId());
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_NOT_EXIST);
        }

        // 校验状态，只有草稿状态才能编辑
        if (!PurchaseOrderConstants.PURCHASE_ORDER_STATUS_DRAFT.equals(purchaseOrder.getStatus())) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_CONFIRMED_CANNOT_MODIFY);
        }

        // 更新主表信息
        BeanUtil.copyProperties(purchaseOrderRequest, purchaseOrder);

        // 校验供应商是否存在
        ErpSupplier supplier = erpSupplierMapper.selectById(purchaseOrder.getSupplierId());
        if (ObjectUtil.isEmpty(supplier)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.SUPPLIER_NOT_EXIST);
        }

        // 校验供应商经营方式是否允许采购
        if (!this.validateSupplierBusinessMode(purchaseOrder.getSupplierId())) {
            throw new ServiceException(PurchaseOrderExceptionEnum.JOINT_VENTURE_SUPPLIER_CANNOT_PURCHASE);
        }

        // 重新计算总金额
        BigDecimal totalAmount = this.calculateTotalAmount(purchaseOrderRequest.getDetailList());
        purchaseOrder.setTotalAmount(totalAmount);

        // 更新主表
        this.updateById(purchaseOrder);

        // 删除原有明细
        purchaseOrderDetailMapper.deleteByOrderId(purchaseOrder.getId());

        // 保存新明细
        this.saveOrderDetails(purchaseOrder.getId(), purchaseOrderRequest.getDetailList());
    }

    @Override
    public PurchaseOrderResponse detail(PurchaseOrderRequest purchaseOrderRequest) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderRequest.getId());
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_NOT_EXIST);
        }

        PurchaseOrderResponse response = new PurchaseOrderResponse();
        BeanUtil.copyProperties(purchaseOrder, response);

        // 填充供应商信息
        this.fillSupplierInfo(response, purchaseOrder.getSupplierId());

        // 填充明细信息
        this.fillOrderDetails(response, purchaseOrder.getId());

        // 填充状态名称等
        this.fillDisplayNames(response);

        return response;
    }

    /**
     * 校验供应商经营方式是否允许采购
     */
    @Override
    public boolean validateSupplierBusinessMode(Long supplierId) {
        ErpSupplier supplier = erpSupplierMapper.selectById(supplierId);
        if (ObjectUtil.isEmpty(supplier)) {
            return false;
        }

        // 联营供应商不允许采购
        return !ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE.equals(supplier.getBusinessMode());
    }

    /**
     * 生成采购入库单号
     */
    @Override
    public String generateOrderNo() {
        return sequenceGeneratorService.generateDateSequence("PURCHASE_ORDER");
    }

    /**
     * 校验采购入库单编码是否重复
     */
    @Override
    public boolean validateOrderNoRepeat(String orderNo, Long id) {
        LambdaQueryWrapper<PurchaseOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseOrder::getOrderNo, orderNo);
        if (ObjectUtil.isNotEmpty(id)) {
            queryWrapper.ne(PurchaseOrder::getId, id);
        }
        return this.count(queryWrapper) > 0;
    }

    /**
     * 计算总金额
     */
    private BigDecimal calculateTotalAmount(List<PurchaseOrderDetailRequest> detailList) {
        if (CollUtil.isEmpty(detailList)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_DETAIL_LIST_EMPTY);
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        for (PurchaseOrderDetailRequest detail : detailList) {
            // 校验商品是否存在
            ErpProduct product = erpProductMapper.selectById(detail.getProductId());
            if (ObjectUtil.isEmpty(product)) {
                throw new ServiceException(PurchaseOrderExceptionEnum.PRODUCT_NOT_EXIST);
            }

            // 校验数量和单价
            if (detail.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException(PurchaseOrderExceptionEnum.PRODUCT_QUANTITY_MUST_GREATER_THAN_ZERO);
            }
            if (detail.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException(PurchaseOrderExceptionEnum.PRODUCT_UNIT_PRICE_MUST_GREATER_THAN_ZERO);
            }

            // 计算明细总价
            BigDecimal detailTotal = detail.getQuantity().multiply(detail.getUnitPrice());
            detail.setTotalPrice(detailTotal);
            totalAmount = totalAmount.add(detailTotal);
        }

        return totalAmount;
    }

    /**
     * 保存订单明细
     */
    private void saveOrderDetails(Long orderId, List<PurchaseOrderDetailRequest> detailList) {
        if (CollUtil.isEmpty(detailList)) {
            return;
        }

        for (PurchaseOrderDetailRequest detailRequest : detailList) {
            PurchaseOrderDetail detail = new PurchaseOrderDetail();
            BeanUtil.copyProperties(detailRequest, detail);
            detail.setOrderId(orderId);
            purchaseOrderDetailMapper.insert(detail);
        }
    }

    /**
     * 填充供应商信息
     */
    private void fillSupplierInfo(PurchaseOrderResponse response, Long supplierId) {
        ErpSupplier supplier = erpSupplierMapper.selectById(supplierId);
        if (ObjectUtil.isNotEmpty(supplier)) {
            response.setSupplierCode(supplier.getSupplierCode());
            response.setSupplierName(supplier.getSupplierName());
            response.setBusinessMode(supplier.getBusinessMode());
        }
    }

    /**
     * 填充订单明细信息
     */
    private void fillOrderDetails(PurchaseOrderResponse response, Long orderId) {
        List<PurchaseOrderDetail> detailList = purchaseOrderDetailMapper.getByOrderId(orderId);
        if (CollUtil.isNotEmpty(detailList)) {
            List<PurchaseOrderDetailResponse> detailResponseList = detailList.stream()
                    .map(this::convertToDetailResponse)
                    .collect(Collectors.toList());
            response.setDetailList(detailResponseList);
            response.setDetailCount(detailResponseList.size());
            response.setProductCount(detailResponseList.size());
            
            // 计算总数量
            BigDecimal totalQuantity = detailList.stream()
                    .map(PurchaseOrderDetail::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setTotalQuantity(totalQuantity);
        }
    }

    /**
     * 转换明细实体为响应对象
     */
    private PurchaseOrderDetailResponse convertToDetailResponse(PurchaseOrderDetail detail) {
        PurchaseOrderDetailResponse response = new PurchaseOrderDetailResponse();
        BeanUtil.copyProperties(detail, response);

        // 填充商品信息
        ErpProduct product = erpProductMapper.selectById(detail.getProductId());
        if (ObjectUtil.isNotEmpty(product)) {
            response.setProductCode(product.getProductCode());
            response.setProductName(product.getProductName());
            response.setProductSpecification(product.getSpecification());
            response.setProductUnit(product.getUnit());
            response.setPricingType(product.getPricingType());
        }

        return response;
    }

    /**
     * 填充显示名称
     */
    private void fillDisplayNames(PurchaseOrderResponse response) {
        // 填充状态名称
        if (StrUtil.isNotBlank(response.getStatus())) {
            switch (response.getStatus()) {
                case PurchaseOrderConstants.PURCHASE_ORDER_STATUS_DRAFT:
                    response.setStatusName("草稿");
                    break;
                case PurchaseOrderConstants.PURCHASE_ORDER_STATUS_CONFIRMED:
                    response.setStatusName("已确认");
                    break;
                case PurchaseOrderConstants.PURCHASE_ORDER_STATUS_COMPLETED:
                    response.setStatusName("已完成");
                    break;
                default:
                    response.setStatusName("未知");
                    break;
            }
        }

        // 填充经营方式名称
        if (StrUtil.isNotBlank(response.getBusinessMode())) {
            switch (response.getBusinessMode()) {
                case ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE:
                    response.setBusinessModeName("购销");
                    break;
                case ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE:
                    response.setBusinessModeName("联营");
                    break;
                case ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT:
                    response.setBusinessModeName("代销");
                    break;
                default:
                    response.setBusinessModeName("未知");
                    break;
            }
        }

        // 填充付款方式名称
        if (StrUtil.isNotBlank(response.getPaymentMethod())) {
            switch (response.getPaymentMethod()) {
                case PurchaseOrderConstants.PAYMENT_METHOD_CASH:
                    response.setPaymentMethodName("现金");
                    break;
                case PurchaseOrderConstants.PAYMENT_METHOD_BANK_TRANSFER:
                    response.setPaymentMethodName("银行转账");
                    break;
                case PurchaseOrderConstants.PAYMENT_METHOD_CHECK:
                    response.setPaymentMethodName("支票");
                    break;
                case PurchaseOrderConstants.PAYMENT_METHOD_CREDIT_CARD:
                    response.setPaymentMethodName("信用卡");
                    break;
                case PurchaseOrderConstants.PAYMENT_METHOD_MONTHLY:
                    response.setPaymentMethodName("月结");
                    break;
                default:
                    response.setPaymentMethodName("未知");
                    break;
            }
        }
    }

    @Override
    public PageResult<PurchaseOrderResponse> findPage(PurchaseOrderQueryRequest purchaseOrderQueryRequest) {
        LambdaQueryWrapper<PurchaseOrder> queryWrapper = this.buildQueryWrapper(purchaseOrderQueryRequest);
        
        // 分页查询
        Page<PurchaseOrder> page = this.page(PageFactory.defaultPage(), queryWrapper);
        
        // 转换为响应对象
        List<PurchaseOrderResponse> responseList = page.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        return PageResultFactory.createPageResult(responseList, page.getTotal(), 
                (int) page.getCurrent(), (int) page.getSize());
    }

    @Override
    public List<PurchaseOrderResponse> findList(PurchaseOrderQueryRequest purchaseOrderQueryRequest) {
        LambdaQueryWrapper<PurchaseOrder> queryWrapper = this.buildQueryWrapper(purchaseOrderQueryRequest);
        
        List<PurchaseOrder> list = this.list(queryWrapper);
        
        return list.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(PurchaseConfirmRequest purchaseConfirmRequest) {
        PurchaseOrder purchaseOrder = this.getById(purchaseConfirmRequest.getId());
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_NOT_EXIST);
        }

        // 校验状态，只有草稿状态才能确认
        if (!PurchaseOrderConstants.PURCHASE_ORDER_STATUS_DRAFT.equals(purchaseOrder.getStatus())) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_STATUS_ERROR);
        }

        // 更新状态为已确认
        LambdaUpdateWrapper<PurchaseOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PurchaseOrder::getId, purchaseOrder.getId())
                .set(PurchaseOrder::getStatus, PurchaseOrderConstants.PURCHASE_ORDER_STATUS_CONFIRMED);
        
        if (StrUtil.isNotBlank(purchaseConfirmRequest.getConfirmRemark())) {
            updateWrapper.set(PurchaseOrder::getRemark, purchaseConfirmRequest.getConfirmRemark());
        }
        
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receive(PurchaseReceiveRequest purchaseReceiveRequest) {
        PurchaseOrder purchaseOrder = this.getById(purchaseReceiveRequest.getId());
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_NOT_EXIST);
        }

        // 校验状态，只有已确认状态才能入库
        if (!PurchaseOrderConstants.PURCHASE_ORDER_STATUS_CONFIRMED.equals(purchaseOrder.getStatus())) {
            throw new ServiceException(PurchaseOrderExceptionEnum.PURCHASE_ORDER_NOT_CONFIRMED_CANNOT_RECEIVE);
        }

        // 更新状态为已完成
        LambdaUpdateWrapper<PurchaseOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PurchaseOrder::getId, purchaseOrder.getId())
                .set(PurchaseOrder::getStatus, PurchaseOrderConstants.PURCHASE_ORDER_STATUS_COMPLETED)
                .set(PurchaseOrder::getPaymentMethod, purchaseReceiveRequest.getPaymentMethod());
        
        if (ObjectUtil.isNotEmpty(purchaseReceiveRequest.getActualTotalAmount())) {
            updateWrapper.set(PurchaseOrder::getTotalAmount, purchaseReceiveRequest.getActualTotalAmount());
        }
        
        if (StrUtil.isNotBlank(purchaseReceiveRequest.getPaymentAccount())) {
            updateWrapper.set(PurchaseOrder::getPaymentAccount, purchaseReceiveRequest.getPaymentAccount());
        }
        
        if (StrUtil.isNotBlank(purchaseReceiveRequest.getReceiveRemark())) {
            updateWrapper.set(PurchaseOrder::getRemark, purchaseReceiveRequest.getReceiveRemark());
        }
        
        this.update(updateWrapper);

        // 更新库存
        this.updateInventoryForReceive(purchaseOrder.getId());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<PurchaseOrder> buildQueryWrapper(PurchaseOrderQueryRequest request) {
        LambdaQueryWrapper<PurchaseOrder> queryWrapper = new LambdaQueryWrapper<>();
        
        // 入库单号
        if (StrUtil.isNotBlank(request.getOrderNo())) {
            queryWrapper.like(PurchaseOrder::getOrderNo, request.getOrderNo());
        }
        
        // 供应商ID
        if (ObjectUtil.isNotEmpty(request.getSupplierId())) {
            queryWrapper.eq(PurchaseOrder::getSupplierId, request.getSupplierId());
        }
        
        // 状态
        if (StrUtil.isNotBlank(request.getStatus())) {
            queryWrapper.eq(PurchaseOrder::getStatus, request.getStatus());
        }
        
        // 状态列表
        if (CollUtil.isNotEmpty(request.getStatusList())) {
            queryWrapper.in(PurchaseOrder::getStatus, request.getStatusList());
        }
        
        // 付款方式
        if (StrUtil.isNotBlank(request.getPaymentMethod())) {
            queryWrapper.eq(PurchaseOrder::getPaymentMethod, request.getPaymentMethod());
        }
        
        // 付款方式列表
        if (CollUtil.isNotEmpty(request.getPaymentMethodList())) {
            queryWrapper.in(PurchaseOrder::getPaymentMethod, request.getPaymentMethodList());
        }
        
        // 金额范围
        if (ObjectUtil.isNotEmpty(request.getMinTotalAmount())) {
            queryWrapper.ge(PurchaseOrder::getTotalAmount, request.getMinTotalAmount());
        }
        if (ObjectUtil.isNotEmpty(request.getMaxTotalAmount())) {
            queryWrapper.le(PurchaseOrder::getTotalAmount, request.getMaxTotalAmount());
        }
        
        // 日期范围
        if (ObjectUtil.isNotEmpty(request.getStartDate())) {
            queryWrapper.ge(PurchaseOrder::getOrderDate, request.getStartDate());
        }
        if (ObjectUtil.isNotEmpty(request.getEndDate())) {
            queryWrapper.le(PurchaseOrder::getOrderDate, request.getEndDate());
        }
        
        // 创建人
        if (ObjectUtil.isNotEmpty(request.getCreateUser())) {
            queryWrapper.eq(PurchaseOrder::getCreateUser, request.getCreateUser());
        }
        
        // 通用搜索
        if (StrUtil.isNotBlank(request.getSearchText())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(PurchaseOrder::getOrderNo, request.getSearchText())
                    .or().like(PurchaseOrder::getRemark, request.getSearchText())
            );
        }
        
        // 排序
        queryWrapper.orderByDesc(PurchaseOrder::getCreateTime);
        
        return queryWrapper;
    }

    /**
     * 转换为响应对象
     */
    private PurchaseOrderResponse convertToResponse(PurchaseOrder purchaseOrder) {
        PurchaseOrderResponse response = new PurchaseOrderResponse();
        BeanUtil.copyProperties(purchaseOrder, response);
        
        // 填充供应商信息
        this.fillSupplierInfo(response, purchaseOrder.getSupplierId());
        
        // 填充显示名称
        this.fillDisplayNames(response);
        
        return response;
    }

    /**
     * 更新库存（采购入库）
     */
    private void updateInventoryForReceive(Long orderId) {
        // 获取采购入库单明细
        List<PurchaseOrderDetail> detailList = purchaseOrderDetailMapper.getByOrderId(orderId);
        
        if (CollUtil.isEmpty(detailList)) {
            log.warn("采购入库单[{}]没有明细数据，跳过库存更新", orderId);
            return;
        }

        // 遍历明细，更新每个商品的库存
        for (PurchaseOrderDetail detail : detailList) {
            try {
                // 检查商品是否已有库存记录，如果没有则初始化
                if (!inventoryService.hasInventoryRecord(detail.getProductId())) {
                    log.info("商品[{}]没有库存记录，先初始化库存", detail.getProductId());
                    inventoryService.initInventory(createInitInventoryRequest(detail.getProductId()));
                }

                // 创建库存操作请求
                InventoryOperationRequest operationRequest = new InventoryOperationRequest();
                operationRequest.setProductId(detail.getProductId());
                operationRequest.setQuantity(detail.getQuantity());
                operationRequest.setUnitCost(detail.getUnitPrice());
                operationRequest.setOperationType(InventoryConstants.OperationType.IN);
                operationRequest.setReferenceType(InventoryConstants.ReferenceType.PURCHASE_ORDER);
                operationRequest.setReferenceId(orderId);
                operationRequest.setRemark("采购入库");

                // 执行库存操作
                inventoryService.operateInventory(operationRequest);
                
                log.info("成功更新商品[{}]库存，入库数量：{}", detail.getProductId(), detail.getQuantity());
                
            } catch (Exception e) {
                log.error("更新商品[{}]库存失败：{}", detail.getProductId(), e.getMessage(), e);
                throw new ServiceException(PurchaseOrderExceptionEnum.INVENTORY_UPDATE_FAILED);
            }
        }
    }

    /**
     * 创建初始化库存请求
     */
    private cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryRequest createInitInventoryRequest(Long productId) {
        cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryRequest request = 
            new cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryRequest();
        request.setProductId(productId);
        request.setCurrentStock(BigDecimal.ZERO);
        request.setMinStock(BigDecimal.ZERO);
        request.setAvgCost(BigDecimal.ZERO);
        request.setTotalValue(BigDecimal.ZERO);
        return request;
    }

}