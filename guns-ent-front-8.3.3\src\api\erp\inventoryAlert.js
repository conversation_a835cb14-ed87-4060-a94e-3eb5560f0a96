/**
 * 库存预警API接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
export class InventoryAlertApi {
  
  /**
   * 获取预警类型选项
   */
  static getAlertTypeOptions() {
    return [
      { label: '库存不足', value: 'LOW_STOCK', color: 'orange' },
      { label: '零库存', value: 'ZERO_STOCK', color: 'red' },
      { label: '库存积压', value: 'OVERSTOCK', color: 'blue' },
      { label: '临期预警', value: 'EXPIRY', color: 'purple' }
    ];
  }

  /**
   * 获取预警级别选项
   */
  static getAlertLevelOptions() {
    return [
      { label: '紧急', value: 'CRITICAL', color: 'red' },
      { label: '警告', value: 'WARNING', color: 'orange' },
      { label: '提醒', value: 'INFO', color: 'blue' }
    ];
  }

  /**
   * 获取目标类型选项
   */
  static getTargetTypeOptions() {
    return [
      { label: '单个商品', value: 'PRODUCT' },
      { label: '商品分类', value: 'CATEGORY' },
      { label: '全部商品', value: 'ALL' }
    ];
  }

  /**
   * 获取阈值类型选项
   */
  static getThresholdTypeOptions() {
    return [
      { label: '数量', value: 'QUANTITY' },
      { label: '百分比', value: 'PERCENTAGE' },
      { label: '天数', value: 'DAYS' }
    ];
  }

  /**
   * 获取比较操作符选项
   */
  static getComparisonOperatorOptions() {
    return [
      { label: '小于等于', value: 'LTE' },
      { label: '小于', value: 'LT' },
      { label: '大于等于', value: 'GTE' },
      { label: '大于', value: 'GT' },
      { label: '等于', value: 'EQ' }
    ];
  }

  /**
   * 获取通知方式选项
   */
  static getNotificationMethodOptions() {
    return [
      { label: '系统通知', value: 'SYSTEM' },
      { label: '邮件通知', value: 'EMAIL' },
      { label: '短信通知', value: 'SMS' },
      { label: '微信通知', value: 'WECHAT' }
    ];
  }

  /**
   * 获取处理状态选项
   */
  static getStatusOptions() {
    return [
      { label: '待处理', value: 'PENDING', color: 'orange' },
      { label: '处理中', value: 'PROCESSING', color: 'blue' },
      { label: '已解决', value: 'RESOLVED', color: 'green' },
      { label: '已忽略', value: 'IGNORED', color: 'gray' }
    ];
  }

  /**
   * 获取预警类型名称
   */
  static getAlertTypeName(type) {
    const options = InventoryAlertApi.getAlertTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取预警类型信息
   */
  static getAlertTypeInfo(type) {
    const options = InventoryAlertApi.getAlertTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? { name: option.label, color: option.color } : { name: type, color: 'default' };
  }

  /**
   * 获取预警级别名称和颜色
   */
  static getAlertLevelInfo(level) {
    const options = InventoryAlertApi.getAlertLevelOptions();
    const option = options.find(item => item.value === level);
    return option ? { name: option.label, color: option.color } : { name: level, color: 'default' };
  }

  /**
   * 获取状态信息
   */
  static getStatusInfo(status) {
    const options = InventoryAlertApi.getStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? { name: option.label, color: option.color } : { name: status, color: 'default' };
  }

  /**
   * 获取目标类型名称
   */
  static getTargetTypeName(type) {
    const options = InventoryAlertApi.getTargetTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取阈值类型名称
   */
  static getThresholdTypeName(type) {
    const options = InventoryAlertApi.getThresholdTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取比较操作符名称
   */
  static getComparisonOperatorName(operator) {
    const options = InventoryAlertApi.getComparisonOperatorOptions();
    const option = options.find(item => item.value === operator);
    return option ? option.label : operator;
  }

  /**
   * 格式化阈值显示
   */
  static formatThresholdValue(value, type) {
    if (value == null) return '-';
    
    switch (type) {
      case 'PERCENTAGE':
        return `${value}%`;
      case 'DAYS':
        return `${value}天`;
      case 'QUANTITY':
      default:
        return value.toString();
    }
  }

  /**
   * 获取预警级别对应的图标
   */
  static getAlertLevelIcon(level) {
    switch (level) {
      case 'CRITICAL':
        return 'exclamation-circle';
      case 'WARNING':
        return 'warning';
      case 'INFO':
        return 'info-circle';
      default:
        return 'question-circle';
    }
  }

  /**
   * 获取预警类型对应的图标
   */
  static getAlertTypeIcon(type) {
    switch (type) {
      case 'LOW_STOCK':
        return 'arrow-down';
      case 'ZERO_STOCK':
        return 'stop';
      case 'OVERSTOCK':
        return 'arrow-up';
      case 'EXPIRY':
        return 'clock-circle';
      default:
        return 'alert';
    }
  }

  /**
   * 验证规则配置
   */
  static validateRuleConfig(rule) {
    const errors = [];

    // 验证目标类型和目标ID
    if ((rule.targetType === 'PRODUCT' || rule.targetType === 'CATEGORY') && !rule.targetId) {
      errors.push('请选择目标对象');
    }

    // 验证阈值配置
    if (rule.thresholdValue == null || rule.thresholdValue < 0) {
      errors.push('阈值必须大于等于0');
    }

    // 验证预警类型和阈值类型的匹配性
    if (rule.ruleType === 'EXPIRY' && rule.thresholdType !== 'DAYS') {
      errors.push('临期预警的阈值类型必须是天数');
    }

    // 验证检查频率
    if (rule.checkFrequency == null || rule.checkFrequency < 1 || rule.checkFrequency > 1440) {
      errors.push('检查频率必须在1-1440分钟之间');
    }

    return errors;
  }

  /**
   * 生成规则描述
   */
  static generateRuleDescription(rule) {
    const alertType = InventoryAlertApi.getAlertTypeName(rule.ruleType);
    const targetType = InventoryAlertApi.getTargetTypeName(rule.targetType);
    const thresholdValue = InventoryAlertApi.formatThresholdValue(rule.thresholdValue, rule.thresholdType);
    const operator = InventoryAlertApi.getComparisonOperatorName(rule.comparisonOperator);
    
    return `${targetType}的${alertType}预警，当库存${operator}${thresholdValue}时触发`;
  }
}
