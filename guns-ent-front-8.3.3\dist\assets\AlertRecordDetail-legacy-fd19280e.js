System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./InventoryAlertRecordApi-legacy-ebfe5eed.js"],(function(e,a){"use strict";var t,l,r,d,o,c,n,s,i,u,f,g,m,p,T,h,y,D,_,b,v,C;return{setters:[e=>{t=e._,l=e.r,r=e.X,d=e.a,o=e.f,c=e.w,n=e.b,s=e.d,i=e.g,u=e.t,f=e.ah,g=e.h,m=e.ag,p=e.Y,T=e.U,h=e.Z,y=e.a0,D=e.i,_=e.B,b=e.n,v=e.M},null,null,null,null,null,null,e=>{C=e.I}],execute:function(){var a=document.createElement("style");a.textContent=".record-detail-container[data-v-c06001de]{max-height:70vh;overflow-y:auto}.detail-card[data-v-c06001de]{margin-bottom:16px}.alert-message[data-v-c06001de],.handle-remark[data-v-c06001de]{white-space:pre-wrap;word-break:break-word}.trigger-condition[data-v-c06001de]{font-family:monospace;background-color:#f5f5f5;padding:8px;border-radius:4px}.action-buttons[data-v-c06001de]{text-align:right;padding-top:16px;border-top:1px solid #f0f0f0}\n",document.head.appendChild(a);const x={name:"AlertRecordDetail",props:{visible:{type:Boolean,default:!1},recordData:{type:Object,default:()=>({})}},emits:["update:visible","process"],setup(e,{emit:a}){const t=l([]),d=l(!1);return r((()=>e.visible),(a=>{a&&e.recordData.id&&(async()=>{if(e.recordData.productId)try{d.value=!0;const a=await C.getHistory({productId:e.recordData.productId,limit:10});t.value=a.data||[]}catch(a){console.error("获取历史记录失败:",a)}finally{d.value=!1}})()})),{historyData:t,historyLoading:d,historyColumns:[{title:"预警时间",dataIndex:"alertTime",key:"alertTime",width:150,slots:{customRender:"alertTime"}},{title:"预警级别",dataIndex:"alertLevel",key:"alertLevel",width:100,slots:{customRender:"alertLevel"}},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:100},{title:"处理状态",dataIndex:"handleStatus",key:"handleStatus",width:100,slots:{customRender:"handleStatus"}},{title:"预警消息",dataIndex:"alertMessage",key:"alertMessage",ellipsis:!0}],handleCancel:()=>{a("update:visible",!1)},handleProcess:()=>{a("process",e.recordData)},formatDateTime:e=>e?m(e).format("YYYY-MM-DD HH:mm:ss"):"-",getAlertTypeColor:e=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"}[e]||"default"),getAlertTypeText:e=>({LOW_STOCK:"库存不足",ZERO_STOCK:"零库存",OVERSTOCK:"库存积压",EXPIRY:"临期预警"}[e]||e),getAlertLevelColor:e=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"}[e]||"default"),getAlertLevelText:e=>({CRITICAL:"紧急",WARNING:"警告",INFO:"提醒"}[e]||e),getHandleStatusColor:e=>({PENDING:"orange",RESOLVED:"green",IGNORED:"gray"}[e]||"default"),getHandleStatusText:e=>({PENDING:"待处理",RESOLVED:"已解决",IGNORED:"已忽略"}[e]||e),getHandleTypeColor:e=>({RESOLVE:"green",IGNORE:"gray"}[e]||"default"),getHandleTypeText:e=>({RESOLVE:"解决",IGNORE:"忽略"}[e]||e),getThresholdTypeText:e=>({QUANTITY:"数量",PERCENTAGE:"百分比",DAYS:"天数"}[e]||e),getComparisonOperatorText:e=>({LTE:"小于等于",LT:"小于",GTE:"大于等于",GT:"大于",EQ:"等于"}[e]||e),getComparisonOperatorSymbol:e=>({LTE:"≤",LT:"<",GTE:"≥",GT:">",EQ:"="}[e]||e)}}},S={class:"record-detail-container"},E={class:"alert-message"},I={class:"trigger-condition"},L={class:"handle-remark"},O={class:"action-buttons"};e("default",t(x,[["render",function(e,a,t,l,r,m){const C=p,x=T,R=h,k=y,N=D,A=_,G=b,w=v;return d(),o(w,{title:"预警记录详情",visible:t.visible,width:800,footer:null,onCancel:l.handleCancel},{default:c((()=>[n("div",S,[s(k,{title:"基本信息",size:"small",class:"detail-card"},{default:c((()=>[s(R,{column:2,bordered:""},{default:c((()=>[s(C,{label:"预警ID"},{default:c((()=>[i(u(t.recordData.id),1)])),_:1}),s(C,{label:"规则名称"},{default:c((()=>[i(u(t.recordData.ruleName),1)])),_:1}),s(C,{label:"商品名称"},{default:c((()=>[i(u(t.recordData.productName),1)])),_:1}),s(C,{label:"商品编码"},{default:c((()=>[i(u(t.recordData.productCode),1)])),_:1}),s(C,{label:"预警类型"},{default:c((()=>[s(x,{color:l.getAlertTypeColor(t.recordData.alertType)},{default:c((()=>[i(u(l.getAlertTypeText(t.recordData.alertType)),1)])),_:1},8,["color"])])),_:1}),s(C,{label:"预警级别"},{default:c((()=>[s(x,{color:l.getAlertLevelColor(t.recordData.alertLevel)},{default:c((()=>[i(u(l.getAlertLevelText(t.recordData.alertLevel)),1)])),_:1},8,["color"])])),_:1}),s(C,{label:"预警时间"},{default:c((()=>[i(u(l.formatDateTime(t.recordData.alertTime)),1)])),_:1}),s(C,{label:"处理状态"},{default:c((()=>[s(x,{color:l.getHandleStatusColor(t.recordData.handleStatus)},{default:c((()=>[i(u(l.getHandleStatusText(t.recordData.handleStatus)),1)])),_:1},8,["color"])])),_:1})])),_:1})])),_:1}),s(k,{title:"库存信息",size:"small",class:"detail-card"},{default:c((()=>[s(R,{column:2,bordered:""},{default:c((()=>[s(C,{label:"当前库存"},{default:c((()=>[n("span",{style:f({color:t.recordData.currentStock<=t.recordData.thresholdValue?"#f5222d":"#52c41a",fontWeight:"bold"})},u(t.recordData.currentStock),5)])),_:1}),s(C,{label:"最小库存"},{default:c((()=>[i(u(t.recordData.minStock||"-"),1)])),_:1}),s(C,{label:"最大库存"},{default:c((()=>[i(u(t.recordData.maxStock||"-"),1)])),_:1}),s(C,{label:"阈值"},{default:c((()=>[i(u(t.recordData.thresholdValue),1)])),_:1}),s(C,{label:"阈值类型"},{default:c((()=>[i(u(l.getThresholdTypeText(t.recordData.thresholdType)),1)])),_:1}),s(C,{label:"比较操作符"},{default:c((()=>[i(u(l.getComparisonOperatorText(t.recordData.comparisonOperator)),1)])),_:1})])),_:1})])),_:1}),s(k,{title:"预警详情",size:"small",class:"detail-card"},{default:c((()=>[s(R,{column:1,bordered:""},{default:c((()=>[s(C,{label:"预警消息"},{default:c((()=>[n("div",E,u(t.recordData.alertMessage),1)])),_:1}),s(C,{label:"触发条件"},{default:c((()=>[n("div",I," 当前库存 ("+u(t.recordData.currentStock)+") "+u(l.getComparisonOperatorSymbol(t.recordData.comparisonOperator))+" 阈值 ("+u(t.recordData.thresholdValue)+") ",1)])),_:1})])),_:1})])),_:1}),"PENDING"!==t.recordData.handleStatus?(d(),o(k,{key:0,title:"处理信息",size:"small",class:"detail-card"},{default:c((()=>[s(R,{column:2,bordered:""},{default:c((()=>[s(C,{label:"处理人"},{default:c((()=>[i(u(t.recordData.handleUserName||"-"),1)])),_:1}),s(C,{label:"处理时间"},{default:c((()=>[i(u(l.formatDateTime(t.recordData.handleTime)),1)])),_:1}),s(C,{label:"处理方式",span:2},{default:c((()=>[s(x,{color:l.getHandleTypeColor(t.recordData.handleType)},{default:c((()=>[i(u(l.getHandleTypeText(t.recordData.handleType)),1)])),_:1},8,["color"])])),_:1}),s(C,{label:"处理备注",span:2},{default:c((()=>[n("div",L,u(t.recordData.handleRemark||"无"),1)])),_:1})])),_:1})])),_:1})):g("",!0),s(k,{title:"相关历史记录",size:"small",class:"detail-card"},{default:c((()=>[s(N,{columns:l.historyColumns,"data-source":l.historyData,pagination:!1,loading:l.historyLoading,size:"small"},{alertLevel:c((({record:e})=>[s(x,{color:l.getAlertLevelColor(e.alertLevel)},{default:c((()=>[i(u(l.getAlertLevelText(e.alertLevel)),1)])),_:2},1032,["color"])])),handleStatus:c((({record:e})=>[s(x,{color:l.getHandleStatusColor(e.handleStatus)},{default:c((()=>[i(u(l.getHandleStatusText(e.handleStatus)),1)])),_:2},1032,["color"])])),alertTime:c((({record:e})=>[i(u(l.formatDateTime(e.alertTime)),1)])),_:1},8,["columns","data-source","loading"])])),_:1}),n("div",O,[s(G,null,{default:c((()=>["PENDING"===t.recordData.handleStatus?(d(),o(A,{key:0,type:"primary",onClick:l.handleProcess},{default:c((()=>a[0]||(a[0]=[i(" 处理预警 ")]))),_:1,__:[0]},8,["onClick"])):g("",!0),s(A,{onClick:l.handleCancel},{default:c((()=>a[1]||(a[1]=[i(" 关闭 ")]))),_:1,__:[1]},8,["onClick"])])),_:1})])])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-c06001de"]]))}}}));
