System.register(["./index-legacy-ee1db0c7.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-efb51034.js","./org-tree-legacy-926da3e5.js","./OrgApi-legacy-c15eac58.js","./org-detail-legacy-7da79c5f.js","./org-add-edit-legacy-63da3c2d.js","./import-export-org-legacy-609ee390.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./set-approver-legacy-303b6a70.js","./index-legacy-dba03026.js","./index-legacy-45c79de7.js","./index-legacy-16a1b89e.js","./org-form-legacy-6e9e4f75.js","./index-legacy-94a6fc23.js","./index-legacy-198191c1.js"],(function(e,l){"use strict";var a,t,n,o,i,s,u,d,r,v,c,g,y,m,p,_,h,x,f,b,k,w,I,C,S,T,L,j,N,O,z,R,E,D,A,F,G,U,B,M,P,J,K,V,q,H,Q,W,X,Y;return{setters:[e=>{a=e.r,t=e.L,n=e.N,o=e.o,i=e.k,s=e.bv,u=e.a,d=e.c,r=e.aR,v=e.aS,c=e.b,g=e.d,y=e.w,m=e.f,p=e.g,_=e.t,h=e.O,x=e.Q,f=e.F,b=e.e,k=e.h,w=e.ah,I=e.at,C=e.aI,S=e.aJ,T=e.M,L=e.E,j=e.m,N=e.n,O=e.B,z=e.I,R=e.p,E=e.q,D=e.D,A=e.l,F=e.V,G=e.W,U=e.J,B=e.u,M=e.v,P=e.G,J=e.H,K=e.U},e=>{V=e._},e=>{q=e._},null,e=>{H=e.default},e=>{Q=e.O},e=>{W=e.default},e=>{X=e.default},e=>{Y=e.default},null,null,null,null,null,null,null,null,null,null],execute:function(){const Z={class:"guns-layout"},$={class:"guns-layout"},ee={class:"guns-layout-sidebar width-100 p-t-12"},le={class:"sidebar-content"},ae={class:"guns-layout-content"},te={class:"guns-layout"},ne={class:"guns-layout-content-application"},oe={class:"content-mian"},ie={class:"content-mian-header"},se={class:"header-content"},ue={class:"header-content-left"},de={class:"header-content-right"},re={class:"content-mian-body"},ve={class:"table-content"},ce={key:0,class:"super-search",style:{"margin-top":"8px"}},ge=["onClick"],ye={key:2,class:"org-type"},me={key:4};e("default",Object.assign({name:"Organization"},{__name:"index",setup(e){const pe=C((()=>S((()=>l.import("./org-level-legacy-cfb08ac8.js")),void 0))),_e=a([{id:"",name:"全部状态"},{id:1,name:"启用"},{id:2,name:"禁用"}]),he=a([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"orgName",title:"机构名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"orgCode",title:"机构编码",width:100,isShow:!0},{dataIndex:"statusFlag",title:"机构状态",ellipsis:!0,width:100,isShow:!0},{dataIndex:"orgType",title:"机构类型",ellipsis:!0,width:100,isShow:!0},{dataIndex:"organizationLevel",title:"层级",ellipsis:!0,width:100,isShow:!0},{dataIndex:"organizationLevelName",title:"层级名称",ellipsis:!0,width:100,isShow:!0},{dataIndex:"orgSort",title:"排序",width:100,isShow:!0},{dataIndex:"createTime",title:"创建时间",width:150,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}]),xe=a([{value:1,name:"一级"},{value:2,name:"二级"},{value:3,name:"三级"},{value:4,name:"四级"},{value:5,name:"五级"},{value:6,name:"六级"},{value:7,name:"七级"},{value:8,name:"八级"},{value:9,name:"九级"},{value:10,name:"十级"}]),fe=a(null),be=a(null),ke=a(null),we=a({orgId:null,statusFlag:null,searchText:""}),Ie=a(null),Ce=a(!1),Se=a(!1),Te=a(null),Le=a(null),je=a(!1),Ne=a(!1),Oe=a(!1),ze=a(!1),Re=t((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),Ee=t((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),De=t((()=>n()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),Ae=t((()=>e=>{var l;let a="";if(null!=e&&null!==(l=e.organizationLevel)&&void 0!==l&&l.levelNumber){let l=xe.value.find((l=>{var a;return l.value==(null==e||null===(a=e.organizationLevel)||void 0===a?void 0:a.levelNumber)}));l&&(a=l.name)}return a}));o((()=>{}));const Fe=()=>{ze.value=!ze.value},Ge=(e,l)=>{we.value.orgId=e[0],Te.value=e[0],Le.value=l.node.orgName,Me()},Ue=({key:e})=>{"1"==e?Ke():"2"==e?Ne.value=!0:"3"==e&&(Oe.value=!0)},Be=()=>{ke.value.reloadOrgTreeData(),Me()},Me=()=>{fe.value.reload()},Pe=()=>{we.value.searchText="",we.value.statusFlag=null,we.value.orgId=null,ke.value.currentSelectKeys=[],be.value.changeDropname("状态"),Me()},Je=e=>{we.value.orgId||(Le.value="",Te.value=""),Ie.value=e,Ce.value=!0},Ke=()=>{if(fe.value.selectedRowList&&0==fe.value.selectedRowList.length)return j.warning("请选择需要删除的组织机构");T.confirm({title:"提示",content:"确定要删除选中的组织机构吗?",icon:g(L),maskClosable:!0,onOk:async()=>{const e=await Q.batchDelete({orgIdList:fe.value.selectedRowList});j.success(e.message),Me()}})},Ve=e=>{Te.value=e?e.orgId:null,Le.value=e?e.orgName:null,Ie.value=null,Ce.value=!0},qe=e=>{Ie.value=e,Ce.value=!0},He=()=>{Ne.value=!1,Me()};return(e,l)=>{const a=N,t=i("plus-outlined"),n=O,o=z,C=R,S=E,be=i("small-dash-outlined"),Ke=D,Qe=A,We=F,Xe=G,Ye=U,Ze=B,$e=M,el=P,ll=J,al=K,tl=q,nl=V,ol=s("permission");return u(),d("div",Z,[r(c("div",$,[g(nl,{width:"292px",cacheKey:"SYSTEM_STRUCTURE_ORGANIZATION"},{content:y((()=>[c("div",ae,[c("div",te,[c("div",ne,[c("div",oe,[c("div",ie,[c("div",se,[c("div",ue,[g(a,{size:16})]),c("div",de,[g(a,{size:16},{default:y((()=>[r((u(),m(n,{type:"primary",class:"border-radius",onClick:l[0]||(l[0]=e=>Je())},{default:y((()=>[g(t),l[7]||(l[7]=p("新建"))])),_:1,__:[7]})),[[ol,["ADD_ORG"]]]),g(Ke,null,{overlay:y((()=>[g(S,{onClick:Ue},{default:y((()=>[r((u(),d("div",null,[g(C,{key:"1"},{default:y((()=>[g(o,{iconClass:"icon-opt-shanchu",color:"#60666b"}),l[8]||(l[8]=c("span",null,"批量删除",-1))])),_:1,__:[8]})])),[[ol,["DELETE_ORG"]]]),g(C,{key:"2"},{default:y((()=>[g(o,{iconClass:"icon-opt-daoru",color:"#60666b"}),l[9]||(l[9]=c("span",null,"导入导出",-1))])),_:1,__:[9]}),g(C,{key:"3"},{default:y((()=>[g(o,{iconClass:"icon-menu-moxingliebiao",color:"#60666b"}),l[10]||(l[10]=c("span",null,"层级维护",-1))])),_:1,__:[10]})])),_:1})])),default:y((()=>[g(n,{class:"border-radius"},{default:y((()=>[l[11]||(l[11]=p(" 更多 ")),g(be)])),_:1,__:[11]})])),_:1})])),_:1})])])]),c("div",re,[c("div",ve,[g(tl,{columns:he.value,where:we.value,fieldBusinessCode:"ORG_TABLE",showTableTool:"",showToolTotal:!1,rowId:"orgId",ref_key:"tableRef",ref:fe,url:"/hrOrganization/page"},{toolLeft:y((()=>[g(Qe,{value:we.value.searchText,"onUpdate:value":l[1]||(l[1]=e=>we.value.searchText=e),bordered:!1,allowClear:"",placeholder:"机构名称、编码（回车搜索）",onPressEnter:Me,style:{width:"240px"},class:"search-input"},{prefix:y((()=>[g(o,{iconClass:"icon-opt-search"})])),_:1},8,["value"]),g(We,{type:"vertical",class:"divider"}),c("a",{onClick:Fe},_(ze.value?"收起":"高级筛选"),1)])),toolBottom:y((()=>[ze.value?(u(),d("div",ce,[g(ll,{model:we.value,labelCol:Re.value,"wrapper-col":Ee.value},{default:y((()=>[g(el,{gutter:16},{default:y((()=>[g($e,h(x(De.value)),{default:y((()=>[g(Ze,{label:"状态:"},{default:y((()=>[g(Ye,{value:we.value.statusFlag,"onUpdate:value":l[2]||(l[2]=e=>we.value.statusFlag=e),placeholder:"请选择状态",style:{width:"100%"},allowClear:""},{default:y((()=>[(u(!0),d(f,null,b(_e.value,(e=>(u(),m(Xe,{value:e.id,key:e.id},{default:y((()=>[p(_(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),g($e,h(x(De.value)),{default:y((()=>[g(Ze,{label:" ",class:"not-label"},{default:y((()=>[g(a,{size:16},{default:y((()=>[g(n,{class:"border-radius",onClick:Me,type:"primary"},{default:y((()=>l[12]||(l[12]=[p("查询")]))),_:1,__:[12]}),g(n,{class:"border-radius",onClick:Pe},{default:y((()=>l[13]||(l[13]=[p("重置")]))),_:1,__:[13]})])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):k("",!0)])),bodyCell:y((({column:e,record:t})=>{var n,i;return["orgName"==e.dataIndex?(u(),d("a",{key:0,onClick:e=>(e=>{je.value=!1,Ie.value=e,Se.value=!0})(t)},_(t.orgName),9,ge)):k("",!0),"statusFlag"==e.dataIndex?(u(),d(f,{key:1},[1==t.statusFlag?(u(),m(al,{key:0,color:"green"},{default:y((()=>l[14]||(l[14]=[p("启用")]))),_:1,__:[14]})):k("",!0),2==t.statusFlag?(u(),m(al,{key:1,color:"red"},{default:y((()=>l[15]||(l[15]=[p("禁用")]))),_:1,__:[15]})):k("",!0)],64)):k("",!0),"orgType"==e.dataIndex?(u(),d("div",ye,[1==t.orgType?(u(),m(al,{key:0,color:"green"},{default:y((()=>l[16]||(l[16]=[p("公司")]))),_:1,__:[16]})):k("",!0),2==t.orgType?(u(),m(al,{key:1,color:"red"},{default:y((()=>l[17]||(l[17]=[p("部门")]))),_:1,__:[17]})):k("",!0)])):k("",!0),"organizationLevel"==e.dataIndex?(u(),d("span",{key:3,style:w({color:null==t||null===(n=t.organizationLevel)||void 0===n?void 0:n.levelColor})},_(Ae.value(t)),5)):k("",!0),"organizationLevelName"==e.dataIndex?(u(),d("span",me,_(null==t||null===(i=t.organizationLevel)||void 0===i?void 0:i.levelName),1)):k("",!0),"action"==e.key?(u(),m(a,{key:5,size:16},{default:y((()=>[r(g(o,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>Je(t)},null,8,["onClick"]),[[ol,["EDIT_ORG"]]]),r(g(o,{iconClass:"icon-opt-shenpirenshezhi",color:"#60666b","font-size":"24px",title:"审批人设置",onClick:e=>(e=>{je.value=!0,Ie.value=e,Se.value=!0})(t)},null,8,["onClick"]),[[ol,["ASSIGN_APPROVER"]]]),r(g(o,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{T.confirm({title:"提示",content:"确定要删除选中的组织机构吗?",icon:g(L),maskClosable:!0,onOk:async()=>{const l=await Q.delete({orgId:e.orgId});j.success(l.message),Me()}})})(t)},null,8,["onClick"]),[[ol,["DELETE_ORG"]]])])),_:2},1024)):k("",!0)]})),_:1},8,["columns","where"])])])])])])])])),default:y((()=>[c("div",ee,[c("div",le,[g(H,{onTreeSelect:Ge,ref_key:"orgTreeRef",ref:ke,isShowEditIcon:"",onDeleteOrg:Me,onAddOrg:Ve,onEditOrg:qe},null,512)])])])),_:1})],512),[[v,!Ne.value]]),Ne.value?(u(),m(Y,{key:0,onBack:l[3]||(l[3]=e=>Ne.value=!1),onBackReload:He})):k("",!0),Ce.value?(u(),m(X,{key:1,visible:Ce.value,"onUpdate:visible":l[4]||(l[4]=e=>Ce.value=e),data:Ie.value,onDone:Be,parentId:Te.value,parentName:Le.value,levelNumberList:xe.value},null,8,["visible","data","parentId","parentName","levelNumberList"])):k("",!0),Se.value?(u(),m(W,{key:2,visible:Se.value,"onUpdate:visible":l[5]||(l[5]=e=>Se.value=e),data:Ie.value,isShowApprover:je.value,levelNumberList:xe.value},null,8,["visible","data","isShowApprover","levelNumberList"])):k("",!0),Oe.value?(u(),m(I(pe),{key:3,visible:Oe.value,"onUpdate:visible":l[6]||(l[6]=e=>Oe.value=e),levelNumberList:xe.value,onDone:Me},null,8,["visible","levelNumberList"])):k("",!0)])}}}))}}}));
