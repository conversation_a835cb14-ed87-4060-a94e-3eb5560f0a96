<template>
  <a-modal
    :visible="visible"
    title="设置库存预警值"
    :width="600"
    :maskClosable="false"
    @cancel="handleCancel"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确认设置
      </a-button>
    </template>

    <div class="set-min-stock-content">
      <!-- 设置提示 -->
      <a-alert
        message="预警值设置说明"
        description="当商品库存低于或等于预警值时，系统将在库存列表中标记为预警状态，提醒及时补货。"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <!-- 商品信息 -->
      <a-card v-if="data.productId" title="商品信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="商品名称">
            <span class="product-name">{{ data.productName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="商品编码">
            {{ data.productCode }}
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            <span :class="getStockClass(data.currentStock, data.minStock)">
              {{ formatStock(data.currentStock, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="最小库存预警值">
            {{ formatStock(data.minStock, data.pricingType) }}
            {{ getStockUnit(data.pricingType, data.unit) }}
          </a-descriptions-item>
          <a-descriptions-item label="最大库存预警值">
            {{ data.maxStock ? formatStock(data.maxStock, data.pricingType) + ' ' + getStockUnit(data.pricingType, data.unit) : '未设置' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 设置表单 -->
      <a-card title="预警值设置" size="small">
        <a-form ref="formRef" :model="minStockForm" :rules="rules" layout="vertical">
          <!-- 商品选择（批量设置时显示） -->
          <a-form-item v-if="!data.productId" label="选择商品" name="productId" required>
            <product-selector 
              v-model:value="minStockForm.productId" 
              :filter="{ businessModeList: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
              @change="onProductChange"
            />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最小库存预警值" name="minStock" required>
                <a-input-number
                  v-model:value="minStockForm.minStock"
                  :min="0"
                  :precision="getPrecision()"
                  :step="getStep()"
                  style="width: 100%"
                  placeholder="请输入最小库存预警值"
                >
                  <template #addonAfter>
                    {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
                  </template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="最大库存预警值" name="maxStock">
                <a-input-number
                  v-model:value="minStockForm.maxStock"
                  :min="0"
                  :precision="getPrecision()"
                  :step="getStep()"
                  style="width: 100%"
                  placeholder="请输入最大库存预警值（可选）"
                >
                  <template #addonAfter>
                    {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
                  </template>
                </a-input-number>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="建议补货量" name="suggestRestock">
                <a-input-number
                  v-model:value="minStockForm.suggestRestock"
                  :min="0"
                  :precision="getPrecision()"
                  :step="getStep()"
                  style="width: 100%"
                  placeholder="建议补货量（可选）"
                >
                  <template #addonAfter>
                    {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
                  </template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预警级别" name="warningLevel">
                <a-select v-model:value="minStockForm.warningLevel" placeholder="请选择预警级别">
                  <a-select-option value="LOW">低级预警</a-select-option>
                  <a-select-option value="MEDIUM">中级预警</a-select-option>
                  <a-select-option value="HIGH">高级预警</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>



          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="minStockForm.remark"
              placeholder="请输入备注信息（可选）"
              :rows="3"
              :maxlength="200"
              showCount
            />
          </a-form-item>
        </a-form>

        <!-- 预警预览 -->
        <a-card v-if="warningPreview.show" title="预警预览" size="small" style="margin-top: 16px; background: #fafafa;">
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="预警状态">
              <a-tag :color="warningPreview.statusColor">
                <icon-font 
                  v-if="warningPreview.isWarning" 
                  iconClass="icon-opt-jinggao" 
                  style="margin-right: 4px;"
                />
                {{ warningPreview.statusText }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="预警说明">
              {{ warningPreview.description }}
            </a-descriptions-item>
            <a-descriptions-item v-if="minStockForm.suggestRestock" label="建议补货">
              补货至 {{ formatStock(minStockForm.suggestRestock, selectedProduct.pricingType) }}
              {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryApi } from '../api/InventoryApi';
import ProductSelector from '@/components/erp/ProductSelector.vue';

export default {
  name: 'SetMinStockModal',
  components: {
    ProductSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const loading = ref(false);
    const selectedProduct = ref({});

    // 预警值设置表单
    const minStockForm = reactive({
      productId: null,
      minStock: null,
      maxStock: null,
      suggestRestock: null,
      warningLevel: 'MEDIUM',
      remark: ''
    });

    // 预警预览
    const warningPreview = reactive({
      show: false,
      isWarning: false,
      statusColor: 'green',
      statusText: '库存正常',
      description: ''
    });

    // 表单验证规则
    const rules = {
      productId: [
        { required: true, message: '请选择商品', trigger: 'change' }
      ],
      minStock: [
        { required: true, message: '请输入最小库存预警值', trigger: 'blur' },
        { type: 'number', min: 0, message: '最小库存预警值不能小于0', trigger: 'blur' }
      ],
      maxStock: [
        { type: 'number', min: 0, message: '最大库存预警值不能小于0', trigger: 'blur' },
        {
          validator: (rule, value) => {
            if (value && minStockForm.minStock && value <= minStockForm.minStock) {
              return Promise.reject('最大库存预警值必须大于最小库存预警值');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ]
    };

    // 监听props.data变化
    watch(() => props.data, (newData) => {
      console.log('SetMinStockModal 接收到数据:', newData);
      if (newData && newData.productId) {
        selectedProduct.value = { ...newData };
        minStockForm.productId = newData.productId;
        minStockForm.minStock = newData.minStock || 0;
        minStockForm.maxStock = newData.maxStock || null;
        console.log('SetMinStockModal 表单数据更新:', minStockForm);
        updatePreview();
      }
    }, { immediate: true });

    // 监听visible变化
    watch(() => props.visible, (newVisible) => {
      console.log('SetMinStockModal visible 变化:', newVisible);
    });

    // 监听表单变化，更新预览
    watch([
      () => minStockForm.minStock,
      () => minStockForm.warningLevel
    ], () => {
      updatePreview();
    });

    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      if (visible) {
        if (!props.data.productId) {
          resetForm();
        }
      }
    });

    // 重置表单
    const resetForm = () => {
      Object.assign(minStockForm, {
        productId: null,
        minStock: null,
        suggestRestock: null,
        warningLevel: 'MEDIUM',
        remark: ''
      });
      selectedProduct.value = {};
      warningPreview.show = false;
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    };

    // 商品变化事件
    const onProductChange = (productId, product) => {
      if (product) {
        selectedProduct.value = { ...product };
        minStockForm.minStock = product.minStock || 0;
        minStockForm.maxStock = product.maxStock || null;
        updatePreview();
      } else {
        selectedProduct.value = {};
        warningPreview.show = false;
      }
    };

    // 更新预警预览
    const updatePreview = () => {
      if (!selectedProduct.value.productId || minStockForm.minStock === null) {
        warningPreview.show = false;
        return;
      }

      const currentStock = parseFloat(selectedProduct.value.currentStock) || 0;
      const minStock = parseFloat(minStockForm.minStock) || 0;

      warningPreview.show = true;

      if (currentStock <= 0) {
        warningPreview.isWarning = true;
        warningPreview.statusColor = 'red';
        warningPreview.statusText = '库存不足';
        warningPreview.description = '当前库存为0，已达到缺货状态';
      } else if (currentStock <= minStock) {
        warningPreview.isWarning = true;
        warningPreview.statusColor = getWarningLevelColor(minStockForm.warningLevel);
        warningPreview.statusText = getWarningLevelText(minStockForm.warningLevel);
        warningPreview.description = `当前库存 ${currentStock} 低于预警值 ${minStock}，将触发${getWarningLevelText(minStockForm.warningLevel)}`;
      } else {
        warningPreview.isWarning = false;
        warningPreview.statusColor = 'green';
        warningPreview.statusText = '库存正常';
        warningPreview.description = `当前库存 ${currentStock} 高于预警值 ${minStock}，库存充足`;
      }
    };

    // 获取预警级别颜色
    const getWarningLevelColor = (level) => {
      const colorMap = {
        'LOW': 'orange',
        'MEDIUM': 'gold',
        'HIGH': 'red'
      };
      return colorMap[level] || 'orange';
    };

    // 获取预警级别文本
    const getWarningLevelText = (level) => {
      const textMap = {
        'LOW': '低级预警',
        'MEDIUM': '中级预警',
        'HIGH': '高级预警'
      };
      return textMap[level] || '预警';
    };

    // 获取精度
    const getPrecision = () => {
      return selectedProduct.value.pricingType === 'WEIGHT' ? 3 : 0;
    };

    // 获取步长
    const getStep = () => {
      return selectedProduct.value.pricingType === 'WEIGHT' ? 0.001 : 1;
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 获取库存样式类
    const getStockClass = (currentStock, minStock) => {
      const current = parseFloat(currentStock) || 0;
      const min = parseFloat(minStock) || 0;
      
      if (current <= 0) return 'stock-danger';
      if (current <= min) return 'stock-warning';
      return 'stock-normal';
    };

    // 取消
    const handleCancel = () => {
      // 重置表单
      if (formRef.value) {
        formRef.value.resetFields();
      }
      // 重置表单数据
      Object.assign(minStockForm, {
        productId: null,
        minStock: null,
        maxStock: null,
        suggestRestock: null,
        warningLevel: 'MEDIUM',
        remark: ''
      });
      emit('update:visible', false);
    };

    // 提交
    const handleSubmit = () => {
      formRef.value.validate().then(async () => {
        loading.value = true;
        try {
          const submitData = {
            productId: minStockForm.productId,
            minStock: minStockForm.minStock,
            maxStock: minStockForm.maxStock,
            suggestRestock: minStockForm.suggestRestock,
            warningLevel: minStockForm.warningLevel,
            remark: minStockForm.remark
          };

          await InventoryApi.setMinStock(submitData);
          
          message.success('预警值设置成功');
          emit('ok');
        } catch (error) {
          message.error('预警值设置失败：' + (error.message || '未知错误'));
        } finally {
          loading.value = false;
        }
      });
    };

    return {
      formRef,
      loading,
      selectedProduct,
      minStockForm,
      warningPreview,
      rules,
      onProductChange,
      getPrecision,
      getStep,
      getStockUnit,
      formatStock,
      getStockClass,
      handleCancel,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.set-min-stock-content {
  max-height: 70vh;
  overflow-y: auto;
}

.product-name {
  font-weight: 500;
  color: #1890ff;
}

.stock-normal {
  color: #52c41a;
  font-weight: 500;
}

.stock-warning {
  color: #faad14;
  font-weight: 500;
}

.stock-danger {
  color: #ff4d4f;
  font-weight: 500;
}
</style>
