package cn.stylefeng.roses.ent.mobile.invite.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 获取邀请界面的详情的请求bean
 *
 * <AUTHOR>
 * @since 2024-04-08 18:41
 */
@Data
public class SysInviteDetailRequest {

    /**
     * 邀请人的用户id
     */
    @ChineseDescription("邀请人的用户id")
    @NotNull(message = "邀请人的用户id不能为空")
    private Long userId;

    /**
     * 邀请加入的部门id
     */
    @ChineseDescription("邀请加入的部门id")
    private Long orgId;

}
