@import './variables.less';
@ym-captcha: ~'@{ym-prefix}captcha';
@ym-icon: ~'@{ym-prefix}icon';

.@{ym-captcha} {
    &-modal {
        position: absolute;
        display: block;
        transition: all @ym-anim-duration ease;
        transform-origin: left center;
        z-index: @ym-z-index-modal;

        &-content {
            .properties(width, 400);
			// position: absolute;
			// left: 0;
            // height: 300px;
			// .properties(top, -157);
            .border-radius();
            .linear-gradient-background();
			box-shadow: 0 0 10px var(--ym-dark, @ym-dark);
			border: 1px solid var(--ym-theme, @ym-theme);
        }

        &-wrap {
			position: relative;
			.properties(padding, 12);
        }
        
        &-embed {
			width: 100%;
			height: 0;
			position: relative;
			left: 0;
			top: 0;
			.properties(padding-bottom, 200);
			overflow: hidden;
		}

		&-loading,
		&-info {
			position: absolute;
        }

        &-loading {
			.properties(width, 374);
			.properties(height, 200);
			.flex();
			flex-direction: column;
			.border-radius();
			color: #fff;
			background: #636363;
            .properties(font-size, @ym-font-size-normal);

            &-spinner {
                .properties(width, 80);
                .properties(height, 80);
                display: inline-block;
                overflow: hidden;
                background: transparent;

                .load > div {
                    transform: scale(0.8);
                    transform-origin: 50px 50px;
                }
                
                .load > div > div {
                    animation: ym-anim-move 3s linear infinite;
                    position: absolute
                }
                
                .load > div > div div:nth-child(1) {
                    .properties(width, 36);
                    .properties(height, 36);
                    border-radius: 50%;
                    border: 6px solid var(--ym-theme, @ym-theme);
                    background: transparent;
                }
                
                .load > div > div div:nth-child(2) {
                    .properties(width, 8.5);
                    .properties(height, 25.5);
                    transform: rotate(-45deg);
                    background: var(--ym-theme, @ym-theme);
                    border-radius: 0 0 4px 4px;
                    position: absolute;
                    .properties(top, 34);
                    .properties(left, 42.5);
                }

                .load {
                    width: 100%;
                    height: 100%;
                    position: relative;
                    transform: translateZ(0) scale(0.8);
                    backface-visibility: hidden;
                    transform-origin: 0 0;
                }
                .load div {
                    box-sizing: content-box;
                }
            }
            
            &-tip {
                .properties(margin-top, 16);
                .letter-spacing(4);
            }
        }
        
        &-info {
			canvas {
				position: absolute;
				.border-radius();

				&:first-child {
					z-index: 100;
				}

				&:last-child {
					z-index: 200;
				}
			}
        }

        &-result {
			position: absolute;
			left: 0;
			.properties(bottom, -32);
			width: 100%;
			color: #fff;
			transition: bottom @ym-anim-duration ease;
			.flex(center, flex-start);
			.properties(text-indent, 12);
			.properties(font-size, 14);
            .properties(height, 32);
            z-index: 220;

			&-success {
				background: var(--ym-success, @ym-success);
			}

			&-error {
				background: var(--ym-danger, @ym-danger);
			}
        }

        &-slider {
            user-select: none;
            position: relative;
			.properties(margin-top, 16);
			.properties(margin-bottom);
            margin-right: 0;
            
            &-track {
                background: #fff;
				border: 1px solid var(--ym-theme, @ym-theme);
				.flex();
				.border-radius(20);
				.properties(height, 38);
				.properties(padding-left, 60);
				overflow: hidden;

				&-tip {
					width: 100%;
					.properties(height, 38);
					.properties(font-size, @ym-font-size-normal);
					color: #1d1e23;
					.flex(center, flex-start);
					flex-wrap: nowrap;
					opacity: 1;
                    transition: opacity .3s ease;
                    
                    &.hide {
                        opacity: 0;
                    }
				}
            }

            &-btn {
                position: absolute;
                .properties(top, -8);
				left: 0;
				.properties(width, 54);
				.properties(height, 54);
				.border-radius-circle();
				border: 1px solid var(--ym-theme, @ym-theme);
				background: #fff;
				.flex();
				cursor: pointer;
                box-shadow: 1px 1px 10px var(--ym-dark, @ym-dark);
                
                &-icon {
                    .properties(width, 28);
                    .properties(height, 24);
                    border: 2px solid var(--ym-dark, @ym-dark);
                    .border-radius();
                    position: relative;
                }

                &-vertical {
                    .properties(width, 8);
                    .properties(height, 32);
                    background: #fff;
                    position: absolute;
                    .properties(left);
                    .properties(top, -4);
                }

                &-horizontal {
                    .properties(width, 20);
                    .properties(height, 2);
                    background: var(--ym-dark, @ym-dark);
                    .border-radius();
                    position: absolute;
                    .properties(left, 2);
                    .properties(top, 11);
                    transition: all .5 ease;
                    animation: ym-anim-scan 1.5s linear infinite;
                    z-index: 20;
                }
			}
        }

        &-panel {
			position: relative;
            border-top: 1px solid fade(#1d1e23, 10%);
            box-shadow: 0 0 2px fade(#fff, 30%);
			.properties(height, 50);
			.properties(padding-left, 12);
			.properties(padding-right, 12);
			.flex(center, space-between);

			&-action{
                .flex();

                .@{ym-icon} {
                    color: #fff;
                    .properties(margin-right, 12);
                    cursor: pointer;

                    &:last-child {
                        margin-right: 0
                    }
                }

                .anticon {
                    .properties(font-size, 14);
                    .properties(width, 14);
                    .properties(height, 14);
                    .properties(margin-right, 16);
                    cursor: pointer;
                    color: #fff;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                > a {
                    .flex();
                    .properties(height, 14);
                }

                &-close {
                    .properties(width, 20);
                    .properties(height, 20);
                    background-color: #fff;
                    .border-radius-circle();
                    .flex();
                    transition: all .4s cubic-bezier(.215, .61, .355,1);
                    box-shadow: 0px 0px 30px 0px fade(#f6ca9d, 50%);

                    & > span {
                        background-color: var(--ym-theme, @ym-theme);
                        .properties(height, 12);
                        .border-radius(6);
                        position: relative;
                        transition: all @ym-anim-duration cubic-bezier(.215, .61, .355,1);
                        position: absolute;
                        top: 50%;
                        .properties(margin-top, -6);
                        .properties(left, 18);
                        .properties(width, 4);
                        .flex();
                    }
                }
			}
        }

        &&-error {
			animation-name: ym-anim-shake;
			animation-duration: @ym-anim-duration;
			animation-timing-function: ease-in-out;
			animation-iteration-count: 1.5;

			.@{ym-captcha}-modal {
				&-info canvas:last-child,
				&-slider-btn {
					transition: all @ym-anim-duration ease;
				}
			}
		}
        
        &-copyright {
			.properties(font-size, 12);
			.properties(line-height, 16);
            color: #fff;

			&-text {
				.flex();

				a {
					.properties(width, 20);
					.properties(height, 20);
					.properties(margin-right);
                    border: 1px solid var(--ym-theme, @ym-theme);
                    .border-radius-circle();
                    .linear-gradient-background();
                    overflow: hidden;

					img {
                        transform: scale(1.1);
						.border-radius-circle();
					}
				}
			}
        }

        &-tooltip {
            z-index: @ym-z-index-modal;

            .ant-tooltip {
                &-inner {
                    .border-radius(8);
                    .linear-gradient-background();
                    min-height: unset;
                }
            }
        }
    }
}

.@{ym-lang-en} {
    .@{ym-captcha} {
        &-modal {
            &-result {
                &-error {
                    .properties(bottom, -48);
                    .properties(height, 48);
                    .properties(padding-left, 8);
                    .properties(padding-right, 8);
                }
            }
        }
    }
}