System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./formatter-legacy-97503ee9.js","./constants-legacy-2a31d63c.js"],(function(e,a){"use strict";var t,r,d,m,l,c,i,s,n,o,b,f;return{setters:[e=>{t=e._,r=e.a,d=e.c,m=e.b,l=e.f,c=e.t,i=e.d,s=e.w,n=e.g,o=e.I,b=e.U},null,e=>{f=e.A},null],execute:function(){var a=document.createElement("style");a.textContent=".member-info-display[data-v-df3ec665]{padding:16px 0}.member-card[data-v-df3ec665]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:12px;padding:20px;color:#fff;box-shadow:0 8px 24px rgba(102,126,234,.3)}.member-basic[data-v-df3ec665]{display:flex;align-items:center;gap:16px;margin-bottom:20px}.member-avatar-large[data-v-df3ec665]{width:60px;height:60px;border-radius:50%;overflow:hidden;background:rgba(255,255,255,.2);display:flex;align-items:center;justify-content:center;flex-shrink:0;border:2px solid rgba(255,255,255,.3)}.member-avatar-large img[data-v-df3ec665]{width:100%;height:100%;object-fit:cover}.member-details[data-v-df3ec665]{flex:1}.member-name-large[data-v-df3ec665]{font-size:18px;font-weight:600;margin-bottom:6px}.member-level[data-v-df3ec665]{margin-bottom:6px}.member-phone[data-v-df3ec665],.member-card-no[data-v-df3ec665]{font-size:13px;opacity:.9;margin-bottom:2px}.member-balance-points[data-v-df3ec665]{display:grid;grid-template-columns:1fr 1fr;gap:16px}.balance-item[data-v-df3ec665],.points-item[data-v-df3ec665]{background:rgba(255,255,255,.15);border-radius:8px;padding:12px;text-align:center}.balance-label[data-v-df3ec665],.points-label[data-v-df3ec665]{font-size:12px;opacity:.8;margin-bottom:4px}.balance-value[data-v-df3ec665]{font-size:18px;font-weight:600;color:#52c41a}.points-value[data-v-df3ec665]{font-size:18px;font-weight:600;color:#faad14}\n",document.head.appendChild(a);const p={class:"member-info-display"},v={class:"member-card"},g={class:"member-basic"},u={class:"member-avatar-large"},x=["src","alt"],y={class:"member-details"},h={class:"member-name-large"},w={class:"member-level"},j={class:"member-phone"},k={class:"member-card-no"},I={class:"member-balance-points"},_={class:"balance-item"},z={class:"balance-value"},N={class:"points-item"},C={class:"points-value"};e("default",t(Object.assign({name:"MemberInfo"},{__name:"MemberInfo",props:{member:{type:Object,required:!0}},setup:e=>(a,t)=>{const M=o,O=b;return r(),d("div",p,[m("div",v,[m("div",g,[m("div",u,[e.member.avatar?(r(),d("img",{key:0,src:e.member.avatar,alt:e.member.memberName},null,8,x)):(r(),l(M,{key:1,iconClass:"icon-user-default"}))]),m("div",y,[m("div",h,c(e.member.memberName),1),m("div",w,[i(O,{color:(V=e.member.levelName,{"普通会员":"default","银卡会员":"#c0c0c0","金卡会员":"#ffd700","钻石会员":"#b9f2ff","VIP会员":"#f50"}[V]||"blue")},{default:s((()=>[n(c(e.member.levelName||"VIP会员"),1)])),_:1},8,["color"])]),m("div",j,c((S=e.member.phone,S?S.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):"")),1),m("div",k,"卡号: "+c(e.member.cardNo),1)])]),m("div",I,[m("div",_,[t[0]||(t[0]=m("div",{class:"balance-label"},"账户余额",-1)),m("div",z,"￥"+c((P=e.member.balance||0,f.formatCurrency(P,{showSymbol:!1}))),1)]),m("div",N,[t[1]||(t[1]=m("div",{class:"points-label"},"可用积分",-1)),m("div",C,c(e.member.points||0),1)])])])]);var P,S,V}}),[["__scopeId","data-v-df3ec665"]]))}}}));
