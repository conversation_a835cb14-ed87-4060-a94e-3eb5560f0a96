# 区域选择组件

区域选择组件是一个可复用的Vue 3组件，用于在表单中选择区域。该组件支持单选和多选模式、搜索过滤、树形结构展示、懒加载等功能，专门为供应商和客户管理中的区域关联功能设计。

## 功能特点

- 支持单选和多选模式
- 树形结构展示区域数据
- 支持搜索过滤
- 支持懒加载子节点
- 支持自定义API接口
- 支持数据回显
- 提供清空功能
- 支持禁用和只读状态
- 响应式设计，适配不同屏幕尺寸

## 使用方法

### 基本用法（多选）

```vue
<template>
  <div>
    <region-selector 
      v-model="selectedRegions" 
      placeholder="请选择区域"
      @change="handleChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RegionSelector from '@/components/common/RegionSelector/index.vue'

const selectedRegions = ref([])

const handleChange = (value, regions) => {
  console.log('选中的区域ID:', value)
  console.log('选中的区域数据:', regions)
}
</script>
```

### 单选模式

```vue
<template>
  <div>
    <region-selector 
      v-model="selectedRegion" 
      :multiple="false"
      placeholder="请选择区域"
      @change="handleChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RegionSelector from '@/components/common/RegionSelector/index.vue'

const selectedRegion = ref(null)

const handleChange = (value, region) => {
  console.log('选中的区域ID:', value)
  console.log('选中的区域数据:', region)
}
</script>
```

### 禁用搜索功能

```vue
<template>
  <div>
    <region-selector 
      v-model="selectedRegions" 
      :showSearch="false"
      placeholder="请选择区域"
    />
  </div>
</template>
```

### 只读和禁用状态

```vue
<template>
  <div>
    <!-- 只读状态 -->
    <region-selector 
      v-model="selectedRegions" 
      readonly
      placeholder="只读状态"
    />
    
    <!-- 禁用状态 -->
    <region-selector 
      v-model="selectedRegions" 
      disabled
      placeholder="禁用状态"
    />
  </div>
</template>
```

### 使用自定义API

```vue
<template>
  <div>
    <region-selector 
      v-model="selectedRegions" 
      :customApi="customTreeApi"
      :customLazyApi="customLazyApi"
      :extraParams="{ type: 'supplier' }"
      placeholder="请选择区域"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RegionSelector from '@/components/common/RegionSelector/index.vue'
import { SupplierApi } from '@/api/supplierApi'

const selectedRegions = ref([])

// 自定义树数据API
const customTreeApi = (params) => {
  return SupplierApi.getRegionTree(params)
}

// 自定义懒加载API
const customLazyApi = (params) => {
  return SupplierApi.getRegionChildren(params)
}
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | Array/String/Number | [] | v-model绑定的值，多选时为数组，单选时为字符串或数字 |
| multiple | Boolean | true | 是否支持多选 |
| placeholder | String | '请选择区域' | 输入框占位符 |
| disabled | Boolean | false | 是否禁用 |
| readonly | Boolean | false | 是否只读 |
| clearable | Boolean | true | 是否可清空 |
| showSearch | Boolean | true | 是否显示搜索框 |
| searchPlaceholder | String | '搜索区域名称' | 搜索框占位符 |
| emptyText | String | '暂无区域数据' | 空数据提示文本 |
| showActionButtons | Boolean | false | 是否显示操作按钮（确定/取消） |
| checkStrictly | Boolean | false | 父子节点选中状态是否不再关联 |
| customApi | Function | null | 自定义API方法，用于获取树数据 |
| customLazyApi | Function | null | 自定义懒加载API方法 |
| extraParams | Object | {} | 额外的查询参数 |
| fieldNames | Object | 见下方 | 字段名映射 |

### fieldNames 默认值

```javascript
{
  children: 'children',
  title: 'regionName',
  key: 'regionId',
  value: 'regionId'
}
```

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value) | v-model更新事件 |
| change | (value, regions) | 选中值变化时触发 |
| search | (searchText) | 搜索时触发 |
| clear | - | 清空选择时触发 |

## 组件方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| clearSelection | - | - | 清空选中的区域 |
| getSelectedRegions | - | Array | 获取当前选中的区域数据 |
| reloadData | - | - | 重新加载树数据 |

## 使用示例

### 在表单中使用

```vue
<template>
  <a-form :model="form" layout="vertical">
    <a-form-item label="关联区域" name="regionIds">
      <region-selector 
        v-model="form.regionIds" 
        placeholder="请选择关联的区域"
        @change="handleRegionChange"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue'
import RegionSelector from '@/components/common/RegionSelector/index.vue'

const form = ref({
  regionIds: []
})

const handleRegionChange = (value, regions) => {
  console.log('表单中的区域选择变化:', value, regions)
}
</script>
```

### 获取组件实例并调用方法

```vue
<template>
  <div>
    <region-selector 
      ref="regionSelectorRef"
      v-model="selectedRegions" 
    />
    <a-button @click="clearRegions">清空选择</a-button>
    <a-button @click="getSelected">获取选中</a-button>
    <a-button @click="reloadData">重新加载</a-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RegionSelector from '@/components/common/RegionSelector/index.vue'

const regionSelectorRef = ref()
const selectedRegions = ref([])

const clearRegions = () => {
  regionSelectorRef.value.clearSelection()
}

const getSelected = () => {
  const selected = regionSelectorRef.value.getSelectedRegions()
  console.log('当前选中的区域:', selected)
}

const reloadData = () => {
  regionSelectorRef.value.reloadData()
}
</script>
```

## 注意事项

1. 组件依赖于 `@/views/erp/region/api/regionApi` 中的API接口
2. 默认使用 `RegionApi.findTree`、`RegionApi.findTreeWithLazy` 和 `RegionApi.findSelector` 方法
3. 可以通过 `customApi` 和 `customLazyApi` 属性自定义API接口
4. 组件支持响应式设计，会根据容器大小自动调整
5. 在表单验证中使用时，建议配合 Ant Design Vue 的表单验证规则
6. 组件会自动处理点击外部关闭下拉框的交互
7. 支持键盘导航和无障碍访问
8. 数据回显功能会优先使用API获取区域信息，确保数据准确性

## 样式定制

组件提供了基本的样式，如需定制可以通过以下方式：

```vue
<style>
/* 自定义输入框样式 */
.region-selector .region-selector-input {
  border-color: #1890ff;
}

/* 自定义下拉面板样式 */
.region-selector-dropdown {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 自定义树节点样式 */
.region-selector-dropdown .ant-tree-node-content-wrapper {
  border-radius: 4px;
}
</style>
```
