package cn.stylefeng.roses.kernel.license.api;

import cn.stylefeng.roses.kernel.license.api.pojo.SecretDTO;

/**
 * license相关操作
 *
 * <AUTHOR>
 * @date 2021/7/8 12:00
 */
public interface LicenseApi {

    /**
     * 创建系统需要的加密解密的秘钥信息
     *
     * <AUTHOR>
     * @date 2021/7/8 12:05
     */
    SecretDTO createSecret();

    /**
     * 创建license证书的字符串
     *
     * @param secretDTO 秘钥信息
     * @param content   机器信息的json字符串
     * <AUTHOR>
     * @date 2021/7/8 13:50
     */
    String createLicenseStr(SecretDTO secretDTO, String content);

    /**
     * 校验license是否正确
     *
     * <AUTHOR>
     * @date 2021/7/8 12:00
     */
    boolean validateLicenseStr(SecretDTO secretDTO, String license);

    /**
     * 解密license文件的内容
     *
     * <AUTHOR>
     * @date 2021/7/8 13:53
     */
    String decryptLicense(SecretDTO secretDTO, String license);

}
