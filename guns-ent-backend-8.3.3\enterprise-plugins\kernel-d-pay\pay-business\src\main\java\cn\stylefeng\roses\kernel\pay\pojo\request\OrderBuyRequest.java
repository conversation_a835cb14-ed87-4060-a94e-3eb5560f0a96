package cn.stylefeng.roses.kernel.pay.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 购买商品的请求
 *
 * <AUTHOR>
 * @since 2024/5/26 0:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderBuyRequest extends BaseRequest {

    /**
     * 商品id
     */
    @ChineseDescription("商品id")
    private Long goodsId;

}