package cn.stylefeng.roses.kernel.apiauth.api.format;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.apiauth.api.CaApiClientApi;
import cn.stylefeng.roses.kernel.rule.format.BaseSimpleFieldFormatProcess;

/**
 * Json响应格式化，针对客户端id的渲染
 *
 * <AUTHOR>
 * @date 2022/9/7 10:09
 */
public class CaApiClientProcess extends BaseSimpleFieldFormatProcess {

    @Override
    public Class<?> getItemClass() {
        return Long.class;
    }

    @Override
    public Object simpleItemFormat(Object businessId) {
        CaApiClientApi caApiClientApi = SpringUtil.getBean(CaApiClientApi.class);
        return caApiClientApi.getCaApiClientName(Convert.toLong(businessId));
    }

}
