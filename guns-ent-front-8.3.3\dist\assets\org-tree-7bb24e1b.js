import{_ as W,r as c,o as A,k as q,a as l,c as h,b as r,f as S,h as f,d as s,w as u,I as d,aR as L,t as b,aM as I,aS as E,M as G,E as H,m as J,l as Q,n as X,bg as Y,a5 as Z,S as ee}from"./index-18a1ea24.js";/* empty css              */import{O as y}from"./OrgApi-021dd6dd.js";const te={class:"box bgColor box-shadow"},oe={key:0,class:"left-header"},se={class:"search"},ae={class:"tree-content"},ne={class:"left-tree"},le={key:0,class:"tree-edit"},re=["title"],ie={class:"edit-icon"},ce=["title"],de={__name:"org-tree",props:{isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["treeSelect","treeData","addOrg","editOrg","deleteOrg"],setup(m,{expose:N,emit:D}){const B=m,p=D,i=c(""),n=c(!1),a=c([]),x=c([]),g=c([]),_=c([]);A(()=>{k()});const k=()=>{n.value=!0,y.tree({searchText:i.value}).then(t=>{i.value&&(g.value=t.data.expandOrgIdList);const e=v(t.data.orgTreeList);a.value=e}).finally(()=>n.value=!1)},M=(t,e)=>{p("treeSelect",t,e)},U=()=>{_.value=[],i.value||k()},K=t=>{p("addOrg",t)},$=t=>{p("editOrg",t)},P=t=>{G.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5417?",icon:s(H),maskClosable:!0,onOk:()=>{n.value=!0,y.delete({orgId:t.orgId}).then(e=>{J.success(e.message),T(),p("deleteOrg",t)}).finally(()=>n.value=!1)}})},R=t=>(_.value.push(t.eventKey),new Promise(e=>{y.tree({orgParentId:t.dataRef.orgId}).then(C=>{const O=v(C.data.orgTreeList);t.dataRef.children=O,a.value=[...a.value]}).finally(()=>{e()})})),T=()=>{n.value=!0,y.tree({searchText:i.value,indexOrgIdList:g.value}).then(t=>{const e=v(t.data.orgTreeList);a.value=[...e]}).finally(()=>n.value=!1)},v=t=>(t&&t.length>0?t.forEach(e=>{e.haveSubOrgFlag?e.isLeaf=!1:e.isLeaf=!0,e.children&&e.children.length>0&&(e.children=v(e.children))}):t=[],t);return N({currentSelectKeys:x,reloadOrgTreeData:T}),(t,e)=>{const C=q("plus-outlined"),O=Q,V=X,j=Y,z=Z,F=ee;return l(),h("div",te,[B.isSetWidth?(l(),h("div",oe,[e[5]||(e[5]=r("span",{class:"left-header-title"},"\u6240\u5C5E\u7EC4\u7EC7",-1)),r("span",null,[m.isShowEditIcon?(l(),S(C,{key:0,class:"header-add",onClick:e[0]||(e[0]=o=>K())})):f("",!0)])])):f("",!0),r("div",se,[s(O,{value:i.value,"onUpdate:value":e[1]||(e[1]=o=>i.value=o),placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22","allow-clear":"",onPressEnter:k,onChange:U},{prefix:u(()=>[s(d,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),r("div",ae,[s(F,{tip:"Loading...",spinning:n.value,delay:100},{default:u(()=>[L(r("div",ne,[s(j,{"show-icon":!0,selectedKeys:x.value,"onUpdate:selectedKeys":e[2]||(e[2]=o=>x.value=o),expandedKeys:g.value,"onUpdate:expandedKeys":e[3]||(e[3]=o=>g.value=o),loadedKeys:_.value,"onUpdate:loadedKeys":e[4]||(e[4]=o=>_.value=o),onSelect:M,"load-data":R,"tree-data":a.value,fieldNames:{children:"children",title:"orgName",key:"orgId",value:"orgId"}},{icon:u(o=>[o.orgType==1?(l(),S(d,{key:0,"icon-class":"icon-nav-gongsi",color:"#43505e",fontSize:"24px"})):f("",!0),o.orgType==2?(l(),S(d,{key:1,"icon-class":"icon-tree-dept",color:"#43505e",fontSize:"24px"})):f("",!0)]),title:u(o=>[m.isShowEditIcon?(l(),h("span",le,[r("span",{class:"edit-title",title:o.orgName},b(o.orgName),9,re),r("span",ie,[s(V,null,{default:u(()=>[s(d,{iconClass:"icon-opt-tianjia",color:"var(--primary-color)",onClick:I(w=>K(o),["stop"])},null,8,["onClick"]),s(d,{iconClass:"icon-opt-bianji",color:"var(--primary-color)",onClick:I(w=>$(o),["stop"])},null,8,["onClick"]),s(d,{iconClass:"icon-opt-shanchu",color:"red",onClick:I(w=>P(o),["stop"])},null,8,["onClick"])]),_:2},1024)])])):(l(),h("span",{key:1,class:"tree-title",title:o.orgName},b(o.orgName),9,ce))]),_:1},8,["selectedKeys","expandedKeys","loadedKeys","tree-data"])],512),[[E,a.value&&a.value.length>0]]),L(s(z,{class:"empty"},null,512),[[E,a.value&&a.value.length==0]])]),_:1},8,["spinning"])])])}}},_e=W(de,[["__scopeId","data-v-2b14d4a7"]]);export{_e as default};
