package cn.stylefeng.roses.kernel.pay.api.pojo;

import lombok.Data;

/**
 * 支付相关的配置
 *
 * <AUTHOR>
 * @date 2022/4/19 12:02
 */
@Data
public class YunGouOSConfig {

    /**
     * 微信商户id
     */
    private String wxShopId;

    /**
     * 支付宝商户id
     */
    private String aliShopId;

    /**
     * 聚合支付的商户id
     */
    private String mergePayShopId;

    /**
     * 聚合支付的url
     */
    private String mergePayUrl = "https://api.pay.yungouos.com/api/pay/merge/nativePay";

    /**
     * 聚合支付回调地址（异步回调）
     */
    private String mergePayNotifyUrl;

    /**
     * 聚合支付成功跳转地址
     */
    private String mergePayReturnUrl;

    /**
     * 聚合支付秘钥
     */
    private String mergePaySecret;

    /**
     * 支付宝商户密钥
     */
    private String payAliKey;

    /**
     * 微信商户密钥
     */
    private String payWxKey;

}
