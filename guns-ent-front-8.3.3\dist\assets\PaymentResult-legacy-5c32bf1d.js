System.register(["./index-legacy-ee1db0c7.js"],(function(e,a){"use strict";var t,n,c,s,o,l,i,r,d,p,f,u,m,y,v,b,g,x,I,h,w,k,_,C,z;return{setters:[e=>{t=e._,n=e.r,c=e.L,s=e.a,o=e.c,l=e.b,i=e.a2,r=e.f,d=e.at,p=e.aj,f=e.aN,u=e.E,m=e.b0,y=e.t,v=e.m,b=e.h,g=e.d,x=e.I,I=e.F,h=e.w,w=e.g,k=e.ae,_=e.ad,C=e.au,z=e.B}],execute:function(){var a=document.createElement("style");a.textContent=".payment-result[data-v-e3cb8c52]{padding:24px;background:#fff;text-align:center}.result-header[data-v-e3cb8c52]{margin-bottom:24px}.result-icon[data-v-e3cb8c52]{font-size:64px;margin-bottom:16px}.result-icon.success[data-v-e3cb8c52]{color:#52c41a}.result-icon.failed[data-v-e3cb8c52]{color:#ff4d4f}.result-icon.cancelled[data-v-e3cb8c52]{color:#faad14}.result-icon.processing[data-v-e3cb8c52]{color:#1890ff}.result-title[data-v-e3cb8c52]{font-size:20px;font-weight:600;color:#262626;margin-bottom:8px}.result-message[data-v-e3cb8c52]{font-size:14px;color:#8c8c8c;line-height:1.5}.payment-details[data-v-e3cb8c52]{margin-bottom:24px;text-align:left}.details-title[data-v-e3cb8c52]{display:flex;align-items:center;gap:6px;margin-bottom:16px;font-size:16px;font-weight:600;color:#262626}.details-content[data-v-e3cb8c52]{background:#fafafa;border-radius:8px;padding:16px}.detail-row[data-v-e3cb8c52]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;font-size:14px}.detail-row[data-v-e3cb8c52]:last-child{margin-bottom:0}.detail-label[data-v-e3cb8c52]{color:#595959;min-width:80px}.detail-value[data-v-e3cb8c52]{color:#262626;font-weight:500}.detail-value.amount[data-v-e3cb8c52]{color:#ff4d4f;font-size:16px;font-weight:600}.detail-value.change[data-v-e3cb8c52]{color:#52c41a;font-weight:600}.transaction-id[data-v-e3cb8c52]{font-family:Courier New,monospace;font-size:12px}.error-details[data-v-e3cb8c52]{margin-bottom:24px;text-align:left}.error-title[data-v-e3cb8c52]{display:flex;align-items:center;gap:6px;margin-bottom:16px;font-size:16px;font-weight:600;color:#ff4d4f}.error-content[data-v-e3cb8c52]{background:#fff2f0;border-radius:8px;padding:16px;border-left:4px solid #ff4d4f}.error-code[data-v-e3cb8c52]{font-size:12px;color:#8c8c8c;margin-bottom:8px;font-family:Courier New,monospace}.error-message[data-v-e3cb8c52]{font-size:14px;color:#ff4d4f;font-weight:500}.result-actions[data-v-e3cb8c52]{display:flex;gap:12px;justify-content:center}.action-btn[data-v-e3cb8c52]{min-width:120px;height:44px;font-size:14px;font-weight:500}@media (max-width: 768px){.payment-result[data-v-e3cb8c52]{padding:20px 16px}.result-icon[data-v-e3cb8c52]{font-size:48px;margin-bottom:12px}.result-title[data-v-e3cb8c52]{font-size:18px}.result-actions[data-v-e3cb8c52]{flex-direction:column;gap:8px}.action-btn[data-v-e3cb8c52]{width:100%;height:40px}}.result-icon[data-v-e3cb8c52]{animation:bounceIn-e3cb8c52 .6s ease-out}@keyframes bounceIn-e3cb8c52{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}to{opacity:1;transform:scale(1)}}\n",document.head.appendChild(a);const A={class:"payment-result"},j={class:"result-header"},P={class:"result-title"},S={key:0,class:"result-message"},B={key:0,class:"payment-details"},N={class:"details-title"},E={class:"details-content"},L={class:"detail-row"},O={class:"detail-value"},R={class:"detail-row"},T={class:"detail-value amount"},D={key:0,class:"detail-row"},F={class:"detail-value"},H={key:1,class:"detail-row"},q={class:"detail-value change"},M={key:2,class:"detail-row"},W={class:"detail-value transaction-id"},Y={key:3,class:"detail-row"},G={class:"detail-value"},J={key:1,class:"error-details"},K={class:"error-title"},Q={class:"error-content"},U={key:0,class:"error-code"},V={class:"error-message"},X={class:"result-actions"},Z=Object.assign({name:"PaymentResult"},{__name:"PaymentResult",props:{status:{type:String,required:!0,validator:e=>["success","failed","cancelled","processing"].includes(e)},paymentInfo:{type:Object,default:null},errorInfo:{type:Object,default:null},message:{type:String,default:""},showPrintButton:{type:Boolean,default:!0}},emits:["complete","retry","cancel","print"],setup(e,{emit:a}){const t=e,Z=a,$=n(!1),ee=n(!1),ae=c((()=>({success:"success"===t.status,failed:"failed"===t.status,cancelled:"cancelled"===t.status,processing:"processing"===t.status}))),te=c((()=>{switch(t.status){case"success":return"支付成功";case"failed":return"支付失败";case"cancelled":return"支付已取消";case"processing":return"支付处理中";default:return"未知状态"}})),ne=e=>(e||0).toFixed(2),ce=e=>e?new Date(e).toLocaleString("zh-CN"):"",se=()=>{Z("complete",{paymentInfo:t.paymentInfo})},oe=async()=>{ee.value=!0;try{Z("retry",{paymentInfo:t.paymentInfo,errorInfo:t.errorInfo})}finally{ee.value=!1}},le=()=>{Z("cancel",{paymentInfo:t.paymentInfo})},ie=async()=>{$.value=!0;try{Z("print",{paymentInfo:t.paymentInfo}),v.success("小票打印中...")}finally{$.value=!1}};return(a,t)=>{const n=z;return s(),o("div",A,[l("div",j,[l("div",{class:i(["result-icon",ae.value])},["success"===e.status?(s(),r(d(p),{key:0})):"failed"===e.status?(s(),r(d(f),{key:1})):"cancelled"===e.status?(s(),r(d(u),{key:2})):(s(),r(d(m),{key:3}))],2),l("div",P,y(te.value),1),d(v)?(s(),o("div",S,y(d(v)),1)):b("",!0)]),e.paymentInfo?(s(),o("div",B,[l("div",N,[g(x,{iconClass:"icon-detail"}),t[0]||(t[0]=l("span",null,"支付详情",-1))]),l("div",E,[l("div",L,[t[1]||(t[1]=l("span",{class:"detail-label"},"支付方式:",-1)),l("span",O,y((c=e.paymentInfo.paymentMethod,{CASH:"现金支付",WECHAT:"微信支付",ALIPAY:"支付宝",CARD:"银行卡"}[c]||c)),1)]),l("div",R,[t[2]||(t[2]=l("span",{class:"detail-label"},"支付金额:",-1)),l("span",T,"¥"+y(ne(e.paymentInfo.amount)),1)]),e.paymentInfo.receivedAmount?(s(),o("div",D,[t[3]||(t[3]=l("span",{class:"detail-label"},"实收金额:",-1)),l("span",F,"¥"+y(ne(e.paymentInfo.receivedAmount)),1)])):b("",!0),e.paymentInfo.changeAmount&&e.paymentInfo.changeAmount>0?(s(),o("div",H,[t[4]||(t[4]=l("span",{class:"detail-label"},"找零金额:",-1)),l("span",q,"¥"+y(ne(e.paymentInfo.changeAmount)),1)])):b("",!0),e.paymentInfo.transactionId?(s(),o("div",M,[t[5]||(t[5]=l("span",{class:"detail-label"},"交易流水:",-1)),l("span",W,y(e.paymentInfo.transactionId),1)])):b("",!0),e.paymentInfo.paymentTime?(s(),o("div",Y,[t[6]||(t[6]=l("span",{class:"detail-label"},"支付时间:",-1)),l("span",G,y(ce(e.paymentInfo.paymentTime)),1)])):b("",!0)])])):b("",!0),"failed"===e.status&&e.errorInfo?(s(),o("div",J,[l("div",K,[g(x,{iconClass:"icon-error"}),t[7]||(t[7]=l("span",null,"错误详情",-1))]),l("div",Q,[e.errorInfo.code?(s(),o("div",U," 错误代码: "+y(e.errorInfo.code),1)):b("",!0),l("div",V,y(e.errorInfo.message||"支付失败，请重试"),1)])])):b("",!0),l("div",X,["success"===e.status?(s(),o(I,{key:0},[e.showPrintButton?(s(),r(n,{key:0,onClick:ie,loading:$.value,class:"action-btn"},{icon:h((()=>[g(x,{iconClass:"icon-print"})])),default:h((()=>[t[8]||(t[8]=w(" 打印小票 "))])),_:1,__:[8]},8,["loading"])):b("",!0),g(n,{type:"primary",onClick:se,class:"action-btn"},{icon:h((()=>[g(d(k))])),default:h((()=>[t[9]||(t[9]=w(" 完成 "))])),_:1,__:[9]})],64)):"failed"===e.status?(s(),o(I,{key:1},[g(n,{onClick:oe,loading:ee.value,class:"action-btn"},{icon:h((()=>[g(d(_))])),default:h((()=>[t[10]||(t[10]=w(" 重试支付 "))])),_:1,__:[10]},8,["loading"]),g(n,{onClick:le,class:"action-btn"},{icon:h((()=>[g(d(C))])),default:h((()=>[t[11]||(t[11]=w(" 取消订单 "))])),_:1,__:[11]})],64)):(s(),r(n,{key:2,onClick:le,class:"action-btn"},{icon:h((()=>[g(d(C))])),default:h((()=>[t[12]||(t[12]=w(" 关闭 "))])),_:1,__:[12]}))])]);var c}}});e("default",t(Z,[["__scopeId","data-v-e3cb8c52"]]))}}}));
