package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRuleRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRuleResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.InventoryAlertCheckService;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.InventoryAlertRuleService;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 库存预警规则管理控制器
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@RestController
@ApiResource(name = "库存预警规则管理", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
public class InventoryAlertRuleController {

    @Resource
    private InventoryAlertRuleService inventoryAlertRuleService;

    @Resource
    private InventoryAlertCheckService inventoryAlertCheckService;

    /**
     * 新增预警规则
     */
    @PostResource(name = "新增预警规则", path = "/erp/inventoryAlert/rule/add", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.ADD_INVENTORY_ALERT_RULE)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ADD_INVENTORY_ALERT_RULE)
    public ResponseData<?> add(@RequestBody @Validated(InventoryAlertRuleRequest.add.class) 
                              InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.add(request);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑预警规则
     */
    @PostResource(name = "编辑预警规则", path = "/erp/inventoryAlert/rule/edit", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.EDIT_INVENTORY_ALERT_RULE)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.EDIT_INVENTORY_ALERT_RULE)
    public ResponseData<?> edit(@RequestBody @Validated(InventoryAlertRuleRequest.edit.class) 
                               InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.edit(request);
        return new SuccessResponseData<>();
    }

    /**
     * 删除预警规则
     */
    @PostResource(name = "删除预警规则", path = "/erp/inventoryAlert/rule/delete", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.DELETE_INVENTORY_ALERT_RULE)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.DELETE_INVENTORY_ALERT_RULE)
    public ResponseData<?> delete(@RequestBody @Validated(InventoryAlertRuleRequest.delete.class) 
                                 InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.del(request);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除预警规则
     */
    @PostResource(name = "批量删除预警规则", path = "/erp/inventoryAlert/rule/batchDelete", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.BATCH_DELETE_INVENTORY_ALERT_RULE)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.BATCH_DELETE_INVENTORY_ALERT_RULE)
    public ResponseData<?> batchDelete(@RequestBody @Validated(InventoryAlertRuleRequest.batchDelete.class) 
                                      InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.batchDelete(request);
        return new SuccessResponseData<>();
    }

    /**
     * 查询预警规则详情
     */
    @GetResource(name = "查询预警规则详情", path = "/erp/inventoryAlert/rule/detail", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.VIEW_INVENTORY_ALERT_RULE_DETAIL)
    public ResponseData<InventoryAlertRuleResponse> detail(@Validated(InventoryAlertRuleRequest.detail.class) 
                                                          InventoryAlertRuleRequest request) {
        InventoryAlertRuleResponse response = inventoryAlertRuleService.detail(request);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询预警规则
     */
    @GetResource(name = "分页查询预警规则", path = "/erp/inventoryAlert/rule/page", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.PAGE_INVENTORY_ALERT_RULE)
    public ResponseData<PageResult<InventoryAlertRuleResponse>> page(InventoryAlertRuleRequest request) {
        PageResult<InventoryAlertRuleResponse> result = inventoryAlertRuleService.findPage(request);
        return new SuccessResponseData<>(result);
    }

    /**
     * 更新预警规则状态
     */
    @PostResource(name = "更新预警规则状态", path = "/erp/inventoryAlert/rule/updateStatus", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.UPDATE_INVENTORY_ALERT_RULE_STATUS)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.UPDATE_INVENTORY_ALERT_RULE_STATUS)
    public ResponseData<?> updateStatus(@RequestBody @Validated(InventoryAlertRuleRequest.updateStatus.class) 
                                       InventoryAlertRuleRequest request) {
        inventoryAlertRuleService.updateStatus(request);
        return new SuccessResponseData<>();
    }

    /**
     * 测试预警规则
     */
    @PostResource(name = "测试预警规则", path = "/erp/inventoryAlert/rule/test", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.TEST_INVENTORY_ALERT_RULE)
    public ResponseData<List<Object>> testRule(@RequestBody @Validated(InventoryAlertRuleRequest.detail.class) 
                                              InventoryAlertRuleRequest request) {
        List<Object> result = inventoryAlertRuleService.testRule(request);
        return new SuccessResponseData<>(result);
    }

    /**
     * 手动执行预警检查
     */
    @PostResource(name = "手动执行预警检查", path = "/erp/inventoryAlert/rule/executeCheck", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.EXECUTE_INVENTORY_ALERT_CHECK)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.EXECUTE_INVENTORY_ALERT_CHECK)
    public ResponseData<?> executeCheck() {
        inventoryAlertCheckService.executeAlertCheck();
        return new SuccessResponseData<>();
    }

    /**
     * 查询预警规则列表（不分页）
     */
    @GetResource(name = "查询预警规则列表", path = "/erp/inventoryAlert/rule/list")
    public ResponseData<List<InventoryAlertRuleResponse>> list(InventoryAlertRuleRequest request) {
        // 设置大的分页参数来获取所有数据
        request.setPageNo(1);
        request.setPageSize(1000);
        PageResult<InventoryAlertRuleResponse> pageResult = inventoryAlertRuleService.findPage(request);
        return new SuccessResponseData<>(pageResult.getRows());
    }
}
