package cn.stylefeng.roses.kernel.ca.server.modular.sso.controller;

import cn.stylefeng.roses.kernel.apiauth.api.annotations.ApiAuth;
import cn.stylefeng.roses.kernel.ca.api.SsoExternalApi;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.request.*;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.response.SsoExternalCreateSessionResponse;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.response.SsoExternalDetectionResponse;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外部单点登录控制器
 * <p>
 * 给其他系统调用的一套api接口，进行本单点服务会话的相关操作
 *
 * <AUTHOR>
 * @date 2021/2/20 16:14
 */
@RestController
@ApiResource(name = "SSO外部单点登录控制器")
public class SsoExternalApiController {

    @Resource
    private SsoExternalApi ssoExternalApi;

    /**
     * 远程根据ca token获取是否有对应的单点登录会话
     * <p>
     * 如果存在对应会话，则返回用户的信息
     *
     * <AUTHOR>
     * @date 2021/2/20 16:20
     */
    @PostResource(name = "远程CA_TOKEN会话检测", path = "/external/api/sso/tokenDetection", requiredLogin = false)
    @ApiAuth
    public ResponseData<SsoExternalDetectionResponse> tokenDetection(
            @Valid @RequestBody SsoExternalDetectionRequest ssoExternalDetectionRequest) {
        SsoExternalDetectionResponse ssoExternalDetectionResponse = ssoExternalApi.tokenDetection(ssoExternalDetectionRequest);
        return new SuccessResponseData<>(ssoExternalDetectionResponse);
    }

    /**
     * 远程根据ca token踢掉登录用户
     *
     * <AUTHOR>
     * @date 2021/2/20 16:47
     */
    @PostResource(name = "远程踢掉单点登录用户", path = "/external/api/sso/kickOff", requiredLogin = false)
    @ApiAuth
    public ResponseData<?> kickOff(@Valid @RequestBody SsoExternalKickOffRequest ssoExternalKickOffRequest) {
        ssoExternalApi.kickOff(ssoExternalKickOffRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 远程创建sso会话（只创建会话，不写cookie）
     *
     * <AUTHOR>
     * @date 2021/2/20 16:47
     */
    @PostResource(name = "远程创建sso会话", path = "/external/api/sso/createSession", requiredLogin = false)
    @ApiAuth
    public ResponseData<SsoExternalCreateSessionResponse> createSession(
            @Valid @RequestBody SsoExternalCreateSessionRequest ssoExternalCreateSessionRequest) {
        SsoExternalCreateSessionResponse ssoExternalCreateSessionResponse = ssoExternalApi.createSession(ssoExternalCreateSessionRequest);
        return new SuccessResponseData<>(ssoExternalCreateSessionResponse);
    }

    /**
     * 刷新单点sso的会话（更新公司和部门）
     *
     * <AUTHOR>
     * @date 2021/3/8 11:29
     */
    @PostResource(name = "刷新单点sso的会话", path = "/external/api/sso/refreshSession", requiredLogin = false)
    @ApiAuth
    public ResponseData<?> refreshSession(@Valid @RequestBody SsoExternalRefreshSessionRequest ssoExternalRefreshSessionRequest) {
        ssoExternalApi.refreshSession(ssoExternalRefreshSessionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 根据帐号ID强迫当前帐号下线
     *
     * <AUTHOR>
     * @date 2021-4-2 16:51
     */
    @PostResource(name = "根据帐号ID强迫当前帐号下线", path = "/external/api/sso/offlineCaByAccountId", requiredLogin = false)
    @ApiAuth
    public ResponseData<?> offlineCaByAccountId(@Valid @RequestBody SsoExternalAccountRequest ssoExternalAccountRequest) {
        ssoExternalApi.offlineCaByAccountId(ssoExternalAccountRequest);
        return new SuccessResponseData<>();
    }

}
