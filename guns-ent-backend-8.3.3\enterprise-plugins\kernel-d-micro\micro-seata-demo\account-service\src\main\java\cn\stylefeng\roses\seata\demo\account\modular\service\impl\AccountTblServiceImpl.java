package cn.stylefeng.roses.seata.demo.account.modular.service.impl;

import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.seata.demo.account.modular.entity.AccountTbl;
import cn.stylefeng.roses.seata.demo.account.modular.mapper.AccountTblMapper;
import cn.stylefeng.roses.seata.demo.account.modular.service.AccountTblService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 业务实现层
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
@Service
public class AccountTblServiceImpl extends ServiceImpl<AccountTblMapper, AccountTbl> implements AccountTblService {

    @Override
    public void updateMoney(String userId, int subMoney) {
        // 获取当前用户信息
        LambdaQueryWrapper<AccountTbl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountTbl::getUserId, userId);
        AccountTbl userAccount = this.getOne(queryWrapper, false);

        // 用户信息为空
        if (userAccount == null) {
            throw new ServiceException("account", "B0011", "用户账户信息为空，userId不正确");
        }

        // 扣减用户余额
        userAccount.setMoney(userAccount.getMoney() - subMoney);

        // 更新用户余额
        this.updateById(userAccount);
    }

}