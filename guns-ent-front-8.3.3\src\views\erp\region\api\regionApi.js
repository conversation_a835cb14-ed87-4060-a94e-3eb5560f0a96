import Request from '@/utils/request/request-util';

/**
 * 区域管理API
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
export class RegionApi {

  /**
   * 查询区域分页
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async findPage(parameter) {
    return await Request.getAndLoadData('/erp/region/page', parameter);
  }

  /**
   * 查询区域列表
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async findList(parameter) {
    return await Request.getAndLoadData('/erp/region/list', parameter);
  }

  /**
   * 查询区域树形结构
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async findTree(parameter) {
    return await Request.getAndLoadData('/erp/region/tree', parameter);
  }

  /**
   * 查询区域列表（按父级ID）
   *
   * <AUTHOR>
   * @date 2025/07/21 13:00
   */
  static async findByParentId(parameter) {
    return await Request.getAndLoadData('/erp/region/listByParent', parameter);
  }

  /**
   * 获取区域树形结构（支持懒加载）
   *
   * <AUTHOR>
   * @date 2025/07/21 13:00
   */
  static async findTreeWithLazy(parameter) {
    return await Request.getAndLoadData('/erp/region/treeWithLazy', parameter);
  }

  /**
   * 批量删除区域
   *
   * <AUTHOR>
   * @date 2025/07/21 13:00
   */
  static async batchDelete(parameter) {
    return await Request.post('/erp/region/batchDelete', parameter);
  }

  /**
   * 获取区域选择器数据
   *
   * <AUTHOR>
   * @date 2025/07/21 13:00
   */
  static async findSelector(parameter) {
    return await Request.getAndLoadData('/erp/region/selector', parameter);
  }

  /**
   * 新增区域
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async add(parameter) {
    return await Request.post('/erp/region/add', parameter);
  }

  /**
   * 编辑区域
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async edit(parameter) {
    return await Request.post('/erp/region/edit', parameter);
  }

  /**
   * 删除区域
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async delete(parameter) {
    return await Request.post('/erp/region/delete', parameter);
  }

  /**
   * 查询区域详情
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async detail(parameter) {
    return await Request.getAndLoadData('/erp/region/detail', parameter);
  }

  /**
   * 更新区域状态
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async updateStatus(parameter) {
    return await Request.post('/erp/region/updateStatus', parameter);
  }

  /**
   * 校验区域编码
   *
   * <AUTHOR>
   * @date 2025/07/20 13:00
   */
  static async validateCode(parameter) {
    return await Request.getAndLoadData('/erp/region/validateCode', parameter);
  }
}

// 为了向后兼容，导出函数形式的API
export const getRegionPage = RegionApi.findPage;
export const getRegionList = RegionApi.findList;
export const getRegionTree = RegionApi.findTree;
export const addRegion = RegionApi.add;
export const editRegion = RegionApi.edit;
export const deleteRegion = RegionApi.delete;
export const getRegionDetail = RegionApi.detail;
export const updateRegionStatus = RegionApi.updateStatus;
export const validateRegionCode = RegionApi.validateCode;
