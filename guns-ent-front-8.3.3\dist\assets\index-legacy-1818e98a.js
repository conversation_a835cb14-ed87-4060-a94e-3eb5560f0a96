System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var o,a,n,s,l,d,r,i,u,c,b,m,g,h,p;return{setters:[e=>{o=e._,a=e.b2,n=e.bR,s=e.r,l=e.o,d=e.a,r=e.f,i=e.w,u=e.b,c=e.d,b=e.bS,m=e.bT,g=e.aF,h=e.bU,p=e.bV}],execute:function(){var t=document.createElement("style");t.textContent="[data-v-450e97bf] .guns-admin-logo{width:210px!important;padding:0!important}[data-v-450e97bf] .guns-admin-body{height:calc(100vh - 52px);overflow-y:auto;overflow-x:hidden}[data-v-450e97bf] .guns-admin-header{box-shadow:0 0 10px #e8e9ea}\n",document.head.appendChild(t);const f=["src"];e("H",o({__name:"index",setup(e){const{push:t}=a();let o=n();const v=s(""),x=s("Guns Tech."),w=e=>{e||t(p)};return l((async()=>{let e=await o.loadThemeInfo();v.value=e.gunsMgrLogo,x.value=e.gunsMgrName})),(e,t)=>(d(),r(h,{"show-collapse":!1,"show-refresh":!1,"fixed-sidebar":!1,"show-breadcrumb":!1,collapse:!0,onLogoClick:w,"project-name":x.value,"layout-style":"top","tab-context-menu":!1},{logo:i((()=>[u("img",{src:v.value,alt:"logo"},null,8,f)])),right:i((e=>[c(b,{"is-mobile":e.isMobile},null,8,["is-mobile"])])),footer:i((()=>[c(m)])),default:i((()=>[g(e.$slots,"default",{},void 0,!0)])),_:3},8,["project-name"]))}},[["__scopeId","data-v-450e97bf"]]))}}}));
