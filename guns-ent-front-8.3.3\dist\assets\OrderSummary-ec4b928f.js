import{A as l}from"./formatter-5a06da9d.js";import{_ as d,a as s,c as m,b as a,t as o,h as n,a2 as i}from"./index-18a1ea24.js";import"./constants-2fa70699.js";const c={key:0,class:"summary-item"},y={class:"value"},b={key:1,class:"summary-item"},f={class:"value amount"},p={key:2,class:"summary-item"},v={class:"value member"},_=Object.assign({name:"OrderSummary"},{__name:"OrderSummary",props:{itemCount:{type:Number,default:0},amount:{type:Number,default:0},member:{type:Object,default:null},mini:{type:Boolean,default:!1},showItemCount:{type:Boolean,default:!0},showAmount:{type:Boolean,default:!0},showMember:{type:Boolean,default:!0}},setup(e){const u=r=>l.formatCurrency(r);return(r,t)=>(s(),m("div",{class:i(["order-summary",{mini:e.mini}])},[e.showItemCount?(s(),m("div",c,[t[0]||(t[0]=a("span",{class:"label"},"\u5546\u54C1\u6570\u91CF:",-1)),a("span",y,o(e.itemCount)+"\u4EF6",1)])):n("",!0),e.showAmount?(s(),m("div",b,[t[1]||(t[1]=a("span",{class:"label"},"\u8BA2\u5355\u91D1\u989D:",-1)),a("span",f,o(u(e.amount)),1)])):n("",!0),e.showMember&&e.member?(s(),m("div",p,[t[2]||(t[2]=a("span",{class:"label"},"\u4F1A\u5458:",-1)),a("span",v,o(e.member.memberName||e.member.name),1)])):n("",!0)],2))}}),w=d(_,[["__scopeId","data-v-8dee532b"]]);export{w as default};
