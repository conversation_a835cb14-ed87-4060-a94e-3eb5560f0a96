package cn.stylefeng.roses.ent.mobile.manage.controller;

import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.*;
import cn.stylefeng.roses.ent.mobile.manage.service.AddressBookService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 移动端-通讯录界面的接口
 *
 * <AUTHOR>
 * @since 2024/3/21 22:09
 */
@RestController
@ApiResource(name = "移动端-通讯录界面的接口")
public class MobileAddressBookController {

    @Resource
    private AddressBookService addressBookService;

    /**
     * 获取通讯录列表
     *
     * <AUTHOR>
     * @since 2024-03-22 9:54
     */
    @GetResource(name = "获取通讯录列表", path = "/mobile/addressBook/getList")
    public ResponseData<List<AddressBookItem>> getList(@Validated AddressBookRequest addressBookRequest) {
        List<AddressBookItem> addressBookItemList = addressBookService.getAddressBookItemList(addressBookRequest);
        return new SuccessResponseData<>(addressBookItemList);
    }

    /**
     * 获取通讯录人员详情
     *
     * <AUTHOR>
     * @since 2024/3/23 19:38
     */
    @GetResource(name = "获取通讯录人员详情", path = "/mobile/addressBook/getUserInfo")
    public ResponseData<AddressBookUserDetail> getUserInfo(@Validated AddressBookUserRequest addressBookUserRequest) {
        AddressBookUserDetail addressBookUserDetail = addressBookService.getUserInfo(addressBookUserRequest);
        return new SuccessResponseData<>(addressBookUserDetail);
    }

    /**
     * 获取邀请成员的二维码的图片
     *
     * <AUTHOR>
     * @since 2024-04-08 16:05
     */
    @GetResource(name = "获取邀请成员的二维码的图片", path = "/mobile/addressBook/getInviteQrCode")
    public void getInviteQrCodePicture(@Validated QrCodeCreateRequest qrCodeCreateRequest) {
        addressBookService.getInviteQrCodePicture(qrCodeCreateRequest);
    }

    /**
     * 获取邀请成员的二维码的URL
     *
     * <AUTHOR>
     * @since 2024-04-08 16:54
     */
    @GetResource(name = "获取邀请成员的二维码的URL", path = "/mobile/addressBook/getInviteUrl")
    public ResponseData<String> getInviteUrl(@Validated QrCodeCreateRequest qrCodeCreateRequest) {
        String inviteUrl = addressBookService.getInviteUrl(qrCodeCreateRequest);
        return new SuccessResponseData<>(inviteUrl);
    }

}
