package cn.stylefeng.roses.kernel.impexp.org.service;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.org.factory.OrgExportFactory;
import cn.stylefeng.roses.kernel.impexp.org.pojo.OrgExportRequest;
import cn.stylefeng.roses.kernel.impexp.org.pojo.base.OrgExcelImportParse;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import cn.stylefeng.roses.kernel.rule.util.ResponseRenderUtil;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 组织机构导出的过程
 *
 * <AUTHOR>
 * @since 2024-02-18 15:39
 */
@Service
public class OrgExportService {

    @Resource
    private HrOrganizationService hrOrganizationService;

    /**
     * 导出并生成excel
     *
     * <AUTHOR>
     * @since 2024-02-18 15:40
     */
    public void exportOrg(OrgExportRequest orgExportRequest) {

        // 1. 根据请求参数获取导出哪个公司下的组织机构
        List<HrOrganization> exportOrg = this.getExportOrg(orgExportRequest);

        // 2. 将这些机构转化为Excel导出的格式，主要为了映射列
        List<OrgExcelImportParse> orgExportParse = OrgExportFactory.createOrgExportData(exportOrg);

        // 3. 将这些机构导出为excel
        InputStream exportExcelTemplate = ResourceUtil.getStream("org-export-template.xlsx");

        // 设置header
        ResponseRenderUtil.setRenderExcelHeader(HttpServletUtil.getResponse(), "组织机构导出");

        // 获取响应流
        ServletOutputStream outputStream = null;
        try {
            outputStream = HttpServletUtil.getResponse().getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 执行导出
        EasyExcel.write(outputStream).withTemplate(exportExcelTemplate).sheet().doFill(orgExportParse);
    }

    /**
     * 获取导出的组织机构信息
     *
     * <AUTHOR>
     * @since 2024-02-18 15:41
     */
    public List<HrOrganization> getExportOrg(OrgExportRequest orgExportRequest) {

        // 查询组织机构信息
        LambdaQueryWrapper<HrOrganization> wrapper = new LambdaQueryWrapper<>();

        // 根据排序查询
        wrapper.orderByAsc(HrOrganization::getOrgSort);

        // 只查询启用的用户
        wrapper.eq(HrOrganization::getStatusFlag, StatusEnum.ENABLE.getCode());

        // 只查询关键的信息
        wrapper.select(HrOrganization::getOrgId, HrOrganization::getOrgName, HrOrganization::getOrgShortName, HrOrganization::getOrgParentId, HrOrganization::getOrgCode, HrOrganization::getOrgSort,
                HrOrganization::getStatusFlag, HrOrganization::getOrgType, HrOrganization::getTaxNo, HrOrganization::getRemark, HrOrganization::getMasterOrgId);

        // 如果组织机构为空，获取整个大组织机构树
        if (ObjectUtil.isEmpty(orgExportRequest) || ObjectUtil.isEmpty(orgExportRequest.getOrgId())) {
            return hrOrganizationService.list(wrapper);
        }

        // 获取需要导出的机构列表
        Long orgId = orgExportRequest.getOrgId();
        if (ObjectUtil.isEmpty(orgExportRequest.getContainSubOrg())) {
            orgExportRequest.setContainSubOrg(false);
        }

        // 如果不需要查询子公司的信息，则只查询选中的组织机构id和直属父级为这个机构的所有机构信息
        if (!orgExportRequest.getContainSubOrg()) {
            wrapper.eq(HrOrganization::getOrgId, orgId);
            wrapper.or().eq(HrOrganization::getOrgParentId, orgId);
            return hrOrganizationService.list(wrapper);
        }

        // 如果需要查询子公司的信息，则需要查询pids为这个机构的所有机构信息
        wrapper.eq(HrOrganization::getOrgId, orgId);
        wrapper.or().like(HrOrganization::getOrgPids, "[" + orgId + "]");

        return this.hrOrganizationService.list(wrapper);
    }

}
