package cn.stylefeng.roses.kernel.websocket.api;

import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 自定义websocket事件，业务可以监听这个事件，处理收到消息的逻辑
 *
 * <AUTHOR>
 * @since 2024/1/15 0:20
 */
@Getter
public class WebSocketEvent extends ApplicationEvent {

    /**
     * websocket信息封装
     */
    private final WebSocketDTO webSocket;

    /**
     * 事件类型
     */
    private final String eventType;

    /**
     * 消息内容
     */
    private String message;

    public WebSocketEvent(WebSocketDTO webSocket, String eventType) {
        super(webSocket);
        this.webSocket = webSocket;
        this.eventType = eventType;
    }

    public WebSocketEvent(WebSocketDTO webSocket, String eventType, String message) {
        super(webSocket);
        this.webSocket = webSocket;
        this.eventType = eventType;
        this.message = message;
    }

}
