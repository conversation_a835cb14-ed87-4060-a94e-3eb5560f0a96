System.register(["./index-legacy-ee1db0c7.js","./index-legacy-8a7fc0f5.js","./OrgApi-legacy-c15eac58.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,l){"use strict";var a,o,t,n,d,i,u,v,s,r,c,m,p,b,C,g,h,y,f,x,w,N,k;return{setters:[e=>{a=e.r,o=e.o,t=e.a,n=e.f,d=e.w,i=e.d,u=e.c,v=e.F,s=e.e,r=e.g,c=e.t,m=e.h,p=e.b,b=e.m,C=e.I,g=e.W,h=e.J,y=e.l,f=e.ci,x=e.n,w=e.M},e=>{N=e._},e=>{k=e.O},null,null,null],execute:function(){e("default",{__name:"org-level",props:{visible:Boolean,levelNumberList:Array},emits:["update:visible","done"],setup(e,{emit:l}){const I=e,U=l,_=a(!1),j=a([]),L=a([{title:"层级级别",width:100,dataIndex:"levelNumber",customCell:e=>({class:"cell-hover"})},{title:"层级名称",width:100,dataIndex:"levelName",customCell:e=>({class:"cell-hoverr"})},{title:"层级编码",width:100,dataIndex:"levelCode",customCell:e=>({class:"cell-hoverr"})},{title:"颜色",width:100,dataIndex:"levelColor",customCell:e=>({class:"cell-hoverr"})},{title:"操作",width:60,align:"center",dataIndex:"action"}]);o((()=>{z()}));const z=async()=>{j.value=await k.organizationLevelList()},S=()=>{let e={levelNumber:void 0,levelName:"",levelCode:"",levelColor:"fff"};j.value.push(e)},A=e=>{U("update:visible",e)},B=async()=>0==j.value.length?b.warning("层级不能为空"):j.value.some((e=>void 0===e.levelNumber||""===e.levelName||""===e.levelCode||""===e.levelColor))?b.warning("存在 undefined 或者空字符串"):(_.value=!0,void k.updateTotal({levelList:j.value}).then((e=>{b.success(e.message),A(!1),U("done")})).finally((()=>_.value=!1)));return(e,l)=>{const a=C,o=g,b=h,k=y,U=f,z=x,O=N,R=w;return t(),n(R,{width:762,maskClosable:!1,visible:I.visible,"confirm-loading":_.value,forceRender:!0,title:"层级维护","body-style":{paddingBottom:"8px"},"onUpdate:visible":A,onOk:B,onClose:l[0]||(l[0]=e=>A(!1))},{default:d((()=>[i(O,{columns:L.value,ref:"tableRef",dataSource:j.value,loading:_.value,size:"small",rowSelection:!1,height100:!1,isPage:!1},{toolRight:d((()=>[i(a,{iconClass:"icon-opt-tianjia","font-size":"24px",title:"新增",color:"#60666b",onClick:S})])),bodyCell:d((({column:e,record:l,index:C})=>["levelNumber"==e.dataIndex?(t(),n(b,{key:0,value:l.levelNumber,"onUpdate:value":e=>l.levelNumber=e,bordered:!1,showArrow:!1,style:{width:"100%"},placeholder:"请选择"},{default:d((()=>[(t(!0),u(v,null,s(I.levelNumberList,(e=>(t(),n(o,{value:e.value,key:e.value},{default:d((()=>[r(c(e.name),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value"])):m("",!0),"levelName"==e.dataIndex?(t(),n(k,{key:1,value:l.levelName,"onUpdate:value":e=>l.levelName=e,bordered:!1,placeholder:"请输入"},null,8,["value","onUpdate:value"])):m("",!0),"levelCode"==e.dataIndex?(t(),n(k,{key:2,value:l.levelCode,"onUpdate:value":e=>l.levelCode=e,bordered:!1,placeholder:"请输入"},null,8,["value","onUpdate:value"])):m("",!0),"levelColor"==e.dataIndex?(t(),n(z,{key:3},{default:d((()=>[i(U,{value:l.levelColor,"onUpdate:value":e=>l.levelColor=e},null,8,["value","onUpdate:value"]),p("span",null,c(l.levelColor),1)])),_:2},1024)):m("",!0),"action"==e.dataIndex?(t(),n(a,{key:4,iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{j.value=j.value.filter(((l,a)=>a!==e))})(C)},null,8,["onClick"])):m("",!0)])),_:1},8,["columns","dataSource","loading"])])),_:1},8,["visible","confirm-loading"])}}})}}}));
