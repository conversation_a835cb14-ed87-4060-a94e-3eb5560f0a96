package cn.stylefeng.guns.gateway.config;

import cn.stylefeng.guns.gateway.core.exception.GunsGatewayExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.util.pattern.PathPatternParser;

/**
 * 网关全局配置
 *
 * <AUTHOR>
 * @date 2017-11-14-下午5:56
 */
@Configuration
public class ContextConfig {

    /**
     * 全局错误拦截器
     *
     * <AUTHOR>
     * @date 2019/5/12 21:21
     */
    @Bean
    public GunsGatewayExceptionHandler gunsGatewayExceptionHandler() {
        return new GunsGatewayExceptionHandler();
    }

    /**
     * 跨域过滤器
     *
     * <AUTHOR>
     * @date 2019-06-07 15:02
     */
    @Bean
    public CorsWebFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedMethod("*");
        config.addAllowedOrigin("*");
        config.addAllowedHeader("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource(new PathPatternParser());
        source.registerCorsConfiguration("/**", config);

        return new CorsWebFilter(source);
    }

}

