package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * POS订单实体类
 *
 * <AUTHOR>
 * @since 2025/08/01 10:20
 */
@TableName(value = "pos_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PosOrder extends BaseEntity {

    /**
     * 订单ID
     */
    @TableId(value = "order_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("订单ID")
    private Long orderId;

    /**
     * 订单号
     */
    @TableField("order_no")
    @ChineseDescription("订单号")
    private String orderNo;

    /**
     * 会员ID
     */
    @TableField("member_id")
    @ChineseDescription("会员ID")
    private Long memberId;

    /**
     * 订单总金额
     */
    @TableField("total_amount")
    @ChineseDescription("订单总金额")
    private BigDecimal totalAmount;

    /**
     * 折扣金额
     */
    @TableField("discount_amount")
    @ChineseDescription("折扣金额")
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    @TableField("final_amount")
    @ChineseDescription("实付金额")
    private BigDecimal finalAmount;

    /**
     * 订单状态（PENDING-待支付，PAID-已支付，CANCELLED-已取消，REFUNDED-已退款）
     */
    @TableField("order_status")
    @ChineseDescription("订单状态")
    private String orderStatus;

    /**
     * 支付状态（UNPAID-未支付，PAID-已支付，PARTIAL-部分支付，REFUNDED-已退款）
     */
    @TableField("payment_status")
    @ChineseDescription("支付状态")
    private String paymentStatus;

    /**
     * 支付方式（CASH-现金，WECHAT-微信，ALIPAY-支付宝，MEMBER-会员卡，CARD-银行卡）
     */
    @TableField("payment_method")
    @ChineseDescription("支付方式")
    private String paymentMethod;

    /**
     * 收银员ID
     */
    @TableField("cashier_id")
    @ChineseDescription("收银员ID")
    private Long cashierId;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;

}