System.register(["./index-legacy-ee1db0c7.js","./app-form-legacy-591d3698.js","./AppApi-legacy-f1c900da.js","./FileApi-legacy-f85a3060.js","./index-legacy-94a6fc23.js","./index-legacy-198191c1.js"],(function(e,l){"use strict";var a,i,t,n,o,s,u,d,v,c,p,r;return{setters:[e=>{a=e.r,i=e.o,t=e.cb,n=e.a,o=e.f,s=e.w,u=e.d,d=e.m,v=e.M},e=>{c=e.default},e=>{p=e.A},e=>{r=e.a},null,null],execute:function(){e("default",{__name:"app-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:l}){const f=e,g=l,m=a(!1),y=a(!1),b=a({iconList:[],statusFlag:1}),h=a(null);i((async()=>{f.data?(y.value=!0,j()):(b.value.appSort=await t("SYSTEM_BASE_APP"),y.value=!1)}));const j=()=>{p.detail({appId:f.data.appId}).then((e=>{b.value=Object.assign({},e),b.value.iconList=[],I("iconList",e.appIcon)}))},I=(e,l)=>{r.getAntdVInfo({fileId:l}).then((a=>{a.uid=l,b.value[e]=[a]}))},A=e=>{g("update:visible",e)},L=async()=>{h.value.$refs.formRef.validate().then((async e=>{if(e){var l,a,i,t;null!==(l=b.value.iconList)&&void 0!==l&&l.length&&(b.value.appIcon=null!==(a=b.value.iconList[0])&&void 0!==a&&null!==(a=a.response)&&void 0!==a&&null!==(a=a.data)&&void 0!==a&&a.fileId?null===(i=b.value.iconList[0])||void 0===i||null===(i=i.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.fileId:null===(t=b.value.iconList[0])||void 0===t?void 0:t.uid),m.value=!0;let e=null;e=y.value?p.edit(b.value):p.add(b.value),e.then((async e=>{m.value=!1,d.success(e.message),A(!1),g("done")})).catch((()=>{m.value=!1}))}}))};return(e,l)=>{const a=v;return n(),o(a,{width:700,maskClosable:!1,visible:f.visible,"confirm-loading":m.value,forceRender:!0,title:y.value?"编辑应用":"新建应用","body-style":{paddingBottom:"8px",height:"550px",overflowY:"auto"},"onUpdate:visible":A,onOk:L,onClose:l[1]||(l[1]=e=>A(!1))},{default:s((()=>[u(c,{form:b.value,"onUpdate:form":l[0]||(l[0]=e=>b.value=e),ref_key:"appFormRef",ref:h},null,8,["form"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
