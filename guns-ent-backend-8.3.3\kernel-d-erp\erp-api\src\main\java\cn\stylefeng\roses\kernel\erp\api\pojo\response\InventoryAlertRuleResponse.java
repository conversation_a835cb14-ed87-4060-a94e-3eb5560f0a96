package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存预警规则响应参数
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Data
public class InventoryAlertRuleResponse {

    /**
     * 预警规则ID
     */
    @ChineseDescription("预警规则ID")
    private Long id;

    /**
     * 规则名称
     */
    @ChineseDescription("规则名称")
    private String ruleName;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)
     */
    @ChineseDescription("预警类型")
    private String ruleType;

    /**
     * 目标类型：PRODUCT(单个商品)、CATEGORY(商品分类)、ALL(全部商品)
     */
    @ChineseDescription("目标类型")
    private String targetType;

    /**
     * 目标ID（商品ID或分类ID，ALL类型时为NULL）
     */
    @ChineseDescription("目标ID")
    private Long targetId;

    /**
     * 目标名称（商品名称或分类名称）
     */
    @ChineseDescription("目标名称")
    private String targetName;

    /**
     * 预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)
     */
    @ChineseDescription("预警级别")
    private String alertLevel;

    /**
     * 阈值类型：QUANTITY(数量)、PERCENTAGE(百分比)、DAYS(天数)
     */
    @ChineseDescription("阈值类型")
    private String thresholdType;

    /**
     * 阈值（根据阈值类型确定含义）
     */
    @ChineseDescription("阈值")
    private BigDecimal thresholdValue;

    /**
     * 比较操作符：LTE(小于等于)、LT(小于)、GTE(大于等于)、GT(大于)、EQ(等于)
     */
    @ChineseDescription("比较操作符")
    private String comparisonOperator;

    /**
     * 是否启用（Y-启用，N-停用）
     */
    @ChineseDescription("是否启用")
    private String isEnabled;

    /**
     * 通知方式：SYSTEM(系统通知)、EMAIL(邮件)、SMS(短信)、WECHAT(微信)，多个用逗号分隔
     */
    @ChineseDescription("通知方式")
    private String notificationMethods;

    /**
     * 通知用户ID列表，用逗号分隔
     */
    @ChineseDescription("通知用户ID列表")
    private String notificationUsers;

    /**
     * 检查频率（分钟）
     */
    @ChineseDescription("检查频率")
    private Integer checkFrequency;

    /**
     * 最后检查时间
     */
    @ChineseDescription("最后检查时间")
    private Date lastCheckTime;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ChineseDescription("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ChineseDescription("更新时间")
    private Date updateTime;

    /**
     * 创建人ID
     */
    @ChineseDescription("创建人ID")
    private Long createUser;

    /**
     * 更新人ID
     */
    @ChineseDescription("更新人ID")
    private Long updateUser;
}
