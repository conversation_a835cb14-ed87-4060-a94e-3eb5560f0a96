package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRecord;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRecordRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRecordResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 库存预警记录服务接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
public interface InventoryAlertRecordService extends IService<InventoryAlertRecord> {

    /**
     * 新增预警记录
     *
     * @param request 请求参数
     */
    void add(InventoryAlertRecordRequest request);

    /**
     * 查询预警记录详情
     *
     * @param request 请求参数
     * @return 预警记录详情
     */
    InventoryAlertRecordResponse detail(InventoryAlertRecordRequest request);

    /**
     * 分页查询预警记录
     *
     * @param request 请求参数
     * @return 分页结果
     */
    PageResult<InventoryAlertRecordResponse> findPage(InventoryAlertRecordRequest request);

    /**
     * 处理预警记录
     *
     * @param request 请求参数
     */
    void handle(InventoryAlertRecordRequest request);

    /**
     * 批量处理预警记录
     *
     * @param request 请求参数
     */
    void batchHandle(InventoryAlertRecordRequest request);

    /**
     * 忽略预警记录
     *
     * @param request 请求参数
     */
    void ignore(InventoryAlertRecordRequest request);

    /**
     * 获取预警统计数据
     *
     * @param tenantId 租户ID
     * @return 统计数据
     */
    Map<String, Object> getStatistics(Long tenantId);

    /**
     * 导出预警记录
     *
     * @param request 请求参数
     * @return 导出数据
     */
    List<InventoryAlertRecordResponse> exportRecords(InventoryAlertRecordRequest request);

    /**
     * 检查是否存在未处理的相同预警
     *
     * @param ruleId    规则ID
     * @param productId 商品ID
     * @return 是否存在
     */
    boolean hasUnresolvedAlert(Long ruleId, Long productId);

    /**
     * 根据商品ID查询预警记录
     *
     * @param productId 商品ID
     * @param status    状态（可选）
     * @return 预警记录列表
     */
    List<InventoryAlertRecord> findByProductId(Long productId, String status);

    /**
     * 根据规则ID查询预警记录
     *
     * @param ruleId 规则ID
     * @param status 状态（可选）
     * @return 预警记录列表
     */
    List<InventoryAlertRecord> findByRuleId(Long ruleId, String status);

    /**
     * 查询最近的预警记录
     *
     * @param limit    限制数量
     * @param tenantId 租户ID
     * @return 预警记录列表
     */
    List<InventoryAlertRecordResponse> findRecentRecords(int limit, Long tenantId);

    /**
     * 清理过期的预警记录
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupExpiredRecords(int days);

    /**
     * 生成建议操作
     *
     * @param record 预警记录
     */
    void generateSuggestedAction(InventoryAlertRecord record);
}
