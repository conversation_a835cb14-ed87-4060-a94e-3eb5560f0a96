/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50722
 Source Host           : localhost:3306
 Source Schema         : guns

 Target Server Type    : MySQL
 Target Server Version : 50722
 File Encoding         : 65001

 Date: 30/07/2025 16:35:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app`  (
  `app_id` bigint(20) NOT NULL COMMENT '主键id',
  `app_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `app_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `app_icon` bigint(20) NULL DEFAULT NULL COMMENT '应用图标，存fileId，上传的图片',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `app_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`app_id`) USING BTREE,
  UNIQUE INDEX `APP_CODE_UNIQUE`(`app_code`) USING BTREE COMMENT 'app编码唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统应用' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_app
-- ----------------------------
INSERT INTO `sys_app` VALUES (1671406669800796161, '门户应用', 'portal', 1673705057339625474, 1, 100.00, '前台门户应用，包含个人信息、常用应用等界面', NULL, 1, 'N', '2023-06-21 14:37:38', NULL, NULL, NULL);
INSERT INTO `sys_app` VALUES (1671406745336016898, '后台管理', 'system_manager', 1673705057339625478, 1, 200.00, '系统后台管理应用，包含组织架构维护、权限配置等界面', NULL, 1, 'N', '2023-06-21 14:38:35', NULL, NULL, NULL);
INSERT INTO `sys_app` VALUES (1701801589128577026, '租户中心', 'tenant_center', 1701802464433684482, 1, 1000.00, '针对同一个功能模块，使用不同维度的数据进行管理', NULL, 1, 'N', '2023-09-13 11:34:51', 1339550467939639299, '2023-09-13 11:36:19', 1339550467939639299);
INSERT INTO `sys_app` VALUES (1717100945571057665, 'API认证', 'api_certify', 1718932070425694209, 1, 1000.00, '针对外部系统访问本系统的接口对接打通', NULL, 0, 'N', '2023-10-25 16:49:02', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_app` VALUES (1721100056577146881, '单点接入', 'sso_auth', 1721101659015184385, 1, 1000.00, '接入统一认证中心的外部应用单点配置', NULL, 2, 'N', '2023-11-05 17:40:05', 1339550467939639299, '2023-11-05 17:41:42', 1339550467939639299);
INSERT INTO `sys_app` VALUES (1815000000000000001, 'ERP管理', 'erp_manager', 1950372507173306369, 1, 300.00, 'ERP企业资源规划管理应用，包含供应商、客户、商品、区域等基础数据管理', NULL, 3, 'N', '2025-07-20 19:19:05', -1, '2025-07-30 09:46:58', 1339550467939639299);

-- ----------------------------
-- Table structure for sys_area
-- ----------------------------
DROP TABLE IF EXISTS `sys_area`;
CREATE TABLE `sys_area`  (
  `area_id` bigint(20) NOT NULL COMMENT '区域id',
  `area_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域编码',
  `area_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域全称',
  `parent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '上级区域编码',
  `area_level` int(11) NULL DEFAULT NULL COMMENT '区域级别',
  `area_sort` decimal(20, 2) NULL DEFAULT 9999.00 COMMENT '排序码',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'N' COMMENT '是否删除',
  `area_pids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所有的上级区域编码,用逗号分隔',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `create_user` bigint(20) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `update_user` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`area_id`) USING BTREE,
  INDEX `area_code`(`area_code`) USING BTREE,
  INDEX `area_name`(`area_name`) USING BTREE,
  INDEX `parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '行政区域表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_click_count
-- ----------------------------
DROP TABLE IF EXISTS `sys_click_count`;
CREATE TABLE `sys_click_count`  (
  `click_count_id` bigint(20) NOT NULL COMMENT '主键id',
  `business_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务的分类标识',
  `business_key_id` bigint(20) NULL DEFAULT NULL COMMENT '业务的主键id',
  `click_count` bigint(20) NULL DEFAULT NULL COMMENT '点击次数',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`click_count_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户点击数量统计' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_click_status
-- ----------------------------
DROP TABLE IF EXISTS `sys_click_status`;
CREATE TABLE `sys_click_status`  (
  `click_status_id` bigint(20) NOT NULL COMMENT '主键id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `business_key_id` bigint(20) NULL DEFAULT NULL COMMENT '业务的主键id',
  `business_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务的分类标识',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`click_status_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户点击状态' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` bigint(20) NOT NULL COMMENT '主键',
  `config_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `config_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '属性编码',
  `config_value` varchar(3500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '属性值',
  `sys_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Y' COMMENT '是否是系统参数：Y-是，N-否',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `status_flag` tinyint(4) NULL DEFAULT 1 COMMENT '状态：1-正常，2-停用',
  `group_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '常量所属分类的编码，来自于“常量的分类”字典',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`config_id`) USING BTREE,
  UNIQUE INDEX `code_unique`(`config_code`) USING BTREE COMMENT '配置编码唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '系统配置是否已经初始化的标识', 'SYS_CONFIG_INIT_FLAG', 'true', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (4, 'Linux本地文件保存路径', 'SYS_LOCAL_FILE_SAVE_PATH_LINUX', '/opt/gunsFilePath', 'Y', NULL, 1, 'file_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (5, 'Windows本地文件保存路径', 'SYS_LOCAL_FILE_SAVE_PATH_WINDOWS', 'D:\\hyProject\\guns-ent-backend-8.3.3\\guns-master\\target', 'Y', NULL, 1, 'file_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (6, '不需要过滤的url', 'SYS_NONE_SECURITY_URLS', '/assets/**,/login,/swagger-ui.html,/favicon.ico,/swagger-ui/**,/error,/webSocket/*,/guns-devops/**,', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (8, 'session过期时间', 'SYS_SESSION_EXPIRED_SECONDS', '3600', 'Y', NULL, 1, 'auth_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (9, '账号单端登录限制', 'SYS_SINGLE_ACCOUNT_LOGIN_FLAG', 'false', 'Y', NULL, 1, 'auth_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (12, '系统默认密码', 'SYS_DEFAULT_PASSWORD', '123456', 'Y', NULL, 1, 'auth_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (21, '系统发布版本', 'SYS_RELEASE_VERSION', '********', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (22, '多租户开关', 'SYS_TENANT_OPEN', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (23, '验证码开关', 'SYS_CAPTCHA_OPEN', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (29, '获取文件生成auth url的失效时间', 'SYS_DEFAULT_FILE_TIMEOUT_SECONDS', '3600', 'Y', NULL, 1, 'file_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (30, '服务默认部署的环境地址', 'SYS_SERVER_DEPLOY_HOST', 'http://localhost:9000/api', 'Y', '主要用来生成文件的访问URL', 1, 'file_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (32, '用于auth模块权限校验的jwt失效时间', 'SYS_AUTH_JWT_TIMEOUT_SECONDS', '604800', 'Y', NULL, 1, 'auth_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (33, 'Druid监控界面的url映射', 'SYS_DRUID_URL_MAPPINGS', '/druid/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (34, 'Druid控制台账号', 'SYS_DRUID_ACCOUNT', 'admin', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (35, 'Druid控制台账号密码', 'SYS_DRUID_PASSWORD', 'YtfYh0GJbwrvNx0Cm5A0', 'Y', '默认是空串，为空会让程序自动创建一个随机密码', 1, 'sys_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (36, 'Druid控制台的监控数据是否可以重置清零', 'SYS_DRUID_RESET_ENABLE', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (37, 'druid web url统计的拦截范围', 'SYS_DRUID_WEB_STAT_FILTER_URL_PATTERN', '/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (38, 'druid web url统计的排除拦截表达式', 'SYS_DRUID_WEB_STAT_FILTER_EXCLUSIONS', '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (39, 'druid web url统计的session统计开关', 'SYS_DRUID_WEB_STAT_FILTER_SESSION_STAT_ENABLE', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (40, 'druid web url统计的session名称', 'SYS_DRUID_WEB_STAT_FILTER_PRINCIPAL_SESSION_NAME', 'Authorization', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (41, 'druid web url统计的session最大监控数', 'SYS_DRUID_WEB_STAT_FILTER_SESSION_STAT_MAX_COUNT', '1000', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (42, 'druid web url统计的cookie名称', 'SYS_DRUID_WEB_STAT_FILTER_PRINCIPAL_COOKIE_NAME', 'Authorization', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (43, 'druid web url统计的是否开启监控单个url调用的sql列表', 'SYS_DRUID_WEB_STAT_FILTER_PROFILE_ENABLE', 'true', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (44, '阿里云短信的accessKeyId', 'SYS_ALIYUN_SMS_ACCESS_KEY_ID', '你的accessKeyId', 'Y', NULL, 1, 'sms_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (45, '阿里云短信的accessKeySecret', 'SYS_ALIYUN_SMS_ACCESS_KEY_SECRET', '你的secret', 'Y', NULL, 1, 'sms_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (46, '阿里云短信的签名', 'SYS_ALIYUN_SMS_SIGN_NAME', '签名名称', 'Y', NULL, 1, 'sms_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (47, '短信发送验证码失效时间', 'SYS_SMS_VALIDATE_EXPIRED_SECONDS', '300', 'Y', NULL, 1, 'sms_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1350666094452482049, '获取XSS排除过滤的url范围', 'SYS_XSS_URL_EXCLUSIONS', '/sysNotice/add,/sysNotice/edit,/databaseInfo/add,/apiResource/record,/sysTheme/add,/sysTheme/edit,/webSocket/*,/sysTableWidth/setTableWidth', 'Y', '', 1, 'sys_config', 'N', '2021-01-17 12:47:46', 1339550467939639299, '2021-03-04 22:14:14', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1350666094452482050, '获取XSS过滤的url范围', 'SYS_XSS_URL_INCLUDES', '/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1356246056131649538, 'websocket的ws-url', 'WEB_SOCKET_WS_URL', 'ws://localhost:9000/api/ws/message/{token}', 'Y', '', 1, 'sys_config', 'N', '2021-02-01 22:20:32', 1339550467939639299, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1367118984192843778, '邮件是否启用账号密码验证', 'SYS_EMAIL_ENABLE_AUTH', 'true', 'N', '', 1, 'java_mail_config', 'N', '2021-03-03 22:25:40', 1339550467939639299, '2021-03-03 22:25:43', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1367119064924807169, '邮箱的账号', 'SYS_EMAIL_ACCOUNT', '<EMAIL>', 'N', '', 1, 'java_mail_config', 'N', '2021-03-03 22:26:00', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1367119226749444098, '邮箱的密码或者授权码', 'SYS_EMAIL_PASSWORD', 'xxx', 'N', '', 1, 'java_mail_config', 'N', '2021-03-03 22:26:38', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1367119286195314689, '邮箱的发送方邮箱', 'SYS_EMAIL_SEND_FROM', '<EMAIL>', 'Y', '', 1, 'java_mail_config', 'N', '2021-03-03 22:26:52', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1367119399810621441, '是否开启tls', 'SYS_EMAIL_START_TLS_ENABLE', 'true', 'N', '使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。', 1, 'java_mail_config', 'N', '2021-03-03 22:27:19', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1367119457260003329, 'SSL安全连接', 'SYS_EMAIL_TLS_ENABLE', 'true', 'N', '', 1, 'java_mail_config', 'N', '2021-03-03 22:27:33', 1339550467939639299, '2021-03-03 22:28:33', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1367119505888763905, '指定的端口连接到在使用指定的套接字工厂', 'SYS_EMAIL_SOCKET_FACTORY_PORT', '465', 'Y', '', 1, 'java_mail_config', 'N', '2021-03-03 22:27:45', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1367119568455196674, 'SMTP超时时长，单位毫秒', 'SYS_EMAIL_SMTP_TIMEOUT', '10000', 'N', '', 1, 'java_mail_config', 'N', '2021-03-03 22:28:00', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1367119662306942977, 'Socket连接超时值，单位毫秒，缺省值不超时', 'SYS_EMAIL_CONNECTION_TIMEOUT', '10000', 'N', 'Socket连接超时值，单位毫秒，缺省值不超时', 1, 'java_mail_config', 'N', '2021-03-03 22:28:22', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610114, 'smtp服务器地址', 'SYS_EMAIL_SMTP_HOST', 'smtp.126.com', 'N', NULL, 1, 'java_mail_config', 'N', '2021-06-09 16:55:01', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610115, 'smtp服务端口', 'SYS_EMAIL_SMTP_PORT', '465', 'Y', NULL, 1, 'java_mail_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610125, '拖拽验证码开关', 'SYS_DRAG_CAPTCHA_OPEN', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610200, 'auth认证用的jwt秘钥，用于校验登录token', 'SYS_AUTH_JWT_SECRET', 'fRuWUqpvzYJScYlASpgvpNVVhYCajN', 'Y', NULL, 1, 'auth_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1402549781675610210, '解析sso加密的数据的秘钥，解密sso单点中jwt中payload的秘钥', 'SYS_AUTH_SSO_DECRYPT_DATA_SECRET', 'sPaMjd6IHz36DkBaNzy4ill2rrW2qZxi', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610215, '获取是否开启sso远程会话校验', 'SYS_AUTH_SSO_SESSION_VALIDATE_SWITCH', 'false', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610220, 'sso会话校验，redis的host', 'SYS_AUTH_SSO_SESSION_VALIDATE_REDIS_HOST', 'localhost', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610225, 'sso会话校验，redis的port', 'SYS_AUTH_SSO_SESSION_VALIDATE_REDIS_PORT', '6379', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610230, 'sso会话校验，redis的密码', 'SYS_AUTH_SSO_SESSION_VALIDATE_REDIS_PASSWORD', '123456', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610235, 'sso会话校验，redis的数据库序号', 'SYS_AUTH_SSO_SESSION_VALIDATE_REDIS_DB_INDEX', '2', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610240, 'sso会话校验，redis的缓存前缀', 'SYS_AUTH_SSO_SESSION_VALIDATE_REDIS_CACHE_PREFIX', 'CA:USER:TOKEN:', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610245, 'sso服务器地址', 'SYS_AUTH_SSO_HOST', 'http://localhost:8888', 'Y', NULL, 1, 'ca_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610300, 'C端用户，注册邮件标题', 'CUSTOMER_REG_EMAIL_TITLE', '用户注册', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610305, '获取注册邮件的内容模板', 'CUSTOMER_REG_EMAIL_CONTENT', '感谢您注册Guns官方论坛，请点击此激活链接激活您的账户：<a href=\"http://localhost:8080/customer/active?verifyCode={}\">http://localhost:8080/customer/active?verifyCode={} </a>', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610310, '获取重置密码的邮件标题', 'CUSTOMER_RESET_PWD_EMAIL_TITLE', '用户校验', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610315, '获取重置密码的邮件内容', 'CUSTOMER_RESET_PWD_EMAIL_CONTENT', '您的验证码是【{}】，此验证码用于修改登录密码，请不要泄露给他人，如果不是您本人操作，请忽略此邮件。', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610320, '存放用户头像的bucket的名称', 'CUSTOMER_FILE_BUCKET', 'customer-bucket', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610325, '存放用户头像的bucket的名称的过期时间', 'CUSTOMER_FILE_BUCKET_EXPIRED_SECONDS', '600', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610330, '获取c端用户缓存的过期时间，用在加快获取速度', 'CUSTOMER_CACHE_EXPIRED_SECONDS', '3600', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610335, '是否开启旧版密码校验', 'CUSTOMER_OPEN_OLD_PASSWORD_VALIDATE', 'false', 'Y', NULL, 1, 'customer_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610400, '是否开启demo演示', 'SYS_DEMO_ENV_FLAG', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610405, '默认存储文件的bucket名称', 'SYS_FILE_DEFAULT_BUCKET', 'defaultBucket', 'Y', NULL, 1, 'file_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1402549781675610500, 'AES秘钥，用在数据库数据加密', 'SYS_ENCRYPT_SECRET_KEY', 'BIBGlkhAu8PpmzPn0RfmnVm9o5VvQY7v', 'Y', NULL, 1, 'security_config', 'N', NULL, NULL, '2023-05-11 11:10:28', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1402549781675610505, '开发模式开关', 'DEVOPS_DEV_SWITCH_STATUS', 'true', 'Y', '在开发模式下，允许devops平台访问某些系统接口', 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1481244035229200386, '全局日志记录，如果开启则所有请求都将记录日志', 'SYS_LOG_GLOBAL_FLAG', 'false', 'Y', NULL, 1, 'file_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1581687626275000321, '登录密码是否进行RSA加密校验，false为关闭', 'SYS_AUTH_PASSWORD_RSA_VALIDATE', 'false', 'Y', NULL, 1, 'auth_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (1697301485767798785, '默认根租户的ID', 'DEFAULT_ROOT_TENANT_ID', '1', 'Y', NULL, 1, 'TENANT_CONFIG', 'N', '2023-09-01 01:33:03', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1709185484166533121, '密码最大重试次数', 'SYS_LOGIN_MAX_ERROR_LOGIN_COUNT', '5', 'Y', '登录账号密码登录最大的错误次数，超过此次数则冻结账号', 1, 'auth_config', 'N', '2023-10-03 20:35:49', 1339550467939639299, '2023-10-03 20:35:58', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1709186904454336514, '密码策略：口令最小长度', 'SYS_LOGIN_MIN_PASSWORD_LENGTH', '6', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 20:41:28', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1709189854337699842, '密码策略：最少特殊符号数量', 'SYS_LOGIN_PASSWORD_MIN_SPECIAL_SYMBOL_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 20:53:11', 1339550467939639299, '2023-10-03 21:19:56', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1709196660577406977, '密码策略：最少大写字母数量', 'SYS_LOGIN_PASSWORD_MIN_UPPER_CASE_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 21:20:14', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1709196708086288385, '密码策略：最少小写字母数量', 'SYS_LOGIN_PASSWORD_MIN_LOWER_CASE_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 21:20:25', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1709196753862922241, '密码策略：最少数字符号的数量', 'SYS_LOGIN_PASSWORD_MIN_NUMBER_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 21:20:36', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1709207802066505730, '密码策略：最少多久更新一次密码，单位天', 'SYS_LOGIN_PASSWORD_MIN_UPDATE_DAYS', '180', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 22:04:30', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1709207873369673730, '密码策略：修改密码时，不能和历史密码重复的次数', 'SYS_LOGIN_PASSWORD_MIN_CANT_REPEAT_TIMES', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 22:04:47', 1339550467939639299, '2023-10-03 22:05:50', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1721467478413938690, '单点所用的统一认证会话的Cookie名', 'CA_COOKIE_NAME', 'CAID', 'Y', '单点所用的统一认证会话的Cookie名', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:00:05', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721467660824219649, 'Cookie过期时间', 'CA_COOKIE_EXPIRED_SECONDS', '259200', 'Y', '统一认证中心保存用户信息的cookie的过期时间，默认30天', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:00:48', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721467822346866690, '单点Cookie的域名', 'CA_COOKIE_DOMAIN', 'localhost', 'Y', '获取cookie的domain', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:01:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721468044485595137, 'CA会话的过期时间', 'CA_SESSION_EXPIRED_SECONDS', '86400', 'Y', 'CA会话的过期时间，用在Ca的会话内存缓存的过期时间', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:02:20', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721468275109400577, 'Token参数名称', 'CA_CALLBACK_URL_TOKEN_FIELD_NAME', 'token', 'Y', 'redirect到客户端应用时，url上携带的token参数的名称，这个token一般是对用户信息的json串进行加密的', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:03:15', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721468380990410754, '错误码参数名', 'CA_CALLBACK_URL_ERROR_CODE_FIELD_NAME', 'errorCode', 'Y', 'redirect到客户端应用时，url上携带的错误码参数的名称', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:03:40', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721468478008856578, '统一的登录界面的地址', 'CA_UNIFY_LOGIN_URL', 'http://localhost:9000/login', 'Y', '统一的登录界面的url地址', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:04:03', 1339550467939639299, '2023-11-06 18:05:24', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1721468577736822786, '统一错误界面地址', 'CA_ERROR_VIEW_URL', 'http://localhost:9000/errorPage', 'Y', 'redirect到错误提示界面的url', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:04:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1721468712923435009, '跳转url的参数名', 'CA_ERROR_VIEW_REDIRECT_URL_FIELD_NAME', 'referUrl', 'Y', 'redirect到错误提示界面携带跳转url的参数名', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:04:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1760911830422331394, '租户编码下拉或手输', 'SAAS_TENANT_CODE_SELECT_FLAG', 'true', 'Y', '租户登录界面编码是下拉还是手动输入，true-下拉，false-手动输入', 1, 'TENANT_CONFIG', 'N', '2024-02-23 14:17:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config` VALUES (1805439191234519042, 'SM2私钥', 'GUOMI_SM2_PRIVATE_KEY', '00c93ade3ab67cd8e678eab57695615eaf73fa19e48bf8a3d425ea086a70592f7a', 'Y', '请自行生成并替换', 1, 'GUO_MI_SM', 'N', '2024-06-25 11:13:41', 1339550467939639299, '2024-06-25 11:14:14', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1805439245798219777, 'SM2公钥', 'GUOMI_SM2_PUBLIC_KEY', '0400e820eec80768ad9e748fceab8096854bfcb965dc36b6028950c62fb10ed4ae935bf15c287927dddc22eaea81c41bf0954a22d5cf0356152e8bdf543a296f17', 'Y', '请自行生成并替换', 1, 'GUO_MI_SM', 'N', '2024-06-25 11:13:54', 1339550467939639299, '2024-06-25 11:14:12', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1805788664473255937, 'SM4对称加密秘钥', 'GUOMI_SM4_KEY', '4LDAuqXrefEUUZuKQBP8vQ==', 'Y', '请自行生成并替换', 1, 'GUO_MI_SM', 'N', '2024-06-25 11:13:54', 1339550467939639299, '2024-06-25 11:14:12', 1339550467939639299);
INSERT INTO `sys_config` VALUES (1829152372339773442, '文件存储类型', 'SYS_FILE_SAVE_TYPE', '10', 'Y', '10-本地，存储到默认路径（jar所在目录）\n11-本地，存储到指定路径下（需要配置linux和windows的路径）\n20-存储到MinIO\n30-存储到阿里云\n40-存储到腾讯云\n50-存储到青云', 1, 'file_config', 'N', '2024-08-29 21:41:24', 1821938198325960706, NULL, NULL);

-- ----------------------------
-- Table structure for sys_database_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_database_info`;
CREATE TABLE `sys_database_info`  (
  `db_id` bigint(20) NOT NULL COMMENT '主键',
  `db_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据库名称（英文名称）',
  `jdbc_driver` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'jdbc的驱动类型',
  `jdbc_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'jdbc的url',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据库连接的账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据库连接密码',
  `schema_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '数据库的schema名称，每种数据库的schema意义都不同',
  `status_flag` tinyint(4) NULL DEFAULT NULL COMMENT '数据源状态：1-正常，2-无法连接',
  `error_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '连接失败原因',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注，摘要',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`db_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多数据源信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_database_info
-- ----------------------------
INSERT INTO `sys_database_info` VALUES (1950474681198854145, 'master', 'com.mysql.cj.jdbc.Driver', '******************************************************************************************************************************************************************************************************', 'root', 'admin', NULL, 1, NULL, '主数据源，项目启动数据源！', 'N', '2025-07-30 16:32:56', NULL, '2025-07-30 16:33:30', -1);

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `dict_id` bigint(20) NOT NULL COMMENT '字典id',
  `dict_type_id` bigint(20) NOT NULL COMMENT '字典类型的id',
  `dict_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典编码',
  `dict_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典名称',
  `dict_name_pinyin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典名称首字母',
  `dict_encode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典编码',
  `dict_short_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典简称',
  `dict_short_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典简称的编码',
  `dict_parent_id` bigint(20) NOT NULL COMMENT '上级字典的id(如果没有上级字典id，则为-1)',
  `dict_pids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '父id集合',
  `status_flag` tinyint(4) NOT NULL COMMENT '状态：(1-启用,2-禁用),参考 StatusEnum',
  `dict_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '排序，带小数点',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`dict_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES (1348235720908619802, 1348235720908619811, 'M', '男', 'n', 'male', NULL, NULL, -1, '[-1],', 1, 110.00, 1, 'N', NULL, NULL, '2023-07-09 22:19:58', 1339550467939639299);
INSERT INTO `sys_dict` VALUES (1348235720908619803, 1348235720908619811, 'F', '女', 'n', 'female', NULL, NULL, -1, '[-1],', 1, 120.00, 1, 'N', NULL, NULL, '2023-07-09 22:19:58', 1339550467939639299);
INSERT INTO `sys_dict` VALUES (1348235720908619804, 1348235720908619812, '1', '启用', 'n', 'male', NULL, NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1348235720908619805, 1348235720908619812, '2', '禁用', 'n', 'female', NULL, NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1348235720908619806, 1348235720908619812, '3', '冻结', 'n', 'female', NULL, NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1350457799368257537, 1350457656690618370, 'low', '低', 'd', NULL, '低', NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1350457870780477442, 1350457656690618370, 'middle', '中', 'z', NULL, '中', NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1350457950417727489, 1350457656690618370, 'high', '高', 'g', NULL, '高', NULL, -1, '[-1],', 1, 3.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1353547360691851266, 1353547215422132226, 'sys_config', '系统配置', 'xtpz', NULL, NULL, NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1353547405457657857, 1353547215422132226, 'file_config', '文件配置', 'wjpz', NULL, NULL, NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1353547460558229506, 1353547215422132226, 'auth_config', '鉴权配置', 'jqpz', NULL, NULL, NULL, -1, '[-1],', 1, 3.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1353547539293704194, 1353547215422132226, 'sms_config', '短信配置', 'dxpz', NULL, NULL, NULL, -1, '[-1],', 1, 4.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1353998066804658177, 1353997993299480577, 'chinese', '中文', 'zw', NULL, NULL, NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1353998106784763906, 1353997993299480577, 'english', 'english', 'yw', NULL, NULL, NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1354040749627662337, 1354040335406587906, 'role_system', '系统角色', 'xtjs', NULL, NULL, NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1354040819219554305, 1354040335406587906, 'role_c', 'C端角色', 'Cdjs', NULL, NULL, NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1354041049981771778, 1354040335406587906, 'role_b', 'B端角色', 'Bdjs', NULL, NULL, NULL, -1, '[-1],', 1, 3.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1365251792270045186, 1365251549365317633, 'Y', '是', 's', NULL, NULL, NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1365251827812577282, 1365251549365317633, 'N', '否', 'f', NULL, NULL, NULL, -1, '[-1],', 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1365252384094728193, 1365252142779641858, 'com.mysql.cj.jdbc.Driver', 'com.mysql.cj.jdbc.Driver', 'com.mysql.cj.jdbc.Driver', NULL, NULL, NULL, -1, '[-1],', 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1402549554864427010, 1353547215422132226, 'java_mail_config', 'java邮件配置', 'javayjpz', NULL, NULL, NULL, -1, '[-1],', 1, 50.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1402549554864427020, 1353547215422132226, 'customer_config', 'C端用户配置', 'cdyhpz', NULL, NULL, NULL, -1, '[-1],', 1, 60.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1526221204984197121, 1353547215422132226, 'ca_config', '单点客户端配置', 'ddpz', NULL, NULL, NULL, -1, '[-1],', 1, 70.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699572911206401, 1569699391469809666, '1', '负责人', 'fzr', NULL, NULL, NULL, -1, '[-1],', 1, 10.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699624215932930, 1569699391469809666, '2', '部长', 'bz', NULL, NULL, NULL, -1, '[-1],', 1, 20.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699732391227394, 1569699391469809666, '3', '体系负责人', 'txfzr', NULL, NULL, NULL, -1, '[-1],', 1, 30.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699780906741762, 1569699391469809666, '4', '部门助理', 'bmzl', NULL, NULL, NULL, -1, '[-1],', 1, 40.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699833889189889, 1569699391469809666, '5', '资产助理', 'zczl', NULL, NULL, NULL, -1, '[-1],', 1, 50.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699872430649345, 1569699391469809666, '6', '考勤专员', 'kqzy', NULL, NULL, NULL, -1, '[-1],', 1, 60.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699912133931009, 1569699391469809666, '7', 'HRBP', 'HRBP', NULL, NULL, NULL, -1, '[-1],', 1, 70.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569699950046244865, 1569699391469809666, '8', '门禁员', 'mjy', NULL, NULL, NULL, -1, '[-1],', 1, 80.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569700054404722690, 1569699391469809666, '9', '办公账号员', 'bgzhy', NULL, NULL, NULL, -1, '[-1],', 1, 90.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1569700365244592129, 1569699391469809666, '10', '转岗须知员', 'zgxzy', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1574327462970531842, 1574327405802168321, '1', '所有人', 'syr', NULL, NULL, NULL, -1, '[-1],', 1, 10.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1574327499536474114, 1574327405802168321, '2', '当前登录人', 'dqdlr', NULL, NULL, NULL, -1, '[-1],', 1, 20.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1574327582113931266, 1574327405802168321, '3', '申请人', 'sqr', NULL, NULL, NULL, -1, '[-1],', 1, 30.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1574327869830602753, 1574327405802168321, '4', '当前登录人部门', 'dqdlrbm', NULL, NULL, NULL, -1, '[-1],', 1, 40.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1574327924788568065, 1574327405802168321, '5', '当前申请人部门', 'dqsqrbm', NULL, NULL, NULL, -1, '[-1],', 1, 50.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1697301378511056897, 1353547215422132226, 'TENANT_CONFIG', '租户配置', 'zhpz', NULL, NULL, NULL, -1, '[-1],', 1, 80.00, 1, 'N', '2023-09-01 01:32:38', 1339550467939639299, '2023-09-01 01:32:46', 1339550467939639299);
INSERT INTO `sys_dict` VALUES (1717480173877596162, 1717480072987807746, '1', '启用', 'qy', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 0, 'N', '2023-10-26 17:55:57', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1717480204684759041, 1717480072987807746, '2', '禁用', 'jy', NULL, NULL, NULL, -1, '[-1],', 1, 101.00, 0, 'N', '2023-10-26 17:56:05', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1721467248725463042, 1353547215422132226, 'CA_SERVER_CONFIG', '单点服务端配置', 'ddfwdpz', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 0, 'N', '2023-11-06 17:59:10', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1722790809880760322, 1722790763315597314, 'A', '证书类型A', 'zslxA', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 0, 'N', '2023-11-10 09:38:32', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1722790840398516225, 1722790763315597314, 'B', '证书类型B', 'zslxB', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 0, 'N', '2023-11-10 09:38:39', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_dict` VALUES (1805439083285716994, 1353547215422132226, 'GUO_MI_SM', '国密秘钥', 'gmmy', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 1, 'N', '2024-06-25 11:13:16', 1339550467939639299, '2024-06-25 11:13:22', 1339550467939639299);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_type_id` bigint(20) NOT NULL COMMENT '字典类型id',
  `dict_type_class` int(11) NULL DEFAULT NULL COMMENT '字典类型： 1-业务类型，2-系统类型，参考 DictTypeClassEnum',
  `dict_type_bus_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典类型业务编码',
  `dict_type_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典类型编码',
  `dict_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典类型名称',
  `dict_type_name_pinyin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典类型名称首字母拼音',
  `dict_type_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '字典类型描述',
  `status_flag` tinyint(4) NULL DEFAULT NULL COMMENT '字典类型的状态：1-启用，2-禁用，参考 StatusEnum',
  `dict_type_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '排序，带小数点',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`dict_type_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1348235720908619811, 1, 'base', 'sex', '性别', 'xb', NULL, 1, 1.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1348235720908619812, 2, 'system', 'user_status', '用户状态', 'yhzt', NULL, 1, 2.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1350457656690618370, 1, 'notice', 'priority_level', '优先级', 'yxj', '', 1, 5.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1353547215422132226, 2, NULL, 'config_group', '系统配置分组', 'xtpzfz', '系统配置分组', 1, 6.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1353997993299480577, 2, NULL, 'languages', '语种', 'yz', 'i18n 多语言', 1, 7.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1354040335406587906, 2, NULL, 'role_type', '角色类型', 'jslx', '', 1, 8.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1365251549365317633, 1, NULL, 'yn', 'yn', 'yn', NULL, 1, 7.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1365252142779641858, 1, NULL, 'jdbc_type', 'jdbc_type', 'jdbc_type', NULL, 1, 8.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1569699391469809666, 1, NULL, 'org_approver_type', '审批人类型', 'sprlx', '组织机构审批人类型', 1, 10.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1574327405802168321, 1, NULL, 'select_relation', '选择关系', 'xzgx', '适用于通用选择器中的选择关系的列举', 1, 20.00, 1, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (1717480072987807746, 1, NULL, 'COMMON_STATUS_FLAG', '通用状态', 'tyzt', NULL, 1, 101.00, 1, 'N', '2023-10-26 17:55:33', 1339550467939639299, '2023-10-26 17:55:42', 1339550467939639299);
INSERT INTO `sys_dict_type` VALUES (1722790763315597314, 1, NULL, 'certificate_type', '证书类型', 'zslx', NULL, 1, 105.00, 0, 'N', '2023-11-10 09:38:21', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_file_business
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_business`;
CREATE TABLE `sys_file_business`  (
  `file_business_id` bigint(20) NOT NULL COMMENT '主键id',
  `business_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务的编码，业务自定义',
  `business_id` bigint(20) NULL DEFAULT NULL COMMENT '业务主键id',
  `file_id` bigint(20) NULL DEFAULT NULL COMMENT '关联文件表的id',
  `download_count` int(11) NULL DEFAULT 0 COMMENT '下载次数',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`file_business_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务关联的文件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_file_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_info`;
CREATE TABLE `sys_file_info`  (
  `file_id` bigint(20) NOT NULL COMMENT '文件主键id',
  `file_code` bigint(20) NOT NULL COMMENT '文件编码，本号升级的依据，解决一个文件多个版本问题，多次上传文件编码不变',
  `file_version` int(11) NOT NULL DEFAULT 1 COMMENT '文件版本，从1开始',
  `file_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '当前状态：0-历史版,1-最新版',
  `file_location` tinyint(4) NOT NULL COMMENT '文件存储位置：1-阿里云，2-腾讯云，3-minio，4-本地',
  `file_bucket` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件仓库（文件夹）',
  `file_origin_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名称（上传时候的文件全名）',
  `file_suffix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件后缀，例如.txt',
  `file_size_kb` bigint(20) NULL DEFAULT NULL COMMENT '文件大小kb为单位',
  `file_size_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件大小信息，计算后的',
  `file_object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '存储到bucket中的名称，主键id+.后缀',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '存储路径',
  `secret_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否为机密文件，Y-是机密，N-不是机密',
  `file_md5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件的md5值',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`file_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_file_info
-- ----------------------------
INSERT INTO `sys_file_info` VALUES (10000, 1479745704687820802, 1, '1', 5, 'defaultBucket', 'defaultAvatar.png', 'png', 8, '7.61 KB', '10000.png', NULL, 'N', NULL, 'N', '2022-01-08 17:24:03', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1479753047148322818, 1479753047165100034, 1, '1', 5, 'defaultBucket', 'logo.png', 'png', 9, '8.86 KB', '1479753047148322818.png', NULL, 'N', NULL, 'N', '2022-01-08 17:53:14', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1673705057339625474, 1673705057339625476, 1, '1', 5, 'defaultBucket', 'icon-app-portal.png', 'png', 10, '9.5 KB', '1673705057339625474.png', NULL, 'N', NULL, 'N', '2023-06-27 22:49:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1673705057339625478, 1673705057339625479, 1, '1', 5, 'defaultBucket', 'icon-app-backend.png', 'png', 8, '8.43 KB', '1673705057339625478.png', NULL, 'N', NULL, 'N', '2023-06-27 22:49:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1678667508563898369, 1678667508563898370, 1, '1', 5, 'defaultBucket', 'bg-login.jpg', 'jpg', 249, '248.61 KB', '1678667508563898369.jpg', NULL, 'N', NULL, 'N', '2023-07-11 15:28:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1701802464433684482, 1701806078527803395, 1, '1', 5, 'defaultBucket', '租户.png', 'png', 4, '3.78 KB', '1701806078527803393.png', NULL, 'N', NULL, 'N', '2023-09-13 11:52:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1704330515919671298, 1704330515919671299, 1, '1', 5, 'defaultBucket', '登录背景.png', 'png', 104, '104.04 KB', '1704330515919671298.png', NULL, 'N', NULL, 'N', '2023-09-20 11:03:55', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1718932070425694209, 1718932070425694210, 1, '1', 5, 'defaultBucket', 'API服务备份.png', 'png', 4, '4.3 KB', '1718932070425694209.png', NULL, 'N', NULL, 'N', '2023-10-30 18:05:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1721101659015184385, 1721101659015184386, 1, '1', 5, 'defaultBucket', '单点认证.png', 'png', 5, '5.44 KB', '1721101659015184385.png', NULL, 'N', NULL, 'N', '2023-11-05 17:46:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1943143874188812290, 1943143874188812291, 1, '1', 4, 'defaultBucket', 'Snipaste_2025-05-26_20-16-29.jpg', 'jpg', 545, '544.66 KB', '1943143874188812290.jpg', NULL, 'N', 'cf09f2b2f80b3d71d7d6ede4e92cf601', 'N', '2025-07-10 11:02:56', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1943143919655067650, 1943143919655067651, 1, '1', 4, 'defaultBucket', '美波(1).jpg', 'jpg', 65, '65.01 KB', '1943143919655067650.jpg', NULL, 'N', '92c0652c7696b5805f6f025610bd41c8', 'N', '2025-07-10 11:03:07', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1945465353521872898, 1945465353521872899, 1, '1', 4, 'defaultBucket', 'Snipaste_2025-05-26_20-16-29(1).jpg', 'jpg', 238, '238.15 KB', '1945465353521872898.jpg', NULL, 'N', '2a4a2d56e1ca42b34db5afcf196eb5be', 'N', '2025-07-16 20:47:40', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1945474215238959105, 1945474215238959106, 1, '1', 4, 'defaultBucket', 'Snipaste_2025-05-26_20-16-29(1).jpg', 'jpg', 238, '238.15 KB', '1945474215238959105.jpg', NULL, 'N', '2a4a2d56e1ca42b34db5afcf196eb5be', 'N', '2025-07-16 21:22:53', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1945474278380011521, 1945474278380011522, 1, '1', 4, 'defaultBucket', '美波(1).jpg', 'jpg', 65, '65.01 KB', '1945474278380011521.jpg', NULL, 'N', '92c0652c7696b5805f6f025610bd41c8', 'N', '2025-07-16 21:23:08', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1948992232589742082, 1948992232589742083, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1948992232589742082.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-26 14:22:13', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1948992241552969729, 1948992241561358338, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1948992241552969729.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'Y', '2025-07-26 14:22:16', 1339550467939639299, '2025-07-26 14:38:36', 1339550467939639299);
INSERT INTO `sys_file_info` VALUES (1948992260846768129, 1948992260846768130, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1948992260846768129.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'Y', '2025-07-26 14:22:20', 1339550467939639299, '2025-07-26 14:38:36', 1339550467939639299);
INSERT INTO `sys_file_info` VALUES (1948993897040564226, 1948993897040564227, 1, '1', 4, 'defaultBucket', 'bg-login1.png', 'png', 882, '882.27 KB', '1948993897040564226.png', NULL, 'N', '773268c31157f4e3ba4b7403f8ab59e4', 'Y', '2025-07-26 14:28:50', 1339550467939639299, '2025-07-26 14:38:36', 1339550467939639299);
INSERT INTO `sys_file_info` VALUES (1948996401652318209, 1948996401652318210, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1948996401652318209.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-26 14:38:47', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1948996412079357953, 1948996412079357954, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1948996412079357953.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-26 14:38:50', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1948997614066221057, 1948997614066221058, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1948997614066221057.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-26 14:43:36', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1949753274223587329, 1949753274223587330, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1949753274223587329.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-28 16:46:20', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1949753283136483329, 1949753283136483330, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1949753283136483329.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-28 16:46:22', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info` VALUES (1950372507173306369, 1950372507173306370, 1, '1', 4, 'defaultBucket', '处理完成图片20250722215414.png', 'png', 438, '437.59 KB', '1950372507173306369.png', NULL, 'N', 'fb7d953c82cc6d55312f146d360167f9', 'N', '2025-07-30 09:46:57', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_group
-- ----------------------------
DROP TABLE IF EXISTS `sys_group`;
CREATE TABLE `sys_group`  (
  `group_id` bigint(20) NOT NULL COMMENT '分组id',
  `group_biz_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属业务类别，例如：PROJECT',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
  `business_id` bigint(20) NULL DEFAULT NULL COMMENT '业务主键id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '所属用户id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务分组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_hr_org_approver
-- ----------------------------
DROP TABLE IF EXISTS `sys_hr_org_approver`;
CREATE TABLE `sys_hr_org_approver`  (
  `org_approver_id` bigint(20) NOT NULL COMMENT '主键id',
  `org_approver_type` tinyint(4) NULL DEFAULT NULL COMMENT '组织审批类型：1-负责人，2-部长，3-体系负责人，4-部门助理，5-资产助理（专员），6-考勤专员，7-HRBP，8-门禁员，9-办公账号员，10-转岗须知员',
  `org_id` bigint(20) NULL DEFAULT NULL COMMENT '组织机构id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`org_approver_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织机构审批人' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_hr_organization
-- ----------------------------
DROP TABLE IF EXISTS `sys_hr_organization`;
CREATE TABLE `sys_hr_organization`  (
  `org_id` bigint(20) NOT NULL COMMENT '主键',
  `org_parent_id` bigint(20) NOT NULL COMMENT '父id，一级节点父id是-1',
  `org_pids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父ids',
  `org_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组织名称',
  `org_short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组织机构简称',
  `org_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组织编码',
  `org_sort` decimal(10, 2) NOT NULL COMMENT '排序',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `org_type` tinyint(4) NULL DEFAULT 1 COMMENT '组织机构类型：1-公司，2-部门',
  `tax_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税号',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `org_level` int(11) NULL DEFAULT NULL COMMENT '组织机构层级',
  `master_org_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对接外部主数据的机构id',
  `master_org_parent_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对接外部主数据的父级机构id',
  `expand_field` json NULL COMMENT '拓展字段',
  `level_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '层级的编码，来自sys_hr_organization_level表',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`org_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织机构信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_hr_organization
-- ----------------------------
INSERT INTO `sys_hr_organization` VALUES (1671418869810540546, -1, '[-1],', '北京公司', '总公司', '10000000', 10.00, 1, 1, NULL, NULL, NULL, '', NULL, NULL, NULL, 1, 'N', '2023-06-21 15:35:09', -1, '2023-06-28 21:51:04', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1671419146928205826, 1671418869810540546, '[-1],[1671418869810540546],', '信息中心', '信息中心', '10010000', 101.00, 1, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 3, 'N', '2023-06-21 15:35:09', -1, '2023-06-28 23:18:55', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1671419297969287170, 1671418869810540546, '[-1],[1671418869810540546],', '发展规划部', '发展规划部', '10020000', 102.00, 1, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 'N', '2023-06-21 15:35:09', -1, '2023-07-11 13:55:14', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1671419436767195137, 1671418869810540546, '[-1],[1671418869810540546],', '法律部', '法律部', '10030000', 103.00, 1, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 'N', '2023-06-21 15:35:09', -1, '2023-07-11 13:55:19', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1671419890196623362, 1671418869810540546, '[-1],[1671418869810540546],', '北京西城区公司', '北京西城区能源管理公司', '10040000', 104.00, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, 'N', '2023-06-21 15:35:09', -1, '2023-06-29 11:44:26', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1671420255612776449, 1671419890196623362, '[-1],[1671418869810540546],[1671419890196623362],', '能源管理部', '能源部', '10040001', 1040.00, 2, 2, NULL, '', NULL, NULL, NULL, NULL, NULL, 2, 'N', '2023-06-21 15:35:09', -1, '2023-06-29 11:44:26', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1674672546647220226, -1, '[-1],', '南京公司', NULL, '20000000', 20.00, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 'N', '2023-06-30 14:53:44', 1339550467939639299, '2023-07-11 14:03:25', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1674675494710255617, 1674672546647220226, '[-1],[1674672546647220226],', '综合管理部', NULL, '20000001', 201.00, 1, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 'N', '2023-06-30 15:05:27', 1339550467939639299, '2023-07-11 13:55:54', 1339550467939639299, 1);
INSERT INTO `sys_hr_organization` VALUES (1943145071222202371, -1, '[-1],', '租户1', NULL, 'z1', 0.00, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'N', '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_organization` VALUES (1943565929107431426, -1, '[-1],', '北京A', NULL, 'bja', 1.00, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'N', '2025-07-11 15:00:02', 1943145071222202370, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_organization` VALUES (1945471195939991553, -1, '[-1],', 'aaa', NULL, 'aaa', 2.00, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'N', '2025-07-16 21:10:53', 1943145071222202370, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_organization` VALUES (1945471246280028163, -1, '[-1],', 'bbb', NULL, 'bbb', 3.00, 1, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'N', '2025-07-16 21:11:05', 1943145071222202370, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_organization` VALUES (1945471338479218690, -1, '[-1],', 'ddd', 'dd', 'dd', 4.00, 1, 1, '12123123123', 'ddd', NULL, NULL, NULL, NULL, NULL, 0, 'N', '2025-07-16 21:11:27', 1943145071222202370, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_organization` VALUES (1945474242145419266, -1, '[-1],', '租户2', NULL, 'z2', 0.00, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL, 1945474242082504705);

-- ----------------------------
-- Table structure for sys_hr_organization_level
-- ----------------------------
DROP TABLE IF EXISTS `sys_hr_organization_level`;
CREATE TABLE `sys_hr_organization_level`  (
  `org_level_id` bigint(20) NOT NULL COMMENT '层级的id',
  `level_number` int(11) NOT NULL COMMENT '层级的级别，例如：1、2',
  `level_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '层级的名称',
  `level_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '层级的编码，需要填在org表中',
  `level_color` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '层级的颜色，16进制，不带#',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`org_level_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织机构层级' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_hr_position
-- ----------------------------
DROP TABLE IF EXISTS `sys_hr_position`;
CREATE TABLE `sys_hr_position`  (
  `position_id` bigint(20) NOT NULL COMMENT '主键',
  `position_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位名称',
  `position_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位编码',
  `position_sort` decimal(10, 2) NOT NULL COMMENT '排序',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`position_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '职位信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_hr_position
-- ----------------------------
INSERT INTO `sys_hr_position` VALUES (1671418731163627522, '员工', 'yg', 1000.00, 1, NULL, NULL, 1, 'N', '2023-06-21 15:28:21', -1, NULL, NULL, 1);
INSERT INTO `sys_hr_position` VALUES (1671418794157879297, '部门负责人', 'bmfzr', 200.00, 1, NULL, NULL, 1, 'N', '2023-06-29 10:20:54', NULL, '2023-06-29 10:21:19', 1673708058896797697, 1);
INSERT INTO `sys_hr_position` VALUES (1671418831935975426, '总经理', 'zjl', 100.00, 1, '', NULL, 1, 'N', '2023-06-21 15:28:21', -1, '2023-06-29 10:21:10', 1673708058896797697, 1);
INSERT INTO `sys_hr_position` VALUES (1943145071222202372, '员工', 'employee', 0.00, 1, NULL, NULL, 0, 'N', '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_position` VALUES (1943565658943922179, '负责人', 'fzr', 1.00, 1, NULL, NULL, 0, 'N', '2025-07-11 14:58:57', 1943145071222202370, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_hr_position` VALUES (1945474242145419267, '员工', 'employee', 0.00, 1, NULL, NULL, 0, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL, 1945474242082504705);

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(20) NOT NULL COMMENT '主键',
  `menu_parent_id` bigint(20) NOT NULL COMMENT '父id，顶级节点的父id是-1',
  `menu_pids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父id集合，中括号包住，逗号分隔',
  `menu_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单的名称',
  `menu_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单的编码',
  `app_id` bigint(20) NOT NULL COMMENT '所属应用id',
  `menu_sort` decimal(20, 2) NOT NULL DEFAULT 100.00 COMMENT '排序',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `menu_type` tinyint(4) NULL DEFAULT NULL COMMENT '菜单类型：10-后台菜单，20-纯前台路由界面，30-内部链接，40-外部链接，50-应用设计',
  `antdv_router` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由地址，浏览器显示的URL，例如/menu',
  `antdv_component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '前端组件名',
  `antdv_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'icon-default' COMMENT '图标编码',
  `antdv_link_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部链接地址',
  `antdv_active_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用于非菜单显示页面的重定向url设置',
  `antdv_visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'Y' COMMENT '是否可见(分离版用)：Y-是，N-否',
  `app_design_business_id` bigint(20) NULL DEFAULT NULL COMMENT '应用设计的业务id',
  `app_design_view_id` bigint(20) NULL DEFAULT NULL COMMENT '应用设计对应业务的视图id',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统菜单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1671406619464953857, -1, '[-1],', '门户主页', 'PORTAL_INDEX', 1671406669800796161, 101.00, 1, NULL, 20, '/index', '/index/index', 'icon-nav-zhuye', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2023-06-21 14:39:24', NULL, '2024-03-11 12:58:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407186899759106, -1, '[-1],', '个人信息', 'PERSONAL_INFO', 1671406669800796161, 102.00, 1, NULL, 20, '/index/personal', '/index/personal', 'icon-xiala-gerenxinxi', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2023-06-21 14:40:04', NULL, '2024-03-11 12:58:48', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407312775016450, -1, '[-1],', '组织架构', 'ORG_INDEX', 1671406745336016898, 101.00, 1, NULL, 10, '/system/structure', '', 'icon-menu-zuzhijiagou', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407539607171073, 1671407312775016450, '[-1],[1671407312775016450],', '人员', 'ORG_USER', 1671406745336016898, 10101.00, 1, NULL, 10, '/system/structure/user', '/system/structure/user/index', 'icon-menu-renyuan', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407615163363330, 1671407312775016450, '[-1],[1671407312775016450],', '机构', 'ORG_LIST', 1671406745336016898, 10102.00, 1, NULL, 10, '/system/structure/organization', '/system/structure/organization/index', 'icon-menu-jigou', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407652933070850, 1671407312775016450, '[-1],[1671407312775016450],', '职位', 'ORG_POSITION', 1671406745336016898, 10103.00, 1, NULL, 10, '/system/structure/position', '/system/structure/position/index', 'icon-menu-zhiwei', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-06-21 14:42:07', NULL, '2024-03-11 11:34:53', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407791416406018, -1, '[-1],', '权限控制', 'AUTH_CONTROL', 1671406745336016898, 102.00, 1, NULL, 10, '/system/auth', '', 'icon-menu-quanxiankongzhi', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407892205531137, 1671407791416406018, '[-1],[1671407791416406018],', '应用', 'AUTH_APP', 1671406745336016898, 10201.00, 1, NULL, 10, '/system/auth/app', '/system/auth/app/index', 'icon-menu-yingyong', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671407967690420226, 1671407791416406018, '[-1],[1671407791416406018],', '角色', 'AUTH_ROLE', 1671406745336016898, 10202.00, 1, NULL, 10, '/system/auth/role', '/system/auth/role/index', 'icon-menu-juese', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671408081100206081, 1671407791416406018, '[-1],[1671407791416406018],', '权限', 'AUTH_PERMISSION', 1671406745336016898, 10203.00, 1, NULL, 10, '/system/auth/permission', '/system/auth/permission/index', 'icon-menu-quanxian', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671408144094457858, 1671407791416406018, '[-1],[1671407791416406018],', '菜单', 'AUTH_MENU', 1671406745336016898, 10204.00, 1, NULL, 10, '/system/auth/menu', '/system/auth/menu/index', 'icon-menu-caidan', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1671408194501603329, 1671407791416406018, '[-1],[1671407791416406018],', '资源', 'AUTH_RESOURCE', 1671406745336016898, 10205.00, 1, NULL, 10, '/system/auth/resource', '/system/auth/resource/index', 'icon-menu-ziyuan', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-21 14:42:07', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673524613037191169, -1, '[-1],', '后台配置', 'BACKEND_CONFIG', 1671406745336016898, 103.00, 1, NULL, 10, '/backend/config', '/backend/config', 'icon-menu-peizhi', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673524865274245118, 1673524613037191169, '[-1],[1673524613037191169],', '系统配置', 'SYS_CONFIG', 1671406745336016898, 10301.00, 1, NULL, 10, '/system/backend/sys_config', '/system/backend/sys-config/index', 'icon-menu-peizhi', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673524865274245121, 1673524613037191169, '[-1],[1673524613037191169],', '字典', 'DICT', 1671406745336016898, 10302.00, 1, NULL, 10, '/system/backend/dict', '/system/backend/dict/index', 'icon-menu-zidian', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673524941069512706, 1673524613037191169, '[-1],[1673524613037191169],', '在线用户', 'ONLINE_USER', 1671406745336016898, 10303.00, 1, NULL, 10, '/system/backend/online', '/system/backend/online/index', 'icon-menu-zaixianyonghu', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673524966277279745, 1673524613037191169, '[-1],[1673524613037191169],', '定时任务', 'TIMER', 1671406745336016898, 10304.00, 1, NULL, 10, '/system/backend/timer', '/system/backend/timer/index', 'icon-menu-dingshirenwu', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673525004151844865, 1673524613037191169, '[-1],[1673524613037191169],', '文件管理', 'FILE_MANAGER', 1671406745336016898, 10305.00, 1, NULL, 10, '/system/backend/file', '/system/backend/file/index', 'icon-menu-wenjianguanli', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673525357136080898, 1673524613037191169, '[-1],[1673524613037191169],', '多数据源', 'MULTI_DS', 1671406745336016898, 10306.00, 1, NULL, 10, '/system/backend/datasource', '/system/backend/datasource/index', 'icon-menu-duoshujuyuan', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-06-27 11:15:17', NULL, '2023-07-07 17:09:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673525659931275265, 1673524613037191169, '[-1],[1673524613037191169],', '日志查看', 'LOG_MANAGER', 1671406745336016898, 10307.00, 1, NULL, 10, '/system/backend/log', '/system/backend/log/index', 'icon-menu-rizhichakan', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-06-27 11:15:17', NULL, '2024-03-11 12:57:08', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673525912344489985, 1673524613037191169, '[-1],[1673524613037191169],', '监控管理', 'MONITOR', 1671406745336016898, 10308.00, 1, '', 10, '/system/backend/monitor', '/system/backend/monitor/index', 'icon-menu-jiankongguanli', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-06-27 11:15:17', NULL, '2024-03-11 12:57:17', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1673526656565014530, 1673524613037191169, '[-1],[1673524613037191169],', '主题配置', 'THEME', 1671406745336016898, 10309.00, 1, NULL, 10, '/system/backend/theme', '/system/backend/theme/index', 'icon-menu-zhutipeizhi', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-06-27 11:15:17', NULL, '2024-03-11 12:57:23', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1701802336574521345, -1, '[-1],', '租户管理', 'TENANT', 1701801589128577026, 100.00, 1, NULL, 10, '/tenant', '', 'icon-menu-zuhuguanli', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-09-13 11:37:50', 1339550467939639299, '2024-01-23 11:06:34', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1710533513796653058, 1673524613037191169, '[-1],[1673524613037191169],', '安全策略', 'anquancelve', 1671406745336016898, 10310.00, 1, NULL, 10, '/backend/security', '/system/backend/security/index', 'icon-menu-anquancelue', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2023-10-07 13:52:25', 1339550467939639299, '2024-03-11 12:57:30', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1711590804271976449, -1, '[-1],', '审计日志', 'shenjirizhi', 1671406745336016898, 104.00, 1, NULL, 10, '/shenjirizhi', '/shenjirizhi', 'icon-menu-shenjirizhi', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-10-10 11:53:42', 1339550467939639299, '2023-10-10 14:04:49', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1711591595250601985, 1711590804271976449, '[-1],[1711590804271976449],', '日常运维', 'richangyunwei', 1671406745336016898, 10401.00, 1, NULL, 10, '/system/audit/normal', '/system/audit/normal/index', 'icon-menu-richangyunwei', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-10-10 11:56:51', 1339550467939639299, '2023-10-10 14:04:49', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1711591743930290178, 1711590804271976449, '[-1],[1711590804271976449],', '安全操作', 'anquancaozuo', 1671406745336016898, 10402.00, 1, NULL, 10, '/system/audit/secure', '/system/audit/secure/index', 'icon-menu-anquancaozuo', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2023-10-10 11:57:26', 1339550467939639299, '2023-10-10 14:04:49', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1718824432245411841, -1, '[-1],', 'API认证', 'ApiRenZheng', 1717100945571057665, 100.00, 1, NULL, 10, '/apiAuth', NULL, 'icon-menu-apirenzheng', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-10-30 10:57:34', 1339550467939639299, '2023-10-30 11:21:58', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1718826323209621506, 1718824432245411841, '[-1],[1718824432245411841],', '外部应用', 'waibuyingyong', 1717100945571057665, 100.00, 1, NULL, 10, '/apiAuth/externalApp', '/api-auth/external-app/index', 'icon-menu-waibuyingyong', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2023-10-30 11:05:04', 1339550467939639299, '2023-10-30 11:22:28', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1718826580437897217, 1718824432245411841, '[-1],[1718824432245411841],', '接口', 'jiekou', 1717100945571057665, 110.00, 1, NULL, 10, '/apiAuth/interface', '/api-auth/interface/index', 'icon-menu-jiekou', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-10-30 11:06:06', 1339550467939639299, '2024-03-15 23:00:40', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1718826709295304705, 1718824432245411841, '[-1],[1718824432245411841],', '接口授权', 'jiekoushouquan', 1717100945571057665, 120.00, 1, NULL, 10, '/apiAuth/interface/authorization', '/api-auth/authorization/index', 'icon-menu-jiekoushouquan', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-10-30 11:06:36', 1339550467939639299, '2024-03-15 23:00:48', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1720008907121758209, -1, '[-1],', '临时秘钥', 'linshimiyao', 1671406745336016898, 105.00, 1, NULL, 10, '/system/temporaryKey', '/temporary-key/index', 'icon-menu-linshimiyao', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2023-11-02 17:24:14', 1339550467939639299, '2023-11-02 17:24:40', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1721100330028990466, -1, '[-1],', '应用接入', 'yingyongjieru', 1721100056577146881, 100.00, 1, NULL, 10, '/ssoClient/manage', '/sso-client/sso-client', 'icon-menu-yingyongjieru', NULL, NULL, 'Y', NULL, NULL, NULL, 6, 'N', '2023-11-05 17:41:10', 1339550467939639299, '2024-03-15 23:01:00', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1746839841395138562, -1, '[-1],', '通知公告', 'tongzhigonggao', 1671406745336016898, 104.00, 1, NULL, 10, '/system/notice', '/system/backend/notice/index', 'icon-menu-wodexiaoxi', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2024-01-15 18:20:48', 1339550467939639299, '2024-03-11 10:14:06', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1747806631403159554, 1671407312775016450, '[-1],[1671407312775016450],', '授权', 'shouquan', 1671406745336016898, 10104.00, 1, NULL, 10, '/system/structure/empower', '/system/structure/empower/index', 'icon-menu-shouquan', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2024-01-18 10:22:28', 1339550467939639299, '2024-03-11 11:34:30', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1749629803979042817, 1701802336574521345, '[-1],[1701802336574521345],', '租户管理', 'zuhuguanli1', 1701801589128577026, 100.00, 1, NULL, 10, '/tenant/manage', '/tenant/manage/index', 'icon-menu-zuhuguanli', NULL, NULL, 'Y', NULL, NULL, NULL, 0, 'N', '2024-01-23 11:07:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1749630086079541249, 1701802336574521345, '[-1],[1701802336574521345],', '功能包管理', 'gongnengbaoguanli', 1701801589128577026, 110.00, 1, NULL, 10, '/tenant/package', '/tenant/package/index', 'icon-menu-gongnengbao', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2024-01-23 11:08:14', 1339550467939639299, '2024-03-11 13:12:24', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1760838262674116609, 1701802336574521345, '[-1],[1701802336574521345],', '租户审核', 'zuhushenhe', 1701801589128577026, 120.00, 1, NULL, 10, '/tenant/examine', '/tenant/examine/index', 'icon-menu-zuhushenhe', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2024-02-23 09:25:31', 1339550467939639299, '2024-03-11 13:12:32', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000010, -1, '[-1],', 'ERP基础资料管理', 'ERP_MANAGER', 1815000000000000001, 101.00, 1, NULL, 10, '/erp', '', 'icon-quanbuxiangmu1', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2025-07-20 19:19:05', -1, '2025-07-26 14:55:43', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000020, 1815000000000000010, '[-1],[1815000000000000010],', '基础数据', 'ERP_BASE_DATA', 1815000000000000001, 10105.00, 1, NULL, 10, '/erp/basedata', '', 'icon-menu-jichushuju', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'Y', '2025-07-20 19:19:05', -1, '2025-07-24 23:04:57', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000030, 1815000000000000010, '[-1],[1815000000000000010],', '供应商管理', 'ERP_SUPPLIER', 1815000000000000001, 10102.00, 1, NULL, 10, '/erp/basedata/supplier', '/erp/supplier/index', 'icon-quanbuxiangmu1', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2025-07-20 19:19:05', -1, '2025-07-26 14:52:42', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000040, 1815000000000000010, '[-1],[1815000000000000010],', '客户管理', 'ERP_CUSTOMER', 1815000000000000001, 10101.00, 1, NULL, 10, '/erp/basedata/customer', '/erp/customer/index', 'icon-quanbuxiangmu1', NULL, NULL, 'Y', NULL, NULL, NULL, 2, 'N', '2025-07-20 19:19:05', -1, '2025-07-26 14:52:37', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000050, 1815000000000000010, '[-1],[1815000000000000010],', '商品管理', 'ERP_PRODUCT', 1815000000000000001, 10104.00, 1, NULL, 10, '/erp/basedata/product', '/erp/product/index', 'icon-quanbuxiangmu1', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2025-07-20 19:19:05', -1, '2025-07-26 14:52:53', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000060, 1815000000000000010, '[-1],[1815000000000000010],', '区域管理', 'ERP_REGION', 1815000000000000001, 10103.00, 1, NULL, 10, '/erp/basedata/region', '/erp/region/index', 'icon-quanbuxiangmu1', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2025-07-20 19:19:05', -1, '2025-07-26 14:52:48', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000070, 1815000000000000010, '[-1],[1815000000000000010],', '产品分类管理', 'ERP_PRODUCT_CATEGORY', 1815000000000000001, 10105.00, 1, NULL, 10, '/erp/basedata/productCategory', '/erp/productCategory/index', 'icon-quanbuxiangmu1', NULL, NULL, 'Y', NULL, NULL, NULL, 3, 'N', '2025-07-21 21:00:00', -1, '2025-07-26 14:52:57', 1339550467939639299);
INSERT INTO `sys_menu` VALUES (1815000000000000080, 1815000000000000010, '[-1],[1815000000000000010],', '采购入库管理', 'ERP_PURCHASE_INBOUND', 1815000000000000001, 10106.00, 1, '采购入库单管理，包括新增、编辑、确认、入库等操作', 10, '/erp/purchase/inbound', '/erp/purchase/inbound/index', 'icon-menu-caigouruku', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2025-07-29 21:09:11', 1, '2025-07-29 21:09:11', 1);
INSERT INTO `sys_menu` VALUES (1815000000000000090, 1815000000000000010, '[-1],[1815000000000000010],', '库存管理', 'ERP_INVENTORY', 1815000000000000001, 10107.00, 1, '库存查询、调整、预警设置、历史记录等管理功能', 10, '/erp/inventory', '/erp/inventory/index', 'icon-menu-kucunguanli', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', '2025-07-29 21:09:11', 1, '2025-07-29 21:09:11', 1);

-- ----------------------------
-- Table structure for sys_menu_options
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu_options`;
CREATE TABLE `sys_menu_options`  (
  `menu_option_id` bigint(20) NOT NULL COMMENT '主键',
  `app_id` bigint(20) NULL DEFAULT NULL COMMENT '冗余字段，菜单所属的应用id',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单id',
  `option_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能或操作的名称',
  `option_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能或操作的编码',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`menu_option_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单下的功能操作' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu_options
-- ----------------------------
INSERT INTO `sys_menu_options` VALUES (1671416717948006401, 1671406745336016898, 1671407539607171073, '新增人员', 'ADD_USER', '2023-06-21 15:17:48', NULL, '2023-07-07 15:09:31', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1671416755763851265, 1671406745336016898, 1671407539607171073, '修改人员', 'EDIT_USER', '2023-06-21 15:17:48', NULL, '2023-07-07 15:09:34', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1675495204221640706, 1671406745336016898, 1671407615163363330, '新增机构', 'ADD_ORG', '2023-07-02 21:22:41', 1339550467939639299, '2023-07-07 15:10:13', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677199976008040449, 1671406669800796161, 1671406619464953857, '公司概况统计', 'COMPANY_STAT_INFO', '2023-07-07 14:16:50', 1339550467939639299, '2023-07-07 14:16:57', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677205540070064129, 1671406745336016898, 1671407539607171073, '删除人员', 'DELETE_USER', '2023-07-07 14:38:56', 1339550467939639299, '2023-07-07 15:09:37', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677205784526684162, 1671406745336016898, 1671407539607171073, '分配角色', 'ASSIGN_USER_ROLE', '2023-07-07 14:39:55', 1339550467939639299, '2023-07-07 15:09:40', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677205870040154114, 1671406745336016898, 1671407539607171073, '重置密码', 'RESET_PASSWORD', '2023-07-07 14:40:15', 1339550467939639299, '2023-07-07 15:09:43', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677205994816503809, 1671406745336016898, 1671407539607171073, '修改状态', 'UPDATE_USER_STATUS', '2023-07-07 14:40:45', 1339550467939639299, '2023-07-07 15:09:46', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677212372381564929, 1671406745336016898, 1671407652933070850, '新增职位', 'ADD_POSITION', '2023-07-07 15:06:05', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677212407240425474, 1671406745336016898, 1671407652933070850, '修改职位', 'EDIT_POSITION', '2023-07-07 15:06:14', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677212448021643265, 1671406745336016898, 1671407652933070850, '删除职位', 'DELETE_POSITION', '2023-07-07 15:06:23', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677213466805501954, 1671406745336016898, 1671407615163363330, '修改机构', 'EDIT_ORG', '2023-07-07 15:10:26', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677213504298385410, 1671406745336016898, 1671407615163363330, '删除机构', 'DELETE_ORG', '2023-07-07 15:10:35', 1339550467939639299, '2023-07-07 15:11:52', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1677213572741038081, 1671406745336016898, 1671407615163363330, '设置审批人', 'ASSIGN_APPROVER', '2023-07-07 15:10:52', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677216034650685442, 1671406745336016898, 1671407892205531137, '新增应用', 'ADD_APP', '2023-07-07 15:20:38', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677216100685807617, 1671406745336016898, 1671407892205531137, '修改应用', 'EDIT_APP', '2023-07-07 15:20:54', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677216141127286786, 1671406745336016898, 1671407892205531137, '删除应用', 'DELETE_APP', '2023-07-07 15:21:04', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677223921938694145, 1671406745336016898, 1671407892205531137, '更新应用状态', 'UPDATE_APP_STATUS', '2023-07-07 15:51:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677228381343678466, 1671406745336016898, 1671407967690420226, '新增角色', 'ADD_ROLE', '2023-07-07 16:09:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677228480924844033, 1671406745336016898, 1671407967690420226, '删除角色', 'DELETE_ROLE', '2023-07-07 16:10:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677228556107743233, 1671406745336016898, 1671407967690420226, '修改角色', 'EDIT_ROLE', '2023-07-07 16:10:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1677229379281846273, 1671406745336016898, 1671408081100206081, '修改权限', 'CHANGE_ROLE_PERMISSION', '2023-07-07 16:13:40', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1678056521171140609, 1671406745336016898, 1673524865274245121, '新增字典', 'ADD_DICT', '2023-07-09 23:00:26', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1678056564691238914, 1671406745336016898, 1673524865274245121, '修改字典', 'EDIT_DICT', '2023-07-09 23:00:36', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1678056611675832321, 1671406745336016898, 1673524865274245121, '删除字典', 'DELETE_DICT', '2023-07-09 23:00:48', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1680610475986931714, 1671406745336016898, 1671408081100206081, '修改数据权限', 'CHANGE_ROLE_DATA_SCOPE', '2023-07-17 00:08:56', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1711189530448498690, 1671406745336016898, 1671408081100206081, '修改权限的权限范围', 'CHANGE_ROLE_BIND_LIMIT', '2023-10-09 09:19:11', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1766013401104175106, 1671406745336016898, 1673525659931275265, '业务日志', 'BUSINESS_LOG', '2024-03-08 16:09:40', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1766013493466943489, 1671406745336016898, 1673525659931275265, '登录日志', 'LOG_LOGIN', '2024-03-08 16:10:02', 1339550467939639299, '2024-03-08 16:41:49', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1766013586857316353, 1671406745336016898, 1673525659931275265, 'API日志', 'OPERATE_LOG', '2024-03-08 16:10:25', 1339550467939639299, '2024-03-08 16:41:24', 1339550467939639299);
INSERT INTO `sys_menu_options` VALUES (1766025381470851073, 1671406745336016898, 1673525912344489985, 'SQL监控', 'SQL_MONITOR', '2024-03-08 16:57:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1766025455466762241, 1671406745336016898, 1673525912344489985, '服务器信息', 'SERVER_MONITOR', '2024-03-08 16:57:34', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1766027897772232705, 1671406745336016898, 1673526656565014530, '主题管理', 'THEME_MANAGER', '2024-03-08 17:07:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1766027932140359682, 1671406745336016898, 1673526656565014530, '主题模板', 'THEME_TEMPLATE', '2024-03-08 17:07:25', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1766027969373196290, 1671406745336016898, 1673526656565014530, '主题属性', 'THEME_ATTR', '2024-03-08 17:07:34', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1811042155548205058, 1671406745336016898, 1710533513796653058, '维护黑白名单', 'BLACK_WHITE_LIST_UPDATE', '2024-07-10 22:17:52', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1811282123950862338, 1671406745336016898, 1710533513796653058, '密码策略配置', 'PASSWORD_STRATEGY_CONFIG', '2024-07-11 14:11:25', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1811330833279811585, 1671406745336016898, 1673525659931275265, '安全日志', 'SECURITY_LOG', '2024-07-11 17:24:58', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000081, 1815000000000000001, 1815000000000000080, '查看', 'ERP_PURCHASE_INBOUND_VIEW', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000082, 1815000000000000001, 1815000000000000080, '新增', 'ERP_PURCHASE_INBOUND_ADD', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000083, 1815000000000000001, 1815000000000000080, '编辑', 'ERP_PURCHASE_INBOUND_EDIT', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000084, 1815000000000000001, 1815000000000000080, '删除', 'ERP_PURCHASE_INBOUND_DELETE', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000085, 1815000000000000001, 1815000000000000080, '确认', 'ERP_PURCHASE_INBOUND_CONFIRM', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000086, 1815000000000000001, 1815000000000000080, '入库', 'ERP_PURCHASE_INBOUND_RECEIVE', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000087, 1815000000000000001, 1815000000000000080, '导出', 'ERP_PURCHASE_INBOUND_EXPORT', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000091, 1815000000000000001, 1815000000000000090, '查看', 'ERP_INVENTORY_VIEW', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000092, 1815000000000000001, 1815000000000000090, '库存调整', 'ERP_INVENTORY_ADJUST', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000093, 1815000000000000001, 1815000000000000090, '查看历史', 'ERP_INVENTORY_HISTORY', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000094, 1815000000000000001, 1815000000000000090, '设置预警值', 'ERP_INVENTORY_SET_MIN_STOCK', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000095, 1815000000000000001, 1815000000000000090, '库存统计', 'ERP_INVENTORY_STATISTICS', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000096, 1815000000000000001, 1815000000000000090, '导出报表', 'ERP_INVENTORY_EXPORT', '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_menu_options` VALUES (1815000000000000100, 1815000000000000001, 1815000000000000030, '新增供应商', 'ADD_SUPPLIER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000101, 1815000000000000001, 1815000000000000030, '修改供应商', 'EDIT_SUPPLIER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000102, 1815000000000000001, 1815000000000000030, '删除供应商', 'DELETE_SUPPLIER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000103, 1815000000000000001, 1815000000000000030, '批量删除供应商', 'BATCH_DELETE_SUPPLIER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000104, 1815000000000000001, 1815000000000000030, '查看供应商详情', 'VIEW_SUPPLIER_DETAIL', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000105, 1815000000000000001, 1815000000000000030, '更新供应商状态', 'UPDATE_SUPPLIER_STATUS', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000106, 1815000000000000001, 1815000000000000030, '导出供应商', 'EXPORT_SUPPLIER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000107, 1815000000000000001, 1815000000000000030, '分页查询供应商', 'PAGE_SUPPLIER', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000108, 1815000000000000001, 1815000000000000030, '查询供应商列表', 'LIST_SUPPLIER', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000110, 1815000000000000001, 1815000000000000040, '新增客户', 'ADD_CUSTOMER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000111, 1815000000000000001, 1815000000000000040, '修改客户', 'EDIT_CUSTOMER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000112, 1815000000000000001, 1815000000000000040, '删除客户', 'DELETE_CUSTOMER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000113, 1815000000000000001, 1815000000000000040, '批量删除客户', 'BATCH_DELETE_CUSTOMER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000114, 1815000000000000001, 1815000000000000040, '查看客户详情', 'VIEW_CUSTOMER_DETAIL', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000115, 1815000000000000001, 1815000000000000040, '更新客户状态', 'UPDATE_CUSTOMER_STATUS', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000116, 1815000000000000001, 1815000000000000040, '导出客户', 'EXPORT_CUSTOMER', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000117, 1815000000000000001, 1815000000000000040, '分页查询客户', 'PAGE_CUSTOMER', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000118, 1815000000000000001, 1815000000000000040, '查询客户列表', 'LIST_CUSTOMER', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000120, 1815000000000000001, 1815000000000000050, '新增商品', 'ADD_PRODUCT', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000121, 1815000000000000001, 1815000000000000050, '修改商品', 'EDIT_PRODUCT', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000122, 1815000000000000001, 1815000000000000050, '删除商品', 'DELETE_PRODUCT', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000123, 1815000000000000001, 1815000000000000050, '批量删除商品', 'BATCH_DELETE_PRODUCT', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000124, 1815000000000000001, 1815000000000000050, '查看商品详情', 'VIEW_PRODUCT_DETAIL', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000125, 1815000000000000001, 1815000000000000050, '更新商品状态', 'UPDATE_PRODUCT_STATUS', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000126, 1815000000000000001, 1815000000000000050, '导出商品', 'EXPORT_PRODUCT', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000127, 1815000000000000001, 1815000000000000050, '分页查询商品', 'PAGE_PRODUCT', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000128, 1815000000000000001, 1815000000000000050, '查询商品列表', 'LIST_PRODUCT', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000130, 1815000000000000001, 1815000000000000060, '新增区域', 'ADD_REGION', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000131, 1815000000000000001, 1815000000000000060, '修改区域', 'EDIT_REGION', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000132, 1815000000000000001, 1815000000000000060, '删除区域', 'DELETE_REGION', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000133, 1815000000000000001, 1815000000000000060, '查看区域详情', 'VIEW_REGION_DETAIL', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000134, 1815000000000000001, 1815000000000000060, '更新区域状态', 'UPDATE_REGION_STATUS', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000135, 1815000000000000001, 1815000000000000060, '导出区域', 'EXPORT_REGION', '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000136, 1815000000000000001, 1815000000000000060, '分页查询区域', 'PAGE_REGION', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000137, 1815000000000000001, 1815000000000000060, '查询区域列表', 'LIST_REGION', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000138, 1815000000000000001, 1815000000000000060, '查询区域树形结构', 'TREE_REGION', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000140, 1815000000000000001, 1815000000000000070, '新增产品分类', 'ADD_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000141, 1815000000000000001, 1815000000000000070, '编辑产品分类', 'EDIT_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000142, 1815000000000000001, 1815000000000000070, '删除产品分类', 'DELETE_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000143, 1815000000000000001, 1815000000000000070, '批量删除产品分类', 'BATCH_DELETE_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000144, 1815000000000000001, 1815000000000000070, '查看产品分类详情', 'VIEW_PRODUCT_CATEGORY_DETAIL', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000145, 1815000000000000001, 1815000000000000070, '查询产品分类列表', 'LIST_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000146, 1815000000000000001, 1815000000000000070, '查询产品分类树形结构', 'TREE_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000147, 1815000000000000001, 1815000000000000070, '导出产品分类', 'EXPORT_PRODUCT_CATEGORY', '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000200, 1815000000000000001, 1815000000000000030, '供应商管理界面', 'ERP_SUPPLIER_MANAGE', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000210, 1815000000000000001, 1815000000000000040, '客户管理界面', 'ERP_CUSTOMER_MANAGE', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000220, 1815000000000000001, 1815000000000000050, '商品管理界面', 'ERP_PRODUCT_MANAGE', '2025-07-24 16:30:00', -1, NULL, NULL);
INSERT INTO `sys_menu_options` VALUES (1815000000000000230, 1815000000000000001, 1815000000000000060, '区域管理界面', 'ERP_REGION_MANAGE', '2025-07-24 16:30:00', -1, NULL, NULL);

-- ----------------------------
-- Table structure for sys_message
-- ----------------------------
DROP TABLE IF EXISTS `sys_message`;
CREATE TABLE `sys_message`  (
  `message_id` bigint(20) NOT NULL COMMENT '主键',
  `receive_user_id` bigint(20) NOT NULL COMMENT '接收用户id',
  `send_user_id` bigint(20) NULL DEFAULT NULL COMMENT '发送用户id',
  `message_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息标题',
  `message_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '消息内容',
  `message_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NORMAL' COMMENT '消息类型：NORMAL-普通类型，URL-带链接跳转',
  `message_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息跳转的URL',
  `priority_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'low' COMMENT '优先级：high-高优先级，middle-中，low-低',
  `message_send_time` datetime(0) NULL DEFAULT NULL COMMENT '消息发送时间',
  `business_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联业务id',
  `business_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型(根据业务id和业务类型可以确定业务数据)',
  `business_detail` json NULL COMMENT '业务的详细信息json',
  `read_flag` tinyint(4) NULL DEFAULT 0 COMMENT '阅读状态：0-未读，1-已读',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`message_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` bigint(20) NOT NULL COMMENT '主键',
  `notice_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `notice_summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '通知摘要',
  `notice_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '通知内容',
  `priority_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优先级，来自字典：high-高优先级，middle-中，low-低',
  `notice_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `notice_end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `notice_user_scope` json NULL COMMENT '通知范围，存用户id集合',
  `publish_status` tinyint(4) NULL DEFAULT NULL COMMENT '是否发布：1-已发布，2-未发布',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `expand_field` json NULL COMMENT '拓展字段',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_portal_user_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_portal_user_app`;
CREATE TABLE `sys_portal_user_app`  (
  `app_link_id` bigint(20) NOT NULL COMMENT '主键id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '所属用户id',
  `app_id` bigint(20) NULL DEFAULT NULL COMMENT '冗余字段，菜单所属的应用id',
  `menu_id` bigint(20) NULL DEFAULT NULL COMMENT '关联的菜单id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`app_link_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户常用功能' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_portal_user_app
-- ----------------------------
INSERT INTO `sys_portal_user_app` VALUES (1678674927629168641, 1339550467939639299, 1671406669800796161, 1671407186899759106, '2023-07-11 15:57:46', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1678674927629168642, 1339550467939639299, 1671406745336016898, 1671407539607171073, '2023-07-11 15:57:46', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1678674927629168643, 1339550467939639299, 1671406745336016898, 1671408081100206081, '2023-07-11 15:57:46', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1678674927629168644, 1339550467939639299, 1671406745336016898, 1671408144094457858, '2023-07-11 15:57:46', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1678674927629168645, 1339550467939639299, 1671406745336016898, 1673524865274245121, '2023-07-11 15:57:46', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1678674927629168646, 1339550467939639299, 1671406745336016898, 1671407615163363330, '2023-07-11 15:57:46', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1678716885718315009, 1678652551806959618, 1671406669800796161, 1671407186899759106, '2023-07-11 18:44:29', 1678652551806959618, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1815000000000000400, 1339550467939639299, 1815000000000000001, 1815000000000000010, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1815000000000000401, 1339550467939639299, 1815000000000000001, 1815000000000000030, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1815000000000000402, 1339550467939639299, 1815000000000000001, 1815000000000000040, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1815000000000000403, 1339550467939639299, 1815000000000000001, 1815000000000000050, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1815000000000000404, 1339550467939639299, 1815000000000000001, 1815000000000000060, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_portal_user_app` VALUES (1815000000000000405, 1339550467939639299, 1815000000000000001, 1815000000000000070, '2025-07-21 21:00:00', -1, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(20) NOT NULL COMMENT '主键id',
  `role_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色编码',
  `role_sort` decimal(10, 2) NOT NULL COMMENT '序号',
  `data_scope_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '数据范围类型：10-仅本人数据，20-本部门数据，30-本部门及以下数据，40-指定部门数据，50-全部数据',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `role_type` tinyint(4) NOT NULL DEFAULT 10 COMMENT '角色类型：10-系统角色，15-业务角色，20-公司角色',
  `role_category_id` bigint(20) NULL DEFAULT NULL COMMENT '所属的角色分类id，如果是业务角色和公司角色可以加上所属分类',
  `role_company_id` bigint(20) NULL DEFAULT NULL COMMENT '角色所属公司id，当角色类型为20时传此值',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1671420545250439170, '后台管理员', 'backendAdmin', 10.00, 50, 1, NULL, 10, NULL, NULL, NULL, 1, 'N', '2023-06-21 15:47:12', -1, NULL, NULL, 1);
INSERT INTO `sys_role` VALUES (1671420608181776386, '普通人员', 'employee', 20.00, 50, 1, NULL, 10, NULL, NULL, NULL, 1, 'N', '2023-06-21 15:47:12', -1, NULL, NULL, 1);
INSERT INTO `sys_role` VALUES (1711587568282554370, '系统管理员', 'systemManager', 1000.00, 50, 1, NULL, 10, NULL, NULL, NULL, 0, 'N', '2023-10-10 11:40:51', 1711617292694245378, NULL, NULL, 1);
INSERT INTO `sys_role` VALUES (1711587661928779778, '安全保密管理员', 'securityManager', 1001.00, 50, 1, NULL, 10, NULL, NULL, NULL, 1, 'N', '2023-10-10 11:41:13', 1711617292694245378, '2023-10-10 11:42:04', 1339550467939639299, 1);
INSERT INTO `sys_role` VALUES (1711587931903545346, '安全审计管理员', 'auditManager', 1002.00, 50, 1, NULL, 10, NULL, NULL, NULL, 0, 'N', '2023-10-10 11:42:17', 1711617292694245378, NULL, NULL, 1);
INSERT INTO `sys_role` VALUES (1943145071222202373, '租户1管理员', 'tenant_admin_role', 0.00, 50, 1, NULL, 10, NULL, 1943145071222202371, NULL, 0, 'N', '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_role` VALUES (1943145071222202374, '租户1基础角色', 'employee', 1.00, 10, 1, NULL, 20, NULL, 1943145071222202371, NULL, 0, 'N', '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_role` VALUES (1945474242145419268, '租户2管理员', 'tenant_admin_role', 0.00, 50, 1, NULL, 10, NULL, 1945474242145419266, NULL, 0, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL, 1945474242082504705);
INSERT INTO `sys_role` VALUES (1945474242145419269, '租户2基础角色', 'employee', 1.00, 10, 1, NULL, 20, NULL, 1945474242145419266, NULL, 0, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL, 1945474242082504705);

-- ----------------------------
-- Table structure for sys_role_category
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_category`;
CREATE TABLE `sys_role_category`  (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `category_parent_id` bigint(20) NOT NULL COMMENT '父级角色分类id',
  `category_pids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父级角色分类id集合',
  `role_category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色分类名称',
  `category_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '角色分类类型：15-业务角色，20-公司角色',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '所属公司id，当类型为20-公司角色时使用',
  `fld_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '角色分类排序',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_category
-- ----------------------------
INSERT INTO `sys_role_category` VALUES (1881997026537611265, -1, '[-1],', '默认业务分类', 15, NULL, 100.00, 'N', '2025-01-22 17:26:26', 1339550467939639299, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_data_scope
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_data_scope`;
CREATE TABLE `sys_role_data_scope`  (
  `role_data_scope_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `data_scope_type` tinyint(4) NOT NULL DEFAULT 20 COMMENT '数据范围类型：10-仅本人数据，20-本部门数据，30-本部门及以下数据，31-本公司及以下数据，32-指定机构层级及以下，40-指定机构集合数据，41-指定机构及以下，50-全部数据',
  `org_level_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '层级的编码，用在类型为32-指定层级及以下，情况时使用',
  `define_org_list` json NULL COMMENT '指定机构集合列表，用在类型为40-指定机构集合数据，情况时使用',
  `define_org_id` bigint(20) NULL DEFAULT NULL COMMENT '指定机构的id，用在类型为41-指定机构及以下，情况时使用',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`role_data_scope_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色数据范围' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_data_scope
-- ----------------------------
INSERT INTO `sys_role_data_scope` VALUES (1920774896534970369, 1671420545250439170, 50, NULL, NULL, NULL, '2025-05-09 17:36:36', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_limit
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_limit`;
CREATE TABLE `sys_role_limit`  (
  `role_limit_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `limit_type` tinyint(4) NOT NULL COMMENT '角色限制类型：1-角色可分配的菜单，2-角色可分配的功能',
  `business_id` bigint(20) NOT NULL COMMENT '业务id，为菜单id或菜单功能id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`role_limit_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色权限限制' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_limit
-- ----------------------------
INSERT INTO `sys_role_limit` VALUES (1945474242598404097, 1945474242145419268, 1, 1671406619464953857, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404098, 1945474242145419268, 1, 1671407652933070850, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404099, 1945474242145419268, 1, 1671407186899759106, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404100, 1945474242145419268, 1, 1671407539607171073, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404101, 1945474242145419268, 1, 1671407615163363330, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404102, 1945474242145419268, 2, 1675495204221640706, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404103, 1945474242145419268, 2, 1677205994816503809, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404104, 1945474242145419268, 2, 1677212448021643265, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404105, 1945474242145419268, 2, 1671416717948006401, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404106, 1945474242145419268, 2, 1671416755763851265, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404107, 1945474242145419268, 2, 1677213572741038081, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404108, 1945474242145419268, 2, 1677205870040154114, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404109, 1945474242145419268, 2, 1677212372381564929, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404110, 1945474242145419268, 2, 1677205784526684162, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404111, 1945474242145419268, 2, 1677213466805501954, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404112, 1945474242145419268, 2, 1677212407240425474, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404113, 1945474242145419268, 2, 1677199976008040449, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404114, 1945474242145419268, 2, 1677205540070064129, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1945474242598404115, 1945474242145419268, 2, 1677213504298385410, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183335763970, 1943145071222202373, 1, 1815000000000000070, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124097, 1943145071222202373, 1, 1671406619464953857, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124098, 1943145071222202373, 1, 1815000000000000050, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124099, 1943145071222202373, 1, 1671407652933070850, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124100, 1943145071222202373, 1, 1815000000000000030, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124101, 1943145071222202373, 1, 1671407186899759106, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124102, 1943145071222202373, 1, 1815000000000000060, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124103, 1943145071222202373, 1, 1815000000000000010, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124104, 1943145071222202373, 1, 1671407539607171073, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124105, 1943145071222202373, 1, 1671407615163363330, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124106, 1943145071222202373, 1, 1815000000000000040, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124107, 1943145071222202373, 2, 1815000000000000135, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124108, 1943145071222202373, 2, 1677205994816503809, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124109, 1943145071222202373, 2, 1815000000000000134, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124110, 1943145071222202373, 2, 1815000000000000133, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124111, 1943145071222202373, 2, 1815000000000000132, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124112, 1943145071222202373, 2, 1815000000000000131, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124113, 1943145071222202373, 2, 1815000000000000130, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124114, 1943145071222202373, 2, 1815000000000000128, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124115, 1943145071222202373, 2, 1815000000000000143, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124116, 1943145071222202373, 2, 1815000000000000142, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124117, 1943145071222202373, 2, 1815000000000000141, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183365124118, 1943145071222202373, 2, 1815000000000000140, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484225, 1943145071222202373, 2, 1815000000000000138, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484226, 1943145071222202373, 2, 1815000000000000137, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484227, 1943145071222202373, 2, 1815000000000000136, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484228, 1943145071222202373, 2, 1815000000000000147, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484229, 1943145071222202373, 2, 1815000000000000146, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484230, 1943145071222202373, 2, 1815000000000000145, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484231, 1943145071222202373, 2, 1815000000000000144, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484232, 1943145071222202373, 2, 1677212448021643265, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484233, 1943145071222202373, 2, 1677213572741038081, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484234, 1943145071222202373, 2, 1677212372381564929, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484235, 1943145071222202373, 2, 1677213504298385410, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484236, 1943145071222202373, 2, 1675495204221640706, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484237, 1943145071222202373, 2, 1815000000000000200, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484238, 1943145071222202373, 2, 1815000000000000210, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484239, 1943145071222202373, 2, 1815000000000000220, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484240, 1943145071222202373, 2, 1815000000000000103, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484241, 1943145071222202373, 2, 1815000000000000102, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484242, 1943145071222202373, 2, 1815000000000000230, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484243, 1943145071222202373, 2, 1815000000000000101, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484244, 1943145071222202373, 2, 1815000000000000100, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484245, 1943145071222202373, 2, 1671416717948006401, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484246, 1943145071222202373, 2, 1815000000000000111, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484247, 1943145071222202373, 2, 1815000000000000110, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484248, 1943145071222202373, 2, 1815000000000000108, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484249, 1943145071222202373, 2, 1671416755763851265, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183394484250, 1943145071222202373, 2, 1815000000000000107, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844353, 1943145071222202373, 2, 1815000000000000106, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844354, 1943145071222202373, 2, 1815000000000000105, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844355, 1943145071222202373, 2, 1677205870040154114, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844356, 1943145071222202373, 2, 1815000000000000104, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844357, 1943145071222202373, 2, 1815000000000000118, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844358, 1943145071222202373, 2, 1815000000000000117, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844359, 1943145071222202373, 2, 1677205784526684162, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844360, 1943145071222202373, 2, 1815000000000000116, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844361, 1943145071222202373, 2, 1815000000000000115, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844362, 1943145071222202373, 2, 1815000000000000114, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844363, 1943145071222202373, 2, 1677213466805501954, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844364, 1943145071222202373, 2, 1815000000000000113, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844365, 1943145071222202373, 2, 1815000000000000112, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844366, 1943145071222202373, 2, 1815000000000000127, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844367, 1943145071222202373, 2, 1677212407240425474, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844368, 1943145071222202373, 2, 1815000000000000126, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844369, 1943145071222202373, 2, 1677199976008040449, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844370, 1943145071222202373, 2, 1815000000000000125, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844371, 1943145071222202373, 2, 1677205540070064129, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844372, 1943145071222202373, 2, 1815000000000000124, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844373, 1943145071222202373, 2, 1815000000000000123, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844374, 1943145071222202373, 2, 1815000000000000122, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844375, 1943145071222202373, 2, 1815000000000000121, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_limit` VALUES (1948999183423844376, 1943145071222202373, 2, 1815000000000000120, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_menu_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `app_id` bigint(20) NULL DEFAULT NULL COMMENT '冗余字段，菜单所属的应用id',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`role_menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色菜单关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1678716210741555201, 1671420608181776386, 1671406669800796161, 1671406619464953857, '2023-07-11 18:41:48', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1678716210745749505, 1671420608181776386, 1671406669800796161, 1671407186899759106, '2023-07-11 18:41:48', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711587973414572034, 1711587568282554370, 1671406669800796161, 1671406619464953857, '2023-10-10 11:42:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711587973414572035, 1711587568282554370, 1671406669800796161, 1671407186899759106, '2023-10-10 11:42:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589882661752834, 1711587568282554370, 1671406745336016898, 1671407539607171073, '2023-10-10 11:50:02', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589939393908739, 1711587568282554370, 1671406745336016898, 1671407615163363330, '2023-10-10 11:50:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589942078263297, 1711587568282554370, 1671406745336016898, 1671407652933070850, '2023-10-10 11:50:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589970272374785, 1711587568282554370, 1671406745336016898, 1673524865274245118, '2023-10-10 11:50:23', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589971467751425, 1711587568282554370, 1671406745336016898, 1673524865274245121, '2023-10-10 11:50:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589976245063682, 1711587568282554370, 1671406745336016898, 1673524941069512706, '2023-10-10 11:50:25', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589980418396162, 1711587568282554370, 1671406745336016898, 1673524966277279745, '2023-10-10 11:50:26', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589985279594498, 1711587568282554370, 1671406745336016898, 1673525004151844865, '2023-10-10 11:50:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711589992342802434, 1711587568282554370, 1671406745336016898, 1673525357136080898, '2023-10-10 11:50:29', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590008524427266, 1711587568282554370, 1671406745336016898, 1673526479934484481, '2023-10-10 11:50:33', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590012949417985, 1711587568282554370, 1671406745336016898, 1673526946869571585, '2023-10-10 11:50:34', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590017626066946, 1711587568282554370, 1671406745336016898, 1673527098267168769, '2023-10-10 11:50:35', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590020511748098, 1711587568282554370, 1671406745336016898, 1673527401095917570, '2023-10-10 11:50:35', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590117073014786, 1711587661928779778, 1671406669800796161, 1671406619464953857, '2023-10-10 11:50:58', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590117077209090, 1711587661928779778, 1671406669800796161, 1671407186899759106, '2023-10-10 11:50:58', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590148257665027, 1711587661928779778, 1671406745336016898, 1671407539607171073, '2023-10-10 11:51:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590262820884482, 1711587661928779778, 1671406745336016898, 1671407967690420226, '2023-10-10 11:51:33', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711590267728220162, 1711587661928779778, 1671406745336016898, 1671408081100206081, '2023-10-10 11:51:34', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711616915294965762, 1711587661928779778, 1671406745336016898, 1710533513796653058, '2023-10-10 13:37:28', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711616928691572739, 1711587661928779778, 1671406745336016898, 1711591595250601985, '2023-10-10 13:37:31', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711616945284239361, 1711587931903545346, 1671406669800796161, 1671406619464953857, '2023-10-10 13:37:35', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711616945284239362, 1711587931903545346, 1671406669800796161, 1671407186899759106, '2023-10-10 13:37:35', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711616974036193282, 1711587931903545346, 1671406745336016898, 1711591743930290178, '2023-10-10 13:37:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711616979362959363, 1711587931903545346, 1671406745336016898, 1711591595250601985, '2023-10-10 13:37:43', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711623295653433346, 1711587661928779778, 1671406745336016898, 1671408144094457858, '2023-10-10 14:02:49', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1711623299860320258, 1711587661928779778, 1671406745336016898, 1671408194501603329, '2023-10-10 14:02:50', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1815000000000000100, 1339550467939639298, 1815000000000000001, 1815000000000000080, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu` VALUES (1815000000000000101, 1339550467939639298, 1815000000000000001, 1815000000000000090, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu` VALUES (1815000000000000200, 1339550467939639299, 1815000000000000001, 1815000000000000010, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1815000000000000202, 1339550467939639299, 1815000000000000001, 1815000000000000030, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1815000000000000203, 1339550467939639299, 1815000000000000001, 1815000000000000040, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1815000000000000204, 1339550467939639299, 1815000000000000001, 1815000000000000050, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1815000000000000205, 1339550467939639299, 1815000000000000001, 1815000000000000060, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1815000000000000206, 1339550467939639299, 1815000000000000001, 1815000000000000070, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1945474242531295233, 1945474242145419268, 1671406669800796161, 1671406619464953857, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1945474242531295234, 1945474242145419268, 1671406745336016898, 1671407652933070850, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1945474242531295235, 1945474242145419268, 1671406669800796161, 1671407186899759106, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1945474242531295236, 1945474242145419268, 1671406745336016898, 1671407539607171073, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1945474242531295237, 1945474242145419268, 1671406745336016898, 1671407615163363330, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243073, 1943145071222202373, 1671406669800796161, 1671406619464953857, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243074, 1943145071222202373, 1815000000000000001, 1815000000000000010, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243075, 1943145071222202373, 1671406745336016898, 1671407539607171073, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243076, 1943145071222202373, 1815000000000000001, 1815000000000000040, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243077, 1943145071222202373, 1671406745336016898, 1671407615163363330, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243078, 1943145071222202373, 1815000000000000001, 1815000000000000030, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243079, 1943145071222202373, 1671406745336016898, 1671407652933070850, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243080, 1943145071222202373, 1815000000000000001, 1815000000000000060, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243081, 1943145071222202373, 1815000000000000001, 1815000000000000050, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243082, 1943145071222202373, 1815000000000000001, 1815000000000000070, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1948999183130243083, 1943145071222202373, 1671406669800796161, 1671407186899759106, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474787776118785, 1671420545250439170, 1671406669800796161, 1671406619464953857, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474787973251106, 1671420545250439170, 1671406669800796161, 1671407186899759106, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474787973251115, 1671420545250439170, 1671406745336016898, 1671407312775016450, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788065525762, 1671420545250439170, 1671406745336016898, 1671407539607171073, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788078108674, 1671420545250439170, 1671406745336016898, 1671407615163363330, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788082302981, 1671420545250439170, 1671406745336016898, 1671407652933070850, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788090691587, 1671420545250439170, 1671406745336016898, 1671407791416406018, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788094885894, 1671420545250439170, 1671406745336016898, 1671407892205531137, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788111663108, 1671420545250439170, 1671406745336016898, 1671407967690420226, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788115857410, 1671420545250439170, 1671406745336016898, 1671408081100206081, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788115857413, 1671420545250439170, 1671406745336016898, 1671408144094457858, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788120051715, 1671420545250439170, 1671406745336016898, 1671408194501603329, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788132634626, 1671420545250439170, 1671406745336016898, 1673524613037191169, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788141023233, 1671420545250439170, 1671406745336016898, 1673524865274245118, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788141023237, 1671420545250439170, 1671406745336016898, 1673524865274245121, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788149411841, 1671420545250439170, 1671406745336016898, 1673524941069512706, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788149411844, 1671420545250439170, 1671406745336016898, 1673524966277279745, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788166189058, 1671420545250439170, 1671406745336016898, 1673525004151844865, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788166189060, 1671420545250439170, 1671406745336016898, 1673525357136080898, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788174577667, 1671420545250439170, 1671406745336016898, 1673525659931275265, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788182966276, 1671420545250439170, 1671406745336016898, 1673525912344489985, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788187160577, 1671420545250439170, 1671406745336016898, 1673526656565014530, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788187160580, 1671420545250439170, 1701801589128577026, 1701802336574521345, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788195549186, 1671420545250439170, 1671406745336016898, 1710533513796653058, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788195549190, 1671420545250439170, 1671406745336016898, 1711590804271976449, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788203937794, 1671420545250439170, 1671406745336016898, 1711591595250601985, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788212326401, 1671420545250439170, 1671406745336016898, 1711591743930290178, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788220715011, 1671420545250439170, 1717100945571057665, 1718824432245411841, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788229103619, 1671420545250439170, 1717100945571057665, 1718826323209621506, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788233297923, 1671420545250439170, 1717100945571057665, 1718826580437897217, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788233297926, 1671420545250439170, 1717100945571057665, 1718826709295304705, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788241686532, 1671420545250439170, 1671406745336016898, 1720008907121758209, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788250075137, 1671420545250439170, 1721100056577146881, 1721100330028990466, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788258463747, 1671420545250439170, 1671406745336016898, 1746839841395138562, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788258463750, 1671420545250439170, 1671406745336016898, 1747806631403159554, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788262658051, 1671420545250439170, 1701801589128577026, 1749629803979042817, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788266852354, 1671420545250439170, 1701801589128577026, 1749630086079541249, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788275240962, 1671420545250439170, 1701801589128577026, 1760838262674116609, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788283629571, 1671420545250439170, 1815000000000000001, 1815000000000000010, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788292018178, 1671420545250439170, 1815000000000000001, 1815000000000000030, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788292018181, 1671420545250439170, 1815000000000000001, 1815000000000000040, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788308795394, 1671420545250439170, 1815000000000000001, 1815000000000000050, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788308795397, 1671420545250439170, 1815000000000000001, 1815000000000000060, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788308795400, 1671420545250439170, 1815000000000000001, 1815000000000000070, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788317184002, 1671420545250439170, 1815000000000000001, 1815000000000000080, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (1950474788325572610, 1671420545250439170, 1815000000000000001, 1815000000000000090, '2025-07-30 16:33:22', -1, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_menu_options
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu_options`;
CREATE TABLE `sys_role_menu_options`  (
  `role_menu_option_id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `app_id` bigint(20) NULL DEFAULT NULL COMMENT '冗余字段，菜单所属的应用id',
  `menu_id` bigint(20) NULL DEFAULT NULL COMMENT '冗余字段，功能所属的菜单id',
  `menu_option_id` bigint(20) NOT NULL COMMENT '菜单功能id，关联sys_menu_options主键id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`role_menu_option_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单下的功能关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu_options
-- ----------------------------
INSERT INTO `sys_role_menu_options` VALUES (1671417849311211521, 1339550467939639303, 1671406745336016898, 1671407539607171073, 1671416717948006401, '2023-06-21 15:20:43', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1671417849311211522, 1339550467939639303, 1671406745336016898, 1671407539607171073, 1671416755763851265, '2023-06-21 15:20:43', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1678716210938687489, 1671420608181776386, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2023-07-11 18:41:48', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711587973414572036, 1711587568282554370, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2023-10-10 11:42:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589882707890177, 1711587568282554370, 1671406745336016898, 1671407539607171073, 1671416717948006401, '2023-10-10 11:50:02', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589882716278786, 1711587568282554370, 1671406745336016898, 1671407539607171073, 1677205540070064129, '2023-10-10 11:50:02', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589939461017601, 1711587568282554370, 1671406745336016898, 1671407615163363330, 1675495204221640706, '2023-10-10 11:50:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589939461017602, 1711587568282554370, 1671406745336016898, 1671407615163363330, 1677213466805501954, '2023-10-10 11:50:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589939461017603, 1711587568282554370, 1671406745336016898, 1671407615163363330, 1677213504298385410, '2023-10-10 11:50:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589939461017604, 1711587568282554370, 1671406745336016898, 1671407615163363330, 1677213572741038081, '2023-10-10 11:50:16', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589942078263298, 1711587568282554370, 1671406745336016898, 1671407652933070850, 1677212372381564929, '2023-10-10 11:50:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589942078263299, 1711587568282554370, 1671406745336016898, 1671407652933070850, 1677212407240425474, '2023-10-10 11:50:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589942078263300, 1711587568282554370, 1671406745336016898, 1671407652933070850, 1677212448021643265, '2023-10-10 11:50:17', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589971501305858, 1711587568282554370, 1671406745336016898, 1673524865274245121, 1678056521171140609, '2023-10-10 11:50:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589971505500162, 1711587568282554370, 1671406745336016898, 1673524865274245121, 1678056564691238914, '2023-10-10 11:50:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711589971505500163, 1711587568282554370, 1671406745336016898, 1673524865274245121, 1678056611675832321, '2023-10-10 11:50:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590117089792002, 1711587661928779778, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2023-10-10 11:50:58', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590148324773890, 1711587661928779778, 1671406745336016898, 1671407539607171073, 1671416755763851265, '2023-10-10 11:51:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590148324773892, 1711587661928779778, 1671406745336016898, 1671407539607171073, 1677205784526684162, '2023-10-10 11:51:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590148324773893, 1711587661928779778, 1671406745336016898, 1671407539607171073, 1677205870040154114, '2023-10-10 11:51:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590148324773894, 1711587661928779778, 1671406745336016898, 1671407539607171073, 1677205994816503809, '2023-10-10 11:51:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590262887993346, 1711587661928779778, 1671406745336016898, 1671407967690420226, 1677228381343678466, '2023-10-10 11:51:33', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590262887993347, 1711587661928779778, 1671406745336016898, 1671407967690420226, 1677228480924844033, '2023-10-10 11:51:33', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590262887993348, 1711587661928779778, 1671406745336016898, 1671407967690420226, 1677228556107743233, '2023-10-10 11:51:33', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590267765968897, 1711587661928779778, 1671406745336016898, 1671408081100206081, 1677229379281846273, '2023-10-10 11:51:34', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711590267765968898, 1711587661928779778, 1671406745336016898, 1671408081100206081, 1680610475986931714, '2023-10-10 11:51:34', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1711616945284239363, 1711587931903545346, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2023-10-10 13:37:35', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000110, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000081, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000111, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000082, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000112, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000083, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000113, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000084, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000114, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000085, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000115, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000086, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000116, 1339550467939639298, 1815000000000000001, 1815000000000000080, 1815000000000000087, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000120, 1339550467939639298, 1815000000000000001, 1815000000000000090, 1815000000000000091, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000121, 1339550467939639298, 1815000000000000001, 1815000000000000090, 1815000000000000092, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000122, 1339550467939639298, 1815000000000000001, 1815000000000000090, 1815000000000000093, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000123, 1339550467939639298, 1815000000000000001, 1815000000000000090, 1815000000000000094, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000124, 1339550467939639298, 1815000000000000001, 1815000000000000090, 1815000000000000095, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000125, 1339550467939639298, 1815000000000000001, 1815000000000000090, 1815000000000000096, '2025-07-29 21:15:36', 1, '2025-07-29 21:15:36', 1);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000300, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000100, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000301, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000101, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000302, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000102, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000303, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000103, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000304, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000104, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000305, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000105, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000306, 1339550467939639299, 1815000000000000001, 1815000000000000030, 1815000000000000106, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000310, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000110, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000311, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000111, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000312, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000112, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000313, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000113, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000314, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000114, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000315, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000115, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000316, 1339550467939639299, 1815000000000000001, 1815000000000000040, 1815000000000000116, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000320, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000120, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000321, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000121, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000322, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000122, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000323, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000123, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000324, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000124, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000325, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000125, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000326, 1339550467939639299, 1815000000000000001, 1815000000000000050, 1815000000000000126, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000330, 1339550467939639299, 1815000000000000001, 1815000000000000060, 1815000000000000130, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000331, 1339550467939639299, 1815000000000000001, 1815000000000000060, 1815000000000000131, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000332, 1339550467939639299, 1815000000000000001, 1815000000000000060, 1815000000000000132, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000333, 1339550467939639299, 1815000000000000001, 1815000000000000060, 1815000000000000133, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000334, 1339550467939639299, 1815000000000000001, 1815000000000000060, 1815000000000000134, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000335, 1339550467939639299, 1815000000000000001, 1815000000000000060, 1815000000000000135, '2025-07-20 19:19:05', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000340, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000140, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000341, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000141, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000342, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000142, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000343, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000143, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000344, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000144, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000345, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000145, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000346, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000146, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1815000000000000347, 1339550467939639299, 1815000000000000001, 1815000000000000070, 1815000000000000147, '2025-07-21 21:00:00', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295238, 1945474242145419268, 1671406745336016898, 1671407615163363330, 1675495204221640706, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295239, 1945474242145419268, 1671406745336016898, 1671407539607171073, 1677205994816503809, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295240, 1945474242145419268, 1671406745336016898, 1671407652933070850, 1677212448021643265, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295241, 1945474242145419268, 1671406745336016898, 1671407539607171073, 1671416717948006401, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295242, 1945474242145419268, 1671406745336016898, 1671407539607171073, 1671416755763851265, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295243, 1945474242145419268, 1671406745336016898, 1671407615163363330, 1677213572741038081, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295244, 1945474242145419268, 1671406745336016898, 1671407539607171073, 1677205870040154114, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295245, 1945474242145419268, 1671406745336016898, 1671407652933070850, 1677212372381564929, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295246, 1945474242145419268, 1671406745336016898, 1671407539607171073, 1677205784526684162, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295247, 1945474242145419268, 1671406745336016898, 1671407615163363330, 1677213466805501954, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295248, 1945474242145419268, 1671406745336016898, 1671407652933070850, 1677212407240425474, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295249, 1945474242145419268, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295250, 1945474242145419268, 1671406745336016898, 1671407539607171073, 1677205540070064129, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1945474242531295251, 1945474242145419268, 1671406745336016898, 1671407615163363330, 1677213504298385410, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183188963330, 1943145071222202373, 1671406745336016898, 1671407539607171073, 1671416717948006401, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323458, 1943145071222202373, 1671406745336016898, 1671407539607171073, 1671416755763851265, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323459, 1943145071222202373, 1671406745336016898, 1671407615163363330, 1675495204221640706, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323460, 1943145071222202373, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323461, 1943145071222202373, 1671406745336016898, 1671407539607171073, 1677205540070064129, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323462, 1943145071222202373, 1671406745336016898, 1671407539607171073, 1677205784526684162, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323463, 1943145071222202373, 1671406745336016898, 1671407539607171073, 1677205870040154114, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323464, 1943145071222202373, 1671406745336016898, 1671407539607171073, 1677205994816503809, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323465, 1943145071222202373, 1671406745336016898, 1671407652933070850, 1677212372381564929, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323466, 1943145071222202373, 1671406745336016898, 1671407652933070850, 1677212407240425474, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323467, 1943145071222202373, 1671406745336016898, 1671407652933070850, 1677212448021643265, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323468, 1943145071222202373, 1671406745336016898, 1671407615163363330, 1677213466805501954, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323469, 1943145071222202373, 1671406745336016898, 1671407615163363330, 1677213504298385410, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323470, 1943145071222202373, 1671406745336016898, 1671407615163363330, 1677213572741038081, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323471, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000100, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323472, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000101, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323473, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000102, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323474, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000103, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323475, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000104, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323476, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000105, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323477, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000106, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183218323478, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000107, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683586, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000108, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683587, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000110, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683588, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000111, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683589, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000112, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683590, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000113, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683591, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000114, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683592, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000115, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683593, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000116, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683594, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000117, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683595, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000118, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683596, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000120, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683597, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000121, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683598, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000122, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683599, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000123, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683600, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000124, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683601, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000125, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683602, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000126, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683603, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000127, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683604, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000128, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683605, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000130, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683606, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000131, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683607, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000132, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683608, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000133, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683609, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000134, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683610, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000135, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683611, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000136, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683612, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000137, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683613, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000138, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683614, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000140, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183247683615, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000141, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043713, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000142, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043714, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000143, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043715, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000144, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043716, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000145, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043717, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000146, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043718, 1943145071222202373, 1815000000000000001, 1815000000000000070, 1815000000000000147, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043719, 1943145071222202373, 1815000000000000001, 1815000000000000030, 1815000000000000200, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043720, 1943145071222202373, 1815000000000000001, 1815000000000000040, 1815000000000000210, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043721, 1943145071222202373, 1815000000000000001, 1815000000000000050, 1815000000000000220, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1948999183277043722, 1943145071222202373, 1815000000000000001, 1815000000000000060, 1815000000000000230, '2025-07-26 14:49:51', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787847421954, 1671420545250439170, 1671406745336016898, 1671407539607171073, 1671416717948006401, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251100, 1671420545250439170, 1671406745336016898, 1671407539607171073, 1671416755763851265, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251104, 1671420545250439170, 1671406745336016898, 1671407615163363330, 1675495204221640706, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251108, 1671420545250439170, 1671406669800796161, 1671406619464953857, 1677199976008040449, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251109, 1671420545250439170, 1671406745336016898, 1671407539607171073, 1677205540070064129, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251110, 1671420545250439170, 1671406745336016898, 1671407539607171073, 1677205784526684162, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251111, 1671420545250439170, 1671406745336016898, 1671407539607171073, 1677205870040154114, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474787973251112, 1671420545250439170, 1671406745336016898, 1671407539607171073, 1677205994816503809, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788069720065, 1671420545250439170, 1671406745336016898, 1671407652933070850, 1677212372381564929, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788078108676, 1671420545250439170, 1671406745336016898, 1671407652933070850, 1677212407240425474, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788082302978, 1671420545250439170, 1671406745336016898, 1671407652933070850, 1677212448021643265, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788082302982, 1671420545250439170, 1671406745336016898, 1671407615163363330, 1677213466805501954, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788090691586, 1671420545250439170, 1671406745336016898, 1671407615163363330, 1677213504298385410, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788094885889, 1671420545250439170, 1671406745336016898, 1671407615163363330, 1677213572741038081, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788094885890, 1671420545250439170, 1671406745336016898, 1671407892205531137, 1677216034650685442, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788094885892, 1671420545250439170, 1671406745336016898, 1671407892205531137, 1677216100685807617, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788103274497, 1671420545250439170, 1671406745336016898, 1671407892205531137, 1677216141127286786, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788111663107, 1671420545250439170, 1671406745336016898, 1671407892205531137, 1677223921938694145, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788111663109, 1671420545250439170, 1671406745336016898, 1671407967690420226, 1677228381343678466, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788115857411, 1671420545250439170, 1671406745336016898, 1671407967690420226, 1677228480924844033, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788120051714, 1671420545250439170, 1671406745336016898, 1671407967690420226, 1677228556107743233, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788120051717, 1671420545250439170, 1671406745336016898, 1671408081100206081, 1677229379281846273, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788132634627, 1671420545250439170, 1671406745336016898, 1673524865274245121, 1678056521171140609, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788141023234, 1671420545250439170, 1671406745336016898, 1673524865274245121, 1678056564691238914, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788141023236, 1671420545250439170, 1671406745336016898, 1673524865274245121, 1678056611675832321, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788141023238, 1671420545250439170, 1671406745336016898, 1671408081100206081, 1680610475986931714, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788149411842, 1671420545250439170, 1671406745336016898, 1671408081100206081, 1711189530448498690, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788149411843, 1671420545250439170, 1671406745336016898, 1673525659931275265, 1766013401104175106, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788149411845, 1671420545250439170, 1671406745336016898, 1673525659931275265, 1766013493466943489, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788157800449, 1671420545250439170, 1671406745336016898, 1673525659931275265, 1766013586857316353, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788174577665, 1671420545250439170, 1671406745336016898, 1673525912344489985, 1766025381470851073, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788174577666, 1671420545250439170, 1671406745336016898, 1673525912344489985, 1766025455466762241, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788182966275, 1671420545250439170, 1671406745336016898, 1673526656565014530, 1766027897772232705, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788187160578, 1671420545250439170, 1671406745336016898, 1673526656565014530, 1766027932140359682, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788187160581, 1671420545250439170, 1671406745336016898, 1673526656565014530, 1766027969373196290, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788195549187, 1671420545250439170, 1671406745336016898, 1710533513796653058, 1811042155548205058, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788195549189, 1671420545250439170, 1671406745336016898, 1710533513796653058, 1811282123950862338, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788203937795, 1671420545250439170, 1671406745336016898, 1673525659931275265, 1811330833279811585, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788212326402, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000081, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788220715010, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000082, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788229103618, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000083, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788233297924, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000084, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788233297925, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000085, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788241686531, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000086, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788250075139, 1671420545250439170, 1815000000000000001, 1815000000000000080, 1815000000000000087, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788258463746, 1671420545250439170, 1815000000000000001, 1815000000000000090, 1815000000000000091, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788258463749, 1671420545250439170, 1815000000000000001, 1815000000000000090, 1815000000000000092, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788262658050, 1671420545250439170, 1815000000000000001, 1815000000000000090, 1815000000000000093, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788266852355, 1671420545250439170, 1815000000000000001, 1815000000000000090, 1815000000000000094, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788275240961, 1671420545250439170, 1815000000000000001, 1815000000000000090, 1815000000000000095, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788283629572, 1671420545250439170, 1815000000000000001, 1815000000000000090, 1815000000000000096, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788292018179, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000100, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788300406785, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000101, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788308795393, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000102, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788308795396, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000103, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788308795399, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000104, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788317184001, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000105, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788325572611, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000106, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788333961218, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000107, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788342349826, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000108, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788350738434, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000110, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788350738435, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000111, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788375904257, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000112, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788375904259, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000113, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788384292867, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000114, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788392681474, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000115, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788392681477, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000116, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788392681479, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000117, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788401070081, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000118, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788401070083, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000120, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788409458692, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000121, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788426235906, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000122, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788434624515, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000123, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788434624518, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000124, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788443013123, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000125, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788451401732, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000126, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788459790337, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000127, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788459790339, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000128, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788472373250, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000130, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788472373254, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000131, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788484956162, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000132, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788493344770, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000133, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788497539073, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000134, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788497539074, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000135, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788505927681, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000136, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788514316289, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000137, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788514316290, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000138, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788522704897, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000140, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788522704898, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000141, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788531093506, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000142, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788539482114, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000143, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788547870722, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000144, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788547870723, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000145, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788547870724, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000146, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788556259330, 1671420545250439170, 1815000000000000001, 1815000000000000070, 1815000000000000147, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788556259331, 1671420545250439170, 1815000000000000001, 1815000000000000030, 1815000000000000200, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788556259332, 1671420545250439170, 1815000000000000001, 1815000000000000040, 1815000000000000210, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788568842241, 1671420545250439170, 1815000000000000001, 1815000000000000050, 1815000000000000220, '2025-07-30 16:33:22', -1, NULL, NULL);
INSERT INTO `sys_role_menu_options` VALUES (1950474788568842242, 1671420545250439170, 1815000000000000001, 1815000000000000060, 1815000000000000230, '2025-07-30 16:33:22', -1, NULL, NULL);

-- ----------------------------
-- Table structure for sys_sms
-- ----------------------------
DROP TABLE IF EXISTS `sys_sms`;
CREATE TABLE `sys_sms`  (
  `sms_id` bigint(20) NOT NULL COMMENT '主键',
  `phone_number` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `validate_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '短信验证码',
  `template_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '短信模板编号',
  `biz_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务id',
  `status_flag` tinyint(4) NULL DEFAULT NULL COMMENT '发送状态：1-未发送，2-发送成功，3-发送失败，4-失效',
  `source` int(11) NULL DEFAULT NULL COMMENT '来源：1-app，2-pc，3-其他',
  `invalid_time` datetime(0) NULL DEFAULT NULL COMMENT '短信失效截止时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`sms_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_table_width
-- ----------------------------
DROP TABLE IF EXISTS `sys_table_width`;
CREATE TABLE `sys_table_width`  (
  `table_width_id` bigint(20) NOT NULL COMMENT '主键id',
  `field_business_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务标识的编码，例如：PROJECT_TABLE',
  `field_type` int(11) NOT NULL COMMENT '宽度记录的类型：1-全体员工，2-个人独有',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '所属用户id',
  `table_width_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义列是否显示、宽度、顺序和列的锁定，一段json',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`table_width_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业务中表的宽度' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_table_width
-- ----------------------------
INSERT INTO `sys_table_width` VALUES (1948399296869163009, 'ERP_CUSTOMER_TABLE', 2, 1339550467939639299, '[{\"id\":\"customerCode\",\"width\":140,\"dataIndex\":\"customerCode\",\"title\":\"客户编码\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"customerName\",\"width\":200,\"dataIndex\":\"customerName\",\"title\":\"客户名称\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"customerShortName\",\"width\":150,\"dataIndex\":\"customerShortName\",\"title\":\"客户简称\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"customerType\",\"width\":120,\"dataIndex\":\"customerType\",\"title\":\"客户类型\",\"checked\":true,\"align\":\"center\",\"isShow\":true},{\"id\":\"customerLevel\",\"width\":120,\"dataIndex\":\"customerLevel\",\"title\":\"客户等级\",\"checked\":true,\"align\":\"center\",\"isShow\":true},{\"id\":\"contactPerson\",\"width\":100,\"dataIndex\":\"contactPerson\",\"title\":\"联系人\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"contactPhone\",\"width\":120,\"dataIndex\":\"contactPhone\",\"title\":\"联系电话\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"contactMobile\",\"width\":120,\"dataIndex\":\"contactMobile\",\"title\":\"手机号码\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"status\",\"width\":100,\"dataIndex\":\"status\",\"title\":\"状态\",\"checked\":true,\"align\":\"center\",\"isShow\":true},{\"id\":\"createTime\",\"width\":140,\"dataIndex\":\"createTime\",\"title\":\"创建时间\",\"checked\":true,\"ellipsis\":true,\"isShow\":true},{\"id\":\"action\",\"key\":\"action\",\"width\":100,\"title\":\"操作\",\"checked\":true,\"isShow\":true}]', '2025-07-24 23:06:07', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_theme
-- ----------------------------
DROP TABLE IF EXISTS `sys_theme`;
CREATE TABLE `sys_theme`  (
  `theme_id` bigint(20) NOT NULL COMMENT '主键',
  `theme_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主题名称',
  `theme_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主题属性，json格式',
  `template_id` bigint(20) NOT NULL COMMENT '主题模板id',
  `status_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否启用：Y-启用，N-禁用',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`theme_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统主题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_theme
-- ----------------------------
INSERT INTO `sys_theme` VALUES (1477272515573542913, 'ERP后台管理系统默认主题', '{\"positionSort\":1000,\"themeId\":\"1477272515573542913\",\"themeName\":\"ERP后台管理系统默认主题\",\"templateId\":\"1477171926286020610\",\"GUNS_MGR_LOGIN_BACKGROUND_IMG\":\"1704330515919671298\",\"GUNS_MGR_BEI_URL\":\"https://baidu.com/\",\"GUNS_SUB_TITLE\":\"ERP\",\"GUNS_MGR_LOGO\":\"1949753274223587329\",\"GUNS_MGR_NAME\":\"ERP Tech.\",\"GUNS_MGR_FAVICON\":\"1949753283136483329\",\"GUNS_MGR_FOOTER_TEXT\":\"欢迎使用\",\"GUNS_MGR_BEI_NO\":\"京ICP备111-1\"}', 1477171926286020610, 'Y', '2022-01-01 21:36:29', 1339550467939639299, '2025-07-28 16:46:27', 1339550467939639299);

-- ----------------------------
-- Table structure for sys_theme_template
-- ----------------------------
DROP TABLE IF EXISTS `sys_theme_template`;
CREATE TABLE `sys_theme_template`  (
  `template_id` bigint(20) NOT NULL COMMENT '主键',
  `template_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主题名称',
  `template_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主题编码',
  `template_type` tinyint(4) NOT NULL COMMENT '主题类型：1-系统类型，2-业务类型',
  `status_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '启用状态：Y-启用，N-禁用',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统主题-模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_theme_template
-- ----------------------------
INSERT INTO `sys_theme_template` VALUES (1477171926286020610, 'Guns后台管理系统模板', 'GUNS_PLATFORM', 1, 'Y', '2022-01-01 14:56:46', 1339550467939639299, '2022-01-01 15:11:27', 1339550467939639299);

-- ----------------------------
-- Table structure for sys_theme_template_field
-- ----------------------------
DROP TABLE IF EXISTS `sys_theme_template_field`;
CREATE TABLE `sys_theme_template_field`  (
  `field_id` bigint(20) NOT NULL COMMENT '主键',
  `field_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性名称',
  `field_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性编码',
  `field_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性展示类型（字典维护），例如：图片，文本等类型',
  `field_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否必填：Y-必填，N-非必填',
  `field_length` int(11) NULL DEFAULT NULL COMMENT '属性值长度',
  `field_description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性描述',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`field_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统主题-模板属性' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_theme_template_field
-- ----------------------------
INSERT INTO `sys_theme_template_field` VALUES (1473949204011819009, '平台名称', 'GUNS_MGR_NAME', 'string', 'Y', 10, 'Guns后台管理系统左上角名称', '2021-12-23 17:30:50', 1339550467939639299, '2022-01-01 14:30:42', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1473949858369380354, '登录页背景图片', 'GUNS_MGR_LOGIN_BACKGROUND_IMG', 'file', 'Y', NULL, 'Guns后台管理系统登录页图片', '2021-12-23 17:33:26', 1339550467939639299, '2022-01-01 14:32:14', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1473950190365319169, '平台LOGO', 'GUNS_MGR_LOGO', 'file', 'Y', NULL, 'Guns后台管理系统左上角logo', '2021-12-23 17:34:45', 1339550467939639299, '2022-01-01 14:46:07', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1473950675281387521, '浏览器Icon', 'GUNS_MGR_FAVICON', 'file', 'Y', NULL, 'Guns后台管理系统标签栏图标', '2021-12-23 17:36:40', 1339550467939639299, '2022-01-01 14:46:56', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1473951200521494529, '页脚文字', 'GUNS_MGR_FOOTER_TEXT', 'string', 'Y', 100, 'Guns后台管理系统页脚文字', '2021-12-23 17:38:46', 1339550467939639299, '2022-01-01 14:48:08', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1473951616827138050, '备案号', 'GUNS_MGR_BEI_NO', 'string', 'N', 100, 'Guns后台管理系统底部备案号', '2021-12-23 17:40:25', 1339550467939639299, '2022-01-01 14:48:46', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1477170929413206017, '备案号跳转链接', 'GUNS_MGR_BEI_URL', 'string', 'N', 200, 'Guns后台管理系统备案号跳转到的链接', '2022-01-01 14:52:49', 1339550467939639299, '2022-01-01 14:55:28', 1339550467939639299);
INSERT INTO `sys_theme_template_field` VALUES (1713914194342457345, '平台副标题名称', 'GUNS_SUB_TITLE', 'string', 'N', NULL, NULL, '2023-10-16 21:46:02', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_theme_template_rel
-- ----------------------------
DROP TABLE IF EXISTS `sys_theme_template_rel`;
CREATE TABLE `sys_theme_template_rel`  (
  `relation_id` bigint(20) NOT NULL COMMENT '主键',
  `template_id` bigint(20) NOT NULL COMMENT '模板主键id',
  `field_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性编码',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`relation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统主题-模板配置关联关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_theme_template_rel
-- ----------------------------
INSERT INTO `sys_theme_template_rel` VALUES (1477175606452236290, 1477171926286020610, 'GUNS_MGR_NAME', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1477175606519345154, 1477171926286020610, 'GUNS_MGR_LOGIN_BACKGROUND_IMG', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1477175606519345155, 1477171926286020610, 'GUNS_MGR_LOGO', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1477175606586454017, 1477171926286020610, 'GUNS_MGR_FAVICON', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1477175606653562881, 1477171926286020610, 'GUNS_MGR_FOOTER_TEXT', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1477175606720671746, 1477171926286020610, 'GUNS_MGR_BEI_NO', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1477175606787780610, 1477171926286020610, 'GUNS_MGR_BEI_URL', '2022-01-01 15:11:24', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_theme_template_rel` VALUES (1713921667560493058, 1477171926286020610, 'GUNS_SUB_TITLE', '2023-10-16 22:16:02', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_timers
-- ----------------------------
DROP TABLE IF EXISTS `sys_timers`;
CREATE TABLE `sys_timers`  (
  `timer_id` bigint(20) NOT NULL COMMENT '定时器id',
  `timer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务名称',
  `action_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '执行任务的class的类名（实现了TimerAction接口的类的全称）',
  `cron` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '定时任务表达式',
  `params` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '参数',
  `job_status` int(11) NULL DEFAULT NULL COMMENT '状态：1-运行，2-停止',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注信息',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`timer_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_timers
-- ----------------------------
INSERT INTO `sys_timers` VALUES (1355878268976271362, '定时刷新服务器状态', 'cn.stylefeng.roses.kernel.monitor.system.holder.SystemHardwareInfoHolder', '0 0/1 * * * ? ', NULL, 1, '每1分钟执行一次，刷新服务器状态', 'N', '2021-01-31 21:59:05', 1339550467939639299, '2021-01-31 22:00:23', 1339550467939639299);
INSERT INTO `sys_timers` VALUES (1385068954897223681, '定时检测数据源的链接状态', 'cn.stylefeng.roses.kernel.dsctn.modular.timer.DataSourceStatusCheckTimer', '0/30 * * * * ? ', '', 1, '', 'N', '2021-04-22 11:12:27', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_toc_customer
-- ----------------------------
DROP TABLE IF EXISTS `sys_toc_customer`;
CREATE TABLE `sys_toc_customer`  (
  `customer_id` bigint(20) NOT NULL COMMENT '主键id',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码，BCrypt',
  `old_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旧密码',
  `old_password_salt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旧的密码盐',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '昵称（显示名称）',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `telephone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机',
  `verify_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱或手机验证码',
  `verified_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否已经邮箱或手机验证通过：Y-通过，N-未通过',
  `avatar` bigint(20) NULL DEFAULT NULL COMMENT '用户头像（文件表id）',
  `avatar_object_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像的文件全名',
  `score` int(11) NULL DEFAULT NULL COMMENT '用户积分',
  `status_flag` tinyint(4) NULL DEFAULT NULL COMMENT '用户状态：1-启用，2-禁用',
  `secret_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户秘钥，用在调用会员校验等',
  `member_expire_time` datetime(0) NULL DEFAULT NULL COMMENT '会员截止日期，到期时间',
  `last_login_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上次登录ip',
  `last_login_time` datetime(0) NULL DEFAULT NULL COMMENT '上次登录时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`customer_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'C端用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_translation
-- ----------------------------
DROP TABLE IF EXISTS `sys_translation`;
CREATE TABLE `sys_translation`  (
  `tran_id` bigint(20) NOT NULL COMMENT '主键id',
  `tran_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `tran_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '多语言条例名称',
  `tran_language_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语种字典',
  `tran_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '翻译的值',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`tran_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多语言' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(20) NOT NULL COMMENT '主键',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码，加密方式为MD5',
  `password_salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码盐',
  `avatar` bigint(20) NULL DEFAULT NULL COMMENT '头像，存的为文件id',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '性别：M-男，F-女',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机',
  `tel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  `super_admin_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否是超级管理员：Y-是，N-否',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-冻结，3-临时冻结',
  `freeze_deadline_time` datetime(0) NULL DEFAULT NULL COMMENT '账号冻结截止时间，如果是临时冻结，这个值需要填写',
  `login_count` int(11) NULL DEFAULT 1 COMMENT '登录次数',
  `last_login_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登陆IP',
  `last_login_time` datetime(0) NULL DEFAULT NULL COMMENT '最后登陆时间',
  `user_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '用户的排序',
  `employee_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `master_user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对接外部主数据的用户id',
  `hidden_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否隐藏：Y-隐藏，N-不隐藏，用在系统内置账号',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户号',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1339550467939639299, '管理员', NULL, 'admin', 'ab94f1a029e3eda398cd054cdc3acfd7', '2dxzcmp9', 10000, '2020-12-01', 'M', '<EMAIL>', '18266668888', NULL, 'Y', 1, NULL, 86, '127.0.0.1', '2025-07-28 16:19:14', 1.00, NULL, NULL, 'N', NULL, 2, 'N', '2020-12-17 20:40:31', -1, '2023-05-23 11:34:38', -1, 1);
INSERT INTO `sys_user` VALUES (1678652551806959618, '张三', NULL, 'zhangsan', '5e4a4cd54816f15983b793f189fae871', 'ls79ab85', 10000, NULL, 'F', NULL, NULL, NULL, 'N', 1, NULL, 2, '127.0.0.1', '2023-07-11 18:44:18', 1000.00, NULL, NULL, 'N', NULL, 3, 'N', '2023-07-11 14:28:51', 1339550467939639299, '2023-07-11 18:45:04', 1339550467939639299, 1);
INSERT INTO `sys_user` VALUES (1711617292694245378, '安全保密管理员', NULL, 'securityAdmin', '6456a4e0b7289d59bbb951760a64ac51', 'es4deaaz', 10000, NULL, 'M', NULL, NULL, NULL, 'N', 1, NULL, 1, NULL, NULL, 2000.00, NULL, NULL, 'N', NULL, 1, 'N', '2023-10-10 13:38:58', 1339550467939639299, '2023-10-10 13:39:37', 1339550467939639299, 1);
INSERT INTO `sys_user` VALUES (1711617412588425219, '系统管理员', NULL, 'systemAdmin', 'ad69c112fb08886c29d612063b55ebf0', 'dw1xxm5m', 10000, NULL, 'M', NULL, NULL, NULL, 'N', 1, NULL, 1, NULL, NULL, 2001.00, NULL, NULL, 'N', NULL, 1, 'N', '2023-10-10 13:39:26', 1339550467939639299, '2023-10-10 13:39:42', 1339550467939639299, 1);
INSERT INTO `sys_user` VALUES (1711620227499085827, '安全审计管理员', NULL, 'auditAdmin', 'aa03db4efb39fe506b8d9a12ab7058ec', 'du0txzw1', 10000, NULL, 'M', NULL, NULL, NULL, 'N', 1, NULL, 1, NULL, NULL, 2002.00, NULL, NULL, 'N', NULL, 0, 'N', '2023-10-10 13:50:37', 1339550467939639299, NULL, NULL, 1);
INSERT INTO `sys_user` VALUES (1943145071222202370, '租户1管理员', '租户1管理员', '<EMAIL>', 'f9ded499d10be98df8cdb8a5a7016011', 'OKqWLmjp', 1943143919655067650, NULL, 'M', '<EMAIL>', '17611112333', NULL, 'N', 1, NULL, 23, '127.0.0.1', '2025-07-26 15:11:27', 0.00, NULL, NULL, 'N', NULL, 0, 'N', '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL, 1943145071222202369);
INSERT INTO `sys_user` VALUES (1945474242145419265, '租户2管理员', '租户2管理员', '<EMAIL>', 'a002752f94df55051a2e9f623a342c46', 'eLXcNWI7', 1945474215238959105, NULL, 'M', '<EMAIL>', '17611112322', NULL, 'N', 1, NULL, 2, '127.0.0.1', '2025-07-16 21:23:17', 0.00, NULL, NULL, 'N', NULL, 0, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL, 1945474242082504705);

-- ----------------------------
-- Table structure for sys_user_certificate
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_certificate`;
CREATE TABLE `sys_user_certificate`  (
  `user_certificate_id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `certificate_type` bigint(20) NOT NULL COMMENT '证书类型，取字典id',
  `certificate_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书编号',
  `issuing_authority` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发证机构名称',
  `date_issued` date NULL DEFAULT NULL COMMENT '证书发证日期',
  `date_expires` date NULL DEFAULT NULL COMMENT '证书到期日期，如果为空，则为长期',
  `attachment_id` bigint(20) NULL DEFAULT NULL COMMENT '文件id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否删除：Y-删除，N-未删除',
  PRIMARY KEY (`user_certificate_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户证书' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_data_scope
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_data_scope`;
CREATE TABLE `sys_user_data_scope`  (
  `user_data_scope_id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `org_id` bigint(20) NOT NULL COMMENT '机构id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`user_data_scope_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户数据范围' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_group
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_group`;
CREATE TABLE `sys_user_group`  (
  `user_group_id` bigint(20) NOT NULL COMMENT '用户组id',
  `user_group_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户分组标题简称',
  `user_group_detail_name` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组内选择项的合并',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_group_detail
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_group_detail`;
CREATE TABLE `sys_user_group_detail`  (
  `detail_id` bigint(20) NOT NULL COMMENT '详情id',
  `user_group_id` bigint(20) NULL DEFAULT NULL COMMENT '所属用户组id',
  `select_type` tinyint(4) NULL DEFAULT NULL COMMENT '授权对象类型：1-用户，2-部门，3-角色，4-职位，5-关系',
  `select_value` bigint(20) NULL DEFAULT NULL COMMENT '授权对象id值，例如：用户id，部门id',
  `select_value_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授权对象名称，例如：张三，研发部，管理员等',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`detail_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户组详情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_org
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_org`;
CREATE TABLE `sys_user_org`  (
  `user_org_id` bigint(20) NOT NULL COMMENT '企业员工主键id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `master_user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对接外部主数据的用户id',
  `org_id` bigint(20) NOT NULL COMMENT '所属机构id',
  `master_org_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对接外部组织机构id',
  `position_id` bigint(20) NULL DEFAULT NULL COMMENT '职位id',
  `main_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否是主部门：Y-是，N-不是',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用：1-启用，2-禁用',
  `expand_field` json NULL COMMENT '拓展字段',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '添加人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`user_org_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户组织机构关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_org
-- ----------------------------
INSERT INTO `sys_user_org` VALUES (1678717069600796673, 1678652551806959618, NULL, 1674675494710255617, NULL, 1671418731163627522, 'Y', 1, NULL, '2023-07-11 18:45:13', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_org` VALUES (1678717105206243330, 1339550467939639299, NULL, 1671419146928205826, NULL, 1671418831935975426, 'Y', 1, NULL, '2023-07-11 18:45:22', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_org` VALUES (1711617456171438082, 1711617292694245378, NULL, 1671418869810540546, NULL, 1671418731163627522, 'Y', 1, NULL, '2023-10-10 13:39:37', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_org` VALUES (1711617477809852418, 1711617412588425219, NULL, 1671418869810540546, NULL, 1671418731163627522, 'Y', 1, NULL, '2023-10-10 13:39:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_org` VALUES (1711620227566194690, 1711620227499085827, NULL, 1671418869810540546, NULL, 1671418731163627522, 'Y', 1, NULL, '2023-10-10 13:50:37', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_org` VALUES (1943145071285116929, 1943145071222202370, NULL, 1943145071222202371, NULL, 1943145071222202372, 'Y', 1, NULL, '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_org` VALUES (1945474242271248385, 1945474242145419265, NULL, 1945474242145419266, NULL, 1945474242145419267, 'Y', 1, NULL, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_password_record
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_password_record`;
CREATE TABLE `sys_user_password_record`  (
  `record_id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `history_password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '历史密码记录',
  `history_password_salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '历史密码记录盐值',
  `update_password_time` datetime(0) NOT NULL COMMENT '修改密码时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户历史密码记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_role_id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `role_type` tinyint(4) NOT NULL DEFAULT 10 COMMENT '角色类型：10-系统角色，15-业务角色，20-公司角色',
  `role_org_id` bigint(20) NULL DEFAULT NULL COMMENT '用户所属机构id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`user_role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1673899829178261505, 1339550467939639299, 1671420545250439170, 10, NULL, '2023-06-28 11:43:13', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (1678716759805308929, 1678652551806959618, 1671420608181776386, 10, NULL, '2023-07-11 18:43:59', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (1711617314710147073, 1711617292694245378, 1711587661928779778, 10, NULL, '2023-10-10 13:39:03', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (1711620340032262145, 1711617412588425219, 1711587568282554370, 10, NULL, '2023-10-10 13:51:04', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (1711620361624539138, 1711620227499085827, 1711587931903545346, 10, NULL, '2023-10-10 13:51:09', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (1943145071352225793, 1943145071222202370, 1943145071222202373, 10, NULL, '2025-07-10 11:07:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (1945474242338357249, 1945474242145419265, 1945474242145419268, 10, NULL, '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_secret_key
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_secret_key`;
CREATE TABLE `sys_user_secret_key`  (
  `user_secret_key_id` bigint(20) NOT NULL COMMENT '秘钥id',
  `secret_key_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥名称',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户id',
  `secret_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥值，经过MD5加密',
  `secret_key_salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥值的密码盐',
  `secret_expiration_time` datetime(0) NULL DEFAULT NULL COMMENT '秘钥过期时间',
  `secret_once_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '秘钥是否使用一次后删除：Y-是，N-否',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`user_secret_key_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户临时秘钥' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
