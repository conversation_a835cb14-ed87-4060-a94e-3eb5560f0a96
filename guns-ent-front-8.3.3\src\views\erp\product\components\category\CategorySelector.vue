<template>
  <div class="category-selector">
    <a-tree-select
      v-model:value="selectedValue"
      :tree-data="treeData"
      :tree-checkable="multiple"
      :show-checked-strategy="multiple ? SHOW_PARENT : null"
      :field-names="{ children: 'children', label: 'categoryName', value: 'categoryId', key: 'categoryId' }"
      :placeholder="placeholder"
      :disabled="disabled"
      :allow-clear="allowClear"
      :multiple="multiple"
      :show-search="true"
      :filter-tree-node="filterTreeNode"
      style="width: 100%"
      @change="handleChange"
    />
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, watch } from 'vue';
import { message, TreeSelect } from 'ant-design-vue';
import { getProductCategoryTree } from '@/views/erp/productCategory/api/productCategoryApi';

export default defineComponent({
  name: 'CategorySelector',
  props: {
    value: {
      type: [Number, String],
      default: undefined
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择产品分类'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const SHOW_PARENT = TreeSelect.SHOW_PARENT;
    const treeData = ref([]);
    const selectedValue = ref(props.multiple ? [] : undefined);
    
    // 加载分类树数据（使用与左侧树相同的API）
    const loadTreeData = async () => {
      try {
        const res = await getProductCategoryTree({
          onlyEnabled: true
        });
        
        console.log('分类选择器API响应:', res);
        
        // 直接使用响应数据（数组），已经是树形结构
        if (res && Array.isArray(res)) {
          treeData.value = formatTreeData(res);
        } else {
          console.error('API响应格式错误:', res);
          message.error('获取分类树失败');
        }
      } catch (error) {
        console.error('加载分类树出错:', error);
        message.error('加载分类树出错');
      }
    };
    
    // 格式化树形数据
    const formatTreeData = (data) => {
      if (!data || !data.length) return [];
      
      // 数据已经是树形结构，直接处理即可
      return data.map(item => ({
        ...item,
        // 确保categoryId是字符串类型（TreeSelect的value需要）
        categoryId: String(item.categoryId),
        // 设置是否为叶子节点
        isLeaf: !item.hasChildren && (!item.children || item.children.length === 0),
        // 递归处理子节点
        children: item.children ? formatTreeData(item.children) : []
      }));
    };
    
    
    // 选择变化事件
    const handleChange = (value) => {
      emit('update:value', value);
      emit('change', value);
    };
    
    // 搜索过滤
    const filterTreeNode = (inputValue, treeNode) => {
      return treeNode.categoryName.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
    };
    
    // 监听外部value变化
    watch(() => props.value, (newVal) => {
      selectedValue.value = newVal;
    }, { immediate: true });
    
    onMounted(() => {
      loadTreeData();
    });
    
    return {
      SHOW_PARENT,
      treeData,
      selectedValue,
      handleChange,
      filterTreeNode
    };
  }
});
</script>

<style scoped>
.category-selector {
  width: 100%;
}
</style>