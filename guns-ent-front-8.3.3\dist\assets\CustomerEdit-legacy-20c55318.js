System.register(["./index-legacy-ee1db0c7.js","./index-legacy-0d30ef09.js","./index-legacy-94a6fc23.js","./CustomerApi-legacy-7faff1b0.js","./index-legacy-b540c599.js","./regionApi-legacy-73888494.js"],(function(e,a){"use strict";var l,t,u,o,r,s,n,m,d,c,i,f,p,v,g,b,_,y,h,U,C,I,N,k,L,O;return{setters:[e=>{l=e._,t=e.s,u=e.r,o=e.X,r=e.m,s=e.a,n=e.f,m=e.w,d=e.d,c=e.c,i=e.F,f=e.e,p=e.g,v=e.t,g=e.l,b=e.u,_=e.v,y=e.G,h=e.W,U=e.J,C=e.$,I=e.y,N=e.H,k=e.M},e=>{L=e.R},null,e=>{O=e.C},null,null],execute:function(){const a={name:"CustomerEdit",components:{RegionSelector:L},props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const l=t({}),s=u(null),n=u(null),m=u(!1),d=u(!1),c=O.getCustomerTypeOptions(),i=O.getCustomerLevelOptions(),f=O.getCustomerStatusOptions(),p=t({customerCode:[{required:!0,message:"请输入客户编码",trigger:"blur"},{max:50,message:"客户编码不能超过50个字符",trigger:"blur"}],customerName:[{required:!0,message:"请输入客户名称",trigger:"blur"},{max:200,message:"客户名称不能超过200个字符",trigger:"blur"}],customerType:[{required:!0,message:"请选择客户类型",trigger:"change"}],contactEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],contactPhone:[{pattern:/^[0-9-()（）\s]+$/,message:"请输入正确的电话号码",trigger:"blur"}],contactMobile:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],creditLimit:[{type:"number",min:0,message:"信用额度不能小于0",trigger:"blur"}],paymentTerms:[{type:"number",min:0,message:"账期天数不能小于0",trigger:"blur"}]}),v=e=>{a("update:visible",e)};return o((()=>e.visible),(async a=>{var t;if(a)if(null!==(t=e.data)&&void 0!==t&&t.customerId){d.value=!0,Object.assign(l,e.data);try{m.value=!0;const a=await O.getCustomerRegions({customerId:e.data.customerId});a&&Array.isArray(a)?l.regionIds=a.map((e=>e.regionId)):l.regionIds=[]}catch(u){console.error("获取客户关联区域失败:",u),r.error("获取客户关联区域失败"),l.regionIds=[]}finally{m.value=!1}}else d.value=!1,Object.keys(l).forEach((e=>{l[e]=void 0})),l.customerType="ENTERPRISE",l.customerLevel="BRONZE",l.status="ACTIVE",l.paymentTerms=30,l.creditLimit=0,l.regionIds=[],n.value&&n.value.resetState&&n.value.resetState()}),{immediate:!0}),{form:l,formRef:s,regionSelectorRef:n,loading:m,isUpdate:d,customerTypeOptions:c,customerLevelOptions:i,statusOptions:f,rules:p,updateVisible:v,save:async()=>{try{await s.value.validate(),m.value=!0;const e={...l},t=d.value?O.edit:O.add,u=await t(e),o=d.value?l.customerId:u.data;o&&await O.updateCustomerRegions({customerId:o,regionIds:l.regionIds||[]}),r.success("保存成功"),v(!1),a("done")}catch(e){console.error("保存客户失败:",e),r.error(e.message||"保存失败")}finally{m.value=!1}},handleRegionChange:(e,a)=>{console.log("客户区域选择变化:",e,a),l.regionIds=e}}}};e("default",l(a,[["render",function(e,a,l,t,u,o){const r=g,O=b,T=_,w=y,R=h,x=U,A=C,E=I,S=L,j=N,P=k;return s(),n(P,{title:t.isUpdate?"编辑客户":"新增客户",width:900,visible:l.visible,"confirm-loading":t.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":t.updateVisible,onOk:t.save},{default:m((()=>[d(j,{ref:"formRef",model:t.form,rules:t.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:m((()=>[d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"客户编码",name:"customerCode"},{default:m((()=>[d(r,{value:t.form.customerCode,"onUpdate:value":a[0]||(a[0]=e=>t.form.customerCode=e),placeholder:"请输入客户编码"},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"客户名称",name:"customerName"},{default:m((()=>[d(r,{value:t.form.customerName,"onUpdate:value":a[1]||(a[1]=e=>t.form.customerName=e),placeholder:"请输入客户名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"客户简称",name:"customerShortName"},{default:m((()=>[d(r,{value:t.form.customerShortName,"onUpdate:value":a[2]||(a[2]=e=>t.form.customerShortName=e),placeholder:"请输入客户简称"},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"客户类型",name:"customerType"},{default:m((()=>[d(x,{value:t.form.customerType,"onUpdate:value":a[3]||(a[3]=e=>t.form.customerType=e),placeholder:"请选择客户类型"},{default:m((()=>[(s(!0),c(i,null,f(t.customerTypeOptions,(e=>(s(),n(R,{key:e.value,value:e.value},{default:m((()=>[p(v(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"客户等级",name:"customerLevel"},{default:m((()=>[d(x,{value:t.form.customerLevel,"onUpdate:value":a[4]||(a[4]=e=>t.form.customerLevel=e),placeholder:"请选择客户等级"},{default:m((()=>[(s(!0),c(i,null,f(t.customerLevelOptions,(e=>(s(),n(R,{key:e.value,value:e.value},{default:m((()=>[p(v(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"状态",name:"status"},{default:m((()=>[d(x,{value:t.form.status,"onUpdate:value":a[5]||(a[5]=e=>t.form.status=e),placeholder:"请选择状态"},{default:m((()=>[(s(!0),c(i,null,f(t.statusOptions,(e=>(s(),n(R,{key:e.value,value:e.value},{default:m((()=>[p(v(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"联系人",name:"contactPerson"},{default:m((()=>[d(r,{value:t.form.contactPerson,"onUpdate:value":a[6]||(a[6]=e=>t.form.contactPerson=e),placeholder:"请输入联系人"},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"联系电话",name:"contactPhone"},{default:m((()=>[d(r,{value:t.form.contactPhone,"onUpdate:value":a[7]||(a[7]=e=>t.form.contactPhone=e),placeholder:"请输入联系电话"},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"手机号码",name:"contactMobile"},{default:m((()=>[d(r,{value:t.form.contactMobile,"onUpdate:value":a[8]||(a[8]=e=>t.form.contactMobile=e),placeholder:"请输入手机号码"},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"邮箱地址",name:"contactEmail"},{default:m((()=>[d(r,{value:t.form.contactEmail,"onUpdate:value":a[9]||(a[9]=e=>t.form.contactEmail=e),placeholder:"请输入邮箱地址"},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{span:24},{default:m((()=>[d(O,{label:"联系地址",name:"contactAddress","label-col":{span:3},"wrapper-col":{span:21}},{default:m((()=>[d(A,{value:t.form.contactAddress,"onUpdate:value":a[10]||(a[10]=e=>t.form.contactAddress=e),placeholder:"请输入联系地址",rows:2},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"营业执照号",name:"businessLicenseNo"},{default:m((()=>[d(r,{value:t.form.businessLicenseNo,"onUpdate:value":a[11]||(a[11]=e=>t.form.businessLicenseNo=e),placeholder:"请输入营业执照号"},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"税务登记号",name:"taxNo"},{default:m((()=>[d(r,{value:t.form.taxNo,"onUpdate:value":a[12]||(a[12]=e=>t.form.taxNo=e),placeholder:"请输入税务登记号"},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"开户银行",name:"bankName"},{default:m((()=>[d(r,{value:t.form.bankName,"onUpdate:value":a[13]||(a[13]=e=>t.form.bankName=e),placeholder:"请输入开户银行"},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"银行账号",name:"bankAccount"},{default:m((()=>[d(r,{value:t.form.bankAccount,"onUpdate:value":a[14]||(a[14]=e=>t.form.bankAccount=e),placeholder:"请输入银行账号"},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"信用额度",name:"creditLimit"},{default:m((()=>[d(E,{value:t.form.creditLimit,"onUpdate:value":a[15]||(a[15]=e=>t.form.creditLimit=e),placeholder:"请输入信用额度",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),d(T,{md:12,sm:24},{default:m((()=>[d(O,{label:"账期天数",name:"paymentTerms"},{default:m((()=>[d(E,{value:t.form.paymentTerms,"onUpdate:value":a[16]||(a[16]=e=>t.form.paymentTerms=e),placeholder:"请输入账期天数",min:0,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{span:24},{default:m((()=>[d(O,{label:"关联区域",name:"regionIds","label-col":{span:3},"wrapper-col":{span:21}},{default:m((()=>[d(S,{ref:"regionSelectorRef",modelValue:t.form.regionIds,"onUpdate:modelValue":a[17]||(a[17]=e=>t.form.regionIds=e),placeholder:"请选择客户服务的区域",onChange:t.handleRegionChange},null,8,["modelValue","onChange"])])),_:1})])),_:1})])),_:1}),d(w,{gutter:16},{default:m((()=>[d(T,{span:24},{default:m((()=>[d(O,{label:"备注",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:m((()=>[d(A,{value:t.form.remark,"onUpdate:value":a[18]||(a[18]=e=>t.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","visible","confirm-loading","onUpdate:visible","onOk"])}]]))}}}));
