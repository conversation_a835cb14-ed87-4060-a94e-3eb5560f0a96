# UniversalTree 组件测试指南（简化版）

## 快速开始

### 运行测试

```bash
# 在项目根目录运行所有UniversalTree测试
npm run test:unit src/components/UniversalTree/__tests__/

# 运行基本组件测试
npm run test:unit src/components/UniversalTree/__tests__/basic.test.js

# 运行工具函数测试
npm run test:unit src/components/UniversalTree/__tests__/utils-basic.test.js
```

## 测试文件说明

### ✅ basic.test.js (4个测试用例)
- 组件实例创建测试
- Props参数接收测试  
- API调用测试
- 公共方法暴露测试

### ✅ utils-basic.test.js (6个测试用例)
- 数据格式化测试
- ID提取测试
- 配置验证测试
- 错误处理测试

## 测试结果示例

```
✓ src/components/UniversalTree/__tests__/basic.test.js (4)
✓ src/components/UniversalTree/__tests__/utils-basic.test.js (6)

Test Files  2 passed (2)
Tests  10 passed (10)
```

## 常见问题

### 1. 如果测试失败
```bash
# 确保依赖已安装
npm install

# 检查Node.js版本（需要16+）
node --version
```

### 2. 如果遇到配置错误
确保项目根目录的 `vitest.config.js` 配置正确。

### 3. 如果需要调试
在测试代码中添加 `console.log` 或使用调试器：
```javascript
it('测试用例', () => {
  console.log('调试信息')
  // 测试代码...
})
```

## 测试覆盖的功能

- ✅ 组件基本渲染
- ✅ 配置参数处理
- ✅ API数据加载
- ✅ 数据格式化
- ✅ 错误处理
- ✅ 公共方法暴露

这些测试确保了UniversalTree组件的核心功能正常工作。