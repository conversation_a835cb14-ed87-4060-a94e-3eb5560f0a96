System.register(["./index-legacy-ee1db0c7.js","./performance-monitor-legacy-4ff0ac5f.js","./constants-legacy-2a31d63c.js"],(function(e,t){"use strict";var r,a,s,i,n,c,o,p,m,l,y,u,g,d,h,b,x,v,M,w,D,O,S;return{setters:[e=>{r=e.R,a=e._,s=e.r,i=e.a,n=e.c,c=e.b,o=e.d,p=e.w,m=e.at,l=e.ac,y=e.g,u=e.aT,g=e.a5,d=e.h,h=e.m,b=e.I,x=e.B,v=e.aU,M=e.j,w=e.T,D=e.a3},e=>{O=e.P,S=e.a},null],execute:function(){var t=document.createElement("style");t.textContent=".member-search[data-v-c14730c4]{padding:16px 0}.search-tabs[data-v-c14730c4]{margin-bottom:16px}.search-tabs[data-v-c14730c4] .ant-tabs-content-holder{padding-top:12px}.card-input[data-v-c14730c4],.phone-input[data-v-c14730c4]{margin-bottom:16px}.card-search-input[data-v-c14730c4],.phone-search-input[data-v-c14730c4]{width:100%}.no-member-found[data-v-c14730c4]{display:flex;align-items:center;justify-content:center;padding:40px 20px}.search-tip[data-v-c14730c4]{margin-top:8px;color:#8c8c8c;font-size:12px}.error-message[data-v-c14730c4]{margin-top:16px}\n",document.head.appendChild(t);class f{static createApiWrapper(e,t={}){const{context:r="Member API调用",showMessage:a=!0,showNotification:s=!1,retryOptions:i={maxRetries:2,retryDelay:1e3}}=t,n=S.measureApiCall(r,e);return O.wrapApiCall(n,{showMessage:a,showNotification:s,context:r,retryOptions:i})}static async getMemberByCardNo(e){return this.createApiWrapper((()=>r.get("/erp/member/getByCardNo",{cardNo:e})),{context:"根据卡号查询会员",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberByPhone(e){return this.createApiWrapper((()=>r.get("/erp/member/getByPhone",{phone:e})),{context:"根据手机号查询会员",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberByIdCard(e){return this.createApiWrapper((()=>r.get("/erp/member/getByIdCard",{idCard:e})),{context:"根据身份证查询会员",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberDetail(e){return this.createApiWrapper((()=>r.get("/erp/member/detail",{memberId:e})),{context:"获取会员详细信息",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async searchMembers(e={}){return this.createApiWrapper((()=>r.get("/erp/member/search",{...e,limit:e.limit||20})),{context:"搜索会员",showMessage:!1,retryOptions:{maxRetries:1,retryDelay:300}})()}static async validateMemberPassword(e){return this.createApiWrapper((()=>r.post("/erp/member/validatePassword",e)),{context:"验证会员密码",showMessage:!1,retryOptions:{maxRetries:0}})()}static async calculateMemberDiscount(e){return this.createApiWrapper((()=>r.post("/erp/member/calculateDiscount",e)),{context:"计算会员折扣",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:300}})()}static async calculatePointsDeduction(e){return this.createApiWrapper((()=>r.post("/erp/member/calculatePointsDeduction",e)),{context:"计算积分抵扣",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:300}})()}static async getMemberPoints(e){return this.createApiWrapper((()=>r.get("/erp/member/points",{memberId:e})),{context:"获取会员积分",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async addMemberPoints(e){return this.createApiWrapper((()=>r.post("/erp/member/addPoints",{...e,operateTime:(new Date).toISOString()})),{context:"增加会员积分",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async deductMemberPoints(e){return this.createApiWrapper((()=>r.post("/erp/member/deductPoints",{...e,operateTime:(new Date).toISOString()})),{context:"扣减会员积分",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberPointsHistory(e={}){return this.createApiWrapper((()=>r.get("/erp/member/pointsHistory",{...e,pageNo:e.pageNo||1,pageSize:e.pageSize||20})),{context:"获取积分明细",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberBalance(e){return this.createApiWrapper((()=>r.get("/erp/member/balance",{memberId:e})),{context:"获取会员余额",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async rechargeMemberBalance(e){return this.createApiWrapper((()=>r.post("/erp/member/recharge",{...e,rechargeTime:(new Date).toISOString()})),{context:"会员余额充值",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async deductMemberBalance(e){return this.createApiWrapper((()=>r.post("/erp/member/deductBalance",{...e,operateTime:(new Date).toISOString()})),{context:"扣减会员余额",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberBalanceHistory(e={}){return this.createApiWrapper((()=>r.get("/erp/member/balanceHistory",{...e,pageNo:e.pageNo||1,pageSize:e.pageSize||20})),{context:"获取余额明细",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async upgradeMemberLevel(e){return this.createApiWrapper((()=>r.post("/erp/member/upgradeLevel",{...e,upgradeTime:(new Date).toISOString()})),{context:"升级会员等级",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberLevelRules(){return this.createApiWrapper((()=>r.get("/erp/member/levelRules")),{context:"获取会员等级规则",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async getMemberConsumptionHistory(e={}){return this.createApiWrapper((()=>r.get("/erp/member/consumptionHistory",{...e,pageNo:e.pageNo||1,pageSize:e.pageSize||20})),{context:"获取消费记录",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberStatistics(e){return this.createApiWrapper((()=>r.get("/erp/member/statistics",{memberId:e})),{context:"获取会员统计",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async freezeMember(e){return this.createApiWrapper((()=>r.post("/erp/member/freeze",{...e,freezeTime:(new Date).toISOString()})),{context:"冻结会员账户",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async unfreezeMember(e){return this.createApiWrapper((()=>r.post("/erp/member/unfreeze",{...e,unfreezeTime:(new Date).toISOString()})),{context:"解冻会员账户",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberCoupons(e={}){return this.createApiWrapper((()=>r.get("/erp/member/coupons",e)),{context:"获取会员优惠券",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async issueCouponToMember(e){return this.createApiWrapper((()=>r.post("/erp/member/issueCoupon",{...e,issueTime:(new Date).toISOString()})),{context:"发放优惠券",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}}const A={class:"member-search"},R={class:"search-methods"},W={class:"card-input"},_={class:"phone-input"},I={key:0,class:"no-member-found"},P={key:1,class:"error-message"};e("default",a(Object.assign({name:"MemberSearch"},{__name:"MemberSearch",emits:["memberFound","searchError"],setup(e,{expose:t,emit:r}){const a=r,O=s("card"),S=s(""),C=s(""),N=s(!1),z=s(!1),B=s(""),T=async()=>{S.value.trim()?await j("card",S.value.trim()):h.warning("请输入会员卡号")},E=async()=>{C.value.trim()?await j("phone",C.value.trim()):h.warning("请输入手机号")},j=async(e,t)=>{try{let r;N.value=!0,z.value=!0,B.value="",r="card"===e?await f.getMemberByCardNo(t):await f.getMemberByPhone(t),r?(a("memberFound",r),S.value="",C.value="",z.value=!1):h.warning("未找到会员信息")}catch(r){console.error("搜索会员失败:",r),B.value=r.message||"搜索会员失败，请重试",a("searchError",r)}finally{N.value=!1}},H=()=>{B.value=""};return t({resetSearch:()=>{S.value="",C.value="",z.value=!1,B.value="",N.value=!1},searchMember:j}),(e,t)=>{const r=b,a=x,s=v,h=M,f=w,j=g,k=D;return i(),n("div",A,[c("div",R,[o(f,{activeKey:O.value,"onUpdate:activeKey":t[2]||(t[2]=e=>O.value=e),size:"small",class:"search-tabs"},{default:p((()=>[o(h,{key:"card",tab:"会员卡"},{default:p((()=>[c("div",W,[o(s,{value:S.value,"onUpdate:value":t[0]||(t[0]=e=>S.value=e),placeholder:"请输入会员卡号或扫描会员卡",size:"large",onSearch:T,onPressEnter:T,loading:N.value,allowClear:"",class:"card-search-input"},{prefix:p((()=>[o(r,{iconClass:"icon-card"})])),enterButton:p((()=>[o(a,{type:"primary"},{icon:p((()=>[o(m(l))])),default:p((()=>[t[3]||(t[3]=y(" 查询 "))])),_:1,__:[3]})])),_:1},8,["value","loading"])])])),_:1}),o(h,{key:"phone",tab:"手机号"},{default:p((()=>[c("div",_,[o(s,{value:C.value,"onUpdate:value":t[1]||(t[1]=e=>C.value=e),placeholder:"请输入会员手机号",size:"large",onSearch:E,onPressEnter:E,loading:N.value,allowClear:"",class:"phone-search-input"},{prefix:p((()=>[o(m(u))])),enterButton:p((()=>[o(a,{type:"primary"},{icon:p((()=>[o(m(l))])),default:p((()=>[t[4]||(t[4]=y(" 查询 "))])),_:1,__:[4]})])),_:1},8,["value","loading"])])])),_:1})])),_:1},8,["activeKey"])]),z.value&&!N.value?(i(),n("div",I,[o(j,{description:"未找到会员信息",image:m(g).PRESENTED_IMAGE_SIMPLE},{default:p((()=>t[5]||(t[5]=[c("p",{class:"search-tip"},"请检查会员卡号或手机号是否正确",-1)]))),_:1,__:[5]},8,["image"])])):d("",!0),B.value?(i(),n("div",P,[o(k,{message:B.value,type:"error","show-icon":"",closable:"",onClose:H},null,8,["message"])])):d("",!0)])}}}),[["__scopeId","data-v-c14730c4"]]))}}}));
