package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * POS订单响应参数
 *
 * <AUTHOR>
 * @since 2025/08/01 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PosOrderResponse extends BaseResponse {

    /**
     * 订单ID
     */
    @ChineseDescription("订单ID")
    private Long orderId;

    /**
     * 订单号
     */
    @ChineseDescription("订单号")
    private String orderNo;

    /**
     * 会员ID
     */
    @ChineseDescription("会员ID")
    private Long memberId;

    /**
     * 会员姓名
     */
    @ChineseDescription("会员姓名")
    private String memberName;

    /**
     * 订单总金额
     */
    @ChineseDescription("订单总金额")
    private BigDecimal totalAmount;

    /**
     * 折扣金额
     */
    @ChineseDescription("折扣金额")
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    @ChineseDescription("实付金额")
    private BigDecimal finalAmount;

    /**
     * 订单状态
     */
    @ChineseDescription("订单状态")
    private String orderStatus;

    /**
     * 订单状态名称
     */
    @ChineseDescription("订单状态名称")
    private String orderStatusName;

    /**
     * 支付状态
     */
    @ChineseDescription("支付状态")
    private String paymentStatus;

    /**
     * 支付状态名称
     */
    @ChineseDescription("支付状态名称")
    private String paymentStatusName;

    /**
     * 支付方式
     */
    @ChineseDescription("支付方式")
    private String paymentMethod;

    /**
     * 支付方式名称
     */
    @ChineseDescription("支付方式名称")
    private String paymentMethodName;

    /**
     * 收银员ID
     */
    @ChineseDescription("收银员ID")
    private Long cashierId;

    /**
     * 收银员姓名
     */
    @ChineseDescription("收银员姓名")
    private String cashierName;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;


    /**
     * 订单项列表
     */
    @ChineseDescription("订单项列表")
    private List<PosOrderItemResponse> orderItems;

}