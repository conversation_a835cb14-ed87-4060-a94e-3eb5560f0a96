System.register(["./index-legacy-ee1db0c7.js"],(function(a,e){"use strict";var t,l,i,n,c,o,s,d,r,p,m,f,u,g,b,_;return{setters:[a=>{t=a._,l=a.a,i=a.c,n=a.b,c=a.d,o=a.I,s=a.w,d=a.g,r=a.M,p=a.aZ,m=a.B,f=a.a7,u=a.p,g=a.a_,b=a.q,_=a.D}],execute:function(){var e=document.createElement("style");e.textContent=".cart-header[data-v-cfa17859]{display:flex;align-items:center;justify-content:space-between;padding:16px;border-bottom:1px solid #f0f0f0;background:#fafafa}.cart-title[data-v-cfa17859]{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#262626}.cart-actions[data-v-cfa17859]{display:flex;align-items:center;gap:8px}[data-v-cfa17859] .danger-item{color:#ff4d4f!important}[data-v-cfa17859] .danger-item:hover{background-color:#fff2f0!important}@media (max-width: 768px){.cart-header[data-v-cfa17859]{padding:12px}.cart-title[data-v-cfa17859]{font-size:14px}}.cart-header[data-v-cfa17859],.cart-actions .ant-btn[data-v-cfa17859]{transition:all .2s ease}.cart-actions .ant-btn[data-v-cfa17859]:hover{transform:translateY(-1px)}\n",document.head.appendChild(e);const y={class:"cart-header"},x={class:"cart-title"},v={class:"cart-actions"},k=Object.assign({name:"CartHeader"},{__name:"CartHeader",props:{itemCount:{type:Number,default:0},isEmpty:{type:Boolean,default:!0},loading:{type:Boolean,default:!1}},emits:["clear-cart","scan-barcode","import-list","export-list","save-template","load-template"],setup(a,{emit:e}){const t=e,k=()=>{r.confirm({title:"确认清空购物车？",content:"此操作将清空购物车中的所有商品，是否继续？",okText:"确认清空",okType:"danger",cancelText:"取消",onOk:()=>{t("clear-cart")}})},C=()=>{t("scan-barcode")},h=({key:a})=>{switch(a){case"import":t("import-list");break;case"export":t("export-list");break;case"save-template":t("save-template");break;case"load-template":t("load-template");break;case"clear":k()}};return(e,t)=>{const r=p,E=m,w=f,z=u,j=g,B=b,T=_;return l(),i("div",y,[n("div",x,[c(o,{iconClass:"icon-cart"}),t[0]||(t[0]=n("span",null,"购物车",-1)),c(r,{count:a.itemCount,"number-style":{backgroundColor:"#52c41a"}},null,8,["count"])]),n("div",v,[c(w,{title:"清空购物车"},{default:s((()=>[c(E,{type:"text",size:"small",disabled:a.isEmpty||a.loading,onClick:k},{icon:s((()=>[c(o,{iconClass:"icon-delete"})])),_:1},8,["disabled"])])),_:1}),c(w,{title:"扫码添加商品"},{default:s((()=>[c(E,{type:"text",size:"small",loading:a.loading,onClick:C},{icon:s((()=>[c(o,{iconClass:"icon-scan"})])),_:1},8,["loading"])])),_:1}),c(T,{trigger:["click"],placement:"bottomRight"},{overlay:s((()=>[c(B,{onClick:h},{default:s((()=>[c(z,{key:"import",disabled:a.loading},{default:s((()=>[c(o,{iconClass:"icon-import"}),t[1]||(t[1]=d(" 导入购物清单 "))])),_:1,__:[1]},8,["disabled"]),c(z,{key:"export",disabled:a.isEmpty||a.loading},{default:s((()=>[c(o,{iconClass:"icon-export"}),t[2]||(t[2]=d(" 导出购物清单 "))])),_:1,__:[2]},8,["disabled"]),c(j),c(z,{key:"save-template",disabled:a.isEmpty||a.loading},{default:s((()=>[c(o,{iconClass:"icon-save"}),t[3]||(t[3]=d(" 保存为模板 "))])),_:1,__:[3]},8,["disabled"]),c(z,{key:"load-template",disabled:a.loading},{default:s((()=>[c(o,{iconClass:"icon-template"}),t[4]||(t[4]=d(" 加载模板 "))])),_:1,__:[4]},8,["disabled"]),c(j),c(z,{key:"clear",class:"danger-item",disabled:a.isEmpty||a.loading},{default:s((()=>[c(o,{iconClass:"icon-delete"}),t[5]||(t[5]=d(" 清空购物车 "))])),_:1,__:[5]},8,["disabled"])])),_:1})])),default:s((()=>[c(E,{type:"text",size:"small"},{icon:s((()=>[c(o,{iconClass:"icon-more"})])),_:1})])),_:1})])])}}});a("default",t(k,[["__scopeId","data-v-cfa17859"]]))}}}));
