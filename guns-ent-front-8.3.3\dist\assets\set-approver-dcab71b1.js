import{_ as O}from"./index-3a0e5c06.js";import{R as l,_ as N,s as V,r as z,k as F,a as n,c as d,d as _,w as p,f as u,b as t,F as A,e as k,t as h,g as M,h as $,M as j,m as w,B as q,be as E,G,a5 as W,a0 as H}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";class L{static bindUserList(r){return l.post("/hrOrgApprover/bindUserList",r)}static delete(r){return l.post("/hrOrgApprover/delete",r)}static getApproverTypeList(r){return l.getAndLoadData("/hrOrgApprover/getApproverTypeList",r)}static getBindingList(r){return l.get("/hrOrgApprover/getBindingList",r)}}const J={style:{width:"100%",height:"100%"}},K={class:"role-ul"},P={class:"title"},Q={class:"content"},X={class:"content-item"},Y={class:"left"},Z=["src"],ee={class:"right"},te={class:"username"},se={class:"companyname"},oe=["onClick"],re={class:"add"},ae={__name:"set-approver",props:{data:Object},setup(f,{expose:r}){const m=f,s=V({approverList:[],isShowRole:!1,current:null}),y=z({selectUserList:[]}),v=()=>{L.getBindingList({orgId:m.data.orgId}).then(o=>{o.code=="00000"&&(s.approverList=o.data)})},U=(o,e)=>{let a={orgId:m.data.orgId,orgApproverType:o,userId:e};j.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5417?",icon:"",maskClosable:!0,onOk:async()=>{const g=await L.delete(a);w.success(g.message),v()}})},B=o=>{y.value.selectUserList=o.bindUserItemList.map(e=>({bizId:e.userId,name:e.name})),s.current=o,s.isShowRole=!0},C=o=>{let e={orgApproverType:s.current.orgApproverType,orgId:m.data.orgId,userIdList:o.selectUserList.map(a=>a.bizId)};L.bindUserList(e).then(a=>{a.code=="00000"&&(w.success(a.message),v())})};return r({getListData:v}),(o,e)=>{const a=q,g=E,I=F("plus-outlined"),b=G,R=W,T=H,x=O;return n(),d("div",J,[_(T,{bordered:!1},{default:p(()=>[s.approverList&&s.approverList.length>0?(n(),u(b,{key:0,class:"row"},{default:p(()=>[t("ul",K,[(n(!0),d(A,null,k(s.approverList,(i,S)=>(n(),d("li",{key:S},[t("div",P,h(i.orgApproverTypeWrapper),1),t("div",Q,[(n(!0),d(A,null,k(i.bindUserItemList,(c,D)=>(n(),u(g,{title:"\u64CD\u4F5C",key:D},{content:p(()=>[_(a,{style:{color:"red"},onClick:ne=>U(i.orgApproverType,c.userId)},{default:p(()=>e[1]||(e[1]=[M("\u5220\u9664")])),_:2,__:[1]},1032,["onClick"])]),default:p(()=>[t("div",X,[t("div",Y,[t("img",{src:c.avatarUrl,alt:"",class:"img"},null,8,Z)]),t("div",ee,[t("div",te,h(c.name),1),t("div",se,h(c.deptName),1)])])]),_:2},1024))),128)),t("div",{class:"box1",onClick:c=>B(i)},[t("div",re,[_(I,{class:"icon"})]),e[2]||(e[2]=t("div",{class:"addrole"},"\u6DFB\u52A0\u6210\u5458",-1))],8,oe)])]))),128))])]),_:1})):(n(),u(b,{key:1,class:"empty"},{default:p(()=>[_(R)]),_:1}))]),_:1}),s.isShowRole?(n(),u(x,{key:0,visible:s.isShowRole,"onUpdate:visible":e[0]||(e[0]=i=>s.isShowRole=i),title:"\u9009\u62E9\u5BA1\u6279\u4EBA",data:y.value,showTab:["user"],onDone:C,isRadio:!1},null,8,["visible","data"])):$("",!0)])}}},Le=N(ae,[["__scopeId","data-v-d995fbb4"]]);export{Le as default};
