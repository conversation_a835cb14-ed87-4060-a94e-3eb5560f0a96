package cn.stylefeng.roses.kernel.oauth2.modular.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.oauth2.api.pojo.params.OauthUserInfoParam;
import cn.stylefeng.roses.kernel.oauth2.api.pojo.result.OauthUserInfoResult;
import cn.stylefeng.roses.kernel.oauth2.modular.entity.OauthUserInfo;
import cn.stylefeng.roses.kernel.oauth2.modular.mapper.OauthUserInfoMapper;
import cn.stylefeng.roses.kernel.oauth2.modular.service.Oauth2UserInfoService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 第三方用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-09
 */
@Service
public class OauthUserInfoServiceImpl extends ServiceImpl<OauthUserInfoMapper, OauthUserInfo> implements Oauth2UserInfoService {

    @Override
    public void add(OauthUserInfoParam param) {
        OauthUserInfo entity = getEntity(param);
        this.save(entity);
    }

    @Override
    public void delete(OauthUserInfoParam param) {
        this.removeById(getKey(param));
    }

    @Override
    public void update(OauthUserInfoParam param) {
        OauthUserInfo oldEntity = getOldEntity(param);
        OauthUserInfo newEntity = getEntity(param);
        BeanUtil.copyProperties(newEntity, oldEntity);
        this.updateById(newEntity);
    }

    @Override
    public OauthUserInfoResult findBySpec(OauthUserInfoParam param) {
        return null;
    }

    @Override
    public List<OauthUserInfoResult> findListBySpec(OauthUserInfoParam param) {
        return null;
    }

    @Override
    public PageResult<OauthUserInfoResult> findPageBySpec(OauthUserInfoParam param) {
        Page<OauthUserInfoResult> page = this.baseMapper.customPageList(PageFactory.defaultPage(), param);
        return PageResultFactory.createPageResult(page);
    }

    @Override
    public String getAvatarUrl(Long userId) {
        OauthUserInfo oauthUserInfo = this.getOne(new QueryWrapper<OauthUserInfo>().eq("user_id", userId));
        return oauthUserInfo.getAvatar();
    }

    private Serializable getKey(OauthUserInfoParam param) {
        return param.getOauthId();
    }

    private OauthUserInfo getOldEntity(OauthUserInfoParam param) {
        return this.getById(getKey(param));
    }

    private OauthUserInfo getEntity(OauthUserInfoParam param) {
        OauthUserInfo entity = new OauthUserInfo();
        BeanUtil.copyProperties(param, entity);
        return entity;
    }

}
