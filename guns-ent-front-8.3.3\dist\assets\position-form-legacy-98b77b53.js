System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js"],(function(e,l){"use strict";var o,t,r,a,u,s,i,n,m,d,f,p;return{setters:[e=>{o=e.s,t=e.a,r=e.f,a=e.w,u=e.d,s=e.l,i=e.u,n=e.v,m=e.y,d=e.$,f=e.G,p=e.H},null],execute:function(){e("default",{__name:"position-form",props:{form:Object},setup(e){const l=o({positionName:[{required:!0,message:"请输入职位名称",type:"string",trigger:"blur"}],positionCode:[{required:!0,message:"请输入职位编码",type:"string",trigger:"blur"}],positionSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}]});return(o,c)=>{const g=s,v=i,_=n,b=m,y=d,w=f,x=p;return t(),r(x,{ref:"formRef",model:e.form,rules:l,layout:"vertical"},{default:a((()=>[u(w,{gutter:20},{default:a((()=>[u(_,{xs:24,sm:24,md:12},{default:a((()=>[u(v,{label:"职位名称:",name:"positionName"},{default:a((()=>[u(g,{value:e.form.positionName,"onUpdate:value":c[0]||(c[0]=l=>e.form.positionName=l),"allow-clear":"",placeholder:"请输入职位名称"},null,8,["value"])])),_:1})])),_:1}),u(_,{xs:24,sm:24,md:12},{default:a((()=>[u(v,{label:"职位编码:",name:"positionCode"},{default:a((()=>[u(g,{value:e.form.positionCode,"onUpdate:value":c[1]||(c[1]=l=>e.form.positionCode=l),"allow-clear":"",placeholder:"请输入职位编码"},null,8,["value"])])),_:1})])),_:1}),u(_,{xs:24,sm:24,md:12},{default:a((()=>[u(v,{label:"排序:",name:"positionSort"},{default:a((()=>[u(b,{value:e.form.positionSort,"onUpdate:value":c[2]||(c[2]=l=>e.form.positionSort=l),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),u(_,{span:24},{default:a((()=>[u(v,{label:"备注"},{default:a((()=>[u(y,{value:e.form.remark,"onUpdate:value":c[3]||(c[3]=l=>e.form.remark=l),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
