import{R as a}from"./index-18a1ea24.js";class s{static findPage(t){return a.getAndLoadData("/erp/inventory/history/page",t)}static findList(t){return a.getAndLoadData("/erp/inventory/history/list",t)}static productHistory(t){return a.getAndLoadData("/erp/inventory/history/product",t)}static detail(t){return a.getAndLoadData("/erp/inventory/history/detail",t)}static statistics(t){return a.getAndLoadData("/erp/inventory/history/statistics",t)}static getOperationTypeOptions(){return[{label:"\u5165\u5E93",value:"IN"},{label:"\u51FA\u5E93",value:"OUT"},{label:"\u8C03\u6574",value:"ADJUST"},{label:"\u9500\u552E",value:"SALE"},{label:"\u8BBE\u7F6E\u9884\u8B66",value:"SET_ALERT"}]}static getOperationTypeName(t){const r=s.getOperationTypeOptions().find(e=>e.value===t);return r?r.label:t}static getOperationTypeColor(t){switch(t){case"IN":return"green";case"OUT":return"red";case"ADJUST":return"blue";case"SALE":return"orange";case"SET_ALERT":return"purple";default:return"default"}}static formatQuantityChange(t,n){if(!t&&t!==0)return"0";const r=parseFloat(t)||0,e=Math.abs(r).toFixed(3);switch(n){case"IN":return"+".concat(e);case"OUT":case"SALE":return"-".concat(e);case"ADJUST":return r>=0?"+".concat(e):"-".concat(e);case"SET_ALERT":return e;default:return r>=0?"+".concat(e):"-".concat(e)}}static formatAmount(t){return t?parseFloat(t).toFixed(2):"0.00"}static exportHistory(t){return a.download("/erp/inventory/history/export",t)}}export{s as I};
