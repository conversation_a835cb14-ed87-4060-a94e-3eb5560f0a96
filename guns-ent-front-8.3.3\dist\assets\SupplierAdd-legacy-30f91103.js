System.register(["./index-legacy-ee1db0c7.js","./index-legacy-0d30ef09.js","./index-legacy-94a6fc23.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-b540c599.js","./regionApi-legacy-73888494.js"],(function(e,l){"use strict";var a,t,u,n,o,s,r,d,i,m,p,c,f,v,g,b,_,h,y,N,E,x,S,C,U,I,T,A,M,R,O,k;return{setters:[e=>{a=e._,t=e.aq,u=e.s,n=e.r,o=e.X,s=e.k,r=e.a,d=e.f,i=e.w,m=e.d,p=e.c,c=e.F,f=e.e,v=e.g,g=e.t,b=e.b,_=e.h,h=e.m,y=e.l,N=e.u,E=e.v,x=e.G,S=e.W,C=e.J,U=e.$,I=e.U,T=e.a7,A=e.y,M=e.H,R=e.M},e=>{O=e.R},null,null,e=>{k=e.S},null,null],execute:function(){var l=document.createElement("style");l.textContent=".ant-form-item[data-v-f65ba9ae]{margin-bottom:16px}.ant-form-item-label[data-v-f65ba9ae]{font-weight:500}.ant-select-multiple .ant-select-selection-item[data-v-f65ba9ae]{background:#f6f6f6;border:1px solid #d9d9d9;border-radius:4px}.ant-input[data-v-f65ba9ae],.ant-select[data-v-f65ba9ae]{border-radius:4px}@media (max-width: 768px){[data-v-f65ba9ae] .ant-modal{width:95%!important;margin:10px auto}[data-v-f65ba9ae] .ant-form-item-label{text-align:left!important}}\n",document.head.appendChild(l);const w={name:"SupplierAdd",components:{RegionSelector:O,QuestionCircleOutlined:t},props:{visible:Boolean},emits:["update:visible","done"],setup(e,{emit:l}){const a=u({supplierType:"ENTERPRISE",businessMode:"PURCHASE_SALE",salesDeduction:null,creditLevel:"C",status:"ACTIVE",regionIds:[]}),t=n(null),s=n(null),r=n(!1),d=k.getSupplierTypeOptions(),i=k.getSupplierStatusOptions(),m=k.getCreditLevelOptions(),p=u({supplierCode:[{required:!0,message:"请输入供应商编码",trigger:"blur"},{max:50,message:"供应商编码不能超过50个字符",trigger:"blur"}],supplierName:[{required:!0,message:"请输入供应商名称",trigger:"blur"},{max:200,message:"供应商名称不能超过200个字符",trigger:"blur"}],supplierType:[{required:!0,message:"请选择供应商类型",trigger:"change"}],businessMode:[{required:!0,message:"请选择经营方式",trigger:"change"}],contactEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],contactPhone:[{pattern:/^[0-9-()（）\s]+$/,message:"请输入正确的电话号码",trigger:"blur"}],contactMobile:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],salesDeduction:[{validator:(e,l)=>"JOINT_VENTURE"!==a.businessMode&&"CONSIGNMENT"!==a.businessMode||null!=l?null!=l&&(l<0||l>100)?Promise.reject("销售扣点必须在0-100之间"):Promise.resolve():Promise.reject("请输入销售扣点"),trigger:"blur"}]}),c=e=>{l("update:visible",e)};return o((()=>e.visible),(e=>{e&&(Object.keys(a).forEach((e=>{a[e]="supplierType"===e?"ENTERPRISE":"businessMode"===e?"PURCHASE_SALE":"creditLevel"===e?"C":"status"===e?"ACTIVE":"regionIds"===e?[]:"salesDeduction"===e?null:void 0})),t.value&&t.value.resetFields(),s.value&&s.value.resetState&&s.value.resetState())}),{immediate:!0}),{form:a,formRef:t,regionSelectorRef:s,loading:r,supplierTypeOptions:d,statusOptions:i,creditLevelOptions:m,rules:p,updateVisible:c,handleBusinessModeChange:e=>{console.log("经营方式变更:",e),"PURCHASE_SALE"===e&&(a.salesDeduction=null)},handleSalesDeductionChange:e=>{console.log("销售扣点变更:",e)},save:async()=>{try{await t.value.validate(),r.value=!0;const e={...a},u=(await k.add(e)).data;u&&a.regionIds&&a.regionIds.length>0&&await k.updateSupplierRegions({supplierId:u,regionIds:a.regionIds}),h.success("新增供应商成功"),c(!1),l("done")}catch(e){console.error("新增供应商失败:",e),h.error(e.message||"新增失败")}finally{r.value=!1}},handleRegionChange:(e,l)=>{console.log("供应商区域选择变化:",e,l),a.regionIds=e}}}},P={style:{display:"flex","align-items":"center",gap:"8px"}},L={style:{display:"flex","justify-content":"space-between","align-items":"center"}},j={style:{display:"flex","justify-content":"space-between","align-items":"center"}},V={style:{display:"flex","justify-content":"space-between","align-items":"center"}};e("default",a(w,[["render",function(e,l,a,t,u,n){const o=y,h=N,k=E,w=x,D=S,q=C,H=U,J=I,B=s("question-circle-outlined"),G=T,z=A,F=O,$=M,Q=R;return r(),d(Q,{visible:a.visible,title:"新增供应商",width:800,"confirm-loading":t.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":t.updateVisible,onOk:t.save},{default:i((()=>[m($,{ref:"formRef",model:t.form,rules:t.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:i((()=>[m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"供应商编码",name:"supplierCode"},{default:i((()=>[m(o,{value:t.form.supplierCode,"onUpdate:value":l[0]||(l[0]=e=>t.form.supplierCode=e),placeholder:"请输入供应商编码"},null,8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"供应商名称",name:"supplierName"},{default:i((()=>[m(o,{value:t.form.supplierName,"onUpdate:value":l[1]||(l[1]=e=>t.form.supplierName=e),placeholder:"请输入供应商名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"供应商简称",name:"supplierShortName"},{default:i((()=>[m(o,{value:t.form.supplierShortName,"onUpdate:value":l[2]||(l[2]=e=>t.form.supplierShortName=e),placeholder:"请输入供应商简称"},null,8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"供应商类型",name:"supplierType"},{default:i((()=>[m(q,{value:t.form.supplierType,"onUpdate:value":l[3]||(l[3]=e=>t.form.supplierType=e),placeholder:"请选择供应商类型"},{default:i((()=>[(r(!0),p(c,null,f(t.supplierTypeOptions,(e=>(r(),d(D,{key:e.value,value:e.value},{default:i((()=>[v(g(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"联系人",name:"contactPerson"},{default:i((()=>[m(o,{value:t.form.contactPerson,"onUpdate:value":l[4]||(l[4]=e=>t.form.contactPerson=e),placeholder:"请输入联系人"},null,8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"联系电话",name:"contactPhone"},{default:i((()=>[m(o,{value:t.form.contactPhone,"onUpdate:value":l[5]||(l[5]=e=>t.form.contactPhone=e),placeholder:"请输入联系电话"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"手机号码",name:"contactMobile"},{default:i((()=>[m(o,{value:t.form.contactMobile,"onUpdate:value":l[6]||(l[6]=e=>t.form.contactMobile=e),placeholder:"请输入手机号码"},null,8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"邮箱地址",name:"contactEmail"},{default:i((()=>[m(o,{value:t.form.contactEmail,"onUpdate:value":l[7]||(l[7]=e=>t.form.contactEmail=e),placeholder:"请输入邮箱地址"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{span:24},{default:i((()=>[m(h,{label:"联系地址",name:"contactAddress","label-col":{span:3},"wrapper-col":{span:21}},{default:i((()=>[m(H,{value:t.form.contactAddress,"onUpdate:value":l[8]||(l[8]=e=>t.form.contactAddress=e),placeholder:"请输入联系地址",rows:2},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"营业执照号",name:"businessLicenseNo"},{default:i((()=>[m(o,{value:t.form.businessLicenseNo,"onUpdate:value":l[9]||(l[9]=e=>t.form.businessLicenseNo=e),placeholder:"请输入营业执照号"},null,8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"税务登记号",name:"taxNo"},{default:i((()=>[m(o,{value:t.form.taxNo,"onUpdate:value":l[10]||(l[10]=e=>t.form.taxNo=e),placeholder:"请输入税务登记号"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"开户银行",name:"bankName"},{default:i((()=>[m(o,{value:t.form.bankName,"onUpdate:value":l[11]||(l[11]=e=>t.form.bankName=e),placeholder:"请输入开户银行"},null,8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"银行账号",name:"bankAccount"},{default:i((()=>[m(o,{value:t.form.bankAccount,"onUpdate:value":l[12]||(l[12]=e=>t.form.bankAccount=e),placeholder:"请输入银行账号"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"经营方式",name:"businessMode"},{default:i((()=>[b("div",P,[m(q,{value:t.form.businessMode,"onUpdate:value":l[13]||(l[13]=e=>t.form.businessMode=e),placeholder:"请选择经营方式",onChange:t.handleBusinessModeChange,style:{flex:"1"}},{default:i((()=>[m(D,{value:"PURCHASE_SALE"},{default:i((()=>[b("div",L,[l[20]||(l[20]=b("span",null,"购销",-1)),m(J,{color:"blue",size:"small"},{default:i((()=>l[19]||(l[19]=[v("采购销售")]))),_:1,__:[19]})])])),_:1}),m(D,{value:"JOINT_VENTURE"},{default:i((()=>[b("div",j,[l[22]||(l[22]=b("span",null,"联营",-1)),m(J,{color:"green",size:"small"},{default:i((()=>l[21]||(l[21]=[v("联合经营")]))),_:1,__:[21]})])])),_:1}),m(D,{value:"CONSIGNMENT"},{default:i((()=>[b("div",V,[l[24]||(l[24]=b("span",null,"代销",-1)),m(J,{color:"orange",size:"small"},{default:i((()=>l[23]||(l[23]=[v("代理销售")]))),_:1,__:[23]})])])),_:1})])),_:1},8,["value","onChange"]),m(G,null,{title:i((()=>l[25]||(l[25]=[b("div",null,[b("div",{style:{"margin-bottom":"8px"}},[b("strong",null,"购销："),v("需要采购入库，管理库存，按采购价结算。适用于传统的采购销售模式。")]),b("div",{style:{"margin-bottom":"8px"}},[b("strong",null,"联营："),v("不需要采购入库，不管理库存，商品归属于供应商，按销售扣点结算。适用于品牌专柜等场景。")]),b("div",null,[b("strong",null,"代销："),v("需要采购入库，管理库存，按实际销售扣点结算。适用于代理销售模式。")])],-1)]))),default:i((()=>[m(B,{style:{color:"#1890ff",cursor:"pointer"}})])),_:1})])])),_:1})])),_:1}),"JOINT_VENTURE"===t.form.businessMode||"CONSIGNMENT"===t.form.businessMode?(r(),d(k,{key:0,md:12,sm:24},{default:i((()=>[m(h,{label:"销售扣点",name:"salesDeduction"},{default:i((()=>[m(z,{value:t.form.salesDeduction,"onUpdate:value":l[14]||(l[14]=e=>t.form.salesDeduction=e),min:0,max:100,precision:2,step:.1,placeholder:"请输入销售扣点",style:{width:"100%"}},{addonAfter:i((()=>l[26]||(l[26]=[v("%")]))),_:1},8,["value"])])),_:1})])),_:1})):_("",!0)])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"信用等级",name:"creditLevel"},{default:i((()=>[m(q,{value:t.form.creditLevel,"onUpdate:value":l[15]||(l[15]=e=>t.form.creditLevel=e),placeholder:"请选择信用等级"},{default:i((()=>[(r(!0),p(c,null,f(t.creditLevelOptions,(e=>(r(),d(D,{key:e.value,value:e.value},{default:i((()=>[v(g(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),m(k,{md:12,sm:24},{default:i((()=>[m(h,{label:"状态",name:"status"},{default:i((()=>[m(q,{value:t.form.status,"onUpdate:value":l[16]||(l[16]=e=>t.form.status=e),placeholder:"请选择状态"},{default:i((()=>[(r(!0),p(c,null,f(t.statusOptions,(e=>(r(),d(D,{key:e.value,value:e.value},{default:i((()=>[v(g(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{span:24},{default:i((()=>[m(h,{label:"关联区域",name:"regionIds","label-col":{span:3},"wrapper-col":{span:21}},{default:i((()=>[m(F,{ref:"regionSelectorRef",modelValue:t.form.regionIds,"onUpdate:modelValue":l[17]||(l[17]=e=>t.form.regionIds=e),placeholder:"请选择供应商服务的区域",onChange:t.handleRegionChange},null,8,["modelValue","onChange"])])),_:1})])),_:1})])),_:1}),m(w,{gutter:16},{default:i((()=>[m(k,{span:24},{default:i((()=>[m(h,{label:"备注",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:i((()=>[m(H,{value:t.form.remark,"onUpdate:value":l[18]||(l[18]=e=>t.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading","onUpdate:visible","onOk"])}],["__scopeId","data-v-f65ba9ae"]]))}}}));
