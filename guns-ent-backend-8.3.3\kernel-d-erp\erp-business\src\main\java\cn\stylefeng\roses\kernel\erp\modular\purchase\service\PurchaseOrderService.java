package cn.stylefeng.roses.kernel.erp.modular.purchase.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseConfirmRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseReceiveRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderResponse;

import java.util.List;

/**
 * 采购入库单Service接口
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
public interface PurchaseOrderService {

    /**
     * 新增采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 采购入库单ID
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    Long add(PurchaseOrderRequest purchaseOrderRequest);

    /**
     * 删除采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    void del(PurchaseOrderRequest purchaseOrderRequest);

    /**
     * 批量删除采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    void batchDelete(PurchaseOrderRequest purchaseOrderRequest);

    /**
     * 编辑采购入库单
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    void edit(PurchaseOrderRequest purchaseOrderRequest);

    /**
     * 查询采购入库单详情
     *
     * @param purchaseOrderRequest 采购入库单请求参数
     * @return 采购入库单详情
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    PurchaseOrderResponse detail(PurchaseOrderRequest purchaseOrderRequest);

    /**
     * 分页查询采购入库单列表
     *
     * @param purchaseOrderQueryRequest 采购入库单查询请求参数
     * @return 采购入库单分页列表
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    PageResult<PurchaseOrderResponse> findPage(PurchaseOrderQueryRequest purchaseOrderQueryRequest);

    /**
     * 查询采购入库单列表
     *
     * @param purchaseOrderQueryRequest 采购入库单查询请求参数
     * @return 采购入库单列表
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    List<PurchaseOrderResponse> findList(PurchaseOrderQueryRequest purchaseOrderQueryRequest);

    /**
     * 确认采购入库单
     *
     * @param purchaseConfirmRequest 采购确认请求参数
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    void confirm(PurchaseConfirmRequest purchaseConfirmRequest);

    /**
     * 采购入库操作
     *
     * @param purchaseReceiveRequest 采购入库请求参数
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    void receive(PurchaseReceiveRequest purchaseReceiveRequest);

    /**
     * 校验供应商经营方式是否允许采购
     *
     * @param supplierId 供应商ID
     * @return 是否允许采购
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    boolean validateSupplierBusinessMode(Long supplierId);

    /**
     * 生成采购入库单号
     *
     * @return 采购入库单号
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    String generateOrderNo();

    /**
     * 校验采购入库单编码是否重复
     *
     * @param orderNo 采购入库单号
     * @param id 采购入库单ID（编辑时传入）
     * @return 是否重复
     * <AUTHOR>
     * @since 2025/07/27 15:00
     */
    boolean validateOrderNoRepeat(String orderNo, Long id);

}