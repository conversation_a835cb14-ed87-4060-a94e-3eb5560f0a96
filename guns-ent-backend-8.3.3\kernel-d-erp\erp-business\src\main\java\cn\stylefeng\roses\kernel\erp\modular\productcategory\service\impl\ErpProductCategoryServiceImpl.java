package cn.stylefeng.roses.kernel.erp.modular.productcategory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpProductCategoryConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpProductCategoryExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProductCategory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductCategoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductCategoryResponse;
import cn.stylefeng.roses.kernel.erp.modular.product.mapper.ErpProductMapper;
import cn.stylefeng.roses.kernel.erp.modular.productcategory.mapper.ErpProductCategoryMapper;
import cn.stylefeng.roses.kernel.erp.modular.productcategory.service.ErpProductCategoryService;

import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.HashMap;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品分类管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
@Service
public class ErpProductCategoryServiceImpl extends ServiceImpl<ErpProductCategoryMapper, ErpProductCategory> implements ErpProductCategoryService {

    @Resource
    private ErpProductCategoryMapper erpProductCategoryMapper;
    
    @Resource
    private ErpProductMapper erpProductMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ErpProductCategoryRequest erpProductCategoryRequest) {
        ErpProductCategory erpProductCategory = new ErpProductCategory();
        BeanUtil.copyProperties(erpProductCategoryRequest, erpProductCategory);

        // 校验分类编码是否重复
        if (this.validateCategoryCodeRepeat(erpProductCategory.getCategoryCode(), null)) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_CODE_REPEAT);
        }

        // 设置默认值
        if (StrUtil.isBlank(erpProductCategory.getStatus())) {
            erpProductCategory.setStatus(ErpProductCategoryConstants.DEFAULT_CATEGORY_STATUS);
        }
        if (ObjectUtil.isNull(erpProductCategory.getSortOrder())) {
            erpProductCategory.setSortOrder(0);
        }

        // 处理父级分类和层级（不包含路径设置）
        this.processParentAndLevelBeforeSave(erpProductCategory);

        // 校验参数
        this.validateCategoryParams(erpProductCategory);

        // 保存到数据库（生成categoryId）
        this.save(erpProductCategory);
        
        // 处理分类路径（保存后，categoryId已生成）
        this.processCategoryPathAfterSave(erpProductCategory);
        
        // 更新分类路径
        this.updateById(erpProductCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ErpProductCategoryRequest erpProductCategoryRequest) {
        ErpProductCategory erpProductCategory = this.queryCategory(erpProductCategoryRequest);

        // 校验分类编码是否重复（排除自己）
        if (this.validateCategoryCodeRepeat(erpProductCategoryRequest.getCategoryCode(), erpProductCategory.getCategoryId())) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_CODE_REPEAT);
        }

        // 校验不能选择自己作为父级
        if (ObjectUtil.isNotNull(erpProductCategoryRequest.getParentId()) && 
            erpProductCategoryRequest.getParentId().equals(erpProductCategory.getCategoryId())) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CANNOT_SELECT_SELF_AS_PARENT);
        }

        BeanUtil.copyProperties(erpProductCategoryRequest, erpProductCategory);

        // 编辑时可以直接处理，因为categoryId已存在
        this.processParentAndLevel(erpProductCategory);

        // 校验参数
        this.validateCategoryParams(erpProductCategory);

        this.updateById(erpProductCategory);
    }

    @Override
    public ErpProductCategoryResponse detail(ErpProductCategoryRequest erpProductCategoryRequest) {
        ErpProductCategory erpProductCategory = this.queryCategory(erpProductCategoryRequest);
        ErpProductCategoryResponse response = new ErpProductCategoryResponse();
        BeanUtil.copyProperties(erpProductCategory, response);

        // 填充扩展信息
        this.fillCategoryExtInfo(response);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(ErpProductCategoryRequest erpProductCategoryRequest) {
        ErpProductCategory erpProductCategory = this.queryCategory(erpProductCategoryRequest);

        // 校验是否有子分类
        if (this.hasChildren(erpProductCategory.getCategoryId())) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_HAS_CHILDREN_CANNOT_DELETE);
        }

        // 校验是否有关联商品
        if (this.hasRelatedProducts(erpProductCategory.getCategoryId())) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_HAS_PRODUCTS_CANNOT_DELETE);
        }

        this.removeById(erpProductCategory.getCategoryId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ErpProductCategoryRequest erpProductCategoryRequest) {
        List<String> categoryIds = erpProductCategoryRequest.getCategoryIds();
        if (ObjectUtil.isEmpty(categoryIds)) {
            return;
        }

        for (String categoryIdStr : categoryIds) {
            Long categoryId = Long.valueOf(categoryIdStr);
            ErpProductCategoryRequest deleteRequest = new ErpProductCategoryRequest();
            deleteRequest.setCategoryId(categoryId);
            this.delete(deleteRequest);
        }
    }

    @Override
    public PageResult<ErpProductCategoryResponse> findPage(ErpProductCategoryRequest erpProductCategoryRequest) {
        LambdaQueryWrapper<ErpProductCategory> wrapper = createWrapper(erpProductCategoryRequest);
        Page<ErpProductCategory> page = this.page(PageFactory.defaultPage(), wrapper);

        // 转换数据
        List<ErpProductCategoryResponse> responseList = page.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResultFactory.createPageResult(responseList, page.getTotal(),
                (int) page.getCurrent(), (int) page.getSize());
    }

    @Override
    public List<ErpProductCategoryResponse> findList(ErpProductCategoryRequest erpProductCategoryRequest) {
        LambdaQueryWrapper<ErpProductCategory> wrapper = createWrapper(erpProductCategoryRequest);
        List<ErpProductCategory> list = this.list(wrapper);
        return list.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    /**
     * 转换为响应对象
     */
    private ErpProductCategoryResponse convertToResponse(ErpProductCategory category) {
        ErpProductCategoryResponse response = new ErpProductCategoryResponse();
        BeanUtil.copyProperties(category, response);
        this.fillCategoryExtInfo(response);
        return response;
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<ErpProductCategory> createWrapper(ErpProductCategoryRequest request) {
        LambdaQueryWrapper<ErpProductCategory> wrapper = new LambdaQueryWrapper<>();

        // 父级分类筛选
        if (ObjectUtil.isNotNull(request.getParentId())) {
            wrapper.eq(ErpProductCategory::getParentId, request.getParentId());
        }

        // 搜索条件
        if (ObjectUtil.isNotEmpty(request.getSearchText())) {
            wrapper.and(w -> w.like(ErpProductCategory::getCategoryName, request.getSearchText())
                    .or().like(ErpProductCategory::getCategoryCode, request.getSearchText()));
        }

        // 状态筛选
        if (ObjectUtil.isNotNull(request.getOnlyEnabled()) && request.getOnlyEnabled()) {
            wrapper.eq(ErpProductCategory::getStatus, YesOrNotEnum.Y.getCode());
        }

        // 只查询未删除的记录
        wrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        wrapper.orderByAsc(ErpProductCategory::getSortOrder);
        wrapper.orderByAsc(ErpProductCategory::getCategoryLevel);

        return wrapper;
    }

    /**
     * 查询分类
     */
    private ErpProductCategory queryCategory(ErpProductCategoryRequest erpProductCategoryRequest) {
        ErpProductCategory erpProductCategory = this.getById(erpProductCategoryRequest.getCategoryId());
        if (ObjectUtil.isNull(erpProductCategory)) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_NOT_EXIST);
        }
        return erpProductCategory;
    }

    /**
     * 校验分类编码是否重复
     */
    private boolean validateCategoryCodeRepeat(String categoryCode, Long excludeCategoryId) {
        LambdaQueryWrapper<ErpProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProductCategory::getCategoryCode, categoryCode);
        wrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());
        
        if (ObjectUtil.isNotNull(excludeCategoryId)) {
            wrapper.ne(ErpProductCategory::getCategoryId, excludeCategoryId);
        }
        
        return this.count(wrapper) > 0;
    }

    /**
     * 处理父级分类和层级（保存前，不设置路径）
     */
    private void processParentAndLevelBeforeSave(ErpProductCategory erpProductCategory) {
        if (ObjectUtil.isNull(erpProductCategory.getParentId()) || erpProductCategory.getParentId().equals(ErpProductCategoryConstants.ROOT_PARENT_ID)) {
            // 根节点
            erpProductCategory.setParentId(ErpProductCategoryConstants.ROOT_PARENT_ID);
            erpProductCategory.setCategoryLevel(1);
            // 不设置路径，等保存后再设置
        } else {
            // 子节点
            ErpProductCategory parentCategory = this.getById(erpProductCategory.getParentId());
            if (ObjectUtil.isNull(parentCategory)) {
                throw new ServiceException(ErpProductCategoryExceptionEnum.PARENT_CATEGORY_NOT_EXIST);
            }
            
            erpProductCategory.setCategoryLevel(parentCategory.getCategoryLevel() + 1);
            // 不设置路径，等保存后再设置
        }
    }

    /**
     * 处理分类路径（保存后，categoryId已生成）
     */
    private void processCategoryPathAfterSave(ErpProductCategory erpProductCategory) {
        if (ObjectUtil.isNull(erpProductCategory.getParentId()) || erpProductCategory.getParentId().equals(ErpProductCategoryConstants.ROOT_PARENT_ID)) {
            // 根节点：路径就是自己的ID
            erpProductCategory.setCategoryPath(erpProductCategory.getCategoryId().toString());
        } else {
            // 子节点：父级路径 + 分隔符 + 自己的ID
            ErpProductCategory parentCategory = this.getById(erpProductCategory.getParentId());
            if (ObjectUtil.isNotNull(parentCategory)) {
                erpProductCategory.setCategoryPath(parentCategory.getCategoryPath() + ErpProductCategoryConstants.PATH_SEPARATOR + erpProductCategory.getCategoryId());
            } else {
                // 如果父级分类不存在，设置为自己的ID（容错处理）
                erpProductCategory.setCategoryPath(erpProductCategory.getCategoryId().toString());
            }
        }
    }

    /**
     * 处理父级分类和层级（编辑时使用，categoryId已存在）
     */
    private void processParentAndLevel(ErpProductCategory erpProductCategory) {
        if (ObjectUtil.isNull(erpProductCategory.getParentId()) || erpProductCategory.getParentId().equals(ErpProductCategoryConstants.ROOT_PARENT_ID)) {
            // 根节点
            erpProductCategory.setParentId(ErpProductCategoryConstants.ROOT_PARENT_ID);
            erpProductCategory.setCategoryLevel(1);
            erpProductCategory.setCategoryPath(erpProductCategory.getCategoryId().toString());
        } else {
            // 子节点
            ErpProductCategory parentCategory = this.getById(erpProductCategory.getParentId());
            if (ObjectUtil.isNull(parentCategory)) {
                throw new ServiceException(ErpProductCategoryExceptionEnum.PARENT_CATEGORY_NOT_EXIST);
            }

            erpProductCategory.setCategoryLevel(parentCategory.getCategoryLevel() + 1);
            erpProductCategory.setCategoryPath(parentCategory.getCategoryPath() + ErpProductCategoryConstants.PATH_SEPARATOR + erpProductCategory.getCategoryId());
        }
    }

    /**
     * 校验分类参数
     */
    private void validateCategoryParams(ErpProductCategory erpProductCategory) {
        // 校验状态
        this.validateStatus(erpProductCategory.getStatus());

        // 校验层级深度
        if (erpProductCategory.getCategoryLevel() > ErpProductCategoryConstants.MAX_LEVEL_DEPTH) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_LEVEL_EXCEED_MAX_DEPTH);
        }
    }

    /**
     * 校验状态
     */
    private void validateStatus(String status) {
        if (!ErpProductCategoryConstants.CATEGORY_STATUS_ENABLE.equals(status) &&
                !ErpProductCategoryConstants.CATEGORY_STATUS_DISABLE.equals(status)) {
            throw new ServiceException(ErpProductCategoryExceptionEnum.CATEGORY_STATUS_ERROR);
        }
    }

    /**
     * 检查是否有子级分类
     */
    private boolean hasChildren(Long categoryId) {
        LambdaQueryWrapper<ErpProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProductCategory::getParentId, categoryId);
        wrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode()); // 只检查未删除的子分类
        return this.count(wrapper) > 0;
    }
    
    /**
     * 检查分类是否有关联商品
     */
    public boolean hasRelatedProducts(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            return false;
        }
        // 简化处理：检查是否有商品使用该分类作为主分类
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getCategoryId, categoryId);
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()); // 只检查未删除的商品
        return erpProductMapper.selectCount(wrapper) > 0;
    }

    @Override
    public int getProductCount(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            return 0;
        }
        // 简化处理：统计使用该分类作为主分类的商品数量
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getCategoryId, categoryId);
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()); // 只统计未删除的商品
        return erpProductMapper.selectCount(wrapper).intValue();
    }
    
    /**
     * 统计分类下的商品数量
     */
    public int countProductsByCategoryId(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            return 0;
        }
        // 简化处理：统计使用该分类作为主分类的商品数量
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getCategoryId, categoryId);
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()); // 只统计未删除的商品
        return erpProductMapper.selectCount(wrapper).intValue();
    }
    
    /**
     * 批量统计分类下的商品数量
     */
    public Map<Long, Integer> countProductsByCategories(List<Long> categoryIds) {
        if (CollectionUtil.isEmpty(categoryIds)) {
            return new HashMap<>();
        }
        // 简化处理：批量统计使用各分类作为主分类的商品数量
        Map<Long, Integer> result = new HashMap<>();
        for (Long categoryId : categoryIds) {
            LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ErpProduct::getCategoryId, categoryId);
            wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()); // 只统计未删除的商品
            result.put(categoryId, erpProductMapper.selectCount(wrapper).intValue());
        }
        return result;
    }

    /**
     * 填充分类扩展信息
     */
    private void fillCategoryExtInfo(ErpProductCategoryResponse response) {
        // 设置层级名称
        response.setCategoryLevelName(getCategoryLevelName(response.getCategoryLevel()));

        // 设置状态名称
        response.setStatusName(ErpProductCategoryConstants.CATEGORY_STATUS_ENABLE.equals(response.getStatus()) ? "启用" : "停用");

        // 设置父级分类名称
        if (ObjectUtil.isNotNull(response.getParentId()) && !response.getParentId().equals(ErpProductCategoryConstants.ROOT_PARENT_ID)) {
            ErpProductCategory parentCategory = this.getById(response.getParentId());
            if (ObjectUtil.isNotNull(parentCategory)) {
                response.setParentName(parentCategory.getCategoryName());
            }
        }

        // 设置树形结构相关字段
        response.setIsLeaf(checkIsLeaf(response.getCategoryId()));
        response.setHasChildren(!response.getIsLeaf());
        response.setKey(String.valueOf(response.getCategoryId()));
        response.setValue(String.valueOf(response.getCategoryId()));
        response.setTitle(response.getCategoryName());
        response.setIcon(getCategoryIcon(response.getCategoryLevel()));
        
        // 设置商品数量
        if (ObjectUtil.isNotNull(response.getCategoryId())) {
            int productCount = this.countProductsByCategoryId(response.getCategoryId());
            response.setProductCount(productCount);
        }
    }

    /**
     * 获取分类层级名称
     */
    private String getCategoryLevelName(Integer level) {
        if (ObjectUtil.isNull(level)) {
            return "未知";
        }
        switch (level) {
            case 1:
                return ErpProductCategoryConstants.LEVEL_1_NAME;
            case 2:
                return ErpProductCategoryConstants.LEVEL_2_NAME;
            case 3:
                return ErpProductCategoryConstants.LEVEL_3_NAME;
            case 4:
                return ErpProductCategoryConstants.LEVEL_4_NAME;
            case 5:
                return ErpProductCategoryConstants.LEVEL_5_NAME;
            default:
                return "未知";
        }
    }

    /**
     * 获取分类图标
     */
    private String getCategoryIcon(Integer level) {
        if (ObjectUtil.isNull(level)) {
            return ErpProductCategoryConstants.LEVEL_1_ICON;
        }
        switch (level) {
            case 1:
                return ErpProductCategoryConstants.LEVEL_1_ICON;
            case 2:
                return ErpProductCategoryConstants.LEVEL_2_ICON;
            case 3:
                return ErpProductCategoryConstants.LEVEL_3_ICON;
            case 4:
                return ErpProductCategoryConstants.LEVEL_4_ICON;
            case 5:
                return ErpProductCategoryConstants.LEVEL_5_ICON;
            default:
                return ErpProductCategoryConstants.LEVEL_1_ICON;
        }
    }

    @Override
    public List<ErpProductCategoryResponse> findTree(ErpProductCategoryRequest erpProductCategoryRequest) {
        // 如果有搜索条件，使用专门的搜索方法
        if (ObjectUtil.isNotEmpty(erpProductCategoryRequest.getSearchText())) {
            return findTreeWithSearch(erpProductCategoryRequest.getSearchText());
        }

        // 查询所有分类
        LambdaQueryWrapper<ErpProductCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 状态筛选
        if (ObjectUtil.isNotNull(erpProductCategoryRequest.getOnlyEnabled()) && erpProductCategoryRequest.getOnlyEnabled()) {
            queryWrapper.eq(ErpProductCategory::getStatus, YesOrNotEnum.Y.getCode());
        }

        // 只查询未删除的记录
        queryWrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        queryWrapper.orderByAsc(ErpProductCategory::getSortOrder);
        queryWrapper.orderByAsc(ErpProductCategory::getCategoryLevel);

        List<ErpProductCategory> allCategories = this.list(queryWrapper);

        // 获取所有分类ID
        List<Long> allCategoryIds = allCategories.stream()
            .map(ErpProductCategory::getCategoryId)
            .collect(Collectors.toList());
            
        // 批量查询商品数量
        Map<Long, Integer> productCountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(allCategoryIds)) {
            productCountMap = this.countProductsByCategories(allCategoryIds);
        }
        
        // 转换为响应对象
        Map<Long, Integer> finalProductCountMap = productCountMap;
        List<ErpProductCategoryResponse> allCategoryResponses = allCategories.stream().map(item -> {
            ErpProductCategoryResponse response = BeanUtil.copyProperties(item, ErpProductCategoryResponse.class);
            this.fillCategoryExtInfo(response);
            
            // 设置商品数量（优先使用批量查询的结果）
            if (finalProductCountMap.containsKey(item.getCategoryId())) {
                response.setProductCount(finalProductCountMap.get(item.getCategoryId()));
            }
            
            return response;
        }).collect(Collectors.toList());

        // 构建树形结构
        return buildTree(allCategoryResponses, null);
    }

    @Override
    public List<ErpProductCategoryResponse> findTreeWithLazy(ErpProductCategoryRequest erpProductCategoryRequest) {
        LambdaQueryWrapper<ErpProductCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 如果指定了父级ID，只查询该父级的直接子节点
        if (ObjectUtil.isNotNull(erpProductCategoryRequest.getParentId())) {
            queryWrapper.eq(ErpProductCategory::getParentId, erpProductCategoryRequest.getParentId());
        } else {
            // 否则查询顶级节点（parentId = 0）
            queryWrapper.eq(ErpProductCategory::getParentId, 0L);
        }

        // 搜索条件
        if (ObjectUtil.isNotEmpty(erpProductCategoryRequest.getSearchText())) {
            // 如果有搜索条件，需要查询所有匹配的节点及其路径
            return findTreeWithSearch(erpProductCategoryRequest.getSearchText());
        }

        // 状态筛选
        if (ObjectUtil.isNotNull(erpProductCategoryRequest.getOnlyEnabled()) && erpProductCategoryRequest.getOnlyEnabled()) {
            queryWrapper.eq(ErpProductCategory::getStatus, YesOrNotEnum.Y.getCode());
        }

        // 只查询未删除的记录
        queryWrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        queryWrapper.orderByAsc(ErpProductCategory::getSortOrder);
        queryWrapper.orderByAsc(ErpProductCategory::getCategoryLevel);

        List<ErpProductCategory> categoryList = this.list(queryWrapper);

        // 转换为响应对象
        return categoryList.stream().map(item -> {
            ErpProductCategoryResponse response = BeanUtil.copyProperties(item, ErpProductCategoryResponse.class);
            // 设置树形结构相关字段
            response.setKey(String.valueOf(item.getCategoryId()));
            response.setValue(String.valueOf(item.getCategoryId()));
            response.setTitle(item.getCategoryName());
            response.setIsLeaf(checkIsLeaf(item.getCategoryId()));
            response.setHasChildren(!response.getIsLeaf());
            response.setIcon(getCategoryIcon(item.getCategoryLevel()));
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ErpProductCategoryResponse> findTreeForSelector(ErpProductCategoryRequest erpProductCategoryRequest) {
        // 查询所有启用状态的分类
        LambdaQueryWrapper<ErpProductCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (ObjectUtil.isNotEmpty(erpProductCategoryRequest.getSearchText())) {
            queryWrapper.and(w -> w.like(ErpProductCategory::getCategoryName, erpProductCategoryRequest.getSearchText())
                    .or().like(ErpProductCategory::getCategoryCode, erpProductCategoryRequest.getSearchText()));
        }

        // 只查询启用状态的记录
        queryWrapper.eq(ErpProductCategory::getStatus, YesOrNotEnum.Y.getCode());
        queryWrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        queryWrapper.orderByAsc(ErpProductCategory::getCategoryLevel);
        queryWrapper.orderByAsc(ErpProductCategory::getSortOrder);

        List<ErpProductCategory> categoryList = this.list(queryWrapper);

        // 转换为响应对象
        return categoryList.stream().map(item -> {
            ErpProductCategoryResponse response = BeanUtil.copyProperties(item, ErpProductCategoryResponse.class);
            this.fillCategoryExtInfo(response);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 检查是否为叶子节点
     */
    private Boolean checkIsLeaf(Long categoryId) {
        return !hasChildren(categoryId);
    }

    /**
     * 构建树形结构
     */
    private List<ErpProductCategoryResponse> buildTree(List<ErpProductCategoryResponse> allCategories, Long parentId) {
        List<ErpProductCategoryResponse> result = new ArrayList<>();

        for (ErpProductCategoryResponse category : allCategories) {
            // 确定当前要查找的父级ID
            Long targetParentId = (parentId == null) ? ErpProductCategoryConstants.ROOT_PARENT_ID : parentId;

            // 确定当前分类的父级ID
            Long categoryParentId = (category.getParentId() == null) ? ErpProductCategoryConstants.ROOT_PARENT_ID : category.getParentId();

            // 判断是否匹配
            if (ObjectUtil.equal(categoryParentId, targetParentId)) {
                List<ErpProductCategoryResponse> children = buildTree(allCategories, category.getCategoryId());
                category.setChildren(children);
                result.add(category);
            }
        }

        return result;
    }

    /**
     * 带搜索的树形查询
     */
    private List<ErpProductCategoryResponse> findTreeWithSearch(String searchText) {
        // 查询所有匹配的分类
        LambdaQueryWrapper<ErpProductCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(w -> w.like(ErpProductCategory::getCategoryName, searchText)
                .or().like(ErpProductCategory::getCategoryCode, searchText));
        queryWrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());
        queryWrapper.orderByAsc(ErpProductCategory::getSortOrder);

        List<ErpProductCategory> matchedCategories = this.list(queryWrapper);

        // 收集所有需要显示的分类ID（包括匹配的分类及其所有父级）
        Set<Long> pathCategoryIds = new HashSet<>();
        for (ErpProductCategory category : matchedCategories) {
            pathCategoryIds.add(category.getCategoryId());
            // 添加路径上的父节点ID
            addParentIds(pathCategoryIds, category.getCategoryPath());
        }

        // 查询所有需要显示的分类
        if (pathCategoryIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ErpProductCategory> pathWrapper = new LambdaQueryWrapper<>();
        pathWrapper.in(ErpProductCategory::getCategoryId, pathCategoryIds);
        pathWrapper.eq(ErpProductCategory::getDelFlag, YesOrNotEnum.N.getCode());
        pathWrapper.orderByAsc(ErpProductCategory::getSortOrder);

        List<ErpProductCategory> pathCategories = this.list(pathWrapper);

        // 转换为响应对象
        List<ErpProductCategoryResponse> pathCategoryResponses = pathCategories.stream().map(item -> {
            ErpProductCategoryResponse response = BeanUtil.copyProperties(item, ErpProductCategoryResponse.class);
            this.fillCategoryExtInfo(response);
            response.setKey(String.valueOf(item.getCategoryId()));
            response.setValue(String.valueOf(item.getCategoryId()));
            response.setTitle(item.getCategoryName());
            response.setIcon(getCategoryIcon(item.getCategoryLevel()));
            return response;
        }).collect(Collectors.toList());

        // 构建树形结构
        return buildTree(pathCategoryResponses, null);
    }

    /**
     * 添加路径上的父节点ID
     */
    private void addParentIds(Set<Long> pathCategoryIds, String categoryPath) {
        if (ObjectUtil.isNotEmpty(categoryPath)) {
            String[] pathIds = categoryPath.split("/");
            for (String pathId : pathIds) {
                if (ObjectUtil.isNotEmpty(pathId)) {
                    pathCategoryIds.add(Long.valueOf(pathId));
                }
            }
        }
    }
}
