package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * POS支付请求参数
 *
 * <AUTHOR>
 * @since 2025/08/01 17:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PosPaymentRequest extends BaseRequest {

    /**
     * 支付ID
     */
    @ChineseDescription("支付ID")
    private Long paymentId;

    /**
     * 支付单号
     */
    @ChineseDescription("支付单号")
    private String paymentNo;

    /**
     * 订单ID
     */
    @ChineseDescription("订单ID")
    @NotNull(message = "订单ID不能为空", groups = {processPayment.class})
    private Long orderId;

    /**
     * 支付方式（CASH-现金，WECHAT-微信，ALIPAY-支付宝，MEMBER-会员卡，CARD-银行卡）
     */
    @ChineseDescription("支付方式")
    @NotNull(message = "支付方式不能为空", groups = {processPayment.class})
    private String paymentMethod;

    /**
     * 支付金额
     */
    @ChineseDescription("支付金额")
    @NotNull(message = "支付金额不能为空", groups = {processPayment.class})
    private BigDecimal paymentAmount;

    /**
     * 实收金额（现金支付时使用）
     */
    @ChineseDescription("实收金额")
    private BigDecimal receivedAmount;

    /**
     * 会员ID（会员卡支付时使用）
     */
    @ChineseDescription("会员ID")
    private Long memberId;

    /**
     * 银行卡号（银行卡支付时使用，脱敏显示）
     */
    @ChineseDescription("银行卡号")
    private String cardNo;

    /**
     * 第三方交易号
     */
    @ChineseDescription("第三方交易号")
    private String transactionId;

    /**
     * 支付状态
     */
    @ChineseDescription("支付状态")
    private String paymentStatus;

    /**
     * 失败原因
     */
    @ChineseDescription("失败原因")
    private String failureReason;

    /**
     * 取消原因
     */
    @ChineseDescription("取消原因")
    private String cancelReason;

    /**
     * 退款金额
     */
    @ChineseDescription("退款金额")
    private BigDecimal refundAmount;

    /**
     * 退款原因
     */
    @ChineseDescription("退款原因")
    private String refundReason;

    /**
     * 混合支付详情列表
     */
    @ChineseDescription("混合支付详情列表")
    private List<PaymentDetailRequest> paymentDetails;

    /**
     * 支付详情请求参数
     */
    @Data
    public static class PaymentDetailRequest {
        /**
         * 支付方式
         */
        @ChineseDescription("支付方式")
        @NotNull(message = "支付方式不能为空")
        private String paymentMethod;

        /**
         * 支付金额
         */
        @ChineseDescription("支付金额")
        @NotNull(message = "支付金额不能为空")
        private BigDecimal paymentAmount;

        /**
         * 实收金额
         */
        @ChineseDescription("实收金额")
        private BigDecimal receivedAmount;

        /**
         * 会员ID
         */
        @ChineseDescription("会员ID")
        private Long memberId;

        /**
         * 银行卡号
         */
        @ChineseDescription("银行卡号")
        private String cardNo;
    }

    /**
     * 参数校验分组：处理支付
     */
    public @interface processPayment {
    }

    /**
     * 参数校验分组：确认支付
     */
    public @interface confirmPayment {
    }

    /**
     * 参数校验分组：支付失败
     */
    public @interface paymentFailure {
    }

    /**
     * 参数校验分组：取消支付
     */
    public @interface cancelPayment {
    }

    /**
     * 参数校验分组：退款处理
     */
    public @interface processRefund {
    }

}