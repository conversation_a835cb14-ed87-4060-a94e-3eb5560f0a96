package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 客户-区域关联响应参数
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpCustomerRegionResponse extends BaseResponse {

    /**
     * 主键ID
     */
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 客户ID
     */
    @ChineseDescription("客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @ChineseDescription("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ChineseDescription("客户名称")
    private String customerName;

    /**
     * 区域ID
     */
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 区域编码
     */
    @ChineseDescription("区域编码")
    private String regionCode;

    /**
     * 区域名称
     */
    @ChineseDescription("区域名称")
    private String regionName;

    /**
     * 区域层级
     */
    @ChineseDescription("区域层级")
    private Integer regionLevel;

    /**
     * 区域层级名称
     */
    @ChineseDescription("区域层级名称")
    private String regionLevelName;

    /**
     * 创建时间
     */
    @ChineseDescription("创建时间")
    private Date createTime;

    /**
     * 创建人ID
     */
    @ChineseDescription("创建人ID")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @ChineseDescription("创建人姓名")
    private String createUserName;

    /**
     * 客户关联的区域ID列表
     */
    @ChineseDescription("客户关联的区域ID列表")
    private List<Long> regionIds;

    /**
     * 客户关联的区域信息列表
     */
    @ChineseDescription("客户关联的区域信息列表")
    private List<ErpRegionResponse> regionList;
}