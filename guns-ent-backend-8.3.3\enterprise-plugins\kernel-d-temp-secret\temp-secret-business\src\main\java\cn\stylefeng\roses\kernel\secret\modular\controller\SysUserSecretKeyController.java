package cn.stylefeng.roses.kernel.secret.modular.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import cn.stylefeng.roses.kernel.secret.modular.entity.SysUserSecretKey;
import cn.stylefeng.roses.kernel.secret.modular.pojo.SysUserSecretKeyRequest;
import cn.stylefeng.roses.kernel.secret.modular.service.SysUserSecretKeyService;
import cn.stylefeng.roses.kernel.secret.modular.wrapper.SecretKeyWrapper;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.wrapper.api.annotation.Wrapper;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户临时秘钥控制器
 *
 * <AUTHOR>
 * @date 2022/03/22 11:33
 */
@RestController
@ApiResource(name = "用户临时秘钥")
public class SysUserSecretKeyController {

    @Resource
    private SysUserSecretKeyService sysUserSecretKeyService;

    /**
     * 添加临时秘钥
     *
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    @PostResource(name = "添加临时秘钥", path = "/sysUserSecretKey/add")
    public ResponseData<SysUserSecretKey> add(
            @RequestBody @Validated(SysUserSecretKeyRequest.add.class) SysUserSecretKeyRequest sysUserSecretKeyRequest) {
        sysUserSecretKeyService.add(sysUserSecretKeyRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除临时秘钥
     *
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    @PostResource(name = "删除临时秘钥", path = "/sysUserSecretKey/delete")
    public ResponseData<?> delete(
            @RequestBody @Validated(SysUserSecretKeyRequest.delete.class) SysUserSecretKeyRequest sysUserSecretKeyRequest) {
        sysUserSecretKeyService.del(sysUserSecretKeyRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 获取列表（带分页）
     *
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    @GetResource(name = "分页查询", path = "/sysUserSecretKey/page")
    @Wrapper(SecretKeyWrapper.class)
    public ResponseData<PageResult<SysUserSecretKey>> page(SysUserSecretKeyRequest sysUserSecretKeyRequest) {
        return new SuccessResponseData<>(sysUserSecretKeyService.findPage(sysUserSecretKeyRequest));
    }

    /**
     * 根据用户账号，或者昵称信息，查询用户列表
     * <p>
     * 用在新增秘钥时候绑定用户
     *
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    @GetResource(name = "根据用户账号，或者昵称信息，查询用户列表", path = "/sysUserSecretKey/getUserListByCondition")
    public ResponseData<List<SysUser>> getUserListByCondition(
            @Validated(SysUserSecretKeyRequest.queryUser.class) SysUserSecretKeyRequest sysUserSecretKeyRequest) {
        return new SuccessResponseData<>(sysUserSecretKeyService.getUserList(sysUserSecretKeyRequest.getCondition()));
    }

}
