/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.auth.api;

import cn.stylefeng.roses.kernel.auth.api.exception.AuthException;
import cn.stylefeng.roses.kernel.auth.api.pojo.auth.LoginRequest;
import cn.stylefeng.roses.kernel.auth.api.pojo.auth.LoginResponse;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.auth.api.pojo.payload.DefaultJwtPayload;
import cn.stylefeng.roses.kernel.auth.api.pojo.sso.LoginBySsoTokenRequest;
import cn.stylefeng.roses.kernel.auth.api.pojo.sso.LogoutBySsoTokenRequest;

/**
 * 认证服务的接口，包括基本的登录退出操作和校验token等操作
 *
 * <AUTHOR>
 * @since 2020/10/26 14:41
 */
public interface AuthServiceApi {

    /**
     * 常规登录操作
     *
     * @param loginRequest 登录的请求
     * @return token 一般为jwt token
     * <AUTHOR>
     * @since 2020/10/26 14:41
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 登录（直接用账号登录），一般用在第三方登录
     *
     * @param username 账号
     * <AUTHOR>
     * @since 2020/10/26 14:40
     */
    LoginResponse loginWithUserName(String username);

    /**
     * 登录（通过账号和sso后的token），一般用在单点登录
     *
     * @param username 账号
     * @param caToken  sso登录成功后的会话
     * <AUTHOR>
     * @since 2021/5/25 22:44
     */
    LoginResponse loginWithUserNameAndCaToken(String username, String caToken);

    /**
     * 通过token进行登录，一般用在单点登录服务
     *
     * @param loginWithTokenRequest 请求
     * <AUTHOR>
     * @since 2021/5/25 22:44
     */
    LoginResponse LoginByCaToken(LoginBySsoTokenRequest loginWithTokenRequest);

    /**
     * 当前登录人退出登录
     *
     * <AUTHOR>
     * @since 2020/10/19 14:16
     */
    void logout();

    /**
     * 移除某个token，也就是退出某个用户
     *
     * @param token 某个用户的登录token
     * <AUTHOR>
     * @since 2020/10/19 14:16
     */
    void logoutWithToken(String token);

    /**
     * 校验jwt token的正确性，调用jwt工具类相关方法校验
     * <p>
     * 结果有三种，第一是jwt过期了，第二是用户随便写的错误token，第三种是token正确，token正确不会抛出异常
     *
     * @param token 某个用户的登录token
     * @return token解析出的用户基本信息
     * @throws AuthException 认证异常，如果token错误或过期，会有相关的异常抛出
     * <AUTHOR>
     * @since 2020/10/19 14:16
     */
    DefaultJwtPayload validateToken(String token) throws AuthException;

    /**
     * 校验用户是否认证通过，认证是校验token的过程，校验失败会抛出异常
     *
     * @param token      用户登陆的token
     * @param requestUrl 被校验的url
     * <AUTHOR>
     * @since 2020/10/22 16:03
     */
    void checkAuth(String token, String requestUrl);

    /**
     * 取消冻结帐号
     *
     * <AUTHOR>
     * @since 2022/1/22 16:37
     */
    void cancelFreeze(LoginRequest loginRequest);

    /**
     * 为指定token创建新的登录信息
     *
     * @param token             用户旧的token
     * @param defaultJwtPayload jwt的payload信息
     * @return 新的当前登录用户
     * <AUTHOR>
     * @since 2022/10/17 0:04
     */
    LoginUser createNewLoginInfo(String token, DefaultJwtPayload defaultJwtPayload);

    /**
     * 通过单点的CaToken将本系统的用户退出
     * <p>
     * 一般用在单点认证中心退出时，认证中心调用本系统退出接口
     *
     * <AUTHOR>
     * @since 2023/11/7 15:57
     */
    void logoutByCaToken(LogoutBySsoTokenRequest logoutBySsoTokenRequest);

}
