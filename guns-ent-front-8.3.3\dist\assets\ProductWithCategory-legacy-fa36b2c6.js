System.register(["./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./SupplierSelector-legacy-fba3813b.js","./index-legacy-efb51034.js","./UniversalTree-legacy-6dcdf778.js","./ProductApi-legacy-33feae42.js","./ProductEdit-legacy-b01b9be9.js","./ProductDetail-legacy-048ea5d4.js","./productCategoryApi-legacy-247b2407.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-94a6fc23.js","./CategorySelector-legacy-c3396c33.js","./index-legacy-9a185ac3.js","./index-legacy-e24582b9.js"],(function(e,a){"use strict";var t,l,o,i,r,n,d,c,s,u,p,f,g,h,b,y,v,m,w,C,x,_,k,S,I,T,P,E,N,O,j,D,U,A,L,R,M,z,V,W,B,G,H,K,F,Q,$,q,J;return{setters:[e=>{t=e._},e=>{l=e._},e=>{o=e._,i=e.P,r=e.K,n=e.r,d=e.L,c=e.N,s=e.s,u=e.k,p=e.a,f=e.c,g=e.d,h=e.w,b=e.b,y=e.g,v=e.t,m=e.h,w=e.O,C=e.Q,x=e.F,_=e.e,k=e.f,S=e.M,I=e.E,T=e.m,P=e.U,E=e.n,N=e.B,O=e.I,j=e.p,D=e.q,U=e.D,A=e.l,L=e.V,R=e.u,M=e.v,z=e.G,V=e.W,W=e.J,B=e.H,G=e.a_},e=>{H=e._},null,e=>{K=e.T,F=e.U},e=>{Q=e.P},e=>{$=e.default},e=>{q=e.default},e=>{J=e.P},null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".guns-layout .table-toolbar[data-v-fb1fe0bb]{margin-bottom:16px;padding:16px 0;border-bottom:1px solid #f0f0f0}.guns-layout .table-toolbar .toolbar-left[data-v-fb1fe0bb]{display:flex;align-items:center;gap:16px}.guns-layout .table-toolbar .toolbar-left .search-input .ant-input[data-v-fb1fe0bb]{border-radius:6px}.guns-layout .table-toolbar .toolbar-left a[data-v-fb1fe0bb]{color:#1890ff;cursor:pointer}.guns-layout .table-toolbar .toolbar-left a[data-v-fb1fe0bb]:hover{color:#40a9ff}.guns-layout .advanced-search[data-v-fb1fe0bb]{padding:16px;background-color:#fafafa;border-radius:6px;margin-bottom:16px}.guns-layout .advanced-search .ant-form-item[data-v-fb1fe0bb]{margin-bottom:0}.guns-layout[data-v-fb1fe0bb]{height:100%;width:100%;margin:0;padding:0}.guns-layout-content[data-v-fb1fe0bb],.guns-layout-content-application[data-v-fb1fe0bb]{width:100%;height:100%;display:flex;flex-direction:column;margin:0;padding:0;box-sizing:border-box}.sidebar-content[data-v-fb1fe0bb]{height:100%;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08)}.content-main[data-v-fb1fe0bb]{width:100%;height:100%;display:flex;flex-direction:column;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08);margin:0;padding:0;box-sizing:border-box}.content-main-header[data-v-fb1fe0bb]{padding:16px 24px;border-bottom:1px solid #f0f0f0;background:#fafafa;border-radius:6px 6px 0 0}.header-content[data-v-fb1fe0bb]{display:flex;justify-content:space-between;align-items:center}.current-category-info[data-v-fb1fe0bb]{font-size:14px;color:#666}.content-main-body[data-v-fb1fe0bb]{flex:1;padding:16px 24px;overflow:auto;width:100%;box-sizing:border-box}.table-content[data-v-fb1fe0bb]{width:100%;height:100%;display:flex;flex-direction:column;box-sizing:border-box}[data-v-fb1fe0bb] .guns-split-panel-body{width:100%!important;flex:1!important;overflow:hidden!important}[data-v-fb1fe0bb] .guns-split-panel{width:100%!important}\n",document.head.appendChild(a);const X={class:"guns-layout"},Y={class:"guns-layout-sidebar width-100 p-t-12"},Z={class:"sidebar-content"},ee={class:"guns-layout-content"},ae={class:"guns-layout"},te={class:"guns-layout-content-application"},le={class:"content-main"},oe={class:"content-main-header"},ie={class:"header-content"},re={class:"header-content-left"},ne={key:0,class:"current-category-info"},de={class:"header-content-right"},ce={class:"content-main-body"},se={class:"table-content"},ue={key:0,class:"super-search",style:{"margin-top":"8px"}},pe={key:0},fe={key:0};e("default",o({name:"ProductWithCategory",components:{PlusOutlined:i,SmallDashOutlined:r,ProductEdit:$,ProductDetail:q,UniversalTree:F},setup(){const e=n(!1),a=n(!1),t=n(!1),l=n({}),o=n(null),i=n(""),r=n(null),u=n(null),p=(new K).setDataSource(J.findTree,J.findTreeWithLazy,"searchText","parentId").setFieldMapping("categoryId","categoryName","children","hasChildren","level").setDisplayConfig({title:"产品分类",showHeader:!0,showSearch:!0,searchPlaceholder:"搜索分类名称",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0}).setInteractionConfig({selectable:!0,expandable:!0,lazyLoad:!1,defaultExpandLevel:1,allowMultiSelect:!1}).enableReadOnlyMode().build(),f=d((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),h=d((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),b=d((()=>c()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),y=s({searchText:"",productCode:void 0,barcode:void 0,brand:void 0,status:void 0,unit:void 0,supplierId:void 0,pricingType:void 0}),v=Q.getProductStatusOptions(),m=Q.getCommonUnitOptions(),w=Q.getPricingTypeOptions(),C=()=>{u.value&&u.value.reload()},x=()=>{var e;const a=null===(e=u.value)||void 0===e?void 0:e.getSelectedRows();a&&0!==a.length?S.confirm({title:"提示",content:`确定要删除选中的 ${a.length} 条数据吗？`,icon:g(I),maskClosable:!0,onOk:()=>{const e=a.map((e=>e.productId));return Q.batchDelete({productIdList:e}).then((()=>{T.success("删除成功"),C()})).catch((e=>{T.error(e.message||"删除失败")}))}}):T.warning("请选择要删除的数据")};return{tableRef:u,categoryTreeRef:o,treeConfig:p,superSearch:e,where:y,columns:[{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"productCode",title:"商品编码",width:140,ellipsis:!0,isShow:!0},{dataIndex:"productName",title:"商品名称",width:200,ellipsis:!0,isShow:!0},{dataIndex:"categoryName",title:"产品分类",width:200,ellipsis:!0,isShow:!0},{dataIndex:"productShortName",title:"商品简称",width:150,ellipsis:!0,isShow:!0},{dataIndex:"barcode",title:"条形码",width:120,ellipsis:!0,isShow:!0},{dataIndex:"brand",title:"品牌",width:100,ellipsis:!0,isShow:!0},{dataIndex:"specification",title:"规格",width:120,ellipsis:!0,isShow:!0},{dataIndex:"unit",title:"基本单位",width:80,align:"center",isShow:!0},{dataIndex:"supplierName",title:"供应商",width:150,ellipsis:!0,isShow:!0},{dataIndex:"pricingType",title:"计价类型",width:100,align:"center",isShow:!0},{dataIndex:"retailPrice",title:"价格",width:120,align:"right",isShow:!0},{dataIndex:"status",title:"状态",width:100,align:"center",isShow:!0},{dataIndex:"createTime",title:"创建时间",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"操作",width:150,isShow:!0}],currentRecord:l,currentCategoryName:i,showEdit:a,showDetailModal:t,statusOptions:v,unitOptions:m,pricingTypeOptions:w,labelCol:f,wrapperCol:h,spanCol:b,reload:C,clear:()=>{Object.keys(y).forEach((e=>{y[e]="searchText"===e?"":void 0})),r.value=null,i.value="",o.value&&o.value.clearSelection(),C()},changeSuperSearch:()=>{e.value=!e.value},handleCategorySelect:e=>{if(console.log("分类树选择:",e),e.keys&&e.keys.length>0){const a=e.keys[0],t=e.nodes[0];r.value=a,i.value=(null==t?void 0:t.categoryName)||"",y.categoryId=a}else r.value=null,i.value="",y.categoryId=void 0;C()},handleCategoryTagClick:e=>{r.value=e,o.value&&o.value.setSelectedKeys([String(e)]),y.categoryId=e,C()},openAddModal:()=>{l.value={},a.value=!0},openEditModal:e=>{l.value={...e},a.value=!0},showDetail:e=>{l.value={...e},t.value=!0},remove:e=>{S.confirm({title:"提示",content:"确定要删除该商品吗？",icon:g(I),maskClosable:!0,onOk:()=>Q.delete({productId:e.productId}).then((()=>{T.success("删除成功"),C()})).catch((e=>{T.error(e.message||"删除失败")}))})},moreClick:({key:e})=>{"1"===e&&x()},batchDelete:x,updateStatus:(e,a)=>{const t=Q.getProductStatusName(a);S.confirm({title:"提示",content:`确定要将商品状态更新为"${t}"吗？`,icon:g(I),maskClosable:!0,onOk:()=>Q.updateStatus({productId:e.productId,status:a}).then((()=>{T.success("状态更新成功"),C()})).catch((e=>{T.error(e.message||"状态更新失败")}))})},getProductStatusName:e=>Q.getProductStatusName(e),getStatusTagColor:e=>Q.getStatusTagColor(e),getPricingTypeName:e=>Q.getPricingTypeName(e),getPricingTypeTagColor:e=>Q.getPricingTypeTagColor(e),formatWeight:e=>Q.formatWeight(e),formatVolume:e=>Q.formatVolume(e),formatShelfLife:e=>Q.formatShelfLife(e),formatPrice:e=>{if(!e.pricingType)return"-";switch(e.pricingType){case"NORMAL":return e.retailPrice?Q.formatPrice(e.retailPrice,"NORMAL"):"-";case"WEIGHT":return e.unitPrice?Q.formatPrice(e.unitPrice,"WEIGHT"):"-";case"PIECE":return e.piecePrice?Q.formatPrice(e.piecePrice,"PIECE"):"-";case"VARIABLE":return e.referencePrice?Q.formatPrice(e.referencePrice,"VARIABLE"):"-";default:return"-"}}}}},[["render",function(e,a,o,i,r,n){const d=F,c=P,s=E,S=u("plus-outlined"),I=N,T=O,K=j,Q=D,$=u("small-dash-outlined"),q=U,J=A,ge=L,he=R,be=M,ye=z,ve=V,me=W,we=H,Ce=B,xe=G,_e=l,ke=t,Se=u("product-edit"),Ie=u("product-detail");return p(),f("div",X,[g(ke,{width:"292px",cacheKey:"ERP_PRODUCT_MANAGEMENT"},{content:h((()=>[b("div",ee,[b("div",ae,[b("div",te,[b("div",le,[b("div",oe,[b("div",ie,[b("div",re,[g(s,{size:16},{default:h((()=>[i.currentCategoryName?(p(),f("span",ne,[a[11]||(a[11]=y(" 当前分类：")),g(c,{color:"blue"},{default:h((()=>[y(v(i.currentCategoryName),1)])),_:1})])):m("",!0)])),_:1})]),b("div",de,[g(s,{size:16},{default:h((()=>[g(I,{type:"primary",class:"border-radius",onClick:i.openAddModal},{default:h((()=>[g(S),a[12]||(a[12]=y(" 新增商品 "))])),_:1,__:[12]},8,["onClick"]),g(q,null,{overlay:h((()=>[g(Q,{onClick:i.moreClick},{default:h((()=>[g(K,{key:"1"},{default:h((()=>[g(T,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[13]||(a[13]=b("span",null,"批量删除",-1))])),_:1,__:[13]})])),_:1},8,["onClick"])])),default:h((()=>[g(I,{class:"border-radius"},{default:h((()=>[a[14]||(a[14]=y(" 更多 ")),g($)])),_:1,__:[14]})])),_:1})])),_:1})])])]),b("div",ce,[b("div",se,[g(_e,{columns:i.columns,where:i.where,fieldBusinessCode:"ERP_PRODUCT_TABLE",showTableTool:"",showToolTotal:!1,rowId:"productId",ref:"tableRef",url:"/erp/product/page"},{toolLeft:h((()=>[g(J,{value:i.where.searchText,"onUpdate:value":a[0]||(a[0]=e=>i.where.searchText=e),bordered:!1,allowClear:"",placeholder:"商品名称、编码（回车搜索）",onPressEnter:i.reload,style:{width:"240px"},class:"search-input"},{prefix:h((()=>[g(T,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),g(ge,{type:"vertical",class:"divider"}),b("a",{onClick:a[1]||(a[1]=(...e)=>i.changeSuperSearch&&i.changeSuperSearch(...e))},v(i.superSearch?"收起":"高级筛选"),1)])),toolBottom:h((()=>[i.superSearch?(p(),f("div",ue,[g(Ce,{model:i.where,labelCol:i.labelCol,"wrapper-col":i.wrapperCol},{default:h((()=>[g(ye,{gutter:16},{default:h((()=>[g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"商品编码:"},{default:h((()=>[g(J,{value:i.where.productCode,"onUpdate:value":a[2]||(a[2]=e=>i.where.productCode=e),placeholder:"请输入商品编码",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"条形码:"},{default:h((()=>[g(J,{value:i.where.barcode,"onUpdate:value":a[3]||(a[3]=e=>i.where.barcode=e),placeholder:"请输入条形码",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"品牌:"},{default:h((()=>[g(J,{value:i.where.brand,"onUpdate:value":a[4]||(a[4]=e=>i.where.brand=e),placeholder:"请输入品牌",allowClear:""},null,8,["value"])])),_:1})])),_:1},16)])),_:1}),g(ye,{gutter:16},{default:h((()=>[g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"状态:"},{default:h((()=>[g(me,{value:i.where.status,"onUpdate:value":a[5]||(a[5]=e=>i.where.status=e),placeholder:"请选择状态",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),f(x,null,_(i.statusOptions,(e=>(p(),k(ve,{key:e.value,value:e.value},{default:h((()=>[y(v(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"基本单位:"},{default:h((()=>[g(me,{value:i.where.unit,"onUpdate:value":a[6]||(a[6]=e=>i.where.unit=e),placeholder:"请选择基本单位",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),f(x,null,_(i.unitOptions,(e=>(p(),k(ve,{key:e.value,value:e.value},{default:h((()=>[y(v(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16)])),_:1}),g(ye,{gutter:16},{default:h((()=>[g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"供应商:"},{default:h((()=>[g(we,{value:i.where.supplierId,"onUpdate:value":a[7]||(a[7]=e=>i.where.supplierId=e),placeholder:"请选择供应商",allowClear:!0},null,8,["value"])])),_:1})])),_:1},16),g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:"计价类型:"},{default:h((()=>[g(me,{value:i.where.pricingType,"onUpdate:value":a[8]||(a[8]=e=>i.where.pricingType=e),placeholder:"请选择计价类型",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),f(x,null,_(i.pricingTypeOptions,(e=>(p(),k(ve,{key:e.value,value:e.value},{default:h((()=>[y(v(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),g(be,w(C(i.spanCol)),{default:h((()=>[g(he,{label:" ",class:"not-label"},{default:h((()=>[g(s,{size:16},{default:h((()=>[g(I,{class:"border-radius",onClick:i.reload,type:"primary"},{default:h((()=>a[15]||(a[15]=[y("查询")]))),_:1,__:[15]},8,["onClick"]),g(I,{class:"border-radius",onClick:i.clear},{default:h((()=>a[16]||(a[16]=[y("重置")]))),_:1,__:[16]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):m("",!0)])),bodyCell:h((({column:e,record:t})=>["status"===e.dataIndex?(p(),k(c,{key:0,color:i.getStatusTagColor(t.status)},{default:h((()=>[y(v(i.getProductStatusName(t.status)),1)])),_:2},1032,["color"])):"categoryName"===e.dataIndex?(p(),f(x,{key:1},[t.categoryName?(p(),k(c,{key:0,color:"blue",onClick:e=>i.handleCategoryTagClick(t.categoryId)},{default:h((()=>[y(v(t.categoryName),1)])),_:2},1032,["onClick"])):(p(),k(c,{key:1,color:"default"},{default:h((()=>a[17]||(a[17]=[y("未分类")]))),_:1,__:[17]}))],64)):"weight"===e.dataIndex?(p(),f(x,{key:2},[y(v(i.formatWeight(t.weight)),1)],64)):"volume"===e.dataIndex?(p(),f(x,{key:3},[y(v(i.formatVolume(t.volume)),1)],64)):"shelfLife"===e.dataIndex?(p(),f(x,{key:4},[y(v(i.formatShelfLife(t.shelfLife)),1)],64)):"supplierName"===e.dataIndex?(p(),f(x,{key:5},[t.supplierName?(p(),f("span",pe,v(t.supplierName),1)):(p(),k(c,{key:1,color:"default"},{default:h((()=>a[18]||(a[18]=[y("未设置")]))),_:1,__:[18]}))],64)):"pricingType"===e.dataIndex?(p(),f(x,{key:6},[t.pricingType?(p(),k(c,{key:0,color:i.getPricingTypeTagColor(t.pricingType)},{default:h((()=>[y(v(i.getPricingTypeName(t.pricingType)),1)])),_:2},1032,["color"])):(p(),k(c,{key:1,color:"default"},{default:h((()=>a[19]||(a[19]=[y("未设置")]))),_:1,__:[19]}))],64)):"retailPrice"===e.dataIndex?(p(),f(x,{key:7},[t.retailPrice||t.unitPrice||t.piecePrice||t.referencePrice?(p(),f("span",fe,v(i.formatPrice(t)),1)):(p(),k(c,{key:1,color:"default"},{default:h((()=>a[20]||(a[20]=[y("未设置")]))),_:1,__:[20]}))],64)):"action"===e.key?(p(),k(s,{key:8,size:16},{default:h((()=>[g(T,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>i.openEditModal(t)},null,8,["onClick"]),g(T,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>i.remove(t)},null,8,["onClick"]),g(q,null,{overlay:h((()=>[g(Q,null,{default:h((()=>["ACTIVE"===t.status?(p(),k(K,{key:0,onClick:e=>i.updateStatus(t,"INACTIVE")},{default:h((()=>a[21]||(a[21]=[y(" 停用 ")]))),_:2,__:[21]},1032,["onClick"])):m("",!0),"INACTIVE"===t.status?(p(),k(K,{key:1,onClick:e=>i.updateStatus(t,"ACTIVE")},{default:h((()=>a[22]||(a[22]=[y(" 启用 ")]))),_:2,__:[22]},1032,["onClick"])):m("",!0),"DISCONTINUED"!==t.status?(p(),k(K,{key:2,onClick:e=>i.updateStatus(t,"DISCONTINUED")},{default:h((()=>a[23]||(a[23]=[y(" 停产 ")]))),_:2,__:[23]},1032,["onClick"])):m("",!0),g(xe),g(K,{onClick:e=>i.showDetail(t)},{default:h((()=>a[24]||(a[24]=[y(" 详情 ")]))),_:2,__:[24]},1032,["onClick"])])),_:2},1024)])),default:h((()=>[g(T,{iconClass:"icon-opt-gengduo","font-size":"24px",title:"更多",color:"#60666b"})])),_:2},1024)])),_:2},1024)):m("",!0)])),_:1},8,["columns","where"])])])])])])])])),default:h((()=>[b("div",Y,[b("div",Z,[g(d,{ref:"categoryTreeRef","data-source":i.treeConfig.dataSource,"field-mapping":i.treeConfig.fieldMapping,"display-config":i.treeConfig.displayConfig,"interaction-config":i.treeConfig.interactionConfig,"action-config":i.treeConfig.actionConfig,onSelect:i.handleCategorySelect},null,8,["data-source","field-mapping","display-config","interaction-config","action-config","onSelect"])])])])),_:1}),g(Se,{visible:i.showEdit,"onUpdate:visible":a[9]||(a[9]=e=>i.showEdit=e),data:i.currentRecord,onDone:i.reload},null,8,["visible","data","onDone"]),g(Ie,{visible:i.showDetailModal,"onUpdate:visible":a[10]||(a[10]=e=>i.showDetailModal=e),data:i.currentRecord},null,8,["visible","data"])])}],["__scopeId","data-v-fb1fe0bb"]]))}}}));
