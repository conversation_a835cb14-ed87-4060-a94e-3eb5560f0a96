package cn.stylefeng.roses.kernel.license.api.exception.enums;

import cn.stylefeng.roses.kernel.license.api.constants.LicenseConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * license加密异常
 *
 * <AUTHOR>
 * @date 2021/7/8 11:38
 */
@Getter
public enum LicenseExceptionEnum implements AbstractExceptionEnum {

    /**
     * 解密license错误
     */
    DECRYPT_LICENSE_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + LicenseConstants.LICENSE_EXCEPTION_STEP_CODE + "01", "解密license错误，请检查license是否正常！");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    LicenseExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }
}
