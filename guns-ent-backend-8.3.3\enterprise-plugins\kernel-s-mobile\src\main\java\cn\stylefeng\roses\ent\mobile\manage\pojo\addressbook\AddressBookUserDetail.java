package cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook;

import cn.stylefeng.roses.kernel.file.api.format.FileUrlFormatProcess;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.annotation.SimpleFieldFormat;
import lombok.Data;

/**
 * 通讯录元素
 *
 * <AUTHOR>
 * @since 2024/3/21 22:22
 */
@Data
public class AddressBookUserDetail {

    /**
     * 头像id
     */
    @ChineseDescription("头像id")
    @SimpleFieldFormat(processClass = FileUrlFormatProcess.class)
    private Long avatar;

    /**
     * 用户id
     */
    @ChineseDescription("用户id")
    private Long userId;

    /**
     * 用户姓名
     */
    @ChineseDescription("用户姓名")
    private String realName;

    /**
     * 性别：M-男，F-女
     * <p>
     * 移动端-个人详情界面
     */
    @ChineseDescription("性别：M-男，F-女")
    private String sex;

    /**
     * 邮箱
     * <p>
     * 移动端-个人详情界面
     */
    @ChineseDescription("邮箱")
    private String email;

    /**
     * 用户手机号
     * <p>
     * 移动端-个人详情界面
     */
    @ChineseDescription("用户手机号")
    private String phone;

    /**
     * 公司名称
     */
    @ChineseDescription("公司名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ChineseDescription("部门名称")
    private String deptName;

    /**
     * 职位名称
     */
    @ChineseDescription("职位名称")
    private String positionName;

    /**
     * 用户工号
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("用户工号")
    private String employeeNumber;

}
