<!--
  商品显示区域主组件
  
  重构后的商品显示区域，拆分为多个子组件，职责更加清晰
  使用Composables管理业务逻辑，组件专注于UI渲染
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="product-display-area">
    <!-- 商品分类导航 -->
    <div class="category-section">
      <div class="category-tabs">
        <a-tabs
          v-model:activeKey="selectedCategoryKey"
          type="card"
          size="small"
          @tab-click="handleCategoryClick"
          class="pos-category-tabs"
        >
          <a-tab-pane key="all" tab="全部商品" />
          <a-tab-pane
            v-for="category in categories"
            :key="category.categoryId"
            :tab="category.categoryName"
          />
        </a-tabs>
      </div>
    </div>

    <!-- 商品搜索和过滤区域 -->
    <div class="product-filter-section">
      <div class="filter-row">
        <!-- 搜索框 -->
        <div class="search-wrapper">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索商品名称、编码、条形码"
            class="search-input"
            @pressEnter="handleSearch"
            @change="handleSearchChange"
            allowClear
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>

        <!-- 价格过滤 -->
        <div class="filter-item">
          <a-select
            v-model:value="priceFilter"
            placeholder="选择价格范围"
            class="filter-select"
            @change="handlePriceFilterChange"
            allowClear
          >
            <a-select-option value="0-50">0-50元</a-select-option>
            <a-select-option value="50-100">50-100元</a-select-option>
            <a-select-option value="100-200">100-200元</a-select-option>
            <a-select-option value="200+">200元以上</a-select-option>
          </a-select>
        </div>
      </div>
    </div>

    <!-- 商品网格 -->
    <div class="product-grid-section">
      <div class="grid-container">
        <div
          v-for="product in displayProducts"
          :key="product.productId"
          class="product-item"
          @click="handleProductClick(product)"
        >
          <div class="product-card">
            <div class="product-info">
              <div class="product-name" :title="product.productName">
                {{ product.productName }}
              </div>
              
              <div class="product-code" v-if="product.productCode">
                编码: {{ product.productCode }}
              </div>
              
              <div class="price-info">
                <div class="current-price">
                  ¥{{ formatPrice(product.retailPrice) }}
                </div>
              </div>
              
              <div class="stock-info">
                库存: {{ product.stockQuantity || 0 }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && displayProducts.length === 0">
        <a-empty description="暂无商品" />
      </div>
      
      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <a-spin size="large" />
        <div class="loading-text">正在加载商品...</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'

// 定义组件名称
defineOptions({
  name: 'ProductDisplayArea'
})

// 定义Props
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  products: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'productAdd',
  'productSelect',
  'categoryChange',
  'search',
  'filterChange'
])

// 响应式状态
const selectedCategoryKey = ref('all')
const searchKeyword = ref('')
const priceFilter = ref('')

// 计算属性 - 直接使用传入的products数据
const displayProducts = computed(() => props.products || [])

// 方法
const formatPrice = (price) => {
  return (price || 0).toFixed(2)
}

const handleCategoryClick = (categoryKey) => {
  selectedCategoryKey.value = categoryKey
  emit('categoryChange', categoryKey)
}

const handleSearch = () => {
  emit('search', searchKeyword.value)
}

const handleSearchChange = (e) => {
  searchKeyword.value = e.target.value
  emit('search', searchKeyword.value)
}

const handlePriceFilterChange = (value) => {
  emit('filterChange', value)
}

const handleProductClick = (product) => {
  // 直接添加到购物车
  handleAddToCart(product)
}

const handleAddToCart = (product) => {
  if (product.stockQuantity <= 0) {
    message.warning('商品已缺货')
    return
  }

  emit('productAdd', product)
  // 移除重复的成功提示，由购物车组合式函数统一处理
}


</script>

<style scoped>
.product-display-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.category-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.product-filter-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-wrapper {
  flex: 1;
}

.search-input {
  max-width: 300px;
}

.filter-select {
  width: 150px;
}

.product-grid-section {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.product-card {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  background: #fff;
}

.product-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-code {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 6px;
}

.price-info {
  margin-bottom: 6px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
}

.stock-info {
  font-size: 12px;
  color: #8c8c8c;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .search-input {
    max-width: none;
  }
}
</style>