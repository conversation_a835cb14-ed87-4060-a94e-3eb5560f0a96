/*
 * Copyright 2013-2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.stylefeng.roses.seata.demo.business;

import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableFeignClients
public class BusinessApplication {

    public static void main(String[] args) {
        SpringApplication.run(BusinessApplication.class, args);
    }

    @FeignClient("storage-service")
    public interface StorageService {

        @GetMapping(path = "/storage/{commodityCode}/{count}")
        ResponseData<?> storage(@PathVariable("commodityCode") String commodityCode, @PathVariable("count") int count);

        @GetMapping(path = "/tcc/storage/{commodityCode}/{count}")
        ResponseData<?> tccStorage(@PathVariable("commodityCode") String commodityCode, @PathVariable("count") int count);

    }

    @FeignClient("order-service")
    public interface OrderService {

        @PostMapping(path = "/order")
        ResponseData<?> order(@RequestParam("userId") String userId,
                           @RequestParam("commodityCode") String commodityCode,
                           @RequestParam("orderCount") int orderCount);

    }

    @FeignClient("account-service")
    public interface AccountService {

        @PostMapping(path = "/tcc/account")
        ResponseData<?> tccAccount(@RequestParam("userId") String userId, @RequestParam("subMoney") int subMoney);

    }

}
