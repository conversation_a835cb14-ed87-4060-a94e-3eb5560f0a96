System.register(["./index-legacy-ee1db0c7.js","./config-data-legacy-7a8805fa.js","./template-add-edit-legacy-fbb24f62.js","./index-legacy-16f295ac.js","./ThemeTemplateFieldApi-legacy-437b0971.js","./template-form-legacy-8b235974.js","./ThemeTemplateApi-legacy-4a2544d6.js"],(function(e,a){"use strict";var t,l,n,i,d,o,s,u,c,v,b,r,p,m,f,y,h,g,k,_,x,j,w,T,C;return{setters:[e=>{t=e._,l=e.r,n=e.o,i=e.X,d=e.a,o=e.f,s=e.w,u=e.g,c=e.h,v=e.b,b=e.aR,r=e.d,p=e.c,m=e.F,f=e.e,y=e.t,h=e.aS,g=e.B,k=e.u,_=e.v,x=e.G,j=e.H,w=e.ch},e=>{T=e.default},e=>{C=e.default},null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent="[data-v-6d71b083] .ant-drawer-title{color:#262626;font-size:18px;font-weight:500}.edit-btn[data-v-6d71b083]{position:absolute;right:24px;top:87px}.content[data-v-6d71b083]{width:100%;overflow-y:auto;overflow-x:hidden;position:relative}.content .content-item[data-v-6d71b083]{width:100%;height:100%}[data-v-6d71b083] .ant-form-item-label>label{color:#60666b}[data-v-6d71b083] .ant-form-item{color:#60666b}[data-v-6d71b083] .ant-checkbox-wrapper-checked .ant-checkbox-disabled .ant-checkbox-inner{--disabled-bg: var(--primary-color);--disabled-color: #fff}[data-v-6d71b083] .ant-checkbox-disabled+span{--disabled-color: black}\n",document.head.appendChild(a);const z={class:"content"},K={class:"content-item"},L={key:0},O={key:1},R={class:"content-item"},S={__name:"tamplate-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const t=e,S=a,A=l("1"),B=l([{key:"1",name:"基础信息",icon:"icon-tab-baseinfo"},{key:"2",name:"模板配置",icon:"icon-menu-zuzhijiagou"}]),F=l({}),I=l(null),X=l(!1),D=l([{name:"模板名称",value:"templateName"},{name:"模板编码",value:"templateCode"},{name:"模板类型",value:"templateType"}]);n((()=>{G()})),i((()=>t.data),(e=>{e&&G()}),{deep:!0});const E=e=>{F.value=Object.assign({},e)},G=()=>{t.data&&(F.value=Object.assign({},t.data),I.value.openConfig(t.data.templateId))},H=e=>{A.value=e},N=()=>{X.value=!0};return(e,a)=>{const l=g,n=k,i=_,G=x,U=j,V=w;return d(),o(V,{width:800,visible:t.visible,title:"模板详情",onClose:a[1]||(a[1]=e=>{S("update:visible",!1)}),isShowTab:!0,activeKey:A.value,tabList:B.value,onTabChange:H},{default:s((()=>["1"==A.value?(d(),o(l,{key:0,type:"primary",class:"border-radius edit-btn",onClick:N},{default:s((()=>a[2]||(a[2]=[u("编辑")]))),_:1,__:[2]})):c("",!0),v("div",z,[b(v("div",K,[r(U,{ref:"formRef",model:F.value,"label-col":{span:6}},{default:s((()=>[r(G,{gutter:16},{default:s((()=>[(d(!0),p(m,null,f(D.value,((e,a)=>(d(),o(i,{span:12,key:a},{default:s((()=>[r(n,{label:e.name},{default:s((()=>["templateType"==e.value?(d(),p("span",L,y(1==F.value[e.value]?"系统类型":"业务类型"),1)):(d(),p("span",O,y(F.value[e.value]),1))])),_:2},1032,["label"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["model"])],512),[[h,"1"==A.value]]),b(v("div",R,[r(T,{ref_key:"ConfigRef",ref:I},null,512)],512),[[h,"2"==A.value]])]),X.value?(d(),o(C,{key:1,visible:X.value,"onUpdate:visible":a[0]||(a[0]=e=>X.value=e),data:F.value,onDone:E},null,8,["visible","data"])):c("",!0)])),_:1},8,["visible","activeKey","tabList"])}}};e("default",t(S,[["__scopeId","data-v-6d71b083"]]))}}}));
