import{am as ue,r as a,L as g,m as o}from"./index-18a1ea24.js";const pe=ue("pos",()=>{const n=a([]),l=a(0),v=a(0),m=a(0),c=a(null),i=a(0),d=a(0),f=a("idle"),N=a(""),A=a(0),D=a(0),s=a([]),y=a(1),x=a([]),b=a(null),w=a([]),M=a(""),T=g(()=>n.value.reduce((e,r)=>e+r.quantity,0)),S=g(()=>n.value.length>0),R=g(()=>S.value&&f.value!=="processing"),$=g(()=>c.value!==null),E=g(()=>s.value.length>0),U=(e,r=1)=>{try{if(e.stock<r)return o.warning("\u5546\u54C1 ".concat(e.productName," \u5E93\u5B58\u4E0D\u8DB3")),!1;const t=n.value.find(u=>u.productId===e.productId);if(t){const u=t.quantity+r;if(e.stock<u)return o.warning("\u5546\u54C1 ".concat(e.productName," \u5E93\u5B58\u4E0D\u8DB3")),!1;t.quantity=u,t.totalPrice=t.unitPrice*u}else n.value.push({productId:e.productId,productName:e.productName,productCode:e.productCode,unitPrice:e.price||e.retailPrice||e.unitPrice,quantity:r,totalPrice:(e.price||e.retailPrice||e.unitPrice)*r,unit:e.unit,pricingType:e.pricingType,retailPrice:e.retailPrice,piecePrice:e.piecePrice,referencePrice:e.referencePrice,imageUrl:e.imageUrl});return p(),o.success("\u5DF2\u6DFB\u52A0 ".concat(e.productName," \u5230\u8D2D\u7269\u8F66")),!0}catch(t){return console.error("\u6DFB\u52A0\u5546\u54C1\u5230\u8D2D\u7269\u8F66\u5931\u8D25:",t),o.error("\u6DFB\u52A0\u5546\u54C1\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}},J=e=>{try{const r=n.value.findIndex(t=>t.productId===e);if(r>-1){const t=n.value.splice(r,1)[0];p(),o.success("\u5DF2\u79FB\u9664 ".concat(t.productName))}}catch(r){console.error("\u79FB\u9664\u5546\u54C1\u5931\u8D25:",r),o.error("\u79FB\u9664\u5546\u54C1\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}},z=(e,r)=>{try{if(r<=0){J(e);return}const t=n.value.find(u=>u.productId===e);t&&(t.quantity=r,t.totalPrice=t.unitPrice*r,p())}catch(t){console.error("\u66F4\u65B0\u5546\u54C1\u6570\u91CF\u5931\u8D25:",t),o.error("\u66F4\u65B0\u6570\u91CF\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}},I=()=>{try{n.value=[],l.value=0,v.value=0,m.value=0,d.value=0,o.success("\u8D2D\u7269\u8F66\u5DF2\u6E05\u7A7A")}catch(e){console.error("\u6E05\u7A7A\u8D2D\u7269\u8F66\u5931\u8D25:",e),o.error("\u6E05\u7A7A\u8D2D\u7269\u8F66\u5931\u8D25")}},p=()=>{try{l.value=n.value.reduce((r,t)=>r+t.totalPrice,0);let e=0;$.value&&i.value>0&&(e=l.value*(i.value/100)),v.value=e,m.value=Math.max(0,l.value-v.value-d.value)}catch(e){console.error("\u8BA1\u7B97\u603B\u91D1\u989D\u5931\u8D25:",e)}},K=e=>{try{c.value=e,e&&e.discountRate?i.value=e.discountRate:i.value=0,p(),e&&o.success("\u5DF2\u7ED1\u5B9A\u4F1A\u5458\uFF1A".concat(e.memberName))}catch(r){console.error("\u8BBE\u7F6E\u4F1A\u5458\u4FE1\u606F\u5931\u8D25:",r),o.error("\u8BBE\u7F6E\u4F1A\u5458\u4FE1\u606F\u5931\u8D25")}},P=()=>{try{c.value=null,i.value=0,d.value=0,p(),o.success("\u5DF2\u53D6\u6D88\u4F1A\u5458\u7ED1\u5B9A")}catch(e){console.error("\u6E05\u9664\u4F1A\u5458\u4FE1\u606F\u5931\u8D25:",e)}},Q=(e,r)=>{try{r<0&&(r=0);const t=l.value-v.value;r>t&&(r=t),d.value=r,c.value&&(c.value.usedPoints=e),p()}catch(t){console.error("\u8BBE\u7F6E\u79EF\u5206\u62B5\u6263\u5931\u8D25:",t),o.error("\u8BBE\u7F6E\u79EF\u5206\u62B5\u6263\u5931\u8D25")}},H=async e=>{try{if(!e||l.value<=0)return;c.value&&c.value.discountRate&&(i.value=c.value.discountRate),p()}catch(r){console.error("\u5E94\u7528\u4F1A\u5458\u6298\u6263\u5931\u8D25:",r)}},j=()=>{try{i.value=0,d.value=0,p()}catch(e){console.error("\u6E05\u9664\u4F1A\u5458\u6298\u6263\u5931\u8D25:",e)}},q=a(100),B=e=>{N.value=e,e!=="CASH"&&(A.value=0,D.value=0)},G=e=>{try{A.value=e,D.value=Math.max(0,e-m.value)}catch(r){console.error("\u8BBE\u7F6E\u5B9E\u6536\u91D1\u989D\u5931\u8D25:",r)}},V=()=>{f.value="processing"},W=()=>{f.value="success",o.success("\u652F\u4ED8\u6210\u529F")},X=(e="\u652F\u4ED8\u5931\u8D25")=>{f.value="failed",o.error(e)},_=()=>{f.value="idle",N.value="",A.value=0,D.value=0},Y=()=>{try{I(),P(),_(),o.success("\u8BA2\u5355\u5B8C\u6210")}catch(e){console.error("\u5B8C\u6210\u8BA2\u5355\u5931\u8D25:",e)}},Z=(e="")=>{try{if(!S.value)return o.warning("\u8D2D\u7269\u8F66\u4E3A\u7A7A\uFF0C\u65E0\u6CD5\u6302\u5355"),!1;const r="SUSPEND-".concat(Date.now(),"-").concat(y.value.toString().padStart(3,"0"));y.value++;const t={suspendId:Date.now(),suspendNo:r,suspendTime:new Date().toISOString(),remark:e,orderData:{cartItems:JSON.parse(JSON.stringify(n.value)),totalAmount:l.value,discountAmount:v.value,finalAmount:m.value,currentMember:c.value?JSON.parse(JSON.stringify(c.value)):null,memberDiscountRate:i.value,pointsDeductionAmount:d.value}};return s.value.push(t),C(),I(),P(),o.success("\u8BA2\u5355\u5DF2\u6302\u8D77\uFF0C\u6302\u5355\u53F7\uFF1A".concat(r)),!0}catch(r){return console.error("\u6302\u5355\u5931\u8D25:",r),o.error("\u6302\u5355\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}},ee=e=>{try{const r=s.value.find(h=>h.suspendId===e);if(!r)return o.error("\u6302\u5355\u4E0D\u5B58\u5728"),!1;S.value&&o.warning("\u5F53\u524D\u8D2D\u7269\u8F66\u5C06\u88AB\u6E05\u7A7A");const t=r.orderData;n.value=t.cartItems||[],l.value=t.totalAmount||0,v.value=t.discountAmount||0,m.value=t.finalAmount||0,t.currentMember&&(c.value=t.currentMember,i.value=t.memberDiscountRate||0,d.value=t.pointsDeductionAmount||0);const u=s.value.findIndex(h=>h.suspendId===e);return u>-1&&(s.value.splice(u,1),C()),o.success("\u5DF2\u6062\u590D\u6302\u5355\uFF1A".concat(r.suspendNo)),!0}catch(r){return console.error("\u6062\u590D\u6302\u5355\u5931\u8D25:",r),o.error("\u6062\u590D\u6302\u5355\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}},re=e=>{try{const r=s.value.findIndex(t=>t.suspendId===e);if(r>-1){const t=s.value.splice(r,1)[0];return C(),o.success("\u5DF2\u5220\u9664\u6302\u5355\uFF1A".concat(t.suspendNo)),!0}return!1}catch(r){return console.error("\u5220\u9664\u6302\u5355\u5931\u8D25:",r),o.error("\u5220\u9664\u6302\u5355\u5931\u8D25"),!1}},F=(e=24)=>{try{const r=Date.now()-e*60*60*1e3,t=s.value.length;s.value=s.value.filter(h=>new Date(h.suspendTime).getTime()>r);const u=t-s.value.length;return u>0&&(C(),o.info("\u5DF2\u6E05\u7406 ".concat(u," \u4E2A\u8FC7\u671F\u6302\u5355"))),u}catch(r){return console.error("\u6E05\u7406\u8FC7\u671F\u6302\u5355\u5931\u8D25:",r),0}},C=()=>{try{const e={suspendedOrders:s.value,suspendCounter:y.value,timestamp:Date.now()};localStorage.setItem("pos_suspended_orders",JSON.stringify(e))}catch(e){console.error("\u4FDD\u5B58\u6302\u5355\u6570\u636E\u5230\u672C\u5730\u5B58\u50A8\u5931\u8D25:",e)}},te=()=>{try{const e=localStorage.getItem("pos_suspended_orders");if(e){const r=JSON.parse(e);s.value=r.suspendedOrders||[],y.value=r.suspendCounter||1,F()}}catch(e){console.error("\u4ECE\u672C\u5730\u5B58\u50A8\u52A0\u8F7D\u6302\u5355\u6570\u636E\u5931\u8D25:",e),s.value=[],y.value=1}},oe=()=>{try{const e={cartItems:n.value,totalAmount:l.value,discountAmount:v.value,finalAmount:m.value,currentMember:c.value,memberDiscountRate:i.value,pointsDeductionAmount:d.value,timestamp:Date.now()};localStorage.setItem("pos_current_cart",JSON.stringify(e))}catch(e){console.error("\u4FDD\u5B58\u8D2D\u7269\u8F66\u5230\u672C\u5730\u5B58\u50A8\u5931\u8D25:",e)}},L=()=>{try{const e=localStorage.getItem("pos_current_cart");if(e){const r=JSON.parse(e),t=Date.now()-60*60*1e3;r.timestamp&&r.timestamp>t?(n.value=r.cartItems||[],l.value=r.totalAmount||0,v.value=r.discountAmount||0,m.value=r.finalAmount||0,c.value=r.currentMember||null,i.value=r.memberDiscountRate||0,d.value=r.pointsDeductionAmount||0,n.value.length>0&&o.info("\u5DF2\u6062\u590D\u4E0A\u6B21\u672A\u5B8C\u6210\u7684\u8BA2\u5355")):localStorage.removeItem("pos_current_cart")}}catch(e){console.error("\u4ECE\u672C\u5730\u5B58\u50A8\u6062\u590D\u8D2D\u7269\u8F66\u5931\u8D25:",e),localStorage.removeItem("pos_current_cart")}},k=()=>{try{localStorage.removeItem("pos_current_cart")}catch(e){console.error("\u6E05\u9664\u672C\u5730\u8D2D\u7269\u8F66\u6570\u636E\u5931\u8D25:",e)}},ae=e=>{x.value=e||[]},ne=e=>{b.value=e},se=e=>{w.value=e||[]},ce=e=>{M.value=e||""},O=e=>typeof e!="number"?"0.00":e.toFixed(2);return{cartItems:n,totalAmount:l,discountAmount:v,finalAmount:m,currentMember:c,memberDiscountRate:i,pointsDeductionAmount:d,pointsExchangeRate:q,paymentStatus:f,selectedPaymentMethod:N,receivedAmount:A,changeAmount:D,suspendedOrders:s,categories:x,selectedCategory:b,products:w,searchKeyword:M,cartItemCount:T,hasCartItems:S,canCheckout:R,hasMember:$,hasSuspendedOrders:E,addToCart:U,removeFromCart:J,updateQuantity:z,clearCart:I,calculateTotal:p,setCurrentMember:K,clearCurrentMember:P,setPointsDeduction:Q,applyMemberDiscount:H,clearMemberDiscount:j,setPaymentMethod:B,setReceivedAmount:G,startPayment:V,paymentSuccess:W,paymentFailed:X,resetPaymentStatus:_,completeOrder:Y,suspendCurrentOrder:Z,resumeSuspendedOrder:ee,deleteSuspendedOrder:re,clearExpiredSuspendedOrders:F,saveCartToLocal:oe,loadCartFromLocal:L,clearCartFromLocal:k,setCategories:ae,setSelectedCategory:ne,setProducts:se,setSearchKeyword:ce,formatAmount:O,getCartSummary:()=>({itemCount:T.value,totalAmount:O(l.value),discountAmount:O(v.value),finalAmount:O(m.value),hasItems:S.value,canCheckout:R.value}),resetAllState:()=>{try{I(),P(),_(),x.value=[],b.value=null,w.value=[],M.value="",k(),o.success("\u72B6\u6001\u5DF2\u91CD\u7F6E")}catch(e){console.error("\u91CD\u7F6E\u72B6\u6001\u5931\u8D25:",e)}},initializeStore:()=>{try{te(),L(),console.log("POS Store \u521D\u59CB\u5316\u5B8C\u6210")}catch(e){console.error("POS Store \u521D\u59CB\u5316\u5931\u8D25:",e)}}}});export{pe as u};
