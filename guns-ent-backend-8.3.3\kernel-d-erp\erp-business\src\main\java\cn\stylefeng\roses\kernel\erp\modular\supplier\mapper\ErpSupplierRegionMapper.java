package cn.stylefeng.roses.kernel.erp.modular.supplier.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplierRegion;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 供应商-区域关联Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/22 16:00
 */
public interface ErpSupplierRegionMapper extends BaseMapper<ErpSupplierRegion> {

    /**
     * 根据供应商ID查询关联的区域ID列表
     *
     * @param supplierId 供应商ID
     * @return 区域ID列表
     */
    @Select("SELECT region_id FROM erp_supplier_region WHERE supplier_id = #{supplierId}")
    List<Long> getRegionIdsBySupplierId(@Param("supplierId") Long supplierId);

    /**
     * 根据区域ID查询关联的供应商
     *
     * @param page 分页参数
     * @param regionId 区域ID
     * @param includeChildRegions 是否包含子区域
     * @param childRegionIds 子区域ID列表
     * @return 供应商列表
     */
    List<ErpSupplierResponse> getSuppliersByRegionId(Page<ErpSupplierResponse> page,
                                                    @Param("regionId") Long regionId,
                                                    @Param("includeChildRegions") Boolean includeChildRegions,
                                                    @Param("childRegionIds") List<Long> childRegionIds);

    /**
     * 统计区域关联的供应商数量
     *
     * @param regionId 区域ID
     * @param includeChildRegions 是否包含子区域
     * @param childRegionIds 子区域ID列表
     * @return 供应商数量
     */
    Long countSuppliersByRegionId(@Param("regionId") Long regionId,
                                 @Param("includeChildRegions") Boolean includeChildRegions,
                                 @Param("childRegionIds") List<Long> childRegionIds);
}