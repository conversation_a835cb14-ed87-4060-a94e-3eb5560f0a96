<template>
  <a-modal
    title="编辑区域"
    :width="800"
    :visible="visible"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="区域编码" name="regionCode">
            <a-input v-model:value="form.regionCode" placeholder="请输入区域编码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="区域名称" name="regionName">
            <a-input v-model:value="form.regionName" placeholder="请输入区域名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="父级区域" name="parentId">
            <a-tree-select
              v-model:value="form.parentId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="regionTreeData"
              placeholder="请选择父级区域"
              tree-default-expand-all
              :field-names="{ children: 'children', title: 'title', key: 'key', value: 'value' }"
              allow-clear
              show-search
              :filter-tree-node="filterTreeNode"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="区域层级" name="regionLevel">
            <a-input
              v-model:value="regionLevelText"
              placeholder="根据父级区域自动设置"
              readonly
              style="background-color: #f5f5f5;"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="排序号" name="sortOrder">
            <a-input-number
              v-model:value="form.sortOrder"
              :min="0"
              :max="9999"
              style="width: 100%"
              placeholder="请输入排序号"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option value="Y">启用</a-select-option>
              <a-select-option value="N">停用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item
            label="备注"
            name="remark"
            :label-col="{ md: { span: 3 }, sm: { span: 24 } }"
            :wrapper-col="{ md: { span: 21 }, sm: { span: 24 } }"
          >
            <a-textarea
              v-model:value="form.remark"
              placeholder="请输入备注"
              :rows="3"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { RegionApi } from '../api/regionApi'

export default {
  name: 'RegionEditForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref()
    const loading = ref(false)
    const regionTreeData = ref([])
    const regionLevelText = ref('1级 - 国家')

    const form = reactive({
      regionId: null,
      regionCode: '',
      regionName: '',
      parentId: undefined,
      regionLevel: 1,
      sortOrder: 0,
      status: 'Y',
      remark: ''
    })

    const rules = {
      regionCode: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],
      regionName: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
      regionLevel: [{ required: true, message: '请选择区域层级', trigger: 'change' }],
      status: [{ required: true, message: '请选择状态', trigger: 'change' }]
    }

    const updateVisible = (visible) => {
      emit('update:visible', visible)
      if (!visible) {
        resetForm()
      }
    }

    const resetForm = () => {
      formRef.value?.resetFields()
      Object.assign(form, {
        regionId: null,
        regionCode: '',
        regionName: '',
        parentId: undefined,
        regionLevel: 1,
        sortOrder: 0,
        status: 'Y',
        remark: ''
      })
      updateRegionLevel()
    }

    // 层级名称映射
    const getLevelText = (level) => {
      const levelMap = {
        1: '1级 - 国家',
        2: '2级 - 省',
        3: '3级 - 市',
        4: '4级 - 区县',
        5: '5级 - 商圈'
      }
      return levelMap[level] || `${level}级 - 未知`
    }

    // 根据父级区域计算层级
    const calculateRegionLevel = (parentId) => {
      if (!parentId) {
        return 1 // 没有父级，默认为国家级
      }

      // 在树数据中查找父级区域
      const findParentLevel = (nodes, targetId) => {
        for (const node of nodes) {
          if (node.regionId === targetId) {
            return node.regionLevel || 1
          }
          if (node.children && node.children.length > 0) {
            const found = findParentLevel(node.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const parentLevel = findParentLevel(regionTreeData.value, targetId)
      return parentLevel ? Math.min(parentLevel + 1, 5) : 1 // 最大5级
    }

    // 更新区域层级显示
    const updateRegionLevel = () => {
      const level = calculateRegionLevel(form.parentId)
      form.regionLevel = level
      regionLevelText.value = getLevelText(level)
    }

    // 树选择器搜索过滤
    const filterTreeNode = (inputValue, treeNode) => {
      return treeNode.title && treeNode.title.toLowerCase().includes(inputValue.toLowerCase())
    }

    const loadRegionTree = async () => {
      try {
        const res = await RegionApi.findTree()
        let treeData = res || []

        // 过滤空白节点和无效数据
        treeData = filterValidNodes(treeData)

        regionTreeData.value = treeData
      } catch (error) {
        console.error('加载区域树失败:', error)
        regionTreeData.value = []
      }
    }

    // 过滤有效节点，移除空白或无效数据，并补充树选择器需要的字段
    const filterValidNodes = (nodes) => {
      if (!Array.isArray(nodes)) return []

      return nodes.filter(node => {
        // 确保节点有必要的字段
        if (!node || !node.regionId || !node.regionName) {
          return false
        }

        // 补充树选择器需要的字段
        node.title = node.regionName
        node.key = String(node.regionId)
        node.value = String(node.regionId)

        // 递归处理子节点
        if (node.children && Array.isArray(node.children)) {
          node.children = filterValidNodes(node.children)
        }

        return true
      })
    }

    const edit = async (record) => {
      try {
        await loadRegionTree()

        // 获取详细信息
        const res = await RegionApi.detail({ regionId: record.regionId })
        Object.assign(form, res)
      } catch (error) {
        console.error('获取区域详情失败:', error)
        message.error('获取详情失败')
      }
    }

    const save = async () => {
      try {
        await formRef.value.validate()
        loading.value = true

        await RegionApi.edit(form)
        // API调用成功，直接显示成功消息
        message.success('编辑成功')
        emit('ok')
        updateVisible(false)
      } catch (error) {
        console.error('编辑区域失败:', error)
        message.error('编辑失败')
      } finally {
        loading.value = false
      }
    }

    // 监听props.data变化，用于编辑时填充表单
    watch(() => props.data, (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        Object.assign(form, newData)
        // 数据填充后更新层级显示
        updateRegionLevel()
      }
    }, { immediate: true })

    // 监听visible变化，显示时加载区域树
    watch(() => props.visible, (visible) => {
      if (visible) {
        loadRegionTree()
      }
    })

    // 监听父级区域变化，自动更新层级
    watch(() => form.parentId, () => {
      updateRegionLevel()
    })

    return {
      formRef,
      loading,
      form,
      rules,
      regionTreeData,
      regionLevelText,
      updateVisible,
      save,
      edit,
      filterTreeNode
    }
  }
}
</script>
