package cn.stylefeng.roses.kernel.micro.api.exception.enums;

import cn.stylefeng.roses.kernel.micro.api.constants.MicroConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 微服务的异常枚举
 *
 * <AUTHOR>
 * @date 2021/5/10 10:20
 */
@Getter
public enum FeignExceptionEnum implements AbstractExceptionEnum {

    /**
     * feign远程调用资源不存在
     */
    REMOTE_SERVICE_NULL(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "01", "feign远程调用资源不存在"),

    /**
     * feign远程调用过程出错
     */
    REMOTE_SERVICE_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "02", "feign远程调用过程出错，错误原因：{}"),

    /**
     * feign远程调用过程出错
     */
    REMOTE_IO_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "03", "feign远程调用过程IO异常");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    FeignExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
