System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js"],(function(e,a){"use strict";var l,s,t,n,i,o,u,r,d,g,v,c,m,p,b,f,y,_;return{setters:[e=>{l=e.R,s=e.r,t=e.s,n=e.o,i=e.k,o=e.a,u=e.f,r=e.w,d=e.d,g=e.h,v=e.m,c=e.l,m=e.u,p=e.v,b=e.G,f=e.H,y=e.M},e=>{_=e._}],execute:function(){class a{static getUserAssignList(e){return l.get("/sysRoleAssign/getUserAssignList",e)}static changeRoleSelect(e){return l.post("/sysRoleAssign/changeRoleSelect",e)}static changeStatus(e){return l.post("/sysRoleAssign/changeStatus",e)}static removeUserOrgBind(e){return l.post("/sysRoleAssign/removeUserOrgBind",e)}static addUserOrgBind(e){return l.post("/sysRoleAssign/addUserOrgBind",e)}static deleteAllOrgBind(e){return l.post("/sysRoleAssign/deleteAllOrgBind",e)}static disableAllOrg(e){return l.post("/sysRoleAssign/disableAllOrg",e)}static syncOtherOrgStatusAndBusinessRole(e){return l.post("/sysRoleAssign/syncOtherOrgStatusAndBusinessRole",e)}}e("E",a);const h=e("_",{__name:"add-org",props:{visible:Boolean,userId:String},emits:["update:visible","done"],setup(e,{emit:l}){const h=e,O=l,A=s(!1),R=s({userId:h.userId,mainFlag:"N",statusFlag:1}),N=s(),U=t({orgName:[{required:!0,message:"请选择机构",type:"string",trigger:"change"}],positionName:[{required:!0,message:"请选择职务",type:"string",trigger:"change"}],mainFlag:[{required:!0,message:"请选择主要部门"}]}),B=s(!1),F=s(""),S=s([]),I=s({});n((()=>{}));const w=e=>{O("update:visible",e)},L=e=>{S.value=[e],"dept"==e?(F.value="机构选择",V("orgId","orgName","selectOrgList")):(F.value="职位选择",V("positionId","positionName","selectPositionList")),B.value=!0},V=(e,a,l)=>{R.value[e]&&R.value[a]?I.value[l]=[{bizId:R.value[e],name:R.value[a]}]:I.value[l]=[]},k=e=>{B.value=!1,"dept"==S.value[0]?x(e,"orgId","orgName","selectOrgList"):"position"==S.value[0]&&x(e,"positionId","positionName","selectPositionList")},x=(e,a,l,s)=>{e[s]&&e[s].length>0?(R.value[a]=e[s][0].bizId,R.value[l]=e[s][0].name):(R.value[a]="",R.value[l]="")},C=async()=>{await N.value.validate(),A.value=!0,a.addUserOrgBind(R.value).then((e=>{A.value=!1,v.success(e.message),w(!1),O("done")})).catch((()=>{A.value=!1}))};return(e,a)=>{const l=c,s=m,t=p,n=i("vxe-switch"),v=b,O=f,V=_,x=y;return o(),u(x,{width:600,maskClosable:!1,visible:h.visible,"confirm-loading":A.value,forceRender:!0,title:"新增机构","body-style":{paddingBottom:"8px"},"onUpdate:visible":w,onOk:C,onClose:a[7]||(a[7]=e=>w(!1))},{default:r((()=>[d(O,{ref_key:"formRef",ref:N,model:R.value,labelCol:{span:4},rules:U},{default:r((()=>[d(v,{gutter:16},{default:r((()=>[d(t,{span:24},{default:r((()=>[d(s,{label:"机构",name:"orgName"},{default:r((()=>[d(l,{value:R.value.orgName,"onUpdate:value":a[0]||(a[0]=e=>R.value.orgName=e),placeholder:"请选择机构",onFocus:a[1]||(a[1]=e=>L("dept"))},null,8,["value"])])),_:1})])),_:1}),d(t,{span:24},{default:r((()=>[d(s,{label:"职务",name:"positionName"},{default:r((()=>[d(l,{value:R.value.positionName,"onUpdate:value":a[2]||(a[2]=e=>R.value.positionName=e),placeholder:"请选择职位",onFocus:a[3]||(a[3]=e=>L("position"))},null,8,["value"])])),_:1})])),_:1}),d(t,{span:12},{default:r((()=>[d(s,{label:"主要部门:",name:"mainFlag",labelCol:{span:8}},{default:r((()=>[d(n,{modelValue:R.value.mainFlag,"onUpdate:modelValue":a[4]||(a[4]=e=>R.value.mainFlag=e),"open-value":"Y","close-value":"N"},null,8,["modelValue"])])),_:1})])),_:1}),d(t,{span:12},{default:r((()=>[d(s,{label:"是否启用",name:"statusFlag",labelCol:{span:8}},{default:r((()=>[d(n,{modelValue:R.value.statusFlag,"onUpdate:modelValue":a[5]||(a[5]=e=>R.value.statusFlag=e),"open-value":1,"close-value":2},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]),B.value?(o(),u(V,{key:0,visible:B.value,"onUpdate:visible":a[6]||(a[6]=e=>B.value=e),title:F.value,data:I.value,showTab:S.value,onDone:k},null,8,["visible","title","data","showTab"])):g("",!0)])),_:1},8,["visible","confirm-loading"])}}}),O=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));e("a",O)}}}));
