package cn.stylefeng.roses.kernel.conversion.service;

import cn.hutool.core.io.IoUtil;
import cn.stylefeng.roses.kernel.conversion.exception.DocumentParseExceptionEnum;
import cn.stylefeng.roses.kernel.conversion.util.*;
import cn.stylefeng.roses.kernel.file.api.FileInfoApi;
import cn.stylefeng.roses.kernel.file.api.FileOperatorApi;
import cn.stylefeng.roses.kernel.file.api.exception.FileException;
import cn.stylefeng.roses.kernel.file.api.exception.enums.FileExceptionEnum;
import cn.stylefeng.roses.kernel.file.api.pojo.response.SysFileInfoResponse;
import cn.stylefeng.roses.kernel.file.api.util.PdfFileTypeUtil;
import cn.stylefeng.roses.kernel.file.api.util.PicFileTypeUtil;
import cn.stylefeng.roses.kernel.file.api.util.SvgFileTypeUtil;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import cn.stylefeng.roses.kernel.rule.util.ResponseRenderUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 文档预览的业务
 *
 * <AUTHOR>
 * @since 2023/10/20 22:22
 */
@Service
@Slf4j
public class DocumentPreviewService {

    @Resource
    private FileInfoApi fileInfoApi;

    @Resource
    private FileOperatorApi fileOperatorApi;

    /**
     * 根据文件id预览文件
     *
     * @param fileId        文件id
     * @param needPdfStream 是否需要强制以pdf流输出，如果为true，图片和svg这种也会输出pdf
     * <AUTHOR>
     * @since 2023/10/20 22:22
     */
    public void documentPreview(Long fileId, Boolean needPdfStream) {

        // 获取文件信息
        SysFileInfoResponse fileInfo = fileInfoApi.getFileInfoWithoutContent(fileId);

        // 获取文件的的后缀名
        String fileSuffix = fileInfo.getFileSuffix().toLowerCase();

        // 判断文件不是图片，不是pdf，不是svg，不是office文档，则抛出异常
        if (!PicFileTypeUtil.getFileImgTypeFlag(fileSuffix) && !PdfFileTypeUtil.getFilePdfTypeFlag(fileSuffix) && !SvgFileTypeUtil.getFileSvgTypeFlag(
                fileSuffix) && !OfficeFileTypeUtil.getFileOfficeTypeFlag(fileSuffix)) {
            throw new FileException(FileExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);
        }

        HttpServletResponse response = HttpServletUtil.getResponse();

        // 如果是图片
        if (PicFileTypeUtil.getFileImgTypeFlag(fileSuffix)) {

            // 如果是pdf流输出，则渲染pdf，如果不需要pdf输出，则直接渲染
            if (needPdfStream) {
                response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                this.renderPicFile(fileInfo);
            } else {
                ResponseRenderUtil.setRenderImageHeader(response);
                this.directRenderFile(fileInfo);
            }

        }

        // 如果是pdf，直接渲染
        else if (PdfFileTypeUtil.getFilePdfTypeFlag(fileSuffix)) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            this.directRenderFile(fileInfo);
        }

        // 如果是svg
        else if (SvgFileTypeUtil.getFileSvgTypeFlag(fileSuffix)) {

            // 如果是pdf流输出，则转化pdf，如果不需要pdf输出，则直接渲染
            if (needPdfStream) {
                response.setContentType(MediaType.APPLICATION_PDF_VALUE);
                this.renderSvgFile(fileInfo);
            } else {
                response.setContentType(SvgFileTypeUtil.SVG_RESPONSE_CONTENT_TYPE);
                this.directRenderFile(fileInfo);
            }
        }

        // 如果是Office类型，则按文档预览方式
        else {
            this.renderOffice(fileInfo);
        }

    }

    /**
     * 直接渲染文件流
     *
     * <AUTHOR>
     * @since 2023/10/20 22:29
     */
    public void directRenderFile(SysFileInfoResponse fileInfo) {

        HttpServletResponse response = HttpServletUtil.getResponse();

        // 获取文件流
        byte[] fileContent = fileOperatorApi.getFileBytes(fileInfo.getFileBucket(), fileInfo.getFileObjectName());

        // 渲染文件流
        ServletOutputStream outputStream = null;

        try {
            outputStream = response.getOutputStream();
            IoUtil.write(outputStream, true, fileContent);
        } catch (IOException e) {
            log.error("直接渲染文件流出现错误！", e);
            throw new ServiceException(DocumentParseExceptionEnum.EXCEPTION_FILE_IO_ERROR);
        } finally {
            IoUtil.close(outputStream);
        }
    }

    /**
     * 渲染office
     *
     * <AUTHOR>
     * @since 2023/5/9 21:04
     */
    public void renderOffice(SysFileInfoResponse fileInfo) {

        HttpServletResponse response = HttpServletUtil.getResponse();

        String fileSuffix = fileInfo.getFileSuffix().toLowerCase();

        // 设置响应头
        response.setHeader("Content-Disposition", "inline;fileName=" + fileInfo.getFileOriginName() + ";fileName*=UTF-8''" + fileInfo.getFileOriginName());

        // 设置pdf响应content-type
        response.setContentType(MediaType.APPLICATION_PDF_VALUE);

        // 获取到文件的内容
        byte[] fileContent = fileOperatorApi.getFileBytes(fileInfo.getFileBucket(), fileInfo.getFileObjectName());

        try {

            // 如果是word文件
            if (fileSuffix.contains("doc") || fileSuffix.contains("docx")) {
                DocUtil.toPdfByBytes(fileContent, fileSuffix, response.getOutputStream());
            }

            // 如果excel文件
            else if (fileSuffix.contains("xls") || fileSuffix.contains("xlsx")) {
                ExcelUtil.toPdfByBytes(fileContent, response.getOutputStream());
            }

            // 如果ppt文件
            else if (fileSuffix.contains("ppt") || fileSuffix.contains("pptx")) {
                PptUtil.toPdfByBytes(fileContent, fileSuffix, response.getOutputStream());
            }


        } catch (Exception e) {
            log.error("渲染文件流出现错误！", e);
            throw new ServiceException(DocumentParseExceptionEnum.EXCEPTION_FILE_IO_ERROR);
        }
    }

    /**
     * 渲染图片流到pdf
     *
     * <AUTHOR>
     * @since 2023/10/20 22:29
     */
    public void renderPicFile(SysFileInfoResponse fileInfo) {

        HttpServletResponse response = HttpServletUtil.getResponse();

        // 获取文件流
        byte[] fileContent = fileOperatorApi.getFileBytes(fileInfo.getFileBucket(), fileInfo.getFileObjectName());

        // 渲染文件流
        ServletOutputStream outputStream = null;

        try {
            outputStream = response.getOutputStream();

            // 图片流转pdf流
            PictureUtil.picToPdf(fileContent, outputStream);

        } catch (IOException e) {
            log.error("渲染图片出现错误！", e);
            throw new ServiceException(DocumentParseExceptionEnum.EXCEPTION_FILE_IO_ERROR);
        } finally {
            IoUtil.close(outputStream);
        }
    }

    /**
     * 渲染svg图片流到pdf
     *
     * <AUTHOR>
     * @since 2023/10/20 22:29
     */
    public void renderSvgFile(SysFileInfoResponse fileInfo) {

        HttpServletResponse response = HttpServletUtil.getResponse();

        // 获取文件流
        byte[] fileContent = fileOperatorApi.getFileBytes(fileInfo.getFileBucket(), fileInfo.getFileObjectName());

        // 渲染文件流
        ServletOutputStream outputStream = null;

        try {
            outputStream = response.getOutputStream();

            // 图片流转pdf流
            PictureUtil.svgToPdf(fileContent, outputStream);

        } catch (IOException e) {
            log.error("渲染svg出现错误！", e);
            throw new ServiceException(DocumentParseExceptionEnum.EXCEPTION_FILE_IO_ERROR);
        } finally {
            IoUtil.close(outputStream);
        }

    }

}
