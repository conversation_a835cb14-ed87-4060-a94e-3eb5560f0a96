System.register(["./index-legacy-ee1db0c7.js","./sys-user-secret-key-form-legacy-431464b0.js"],(function(e,t){"use strict";var s,r,a,l,o,n,i,c,d;return{setters:[e=>{s=e.R,r=e.r,a=e.a,l=e.f,o=e.w,n=e.d,i=e.m,c=e.M},e=>{d=e.default}],execute:function(){class t{static findPage(e){return s.getAndLoadData("/sysUserSecretKey/page",e)}static add(e){return s.post("/sysUserSecretKey/add",e)}static edit(e){return s.post("/sysUserSecretKey/edit",e)}static delete(e){return s.post("/sysUserSecretKey/delete",e)}static batchDelete(e){return s.post("/sysUserSecretKey/batchDelete",e)}static detail(e){return s.getAndLoadData("/sysUserSecretKey/detail",e)}}e("S",t);const u=e("_",{__name:"sys-user-secret-key-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:s}){const u=e,y=s,f=r(!1),v=r({sysUserSecretKeySort:1e3,secretOnceFlag:"N"}),m=r(null),p=e=>{y("update:visible",e)},b=async()=>{m.value.$refs.formRef.validate().then((async e=>{e&&(f.value=!0,t.add(v.value).then((async e=>{f.value=!1,i.success(e.message),p(!1),y("done")})).catch((()=>{f.value=!1})))}))};return(e,t)=>{const s=c;return a(),l(s,{width:700,maskClosable:!1,visible:u.visible,"confirm-loading":f.value,forceRender:!0,title:"新建临时秘钥","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":p,onOk:b,onClose:t[1]||(t[1]=e=>p(!1))},{default:o((()=>[n(d,{form:v.value,"onUpdate:form":t[0]||(t[0]=e=>v.value=e),ref_key:"sysUserSecretKeyFormRef",ref:m},null,8,["form"])])),_:1},8,["visible","confirm-loading"])}}}),y=Object.freeze(Object.defineProperty({__proto__:null,default:u},Symbol.toStringTag,{value:"Module"}));e("s",y)}}}));
