package cn.stylefeng.roses.kernel.micro.feign.example.modular;

import cn.hutool.core.lang.Dict;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class ExampleConsumerFallbackFactory implements FallbackFactory<ExampleConsumer> {

    @Override
    public ExampleConsumer create(Throwable cause) {
        return new ExampleConsumer() {
            @Override
            public ResponseData<?> success(Dict dict) {
                return new SuccessResponseData<>("501", "降级success方法", null);
            }

            @Override
            public ResponseData<?> error(Dict dict) {
                return new SuccessResponseData<>("501", "降级error方法", null);
            }
        };
    }

}