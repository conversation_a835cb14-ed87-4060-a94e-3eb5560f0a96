package cn.stylefeng.roses.kernel.manage.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * API客户端异常相关枚举
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@Getter
public enum ApiClientExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    API_CLIENT_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE +  "10001", "API客户端查询结果不存在");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ApiClientExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
