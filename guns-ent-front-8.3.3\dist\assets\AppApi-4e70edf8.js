import{R as s}from"./index-18a1ea24.js";class p{static findPage(t){return s.getAndLoadData("/sysApp/page",t)}static add(t){return s.post("/sysApp/add",t)}static edit(t){return s.post("/sysApp/edit",t)}static delete(t){return s.post("/sysApp/delete",t)}static batchDelete(t){return s.post("/sysApp/batchDelete",t)}static detail(t){return s.getAndLoadData("/sysApp/detail",t)}static list(t){return s.getAndLoadData("/sysApp/list",t)}static updateStatus(t){return s.post("/sysApp/updateStatus",t)}}export{p as A};
