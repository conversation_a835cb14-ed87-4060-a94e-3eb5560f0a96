import{_ as P,r as y,o as W,a as i,c as p,b as r,d as u,w as m,I as R,aR as q,f as F,h as _,t as L,aS as J,l as X,bg as ae,a5 as M,S as ne,s as le,F as O,e as oe,g as U,bj as ie,aK as ce,v as de,a0 as ue,G as pe,M as _e}from"./index-18a1ea24.js";/* empty css              */import{_ as re}from"./index-02bf6f00.js";/* empty css              */import{O as Q}from"./OrgApi-021dd6dd.js";const he={class:"box bgColor box-shadow"},ve={class:"search"},ye={class:"tree-content"},fe={class:"left-tree"},ge={class:"not-tree-edit"},me={class:"edit-title"},xe={__name:"org-user-tree",props:{companySearchFlag:{type:Boolean,default:!1}},emits:["treeSelect","treeData","checkedTree"],setup(k,{expose:$,emit:S}){const b=S,w=k,x=y(""),n=y(!1),c=y([]),h=y([]),T=y([]),v=y([]),I=y([]);W(()=>{C()});const C=()=>{v.value=[],n.value=!0,Q.tree({searchText:x.value,companySearchFlag:w.companySearchFlag}).then(d=>{T.value=d.data.expandOrgIdList;const t=D(d.data.orgTreeList);c.value=t}).finally(()=>n.value=!1)},D=d=>(d&&d.length>0&&d.forEach(t=>{t.id=t.orgId,t.name=t.orgName,t.type=t.orgType+"",t.haveSubOrgFlag?t.isLeaf=!1:t.isLeaf=!0,t.children&&t.children.length>0&&(t.children=D(t.children))}),d),A=(d,t)=>{b("treeSelect",d,t)},B=()=>{x.value||C()},N=d=>(v.value.push(d.eventKey),new Promise(t=>{Q.tree({orgParentId:d.dataRef.orgId,companySearchFlag:w.companySearchFlag}).then(K=>{const s=D(K.data.orgTreeList);d.dataRef.children=s,c.value=[...c.value]}).finally(()=>{t()})})),E=(d,{checked:t,node:K})=>{b("checkedTree",t,K)};return $({currentSelectKeys:h,checkedKeyss:I}),(d,t)=>{const K=X,s=ae,e=M,o=ne;return i(),p("div",he,[r("div",ve,[u(K,{value:x.value,"onUpdate:value":t[0]||(t[0]=a=>x.value=a),placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22","allow-clear":"",onPressEnter:C,onChange:B},{prefix:m(()=>[u(R,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),r("div",ye,[u(o,{tip:"Loading...",spinning:n.value,delay:100},{default:m(()=>[q(r("div",fe,[u(s,{"show-icon":!0,selectedKeys:h.value,"onUpdate:selectedKeys":t[1]||(t[1]=a=>h.value=a),expandedKeys:T.value,"onUpdate:expandedKeys":t[2]||(t[2]=a=>T.value=a),loadedKeys:v.value,"onUpdate:loadedKeys":t[3]||(t[3]=a=>v.value=a),checkedKeys:I.value,"onUpdate:checkedKeys":t[4]||(t[4]=a=>I.value=a),checkable:"",checkStrictly:"",onCheck:E,onSelect:A,"load-data":N,"tree-data":c.value,fieldNames:{children:"children",title:"name",key:"id",value:"id"}},{icon:m(a=>[a.type=="1"?(i(),F(R,{key:0,"icon-class":"icon-tree-gongsi",color:"#43505e",fontSize:"24px"})):_("",!0),a.type=="2"?(i(),F(R,{key:1,"icon-class":"icon-tree-dept",color:"#43505e",fontSize:"24px"})):_("",!0)]),title:m(a=>[r("span",ge,[r("span",me,L(a.name),1)])]),_:1},8,["selectedKeys","expandedKeys","loadedKeys","checkedKeys","tree-data"])],512),[[J,c.value&&c.value.length>0]]),q(u(e,{class:"empty"},null,512),[[J,c.value&&c.value.length==0]])]),_:1},8,["spinning"])])])}}},ke=P(xe,[["__scopeId","data-v-a352df08"]]);const Se={class:"selected"},we={class:"selected-top"},be={class:"selected-del"},Te={class:"selected-bottom"},Ie={class:"bottom-list"},Ce={key:1},Ke={class:"selected-name"},Oe={key:0},Le={key:1},$e={key:2},De={class:"selected-del"},Ne={__name:"selecteds-list",props:{list:Array},emits:["delete","deleteAll"],setup(k,{emit:$}){const S=$,b=le({simpleImage:M.PRESENTED_IMAGE_SIMPLE}),w=n=>{S("delete",n)},x=()=>{S("deleteAll")};return(n,c)=>{const h=R,T=M;return i(),p("div",Se,[r("div",we,[c[0]||(c[0]=r("span",null,"\u5DF2\u9009",-1)),r("span",be,[u(h,{iconClass:"icon-opt-shanchu",title:"\u5220\u9664\u5168\u90E8",color:"#000",onClick:x})])]),r("div",Te,[r("div",Ie,[k.list&&k.list.length==0?(i(),F(T,{key:0,image:b.simpleImage},null,8,["image"])):(i(),p("div",Ce,[(i(!0),p(O,null,oe(k.list,(v,I)=>(i(),p("div",{class:"list",key:v.id},[r("div",Ke,[U(L(I+1)+" "+L(v.name)+" ( ",1),v.type=="1"?(i(),p("span",Oe,"\u516C\u53F8")):_("",!0),v.type=="2"?(i(),p("span",Le,"\u90E8\u95E8")):_("",!0),v.type=="3"?(i(),p("span",$e,"\u4EBA\u5458")):_("",!0),c[1]||(c[1]=U(" ) "))]),r("div",De,[u(h,{iconClass:"icon-opt-shanchu",title:"\u5220\u9664",color:"#000",onClick:C=>w(v)},null,8,["onClick"])])]))),128))]))])])])}}},Ee=P(Ne,[["__scopeId","data-v-12a0ed0e"]]);const Re={class:"box"},Ue={class:"search"},Fe={class:"user-table"},Ae={key:0},Be={key:1},Me={key:0},Pe={key:1},Ve={__name:"index",props:{width:{type:String,default:"60%"},visible:Boolean,title:{type:String,default:"\u6807\u9898"},footer:{type:String,default:void 0},list:{type:Array,default:[]}},emits:["update:visible","done"],setup(k,{emit:$}){const S=k,b=$,w=y(!1),x=y({searchText:"",orgIdCondition:""}),n=y([]),c=y(null),h=y(null),T=y([{dataIndex:"realName",title:"\u59D3\u540D",align:"center",width:100,isShow:!0},{dataIndex:"company",title:"\u516C\u53F8",align:"center",width:100,isShow:!0},{dataIndex:"dept",title:"\u90E8\u95E8",align:"center",width:100,isShow:!0},{dataIndex:"positionName",title:"\u804C\u52A1",align:"center",width:100,isShow:!0}]);W(()=>{n.value=ie(S.list);let s=[],e=[];n.value.forEach(o=>{o.type=="3"?e.push(o.id):s.push(o.id)}),ce(()=>{h.value.checkedKeyss=s,c.value.selectedRowList=e})});const v=(s,e)=>{x.value.orgIdCondition=s[0],C()},I=s=>(s.data.rows.forEach(e=>{e.id=e.userId,e.name=e.realName,e.type="3"}),s),C=()=>{c.value.reload()},D=(s,e)=>{s?n.value.find(o=>o.type==e.orgType&&o.id==e.id)||n.value.push(e):E(e)},A=(s,e,o)=>{e?n.value.find(a=>a.id==s.id&&a.type==s.type)||n.value.push(s):E(s)},B=(s,e,o)=>{s?o.forEach(a=>{n.value.find(f=>f.id==a.id&&f.type=="3")||n.value.push(a)}):o.forEach(a=>{for(let f=n.value.length-1;f>=0;f--)a.id==n.value[f].id&&n.value.splice(f,1)})},N=s=>{var e;if((e=c.value)!=null&&e.selectedRowList){let o=[];s&&s.length>0&&s.forEach(a=>{n.value.find(f=>f.id==a.id&&f.type=="3")&&o.push(a.id)}),c.value.selectedRowList=o}},E=s=>{if(n.value.splice(n.value.findIndex(e=>e.type==s.type&&e.id===s.id),1),s.type!="3")h.value.checkedKeyss.checked.find(e=>e==s.id)&&h.value.checkedKeyss.checked.splice(h.value.checkedKeyss.checked.findIndex(e=>e===s.id),1);else{let e=n.value.filter(o=>o.type=="3");N(e)}},d=()=>{n.value=[],h.value.checkedKeyss.checked=[],N([])},t=s=>{b("update:visible",s)},K=()=>{w.value=!0,w.value=!1,t(!1),b("done",n.value)};return(s,e)=>{const o=de,a=R,f=X,Z=re,ee=ue,te=pe,se=_e;return k.visible?(i(),F(se,{key:0,width:S.width,visible:S.visible,"confirm-loading":w.value,"body-style":{paddingBottom:"8px",height:"calc(100vh - 400px)",overflowY:"auto",minHeight:"700px"},onOk:K,title:k.title,onCancel:e[3]||(e[3]=g=>t(!1)),footer:k.footer,maskClosable:!1,wrapClassName:"selection-modal"},{default:m(()=>[r("div",Re,[u(te,{class:"user-select",gutter:16},{default:m(()=>[u(o,{xs:24,sm:24,md:6,class:"height100"},{default:m(()=>[u(ke,{onTreeSelect:v,onCheckedTree:D,ref_key:"OrgUserSelectTreeRef",ref:h},null,512)]),_:1}),u(o,{xs:24,sm:24,md:12,class:"height100"},{default:m(()=>[u(ee,{bordered:!1,style:{height:"100%"}},{default:m(()=>[r("div",Ue,[u(f,{value:x.value.searchText,"onUpdate:value":e[0]||(e[0]=g=>x.value.searchText=g),placeholder:"\u59D3\u540D\u3001\u8D26\u53F7\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:C,style:{width:"300px"}},{suffix:m(()=>[u(a,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),r("div",Fe,[u(Z,{columns:T.value,where:x.value,bordered:"",isShowRowSelect:"",rowId:"userId",ref_key:"tableRef",ref:c,url:"/sysUser/page",customData:I,onOnSelect:A,onOnSelectAll:B,onTableListChange:e[1]||(e[1]=g=>N(g))},{bodyCell:m(({column:g,record:l})=>{var V,z,G,j,H,Y;return[g.dataIndex=="company"?(i(),p(O,{key:0},[U(L((V=l==null?void 0:l.userOrgDTO)!=null&&V.companyName?(z=l==null?void 0:l.userOrgDTO)==null?void 0:z.companyName:""),1)],64)):_("",!0),g.dataIndex=="dept"?(i(),p(O,{key:1},[U(L((G=l==null?void 0:l.userOrgDTO)!=null&&G.deptName?(j=l==null?void 0:l.userOrgDTO)==null?void 0:j.deptName:""),1)],64)):_("",!0),g.dataIndex=="positionName"?(i(),p(O,{key:2},[U(L((H=l==null?void 0:l.userOrgDTO)!=null&&H.positionName?(Y=l==null?void 0:l.userOrgDTO)==null?void 0:Y.positionName:""),1)],64)):_("",!0),g.dataIndex=="sex"?(i(),p(O,{key:3},[l.sex=="M"?(i(),p("span",Ae,"\u7537")):_("",!0),l.sex=="F"?(i(),p("span",Be,"\u5973")):_("",!0)],64)):_("",!0),g.dataIndex=="statusFlag"?(i(),p(O,{key:4},[l.statusFlag==1?(i(),p("span",Me,"\u542F\u7528")):_("",!0),l.statusFlag==2?(i(),p("span",Pe,"\u7981\u7528")):_("",!0)],64)):_("",!0)]}),_:1},8,["columns","where"])])]),_:1})]),_:1}),u(o,{xs:24,sm:24,md:6,class:"height100"},{default:m(()=>[u(Ee,{list:n.value,"onUpdate:list":e[2]||(e[2]=g=>n.value=g),onDelete:E,onDeleteAll:d},null,8,["list"])]),_:1})]),_:1})])]),_:1},8,["width","visible","confirm-loading","title","footer"])):_("",!0)}}},qe=P(Ve,[["__scopeId","data-v-50dfe81b"]]);export{qe as _};
