System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./RoleTypeApi-legacy-6008d05b.js"],(function(e,a){"use strict";var l,t,r,o,d,u,n,i,s,c,v,y,g,m,p;return{setters:[e=>{l=e.r,t=e.s,r=e.o,o=e.a,d=e.f,u=e.w,n=e.d,i=e.m,s=e.l,c=e.u,v=e.as,y=e.y,g=e.H,m=e.M},null,null,e=>{p=e.R}],execute:function(){e("default",{__name:"role-type-add-edit",props:{visible:Boolean,data:Object,roleType:Number,categoryParentId:String,companyData:Object},emits:["update:visible","done"],setup(e,{emit:a}){const f=e,b=a,h=l(!1),w=l(!1),C=l({categoryType:f.roleType,categoryParentId:f.categoryParentId,fldSort:100}),I=t({roleCategoryName:[{required:!0,message:"请输入角色分类名称",type:"string",trigger:"blur"}],categoryParentId:[{required:!0,message:"请选择所属父级",type:"string",trigger:"blur"}],fldSort:[{required:!0,message:"请输入角色分类排序",type:"number",trigger:"blur"}]}),N=l(null),_=l([]);r((async()=>{var e;f.data?(w.value=!0,C.value=Object.assign({},f.data)):(w.value=!1,20==f.roleType&&(C.value.companyId=null===(e=f.companyData)||void 0===e?void 0:e.companyId)),x()}));const x=async()=>{var e;const a=await p.treeList({categoryType:f.roleType,ignoreCategoryId:null===(e=C.value)||void 0===e?void 0:e.id});_.value=[{id:"-1",roleCategoryName:"根节点",children:a.data}]},P=e=>{b("update:visible",e)},S=async()=>{await N.value.validate(),h.value=!0;let e=null;e=w.value?p.edit(C.value):p.add(C.value),e.then((async e=>{h.value=!1,i.success(e.message),P(!1),b("done")})).catch((()=>{h.value=!1}))};return(e,a)=>{const l=s,t=c,r=v,i=y,p=g,b=m;return o(),d(b,{width:524,maskClosable:!1,visible:f.visible,"confirm-loading":h.value,forceRender:!0,title:w.value?"编辑角色分类":"新建角色分类","body-style":{paddingBottom:"8px"},"onUpdate:visible":P,onOk:S,onClose:a[3]||(a[3]=e=>P(!1))},{default:u((()=>[n(p,{ref_key:"formRef",ref:N,model:C.value,rules:I,layout:"vertical"},{default:u((()=>[n(t,{label:"角色分类名称:",name:"roleCategoryName"},{default:u((()=>[n(l,{value:C.value.roleCategoryName,"onUpdate:value":a[0]||(a[0]=e=>C.value.roleCategoryName=e),"allow-clear":"",placeholder:"请输入角色分类名称"},null,8,["value"])])),_:1}),n(t,{label:"所属父级:",name:"categoryParentId"},{default:u((()=>[n(r,{value:C.value.categoryParentId,"onUpdate:value":a[1]||(a[1]=e=>C.value.categoryParentId=e),style:{width:"100%"},showSearch:"","tree-data":_.value,treeNodeFilterProp:"roleCategoryName","dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择所属父级",fieldNames:{children:"children",label:"roleCategoryName",key:"id",value:"id"},"allow-clear":"","tree-default-expand-all":""},null,8,["value","tree-data"])])),_:1}),n(t,{label:"角色分类排序:",name:"fldSort"},{default:u((()=>[n(i,{value:C.value.fldSort,"onUpdate:value":a[2]||(a[2]=e=>C.value.fldSort=e),min:0,style:{width:"100%"},placeholder:"请输入角色分类排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
