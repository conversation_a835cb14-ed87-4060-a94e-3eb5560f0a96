package cn.stylefeng.roses.kernel.erp.modular.supplier.service;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.BusinessModeChangeValidationResponse;

import java.math.BigDecimal;

/**
 * 供应商经营方式业务规则服务接口
 *
 * <AUTHOR>
 * @since 2025/07/27 21:55
 */
public interface SupplierBusinessModeRuleService {

    /**
     * 验证经营方式是否有效
     *
     * @param businessMode 经营方式
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    boolean validateBusinessMode(String businessMode);

    /**
     * 验证销售扣点是否有效
     *
     * @param businessMode 经营方式
     * @param salesDeduction 销售扣点
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    boolean validateSalesDeduction(String businessMode, BigDecimal salesDeduction);

    /**
     * 验证经营方式变更的影响
     *
     * @param currentSupplier 当前供应商信息
     * @param newBusinessMode 新的经营方式
     * @return 变更影响验证结果
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    BusinessModeChangeValidationResponse validateBusinessModeChange(ErpSupplier currentSupplier, String newBusinessMode);

    /**
     * 检查供应商是否可以创建采购入库单
     *
     * @param supplier 供应商信息
     * @return 是否可以创建采购入库单
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    boolean canCreatePurchaseOrder(ErpSupplier supplier);

    /**
     * 检查供应商商品是否需要库存管理
     *
     * @param supplier 供应商信息
     * @return 是否需要库存管理
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    boolean needInventoryManagement(ErpSupplier supplier);

    /**
     * 获取经营方式的描述信息
     *
     * @param businessMode 经营方式
     * @return 描述信息
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    String getBusinessModeDescription(String businessMode);

    /**
     * 检查经营方式是否需要销售扣点
     *
     * @param businessMode 经营方式
     * @return 是否需要销售扣点
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    boolean needSalesDeduction(String businessMode);

    /**
     * 验证供应商经营方式相关参数的完整性
     *
     * @param supplier 供应商信息
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    void validateSupplierBusinessModeParams(ErpSupplier supplier);

}