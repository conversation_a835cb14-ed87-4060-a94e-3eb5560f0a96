server {
    listen 80;

    location /api/ {
        proxy_pass        http://localhost:8000/guns/;
        proxy_set_header   Host    $host;
        proxy_set_header   X-Real-IP   $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        client_max_body_size    1000m;
    }

    location / {
        alias /usr/local/front-apps/guns/;
        index index.html;
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root html;
    }

}
