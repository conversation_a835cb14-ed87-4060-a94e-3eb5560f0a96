<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-wrapper</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>wrapper-sdk</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--wrapper模块的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>wrapper-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--db模块的api-->
        <!--包装的时候可能包装PageResult对象-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- aop -->
        <!-- 包装过程是在aop中进行 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- mybatis-plus dao框架 -->
        <!-- 包装分页的参数可能 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

    </dependencies>

</project>
