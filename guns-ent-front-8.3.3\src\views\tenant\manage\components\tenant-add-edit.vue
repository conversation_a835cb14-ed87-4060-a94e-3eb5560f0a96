<template>
  <!-- 新增编辑 -->
  <a-modal
    :width="900"
    :maskClosable="false"
    :visible="props.visible"
    :confirm-loading="loading"
    :forceRender="true"
    :title="isUpdate ? '编辑租户' : '新建租户'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
    class="common-modal"
    @close="updateVisible(false)"
  >
    <tenant-form v-model:form="form" ref="tenantFormRef" :isUpdate="isUpdate" />
  </a-modal>
</template>

<script setup name="TenantAddEdit">
import { ref, onMounted } from 'vue';
import TenantForm from './tenant-form.vue';
import { message } from 'ant-design-vue';
import { TenantApi } from '../api/TenantApi';
import { getNowTime, GetNextMonthDay } from '@/utils/common/time-util';
import { FileApi } from '@/views/system/backend/file/api/FileApi';

const props = defineProps({
  visible: Boolean,
  data: Object
});

const emits = defineEmits(['update:visible', 'done']);
// 弹框加载
const loading = ref(false);
// 是否是编辑状态
const isUpdate = ref(false);
// 表单数据
const form = ref({
  iconList: [],
  statusFlag: 1,
  activeDate: getNowTime() + ' 00:00:00',
  expireDate: GetNextMonthDay(getNowTime(), 1) + ' 00:00:00',
  tenantLinkList: [], //开通功能
});
// ref
const tenantFormRef = ref(null);

onMounted(() => {
  if (props.data) {
    isUpdate.value = true;
    getDetail();
  } else {
    isUpdate.value = false;
  }
});

// 获取详情
const getDetail = () => {
  TenantApi.detail({ tenantId: props.data.tenantId }).then(res => {
    form.value = Object.assign({}, res);
    form.value.iconList = [];
    setFileList('iconList', res.tenantLogo);
    if (!form.value.tenantLinkList) {
      form.value.tenantLinkList = [];
    }
  });
};

// 设置文件列表
const setFileList = (name, fileId) => {
  FileApi.getAntdVInfo({ fileId: fileId }).then(res => {
    res.uid = fileId;
    form.value[name] = [res];
  });
};

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};

// 点击保存
const save = async () => {
  tenantFormRef.value.$refs.formRef.validate().then(async valid => {
    if (valid) {
      if (form.value.iconList?.length) {
        form.value.tenantLogo = form.value.iconList[0]?.response?.data?.fileId
          ? form.value.iconList[0]?.response?.data?.fileId
          : form.value.iconList[0]?.uid;
      }
      // 修改加载框为正在加载
      loading.value = true;

      let result = null;

      // 执行编辑或修改
      if (isUpdate.value) {
        result = TenantApi.edit(form.value);
      } else {
        result = TenantApi.add(form.value);
      }
      result
        .then(async result => {
          // 移除加载框
          loading.value = false;

          // 提示添加成功
          message.success(result.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          emits('done');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<style></style>
