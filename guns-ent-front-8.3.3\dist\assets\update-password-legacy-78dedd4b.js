System.register(["./index-legacy-ee1db0c7.js"],(function(e,a){"use strict";var s,t,r,l,d,o,n,u,c,p,i,w,m,v,b,g,f,h,_;return{setters:[e=>{s=e._,t=e.H,r=e.r,l=e.s,d=e.a,o=e.c,n=e.d,u=e.w,c=e.aH,p=e.at,i=e.g,w=e.bj,m=e.bJ,v=e.m,b=e.bP,g=e.bQ,f=e.aa,h=e.u,_=e.B}],execute:function(){var a=document.createElement("style");a.textContent='.form[data-v-97e30c71]{width:50%;height:100%}.save-btn[data-v-97e30c71] .ant-form-item-label>label:after{content:""}@media screen and (max-width: 768px){.form[data-v-97e30c71]{width:100%}}\n',document.head.appendChild(a);const y={class:"form"};e("default",s({__name:"update-password",setup(e){const a=t.useForm,s=r(!1),P=l({password:"",newPassword:"",password2:""}),x=l({password:[{required:!0,type:"string",message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,type:"string",message:"请输入新密码",trigger:"blur"}],password2:[{required:!0,type:"string",trigger:"blur",validator:async(e,a)=>a?a!==P.newPassword?Promise.reject("两次输入密码不一致"):Promise.resolve():Promise.reject("请再次输入新密码")}]}),{resetFields:j,validate:q,validateInfos:C}=a(P,x),U=()=>{q().then((()=>{s.value=!0;let e=w(P);delete e.password2,m.updatePassword(e).then((async e=>{e.success?(v.success("修改成功！"),j(),await b.logout(),g()):v.error(e.message)})).finally((()=>{s.value=!1}))}))};return(e,a)=>{const s=f,r=h,l=_,w=t;return d(),o("div",y,[n(w,{"label-col":{sm:{span:6}},"wrapper-col":{sm:{span:18}}},{default:u((()=>[n(r,c({label:"旧密码"},p(C).password),{default:u((()=>[n(s,{value:P.password,"onUpdate:value":a[0]||(a[0]=e=>P.password=e),placeholder:"请输入旧密码"},null,8,["value"])])),_:1},16),n(r,c({label:"新密码"},p(C).newPassword),{default:u((()=>[n(s,{value:P.newPassword,"onUpdate:value":a[1]||(a[1]=e=>P.newPassword=e),placeholder:"请输入新密码"},null,8,["value"])])),_:1},16),n(r,c({label:"重复密码"},p(C).password2),{default:u((()=>[n(s,{value:P.password2,"onUpdate:value":a[2]||(a[2]=e=>P.password2=e),placeholder:"请再次输入新密码"},null,8,["value"])])),_:1},16),n(r,{label:" ",class:"save-btn"},{default:u((()=>[n(l,{type:"primary","html-type":"submit",onClick:U},{default:u((()=>a[3]||(a[3]=[i("保存")]))),_:1,__:[3]})])),_:1})])),_:1})])}}},[["__scopeId","data-v-97e30c71"]]))}}}));
