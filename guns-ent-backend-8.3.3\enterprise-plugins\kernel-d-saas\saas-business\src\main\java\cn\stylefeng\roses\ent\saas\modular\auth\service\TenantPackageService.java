package cn.stylefeng.roses.ent.saas.modular.auth.service;

import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackage;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantPackageRequest;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 租户-功能包服务类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
public interface TenantPackageService extends IService<TenantPackage> {

    /**
     * 新增租户-功能包
     *
     * @param tenantPackageRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void add(TenantPackageRequest tenantPackageRequest);

    /**
     * 删除租户-功能包
     *
     * @param tenantPackageRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void del(TenantPackageRequest tenantPackageRequest);

    /**
     * 批量删除租户-功能包
     *
     * @param tenantPackageRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void batchDelete(TenantPackageRequest tenantPackageRequest);

    /**
     * 编辑租户-功能包
     *
     * @param tenantPackageRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void edit(TenantPackageRequest tenantPackageRequest);

    /**
     * 查询详情租户-功能包
     *
     * @param tenantPackageRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    TenantPackage detail(TenantPackageRequest tenantPackageRequest);

    /**
     * 获取租户-功能包列表
     *
     * @param tenantPackageRequest         请求参数
     * @return List<TenantPackage>  返回结果
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    List<TenantPackage> findList(TenantPackageRequest tenantPackageRequest);

    /**
     * 获取租户-功能包分页列表
     *
     * @param tenantPackageRequest                请求参数
     * @return PageResult<TenantPackage>   返回结果
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    PageResult<TenantPackage> findPage(TenantPackageRequest tenantPackageRequest);

}
