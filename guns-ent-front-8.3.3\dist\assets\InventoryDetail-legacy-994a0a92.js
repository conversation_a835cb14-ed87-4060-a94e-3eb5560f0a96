System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-510bfbb8.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./InventoryHistoryApi-legacy-e1cc044f.js"],(function(t,e){"use strict";var a,o,i,l,r,n,s,c,d,u,p,y,g,f,m,k,S,T,h,v,_,C,b,x,N,O,A;return{setters:[t=>{a=t._,o=t.r,i=t.X,l=t.a,r=t.f,n=t.w,s=t.b,c=t.d,d=t.t,u=t.g,p=t.h,y=t.c,g=t.a2,f=t.F,m=t.Y,k=t.U,S=t.Z,T=t.a0,h=t.a4,v=t.v,_=t.G,C=t.I,b=t.B,x=t.i,N=t.a5,O=t.M},null,null,null,null,null,null,null,t=>{A=t.I}],execute:function(){var e=document.createElement("style");e.textContent=".inventory-detail-content[data-v-6542a158]{max-height:70vh;overflow-y:auto}.product-name[data-v-6542a158]{font-weight:500;color:#1890ff}.stock-status-info[data-v-6542a158]{display:flex;align-items:center;gap:16px}.stock-status-desc[data-v-6542a158]{color:#8c8c8c;font-size:14px}.quantity-increase[data-v-6542a158]{color:#52c41a;font-weight:500}.quantity-decrease[data-v-6542a158]{color:#ff4d4f;font-weight:500}.empty-history[data-v-6542a158]{padding:40px 0;text-align:center}.ant-statistic[data-v-6542a158]{text-align:center}.ant-statistic-title[data-v-6542a158]{color:#8c8c8c;font-size:14px}.ant-statistic-content[data-v-6542a158]{color:#262626;font-size:20px;font-weight:500}\n",document.head.appendChild(e);const w={name:"InventoryDetail",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","showHistory"],setup(t,{emit:e}){const a=o(!1),l=o([]);i((()=>t.visible),(e=>{e&&t.data.productId&&r()}));const r=async()=>{a.value=!0;try{const e=await A.productHistory({productId:t.data.productId,pageSize:3});if(e&&!1!==e.success){let t=(Array.isArray(e)?e:e.data||[]).map((t=>({...t,quantity:t.quantityChange,beforeStock:t.stockBefore,afterStock:t.stockAfter,operatorName:t.operationUserName||t.operatorName||"未知操作员",operationTime:n(t.operationTime)})));l.value=t.slice(0,3)}else l.value=[]}catch(e){l.value=[]}finally{a.value=!1}},n=t=>{if(!t)return"-";const e="string"==typeof t?new Date(t):t;return isNaN(e.getTime())?t:`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`};return{historyLoading:a,recentHistory:l,historyColumns:[{title:"操作类型",key:"operationType",width:100},{title:"数量变化",key:"quantity",width:120,align:"right"},{title:"操作前",key:"beforeStock",width:100,align:"right"},{title:"操作后",key:"afterStock",width:100,align:"right"},{title:"操作时间",key:"operationTime",width:160},{title:"操作人",key:"operatorName",width:100},{title:"备注",key:"remark",width:150,ellipsis:!0}],getPricingTypeName:t=>({NORMAL:"普通",WEIGHT:"称重",PIECE:"计件",VARIABLE:"变价"}[t]||t),getPricingTypeColor:t=>({NORMAL:"blue",WEIGHT:"orange",PIECE:"green",VARIABLE:"purple"}[t]||"default"),getBusinessModeColor:t=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"}[t]||"default"),getStockUnit:(t,e)=>{switch(t){case"WEIGHT":return"kg";case"PIECE":return"件";default:return e||"个"}},formatStock:(t,e)=>{if(!t)return"0";const a="WEIGHT"===e?3:0;return parseFloat(t).toFixed(a)},formatAmount:t=>t?parseFloat(t).toFixed(2):"0.00",formatDateTime:n,getStockValueStyle:(t,e)=>{const a=parseFloat(t)||0,o=parseFloat(e)||0;return a<=0?{color:"#ff4d4f"}:a<=o?{color:"#faad14"}:{color:"#52c41a"}},getStockStatusName:t=>({NORMAL:"库存正常",WARNING:"库存预警",OUT_OF_STOCK:"库存不足"}[t]||t),getStockStatusColor:t=>({NORMAL:"green",WARNING:"orange",OUT_OF_STOCK:"red"}[t]||"default"),getStockStatusDescription:(t,e,a)=>{const o=parseFloat(e)||0,i=parseFloat(a)||0;switch(t){case"WARNING":return`当前库存 ${o} 已低于预警值 ${i}，建议及时补货`;case"OUT_OF_STOCK":return"当前库存为0，请尽快补货";default:return"库存充足，无需补货"}},getOperationTypeName:t=>A.getOperationTypeName(t),getOperationTypeColor:t=>A.getOperationTypeColor(t),formatQuantityChange:(t,e)=>A.formatQuantityChange(t,e),getQuantityChangeClass:(t,e)=>{const a=parseFloat(t)||0;switch(e){case"IN":return"quantity-increase";case"OUT":case"SALE":return"quantity-decrease";case"ADJUST":return a>=0?"quantity-increase":"quantity-decrease";default:return""}},showAllHistory:()=>{e("update:visible",!1),e("showHistory",t.data)},handleCancel:()=>{e("update:visible",!1)}}}},I={class:"inventory-detail-content"},E={class:"product-name"},U={class:"stock-status-info"},F={class:"stock-status-desc"},H={key:0,class:"empty-history"};t("default",a(w,[["render",function(t,e,a,o,i,A){const w=m,z=k,q=S,M=T,R=h,j=v,L=_,G=C,P=b,B=x,W=N,$=O;return l(),r($,{visible:a.visible,title:"库存详情",width:1e3,footer:null,onCancel:o.handleCancel},{default:n((()=>[s("div",I,[c(M,{title:"商品信息",size:"small",style:{"margin-bottom":"16px"}},{default:n((()=>[c(q,{column:3,bordered:"",size:"small"},{default:n((()=>[c(w,{label:"商品名称"},{default:n((()=>[s("span",E,d(a.data.productName),1)])),_:1}),c(w,{label:"商品编码"},{default:n((()=>[u(d(a.data.productCode),1)])),_:1}),c(w,{label:"条形码"},{default:n((()=>[u(d(a.data.barcode||"-"),1)])),_:1}),c(w,{label:"商品分类"},{default:n((()=>[u(d(a.data.categoryName),1)])),_:1}),c(w,{label:"计价类型"},{default:n((()=>[c(z,{color:o.getPricingTypeColor(a.data.pricingType)},{default:n((()=>[u(d(o.getPricingTypeName(a.data.pricingType)),1)])),_:1},8,["color"])])),_:1}),c(w,{label:"商品单位"},{default:n((()=>[u(d(o.getStockUnit(a.data.pricingType,a.data.unit)),1)])),_:1}),c(w,{label:"规格"},{default:n((()=>[u(d(a.data.specification||"-"),1)])),_:1}),c(w,{label:"零售价"},{default:n((()=>[u(" ¥"+d(o.formatAmount(a.data.retailPrice)),1)])),_:1}),c(w,{label:"商品状态"},{default:n((()=>[c(z,{color:"Y"===a.data.status?"green":"red"},{default:n((()=>[u(d("Y"===a.data.status?"启用":"禁用"),1)])),_:1},8,["color"])])),_:1})])),_:1})])),_:1}),c(M,{title:"供应商信息",size:"small",style:{"margin-bottom":"16px"}},{default:n((()=>[c(q,{column:2,bordered:"",size:"small"},{default:n((()=>[c(w,{label:"供应商名称"},{default:n((()=>[u(d(a.data.supplierName),1)])),_:1}),c(w,{label:"供应商编码"},{default:n((()=>[u(d(a.data.supplierCode),1)])),_:1}),c(w,{label:"经营方式"},{default:n((()=>[c(z,{color:o.getBusinessModeColor(a.data.businessMode)},{default:n((()=>[u(d(a.data.businessModeName),1)])),_:1},8,["color"])])),_:1}),c(w,{label:"联系电话"},{default:n((()=>[u(d(a.data.supplierPhone||"-"),1)])),_:1})])),_:1})])),_:1}),c(M,{title:"库存信息",size:"small",style:{"margin-bottom":"16px"}},{default:n((()=>[c(L,{gutter:16},{default:n((()=>[c(j,{span:6},{default:n((()=>[c(R,{title:"当前库存",value:o.formatStock(a.data.currentStock,a.data.pricingType),suffix:o.getStockUnit(a.data.pricingType,a.data.unit),"value-style":o.getStockValueStyle(a.data.currentStock,a.data.minStock)},null,8,["value","suffix","value-style"])])),_:1}),c(j,{span:6},{default:n((()=>[c(R,{title:"预警值",value:o.formatStock(a.data.minStock,a.data.pricingType),suffix:o.getStockUnit(a.data.pricingType,a.data.unit),"value-style":{color:"#8c8c8c"}},null,8,["value","suffix"])])),_:1}),c(j,{span:6},{default:n((()=>[c(R,{title:"平均成本",value:a.data.avgCost,prefix:"¥",precision:2,"value-style":{color:"#1890ff"}},null,8,["value"])])),_:1}),c(j,{span:6},{default:n((()=>[c(R,{title:"库存价值",value:a.data.totalValue,prefix:"¥",precision:2,"value-style":{color:"#52c41a"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(M,{title:"库存状态",size:"small",style:{"margin-bottom":"16px"}},{default:n((()=>[s("div",U,[c(z,{color:o.getStockStatusColor(a.data.stockStatus),style:{"font-size":"14px",padding:"8px 16px"}},{default:n((()=>["WARNING"===a.data.stockStatus?(l(),r(G,{key:0,iconClass:"icon-opt-jinggao",style:{"margin-right":"8px"}})):p("",!0),"OUT_OF_STOCK"===a.data.stockStatus?(l(),r(G,{key:1,iconClass:"icon-opt-quehuo",style:{"margin-right":"8px"}})):p("",!0),u(" "+d(o.getStockStatusName(a.data.stockStatus)),1)])),_:1},8,["color"]),s("span",F,d(o.getStockStatusDescription(a.data.stockStatus,a.data.currentStock,a.data.minStock)),1)])])),_:1}),c(M,{title:"最近库存变动",size:"small"},{extra:n((()=>[c(P,{type:"link",size:"small",onClick:o.showAllHistory},{default:n((()=>e[0]||(e[0]=[u(" 查看全部历史 ")]))),_:1,__:[0]},8,["onClick"])])),default:n((()=>[c(B,{columns:o.historyColumns,"data-source":o.recentHistory,pagination:!1,size:"small",loading:o.historyLoading},{bodyCell:n((({column:t,record:e})=>["operationType"===t.key?(l(),r(z,{key:0,color:o.getOperationTypeColor(e.operationType)},{default:n((()=>[u(d(o.getOperationTypeName(e.operationType)),1)])),_:2},1032,["color"])):p("",!0),"quantity"===t.key?(l(),y("span",{key:1,class:g(o.getQuantityChangeClass(e.quantityChange,e.operationType))},d(o.formatQuantityChange(e.quantityChange,e.operationType))+" "+d(o.getStockUnit(a.data.pricingType,a.data.unit)),3)):p("",!0),"beforeStock"===t.key?(l(),y(f,{key:2},[u(d(o.formatStock(e.stockBefore,a.data.pricingType))+" "+d(o.getStockUnit(a.data.pricingType,a.data.unit)),1)],64)):p("",!0),"afterStock"===t.key?(l(),y(f,{key:3},[u(d(o.formatStock(e.stockAfter,a.data.pricingType))+" "+d(o.getStockUnit(a.data.pricingType,a.data.unit)),1)],64)):p("",!0),"operationTime"===t.key?(l(),y(f,{key:4},[u(d(e.operationTime),1)],64)):p("",!0),"operatorName"===t.key?(l(),y(f,{key:5},[u(d(e.operatorName||e.operationUserName||"-"),1)],64)):p("",!0),"remark"===t.key?(l(),y(f,{key:6},[u(d(e.remark||"-"),1)],64)):p("",!0)])),_:1},8,["columns","data-source","loading"]),o.recentHistory&&0!==o.recentHistory.length?p("",!0):(l(),y("div",H,[c(W,{description:"暂无库存变动记录"})]))])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-6542a158"]]))}}}));
