package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * 采购入库单模块常量
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
public interface PurchaseOrderConstants {

    /**
     * 采购入库单模块名称
     */
    String PURCHASE_ORDER_MODULE_NAME = "purchase-order";

    /**
     * 采购入库单模块前缀
     */
    String PURCHASE_ORDER_MODULE_PREFIX = "PURCHASE_ORDER";

    /**
     * 采购入库单状态：草稿
     */
    String PURCHASE_ORDER_STATUS_DRAFT = "DRAFT";

    /**
     * 采购入库单状态：已确认
     */
    String PURCHASE_ORDER_STATUS_CONFIRMED = "CONFIRMED";

    /**
     * 采购入库单状态：已完成
     */
    String PURCHASE_ORDER_STATUS_COMPLETED = "COMPLETED";

    /**
     * 默认采购入库单状态
     */
    String DEFAULT_PURCHASE_ORDER_STATUS = PURCHASE_ORDER_STATUS_DRAFT;

    /**
     * 付款方式：现金
     */
    String PAYMENT_METHOD_CASH = "CASH";

    /**
     * 付款方式：银行转账
     */
    String PAYMENT_METHOD_BANK_TRANSFER = "BANK_TRANSFER";

    /**
     * 付款方式：支票
     */
    String PAYMENT_METHOD_CHECK = "CHECK";

    /**
     * 付款方式：信用卡
     */
    String PAYMENT_METHOD_CREDIT_CARD = "CREDIT_CARD";

    /**
     * 付款方式：月结
     */
    String PAYMENT_METHOD_MONTHLY = "MONTHLY";

    /**
     * 默认付款方式
     */
    String DEFAULT_PAYMENT_METHOD = PAYMENT_METHOD_CASH;

    /**
     * 采购入库单号前缀
     */
    String PURCHASE_ORDER_NO_PREFIX = "PO";

    /**
     * 采购入库单号日期格式
     */
    String PURCHASE_ORDER_NO_DATE_FORMAT = "yyyyMMdd";

}