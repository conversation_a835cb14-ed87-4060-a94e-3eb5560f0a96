import{R as k,_ as h,r as x,o as O,k as c,a as d,c as p,b as t,h as m,d as e,w as s,g as _,t as y,f as w,a0 as A,v as C,G as I}from"./index-18a1ea24.js";/* empty css              */class P{static orgInfoStat(r){return k.getAndLoadData("/org/statInfo",r)}}const z={class:"card"},S={class:"card-header"},B={class:"card-header-title"},V={key:0},D={key:1},T={class:"card-body"},q={__name:"company-overview",props:{type:{type:String,default:"system",required:!0}},setup(i){const r=i,n=x({organizationNum:0,enterprisePersonNum:0,positionNum:0}),g=()=>{P.orgInfoStat().then(a=>{r.type==="system"?(n.value.organizationNum=a.organizationNum,n.value.enterprisePersonNum=a.enterprisePersonNum,n.value.positionNum=a.positionNum):r.type==="current"&&(n.value.organizationNum=a.currentDeptNum,n.value.enterprisePersonNum=a.currentCompanyPersonNum)})};return O(()=>{g()}),(a,o)=>{const v=c("ApartmentOutlined"),l=A,u=C,f=c("TeamOutlined"),N=c("AuditOutlined"),b=I;return d(),p("div",z,[t("div",S,[t("div",B,[i.type==="system"?(d(),p("span",V,"\u7CFB\u7EDF\u516C\u53F8\u6982\u51B5")):m("",!0),i.type==="current"?(d(),p("span",D,"\u5F53\u524D\u516C\u53F8\u6982\u51B5")):m("",!0)])]),t("div",T,[e(l,{bordered:!1,class:"function","body-style":{padding:"1em"}},{default:s(()=>[e(b,{gutter:16},{default:s(()=>[e(u,{lg:8,md:12,sm:12,xs:12},{default:s(()=>[e(l,{hoverable:"","body-style":{padding:0},class:"statistic card-bg1"},{default:s(()=>[t("div",null,[e(v,{class:"app-link-icon"}),o[0]||(o[0]=_("\xA0\xA0\xA0")),t("span",null,"\u7EC4\u7EC7\u673A\u6784\uFF1A"+y(n.value.organizationNum),1)])]),_:1})]),_:1}),e(u,{lg:8,md:12,sm:12,xs:12},{default:s(()=>[e(l,{hoverable:"","body-style":{padding:0},class:"statistic card-bg2"},{default:s(()=>[t("div",null,[e(f,{class:"app-link-icon"}),o[1]||(o[1]=_("\xA0\xA0\xA0")),t("span",null,"\u4EBA\u5458\u603B\u6570\uFF1A"+y(n.value.enterprisePersonNum),1)])]),_:1})]),_:1}),i.type==="system"?(d(),w(u,{key:0,lg:8,md:12,sm:12,xs:12},{default:s(()=>[e(l,{hoverable:"","body-style":{padding:0},class:"statistic card-bg3"},{default:s(()=>[t("div",null,[e(N,{class:"app-link-icon"}),o[2]||(o[2]=_("\xA0\xA0\xA0")),t("span",null,"\u804C\u4F4D\u603B\u6570\uFF1A"+y(n.value.positionNum),1)])]),_:1})]),_:1})):m("",!0)]),_:1})]),_:1})])])}}},G=h(q,[["__scopeId","data-v-17a49bbe"]]);export{G as default};
