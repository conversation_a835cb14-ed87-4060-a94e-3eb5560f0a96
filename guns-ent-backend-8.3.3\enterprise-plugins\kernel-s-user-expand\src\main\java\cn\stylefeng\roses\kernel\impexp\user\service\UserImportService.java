package cn.stylefeng.roses.kernel.impexp.user.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.auth.api.password.PasswordStoredEncryptApi;
import cn.stylefeng.roses.kernel.auth.api.pojo.password.SaltedEncryptResult;
import cn.stylefeng.roses.kernel.file.api.constants.FileConstants;
import cn.stylefeng.roses.kernel.impexp.user.enums.OperateTypeEnums;
import cn.stylefeng.roses.kernel.impexp.user.exceptions.UserImportExceptionEnum;
import cn.stylefeng.roses.kernel.impexp.user.factory.UserImportValidateFactory;
import cn.stylefeng.roses.kernel.impexp.user.pojo.EnsureImportUserRequest;
import cn.stylefeng.roses.kernel.impexp.user.pojo.UserExcelImportPreview;
import cn.stylefeng.roses.kernel.impexp.user.pojo.UserImportPreviewResult;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.UserExcelImportParse;
import cn.stylefeng.roses.kernel.office.api.OfficeExcelApi;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import cn.stylefeng.roses.kernel.rule.util.ResponseRenderUtil;
import cn.stylefeng.roses.kernel.sys.api.SysRoleServiceApi;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserRole;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserOrgService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserRoleService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户导入相关的业务
 *
 * <AUTHOR>
 * @since 2024-02-04 14:24
 */
@Service
@Slf4j
public class UserImportService {

    @Resource
    private OfficeExcelApi officeExcelApi;

    @Resource
    private PasswordStoredEncryptApi passwordStoredEncryptApi;

    @Resource
    private SysRoleServiceApi sysRoleServiceApi;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysUserRoleService sysUserRoleService;

    @Resource
    private SysUserOrgService sysUserOrgService;

    /**
     * 下载导入的Excel模板
     *
     * <AUTHOR>
     * @since 2024-02-04 14:25
     */
    public void downloadImportTemplate() {
        InputStream stream = ResourceUtil.getStream("user-import-template.xlsx");

        FastByteArrayOutputStream read = null;
        ServletOutputStream outputStream = null;
        try {
            read = IoUtil.read(stream);
            outputStream = HttpServletUtil.getResponse().getOutputStream();

            // 设置excel文件流
            ResponseRenderUtil.setRenderExcelHeader(HttpServletUtil.getResponse(), "用户导入模板.xlsx");

            read.writeTo(outputStream);
        } catch (IOException e) {
            log.error("获取导入的Excel模板失败！", e);
        } finally {
            IoUtil.close(read);
            IoUtil.close(outputStream);
        }
    }

    /**
     * 获取上传的用户的excel数据，并返回预览数据
     *
     * <AUTHOR>
     * @since 2024-02-04 14:42
     */
    public UserImportPreviewResult uploadAndGetPreviewData(@RequestPart("file") MultipartFile file) {

        // 1. 先解析Excel数据，转换为实体类
        List<UserExcelImportParse> userExcelImportParses = null;
        try {
            userExcelImportParses = officeExcelApi.easyReadToList(file.getInputStream(), 2, UserExcelImportParse.class);
        } catch (IOException e) {
            throw new ServiceException(UserImportExceptionEnum.PARSE_EXCEL_ERROR);
        }

        // 2. 开始校验数据，返回给前端校验后的结果
        List<UserExcelImportPreview> validateResult = UserImportValidateFactory.createValidateResult(userExcelImportParses);

        // 3. 判断里边是否有错误数据，如果有错误数据，则设置标识为false
        String jsonString = JSON.toJSONString(validateResult);
        boolean hasError = jsonString.contains("\"validateResult\":false");

        UserImportPreviewResult userImportPreviewResult = new UserImportPreviewResult();
        userImportPreviewResult.setTotalSuccess(!hasError);
        userImportPreviewResult.setPreviewData(validateResult);
        return userImportPreviewResult;
    }

    /**
     * 确认导入用户
     *
     * <AUTHOR>
     * @since 2024/2/10 17:47
     */
    public void ensureImportUser(List<EnsureImportUserRequest> ensureImportUserRequests) {

        if (ObjectUtil.isEmpty(ensureImportUserRequests)) {
            return;
        }

        // 针对新增的用户进行业务处理
        List<EnsureImportUserRequest> needToAdd = ensureImportUserRequests.stream()
                .filter(i -> OperateTypeEnums.ADD.getCode().equals(i.getOperateType())).collect(Collectors.toList());
        this.doAddUserBatch(needToAdd);

        // 针对修改的用户进行业务处理
        List<EnsureImportUserRequest> needToUpdate = ensureImportUserRequests.stream()
                .filter(i -> OperateTypeEnums.EDIT.getCode().equals(i.getOperateType())).collect(Collectors.toList());
        this.doUpdateUserBatch(needToUpdate);
    }

    /**
     * 执行批量新增用户的逻辑
     *
     * <AUTHOR>
     * @since 2024/2/12 21:48
     */
    private void doAddUserBatch(List<EnsureImportUserRequest> needToAdd) {

        if (ObjectUtil.isEmpty(needToAdd)) {
            return;
        }

        // 先生成一个用户的id
        for (EnsureImportUserRequest ensureImportUserRequest : needToAdd) {
            ensureImportUserRequest.setUserId(IdWorker.getId());
        }

        // 将用户请求bean转化为sysUser对象
        List<SysUser> sysUsers = BeanUtil.copyToList(needToAdd, SysUser.class, CopyOptions.create().ignoreError());

        for (SysUser sysUser : sysUsers) {
            // 将密码加密存储到库中
            SaltedEncryptResult saltedEncryptResult = passwordStoredEncryptApi.encryptWithSalt(sysUser.getPassword());
            sysUser.setPassword(saltedEncryptResult.getEncryptPassword());
            sysUser.setPasswordSalt(saltedEncryptResult.getPasswordSalt());

            // 设置用户默认头像
            sysUser.setAvatar(FileConstants.DEFAULT_AVATAR_FILE_ID);

            // 设置非管理员
            sysUser.setSuperAdminFlag(YesOrNotEnum.N.getCode());
        }

        // 给用户绑定默认角色
        List<SysUserRole> sysUserRoles = new ArrayList<>();
        // 添加一个用户的默认角色
        Long defaultRoleId = sysRoleServiceApi.getDefaultRoleId();
        for (SysUser sysUser : sysUsers) {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(sysUser.getUserId());
            sysUserRole.setRoleId(defaultRoleId);
            sysUserRoles.add(sysUserRole);
        }

        // 添加用户的任职信息，根据请求的参数
        List<SysUserOrg> sysUserOrgList = new ArrayList<>();
        for (EnsureImportUserRequest ensureImportUserRequest : needToAdd) {
            Long userId = ensureImportUserRequest.getUserId();
            Long orgId = ensureImportUserRequest.getOrgId();
            Long positionId = ensureImportUserRequest.getPositionId();

            SysUserOrg sysUserOrg = new SysUserOrg();
            sysUserOrg.setUserId(userId);
            sysUserOrg.setOrgId(orgId);
            sysUserOrg.setPositionId(positionId);
            sysUserOrg.setMainFlag(YesOrNotEnum.Y.getCode());
            sysUserOrg.setStatusFlag(StatusEnum.ENABLE.getCode());
            sysUserOrgList.add(sysUserOrg);
        }

        // 批量保存用户
        this.sysUserService.quickBatchSaveUser(sysUsers);

        // 批量保存用户机构
        this.sysUserRoleService.quickBatchSaveUserRole(sysUserRoles);

        // 批量保存用户角色
        this.sysUserOrgService.quickBatchSaveUserOrg(sysUserOrgList);
    }

    /**
     * 执行批量编辑用户
     *
     * <AUTHOR>
     * @since 2024/2/12 23:46
     */
    private void doUpdateUserBatch(List<EnsureImportUserRequest> needToUpdate) {
        if (ObjectUtil.isEmpty(needToUpdate)) {
            return;
        }

        // 将用户请求bean转化为sysUser对象
        List<SysUser> sysUsers = BeanUtil.copyToList(needToUpdate, SysUser.class, CopyOptions.create().ignoreError());

        // 将密码加密存储到库中
        for (SysUser sysUser : sysUsers) {

            if (ObjectUtil.isEmpty(sysUser.getPassword())) {
                continue;
            }

            SaltedEncryptResult saltedEncryptResult = passwordStoredEncryptApi.encryptWithSalt(sysUser.getPassword());
            sysUser.setPassword(saltedEncryptResult.getEncryptPassword());
            sysUser.setPasswordSalt(saltedEncryptResult.getPasswordSalt());
        }

        // 批量更新用户信息
        this.sysUserService.updateBatchById(sysUsers);
    }

}
