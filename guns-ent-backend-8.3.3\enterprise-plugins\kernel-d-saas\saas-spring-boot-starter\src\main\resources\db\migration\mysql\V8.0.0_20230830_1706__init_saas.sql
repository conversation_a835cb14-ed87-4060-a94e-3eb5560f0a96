CREATE TABLE `ent_tenant`  (
  `tenant_id` bigint NOT NULL COMMENT '主键id',
  `tenant_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户唯一标识',
  `tenant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户名称',
  `tenant_logo` bigint NULL DEFAULT NULL COMMENT '租户logo，存储文件id',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司地址',
  `company_social_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注册邮箱',
  `safe_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '安全手机（注册时的手机号）',
  `status_flag` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `active_date` datetime(0) NULL DEFAULT NULL COMMENT '租户开通时间',
  `expire_date` datetime(0) NULL DEFAULT NULL COMMENT '租户到期时间',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`tenant_id`) USING BTREE,
  UNIQUE INDEX `garbage_tenant_tenant_code_uindex`(`tenant_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户信息' ROW_FORMAT = Dynamic;

-- 初始化默认租户
INSERT INTO `ent_tenant`(`tenant_id`, `tenant_code`, `tenant_name`, `tenant_logo`, `company_name`, `company_address`, `company_social_code`, `email`, `safe_phone`, `status_flag`, `active_date`, `expire_date`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1, 'root', '根管理端', 10000, NULL, NULL, NULL, '<EMAIL>', '1001', 1, '2023-01-01 00:00:00', '2099-01-01 00:00:00', NULL, 0, 'N', NULL, NULL, NULL, NULL);

-- 初始化应用和菜单
INSERT INTO `sys_app`(`app_id`, `app_name`, `app_code`, `app_icon`, `status_flag`, `app_sort`, `remark`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1701801589128577026, '租户中心', 'tenant_center', 1701802464433684482, 1, 1000.00, '针对同一个功能模块，使用不同维度的数据进行管理', NULL, 1, 'N', '2023-09-13 11:34:51', 1339550467939639299, '2023-09-13 11:36:19', 1339550467939639299);
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1701802336574521345, -1, '[-1],', '租户管理', 'TENANT', 1701801589128577026, 100.00, 1, NULL, 10, '/tenant', '/system/backend/tenant/index', 'icon-menu-zuhuguanli', NULL, NULL, 'Y', NULL, 0, 'N', '2023-09-13 11:37:50', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_info`(`file_id`, `file_code`, `file_version`, `file_status`, `file_location`, `file_bucket`, `file_origin_name`, `file_suffix`, `file_size_kb`, `file_size_info`, `file_object_name`, `file_path`, `secret_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1701802464433684482, 1701806078527803395, 1, '1', 5, 'defaultBucket', '租户.png', 'png', 4, '3.78 KB', '1701806078527803393.png', NULL, 'N', 'N', '2023-09-13 11:52:42', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_storage`(`file_id`, `file_bytes`) VALUES (1701802464433684482, 0x89504E470D0A1A0A0000000D49484452000000C8000000C80806000000AD58AE9E000000017352474200AECE1CE900000EDC49444154785EED9DBF8FDCC615C767C83BC851E4C66500230AE052173855A04EAA52A649D44AF70F18D01D8CA43B6D97C03809701FDCA955D2A44CE575A532064EA5012F70414A3756142B3A7202727F68776F77F9387CF3DE0CF9BDCA86869CE1F7BD0FDF9B99375C6BF00705A0C056052CB481025060BB020004DE010576280040E01E500080C007A0809F0288207EBAE1AA81280040940DFDE93377BB28CB1363EDEDF5A1B8C28E5E7D6EC7CA431C74F70044C9FC77BE70F76CE6CE8C31D7C0581BD2C4183BBA38B6E74A431D74B70044D8FC2DC0581F194011B655D51D001112FDCE6971628D7D4488184D239A38E3CE5F1DE7A3A686F8F7EE0A0090EE1AEEBC032318D7220A40096C3C44907002CFC07812AE87C59D1151028A8C08C2286EB5227555960FADB1126020A230DA6EDBAD000883C8CA600014061B029000224606C64650F6B2ECF9378FED24C0E30FE29688201E668E1C0C80E26153441006D11203E31A28C6B8719E65234414BA33208210B44A1C0C8042B031228887488B3AA9E9065F0FFFDCB92BB3E7A8F7DA6E5A44900DDAF41F8CF5870628882084F77F0D867367C6997B84E6DC4D26AEB4877B7B66A23D064494F7A6450431C6A88261CD38B7F6707DE2AC1CC55018396364D08074A8ACED1C3D9C714F287B14CA0B048307659080042C206C04870AC6FA8D004AA3B4411A0C0A106D30384AD4B541195A05F1200051042358A52D40091230AEDDB4D78008969C6F2CEFE088184D6EB0040AC761ACA6EED49EB3EDC0B8DAF70E90A1BE59010A1712ABF7E90D20430563D364BE28AB7D1C77C270BCB7ADD7054B29DB0E84AB7DF280008CCDAE30DD4731F78C750F15363E6B5028CBD85C8E1CEA3EC902A20D464A9FE23938758F008A1F42C901A20D46550E926A298662C5C024D552FB64005105634B3988DF3B49FF2AC532961A94942A88A30744D198C677D75B1F01DA0834B535268D0AE26801D1345EDFC188AB8C256E50A20344314FAE2386C4E61EEDFD2EDF4A358D35A62EF78F6D7E170D208A95B5BD5BBBEF8A96362831AD10AA030230DEBBF3275FBA1BD5FF7DFB997DDBD5C939AE07288A1FAFEE6301A1AF5356607CF4FA875BEFF2BC0664BF28DE7E7FEBC3D7118232B87A2FF10802305623C63218EB80C508CAD0CA58C4001942652D3582AC478CA6EBA20545B18C456A31252820C861575DBF2D18B147946A7CDA652CA1410906482D9CA97F624CFA2FBA73D45DC14801947AB12577275A8591A1400902C87493CF7D274C4674EBE8DC60A4008AD23ED624CFECFD109F540D02C8C1D3F22BB13749847552A1C1480694EAD77BA5BE4A69CDF8E228BBCFFD52660764B6AFF115F740D7EF176339883418A98022F5A342AEB4F7B977E2D90139382DABD4AAE9A78DBDF90118CDD2C5B6EA558D5868C16672719CFDA259217A0B5640422EE5C65827A51D319ACC3C4450B8FD84159083D3D23519ADEDBF733F70DBFE37B58F1D8C5452AF22CC1C8575C2CE0648C0B94734E79B530323565042A75B9C7391140099DB590D94D4C1880594D060CC9F73A8802C40913ADFDC3730B44091020380AC5A38D8F9E6BE8321058A34180064EB0C9BE7D8E6D0C008058AE671E9EA99869E62ED5A88F22A37193A185CA02895995CF30700D2BC564B2A580418BB85A4EEA3289E0ADDF80000A41990A509BD1D5D1CDBF3E54B00065DC0AAE53650140FBFED7C0000D2CEBE55EB7A89F8C7FDEC4FBB4EF0B5BFEDB0AE9883F2C1BBF28F765A8418ACA4A88BB200C45FBD4BEBDC8BBCF8F1A9FF2D867B65917F70E4AC3D165660D216C41E01620F6BB1E58F6E0214A29717D9FEC7C6E6BFD700A38AFAA6CCBEB6996B551DDE1B40961F44E9E82640D9028A2618CBDFC5F22961EA2520733B292D15D6A01857FC352FDF5D125FAEBD6CA609C6A62F2B0290CFED7893A7296D365D1A57BEB4AE7C3A3450D4C0B066EC0A3BDA76D009806C016425A284298BDE1501A6A094F98BDCBD79D9CB50317B284D30726B0F9BCE91039006409641913ABAB902842B5FF411940A0C67B32363B307922F80B627420108111075506CF9D25EE5A7A9479454C098DB1B80B404441D14632E6D611FA7064A0D469E3D332EBB2B1D31BA7CB30A807802120528C63ECDAFDEBC9074B8B67D15F6E65D97BB67C6988FDB5EDBA1FDA533EE2F5DC04004612E4BFEE517FFFBF56C43ABCAA9459D21C6DDF9D9AEB79A16FFFCC34FFFDD01B0C5A588201D23C85CC95FFDF93F3FABFE7B695546CD39381CC3F71EDA60CCC70D40D62CA84DFA1C90F9B06A50B2FDBBCEB8A3214414A53AA9AD950800247240AE81628B07C213D4E0652C6A7B18D5424543D12700490490E561167B371F38255038CB5854C1202E4C00900401598E2A0A4B9E9DEBBD54C168B9B40D401206640514F9DDE4D6F55E6A6074D81C05203D00240A5076D47BA98251948FBB146C02901E01B232A1D7381CB456EFA5550E629D3BE59A2B01901E02A20E8A3197C696FF125E6D339C60601F64CB0E566CFB20BE1B6D2BAB5E7AC74D3986DF788F0A8C50E7F311417A1C41D63D4B6D2ED0E8E25E0D1AF730BCEEBA7611001910201B522FE932160E9F15010329D68052AC6D5EA958EFE5038A28180004802C1450ACF7A280A20206000120D7145880225FC6B2C91AAA60001000B2F3EDAD54EF558DE9D212EBA428E1A76B1B4CD20738496FE33482475CA33C020C400008899760BBE21DEAA44803EFD808800090562EC4064A0546C73AA95603F76C0C40008897EB786F3A56F55A097DF1118000102F40AA8B665F1AF95B9B1BD8C2FE2EA54F0D011000D2C6BF57DA0210BA74DA357E963ED4DD2DB51F64FDA30D5CCF15E23E0084AEAAB65F0110BAADD85A0210BA940084F9BB5874E9F55A0210BAF600048090BC059374924C75A35EFFC2145D86D5969883F82A17E63AAC626115CBDBB39062D1A5438A85148BE42D48B1483221C5DA2613522CBA0349B4448A8514CBDBCF9062D1A5438A85148BE42D48B1483221C5428A457714CD9648B1906279FB1F522CBA7448B1906291BC0529164926A45848B1E88EA2D9122916522C6FFF438A45970E2916522C92B720C522C984140B2916DD51345B22C5428AE5ED7F48B1E8D221C5428A45F216A4582499906221C5A23B8A664BA45848B1BCFD0F29165D3AA45848B148DE82148B2413522CA4587447D16C89140B2996B7FF21C5A24B87140B2916C95B90629164428A85148BEE289A2D916221C5F2F63FA45874E9906221C522790B522C924C48B19062D11D45B325522CA458DEFE87148B2E1D522CA458246F418A4592092916522CBAA368B6448A8514CBDBFF9062D1A5438A85148BE42D48B1483221C5DA26D3275FBA1B1FBDFEE1D6BB3CBF419752A7659F23C87E51BCFDFED687AFBFFDCCBEE5501711842982CC8D9102287D04841B8CB93D010833202980D2274042810140987F0928A5D4AB0F808406038008011263444919102930008830203181922220D260001025406200252540B4C00020CA806882920220DA6000904800D1002566406201038044068824283102121B180064AAC0C41977FEEA381F71ECBA72DE23E486634C80C40A4665CB3BA7C58935F69131E6761BDBBAD2DE7FC5B4BF66DB74BCABADCF8EE7D2FD06054A0C802400C6135FDFEC2320732D06018A2620B182F1E93377FBAA2C1F5A63BDC1983B519F015901652FCB9E7FF3D84E7CDF2421AEE348BD3400190218510352BD010AE7CE8C33F7181DB38E287D03451290218151FB9D35E38BA3EC3E970FB2CD41AA01D59094EE3BAEC12DCF518C71E33CCB467D882812800C0E8C99B370A65753DE98FF0E4E8B33335D7908F137A9407165F69C6B95826B906D52AF90800C158CA91DDDF9C5717EC865D32080048C226BCFEDCE530525042031835194E549C097E6C22F2E8E33F6173EFB0DABD11E9CBA47C6B8334E92B7DECB9AB12BEC28A588C20908C0987B863DBC38B6E7DC3E1704904013F6A6679FB8D21EA6000A07205183C1BF58B3DBF6CC13F3E5CE8200329FB073AD6B3791B1F6EF1363EC28C4DBA4E538569A2FCF51BA00122B18B38DE22A6B68B5EBDD4553894A8C6080CC1F7C6903A875C940ECE2F98CAF02E5E67F8BDFB8BDECEF6DAEB757E56FDFFC24FF07D7C710DAF4BDABAD6F3948C7FEC5369483030250AEBB824F590EF7F2654707F5AE93EAD8AF18188B994DC701B7BE7CBACA556D26BA93BE8563AA182903328B189DCB41A85ACDDA8983A106C87244A941B1EE21F3EE7B93F66A62CF07961A209C75524DC6599F4F6A577B8BA558BB84A997859540D128634905104D30625968890290393C430125764000C6FBD7795480ACA45FD26BE9C6D4652C12F55EB102A209468C7B58953F4609C8EA3C45A64C6129050C5EEF151B206A605833CEAD3D8CAD0075793A103520CAA0D4C56F21EABD620164BAA228FE02AA4BD26307437D15ABE56A46DD5CF34DC759EFA50D881618CEB8271A8B223EBE9624201B361DE5D7E319EABDB40051AA913329829134203180D26519521A10A53AA91A8C18BF58D326A2243107697A20B5D4CBF3B3455280F4BD4EAAC92F38FEBD1780C41051DAECF88606046070A031BD47AF00D9004A9415C4A100195A9D141F06DBEFD44B406207851390D4D24B09A7E6ECA3D780ACEEA3E85510AF2F6D720002303831186804597F6CED52FB39285D0001183260F46299B78B545A859155BD9731D9D7ED3F6A610F9D297FCEF169CE96BA457984B9E53378371F448AB54B1D2550BC0D2678E1A0C1187C04D9987EC957100BFA3BB9AB28BF0E431E3D73C3C14790CDF31485023E66C37ADC0E606C100D806CF124AD823E0FC7EE76494295B5DD1ED4EF6A00D2A05B5F4149B980D0CFD5FDAE022044DD1497578923A4350318349D30496FA7D3A275AAA0000C3F832382F8E9A67778ABE578FB5072DEF291599B03908E72461A51D4BFFDD551D6682E07204CA688041480C1644FCC4198859CDF4EE963DD0023903D114102092B040AC008643F4490C0C22E4794001FEB061842F6430411127A516ADFED1BC40043C85E8820C2422F77E751418CCA5A257B218228095F754B00056028DAA7EA1A80281BA0EABE9ED05FADFEB6DFDE9E99C4FCCDDA0864131902001191199DA4AA000049D57218B78802004444667492AA02002455CB61DC220A00101199D149AA0A0090542D87718B28004044644627A92A004052B51CC62DA200001191199DA4AA000049D57218B78802004444667492AA02002455CB61DC220A00101199D149AA0A0090542D87718B28004044644627A92A004052B51CC62DA200001191199DA4AA000049D57218B78802FF0772743FD7F9171A170000000049454E44AE426082);