System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-da493ce1.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var a,u,n,t,d,o,i,r,s,v,c,p,y,f;return{setters:[e=>{a=e.o,u=e.r,n=e.X,t=e.a,d=e.c,o=e.d,i=e.ah,r=e.w,s=e.f,v=e.h,c=e.l,p=e.bf,y=e.a0},null,e=>{f=e._},null,null,null,null,null],execute:function(){const l={class:"wh100"},g={__name:"index",props:{value:{type:[String,Array],default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"请选择"},width:{type:String,default:"100%"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null},normal:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(e,{emit:y}){const g=e,m=y;a((()=>{g.value?h.value=g.normal?g.value:JSON.parse(g.value):h.value=[],O()}));const h=u([]),b=u(""),x=u(!1),j=()=>{x.value=!0},w=e=>{h.value=null==e?void 0:e.map((e=>({id:e.id,name:e.name,type:e.type}))),O(),S()},O=()=>{b.value=h.value.map((e=>{let l="";return"1"==e.type&&(l="(公司)"),"2"==e.type&&(l="(部门)"),"3"==e.type&&(l="(人员)"),e.name+l})).join("；")},S=()=>{let e=[...h.value];var l;g.normal||(e=0==(null===(l=e)||void 0===l?void 0:l.length)?"":JSON.stringify(e)),m("update:value",e),m("onChange",g.record),_()},_=async()=>{var e;!g.normal&&null!==(e=g.formRef)&&void 0!==e&&e.validateFields&&await g.formRef.validateFields([g.record.fieldCode])};return n((()=>g.value),(e=>{g.value?h.value=g.normal?h.value:JSON.parse(g.value):h.value=[],O()}),{deep:!0}),(a,u)=>{const n=c,y=f,m=p;return t(),d("div",l,[o(n,{value:b.value,"onUpdate:value":u[0]||(u[0]=e=>b.value=e),disabled:g.readonly||g.disabled,class:"w-full",style:i({width:g.width}),placeholder:e.placeholder,onFocus:j},null,8,["value","disabled","style","placeholder"]),o(m,null,{default:r((()=>[x.value?(t(),s(y,{key:0,visible:x.value,"onUpdate:visible":u[1]||(u[1]=e=>x.value=e),list:h.value,title:"机构人员选择",onDone:w},null,8,["visible","list"])):v("",!0)])),_:1})])}}},m={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const l=u(""),a=u(!1),n=u(!1),i=u("请选择"),s=()=>{console.log(l.value)};return(e,u)=>{const v=g,c=y;return t(),d("div",m,[o(c,{title:"机构人员选择(input)",bordered:!1},{default:r((()=>[o(v,{value:l.value,"onUpdate:value":u[0]||(u[0]=e=>l.value=e),disabled:a.value,readonly:n.value,placeholder:i.value,onOnChange:s,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])])),_:1})])}}})}}}));
