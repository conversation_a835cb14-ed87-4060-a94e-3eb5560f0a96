import{_ as b,a as c,c as m,b as e,f as _,t as a,d as f,w as v,g as u,I as h,U as p}from"./index-18a1ea24.js";/* empty css              */import{A as g}from"./formatter-5a06da9d.js";import"./constants-2fa70699.js";const y={class:"member-info-display"},I={class:"member-card"},N={class:"member-basic"},x={class:"member-avatar-large"},M=["src","alt"],k={class:"member-details"},V={class:"member-name-large"},B={class:"member-level"},C={class:"member-phone"},w={class:"member-card-no"},A={class:"member-balance-points"},P={class:"balance-item"},j={class:"balance-value"},F={class:"points-item"},O={class:"points-value"},S=Object.assign({name:"MemberInfo"},{__name:"MemberInfo",props:{member:{type:Object,required:!0}},setup(t){const n=s=>g.formatCurrency(s,{showSymbol:!1}),r=s=>s?s.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):"",l=s=>({\u666E\u901A\u4F1A\u5458:"default",\u94F6\u5361\u4F1A\u5458:"#c0c0c0",\u91D1\u5361\u4F1A\u5458:"#ffd700",\u94BB\u77F3\u4F1A\u5458:"#b9f2ff",VIP\u4F1A\u5458:"#f50"})[s]||"blue";return(s,o)=>{const i=h,d=p;return c(),m("div",y,[e("div",I,[e("div",N,[e("div",x,[t.member.avatar?(c(),m("img",{key:0,src:t.member.avatar,alt:t.member.memberName},null,8,M)):(c(),_(i,{key:1,iconClass:"icon-user-default"}))]),e("div",k,[e("div",V,a(t.member.memberName),1),e("div",B,[f(d,{color:l(t.member.levelName)},{default:v(()=>[u(a(t.member.levelName||"VIP\u4F1A\u5458"),1)]),_:1},8,["color"])]),e("div",C,a(r(t.member.phone)),1),e("div",w,"\u5361\u53F7: "+a(t.member.cardNo),1)])]),e("div",A,[e("div",P,[o[0]||(o[0]=e("div",{class:"balance-label"},"\u8D26\u6237\u4F59\u989D",-1)),e("div",j,"\uFFE5"+a(n(t.member.balance||0)),1)]),e("div",F,[o[1]||(o[1]=e("div",{class:"points-label"},"\u53EF\u7528\u79EF\u5206",-1)),e("div",O,a(t.member.points||0),1)])])])])}}}),L=b(S,[["__scopeId","data-v-df3ec665"]]);export{L as default};
