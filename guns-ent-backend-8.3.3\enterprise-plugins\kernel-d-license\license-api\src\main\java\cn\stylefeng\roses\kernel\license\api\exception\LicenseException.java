package cn.stylefeng.roses.kernel.license.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.license.api.constants.LicenseConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * license异常
 *
 * <AUTHOR>
 * @date 2021/7/8 11:38
 */
public class LicenseException extends ServiceException {

    public LicenseException(AbstractExceptionEnum exception, Object... params) {
        super(LicenseConstants.LICENSE_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public LicenseException(AbstractExceptionEnum exception) {
        super(LicenseConstants.LICENSE_MODULE_NAME, exception);
    }

}
