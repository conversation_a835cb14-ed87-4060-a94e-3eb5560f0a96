import{R as l,r as d,o as _,a as y,f as F,w as L,d as h,m as I,M as S}from"./index-18a1ea24.js";import A from"./sso-client-form-e7a76767.js";import{a as D}from"./FileApi-418f4d35.js";class f{static findPage(e){return l.getAndLoadData("/ssoClient/page",e)}static add(e){return l.post("/ssoClient/add",e)}static edit(e){return l.post("/ssoClient/edit",e)}static delete(e){return l.post("/ssoClient/delete",e)}static batchDelete(e){return l.post("/ssoClient/batchDelete",e)}static detail(e){return l.getAndLoadData("/ssoClient/detail",e)}static updateStatus(e){return l.post("/ssoClient/updateStatus",e)}}const R={__name:"sso-client-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(m,{emit:e}){const r=m,p=e,i=d(!1),n=d(!1),s=d({clientSort:1e3,loginPageType:2,unifiedLogoutFlag:"Y"}),u=d(null);_(()=>{r.data?(n.value=!0,g()):n.value=!1});const g=()=>{f.detail({clientId:r.data.clientId}).then(a=>{s.value=Object.assign({},a),s.value.clientLogoFileId&&b(s.value.clientLogoFileId,"clientLogoFileIdFileList")})},b=(a,t)=>{D.getAntdVInfoBatch({fileIdList:[a]}).then(o=>{u.value[t]=o.data})},c=a=>{p("update:visible",a)},C=async()=>{u.value.$refs.formRef.validate().then(async a=>{if(a){i.value=!0;let t=null;n.value?t=f.edit(s.value):t=f.add(s.value),t.then(async o=>{i.value=!1,I.success(o.message),c(!1),p("done")}).catch(()=>{i.value=!1})}})};return(a,t)=>{const o=S;return y(),F(o,{width:900,maskClosable:!1,visible:r.visible,"confirm-loading":i.value,forceRender:!0,title:n.value?"\u7F16\u8F91\u7B2C\u4E09\u65B9\u4E1A\u52A1\u7CFB\u7EDF\u914D\u7F6E":"\u65B0\u589E\u7B2C\u4E09\u65B9\u4E1A\u52A1\u7CFB\u7EDF\u914D\u7F6E","body-style":{paddingBottom:"8px"},"onUpdate:visible":c,onOk:C,class:"common-modal",onClose:t[1]||(t[1]=v=>c(!1))},{default:L(()=>[h(A,{form:s.value,"onUpdate:form":t[0]||(t[0]=v=>s.value=v),ref_key:"ssoClientFormRef",ref:u},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}},j=Object.freeze(Object.defineProperty({__proto__:null,default:R},Symbol.toStringTag,{value:"Module"}));export{f as S,R as _,j as s};
