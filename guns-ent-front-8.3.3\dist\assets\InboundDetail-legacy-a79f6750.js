System.register(["./index-legacy-ee1db0c7.js","./index-legacy-9e3f6b64.js","./index-legacy-510bfbb8.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./PurchaseApi-legacy-77810512.js"],(function(t,a){"use strict";var e,l,o,n,i,d,s,r,u,c,p,m,f,g,b,y,C,v,x,E,_,h,k,A,N,T;return{setters:[t=>{e=t._,l=t.L,o=t.a,n=t.f,i=t.w,d=t.b,s=t.d,r=t.t,u=t.g,c=t.c,p=t.h,m=t.F,f=t.e,g=t.Y,b=t.U,y=t.Z,C=t.a0,v=t.i,x=t.a4,E=t.v,_=t.G,h=t.I,k=t.b8,A=t.b9,N=t.M},null,null,null,null,null,null,null,null,t=>{T=t.P}],execute:function(){var a=document.createElement("style");a.textContent=".inbound-detail-content[data-v-987baa8b]{max-height:70vh;overflow-y:auto}.order-no[data-v-987baa8b]{font-weight:500;color:#1890ff}.product-info[data-v-987baa8b]{text-align:left}.product-name[data-v-987baa8b]{font-weight:500;color:#262626;margin-bottom:4px}.product-details[data-v-987baa8b]{font-size:12px;color:#8c8c8c}.product-code[data-v-987baa8b]{margin-right:8px}.product-spec[data-v-987baa8b]{margin-left:8px}.total-price[data-v-987baa8b]{font-weight:500;color:#1890ff}.payment-account[data-v-987baa8b]{font-family:Courier New,monospace;color:#262626;font-weight:500}.amount-text[data-v-987baa8b]{font-weight:500;color:#1890ff}.text-muted[data-v-987baa8b]{color:#8c8c8c;font-style:italic}.operation-log[data-v-987baa8b]{padding:8px 0}.operation-title[data-v-987baa8b]{display:flex;justify-content:space-between;align-items:center;margin-bottom:4px}.operation-name[data-v-987baa8b]{font-weight:500;color:#262626}.operation-time[data-v-987baa8b]{font-size:12px;color:#8c8c8c}.operation-user[data-v-987baa8b],.operation-remark[data-v-987baa8b]{font-size:12px;color:#8c8c8c;margin-bottom:2px}.ant-statistic[data-v-987baa8b]{text-align:center}.ant-statistic-title[data-v-987baa8b]{color:#8c8c8c;font-size:14px}.ant-statistic-content[data-v-987baa8b]{color:#262626;font-size:20px;font-weight:500}\n",document.head.appendChild(a);const P={name:"InboundDetail",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(t,{emit:a}){const e=l((()=>t.data.detailList?t.data.detailList.length:0)),o=l((()=>t.data.detailList&&0!==t.data.detailList.length?t.data.detailList.reduce(((t,a)=>t+(parseFloat(a.quantity)||0)),0):0)),n=l((()=>t.data.detailList&&0!==t.data.detailList.length?t.data.detailList.reduce(((t,a)=>t+(parseFloat(a.totalPrice)||0)),0):0)),i=l((()=>0===o.value?0:n.value/o.value)),d=l((()=>t.data.paymentMethod||t.data.paymentAccount||"COMPLETED"===t.data.status));return{detailColumns:[{title:"商品信息",key:"productInfo",width:250},{title:"数量",key:"quantity",width:120,align:"center"},{title:"单价",key:"unitPrice",width:100,align:"right"},{title:"总价",key:"totalPrice",width:120,align:"right"},{title:"备注",key:"remark",width:200}],productCount:e,totalQuantity:o,totalAmount:n,averagePrice:i,showPaymentInfo:d,getStatusName:t=>T.getPurchaseStatusName(t),getStatusTagColor:t=>T.getStatusTagColor(t),getBusinessModeColor:t=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"}[t]||"default"),getPaymentMethodName:t=>({CASH:"现金",BANK_TRANSFER:"银行转账",CHECK:"支票",CREDIT_CARD:"信用卡",MONTHLY:"月结",ALIPAY:"支付宝",WECHAT:"微信支付",CREDIT:"赊账"}[t]||t),getPaymentMethodColor:t=>({CASH:"green",BANK_TRANSFER:"blue",CHECK:"orange",CREDIT_CARD:"purple",MONTHLY:"cyan",ALIPAY:"blue",WECHAT:"green",CREDIT:"red"}[t]||"default"),getPaymentStatusName:t=>({DRAFT:"待付款",CONFIRMED:"待付款",COMPLETED:"已付款"}[t]||"未知"),getPaymentStatusColor:t=>({DRAFT:"orange",CONFIRMED:"orange",COMPLETED:"green"}[t]||"default"),getQuantityUnit:t=>{switch(t.pricingType){case"WEIGHT":return"kg";case"PIECE":return"件";default:return t.unit||"个"}},formatAmount:t=>t?parseFloat(t).toFixed(2):"0.00",getOperationColor:t=>({CREATE:"blue",EDIT:"orange",CONFIRM:"green",RECEIVE:"purple",CANCEL:"red"}[t]||"default"),getOperationIcon:t=>({CREATE:"icon-opt-xinzeng",EDIT:"icon-opt-bianji",CONFIRM:"icon-opt-queren",RECEIVE:"icon-opt-rukudan",CANCEL:"icon-opt-quxiao"}[t]||"icon-opt-caozuo"),handleCancel:()=>{a("update:visible",!1)}}}},I={class:"inbound-detail-content"},M={class:"order-no"},L={key:1,class:"text-muted"},R={key:0,class:"payment-account"},w={key:1,class:"text-muted"},D={class:"amount-text"},O={class:"amount-text"},S={key:0,class:"product-info"},z={class:"product-name"},F={class:"product-details"},j={class:"product-code"},H={key:0,class:"product-spec"},q={key:3,class:"total-price"},U={class:"operation-log"},B={class:"operation-title"},Y={class:"operation-name"},K={class:"operation-time"},Q={class:"operation-user"},G={key:0,class:"operation-remark"};t("default",e(P,[["render",function(t,a,e,l,T,P){const V=g,W=b,J=y,Z=C,X=v,$=x,tt=E,at=_,et=h,lt=k,ot=A,nt=N;return o(),n(nt,{visible:e.visible,title:"入库单详情",width:1200,footer:null,onCancel:l.handleCancel},{default:i((()=>[d("div",I,[s(Z,{title:"基本信息",size:"small",style:{"margin-bottom":"16px"}},{default:i((()=>[s(J,{column:3,bordered:"",size:"small"},{default:i((()=>[s(V,{label:"入库单号"},{default:i((()=>[d("span",M,r(e.data.orderNo),1)])),_:1}),s(V,{label:"状态"},{default:i((()=>[s(W,{color:l.getStatusTagColor(e.data.status)},{default:i((()=>[u(r(e.data.statusName||l.getStatusName(e.data.status)),1)])),_:1},8,["color"])])),_:1}),s(V,{label:"订单日期"},{default:i((()=>[u(r(e.data.orderDate),1)])),_:1}),s(V,{label:"供应商名称"},{default:i((()=>[u(r(e.data.supplierName),1)])),_:1}),s(V,{label:"供应商编码"},{default:i((()=>[u(r(e.data.supplierCode),1)])),_:1}),s(V,{label:"经营方式"},{default:i((()=>[s(W,{color:l.getBusinessModeColor(e.data.businessMode)},{default:i((()=>[u(r(e.data.businessModeName),1)])),_:1},8,["color"])])),_:1}),s(V,{label:"创建人"},{default:i((()=>[u(r(e.data.createUserName),1)])),_:1}),s(V,{label:"创建时间"},{default:i((()=>[u(r(e.data.createTime),1)])),_:1}),s(V,{label:"更新时间"},{default:i((()=>[u(r(e.data.updateTime),1)])),_:1}),s(V,{label:"备注",span:3},{default:i((()=>[u(r(e.data.remark||"无"),1)])),_:1})])),_:1})])),_:1}),l.showPaymentInfo?(o(),n(Z,{key:0,title:"付款信息",size:"small",style:{"margin-bottom":"16px"}},{default:i((()=>[s(J,{column:3,bordered:"",size:"small"},{default:i((()=>[s(V,{label:"付款方式"},{default:i((()=>[e.data.paymentMethod?(o(),n(W,{key:0,color:l.getPaymentMethodColor(e.data.paymentMethod)},{default:i((()=>[u(r(e.data.paymentMethodName||l.getPaymentMethodName(e.data.paymentMethod)),1)])),_:1},8,["color"])):(o(),c("span",L,"未设置"))])),_:1}),s(V,{label:"付款账户"},{default:i((()=>[e.data.paymentAccount?(o(),c("span",R,r(e.data.paymentAccount),1)):(o(),c("span",w,"未设置"))])),_:1}),s(V,{label:"付款状态"},{default:i((()=>[s(W,{color:l.getPaymentStatusColor(e.data.status)},{default:i((()=>[u(r(l.getPaymentStatusName(e.data.status)),1)])),_:1},8,["color"])])),_:1}),s(V,{label:"应付金额"},{default:i((()=>[d("span",D,"¥"+r(l.formatAmount(e.data.totalAmount)),1)])),_:1}),"COMPLETED"===e.data.status?(o(),n(V,{key:0,label:"实付金额"},{default:i((()=>[d("span",O,"¥"+r(l.formatAmount(e.data.actualPaymentAmount||e.data.totalAmount)),1)])),_:1})):p("",!0),"COMPLETED"===e.data.status&&e.data.paymentTime?(o(),n(V,{key:1,label:"付款时间"},{default:i((()=>[u(r(e.data.paymentTime),1)])),_:1})):p("",!0)])),_:1})])),_:1})):p("",!0),s(Z,{title:"商品明细",size:"small",style:{"margin-bottom":"16px"}},{default:i((()=>[s(X,{columns:l.detailColumns,"data-source":e.data.detailList||[],pagination:!1,size:"small",bordered:"",scroll:{x:1e3}},{bodyCell:i((({column:t,record:a})=>["productInfo"===t.key?(o(),c("div",S,[d("div",z,r(a.productName),1),d("div",F,[d("span",j,r(a.productCode),1),a.specification?(o(),c("span",H,r(a.specification),1)):p("",!0)])])):p("",!0),"quantity"===t.key?(o(),c(m,{key:1},[u(r(a.quantity)+" "+r(l.getQuantityUnit(a)),1)],64)):p("",!0),"unitPrice"===t.key?(o(),c(m,{key:2},[u(" ¥"+r(l.formatAmount(a.unitPrice)),1)],64)):p("",!0),"totalPrice"===t.key?(o(),c("span",q,"¥"+r(l.formatAmount(a.totalPrice)),1)):p("",!0),"remark"===t.key?(o(),c(m,{key:4},[u(r(a.remark||"-"),1)],64)):p("",!0)])),_:1},8,["columns","data-source"])])),_:1}),s(Z,{title:"汇总信息",size:"small"},{default:i((()=>[s(at,{gutter:16},{default:i((()=>[s(tt,{span:6},{default:i((()=>[s($,{title:"商品种类",value:l.productCount,suffix:"种"},null,8,["value"])])),_:1}),s(tt,{span:6},{default:i((()=>[s($,{title:"总数量",value:l.totalQuantity,suffix:"件"},null,8,["value"])])),_:1}),s(tt,{span:6},{default:i((()=>[s($,{title:"总金额",value:l.totalAmount,prefix:"¥",precision:2},null,8,["value"])])),_:1}),s(tt,{span:6},{default:i((()=>[s($,{title:"平均单价",value:l.averagePrice,prefix:"¥",precision:2},null,8,["value"])])),_:1})])),_:1})])),_:1}),e.data.operationLogs&&e.data.operationLogs.length>0?(o(),n(Z,{key:1,title:"操作历史",size:"small",style:{"margin-top":"16px"}},{default:i((()=>[s(ot,null,{default:i((()=>[(o(!0),c(m,null,f(e.data.operationLogs,(t=>(o(),n(lt,{key:t.id,color:l.getOperationColor(t.operation)},{dot:i((()=>[s(et,{iconClass:l.getOperationIcon(t.operation)},null,8,["iconClass"])])),default:i((()=>[d("div",U,[d("div",B,[d("span",Y,r(t.operationName),1),d("span",K,r(t.operationTime),1)]),d("div",Q,"操作人："+r(t.operatorName),1),t.remark?(o(),c("div",G,"备注："+r(t.remark),1)):p("",!0)])])),_:2},1032,["color"])))),128))])),_:1})])),_:1})):p("",!0)])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-987baa8b"]]))}}}));
