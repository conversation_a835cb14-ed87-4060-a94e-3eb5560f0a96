import{aJ as A,m as _,aV as M}from"./index-18a1ea24.js";import{N as T}from"./constants-2fa70699.js";const t={NETWORK_ERROR:"NETWORK_ERROR",TIMEOUT_ERROR:"TIMEOUT_ERROR",CONNECTION_ERROR:"CONNECTION_ERROR",INSUFFICIENT_INVENTORY:"INSUFFICIENT_INVENTORY",INVALID_MEMBER:"INVALID_MEMBER",MEMBER_EXPIRED:"MEMBER_EXPIRED",MEMBER_INACTIVE:"MEMBER_INACTIVE",PAYMENT_FAILED:"PAYMENT_FAILED",PAYMENT_TIMEOUT:"PAYMENT_TIMEOUT",PAYMENT_CANCELLED:"PAYMENT_CANCELLED",ORDER_NOT_FOUND:"ORDER_NOT_FOUND",PRODUCT_NOT_FOUND:"PRODUCT_NOT_FOUND",CATEGORY_NOT_FOUND:"CATEGORY_NOT_FOUND",INVALID_QUANTITY:"INVALID_QUANTITY",INVALID_PRICE:"INVALID_PRICE",INVALID_AMOUNT:"INVALID_AMOUNT",INVALID_DISCOUNT:"INVALID_DISCOUNT",INVALID_POINTS:"INVALID_POINTS",EMPTY_CART:"EMPTY_CART",CART_ITEM_LIMIT_EXCEEDED:"CART_ITEM_LIMIT_EXCEEDED",INVALID_BARCODE:"INVALID_BARCODE",INVALID_CARD_NO:"INVALID_CARD_NO",INVALID_PHONE:"INVALID_PHONE",PERMISSION_DENIED:"PERMISSION_DENIED",UNAUTHORIZED:"UNAUTHORIZED",ACCESS_FORBIDDEN:"ACCESS_FORBIDDEN",SYSTEM_ERROR:"SYSTEM_ERROR",SYSTEM_MAINTENANCE:"SYSTEM_MAINTENANCE",DATABASE_ERROR:"DATABASE_ERROR",SERVICE_UNAVAILABLE:"SERVICE_UNAVAILABLE",INVALID_CONFIG:"INVALID_CONFIG",MISSING_CONFIG:"MISSING_CONFIG",FILE_NOT_FOUND:"FILE_NOT_FOUND",FILE_UPLOAD_FAILED:"FILE_UPLOAD_FAILED",FILE_SIZE_EXCEEDED:"FILE_SIZE_EXCEEDED",CACHE_ERROR:"CACHE_ERROR",CACHE_EXPIRED:"CACHE_EXPIRED",CONCURRENT_MODIFICATION:"CONCURRENT_MODIFICATION",RESOURCE_LOCKED:"RESOURCE_LOCKED",BUSINESS_RULE_VIOLATION:"BUSINESS_RULE_VIOLATION",OPERATION_NOT_ALLOWED:"OPERATION_NOT_ALLOWED",TIME_RESTRICTION:"TIME_RESTRICTION",UNKNOWN_ERROR:"UNKNOWN_ERROR"},m={[t.NETWORK_ERROR]:"\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8BBE\u7F6E",[t.TIMEOUT_ERROR]:"\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5",[t.CONNECTION_ERROR]:"\u8FDE\u63A5\u670D\u52A1\u5668\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5",[t.INSUFFICIENT_INVENTORY]:"\u5546\u54C1\u5E93\u5B58\u4E0D\u8DB3",[t.INVALID_MEMBER]:"\u4F1A\u5458\u4FE1\u606F\u65E0\u6548\u6216\u4E0D\u5B58\u5728",[t.MEMBER_EXPIRED]:"\u4F1A\u5458\u5361\u5DF2\u8FC7\u671F",[t.MEMBER_INACTIVE]:"\u4F1A\u5458\u8D26\u6237\u5DF2\u88AB\u51BB\u7ED3\u6216\u505C\u7528",[t.PAYMENT_FAILED]:"\u652F\u4ED8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5",[t.PAYMENT_TIMEOUT]:"\u652F\u4ED8\u8D85\u65F6\uFF0C\u8BF7\u91CD\u65B0\u53D1\u8D77\u652F\u4ED8",[t.PAYMENT_CANCELLED]:"\u652F\u4ED8\u5DF2\u53D6\u6D88",[t.ORDER_NOT_FOUND]:"\u8BA2\u5355\u4E0D\u5B58\u5728",[t.PRODUCT_NOT_FOUND]:"\u5546\u54C1\u4E0D\u5B58\u5728",[t.CATEGORY_NOT_FOUND]:"\u5546\u54C1\u5206\u7C7B\u4E0D\u5B58\u5728",[t.INVALID_QUANTITY]:"\u5546\u54C1\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0",[t.INVALID_PRICE]:"\u5546\u54C1\u4EF7\u683C\u683C\u5F0F\u4E0D\u6B63\u786E",[t.INVALID_AMOUNT]:"\u91D1\u989D\u683C\u5F0F\u4E0D\u6B63\u786E",[t.INVALID_DISCOUNT]:"\u6298\u6263\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7\u5546\u54C1\u603B\u4EF7",[t.INVALID_POINTS]:"\u79EF\u5206\u6570\u91CF\u683C\u5F0F\u4E0D\u6B63\u786E",[t.EMPTY_CART]:"\u8D2D\u7269\u8F66\u4E3A\u7A7A\uFF0C\u65E0\u6CD5\u7ED3\u7B97",[t.CART_ITEM_LIMIT_EXCEEDED]:"\u8D2D\u7269\u8F66\u5546\u54C1\u6570\u91CF\u8D85\u8FC7\u9650\u5236",[t.INVALID_BARCODE]:"\u5546\u54C1\u6761\u7801\u683C\u5F0F\u4E0D\u6B63\u786E",[t.INVALID_CARD_NO]:"\u5361\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E",[t.INVALID_PHONE]:"\u624B\u673A\u53F7\u7801\u683C\u5F0F\u4E0D\u6B63\u786E",[t.PERMISSION_DENIED]:"\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u6267\u884C\u6B64\u64CD\u4F5C",[t.UNAUTHORIZED]:"\u7528\u6237\u672A\u767B\u5F55\u6216\u767B\u5F55\u5DF2\u8FC7\u671F",[t.ACCESS_FORBIDDEN]:"\u8BBF\u95EE\u88AB\u62D2\u7EDD",[t.SYSTEM_ERROR]:"\u7CFB\u7EDF\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458",[t.SYSTEM_MAINTENANCE]:"\u7CFB\u7EDF\u6B63\u5728\u7EF4\u62A4\u4E2D\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5",[t.DATABASE_ERROR]:"\u6570\u636E\u5E93\u8FDE\u63A5\u9519\u8BEF",[t.SERVICE_UNAVAILABLE]:"\u670D\u52A1\u6682\u65F6\u4E0D\u53EF\u7528",[t.INVALID_CONFIG]:"\u7CFB\u7EDF\u914D\u7F6E\u9519\u8BEF",[t.MISSING_CONFIG]:"\u7F3A\u5C11\u5FC5\u8981\u7684\u7CFB\u7EDF\u914D\u7F6E",[t.FILE_NOT_FOUND]:"\u6587\u4EF6\u4E0D\u5B58\u5728",[t.FILE_UPLOAD_FAILED]:"\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25",[t.FILE_SIZE_EXCEEDED]:"\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7\u9650\u5236",[t.CACHE_ERROR]:"\u7F13\u5B58\u64CD\u4F5C\u5931\u8D25",[t.CACHE_EXPIRED]:"\u7F13\u5B58\u5DF2\u8FC7\u671F",[t.CONCURRENT_MODIFICATION]:"\u6570\u636E\u5DF2\u88AB\u5176\u4ED6\u7528\u6237\u4FEE\u6539\uFF0C\u8BF7\u5237\u65B0\u540E\u91CD\u8BD5",[t.RESOURCE_LOCKED]:"\u8D44\u6E90\u88AB\u9501\u5B9A\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5",[t.BUSINESS_RULE_VIOLATION]:"\u8FDD\u53CD\u4E1A\u52A1\u89C4\u5219",[t.OPERATION_NOT_ALLOWED]:"\u5F53\u524D\u72B6\u6001\u4E0B\u4E0D\u5141\u8BB8\u6B64\u64CD\u4F5C",[t.TIME_RESTRICTION]:"\u5F53\u524D\u65F6\u95F4\u4E0D\u5141\u8BB8\u6B64\u64CD\u4F5C",[t.UNKNOWN_ERROR]:"\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5"},E={LOW:"LOW",MEDIUM:"MEDIUM",HIGH:"HIGH",CRITICAL:"CRITICAL"},D={[t.INVALID_QUANTITY]:E.LOW,[t.INVALID_PRICE]:E.LOW,[t.INVALID_AMOUNT]:E.LOW,[t.INVALID_DISCOUNT]:E.LOW,[t.INVALID_POINTS]:E.LOW,[t.EMPTY_CART]:E.LOW,[t.INVALID_BARCODE]:E.LOW,[t.INVALID_CARD_NO]:E.LOW,[t.INVALID_PHONE]:E.LOW,[t.INSUFFICIENT_INVENTORY]:E.MEDIUM,[t.INVALID_MEMBER]:E.MEDIUM,[t.MEMBER_EXPIRED]:E.MEDIUM,[t.MEMBER_INACTIVE]:E.MEDIUM,[t.PAYMENT_FAILED]:E.MEDIUM,[t.PAYMENT_TIMEOUT]:E.MEDIUM,[t.ORDER_NOT_FOUND]:E.MEDIUM,[t.PRODUCT_NOT_FOUND]:E.MEDIUM,[t.CATEGORY_NOT_FOUND]:E.MEDIUM,[t.CART_ITEM_LIMIT_EXCEEDED]:E.MEDIUM,[t.BUSINESS_RULE_VIOLATION]:E.MEDIUM,[t.OPERATION_NOT_ALLOWED]:E.MEDIUM,[t.TIME_RESTRICTION]:E.MEDIUM,[t.NETWORK_ERROR]:E.HIGH,[t.TIMEOUT_ERROR]:E.HIGH,[t.CONNECTION_ERROR]:E.HIGH,[t.PERMISSION_DENIED]:E.HIGH,[t.UNAUTHORIZED]:E.HIGH,[t.ACCESS_FORBIDDEN]:E.HIGH,[t.INVALID_CONFIG]:E.HIGH,[t.MISSING_CONFIG]:E.HIGH,[t.CONCURRENT_MODIFICATION]:E.HIGH,[t.RESOURCE_LOCKED]:E.HIGH,[t.SYSTEM_ERROR]:E.CRITICAL,[t.SYSTEM_MAINTENANCE]:E.CRITICAL,[t.DATABASE_ERROR]:E.CRITICAL,[t.SERVICE_UNAVAILABLE]:E.CRITICAL,[t.CACHE_ERROR]:E.CRITICAL,[t.UNKNOWN_ERROR]:E.MEDIUM};function u(I,e=""){return e||m[I]||m[t.UNKNOWN_ERROR]}function l(I){return D[I]||E.MEDIUM}function p(I){return[t.NETWORK_ERROR,t.TIMEOUT_ERROR,t.CONNECTION_ERROR,t.SERVICE_UNAVAILABLE,t.DATABASE_ERROR,t.CACHE_ERROR,t.RESOURCE_LOCKED].includes(I)}function d(I){return![t.SYSTEM_ERROR,t.DATABASE_ERROR,t.CACHE_ERROR,t.INVALID_CONFIG,t.MISSING_CONFIG].includes(I)}function C(I,e="",r={}){return{type:I,message:e||u(I),severity:l(I),retryable:p(I),userFacing:d(I),timestamp:new Date().toISOString(),details:r}}class S{static wrapApiCall(e,r={}){const{showMessage:s=!0,showNotification:n=!1,context:o="\u64CD\u4F5C",onError:i,retryOptions:a={}}=r;return async(...c)=>{try{if(a.maxRetries>0){const{RetryHandler:N}=await A(()=>import("./retry-handler-e79b4741.js"),["assets/retry-handler-e79b4741.js","assets/index-18a1ea24.js","assets/index-747cb573.css","assets/constants-2fa70699.js"]);return await N.withRetry(()=>e(...c),a)}return await e(...c)}catch(N){const R=this.processError(N,o);throw R.userFacing&&(s&&this.showErrorMessage(R),n&&this.showErrorNotification(R,o)),this.logError(R,o),typeof i=="function"&&i(R),R}}}static processError(e,r=""){if(e&&e.type&&e.severity)return e;let s=t.UNKNOWN_ERROR,n="",o={};if(e instanceof Error)n=e.message,o={name:e.name,stack:e.stack},s=this.inferErrorType(e);else if(typeof e=="object"&&e!==null)if(e.response){const{status:i,data:a}=e.response;o={status:i,data:a},i>=500?s=t.SYSTEM_ERROR:i===404?s=t.PRODUCT_NOT_FOUND:i===401?s=t.UNAUTHORIZED:i===403?s=t.ACCESS_FORBIDDEN:i===400&&(s=this.inferErrorTypeFromResponse(a)),n=(a==null?void 0:a.message)||e.message||"HTTP ".concat(i," \u9519\u8BEF")}else e.code?(s=e.code,n=e.message||"",o=e.details||{}):n=e.message||JSON.stringify(e);else n=String(e);return C(s,n,{...o,context:r,originalError:e})}static inferErrorType(e){const r=e.message.toLowerCase();return r.includes("network")||r.includes("\u7F51\u7EDC")?t.NETWORK_ERROR:r.includes("timeout")||r.includes("\u8D85\u65F6")?t.TIMEOUT_ERROR:r.includes("connection")||r.includes("\u8FDE\u63A5")?t.CONNECTION_ERROR:r.includes("inventory")||r.includes("\u5E93\u5B58")?t.INSUFFICIENT_INVENTORY:r.includes("member")||r.includes("\u4F1A\u5458")?t.INVALID_MEMBER:r.includes("payment")||r.includes("\u652F\u4ED8")?t.PAYMENT_FAILED:r.includes("permission")||r.includes("\u6743\u9650")?t.PERMISSION_DENIED:t.UNKNOWN_ERROR}static inferErrorTypeFromResponse(e){if(!e)return t.UNKNOWN_ERROR;const{code:r,message:s=""}=e;if(r&&Object.values(t).includes(r))return r;const n=s.toLowerCase();return n.includes("quantity")||n.includes("\u6570\u91CF")?t.INVALID_QUANTITY:n.includes("price")||n.includes("\u4EF7\u683C")?t.INVALID_PRICE:n.includes("amount")||n.includes("\u91D1\u989D")?t.INVALID_AMOUNT:n.includes("cart")||n.includes("\u8D2D\u7269\u8F66")?t.EMPTY_CART:t.UNKNOWN_ERROR}static showErrorMessage(e){const r=this.getMessageType(e.severity);_[r](e.message,3)}static showErrorNotification(e,r){const s=this.getNotificationType(e.severity);M[s]({message:"".concat(r,"\u5931\u8D25"),description:e.message,duration:this.getNotificationDuration(e.severity),placement:"topRight"})}static getMessageType(e){switch(e){case"LOW":return"warning";case"MEDIUM":return"error";case"HIGH":return"error";case"CRITICAL":return"error";default:return"error"}}static getNotificationType(e){switch(e){case"LOW":return"warning";case"MEDIUM":return"error";case"HIGH":return"error";case"CRITICAL":return"error";default:return"error"}}static getNotificationDuration(e){switch(e){case"LOW":return 3;case"MEDIUM":return 5;case"HIGH":return 8;case"CRITICAL":return 0;default:return 5}}static logError(e,r){const s=this.getLogLevel(e.severity),n="[POS Error] ".concat(r,": ").concat(e.message),o={type:e.type,message:e.message,severity:e.severity,context:r,timestamp:e.timestamp,details:e.details};switch(s){case"warn":console.warn(n,o);break;case"error":console.error(n,o);break;default:console.log(n,o)}this.sendToLogService(e,r)}static getLogLevel(e){switch(e){case"LOW":return"info";case"MEDIUM":return"warn";case"HIGH":return"error";case"CRITICAL":return"error";default:return"info"}}static sendToLogService(e,r){if(e.severity==="HIGH"||e.severity==="CRITICAL")try{window.logService&&typeof window.logService.captureException=="function"&&window.logService.captureException(e,{tags:{context:r,severity:e.severity,type:e.type},extra:e.details})}catch(s){console.warn("\u53D1\u9001\u9519\u8BEF\u65E5\u5FD7\u5931\u8D25:",s)}}static createErrorBoundary(e){return(r,s)=>{const n=this.processError(r,"\u7EC4\u4EF6 ".concat(e));_.error("\u9875\u9762\u51FA\u73B0\u9519\u8BEF\uFF0C\u8BF7\u5237\u65B0\u540E\u91CD\u8BD5"),this.logError({...n,details:{...n.details,componentStack:s.componentStack,errorBoundary:!0}},"\u7EC4\u4EF6 ".concat(e," \u9519\u8BEF\u8FB9\u754C"))}}static globalErrorHandler(e,r="global"){const s=this.processError(e,r);s.severity==="CRITICAL"&&M.error({message:"\u7CFB\u7EDF\u9519\u8BEF",description:"\u7CFB\u7EDF\u9047\u5230\u4E25\u91CD\u9519\u8BEF\uFF0C\u8BF7\u5237\u65B0\u9875\u9762\u6216\u8054\u7CFB\u7BA1\u7406\u5458",duration:0,placement:"topRight"}),this.logError(s,r)}static unhandledRejectionHandler(e){const r=e.reason,s=this.processError(r,"unhandled promise rejection");e.preventDefault(),this.logError(s,"unhandled promise rejection"),s.userFacing&&_.error(s.message)}}class g{static init(){window.posMetrics||(window.posMetrics=[]),this.setupPerformanceObserver(),this.setupMemoryMonitor(),this.setupVisibilityMonitor()}static measureComponentRender(e,r){const s=performance.now(),n="".concat(e,"-render-start"),o="".concat(e,"-render-end"),i="".concat(e,"-render");performance.mark(n);try{const a=r();return a&&typeof a.then=="function"?a.then(c=>(this.completeRenderMeasurement(e,s,n,o,i),c)).catch(c=>{throw this.completeRenderMeasurement(e,s,n,o,i,c),c}):(this.completeRenderMeasurement(e,s,n,o,i),a)}catch(a){throw this.completeRenderMeasurement(e,s,n,o,i,a),a}}static completeRenderMeasurement(e,r,s,n,o,i=null){const c=performance.now()-r;performance.mark(n),performance.measure(o,s,n),this.recordMetric("component_render",{component:e,renderTime:c,timestamp:Date.now(),status:i?"error":"success",error:i?i.message:null});const N=T.PERFORMANCE_THRESHOLDS.COMPONENT_RENDER_TIME;c>N&&(console.warn("\u7EC4\u4EF6 ".concat(e," \u6E32\u67D3\u65F6\u95F4\u8FC7\u957F: ").concat(c.toFixed(2),"ms (\u9608\u503C: ").concat(N,"ms)")),this.recordMetric("performance_warning",{type:"slow_render",component:e,renderTime:c,threshold:N,timestamp:Date.now()}))}static measureApiCall(e,r){return async(...s)=>{const n=performance.now(),o="".concat(e,"-api-start"),i="".concat(e,"-api-end"),a="".concat(e,"-api");performance.mark(o);try{const c=await r(...s),R=performance.now()-n;performance.mark(i),performance.measure(a,o,i),this.recordMetric("api_call",{api:e,duration:R,status:"success",timestamp:Date.now(),args:this.sanitizeArgs(s)});const O=T.PERFORMANCE_THRESHOLDS.API_CALL_TIME;return R>O&&(console.warn("API ".concat(e," \u8C03\u7528\u65F6\u95F4\u8FC7\u957F: ").concat(R.toFixed(2),"ms (\u9608\u503C: ").concat(O,"ms)")),this.recordMetric("performance_warning",{type:"slow_api",api:e,duration:R,threshold:O,timestamp:Date.now()})),c}catch(c){const R=performance.now()-n;throw performance.mark(i),performance.measure(a,o,i),this.recordMetric("api_call",{api:e,duration:R,status:"error",error:c.message,timestamp:Date.now(),args:this.sanitizeArgs(s)}),c}}}static sanitizeArgs(e){return e.map(r=>{if(typeof r=="object"&&r!==null){const s={...r};return["password","token","cardNo","phone","idCard"].forEach(o=>{s[o]&&(s[o]="***")}),s}return r})}static recordMetric(e,r){window.posMetrics||(window.posMetrics=[]);const s={type:e,...r,id:this.generateMetricId(),userAgent:navigator.userAgent,url:window.location.href};window.posMetrics.push(s),window.posMetrics.length>1e3&&(window.posMetrics=window.posMetrics.slice(-500)),this.sendMetricToService(s)}static generateMetricId(){return"".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))}static sendMetricToService(e){if(["performance_warning","api_call","component_render"].includes(e.type))try{window.monitoringService&&typeof window.monitoringService.track=="function"&&window.monitoringService.track("pos_performance",e)}catch(s){console.warn("\u53D1\u9001\u6027\u80FD\u6307\u6807\u5931\u8D25:",s)}}static getMetrics(e={}){if(!window.posMetrics)return[];let r=[...window.posMetrics];return e.type&&(r=r.filter(s=>s.type===e.type)),e.component&&(r=r.filter(s=>s.component===e.component)),e.api&&(r=r.filter(s=>s.api===e.api)),e.startTime&&e.endTime&&(r=r.filter(s=>s.timestamp>=e.startTime&&s.timestamp<=e.endTime)),r}static getPerformanceStats(e){const r=this.getMetrics({type:e});if(r.length===0)return{count:0,average:0,min:0,max:0,p95:0,p99:0};const s=r.map(i=>i.renderTime||i.duration||0).sort((i,a)=>i-a),n=s.reduce((i,a)=>i+a,0),o=s.length;return{count:o,average:n/o,min:s[0],max:s[o-1],p95:s[Math.floor(o*.95)],p99:s[Math.floor(o*.99)]}}static clearMetrics(){window.posMetrics&&(window.posMetrics=[]),performance.clearMarks&&performance.clearMarks(),performance.clearMeasures&&performance.clearMeasures()}static setupPerformanceObserver(){if("PerformanceObserver"in window)try{new PerformanceObserver(n=>{n.getEntries().forEach(i=>{i.entryType==="navigation"&&this.recordMetric("navigation",{domContentLoaded:i.domContentLoadedEventEnd-i.domContentLoadedEventStart,loadComplete:i.loadEventEnd-i.loadEventStart,firstPaint:i.responseEnd-i.requestStart,timestamp:Date.now()})})}).observe({entryTypes:["navigation"]}),new PerformanceObserver(n=>{n.getEntries().forEach(i=>{i.duration>1e3&&this.recordMetric("resource_load",{name:i.name,duration:i.duration,size:i.transferSize,timestamp:Date.now()})})}).observe({entryTypes:["resource"]});const s=new PerformanceObserver(n=>{n.getEntries().forEach(i=>{this.recordMetric("long_task",{duration:i.duration,startTime:i.startTime,timestamp:Date.now()})})});"longtask"in PerformanceObserver.supportedEntryTypes&&s.observe({entryTypes:["longtask"]})}catch(e){console.warn("\u8BBE\u7F6E\u6027\u80FD\u89C2\u5BDF\u5668\u5931\u8D25:",e)}}static setupMemoryMonitor(){setInterval(()=>{if(performance.memory){const e={used:performance.memory.usedJSHeapSize,total:performance.memory.totalJSHeapSize,limit:performance.memory.jsHeapSizeLimit},r=e.used/e.limit*100;this.recordMetric("memory_usage",{...e,usagePercent:r,timestamp:Date.now()});const s=T.PERFORMANCE_THRESHOLDS.MEMORY_USAGE_PERCENT;if(r>s&&(console.warn("\u5185\u5B58\u4F7F\u7528\u7387\u8FC7\u9AD8: ".concat(r.toFixed(2),"% (\u9608\u503C: ").concat(s,"%)")),this.recordMetric("performance_warning",{type:"high_memory_usage",usagePercent:r,threshold:s,timestamp:Date.now()}),window.gc&&typeof window.gc=="function"))try{window.gc()}catch(n){console.warn("\u624B\u52A8\u5783\u573E\u56DE\u6536\u5931\u8D25:",n)}}},3e4)}static setupVisibilityMonitor(){let e=Date.now();document.addEventListener("visibilitychange",()=>{const r=Date.now();if(document.hidden){const s=r-e;this.recordMetric("page_visibility",{event:"hidden",visibleDuration:s,timestamp:r})}else e=r,this.recordMetric("page_visibility",{event:"visible",timestamp:r})})}static generateReport(e={}){const{timeRange:r=36e5,includeDetails:s=!1}=e,n=Date.now(),o=n-r,i=this.getMetrics({startTime:o,endTime:n}),a={timeRange:{startTime:o,endTime:n},summary:{totalMetrics:i.length,componentRenders:i.filter(c=>c.type==="component_render").length,apiCalls:i.filter(c=>c.type==="api_call").length,warnings:i.filter(c=>c.type==="performance_warning").length},performance:{componentRender:this.getPerformanceStats("component_render"),apiCall:this.getPerformanceStats("api_call")},warnings:i.filter(c=>c.type==="performance_warning"),memory:this.getLatestMemoryUsage()};return s&&(a.details=i),a}static getLatestMemoryUsage(){const e=this.getMetrics({type:"memory_usage"});return e.length>0?e[e.length-1]:null}static exportMetrics(e="json"){const r=this.getMetrics();if(e==="csv"){const s=["type","timestamp","component","api","duration","status"];return[s.join(","),...r.map(o=>s.map(i=>o[i]||"").join(","))].join("\n")}return JSON.stringify(r,null,2)}}export{S as P,g as a,p as i};
