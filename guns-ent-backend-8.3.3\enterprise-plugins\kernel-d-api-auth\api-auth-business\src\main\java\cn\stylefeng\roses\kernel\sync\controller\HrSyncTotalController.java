package cn.stylefeng.roses.kernel.sync.controller;

import cn.stylefeng.roses.kernel.apiauth.api.annotations.ApiAuth;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import cn.stylefeng.roses.kernel.sync.pojo.OrganizationSyncVo;
import cn.stylefeng.roses.kernel.sync.pojo.PositionSyncVo;
import cn.stylefeng.roses.kernel.sync.pojo.UserOrgSyncVo;
import cn.stylefeng.roses.kernel.sync.pojo.UserSyncVo;
import cn.stylefeng.roses.kernel.sync.service.SyncOrganizationService;
import cn.stylefeng.roses.kernel.sync.service.SyncPositionService;
import cn.stylefeng.roses.kernel.sync.service.SyncUserOrgService;
import cn.stylefeng.roses.kernel.sync.service.SyncUserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 人力资源数据-全量同步接口
 *
 * <AUTHOR>
 * @since 2023/10/29 22:46
 */
@RestController
@ApiResource(name = "人力资源数据-全量同步接口")
public class HrSyncTotalController {

    @Resource
    private SyncOrganizationService syncOrganizationService;

    @Resource
    private SyncPositionService syncPositionService;

    @Resource
    private SyncUserService syncUserService;

    @Resource
    private SyncUserOrgService syncUserOrgService;

    /**
     * 全量同步组织机构
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "全量同步组织机构", path = "/hrSyncTotal/organization", requiredLogin = false)
    @ApiAuth
    public ResponseData<List<OrganizationSyncVo>> organization() {
        List<OrganizationSyncVo> totalOrganization = syncOrganizationService.getTotalOrganization();
        return new SuccessResponseData<>(totalOrganization);
    }

    /**
     * 全量同步职位信息
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "全量同步职位信息", path = "/hrSyncTotal/position", requiredLogin = false)
    @ApiAuth
    public ResponseData<List<PositionSyncVo>> position() {
        List<PositionSyncVo> totalPosition = syncPositionService.getTotalPosition();
        return new SuccessResponseData<>(totalPosition);
    }

    /**
     * 全量同步用户数据
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "全量同步用户信息", path = "/hrSyncTotal/user", requiredLogin = false)
    @ApiAuth
    public ResponseData<List<UserSyncVo>> user() {
        List<UserSyncVo> totalUser = syncUserService.getTotalUser();
        return new SuccessResponseData<>(totalUser);
    }

    /**
     * 全量同步用户和组织机构关联数据
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "全量同步用户和组织机构关联数据", path = "/hrSyncTotal/userOrgLink", requiredLogin = false)
    @ApiAuth
    public ResponseData<List<UserOrgSyncVo>> userOrgLink() {
        List<UserOrgSyncVo> totalUserOrg = syncUserOrgService.getTotalUserOrg();
        return new SuccessResponseData<>(totalUserOrg);
    }

}
