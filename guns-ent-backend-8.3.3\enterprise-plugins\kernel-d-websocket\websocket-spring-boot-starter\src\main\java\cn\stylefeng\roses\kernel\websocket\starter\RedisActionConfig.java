package cn.stylefeng.roses.kernel.websocket.starter;

import cn.stylefeng.roses.kernel.websocket.api.constants.WebSocketActionConstants;
import cn.stylefeng.roses.kernel.websocket.redis.action.RemoveAction;
import cn.stylefeng.roses.kernel.websocket.redis.action.SendMessageAction;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * Redis动作配置
 *
 * <AUTHOR>
 * @since 2024-01-15 14:41
 */
@Configuration
@ConditionalOnClass(name = "org.springframework.data.redis.connection.RedisConnectionFactory")
public class RedisActionConfig {

    /**
     * 发送消息的redis事件监听
     *
     * <AUTHOR>
     * @since 2024-01-19 13:54
     */
    @Bean(name = WebSocketActionConstants.SEND_MESSAGE_ACTION_BEAN_NAME)
    public SendMessageAction sendMessageAction() {
        return new SendMessageAction();
    }

    /**
     * 删除redis缓存的事件监听
     *
     * <AUTHOR>
     * @since 2024-01-19 13:55
     */
    @Bean(name = WebSocketActionConstants.REMOVE_ACTION_BEAN_NAME)
    public RemoveAction removeAction() {
        return new RemoveAction();
    }

}