import{_ as u,aP as f,m as n,bZ as c,b_ as m,a as _,f as i,bY as e,S as l}from"./index-18a1ea24.js";import{S as s}from"./sso-util-3b0c5c46.js";const g=f({name:"Sso",setup(){const o=s.getUrlParam("token");s.getUrlParam("callback");const a=s.getUrlParam("from"),t=()=>{e.query&&e.query.from?e.push(String(e.query.from)):e.push("/").catch(()=>{})};a&&a==="oauth2"?(n.success("\u767B\u5F55\u6210\u529F"),c(o,!0),m(),t()):s.tokenExchange(o).then(r=>{n.success("\u767B\u5F55\u6210\u529F"),c(r.token,!0),m(),t()})}});function k(o,a,t,r,h,S){const p=l;return _(),i(p)}const P=u(g,[["render",k]]);export{P as default};
