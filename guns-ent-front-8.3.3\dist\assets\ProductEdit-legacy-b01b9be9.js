System.register(["./index-legacy-ee1db0c7.js","./SupplierSelector-legacy-fba3813b.js","./index-legacy-94a6fc23.js","./ProductApi-legacy-33feae42.js","./CategorySelector-legacy-c3396c33.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-9a185ac3.js","./productCategoryApi-legacy-247b2407.js"],(function(e,l){"use strict";var a,r,t,u,i,d,o,n,m,s,p,c,f,g,v,b,y,_,h,P,U,C,I,T,w,O,k,S,N;return{setters:[e=>{a=e._,r=e.s,t=e.r,u=e.X,i=e.k,d=e.a,o=e.f,n=e.w,m=e.d,s=e.c,p=e.F,c=e.e,f=e.g,g=e.t,v=e.h,b=e.m,y=e.l,_=e.u,h=e.v,P=e.G,U=e.W,C=e.J,I=e.y,T=e.$,w=e.H,O=e.M},e=>{k=e._},null,e=>{S=e.P},e=>{N=e.default},null,null,null,null],execute:function(){const l={name:"ProductEdit",components:{CategorySelector:N,SupplierSelector:k},props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:l}){const a=r({}),i=t(null),d=t(!1),o=t(!1),n=S.getProductStatusOptions(),m=S.getCommonUnitOptions(),s=S.getPricingTypeOptions(),p=r({productCode:[{required:!0,message:"请输入商品编码",trigger:"blur"},{max:50,message:"商品编码不能超过50个字符",trigger:"blur"}],productName:[{required:!0,message:"请输入商品名称",trigger:"blur"},{max:200,message:"商品名称不能超过200个字符",trigger:"blur"}],unit:[{required:!0,message:"请选择基本单位",trigger:"change"}],categoryId:[{required:!0,message:"请选择商品分类",trigger:"change"}],weight:[{type:"number",min:0,message:"重量不能小于0",trigger:"blur"}],volume:[{type:"number",min:0,message:"体积不能小于0",trigger:"blur"}],shelfLife:[{type:"number",min:0,message:"保质期不能小于0",trigger:"blur"}],supplierId:[{required:!0,message:"请选择供应商",trigger:"change"}],pricingType:[{required:!0,message:"请选择计价类型",trigger:"change"}]}),c=e=>{l("update:visible",e)},f=e=>{switch(delete p.retailPrice,delete p.unitPrice,delete p.piecePrice,delete p.referencePrice,e){case"NORMAL":p.retailPrice=[{required:!0,message:"请输入零售价格",trigger:"blur",type:"number"}];break;case"WEIGHT":p.unitPrice=[{required:!0,message:"请输入单位价格",trigger:"blur",type:"number"}];break;case"PIECE":p.piecePrice=[{required:!0,message:"请输入单份价格",trigger:"blur",type:"number"}]}};return u((()=>e.visible),(l=>{var r;l&&(null!==(r=e.data)&&void 0!==r&&r.productId?(o.value=!0,Object.assign(a,e.data),e.data.categoryId?a.categoryId=e.data.categoryId:a.categoryId=void 0,a.pricingType&&f(a.pricingType)):(o.value=!1,Object.keys(a).forEach((e=>{a[e]=void 0})),a.unit="个",a.status="ACTIVE",a.pricingType="NORMAL",a.categoryId=void 0,f("NORMAL")))}),{immediate:!0}),{form:a,formRef:i,loading:d,isUpdate:o,statusOptions:n,unitOptions:m,pricingTypeOptions:s,rules:p,updateVisible:c,save:()=>{i.value.validate().then((()=>(d.value=!0,(o.value?S.edit:S.add)(a)))).then((()=>{b.success("保存成功"),c(!1),l("done")})).catch((e=>{b.error(e.message||"保存失败")})).finally((()=>{d.value=!1}))},handleSupplierChange:(e,l)=>{console.log("供应商变更:",e,l)},handlePricingTypeChange:e=>{a.retailPrice=void 0,a.unitPrice=void 0,a.piecePrice=void 0,a.referencePrice=void 0,f(e)}}}};e("default",a(l,[["render",function(e,l,a,r,t,u){const b=y,S=_,N=h,E=P,j=U,A=C,L=I,q=i("category-selector"),x=k,R=T,M=w,V=O;return d(),o(V,{title:r.isUpdate?"编辑商品":"新增商品",width:900,visible:a.visible,"confirm-loading":r.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":r.updateVisible,onOk:r.save},{default:n((()=>[m(M,{ref:"formRef",model:r.form,rules:r.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:n((()=>[m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"商品编码",name:"productCode"},{default:n((()=>[m(b,{value:r.form.productCode,"onUpdate:value":l[0]||(l[0]=e=>r.form.productCode=e),placeholder:"请输入商品编码"},null,8,["value"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"商品名称",name:"productName"},{default:n((()=>[m(b,{value:r.form.productName,"onUpdate:value":l[1]||(l[1]=e=>r.form.productName=e),placeholder:"请输入商品名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"商品简称",name:"productShortName"},{default:n((()=>[m(b,{value:r.form.productShortName,"onUpdate:value":l[2]||(l[2]=e=>r.form.productShortName=e),placeholder:"请输入商品简称"},null,8,["value"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"条形码",name:"barcode"},{default:n((()=>[m(b,{value:r.form.barcode,"onUpdate:value":l[3]||(l[3]=e=>r.form.barcode=e),placeholder:"请输入条形码"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"品牌",name:"brand"},{default:n((()=>[m(b,{value:r.form.brand,"onUpdate:value":l[4]||(l[4]=e=>r.form.brand=e),placeholder:"请输入品牌"},null,8,["value"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"规格",name:"specification"},{default:n((()=>[m(b,{value:r.form.specification,"onUpdate:value":l[5]||(l[5]=e=>r.form.specification=e),placeholder:"请输入规格"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"基本单位",name:"unit"},{default:n((()=>[m(A,{value:r.form.unit,"onUpdate:value":l[6]||(l[6]=e=>r.form.unit=e),placeholder:"请选择基本单位","show-search":""},{default:n((()=>[(d(!0),s(p,null,c(r.unitOptions,(e=>(d(),o(j,{key:e.value,value:e.value},{default:n((()=>[f(g(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"状态",name:"status"},{default:n((()=>[m(A,{value:r.form.status,"onUpdate:value":l[7]||(l[7]=e=>r.form.status=e),placeholder:"请选择状态"},{default:n((()=>[(d(!0),s(p,null,c(r.statusOptions,(e=>(d(),o(j,{key:e.value,value:e.value},{default:n((()=>[f(g(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"重量(kg)",name:"weight"},{default:n((()=>[m(L,{value:r.form.weight,"onUpdate:value":l[8]||(l[8]=e=>r.form.weight=e),placeholder:"请输入重量",min:0,precision:3,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"体积(m³)",name:"volume"},{default:n((()=>[m(L,{value:r.form.volume,"onUpdate:value":l[9]||(l[9]=e=>r.form.volume=e),placeholder:"请输入体积",min:0,precision:3,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"保质期(天)",name:"shelfLife"},{default:n((()=>[m(L,{value:r.form.shelfLife,"onUpdate:value":l[10]||(l[10]=e=>r.form.shelfLife=e),placeholder:"请输入保质期",min:0,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"商品分类",name:"categoryId"},{default:n((()=>[m(q,{value:r.form.categoryId,"onUpdate:value":l[11]||(l[11]=e=>r.form.categoryId=e)},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"供应商",name:"supplierId"},{default:n((()=>[m(x,{value:r.form.supplierId,"onUpdate:value":l[12]||(l[12]=e=>r.form.supplierId=e),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},onChange:r.handleSupplierChange},null,8,["value","onChange"])])),_:1})])),_:1}),m(N,{md:12,sm:24},{default:n((()=>[m(S,{label:"计价类型",name:"pricingType"},{default:n((()=>[m(A,{value:r.form.pricingType,"onUpdate:value":l[13]||(l[13]=e=>r.form.pricingType=e),placeholder:"请选择计价类型",onChange:r.handlePricingTypeChange},{default:n((()=>[(d(!0),s(p,null,c(r.pricingTypeOptions,(e=>(d(),o(j,{key:e.value,value:e.value},{default:n((()=>[f(g(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","onChange"])])),_:1})])),_:1})])),_:1}),r.form.pricingType?(d(),o(E,{key:0,gutter:16},{default:n((()=>["NORMAL"===r.form.pricingType?(d(),o(N,{key:0,md:12,sm:24},{default:n((()=>[m(S,{label:"零售价格",name:"retailPrice"},{default:n((()=>[m(L,{value:r.form.retailPrice,"onUpdate:value":l[14]||(l[14]=e=>r.form.retailPrice=e),placeholder:"请输入零售价格",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):v("",!0),"WEIGHT"===r.form.pricingType?(d(),o(N,{key:1,md:12,sm:24},{default:n((()=>[m(S,{label:"单位价格",name:"unitPrice"},{default:n((()=>[m(L,{value:r.form.unitPrice,"onUpdate:value":l[15]||(l[15]=e=>r.form.unitPrice=e),placeholder:"请输入单位价格(每公斤)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):v("",!0),"PIECE"===r.form.pricingType?(d(),o(N,{key:2,md:12,sm:24},{default:n((()=>[m(S,{label:"单份价格",name:"piecePrice"},{default:n((()=>[m(L,{value:r.form.piecePrice,"onUpdate:value":l[16]||(l[16]=e=>r.form.piecePrice=e),placeholder:"请输入单份价格",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):v("",!0),"VARIABLE"===r.form.pricingType?(d(),o(N,{key:3,md:12,sm:24},{default:n((()=>[m(S,{label:"参考价格",name:"referencePrice"},{default:n((()=>[m(L,{value:r.form.referencePrice,"onUpdate:value":l[17]||(l[17]=e=>r.form.referencePrice=e),placeholder:"请输入参考价格(可选)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):v("",!0)])),_:1})):v("",!0),m(E,{gutter:16},{default:n((()=>[m(N,{span:24},{default:n((()=>[m(S,{label:"存储条件",name:"storageCondition","label-col":{span:3},"wrapper-col":{span:21}},{default:n((()=>[m(R,{value:r.form.storageCondition,"onUpdate:value":l[18]||(l[18]=e=>r.form.storageCondition=e),placeholder:"请输入存储条件",rows:2},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(E,{gutter:16},{default:n((()=>[m(N,{span:24},{default:n((()=>[m(S,{label:"备注",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:n((()=>[m(R,{value:r.form.remark,"onUpdate:value":l[19]||(l[19]=e=>r.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","visible","confirm-loading","onUpdate:visible","onOk"])}]]))}}}));
