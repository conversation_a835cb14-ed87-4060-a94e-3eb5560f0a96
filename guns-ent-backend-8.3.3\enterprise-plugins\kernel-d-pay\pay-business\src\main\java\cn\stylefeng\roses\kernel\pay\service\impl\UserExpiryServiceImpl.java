package cn.stylefeng.roses.kernel.pay.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.pay.api.entity.UserExpiry;
import cn.stylefeng.roses.kernel.pay.mapper.UserExpiryMapper;
import cn.stylefeng.roses.kernel.pay.service.UserExpiryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 用户商品到期时间业务实现层
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@Service
public class UserExpiryServiceImpl extends ServiceImpl<UserExpiryMapper, UserExpiry> implements UserExpiryService {

    @Override
    public void addUserExpiryDays(Long userId, Long goodsId, int days) {

        // 获取当前时间是多少
        Date currentDate = new Date();

        // 获取用户针对某个商品的到期时间
        LambdaQueryWrapper<UserExpiry> queryWrapper = new LambdaQueryWrapper<UserExpiry>()
                .eq(UserExpiry::getUserId, userId)
                .eq(UserExpiry::getGoodsId, goodsId);
        UserExpiry userExpiry = this.getOne(queryWrapper, false);

        // 如果查询到空的，则初始化一个
        boolean addFlag = false;
        if (ObjectUtil.isEmpty(userExpiry)) {
            addFlag = true;
            userExpiry = new UserExpiry();
            userExpiry.setUserId(userId);
            userExpiry.setGoodsId(goodsId);
            userExpiry.setExpiryDate(currentDate);
        }

        // 如果查询到的不是空的
        else {

            // 判断是否已经过期了，过期了就重置一下到当前时间
            if (userExpiry.getExpiryDate().before(currentDate)) {
                userExpiry.setExpiryDate(currentDate);
            }
        }

        // 开始累加天数
        DateTime finalDate = DateUtil.offsetDay(userExpiry.getExpiryDate(), days);
        userExpiry.setExpiryDate(finalDate);

        // 如果是新增的则插入
        if (addFlag) {
            this.save(userExpiry);
        } else {
            this.updateById(userExpiry);
        }

    }

    @Override
    public void addUserExpiryMonths(Long userId, Long goodsId, int months) {
        this.addUserExpiryDays(userId, goodsId, months * 30);
    }

}
