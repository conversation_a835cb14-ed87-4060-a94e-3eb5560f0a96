import{R as e}from"./index-18a1ea24.js";class o{static page(t){return e.getAndLoadData("/erp/inventoryAlert/record/page",t)}static findPage(t){return this.page(t)}static detail(t){return e.getAndLoadData("/erp/inventoryAlert/record/detail",t)}static handle(t){return e.post("/erp/inventoryAlert/record/handle",t)}static batchHandle(t){return e.post("/erp/inventoryAlert/record/batchHandle",t)}static ignore(t){return e.post("/erp/inventoryAlert/record/ignore",t)}static getStatistics(t){return e.getAndLoadData("/erp/inventoryAlert/record/statistics",t)}static exportRecords(t){return e.downLoad("/erp/inventoryAlert/record/export",t)}static getRecentRecords(t){return e.getAndLoadData("/erp/inventoryAlert/record/recent",t)}static getRecordsByProduct(t){return e.getAndLoadData("/erp/inventoryAlert/record/byProduct",t)}static getOverview(t){return e.getAndLoadData("/erp/inventoryAlert/record/overview",t)}static delete(t){return e.post("/erp/inventoryAlert/record/delete",t)}static getHistory(t){return e.getAndLoadData("/erp/inventoryAlert/record/history",t)}}export{o as I};
