import Request from '@/utils/request/request-util';

/**
 * 采购入库管理API
 *
 * <AUTHOR>
 * @since 2025/01/28 15:00
 */
export class PurchaseApi {

  /**
   * 新增采购入库单
   * @param {Object} params 采购入库单信息
   * @returns {Promise}
   */
  static add(params) {
    return Request.post('/purchase/order/add', params);
  }

  /**
   * 删除采购入库单
   * @param {Object} params 包含id的参数
   * @returns {Promise}
   */
  static delete(params) {
    return Request.post('/purchase/order/delete', params);
  }

  /**
   * 编辑采购入库单
   * @param {Object} params 采购入库单信息
   * @returns {Promise}
   */
  static edit(params) {
    return Request.post('/purchase/order/edit', params);
  }

  /**
   * 查询采购入库单详情
   * @param {Object} params 包含id的参数
   * @returns {Promise}
   */
  static detail(params) {
    return Request.getAndLoadData('/purchase/order/detail', params);
  }

  /**
   * 分页查询采购入库单列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findPage(params) {
    return Request.getAndLoadData('/purchase/order/page', params);
  }

  /**
   * 查询采购入库单列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findList(params) {
    return Request.getAndLoadData('/purchase/order/list', params);
  }

  /**
   * 确认采购入库单
   * @param {Object} params 包含id的参数
   * @returns {Promise}
   */
  static confirm(params) {
    return Request.post('/purchase/order/confirm', params);
  }

  /**
   * 执行入库操作
   * @param {Object} params 入库请求参数
   * @returns {Promise}
   */
  static receive(params) {
    return Request.post('/purchase/order/receive', params);
  }

  /**
   * 生成采购入库单号
   * @returns {Promise}
   */
  static generateOrderNo() {
    return Request.getAndLoadData('/purchase/order/generateOrderNo');
  }

  /**
   * 验证供应商经营方式
   * @param {Object} params 包含supplierId的参数
   * @returns {Promise}
   */
  static validateSupplierBusinessMode(params) {
    return Request.getAndLoadData('/purchase/order/validateSupplierBusinessMode', params);
  }

  /**
   * 取消采购入库单
   * @param {Object} params 包含purchaseId的参数
   * @returns {Promise}
   */
  static cancel(params) {
    return Request.post('/erp/purchase/cancel', params);
  }

  /**
   * 获取采购入库单状态选项
   * @returns {Array}
   */
  static getPurchaseStatusOptions() {
    return [
      { label: '草稿', value: 'DRAFT' },
      { label: '已确认', value: 'CONFIRMED' },
      { label: '已完成', value: 'COMPLETED' }
    ];
  }

  /**
   * 获取采购类型选项
   * @returns {Array}
   */
  static getPurchaseTypeOptions() {
    return [
      { label: '普通采购', value: 'NORMAL' },
      { label: '紧急采购', value: 'URGENT' },
      { label: '补货采购', value: 'REPLENISHMENT' }
    ];
  }

  /**
   * 获取采购入库单状态名称
   * @param {String} status 采购入库单状态
   * @returns {String}
   */
  static getPurchaseStatusName(status) {
    const options = PurchaseApi.getPurchaseStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? option.label : status;
  }

  /**
   * 获取采购类型名称
   * @param {String} type 采购类型
   * @returns {String}
   */
  static getPurchaseTypeName(type) {
    const options = PurchaseApi.getPurchaseTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取状态标签颜色
   * @param {String} status 采购入库单状态
   * @returns {String}
   */
  static getStatusTagColor(status) {
    switch (status) {
      case 'DRAFT':
        return 'orange';
      case 'CONFIRMED':
        return 'blue';
      case 'COMPLETED':
        return 'green';
      default:
        return 'default';
    }
  }

  /**
   * 获取采购类型标签颜色
   * @param {String} type 采购类型
   * @returns {String}
   */
  static getPurchaseTypeTagColor(type) {
    switch (type) {
      case 'NORMAL':
        return 'blue';
      case 'URGENT':
        return 'red';
      case 'REPLENISHMENT':
        return 'green';
      default:
        return 'default';
    }
  }

  /**
   * 校验采购入库单是否可以确认
   * @param {Object} params 包含purchaseId的参数
   * @returns {Promise}
   */
  static validateConfirm(params) {
    return Request.getAndLoadData('/erp/purchase/validateConfirm', params);
  }

  /**
   * 校验采购入库单是否可以入库
   * @param {Object} params 包含purchaseId的参数
   * @returns {Promise}
   */
  static validateInbound(params) {
    return Request.getAndLoadData('/erp/purchase/validateInbound', params);
  }

  /**
   * 获取采购入库单明细列表
   * @param {Object} params 包含purchaseId的参数
   * @returns {Promise}
   */
  static getPurchaseItems(params) {
    return Request.getAndLoadData('/erp/purchase/items', params);
  }

  /**
   * 新增采购入库单明细
   * @param {Object} params 采购入库单明细信息
   * @returns {Promise}
   */
  static addPurchaseItem(params) {
    return Request.post('/erp/purchase/addItem', params);
  }

  /**
   * 编辑采购入库单明细
   * @param {Object} params 采购入库单明细信息
   * @returns {Promise}
   */
  static editPurchaseItem(params) {
    return Request.post('/erp/purchase/editItem', params);
  }

  /**
   * 删除采购入库单明细
   * @param {Object} params 包含itemId的参数
   * @returns {Promise}
   */
  static deletePurchaseItem(params) {
    return Request.post('/erp/purchase/deleteItem', params);
  }

  /**
   * 批量导入采购入库单明细
   * @param {Object} params 包含purchaseId和items的参数
   * @returns {Promise}
   */
  static batchImportItems(params) {
    return Request.post('/erp/purchase/batchImportItems', params);
  }

  /**
   * 导出采购入库单
   * @param {Object} params 导出参数
   * @returns {void}
   */
  static exportPurchase(params) {
    return Request.downLoad('/erp/purchase/export', params);
  }

  /**
   * 获取采购统计信息
   * @param {Object} params 统计参数
   * @returns {Promise}
   */
  static getPurchaseStatistics(params) {
    return Request.getAndLoadData('/erp/purchase/statistics', params);
  }

  /**
   * 格式化金额显示
   * @param {Number} amount 金额
   * @returns {String}
   */
  static formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toFixed(2);
  }

  /**
   * 格式化数量显示
   * @param {Number} quantity 数量
   * @param {String} unit 单位
   * @returns {String}
   */
  static formatQuantity(quantity, unit) {
    if (!quantity) return '-';
    return `${quantity}${unit || ''}`;
  }

  /**
   * 计算采购入库单总金额
   * @param {Array} items 采购入库单明细列表
   * @returns {Number}
   */
  static calculateTotalAmount(items) {
    if (!items || items.length === 0) return 0;
    return items.reduce((total, item) => {
      return total + (item.quantity * item.unitPrice);
    }, 0);
  }

  /**
   * 计算采购入库单总数量
   * @param {Array} items 采购入库单明细列表
   * @returns {Number}
   */
  static calculateTotalQuantity(items) {
    if (!items || items.length === 0) return 0;
    return items.reduce((total, item) => {
      return total + item.quantity;
    }, 0);
  }

}