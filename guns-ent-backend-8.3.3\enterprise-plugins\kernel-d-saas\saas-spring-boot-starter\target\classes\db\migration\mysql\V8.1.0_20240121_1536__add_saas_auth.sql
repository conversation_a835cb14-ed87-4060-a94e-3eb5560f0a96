CREATE TABLE `ent_tenant_link`  (
  `tenant_link_id` bigint NOT NULL COMMENT '主键id',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `package_id` bigint NOT NULL COMMENT '功能包id',
  `service_end_time` datetime NULL DEFAULT NULL COMMENT '服务结束时间，空则是长期',
  `trial_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否为试用：Y-试用，N-非试用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  PRIMARY KEY (`tenant_link_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户和功能包的关联' ROW_FORMAT = Dynamic;

CREATE TABLE `ent_tenant_package`  (
  `package_id` bigint NOT NULL COMMENT '主键id',
  `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能包名称',
  `package_price` decimal(10, 2) NOT NULL COMMENT '功能包价格',
  `version_flag` bigint NULL DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  PRIMARY KEY (`package_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户-功能包' ROW_FORMAT = Dynamic;

CREATE TABLE `ent_tenant_package_auth`  (
  `package_auth_id` bigint NOT NULL COMMENT '主键',
  `package_id` bigint NOT NULL COMMENT '功能包id',
  `limit_type` tinyint NOT NULL COMMENT '功能包绑定权限类型：1-菜单，2-功能',
  `business_id` bigint NOT NULL COMMENT '业务id，为菜单id或菜单功能id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`package_auth_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户-功能包授权范围' ROW_FORMAT = Dynamic;

UPDATE `sys_menu` SET `menu_parent_id` = -1, `menu_pids` = '[-1],', `menu_name` = '租户管理', `menu_code` = 'TENANT', `app_id` = 1701801589128577026, `menu_sort` = 100.00, `status_flag` = 1, `remark` = NULL, `menu_type` = 10, `antdv_router` = '/tenant', `antdv_component` = '', `antdv_icon` = 'icon-menu-zuhuguanli', `antdv_link_url` = NULL, `antdv_active_url` = NULL, `antdv_visible` = 'Y', `expand_field` = NULL, `version_flag` = 1, `del_flag` = 'N', `create_time` = '2023-09-13 11:37:50', `create_user` = 1339550467939639299, `update_time` = '2024-01-23 11:06:34', `update_user` = 1339550467939639299 WHERE `menu_id` = 1701802336574521345;
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749629803979042817, 1701802336574521345, '[-1],[1701802336574521345],', '租户管理', 'zuhuguanli1', 1701801589128577026, 100.00, 1, NULL, 10, '/tenant/manage', '/tenant/manage/index', 'icon-menu-zuhuguanli', NULL, NULL, 'Y', NULL, 0, 'N', '2024-01-23 11:07:06', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749630086079541249, 1701802336574521345, '[-1],[1701802336574521345],', '功能包管理', 'gongnengbaoguanli', 1701801589128577026, 110.00, 1, NULL, 10, '/tenant/package', '/tenant/package/index', 'icon-shujuku', NULL, NULL, 'Y', NULL, 0, 'N', '2024-01-23 11:08:14', 1339550467939639299, NULL, NULL);

INSERT INTO `ent_tenant_package`(`package_id`, `package_name`, `package_price`, `version_flag`, `create_time`, `create_user`, `update_time`, `update_user`, `del_flag`) VALUES (1749698214931165185, '基础系统管理', 0.00, 0, '2024-01-23 15:38:57', 1339550467939639299, NULL, NULL, 'N');
INSERT INTO `ent_tenant_package`(`package_id`, `package_name`, `package_price`, `version_flag`, `create_time`, `create_user`, `update_time`, `update_user`, `del_flag`) VALUES (1749698250385616898, 'API管理', 1000.00, 0, '2024-01-23 15:39:05', 1339550467939639299, NULL, NULL, 'N');

INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698263543148545, 1749698214931165185, 1, 1671407539607171073, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698263543148546, 1749698214931165185, 2, 1671416717948006401, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698263543148547, 1749698214931165185, 2, 1671416755763851265, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698263543148548, 1749698214931165185, 2, 1677205540070064129, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698263543148550, 1749698214931165185, 2, 1677205870040154114, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698263543148551, 1749698214931165185, 2, 1677205994816503809, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698299991650305, 1749698214931165185, 2, 1677205784526684162, '2024-01-23 15:39:17', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698316001308674, 1749698214931165185, 1, 1671407615163363330, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698316001308675, 1749698214931165185, 2, 1675495204221640706, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698316001308676, 1749698214931165185, 2, 1677213466805501954, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698316001308677, 1749698214931165185, 2, 1677213504298385410, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698316001308678, 1749698214931165185, 2, 1677213572741038081, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698345969610753, 1749698214931165185, 1, 1671407652933070850, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698345969610754, 1749698214931165185, 2, 1677212372381564929, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698345969610755, 1749698214931165185, 2, 1677212407240425474, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698345969610756, 1749698214931165185, 2, 1677212448021643265, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698388319498242, 1749698214931165185, 1, 1671406619464953857, '2024-01-23 15:39:38', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698388319498243, 1749698214931165185, 1, 1671407186899759106, '2024-01-23 15:39:38', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698388319498244, 1749698214931165185, 2, 1677199976008040449, '2024-01-23 15:39:38', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698424969326593, 1749698250385616898, 1, 1718826580437897217, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698424969326594, 1749698250385616898, 1, 1718826709295304705, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698425036435457, 1749698250385616898, 1, 1718826323209621506, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth`(`package_auth_id`, `package_id`, `limit_type`, `business_id`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1749698425036435458, 1749698250385616898, 1, 1718824432245411841, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
