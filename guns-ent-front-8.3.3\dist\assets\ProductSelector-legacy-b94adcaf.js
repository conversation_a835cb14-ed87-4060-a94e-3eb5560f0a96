System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./ProductApi-legacy-33feae42.js"],(function(e,t){"use strict";var a,r,l,o,d,n,c,i,u,s,p,f,g,v,h,y,C,m,w,S;return{setters:[e=>{a=e._,r=e.r,l=e.L,o=e.X,d=e.o,n=e.a,c=e.f,i=e.w,u=e.c,s=e.F,p=e.e,f=e.b,g=e.t,v=e.g,h=e.h,y=e.m,C=e.U,m=e.W,w=e.J},null,e=>{S=e.P}],execute:function(){var t=document.createElement("style");t.textContent=".product-option[data-v-daa82fff]{padding:4px 0}.product-name[data-v-daa82fff]{font-weight:500;color:#262626;margin-bottom:2px}.product-info[data-v-daa82fff]{display:flex;align-items:center;gap:8px;flex-wrap:wrap}.product-code[data-v-daa82fff]{font-size:12px;color:#8c8c8c}.product-stock[data-v-daa82fff]{font-size:12px;color:#1890ff}.ant-tag[data-v-daa82fff]{margin:0}\n",document.head.appendChild(t);const b={name:"ProductSelector",props:{value:{type:[String,Number],default:void 0},placeholder:{type:String,default:"请选择商品"},allowClear:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},filter:{type:Object,default:()=>({})}},emits:["update:value","change"],setup(e,{emit:t}){const a=r(!1),n=r([]),c=r(""),i=l({get:()=>e.value,set:e=>{t("update:value",e)}}),u=l((()=>{let t=n.value;if(e.filter.businessModeList&&e.filter.businessModeList.length>0&&(t=t.filter((t=>e.filter.businessModeList.includes(t.businessMode)))),e.filter.pricingType&&(t=t.filter((t=>t.pricingType===e.filter.pricingType))),c.value){const e=c.value.toLowerCase();t=t.filter((t=>t.productName.toLowerCase().includes(e)||t.productCode.toLowerCase().includes(e)||t.barcode&&t.barcode.toLowerCase().includes(e)))}return t}));return o((()=>e.filter),(()=>{e.value&&u.value.length>0&&(u.value.find((t=>t.productId===e.value))||(i.value=void 0,t("change",void 0,null)))}),{deep:!0}),d((()=>{(async()=>{a.value=!0;try{const e=await S.findList({status:"Y",pageSize:1e3});e.success&&(n.value=e.data||[])}catch(e){console.error("加载商品列表失败:",e),y.error("加载商品列表失败")}finally{a.value=!1}})()})),{loading:a,selectedValue:i,filteredProducts:u,handleSearch:e=>{c.value=e},handleChange:(e,a)=>{const r=n.value.find((t=>t.productId===e));t("change",e,r)},handleClear:()=>{t("change",void 0,null)},getPricingTypeColor:e=>{switch(e){case"NORMAL":return"blue";case"WEIGHT":return"orange";case"PIECE":return"green";case"VARIABLE":return"purple";default:return"default"}},getStockUnit:(e,t)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"件";default:return t||"个"}},formatStock:(e,t)=>{if(!e)return"0";const a="WEIGHT"===t?3:0;return parseFloat(e).toFixed(a)}}}},T={class:"product-option"},k={class:"product-name"},x={class:"product-info"},I={class:"product-code"},L={key:1,class:"product-stock"};e("_",a(b,[["render",function(e,t,a,r,l,o){const d=C,y=m,S=w;return n(),c(S,{value:r.selectedValue,"onUpdate:value":t[0]||(t[0]=e=>r.selectedValue=e),placeholder:a.placeholder,loading:r.loading,allowClear:a.allowClear,disabled:a.disabled,showSearch:"",filterOption:!1,onSearch:r.handleSearch,onChange:r.handleChange,onClear:r.handleClear,style:{width:"100%"}},{default:i((()=>[(n(!0),u(s,null,p(r.filteredProducts,(e=>(n(),c(y,{key:e.productId,value:e.productId,title:`${e.productName} (${e.productCode})`},{default:i((()=>[f("div",T,[f("div",k,g(e.productName),1),f("div",x,[f("span",I,g(e.productCode),1),e.pricingTypeName?(n(),c(d,{key:0,size:"small",color:r.getPricingTypeColor(e.pricingType)},{default:i((()=>[v(g(e.pricingTypeName),1)])),_:2},1032,["color"])):h("",!0),void 0!==e.currentStock?(n(),u("span",L," 库存: "+g(r.formatStock(e.currentStock,e.pricingType))+" "+g(r.getStockUnit(e.pricingType,e.unit)),1)):h("",!0)])])])),_:2},1032,["value","title"])))),128))])),_:1},8,["value","placeholder","loading","allowClear","disabled","onSearch","onChange","onClear"])}],["__scopeId","data-v-daa82fff"]]))}}}));
