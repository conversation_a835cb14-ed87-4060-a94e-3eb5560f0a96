-------------------------------------------------------------------------------
Test set: cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest
-------------------------------------------------------------------------------
Tests run: 14, Failures: 2, Errors: 10, Skipped: 0, Time elapsed: 1.775 s <<< FAILURE! -- in cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest
cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testCategoryQuery -- Time elapsed: 1.631 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeCategoryQuery(UnifiedProductQueryServiceImpl.java:127)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:95)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testCategoryQuery(UnifiedProductQueryServiceTest.java:102)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testAllQuery -- Time elapsed: 0.006 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testAllQuery(UnifiedProductQueryServiceTest.java:171)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testBrandFilter -- Time elapsed: 0.005 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testBrandFilter(UnifiedProductQueryServiceTest.java:267)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testParameterValidation -- Time elapsed: 0.013 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <java.lang.IllegalArgumentException> but was: <java.lang.NullPointerException>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testParameterValidation(UnifiedProductQueryServiceTest.java:213)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.erp.api.pojo.request.UnifiedProductQueryRequest$QueryMode.ordinal()" because "this.queryMode" is null
	at cn.stylefeng.roses.kernel.erp.api.pojo.request.UnifiedProductQueryRequest.validate(UnifiedProductQueryRequest.java:201)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.lambda$testParameterValidation$0(UnifiedProductQueryServiceTest.java:214)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSearchQuery -- Time elapsed: 0.009 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.getSearchSuggestions(UnifiedProductQueryServiceImpl.java:385)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeSearchQuery(UnifiedProductQueryServiceImpl.java:157)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:97)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSearchQuery(UnifiedProductQueryServiceTest.java:129)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSearchSuggestions -- Time elapsed: 0.011 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.getSearchSuggestions(UnifiedProductQueryServiceImpl.java:385)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSearchSuggestions(UnifiedProductQueryServiceTest.java:189)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testCacheClear -- Time elapsed: 0.005 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception thrown: java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.remove(String[])" because "this.cacheOperatorApi" is null
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:152)
	at org.junit.jupiter.api.AssertDoesNotThrow.createAssertionFailedError(AssertDoesNotThrow.java:84)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:53)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:36)
	at org.junit.jupiter.api.Assertions.assertDoesNotThrow(Assertions.java:3168)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testCacheClear(UnifiedProductQueryServiceTest.java:327)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.remove(String[])" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.clearQueryCache(UnifiedProductQueryServiceImpl.java:460)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.lambda$testCacheClear$5(UnifiedProductQueryServiceTest.java:328)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:49)
	... 6 more

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testHotSearchKeywords -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.getHotSearchKeywords(UnifiedProductQueryServiceImpl.java:412)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testHotSearchKeywords(UnifiedProductQueryServiceTest.java:199)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSortingOptions -- Time elapsed: 0.007 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSortingOptions(UnifiedProductQueryServiceTest.java:302)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPerformanceMetrics -- Time elapsed: 0.005 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeCategoryQuery(UnifiedProductQueryServiceImpl.java:127)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:95)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPerformanceMetrics(UnifiedProductQueryServiceTest.java:347)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPriceRangeFilter -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPriceRangeFilter(UnifiedProductQueryServiceTest.java:250)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPricingTypeFilter -- Time elapsed: 0.004 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPricingTypeFilter(UnifiedProductQueryServiceTest.java:284)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

