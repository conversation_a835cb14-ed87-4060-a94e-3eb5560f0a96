System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js"],(function(e,l){"use strict";var a,t,d,u,r,o,n,v,i,c,s,p,m,y,f,b,S,h,g;return{setters:[e=>{a=e.r,t=e.L,d=e.o,u=e.R,r=e.bd,o=e.X,n=e.a,v=e.f,i=e.w,c=e.c,s=e.F,p=e.e,m=e.g,y=e.t,f=e.at,b=e.W,S=e.J,h=e.d,g=e.a0},null],execute:function(){const l={__name:"index",props:{value:{type:[String,Array],default:void 0},record:{type:Object,default:{}},formData:{type:Object,default:{}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择"},readonly:{type:Boolean,default:!1},isDesgin:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},dataSources:{type:Number,default:1},backendUrl:{type:String,default:""},valueType:{type:Number,default:1},dataList:{type:Array,default:[]}},emits:["update:value"],setup(e,{emit:l}){const h=e,g=l,k=a(null),L=a([]),U=a(""),x=t((()=>{var e;return!(null!==(e=h.record)&&void 0!==e&&e.itemMultipleChoiceFlag||h.multiple)})),T=t((()=>{var e;return null!==(e=h.record)&&void 0!==e&&null!==(e=e.formItemSelect)&&void 0!==e&&e.dataFromType?h.record.formItemSelect.dataFromType:h.dataSources?h.dataSources:1})),w=t((()=>{var e,l;return null!==(e=h.record)&&void 0!==e&&null!==(e=e.formItemSelect)&&void 0!==e&&e.valueType?null===(l=h.record)||void 0===l||null===(l=l.formItemSelect)||void 0===l?void 0:l.valueType:h.valueType?h.valueType:1})),I=t((()=>{var e,l;return null!==(e=h.record)&&void 0!==e&&null!==(e=e.formItemSelect)&&void 0!==e&&e.backendUrl?null===(l=h.record)||void 0===l||null===(l=l.formItemSelect)||void 0===l?void 0:l.backendUrl:h.backendUrl?h.backendUrl:""}));d((()=>{var e;null!==(e=h.record)&&void 0!==e&&null!==(e=e.formItemSelect)&&void 0!==e&&e.linkFieldCode&&(U.value=h.record.formItemSelect.linkFieldCode),2==h.dataSources&&(L.value=h.dataList),_(),D()}));const _=e=>{if(h.isDesgin||1!=T.value||!I.value)return;let l={searchText:e};U.value&&(l[U.value]=h.formData[U.value],l.valueType=w.value),u.get(I.value,l).then((e=>{h.backendUrl?L.value=e.data:h.record.dictList=e.data}))},C=r((e=>{_(e)}),500),F=(e,l)=>l.label.toLowerCase().indexOf(e.toLowerCase())>=0,B=()=>{let e="";var l;e=x.value||h.normal?k.value:0===(null===(l=k.value)||void 0===l?void 0:l.length)?"":JSON.stringify(k.value),g("update:value",e)},D=()=>{x.value?k.value=h.value:k.value=h.value?h.normal?h.value:JSON.parse(h.value):[]};return o((()=>h.formData[U.value]),(e=>{e&&_()}),{deep:!0}),o((()=>h.value),(e=>{D()}),{deep:!0}),o((()=>{var e;return null===(e=h.record)||void 0===e?void 0:e.dictList}),(e=>{e&&(L.value=e)}),{deep:!0,immediate:!0}),(l,a)=>{const t=b,d=S;return n(),v(d,{"show-search":"",allowClear:"",value:k.value,"onUpdate:value":a[0]||(a[0]=e=>k.value=e),onChange:B,style:{width:"100%"},placeholder:e.placeholder,disabled:e.readonly||e.disabled,mode:x.value?"":"multiple","filter-option":1!=T.value&&F,onSearch:f(C)},{default:i((()=>[(n(!0),c(s,null,p(L.value,(e=>(n(),v(t,{value:1==w.value?e.id:e.code,key:e.id,label:e.name},{default:i((()=>[m(y(e.name),1)])),_:2},1032,["value","label"])))),128))])),_:1},8,["value","placeholder","disabled","mode","filter-option","onSearch"])}}},k={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const t=a(null),d=a(null),u=a(!1),r=a(!1),o=a("请选择"),v=a([{id:"1",name:"张三"},{id:"2",name:"李四"}]);return(e,a)=>{const s=l,p=g;return n(),c("div",k,[h(p,{title:"自定义选择-自定义填充数据",bordered:!1},{default:i((()=>[h(s,{value:t.value,"onUpdate:value":a[0]||(a[0]=e=>t.value=e),disabled:u.value,readonly:r.value,dataList:v.value,dataSources:2,multiple:!1,placeholder:o.value,style:{width:"300px"}},null,8,["value","disabled","readonly","dataList","placeholder"])])),_:1}),h(p,{title:"自定义选择-动态接口（只支持get）",bordered:!1},{default:i((()=>[h(s,{value:d.value,"onUpdate:value":a[1]||(a[1]=e=>d.value=e),disabled:u.value,readonly:r.value,dataSources:1,backendUrl:"/my/user/selectList",multiple:!1,placeholder:o.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])])),_:1})])}}})}}}));
