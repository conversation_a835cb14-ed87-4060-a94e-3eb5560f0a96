package cn.stylefeng.roses.seata.demo.account.modular.service;

import io.seata.rm.tcc.api.BusinessActionContext;
import io.seata.rm.tcc.api.BusinessActionContextParameter;
import io.seata.rm.tcc.api.LocalTCC;
import io.seata.rm.tcc.api.TwoPhaseBusinessAction;

/**
 * TCC方式更新账户
 *
 * <AUTHOR>
 * @date 2021/10/13 21:36
 */
@LocalTCC
public interface TccUpdateAccountService {

    /**
     * TCC方式更新账户主方法
     * <p>
     * TwoPhaseBusinessAction注解的name属性是tcc的bean名称，全局唯一
     * BusinessActionContextParameter注解 传递参数到二阶段中
     *
     * <AUTHOR>
     * @date 2021/10/13 21:39
     */
    @TwoPhaseBusinessAction(name = "updateAccountTry", commitMethod = "updateAccountConfirm", rollbackMethod = "updateAccountCancel")
    boolean updateAccountTry(@BusinessActionContextParameter(paramName = "userId") String userId,
                          @BusinessActionContextParameter(paramName = "subMoney") int subMoney);

    /**
     * 确认方法
     *
     * <AUTHOR>
     * @date 2021/10/13 21:44
     */
    boolean updateAccountConfirm(BusinessActionContext context);

    /**
     * 回滚方法
     *
     * <AUTHOR>
     * @date 2021/10/13 21:44
     */
    boolean updateAccountCancel(BusinessActionContext context);

}