package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 供应商模块异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/22 16:30
 */
@Getter
public enum ErpSupplierExceptionEnum implements AbstractExceptionEnum {

    /**
     * 供应商不存在
     */
    SUPPLIER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "供应商不存在"),

    /**
     * 供应商编码已存在
     */
    SUPPLIER_CODE_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "供应商编码已存在"),

    /**
     * 供应商状态错误
     */
    SUPPLIER_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "供应商状态错误，只能为：正常、停用、黑名单"),

    /**
     * 供应商已关联业务数据，无法删除
     */
    SUPPLIER_HAS_BUSINESS_DATA_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10004", "供应商已关联业务数据，无法删除"),

    /**
     * 供应商已关联业务数据，无法停用
     */
    SUPPLIER_HAS_BUSINESS_DATA_CANNOT_DISABLE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10005", "供应商已关联业务数据，无法停用"),

    /**
     * 供应商已关联业务数据，无法设为停用
     */
    SUPPLIER_HAS_BUSINESS_DATA_CANNOT_INACTIVE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10006", "供应商已关联业务数据，无法设为停用"),

    /**
     * 供应商ID列表为空
     */
    SUPPLIER_ID_LIST_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10007", "供应商ID列表为空"),

    /**
     * 区域ID不能为空
     */
    REGION_ID_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10008", "区域ID不能为空"),

    /**
     * 区域ID列表为空
     */
    REGION_ID_LIST_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10009", "区域ID列表为空"),
    
    /**
     * 供应商类型错误
     */
    SUPPLIER_TYPE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10010", "供应商类型错误，只能为：企业、个体"),
    
    /**
     * 供应商信用等级错误
     */
    SUPPLIER_CREDIT_LEVEL_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10011", "供应商信用等级错误，只能为：A-优秀，B-良好，C-一般，D-较差"),

    /**
     * 供应商经营方式错误
     */
    SUPPLIER_BUSINESS_MODE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10012", "供应商经营方式错误，只能为：购销、联营、代销");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ErpSupplierExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }
}