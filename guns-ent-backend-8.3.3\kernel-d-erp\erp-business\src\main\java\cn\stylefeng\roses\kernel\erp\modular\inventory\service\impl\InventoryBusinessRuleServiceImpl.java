package cn.stylefeng.roses.kernel.erp.modular.inventory.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpSupplierConstants;
import cn.stylefeng.roses.kernel.erp.api.constants.InventoryConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.InventoryExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryOperationRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValidationResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryBusinessRuleService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存管理业务规则服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/28 11:30
 */
@Slf4j
@Service
public class InventoryBusinessRuleServiceImpl implements InventoryBusinessRuleService {

    @Override
    public boolean validateOperationType(String operationType) {
        if (StrUtil.isBlank(operationType)) {
            return false;
        }

        return InventoryConstants.OperationType.IN.equals(operationType) ||
                InventoryConstants.OperationType.OUT.equals(operationType) ||
                InventoryConstants.OperationType.ADJUST.equals(operationType) ||
                InventoryConstants.OperationType.SALE.equals(operationType);
    }

    @Override
    public boolean validateReferenceType(String referenceType) {
        if (StrUtil.isBlank(referenceType)) {
            return false;
        }

        return InventoryConstants.ReferenceType.PURCHASE_ORDER.equals(referenceType) ||
                InventoryConstants.ReferenceType.SALE_ORDER.equals(referenceType) ||
                InventoryConstants.ReferenceType.ADJUST_ORDER.equals(referenceType);
    }

    @Override
    public InventoryValidationResponse validateInventoryOperation(InventoryOperationRequest request) {
        InventoryValidationResponse response = new InventoryValidationResponse();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        if (request == null) {
            errors.add("库存操作请求不能为空");
            response.setCanOperate(false);
            response.setErrors(errors);
            response.setDescription("请求参数错误");
            return response;
        }

        // 验证商品ID
        if (request.getProductId() == null) {
            errors.add("商品ID不能为空");
        }

        // 验证操作类型
        if (!this.validateOperationType(request.getOperationType())) {
            errors.add("无效的操作类型：" + request.getOperationType());
        }

        // 验证操作数量
        if (request.getQuantity() == null || request.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("操作数量必须大于0");
        }

        // 验证入库操作的单位成本
        if (InventoryConstants.OperationType.IN.equals(request.getOperationType())) {
            if (request.getUnitCost() == null || request.getUnitCost().compareTo(BigDecimal.ZERO) < 0) {
                errors.add("入库操作必须提供有效的单位成本");
            }
        }

        // 验证关联单据类型
        if (StrUtil.isNotBlank(request.getReferenceType()) && 
            !this.validateReferenceType(request.getReferenceType())) {
            errors.add("无效的关联单据类型：" + request.getReferenceType());
        }

        // 验证调整操作
        if (InventoryConstants.OperationType.ADJUST.equals(request.getOperationType())) {
            if (request.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                errors.add("库存调整数量不能为0");
            }
        }

        response.setCanOperate(errors.isEmpty());
        response.setErrors(errors);
        response.setWarnings(warnings);

        if (response.getCanOperate()) {
            response.setDescription("库存操作验证通过");
            response.setSuggestion("可以执行库存操作");
        } else {
            response.setDescription("库存操作验证失败");
            response.setSuggestion("请修正错误后重试");
        }

        return response;
    }

    @Override
    public boolean needInventoryManagement(ErpProduct product, ErpSupplier supplier) {
        if (product == null || supplier == null) {
            return false;
        }

        // 联营模式的商品不进行库存管理
        if (ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE.equals(supplier.getBusinessMode())) {
            return false;
        }

        // 购销和代销模式需要库存管理
        return ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE.equals(supplier.getBusinessMode()) ||
                ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(supplier.getBusinessMode());
    }

    @Override
    public boolean isInventorySufficient(Inventory inventory, BigDecimal requiredQuantity) {
        if (inventory == null || requiredQuantity == null) {
            return false;
        }

        BigDecimal currentStock = inventory.getCurrentStock() != null ? 
                inventory.getCurrentStock() : BigDecimal.ZERO;

        return currentStock.compareTo(requiredQuantity) >= 0;
    }

    @Override
    public boolean needInventoryWarning(Inventory inventory) {
        if (inventory == null) {
            return false;
        }

        BigDecimal currentStock = inventory.getCurrentStock() != null ? 
                inventory.getCurrentStock() : BigDecimal.ZERO;
        BigDecimal minStock = inventory.getMinStock() != null ? 
                inventory.getMinStock() : BigDecimal.ZERO;

        return currentStock.compareTo(minStock) <= 0;
    }

    @Override
    public BigDecimal calculateTotalValue(BigDecimal currentStock, BigDecimal avgCost) {
        if (currentStock == null || avgCost == null) {
            return new BigDecimal("0.00");
        }

        return currentStock.multiply(avgCost).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateNewAvgCost(BigDecimal currentStock, BigDecimal currentAvgCost, 
                                          BigDecimal inQuantity, BigDecimal inCost) {
        if (currentStock == null || currentAvgCost == null || 
            inQuantity == null || inCost == null) {
            return new BigDecimal("0.00");
        }

        // 如果当前库存为0，直接返回入库成本
        if (currentStock.compareTo(BigDecimal.ZERO) == 0) {
            return inCost;
        }

        // 计算总成本
        BigDecimal currentTotalCost = currentStock.multiply(currentAvgCost);
        BigDecimal inTotalCost = inQuantity.multiply(inCost);
        BigDecimal newTotalCost = currentTotalCost.add(inTotalCost);

        // 计算新的总数量
        BigDecimal newTotalQuantity = currentStock.add(inQuantity);

        // 计算新的平均成本
        if (newTotalQuantity.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal("0.00");
        }

        return newTotalCost.divide(newTotalQuantity, 2, RoundingMode.HALF_UP);
    }

    @Override
    public String getInventoryStatus(Inventory inventory) {
        if (inventory == null) {
            return InventoryConstants.Status.OUT_OF_STOCK;
        }

        BigDecimal currentStock = inventory.getCurrentStock() != null ? 
                inventory.getCurrentStock() : BigDecimal.ZERO;

        // 缺货
        if (currentStock.compareTo(BigDecimal.ZERO) <= 0) {
            return InventoryConstants.Status.OUT_OF_STOCK;
        }

        // 预警
        if (this.needInventoryWarning(inventory)) {
            return InventoryConstants.Status.WARNING;
        }

        // 正常
        return InventoryConstants.Status.NORMAL;
    }

    @Override
    public String getInventoryStatusDescription(String status) {
        if (StrUtil.isBlank(status)) {
            return "未知状态";
        }

        switch (status) {
            case InventoryConstants.Status.NORMAL:
                return "库存正常";
            case InventoryConstants.Status.WARNING:
                return "库存预警";
            case InventoryConstants.Status.OUT_OF_STOCK:
                return "库存缺货";
            default:
                return "未知状态";
        }
    }

    @Override
    public void validateInventoryParams(Inventory inventory) {
        if (inventory == null) {
            throw new ServiceException("库存管理", 
                    InventoryExceptionEnum.INVENTORY_NOT_EXIST.getErrorCode(), 
                    "库存信息不能为空");
        }

        // 验证商品ID
        if (inventory.getProductId() == null) {
            throw new ServiceException("库存管理", 
                    InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        // 验证当前库存
        if (inventory.getCurrentStock() != null && 
            inventory.getCurrentStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("库存管理", 
                    InventoryExceptionEnum.INVENTORY_QUANTITY_MUST_GREATER_THAN_ZERO);
        }

        // 验证最小库存
        if (inventory.getMinStock() != null && 
            inventory.getMinStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("库存管理", 
                    InventoryExceptionEnum.MIN_STOCK_MUST_GREATER_THAN_OR_EQUAL_ZERO);
        }

        // 验证平均成本
        if (inventory.getAvgCost() != null && 
            inventory.getAvgCost().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("库存管理", 
                    InventoryExceptionEnum.AVG_COST_MUST_GREATER_THAN_OR_EQUAL_ZERO);
        }

        // 验证库存总价值
        if (inventory.getTotalValue() != null && 
            inventory.getTotalValue().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("库存管理", 
                    InventoryExceptionEnum.TOTAL_VALUE_MUST_GREATER_THAN_OR_EQUAL_ZERO);
        }

        log.info("库存参数验证通过，商品ID：{}，当前库存：{}，最小库存：{}，平均成本：{}，总价值：{}", 
                inventory.getProductId(), inventory.getCurrentStock(), inventory.getMinStock(), 
                inventory.getAvgCost(), inventory.getTotalValue());
    }

    @Override
    public boolean validateOperationQuantity(String operationType, BigDecimal quantity, BigDecimal currentStock) {
        if (StrUtil.isBlank(operationType) || quantity == null) {
            return false;
        }

        BigDecimal stock = currentStock != null ? currentStock : BigDecimal.ZERO;

        switch (operationType) {
            case InventoryConstants.OperationType.IN:
                // 入库数量必须大于0
                return quantity.compareTo(BigDecimal.ZERO) > 0;

            case InventoryConstants.OperationType.OUT:
            case InventoryConstants.OperationType.SALE:
                // 出库数量必须大于0且不能大于当前库存
                return quantity.compareTo(BigDecimal.ZERO) > 0 && 
                       quantity.compareTo(stock) <= 0;

            case InventoryConstants.OperationType.ADJUST:
                // 调整数量不能为0
                return quantity.compareTo(BigDecimal.ZERO) != 0;

            default:
                return false;
        }
    }

}