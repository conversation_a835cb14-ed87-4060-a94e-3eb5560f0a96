package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品请求参数
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpProductRequest extends BaseRequest {

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空", groups = {edit.class, delete.class, detail.class, updateStatus.class, validatePricingTypeChange.class})
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @NotBlank(message = "商品编码不能为空", groups = {add.class, edit.class})
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品简称
     */
    @ChineseDescription("商品简称")
    private String productShortName;

    /**
     * 条形码
     */
    @ChineseDescription("条形码")
    private String barcode;

    /**
     * 商品分类ID
     */
    @NotNull(message = "商品分类不能为空", groups = {add.class, edit.class})
    @ChineseDescription("商品分类ID")
    private Long categoryId;



    /**
     * 品牌
     */
    @ChineseDescription("品牌")
    private String brand;

    /**
     * 规格
     */
    @ChineseDescription("规格")
    private String specification;

    /**
     * 基本单位
     */
    @NotBlank(message = "基本单位不能为空", groups = {add.class, edit.class})
    @ChineseDescription("基本单位")
    private String unit;

    /**
     * 重量（kg）
     */
    @ChineseDescription("重量")
    private BigDecimal weight;

    /**
     * 体积（立方米）
     */
    @ChineseDescription("体积")
    private BigDecimal volume;

    /**
     * 保质期（天）
     */
    @ChineseDescription("保质期")
    private Integer shelfLife;

    /**
     * 存储条件
     */
    @ChineseDescription("存储条件")
    private String storageCondition;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，DISCONTINUED-停产）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商不能为空", groups = {add.class, edit.class, bySupplier.class})
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 计价类型：NORMAL(普通)、WEIGHT(计重)、PIECE(计件)、VARIABLE(不定价)
     */
    @NotBlank(message = "计价类型不能为空", groups = {add.class, edit.class, validatePricingTypeChange.class})
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 零售价格
     */
    @NotNull(message = "零售价格不能为空", groups = {add.class, edit.class})
    @ChineseDescription("零售价格")
    private BigDecimal retailPrice;

    /**
     * 单位价格（计重商品用）
     */
    @ChineseDescription("单位价格")
    private BigDecimal unitPrice;

    /**
     * 单份价格（计件商品用）
     */
    @ChineseDescription("单份价格")
    private BigDecimal piecePrice;

    /**
     * 参考价格（不定价商品用）
     */
    @ChineseDescription("参考价格")
    private BigDecimal referencePrice;

    /**
     * 商品ID列表（批量操作用）
     */
    @ChineseDescription("商品ID列表")
    private List<Long> productIdList;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：更新状态
     */
    public @interface updateStatus {
    }



    /**
     * 参数校验分组：按供应商查询
     */
    public @interface bySupplier {
    }

    /**
     * 参数校验分组：验证计价类型变更
     */
    public @interface validatePricingTypeChange {
    }
}