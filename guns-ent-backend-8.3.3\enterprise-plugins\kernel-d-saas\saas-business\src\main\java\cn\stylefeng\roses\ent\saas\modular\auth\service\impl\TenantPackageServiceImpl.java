package cn.stylefeng.roses.ent.saas.modular.auth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackage;
import cn.stylefeng.roses.ent.saas.modular.auth.enums.TenantPackageExceptionEnum;
import cn.stylefeng.roses.ent.saas.modular.auth.mapper.TenantPackageMapper;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantPackageRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantLinkService;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageService;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 租户-功能包业务实现层
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@Service
public class TenantPackageServiceImpl extends ServiceImpl<TenantPackageMapper, TenantPackage> implements TenantPackageService {

    @Resource
    private TenantLinkService tenantLinkService;

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    @Override
    public void add(TenantPackageRequest tenantPackageRequest) {
        TenantPackage tenantPackage = new TenantPackage();
        BeanUtil.copyProperties(tenantPackageRequest, tenantPackage);
        this.save(tenantPackage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(TenantPackageRequest tenantPackageRequest) {

        // 1. 删除功能包
        TenantPackage tenantPackage = this.queryTenantPackage(tenantPackageRequest);
        this.removeById(tenantPackage.getPackageId());

        // 2. 删除功能包绑定的租户
        tenantLinkService.removeByPackageId(tenantPackage.getPackageId());

        // 3. 删除功能包绑定的权限范围
        tenantPackageAuthService.removeByPackageId(tenantPackage.getPackageId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(TenantPackageRequest tenantPackageRequest) {
        this.removeByIds(tenantPackageRequest.getBatchDeleteIdList());
    }

    @Override
    public void edit(TenantPackageRequest tenantPackageRequest) {
        TenantPackage tenantPackage = this.queryTenantPackage(tenantPackageRequest);
        BeanUtil.copyProperties(tenantPackageRequest, tenantPackage);
        this.updateById(tenantPackage);
    }

    @Override
    public TenantPackage detail(TenantPackageRequest tenantPackageRequest) {
        return this.queryTenantPackage(tenantPackageRequest);
    }

    @Override
    public PageResult<TenantPackage> findPage(TenantPackageRequest tenantPackageRequest) {
        LambdaQueryWrapper<TenantPackage> wrapper = createWrapper(tenantPackageRequest);
        Page<TenantPackage> pageList = this.page(PageFactory.defaultPage(), wrapper);
        return PageResultFactory.createPageResult(pageList);
    }

    @Override
    public List<TenantPackage> findList(TenantPackageRequest tenantPackageRequest) {
        LambdaQueryWrapper<TenantPackage> wrapper = this.createWrapper(tenantPackageRequest);
        return this.list(wrapper);
    }

    /**
     * 获取信息
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    private TenantPackage queryTenantPackage(TenantPackageRequest tenantPackageRequest) {
        TenantPackage tenantPackage = this.getById(tenantPackageRequest.getPackageId());
        if (ObjectUtil.isEmpty(tenantPackage)) {
            throw new ServiceException(TenantPackageExceptionEnum.TENANT_PACKAGE_NOT_EXISTED);
        }
        return tenantPackage;
    }

    /**
     * 创建查询wrapper
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    private LambdaQueryWrapper<TenantPackage> createWrapper(TenantPackageRequest tenantPackageRequest) {
        LambdaQueryWrapper<TenantPackage> queryWrapper = new LambdaQueryWrapper<>();

        // 根据包名称查询
        String searchText = tenantPackageRequest.getSearchText();
        if (StrUtil.isNotBlank(searchText)) {
            queryWrapper.like(TenantPackage::getPackageName, searchText);
        }

        return queryWrapper;
    }

}
