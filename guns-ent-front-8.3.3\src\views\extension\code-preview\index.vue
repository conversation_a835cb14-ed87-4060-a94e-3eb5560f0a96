<template>
  <div class="guns-body guns-body-card">
    <a-card title="代码预览" :bordered="false">
      <guns-code :code="source" height="480px" style="width: 400px" />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const source = ref(
  '{"dimensions":["product","data1","data2"],"source":[{"product":"Mon","data1":120,"data2":130},{"product":"Tue","data1":200,"data2":130},{"product":"Wed","data1":150,"data2":312},{"product":"Thu","data1":80,"data2":268},{"product":"Fri","data1":70,"data2":155},{"product":"Sat","data1":110,"data2":117},{"product":"Sun","data1":130,"data2":160}]}'
);
</script>

<style></style>
