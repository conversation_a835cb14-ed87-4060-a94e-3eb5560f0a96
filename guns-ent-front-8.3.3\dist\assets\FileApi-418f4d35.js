import{R as e,bh as o}from"./index-18a1ea24.js";const i="/sysFileInfo/upload";class a{static findSelectPageList(t){return e.getAndLoadData("/sysFileInfo/fileInfoListPage",t)}static findPage(t){return e.getAndLoadData("/sysFileInfo/fileInfoListPage",t)}static commonUpload(t,n){return n.append("secretFlag",t),e.post(i,n)}static delete(t){return e.post("/sysFileInfo/deleteReally",t)}static download(t){t.secretFlag==="Y"?window.location.href="".concat(o,"/sysFileInfo/privateDownload?fileId=").concat(t.fileId,"&token=").concat(t.token):window.location.href="".concat(o,"/sysFileInfo/publicDownload?fileId=").concat(t.fileId,"&token=").concat(t.token)}static detail(t){return e.getAndLoadData("/sysFileInfo/detail",t)}static getAntdVInfo(t){return e.getAndLoadData("/sysFileInfo/getAntdVInfo",t)}static getAntdVInfoBatch(t){return e.post("/sysFileInfo/getAntdVInfoBatch",t)}}export{i as F,a};
