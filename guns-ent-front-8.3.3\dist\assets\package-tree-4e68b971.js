import{_ as O,r as l,o as R,k as C,a as I,c as z,b as i,d as t,w as r,I as v,aR as b,t as A,aM as m,aS as S,f as F,h as q,M as G,E as H,m as K,l as J,B as Q,n as W,bg as X,a5 as Y,S as Z}from"./index-18a1ea24.js";/* empty css              */import{P as k,_ as ee}from"./package-add-edit-d6049334.js";/* empty css              */const ae={class:"box bgColor box-shadow"},te={class:"search",style:{display:"flex"}},se={class:"tree-content"},ne={class:"left-tree"},oe={class:"tree-edit"},le=["title"],ce={class:"edit-icon"},ie={__name:"package-tree",props:{},emits:["treeSelect","treeData","deletePackage"],setup(re,{expose:w,emit:N}){const g=N,u=l(""),n=l(!1),c=l([]),o=l([]),f=l([]),d=l(!1),h=l(null);R(()=>{p(!0)});const p=(s=!1)=>{n.value=!0,k.list({searchText:u.value}).then(e=>{c.value=e,s&&(e==null?void 0:e.length)>0&&(o.value=[e[0].packageId],g("treeSelect",e[0].packageId))}).finally(()=>n.value=!1)},P=(s,e)=>{g("treeSelect",e.node.packageId)},T=()=>{treeLoadKeys.value=[],u.value||p()},y=s=>{h.value=s,d.value=!0},B=s=>{G.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5417?",icon:t(H),maskClosable:!0,onOk:()=>{n.value=!0,k.delete({packageId:s.packageId}).then(e=>{var _;K.success(e.message),p(),((_=o.value)==null?void 0:_.length)>0&&o.value[0]==s.packageId&&(o.value=[],g("treeSelect",""))}).finally(()=>n.value=!1)}})},D=s=>{n.value=!0,k.refreshPackageTenantRoles({packageId:s.packageId}).then(e=>{K.success(e.message)}).finally(()=>{n.value=!1})};return w({currentSelectKeys:o}),(s,e)=>{const _=J,E=C("plus-outlined"),M=Q,$=C("redo-outlined"),U=W,L=X,V=Y,j=Z;return I(),z("div",ae,[i("div",te,[t(_,{value:u.value,"onUpdate:value":e[0]||(e[0]=a=>u.value=a),placeholder:"\u529F\u80FD\u5305\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22","allow-clear":"",onPressEnter:p,onChange:T},{prefix:r(()=>[t(v,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),t(M,{type:"primary",style:{"margin-left":"10px"},class:"border-radius",onClick:e[1]||(e[1]=a=>y())},{default:r(()=>[t(E)]),_:1})]),i("div",se,[t(j,{tip:"Loading...",spinning:n.value,delay:100},{default:r(()=>[b(i("div",ne,[t(L,{"show-icon":!0,selectedKeys:o.value,"onUpdate:selectedKeys":e[2]||(e[2]=a=>o.value=a),expandedKeys:f.value,"onUpdate:expandedKeys":e[3]||(e[3]=a=>f.value=a),onSelect:P,"tree-data":c.value,fieldNames:{children:"children",title:"packageName",key:"packageId",value:"packageId"}},{icon:r(a=>[t(v,{"icon-class":"icon-opt-shangchuandaoshujuku",color:"#43505e",fontSize:"24px"})]),title:r(a=>[i("span",oe,[i("span",{class:"edit-title",title:a.packageName},A(a.packageName),9,le),i("span",ce,[t(U,null,{default:r(()=>[t(v,{iconClass:"icon-opt-tianjia",color:"var(--primary-color)",onClick:m(x=>y(a),["stop"])},null,8,["onClick"]),t($,{title:"\u5237\u65B0",style:{color:"var(--primary-color)"},onClick:m(x=>D(a),["stop"])},null,8,["onClick"]),t(v,{iconClass:"icon-opt-shanchu",color:"red",onClick:m(x=>B(a),["stop"])},null,8,["onClick"])]),_:2},1024)])])]),_:1},8,["selectedKeys","expandedKeys","tree-data"])],512),[[S,c.value&&c.value.length>0]]),b(t(V,{class:"empty"},null,512),[[S,c.value&&c.value.length==0]])]),_:1},8,["spinning"])]),d.value?(I(),F(ee,{key:0,visible:d.value,"onUpdate:visible":e[4]||(e[4]=a=>d.value=a),data:h.value,onDone:p},null,8,["visible","data"])):q("",!0)])}}},ve=O(ie,[["__scopeId","data-v-a4718b64"]]);export{ve as default};
