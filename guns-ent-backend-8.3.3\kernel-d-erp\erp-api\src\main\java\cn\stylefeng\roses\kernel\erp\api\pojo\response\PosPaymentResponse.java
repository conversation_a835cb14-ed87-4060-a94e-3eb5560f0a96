package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * POS支付响应参数
 *
 * <AUTHOR>
 * @since 2025/08/01 17:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PosPaymentResponse extends BaseResponse {

    /**
     * 支付ID
     */
    @ChineseDescription("支付ID")
    private Long paymentId;

    /**
     * 订单ID
     */
    @ChineseDescription("订单ID")
    private Long orderId;

    /**
     * 支付单号
     */
    @ChineseDescription("支付单号")
    private String paymentNo;

    /**
     * 支付方式
     */
    @ChineseDescription("支付方式")
    private String paymentMethod;

    /**
     * 支付方式名称
     */
    @ChineseDescription("支付方式名称")
    private String paymentMethodName;

    /**
     * 支付金额
     */
    @ChineseDescription("支付金额")
    private BigDecimal paymentAmount;

    /**
     * 实收金额
     */
    @ChineseDescription("实收金额")
    private BigDecimal receivedAmount;

    /**
     * 找零金额
     */
    @ChineseDescription("找零金额")
    private BigDecimal changeAmount;

    /**
     * 支付状态
     */
    @ChineseDescription("支付状态")
    private String paymentStatus;

    /**
     * 支付状态名称
     */
    @ChineseDescription("支付状态名称")
    private String paymentStatusName;

    /**
     * 第三方交易号
     */
    @ChineseDescription("第三方交易号")
    private String transactionId;

    /**
     * 支付时间
     */
    @ChineseDescription("支付时间")
    private LocalDateTime paymentTime;

    /**
     * 会员ID
     */
    @ChineseDescription("会员ID")
    private Long memberId;

    /**
     * 会员姓名
     */
    @ChineseDescription("会员姓名")
    private String memberName;

    /**
     * 银行卡号（脱敏）
     */
    @ChineseDescription("银行卡号")
    private String cardNo;

    /**
     * 失败原因
     */
    @ChineseDescription("失败原因")
    private String failureReason;


}