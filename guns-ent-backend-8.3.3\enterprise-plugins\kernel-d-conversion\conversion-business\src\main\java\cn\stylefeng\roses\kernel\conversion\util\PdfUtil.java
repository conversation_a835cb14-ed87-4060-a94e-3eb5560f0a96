package cn.stylefeng.roses.kernel.conversion.util;

import cn.hutool.core.io.resource.ResourceUtil;
import com.aspose.pdf.*;
import com.aspose.pdf.text.FontTypes;

import java.io.*;

/**
 * pdf相关工具类
 *
 * <AUTHOR>
 * @date 2021/11/1 10:55
 */
public class PdfUtil {

    public static Font trueTypeFont = null;

    static {
        trueTypeFont = FontRepository.openFont(ResourceUtil.getStream("fonts/msyh.ttf"), FontTypes.TTF);
    }

    /**
     * pdf文件添加图片水印
     *
     * @param originFileStream    原始pdf文件流
     * @param watermarkFileStream 图片水印文件流
     * @param outputStream        输出的文件流
     * <AUTHOR>
     * @date 2021/11/1 10:56
     */
    public static void addPicWatermark(InputStream originFileStream, InputStream watermarkFileStream, OutputStream outputStream) {

        // Load PDF document
        Document pdf = new Document(originFileStream);

        // Load watermark image
        ImageStamp imageStamp = new ImageStamp(watermarkFileStream);
        imageStamp.setBackground(false);
        imageStamp.setOpacity(0.4);

        // Iterate through each page to add the watermark
        for (Page page : pdf.getPages()) {
            imageStamp.setWidth(page.getPageInfo().getWidth() / 2);
            imageStamp.setHeight(page.getPageInfo().getHeight() / 3);
            imageStamp.setXIndent(page.getPageInfo().getWidth() / 4);
            imageStamp.setYIndent(page.getPageInfo().getHeight() / 4);

            page.addStamp(imageStamp);
        }

        // Save document
        pdf.save(outputStream);
    }

    /**
     * pdf文件添加文字水印
     *
     * @param originPdfFileStream 被添加水印的pdf流
     * @param watermark           水印文字
     * @param outputPdfStream     生成pdf文件的路径
     * <AUTHOR>
     * @date 2021/11/1 15:27
     */
    public static void addTextWatermark(InputStream originPdfFileStream, String watermark, OutputStream outputPdfStream) {

        // Load PDF document
        Document pdf = new Document(originPdfFileStream);

        // Create text stamp
        TextStamp textStamp = new TextStamp(watermark);
        textStamp.setBackground(false);
        textStamp.setOpacity(0.3);
        textStamp.setRotateAngle(-45);
        textStamp.setHorizontalAlignment(HorizontalAlignment.Center);
        textStamp.setVerticalAlignment(VerticalAlignment.Center);
        textStamp.getTextState().setFont(trueTypeFont);
        textStamp.getTextState().setFontSize(20);
        textStamp.getTextState().setForegroundColor(com.aspose.pdf.Color.getGreen());

        // Iterate through each page to add the watermark
        for (Page page : pdf.getPages()) {
            page.addStamp(textStamp);
        }

        // Save document
        pdf.save(outputPdfStream);
    }

    public static void main(String[] args) throws FileNotFoundException {
        PdfUtil.addPicWatermark(new FileInputStream("C:\\Users\\<USER>\\Pictures\\1.pdf"),
                new FileInputStream("C:\\Users\\<USER>\\Pictures\\guns-logo.26018ec1.png"),
                new FileOutputStream("C:\\Users\\<USER>\\Pictures\\shuiyin.pdf"));

        PdfUtil.addTextWatermark(new FileInputStream("C:\\Users\\<USER>\\Pictures\\1.pdf"),
                "fengshuonan",
                new FileOutputStream("C:\\Users\\<USER>\\Pictures\\wenzishuiyin.pdf"));
    }

}
