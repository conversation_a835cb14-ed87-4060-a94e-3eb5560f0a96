System.register(["./index-legacy-ee1db0c7.js","./org-form-legacy-6e9e4f75.js","./OrgApi-legacy-c15eac58.js","./index-legacy-dba03026.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./index-legacy-94a6fc23.js"],(function(e,a){"use strict";var l,t,n,r,s,i,u,d,o,c,g;return{setters:[e=>{l=e.r,t=e.o,n=e.cb,r=e.a,s=e.f,i=e.w,u=e.d,d=e.m,o=e.M},e=>{c=e.default},e=>{g=e.O},null,null,null,null,null,null,null,null],execute:function(){e("default",{__name:"org-add-edit",props:{visible:Boolean,data:Object,parentId:String,parentName:String,levelNumberList:Array},emits:["update:visible","done"],setup(e,{emit:a}){const v=e,m=a,y=l(!1),f=l(!1),p=l({orgParentId:v.parentId,parentOrgName:v.parentName,orgType:1,statusFlag:1}),b=l(null);t((async()=>{v.data?(f.value=!0,j()):(p.value.orgSort=await n("SYSTEM_HR_ORGANIZATION"),f.value=!1)}));const j=()=>{g.detail({orgId:v.data.orgId}).then((e=>{p.value=Object.assign({},e)}))},x=e=>{m("update:visible",e)},N=async()=>{b.value.$refs.formRef.validate().then((async e=>{if(e){p.value.orgParentId||(p.value.orgParentId="-1",p.value.parentOrgName="根节点"),y.value=!0;let e=null;e=f.value?g.edit(p.value):g.add(p.value),e.then((async e=>{y.value=!1,d.success(e.message),x(!1),m("done")})).catch((()=>{y.value=!1}))}}))};return(a,l)=>{const t=o;return r(),s(t,{width:800,maskClosable:!1,visible:v.visible,"confirm-loading":y.value,forceRender:!0,title:f.value?"编辑机构":"新建机构","body-style":{paddingBottom:"8px"},"onUpdate:visible":x,onOk:N,class:"common-modal",onClose:l[1]||(l[1]=e=>x(!1))},{default:i((()=>[u(c,{form:p.value,"onUpdate:form":l[0]||(l[0]=e=>p.value=e),ref_key:"orgFormRef",ref:b,isUpdate:f.value,levelNumberList:e.levelNumberList},null,8,["form","isUpdate","levelNumberList"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
