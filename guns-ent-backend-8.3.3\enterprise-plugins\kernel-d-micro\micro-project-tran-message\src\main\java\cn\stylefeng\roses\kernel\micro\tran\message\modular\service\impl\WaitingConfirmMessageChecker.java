package cn.stylefeng.roses.kernel.micro.tran.message.modular.service.impl;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.micro.api.TranMessageServiceApi;
import cn.stylefeng.roses.kernel.micro.api.example.order.enums.OrderStatusEnum;
import cn.stylefeng.roses.kernel.micro.api.example.order.model.GoodsOrder;
import cn.stylefeng.roses.kernel.micro.api.pojo.TranMessage;
import cn.stylefeng.roses.kernel.micro.api.pojo.request.TranMessageRequest;
import cn.stylefeng.roses.kernel.micro.tran.message.modular.consumer.GoodsOrderConsumer;
import cn.stylefeng.roses.kernel.micro.tran.message.modular.service.AbstractMessageChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * 处理状态为“待确认”但已超时的消息
 *
 * <AUTHOR>
 * @date 2018-05-08 23:07
 */
@Service
@Slf4j
public class WaitingConfirmMessageChecker extends AbstractMessageChecker {

    @Resource
    private TranMessageServiceApi messageServiceApi;

    @Resource
    private GoodsOrderConsumer goodsOrderConsumer;

    @Override
    protected void processMessage(Map<String, TranMessage> messages) {
        for (Map.Entry<String, TranMessage> entry : messages.entrySet()) {
            TranMessage message = entry.getValue();
            try {

                Long orderId = message.getBizUniqueId();

                if (orderId == null) {

                    //如果订单失败，则删掉没用的消息
                    messageServiceApi.deleteMessageByMessageId(message.getMessageId());
                } else {
                    GoodsOrder order = goodsOrderConsumer.findOrderById(orderId);

                    //如果订单成功，则确认消息并发送
                    if (order != null && OrderStatusEnum.SUCCESS.getStatus().equals(order.getStatus())) {
                        messageServiceApi.confirmAndSendMessage(message.getMessageId());
                    } else {

                        //如果订单失败，则删掉没用的消息
                        messageServiceApi.deleteMessageByMessageId(message.getMessageId());
                    }
                }

            } catch (Exception e) {
                log.error("处理待确认消息异常！messageId=" + message.getMessageId(), e);
            }
        }
    }

    @Override
    protected PageResult<TranMessage> getPageResult(TranMessageRequest tranMessageRequest) {
        return messageServiceApi.listPageWaitConfirmTimeOutMessages(tranMessageRequest);
    }

}
