<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-conversion</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>conversion-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- aspose文件操作的功能 -->
        <dependency>
            <groupId>my.aspose</groupId>
            <artifactId>aspose-pdf</artifactId>
            <version>${aspose.version}</version>
        </dependency>
        <dependency>
            <groupId>my.aspose</groupId>
            <artifactId>aspose-cells</artifactId>
            <version>${aspose.version}</version>
        </dependency>
        <dependency>
            <groupId>my.aspose</groupId>
            <artifactId>aspose-slides</artifactId>
            <version>${aspose.version}</version>
        </dependency>
        <dependency>
            <groupId>my.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>${aspose.version}</version>
        </dependency>

        <!-- conversion-api -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>conversion-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 文件相关的api -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>file-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
