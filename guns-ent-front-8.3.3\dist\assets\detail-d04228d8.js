import{_ as L}from"./index-02bf6f00.js";import{_ as C,r as l,o as k,a as p,c as m,b as t,d as s,w as a,g as B,h as E,B as N,n as S,I as V,l as z}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */const F={class:"guns-layout"},M={class:"guns-layout-content"},R={class:"guns-layout"},U={class:"guns-layout-content-application"},H={class:"content-mian"},P={class:"content-mian-header"},D={class:"header-content"},j={class:"header-content-left"},q={class:"header-content-right"},A={class:"content-mian-body"},G={class:"table-content"},J=["innerHTML"],K={__name:"detail",props:{businessLogId:{type:String}},emits:["updateType"],setup(h,{emit:v}){const g=v,f=h,i=l(null),b=l([{title:"\u5E8F\u53F7",isShow:!0,width:50},{title:"\u65E5\u5FD7\u8BB0\u5F55\u5185\u5BB9",isShow:!0,dataIndex:"logContent"}]),o=l({businessLogId:"",searchText:""}),d=()=>{i.value.reload()},y=n=>{let e="";return n&&(e=n.replace(new RegExp("\n","g"),"<br>")),e},_=()=>{g("updateType",{type:"list",businessLogId:null})};return k(()=>{o.value.businessLogId=f.businessLogId,o.value.searchText="",d(),document.addEventListener("keydown",function(n){if(n.key==="Backspace"){const e=document.activeElement;e&&e.tagName==="INPUT"||_()}})}),(n,e)=>{const u=N,r=S,I=V,x=z,T=L;return p(),m("div",F,[t("div",M,[t("div",R,[t("div",U,[t("div",H,[t("div",P,[t("div",D,[t("div",j,[s(r,{size:16},{default:a(()=>[s(u,{onClick:_,class:"border-radius"},{default:a(()=>e[1]||(e[1]=[B("\u8FD4\u56DE")])),_:1,__:[1]})]),_:1})]),t("div",q,[s(r,{size:16})])])]),t("div",A,[t("div",G,[s(T,{columns:b.value,where:o.value,rowSelection:!1,pageSize:100,isInit:!1,ref_key:"tableRef",ref:i,url:"/sysLogBusinessContent/page",showTableTool:"",showToolTotal:!1},{toolLeft:a(()=>[s(x,{value:o.value.searchText,"onUpdate:value":e[0]||(e[0]=c=>o.value.searchText=c),placeholder:"\u65E5\u5FD7\u5185\u5BB9 (\u56DE\u8F66\u641C\u7D22)",onPressEnter:d,class:"search-input",bordered:!1},{prefix:a(()=>[s(I,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:a(({column:c,record:w})=>[c.dataIndex=="logContent"?(p(),m("div",{key:0,innerHTML:y(w.logContent)},null,8,J)):E("",!0)]),_:1},8,["columns","where"])])])])])])])])}}},Z=C(K,[["__scopeId","data-v-d3925816"]]);export{Z as default};
