<template>
  <div class="inventory-alert-record-container">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        class="search-form"
      >
        <a-form-item label="商品名称" name="productName">
          <a-input
            v-model:value="searchForm.productName"
            placeholder="请输入商品名称"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="预警类型" name="alertType">
          <a-select
            v-model:value="searchForm.alertType"
            placeholder="请选择预警类型"
            allow-clear
            style="width: 150px"
          >
            <a-select-option value="LOW_STOCK">库存不足</a-select-option>
            <a-select-option value="ZERO_STOCK">零库存</a-select-option>
            <a-select-option value="OVERSTOCK">库存积压</a-select-option>
            <a-select-option value="EXPIRY">临期预警</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="预警级别" name="alertLevel">
          <a-select
            v-model:value="searchForm.alertLevel"
            placeholder="请选择预警级别"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="CRITICAL">紧急</a-select-option>
            <a-select-option value="WARNING">警告</a-select-option>
            <a-select-option value="INFO">提醒</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="处理状态" name="handleStatus">
          <a-select
            v-model:value="searchForm.handleStatus"
            placeholder="请选择处理状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="PENDING">待处理</a-select-option>
            <a-select-option value="RESOLVED">已解决</a-select-option>
            <a-select-option value="IGNORED">已忽略</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="预警时间" name="alertTimeRange">
          <a-range-picker
            v-model:value="searchForm.alertTimeRange"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作区域 -->
    <a-card :bordered="false" class="action-card">
      <a-space>
        <a-button
          type="primary"
          :disabled="!hasSelected"
          @click="handleBatchProcess"
        >
          <template #icon>
            <CheckOutlined />
          </template>
          批量处理
        </a-button>
        <a-button @click="handleExport">
          <template #icon>
            <DownloadOutlined />
          </template>
          导出
        </a-button>
        <a-button @click="handleRefresh">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </a-space>
    </a-card>

    <!-- 数据表格 -->
    <a-card :bordered="false" class="table-card">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1200 }"
        @change="handleTableChange"
      >
        <template #alertType="{ record }">
          <a-tag :color="getAlertTypeColor(record.alertType)">
            {{ getAlertTypeText(record.alertType) }}
          </a-tag>
        </template>

        <template #alertLevel="{ record }">
          <a-tag :color="getAlertLevelColor(record.alertLevel)">
            {{ getAlertLevelText(record.alertLevel) }}
          </a-tag>
        </template>

        <template #currentStock="{ record }">
          <span :style="{ color: record.currentStock <= record.thresholdValue ? '#f5222d' : '#52c41a' }">
            {{ record.currentStock }}
          </span>
        </template>

        <template #handleStatus="{ record }">
          <a-tag :color="getHandleStatusColor(record.handleStatus)">
            {{ getHandleStatusText(record.handleStatus) }}
          </a-tag>
        </template>

        <template #alertTime="{ record }">
          {{ dayjs(record.alertTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>

        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-button
              v-if="record.handleStatus === 'PENDING'"
              type="link"
              size="small"
              @click="handleProcess(record)"
            >
              处理
            </a-button>
            <a-popconfirm
              title="确定要删除这条预警记录吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 批量处理弹窗 -->
    <a-modal
      v-model:visible="batchProcessVisible"
      title="批量处理预警记录"
      @ok="handleBatchProcessConfirm"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="处理操作">
          <a-radio-group v-model:value="batchProcessType">
            <a-radio value="RESOLVED">标记为已解决</a-radio>
            <a-radio value="IGNORED">标记为已忽略</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="处理备注">
          <a-textarea
            v-model:value="batchProcessRemark"
            placeholder="请输入处理备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <AlertRecordDetail
      v-model:visible="detailVisible"
      :record-data="currentRecord"
    />

    <!-- 处理弹窗 -->
    <AlertRecordProcess
      v-model:visible="processVisible"
      :record-data="currentRecord"
      @success="handleProcessSuccess"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  CheckOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { InventoryAlertRecordApi } from '../api/InventoryAlertRecordApi';
import AlertRecordDetail from '../components/AlertRecordDetail.vue';
import AlertRecordProcess from '../components/AlertRecordProcess.vue';

export default {
  name: 'InventoryAlertRecordIndex',
  components: {
    SearchOutlined,
    ReloadOutlined,
    CheckOutlined,
    DownloadOutlined,
    AlertRecordDetail,
    AlertRecordProcess
  },
  setup() {
    const searchFormRef = ref();
    const loading = ref(false);
    const dataSource = ref([]);
    const selectedRowKeys = ref([]);
    const detailVisible = ref(false);
    const processVisible = ref(false);
    const batchProcessVisible = ref(false);
    const currentRecord = ref({});
    const batchProcessType = ref('RESOLVED');
    const batchProcessRemark = ref('');

    const searchForm = reactive({
      productName: '',
      alertType: undefined,
      alertLevel: undefined,
      handleStatus: undefined,
      alertTimeRange: []
    });

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    });

    const columns = [
      {
        title: '商品名称',
        dataIndex: 'productName',
        key: 'productName',
        width: 150
      },
      {
        title: '预警类型',
        dataIndex: 'alertType',
        key: 'alertType',
        width: 100,
        slots: { customRender: 'alertType' }
      },
      {
        title: '预警级别',
        dataIndex: 'alertLevel',
        key: 'alertLevel',
        width: 100,
        slots: { customRender: 'alertLevel' }
      },
      {
        title: '当前库存',
        dataIndex: 'currentStock',
        key: 'currentStock',
        width: 100,
        slots: { customRender: 'currentStock' }
      },
      {
        title: '阈值',
        dataIndex: 'thresholdValue',
        key: 'thresholdValue',
        width: 80
      },
      {
        title: '预警消息',
        dataIndex: 'alertMessage',
        key: 'alertMessage',
        width: 200,
        ellipsis: true
      },
      {
        title: '处理状态',
        dataIndex: 'handleStatus',
        key: 'handleStatus',
        width: 100,
        slots: { customRender: 'handleStatus' }
      },
      {
        title: '预警时间',
        dataIndex: 'alertTime',
        key: 'alertTime',
        width: 150,
        slots: { customRender: 'alertTime' }
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right',
        slots: { customRender: 'action' }
      }
    ];

    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys;
      },
      getCheckboxProps: (record) => ({
        disabled: record.handleStatus !== 'PENDING'
      })
    };

    const hasSelected = computed(() => selectedRowKeys.value.length > 0);

    // 获取数据
    const fetchData = async () => {
      try {
        loading.value = true;
        const params = {
          current: pagination.current,
          size: pagination.pageSize,
          ...searchForm
        };

        if (searchForm.alertTimeRange && searchForm.alertTimeRange.length === 2) {
          params.startTime = searchForm.alertTimeRange[0].format('YYYY-MM-DD');
          params.endTime = searchForm.alertTimeRange[1].format('YYYY-MM-DD');
        }

        const response = await InventoryAlertRecordApi.page(params);
        dataSource.value = response.data.records;
        pagination.total = response.data.total;
      } catch (error) {
        console.error('获取预警记录失败:', error);
        message.error('获取预警记录失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchData();
    };

    // 重置
    const handleReset = () => {
      searchFormRef.value.resetFields();
      pagination.current = 1;
      fetchData();
    };

    // 刷新
    const handleRefresh = () => {
      fetchData();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchData();
    };

    // 查看详情
    const handleView = (record) => {
      currentRecord.value = record;
      detailVisible.value = true;
    };

    // 处理记录
    const handleProcess = (record) => {
      currentRecord.value = record;
      processVisible.value = true;
    };

    // 处理成功回调
    const handleProcessSuccess = () => {
      processVisible.value = false;
      fetchData();
    };

    // 删除记录
    const handleDelete = async (record) => {
      try {
        await InventoryAlertRecordApi.delete({ id: record.id });
        message.success('删除成功');
        fetchData();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    };

    // 批量处理
    const handleBatchProcess = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要处理的记录');
        return;
      }
      batchProcessVisible.value = true;
    };

    // 确认批量处理
    const handleBatchProcessConfirm = async () => {
      try {
        await InventoryAlertRecordApi.batchHandle({
          idList: selectedRowKeys.value,
          handleType: batchProcessType.value,
          handleRemark: batchProcessRemark.value
        });
        message.success('批量处理成功');
        batchProcessVisible.value = false;
        selectedRowKeys.value = [];
        fetchData();
      } catch (error) {
        console.error('批量处理失败:', error);
        message.error('批量处理失败');
      }
    };

    // 导出
    const handleExport = () => {
      message.info('导出功能开发中...');
    };

    // 获取预警类型颜色
    const getAlertTypeColor = (type) => {
      const colors = {
        'LOW_STOCK': 'orange',
        'ZERO_STOCK': 'red',
        'OVERSTOCK': 'purple',
        'EXPIRY': 'volcano'
      };
      return colors[type] || 'default';
    };

    // 获取预警类型文本
    const getAlertTypeText = (type) => {
      const texts = {
        'LOW_STOCK': '库存不足',
        'ZERO_STOCK': '零库存',
        'OVERSTOCK': '库存积压',
        'EXPIRY': '临期预警'
      };
      return texts[type] || type;
    };

    // 获取预警级别颜色
    const getAlertLevelColor = (level) => {
      const colors = {
        'CRITICAL': 'red',
        'WARNING': 'orange',
        'INFO': 'blue'
      };
      return colors[level] || 'default';
    };

    // 获取预警级别文本
    const getAlertLevelText = (level) => {
      const texts = {
        'CRITICAL': '紧急',
        'WARNING': '警告',
        'INFO': '提醒'
      };
      return texts[level] || level;
    };

    // 获取处理状态颜色
    const getHandleStatusColor = (status) => {
      const colors = {
        'PENDING': 'orange',
        'RESOLVED': 'green',
        'IGNORED': 'gray'
      };
      return colors[status] || 'default';
    };

    // 获取处理状态文本
    const getHandleStatusText = (status) => {
      const texts = {
        'PENDING': '待处理',
        'RESOLVED': '已解决',
        'IGNORED': '已忽略'
      };
      return texts[status] || status;
    };

    onMounted(() => {
      fetchData();
    });

    return {
      searchFormRef,
      loading,
      dataSource,
      selectedRowKeys,
      detailVisible,
      processVisible,
      batchProcessVisible,
      currentRecord,
      batchProcessType,
      batchProcessRemark,
      searchForm,
      pagination,
      columns,
      rowSelection,
      hasSelected,
      dayjs,
      handleSearch,
      handleReset,
      handleRefresh,
      handleTableChange,
      handleView,
      handleProcess,
      handleProcessSuccess,
      handleDelete,
      handleBatchProcess,
      handleBatchProcessConfirm,
      handleExport,
      getAlertTypeColor,
      getAlertTypeText,
      getAlertLevelColor,
      getAlertLevelText,
      getHandleStatusColor,
      getHandleStatusText
    };
  }
};
</script>

<style scoped>
.inventory-alert-record-container {
  padding: 16px;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: -16px;
}
</style>
