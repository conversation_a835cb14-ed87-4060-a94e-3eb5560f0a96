import{_ as y,s as k,X as g,a as i,f as n,w as o,d as s,g as l,t as m,m as v,Y as p,U as O,Z as j,M as x}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{R as I}from"./regionApi-2c103d88.js";const L={name:"RegionDetailForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(_,{emit:e}){const f=k({}),t=r=>{e("update:visible",r),r||Object.keys(f).forEach(a=>{delete f[a]})},u=async r=>{try{const a=await I.detail({regionId:r.regionId});Object.assign(f,a)}catch(a){console.error("\u83B7\u53D6\u533A\u57DF\u8BE6\u60C5\u5931\u8D25:",a),v.error("\u83B7\u53D6\u8BE6\u60C5\u5931\u8D25")}};return g(()=>_.data,r=>{r&&Object.keys(r).length>0&&(r.regionId?u(r):Object.assign(f,r))},{immediate:!0}),g(()=>_.visible,r=>{r&&_.data&&_.data.regionId&&u(_.data)}),{form:f,updateVisible:t,detail:u}}};function N(_,e,f,t,u,r){const a=p,d=O,b=j,c=x;return i(),n(c,{title:"\u533A\u57DF\u8BE6\u60C5",width:800,visible:f.visible,footer:null,"onUpdate:visible":t.updateVisible},{default:o(()=>[s(b,{column:2,bordered:""},{default:o(()=>[s(a,{label:"\u533A\u57DF\u7F16\u7801"},{default:o(()=>[l(m(t.form.regionCode||"\u65E0"),1)]),_:1}),s(a,{label:"\u533A\u57DF\u540D\u79F0"},{default:o(()=>[l(m(t.form.regionName||"\u65E0"),1)]),_:1}),s(a,{label:"\u533A\u57DF\u5C42\u7EA7"},{default:o(()=>[t.form.regionLevel===1?(i(),n(d,{key:0,color:"red"},{default:o(()=>e[0]||(e[0]=[l("\u56FD\u5BB6")])),_:1,__:[0]})):t.form.regionLevel===2?(i(),n(d,{key:1,color:"orange"},{default:o(()=>e[1]||(e[1]=[l("\u7701")])),_:1,__:[1]})):t.form.regionLevel===3?(i(),n(d,{key:2,color:"yellow"},{default:o(()=>e[2]||(e[2]=[l("\u5E02")])),_:1,__:[2]})):t.form.regionLevel===4?(i(),n(d,{key:3,color:"green"},{default:o(()=>e[3]||(e[3]=[l("\u533A\u53BF")])),_:1,__:[3]})):t.form.regionLevel===5?(i(),n(d,{key:4,color:"blue"},{default:o(()=>e[4]||(e[4]=[l("\u5546\u5708")])),_:1,__:[4]})):(i(),n(d,{key:5,color:"default"},{default:o(()=>e[5]||(e[5]=[l("\u672A\u77E5")])),_:1,__:[5]}))]),_:1}),s(a,{label:"\u7236\u7EA7\u533A\u57DF"},{default:o(()=>[l(m(t.form.parentRegionName||"\u65E0"),1)]),_:1}),s(a,{label:"\u6392\u5E8F\u53F7"},{default:o(()=>[l(m(t.form.sortOrder||0),1)]),_:1}),s(a,{label:"\u72B6\u6001"},{default:o(()=>[t.form.status==="Y"?(i(),n(d,{key:0,color:"green"},{default:o(()=>e[6]||(e[6]=[l("\u542F\u7528")])),_:1,__:[6]})):t.form.status==="N"?(i(),n(d,{key:1,color:"red"},{default:o(()=>e[7]||(e[7]=[l("\u505C\u7528")])),_:1,__:[7]})):(i(),n(d,{key:2,color:"default"},{default:o(()=>[l(m(t.form.status||"\u672A\u77E5"),1)]),_:1}))]),_:1}),s(a,{label:"\u5907\u6CE8",span:2},{default:o(()=>[l(m(t.form.remark||"\u65E0"),1)]),_:1})]),_:1})]),_:1},8,["visible","onUpdate:visible"])}const w=y(L,[["render",N]]);export{w as default};
