package cn.stylefeng.roses.ent.mobile.manage.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.mobile.manage.enums.AddressBookTypeEnum;
import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.AddressBookItem;
import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.AddressBookUserDetail;
import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.OrgUserStatTotal;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.sys.api.pojo.org.CompanyDeptDTO;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.position.entity.HrPosition;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 通讯录工厂
 *
 * <AUTHOR>
 * @since 2024/3/21 23:20
 */
public class AddressBookFactory {

    /**
     * 创建通讯录列表，组装机构和人员的信息
     *
     * <AUTHOR>
     * @since 2024/3/21 23:21
     */
    public static List<AddressBookItem> createAddressBookItemList(List<HrOrganization> orgList, List<OrgUserStatTotal> orgUserStatTotals, List<SysUserOrg> sysUserOrgList, List<SysUser> sysUserList,
                                                                  List<HrPosition> hrPositionList) {

        List<AddressBookItem> addressBookItemList = new ArrayList<>();

        // 1. 先添加组织信息
        if (ObjectUtil.isNotEmpty(orgList)) {
            for (HrOrganization hrOrganization : orgList) {
                AddressBookItem addressBookItem = createOrgItem(orgUserStatTotals, hrOrganization);
                addressBookItemList.add(addressBookItem);
            }
        }

        // 2. 获取人员和职位的映射关系
        HashMap<Long, Long> userPositionMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(sysUserOrgList)) {
            for (SysUserOrg sysUserOrg : sysUserOrgList) {
                userPositionMap.put(sysUserOrg.getUserId(), sysUserOrg.getPositionId());
            }
        }

        // 2. 再添加人员信息
        if (ObjectUtil.isNotEmpty(sysUserList)) {
            for (SysUser sysUser : sysUserList) {
                AddressBookItem addressBookItem = new AddressBookItem();
                addressBookItem.setItemType(AddressBookTypeEnum.USER.getCode());
                addressBookItem.setUserId(sysUser.getUserId());
                addressBookItem.setRealName(sysUser.getRealName());
                addressBookItem.setAdminFlag(YesOrNotEnum.Y.getCode().equals(sysUser.getSuperAdminFlag()));
                addressBookItem.setUserStatus(sysUser.getStatusFlag());
                addressBookItem.setAvatar(sysUser.getAvatar());

                // 设置人员的职位名称
                for (HrPosition hrPosition : hrPositionList) {
                    if (hrPosition.getPositionId().equals(userPositionMap.get(sysUser.getUserId()))) {
                        addressBookItem.setPositionName(hrPosition.getPositionName());
                    }
                }

                addressBookItemList.add(addressBookItem);
            }
        }

        return addressBookItemList;
    }

    /**
     * 创建用户详情信息
     *
     * <AUTHOR>
     * @since 2024/3/24 22:31
     */
    public static AddressBookUserDetail createUserDetail(SysUser sysUser, CompanyDeptDTO companyDeptInfo, String positionName) {
        AddressBookUserDetail addressBookUserDetail = new AddressBookUserDetail();
        if (sysUser != null) {
            addressBookUserDetail.setAvatar(sysUser.getAvatar());
            addressBookUserDetail.setUserId(sysUser.getUserId());
            addressBookUserDetail.setRealName(sysUser.getRealName());
            addressBookUserDetail.setSex(sysUser.getSex());
            addressBookUserDetail.setEmail(sysUser.getEmail());
            addressBookUserDetail.setPhone(sysUser.getPhone());
            addressBookUserDetail.setEmployeeNumber(sysUser.getEmployeeNumber());
        }
        if (companyDeptInfo != null) {
            addressBookUserDetail.setCompanyName(companyDeptInfo.getCompanyName());
            addressBookUserDetail.setDeptName(companyDeptInfo.getDeptName());
        }
        addressBookUserDetail.setPositionName(positionName);
        return addressBookUserDetail;
    }

    /**
     * 创建组织机构的元素信息和人员统计信息
     *
     * <AUTHOR>
     * @since 2024-03-29 13:05
     */
    private static AddressBookItem createOrgItem(List<OrgUserStatTotal> orgUserStatTotals, HrOrganization hrOrganization) {
        AddressBookItem addressBookItem = new AddressBookItem();
        addressBookItem.setItemType(hrOrganization.getOrgType());
        addressBookItem.setOrgId(hrOrganization.getOrgId());
        addressBookItem.setOrgName(hrOrganization.getOrgName());

        // 获取组织机构下人的数量
        int userCount = 0;
        for (OrgUserStatTotal orgUserStat : orgUserStatTotals) {
            if (orgUserStat.getOrgId().equals(hrOrganization.getOrgId()) || orgUserStat.getOrgPids().contains("[" + hrOrganization.getOrgId() + "]")) {
                userCount++;
            }
        }
        addressBookItem.setOrgUserCount(userCount);

        return addressBookItem;
    }

}
