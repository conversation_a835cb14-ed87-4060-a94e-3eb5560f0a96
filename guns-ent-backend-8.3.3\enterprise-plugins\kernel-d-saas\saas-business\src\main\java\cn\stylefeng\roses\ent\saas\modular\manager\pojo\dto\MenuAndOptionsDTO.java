package cn.stylefeng.roses.ent.saas.modular.manager.pojo.dto;

import cn.stylefeng.roses.kernel.sys.modular.role.entity.SysRoleLimit;
import cn.stylefeng.roses.kernel.sys.modular.role.entity.SysRoleMenu;
import cn.stylefeng.roses.kernel.sys.modular.role.entity.SysRoleMenuOptions;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用在封装菜单和菜单功能的dto
 *
 * <AUTHOR>
 * @since 2023/9/7 14:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MenuAndOptionsDTO {

    /**
     * 角色菜单的权限绑定集合
     */
    private List<SysRoleMenu> sysRoleMenuList;

    /**
     * 角色菜单功能的绑定集合
     */
    private List<SysRoleMenuOptions> sysRoleMenuOptionsList;

    /**
     * 角色可分配权限的限制集合
     */
    private List<SysRoleLimit> roleLimitList;

}
