import{r as i,o as b,bX as f,a as _,f as y,w as C,d as h,m as k,M as x}from"./index-18a1ea24.js";import U from"./config-form-da9929a4.js";const R={__name:"config-add-edit",props:{visible:Boolean,data:Object,groupCode:String},emits:["update:visible","done"],setup(m,{emit:v}){const t=m,r=v,l=i(!1),s=i(!1),o=i({sysFlag:"Y",groupCode:t.groupCode}),u=i(null);b(()=>{t.data?(s.value=!0,p()):s.value=!1});const p=()=>{f.detail({configId:t.data.configId}).then(a=>{o.value=Object.assign({},a)})},n=a=>{r("update:visible",a)},g=async()=>{u.value.$refs.formRef.validate().then(async a=>{if(a){l.value=!0;let e=null;s.value?e=f.edit(o.value):e=f.add(o.value),e.then(async d=>{l.value=!1,k.success(d.message),n(!1),r("done")}).catch(()=>{l.value=!1})}})};return(a,e)=>{const d=x;return _(),y(d,{width:700,maskClosable:!1,visible:t.visible,"confirm-loading":l.value,forceRender:!0,title:s.value?"\u7F16\u8F91\u914D\u7F6E":"\u65B0\u5EFA\u914D\u7F6E","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":n,onOk:g,onClose:e[1]||(e[1]=c=>n(!1))},{default:C(()=>[h(U,{form:o.value,"onUpdate:form":e[0]||(e[0]=c=>o.value=c),ref_key:"configFormRef",ref:u,isUpdate:s.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}};export{R as default};
