<!--
  购物车汇总组件
  
  显示购物车总计信息，包含优惠券应用和结账功能
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cart-summary">
    <div class="summary-content">
      <!-- 左侧区域 - 会员信息 -->
      <div class="member-section">
        <!-- 会员选择/显示 -->
        <div class="member-info">
          <div v-if="!member" class="member-select">
            <a-button type="link" size="small" @click="handleSelectMember" class="select-member-btn">
              <template #icon>
                <user-add-outlined />
              </template>
              选择会员
            </a-button>
          </div>
          <div v-else class="member-display">
            <div class="member-name">
              <user-outlined />
              {{ member.memberName || member.name }}
            </div>
            <a-button type="link" size="small" @click="handleClearMember" class="clear-member-btn">
              <close-outlined />
            </a-button>
          </div>
        </div>
        
        <!-- 会员详细信息 -->
        <div v-if="member" class="member-details">
          <div class="member-detail-item">
            <span class="detail-label">余额:</span>
            <span class="detail-value">{{ formatPrice(member.balance || 0) }}</span>
          </div>
          <div class="member-detail-item">
            <span class="detail-label">积分:</span>
            <span class="detail-value">{{ member.points || 0 }}</span>
          </div>
          <div class="member-detail-item">
            <span class="detail-label">等级:</span>
            <span class="detail-value">{{ member.level || '普通会员' }}</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧区域 - 价格信息和操作 -->
      <div class="price-section">
        <!-- 金额明细 -->
        <div class="amount-details">
          <div class="amount-row">
            <span class="amount-label">小计({{ summary.itemCount }}件):</span>
            <span class="amount-value">{{ formatPrice(total.totalAmount) }}</span>
          </div>
          
          <div v-if="total.discountAmount > 0" class="amount-row discount-row">
            <span class="amount-label">优惠:</span>
            <span class="amount-value discount-value">-{{ formatPrice(total.discountAmount) }}</span>
          </div>
          
          <div class="amount-row total-row">
            <span class="amount-label">实付:</span>
            <span class="amount-value total-value">{{ formatPrice(total.finalAmount) }}</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button 
            size="large"
            :loading="loading"
            :disabled="!canCheckout"
            @click="handleSuspendOrder"
            class="suspend-btn"
          >
            挂单
          </a-button>
          
          <a-button 
            size="large"
            type="primary"
            :loading="loading"
            :disabled="!canCheckout"
            @click="handleCheckout"
            class="checkout-btn"
          >
            结算
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { UserOutlined, UserAddOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { AmountFormatter } from '../../utils/formatter'

// 定义组件名称
defineOptions({
  name: 'CartSummary'
})

// 定义Props
const props = defineProps({
  // 购物车汇总信息
  summary: {
    type: Object,
    required: true
  },
  // 总计信息
  total: {
    type: Object,
    required: true
  },
  // 当前会员信息
  member: {
    type: Object,
    default: null
  },
  // 是否可以结账
  canCheckout: {
    type: Boolean,
    default: false
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'checkout',
  'suspend-order',
  'select-member',
  'clear-member'
])

// ==================== 方法 ====================

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @returns {string} 格式化后的价格
 */
const formatPrice = (price) => {
  return AmountFormatter.formatCurrency(price || 0)
}

/**
 * 处理结账
 */
const handleCheckout = () => {
  if (!props.canCheckout) {
    message.warning('购物车为空或商品信息有误，无法结账')
    return
  }
  
  emit('checkout')
}

/**
 * 处理挂单
 */
const handleSuspendOrder = () => {
  if (!props.canCheckout) {
    message.warning('购物车为空，无法挂单')
    return
  }
  
  emit('suspend-order')
}

/**
 * 处理选择会员
 */
const handleSelectMember = () => {
  emit('select-member')
}

/**
 * 处理清除会员
 */
const handleClearMember = () => {
  emit('clear-member')
}
</script>

<style scoped>
.cart-summary {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.summary-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

/* 左侧会员区域 */
.member-section {
  flex: 1;
  min-width: 0;
}

.member-info {
  margin-bottom: 8px;
}

.member-select {
  display: flex;
  align-items: center;
}

.select-member-btn {
  padding: 0;
  height: auto;
  color: #1890ff;
  font-size: 13px;
}

.member-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.member-name {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #52c41a;
  font-weight: 500;
}

.clear-member-btn {
  padding: 0;
  height: auto;
  color: #8c8c8c;
  font-size: 12px;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.detail-label {
  color: #8c8c8c;
}

.detail-value {
  color: #262626;
  font-weight: 500;
}

/* 右侧价格区域 */
.price-section {
  flex: 1;
  min-width: 0;
}

.amount-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.amount-label {
  color: #595959;
}

.amount-value {
  color: #262626;
  font-weight: 500;
}

.discount-row .amount-label {
  color: #52c41a;
}

.discount-value {
  color: #52c41a !important;
}

.total-row {
  font-size: 14px;
  font-weight: 600;
  padding-top: 4px;
  border-top: 1px solid #f0f0f0;
  margin-top: 4px;
}

.total-value {
  color: #ff4d4f;
  font-size: 16px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
}

.suspend-btn,
.checkout-btn {
  flex: 1;
  height: 44px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.suspend-btn {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 1px solid #d6e4ff;
  color: #1890ff;
}

.suspend-btn:hover {
  background: linear-gradient(135deg, #e6f3ff 0%, #d6e4ff 100%);
  border-color: #adc6ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.checkout-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: 1px solid #1890ff;
  color: #fff;
}

.checkout-btn:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .member-section,
  .price-section {
    flex: none;
    width: 100%;
  }
}
</style>