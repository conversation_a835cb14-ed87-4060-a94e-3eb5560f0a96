/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.oauth2.modular.factory;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.auth.api.password.PasswordStoredEncryptApi;
import cn.stylefeng.roses.kernel.rule.enums.SexEnum;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.model.AuthUser;

/**
 * 用户信息填充，用于创建和修改用户时，添加一些基础信息
 *
 * <AUTHOR>
 * @since 2020/11/21 12:55
 */
public class OauthSysUserCreateFactory {

    /**
     * 创建第三方应用在本应用的用户
     *
     * <AUTHOR>
     * @since 2019/6/9 19:11
     */
    public static SysUser createOAuth2User(AuthUser oauthUser) {

        SysUser systemUser = new SysUser();

        // 设置名字
        systemUser.setRealName(oauthUser.getNickname());
        systemUser.setNickName(oauthUser.getNickname());

        // 设置账号
        systemUser.setAccount("OAUTH2_" + oauthUser.getSource() + "_" + oauthUser.getUsername());

        // 设置密码
        PasswordStoredEncryptApi passwordStoredEncryptApi = SpringUtil.getBean(PasswordStoredEncryptApi.class);
        systemUser.setPassword(passwordStoredEncryptApi.encrypt(RandomUtil.randomString(20)));

        // 设置性别
        systemUser.setSex(
                oauthUser.getGender().equals(AuthUserGender.FEMALE)
                        ? SexEnum.F.getCode() : SexEnum.M.getCode());

        // 设置邮箱电话
        systemUser.setEmail(oauthUser.getEmail());
        systemUser.setPhone("未设置");

        return systemUser;
    }

}
