package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品采购统计响应类
 *
 * <AUTHOR>
 * @since 2025/07/28 10:00
 */
@Data
public class ProductPurchaseStatsResponse {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 计价类型
     */
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 总数量
     */
    @ChineseDescription("总数量")
    private BigDecimal totalQuantity;

    /**
     * 总金额
     */
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

    /**
     * 供应商数量
     */
    @ChineseDescription("供应商数量")
    private Long supplierCount;

    /**
     * 采购次数
     */
    @ChineseDescription("采购次数")
    private Long purchaseCount;

    /**
     * 平均价格
     */
    @ChineseDescription("平均价格")
    private BigDecimal avgPrice;

}