package cn.stylefeng.roses.kernel.micro.feign.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * feign示例服务提供者
 *
 * <AUTHOR>
 * @date 2021/5/12 19:42
 */
@SpringBootApplication(scanBasePackages = "cn.stylefeng", exclude = DataSourceAutoConfiguration.class)
@EnableFeignClients(
        basePackages = {"cn.stylefeng.roses.kernel.micro.core.auth.consumer"})
public class GunsFeignProviderApplication {

    public static void main(String[] args) {
        SpringApplication.run(GunsFeignProviderApplication.class, args);
    }

}

