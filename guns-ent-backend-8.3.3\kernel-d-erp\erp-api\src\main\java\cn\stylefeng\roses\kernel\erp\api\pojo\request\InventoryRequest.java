package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 库存请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryRequest extends BaseRequest {

    /**
     * 库存ID
     */
    @ChineseDescription("库存ID")
    private Long id;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 当前库存
     */
    @DecimalMin(value = "0", message = "当前库存必须大于等于0")
    @ChineseDescription("当前库存")
    private BigDecimal currentStock;

    /**
     * 最小库存（预警值）
     */
    @DecimalMin(value = "0", message = "最小库存必须大于等于0")
    @ChineseDescription("最小库存")
    private BigDecimal minStock;

    /**
     * 最大库存（预警值）
     */
    @DecimalMin(value = "0", message = "最大库存必须大于等于0")
    @ChineseDescription("最大库存")
    private BigDecimal maxStock;

    /**
     * 平均成本
     */
    @DecimalMin(value = "0", message = "平均成本必须大于等于0")
    @ChineseDescription("平均成本")
    private BigDecimal avgCost;

    /**
     * 库存总价值
     */
    @DecimalMin(value = "0", message = "库存总价值必须大于等于0")
    @ChineseDescription("库存总价值")
    private BigDecimal totalValue;

}