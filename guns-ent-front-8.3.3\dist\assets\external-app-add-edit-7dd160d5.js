import{r as o,o as g,a as x,f as C,w as _,d as k,m as y,M as h}from"./index-18a1ea24.js";import U from"./external-app-form-f780e1e8.js";import{E as p}from"./ExternalAppApi-19e996ee.js";/* empty css              */import"./print-4e42e756.js";const F={__name:"external-app-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(m,{emit:v}){const i=m,d=v,s=o(!1),t=o(!1),l=o({apiClientStatus:1,apiClientSort:100,apiClientTokenExpiration:7200}),f=o(null);g(()=>{i.data?(t.value=!0,c()):t.value=!1});const c=()=>{p.detail({apiClientId:i.data.apiClientId}).then(a=>{l.value=Object.assign({},a)})},n=a=>{d("update:visible",a)},b=async()=>{f.value.$refs.formRef.validate().then(async a=>{if(a){s.value=!0;let e=null;t.value?e=p.edit(l.value):e=p.add(l.value),e.then(async r=>{s.value=!1,y.success(r.message),n(!1),d("done")}).catch(()=>{s.value=!1})}})};return(a,e)=>{const r=h;return x(),C(r,{width:700,maskClosable:!1,visible:i.visible,"confirm-loading":s.value,forceRender:!0,title:t.value?"\u7F16\u8F91\u5916\u90E8\u5E94\u7528":"\u65B0\u5EFA\u5916\u90E8\u5E94\u7528","body-style":{paddingBottom:"8px",height:"610px",overflowY:"auto"},"onUpdate:visible":n,onOk:b,onClose:e[1]||(e[1]=u=>n(!1))},{default:_(()=>[k(U,{form:l.value,"onUpdate:form":e[0]||(e[0]=u=>l.value=u),ref_key:"appFormRef",ref:f,isUpdate:t.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}};export{F as default};
