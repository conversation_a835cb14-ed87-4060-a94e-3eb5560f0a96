System.register(["./index-legacy-ee1db0c7.js","./index-legacy-16f295ac.js","./index-legacy-5b978ff2.js","./index-legacy-b540c599.js","./UsersApi-legacy-88b5f949.js","./user-add-edit-legacy-31263f1f.js","./allocation-role-legacy-6a541c72.js","./SysDictTypeApi-legacy-1047ef23.js","./user-form-legacy-37ed40c0.js","./index-legacy-dba03026.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js","./index-legacy-198191c1.js","./index-legacy-c65a6a4e.js","./index-legacy-94a6fc23.js","./FileApi-legacy-f85a3060.js"],(function(e,a){"use strict";var t,l,n,i,d,s,u,c,o,r,m,v,p,g,y,b,x,f,h,I,k,_,j,w,F,N,L,C,T,O,S,U,D;return{setters:[e=>{t=e._,l=e.r,n=e.o,i=e.X,d=e.bv,s=e.a,u=e.f,c=e.w,o=e.b,r=e.d,m=e.g,v=e.t,p=e.aR,g=e.c,y=e.F,b=e.e,x=e.aS,f=e.h,h=e.bK,I=e.B,k=e.u,_=e.v,j=e.G,w=e.H,F=e.i,N=e.C,L=e.ab,C=e.a7,T=e.ch},null,null,null,e=>{O=e.U},e=>{S=e.default},e=>{U=e.default},e=>{D=e.S},null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent="[data-v-3ac33d54] .ant-drawer-title{color:#262626;font-size:18px;font-weight:500}.top[data-v-3ac33d54]{display:flex;align-items:center;justify-content:space-between;height:40px;line-height:40px;margin-bottom:14px}.top .top-left[data-v-3ac33d54]{display:flex;align-items:center}.top .username[data-v-3ac33d54]{margin-left:8px;font-size:20px;font-weight:500;color:#43505e}.content[data-v-3ac33d54]{width:100%;overflow-y:auto;overflow-x:hidden}.content .content-item[data-v-3ac33d54]{width:100%;height:100%}[data-v-3ac33d54] .ant-form-item-label>label{color:#60666b}[data-v-3ac33d54] .ant-form-item{color:#60666b}[data-v-3ac33d54] .ant-checkbox-wrapper-checked .ant-checkbox-disabled .ant-checkbox-inner{--disabled-bg: var(--primary-color);--disabled-color: #fff}[data-v-3ac33d54] .ant-checkbox-disabled+span{--disabled-color: black}\n",document.head.appendChild(a);const z={class:"top"},A={class:"top-left"},E={class:"username"},R={class:"top-right"},B={class:"content"},K={class:"content-item"},Y={key:0},M={key:1},P={key:2},W={key:3},G={class:"content-item"},H={key:0},X={key:1},q={key:0},J={key:1},Q={class:"content-item"},V={class:"content-item"},Z={key:0,style:{width:"100%",display:"flex","align-items":"center"}},$={class:"filename"},ee=["onClick"],ae={__name:"user-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const t=e,ae=a,te=l(null),le=l("1"),ne=l([{key:"1",name:"基础信息",icon:"icon-tab-jichuxinxi"},{key:"2",name:"组织机构",icon:"icon-tab-zuzhijigou"},{key:"3",name:"角色信息",icon:"icon-tab-jiaosexinxi"},{key:"4",name:"用户证书",icon:"icon-tab-yonghuzhengshu"}]),ie=l({userOrgList:[]}),de=l(!1),se=l(!1),ue=l([{name:"超级管理员",value:"superAdminFlag"},{name:"姓名",value:"realName"},{name:"账号",value:"account"},{name:"工号",value:"employeeNumber"},{name:"性别",value:"sex"},{name:"生日",value:"birthday"},{name:"邮箱",value:"email"},{name:"手机号",value:"phone"},{name:"上次登录IP",value:"lastLoginIp"},{name:"上次登录时间",value:"lastLoginTime"},{name:"登录次数",value:"loginCount"},{name:"用户状态",value:"statusFlag"},{name:"创建时间",value:"createTime"},{name:"上次更新时间",value:"updateTime"}]),ce=l([{key:"index",title:"序号",align:"center",customRender:({index:e})=>e+1},{title:"机构名称",align:"center",dataIndex:"orgName"},{title:"职位",align:"center",dataIndex:"positionName"},{title:"是否是主部门",align:"center",dataIndex:"mainFlag"},{title:"是否启用",align:"center",dataIndex:"statusFlag"}]),oe=l([{key:"index",title:"序号",width:60,align:"center",customRender:({index:e})=>e+1},{title:"证书类型",align:"center",dataIndex:"certificateType"},{title:"证书编号",align:"center",dataIndex:"certificateNo"},{title:"发证机构名称",align:"center",dataIndex:"issuingAuthority"},{title:"发证日期",align:"center",dataIndex:"dateIssued"},{title:"附件",align:"center",width:150,ellipsis:!0,dataIndex:"attachmentId"},{title:"到期日期",align:"center",dataIndex:"dateExpires"}]),re=l([]),me=l([]);n((()=>{ye(),ge(),ve()})),i((()=>t.data),(e=>{e&&ge()}),{deep:!0});const ve=async()=>{me.value=await D.getDictListByParams({dictTypeId:"1722790763315597314"})},pe=e=>{let a="";return me.value.find((a=>a.dictId==e))&&(a=me.value.find((a=>a.dictId==e)).dictName),a},ge=()=>{O.detail({userId:t.data.userId}).then((e=>{ie.value=Object.assign({},e),e.userOrgDTOList&&e.userOrgDTOList.length>0&&(ie.value.userOrgList=e.userOrgDTOList.map((e=>({mainFlag:e.mainFlag,statusFlag:e.statusFlag,positionId:e.positionId,positionName:e.positionName,orgId:e.deptId?e.deptId:e.companyId,orgName:e.deptName?e.deptName:e.companyName})))),e.userCertificateList.length>0&&ie.value.userCertificateList.forEach((e=>{e.attachmentId&&(e.attachmentName=e.attachmentIdWrapper.name,e.attachmentUrl=e.attachmentIdWrapper.thumbUrl)}))}))},ye=async()=>{re.value=await O.roleList()},be=e=>{le.value=e},xe=()=>{"3"==le.value?se.value=!0:de.value=!0},fe=e=>{let a=e;return e&&e.length>2&&(a=e.substr(0,1)),a};return(e,a)=>{const l=h,n=I,i=k,O=_,D=j,me=w,ve=F,ye=N,he=L,Ie=C,ke=T,_e=d("permission");return s(),u(ke,{width:800,visible:t.visible,title:"用户信息",onClose:a[3]||(a[3]=e=>{ae("update:visible",!1)}),isShowTab:!0,activeKey:le.value,tabList:ne.value,onTabChange:be},{top:c((()=>[o("div",z,[o("div",A,[r(l,{style:{"background-color":"#6f9ae7"}},{default:c((()=>[m(v(fe(ie.value.realName)),1)])),_:1}),o("span",E,v(ie.value.realName),1)]),o("div",R,[p((s(),u(n,{type:"primary",class:"border-radius",onClick:xe},{default:c((()=>a[4]||(a[4]=[m("编辑")]))),_:1,__:[4]})),[[_e,["EDIT_USER"]]])])])])),default:c((()=>[o("div",B,[p(o("div",K,[r(me,{ref:"formRef",model:ie.value,"label-col":{span:6}},{default:c((()=>[r(D,{gutter:16},{default:c((()=>[(s(!0),g(y,null,b(ue.value,((e,a)=>(s(),u(O,{span:12,key:a},{default:c((()=>[r(i,{label:e.name},{default:c((()=>["superAdminFlag"==e.value?(s(),g("span",Y,v("Y"==ie.value[e.value]?"是":"否"),1)):"statusFlag"==e.value?(s(),g("span",M,v(1==ie.value[e.value]?"启用":"禁用"),1)):"sex"==e.value?(s(),g("span",P,v("M"==ie.value[e.value]?"男":"Y"==ie.value[e.value]?"否":""),1)):(s(),g("span",W,v(ie.value[e.value]),1))])),_:2},1032,["label"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["model"])],512),[[x,"1"==le.value]]),p(o("div",G,[r(ve,{dataSource:ie.value.userOrgList,bordered:"",size:"small",scroll:{x:"max-content"},columns:ce.value,pagination:!1,ref_key:"tableRef",ref:te},{bodyCell:c((({column:e,record:a})=>["mainFlag"==e.dataIndex?(s(),g(y,{key:0},["Y"==a.mainFlag?(s(),g("span",H,"是")):f("",!0),"N"==a.mainFlag?(s(),g("span",X,"否")):f("",!0)],64)):f("",!0),"statusFlag"==e.dataIndex?(s(),g(y,{key:1},[1==a.statusFlag?(s(),g("span",q,"启用")):f("",!0),2==a.statusFlag?(s(),g("span",J,"禁用")):f("",!0)],64)):f("",!0)])),_:1},8,["dataSource","columns"])],512),[[x,"2"==le.value]]),p(o("div",Q,[r(me,{ref:"formRef",model:ie.value,"label-col":{span:3}},{default:c((()=>[r(D,null,{default:c((()=>[r(i,{label:"角色信息",style:{width:"100%"}},{default:c((()=>[r(he,{value:ie.value.roleIdList,"onUpdate:value":a[0]||(a[0]=e=>ie.value.roleIdList=e),disabled:""},{default:c((()=>[(s(!0),g(y,null,b(re.value,(e=>(s(),u(ye,{value:e.roleId,name:"type",key:e.roleId},{default:c((()=>[m(v(e.roleName),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1},8,["model"])],512),[[x,"3"==le.value]]),p(o("div",V,[r(ve,{dataSource:ie.value.userCertificateList,scroll:{x:"max-content"},size:"small",bordered:"",columns:oe.value,pagination:!1},{bodyCell:c((({column:e,record:a})=>["certificateType"==e.dataIndex?(s(),g(y,{key:0},[m(v(pe(a.certificateType)),1)],64)):f("",!0),"dateIssued"==e.dataIndex?(s(),g(y,{key:1},[m(v(a[e.dataIndex]?a[e.dataIndex].substr(0,10):""),1)],64)):f("",!0),"dateExpires"==e.dataIndex?(s(),g(y,{key:2},[m(v(a[e.dataIndex]?a[e.dataIndex].substr(0,10):"长期"),1)],64)):f("",!0),"attachmentId"==e.dataIndex?(s(),g(y,{key:3},[a.attachmentId?(s(),g("div",Z,[o("span",$,[o("a",{onClick:e=>(e=>{const{href:a}=router.resolve({path:e.attachmentUrl});window.open(a,"_blank")})(a)},[r(Ie,null,{title:c((()=>[m(v(a.attachmentName),1)])),default:c((()=>[m(" "+v(a.attachmentName),1)])),_:2},1024)],8,ee)])])):f("",!0)],64)):f("",!0)])),_:1},8,["dataSource","columns"])],512),[[x,"4"==le.value]])]),de.value?(s(),u(S,{key:0,visible:de.value,"onUpdate:visible":a[1]||(a[1]=e=>de.value=e),data:ie.value,onDone:ge},null,8,["visible","data"])):f("",!0),se.value?(s(),u(U,{key:1,visible:se.value,"onUpdate:visible":a[2]||(a[2]=e=>se.value=e),data:ie.value,onDone:ge},null,8,["visible","data"])):f("",!0)])),_:1},8,["visible","activeKey","tabList"])}}};e("default",t(ae,[["__scopeId","data-v-3ac33d54"]]))}}}));
