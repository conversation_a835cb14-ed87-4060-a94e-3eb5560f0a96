<template>
  <a-modal
    :visible="visible"
    title="库存变动历史"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="inventory-history-content">
      <!-- 商品信息 -->
      <a-card title="商品信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="4" bordered size="small">
          <a-descriptions-item label="商品名称">
            <span class="product-name">{{ data.productName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="商品编码">
            {{ data.productCode }}
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            <span :class="getStockClass(data.currentStock, data.minStock)">
              {{ formatStock(data.currentStock, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="预警值">
            {{ formatStock(data.minStock, data.pricingType) }}
            {{ getStockUnit(data.pricingType, data.unit) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 筛选条件 -->
      <a-card title="筛选条件" size="small" style="margin-bottom: 16px">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="操作类型">
            <a-select 
              v-model:value="searchForm.operationType" 
              placeholder="请选择操作类型" 
              allowClear
              style="width: 120px"
              @change="loadHistory"
            >
              <a-select-option v-for="item in operationTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="操作时间">
            <a-range-picker
              v-model:value="dateRange"
              style="width: 240px"
              :placeholder="['开始时间', '结束时间']"
              format="YYYY-MM-DD"
              @change="onDateRangeChange"
            />
          </a-form-item>
          <a-form-item label="操作人">
            <a-input
              v-model:value="searchForm.operatorName"
              placeholder="请输入操作人"
              allowClear
              style="width: 120px"
              @pressEnter="loadHistory"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="loadHistory">搜索</a-button>
              <a-button @click="resetSearch">重置</a-button>
              <a-button @click="exportHistory">导出</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 历史记录列表 -->
      <a-card title="变动记录" size="small">
        <template #extra>
          <a-space>
            <span class="record-count">共 {{ pagination.total }} 条记录</span>
          </a-space>
        </template>



        <a-table
          :columns="columns"
          :data-source="historyList"
          :pagination="pagination"
          :loading="loading"
          size="small"
          bordered
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <!-- 操作类型 -->
            <template v-if="column.key === 'operationType'">
              <a-tag :color="getOperationTypeColor(record.operationType)">
                {{ getOperationTypeName(record.operationType) }}
              </a-tag>
            </template>

            <!-- 数量变化 -->
            <template v-if="column.key === 'quantity'">
              <span :class="getQuantityChangeClass(record.quantityChange, record.operationType)">
                {{ formatQuantityChange(record.quantityChange, record.operationType) }}
                {{ getStockUnit(data.pricingType, data.unit) }}
              </span>
            </template>

            <!-- 操作前库存 -->
            <template v-if="column.key === 'beforeStock'">
              {{ formatStock(record.stockBefore, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </template>

            <!-- 操作后库存 -->
            <template v-if="column.key === 'afterStock'">
              {{ formatStock(record.stockAfter, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </template>

            <!-- 单位成本 -->
            <template v-if="column.key === 'unitCost'">
              <span v-if="record.unitCost">¥{{ formatAmount(record.unitCost) }}</span>
              <span v-else>-</span>
            </template>

            <!-- 总价值变化 -->
            <template v-if="column.key === 'totalValueChange'">
              <span v-if="record.totalValueChange" :class="getValueChangeClass(record.totalValueChange)">
                {{ formatValueChange(record.totalValueChange) }}
              </span>
              <span v-else>-</span>
            </template>

            <!-- 关联单据 -->
            <template v-if="column.key === 'referenceType'">
              <span v-if="record.referenceTypeName">{{ record.referenceTypeName }}</span>
              <span v-else-if="record.referenceType">{{ getReferenceTypeName(record.referenceType) }}</span>
              <span v-else>-</span>
            </template>

            <!-- 操作时间 -->
            <template v-if="column.key === 'operationTime'">
              {{ record.operationTime }}
            </template>

            <!-- 操作人 -->
            <template v-if="column.key === 'operatorName'">
              {{ record.operationUserName || record.operatorName || '-' }}
            </template>

            <!-- 备注 -->
            <template v-if="column.key === 'remark'">
              <a-tooltip v-if="record.remark && record.remark.length > 20" :title="record.remark">
                {{ record.remark.substring(0, 20) }}...
              </a-tooltip>
              <span v-else>{{ record.remark || '-' }}</span>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryHistoryApi } from '../api/InventoryHistoryApi';

export default {
  name: 'InventoryHistory',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const loading = ref(false);
    const historyList = ref([]);
    const dateRange = ref([]);

    // 搜索表单
    const searchForm = reactive({
      productId: null,
      operationType: undefined,
      operatorName: '',
      startTime: undefined,
      endTime: undefined
    });

    // 分页参数
    const pagination = reactive({
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    });

    // 操作类型选项
    const operationTypeOptions = InventoryHistoryApi.getOperationTypeOptions();

    // 表格列定义
    const columns = [
      {
        title: '操作类型',
        key: 'operationType',
        width: 100,
        fixed: 'left'
      },
      {
        title: '数量变化',
        key: 'quantity',
        width: 120,
        align: 'right'
      },
      {
        title: '操作前库存',
        key: 'beforeStock',
        width: 120,
        align: 'right'
      },
      {
        title: '操作后库存',
        key: 'afterStock',
        width: 120,
        align: 'right'
      },
      {
        title: '关联单据',
        key: 'referenceType',
        width: 120
      },
      {
        title: '操作时间',
        key: 'operationTime',
        width: 160
      },
      {
        title: '操作人',
        key: 'operatorName',
        width: 100
      },
      {
        title: '备注',
        key: 'remark',
        width: 200,
        ellipsis: true
      }
    ];

    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      if (visible && props.data.productId) {
        searchForm.productId = props.data.productId;
        resetSearch();
        loadHistory();
      }
    });

    // 加载历史记录
    const loadHistory = async () => {
      loading.value = true;
      try {
        const params = {
          ...searchForm,
          pageNo: pagination.current,
          pageSize: pagination.pageSize
        };

        const response = await InventoryHistoryApi.findPage(params);
        
        // 处理响应数据
        if (response && response.success !== false) {
          // 兼容不同的响应格式
          let records = [];
          let total = 0;
          
          if (response.rows) {
            // 直接响应格式（API直接返回分页数据）
            records = response.rows;
            total = response.totalRows || 0;
          } else if (response.data && response.data.rows) {
            // 标准分页响应格式（使用rows字段）
            records = response.data.rows;
            total = response.data.totalRows || 0;
          } else if (response.data && response.data.records) {
            // 备用分页响应格式（使用records字段）
            records = response.data.records;
            total = response.data.total || 0;
          } else if (Array.isArray(response.data)) {
            // 直接返回数组格式
            records = response.data;
            total = records.length;
          } else if (Array.isArray(response)) {
            // 响应本身就是数组
            records = response;
            total = records.length;
          }
          
          // 处理数据格式，确保字段映射正确
          historyList.value = records.map(item => ({
            ...item,
            // 确保字段名称正确映射
            quantity: item.quantityChange,
            beforeStock: item.stockBefore,
            afterStock: item.stockAfter,
            operatorName: item.operationUserName || item.operatorName || (item.operationUser ? `操作员${item.operationUser}` : '系统操作'),
            // 格式化操作时间
            operationTime: formatDateTime(item.operationTime)
          }));
          
          pagination.total = total;
          // 更新分页信息
          if (response.pageNo) {
            pagination.current = response.pageNo;
            pagination.pageSize = response.pageSize;
          } else if (response.data && response.data.pageNo) {
            pagination.current = response.data.pageNo;
            pagination.pageSize = response.data.pageSize;
          }
        } else {
          historyList.value = [];
          pagination.total = 0;
        }
      } catch (error) {
        message.error('加载库存历史失败：' + (error.message || '未知错误'));
        historyList.value = [];
        pagination.total = 0;
      } finally {
        loading.value = false;
      }
    };

    // 表格变化处理
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      loadHistory();
    };

    // 日期范围变化
    const onDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        searchForm.startTime = dates[0];
        searchForm.endTime = dates[1];
      } else {
        searchForm.startTime = undefined;
        searchForm.endTime = undefined;
      }
      loadHistory();
    };

    // 重置搜索
    const resetSearch = () => {
      searchForm.operationType = undefined;
      searchForm.operatorName = '';
      searchForm.startTime = undefined;
      searchForm.endTime = undefined;
      dateRange.value = [];
      pagination.current = 1;
    };

    // 导出历史记录
    const exportHistory = () => {
      try {
        InventoryHistoryApi.exportHistory({
          ...searchForm,
          productId: props.data.productId
        });
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败：' + (error.message || '未知错误'));
      }
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-';
      
      // 如果是字符串，尝试转换为Date对象
      const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime;
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) return dateTime;
      
      // 格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    // 获取库存样式类
    const getStockClass = (currentStock, minStock) => {
      const current = parseFloat(currentStock) || 0;
      const min = parseFloat(minStock) || 0;
      
      if (current <= 0) return 'stock-danger';
      if (current <= min) return 'stock-warning';
      return 'stock-normal';
    };

    // 获取操作类型名称
    const getOperationTypeName = (operationType) => {
      return InventoryHistoryApi.getOperationTypeName(operationType);
    };

    // 获取操作类型颜色
    const getOperationTypeColor = (operationType) => {
      return InventoryHistoryApi.getOperationTypeColor(operationType);
    };

    // 格式化数量变化
    const formatQuantityChange = (quantity, operationType) => {
      return InventoryHistoryApi.formatQuantityChange(quantity, operationType);
    };

    // 获取数量变化样式类
    const getQuantityChangeClass = (quantity, operationType) => {
      const numQuantity = parseFloat(quantity) || 0;
      switch (operationType) {
        case 'IN':
          return 'quantity-increase';
        case 'OUT':
        case 'SALE':
          return 'quantity-decrease';
        case 'ADJUST':
          return numQuantity >= 0 ? 'quantity-increase' : 'quantity-decrease';
        case 'SET_ALERT':
          return '';
        default:
          return '';
      }
    };

    // 格式化价值变化
    const formatValueChange = (valueChange) => {
      const value = parseFloat(valueChange) || 0;
      return value >= 0 ? `+¥${value.toFixed(2)}` : `-¥${Math.abs(value).toFixed(2)}`;
    };

    // 获取价值变化样式类
    const getValueChangeClass = (valueChange) => {
      const value = parseFloat(valueChange) || 0;
      return value >= 0 ? 'value-increase' : 'value-decrease';
    };

    // 获取关联单据类型名称
    const getReferenceTypeName = (referenceType) => {
      const typeMap = {
        'PURCHASE_ORDER': '采购入库单',
        'SALE_ORDER': '销售单',
        'ADJUST_ORDER': '调整单',
        'MANUAL': '手动操作'
      };
      return typeMap[referenceType] || referenceType;
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    return {
      loading,
      historyList,
      dateRange,
      searchForm,
      pagination,
      operationTypeOptions,
      columns,
      loadHistory,
      handleTableChange,
      onDateRangeChange,
      resetSearch,
      exportHistory,
      getStockUnit,
      formatStock,
      formatAmount,
      getStockClass,
      getOperationTypeName,
      getOperationTypeColor,
      formatQuantityChange,
      getQuantityChangeClass,
      formatValueChange,
      getValueChangeClass,
      getReferenceTypeName,
      handleCancel
    };
  }
};
</script>

<style scoped>
.inventory-history-content {
  max-height: 70vh;
  overflow-y: auto;
}

.product-name {
  font-weight: 500;
  color: #1890ff;
}

.record-count {
  color: #8c8c8c;
  font-size: 12px;
}

.stock-normal {
  color: #52c41a;
  font-weight: 500;
}

.stock-warning {
  color: #faad14;
  font-weight: 500;
}

.stock-danger {
  color: #ff4d4f;
  font-weight: 500;
}

.quantity-increase {
  color: #52c41a;
  font-weight: 500;
}

.quantity-decrease {
  color: #ff4d4f;
  font-weight: 500;
}

.value-increase {
  color: #52c41a;
  font-weight: 500;
}

.value-decrease {
  color: #ff4d4f;
  font-weight: 500;
}
</style>
