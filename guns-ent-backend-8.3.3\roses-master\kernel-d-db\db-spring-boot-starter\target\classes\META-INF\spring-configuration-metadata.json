{"groups": [{"name": "spring.datasource", "type": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties", "sourceType": "cn.stylefeng.roses.kernel.db.starter.ProjectDruidPropertiesAutoConfiguration", "sourceMethod": "druidProperties()"}, {"name": "tenant", "type": "cn.stylefeng.roses.kernel.db.api.pojo.tenant.TenantTableProperties", "sourceType": "cn.stylefeng.roses.kernel.db.starter.ProjectMyBatisPlusAutoConfiguration", "sourceMethod": "tenantTableProperties()"}], "properties": [{"name": "spring.datasource.data-source-name", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.driver-class-name", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.filters", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.initial-size", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.keep-alive", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.max-active", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.max-pool-prepared-statement-per-connection-size", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.max-wait", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.min-evictable-idle-time-millis", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.min-idle", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.password", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.pool-prepared-statements", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.test-on-borrow", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.test-on-return", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.test-while-idle", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.time-between-eviction-runs-millis", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.url", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.username", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.validation-query", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "spring.datasource.validation-query-timeout", "type": "java.lang.Integer", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.druid.DruidProperties"}, {"name": "tenant.business-table-list", "type": "java.util.List<java.lang.String>", "sourceType": "cn.stylefeng.roses.kernel.db.api.pojo.tenant.TenantTableProperties"}], "hints": []}