package cn.stylefeng.roses.kernel.impexp.org.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.org.pojo.base.OrgExcelImportParse;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.sys.api.enums.org.OrgTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织机构导出数据的创建
 *
 * <AUTHOR>
 * @since 2024-02-18 16:04
 */
public class OrgExportFactory {

    /**
     * 组织机构导出数据的创建
     *
     * <AUTHOR>
     * @since 2024-02-18 16:04
     */
    public static List<OrgExcelImportParse> createOrgExportData(List<HrOrganization> organizationList) {

        if (ObjectUtil.isEmpty(organizationList)) {
            return new ArrayList<>();
        }

        List<OrgExcelImportParse> orgExcelImportParses = new ArrayList<>();

        int number = 1;

        for (HrOrganization organization : organizationList) {
            OrgExcelImportParse orgExcelImportParse = new OrgExcelImportParse();

            // 转化机构id
            orgExcelImportParse.setOrgId(String.valueOf(organization.getOrgId()));

            // 转化序号
            orgExcelImportParse.setNumber(String.valueOf(number));
            number++;

            // 转化机构名称
            orgExcelImportParse.setOrgName(organization.getOrgName());

            // 转化机构简称
            orgExcelImportParse.setOrgShortName(organization.getOrgShortName());

            // 父级机构名称
            orgExcelImportParse.setParentOrgName(organization.getParentOrgName());

            // 转化机构编码
            orgExcelImportParse.setOrgCode(organization.getOrgCode());

            // 转化排序
            orgExcelImportParse.setOrgSort(String.valueOf(organization.getOrgSort()));

            // 转化状态
            orgExcelImportParse.setStatusFlag(StatusEnum.codeToEnum(organization.getStatusFlag()).getMessage());

            // 转化机构类型
            orgExcelImportParse.setOrgType(OrgTypeEnum.getCodeMessage(organization.getOrgType()));

            // 转化税号
            orgExcelImportParse.setTaxNo(organization.getTaxNo());

            // 转化备注
            orgExcelImportParse.setRemark(organization.getRemark());

            // 转化对外主数据系统的机构id
            orgExcelImportParse.setMasterOrgId(String.valueOf(organization.getMasterOrgId()));

            orgExcelImportParses.add(orgExcelImportParse);
        }

        return orgExcelImportParses;
    }

}
