package cn.stylefeng.roses.ent.mobile.manage.pojo.index;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.sys.modular.login.pojo.IndexUserOrgInfo;
import cn.stylefeng.roses.kernel.sys.modular.message.entity.SysMessage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 移动端用户的首页信息
 *
 * <AUTHOR>
 * @since 2024/3/20 21:32
 */
@Data
public class MobileUserIndexInfo {

    /**
     * 用户主键id
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("用户主键id")
    private Long userId;

    /**
     * 真实姓名
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("真实姓名")
    private String realName;

    /**
     * 头像地址
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("头像地址")
    private String avatarUrl;

    /**
     * 头像id
     * <p>
     * 移动端-个人详情界面用
     */
    @ChineseDescription("头像id")
    private Long avatar;

    /**
     * 用户工号
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("用户工号")
    private String employeeNumber;

    /**
     * 用户手机号
     * <p>
     * 移动端-个人详情界面
     */
    @ChineseDescription("用户手机号")
    private String phone;

    /**
     * 性别：M-男，F-女
     * <p>
     * 移动端-个人详情界面
     */
    @ChineseDescription("性别：M-男，F-女")
    private String sex;

    /**
     * 邮箱
     * <p>
     * 移动端-个人详情界面
     */
    @ChineseDescription("邮箱")
    private String email;

    /**
     * 账号
     */
    @ChineseDescription("账号")
    private String account;

    /**
     * 生日
     */
    @ChineseDescription("生日")
    private Date birthday;

    /**
     * 当前用户的部门和任职信息
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("当前用户的部门和任职信息")
    private List<IndexUserOrgInfo> userOrgInfoList;

    /**
     * 系统消息列表
     * <p>
     * 移动端-首页用
     */
    @ChineseDescription("系统消息列表")
    private List<SysMessage> sysMessageList;

}
