package cn.stylefeng.roses.ent.mobile.invite.pojo.response;

import cn.stylefeng.roses.ent.mobile.invite.entity.SysInviteUser;
import cn.stylefeng.roses.kernel.file.api.format.FileUrlFormatProcess;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.annotation.SimpleFieldFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邀请用户返回值封装
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysInviteUserVo extends SysInviteUser {

    /**
     * 默认的用户头像
     */
    @SimpleFieldFormat(processClass = FileUrlFormatProcess.class)
    private Long avatarId = 10000L;

    /**
     * 公司名称
     */
    @ChineseDescription("公司名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ChineseDescription("部门名称")
    private String deptName;

    /**
     * 邀请人姓名
     */
    @ChineseDescription("邀请人姓名")
    private String inviteUserName;

    /**
     * 邀请人电话
     */
    @ChineseDescription("邀请人电话")
    private String inviteUserPhone;

}
