<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.ent.saas.modular.auth.mapper.TenantPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackage">
		<id column="package_id" property="packageId" />
		<result column="package_name" property="packageName" />
		<result column="package_price" property="packagePrice" />
		<result column="version_flag" property="versionFlag" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		package_id,package_name,package_price,version_flag,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.ent.saas.modular.auth.pojo.response.TenantPackageVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		ent_tenant_package tbl
		WHERE
		<where>
        <if test="param.packageId != null and param.packageId != ''">
            and tbl.package_id like concat('%',#{param.packageId},'%')
        </if>
        <if test="param.packageName != null and param.packageName != ''">
            and tbl.package_name like concat('%',#{param.packageName},'%')
        </if>
        <if test="param.packagePrice != null and param.packagePrice != ''">
            and tbl.package_price like concat('%',#{param.packagePrice},'%')
        </if>
        <if test="param.versionFlag != null and param.versionFlag != ''">
            and tbl.version_flag like concat('%',#{param.versionFlag},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
