System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-d2fc620d.js"],(function(e,n){"use strict";var t,i,l,d,s,a,o,c,r,u,v,g;return{setters:[e=>{t=e.r,i=e.s,l=e.a,d=e.c,s=e.d,a=e.w,o=e.g,c=e.b,r=e.t,u=e.B,v=e.a0},null,e=>{g=e._}],execute:function(){const n={class:"guns-body guns-body-card"},y={style:{"margin-top":"12px"}},p={style:{"margin-top":"12px"}},b={style:{"margin-top":"12px"}};e("default",{__name:"index",setup(e){const x=t(!1),$=()=>{x.value=!0},_=i({location:"",address:"",lngAndLat:""}),f=e=>{var n,t,i;console.log(e),_.location=`${null===(n=e.city)||void 0===n?void 0:n.province}/${null===(t=e.city)||void 0===t?void 0:t.city}/${null===(i=e.city)||void 0===i?void 0:i.district}`,_.address=`${e.name} ${e.address}`,_.lngAndLat=`${e.lng},${e.lat}`,x.value=!1};return(e,t)=>{const i=u,m=g,j=v;return l(),d("div",n,[s(j,{title:"地图选择",bordered:!1},{default:a((()=>[s(i,{class:"ele-btn-icon",onClick:$},{default:a((()=>t[1]||(t[1]=[o(" 打开地图位置选择器 ")]))),_:1,__:[1]}),c("div",y,"选择位置: "+r(_.location),1),c("div",p,"详细地址: "+r(_.address),1),c("div",b,"经 纬 度 : "+r(_.lngAndLat),1),s(m,{"need-city":!0,visible:x.value,"onUpdate:visible":t[0]||(t[0]=e=>x.value=e),"search-type":0,onDone:f},null,8,["visible"])])),_:1})])}}})}}}));
