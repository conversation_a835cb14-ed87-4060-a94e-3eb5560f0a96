package cn.stylefeng.roses.kernel.erp.starter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * ERP模块配置
 *
 * <AUTHOR>
 * @since 2025/07/20 14:30
 */
@Data
@ConfigurationProperties(prefix = "roses.erp")
public class ErpModuleConfig {

    /**
     * 是否启用ERP模块
     */
    private Boolean enabled = true;

    /**
     * ERP模块名称
     */
    private String moduleName = "ERP企业资源规划";

    /**
     * ERP模块版本
     */
    private String moduleVersion = "1.0.0";

    /**
     * ERP模块描述
     */
    private String moduleDescription = "ERP企业资源规划管理模块，提供供应商、客户、商品、区域等基础数据管理功能";

    /**
     * 是否启用数据权限控制
     */
    private Boolean dataPermissionEnabled = true;

    /**
     * 是否启用操作日志记录
     */
    private Boolean operateLogEnabled = true;

    /**
     * 是否启用缓存
     */
    private Boolean cacheEnabled = true;

    /**
     * 缓存过期时间（秒）
     */
    private Long cacheExpireSeconds = 3600L;

}
