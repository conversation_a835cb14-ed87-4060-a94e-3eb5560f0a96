package cn.stylefeng.roses.kernel.erp.modular.pos.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment;
import cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosPaymentMapper;
import cn.stylefeng.roses.kernel.erp.modular.pos.service.PosOrderService;
import cn.stylefeng.roses.kernel.erp.api.exception.ErpException;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.erp.modular.pos.service.PosPaymentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * POS支付服务实现类
 *
 * <AUTHOR>
 * @since 2025/08/01 11:00
 */
@Service
public class PosPaymentServiceImpl extends ServiceImpl<PosPaymentMapper, PosPayment> implements PosPaymentService {

    @Resource
    private PosPaymentMapper posPaymentMapper;

    @Resource
    private PosOrderService posOrderService;

    // 支付方式常量
    private static final String PAYMENT_METHOD_CASH = "CASH";
    private static final String PAYMENT_METHOD_WECHAT = "WECHAT";
    private static final String PAYMENT_METHOD_ALIPAY = "ALIPAY";
    private static final String PAYMENT_METHOD_MEMBER = "MEMBER";
    private static final String PAYMENT_METHOD_CARD = "CARD";

    // 支付状态常量
    private static final String PAYMENT_STATUS_PENDING = "PENDING";
    private static final String PAYMENT_STATUS_SUCCESS = "SUCCESS";
    private static final String PAYMENT_STATUS_FAILED = "FAILED";
    private static final String PAYMENT_STATUS_CANCELLED = "CANCELLED";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processCashPayment(Long orderId, BigDecimal paymentAmount, BigDecimal receivedAmount) {
        // 参数校验
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isNull(paymentAmount) || ObjectUtil.isNull(receivedAmount)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        if (receivedAmount.compareTo(paymentAmount) < 0) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_AMOUNT_INSUFFICIENT);
        }

        // 校验支付金额
        if (!validatePaymentAmount(orderId, paymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_AMOUNT_ERROR);
        }

        // 计算找零
        BigDecimal changeAmount = calculateChange(paymentAmount, receivedAmount);

        // 创建支付记录
        PosPayment payment = new PosPayment();
        payment.setOrderId(orderId);
        payment.setPaymentNo(generatePaymentNo(PAYMENT_METHOD_CASH));
        payment.setPaymentMethod(PAYMENT_METHOD_CASH);
        payment.setPaymentAmount(paymentAmount);
        payment.setReceivedAmount(receivedAmount);
        payment.setChangeAmount(changeAmount);
        payment.setPaymentStatus(PAYMENT_STATUS_SUCCESS);
        payment.setPaymentTime(LocalDateTime.now());

        this.save(payment);

        // 更新订单支付状态
        posOrderService.updatePaymentStatus(orderId, "PAID", PAYMENT_METHOD_CASH);

        return payment.getPaymentId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processQrCodePayment(Long orderId, BigDecimal paymentAmount, String paymentMethod) {
        // 参数校验
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isNull(paymentAmount) || StrUtil.isBlank(paymentMethod)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        if (!PAYMENT_METHOD_WECHAT.equals(paymentMethod) && !PAYMENT_METHOD_ALIPAY.equals(paymentMethod)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_METHOD_NOT_SUPPORTED);
        }

        // 校验支付金额
        if (!validatePaymentAmount(orderId, paymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_AMOUNT_ERROR);
        }

        // 创建支付记录
        PosPayment payment = new PosPayment();
        payment.setOrderId(orderId);
        payment.setPaymentNo(generatePaymentNo(paymentMethod));
        payment.setPaymentMethod(paymentMethod);
        payment.setPaymentAmount(paymentAmount);
        payment.setPaymentStatus(PAYMENT_STATUS_PENDING);

        this.save(payment);

        // 注意：扫码支付需要等待第三方回调确认，这里先返回支付记录ID
        // 实际的支付确认会通过 confirmPaymentSuccess 方法处理

        return payment.getPaymentId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processMemberCardPayment(Long orderId, BigDecimal paymentAmount, Long memberId) {
        // 参数校验
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isNull(paymentAmount) || ObjectUtil.isNull(memberId)) {
            throw new ErpException(ErpPosExceptionEnum.MEMBER_NOT_EXIST);
        }

        // 校验支付金额
        if (!validatePaymentAmount(orderId, paymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_AMOUNT_ERROR);
        }

        // 校验会员余额
        if (!validateMemberBalance(memberId, paymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.MEMBER_POINTS_INSUFFICIENT);
        }

        // 创建支付记录
        PosPayment payment = new PosPayment();
        payment.setOrderId(orderId);
        payment.setPaymentNo(generatePaymentNo(PAYMENT_METHOD_MEMBER));
        payment.setPaymentMethod(PAYMENT_METHOD_MEMBER);
        payment.setPaymentAmount(paymentAmount);
        payment.setPaymentStatus(PAYMENT_STATUS_SUCCESS);
        payment.setPaymentTime(LocalDateTime.now());
        payment.setRemark("会员ID: " + memberId);

        this.save(payment);

        // 更新订单支付状态
        posOrderService.updatePaymentStatus(orderId, "PAID", PAYMENT_METHOD_MEMBER);

        // TODO: 扣减会员余额（需要集成会员系统）

        return payment.getPaymentId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processBankCardPayment(Long orderId, BigDecimal paymentAmount, String cardNo) {
        // 参数校验
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isNull(paymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        // 校验支付金额
        if (!validatePaymentAmount(orderId, paymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_AMOUNT_ERROR);
        }

        // 创建支付记录
        PosPayment payment = new PosPayment();
        payment.setOrderId(orderId);
        payment.setPaymentNo(generatePaymentNo(PAYMENT_METHOD_CARD));
        payment.setPaymentMethod(PAYMENT_METHOD_CARD);
        payment.setPaymentAmount(paymentAmount);
        payment.setPaymentStatus(PAYMENT_STATUS_PENDING);
        
        if (StrUtil.isNotBlank(cardNo)) {
            payment.setRemark("银行卡号: " + maskCardNo(cardNo));
        }

        this.save(payment);

        // 注意：银行卡支付需要等待POS机确认，这里先返回支付记录ID
        // 实际的支付确认会通过 confirmPaymentSuccess 方法处理

        return payment.getPaymentId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> processMixedPayment(Long orderId, List<PaymentDetail> paymentDetails) {
        // 参数校验
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isEmpty(paymentDetails)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        // 计算总支付金额
        BigDecimal totalPaymentAmount = paymentDetails.stream()
                .map(PaymentDetail::getPaymentAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 校验总支付金额
        if (!validatePaymentAmount(orderId, totalPaymentAmount)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_AMOUNT_ERROR);
        }

        List<Long> paymentIds = new ArrayList<>();

        // 处理每种支付方式
        for (PaymentDetail detail : paymentDetails) {
            Long paymentId = null;
            
            switch (detail.getPaymentMethod()) {
                case PAYMENT_METHOD_CASH:
                    paymentId = processCashPayment(orderId, detail.getPaymentAmount(), detail.getReceivedAmount());
                    break;
                case PAYMENT_METHOD_WECHAT:
                case PAYMENT_METHOD_ALIPAY:
                    paymentId = processQrCodePayment(orderId, detail.getPaymentAmount(), detail.getPaymentMethod());
                    break;
                case PAYMENT_METHOD_MEMBER:
                    paymentId = processMemberCardPayment(orderId, detail.getPaymentAmount(), detail.getMemberId());
                    break;
                case PAYMENT_METHOD_CARD:
                    paymentId = processBankCardPayment(orderId, detail.getPaymentAmount(), detail.getCardNo());
                    break;
                default:
                    throw new ErpException(ErpPosExceptionEnum.PAYMENT_METHOD_NOT_SUPPORTED);
            }
            
            if (paymentId != null) {
                paymentIds.add(paymentId);
            }
        }

        return paymentIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmPaymentSuccess(Long paymentId, String transactionId) {
        if (ObjectUtil.isNull(paymentId)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        PosPayment payment = this.getById(paymentId);
        if (ObjectUtil.isNull(payment)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        // 更新支付状态
        LambdaUpdateWrapper<PosPayment> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosPayment::getPaymentId, paymentId)
               .set(PosPayment::getPaymentStatus, PAYMENT_STATUS_SUCCESS)
               .set(PosPayment::getPaymentTime, LocalDateTime.now());
        
        if (StrUtil.isNotBlank(transactionId)) {
            wrapper.set(PosPayment::getTransactionId, transactionId);
        }

        this.update(wrapper);

        // 更新订单支付状态
        posOrderService.updatePaymentStatus(payment.getOrderId(), "PAID", payment.getPaymentMethod());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlePaymentFailure(Long paymentId, String failureReason) {
        if (ObjectUtil.isNull(paymentId)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        LambdaUpdateWrapper<PosPayment> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosPayment::getPaymentId, paymentId)
               .set(PosPayment::getPaymentStatus, PAYMENT_STATUS_FAILED);
        
        if (StrUtil.isNotBlank(failureReason)) {
            wrapper.set(PosPayment::getRemark, failureReason);
        }

        this.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPayment(Long paymentId, String reason) {
        if (ObjectUtil.isNull(paymentId)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        LambdaUpdateWrapper<PosPayment> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosPayment::getPaymentId, paymentId)
               .set(PosPayment::getPaymentStatus, PAYMENT_STATUS_CANCELLED);
        
        if (StrUtil.isNotBlank(reason)) {
            wrapper.set(PosPayment::getRemark, reason);
        }

        this.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processRefund(Long paymentId, BigDecimal refundAmount, String reason) {
        if (ObjectUtil.isNull(paymentId) || ObjectUtil.isNull(refundAmount)) {
            throw new ErpException(ErpPosExceptionEnum.REFUND_FAILED);
        }

        PosPayment originalPayment = this.getById(paymentId);
        if (ObjectUtil.isNull(originalPayment)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        if (!PAYMENT_STATUS_SUCCESS.equals(originalPayment.getPaymentStatus())) {
            throw new ErpException(ErpPosExceptionEnum.REFUND_FAILED);
        }

        if (refundAmount.compareTo(originalPayment.getPaymentAmount()) > 0) {
            throw new ErpException(ErpPosExceptionEnum.REFUND_AMOUNT_INVALID);
        }

        // 创建退款记录
        PosPayment refundPayment = new PosPayment();
        refundPayment.setOrderId(originalPayment.getOrderId());
        refundPayment.setPaymentNo(generatePaymentNo("REFUND"));
        refundPayment.setPaymentMethod(originalPayment.getPaymentMethod());
        refundPayment.setPaymentAmount(refundAmount.negate()); // 负数表示退款
        refundPayment.setPaymentStatus(PAYMENT_STATUS_SUCCESS);
        refundPayment.setPaymentTime(LocalDateTime.now());
        refundPayment.setRemark("退款原因: " + (StrUtil.isNotBlank(reason) ? reason : "无"));

        this.save(refundPayment);

        // 更新订单支付状态为已退款
        posOrderService.updatePaymentStatus(originalPayment.getOrderId(), "REFUNDED", originalPayment.getPaymentMethod());

        return refundPayment.getPaymentId();
    }

    @Override
    public BigDecimal calculateChange(BigDecimal paymentAmount, BigDecimal receivedAmount) {
        if (ObjectUtil.isNull(paymentAmount) || ObjectUtil.isNull(receivedAmount)) {
            return BigDecimal.ZERO;
        }
        return receivedAmount.subtract(paymentAmount);
    }

    @Override
    public List<PosPayment> getPaymentsByOrderId(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PosPayment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosPayment::getOrderId, orderId)
               .orderByDesc(PosPayment::getCreateTime);

        return this.list(wrapper);
    }

    @Override
    public PosPayment getPaymentById(Long paymentId) {
        if (ObjectUtil.isNull(paymentId)) {
            return null;
        }
        return this.getById(paymentId);
    }

    @Override
    public PosPayment getPaymentByNo(String paymentNo) {
        if (StrUtil.isBlank(paymentNo)) {
            return null;
        }

        LambdaQueryWrapper<PosPayment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosPayment::getPaymentNo, paymentNo);

        return this.getOne(wrapper);
    }

    @Override
    public PageResult<PosPayment> findPaymentPage(Integer pageNo, Integer pageSize,
                                                 String paymentMethod, String paymentStatus, Long orderId) {
        LambdaQueryWrapper<PosPayment> wrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(paymentMethod)) {
            wrapper.eq(PosPayment::getPaymentMethod, paymentMethod);
        }
        if (StrUtil.isNotBlank(paymentStatus)) {
            wrapper.eq(PosPayment::getPaymentStatus, paymentStatus);
        }
        if (ObjectUtil.isNotNull(orderId)) {
            wrapper.eq(PosPayment::getOrderId, orderId);
        }
        
        wrapper.orderByDesc(PosPayment::getCreateTime);

        Page<PosPayment> page = new Page<>(pageNo, pageSize);
        Page<PosPayment> resultPage = this.page(page, wrapper);
        
        return PageResultFactory.createPageResult(resultPage);
    }

    @Override
    public String generatePaymentNo(String paymentMethod) {
        // 生成格式：PAY + 支付方式前缀 + yyyyMMddHHmmss + 3位随机数
        String prefix = getPaymentMethodPrefix(paymentMethod);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 900) + 100; // 100-999的随机数
        return "PAY" + prefix + timestamp + random;
    }

    @Override
    public boolean validatePaymentAmount(Long orderId, BigDecimal paymentAmount) {
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isNull(paymentAmount)) {
            return false;
        }

        PosOrder order = posOrderService.getOrderById(orderId);
        if (ObjectUtil.isNull(order)) {
            return false;
        }

        // 获取已支付金额
        BigDecimal paidAmount = getPaidAmountByOrderId(orderId);
        
        // 计算待支付金额
        BigDecimal unpaidAmount = order.getFinalAmount().subtract(paidAmount);

        // 支付金额应该等于待支付金额（允许部分支付的情况下可以小于等于）
        return paymentAmount.compareTo(unpaidAmount) <= 0 && paymentAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    @Override
    public boolean validateMemberBalance(Long memberId, BigDecimal paymentAmount) {
        if (ObjectUtil.isNull(memberId) || ObjectUtil.isNull(paymentAmount)) {
            return false;
        }

        // TODO: 集成会员系统，查询会员余额
        // 这里暂时返回true，实际实现时需要调用会员服务
        return true;
    }

    @Override
    public BigDecimal getPaidAmountByOrderId(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return BigDecimal.ZERO;
        }

        LambdaQueryWrapper<PosPayment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosPayment::getOrderId, orderId)
               .eq(PosPayment::getPaymentStatus, PAYMENT_STATUS_SUCCESS);

        List<PosPayment> payments = this.list(wrapper);
        
        return payments.stream()
                .map(PosPayment::getPaymentAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getUnpaidAmountByOrderId(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return BigDecimal.ZERO;
        }

        PosOrder order = posOrderService.getOrderById(orderId);
        if (ObjectUtil.isNull(order)) {
            return BigDecimal.ZERO;
        }

        BigDecimal paidAmount = getPaidAmountByOrderId(orderId);
        return order.getFinalAmount().subtract(paidAmount);
    }

    /**
     * 获取支付方式前缀
     *
     * @param paymentMethod 支付方式
     * @return 前缀
     */
    private String getPaymentMethodPrefix(String paymentMethod) {
        switch (paymentMethod) {
            case PAYMENT_METHOD_CASH:
                return "CSH";
            case PAYMENT_METHOD_WECHAT:
                return "WX";
            case PAYMENT_METHOD_ALIPAY:
                return "ALI";
            case PAYMENT_METHOD_MEMBER:
                return "MBR";
            case PAYMENT_METHOD_CARD:
                return "CRD";
            case "REFUND":
                return "RFD";
            default:
                return "OTH";
        }
    }

    /**
     * 银行卡号脱敏
     *
     * @param cardNo 银行卡号
     * @return 脱敏后的卡号
     */
    private String maskCardNo(String cardNo) {
        if (StrUtil.isBlank(cardNo) || cardNo.length() < 8) {
            return cardNo;
        }
        
        String prefix = cardNo.substring(0, 4);
        String suffix = cardNo.substring(cardNo.length() - 4);
        return prefix + "****" + suffix;
    }

}