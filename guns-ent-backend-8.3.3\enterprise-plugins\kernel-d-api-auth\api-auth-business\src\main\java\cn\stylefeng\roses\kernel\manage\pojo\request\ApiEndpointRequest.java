package cn.stylefeng.roses.kernel.manage.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * API资源接口列表封装类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiEndpointRequest extends BaseRequest {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {edit.class, delete.class, detail.class})
    @ChineseDescription("主键id")
    private Long apiClientResourceId;

    /**
     * 资源编码，与sys_resource表编码对应
     */
    @NotBlank(message = "资源编码不能为空", groups = {add.class, edit.class})
    @ChineseDescription("资源编码")
    private String resourceCode;

    /**
     * 主键id集合
     */
    @NotNull(message = "主键id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("主键id集合")
    private List<Long> apiClientResourceIdList;

}
