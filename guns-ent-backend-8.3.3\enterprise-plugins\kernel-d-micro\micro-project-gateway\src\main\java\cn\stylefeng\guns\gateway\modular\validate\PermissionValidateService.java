package cn.stylefeng.guns.gateway.modular.validate;

import cn.stylefeng.roses.kernel.auth.api.exception.AuthException;
import cn.stylefeng.roses.kernel.auth.api.exception.enums.AuthExceptionEnum;
import cn.stylefeng.roses.kernel.auth.api.remote.CheckPermissionApi;
import cn.stylefeng.roses.kernel.scanner.api.pojo.resource.ResourceDefinition;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 权限校验
 *
 * <AUTHOR>
 * @since 2023/8/7 22:41
 */
@Service
public class PermissionValidateService {

    @Resource
    private CheckPermissionApi checkPermissionApi;

    /**
     * 校验token是否有对应的
     *
     * <AUTHOR>
     * @since 2023/8/7 22:24
     */
    public void validatePermission(String token, ResourceDefinition resourceDefinition) {
        Boolean validateResult = checkPermissionApi.remoteCheckPermission(token, resourceDefinition.getUrl());
        if (!validateResult) {
            throw new AuthException(AuthExceptionEnum.PERMISSION_RES_VALIDATE_ERROR);
        }
    }

}
