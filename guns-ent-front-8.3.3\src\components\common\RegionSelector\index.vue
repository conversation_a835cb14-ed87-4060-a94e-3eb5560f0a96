<template>
  <div class="region-selector">
    <!-- 输入框显示选中的区域 -->
    <a-input
      v-model:value="displayValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      class="region-selector-input"
      @focus="handleInputFocus"
      @click="handleInputClick"
    >
      <template #suffix>
        <div class="region-selector-suffix">
          <a-button
            v-if="clearable && selectedRegions.length > 0 && !disabled && !readonly"
            type="text"
            size="small"
            class="clear-btn"
            @click.stop="handleClear"
          >
            <template #icon>
              <CloseCircleOutlined />
            </template>
          </a-button>
          <DownOutlined class="dropdown-icon" :class="{ 'dropdown-icon-open': dropdownVisible }" />
        </div>
      </template>
    </a-input>

    <!-- 下拉选择面板 -->
    <div
      v-if="dropdownVisible"
      class="region-selector-dropdown"
      @click.stop
    >
      <div class="region-selector-content">
        <!-- 搜索框 -->
        <div class="search-box" v-if="showSearch">
          <a-input
            v-model:value="searchText"
            :placeholder="searchPlaceholder"
            allowClear
            @pressEnter="handleSearch"
            @change="handleSearchChange"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </div>

        <!-- 树形结构 -->
        <div class="tree-content">
          <a-spin :spinning="loading">
            <div class="tree-wrapper" v-if="treeData && treeData.length > 0">
              <a-tree
                v-model:checkedKeys="checkedKeys"
                v-model:expandedKeys="expandedKeys"
                :tree-data="treeData"
                :checkable="multiple"
                :selectable="true"
                :checkStrictly="checkStrictly"
                :load-data="loadData"
                :fieldNames="fieldNames"
                @check="handleCheck"
                @select="handleSelect"
              >
                <template #title="{ title, key, dataRef }">
                  <span class="tree-node-title">
                    {{ title }}
                  </span>
                </template>
              </a-tree>
            </div>
            <div v-else class="empty-content">
              <a-empty :description="emptyText" />
            </div>
          </a-spin>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons" v-if="showActionButtons">
          <a-space>
            <a-button size="small" @click="handleCancel">取消</a-button>
            <a-button type="primary" size="small" @click="handleConfirm">确定</a-button>
          </a-space>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  DownOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import { RegionApi } from '@/views/erp/region/api/regionApi'

// Props定义
const props = defineProps({
  // v-model绑定的值
  modelValue: {
    type: [Array, String, Number],
    default: () => []
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请选择区域'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  },
  // 是否可清空
  clearable: {
    type: Boolean,
    default: true
  },
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: true
  },
  // 搜索框占位符
  searchPlaceholder: {
    type: String,
    default: '搜索区域名称'
  },
  // 空数据提示文本
  emptyText: {
    type: String,
    default: '暂无区域数据'
  },
  // 是否显示操作按钮
  showActionButtons: {
    type: Boolean,
    default: false
  },
  // 父子节点选中状态不再关联
  checkStrictly: {
    type: Boolean,
    default: true
  },
  // 自定义API方法
  customApi: {
    type: Function,
    default: null
  },
  // 自定义懒加载API方法
  customLazyApi: {
    type: Function,
    default: null
  },
  // 额外的查询参数
  extraParams: {
    type: Object,
    default: () => ({})
  },
  // 字段名映射
  fieldNames: {
    type: Object,
    default: () => ({
      children: 'children',
      title: 'regionName',
      key: 'regionId',
      value: 'regionId'
    })
  }
})

// Emits定义
const emits = defineEmits(['update:modelValue', 'change', 'search', 'clear'])

// 响应式数据
const dropdownVisible = ref(false)
const loading = ref(false)
const searchText = ref('')
const treeData = ref([])
const checkedKeys = ref([])
const expandedKeys = ref([])
const selectedRegions = ref([])

// 防止事件冲突的标志
const isHandlingCheck = ref(false)

// 计算属性
const displayValue = computed(() => {
  if (selectedRegions.value.length === 0) {
    return ''
  }
  if (props.multiple) {
    return selectedRegions.value.map(region => region.regionName).join(', ')
  } else {
    return selectedRegions.value[0]?.regionName || ''
  }
})

// 监听modelValue变化
watch(() => props.modelValue, (newValue, oldValue) => {
  // 立即处理状态清理，无论树数据是否加载完成
  if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
    // 如果新值为空，立即清理状态
    selectedRegions.value = []
    checkedKeys.value = []
  } else if (treeData.value.length > 0) {
    // 如果有值且树数据已加载，初始化选中状态
    initSelectedRegions(newValue)
  }
  // 如果有值但树数据未加载，等待树数据加载完成后处理
}, { immediate: true })

// 监听树数据变化，在数据加载完成后初始化选中状态
watch(treeData, (newTreeData) => {
  if (newTreeData.length > 0 && props.modelValue &&
      (Array.isArray(props.modelValue) ? props.modelValue.length > 0 : props.modelValue)) {
    initSelectedRegions(props.modelValue)
  }
}, { immediate: false })

// 注意：选中状态变化现在在 handleCheck 中直接处理，避免重复调用

// 组件挂载时加载数据
onMounted(() => {
  loadTreeData()
  // 添加点击外部关闭下拉框的事件监听
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听并清理状态
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  // 清理组件状态，防止内存泄漏
  selectedRegions.value = []
  checkedKeys.value = []
  expandedKeys.value = []
  treeData.value = []
})

// 点击外部关闭下拉框
const handleClickOutside = (event) => {
  const regionSelector = event.target.closest('.region-selector')
  if (!regionSelector && dropdownVisible.value) {
    dropdownVisible.value = false
  }
}

// 初始化选中的区域
const initSelectedRegions = (value) => {
  // 首先清理所有状态，确保没有缓存数据
  selectedRegions.value = []
  checkedKeys.value = []

  // 如果没有值或值为空，直接返回（已经清理了状态）
  if (!value || (Array.isArray(value) && value.length === 0)) {
    return
  }

  try {
    if (props.multiple) {
      // 多选模式：value应该是数组
      const regionIds = Array.isArray(value) ? value : [value]
      if (regionIds.length > 0) {
        // 确保ID是字符串类型，与树组件保持一致
        const stringIds = regionIds.map(id => String(id)).filter(id => id && id !== 'undefined' && id !== 'null')
        if (stringIds.length > 0) {
          // 根据ID获取区域详细信息
          const regions = getRegionsByIds(stringIds)
          selectedRegions.value = regions
          checkedKeys.value = stringIds
        }
      }
    } else {
      // 单选模式：value应该是单个ID
      if (value) {
        const stringId = String(value)
        if (stringId && stringId !== 'undefined' && stringId !== 'null') {
          const regions = getRegionsByIds([stringId])
          selectedRegions.value = regions
        }
      }
    }
  } catch (error) {
    console.error('初始化选中区域失败:', error)
    // 出错时确保状态被清理
    selectedRegions.value = []
    checkedKeys.value = []
  }
}

// 根据ID获取区域信息
const getRegionsByIds = (regionIds) => {
  if (!regionIds || regionIds.length === 0) return []

  const regions = []
  const findRegionsInTree = (nodes, ids) => {
    for (const node of nodes) {
      // 确保比较时都是字符串类型
      if (ids.includes(String(node.regionId))) {
        regions.push({
          regionId: node.regionId,
          regionName: node.regionName,
          regionCode: node.regionCode,
          regionLevel: node.regionLevel
        })
      }
      if (node.children && node.children.length > 0) {
        findRegionsInTree(node.children, ids)
      }
    }
  }

  if (treeData.value.length > 0) {
    findRegionsInTree(treeData.value, regionIds)
  }

  return regions
}

// 加载树数据
const loadTreeData = async () => {
  loading.value = true
  try {
    const apiMethod = props.customApi || RegionApi.findTree
    const params = {
      searchText: searchText.value,
      ...props.extraParams
    }
    
    const result = await apiMethod(params)
    treeData.value = processTreeData(result)

    // 设置默认展开的节点
    if (searchText.value) {
      expandedKeys.value = extractAllIds(result)
    } else {
      expandedKeys.value = extractNodeIdsByLevel(result, 3)
    }

    // 数据加载完成后，如果有初始值，需要初始化选中状态
    if (props.modelValue && (Array.isArray(props.modelValue) ? props.modelValue.length > 0 : props.modelValue)) {
      initSelectedRegions(props.modelValue)
    }
  } catch (error) {
    console.error('加载区域数据失败:', error)
    message.error('加载区域数据失败')
  } finally {
    loading.value = false
  }
}

// 处理树数据
const processTreeData = (data) => {
  if (!data || !Array.isArray(data)) return []

  return data.map(item => {
    const processedItem = {
      ...item,
      isLeaf: !item.children || item.children.length === 0,
      // 确保必要的字段存在，并确保 regionId 是字符串类型
      regionId: String(item.regionId || item.id || ''),
      regionName: item.regionName || item.name || item.title || '',
      regionCode: item.regionCode || item.code || '',
      regionLevel: item.regionLevel || item.level || 0,
      // 为 Ant Design Vue 树组件添加必要的字段
      key: String(item.regionId || item.id || ''),
      title: item.regionName || item.name || item.title || '',
      value: String(item.regionId || item.id || '')
    }

    // 递归处理子节点
    if (item.children && item.children.length > 0) {
      processedItem.children = processTreeData(item.children)
      processedItem.isLeaf = false
    }

    return processedItem
  })
}

// 提取所有节点ID
const extractAllIds = (data) => {
  const ids = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      ids.push(node.regionId)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(data)
  return ids
}

// 按层级提取节点ID
const extractNodeIdsByLevel = (data, maxLevel) => {
  const ids = []
  const traverse = (nodes, level = 1) => {
    nodes.forEach(node => {
      if (level <= maxLevel) {
        ids.push(node.regionId)
      }
      if (node.children && node.children.length > 0 && level < maxLevel) {
        traverse(node.children, level + 1)
      }
    })
  }
  traverse(data)
  return ids
}

// 内部更新选中的区域（不触发外部事件）
const updateSelectedRegionsInternal = (keys) => {
  if (!props.multiple) return

  const regions = []
  const findRegionsInTree = (nodes, targetKeys) => {
    for (const node of nodes) {
      // 确保比较时都是字符串类型
      const nodeIdStr = String(node.regionId)
      const isMatch = targetKeys.includes(nodeIdStr)

      if (isMatch) {
        regions.push({
          regionId: node.regionId,
          regionName: node.regionName,
          regionCode: node.regionCode,
          regionLevel: node.regionLevel
        })
      }

      if (node.children && node.children.length > 0) {
        findRegionsInTree(node.children, targetKeys)
      }
    }
  }

  findRegionsInTree(treeData.value, keys)
  selectedRegions.value = regions
}

// 更新选中的区域（触发外部事件）
const updateSelectedRegions = (keys) => {
  updateSelectedRegionsInternal(keys)

  // 触发更新事件
  emits('update:modelValue', keys)
  emits('change', keys, selectedRegions.value)
}

// 事件处理
const handleInputFocus = () => {
  if (!props.disabled && !props.readonly) {
    dropdownVisible.value = true
  }
}

const handleInputClick = () => {
  if (!props.disabled && !props.readonly) {
    // 确保点击时总是显示下拉框，避免切换逻辑导致的延迟
    dropdownVisible.value = true
  }
}

const handleClear = () => {
  selectedRegions.value = []
  checkedKeys.value = []
  emits('update:modelValue', props.multiple ? [] : null)
  emits('clear')
  emits('change', props.multiple ? [] : null, [])
}

const handleSearch = () => {
  loadTreeData()
  emits('search', searchText.value)
}

const handleSearchChange = () => {
  if (!searchText.value) {
    loadTreeData()
  }
}

const handleCheck = (checkedKeysValue, info) => {
  // 设置处理标志，防止与 handleSelect 冲突
  isHandlingCheck.value = true

  if (props.multiple) {
    // 处理 checkedKeysValue 参数
    // 在 Ant Design Vue 中，checkedKeysValue 可能是数组或对象
    let actualKeys = []

    if (Array.isArray(checkedKeysValue)) {
      // 如果是数组，直接使用
      actualKeys = checkedKeysValue
    } else if (checkedKeysValue && typeof checkedKeysValue === 'object') {
      // 如果是对象，可能有 checked 属性
      if (checkedKeysValue.checked && Array.isArray(checkedKeysValue.checked)) {
        actualKeys = checkedKeysValue.checked
      } else if (checkedKeysValue.checkedKeys && Array.isArray(checkedKeysValue.checkedKeys)) {
        actualKeys = checkedKeysValue.checkedKeys
      } else {
        // 尝试直接使用对象的键
        actualKeys = Object.keys(checkedKeysValue)
      }
    } else {
      actualKeys = []
    }

    // 确保是字符串数组
    const stringKeys = actualKeys.map(key => String(key))

    // 更新内部状态
    checkedKeys.value = stringKeys

    // 更新选中的区域信息
    updateSelectedRegionsInternal(stringKeys)

    // 触发外部事件
    emits('update:modelValue', stringKeys)
    emits('change', stringKeys, selectedRegions.value)
  }

  // 清除处理标志
  setTimeout(() => {
    isHandlingCheck.value = false
  }, 0)
}

const handleSelect = (selectedKeys, info) => {
  // 如果正在处理 check 事件，跳过 select 处理
  if (isHandlingCheck.value) {
    return
  }

  if (props.multiple) {
    // 多选模式：根据 info.selected 和 info.node 来处理选择/取消选择
    if (info.node) {
      const clickedKey = String(info.node.key || info.node.regionId)
      const currentChecked = [...checkedKeys.value]

      if (info.selected) {
        // 选中节点：添加到 checkedKeys 中
        if (!currentChecked.includes(clickedKey)) {
          const newChecked = [...currentChecked, clickedKey]
          checkedKeys.value = newChecked
          updateSelectedRegionsInternal(newChecked)
          emits('update:modelValue', newChecked)
          emits('change', newChecked, selectedRegions.value)
        }
      } else {
        // 取消选中节点：从 checkedKeys 中移除
        if (currentChecked.includes(clickedKey)) {
          const newChecked = currentChecked.filter(k => k !== clickedKey)
          checkedKeys.value = newChecked
          updateSelectedRegionsInternal(newChecked)
          emits('update:modelValue', newChecked)
          emits('change', newChecked, selectedRegions.value)
        }
      }
    }
  } else if (!props.multiple && selectedKeys.length > 0) {
    // 单选模式
    const selectedNode = info.selectedNodes[0]
    selectedRegions.value = [selectedNode]
    emits('update:modelValue', selectedKeys[0])
    emits('change', selectedKeys[0], selectedNode)
    dropdownVisible.value = false
  }
}

const handleCancel = () => {
  dropdownVisible.value = false
}

// 重置组件状态（供外部调用）
const resetState = () => {
  selectedRegions.value = []
  checkedKeys.value = []
  expandedKeys.value = []
  dropdownVisible.value = false
  searchText.value = ''
}

// 暴露方法给父组件（合并所有暴露的方法）
defineExpose({
  resetState,
  clearSelection: handleClear,
  getSelectedRegions: () => selectedRegions.value,
  reloadData: loadTreeData
})

const handleConfirm = () => {
  const value = props.multiple ? checkedKeys.value : (selectedRegions.value[0]?.regionId || null)
  emits('update:modelValue', value)
  emits('change', value, selectedRegions.value)
  dropdownVisible.value = false
}

// 懒加载
const loadData = async (treeNode) => {
  const apiMethod = props.customLazyApi || RegionApi.findTreeWithLazy
  const params = {
    parentId: treeNode.dataRef.regionId,
    ...props.extraParams
  }
  
  try {
    const result = await apiMethod(params)
    treeNode.dataRef.children = processTreeData(result)
    treeData.value = [...treeData.value]
  } catch (error) {
    console.error('懒加载区域数据失败:', error)
    message.error('加载子区域失败')
  }
}




</script>

<style scoped lang="less">
.region-selector {
  width: 100%;
  position: relative;

  .region-selector-input {
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(.ant-input-disabled) {
      border-color: #40a9ff;
    }

    &:focus,
    &.ant-input-focused {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &.ant-input-disabled {
      cursor: not-allowed;
      background-color: #f5f5f5;
    }

    &[readonly] {
      cursor: pointer;
      background-color: #fafafa;
    }
  }

  .region-selector-suffix {
    display: flex;
    align-items: center;
    gap: 4px;

    .clear-btn {
      padding: 0;
      width: 16px;
      height: 16px;
      min-width: 16px;
      border: none;
      background: transparent;
      color: #bfbfbf;
      
      &:hover {
        color: #8c8c8c;
      }
    }

    .dropdown-icon {
      color: #bfbfbf;
      transition: transform 0.3s;
      
      &.dropdown-icon-open {
        transform: rotate(180deg);
      }
    }
  }
}

.region-selector-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  min-width: 300px;
  max-width: 500px;
  margin-top: 4px;
  border: 1px solid #d9d9d9;
  animation: slideDown 0.2s ease-out;

  .region-selector-content {
    padding: 8px;

    .search-box {
      margin-bottom: 8px;
    }

    .tree-content {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 8px;

      .tree-wrapper {
        .tree-node-title {
          user-select: none;
        }
      }

      .empty-content {
        padding: 20px;
        text-align: center;
      }
    }

    .action-buttons {
      margin-top: 8px;
      text-align: right;
      border-top: 1px solid #f0f0f0;
      padding-top: 8px;
    }
  }
}

// 树组件样式优化
:deep(.ant-tree) {
  .ant-tree-node-content-wrapper {
    width: calc(100% - 24px);
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .ant-tree-title {
    width: 100%;
    padding: 2px 4px;
  }

  .ant-tree-treenode {
    padding: 2px 0;

    &.ant-tree-treenode-selected {
      .ant-tree-node-content-wrapper {
        background-color: #e6f7ff;
        border-color: #91d5ff;
      }
    }
  }

  .ant-tree-checkbox {
    margin-right: 8px;
  }

  .ant-tree-switcher {
    width: 20px;
    height: 20px;
    line-height: 20px;

    .ant-tree-switcher-icon {
      font-size: 12px;
    }
  }

  .ant-tree-indent-unit {
    width: 16px;
  }
}

// 滚动条样式
.tree-content {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 动画效果
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .region-selector-dropdown {
    min-width: 280px;
    max-width: 100%;

    .region-selector-content {
      .tree-content {
        max-height: 250px;
      }
    }
  }
}
</style>
