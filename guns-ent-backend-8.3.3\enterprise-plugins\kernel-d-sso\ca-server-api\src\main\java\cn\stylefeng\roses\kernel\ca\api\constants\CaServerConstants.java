package cn.stylefeng.roses.kernel.ca.api.constants;

/**
 * 统一认证模块
 *
 * <AUTHOR>
 * @date 2021/1/20 16:41
 */
public interface CaServerConstants {

    /**
     * 单点登录模块
     */
    String SSO_SERVER_MODULE_NAME = "kernel-d-sso-server";

    /**
     * 异常枚举的步进值
     */
    String SSO_SERVER_EXCEPTION_STEP_CODE = "30";

    /**
     * redirect的spring mvc前缀标识
     */
    String REDIRECT_PREFIX = "redirect:";

    /**
     * 统一认证中心保存用户信息的cookie的key名称
     */
    String CA_COOKIE_NAME = "CAID";

    /**
     * 统一认证中心保存用户信息的cookie的过期时间
     * <p>
     * 此时间一般大于CA用户会话时间，但是真正CA统一认证中心会话的过期时间以会话过期时间为主
     */
    Long CA_COOKIE_EXPIRED_SECONDS = 3600L * 24 * 30;

    /**
     * CA会话的过期时间
     */
    Long CA_SESSION_EXPIRED_SECONDS = 3600L * 24;

    /**
     * redirect到客户端应用时携带jwt token的payload中，用户信息字段名称
     */
    String CA_JWT_PAYLOAD_USER_INFO_FIELD_NAME = "userInfo";

    /**
     * redirect到客户端应用时，url上携带的token参数的名称
     */
    String CA_CALLBACK_URL_TOKEN_FIELD_NAME = "token";

    /**
     * redirect到客户端应用时，url上携带的callback参数的名称
     */
    String CA_CALLBACK_FIELD_NAME = "callback";

    /**
     * redirect到客户端应用时，url上携带的 错误码 参数的名称
     */
    String CA_CALLBACK_URL_ERROR_CODE_FIELD_NAME = "errorCode";

    /**
     * 统一的登录界面的url地址
     */
    String CA_UNIFY_LOGIN_URL = "http://localhost:8080/loginPage";

    /**
     * 错误提示界面
     */
    String CA_ERROR_VIEW_URL = "http://localhost:8080/errorPage";

    /**
     * 携带原页面url地址的参数名
     */
    String CA_ERROR_VIEW_REDIRECT_URL_FIELD_NAME = "referUrl";

    /**
     * 缓存CA登录用户信息的缓存前缀，key为token的前缀
     */
    String CA_USER_TOKEN_CACHE_PREFIX = "CA:USER:TOKEN:";

    /**
     * 缓存CA登录用户信息的缓存前缀，key为userId
     */
    String CA_USER_ID_CACHE_PREFIX = "CA:USER:ID:";

    /**
     * 缓存CA登录用户的loginCode的缓存前缀
     */
    String CA_LOGIN_CODE_CACHE_PREFIX = "CA:SSO_LOGIN_CODE:";

    /**
     * 缓存在一个浏览器登陆过所有子业务系统的token，key为CAID，也就是单点成功的唯一id
     */
    String CA_CLIENT_TOKEN_CACHE_PREFIX = "CA:CLIENT:TOKENS:";

}
