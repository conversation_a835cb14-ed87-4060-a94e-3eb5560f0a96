<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.supplier.mapper.ErpSupplierRegionMapper">

    <!-- 根据区域ID查询关联的供应商 -->
    <select id="getSuppliersByRegionId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse">
        SELECT
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            s.supplier_short_name,
            s.supplier_type,
            s.region_id,
            s.contact_person,
            s.contact_phone,
            s.contact_mobile,
            s.contact_email,
            s.contact_address,
            s.business_license_no,
            s.tax_no,
            s.bank_name,
            s.bank_account,
            s.credit_level,
            s.status,
            s.remark,
            s.create_time,
            s.create_user,
            s.update_time,
            s.update_user,
            r.region_name as regionName
        FROM
            erp_supplier s
        LEFT JOIN
            erp_region r ON s.region_id = r.region_id
        WHERE
            s.supplier_id IN (
                SELECT DISTINCT
                    sr.supplier_id
                FROM
                    erp_supplier_region sr
                WHERE
                    <if test="includeChildRegions and childRegionIds != null and childRegionIds.size > 0">
                        sr.region_id IN
                        <foreach collection="childRegionIds" item="regionId" open="(" separator="," close=")">
                            #{regionId}
                        </foreach>
                    </if>
                    <if test="!includeChildRegions">
                        sr.region_id = #{regionId}
                    </if>
            )
            AND s.del_flag = 'N'
        ORDER BY
            s.create_time DESC
    </select>

    <!-- 统计区域关联的供应商数量 -->
    <select id="countSuppliersByRegionId" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT s.supplier_id)
        FROM
            erp_supplier s
        WHERE
            s.supplier_id IN (
                SELECT DISTINCT
                    sr.supplier_id
                FROM
                    erp_supplier_region sr
                WHERE
                    <if test="includeChildRegions and childRegionIds != null and childRegionIds.size > 0">
                        sr.region_id IN
                        <foreach collection="childRegionIds" item="regionId" open="(" separator="," close=")">
                            #{regionId}
                        </foreach>
                    </if>
                    <if test="!includeChildRegions">
                        sr.region_id = #{regionId}
                    </if>
            )
            AND s.del_flag = 'N'
    </select>

</mapper>