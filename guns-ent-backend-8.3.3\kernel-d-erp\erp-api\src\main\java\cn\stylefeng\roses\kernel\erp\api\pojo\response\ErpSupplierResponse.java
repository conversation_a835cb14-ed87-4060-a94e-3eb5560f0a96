package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 供应商响应参数
 *
 * <AUTHOR>
 * @since 2025/07/22 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpSupplierResponse extends BaseResponse {

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 供应商简称
     */
    @ChineseDescription("供应商简称")
    private String supplierShortName;

    /**
     * 供应商类型（ENTERPRISE-企业，INDIVIDUAL-个体）
     */
    @ChineseDescription("供应商类型")
    private String supplierType;

    /**
     * 供应商类型名称
     */
    @ChineseDescription("供应商类型名称")
    private String supplierTypeName;

    /**
     * 所属区域ID
     */
    @ChineseDescription("所属区域ID")
    private Long regionId;

    /**
     * 所属区域名称
     */
    @ChineseDescription("所属区域名称")
    private String regionName;

    /**
     * 联系人
     */
    @ChineseDescription("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @ChineseDescription("联系电话")
    private String contactPhone;

    /**
     * 手机号码
     */
    @ChineseDescription("手机号码")
    private String contactMobile;

    /**
     * 邮箱地址
     */
    @ChineseDescription("邮箱地址")
    private String contactEmail;

    /**
     * 联系地址
     */
    @ChineseDescription("联系地址")
    private String contactAddress;

    /**
     * 营业执照号
     */
    @ChineseDescription("营业执照号")
    private String businessLicenseNo;

    /**
     * 税务登记号
     */
    @ChineseDescription("税务登记号")
    private String taxNo;

    /**
     * 开户银行
     */
    @ChineseDescription("开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @ChineseDescription("银行账号")
    private String bankAccount;

    /**
     * 信用等级（A-优秀，B-良好，C-一般，D-较差）
     */
    @ChineseDescription("信用等级")
    private String creditLevel;

    /**
     * 信用等级名称
     */
    @ChineseDescription("信用等级名称")
    private String creditLevelName;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，BLACKLIST-黑名单）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 状态名称
     */
    @ChineseDescription("状态名称")
    private String statusName;

    /**
     * 经营方式：PURCHASE_SALE(购销)、JOINT_VENTURE(联营)、CONSIGNMENT(代销)
     */
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 经营方式名称
     */
    @ChineseDescription("经营方式名称")
    private String businessModeName;

    /**
     * 销售扣点（联营和代销使用）
     */
    @ChineseDescription("销售扣点")
    private java.math.BigDecimal salesDeduction;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 供应商关联的区域ID列表
     */
    @ChineseDescription("供应商关联的区域ID列表")
    private List<Long> regionIds;

    /**
     * 供应商关联的区域信息列表
     */
    @ChineseDescription("供应商关联的区域信息列表")
    private List<ErpRegionResponse> regionList;
}