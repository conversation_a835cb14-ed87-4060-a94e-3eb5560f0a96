package cn.stylefeng.roses.ent.mobile.manage.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 邀请成员加入的属性配置
 *
 * <AUTHOR>
 * @since 2024-04-08 16:33
 */
@Component
@ConfigurationProperties(prefix = "invite.join")
@Data
public class InviteJoinProperties {

    /**
     * qrcode访问的地址
     */
    private String frontUrl;

}
