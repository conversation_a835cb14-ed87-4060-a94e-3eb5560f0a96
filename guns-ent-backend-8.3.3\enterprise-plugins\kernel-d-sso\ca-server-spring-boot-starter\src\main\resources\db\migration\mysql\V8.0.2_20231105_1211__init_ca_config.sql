CREATE TABLE `ent_sso_client`  (
  `client_id` bigint NOT NULL COMMENT '业务应用的id',
  `client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `client_logo_file_id` bigint NULL DEFAULT NULL COMMENT '应用图标的文件id',
  `login_page_type` tinyint NOT NULL DEFAULT 2 COMMENT '登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面',
  `unified_logout_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Y' COMMENT '是否统一退出：Y-是，N-否',
  `sso_callback_url` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回调业务地址，单点登录到业务端时，跳转到业务端的地址',
  `sso_logout_url` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退出地址，从认证中心退出后，通知业务端的地址',
  `custom_login_url` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用登录的地址，针对自定义登录界面',
  `ca_token_secret` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加密和解密的密钥，针对单点到业务系统的token（对称加密）',
  `client_status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `client_sort` decimal(11, 2) NULL DEFAULT 999.00 COMMENT '排序码',
  `client_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用的描述',
  `version_flag` bigint NULL DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '更新人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  PRIMARY KEY (`client_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '单点登录客户端' ROW_FORMAT = Dynamic;

INSERT INTO `sys_file_info`(`file_id`, `file_code`, `file_version`, `file_status`, `file_location`, `file_bucket`, `file_origin_name`, `file_suffix`, `file_size_kb`, `file_size_info`, `file_object_name`, `file_path`, `secret_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721101659015184385, 1721101659015184386, 1, '1', 5, 'defaultBucket', '单点认证.png', 'png', 5, '5.44 KB', '1721101659015184385.png', NULL, 'N', 'N', '2023-11-05 17:46:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_file_storage`(`file_id`, `file_bytes`) VALUES (1721101659015184385, 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

INSERT INTO `sys_app`(`app_id`, `app_name`, `app_code`, `app_icon`, `status_flag`, `app_sort`, `remark`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721100056577146881, '单点接入', 'sso_auth', 1721101659015184385, 1, 1000.00, '接入统一认证中心的外部应用单点配置', NULL, 2, 'N', '2023-11-05 17:40:05', 1339550467939639299, '2023-11-05 17:41:42', 1339550467939639299);
INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721100330028990466, -1, '[-1],', '应用接入', 'yingyongjieru', 1721100056577146881, 100.00, 1, NULL, 10, '/ssoClient/manage', '/sso-client/sso-client', 'icon-menu-zhuti', NULL, NULL, 'Y', NULL, 4, 'N', '2023-11-05 17:41:10', 1339550467939639299, '2023-11-05 17:49:44', 1339550467939639299);

INSERT INTO `sys_dict`(`dict_id`, `dict_type_id`, `dict_code`, `dict_name`, `dict_name_pinyin`, `dict_encode`, `dict_short_name`, `dict_short_code`, `dict_parent_id`, `dict_pids`, `status_flag`, `dict_sort`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721467248725463042, 1353547215422132226, 'CA_SERVER_CONFIG', '单点服务端配置', 'ddfwdpz', NULL, NULL, NULL, -1, '[-1],', 1, 100.00, 0, 'N', '2023-11-06 17:59:10', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721467478413938690, '单点所用的统一认证会话的Cookie名', 'CA_COOKIE_NAME', 'CAID', 'Y', '单点所用的统一认证会话的Cookie名', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:00:05', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721467660824219649, 'Cookie过期时间', 'CA_COOKIE_EXPIRED_SECONDS', '259200', 'Y', '统一认证中心保存用户信息的cookie的过期时间，默认30天', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:00:48', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721467822346866690, '单点Cookie的域名', 'CA_COOKIE_DOMAIN', 'localhost', 'Y', '获取cookie的domain', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:01:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721468044485595137, 'CA会话的过期时间', 'CA_SESSION_EXPIRED_SECONDS', '86400', 'Y', 'CA会话的过期时间，用在Ca的会话内存缓存的过期时间', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:02:20', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721468275109400577, 'Token参数名称', 'CA_CALLBACK_URL_TOKEN_FIELD_NAME', 'token', 'Y', 'redirect到客户端应用时，url上携带的token参数的名称，这个token一般是对用户信息的json串进行加密的', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:03:15', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721468380990410754, '错误码参数名', 'CA_CALLBACK_URL_ERROR_CODE_FIELD_NAME', 'errorCode', 'Y', 'redirect到客户端应用时，url上携带的错误码参数的名称', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:03:40', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721468478008856578, '统一的登录界面的地址', 'CA_UNIFY_LOGIN_URL', 'http://localhost:9000/login', 'Y', '统一的登录界面的url地址', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:04:03', 1339550467939639299, '2023-11-06 18:05:24', 1339550467939639299);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721468577736822786, '统一错误界面地址', 'CA_ERROR_VIEW_URL', 'http://localhost:9000/errorPage', 'Y', 'redirect到错误提示界面的url', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:04:27', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1721468712923435009, '跳转url的参数名', 'CA_ERROR_VIEW_REDIRECT_URL_FIELD_NAME', 'referUrl', 'Y', 'redirect到错误提示界面携带跳转url的参数名', 1, 'CA_SERVER_CONFIG', 'N', '2023-11-06 18:04:59', 1339550467939639299, NULL, NULL);
