import{c8 as a,c9 as i,R as n}from"./index-18a1ea24.js";class l{static redirectDetection(){window.location.href="".concat(a,"/sso/detection?clientId=").concat(i,"&ssoCallback=").concat(encodeURIComponent(window.location.href))}static activateByLoginCode(t,e,o){window.location.href="".concat(a,"/sso/activateByLoginCode?clientId=").concat(t,"&ssoCallback=").concat(e,"&ssoLoginCode=").concat(o)}static tokenExchange(t){return n.postAndLoadData("/loginByCaToken",{token:t})}static getUrlParam(t){let e="(^|&)".concat(t,"=([^&]*)(&|$)"),o=window.location.search.substr(1).match(e);return o!=null?unescape(o[2]):null}static ssoLogoutRedirect(){window.location.href="".concat(a,"/sso/logout?clientId=").concat(i)}}export{l as S};
