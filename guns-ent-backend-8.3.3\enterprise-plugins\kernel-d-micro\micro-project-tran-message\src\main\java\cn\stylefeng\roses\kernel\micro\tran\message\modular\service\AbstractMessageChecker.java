package cn.stylefeng.roses.kernel.micro.tran.message.modular.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.micro.api.TranMessageServiceApi;
import cn.stylefeng.roses.kernel.micro.api.pojo.TranMessage;
import cn.stylefeng.roses.kernel.micro.api.pojo.request.TranMessageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抽象消息校验重发服务
 *
 * <AUTHOR>
 * @date 2018-05-08 23:04
 */
@Slf4j
public abstract class AbstractMessageChecker {

    @Resource
    private TranMessageServiceApi messageServiceApi;

    public void checkMessages() {
        try {
            //存放查询结果
            Map<String, TranMessage> messageMap = new HashMap<>();

            int pageSize = 2000;            //每页条数
            int maxHandlePageCount = 3;     //一次最多处理页数

            int currentPage = 1;            //当前处理页

            TranMessageRequest pageQuery = new TranMessageRequest();
            pageQuery.setPageSize(pageSize);
            pageQuery.setPageNo(currentPage);
            pageQuery.setOrderBy("create_time");
            pageQuery.setSortBy("asc");

            PageResult<TranMessage> pageResult = getPageResult(pageQuery);

            List<TranMessage> rows = pageResult.getRows();
            for (TranMessage item : rows) {
                messageMap.put(item.getMessageId(), item);
            }

            long totalPage = pageResult.getTotalPage();
            if (totalPage > maxHandlePageCount) {
                totalPage = maxHandlePageCount;
            }

            for (currentPage = 2; currentPage <= totalPage; currentPage++) {

                pageQuery = new TranMessageRequest();
                pageQuery.setPageSize(pageSize);
                pageQuery.setPageNo(currentPage);
                pageQuery.setOrderBy("create_time");
                pageQuery.setSortBy("asc");

                pageResult = getPageResult(pageQuery);
                if (pageResult != null && pageResult.getRows() != null) {
                    List<TranMessage> otherResults = pageResult.getRows();
                    for (TranMessage rowItem : otherResults) {
                        messageMap.put(rowItem.getMessageId(), rowItem);
                    }
                } else {
                    break;
                }
            }

            //开始处理
            processMessage(messageMap);

        } catch (Exception e) {
            log.error("处理待发送状态的消息异常！", e);
        }
    }

    protected abstract void processMessage(Map<String, TranMessage> messages);

    protected abstract PageResult<TranMessage> getPageResult(TranMessageRequest tranMessageRequest);

}
