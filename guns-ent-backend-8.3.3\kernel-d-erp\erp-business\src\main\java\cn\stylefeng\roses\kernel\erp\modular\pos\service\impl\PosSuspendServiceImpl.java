package cn.stylefeng.roses.kernel.erp.modular.pos.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosSuspendedOrder;
import cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosSuspendedOrderMapper;
import cn.stylefeng.roses.kernel.erp.api.exception.ErpException;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.erp.modular.pos.service.PosSuspendService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * POS挂单管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/08/01 11:30
 */
@Service
public class PosSuspendServiceImpl extends ServiceImpl<PosSuspendedOrderMapper, PosSuspendedOrder> implements PosSuspendService {

    @Resource
    private PosSuspendedOrderMapper posSuspendedOrderMapper;

    // 挂单状态常量
    private static final String SUSPEND_STATUS_ACTIVE = "ACTIVE";
    private static final String SUSPEND_STATUS_RESUMED = "RESUMED";
    private static final String SUSPEND_STATUS_EXPIRED = "EXPIRED";

    // 默认过期时间（小时）
    private static final Integer DEFAULT_EXPIRE_HOURS = 24;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long suspendOrder(Long cashierId, SuspendOrderData orderData, Integer expireHours) {
        // 参数校验
        if (ObjectUtil.isNull(cashierId)) {
            throw new ErpException(ErpPosExceptionEnum.OPERATION_FAILED);
        }
        if (ObjectUtil.isNull(orderData) || ObjectUtil.isEmpty(orderData.getOrderItems())) {
            throw new ErpException(ErpPosExceptionEnum.CART_IS_EMPTY);
        }

        // 设置默认过期时间
        if (ObjectUtil.isNull(expireHours) || expireHours <= 0) {
            expireHours = DEFAULT_EXPIRE_HOURS;
        }

        // 创建挂单记录
        PosSuspendedOrder suspendedOrder = new PosSuspendedOrder();
        suspendedOrder.setSuspendNo(generateSuspendNo());
        suspendedOrder.setCashierId(cashierId);
        suspendedOrder.setOrderData(JSONUtil.toJsonStr(orderData));
        suspendedOrder.setSuspendTime(LocalDateTime.now());
        suspendedOrder.setExpireTime(LocalDateTime.now().plusHours(expireHours));
        suspendedOrder.setStatus(SUSPEND_STATUS_ACTIVE);

        this.save(suspendedOrder);

        return suspendedOrder.getSuspendId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SuspendOrderData resumeOrder(Long suspendId) {
        if (ObjectUtil.isNull(suspendId)) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_NOT_EXIST);
        }

        PosSuspendedOrder suspendedOrder = this.getById(suspendId);
        if (ObjectUtil.isNull(suspendedOrder)) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_NOT_EXIST);
        }

        // 校验挂单是否可以恢复
        if (!validateCanResume(suspendId)) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_ALREADY_RESTORED);
        }

        // 解析订单数据
        SuspendOrderData orderData;
        try {
            orderData = JSONUtil.toBean(suspendedOrder.getOrderData(), SuspendOrderData.class);
        } catch (Exception e) {
            throw new ErpException(ErpPosExceptionEnum.OPERATION_FAILED);
        }

        // 更新挂单状态为已恢复
        updateSuspendedOrderStatus(suspendId, SUSPEND_STATUS_RESUMED);

        return orderData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSuspendedOrder(Long suspendId) {
        if (ObjectUtil.isNull(suspendId)) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_NOT_EXIST);
        }

        PosSuspendedOrder suspendedOrder = this.getById(suspendId);
        if (ObjectUtil.isNull(suspendedOrder)) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_NOT_EXIST);
        }

        // 只能删除有效状态或已过期的挂单
        if (!SUSPEND_STATUS_ACTIVE.equals(suspendedOrder.getStatus()) &&
            !SUSPEND_STATUS_EXPIRED.equals(suspendedOrder.getStatus())) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_ALREADY_RESTORED);
        }

        this.removeById(suspendId);
    }

    @Override
    public List<PosSuspendedOrder> getSuspendedOrdersByCashier(Long cashierId) {
        if (ObjectUtil.isNull(cashierId)) {
            return null;
        }

        LambdaQueryWrapper<PosSuspendedOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosSuspendedOrder::getCashierId, cashierId)
               .eq(PosSuspendedOrder::getStatus, SUSPEND_STATUS_ACTIVE)
               .orderByDesc(PosSuspendedOrder::getSuspendTime);

        return this.list(wrapper);
    }

    @Override
    public List<PosSuspendedOrder> getActiveSuspendedOrders() {
        LambdaQueryWrapper<PosSuspendedOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosSuspendedOrder::getStatus, SUSPEND_STATUS_ACTIVE)
               .orderByDesc(PosSuspendedOrder::getSuspendTime);

        return this.list(wrapper);
    }

    @Override
    public PageResult<PosSuspendedOrder> findSuspendedOrderPage(Integer pageNo, Integer pageSize,
                                                               Long cashierId, String status) {
        LambdaQueryWrapper<PosSuspendedOrder> wrapper = new LambdaQueryWrapper<>();
        
        if (ObjectUtil.isNotNull(cashierId)) {
            wrapper.eq(PosSuspendedOrder::getCashierId, cashierId);
        }
        if (StrUtil.isNotBlank(status)) {
            wrapper.eq(PosSuspendedOrder::getStatus, status);
        }
        
        wrapper.orderByDesc(PosSuspendedOrder::getSuspendTime);

        Page<PosSuspendedOrder> page = new Page<>(pageNo, pageSize);
        Page<PosSuspendedOrder> resultPage = this.page(page, wrapper);
        
        return PageResultFactory.createPageResult(resultPage);
    }

    @Override
    public PosSuspendedOrder getSuspendedOrderById(Long suspendId) {
        if (ObjectUtil.isNull(suspendId)) {
            return null;
        }
        return this.getById(suspendId);
    }

    @Override
    public PosSuspendedOrder getSuspendedOrderByNo(String suspendNo) {
        if (StrUtil.isBlank(suspendNo)) {
            return null;
        }

        LambdaQueryWrapper<PosSuspendedOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosSuspendedOrder::getSuspendNo, suspendNo);

        return this.getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredSuspendedOrders() {
        // 查询已过期但状态仍为有效的挂单
        LambdaQueryWrapper<PosSuspendedOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PosSuspendedOrder::getStatus, SUSPEND_STATUS_ACTIVE)
                   .lt(PosSuspendedOrder::getExpireTime, LocalDateTime.now());

        List<PosSuspendedOrder> expiredOrders = this.list(queryWrapper);
        
        if (ObjectUtil.isEmpty(expiredOrders)) {
            return 0;
        }

        // 批量更新状态为已过期
        LambdaUpdateWrapper<PosSuspendedOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PosSuspendedOrder::getStatus, SUSPEND_STATUS_ACTIVE)
                    .lt(PosSuspendedOrder::getExpireTime, LocalDateTime.now())
                    .set(PosSuspendedOrder::getStatus, SUSPEND_STATUS_EXPIRED);

        boolean updated = this.update(updateWrapper);
        
        return updated ? expiredOrders.size() : 0;
    }

    @Override
    public String generateSuspendNo() {
        // 生成格式：SUS + yyyyMMddHHmmss + 3位随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 900) + 100; // 100-999的随机数
        return "SUS" + timestamp + random;
    }

    @Override
    public boolean validateCanResume(Long suspendId) {
        if (ObjectUtil.isNull(suspendId)) {
            return false;
        }

        PosSuspendedOrder suspendedOrder = this.getById(suspendId);
        if (ObjectUtil.isNull(suspendedOrder)) {
            return false;
        }

        // 只有有效状态的挂单可以恢复
        if (!SUSPEND_STATUS_ACTIVE.equals(suspendedOrder.getStatus())) {
            return false;
        }

        // 检查是否已过期
        if (LocalDateTime.now().isAfter(suspendedOrder.getExpireTime())) {
            // 自动更新过期状态
            updateSuspendedOrderStatus(suspendId, SUSPEND_STATUS_EXPIRED);
            return false;
        }

        return true;
    }

    @Override
    public boolean isExpired(Long suspendId) {
        if (ObjectUtil.isNull(suspendId)) {
            return true;
        }

        PosSuspendedOrder suspendedOrder = this.getById(suspendId);
        if (ObjectUtil.isNull(suspendedOrder)) {
            return true;
        }

        return LocalDateTime.now().isAfter(suspendedOrder.getExpireTime());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSuspendedOrderStatus(Long suspendId, String status) {
        if (ObjectUtil.isNull(suspendId) || StrUtil.isBlank(status)) {
            throw new ErpException(ErpPosExceptionEnum.SUSPEND_ORDER_NOT_EXIST);
        }

        LambdaUpdateWrapper<PosSuspendedOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosSuspendedOrder::getSuspendId, suspendId)
               .set(PosSuspendedOrder::getStatus, status);

        this.update(wrapper);
    }

    @Override
    public SuspendOrderStats getSuspendOrderStatsByCashier(Long cashierId) {
        if (ObjectUtil.isNull(cashierId)) {
            return new SuspendOrderStats(0, 0, 0, 0);
        }

        // 统计各状态的挂单数量
        LambdaQueryWrapper<PosSuspendedOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosSuspendedOrder::getCashierId, cashierId);

        List<PosSuspendedOrder> allOrders = this.list(wrapper);
        
        int activeCount = 0;
        int resumedCount = 0;
        int expiredCount = 0;
        
        for (PosSuspendedOrder order : allOrders) {
            switch (order.getStatus()) {
                case SUSPEND_STATUS_ACTIVE:
                    // 检查是否实际已过期
                    if (LocalDateTime.now().isAfter(order.getExpireTime())) {
                        expiredCount++;
                        // 异步更新状态
                        updateSuspendedOrderStatus(order.getSuspendId(), SUSPEND_STATUS_EXPIRED);
                    } else {
                        activeCount++;
                    }
                    break;
                case SUSPEND_STATUS_RESUMED:
                    resumedCount++;
                    break;
                case SUSPEND_STATUS_EXPIRED:
                    expiredCount++;
                    break;
            }
        }

        return new SuspendOrderStats(activeCount, resumedCount, expiredCount, allOrders.size());
    }

}