package cn.stylefeng.roses.kernel.oauth2.modular.factory;

import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.oauth2.api.enums.OAuth2ConfigEnum;
import cn.stylefeng.roses.kernel.oauth2.api.pojo.OAuth2Config;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.request.*;

/**
 * OAuth2 请求的构建器
 *
 * <AUTHOR>
 * @date 2022/7/3 10:27
 */
public class OAuthRequestFactory {

    /**
     * 服务器基础地址
     */
    private static final String BASE_PREFIX = "/oauth2/callback";

    /**
     * 根据具体的授权来源，获取授权请求工具类
     *
     * <AUTHOR>
     * @date 2022/7/3 10:27
     */
    public static AuthRequest getAuthRequest(String source) {
        OAuth2Config oAuth2Config = SpringUtil.getBean(OAuth2Config.class);

        AuthRequest authRequest = null;
        switch (source) {
            case "qq":
                authRequest = new AuthQqRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.QQ.getClientId())
                        .clientSecret(OAuth2ConfigEnum.QQ.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/qq")
                        .build());
                break;
            case "gitee":
                authRequest = new AuthGiteeRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.GITEE.getClientId())
                        .clientSecret(OAuth2ConfigEnum.GITEE.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/gitee")
                        .build());
                break;
            case "dingtalk":
                authRequest = new AuthDingTalkRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.DING.getClientId())
                        .clientSecret(OAuth2ConfigEnum.DING.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/dingtalk")
                        .build());
                break;
            case "baidu":
                authRequest = new AuthBaiduRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.BAIDU.getClientId())
                        .clientSecret(OAuth2ConfigEnum.BAIDU.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/baidu")
                        .build());
                break;
            case "weibo":
                authRequest = new AuthWeiboRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.WEIBO.getClientId())
                        .clientSecret(OAuth2ConfigEnum.WEIBO.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/weibo")
                        .build());
                break;
            case "coding":
                authRequest = new AuthCodingRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.CODING.getClientId())
                        .clientSecret(OAuth2ConfigEnum.CODING.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/coding")
                        .build());
                break;
            case "oschina":
                authRequest = new AuthOschinaRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.OSC.getClientId())
                        .clientSecret(OAuth2ConfigEnum.OSC.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/oschina")
                        .build());
                break;
            case "alipay":
                authRequest = new AuthAlipayRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.ALIPAY.getClientId())
                        .clientSecret(OAuth2ConfigEnum.ALIPAY.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/alipay")
                        .build(), "alipayPublicKey");
                break;
            case "wechat":
                authRequest = new AuthWeChatOpenRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.WECHAT.getClientId())
                        .clientSecret(OAuth2ConfigEnum.WECHAT.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/wechat")
                        .build());
                break;
            case "wechat-mp":
                authRequest = new AuthWeChatMpRequest(AuthConfig.builder()
                        .clientId(OAuth2ConfigEnum.WECHAT_MP.getClientId())
                        .clientSecret(OAuth2ConfigEnum.WECHAT_MP.getSecret())
                        .redirectUri(oAuth2Config.getBackendCallbackHost() + BASE_PREFIX + "/wechat-mp")
                        .build());
                break;
        }

        if (null == authRequest) {
            throw new AuthException("未获取到有效的Auth配置");
        }

        return authRequest;
    }

}
