package cn.stylefeng.roses.kernel.ca.api.pojo.external.request;

import cn.stylefeng.roses.kernel.ca.api.pojo.external.BaseSsoExternalApiRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 刷新SSO会话内容
 *
 * <AUTHOR>
 * @date 2021/3/8 10:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SsoExternalRefreshSessionRequest extends BaseSsoExternalApiRequest {

    /**
     * caToken单点登录的token
     */
    @NotBlank(message = "caToken不能为空")
    private String caToken;

    /**
     * 公司id
     */
    @NotBlank(message = "公司id不能为空")
    private Long companyId;

    /**
     * 部门id
     */
    @NotBlank(message = "部门id不能为空")
    private Long deptId;

}
