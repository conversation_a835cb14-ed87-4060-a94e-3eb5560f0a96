<template>
  <a-modal
    :visible="visible"
    title="入库单详情"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="inbound-detail-content">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="入库单号">
            <span class="order-no">{{ data.orderNo }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusTagColor(data.status)">
              {{ data.statusName || getStatusName(data.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="订单日期">
            {{ data.orderDate }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商名称">
            {{ data.supplierName }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商编码">
            {{ data.supplierCode }}
          </a-descriptions-item>
          <a-descriptions-item label="经营方式">
            <a-tag :color="getBusinessModeColor(data.businessMode)">
              {{ data.businessModeName }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ data.createUserName }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ data.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ data.updateTime }}
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="3">
            {{ data.remark || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 付款信息 -->
      <a-card v-if="showPaymentInfo" title="付款信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="付款方式">
            <a-tag v-if="data.paymentMethod" :color="getPaymentMethodColor(data.paymentMethod)">
              {{ data.paymentMethodName || getPaymentMethodName(data.paymentMethod) }}
            </a-tag>
            <span v-else class="text-muted">未设置</span>
          </a-descriptions-item>
          <a-descriptions-item label="付款账户">
            <span v-if="data.paymentAccount" class="payment-account">{{ data.paymentAccount }}</span>
            <span v-else class="text-muted">未设置</span>
          </a-descriptions-item>
          <a-descriptions-item label="付款状态">
            <a-tag :color="getPaymentStatusColor(data.status)">
              {{ getPaymentStatusName(data.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="应付金额">
            <span class="amount-text">¥{{ formatAmount(data.totalAmount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="实付金额" v-if="data.status === 'COMPLETED'">
            <span class="amount-text">¥{{ formatAmount(data.actualPaymentAmount || data.totalAmount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="付款时间" v-if="data.status === 'COMPLETED' && data.paymentTime">
            {{ data.paymentTime }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 商品明细 -->
      <a-card title="商品明细" size="small" style="margin-bottom: 16px">
        <a-table
          :columns="detailColumns"
          :data-source="data.detailList || []"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ x: 1000 }"
        >
          <template #bodyCell="{ column, record }">
            <!-- 商品信息 -->
            <template v-if="column.key === 'productInfo'">
              <div class="product-info">
                <div class="product-name">{{ record.productName }}</div>
                <div class="product-details">
                  <span class="product-code">{{ record.productCode }}</span>
                  <span v-if="record.specification" class="product-spec">{{ record.specification }}</span>
                </div>
              </div>
            </template>

            <!-- 数量 -->
            <template v-if="column.key === 'quantity'">
              {{ record.quantity }} {{ getQuantityUnit(record) }}
            </template>

            <!-- 单价 -->
            <template v-if="column.key === 'unitPrice'">
              ¥{{ formatAmount(record.unitPrice) }}
            </template>

            <!-- 总价 -->
            <template v-if="column.key === 'totalPrice'">
              <span class="total-price">¥{{ formatAmount(record.totalPrice) }}</span>
            </template>

            <!-- 备注 -->
            <template v-if="column.key === 'remark'">
              {{ record.remark || '-' }}
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 汇总信息 -->
      <a-card title="汇总信息" size="small">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="商品种类" :value="productCount" suffix="种" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总数量" :value="totalQuantity" suffix="件" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总金额" :value="totalAmount" prefix="¥" :precision="2" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均单价" :value="averagePrice" prefix="¥" :precision="2" />
          </a-col>
        </a-row>
      </a-card>

      <!-- 操作历史 -->
      <a-card v-if="data.operationLogs && data.operationLogs.length > 0" title="操作历史" size="small" style="margin-top: 16px">
        <a-timeline>
          <a-timeline-item
            v-for="log in data.operationLogs"
            :key="log.id"
            :color="getOperationColor(log.operation)"
          >
            <template #dot>
              <icon-font :iconClass="getOperationIcon(log.operation)" />
            </template>
            <div class="operation-log">
              <div class="operation-title">
                <span class="operation-name">{{ log.operationName }}</span>
                <span class="operation-time">{{ log.operationTime }}</span>
              </div>
              <div class="operation-user">操作人：{{ log.operatorName }}</div>
              <div v-if="log.remark" class="operation-remark">备注：{{ log.remark }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { computed } from 'vue';
import { PurchaseApi } from '../../api/PurchaseApi';

export default {
  name: 'InboundDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    // 明细表格列定义
    const detailColumns = [
      {
        title: '商品信息',
        key: 'productInfo',
        width: 250
      },
      {
        title: '数量',
        key: 'quantity',
        width: 120,
        align: 'center'
      },
      {
        title: '单价',
        key: 'unitPrice',
        width: 100,
        align: 'right'
      },
      {
        title: '总价',
        key: 'totalPrice',
        width: 120,
        align: 'right'
      },
      {
        title: '备注',
        key: 'remark',
        width: 200
      }
    ];

    // 计算属性
    const productCount = computed(() => {
      return props.data.detailList ? props.data.detailList.length : 0;
    });

    const totalQuantity = computed(() => {
      if (!props.data.detailList || props.data.detailList.length === 0) return 0;
      return props.data.detailList.reduce((total, item) => {
        return total + (parseFloat(item.quantity) || 0);
      }, 0);
    });

    const totalAmount = computed(() => {
      if (!props.data.detailList || props.data.detailList.length === 0) return 0;
      return props.data.detailList.reduce((total, item) => {
        return total + (parseFloat(item.totalPrice) || 0);
      }, 0);
    });

    const averagePrice = computed(() => {
      if (totalQuantity.value === 0) return 0;
      return totalAmount.value / totalQuantity.value;
    });

    // 获取状态名称
    const getStatusName = (status) => {
      return PurchaseApi.getPurchaseStatusName(status);
    };

    // 获取状态标签颜色
    const getStatusTagColor = (status) => {
      return PurchaseApi.getStatusTagColor(status);
    };

    // 获取经营方式颜色
    const getBusinessModeColor = (businessMode) => {
      const colorMap = {
        'PURCHASE_SALE': 'blue',
        'JOINT_VENTURE': 'orange',
        'CONSIGNMENT': 'green'
      };
      return colorMap[businessMode] || 'default';
    };

    // 是否显示付款信息
    const showPaymentInfo = computed(() => {
      return props.data.paymentMethod || props.data.paymentAccount || props.data.status === 'COMPLETED';
    });

    // 获取付款方式名称
    const getPaymentMethodName = (paymentMethod) => {
      const methodMap = {
        'CASH': '现金',
        'BANK_TRANSFER': '银行转账',
        'CHECK': '支票',
        'CREDIT_CARD': '信用卡',
        'MONTHLY': '月结',
        'ALIPAY': '支付宝',
        'WECHAT': '微信支付',
        'CREDIT': '赊账'
      };
      return methodMap[paymentMethod] || paymentMethod;
    };

    // 获取付款方式颜色
    const getPaymentMethodColor = (paymentMethod) => {
      const colorMap = {
        'CASH': 'green',
        'BANK_TRANSFER': 'blue',
        'CHECK': 'orange',
        'CREDIT_CARD': 'purple',
        'MONTHLY': 'cyan',
        'ALIPAY': 'blue',
        'WECHAT': 'green',
        'CREDIT': 'red'
      };
      return colorMap[paymentMethod] || 'default';
    };

    // 获取付款状态名称
    const getPaymentStatusName = (status) => {
      const statusMap = {
        'DRAFT': '待付款',
        'CONFIRMED': '待付款',
        'COMPLETED': '已付款'
      };
      return statusMap[status] || '未知';
    };

    // 获取付款状态颜色
    const getPaymentStatusColor = (status) => {
      const colorMap = {
        'DRAFT': 'orange',
        'CONFIRMED': 'orange',
        'COMPLETED': 'green'
      };
      return colorMap[status] || 'default';
    };

    // 获取数量单位
    const getQuantityUnit = (record) => {
      switch (record.pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return record.unit || '个';
      }
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 获取操作颜色
    const getOperationColor = (operation) => {
      const colorMap = {
        'CREATE': 'blue',
        'EDIT': 'orange',
        'CONFIRM': 'green',
        'RECEIVE': 'purple',
        'CANCEL': 'red'
      };
      return colorMap[operation] || 'default';
    };

    // 获取操作图标
    const getOperationIcon = (operation) => {
      const iconMap = {
        'CREATE': 'icon-opt-xinzeng',
        'EDIT': 'icon-opt-bianji',
        'CONFIRM': 'icon-opt-queren',
        'RECEIVE': 'icon-opt-rukudan',
        'CANCEL': 'icon-opt-quxiao'
      };
      return iconMap[operation] || 'icon-opt-caozuo';
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    return {
      detailColumns,
      productCount,
      totalQuantity,
      totalAmount,
      averagePrice,
      showPaymentInfo,
      getStatusName,
      getStatusTagColor,
      getBusinessModeColor,
      getPaymentMethodName,
      getPaymentMethodColor,
      getPaymentStatusName,
      getPaymentStatusColor,
      getQuantityUnit,
      formatAmount,
      getOperationColor,
      getOperationIcon,
      handleCancel
    };
  }
};
</script>

<style scoped>
.inbound-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.order-no {
  font-weight: 500;
  color: #1890ff;
}

.product-info {
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-details {
  font-size: 12px;
  color: #8c8c8c;
}

.product-code {
  margin-right: 8px;
}

.product-spec {
  margin-left: 8px;
}

.total-price {
  font-weight: 500;
  color: #1890ff;
}

.payment-account {
  font-family: 'Courier New', monospace;
  color: #262626;
  font-weight: 500;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.text-muted {
  color: #8c8c8c;
  font-style: italic;
}

.operation-log {
  padding: 8px 0;
}

.operation-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.operation-name {
  font-weight: 500;
  color: #262626;
}

.operation-time {
  font-size: 12px;
  color: #8c8c8c;
}

.operation-user,
.operation-remark {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
}

.ant-statistic-content {
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}
</style>
