package cn.stylefeng.roses.kernel.ca.api.pojo.external.request;

import cn.stylefeng.roses.kernel.ca.api.pojo.external.BaseSsoExternalApiRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 刷新SSO会话内容
 *
 * <AUTHOR>
 * @date 2021/3/8 10:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SsoExternalAccountRequest extends BaseSsoExternalApiRequest {

    /**
     * 帐号ID不能为空
     */
    @NotNull(message = "帐号ID不能为空")
    private Long accountId;
}
