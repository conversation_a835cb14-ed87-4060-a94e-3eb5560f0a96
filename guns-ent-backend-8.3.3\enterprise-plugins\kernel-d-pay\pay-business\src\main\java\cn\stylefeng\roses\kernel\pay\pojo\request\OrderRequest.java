package cn.stylefeng.roses.kernel.pay.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单封装类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderRequest extends BaseRequest {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = {edit.class, delete.class, orderStatus.class, detail.class})
    @ChineseDescription("订单id")
    private Long orderId;
    /**
     * 订单号，时间戳加6位随机字符串
     */
    @NotBlank(message = "订单号，时间戳加6位随机字符串不能为空", groups = {add.class, edit.class})
    @ChineseDescription("订单号，时间戳加6位随机字符串")
    private String orderNumber;
    /**
     * 订单所属用户id
     */
    @NotNull(message = "订单所属用户id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("订单所属用户id")
    private Long userId;
    /**
     * 商品id
     */
    @ChineseDescription("商品id")
    private Long goodsId;
    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String goodsName;
    /**
     * 商品原价
     */
    @ChineseDescription("商品原价")
    private BigDecimal originalPrice;
    /**
     * 实付金额
     */
    @ChineseDescription("实付金额")
    private BigDecimal payPrice;
    /**
     * 状态，1待支付、2已完成、3已取消、4已退款
     */
    @NotNull(message = "状态，1待支付、2已完成、3已取消、4已退款不能为空", groups = {add.class, edit.class})
    @ChineseDescription("状态，1待支付、2已完成、3已取消、4已退款")
    private Integer state;
    /**
     * 支付时间
     */
    @ChineseDescription("支付时间")
    private String payTime;
    /**
     * YunGouOS系统单号
     */
    @ChineseDescription("YunGouOS系统单号")
    private String orderNo;
    /**
     * 支付单号
     */
    @ChineseDescription("支付单号")
    private String payNo;
    /**
     * 支付渠道：alipay，wxpay
     */
    @ChineseDescription("支付渠道：alipay，wxpay")
    private String payChannel;
    /**
     * 支付时的签名
     */
    @ChineseDescription("支付时的签名")
    private String sign;
    /**
     * 用户openId
     */
    @ChineseDescription("用户openId")
    private String openId;

    /**
     * 批量删除用的id集合
     */
    @NotNull(message = "批量删除id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("批量删除用的id集合")
    private List<Long> batchDeleteIdList;

    /**
     * 是否开过发票
     */
    @ChineseDescription("是否开过发票：Y-开过，N-未开过")
    private String invoiceFlag;

    /**
     * 获取订单状态
     */
    public @interface orderStatus {

    }

}