System.register(["./index-legacy-ee1db0c7.js","./index-legacy-9a185ac3.js","./productCategoryApi-legacy-247b2407.js"],(function(e,l){"use strict";var a,t,r,d,o,n,c,i,s,u,h;return{setters:[e=>{a=e._,t=e.aP,r=e.as,d=e.r,o=e.X,n=e.o,c=e.a,i=e.c,s=e.d,u=e.m},null,e=>{h=e.g}],execute:function(){var l=document.createElement("style");l.textContent=".category-selector[data-v-50dbb64b]{width:100%}\n",document.head.appendChild(l);const g=t({name:"CategorySelector",props:{value:{type:[Number,String],default:void 0},multiple:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择产品分类"},disabled:{type:Boolean,default:!1},allowClear:{type:Boolean,default:!0}},emits:["update:value","change"],setup(e,{emit:l}){const a=r.SHOW_PARENT,t=d([]),c=d(e.multiple?[]:void 0),i=e=>e&&e.length?e.map((e=>({...e,categoryId:String(e.categoryId),isLeaf:!(e.hasChildren||e.children&&0!==e.children.length),children:e.children?i(e.children):[]}))):[];return o((()=>e.value),(e=>{c.value=e}),{immediate:!0}),n((()=>{(async()=>{try{const e=await h({onlyEnabled:!0});console.log("分类选择器API响应:",e),e&&Array.isArray(e)?t.value=i(e):(console.error("API响应格式错误:",e),u.error("获取分类树失败"))}catch(e){console.error("加载分类树出错:",e),u.error("加载分类树出错")}})()})),{SHOW_PARENT:a,treeData:t,selectedValue:c,handleChange:e=>{l("update:value",e),l("change",e)},filterTreeNode:(e,l)=>l.categoryName.toLowerCase().indexOf(e.toLowerCase())>=0}}}),y={class:"category-selector"};e("default",a(g,[["render",function(e,l,a,t,d,o){const n=r;return c(),i("div",y,[s(n,{value:e.selectedValue,"onUpdate:value":l[0]||(l[0]=l=>e.selectedValue=l),"tree-data":e.treeData,"tree-checkable":e.multiple,"show-checked-strategy":e.multiple?e.SHOW_PARENT:null,"field-names":{children:"children",label:"categoryName",value:"categoryId",key:"categoryId"},placeholder:e.placeholder,disabled:e.disabled,"allow-clear":e.allowClear,multiple:e.multiple,"show-search":!0,"filter-tree-node":e.filterTreeNode,style:{width:"100%"},onChange:e.handleChange},null,8,["value","tree-data","tree-checkable","show-checked-strategy","placeholder","disabled","allow-clear","multiple","filter-tree-node","onChange"])])}],["__scopeId","data-v-50dbb64b"]]))}}}));
