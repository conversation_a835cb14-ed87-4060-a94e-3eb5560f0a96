import{ay as K,az as Q,r as W,a as Z,c as q,d as M,w as j,g as tt,b as et,t as nt,m as rt,B as ot,a0 as it}from"./index-18a1ea24.js";/* empty css              */var D={exports:{}};/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */(function(x,T){(function(E,m){x.exports=m()})(K,function(){return function(){var S={686:function(s,u,t){t.d(u,{default:function(){return J}});var a=t(279),f=t.n(a),l=t(370),h=t.n(l),y=t(817),g=t.n(y);function d(i){try{return document.execCommand(i)}catch(n){return!1}}var v=function(n){var e=g()(n);return d("cut"),e},p=v;function _(i){var n=document.documentElement.getAttribute("dir")==="rtl",e=document.createElement("textarea");e.style.fontSize="12pt",e.style.border="0",e.style.padding="0",e.style.margin="0",e.style.position="absolute",e.style[n?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return e.style.top="".concat(r,"px"),e.setAttribute("readonly",""),e.value=i,e}var P=function(n,e){var r=_(n);e.container.appendChild(r);var o=g()(r);return d("copy"),r.remove(),o},B=function(n){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},r="";return typeof n=="string"?r=P(n,e):n instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(n==null?void 0:n.type)?r=P(n.value,e):(r=g()(n),d("copy")),r},k=B;function C(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?C=function(e){return typeof e}:C=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(i)}var F=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=n.action,r=e===void 0?"copy":e,o=n.container,c=n.target,b=n.text;if(r!=="copy"&&r!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(c!==void 0)if(c&&C(c)==="object"&&c.nodeType===1){if(r==="copy"&&c.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(r==="cut"&&(c.hasAttribute("readonly")||c.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}else throw new Error('Invalid "target" value, use a valid Element');if(b)return k(b,{container:o});if(c)return r==="cut"?p(c):k(c,{container:o})},H=F;function w(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?w=function(e){return typeof e}:w=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(i)}function I(i,n){if(!(i instanceof n))throw new TypeError("Cannot call a class as a function")}function R(i,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(i,r.key,r)}}function V(i,n,e){return n&&R(i.prototype,n),e&&R(i,e),i}function z(i,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");i.prototype=Object.create(n&&n.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),n&&L(i,n)}function L(i,n){return L=Object.setPrototypeOf||function(r,o){return r.__proto__=o,r},L(i,n)}function U(i){var n=X();return function(){var r=A(i),o;if(n){var c=A(this).constructor;o=Reflect.construct(r,arguments,c)}else o=r.apply(this,arguments);return Y(this,o)}}function Y(i,n){return n&&(w(n)==="object"||typeof n=="function")?n:G(i)}function G(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}function X(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(i){return!1}}function A(i){return A=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},A(i)}function N(i,n){var e="data-clipboard-".concat(i);if(n.hasAttribute(e))return n.getAttribute(e)}var $=function(i){z(e,i);var n=U(e);function e(r,o){var c;return I(this,e),c=n.call(this),c.resolveOptions(o),c.listenClick(r),c}return V(e,[{key:"resolveOptions",value:function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof o.action=="function"?o.action:this.defaultAction,this.target=typeof o.target=="function"?o.target:this.defaultTarget,this.text=typeof o.text=="function"?o.text:this.defaultText,this.container=w(o.container)==="object"?o.container:document.body}},{key:"listenClick",value:function(o){var c=this;this.listener=h()(o,"click",function(b){return c.onClick(b)})}},{key:"onClick",value:function(o){var c=o.delegateTarget||o.currentTarget,b=this.action(c)||"copy",O=H({action:b,container:this.container,target:this.target(c),text:this.text(c)});this.emit(O?"success":"error",{action:b,text:O,trigger:c,clearSelection:function(){c&&c.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(o){return N("action",o)}},{key:"defaultTarget",value:function(o){var c=N("target",o);if(c)return document.querySelector(c)}},{key:"defaultText",value:function(o){return N("text",o)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(o){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return k(o,c)}},{key:"cut",value:function(o){return p(o)}},{key:"isSupported",value:function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],c=typeof o=="string"?[o]:o,b=!!document.queryCommandSupported;return c.forEach(function(O){b=b&&!!document.queryCommandSupported(O)}),b}}]),e}(f()),J=$},828:function(s){var u=9;if(typeof Element<"u"&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}function a(f,l){for(;f&&f.nodeType!==u;){if(typeof f.matches=="function"&&f.matches(l))return f;f=f.parentNode}}s.exports=a},438:function(s,u,t){var a=t(828);function f(y,g,d,v,p){var _=h.apply(this,arguments);return y.addEventListener(d,_,p),{destroy:function(){y.removeEventListener(d,_,p)}}}function l(y,g,d,v,p){return typeof y.addEventListener=="function"?f.apply(null,arguments):typeof d=="function"?f.bind(null,document).apply(null,arguments):(typeof y=="string"&&(y=document.querySelectorAll(y)),Array.prototype.map.call(y,function(_){return f(_,g,d,v,p)}))}function h(y,g,d,v){return function(p){p.delegateTarget=a(p.target,g),p.delegateTarget&&v.call(y,p)}}s.exports=l},879:function(s,u){u.node=function(t){return t!==void 0&&t instanceof HTMLElement&&t.nodeType===1},u.nodeList=function(t){var a=Object.prototype.toString.call(t);return t!==void 0&&(a==="[object NodeList]"||a==="[object HTMLCollection]")&&"length"in t&&(t.length===0||u.node(t[0]))},u.string=function(t){return typeof t=="string"||t instanceof String},u.fn=function(t){var a=Object.prototype.toString.call(t);return a==="[object Function]"}},370:function(s,u,t){var a=t(879),f=t(438);function l(d,v,p){if(!d&&!v&&!p)throw new Error("Missing required arguments");if(!a.string(v))throw new TypeError("Second argument must be a String");if(!a.fn(p))throw new TypeError("Third argument must be a Function");if(a.node(d))return h(d,v,p);if(a.nodeList(d))return y(d,v,p);if(a.string(d))return g(d,v,p);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function h(d,v,p){return d.addEventListener(v,p),{destroy:function(){d.removeEventListener(v,p)}}}function y(d,v,p){return Array.prototype.forEach.call(d,function(_){_.addEventListener(v,p)}),{destroy:function(){Array.prototype.forEach.call(d,function(_){_.removeEventListener(v,p)})}}}function g(d,v,p){return f(document.body,d,v,p)}s.exports=l},817:function(s){function u(t){var a;if(t.nodeName==="SELECT")t.focus(),a=t.value;else if(t.nodeName==="INPUT"||t.nodeName==="TEXTAREA"){var f=t.hasAttribute("readonly");f||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),f||t.removeAttribute("readonly"),a=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var l=window.getSelection(),h=document.createRange();h.selectNodeContents(t),l.removeAllRanges(),l.addRange(h),a=l.toString()}return a}s.exports=u},279:function(s){function u(){}u.prototype={on:function(t,a,f){var l=this.e||(this.e={});return(l[t]||(l[t]=[])).push({fn:a,ctx:f}),this},once:function(t,a,f){var l=this;function h(){l.off(t,h),a.apply(f,arguments)}return h._=a,this.on(t,h,f)},emit:function(t){var a=[].slice.call(arguments,1),f=((this.e||(this.e={}))[t]||[]).slice(),l=0,h=f.length;for(l;l<h;l++)f[l].fn.apply(f[l].ctx,a);return this},off:function(t,a){var f=this.e||(this.e={}),l=f[t],h=[];if(l&&a)for(var y=0,g=l.length;y<g;y++)l[y].fn!==a&&l[y].fn._!==a&&h.push(l[y]);return h.length?f[t]=h:delete f[t],this}},s.exports=u,s.exports.TinyEmitter=u}},E={};function m(s){if(E[s])return E[s].exports;var u=E[s]={exports:{}};return S[s](u,u.exports,m),u.exports}return function(){m.n=function(s){var u=s&&s.__esModule?function(){return s.default}:function(){return s};return m.d(u,{a:u}),u}}(),function(){m.d=function(s,u){for(var t in u)m.o(u,t)&&!m.o(s,t)&&Object.defineProperty(s,t,{enumerable:!0,get:u[t]})}}(),function(){m.o=function(s,u){return Object.prototype.hasOwnProperty.call(s,u)}}(),m(686)}().default})})(D);var ut=D.exports;const at=Q(ut),ct=x=>{const T=(x==null?void 0:x.appendToBody)===void 0?!0:x.appendToBody;return{toClipboard(S,E){return new Promise((m,s)=>{const u=document.createElement("button"),t=new at(u,{text:()=>S,action:()=>"copy",container:E!==void 0?E:document.body});t.on("success",a=>{t.destroy(),m(a)}),t.on("error",a=>{t.destroy(),s(a)}),T&&document.body.appendChild(u),u.click(),T&&document.body.removeChild(u)})}}},st={class:"guns-body guns-body-card"},pt={__name:"index",setup(x){const{toClipboard:T}=ct(),S=W("\u6211\u662F\u590D\u5236\u7684\u5185\u5BB9"),E=async()=>{try{await T(S.value),rt.success("\u590D\u5236\u6210\u529F")}catch(m){console.error(m)}};return(m,s)=>{const u=ot,t=it;return Z(),q("div",st,[M(t,{title:"\u4E00\u952E\u590D\u5236",bordered:!1},{default:j(()=>[M(u,{onClick:E},{default:j(()=>s[0]||(s[0]=[tt("\u70B9\u51FB\u590D\u5236")])),_:1,__:[0]}),et("div",null,nt(S.value),1)]),_:1})])}}};export{pt as default};
