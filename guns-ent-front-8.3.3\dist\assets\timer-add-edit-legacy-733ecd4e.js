System.register(["./index-legacy-ee1db0c7.js","./timer-form-legacy-0d0c4784.js"],(function(e,t){"use strict";var a,l,i,o,s,n,r,u,d,v;return{setters:[e=>{a=e.r,l=e.o,i=e.a,o=e.f,s=e.w,n=e.d,r=e.m,u=e.M},e=>{d=e._,v=e.S}],execute:function(){e("default",{__name:"timer-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:t}){const c=e,f=t,m=a(!1),b=a(!1),p=a({positionSort:1e3}),g=a(null);l((()=>{c.data?(b.value=!0,p.value=Object.assign({},c.data)):b.value=!1}));const y=e=>{f("update:visible",e)},h=async()=>{g.value.$refs.formRef.validate().then((async e=>{if(e){m.value=!0;let e=null;e=b.value?v.edit(p.value):v.add(p.value),e.then((async e=>{m.value=!1,r.success(e.message),y(!1),f("done")})).catch((()=>{m.value=!1}))}}))};return(e,t)=>{const a=u;return i(),o(a,{width:700,maskClosable:!1,visible:c.visible,"confirm-loading":m.value,forceRender:!0,title:b.value?"编辑定时任务":"新建定时任务","body-style":{paddingBottom:"8px",height:"550px",overflowY:"auto"},"onUpdate:visible":y,onOk:h,onClose:t[1]||(t[1]=e=>y(!1))},{default:s((()=>[n(d,{form:p.value,"onUpdate:form":t[0]||(t[0]=e=>p.value=e),ref_key:"timerFormRef",ref:g},null,8,["form"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
