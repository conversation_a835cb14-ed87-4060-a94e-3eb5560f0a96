package cn.stylefeng.roses.kernel.impexp.org.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.org.pojo.EnsureImportOrgItem;
import cn.stylefeng.roses.kernel.rule.constants.TreeConstants;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织机构创建工厂
 *
 * <AUTHOR>
 * @since 2024-02-19 8:35
 */
public class OrgInfoFactory {

    /**
     * 根据最终确认的机构信息，转化成数据库的机构信息
     *
     * <AUTHOR>
     * @since 2024-02-19 8:35
     */
    public static List<HrOrganization> createOrgInfo(List<EnsureImportOrgItem> importOrgItemList) {

        if (ObjectUtil.isEmpty(importOrgItemList)) {
            return new ArrayList<>();
        }

        List<HrOrganization> hrOrganizations = new ArrayList<>();

        for (EnsureImportOrgItem ensureImportOrgItem : importOrgItemList) {
            HrOrganization hrOrganization = new HrOrganization();

            // 设置机构id
            hrOrganization.setOrgId(ensureImportOrgItem.getOrgId());

            // 设置机构父级id
            hrOrganization.setOrgParentId(ensureImportOrgItem.getOrgParentId());

            // 设置父级ids
            hrOrganization.setOrgPids(ensureImportOrgItem.getOrgPids());

            // 设置组织机构名称
            hrOrganization.setOrgName(ensureImportOrgItem.getOrgName());

            // 设置简称
            hrOrganization.setOrgShortName(ensureImportOrgItem.getOrgShortName());

            // 设置机构编码
            hrOrganization.setOrgCode(ensureImportOrgItem.getOrgCode());

            // 设置排序
            hrOrganization.setOrgSort(ensureImportOrgItem.getOrgSort());

            // 设置状态
            hrOrganization.setStatusFlag(ensureImportOrgItem.getStatusFlag());

            // 设置机构类型
            hrOrganization.setOrgType(ensureImportOrgItem.getOrgType());

            // 设置税号
            hrOrganization.setTaxNo(ensureImportOrgItem.getTaxNo());

            // 设置描述
            hrOrganization.setRemark(ensureImportOrgItem.getRemark());

            // 设置组织机构外部主数据id
            hrOrganization.setMasterOrgId(ensureImportOrgItem.getMasterOrgId());

            hrOrganizations.add(hrOrganization);
        }

        return hrOrganizations;
    }

    /**
     * 创建一级节点的父级的组织机构信息
     *
     * <AUTHOR>
     * @since 2025/1/21 11:48
     */
    public static HrOrganization createRootParentOrgInfo() {
        HrOrganization org = new HrOrganization();
        org.setOrgId(TreeConstants.DEFAULT_PARENT_ID);
        org.setOrgPids("");
        return org;
    }

}
