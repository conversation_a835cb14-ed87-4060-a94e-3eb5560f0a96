package cn.stylefeng.roses.kernel.erp.modular.purchase.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PurchaseOrder;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PurchaseOrderQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderStatusCountResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.MonthlyPurchaseStatsResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrder> {

    /**
     * 根据入库单号查询采购入库单
     *
     * @param orderNo 入库单号
     * @return 采购入库单信息
     */
    @Select("SELECT * FROM erp_purchase_order WHERE order_no = #{orderNo}")
    PurchaseOrder getByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 统计供应商关联的采购入库单数量
     *
     * @param supplierId 供应商ID
     * @return 采购入库单数量
     */
    @Select("SELECT COUNT(1) FROM erp_purchase_order WHERE supplier_id = #{supplierId}")
    Long countBySupplierId(@Param("supplierId") Long supplierId);

    /**
     * 获取今日采购入库单数量（用于生成单号）
     *
     * @param dateStr 日期字符串（格式：yyyyMMdd）
     * @return 今日采购入库单数量
     */
    @Select("SELECT COUNT(1) FROM erp_purchase_order WHERE DATE_FORMAT(order_date, '%Y%m%d') = #{dateStr}")
    Long getTodayOrderCount(@Param("dateStr") String dateStr);

    /**
     * 分页查询采购入库单列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 采购入库单列表
     */
    Page<PurchaseOrderResponse> selectPurchaseOrderPage(Page<PurchaseOrderResponse> page, @Param("request") PurchaseOrderQueryRequest request);

    /**
     * 根据供应商ID统计采购金额
     *
     * @param supplierId 供应商ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 采购总金额
     */
    BigDecimal getTotalAmountBySupplier(@Param("supplierId") Long supplierId, 
                                       @Param("startDate") LocalDate startDate, 
                                       @Param("endDate") LocalDate endDate);

    /**
     * 统计各状态的采购入库单数量
     *
     * @param supplierId 供应商ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 状态统计列表
     */
    List<PurchaseOrderStatusCountResponse> getOrderCountByStatus(@Param("supplierId") Long supplierId,
                                                               @Param("startDate") LocalDate startDate,
                                                               @Param("endDate") LocalDate endDate);

    /**
     * 获取供应商的最近采购记录
     *
     * @param supplierId 供应商ID
     * @param limit 限制数量
     * @return 最近采购记录列表
     */
    List<PurchaseOrderResponse> getRecentOrdersBySupplier(@Param("supplierId") Long supplierId, @Param("limit") Integer limit);

    /**
     * 获取月度采购统计
     *
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 月度统计列表
     */
    List<MonthlyPurchaseStatsResponse> getMonthlyPurchaseStats(@Param("startDate") LocalDate startDate, 
                                                             @Param("endDate") LocalDate endDate);

}