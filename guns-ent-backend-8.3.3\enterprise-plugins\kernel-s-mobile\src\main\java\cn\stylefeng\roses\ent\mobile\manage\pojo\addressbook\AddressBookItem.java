package cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook;

import cn.stylefeng.roses.kernel.file.api.format.FileUrlFormatProcess;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.annotation.EnumFieldFormat;
import cn.stylefeng.roses.kernel.rule.annotation.SimpleFieldFormat;
import cn.stylefeng.roses.kernel.sys.api.enums.user.UserStatusEnum;
import lombok.Data;

/**
 * 通讯录元素
 *
 * <AUTHOR>
 * @since 2024/3/21 22:22
 */
@Data
public class AddressBookItem {

    /**
     * 元素类型：1-公司，2-部门，3-人员
     */
    @ChineseDescription("元素类型：1-公司，2-部门，3-人员")
    private Integer itemType;

    /**
     * 组织机构id
     */
    @ChineseDescription("组织机构id")
    private Long orgId;

    /**
     * 机构名称
     */
    @ChineseDescription("机构名称")
    private String orgName;

    /**
     * 头像id
     */
    @ChineseDescription("头像id")
    @SimpleFieldFormat(processClass = FileUrlFormatProcess.class)
    private Long avatar;

    /**
     * 用户id
     */
    @ChineseDescription("用户id")
    private Long userId;

    /**
     * 用户姓名
     */
    @ChineseDescription("用户姓名")
    private String realName;

    /**
     * 是否是管理员
     */
    @ChineseDescription("是否是管理员")
    private Boolean adminFlag;

    /**
     * 用户状态
     */
    @ChineseDescription("用户状态")
    @EnumFieldFormat(processEnum = UserStatusEnum.class)
    private Integer userStatus;

    /**
     * 职位名称
     */
    @ChineseDescription("职位名称")
    private String positionName;

    /**
     * 组织机构下人的数量
     */
    @ChineseDescription("组织机构下人的数量")
    private Integer orgUserCount = 0;

}
