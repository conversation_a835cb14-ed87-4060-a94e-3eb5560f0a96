package cn.stylefeng.roses.kernel.websocket.api.pojo;

import cn.hutool.core.io.IoUtil;
import jakarta.websocket.Session;
import lombok.Data;

import java.util.Date;

/**
 * 贯穿整个WebSocket生命周期的对象
 *
 * <AUTHOR>
 * @since 2024/1/14 23:05
 */
@Data
public class WebSocketDTO {

    /**
     * 代表一个连接javax.websocket.Session
     */
    private Session session;

    /**
     * 用户账号，一个用户可以在多个地方登陆
     */
    private String userAccount;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 唯一标识，一版是用户token
     */
    private String identifier;

    /**
     * 最后心跳时间
     */
    private Date lastHeart;

    /**
     * 关闭websocket
     *
     * <AUTHOR>
     * @since 2024/1/14 23:05
     */
    public void closeSession() {
        IoUtil.close(session);
    }

}
