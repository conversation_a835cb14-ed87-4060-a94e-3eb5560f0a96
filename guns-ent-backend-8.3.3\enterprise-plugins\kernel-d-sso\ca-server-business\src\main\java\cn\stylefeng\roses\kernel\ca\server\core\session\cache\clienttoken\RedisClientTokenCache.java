package cn.stylefeng.roses.kernel.ca.server.core.session.cache.clienttoken;

import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.ca.api.pojo.CaClientInfo;
import cn.stylefeng.roses.kernel.cache.redis.AbstractRedisCacheOperator;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Set;

/**
 * 基于Redis的客户Token集合，本缓存为了存用户一次登录后，单点过的所有业务系统的token
 * <p>
 * key：   CAID，用户单点成功的后在单点服务器的唯一标识
 * value： 用户所有在业务端单点的token，一般这个缓存是用在单点退出时候清除业务缓存
 *
 * <AUTHOR>
 * @date 2022/5/20 9:57
 */
public class RedisClientTokenCache extends AbstractRedisCacheOperator<Set<CaClientInfo>> {

    public RedisClientTokenCache(RedisTemplate<String, Set<CaClientInfo>> redisTemplate) {
        super(redisTemplate);
    }

    @Override
    public String getCommonKeyPrefix() {
        return CaServerConstants.CA_CLIENT_TOKEN_CACHE_PREFIX;
    }

}
