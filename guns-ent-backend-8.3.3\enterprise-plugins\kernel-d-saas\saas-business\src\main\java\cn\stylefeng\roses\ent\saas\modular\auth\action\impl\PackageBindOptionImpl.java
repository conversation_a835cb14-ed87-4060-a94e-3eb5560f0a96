package cn.stylefeng.roses.ent.saas.modular.auth.action.impl;

import cn.stylefeng.roses.ent.saas.modular.auth.action.PackageBindPermissionAction;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.kernel.sys.api.enums.PermissionNodeTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.role.enums.RoleLimitTypeEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 功能包绑定菜单功能的实现
 *
 * <AUTHOR>
 * @since 2024/1/22 0:48
 */
@Service
public class PackageBindOptionImpl implements PackageBindPermissionAction {

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    @Override
    public PermissionNodeTypeEnum getPackageBindPermissionNodeType() {
        return PermissionNodeTypeEnum.OPTIONS;
    }

    @Override
    public void doPackageBindPermissionAction(PackageBindPermissionRequest packageBindPermissionRequest) {
        Long packageId = packageBindPermissionRequest.getPackageId();
        Long menuOptionId = packageBindPermissionRequest.getNodeId();

        if (packageBindPermissionRequest.getChecked()) {
            TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
            tenantPackageAuth.setPackageId(packageId);
            tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU_OPTIONS.getCode());
            tenantPackageAuth.setBusinessId(menuOptionId);
            this.tenantPackageAuthService.save(tenantPackageAuth);
        } else {
            LambdaUpdateWrapper<TenantPackageAuth> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TenantPackageAuth::getPackageId, packageId);
            wrapper.eq(TenantPackageAuth::getBusinessId, menuOptionId);
            this.tenantPackageAuthService.remove(wrapper);
        }
    }

}
