package cn.stylefeng.roses.kernel.erp.modular.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.InventoryConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.InventoryExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryHistory;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryHistoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryHistoryMapper;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryHistoryService;
import cn.stylefeng.roses.kernel.erp.modular.product.service.ErpProductService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存历史Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@Service
public class InventoryHistoryServiceImpl extends ServiceImpl<InventoryHistoryMapper, InventoryHistory> implements InventoryHistoryService {

    @Resource
    private InventoryHistoryMapper inventoryHistoryMapper;

    @Resource
    private ErpProductService erpProductService;

    @Resource
    private ErpSupplierService erpSupplierService;

    @Override
    public PageResult<InventoryHistoryResponse> findPage(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        // 使用优化的Mapper查询，避免N+1问题
        Page<InventoryHistoryResponse> page = new Page<>(
            PageFactory.defaultPage().getCurrent(), 
            PageFactory.defaultPage().getSize()
        );
        
        Page<InventoryHistoryResponse> result = inventoryHistoryMapper.selectInventoryHistoryPage(page, inventoryHistoryQueryRequest);
        
        return PageResultFactory.createPageResult(result.getRecords(), result.getTotal(),
                (int) result.getSize(), (int) result.getCurrent());
    }

    @Override
    public List<InventoryHistoryResponse> findList(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        LambdaQueryWrapper<InventoryHistory> wrapper = createQueryWrapper(inventoryHistoryQueryRequest);
        List<InventoryHistory> historyList = this.list(wrapper);
        return historyList.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public List<InventoryHistoryResponse> findProductHistory(Long productId) {
        return List.of();
    }

    public List<InventoryHistoryResponse> findProductHistory(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        if (ObjectUtil.isEmpty(inventoryHistoryQueryRequest.getProductId())) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        // 使用优化的Mapper查询，直接返回完整的响应对象
        Integer limit = null;
        if (inventoryHistoryQueryRequest.getPageSize() != null && inventoryHistoryQueryRequest.getPageSize() > 0) {
            limit = inventoryHistoryQueryRequest.getPageSize();
        }
        
        return inventoryHistoryMapper.selectByProductId(inventoryHistoryQueryRequest.getProductId(), limit);
    }

    @Override
    public InventoryHistoryResponse detail(Long historyId) {
        if (ObjectUtil.isEmpty(historyId)) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_HISTORY_NOT_EXIST);
        }

        InventoryHistory inventoryHistory = this.getById(historyId);
        if (ObjectUtil.isEmpty(inventoryHistory)) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_HISTORY_NOT_EXIST);
        }

        return convertToResponse(inventoryHistory);
    }

    @Override
    public List<InventoryHistoryResponse> findStatistics(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        LambdaQueryWrapper<InventoryHistory> wrapper = createQueryWrapper(inventoryHistoryQueryRequest);
        wrapper.orderByDesc(InventoryHistory::getOperationTime);
        
        List<InventoryHistory> historyList = this.list(wrapper);
        return historyList.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordInventoryHistory(Long productId, String operationType, BigDecimal quantityChange, 
                                     BigDecimal stockBefore, BigDecimal stockAfter, 
                                     Long referenceId, String referenceType, String remark) {
        
        if (ObjectUtil.isEmpty(productId)) {
            throw new ServiceException(InventoryExceptionEnum.PRODUCT_NOT_EXIST);
        }

        if (StrUtil.isBlank(operationType)) {
            throw new ServiceException(InventoryExceptionEnum.INVENTORY_OPERATION_TYPE_ERROR);
        }

        InventoryHistory inventoryHistory = new InventoryHistory();
        inventoryHistory.setProductId(productId);
        inventoryHistory.setOperationType(operationType);
        inventoryHistory.setQuantityChange(quantityChange);
        inventoryHistory.setStockBefore(stockBefore);
        inventoryHistory.setStockAfter(stockAfter);
        inventoryHistory.setReferenceId(referenceId);
        inventoryHistory.setReferenceType(referenceType);
        inventoryHistory.setRemark(remark);
        inventoryHistory.setOperationTime(LocalDateTime.now());

        // 设置操作人员信息
        try {
            Long userId = LoginContext.me().getLoginUser().getUserId();
            // inventoryHistory.setOperationUser(userId);
        } catch (Exception e) {
            // 如果获取用户信息失败，设置默认值
            // inventoryHistory.setOperationUser(0L);
        }

        this.save(inventoryHistory);
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<InventoryHistory> createQueryWrapper(InventoryHistoryQueryRequest inventoryHistoryQueryRequest) {
        LambdaQueryWrapper<InventoryHistory> wrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotEmpty(inventoryHistoryQueryRequest.getProductId())) {
            wrapper.eq(InventoryHistory::getProductId, inventoryHistoryQueryRequest.getProductId());
        }

        if (StrUtil.isNotBlank(inventoryHistoryQueryRequest.getOperationType())) {
            wrapper.eq(InventoryHistory::getOperationType, inventoryHistoryQueryRequest.getOperationType());
        }

        if (CollectionUtil.isNotEmpty(inventoryHistoryQueryRequest.getOperationTypeList())) {
            wrapper.in(InventoryHistory::getOperationType, inventoryHistoryQueryRequest.getOperationTypeList());
        }

        if (StrUtil.isNotBlank(inventoryHistoryQueryRequest.getReferenceType())) {
            wrapper.eq(InventoryHistory::getReferenceType, inventoryHistoryQueryRequest.getReferenceType());
        }

        if (CollectionUtil.isNotEmpty(inventoryHistoryQueryRequest.getReferenceTypeList())) {
            wrapper.in(InventoryHistory::getReferenceType, inventoryHistoryQueryRequest.getReferenceTypeList());
        }

        if (ObjectUtil.isNotEmpty(inventoryHistoryQueryRequest.getReferenceId())) {
            wrapper.eq(InventoryHistory::getReferenceId, inventoryHistoryQueryRequest.getReferenceId());
        }

        if (ObjectUtil.isNotEmpty(inventoryHistoryQueryRequest.getStartTime())) {
            wrapper.ge(InventoryHistory::getOperationTime, inventoryHistoryQueryRequest.getStartTime());
        }

        if (ObjectUtil.isNotEmpty(inventoryHistoryQueryRequest.getEndTime())) {
            wrapper.le(InventoryHistory::getOperationTime, inventoryHistoryQueryRequest.getEndTime());
        }

        // if (ObjectUtil.isNotEmpty(inventoryHistoryQueryRequest.getOperationUser())) {
        //     wrapper.eq(InventoryHistory::getOperationUser, inventoryHistoryQueryRequest.getOperationUser());
        // }



        wrapper.orderByDesc(InventoryHistory::getOperationTime);
        return wrapper;
    }

    /**
     * 转换为响应对象
     */
    private InventoryHistoryResponse convertToResponse(InventoryHistory inventoryHistory) {
        InventoryHistoryResponse response = new InventoryHistoryResponse();
        BeanUtil.copyProperties(inventoryHistory, response);

        // 获取商品信息
        if (ObjectUtil.isNotEmpty(inventoryHistory.getProductId())) {
            ErpProductRequest productRequest = new ErpProductRequest();
            productRequest.setProductId(inventoryHistory.getProductId());
            ErpProductResponse product = erpProductService.detail(productRequest);
            if (ObjectUtil.isNotEmpty(product)) {
                response.setProductCode(product.getProductCode());
                response.setProductName(product.getProductName());
                response.setProductShortName(product.getProductShortName());
                response.setBarcode(product.getBarcode());
                response.setUnit(product.getUnit());

                // 获取供应商信息
                if (ObjectUtil.isNotEmpty(product.getSupplierId())) {
                    ErpSupplierRequest supplierRequest = new ErpSupplierRequest();
                    supplierRequest.setSupplierId(product.getSupplierId());
                    ErpSupplierResponse supplier = erpSupplierService.detail(supplierRequest);
                    if (ObjectUtil.isNotEmpty(supplier)) {
                        response.setSupplierId(supplier.getSupplierId());
                        response.setSupplierCode(supplier.getSupplierCode());
                        response.setSupplierName(supplier.getSupplierName());
                    }
                }
            }
        }

        // 获取操作人员姓名
        // if (ObjectUtil.isNotEmpty(inventoryHistory.getOperationUser())) {
        //     try {
        //         // 这里应该调用用户服务获取用户姓名，暂时设置为默认值
        //         // 实际项目中需要注入用户服务并调用相应方法
        //         response.setOperationUserName("操作员" + inventoryHistory.getOperationUser());
        //     } catch (Exception e) {
        //         // 如果获取用户信息失败，设置默认值
        //         response.setOperationUserName("未知操作员");
        //     }
        // }
        
        // 暂时设置默认操作人员名称
        // response.setOperationUserName("系统操作员");

        // 设置操作类型名称
        response.setOperationTypeName(getOperationTypeName(inventoryHistory.getOperationType()));

        // 设置关联单据类型名称
        response.setReferenceTypeName(getReferenceTypeName(inventoryHistory.getReferenceType()));

        return response;
    }

    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(String operationType) {
        if (StrUtil.isBlank(operationType)) {
            return "";
        }

        switch (operationType) {
            case InventoryConstants.OperationType.IN:
                return "入库";
            case InventoryConstants.OperationType.OUT:
                return "出库";
            case InventoryConstants.OperationType.ADJUST:
                return "调整";
            case InventoryConstants.OperationType.SALE:
                return "销售";
            case InventoryConstants.OperationType.SET_ALERT:
                return "设置预警";
            default:
                return operationType;
        }
    }

    /**
     * 获取关联单据类型名称
     */
    private String getReferenceTypeName(String referenceType) {
        if (StrUtil.isBlank(referenceType)) {
            return "";
        }

        switch (referenceType) {
            case InventoryConstants.ReferenceType.PURCHASE_ORDER:
                return "采购入库单";
            case InventoryConstants.ReferenceType.SALE_ORDER:
                return "销售单";
            case InventoryConstants.ReferenceType.ADJUST_ORDER:
                return "调整单";
            default:
                return referenceType;
        }
    }
}