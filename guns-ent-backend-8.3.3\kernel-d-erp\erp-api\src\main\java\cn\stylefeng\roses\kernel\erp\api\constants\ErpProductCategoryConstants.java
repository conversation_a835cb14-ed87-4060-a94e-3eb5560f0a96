package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * 产品分类常量
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
public interface ErpProductCategoryConstants {

    /**
     * 默认分类状态
     */
    String DEFAULT_CATEGORY_STATUS = "Y";

    /**
     * 分类状态-启用
     */
    String CATEGORY_STATUS_ENABLE = "Y";

    /**
     * 分类状态-停用
     */
    String CATEGORY_STATUS_DISABLE = "N";

    /**
     * 根节点父级ID
     */
    Long ROOT_PARENT_ID = 0L;

    /**
     * 路径分隔符
     */
    String PATH_SEPARATOR = "/";

    /**
     * 最大层级深度
     */
    Integer MAX_LEVEL_DEPTH = 5;

    /**
     * 分类层级名称映射
     */
    String LEVEL_1_NAME = "一级分类";
    String LEVEL_2_NAME = "二级分类";
    String LEVEL_3_NAME = "三级分类";
    String LEVEL_4_NAME = "四级分类";
    String LEVEL_5_NAME = "五级分类";

    /**
     * 分类图标映射
     */
    String LEVEL_1_ICON = "folder";
    String LEVEL_2_ICON = "folder-open";
    String LEVEL_3_ICON = "file";
    String LEVEL_4_ICON = "file-text";
    String LEVEL_5_ICON = "tag";
}
