package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;

/**
 * 网络相关异常
 *
 * <AUTHOR>
 * @since 2025/08/01 20:35
 */
public class NetworkException extends PosException {

    public NetworkException(AbstractExceptionEnum exception) {
        super(exception);
    }

    public NetworkException(AbstractExceptionEnum exception, Object... params) {
        super(exception, params);
    }

    /**
     * 操作失败异常
     */
    public static NetworkException operationFailed(String operation, String reason) {
        return new NetworkException(ErpPosExceptionEnum.OPERATION_FAILED, operation, reason);
    }
}
