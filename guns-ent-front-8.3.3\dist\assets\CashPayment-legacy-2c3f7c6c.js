System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js"],(function(a,e){"use strict";var t,n,c,i,o,l,s,d,f,p,u,r,m,v,g,x,h,b,y,k;return{setters:[a=>{t=a._,n=a.r,c=a.L,i=a.X,o=a.a,l=a.c,s=a.b,d=a.d,f=a.I,p=a.g,u=a.t,r=a.F,m=a.e,v=a.w,g=a.h,x=a.at,h=a.ae,b=a.m,y=a.y,k=a.B},null],execute:function(){var e=document.createElement("style");e.textContent=".cash-payment[data-v-c905453f]{padding:20px 24px;background:#f9f9f9;border-top:1px solid #f0f0f0}.cash-input-section[data-v-c905453f]{margin-bottom:20px}.input-group[data-v-c905453f]{margin-bottom:12px}.input-label[data-v-c905453f]{display:flex;align-items:center;gap:6px;margin-bottom:8px;font-size:14px;font-weight:500;color:#262626}.cash-input[data-v-c905453f]{width:100%}.payment-info[data-v-c905453f]{display:flex;justify-content:space-between;padding:8px 12px;background:#fff;border-radius:6px;border:1px solid #e8e8e8}.info-item[data-v-c905453f]{display:flex;align-items:center;gap:8px;font-size:13px}.info-label[data-v-c905453f]{color:#595959}.info-value[data-v-c905453f]{font-weight:600;color:#262626}.info-value.received[data-v-c905453f]{color:#1890ff}.quick-amounts[data-v-c905453f]{margin-bottom:20px}.quick-title[data-v-c905453f]{display:flex;align-items:center;gap:6px;margin-bottom:12px;font-size:13px;color:#595959}.amount-buttons[data-v-c905453f]{display:flex;flex-wrap:wrap;gap:8px}.quick-amount-btn[data-v-c905453f]{min-width:60px;height:32px;border-radius:6px;font-size:12px}.quick-amount-btn.exact[data-v-c905453f]{background:#52c41a;border-color:#52c41a;color:#fff}.quick-amount-btn.exact[data-v-c905453f]:hover{background:#73d13d;border-color:#73d13d}.quick-amount-btn.clear[data-v-c905453f]{background:#ff7875;border-color:#ff7875;color:#fff}.quick-amount-btn.clear[data-v-c905453f]:hover{background:#ff9c99;border-color:#ff9c99}.change-display[data-v-c905453f]{margin-bottom:20px;background:#e6f7ff;border-radius:8px;border-left:4px solid #1890ff;overflow:hidden}.change-header[data-v-c905453f]{display:flex;align-items:center;gap:6px;padding:12px 16px;background:#bae7ff;font-size:13px;font-weight:500;color:#0050b3}.change-content[data-v-c905453f]{padding:16px}.change-amount[data-v-c905453f]{display:flex;justify-content:space-between;align-items:center}.change-label[data-v-c905453f]{font-size:14px;color:#262626;font-weight:500}.change-value[data-v-c905453f]{font-size:20px;font-weight:700;color:#1890ff}.cash-actions[data-v-c905453f]{display:flex;gap:12px}.action-btn[data-v-c905453f]{flex:1;height:44px;font-size:14px;font-weight:500}.action-btn.confirm[data-v-c905453f]{flex:2}@media (max-width: 768px){.cash-payment[data-v-c905453f]{padding:16px 20px}.payment-info[data-v-c905453f]{flex-direction:column;gap:8px}.amount-buttons[data-v-c905453f]{gap:6px}.quick-amount-btn[data-v-c905453f]{min-width:50px;height:28px;font-size:11px}.change-value[data-v-c905453f]{font-size:18px}.cash-actions[data-v-c905453f]{flex-direction:column;gap:8px}.action-btn[data-v-c905453f]{height:40px}}.change-display[data-v-c905453f]{animation:slideDown-c905453f .3s ease-out}@keyframes slideDown-c905453f{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}\n",document.head.appendChild(e);const w={class:"cash-payment"},_={class:"cash-input-section"},z={class:"input-group"},C={class:"input-label"},q={class:"payment-info"},A={class:"info-item"},j={class:"info-value"},P={class:"info-item"},B={class:"info-value received"},D={class:"info-item"},E={class:"info-value"},F={class:"quick-amounts"},I={class:"quick-title"},N={class:"amount-buttons"},Y={key:0,class:"change-display"},L={class:"change-header"},M={class:"change-content"},O={class:"change-amount"},S={class:"change-value"},U={class:"cash-actions"},X=Object.assign({name:"CashPayment"},{__name:"CashPayment",props:{paymentAmount:{type:Number,required:!0},initialAmount:{type:Number,default:0},loading:{type:Boolean,default:!1}},emits:["confirm-payment","cancel-payment","amount-change"],setup(a,{emit:e}){const t=a,X=e,G=n(0),H=[10,20,50,100,200,500],J=c((()=>Math.max(0,G.value-t.paymentAmount))),K=c((()=>G.value>=t.paymentAmount)),Q=a=>(a||0).toFixed(2),R=a=>{G.value=a,V(a)},T=()=>{G.value=0,V(0)},V=a=>{X("amount-change",{receivedAmount:a,changeAmount:J.value,canConfirm:K.value})},W=()=>{K.value?X("confirm-payment",{receivedAmount:G.value,changeAmount:J.value}):b.warning("请输入正确的实收金额")},Z=()=>{X("cancel-payment")};return i((()=>t.initialAmount),(a=>{a>0&&R(a)}),{immediate:!0}),i((()=>t.paymentAmount),(a=>{G.value<a&&R(a)})),(e,t)=>{const n=y,c=k;return o(),l("div",w,[s("div",_,[s("div",z,[s("div",C,[d(f,{iconClass:"icon-cash"}),t[2]||(t[2]=p(" 实收金额 "))]),d(n,{value:G.value,"onUpdate:value":t[0]||(t[0]=a=>G.value=a),min:0,precision:2,step:.01,size:"large",placeholder:"请输入实收金额",class:"cash-input",onChange:V,onPressEnter:W},null,8,["value"])]),s("div",q,[s("div",A,[t[3]||(t[3]=s("span",{class:"info-label"},"应付金额:",-1)),s("span",j,"¥"+u(Q(a.paymentAmount)),1)]),s("div",P,[t[4]||(t[4]=s("span",{class:"info-label"},"实收金额:",-1)),s("span",B,"¥"+u(Q(G.value)),1)]),s("div",D,[t[5]||(t[5]=s("span",{class:"info-label"},"找零金额:",-1)),s("span",E,"¥"+u(Q(J.value)),1)])])]),s("div",F,[s("div",I,[d(f,{iconClass:"icon-quick"}),t[6]||(t[6]=p(" 快捷金额 "))]),s("div",N,[(o(),l(r,null,m(H,(a=>d(c,{key:a,size:"small",onClick:e=>R(a),class:"quick-amount-btn"},{default:v((()=>[p(" ¥"+u(a),1)])),_:2},1032,["onClick"]))),64)),d(c,{size:"small",onClick:t[1]||(t[1]=e=>R(a.paymentAmount)),class:"quick-amount-btn exact"},{default:v((()=>t[7]||(t[7]=[p(" 刚好 ")]))),_:1,__:[7]}),d(c,{size:"small",onClick:T,class:"quick-amount-btn clear"},{default:v((()=>t[8]||(t[8]=[p(" 清空 ")]))),_:1,__:[8]})])]),J.value>0?(o(),l("div",Y,[s("div",L,[d(f,{iconClass:"icon-change"}),t[9]||(t[9]=p(" 找零信息 "))]),s("div",M,[s("div",O,[t[10]||(t[10]=s("span",{class:"change-label"},"找零金额:",-1)),s("span",S,"¥"+u(Q(J.value)),1)])])])):g("",!0),s("div",U,[d(c,{size:"large",onClick:Z,class:"action-btn cancel"},{default:v((()=>t[11]||(t[11]=[p(" 取消 ")]))),_:1,__:[11]}),d(c,{type:"primary",size:"large",onClick:W,disabled:!K.value,loading:a.loading,class:"action-btn confirm"},{icon:v((()=>[d(x(h))])),default:v((()=>[t[12]||(t[12]=p(" 确认收款 "))])),_:1,__:[12]},8,["disabled","loading"])])])}}});a("default",t(X,[["__scopeId","data-v-c905453f"]]))}}}));
