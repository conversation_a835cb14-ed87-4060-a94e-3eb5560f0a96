import{R as p,_ as f,s as y,o as v,a as b,c as I,b as c,d as e,w as a,g as l,t as n,i as k,a0 as g,v as x,G as j,Y as h,Z as D}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */class w{static getSystemInfo(t){return p.getAndLoadData("/getSystemInfo",t)}}const N={class:"guns-layout"},C={class:"guns-layout-content"},A={class:"guns-layout"},S={class:"guns-layout-content-application"},T={class:"content-mian"},V={class:"content-mian-body"},B={class:"table-content"},F={__name:"server",setup(r){const t=y({cpuColumns:[{title:"\u5C5E\u6027",dataIndex:"key"},{title:"\u503C",dataIndex:"value"}],memoryColumns:[{title:"\u5C5E\u6027",dataIndex:"key"},{title:"\u5185\u5B58",dataIndex:"value"},{title:"jvm",dataIndex:"jvm"}],diskColumns:[{title:"\u76D8\u7B26\u8DEF\u5F84",dataIndex:"dirName"},{title:"\u6587\u4EF6\u7CFB\u7EDF",dataIndex:"sysTypeName"},{title:"\u76D8\u7B26\u7C7B\u578B",dataIndex:"typeName"},{title:"\u603B\u5927\u5C0F",dataIndex:"total"},{title:"\u53EF\u7528\u5927\u5C0F",dataIndex:"free"},{title:"\u5DF2\u7528\u5927\u5C0F",dataIndex:"used"},{title:"\u5DF2\u7528\u767E\u5206\u6BD4",dataIndex:"usage"}],cpuData:[],memoryData:[],diskData:[],systemInfo:{cpu:{},mem:{},jvm:{},sys:{},sysFiles:[]}});return v(async()=>{let s=await w.getSystemInfo();t.systemInfo=s,t.cpuData[0]={key:"\u6838\u5FC3\u6570",value:s.cpu.cpuNum},t.cpuData[1]={key:"\u7528\u6237\u4F7F\u7528\u7387",value:s.cpu.used},t.cpuData[2]={key:"\u7CFB\u7EDF\u4F7F\u7528\u7387",value:s.cpu.sys},t.cpuData[3]={key:"\u5F53\u524D\u7A7A\u95F2\u7387",value:s.cpu.free},t.memoryData[0]={key:"\u603B\u5185\u5B58",value:s.mem.total,jvm:s.jvm.total},t.memoryData[1]={key:"\u5DF2\u7528\u5185\u5B58",value:s.mem.used,jvm:s.jvm.used},t.memoryData[2]={key:"\u5269\u4F59\u5185\u5B58",value:s.mem.free,jvm:s.jvm.free},t.memoryData[3]={key:"\u4F7F\u7528\u7387",value:s.mem.usage,jvm:s.jvm.usage}}),(s,M)=>{const _=k,m=g,d=x,u=j,o=h,i=D;return b(),I("div",N,[c("div",C,[c("div",A,[c("div",S,[c("div",T,[c("div",V,[c("div",B,[e(u,{class:"bottom-1"},{default:a(()=>[e(d,{xs:24,sm:24,md:12,class:"block-space-right"},{default:a(()=>[e(m,{title:"CPU\u4FE1\u606F",class:"card-width",bordered:!1},{default:a(()=>[e(_,{"row-key":"key",columns:t.cpuColumns,"data-source":t.cpuData,pagination:!1},null,8,["columns","data-source"])]),_:1})]),_:1}),e(d,{xs:24,sm:24,md:12,class:"block-space-left"},{default:a(()=>[e(m,{title:"\u5185\u5B58\u4FE1\u606F",class:"card-width",bordered:!1},{default:a(()=>[e(_,{"row-key":"key",columns:t.memoryColumns,"data-source":t.memoryData,pagination:!1},null,8,["columns","data-source"])]),_:1})]),_:1})]),_:1}),e(u,{class:"bottom-1"},{default:a(()=>[e(d,{span:24,class:"block-space-top"},{default:a(()=>[e(m,{title:"JAVA\u865A\u62DF\u673A\u4FE1\u606F",class:"card-width",bordered:!1},{default:a(()=>[e(i,{column:2},{default:a(()=>[e(o,{label:"jvm\u540D\u79F0",class:"b-r-1"},{default:a(()=>[l(n(t.systemInfo.jvm.name),1)]),_:1}),e(o,{label:"java\u7248\u672C"},{default:a(()=>[l(n(t.systemInfo.jvm.version),1)]),_:1}),e(o,{label:"\u542F\u52A8\u65F6\u95F4",class:"b-r-1"},{default:a(()=>[l(n(t.systemInfo.jvm.startTime),1)]),_:1}),e(o,{label:"\u8FD0\u884C\u65F6\u957F"},{default:a(()=>[l(n(t.systemInfo.jvm.runTime),1)]),_:1}),e(o,{label:"\u5B89\u88C5\u8DEF\u5F84",span:2},{default:a(()=>[l(n(t.systemInfo.jvm.home),1)]),_:1}),e(o,{label:"\u9879\u76EE\u8DEF\u5F84"},{default:a(()=>[l(n(t.systemInfo.sys.userDir),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(u,{class:"bottom-1"},{default:a(()=>[e(d,{span:24,class:"block-space-top"},{default:a(()=>[e(m,{title:"\u670D\u52A1\u5668\u4FE1\u606F",class:"card-width",bordered:!1},{default:a(()=>[e(i,{column:2},{default:a(()=>[e(o,{label:"\u670D\u52A1\u5668\u540D\u79F0",class:"b-r-1"},{default:a(()=>[l(n(t.systemInfo.sys.computerName),1)]),_:1}),e(o,{label:"\u64CD\u4F5C\u7CFB\u7EDF"},{default:a(()=>[l(n(t.systemInfo.sys.osName),1)]),_:1}),e(o,{label:"\u670D\u52A1\u5668IP",class:"b-r-1"},{default:a(()=>[l(n(t.systemInfo.sys.computerIp),1)]),_:1}),e(o,{label:"\u7CFB\u7EDF\u67B6\u6784"},{default:a(()=>[l(n(t.systemInfo.sys.osArch),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(u,{class:"bottom-1"},{default:a(()=>[e(d,{span:24,class:"block-space-top"},{default:a(()=>[e(m,{title:"\u78C1\u76D8\u4FE1\u606F",class:"card-width",bordered:!1},{default:a(()=>[e(_,{"row-key":"dirName",columns:t.diskColumns,scroll:{x:"max-content"},"data-source":t.systemInfo.sysFiles,pagination:!1},null,8,["columns","data-source"])]),_:1})]),_:1})]),_:1})])])])])])])])}}},J=f(F,[["__scopeId","data-v-440b7f5e"]]);export{J as default};
