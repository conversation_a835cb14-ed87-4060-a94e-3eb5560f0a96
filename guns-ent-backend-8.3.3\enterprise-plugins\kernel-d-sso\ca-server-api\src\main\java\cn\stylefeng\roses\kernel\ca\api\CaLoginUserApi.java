package cn.stylefeng.roses.kernel.ca.api;

import cn.stylefeng.roses.kernel.ca.api.exception.CaServerException;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;

/**
 * 统一认证中心的登录用户相关操作类
 * <p>
 * 在统一认证中心登录的用户会在浏览器端设置对应cookie
 *
 * <AUTHOR>
 * @date 2021/1/21 9:39
 */
public interface CaLoginUserApi {

    /**
     * 获取当前用户的统一认证中心的token（一般从cookie中获取）
     * <p>
     * 用户在单点登录之后，统一认证中心会把用户的token标识写入到cookie中
     *
     * <AUTHOR>
     * @date 2021/1/21 13:33
     */
    String getUserCaToken() throws CaServerException;

    /**
     * 获取CA统一认证中心的当前登录用户
     *
     * <AUTHOR>
     * @date 2021/1/21 16:48
     */
    CaLoginUser getLoginUser() throws CaServerException;

    /**
     * 暂存ssoLoginCode
     *
     * @param ssoLoginCode 校验账号密码成功后生成的随机字符串
     * @param caLoginUser  ca登录用户
     * <AUTHOR>
     * @date 2021/1/27 19:12
     */
    void stashSsoLoginCode(String ssoLoginCode, CaLoginUser caLoginUser);

    /**
     * 销毁暂存的ssoLoginCode
     *
     * @param ssoLoginCode 校验账号密码成功后生成的随机字符串
     * @return ca登录用户
     * <AUTHOR>
     * @date 2021/1/27 19:30
     */
    CaLoginUser destroySsoLoginCode(String ssoLoginCode);

}
