package cn.stylefeng.roses.kernel.pay.service;

import cn.stylefeng.roses.kernel.pay.entity.OrderInvoice;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderInvoiceRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 订单开票记录服务类
 *
 * <AUTHOR>
 * @since 2024/06/21 16:49
 */
public interface OrderInvoiceService extends IService<OrderInvoice> {

    /**
     * 新增订单开票记录
     *
     * @param orderInvoiceRequest 请求参数
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    void add(OrderInvoiceRequest orderInvoiceRequest);

    /**
     * 查询详情订单开票记录
     *
     * @param orderInvoiceRequest 请求参数
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    OrderInvoice detail(OrderInvoiceRequest orderInvoiceRequest);

    /**
     * 获取当前用户申请过的开票记录
     *
     * @param orderInvoiceRequest 请求参数
     * @return List<OrderInvoice>  返回结果
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    List<OrderInvoice> findList(OrderInvoiceRequest orderInvoiceRequest);

}
