package cn.stylefeng.roses.kernel.impexp.org.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 确认导入机构的请求参数
 *
 * <AUTHOR>
 * @since 2024/2/16 17:54
 */
@Data
public class EnsureImportOrgItem {

    /**
     * 业务操作类型：1-新增，2-修改，3-删除
     */
    @ChineseDescription("业务操作类型：1-新增，2-修改，3-删除")
    private Integer operateType;

    /**
     * 主键
     */
    @ChineseDescription("主键")
    private Long orgId;

    /**
     * 机构名称
     */
    @ChineseDescription("机构名称")
    private String orgName;

    /**
     * 机构简称
     */
    @ChineseDescription("机构简称")
    private String orgShortName;

    /**
     * 父级机构
     */
    @ChineseDescription("父级机构")
    private String parentOrgName;

    /**
     * 机构编码
     */
    @ChineseDescription("机构编码")
    private String orgCode;

    /**
     * 排序
     */
    @ChineseDescription("排序")
    private BigDecimal orgSort;

    /**
     * 状态：启用和禁用
     */
    @ChineseDescription("状态：启用和禁用")
    private Integer statusFlag;

    /**
     * 机构类型
     */
    @ChineseDescription("机构类型")
    private Integer orgType;

    /**
     * 税号
     */
    @ChineseDescription("税号")
    private String taxNo;

    /**
     * 描述
     */
    @ChineseDescription("描述")
    private String remark;

    /**
     * 对外主数据系统的机构id
     */
    @ChineseDescription("对外主数据系统的机构id")
    private String masterOrgId;

    //-------------------------------补充字段，用在处理数据时-------------------------------
    /**
     * 父级id
     */
    @ChineseDescription("父级id")
    private Long orgParentId;

    /**
     * 父ids
     */
    @ChineseDescription("父ids")
    private String orgPids;

}
