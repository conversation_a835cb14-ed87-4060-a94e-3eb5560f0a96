package cn.stylefeng.roses.kernel.manage.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.manage.entity.ApiEndpoint;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiEndpointRequest;
import cn.stylefeng.roses.kernel.manage.service.ApiEndpointService;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * API资源接口列表控制器
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@RestController
@ApiResource(name = "API资源接口列表")
public class ApiEndpointController {

    @Resource
    private ApiEndpointService apiEndpointService;

    /**
     * 获取API资源接口（带分页）
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @GetResource(name = "获取API资源接口（带分页）", path = "/apiEndpoint/page")
    public ResponseData<PageResult<ApiEndpoint>> page(ApiEndpointRequest apiEndpointRequest) {
        return new SuccessResponseData<>(apiEndpointService.findPage(apiEndpointRequest));
    }

    /**
     * 添加API资源接口
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "添加API资源接口", path = "/apiEndpoint/add")
    public ResponseData<ApiEndpoint> add(@RequestBody @Validated(ApiEndpointRequest.add.class) ApiEndpointRequest apiEndpointRequest) {
        apiEndpointService.add(apiEndpointRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除API资源接口
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "删除API资源接口", path = "/apiEndpoint/delete")
    public ResponseData<?> delete(@RequestBody @Validated(ApiEndpointRequest.delete.class) ApiEndpointRequest apiEndpointRequest) {
        apiEndpointService.del(apiEndpointRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除API资源接口
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "批量删除API资源接口", path = "/apiEndpoint/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody @Validated(BaseRequest.batchDelete.class) ApiEndpointRequest apiEndpointRequest) {
        apiEndpointService.batchDelete(apiEndpointRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑API资源接口
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "编辑API资源接口", path = "/apiEndpoint/edit")
    public ResponseData<?> edit(@RequestBody @Validated(ApiEndpointRequest.edit.class) ApiEndpointRequest apiEndpointRequest) {
        apiEndpointService.edit(apiEndpointRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看API资源接口详情
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @GetResource(name = "查看API资源接口详情", path = "/apiEndpoint/detail")
    public ResponseData<ApiEndpoint> detail(@Validated(ApiEndpointRequest.detail.class) ApiEndpointRequest apiEndpointRequest) {
        return new SuccessResponseData<>(apiEndpointService.detail(apiEndpointRequest));
    }

    /**
     * 模糊搜索接口路径对应的API接口下拉列表
     *
     * <AUTHOR>
     * @since 2023/10/24 23:36
     */
    @GetResource(name = "模糊搜索接口路径对应的API接口下拉列表", path = "/apiEndpoint/getUrlSelectList")
    public ResponseData<List<ApiEndpoint>> getUrlSelectList(ApiEndpointRequest apiEndpointRequest) {
        return new SuccessResponseData<>(apiEndpointService.getUrlSelectList(apiEndpointRequest));
    }

}
