import{_ as fe,r as S,L as ge,o as ye,a as _,c as D,at as p,b as k,t as j,f as b,P as ve,h as L,d as O,w as B,I as N,aR as $,aM as U,F as me,e as _e,aS as Y,m as Z,l as Ce,n as we,aO as Ee,a5 as Re,S as xe}from"./index-18a1ea24.js";/* empty css              */const A={DATA_LOAD_ERROR:"DATA_LOAD_ERROR",LAZY_LOAD_ERROR:"LAZY_LOAD_ERROR",SEARCH_ERROR:"SEARCH_ERROR",ACTION_ERROR:"ACTION_ERROR",CONFIG_ERROR:"CONFIG_ERROR"};class R{static formatTreeData(a,n){return!a||!a.length?[]:a.map(l=>({...l,key:String(l[n.key]),title:l[n.title],children:l[n.children]?this.formatTreeData(l[n.children],n):[],isLeaf:n.hasChildren?!l[n.hasChildren]:!l[n.children]||l[n.children].length===0}))}static extractAllIds(a){const n=[],l=s=>{s.forEach(u=>{n.push(u.key),u.children&&u.children.length>0&&l(u.children)})};return l(a),n}static extractNodeIdsByLevel(a,n,l){const s=[],u=d=>{d.forEach(i=>{const h=i[l.level||"level"]||1;h<=n&&s.push(i.key),i.children&&i.children.length>0&&h<n&&u(i.children)})};return u(a),s}static setIsLeaf(a,n,l){return!a||!a.length?[]:a.map(s=>{if(s[n.key]=String(s[n.key]),n.hasChildren&&s[n.hasChildren]!==void 0)s.isLeaf=!s[n.hasChildren];else{const u=s[n.children]&&s[n.children].length>0,d=s[n.level||"level"]||1,i=l?d>=l:!1;s.isLeaf=!u&&i}return s[n.children]&&s[n.children].length>0&&(s[n.children]=this.setIsLeaf(s[n.children],n,l)),s})}static searchTreeNodes(a,n,l){if(!n||!n.trim())return a;const s=n.toLowerCase().trim(),u=[],d=i=>{const v=(i[l.title]||"").toLowerCase().includes(s);let g=[];return i.children&&i.children.length>0&&(g=i.children.map(d).filter(Boolean)),v||g.length>0?{...i,children:g}:null};return a.forEach(i=>{const h=d(i);h&&u.push(h)}),u}static getNodePath(a,n,l){const s=[],u=(d,i,h)=>{for(const v of d){const g=[...h,v];if(String(v[l.key])===String(i))return s.push(...g),!0;if(v.children&&v.children.length>0&&u(v.children,i,g))return!0}return!1};return u(a,n,[]),s}static getParentKeys(a,n,l){return this.getNodePath(a,n,l).slice(0,-1).map(u=>String(u[l.key]))}static expandToNode(a,n,l){return this.getParentKeys(a,n,l)}static validateConfig(a){const n=[],l=[];return(!a.dataSource||!a.dataSource.api)&&n.push("dataSource.api \u662F\u5FC5\u9700\u7684"),a.fieldMapping?(a.fieldMapping.key||n.push("fieldMapping.key \u662F\u5FC5\u9700\u7684"),a.fieldMapping.title||n.push("fieldMapping.title \u662F\u5FC5\u9700\u7684"),a.fieldMapping.children||n.push("fieldMapping.children \u662F\u5FC5\u9700\u7684")):n.push("fieldMapping \u662F\u5FC5\u9700\u7684"),a.displayConfig||l.push("\u5EFA\u8BAE\u63D0\u4F9B displayConfig \u914D\u7F6E"),a.interactionConfig||l.push("\u5EFA\u8BAE\u63D0\u4F9B interactionConfig \u914D\u7F6E"),{isValid:n.length===0,errors:n,warnings:l}}}class G{static handleError(a,n){switch(a.type){case A.DATA_LOAD_ERROR:return this.handleDataLoadError(a,n);case A.LAZY_LOAD_ERROR:return this.handleLazyLoadError(a,n);case A.SEARCH_ERROR:return this.handleSearchError(a,n);case A.ACTION_ERROR:return this.handleActionError(a,n);case A.CONFIG_ERROR:return this.handleConfigError(a,n);default:return this.handleUnknownError(a,n)}}static handleDataLoadError(a,n){return console.error("\u6811\u6570\u636E\u52A0\u8F7D\u5931\u8D25:",a),{showRetry:!0,message:"\u6570\u636E\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"}}static handleLazyLoadError(a,n){return console.error("\u61D2\u52A0\u8F7D\u5931\u8D25:",a),{showNodeRetry:!0,message:"\u5B50\u8282\u70B9\u52A0\u8F7D\u5931\u8D25"}}static handleSearchError(a,n){return console.error("\u641C\u7D22\u5931\u8D25:",a),{clearSearch:!0,message:"\u641C\u7D22\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"}}static handleActionError(a,n){return console.error("\u64CD\u4F5C\u5931\u8D25:",a),{message:a.message||"\u64CD\u4F5C\u5931\u8D25"}}static handleConfigError(a,n){return console.error("\u914D\u7F6E\u9519\u8BEF:",a),{message:"\u7EC4\u4EF6\u914D\u7F6E\u9519\u8BEF"}}static handleUnknownError(a,n){return console.error("\u672A\u77E5\u9519\u8BEF:",a),{message:"\u53D1\u751F\u672A\u77E5\u9519\u8BEF"}}}function ke(){return{dataSource:{api:null,lazyLoadApi:null,searchParam:"searchText",parentIdParam:"parentId"},fieldMapping:{key:"id",title:"name",children:"children",hasChildren:"hasChildren",level:"level"},displayConfig:{title:"",showHeader:!0,showSearch:!0,searchPlaceholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57\u641C\u7D22",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0},interactionConfig:{selectable:!0,expandable:!0,lazyLoad:!1,defaultExpandLevel:1,allowMultiSelect:!1},actionConfig:{allowAdd:!1,allowEdit:!1,allowDelete:!1,customActions:[]}}}class Fe{constructor(){this.config=ke()}setDataSource(a,n=null,l="searchText",s="parentId"){return this.config.dataSource={api:a,lazyLoadApi:n,searchParam:l,parentIdParam:s},this}setFieldMapping(a,n,l,s=null,u=null){return this.config.fieldMapping={key:a,title:n,children:l,hasChildren:s,level:u},this}setDisplayConfig(a){return this.config.displayConfig={...this.config.displayConfig,...a},this}setInteractionConfig(a){return this.config.interactionConfig={...this.config.interactionConfig,...a},this}setActionConfig(a){return this.config.actionConfig={...this.config.actionConfig,...a},this}enableReadOnlyMode(){return this.config.displayConfig.showAddButton=!1,this.config.displayConfig.showEditIcons=!1,this.config.actionConfig.allowAdd=!1,this.config.actionConfig.allowEdit=!1,this.config.actionConfig.allowDelete=!1,this}enableEditMode(){return this.config.displayConfig.showAddButton=!0,this.config.displayConfig.showEditIcons=!0,this.config.actionConfig.allowAdd=!0,this.config.actionConfig.allowEdit=!0,this.config.actionConfig.allowDelete=!0,this}build(){const a=R.validateConfig(this.config);return a.isValid||console.warn("\u914D\u7F6E\u9A8C\u8BC1\u5931\u8D25:",a.errors),a.warnings.length>0&&console.warn("\u914D\u7F6E\u8B66\u544A:",a.warnings),{...this.config}}}const Ae={class:"box bgColor box-shadow"},Se={key:0,class:"left-header"},De={class:"left-header-title"},Le={key:1,class:"search"},Oe={class:"tree-content"},Te={class:"left-tree"},Ie={key:0,class:"tree-edit"},Ke=["title"],be={class:"edit-icon"},Ne={key:1,class:"not-tree-edit"},Pe=["title"],Be=Object.assign({name:"UniversalTree"},{__name:"UniversalTree",props:{dataSource:{type:Object,required:!0},fieldMapping:{type:Object,required:!0},displayConfig:{type:Object,default:()=>({showHeader:!0,showSearch:!0,showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0})},interactionConfig:{type:Object,default:()=>({selectable:!0,expandable:!0,lazyLoad:!1,defaultExpandLevel:2,allowMultiSelect:!1})},actionConfig:{type:Object,default:()=>({allowAdd:!1,allowEdit:!1,allowDelete:!1,customActions:[]})}},emits:["select","expand","search","add","edit","delete","customAction","load","loadError","nodeClick","nodeDoubleClick"],setup(P,{expose:a,emit:n}){const l=P,s=n,u=S(!1),d=S([]),i=S([]),h=S([]),v=S([]),g=S(""),W=ge(()=>({children:l.fieldMapping.children,title:l.fieldMapping.title,key:l.fieldMapping.key,value:l.fieldMapping.key})),{dataSource:x,fieldMapping:r,displayConfig:C,interactionConfig:T,actionConfig:w}=l,I=async()=>{if(!x.api){console.error("\u6570\u636E\u6E90API\u672A\u914D\u7F6E");return}try{u.value=!0;const t={};g.value&&g.value.trim()&&x.searchParam&&(t[x.searchParam]=g.value.trim());const e=await x.api(t),o=(e==null?void 0:e.data)||e||[],c=R.setIsLeaf(o,r);d.value=R.formatTreeData(c,r),g.value&&g.value.trim()?h.value=R.extractAllIds(d.value):h.value.length===0&&T.defaultExpandLevel&&(h.value=R.extractNodeIdsByLevel(d.value,T.defaultExpandLevel,r)),s("load",d.value)}catch(t){console.error("\u52A0\u8F7D\u6811\u6570\u636E\u5931\u8D25:",t),d.value=[];const e={type:A.DATA_LOAD_ERROR,message:"\u6570\u636E\u52A0\u8F7D\u5931\u8D25",originalError:t},o=G.handleError(e,{searchText:g.value});Z.error(o.message),s("loadError",t)}finally{u.value=!1}},q=async t=>!x.lazyLoadApi||!x.parentIdParam||v.value.includes(t.eventKey)?Promise.resolve():(v.value.push(t.eventKey),new Promise(e=>{const o={};o[x.parentIdParam]=t.dataRef[r.key],x.lazyLoadApi(o).then(c=>{const f=(c==null?void 0:c.data)||c||[],m=R.setIsLeaf(f,r),K=R.formatTreeData(m,r);t.dataRef&&(t.dataRef[r.children]=K,d.value=[...d.value])}).catch(c=>{console.error("\u61D2\u52A0\u8F7D\u5931\u8D25:",c);const f=v.value.indexOf(t.eventKey);f>-1&&v.value.splice(f,1);const m={type:A.LAZY_LOAD_ERROR,message:"\u5B50\u8282\u70B9\u52A0\u8F7D\u5931\u8D25",originalError:c},K=G.handleError(m,{nodeKey:t.eventKey});Z.error(K.message)}).finally(()=>{e()})})),J=(t,e)=>{i.value=t;const c=(e.selectedNodes||[]).map(f=>f.dataRef||f);s("select",{keys:t,nodes:c,event:e})},Q=(t,e)=>{var f;h.value=t;const c=((e==null?void 0:e.expandedNodes)||[]).map(m=>m.dataRef||m);s("expand",{keys:t,nodes:c,expanded:e==null?void 0:e.expanded,node:((f=e==null?void 0:e.node)==null?void 0:f.dataRef)||(e==null?void 0:e.node),event:e})},X=()=>{var e;const t=((e=g.value)==null?void 0:e.trim())||"";s("search",{searchText:t,isSearching:!!t,timestamp:Date.now()}),I()},ee=()=>{var e;((e=g.value)==null?void 0:e.trim())||""||(s("search",{searchText:"",isSearching:!1,timestamp:Date.now()}),I())},z=(t=null)=>{s("add",{parentNode:t||null,action:"add",timestamp:Date.now(),context:{isRootAdd:!t,parentKey:t?t[r.key]:null,parentTitle:t?t[r.title]:null}})},te=t=>{var e;if(!t){console.warn("\u7F16\u8F91\u64CD\u4F5C\u7F3A\u5C11\u8282\u70B9\u6570\u636E");return}s("edit",{node:t,action:"edit",timestamp:Date.now(),context:{nodeKey:t[r.key],nodeTitle:t[r.title],hasChildren:((e=t[r.children])==null?void 0:e.length)>0}})},ae=t=>{var e;if(!t){console.warn("\u5220\u9664\u64CD\u4F5C\u7F3A\u5C11\u8282\u70B9\u6570\u636E");return}s("delete",{node:t,action:"delete",timestamp:Date.now(),context:{nodeKey:t[r.key],nodeTitle:t[r.title],hasChildren:((e=t[r.children])==null?void 0:e.length)>0,isSelected:i.value.includes(String(t[r.key]))}})},ne=(t,e)=>{var c,f;if(!t||!e){console.warn("\u81EA\u5B9A\u4E49\u64CD\u4F5C\u7F3A\u5C11\u5FC5\u8981\u53C2\u6570");return}const o=(c=w==null?void 0:w.customActions)==null?void 0:c.find(m=>m.key===t);s("customAction",{actionKey:t,node:e,action:"custom",timestamp:Date.now(),context:{nodeKey:e[r.key],nodeTitle:e[r.title],actionConfig:o,hasChildren:((f=e[r.children])==null?void 0:f.length)>0}})},se=()=>{I()},le=()=>i.value,F=t=>{i.value=t},V=t=>{const e=[...new Set([...h.value,...t])];h.value=e,s("expand",{keys:e,nodes:[],expanded:!0,programmatic:!0,timestamp:Date.now()})},re=t=>{const e=h.value.filter(o=>!t.includes(o));h.value=e,s("expand",{keys:e,nodes:[],expanded:!1,programmatic:!0,timestamp:Date.now()})},oe=()=>{i.value=[],s("select",{keys:[],nodes:[],cleared:!0,programmatic:!0,timestamp:Date.now()})},ie=()=>{const t=R.extractAllIds(d.value);h.value=t,s("expand",{keys:t,nodes:[],expanded:!0,expandAll:!0,programmatic:!0,timestamp:Date.now()})},ce=()=>{h.value=[],s("expand",{keys:[],nodes:[],expanded:!1,collapseAll:!0,programmatic:!0,timestamp:Date.now()})},de=t=>{const e=(o,c)=>{for(const f of o){if(String(f[r.key])===String(c))return f;if(f[r.children]&&f[r.children].length>0){const m=e(f[r.children],c);if(m)return m}}return null};return e(d.value,t)},H=t=>{const e=R.getParentKeys(d.value,t,r);e.length>0&&V(e)},ue=t=>{H(t),setTimeout(()=>{F([String(t)])},100)},he=(t,e)=>{var c;if(!e)return;const o=e.dataRef||e;s("nodeClick",{node:o,event:t,timestamp:Date.now(),context:{nodeKey:o[r.key],nodeTitle:o[r.title],hasChildren:((c=o[r.children])==null?void 0:c.length)>0,isSelected:i.value.includes(String(o[r.key])),isExpanded:h.value.includes(String(o[r.key]))}})},pe=(t,e)=>{var c;if(!e)return;const o=e.dataRef||e;s("nodeDoubleClick",{node:o,event:t,timestamp:Date.now(),context:{nodeKey:o[r.key],nodeTitle:o[r.title],hasChildren:((c=o[r.children])==null?void 0:c.length)>0,isSelected:i.value.includes(String(o[r.key])),isExpanded:h.value.includes(String(o[r.key]))}})};return a({reload:se,loadTreeData:I,getSelectedNodes:le,setSelectedKeys:F,clearSelection:oe,expandNodes:V,collapseNodes:re,expandAll:ie,collapseAll:ce,expandToNode:H,selectAndExpandToNode:ue,findNodeByKey:de,getTreeData:()=>d.value,getExpandedKeys:()=>h.value,getLoadedKeys:()=>v.value,getSearchText:()=>g.value,isLoading:()=>u.value}),ye(()=>{I()}),(t,e)=>{var M;const o=Ce,c=we,f=Ee,m=Re,K=xe;return _(),D("div",Ae,[p(C).showHeader&&p(C).isSetWidth?(_(),D("div",Se,[k("span",De,j(p(C).title||""),1),k("span",null,[p(C).showAddButton&&((M=p(w))!=null&&M.allowAdd)?(_(),b(p(ve),{key:0,class:"header-add",onClick:e[0]||(e[0]=y=>z()),title:"\u65B0\u589E"+(p(C).title||"\u8282\u70B9")},null,8,["title"])):L("",!0)])])):L("",!0),p(C).showSearch?(_(),D("div",Le,[O(o,{value:g.value,"onUpdate:value":e[1]||(e[1]=y=>g.value=y),placeholder:p(C).searchPlaceholder||"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD\u641C\u7D22","allow-clear":"",onPressEnter:X,onChange:ee},{prefix:B(()=>[O(N,{iconClass:"icon-opt-search"})]),_:1},8,["value","placeholder"])])):L("",!0),k("div",Oe,[O(K,{tip:"Loading...",spinning:u.value,delay:100},{default:B(()=>[$(k("div",Te,[O(f,{selectedKeys:i.value,"onUpdate:selectedKeys":e[2]||(e[2]=y=>i.value=y),expandedKeys:h.value,"onUpdate:expandedKeys":e[3]||(e[3]=y=>h.value=y),loadedKeys:v.value,"onUpdate:loadedKeys":e[4]||(e[4]=y=>v.value=y),onSelect:J,onExpand:Q,onClick:he,onDblclick:pe,"load-data":p(T).lazyLoad?q:void 0,"tree-data":d.value,"show-icon":p(C).showIcon,multiple:p(T).allowMultiSelect,selectable:p(T).selectable,"field-names":W.value},{title:B(y=>[p(C).showEditIcons&&p(w)?(_(),D("span",Ie,[k("span",{class:"edit-title",title:y[p(r).title]||""},j(y[p(r).title]||""),9,Ke),k("span",be,[O(c,null,{default:B(()=>[p(w).allowAdd?(_(),b(N,{key:0,iconClass:"icon-opt-tianjia",color:"var(--primary-color)",title:"\u65B0\u589E\u5B50\u8282\u70B9",onClick:U(E=>z(y),["stop"])},null,8,["onClick"])):L("",!0),p(w).allowEdit?(_(),b(N,{key:1,iconClass:"icon-opt-bianji",color:"var(--primary-color)",title:"\u7F16\u8F91\u8282\u70B9",onClick:U(E=>te(y),["stop"])},null,8,["onClick"])):L("",!0),p(w).allowDelete?(_(),b(N,{key:2,iconClass:"icon-opt-shanchu",color:"red",title:"\u5220\u9664\u8282\u70B9",onClick:U(E=>ae(y),["stop"])},null,8,["onClick"])):L("",!0),(_(!0),D(me,null,_e(p(w).customActions||[],E=>(_(),b(N,{key:E.key,iconClass:E.icon,color:E.color||"var(--primary-color)",title:E.title||E.key,onClick:U(Ue=>ne(E.key,y),["stop"])},null,8,["iconClass","color","title","onClick"]))),128))]),_:2},1024)])])):(_(),D("span",Ne,[k("span",{class:"edit-title",title:y[p(r).title]||""},j(y[p(r).title]||""),9,Pe)]))]),_:1},8,["selectedKeys","expandedKeys","loadedKeys","load-data","tree-data","show-icon","multiple","selectable","field-names"])],512),[[Y,d.value&&d.value.length>0]]),$(O(m,{class:"empty"},null,512),[[Y,d.value&&d.value.length===0]])]),_:1},8,["spinning"])])])}}}),Ve=fe(Be,[["__scopeId","data-v-f00d3699"]]);export{Fe as T,Ve as U};
