# ERP模块

## 模块介绍

ERP（Enterprise Resource Planning）企业资源规划模块是Guns企业版的核心业务模块，提供完整的基础数据管理功能：

- **供应商管理**：供应商档案维护、状态管理、信用等级管理
- **客户管理**：客户档案维护、客户等级管理、信用额度管理
- **商品管理**：商品档案维护、分类管理、库存单位管理
- **区域管理**：地理区域树形结构管理、层级管理

该模块采用标准的三层架构设计：

- **erp-api**: 定义接口、实体类、请求响应对象
- **erp-business**: 实现具体的业务逻辑（采用模块化结构）
- **erp-spring-boot-starter**: 提供自动配置

## 🎉 系统集成完成状态

✅ **代码结构重组** - 已完成
- 业务代码已从 `business/` 重组为 `modular/` 结构
- 每个业务模块独立包结构：supplier、customer、product、region
- 所有import语句已更新为jakarta.*

✅ **服务实现文件** - 已完成
- ErpSupplierServiceImpl.java - 供应商管理服务实现
- ErpCustomerServiceImpl.java - 客户管理服务实现
- ErpProductServiceImpl.java - 商品管理服务实现
- ErpRegionServiceImpl.java - 区域管理服务实现

✅ **系统集成设置** - 已完成
- 数据库迁移脚本：
- 菜单配置：ERP管理主菜单及各子模块菜单
- 权限配置：完整的功能操作权限设置
- 模块注册：Spring Boot自动配置和依赖注入

## 模块结构

```
kernel-d-erp/
├── erp-api/                           # API模块
│   └── src/main/java/cn/stylefeng/roses/kernel/erp/api/
│       ├── constants/                 # 常量定义
│       ├── exception/                 # 异常定义
│       └── pojo/                      # 请求响应对象
│           ├── entity/                # 实体类
│           ├── request/               # 请求对象
│           └── response/              # 响应对象
├── erp-business/                      # 业务模块（模块化结构）
│   └── src/main/java/cn/stylefeng/roses/kernel/erp/modular/
│       ├── supplier/                  # 供应商模块
│       │   ├── controller/            # 供应商控制器
│       │   ├── mapper/                # 供应商Mapper
│       │   └── service/               # 供应商服务
│       ├── customer/                  # 客户模块
│       │   ├── controller/            # 客户控制器
│       │   ├── mapper/                # 客户Mapper
│       │   └── service/               # 客户服务
│       ├── product/                   # 商品模块
│       │   ├── controller/            # 商品控制器
│       │   ├── mapper/                # 商品Mapper
│       │   └── service/               # 商品服务
│       └── region/                    # 区域模块
│           ├── controller/            # 区域控制器
│           ├── mapper/                # 区域Mapper
│           └── service/               # 区域服务
└── erp-spring-boot-starter/           # 自动配置模块
    └── src/main/
        ├── java/cn/stylefeng/roses/kernel/erp/starter/
        │   ├── ErpAutoConfiguration.java    # 自动配置类
        │   └── ErpModuleConfig.java         # 模块配置类
        └── resources/META-INF/
            └── spring.factories
```

## 集成方式

### 1. 在主项目中添加依赖

在需要使用ERP模块的项目的`pom.xml`中添加以下依赖：

```xml
<dependency>
    <groupId>com.javaguns.ent</groupId>
    <artifactId>erp-spring-boot-starter</artifactId>
    <version>8.3.3</version>
</dependency>
```

### 2. 数据库初始化

执行以下SQL脚本初始化ERP模块：

```sql
-- 1. 执行ERP表结构创建脚本

-- 2. 执行系统集成配置脚本
```

### 3. 模块配置

在 `application.yml` 中添加ERP模块配置：

```yaml
roses:
  erp:
    enabled: true                    # 启用ERP模块
    module-name: "ERP企业资源规划"    # 模块名称
    data-permission-enabled: true    # 启用数据权限控制
    operate-log-enabled: true        # 启用操作日志记录
    cache-enabled: true              # 启用缓存
```

### 4. 自动配置

ERP模块支持Spring Boot自动配置，添加依赖后会自动装配相关组件。

## API接口说明

### 供应商管理接口

| 接口路径 | 请求方式 | 功能描述 |
|---------|---------|---------|
| `/erp/supplier/add` | POST | 新增供应商 |
| `/erp/supplier/edit` | POST | 修改供应商 |
| `/erp/supplier/delete` | POST | 删除供应商 |
| `/erp/supplier/page` | GET | 分页查询供应商列表 |
| `/erp/supplier/detail` | GET | 查询供应商详情 |
| `/erp/supplier/updateStatus` | POST | 更新供应商状态 |

### 客户管理接口

| 接口路径 | 请求方式 | 功能描述 |
|---------|---------|---------|
| `/erp/customer/add` | POST | 新增客户 |
| `/erp/customer/edit` | POST | 修改客户 |
| `/erp/customer/delete` | POST | 删除客户 |
| `/erp/customer/page` | GET | 分页查询客户列表 |
| `/erp/customer/detail` | GET | 查询客户详情 |
| `/erp/customer/updateStatus` | POST | 更新客户状态 |

### 商品管理接口

| 接口路径 | 请求方式 | 功能描述 |
|---------|---------|---------|
| `/erp/product/add` | POST | 新增商品 |
| `/erp/product/edit` | POST | 修改商品 |
| `/erp/product/delete` | POST | 删除商品 |
| `/erp/product/page` | GET | 分页查询商品列表 |
| `/erp/product/detail` | GET | 查询商品详情 |
| `/erp/product/updateStatus` | POST | 更新商品状态 |

### 区域管理接口

| 接口路径 | 请求方式 | 功能描述 |
|---------|---------|---------|
| `/erp/region/add` | POST | 新增区域 |
| `/erp/region/edit` | POST | 修改区域 |
| `/erp/region/delete` | POST | 删除区域 |
| `/erp/region/tree` | GET | 查询区域树形结构 |
| `/erp/region/detail` | GET | 查询区域详情 |
| `/erp/region/updateStatus` | POST | 更新区域状态 |

## 使用示例

### 1. 注入ERP服务

```java
@Resource
private ErpApi erpApi;

public void testErp() {
    String result = erpApi.testErpModule();
    System.out.println(result);
}
```

### 2. 调用测试接口

启动应用后，可以通过以下URL测试模块是否正常工作：

```
GET http://localhost:8080/erp/test
GET http://localhost:8080/erp/version
```

## 扩展开发

### 1. 添加新的API接口

在`erp-api`模块中的`ErpApi`接口中添加新方法：

```java
public interface ErpApi {
    // 现有方法...
    
    /**
     * 新增的业务方法
     */
    String newBusinessMethod();
}
```

### 2. 实现业务逻辑

在`erp-business`模块中的`ErpServiceImpl`类中实现新方法：

```java
@Override
public String newBusinessMethod() {
    // 实现具体业务逻辑
    return "新业务方法执行成功";
}
```

### 3. 添加控制器接口

在`ErpTestController`中添加新的REST接口：

```java
@GetResource(name = "新业务接口", path = "/erp/newBusiness")
public ResponseData<String> newBusiness() {
    String result = erpApi.newBusinessMethod();
    return new SuccessResponseData<>(result);
}
```

## 注意事项

1. 确保项目中已经包含了Guns框架的基础依赖
2. ERP模块依赖于系统管理模块和数据库操作模块
3. 所有的控制器都使用了`@ApiResource`和`@GetResource`注解进行资源扫描
4. 响应数据统一使用`ResponseData`和`SuccessResponseData`进行封装

## 版本信息

- 当前版本：8.3.3
- 兼容的Guns版本：8.3.3
- Spring Boot版本：3.2.10
