import{R as e}from"./index-18a1ea24.js";class u{static add(t){return e.post("/purchase/order/add",t)}static delete(t){return e.post("/purchase/order/delete",t)}static edit(t){return e.post("/purchase/order/edit",t)}static detail(t){return e.getAndLoadData("/purchase/order/detail",t)}static findPage(t){return e.getAndLoadData("/purchase/order/page",t)}static findList(t){return e.getAndLoadData("/purchase/order/list",t)}static confirm(t){return e.post("/purchase/order/confirm",t)}static receive(t){return e.post("/purchase/order/receive",t)}static generateOrderNo(){return e.getAndLoadData("/purchase/order/generateOrderNo")}static validateSupplierBusinessMode(t){return e.getAndLoadData("/purchase/order/validateSupplierBusinessMode",t)}static cancel(t){return e.post("/erp/purchase/cancel",t)}static getPurchaseStatusOptions(){return[{label:"\u8349\u7A3F",value:"DRAFT"},{label:"\u5DF2\u786E\u8BA4",value:"CONFIRMED"},{label:"\u5DF2\u5B8C\u6210",value:"COMPLETED"}]}static getPurchaseTypeOptions(){return[{label:"\u666E\u901A\u91C7\u8D2D",value:"NORMAL"},{label:"\u7D27\u6025\u91C7\u8D2D",value:"URGENT"},{label:"\u8865\u8D27\u91C7\u8D2D",value:"REPLENISHMENT"}]}static getPurchaseStatusName(t){const r=u.getPurchaseStatusOptions().find(s=>s.value===t);return r?r.label:t}static getPurchaseTypeName(t){const r=u.getPurchaseTypeOptions().find(s=>s.value===t);return r?r.label:t}static getStatusTagColor(t){switch(t){case"DRAFT":return"orange";case"CONFIRMED":return"blue";case"COMPLETED":return"green";default:return"default"}}static getPurchaseTypeTagColor(t){switch(t){case"NORMAL":return"blue";case"URGENT":return"red";case"REPLENISHMENT":return"green";default:return"default"}}static validateConfirm(t){return e.getAndLoadData("/erp/purchase/validateConfirm",t)}static validateInbound(t){return e.getAndLoadData("/erp/purchase/validateInbound",t)}static getPurchaseItems(t){return e.getAndLoadData("/erp/purchase/items",t)}static addPurchaseItem(t){return e.post("/erp/purchase/addItem",t)}static editPurchaseItem(t){return e.post("/erp/purchase/editItem",t)}static deletePurchaseItem(t){return e.post("/erp/purchase/deleteItem",t)}static batchImportItems(t){return e.post("/erp/purchase/batchImportItems",t)}static exportPurchase(t){return e.downLoad("/erp/purchase/export",t)}static getPurchaseStatistics(t){return e.getAndLoadData("/erp/purchase/statistics",t)}static formatAmount(t){return t?parseFloat(t).toFixed(2):"0.00"}static formatQuantity(t,a){return t?"".concat(t).concat(a||""):"-"}static calculateTotalAmount(t){return!t||t.length===0?0:t.reduce((a,r)=>a+r.quantity*r.unitPrice,0)}static calculateTotalQuantity(t){return!t||t.length===0?0:t.reduce((a,r)=>a+r.quantity,0)}}export{u as P};
