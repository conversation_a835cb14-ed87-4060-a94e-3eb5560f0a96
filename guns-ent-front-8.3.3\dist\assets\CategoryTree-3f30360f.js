import{U as w}from"./UniversalTree-6547889b.js";import{P as n}from"./productCategoryApi-39e417fd.js";import{_ as E,r as I,L as l,a as N,f as z}from"./index-18a1ea24.js";/* empty css              */const P=Object.assign({name:"CategoryTree"},{__name:"CategoryTree",props:{lazyLoad:{type:Boolean,default:!0}},emits:["select"],setup(s,{expose:r,emit:c}){const d=s,o=c,a=I(),i={api:n.findTree,lazyLoadApi:n.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},f={key:"categoryId",title:"categoryName",children:"children",hasChildren:"hasChildren",level:"categoryLevel"},p={title:"\u4EA7\u54C1\u5206\u7C7B",showHeader:!1,showSearch:!0,searchPlaceholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0\u641C\u7D22",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0},u=l(()=>({selectable:!0,expandable:!0,lazyLoad:d.lazyLoad,defaultExpandLevel:2,allowMultiSelect:!1})),h={allowAdd:!1,allowEdit:!1,allowDelete:!1},g=(e,t)=>{if(e.length>0){const T=t[0];o("select",e[0],T.categoryName)}else o("select",null)},y=e=>{},_=e=>{},m=e=>{},v=e=>{console.error("\u4EA7\u54C1\u5206\u7C7B\u6811\u6570\u636E\u52A0\u8F7D\u5931\u8D25:",e)},S=()=>{var e;(e=a.value)==null||e.reload()},x=()=>{var e;return(e=a.value)==null?void 0:e.getSelectedNodes()},C=e=>{var t;(t=a.value)==null||t.setSelectedKeys(e)},L=l(()=>{var e;return((e=a.value)==null?void 0:e.getSelectedNodes())||[]});return r({reload:S,getSelectedNodes:x,setSelectedKeys:C,selectedKeys:L}),(e,t)=>(N(),z(w,{ref_key:"universalTreeRef",ref:a,"data-source":i,"field-mapping":f,"display-config":p,"interaction-config":u.value,"action-config":h,onSelect:g,onExpand:y,onSearch:_,onLoad:m,onLoadError:v},null,8,["interaction-config"]))}}),K=E(P,[["__scopeId","data-v-32a083d3"]]);export{K as default};
