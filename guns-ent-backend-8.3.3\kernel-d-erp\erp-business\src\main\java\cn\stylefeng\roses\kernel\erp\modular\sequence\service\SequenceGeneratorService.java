package cn.stylefeng.roses.kernel.erp.modular.sequence.service;

/**
 * 序列号生成器Service接口
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
public interface SequenceGeneratorService {

    /**
     * 生成下一个序列号
     *
     * @param sequenceName 序列名称
     * @return 生成的序列号
     */
    String generateNextSequence(String sequenceName);

    /**
     * 生成带日期格式的序列号
     *
     * @param sequenceName 序列名称
     * @return 生成的序列号
     */
    String generateDateSequence(String sequenceName);

    /**
     * 检查并重置序列（如果需要）
     *
     * @param sequenceName 序列名称
     */
    void checkAndResetSequence(String sequenceName);

    /**
     * 手动重置序列
     *
     * @param sequenceName 序列名称
     * @param resetValue 重置值
     */
    void resetSequence(String sequenceName, Long resetValue);

    /**
     * 获取当前序列值
     *
     * @param sequenceName 序列名称
     * @return 当前序列值
     */
    Long getCurrentValue(String sequenceName);

}
