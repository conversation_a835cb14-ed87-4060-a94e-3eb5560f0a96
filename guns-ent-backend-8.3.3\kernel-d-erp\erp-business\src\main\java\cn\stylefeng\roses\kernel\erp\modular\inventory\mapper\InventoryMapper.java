package cn.stylefeng.roses.kernel.erp.modular.inventory.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
public interface InventoryMapper extends BaseMapper<Inventory> {

    /**
     * 根据商品ID查询库存信息
     *
     * @param productId 商品ID
     * @return 库存信息
     */
    @Select("SELECT * FROM erp_inventory WHERE product_id = #{productId}")
    Inventory getByProductId(@Param("productId") Long productId);

    /**
     * 分页查询库存列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 库存列表
     */
    Page<InventoryResponse> selectInventoryPage(Page<InventoryResponse> page, @Param("request") InventoryQueryRequest request);

    /**
     * 查询预警库存列表
     *
     * @param request 查询条件
     * @return 预警库存列表
     */
    List<InventoryResponse> selectWarningInventory(@Param("request") InventoryQueryRequest request);

    /**
     * 查询缺货商品列表
     *
     * @param request 查询条件
     * @return 缺货商品列表
     */
    List<InventoryResponse> selectOutOfStockInventory(@Param("request") InventoryQueryRequest request);

    /**
     * 统计库存总价值
     *
     * @param request 查询条件
     * @return 库存总价值
     */
    BigDecimal getTotalInventoryValue(@Param("request") InventoryQueryRequest request);

    /**
     * 统计库存总数量
     *
     * @param request 查询条件
     * @return 库存总数量
     */
    BigDecimal getTotalInventoryQuantity(@Param("request") InventoryQueryRequest request);

    /**
     * 统计商品种类数量
     *
     * @param request 查询条件
     * @return 商品种类数量
     */
    Integer getProductCount(@Param("request") InventoryQueryRequest request);

    /**
     * 统计预警商品数量
     *
     * @param request 查询条件
     * @return 预警商品数量
     */
    Integer getWarningProductCount(@Param("request") InventoryQueryRequest request);

    /**
     * 统计缺货商品数量
     *
     * @param request 查询条件
     * @return 缺货商品数量
     */
    Integer getOutOfStockProductCount(@Param("request") InventoryQueryRequest request);

    /**
     * 按供应商统计库存价值
     *
     * @param request 查询条件
     * @return 供应商库存价值统计列表
     */
    List<InventoryValueResponse.SupplierInventoryValue> getSupplierInventoryValues(@Param("request") InventoryQueryRequest request);

    /**
     * 按商品分类统计库存价值
     *
     * @param request 查询条件
     * @return 商品分类库存价值统计列表
     */
    List<InventoryValueResponse.CategoryInventoryValue> getCategoryInventoryValues(@Param("request") InventoryQueryRequest request);

    /**
     * 按经营方式统计库存价值
     *
     * @param request 查询条件
     * @return 经营方式库存价值统计列表
     */
    List<InventoryValueResponse.BusinessModeInventoryValue> getBusinessModeInventoryValues(@Param("request") InventoryQueryRequest request);

    /**
     * 根据供应商ID统计库存商品数量
     *
     * @param supplierId 供应商ID
     * @return 库存商品数量
     */
    @Select("SELECT COUNT(1) FROM erp_inventory i LEFT JOIN erp_product p ON i.product_id = p.product_id WHERE p.supplier_id = #{supplierId}")
    Long countBySupplier(@Param("supplierId") Long supplierId);

    /**
     * 批量更新库存
     *
     * @param inventoryList 库存列表
     * @return 更新数量
     */
    Integer batchUpdateInventory(@Param("inventoryList") List<Inventory> inventoryList);

    /**
     * 根据商品ID列表查询库存信息
     *
     * @param productIds 商品ID列表
     * @return 库存信息列表
     */
    List<Inventory> selectByProductIds(@Param("productIds") List<Long> productIds);

}