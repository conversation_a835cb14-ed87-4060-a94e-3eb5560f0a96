package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存预警记录实体类
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@TableName("erp_inventory_alert_record")
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertRecord extends BaseEntity {

    /**
     * 预警记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 预警规则ID
     */
    @TableField("rule_id")
    private Long ruleId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)
     */
    @TableField("alert_type")
    private String alertType;

    /**
     * 预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)
     */
    @TableField("alert_level")
    private String alertLevel;

    /**
     * 当前库存数量
     */
    @TableField("current_stock")
    private BigDecimal currentStock;

    /**
     * 触发阈值
     */
    @TableField("threshold_value")
    private BigDecimal thresholdValue;

    /**
     * 预警消息
     */
    @TableField("alert_message")
    private String alertMessage;

    /**
     * 处理状态：PENDING(待处理)、PROCESSING(处理中)、RESOLVED(已解决)、IGNORED(已忽略)
     */
    @TableField("status")
    private String status;

    /**
     * 建议操作
     */
    @TableField("suggested_action")
    private String suggestedAction;

    /**
     * 建议补货数量
     */
    @TableField("suggested_quantity")
    private BigDecimal suggestedQuantity;

    /**
     * 建议供应商ID
     */
    @TableField("suggested_supplier_id")
    private Long suggestedSupplierId;

    /**
     * 是否已发送通知（Y-已发送，N-未发送）
     */
    @TableField("notification_sent")
    private String notificationSent;

    /**
     * 已发送的通知方式
     */
    @TableField("notification_methods")
    private String notificationMethods;

    /**
     * 通知发送时间
     */
    @TableField("notification_time")
    private Date notificationTime;

    /**
     * 处理人ID
     */
    @TableField("handler_user")
    private Long handlerUser;

    /**
     * 处理时间
     */
    @TableField("handle_time")
    private Date handleTime;

    /**
     * 处理备注
     */
    @TableField("handle_remark")
    private String handleRemark;

    /**
     * 预警时间
     */
    @TableField("alert_time")
    private Date alertTime;

    /**
     * 删除标记（Y-已删除，N-未删除）
     */
    @TableField("del_flag")
    private String delFlag;
}
