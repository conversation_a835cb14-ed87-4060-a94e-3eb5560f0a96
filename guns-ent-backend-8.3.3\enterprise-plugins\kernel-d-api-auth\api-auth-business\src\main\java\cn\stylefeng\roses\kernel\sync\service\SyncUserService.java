package cn.stylefeng.roses.kernel.sync.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.sync.factory.SyncUserFactory;
import cn.stylefeng.roses.kernel.sync.pojo.UserSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步业务-用户信息
 *
 * <AUTHOR>
 * @since 2023/10/30 13:44
 */
@Service
public class SyncUserService {

    @Resource
    private SysUserService sysUserService;

    /**
     * 获取所有用户信息
     *
     * <AUTHOR>
     * @since 2023/10/30 13:45
     */
    public List<UserSyncVo> getTotalUser() {
        try {
            // 关闭多租户开关
            TenantSwitchHolder.set(false);

            List<SysUser> sysUserList = sysUserService.list(createWrapper());

            // 组织机构信息转化为返回的VO信息
            return SyncUserFactory.createUserVo(sysUserList);

        } finally {
            TenantSwitchHolder.remove();
        }
    }

    /**
     * 分页获取用户信息
     *
     * <AUTHOR>
     * @since 2023/10/30 13:52
     */
    public PageResult<UserSyncVo> getPageUsers(BaseRequest baseRequest) {

        Page<SysUser> sysUserPage = PageFactory.defaultPage(baseRequest);

        LambdaQueryWrapper<SysUser> wrapper = this.createWrapper();

        Page<SysUser> page;
        try {
            TenantSwitchHolder.set(false);
            page = this.sysUserService.page(sysUserPage, wrapper);
        } finally {
            TenantSwitchHolder.remove();
        }

        if (ObjectUtil.isEmpty(page.getRecords())) {
            return PageResultFactory.createPageResult(new ArrayList<>(), page.getTotal(), Convert.toInt(page.getSize()),
                    Convert.toInt(page.getCurrent()));
        }

        List<SysUser> records = page.getRecords();
        List<UserSyncVo> sysUserVo = SyncUserFactory.createUserVo(records);
        return PageResultFactory.createPageResult(sysUserVo, page.getTotal(), Convert.toInt(page.getSize()),
                Convert.toInt(page.getCurrent()));
    }

    /**
     * 创建wrapper信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:03
     */
    private LambdaQueryWrapper<SysUser> createWrapper() {

        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询必要字段
        queryWrapper.select(SysUser::getUserId, SysUser::getRealName, SysUser::getNickName, SysUser::getAccount, SysUser::getBirthday,
                SysUser::getSex, SysUser::getEmail, SysUser::getPhone, SysUser::getTel, SysUser::getStatusFlag, SysUser::getUserSort);

        // 只查询启用的
        queryWrapper.eq(SysUser::getStatusFlag, StatusEnum.ENABLE.getCode());

        // 根据id排序
        queryWrapper.orderByAsc(SysUser::getUserId);

        return queryWrapper;
    }

}
