package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 采购入库单明细请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderDetailRequest extends BaseRequest {

    /**
     * 明细ID
     */
    @NotNull(message = "明细ID不能为空", groups = {edit.class, delete.class, detail.class})
    @ChineseDescription("明细ID")
    private Long id;

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空", groups = {add.class, edit.class})
    @ChineseDescription("入库单ID")
    private Long orderId;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空", groups = {add.class, edit.class})
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {add.class, edit.class})
    @DecimalMin(value = "0.001", message = "数量必须大于0", groups = {add.class, edit.class})
    @ChineseDescription("数量")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空", groups = {add.class, edit.class})
    @DecimalMin(value = "0.01", message = "单价必须大于0", groups = {add.class, edit.class})
    @ChineseDescription("单价")
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @DecimalMin(value = "0", message = "总价必须大于等于0", groups = {add.class, edit.class})
    @ChineseDescription("总价")
    private BigDecimal totalPrice;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 商品名称（查询条件）
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品编码（查询条件）
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

}