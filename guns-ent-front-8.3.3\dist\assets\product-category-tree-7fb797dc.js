import{P as c}from"./productCategoryApi-39e417fd.js";import{U as A}from"./UniversalTree-6547889b.js";import{_ as P,r as B,L as d,a as W,f as D,M as K,d as M,E as b,m as s}from"./index-18a1ea24.js";/* empty css              */const z=Object.assign({name:"ProductCategoryTree"},{__name:"product-category-tree",props:{isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["treeSelect","addCategory","editCategory","deleteCategory"],setup(l,{expose:i,emit:h}){const t=l,r=h,a=B(),u={api:c.findTree,lazyLoadApi:c.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},p={key:"categoryId",title:"categoryName",children:"children",hasChildren:"hasChildren",level:"categoryLevel"},g=d(()=>({title:"\u4EA7\u54C1\u5206\u7C7B",showHeader:t.isSetWidth,showSearch:!0,searchPlaceholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22",showAddButton:t.isShowEditIcon,showEditIcons:t.isShowEditIcon,showIcon:!1,isSetWidth:t.isSetWidth})),y={selectable:!0,expandable:!0,lazyLoad:!0,defaultExpandLevel:2,allowMultiSelect:!1},f=d(()=>({allowAdd:t.isShowEditIcon,allowEdit:t.isShowEditIcon,allowDelete:t.isShowEditIcon})),S=e=>{const{keys:o,nodes:N}=e;r("treeSelect",o,{selectedNodes:N})},m=e=>{},_=e=>{},C=e=>{r("addCategory",e)},E=e=>{r("editCategory",e)},v=e=>{K.confirm({title:"\u786E\u8BA4\u5220\u9664",content:'\u786E\u5B9A\u8981\u5220\u9664\u5206\u7C7B"'.concat(e.categoryName,'"\u5417\uFF1F'),icon:M(b),okText:"\u786E\u5B9A",cancelText:"\u53D6\u6D88",onOk:()=>c.delete({categoryId:e.categoryId}).then(()=>{s.success("\u5220\u9664\u6210\u529F"),r("deleteCategory",e),n()}).catch(o=>{console.error("\u5220\u9664\u5206\u7C7B\u5931\u8D25:",o),s.error("\u5220\u9664\u5931\u8D25")})})},w=e=>{},x=e=>{console.error("\u4EA7\u54C1\u5206\u7C7B\u6811\u6570\u636E\u52A0\u8F7D\u5931\u8D25:",e)},n=()=>{var e;(e=a.value)==null||e.reload()},I=()=>{var e;return(e=a.value)==null?void 0:e.getSelectedNodes()},T=e=>{var o;(o=a.value)==null||o.setSelectedKeys(e)},L=n,k=d(()=>{var e;return((e=a.value)==null?void 0:e.getSelectedNodes())||[]});return i({reload:n,reloadCategoryTreeData:L,getSelectedNodes:I,setSelectedKeys:T,currentSelectKeys:k}),(e,o)=>(W(),D(A,{ref_key:"universalTreeRef",ref:a,"data-source":u,"field-mapping":p,"display-config":g.value,"interaction-config":y,"action-config":f.value,onSelect:S,onExpand:m,onSearch:_,onAdd:C,onEdit:E,onDelete:v,onLoad:w,onLoadError:x},null,8,["display-config","action-config"]))}}),j=P(z,[["__scopeId","data-v-14cde36e"]]);export{j as default};
