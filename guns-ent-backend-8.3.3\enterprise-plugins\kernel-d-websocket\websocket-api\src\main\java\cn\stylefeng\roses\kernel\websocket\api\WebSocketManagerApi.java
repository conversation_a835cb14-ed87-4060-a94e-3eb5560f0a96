package cn.stylefeng.roses.kernel.websocket.api;

import cn.stylefeng.roses.kernel.websocket.api.constants.WebsocketConstants;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;

import java.util.List;
import java.util.Map;

/**
 * 通用WebSocket管理API，外部业务调用本业务获取websocket session
 *
 * <AUTHOR>
 * @since 2024/1/14 23:46
 */
public interface WebSocketManagerApi {

    /**
     * 根据标识获取websocket session
     *
     * @param identifier 标识
     * @return WebSocket
     * <AUTHOR>
     * @since 2024/1/15 0:29
     */
    WebSocketDTO get(String identifier);

    /**
     * 同一用户多点登录时使用
     *
     * @param userId 用户id
     * @return WebSocket列表
     * <AUTHOR>
     * @since 2024/1/15 0:29
     */
    List<WebSocketDTO> getList(String userId);

    /**
     * 放入一个 websocket session
     *
     * @param identifier 标识
     * @param webSocket  websocket session
     * <AUTHOR>
     * @since 2024/1/15 0:30
     */
    void put(String identifier, WebSocketDTO webSocket);

    /**
     * 删掉一个 websocket session
     *
     * @param identifier token标识
     * <AUTHOR>
     * @since 2024/1/15 0:30
     */
    void remove(String identifier);

    /**
     * 获取当前机器上的保存的WebSocket
     *
     * @return WebSocket集合，key是identifier，value是WebSocketDTO对象
     * <AUTHOR>
     * @since 2024/1/15 0:30
     */
    Map<String, WebSocketDTO> localWebSocketMap();

    /**
     * 统计当前实例在线人数,如果不允许一个账号多次登录,默认实现就可以,如果一个人多次登录,需要重写该方法
     *
     * @return 统计当前实例在线人数
     * <AUTHOR>
     * @since 2024/1/15 0:31
     */
    default int size() {
        return localWebSocketMap().size();
    }

    /**
     * 给某人发送消息
     *
     * @param userId  用户id
     * @param message 消息内容
     * <AUTHOR>
     * @since 2024/1/15 0:31
     */
    void sendMessage(String userId, String message);

    /**
     * 给某人发送消息
     *
     * @param userId        用户id
     * @param message       消息提示
     * @param messageObject 消息内容
     * <AUTHOR>
     * @since 2024/1/15 0:31
     */
    void sendMessageObject(String userId, String message, Object messageObject);

    /**
     * 广播
     *
     * @param message 消息内容
     * <AUTHOR>
     * @since 2024/1/15 0:31
     */
    void broadcast(String message);

    /**
     * WebSocket接收到消息的函数调用
     *
     * @param identifier 标识
     * @param message    消息内容
     * <AUTHOR>
     * @since 2024/1/15 0:32
     */
    void onMessage(String identifier, String message);

    /**
     * 在OnMessage中判断是否是心跳,
     * 从客户端的消息判断是否是ping消息
     *
     * @param identifier 标识
     * @param message    消息
     * @return 是否是ping消息
     * <AUTHOR>
     * @since 2024/1/15 0:32
     */
    default boolean pingFlag(String identifier, String message) {
        return WebsocketConstants.PING_CONTENT.equalsIgnoreCase(message);
    }

    /**
     * 返回心跳信息
     *
     * @param identifier 标识
     * @param message    消息
     * @return 返回的pong消息内容, 默认返回pong, 可以被重写
     * <AUTHOR>
     * @since 2024/1/15 0:34
     */
    default String getPongContent(String identifier, String message) {
        return WebsocketConstants.PONG_CONTENT;
    }

}
