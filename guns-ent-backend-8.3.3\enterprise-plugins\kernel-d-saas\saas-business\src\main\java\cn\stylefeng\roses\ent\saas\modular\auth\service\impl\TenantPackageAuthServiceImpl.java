package cn.stylefeng.roses.ent.saas.modular.auth.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.action.PackageBindPermissionAction;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth;
import cn.stylefeng.roses.ent.saas.modular.auth.mapper.TenantPackageAuthMapper;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.PackageAuthInfo;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantLinkService;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantService;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.sys.api.factory.RoleBindPermissionFactory;
import cn.stylefeng.roses.kernel.sys.api.pojo.role.response.RoleBindPermissionResponse;
import cn.stylefeng.roses.kernel.sys.modular.role.enums.RoleLimitTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.role.service.PermissionAssignService;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 租户-功能包授权范围业务实现层
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@Service
public class TenantPackageAuthServiceImpl extends ServiceImpl<TenantPackageAuthMapper, TenantPackageAuth> implements TenantPackageAuthService {

    @Resource
    private PermissionAssignService permissionAssignService;

    @Resource
    private TenantLinkService tenantLinkService;

    @Resource
    private TenantService tenantService;

    @Resource
    private SysRoleService sysRoleService;

    @Override
    public void removeByPackageId(Long packageId) {
        if (ObjectUtil.isEmpty(packageId)) {
            return;
        }
        LambdaQueryWrapper<TenantPackageAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPackageAuth::getPackageId, packageId);
        this.remove(queryWrapper);
    }

    @Override
    public PackageAuthInfo getPackageAuthInfo(List<Long> packageIdList) {
        if (ObjectUtil.isEmpty(packageIdList)) {
            return new PackageAuthInfo();
        }
        LambdaQueryWrapper<TenantPackageAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantPackageAuth::getPackageId, packageIdList);
        List<TenantPackageAuth> tenantPackageAuths = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(tenantPackageAuths)) {
            return new PackageAuthInfo();
        }

        // 找到所有的菜单id
        Set<Long> menuIdList = tenantPackageAuths.stream().filter(i -> RoleLimitTypeEnum.MENU.getCode().equals(i.getLimitType())).map(TenantPackageAuth::getBusinessId).collect(Collectors.toSet());

        // 找到所有的菜单功能id
        Set<Long> menuFunctionIdList = tenantPackageAuths.stream().filter(i -> RoleLimitTypeEnum.MENU_OPTIONS.getCode().equals(i.getLimitType())).map(TenantPackageAuth::getBusinessId)
                .collect(Collectors.toSet());

        PackageAuthInfo packageAuthInfo = new PackageAuthInfo();
        packageAuthInfo.setMenuIdList(menuIdList);
        packageAuthInfo.setMenuOptionIdList(menuFunctionIdList);
        return packageAuthInfo;
    }

    @Override
    public RoleBindPermissionResponse getPackageAuth(PackageBindPermissionRequest packageBindPermissionRequest) {

        // 1. 整理出来一个总的相应的结构树，选择状态为空
        RoleBindPermissionResponse selectTreeStructure = permissionAssignService.createSelectTreeStructure();

        // 2. 获取角色限制所对应的菜单和功能列表
        Set<Long> roleBindLimitList = this.getPackageAuthBusinessList(packageBindPermissionRequest.getPackageId());

        // 3. 组合结构和角色绑定的限制信息，填充选择状态，封装返回结果
        return RoleBindPermissionFactory.fillCheckedFlag(selectTreeStructure, roleBindLimitList);

    }

    @Override
    public Set<Long> getPackageAuthBusinessList(Long packageId) {
        LambdaQueryWrapper<TenantPackageAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantPackageAuth::getPackageId, packageId);
        queryWrapper.select(TenantPackageAuth::getBusinessId);
        List<TenantPackageAuth> tenantPackageAuths = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(tenantPackageAuths)) {
            return new HashSet<>();
        }
        return tenantPackageAuths.stream().map(TenantPackageAuth::getBusinessId).collect(Collectors.toSet());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePackageBindPermission(PackageBindPermissionRequest packageBindPermissionRequest) {
        Map<String, PackageBindPermissionAction> operateActionMap = SpringUtil.getBeansOfType(PackageBindPermissionAction.class);
        for (PackageBindPermissionAction roleBindLimitAction : operateActionMap.values()) {
            if (roleBindLimitAction.getPackageBindPermissionNodeType().getCode().equals(packageBindPermissionRequest.getPermissionNodeType())) {
                roleBindLimitAction.doPackageBindPermissionAction(packageBindPermissionRequest);
                return;
            }
        }
    }

    @Override
    public void refreshPackageTenantRoles(PackageBindPermissionRequest packageBindPermissionRequest) {

        Long packageId = packageBindPermissionRequest.getPackageId();

        // 1. 获取功能包对应的租户id集合
        List<Long> tenantIdList = tenantLinkService.getPackageTenantIdList(packageId);

        // 2. 遍历这些租户，找到这个租户的最新的功能包
        for (Long tenantId : tenantIdList) {

            // 2.1 获取租户绑定的功能包列表
            List<Long> tenantPackageIdList = tenantLinkService.getTenantPackageIdList(tenantId);
            if (ObjectUtil.isEmpty(tenantPackageIdList)) {
                break;
            }

            // 2.3 修改租户的功能包对应的权限
            this.updateTenantPackageRole(tenantId, tenantPackageIdList);
        }

    }

    /**
     * 修改租户的功能包对应的角色和权限的信息
     *
     * <AUTHOR>
     * @since 2024-01-23 14:59
     */
    @Override
    public void updateTenantPackageRole(Long tenantId, List<Long> tenantPackageIdList) {
        // 2.2 获取这些功能包最新的权限
        PackageAuthInfo packageAuthInfo = getPackageAuthInfo(tenantPackageIdList);

        // 2.2 获取租户的管理员角色
        Set<Long> tenantAdminRoleId = tenantService.getTenantAdminRoleId(tenantId);

        // 2.3 将租户的管理员角色从新赋予对应的权限，以及权限范围
        try {
            TenantSwitchHolder.set(false);
            sysRoleService.refreshRoleAuth(tenantAdminRoleId, packageAuthInfo.getMenuIdList(), packageAuthInfo.getMenuOptionIdList());
        } finally {
            TenantSwitchHolder.remove();
        }
    }

}
