package cn.stylefeng.roses.kernel.impexp.org.exceptions;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 导入组织机构的报错
 *
 * <AUTHOR>
 * @since 2024/2/19 0:42
 */
@Getter
public enum OrgImportExceptionEnum implements AbstractExceptionEnum {

    /**
     * 所属组织机构id不存在
     */
    BELONG_ORG_ID_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "所属组织机构id不存在"),

    /**
     * 所属组织机构不存在
     */
    BELONG_ORG_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "所属组织机构不存在");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    OrgImportExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
