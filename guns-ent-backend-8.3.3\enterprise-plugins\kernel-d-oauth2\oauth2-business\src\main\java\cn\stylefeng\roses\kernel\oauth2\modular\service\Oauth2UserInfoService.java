/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.oauth2.modular.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.oauth2.api.pojo.params.OauthUserInfoParam;
import cn.stylefeng.roses.kernel.oauth2.api.pojo.result.OauthUserInfoResult;
import cn.stylefeng.roses.kernel.oauth2.modular.entity.OauthUserInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 外部oauth2用户相关Api
 *
 * <AUTHOR>
 * @date 2022-07-01 14:16:39
 */
public interface Oauth2UserInfoService extends IService<OauthUserInfo> {

    /**
     * 新增oauth2用户信息
     *
     * <AUTHOR>
     * @date 2022/7/1 15:39
     */
    void add(OauthUserInfoParam param);

    /**
     * 删除oauth2信息
     *
     * <AUTHOR>
     * @date 2022/7/1 15:39
     */
    void delete(OauthUserInfoParam param);

    /**
     * 更新用户信息
     *
     * <AUTHOR>
     * @date 2022/7/1 15:39
     */
    void update(OauthUserInfoParam param);

    /**
     * 查询单条数据，Specification模式
     *
     * <AUTHOR>
     * @date 2022/7/1 15:39
     */
    OauthUserInfoResult findBySpec(OauthUserInfoParam param);

    /**
     * 查询列表，Specification模式
     *
     * <AUTHOR>
     * @date 2022/7/1 15:40
     */
    List<OauthUserInfoResult> findListBySpec(OauthUserInfoParam param);

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     * @date 2022/7/1 15:40
     */
    PageResult<OauthUserInfoResult> findPageBySpec(OauthUserInfoParam param);

    /**
     * 获取用户头像地址
     *
     * <AUTHOR>
     * @date 2022/7/1 15:40
     */
    String getAvatarUrl(Long userId);

}
