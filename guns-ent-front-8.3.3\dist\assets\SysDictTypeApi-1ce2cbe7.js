import{R as e}from"./index-18a1ea24.js";class d{static findDictTypePage(t){return e.getAndLoadData("/dictType/page",t)}static add(t){return e.post("/dictType/add",t)}static del(t){return e.post("/dictType/delete",t)}static batchDel(t){return e.post("/dictType/batchDelete",t)}static edit(t){return e.post("/dictType/edit",t)}static getDictListByParams(t){return e.getAndLoadData("/dict/list",t)}static getDictTypeList(t){return e.getAndLoadData("/dictType/list",t)}}export{d as S};
