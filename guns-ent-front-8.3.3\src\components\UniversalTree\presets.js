/**
 * 通用树结构组件预设配置
 * 
 * <AUTHOR>
 * @since 2025/01/24
 */

import { TreeConfigBuilder } from './utils.js'
import { RegionApi } from '@/views/erp/region/api/regionApi.js'
import { ProductCategoryApi } from '@/views/erp/productCategory/api/productCategoryApi.js'

/**
 * 区域管理树配置（编辑模式）
 */
export function createRegionTreeConfig() {
  return new TreeConfigBuilder()
    .setDataSource(
      RegionApi.findTree,
      RegionApi.findTreeWithLazy,
      'searchText',
      'parentId'
    )
    .setFieldMapping('regionId', 'regionName', 'children', 'hasChildren', 'level')
    .setDisplayConfig({
      title: '区域管理',
      showHeader: true,
      showSearch: true,
      searchPlaceholder: '搜索区域名称',
      showAddButton: true,
      showEditIcons: true,
      showIcon: false,
      isSetWidth: true
    })
    .setInteractionConfig({
      selectable: true,
      expandable: true,
      lazyLoad: true,
      defaultExpandLevel: 2,
      allowMultiSelect: false
    })
    .enableEditMode()
    .build()
}

/**
 * 产品分类树配置（编辑模式）
 */
export function createProductCategoryTreeConfig() {
  return new TreeConfigBuilder()
    .setDataSource(
      ProductCategoryApi.findTree,
      ProductCategoryApi.findTreeWithLazy,
      'searchText',
      'parentId'
    )
    .setFieldMapping('categoryId', 'categoryName', 'children', 'hasChildren', 'level')
    .setDisplayConfig({
      title: '产品分类',
      showHeader: true,
      showSearch: true,
      searchPlaceholder: '搜索分类名称',
      showAddButton: true,
      showEditIcons: true,
      showIcon: false,
      isSetWidth: true
    })
    .setInteractionConfig({
      selectable: true,
      expandable: true,
      lazyLoad: true,
      defaultExpandLevel: 2,
      allowMultiSelect: false
    })
    .enableEditMode()
    .build()
}

/**
 * 产品分类树配置（只读模式 - 用于商品管理页面）
 */
export function createProductCategoryReadOnlyTreeConfig() {
  return new TreeConfigBuilder()
    .setDataSource(
      ProductCategoryApi.findTree,
      ProductCategoryApi.findTreeWithLazy,
      'searchText',
      'parentId'
    )
    .setFieldMapping('categoryId', 'categoryName', 'children', 'hasChildren', 'level')
    .setDisplayConfig({
      title: '产品分类',
      showHeader: true,
      showSearch: true,
      searchPlaceholder: '搜索分类名称',
      showAddButton: false,
      showEditIcons: false,
      showIcon: false,
      isSetWidth: true
    })
    .setInteractionConfig({
      selectable: true,
      expandable: true,
      lazyLoad: false, // 商品管理页面使用完整树，不使用懒加载
      defaultExpandLevel: 1,
      allowMultiSelect: false
    })
    .enableReadOnlyMode()
    .build()
}

/**
 * 区域树配置（只读模式 - 用于客户/供应商管理页面）
 */
export function createRegionReadOnlyTreeConfig() {
  return new TreeConfigBuilder()
    .setDataSource(
      RegionApi.findTree,
      null, // 只读模式不需要懒加载
      'searchText',
      'parentId'
    )
    .setFieldMapping('regionId', 'regionName', 'children', 'hasChildren', 'level')
    .setDisplayConfig({
      title: '区域筛选',
      showHeader: true,
      showSearch: true,
      searchPlaceholder: '搜索区域名称',
      showAddButton: false,
      showEditIcons: false,
      showIcon: false,
      isSetWidth: true
    })
    .setInteractionConfig({
      selectable: true,
      expandable: true,
      lazyLoad: false,
      defaultExpandLevel: 2,
      allowMultiSelect: false
    })
    .enableReadOnlyMode()
    .build()
}

/**
 * 通用只读树配置
 * @param {Object} options 配置选项
 * @param {Function} options.api 数据API
 * @param {Function} options.lazyLoadApi 懒加载API（可选）
 * @param {string} options.keyField 主键字段名
 * @param {string} options.titleField 标题字段名
 * @param {string} options.childrenField 子节点字段名
 * @param {string} options.hasChildrenField 是否有子节点字段名（可选）
 * @param {string} options.levelField 层级字段名（可选）
 * @param {string} options.title 树标题
 * @param {string} options.searchPlaceholder 搜索占位符
 * @param {number} options.defaultExpandLevel 默认展开层级
 * @param {boolean} options.lazyLoad 是否懒加载
 */
export function createReadOnlyTreeConfig(options) {
  const {
    api,
    lazyLoadApi = null,
    keyField,
    titleField,
    childrenField = 'children',
    hasChildrenField = 'hasChildren',
    levelField = 'level',
    title = '数据筛选',
    searchPlaceholder = '请输入关键字搜索',
    defaultExpandLevel = 1,
    lazyLoad = false
  } = options

  return new TreeConfigBuilder()
    .setDataSource(api, lazyLoadApi, 'searchText', 'parentId')
    .setFieldMapping(keyField, titleField, childrenField, hasChildrenField, levelField)
    .setDisplayConfig({
      title,
      showHeader: true,
      showSearch: true,
      searchPlaceholder,
      showAddButton: false,
      showEditIcons: false,
      showIcon: false,
      isSetWidth: true
    })
    .setInteractionConfig({
      selectable: true,
      expandable: true,
      lazyLoad,
      defaultExpandLevel,
      allowMultiSelect: false
    })
    .enableReadOnlyMode()
    .build()
}

/**
 * 通用编辑树配置
 * @param {Object} options 配置选项
 * @param {Function} options.api 数据API
 * @param {Function} options.lazyLoadApi 懒加载API（可选）
 * @param {string} options.keyField 主键字段名
 * @param {string} options.titleField 标题字段名
 * @param {string} options.childrenField 子节点字段名
 * @param {string} options.hasChildrenField 是否有子节点字段名（可选）
 * @param {string} options.levelField 层级字段名（可选）
 * @param {string} options.title 树标题
 * @param {string} options.searchPlaceholder 搜索占位符
 * @param {number} options.defaultExpandLevel 默认展开层级
 * @param {boolean} options.lazyLoad 是否懒加载
 * @param {Array} options.customActions 自定义操作（可选）
 */
export function createEditTreeConfig(options) {
  const {
    api,
    lazyLoadApi = null,
    keyField,
    titleField,
    childrenField = 'children',
    hasChildrenField = 'hasChildren',
    levelField = 'level',
    title = '数据管理',
    searchPlaceholder = '请输入关键字搜索',
    defaultExpandLevel = 2,
    lazyLoad = true,
    customActions = []
  } = options

  return new TreeConfigBuilder()
    .setDataSource(api, lazyLoadApi, 'searchText', 'parentId')
    .setFieldMapping(keyField, titleField, childrenField, hasChildrenField, levelField)
    .setDisplayConfig({
      title,
      showHeader: true,
      showSearch: true,
      searchPlaceholder,
      showAddButton: true,
      showEditIcons: true,
      showIcon: false,
      isSetWidth: true
    })
    .setInteractionConfig({
      selectable: true,
      expandable: true,
      lazyLoad,
      defaultExpandLevel,
      allowMultiSelect: false
    })
    .setActionConfig({
      allowAdd: true,
      allowEdit: true,
      allowDelete: true,
      customActions
    })
    .build()
}