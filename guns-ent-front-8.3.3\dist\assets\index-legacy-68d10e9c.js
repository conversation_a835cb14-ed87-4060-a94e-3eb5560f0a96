System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./InventoryAlertRecordApi-legacy-ebfe5eed.js","./inventoryAlert-legacy-c3ff7cb2.js","./AlertRuleForm-legacy-489233d6.js","./AlertTestResult-legacy-2ca32f43.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./index-legacy-45c79de7.js","./index-legacy-510bfbb8.js"],(function(e,t){"use strict";var l,a,r,s,n,i,o,c,d,u,h,g,y,p,v,f,m,_,k,A,C,w,T,R,L,b,x,S,F,O,I,E,D,j,N,V,P,z,Y,U,M,q,K,B,W;return{setters:[e=>{l=e.R,a=e.am,r=e._,s=e.ac,n=e.ad,i=e.P,o=e.an,c=e.E,d=e.ao,u=e.ap,h=e.aq,g=e.s,y=e.r,p=e.L,v=e.o,f=e.ag,m=e.k,_=e.a,k=e.c,A=e.b,C=e.d,w=e.w,T=e.F,R=e.e,L=e.f,b=e.g,x=e.t,S=e.ar,F=e.h,O=e.m,I=e.M,E=e.l,D=e.v,j=e.W,N=e.J,V=e.U,P=e.B,z=e.n,Y=e.G,U=e.a9,M=e.i},null,null,null,null,e=>{q=e.I},e=>{K=e.I},e=>{B=e.default},e=>{W=e.default},null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".search-area[data-v-4673e48d]{margin-bottom:16px;padding:16px;background:#fff;border-radius:6px}.table-area[data-v-4673e48d]{background:#fff;border-radius:6px}.batch-actions[data-v-4673e48d]{margin-top:16px;padding:12px 16px;background:#f0f2f5;border-radius:6px}\n",document.head.appendChild(t);class ${static add(e){return l.post("/erp/inventoryAlert/rule/add",e)}static edit(e){return l.post("/erp/inventoryAlert/rule/edit",e)}static delete(e){return l.post("/erp/inventoryAlert/rule/delete",e)}static batchDelete(e){return l.post("/erp/inventoryAlert/rule/batchDelete",e)}static detail(e){return l.getAndLoadData("/erp/inventoryAlert/rule/detail",e)}static findPage(e){return l.getAndLoadData("/erp/inventoryAlert/rule/page",e)}static findList(e){return l.getAndLoadData("/erp/inventoryAlert/rule/list",e)}static updateStatus(e){return l.post("/erp/inventoryAlert/rule/updateStatus",e)}static testRule(e){return l.post("/erp/inventoryAlert/rule/test",e)}static executeCheck(){return l.post("/erp/inventoryAlert/rule/executeCheck")}}const G=a("inventoryAlert",{state:()=>({ruleList:[],ruleTotal:0,ruleLoading:!1,recordList:[],recordTotal:0,recordLoading:!1,statistics:{totalAlerts:0,criticalAlerts:0,warningAlerts:0,infoAlerts:0,pendingAlerts:0,processingAlerts:0,resolvedAlerts:0,ignoredAlerts:0},currentRule:null,currentRecord:null,productOptions:[],categoryOptions:[],userOptions:[],recentRecords:[],overview:null}),getters:{enabledRulesCount:e=>e.ruleList.filter((e=>"Y"===e.isEnabled)).length,pendingAlertsCount:e=>e.statistics.pendingAlerts||0,criticalAlertsCount:e=>e.statistics.criticalAlerts||0,totalAlertsCount:e=>e.statistics.totalAlerts||0,resolveRate:e=>{const t=e.statistics.totalAlerts||0,l=e.statistics.resolvedAlerts||0;return t>0?Math.round(l/t*100):0}},actions:{async loadRuleList(e={}){this.ruleLoading=!0;try{const t=await $.findPage(e);this.ruleList=t.rows||[],this.ruleTotal=t.totalRows||0}catch(t){console.error("加载预警规则列表失败:",t),this.ruleList=[],this.ruleTotal=0}finally{this.ruleLoading=!1}},async loadRecordList(e={}){this.recordLoading=!0;try{const t=await q.findPage(e);this.recordList=t.rows||[],this.recordTotal=t.totalRows||0}catch(t){console.error("加载预警记录列表失败:",t),this.recordList=[],this.recordTotal=0}finally{this.recordLoading=!1}},async loadStatistics(e={}){try{const t=await q.getStatistics(e);this.statistics={totalAlerts:t.total_alerts||0,criticalAlerts:t.critical_alerts||0,warningAlerts:t.warning_alerts||0,infoAlerts:t.info_alerts||0,pendingAlerts:t.pending_alerts||0,processingAlerts:t.processing_alerts||0,resolvedAlerts:t.resolved_alerts||0,ignoredAlerts:t.ignored_alerts||0}}catch(t){console.error("加载预警统计数据失败:",t)}},async loadRecentRecords(e={}){try{const t=await q.getRecentRecords(e);this.recentRecords=t||[]}catch(t){console.error("加载最近预警记录失败:",t),this.recentRecords=[]}},async loadOverview(e={}){try{const t=await q.getOverview(e);this.overview=t,t.statistics&&(this.statistics={totalAlerts:t.statistics.total_alerts||0,criticalAlerts:t.statistics.critical_alerts||0,warningAlerts:t.statistics.warning_alerts||0,infoAlerts:t.statistics.info_alerts||0,pendingAlerts:t.statistics.pending_alerts||0,processingAlerts:t.statistics.processing_alerts||0,resolvedAlerts:t.statistics.resolved_alerts||0,ignoredAlerts:t.statistics.ignored_alerts||0}),t.recentRecords&&(this.recentRecords=t.recentRecords)}catch(t){console.error("加载概览数据失败:",t)}},setCurrentRule(e){this.currentRule=e},setCurrentRecord(e){this.currentRecord=e},updateRuleStatus(e,t){const l=this.ruleList.find((t=>t.id===e));l&&(l.isEnabled=t)},updateRecordStatus(e,t){const l=this.recordList.find((t=>t.id===e));l&&(l.status=t,l.handleTime=new Date);const a=this.recentRecords.find((t=>t.id===e));a&&(a.status=t,a.handleTime=new Date)},clearData(){this.ruleList=[],this.ruleTotal=0,this.recordList=[],this.recordTotal=0,this.currentRule=null,this.currentRecord=null,this.recentRecords=[],this.overview=null},async refreshData(){await Promise.all([this.loadStatistics(),this.loadRecentRecords(),this.loadRuleList({pageNo:1,pageSize:10}),this.loadRecordList({pageNo:1,pageSize:10,statusFilter:"PENDING"})])},async refreshOverview(){await this.loadOverview()}}}),H={class:"guns-layout"},J={class:"guns-layout-content"},Q={class:"content-main"},Z={class:"content-main-body"},X={class:"search-area"},ee={class:"table-area"},te={key:0,class:"batch-actions"};e("default",r({name:"InventoryAlertRuleIndex",components:{SearchOutlined:s,ReloadOutlined:n,PlusOutlined:i,PlayCircleOutlined:o,ExclamationCircleOutlined:c,WarningOutlined:d,InfoCircleOutlined:u,QuestionCircleOutlined:h,AlertRuleForm:B,AlertTestResult:W},setup(){const e=G(),t=g({searchText:"",ruleTypeFilter:void 0,alertLevelFilter:void 0,isEnabledFilter:void 0}),l=y(!1),a=y(!1),r=y(null),s=y([]),n=y([]),i=K.getAlertTypeOptions(),o=K.getAlertLevelOptions();K.getTargetTypeOptions();const m=p((()=>e.ruleList)),_=p((()=>e.ruleLoading)),k=p((()=>e.ruleTotal)),A=p((()=>({current:1,pageSize:20,total:k.value,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}))),C=p((()=>({selectedRowKeys:n.value,onChange:e=>{n.value=e}}))),w=(l={})=>{const a={pageNo:1,pageSize:20,...t,...l};e.loadRuleList(a)},T=()=>{n.value=[]};return v((()=>{w()})),{searchForm:t,formVisible:l,testVisible:a,currentRule:r,testResult:s,selectedRowKeys:n,alertTypeOptions:i,alertLevelOptions:o,columns:[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:200,ellipsis:!0},{title:"预警类型",dataIndex:"ruleType",key:"ruleType",width:120},{title:"预警级别",dataIndex:"alertLevel",key:"alertLevel",width:100},{title:"目标类型",dataIndex:"targetType",key:"targetType",width:120},{title:"阈值",dataIndex:"thresholdValue",key:"thresholdValue",width:100},{title:"检查频率(分钟)",dataIndex:"checkFrequency",key:"checkFrequency",width:120},{title:"启用状态",dataIndex:"isEnabled",key:"isEnabled",width:100},{title:"最后检查时间",dataIndex:"lastCheckTime",key:"lastCheckTime",width:160},{title:"操作",key:"action",width:200,fixed:"right"}],ruleList:m,ruleLoading:_,pagination:A,rowSelection:C,handleSearch:()=>{w()},handleReset:()=>{Object.keys(t).forEach((e=>{t[e]=void 0})),w()},handleAdd:()=>{r.value=null,l.value=!0},handleEdit:async e=>{try{const t=await $.detail({id:e.id});r.value=t.data,l.value=!0}catch(t){console.error("获取预警规则详情失败:",t),O.error("获取详情失败，请重试")}},handleDelete:e=>{I.confirm({title:"确认删除",content:`确定要删除预警规则"${e.ruleName}"吗？`,icon:c,onOk:async()=>{try{await $.delete({id:e.id}),O.success("删除成功"),w()}catch(t){O.error("删除失败")}}})},handleBatchDelete:()=>{0!==n.value.length?I.confirm({title:"确认批量删除",content:`确定要删除选中的 ${n.value.length} 条预警规则吗？`,icon:c,onOk:async()=>{try{await $.batchDelete({idList:n.value}),O.success("批量删除成功"),T(),w()}catch(e){O.error("批量删除失败")}}}):O.warning("请选择要删除的记录")},clearSelection:T,handleStatusChange:async(t,l)=>{try{await $.updateStatus({id:t.id,isEnabled:l?"Y":"N"}),O.success("状态更新成功"),e.updateRuleStatus(t.id,l?"Y":"N")}catch(a){O.error("状态更新失败")}},handleTest:async e=>{try{const t=await $.testRule({id:e.id});s.value=t,a.value=!0}catch(t){O.error("测试失败")}},handleExecuteCheck:async()=>{try{await $.executeCheck(),O.success("预警检查已开始执行")}catch(e){O.error("执行预警检查失败")}},handleTableChange:e=>{w({pageNo:e.current,pageSize:e.pageSize})},handleFormSubmit:async e=>{try{e.id?(await $.edit(e),O.success("编辑预警规则成功")):(await $.add(e),O.success("新增预警规则成功")),l.value=!1,w()}catch(t){console.error("保存预警规则失败:",t),O.error("保存失败，请重试")}},handleFormCancel:()=>{l.value=!1},getAlertTypeInfo:e=>K.getAlertTypeInfo(e),getAlertLevelInfo:e=>K.getAlertLevelInfo(e),getTargetTypeName:e=>K.getTargetTypeName(e),formatThresholdValue:(e,t)=>K.formatThresholdValue(e,t),getAlertLevelIcon:e=>{const t=K.getAlertLevelIcon(e);return{"exclamation-circle":c,warning:d,"info-circle":u,"question-circle":h}[t]||h},dayjs:f}}},[["render",function(e,t,l,a,r,s){const n=m("SearchOutlined"),i=E,o=D,c=j,d=N,u=V,h=P,g=m("ReloadOutlined"),y=m("PlusOutlined"),p=m("PlayCircleOutlined"),v=z,f=Y,O=U,I=M,q=m("alert-rule-form"),K=m("alert-test-result");return _(),k("div",H,[A("div",J,[A("div",Q,[A("div",Z,[A("div",X,[C(f,{gutter:16},{default:w((()=>[C(o,{span:6},{default:w((()=>[C(i,{value:a.searchForm.searchText,"onUpdate:value":t[0]||(t[0]=e=>a.searchForm.searchText=e),placeholder:"规则名称","allow-clear":"",onPressEnter:a.handleSearch},{prefix:w((()=>[C(n)])),_:1},8,["value","onPressEnter"])])),_:1}),C(o,{span:4},{default:w((()=>[C(d,{value:a.searchForm.ruleTypeFilter,"onUpdate:value":t[1]||(t[1]=e=>a.searchForm.ruleTypeFilter=e),placeholder:"预警类型","allow-clear":"",onChange:a.handleSearch},{default:w((()=>[(_(!0),k(T,null,R(a.alertTypeOptions,(e=>(_(),L(c,{key:e.value,value:e.value},{default:w((()=>[b(x(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","onChange"])])),_:1}),C(o,{span:4},{default:w((()=>[C(d,{value:a.searchForm.alertLevelFilter,"onUpdate:value":t[2]||(t[2]=e=>a.searchForm.alertLevelFilter=e),placeholder:"预警级别","allow-clear":"",onChange:a.handleSearch},{default:w((()=>[(_(!0),k(T,null,R(a.alertLevelOptions,(e=>(_(),L(c,{key:e.value,value:e.value},{default:w((()=>[C(u,{color:e.color},{default:w((()=>[b(x(e.label),1)])),_:2},1032,["color"])])),_:2},1032,["value"])))),128))])),_:1},8,["value","onChange"])])),_:1}),C(o,{span:4},{default:w((()=>[C(d,{value:a.searchForm.isEnabledFilter,"onUpdate:value":t[3]||(t[3]=e=>a.searchForm.isEnabledFilter=e),placeholder:"启用状态","allow-clear":"",onChange:a.handleSearch},{default:w((()=>[C(c,{value:"Y"},{default:w((()=>t[6]||(t[6]=[b("启用")]))),_:1,__:[6]}),C(c,{value:"N"},{default:w((()=>t[7]||(t[7]=[b("停用")]))),_:1,__:[7]})])),_:1},8,["value","onChange"])])),_:1}),C(o,{span:6},{default:w((()=>[C(v,null,{default:w((()=>[C(h,{type:"primary",onClick:a.handleSearch},{default:w((()=>[C(n),t[8]||(t[8]=b(" 搜索 "))])),_:1,__:[8]},8,["onClick"]),C(h,{onClick:a.handleReset},{default:w((()=>[C(g),t[9]||(t[9]=b(" 重置 "))])),_:1,__:[9]},8,["onClick"]),C(h,{type:"primary",onClick:a.handleAdd},{default:w((()=>[C(y),t[10]||(t[10]=b(" 新增规则 "))])),_:1,__:[10]},8,["onClick"]),C(h,{onClick:a.handleExecuteCheck},{default:w((()=>[C(p),t[11]||(t[11]=b(" 执行检查 "))])),_:1,__:[11]},8,["onClick"])])),_:1})])),_:1})])),_:1})]),A("div",ee,[C(I,{columns:a.columns,"data-source":a.ruleList,loading:a.ruleLoading,pagination:a.pagination,"row-key":"id","row-selection":a.rowSelection,onChange:a.handleTableChange},{bodyCell:w((({column:e,record:l})=>["ruleType"===e.key?(_(),L(u,{key:0,color:a.getAlertTypeInfo(l.ruleType).color},{default:w((()=>[b(x(a.getAlertTypeInfo(l.ruleType).name),1)])),_:2},1032,["color"])):"alertLevel"===e.key?(_(),L(u,{key:1,color:a.getAlertLevelInfo(l.alertLevel).color},{icon:w((()=>[(_(),L(S(a.getAlertLevelIcon(l.alertLevel))))])),default:w((()=>[b(" "+x(a.getAlertLevelInfo(l.alertLevel).name),1)])),_:2},1032,["color"])):"targetType"===e.key?(_(),k(T,{key:2},[b(x(a.getTargetTypeName(l.targetType)),1)],64)):"thresholdValue"===e.key?(_(),k(T,{key:3},[b(x(a.formatThresholdValue(l.thresholdValue,l.thresholdType)),1)],64)):"isEnabled"===e.key?(_(),L(O,{key:4,checked:"Y"===l.isEnabled,onChange:e=>a.handleStatusChange(l,e)},null,8,["checked","onChange"])):"lastCheckTime"===e.key?(_(),k(T,{key:5},[b(x(l.lastCheckTime?a.dayjs(l.lastCheckTime).format("YYYY-MM-DD HH:mm:ss"):"-"),1)],64)):"action"===e.key?(_(),L(v,{key:6},{default:w((()=>[C(h,{type:"link",size:"small",onClick:e=>a.handleEdit(l)},{default:w((()=>t[12]||(t[12]=[b(" 编辑 ")]))),_:2,__:[12]},1032,["onClick"]),C(h,{type:"link",size:"small",onClick:e=>a.handleTest(l)},{default:w((()=>t[13]||(t[13]=[b(" 测试 ")]))),_:2,__:[13]},1032,["onClick"]),C(h,{type:"link",size:"small",danger:"",onClick:e=>a.handleDelete(l)},{default:w((()=>t[14]||(t[14]=[b(" 删除 ")]))),_:2,__:[14]},1032,["onClick"])])),_:2},1024)):F("",!0)])),_:1},8,["columns","data-source","loading","pagination","row-selection","onChange"])]),a.selectedRowKeys.length>0?(_(),k("div",te,[C(v,null,{default:w((()=>[A("span",null,"已选择 "+x(a.selectedRowKeys.length)+" 项",1),C(h,{danger:"",onClick:a.handleBatchDelete},{default:w((()=>t[15]||(t[15]=[b(" 批量删除 ")]))),_:1,__:[15]},8,["onClick"]),C(h,{onClick:a.clearSelection},{default:w((()=>t[16]||(t[16]=[b(" 取消选择 ")]))),_:1,__:[16]},8,["onClick"])])),_:1})])):F("",!0)])])]),C(q,{visible:a.formVisible,"onUpdate:visible":t[4]||(t[4]=e=>a.formVisible=e),"form-data":a.currentRule,onSubmit:a.handleFormSubmit,onCancel:a.handleFormCancel},null,8,["visible","form-data","onSubmit","onCancel"]),C(K,{visible:a.testVisible,"onUpdate:visible":t[5]||(t[5]=e=>a.testVisible=e),data:a.testResult},null,8,["visible","data"])])}],["__scopeId","data-v-4673e48d"]]))}}}));
