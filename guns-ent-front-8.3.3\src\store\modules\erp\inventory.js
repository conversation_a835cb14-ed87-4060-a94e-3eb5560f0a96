/**
 * 库存管理状态管理
 * 
 * 管理库存相关的状态和操作，包括：
 * - 库存列表管理
 * - 库存历史记录
 * - 预警商品管理
 * - 搜索和筛选状态
 * - 库存调整操作
 * - 预警设置
 * - 价值统计
 */
import { defineStore } from 'pinia';
import { InventoryApi } from '@/views/erp/inventory/api/InventoryApi';
import { message } from 'ant-design-vue';

export const useInventoryStore = defineStore({
  id: 'inventory',
  state: () => ({
    // 库存列表数据
    inventory: [],
    // 库存历史记录
    inventoryHistory: [],
    // 预警商品列表
    warningItems: [],
    // 库存变动记录
    inventoryChanges: [],
    // 当前选中的库存项
    currentInventoryItem: null,
    // 加载状态
    loading: false,
    // 历史记录加载状态
    historyLoading: false,
    // 预警加载状态
    warningLoading: false,
    // 变动记录加载状态
    changesLoading: false,
    // 搜索查询条件
    searchQuery: '',
    // 供应商筛选
    supplierFilter: '',
    // 库存状态筛选
    statusFilter: '',
    // 计价类型筛选
    pricingTypeFilter: '',
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    // 历史记录分页信息
    historyPagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    // 库存统计信息
    statistics: {
      totalValue: 0,
      totalProducts: 0,
      warningProducts: 0,
      outOfStockProducts: 0,
      normalProducts: 0,
      slowMovingProducts: 0
    },
    // 统计加载状态
    statisticsLoading: false,
    // 库存价值统计
    valueStatistics: {
      totalValue: 0,
      purchaseValue: 0,
      retailValue: 0,
      profitMargin: 0
    },
    // 价值统计加载状态
    valueLoading: false
  }),
  
  getters: {
    /**
     * 获取筛选后的库存列表
     */
    filteredInventory: (state) => {
      let filtered = [...state.inventory];
      
      // 按搜索条件筛选
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(item => 
          item.productName?.toLowerCase().includes(query) ||
          item.productCode?.toLowerCase().includes(query) ||
          item.barcode?.toLowerCase().includes(query) ||
          item.supplierName?.toLowerCase().includes(query)
        );
      }
      
      // 按供应商筛选
      if (state.supplierFilter) {
        filtered = filtered.filter(item => 
          item.supplierId === state.supplierFilter
        );
      }
      
      // 按库存状态筛选
      if (state.statusFilter) {
        filtered = filtered.filter(item => 
          item.inventoryStatus === state.statusFilter
        );
      }
      
      // 按计价类型筛选
      if (state.pricingTypeFilter) {
        filtered = filtered.filter(item => 
          item.pricingType === state.pricingTypeFilter
        );
      }
      
      return filtered;
    },
    
    /**
     * 获取正常库存商品列表
     */
    normalInventory: (state) => {
      return state.inventory.filter(item => item.inventoryStatus === 'NORMAL');
    },
    
    /**
     * 获取预警库存商品列表
     */
    warningInventory: (state) => {
      return state.inventory.filter(item => item.inventoryStatus === 'WARNING');
    },
    
    /**
     * 获取缺货商品列表
     */
    outOfStockInventory: (state) => {
      return state.inventory.filter(item => item.inventoryStatus === 'OUT_OF_STOCK');
    },
    
    /**
     * 获取滞销商品列表
     */
    slowMovingInventory: (state) => {
      return state.inventory.filter(item => item.inventoryStatus === 'SLOW_MOVING');
    },
    
    /**
     * 获取库存状态统计
     */
    statusStats: (state) => {
      const stats = {
        NORMAL: 0,
        WARNING: 0,
        OUT_OF_STOCK: 0,
        SLOW_MOVING: 0,
        EXPIRED: 0
      };
      
      state.inventory.forEach(item => {
        if (stats.hasOwnProperty(item.inventoryStatus)) {
          stats[item.inventoryStatus]++;
        }
      });
      
      return stats;
    },
    
    /**
     * 获取计价类型统计
     */
    pricingTypeStats: (state) => {
      const stats = {
        NORMAL: 0,
        WEIGHT: 0,
        PIECE: 0,
        VARIABLE: 0
      };
      
      state.inventory.forEach(item => {
        if (stats.hasOwnProperty(item.pricingType)) {
          stats[item.pricingType]++;
        }
      });
      
      return stats;
    },
    
    /**
     * 获取库存总价值
     */
    totalInventoryValue: (state) => {
      return state.inventory.reduce((total, item) => {
        return total + (item.currentStock * item.avgCost || 0);
      }, 0);
    }
  },
  
  actions: {
    /**
     * 获取库存列表（分页）
     */
    async fetchInventory(params = {}) {
      this.loading = true;
      try {
        const queryParams = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          productName: this.searchQuery || undefined,
          supplierId: this.supplierFilter || undefined,
          inventoryStatus: this.statusFilter || undefined,
          pricingType: this.pricingTypeFilter || undefined,
          ...params
        };
        
        const result = await InventoryApi.findPage(queryParams);
        
        if (result && result.rows) {
          this.inventory = result.rows;
          this.pagination.total = result.totalRows;
          this.pagination.current = result.pageNo;
          this.pagination.pageSize = result.pageSize;
        }
        
        return result;
      } catch (error) {
        console.error('获取库存列表失败:', error);
        message.error('获取库存列表失败');
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 获取所有库存列表（不分页）
     */
    async fetchAllInventory(params = {}) {
      try {
        const result = await InventoryApi.findList(params);
        
        if (result) {
          return result;
        }
        
        return [];
      } catch (error) {
        console.error('获取所有库存列表失败:', error);
        message.error('获取所有库存列表失败');
        throw error;
      }
    },
    
    /**
     * 根据商品ID获取库存信息
     */
    async fetchInventoryByProduct(productId) {
      try {
        const result = await InventoryApi.getInventoryByProduct({ productId });
        
        if (result) {
          this.currentInventoryItem = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取商品库存信息失败:', error);
        message.error('获取商品库存信息失败');
        throw error;
      }
    },
    
    /**
     * 根据供应商ID获取库存信息
     */
    async fetchInventoryBySupplier(supplierId) {
      try {
        const result = await InventoryApi.getInventoryBySupplier({ supplierId });
        
        if (result) {
          return result;
        }
        
        return [];
      } catch (error) {
        console.error('获取供应商库存信息失败:', error);
        message.error('获取供应商库存信息失败');
        throw error;
      }
    },
    
    /**
     * 获取库存历史记录
     */
    async fetchInventoryHistory(params = {}) {
      this.historyLoading = true;
      try {
        const queryParams = {
          pageNo: this.historyPagination.current,
          pageSize: this.historyPagination.pageSize,
          ...params
        };
        
        const result = await InventoryApi.getInventoryHistory(queryParams);
        
        if (result && result.rows) {
          this.inventoryHistory = result.rows;
          this.historyPagination.total = result.totalRows;
          this.historyPagination.current = result.pageNo;
          this.historyPagination.pageSize = result.pageSize;
        }
        
        return result;
      } catch (error) {
        console.error('获取库存历史记录失败:', error);
        message.error('获取库存历史记录失败');
        throw error;
      } finally {
        this.historyLoading = false;
      }
    },
    
    /**
     * 获取库存变动记录
     */
    async fetchInventoryChanges(params = {}) {
      this.changesLoading = true;
      try {
        const result = await InventoryApi.getInventoryChanges(params);
        
        if (result) {
          this.inventoryChanges = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取库存变动记录失败:', error);
        message.error('获取库存变动记录失败');
        throw error;
      } finally {
        this.changesLoading = false;
      }
    },
    
    /**
     * 获取库存预警信息
     */
    async fetchInventoryAlerts(params = {}) {
      this.warningLoading = true;
      try {
        const result = await InventoryApi.getInventoryAlerts(params);
        
        if (result) {
          this.warningItems = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取库存预警信息失败:', error);
        message.error('获取库存预警信息失败');
        throw error;
      } finally {
        this.warningLoading = false;
      }
    },
    
    /**
     * 获取库存统计信息
     */
    async fetchInventoryStatistics(params = {}) {
      this.statisticsLoading = true;
      try {
        const result = await InventoryApi.getInventoryStatistics(params);
        
        if (result) {
          this.statistics = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取库存统计信息失败:', error);
        message.error('获取库存统计信息失败');
        throw error;
      } finally {
        this.statisticsLoading = false;
      }
    },
    
    /**
     * 获取库存价值统计
     */
    async fetchInventoryValue(params = {}) {
      this.valueLoading = true;
      try {
        const result = await InventoryApi.getInventoryValue(params);
        
        if (result) {
          this.valueStatistics = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取库存价值统计失败:', error);
        message.error('获取库存价值统计失败');
        throw error;
      } finally {
        this.valueLoading = false;
      }
    },
    
    /**
     * 手动调整库存
     */
    async adjustInventory(adjustData) {
      try {
        const result = await InventoryApi.adjustInventory(adjustData);
        
        if (result) {
          message.success('库存调整成功');
          // 重新获取库存列表
          await this.fetchInventory();
          
          // 如果调整的是当前库存项，更新当前库存项信息
          if (this.currentInventoryItem && this.currentInventoryItem.productId === adjustData.productId) {
            await this.fetchInventoryByProduct(adjustData.productId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('库存调整失败:', error);
        message.error('库存调整失败');
        throw error;
      }
    },
    
    /**
     * 批量调整库存
     */
    async batchAdjustInventory(adjustments) {
      try {
        const result = await InventoryApi.batchAdjustInventory({ adjustments });
        
        if (result) {
          message.success(`成功调整 ${adjustments.length} 个商品的库存`);
          // 重新获取库存列表
          await this.fetchInventory();
        }
        
        return result;
      } catch (error) {
        console.error('批量调整库存失败:', error);
        message.error('批量调整库存失败');
        throw error;
      }
    },
    
    /**
     * 设置库存预警阈值
     */
    async setInventoryAlert(alertData) {
      try {
        const result = await InventoryApi.setInventoryAlert(alertData);
        
        if (result) {
          message.success('库存预警设置成功');
          // 重新获取库存列表
          await this.fetchInventory();
          
          // 重新获取预警信息
          await this.fetchInventoryAlerts();
        }
        
        return result;
      } catch (error) {
        console.error('设置库存预警失败:', error);
        message.error('设置库存预警失败');
        throw error;
      }
    },
    
    /**
     * 导出库存报表
     */
    async exportInventory(params = {}) {
      try {
        await InventoryApi.exportInventory(params);
        message.success('库存报表导出成功');
      } catch (error) {
        console.error('导出库存报表失败:', error);
        message.error('导出库存报表失败');
        throw error;
      }
    },
    
    /**
     * 导出库存预警报表
     */
    async exportInventoryAlerts(params = {}) {
      try {
        await InventoryApi.exportInventoryAlerts(params);
        message.success('库存预警报表导出成功');
      } catch (error) {
        console.error('导出库存预警报表失败:', error);
        message.error('导出库存预警报表失败');
        throw error;
      }
    },
    
    /**
     * 导出库存变动报表
     */
    async exportInventoryChanges(params = {}) {
      try {
        await InventoryApi.exportInventoryChanges(params);
        message.success('库存变动报表导出成功');
      } catch (error) {
        console.error('导出库存变动报表失败:', error);
        message.error('导出库存变动报表失败');
        throw error;
      }
    },
    
    /**
     * 设置搜索条件
     */
    setSearchQuery(query) {
      this.searchQuery = query;
    },
    
    /**
     * 设置供应商筛选
     */
    setSupplierFilter(supplierId) {
      this.supplierFilter = supplierId;
    },
    
    /**
     * 设置库存状态筛选
     */
    setStatusFilter(status) {
      this.statusFilter = status;
    },
    
    /**
     * 设置计价类型筛选
     */
    setPricingTypeFilter(pricingType) {
      this.pricingTypeFilter = pricingType;
    },
    
    /**
     * 设置分页信息
     */
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    
    /**
     * 设置历史记录分页信息
     */
    setHistoryPagination(pagination) {
      this.historyPagination = { ...this.historyPagination, ...pagination };
    },
    
    /**
     * 设置当前库存项
     */
    setCurrentInventoryItem(item) {
      this.currentInventoryItem = item;
    },
    
    /**
     * 清空当前库存项
     */
    clearCurrentInventoryItem() {
      this.currentInventoryItem = null;
    },
    
    /**
     * 清空搜索和筛选条件
     */
    clearFilters() {
      this.searchQuery = '';
      this.supplierFilter = '';
      this.statusFilter = '';
      this.pricingTypeFilter = '';
    },
    
    /**
     * 重置分页到第一页
     */
    resetPagination() {
      this.pagination.current = 1;
    },
    
    /**
     * 重置历史记录分页到第一页
     */
    resetHistoryPagination() {
      this.historyPagination.current = 1;
    },
    
    /**
     * 刷新库存列表
     */
    async refreshInventory() {
      await this.fetchInventory();
    },
    
    /**
     * 刷新库存统计信息
     */
    async refreshStatistics() {
      await this.fetchInventoryStatistics();
      await this.fetchInventoryValue();
    },
    
    /**
     * 刷新预警信息
     */
    async refreshAlerts() {
      await this.fetchInventoryAlerts();
    },
    
    /**
     * 清空库存历史记录
     */
    clearInventoryHistory() {
      this.inventoryHistory = [];
    },
    
    /**
     * 清空库存变动记录
     */
    clearInventoryChanges() {
      this.inventoryChanges = [];
    },
    
    /**
     * 判断库存是否预警
     */
    isStockWarning(currentStock, minStock) {
      return InventoryApi.isStockWarning(currentStock, minStock);
    },
    
    /**
     * 判断库存是否缺货
     */
    isOutOfStock(currentStock) {
      return InventoryApi.isOutOfStock(currentStock);
    },
    
    /**
     * 格式化库存数量显示
     */
    formatQuantity(quantity, unit) {
      return InventoryApi.formatQuantity(quantity, unit);
    },
    
    /**
     * 格式化库存价值显示
     */
    formatValue(value) {
      return InventoryApi.formatValue(value);
    }
  }
});