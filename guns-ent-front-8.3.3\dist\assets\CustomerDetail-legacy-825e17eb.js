System.register(["./index-legacy-ee1db0c7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./CustomerApi-legacy-7faff1b0.js"],(function(e,t){"use strict";var a,l,o,d,r,s,u,n,i,m,c,f,g,b,v,p,_,C,y;return{setters:[e=>{a=e._,l=e.r,o=e.X,d=e.a,r=e.f,s=e.w,u=e.d,n=e.g,i=e.t,m=e.b,c=e.c,f=e.F,g=e.e,b=e.m,v=e.Y,p=e.U,_=e.Z,C=e.M},null,null,e=>{y=e.C}],execute:function(){var t=document.createElement("style");t.textContent=".region-tags[data-v-6d7d4582]{display:flex;flex-wrap:wrap;gap:4px}.region-tags .ant-tag[data-v-6d7d4582]{margin:0}\n",document.head.appendChild(t);const T={name:"CustomerDetail",props:{visible:Boolean,data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{emit:t}){const a=l([]),d=async e=>{if(e)try{const t=await y.getCustomerRegions({customerId:e});a.value=t||[]}catch(t){console.error("获取客户关联区域失败:",t),b.error("获取客户关联区域失败"),a.value=[]}else a.value=[]};return o((()=>{var t;return null===(t=e.data)||void 0===t?void 0:t.customerId}),(t=>{t&&e.visible&&d(t)}),{immediate:!0}),o((()=>e.visible),(t=>{var a;t&&null!==(a=e.data)&&void 0!==a&&a.customerId&&d(e.data.customerId)})),{regionList:a,updateVisible:e=>{t("update:visible",e)},getCustomerTypeName:e=>y.getCustomerTypeName(e),getCustomerLevelName:e=>y.getCustomerLevelName(e),getCustomerStatusName:e=>y.getCustomerStatusName(e),getStatusTagColor:e=>y.getStatusTagColor(e),getCustomerLevelTagColor:e=>y.getCustomerLevelTagColor(e),formatAmount:e=>y.formatAmount(e),formatPaymentTerms:e=>y.formatPaymentTerms(e)}}},L={style:{color:"#1890ff","font-weight":"bold"}},N={style:{color:"#f5222d","font-weight":"bold"}},h={style:{color:"#52c41a","font-weight":"bold"}},x={key:0,class:"region-tags"},w={key:1};e("default",a(T,[["render",function(e,t,a,l,o,b){const y=v,T=p,A=_,S=C;return d(),r(S,{title:"客户详情",width:800,visible:a.visible,footer:null,"onUpdate:visible":l.updateVisible},{default:s((()=>[u(A,{column:2,bordered:""},{default:s((()=>[u(y,{label:"客户编码"},{default:s((()=>[n(i(a.data.customerCode),1)])),_:1}),u(y,{label:"客户名称"},{default:s((()=>[n(i(a.data.customerName),1)])),_:1}),u(y,{label:"客户简称"},{default:s((()=>[n(i(a.data.customerShortName||"-"),1)])),_:1}),u(y,{label:"客户类型"},{default:s((()=>[u(T,null,{default:s((()=>[n(i(l.getCustomerTypeName(a.data.customerType)),1)])),_:1})])),_:1}),u(y,{label:"客户等级"},{default:s((()=>[u(T,{color:l.getCustomerLevelTagColor(a.data.customerLevel)},{default:s((()=>[n(i(l.getCustomerLevelName(a.data.customerLevel)),1)])),_:1},8,["color"])])),_:1}),u(y,{label:"状态"},{default:s((()=>[u(T,{color:l.getStatusTagColor(a.data.status)},{default:s((()=>[n(i(l.getCustomerStatusName(a.data.status)),1)])),_:1},8,["color"])])),_:1}),u(y,{label:"联系人"},{default:s((()=>[n(i(a.data.contactPerson||"-"),1)])),_:1}),u(y,{label:"联系电话"},{default:s((()=>[n(i(a.data.contactPhone||"-"),1)])),_:1}),u(y,{label:"手机号码"},{default:s((()=>[n(i(a.data.contactMobile||"-"),1)])),_:1}),u(y,{label:"邮箱地址"},{default:s((()=>[n(i(a.data.contactEmail||"-"),1)])),_:1}),u(y,{label:"联系地址",span:2},{default:s((()=>[n(i(a.data.contactAddress||"-"),1)])),_:1}),u(y,{label:"营业执照号"},{default:s((()=>[n(i(a.data.businessLicenseNo||"-"),1)])),_:1}),u(y,{label:"税务登记号"},{default:s((()=>[n(i(a.data.taxNo||"-"),1)])),_:1}),u(y,{label:"开户银行"},{default:s((()=>[n(i(a.data.bankName||"-"),1)])),_:1}),u(y,{label:"银行账号"},{default:s((()=>[n(i(a.data.bankAccount||"-"),1)])),_:1}),u(y,{label:"信用额度"},{default:s((()=>[m("span",L,i(l.formatAmount(a.data.creditLimit)),1)])),_:1}),u(y,{label:"已用额度"},{default:s((()=>[m("span",N,i(l.formatAmount(a.data.usedCredit)),1)])),_:1}),u(y,{label:"可用额度"},{default:s((()=>[m("span",h,i(l.formatAmount(a.data.availableCredit)),1)])),_:1}),u(y,{label:"账期天数"},{default:s((()=>[n(i(l.formatPaymentTerms(a.data.paymentTerms)),1)])),_:1}),u(y,{label:"关联区域",span:2},{default:s((()=>[l.regionList&&l.regionList.length>0?(d(),c("div",x,[(d(!0),c(f,null,g(l.regionList,(e=>(d(),r(T,{key:e.regionId,color:"blue"},{default:s((()=>[n(i(e.regionName),1)])),_:2},1024)))),128))])):(d(),c("span",w,"-"))])),_:1}),u(y,{label:"备注",span:2},{default:s((()=>[n(i(a.data.remark||"-"),1)])),_:1}),u(y,{label:"创建时间"},{default:s((()=>[n(i(a.data.createTime),1)])),_:1}),u(y,{label:"更新时间"},{default:s((()=>[n(i(a.data.updateTime),1)])),_:1})])),_:1})])),_:1},8,["visible","onUpdate:visible"])}],["__scopeId","data-v-6d7d4582"]]))}}}));
