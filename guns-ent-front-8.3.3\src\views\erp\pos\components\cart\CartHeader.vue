<!--
  购物车头部组件
  
  显示购物车标题、商品数量和操作按钮
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cart-header">
    <!-- 购物车标题 -->
    <div class="cart-title">
      <icon-font iconClass="icon-cart" />
      <span>购物车</span>
      <div class="cart-count-info" v-if="!isEmpty">
        <span class="count-text">{{ itemCount }}种商品，共{{ totalQuantity }}件</span>
        <a-badge 
          :count="itemCount" 
          :number-style="{ backgroundColor: '#52c41a' }"
        />
      </div>
    </div>
    
    <!-- 操作按钮组 -->
    <div class="cart-actions">
      <!-- 清空购物车 -->
      <a-button 
        type="text" 
        size="small" 
        :disabled="isEmpty || loading"
        @click="handleClearCart"
        class="clear-cart-btn"
      >
        <template #icon>
          <icon-font iconClass="icon-delete" />
        </template>
        清空购物车
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { Modal } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'

// 定义组件名称
defineOptions({
  name: 'CartHeader'
})

// 定义Props
const props = defineProps({
  // 商品种类数量
  itemCount: {
    type: Number,
    default: 0
  },
  // 商品总件数
  totalQuantity: {
    type: Number,
    default: 0
  },
  // 是否为空购物车
  isEmpty: {
    type: Boolean,
    default: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'clear-cart'
])

// ==================== 事件处理方法 ====================

/**
 * 处理清空购物车
 */
const handleClearCart = () => {
  // 直接触发事件，让父组件处理确认弹窗
  emit('clear-cart')
}


</script>

<style scoped>
.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 购物车标题 */
.cart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 购物车数量信息 */
.cart-count-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.count-text {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

/* 操作按钮组 */
.cart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 清空购物车按钮样式 */
.clear-cart-btn {
  color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  background-color: transparent !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

.clear-cart-btn:hover {
  color: #fff !important;
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

.clear-cart-btn:disabled {
  color: #d9d9d9 !important;
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5 !important;
}

/* 危险菜单项 */
:deep(.danger-item) {
  color: #ff4d4f !important;
}

:deep(.danger-item:hover) {
  background-color: #fff2f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cart-header {
    padding: 12px;
  }
  
  .cart-title {
    font-size: 14px;
  }
}

/* 动画效果 */
.cart-header {
  transition: all 0.2s ease;
}

.cart-actions .ant-btn {
  transition: all 0.2s ease;
}

.cart-actions .ant-btn:hover {
  transform: translateY(-1px);
}
</style>