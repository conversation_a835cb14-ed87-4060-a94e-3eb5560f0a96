package cn.stylefeng.roses.ent.mobile.invite.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邀请用户封装类
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysInviteUserRequest extends BaseRequest {

    /**
     * 主键
     */
    @ChineseDescription("主键")
    @NotNull(message = "主键id不能为空", groups = {agree.class, disAgree.class})
    private Long inviteUserId;
    /**
     * 邀请机构id
     */
    @NotNull(message = "邀请机构id不能为空", groups = {add.class})
    @ChineseDescription("邀请机构id")
    private Long orgId;
    /**
     * 来自谁的邀请
     */
    @NotNull(message = "来自谁的邀请不能为空", groups = {add.class})
    @ChineseDescription("来自谁的邀请")
    private Long fromUserId;
    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空", groups = {add.class})
    @ChineseDescription("真实姓名")
    private String realName;
    /**
     * 用户手机号
     */
    @NotBlank(message = "用户手机号不能为空", groups = {add.class})
    @ChineseDescription("用户手机号")
    private String phoneNumber;
    /**
     * 手机号验证码
     */
    @ChineseDescription("手机号验证码")
    @NotBlank(message = "手机号验证码不能为空", groups = {add.class})
    private String phoneValidateNumber;
    /**
     * 申请加入理由
     */
    @ChineseDescription("申请加入理由")
    private String applyReason;

    /**
     * 同意申请
     */
    public @interface agree {

    }

    /**
     * 拒绝加入申请
     */
    public @interface disAgree {

    }

}