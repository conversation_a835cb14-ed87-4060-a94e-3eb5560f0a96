import Request from '@/utils/request/request-util';

/**
 * 后台管理 -租户管理api
 *
 */
export class TenantApi {
    /**
   * 分页
   * @param {*} params
   * @returns
   */
  static findPage(params) {
    return Request.getAndLoadData('/tenant/page', params);
  }

  /**
   * 添加
   * @param {*} params
   * @returns
   */
  static add(params) {
    return Request.post('/tenant/add', params);
  }
  /**
   * 编辑
   * @param {*} params
   * @returns
   */
  static edit(params) {
    return Request.post('/tenant/edit', params);
  }
  /**
   * 删除单个
   * @param {*} params
   * @returns
   */
  static delete(params) {
    return Request.post('/tenant/delete', params);
  }
  /**
   * 删除批量
   * @param {*} params
   * @returns
   */
  static batchDelete(params) {
    return Request.post('/tenant/batchDelete', params);
  }
  /**
   * 详情
   * @param {*} params
   * @returns
   */
  static detail(params) {
    return Request.getAndLoadData('/tenant/detail', params);
  }
  /**
   * 列表
   * @param {*} params
   * @returns
   */
  static list(params) {
    return Request.getAndLoadData('/tenant/tenantDropdown', params);
  }

  /**
   * 检查企业名称是否存在
   * @param {*} params
   * @returns
   */
  static checkTenantCode(params) {
    return Request.get('/tenant/checkTenantCode', params);
  }

  /**
   * 获取功能包列表
   * @param {*} params
   * @returns
   */
  static tenantPackageList(params) {
    return Request.get('/tenantPackage/list', params);
  }
}