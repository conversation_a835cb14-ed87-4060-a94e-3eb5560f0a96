package cn.stylefeng.roses.kernel.ca.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * 统一认证服务端异常
 *
 * <AUTHOR>
 * @date 2021/1/20 16:46
 */
public class CaServerException extends ServiceException {

    public CaServerException(AbstractExceptionEnum exception, Object... params) {
        super(CaServerConstants.SSO_SERVER_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public CaServerException(AbstractExceptionEnum exception) {
        super(CaServerConstants.SSO_SERVER_MODULE_NAME, exception);
    }

}
