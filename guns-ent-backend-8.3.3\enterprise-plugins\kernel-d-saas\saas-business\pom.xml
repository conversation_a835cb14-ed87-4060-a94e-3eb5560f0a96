<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-saas</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>saas-business</artifactId>

    <packaging>jar</packaging>

    <properties>
        <mp.version>3.4.0</mp.version>
    </properties>

    <dependencies>

        <!--邮箱的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>email-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--saas的api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>saas-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--系统管理模块-->
        <!--租户的管理需要有system的支撑-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--定时任务-->
        <!--定时创建租户-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>timer-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--配置管理的业务-->
        <!--多租户创建需要copy配置-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>config-business</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
