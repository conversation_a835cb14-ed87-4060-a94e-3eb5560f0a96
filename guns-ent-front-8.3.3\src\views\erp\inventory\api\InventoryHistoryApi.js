import Request from '@/utils/request/request-util';

/**
 * 库存历史管理API
 *
 * <AUTHOR>
 * @since 2025/01/28 16:00
 */
export class InventoryHistoryApi {
  
  /**
   * 分页查询库存历史列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/inventory/history/page', params);
  }

  /**
   * 查询库存历史列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findList(params) {
    return Request.getAndLoadData('/erp/inventory/history/list', params);
  }

  /**
   * 查询商品库存历史
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static productHistory(params) {
    return Request.getAndLoadData('/erp/inventory/history/product', params);
  }

  /**
   * 查询库存历史详情
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/inventory/history/detail', params);
  }

  /**
   * 按操作类型统计库存变动
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static statistics(params) {
    return Request.getAndLoadData('/erp/inventory/history/statistics', params);
  }

  /**
   * 获取操作类型选项
   * @returns {Array}
   */
  static getOperationTypeOptions() {
    return [
      { label: '入库', value: 'IN' },
      { label: '出库', value: 'OUT' },
      { label: '调整', value: 'ADJUST' },
      { label: '销售', value: 'SALE' },
      { label: '设置预警', value: 'SET_ALERT' }
    ];
  }

  /**
   * 获取操作类型名称
   * @param {String} operationType 操作类型
   * @returns {String}
   */
  static getOperationTypeName(operationType) {
    const options = InventoryHistoryApi.getOperationTypeOptions();
    const option = options.find(item => item.value === operationType);
    return option ? option.label : operationType;
  }

  /**
   * 获取操作类型标签颜色
   * @param {String} operationType 操作类型
   * @returns {String}
   */
  static getOperationTypeColor(operationType) {
    switch (operationType) {
      case 'IN':
        return 'green';
      case 'OUT':
        return 'red';
      case 'ADJUST':
        return 'blue';
      case 'SALE':
        return 'orange';
      case 'SET_ALERT':
        return 'purple';
      default:
        return 'default';
    }
  }

  /**
   * 格式化数量变化
   * @param {Number} quantity 数量
   * @param {String} operationType 操作类型
   * @returns {String}
   */
  static formatQuantityChange(quantity, operationType) {
    if (!quantity && quantity !== 0) return '0';
    
    const numQuantity = parseFloat(quantity) || 0;
    const formattedQuantity = Math.abs(numQuantity).toFixed(3);
    
    switch (operationType) {
      case 'IN':
        return `+${formattedQuantity}`;
      case 'OUT':
      case 'SALE':
        return `-${formattedQuantity}`;
      case 'ADJUST':
        return numQuantity >= 0 ? `+${formattedQuantity}` : `-${formattedQuantity}`;
      case 'SET_ALERT':
        return formattedQuantity;
      default:
        return numQuantity >= 0 ? `+${formattedQuantity}` : `-${formattedQuantity}`;
    }
  }

  /**
   * 格式化金额
   * @param {Number} amount 金额
   * @returns {String}
   */
  static formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toFixed(2);
  }

  /**
   * 导出库存历史数据
   * @param {Object} params 导出参数
   * @returns {Promise}
   */
  static exportHistory(params) {
    return Request.download('/erp/inventory/history/export', params);
  }
}
