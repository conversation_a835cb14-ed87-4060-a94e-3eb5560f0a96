/**
 * POS系统用户反馈工具类
 * 
 * 提供统一的用户反馈机制，包括消息提示、通知、确认对话框等
 *
 * <AUTHOR>
 * @since 2025/08/01 21:30
 */
import { message, notification, Modal } from 'ant-design-vue';
import { h } from 'vue';

export class PosFeedback {

  // 反馈类型
  static FEEDBACK_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info',
    LOADING: 'loading'
  };

  // 通知位置
  static NOTIFICATION_PLACEMENT = {
    TOP_LEFT: 'topLeft',
    TOP_RIGHT: 'topRight',
    BOTTOM_LEFT: 'bottomLeft',
    BOTTOM_RIGHT: 'bottomRight'
  };

  // 消息配置
  static messageConfig = {
    duration: 3,
    maxCount: 5,
    top: '24px'
  };

  // 通知配置
  static notificationConfig = {
    placement: 'topRight',
    duration: 4.5,
    maxCount: 3
  };

  /**
   * 初始化反馈系统
   */
  static init() {
    // 配置全局消息
    message.config(this.messageConfig);
    
    // 配置全局通知
    notification.config(this.notificationConfig);
  }

  /**
   * 显示成功消息
   * @param {string} content 消息内容
   * @param {Object} options 选项
   */
  static success(content, options = {}) {
    const { duration = 3, onClose, key } = options;
    
    return message.success({
      content,
      duration,
      onClose,
      key
    });
  }

  /**
   * 显示错误消息
   * @param {string} content 消息内容
   * @param {Object} options 选项
   */
  static error(content, options = {}) {
    const { duration = 5, onClose, key } = options;
    
    return message.error({
      content,
      duration,
      onClose,
      key
    });
  }

  /**
   * 显示警告消息
   * @param {string} content 消息内容
   * @param {Object} options 选项
   */
  static warning(content, options = {}) {
    const { duration = 4, onClose, key } = options;
    
    return message.warning({
      content,
      duration,
      onClose,
      key
    });
  }

  /**
   * 显示信息消息
   * @param {string} content 消息内容
   * @param {Object} options 选项
   */
  static info(content, options = {}) {
    const { duration = 3, onClose, key } = options;
    
    return message.info({
      content,
      duration,
      onClose,
      key
    });
  }

  /**
   * 显示加载消息
   * @param {string} content 消息内容
   * @param {Object} options 选项
   */
  static loading(content, options = {}) {
    const { duration = 0, onClose, key } = options;
    
    return message.loading({
      content,
      duration,
      onClose,
      key
    });
  }

  /**
   * 显示成功通知
   * @param {string} title 标题
   * @param {string} description 描述
   * @param {Object} options 选项
   */
  static notifySuccess(title, description = '', options = {}) {
    const {
      duration = 4.5,
      placement = this.NOTIFICATION_PLACEMENT.TOP_RIGHT,
      onClose,
      onClick,
      key,
      btn
    } = options;

    return notification.success({
      message: title,
      description,
      duration,
      placement,
      onClose,
      onClick,
      key,
      btn
    });
  }

  /**
   * 显示错误通知
   * @param {string} title 标题
   * @param {string} description 描述
   * @param {Object} options 选项
   */
  static notifyError(title, description = '', options = {}) {
    const {
      duration = 0, // 错误通知不自动关闭
      placement = this.NOTIFICATION_PLACEMENT.TOP_RIGHT,
      onClose,
      onClick,
      key,
      btn
    } = options;

    return notification.error({
      message: title,
      description,
      duration,
      placement,
      onClose,
      onClick,
      key,
      btn
    });
  }

  /**
   * 显示警告通知
   * @param {string} title 标题
   * @param {string} description 描述
   * @param {Object} options 选项
   */
  static notifyWarning(title, description = '', options = {}) {
    const {
      duration = 6,
      placement = this.NOTIFICATION_PLACEMENT.TOP_RIGHT,
      onClose,
      onClick,
      key,
      btn
    } = options;

    return notification.warning({
      message: title,
      description,
      duration,
      placement,
      onClose,
      onClick,
      key,
      btn
    });
  }

  /**
   * 显示信息通知
   * @param {string} title 标题
   * @param {string} description 描述
   * @param {Object} options 选项
   */
  static notifyInfo(title, description = '', options = {}) {
    const {
      duration = 4.5,
      placement = this.NOTIFICATION_PLACEMENT.TOP_RIGHT,
      onClose,
      onClick,
      key,
      btn
    } = options;

    return notification.info({
      message: title,
      description,
      duration,
      placement,
      onClose,
      onClick,
      key,
      btn
    });
  }

  /**
   * 显示确认对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @param {Object} options 选项
   */
  static confirm(title, content = '', options = {}) {
    const {
      okText = '确定',
      cancelText = '取消',
      okType = 'primary',
      onOk,
      onCancel,
      centered = true,
      maskClosable = false
    } = options;

    return Modal.confirm({
      title,
      content,
      okText,
      cancelText,
      okType,
      onOk,
      onCancel,
      centered,
      maskClosable
    });
  }

  /**
   * 显示信息对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @param {Object} options 选项
   */
  static showInfo(title, content = '', options = {}) {
    const {
      okText = '知道了',
      onOk,
      centered = true,
      maskClosable = false
    } = options;

    return Modal.info({
      title,
      content,
      okText,
      onOk,
      centered,
      maskClosable
    });
  }

  /**
   * 显示成功对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @param {Object} options 选项
   */
  static showSuccess(title, content = '', options = {}) {
    const {
      okText = '确定',
      onOk,
      centered = true,
      maskClosable = false
    } = options;

    return Modal.success({
      title,
      content,
      okText,
      onOk,
      centered,
      maskClosable
    });
  }

  /**
   * 显示错误对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @param {Object} options 选项
   */
  static showError(title, content = '', options = {}) {
    const {
      okText = '确定',
      onOk,
      centered = true,
      maskClosable = false
    } = options;

    return Modal.error({
      title,
      content,
      okText,
      onOk,
      centered,
      maskClosable
    });
  }

  /**
   * 显示警告对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @param {Object} options 选项
   */
  static showWarning(title, content = '', options = {}) {
    const {
      okText = '确定',
      onOk,
      centered = true,
      maskClosable = false
    } = options;

    return Modal.warning({
      title,
      content,
      okText,
      onOk,
      centered,
      maskClosable
    });
  }

  /**
   * POS专用反馈方法
   */

  /**
   * 商品添加成功反馈
   */
  static productAdded(productName, quantity = 1) {
    this.success(`已添加 ${productName} × ${quantity}`);
  }

  /**
   * 商品移除反馈
   */
  static productRemoved(productName) {
    this.info(`已移除 ${productName}`);
  }

  /**
   * 订单创建成功反馈
   */
  static orderCreated(orderNo) {
    this.notifySuccess(
      '订单创建成功',
      `订单号：${orderNo}`,
      {
        duration: 3,
        btn: h('a-button', {
          type: 'primary',
          size: 'small',
          onClick: () => {
            // 可以添加查看订单详情的逻辑
            notification.close(orderNo);
          }
        }, '查看详情')
      }
    );
  }

  /**
   * 支付成功反馈
   */
  static paymentSuccess(orderNo, amount, paymentMethod) {
    this.notifySuccess(
      '支付成功',
      `订单 ${orderNo} 支付成功\n支付金额：¥${amount}\n支付方式：${paymentMethod}`,
      { duration: 5 }
    );
  }

  /**
   * 支付失败反馈
   */
  static paymentFailed(orderNo, reason) {
    this.notifyError(
      '支付失败',
      `订单 ${orderNo} 支付失败\n失败原因：${reason}`,
      {
        btn: h('a-button', {
          type: 'primary',
          size: 'small',
          onClick: () => {
            // 可以添加重试支付的逻辑
            notification.close(`payment_failed_${orderNo}`);
          }
        }, '重试支付')
      }
    );
  }

  /**
   * 库存不足警告
   */
  static stockInsufficient(productName, currentStock, requiredQuantity) {
    this.notifyWarning(
      '库存不足',
      `商品 ${productName} 库存不足\n当前库存：${currentStock}\n需要数量：${requiredQuantity}`,
      { duration: 8 }
    );
  }

  /**
   * 会员信息加载成功
   */
  static memberLoaded(memberName, memberLevel) {
    this.success(`会员 ${memberName}（${memberLevel}）信息加载成功`);
  }

  /**
   * 网络连接错误反馈
   */
  static networkError(operation = '') {
    const message = operation ? `${operation}失败，网络连接异常` : '网络连接异常';
    this.notifyError(
      '网络错误',
      message,
      {
        btn: h('a-button', {
          type: 'primary',
          size: 'small',
          onClick: () => {
            window.location.reload();
          }
        }, '刷新页面')
      }
    );
  }

  /**
   * 操作确认
   */
  static confirmOperation(title, content, onConfirm, onCancel) {
    return this.confirm(title, content, {
      onOk: onConfirm,
      onCancel: onCancel,
      okType: 'primary'
    });
  }

  /**
   * 危险操作确认
   */
  static confirmDangerousOperation(title, content, onConfirm, onCancel) {
    return this.confirm(title, content, {
      onOk: onConfirm,
      onCancel: onCancel,
      okType: 'danger',
      okText: '确认删除',
      cancelText: '取消'
    });
  }

  /**
   * 清除所有消息
   */
  static clearAllMessages() {
    message.destroy();
  }

  /**
   * 清除所有通知
   */
  static clearAllNotifications() {
    notification.destroy();
  }

  /**
   * 清除所有反馈
   */
  static clearAll() {
    this.clearAllMessages();
    this.clearAllNotifications();
  }
}
