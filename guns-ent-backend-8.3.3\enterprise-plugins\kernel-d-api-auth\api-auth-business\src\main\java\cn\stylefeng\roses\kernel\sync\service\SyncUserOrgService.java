package cn.stylefeng.roses.kernel.sync.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.sync.factory.SyncUserOrgFactory;
import cn.stylefeng.roses.kernel.sync.pojo.UserOrgSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserOrgService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步业务-用户组织机构的关联信息
 *
 * <AUTHOR>
 * @since 2023/10/30 14:07
 */
@Service
public class SyncUserOrgService {

    @Resource
    private SysUserOrgService sysUserOrgService;

    /**
     * 获取所有用户的机构关联信息
     *
     * <AUTHOR>
     * @since 2023/10/30 14:08
     */
    public List<UserOrgSyncVo> getTotalUserOrg() {
        try {
            // 关闭多租户开关
            TenantSwitchHolder.set(false);

            List<SysUserOrg> sysUserOrgs = sysUserOrgService.list(createWrapper());

            // 组织机构信息转化为返回的VO信息
            return SyncUserOrgFactory.createUserOrgVo(sysUserOrgs);

        } finally {
            TenantSwitchHolder.remove();
        }
    }

    /**
     * 分页获取用户和机构的关联信息
     *
     * <AUTHOR>
     * @since 2023/10/30 14:09
     */
    public PageResult<UserOrgSyncVo> getPageUserOrgs(BaseRequest baseRequest) {

        Page<SysUserOrg> sysUserOrgPage = PageFactory.defaultPage(baseRequest);

        LambdaQueryWrapper<SysUserOrg> wrapper = this.createWrapper();

        Page<SysUserOrg> page;
        try {
            TenantSwitchHolder.set(false);
            page = this.sysUserOrgService.page(sysUserOrgPage, wrapper);
        } finally {
            TenantSwitchHolder.remove();
        }

        if (ObjectUtil.isEmpty(page.getRecords())) {
            return PageResultFactory.createPageResult(new ArrayList<>(), page.getTotal(), Convert.toInt(page.getSize()),
                    Convert.toInt(page.getCurrent()));
        }

        List<SysUserOrg> records = page.getRecords();
        List<UserOrgSyncVo> sysUserVo = SyncUserOrgFactory.createUserOrgVo(records);
        return PageResultFactory.createPageResult(sysUserVo, page.getTotal(), Convert.toInt(page.getSize()),
                Convert.toInt(page.getCurrent()));
    }

    /**
     * 创建wrapper信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:03
     */
    private LambdaQueryWrapper<SysUserOrg> createWrapper() {

        LambdaQueryWrapper<SysUserOrg> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询必要字段
        queryWrapper.select(SysUserOrg::getUserOrgId, SysUserOrg::getUserId, SysUserOrg::getOrgId, SysUserOrg::getMainFlag,
                SysUserOrg::getPositionId);

        // 根据id排序
        queryWrapper.orderByAsc(SysUserOrg::getUserOrgId);

        return queryWrapper;
    }

}
