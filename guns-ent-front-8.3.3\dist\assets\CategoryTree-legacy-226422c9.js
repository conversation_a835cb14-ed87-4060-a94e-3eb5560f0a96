System.register(["./UniversalTree-legacy-6dcdf778.js","./productCategoryApi-legacy-247b2407.js","./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js"],(function(e,a){"use strict";var l,t,o,r,n,d,s;return{setters:[e=>{l=e.U},e=>{t=e.P},e=>{o=e._,r=e.r,n=e.L,d=e.a,s=e.f},null],execute:function(){const a=Object.assign({name:"CategoryTree"},{__name:"CategoryTree",props:{lazyLoad:{type:Boolean,default:!0}},emits:["select"],setup(e,{expose:a,emit:o}){const c=e,i=o,u=r(),y={api:t.findTree,lazyLoadApi:t.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},g={key:"categoryId",title:"categoryName",children:"children",hasChildren:"hasChildren",level:"categoryLevel"},v={title:"产品分类",showHeader:!1,showSearch:!0,searchPlaceholder:"请输入分类名称搜索",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0},h=n((()=>({selectable:!0,expandable:!0,lazyLoad:c.lazyLoad,defaultExpandLevel:2,allowMultiSelect:!1}))),p={allowAdd:!1,allowEdit:!1,allowDelete:!1},f=(e,a)=>{if(e.length>0){const l=a[0];i("select",e[0],l.categoryName)}else i("select",null)},S=e=>{},m=e=>{},L=e=>{},w=e=>{console.error("产品分类树数据加载失败:",e)};return a({reload:()=>{var e;null===(e=u.value)||void 0===e||e.reload()},getSelectedNodes:()=>{var e;return null===(e=u.value)||void 0===e?void 0:e.getSelectedNodes()},setSelectedKeys:e=>{var a;null===(a=u.value)||void 0===a||a.setSelectedKeys(e)},selectedKeys:n((()=>{var e;return(null===(e=u.value)||void 0===e?void 0:e.getSelectedNodes())||[]}))}),(e,a)=>(d(),s(l,{ref_key:"universalTreeRef",ref:u,"data-source":y,"field-mapping":g,"display-config":v,"interaction-config":h.value,"action-config":p,onSelect:f,onExpand:S,onSearch:m,onLoad:L,onLoadError:w},null,8,["interaction-config"]))}});e("default",o(a,[["__scopeId","data-v-32a083d3"]]))}}}));
