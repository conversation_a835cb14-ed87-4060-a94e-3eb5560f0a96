package cn.stylefeng.roses.kernel.pay.mapper;

import cn.stylefeng.roses.kernel.pay.api.entity.Goods;
import cn.stylefeng.roses.kernel.pay.pojo.request.GoodsRequest;
import cn.stylefeng.roses.kernel.pay.pojo.response.GoodsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
public interface GoodsMapper extends BaseMapper<Goods> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    List<GoodsVo> customFindList(@Param("page") Page page, @Param("param")GoodsRequest request);

}
