package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;

/**
 * 会员相关异常
 *
 * <AUTHOR>
 * @since 2025/08/01 20:25
 */
public class MemberException extends PosException {

    public MemberException(AbstractExceptionEnum exception) {
        super(exception);
    }

    public MemberException(AbstractExceptionEnum exception, Object... params) {
        super(exception, params);
    }

    /**
     * 会员不存在异常
     */
    public static MemberException notFound(String memberInfo) {
        return new MemberException(ErpPosExceptionEnum.MEMBER_NOT_EXIST, memberInfo);
    }

    /**
     * 会员积分不足异常
     */
    public static MemberException pointsInsufficient(String memberName, Integer currentPoints, Integer requiredPoints) {
        return new MemberException(ErpPosExceptionEnum.MEMBER_POINTS_INSUFFICIENT, memberName, currentPoints, requiredPoints);
    }

    /**
     * 会员卡已过期异常
     */
    public static MemberException cardExpired(String cardNumber) {
        return new MemberException(ErpPosExceptionEnum.MEMBER_CARD_EXPIRED, cardNumber);
    }
}
