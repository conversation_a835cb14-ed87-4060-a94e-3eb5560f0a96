package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * 库存管理相关常量
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
public interface InventoryConstants {

    /**
     * 库存操作类型
     */
    interface OperationType {
        /**
         * 入库
         */
        String IN = "IN";

        /**
         * 出库
         */
        String OUT = "OUT";

        /**
         * 调整
         */
        String ADJUST = "ADJUST";

        /**
         * 销售
         */
        String SALE = "SALE";

        /**
         * 设置预警值
         */
        String SET_ALERT = "SET_ALERT";
    }

    /**
     * 关联单据类型
     */
    interface ReferenceType {
        /**
         * 采购入库单
         */
        String PURCHASE_ORDER = "PURCHASE_ORDER";

        /**
         * 销售单
         */
        String SALE_ORDER = "SALE_ORDER";

        /**
         * 调整单
         */
        String ADJUST_ORDER = "ADJUST_ORDER";

        /**
         * 手动操作
         */
        String MANUAL = "MANUAL";
    }

    /**
     * 库存状态
     */
    interface Status {
        /**
         * 正常
         */
        String NORMAL = "NORMAL";

        /**
         * 预警
         */
        String WARNING = "WARNING";

        /**
         * 缺货
         */
        String OUT_OF_STOCK = "OUT_OF_STOCK";
    }

    /**
     * 默认值
     */
    interface DefaultValue {
        /**
         * 默认最小库存
         */
        String DEFAULT_MIN_STOCK = "0";

        /**
         * 默认当前库存
         */
        String DEFAULT_CURRENT_STOCK = "0";

        /**
         * 默认平均成本
         */
        String DEFAULT_AVG_COST = "0";

        /**
         * 默认库存总价值
         */
        String DEFAULT_TOTAL_VALUE = "0";
    }

}