package cn.stylefeng.roses.kernel.ca.server.core.threadlocal;


import cn.stylefeng.roses.kernel.ca.server.modular.manage.entity.SsoClient;

/**
 * 临时缓存当前操作的Ca客户端数据
 *
 * <AUTHOR>
 * @date 2021/1/27 11:01
 */
public class CaClientHolder {

    private static final ThreadLocal<SsoClient> CA_CLIENT_HOLDER = new ThreadLocal<>();

    public static void set(SsoClient caClient) {
        CA_CLIENT_HOLDER.set(caClient);
    }

    public static SsoClient get() {
        return CA_CLIENT_HOLDER.get();
    }

    public static void remove() {
        CA_CLIENT_HOLDER.remove();
    }

}
