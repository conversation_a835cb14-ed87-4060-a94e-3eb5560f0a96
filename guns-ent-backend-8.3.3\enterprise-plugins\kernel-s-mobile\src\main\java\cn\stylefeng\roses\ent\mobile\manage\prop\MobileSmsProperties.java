package cn.stylefeng.roses.ent.mobile.manage.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 移动端短信发送模板的配置
 *
 * <AUTHOR>
 * @since 2024-03-25 10:26
 */
@Data
@Component
@ConfigurationProperties(prefix = "ent.mobile")
public class MobileSmsProperties {

    /**
     * 短信发送模板的名称
     * <p>
     * 用在更换手机验证中
     */
    private String changePhoneSmsTemplateCode;

    /**
     * 短信验证码模板中，验证码编码的参数名
     * <p>
     * 用在更换手机验证中
     */
    private String changePhoneSmsCodeFieldName;

    /**
     * 邀请用户的短信模板编号
     */
    private String inviteSmsTemplateCode;

    /**
     * 邀请用户的短信模板，验证码的参数名
     */
    private String inviteSmsCodeFieldName;

}
