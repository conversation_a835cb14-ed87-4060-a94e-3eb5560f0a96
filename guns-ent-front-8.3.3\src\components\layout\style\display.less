@import './themes/default.less';

@media only screen and (max-width: @screen-xs-max) {
  .hidden-xs-only {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-sm-min) {
  .hidden-sm-and-up {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-sm-min) and (max-width: @screen-sm-max) {
  .hidden-sm-only {
    display: none !important;
  }
}

@media only screen and (max-width: @screen-sm-max) {
  .hidden-sm-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-md-min) {
  .hidden-md-and-up {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-md-min) and (max-width: @screen-md-max) {
  .hidden-md-only {
    display: none !important;
  }
}

@media only screen and (max-width: @screen-md-max) {
  .hidden-md-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-lg-min) {
  .hidden-lg-and-up {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-lg-min) and (max-width: @screen-lg-max) {
  .hidden-lg-only {
    display: none !important;
  }
}

@media only screen and (max-width: @screen-lg-max) {
  .hidden-lg-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-xl-min) {
  .hidden-xl-and-up {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-xl-min) and (max-width: @screen-xl-max) {
  .hidden-xl-only {
    display: none !important;
  }
}

@media only screen and (max-width: @screen-xl-max) {
  .hidden-xl-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-xxl-min) {
  .hidden-xxl-and-up {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-xxl-min) and (max-width: @screen-xxl-max) {
  .hidden-xxl-only {
    display: none !important;
  }
}

@media only screen and (max-width: @screen-xxl-max) {
  .hidden-xxl-and-down {
    display: none !important;
  }
}

@media only screen and (min-width: @screen-xxxl-min) {
  .hidden-xxxl-only {
    display: none !important;
  }
}
