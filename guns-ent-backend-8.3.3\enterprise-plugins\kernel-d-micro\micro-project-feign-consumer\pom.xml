<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-micro</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>micro-project-feign-consumer</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- 微服务核心包 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>micro-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--资源扫描-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- web应用程序 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

</project>
