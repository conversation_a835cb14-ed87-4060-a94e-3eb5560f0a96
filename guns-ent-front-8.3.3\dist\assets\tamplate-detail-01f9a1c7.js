import{_ as O,r as s,o as z,X as E,a,f as u,w as l,g as I,h as f,b as r,aR as b,d as m,c as p,F as $,e as G,t as g,aS as y,B as H,u as M,v as U,G as X,H as q,ch as A}from"./index-18a1ea24.js";import J from"./config-data-9ea15c30.js";import P from"./template-add-edit-23974592.js";/* empty css              */import"./ThemeTemplateFieldApi-b2a7ece4.js";import"./template-form-e6240596.js";import"./ThemeTemplateApi-ac0cf8e8.js";const Q={class:"content"},W={class:"content-item"},Y={key:0},Z={key:1},ee={class:"content-item"},te={__name:"tamplate-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(k,{emit:C}){const i=k,h=C,_=s("1"),w=s([{key:"1",name:"\u57FA\u7840\u4FE1\u606F",icon:"icon-tab-baseinfo"},{key:"2",name:"\u6A21\u677F\u914D\u7F6E",icon:"icon-menu-zuzhijiagou"}]),o=s({}),d=s(null),c=s(!1),B=s([{name:"\u6A21\u677F\u540D\u79F0",value:"templateName"},{name:"\u6A21\u677F\u7F16\u7801",value:"templateCode"},{name:"\u6A21\u677F\u7C7B\u578B",value:"templateType"}]);z(()=>{v()}),E(()=>i.data,e=>{e&&v()},{deep:!0});const D=e=>{o.value=Object.assign({},e)},v=()=>{i.data&&(o.value=Object.assign({},i.data),d.value.openConfig(i.data.templateId))},x=e=>{h("update:visible",e)},T=e=>{_.value=e},N=()=>{c.value=!0};return(e,n)=>{const V=H,j=M,L=U,R=X,S=q,F=A;return a(),u(F,{width:800,visible:i.visible,title:"\u6A21\u677F\u8BE6\u60C5",onClose:n[1]||(n[1]=t=>x(!1)),isShowTab:!0,activeKey:_.value,tabList:w.value,onTabChange:T},{default:l(()=>[_.value=="1"?(a(),u(V,{key:0,type:"primary",class:"border-radius edit-btn",onClick:N},{default:l(()=>n[2]||(n[2]=[I("\u7F16\u8F91")])),_:1,__:[2]})):f("",!0),r("div",Q,[b(r("div",W,[m(S,{ref:"formRef",model:o.value,"label-col":{span:6}},{default:l(()=>[m(R,{gutter:16},{default:l(()=>[(a(!0),p($,null,G(B.value,(t,K)=>(a(),u(L,{span:12,key:K},{default:l(()=>[m(j,{label:t.name},{default:l(()=>[t.value=="templateType"?(a(),p("span",Y,g(o.value[t.value]==1?"\u7CFB\u7EDF\u7C7B\u578B":"\u4E1A\u52A1\u7C7B\u578B"),1)):(a(),p("span",Z,g(o.value[t.value]),1))]),_:2},1032,["label"])]),_:2},1024))),128))]),_:1})]),_:1},8,["model"])],512),[[y,_.value=="1"]]),b(r("div",ee,[m(J,{ref_key:"ConfigRef",ref:d},null,512)],512),[[y,_.value=="2"]])]),c.value?(a(),u(P,{key:1,visible:c.value,"onUpdate:visible":n[0]||(n[0]=t=>c.value=t),data:o.value,onDone:D},null,8,["visible","data"])):f("",!0)]),_:1},8,["visible","activeKey","tabList"])}}},ce=O(te,[["__scopeId","data-v-6d71b083"]]);export{ce as default};
