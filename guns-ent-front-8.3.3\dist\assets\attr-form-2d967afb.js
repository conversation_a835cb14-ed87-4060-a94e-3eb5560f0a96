import{s as w,L as q,a as s,f as m,w as t,d as e,g as d,h as N,l as U,u as R,v as x,W as C,J as L,y as T,$ as D,G as k,H as B}from"./index-18a1ea24.js";/* empty css              */const $={__name:"attr-form",props:{form:Object,isUpdate:Boolean},setup(a){const p=a,_=w({fieldName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5C5E\u6027\u540D\u79F0",type:"string",trigger:"blur"}],fieldCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5C5E\u6027\u7F16\u7801",type:"string",trigger:"blur"}],fieldType:[{required:!0,message:"\u8BF7\u8F93\u5165\u5C5E\u6027\u7C7B\u578B",type:"string",trigger:"blur"}],fieldRequired:[{required:!0,message:"\u8BF7\u9009\u62E9\u662F\u5426\u5FC5\u586B",type:"string",trigger:"blur"}],fieldLength:[{message:"\u8BF7\u8F93\u5165\u5C5E\u6027\u957F\u5EA6",type:"number",trigger:"blur"}],fieldDescription:[{message:"\u8BF7\u8F93\u5165\u5C5E\u6027\u63CF\u8FF0",type:"string",trigger:"blur"}]}),g=q(()=>p.form.fieldType!=="file");return(V,l)=>{const f=U,n=R,u=x,r=C,i=L,c=T,v=D,b=k,y=B;return s(),m(y,{ref:"formRef",model:a.form,rules:_,layout:"vertical"},{default:t(()=>[e(b,{gutter:20},{default:t(()=>[e(u,{span:24},{default:t(()=>[e(n,{label:"\u5C5E\u6027\u540D\u79F0:",name:"fieldName"},{default:t(()=>[e(f,{value:a.form.fieldName,"onUpdate:value":l[0]||(l[0]=o=>a.form.fieldName=o),placeholder:"\u8BF7\u8F93\u5165\u5C5E\u6027\u540D\u79F0","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(u,{span:24},{default:t(()=>[e(n,{label:"\u5C5E\u6027\u7F16\u7801:",name:"fieldCode"},{default:t(()=>[e(f,{value:a.form.fieldCode,"onUpdate:value":l[1]||(l[1]=o=>a.form.fieldCode=o),placeholder:"\u8BF7\u8F93\u5165\u5C5E\u6027\u7F16\u7801","allow-clear":"",autocomplete:"off",disabled:a.isUpdate},null,8,["value","disabled"])]),_:1})]),_:1}),e(u,{span:24},{default:t(()=>[e(n,{label:"\u5C5E\u6027\u7C7B\u578B:",name:"fieldType"},{default:t(()=>[e(i,{value:a.form.fieldType,"onUpdate:value":l[2]||(l[2]=o=>a.form.fieldType=o),placeholder:"\u8BF7\u9009\u62E9\u5C5E\u6027\u7C7B\u578B","allow-clear":"",autocomplete:"off"},{default:t(()=>[e(r,{value:"string"},{default:t(()=>l[6]||(l[6]=[d("\u5B57\u7B26\u7C7B\u578B")])),_:1,__:[6]}),e(r,{value:"file"},{default:t(()=>l[7]||(l[7]=[d("\u6587\u4EF6\u7C7B\u578B")])),_:1,__:[7]})]),_:1},8,["value"])]),_:1})]),_:1}),e(u,{span:24},{default:t(()=>[e(n,{label:"\u662F\u5426\u5FC5\u586B",name:"fieldRequired"},{default:t(()=>[e(i,{value:a.form.fieldRequired,"onUpdate:value":l[3]||(l[3]=o=>a.form.fieldRequired=o),placeholder:"\u8BF7\u9009\u62E9\u662F\u5426\u5FC5\u586B","allow-clear":"",autocomplete:"off"},{default:t(()=>[e(r,{value:"Y"},{default:t(()=>l[8]||(l[8]=[d("\u5FC5\u586B")])),_:1,__:[8]}),e(r,{value:"N"},{default:t(()=>l[9]||(l[9]=[d("\u975E\u5FC5\u586B")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1}),e(u,{span:24},{default:t(()=>[g.value?(s(),m(n,{key:0,label:"\u5C5E\u6027\u957F\u5EA6",name:"fieldLength"},{default:t(()=>[e(c,{value:a.form.fieldLength,"onUpdate:value":l[4]||(l[4]=o=>a.form.fieldLength=o),style:{width:"100%"},palceholder:"\u8BF7\u8F93\u5165\u5C5E\u6027\u957F\u5EA6","allow-clear":"",autocomplete:""},null,8,["value"])]),_:1})):N("",!0)]),_:1}),e(u,{span:24},{default:t(()=>[e(n,{label:"\u5C5E\u6027\u63CF\u8FF0",name:"fieldDescription"},{default:t(()=>[e(v,{value:a.form.fieldDescription,"onUpdate:value":l[5]||(l[5]=o=>a.form.fieldDescription=o),placeholder:"\u8BF7\u8F93\u5165\u5C5E\u6027\u63CF\u8FF0","auto-size":{minRows:3,maxRows:5}},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{$ as default};
