package cn.stylefeng.roses.ent.mobile.manage.pojo.common;

import cn.stylefeng.roses.kernel.file.api.format.FileUrlFormatProcess;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.annotation.SimpleFieldFormat;
import lombok.Data;

/**
 * 通用的组织人员信息列表的实体
 *
 * <AUTHOR>
 * @since 2024-04-02 14:06
 */
@Data
public class OrgUserItem {

    /**
     * 元素类型：1-公司，2-部门，3-人员
     */
    @ChineseDescription("元素类型：1-公司，2-部门，3-人员")
    private Integer itemType;

    /**
     * 组织机构id
     */
    @ChineseDescription("组织机构id")
    private Long orgId;

    /**
     * 机构名称
     */
    @ChineseDescription("机构名称")
    private String orgName;

    /**
     * 头像id
     */
    @ChineseDescription("头像id")
    @SimpleFieldFormat(processClass = FileUrlFormatProcess.class)
    private Long avatar;

    /**
     * 用户id
     */
    @ChineseDescription("用户id")
    private Long userId;

    /**
     * 用户姓名
     */
    @ChineseDescription("用户姓名")
    private String realName;

}
