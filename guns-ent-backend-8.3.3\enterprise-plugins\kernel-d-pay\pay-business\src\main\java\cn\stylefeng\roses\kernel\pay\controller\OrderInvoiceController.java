package cn.stylefeng.roses.kernel.pay.controller;

import cn.stylefeng.roses.kernel.pay.entity.OrderInvoice;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderInvoiceRequest;
import cn.stylefeng.roses.kernel.pay.service.OrderInvoiceService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单开票记录控制器
 *
 * <AUTHOR>
 * @since 2024/06/21 16:49
 */
@RestController
@ApiResource(name = "订单开票记录")
public class OrderInvoiceController {

    @Resource
    private OrderInvoiceService orderInvoiceService;

    /**
     * 添加订单开票记录
     *
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    @PostResource(name = "添加订单开票记录", path = "/orderInvoice/add")
    public ResponseData<OrderInvoice> add(@RequestBody @Validated(OrderInvoiceRequest.add.class) OrderInvoiceRequest orderInvoiceRequest) {
        orderInvoiceService.add(orderInvoiceRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看订单开票记录详情
     *
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    @GetResource(name = "查看订单开票记录详情", path = "/orderInvoice/detail")
    public ResponseData<OrderInvoice> detail(@Validated(OrderInvoiceRequest.detail.class) OrderInvoiceRequest orderInvoiceRequest) {
        return new SuccessResponseData<>(orderInvoiceService.detail(orderInvoiceRequest));
    }

    /**
     * 获取订单开票记录列表
     *
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    @GetResource(name = "获取订单开票记录列表", path = "/orderInvoice/list")
    public ResponseData<List<OrderInvoice>> list(OrderInvoiceRequest orderInvoiceRequest) {
        return new SuccessResponseData<>(orderInvoiceService.findList(orderInvoiceRequest));
    }

}
