package cn.stylefeng.roses.kernel.manage.pojo.response;

import cn.stylefeng.roses.kernel.manage.entity.ApiEndpoint;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API接口选中状态
 *
 * <AUTHOR>
 * @since 2023/10/25 15:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiEndpointCheckedFlag extends ApiEndpoint {

    /**
     * 当前URL请求路径是否选中
     */
    @ChineseDescription("当前URL请求路径是否选中")
    private Boolean apiCheckedFlag = false;

}
