/**
 * ERP业务表单验证工具函数
 * 
 * 提供各种业务相关的表单验证规则和验证函数
 */

import { 
  BUSINESS_MODE, 
  PRICING_TYPE, 
  PURCHASE_ORDER_STATUS,
  PRECISION_CONFIG 
} from './constants'

/**
 * 验证供应商编码格式
 * @param {string} code - 供应商编码
 * @returns {boolean} 验证结果
 */
export function validateSupplierCode(code) {
  if (!code || typeof code !== 'string') {
    return false
  }
  // 供应商编码：字母开头，可包含字母、数字、下划线，长度3-20位
  const regex = /^[A-Za-z][A-Za-z0-9_]{2,19}$/
  return regex.test(code)
}

/**
 * 验证商品编码格式
 * @param {string} code - 商品编码
 * @returns {boolean} 验证结果
 */
export function validateProductCode(code) {
  if (!code || typeof code !== 'string') {
    return false
  }
  // 商品编码：可以是纯数字（条码）或字母数字组合，长度6-20位
  const regex = /^[A-Za-z0-9]{6,20}$/
  return regex.test(code)
}

/**
 * 验证价格格式
 * @param {number|string} price - 价格值
 * @param {number} precision - 精度，默认2位小数
 * @returns {boolean} 验证结果
 */
export function validatePrice(price, precision = PRECISION_CONFIG.PRICE) {
  if (price === null || price === undefined || price === '') {
    return false
  }
  const numPrice = Number(price)
  if (isNaN(numPrice) || numPrice < 0) {
    return false
  }
  // 检查小数位数是否超过精度要求
  const decimalPlaces = (numPrice.toString().split('.')[1] || '').length
  return decimalPlaces <= precision
}

/**
 * 验证数量格式
 * @param {number|string} quantity - 数量值
 * @param {number} precision - 精度，默认3位小数
 * @returns {boolean} 验证结果
 */
export function validateQuantity(quantity, precision = PRECISION_CONFIG.QUANTITY) {
  if (quantity === null || quantity === undefined || quantity === '') {
    return false
  }
  const numQuantity = Number(quantity)
  if (isNaN(numQuantity) || numQuantity <= 0) {
    return false
  }
  // 检查小数位数是否超过精度要求
  const decimalPlaces = (numQuantity.toString().split('.')[1] || '').length
  return decimalPlaces <= precision
}

/**
 * 验证销售扣点格式
 * @param {number|string} deduction - 销售扣点值
 * @returns {boolean} 验证结果
 */
export function validateSalesDeduction(deduction) {
  if (deduction === null || deduction === undefined || deduction === '') {
    return false
  }
  const numDeduction = Number(deduction)
  if (isNaN(numDeduction) || numDeduction < 0 || numDeduction > 100) {
    return false
  }
  // 检查小数位数不超过2位
  const decimalPlaces = (numDeduction.toString().split('.')[1] || '').length
  return decimalPlaces <= PRECISION_CONFIG.PERCENTAGE
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 验证结果
 */
export function validatePhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return false
  }
  // 中国大陆手机号格式
  const regex = /^1[3-9]\d{9}$/
  return regex.test(phone)
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 验证结果
 */
export function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return false
  }
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return regex.test(email)
}

/**
 * 验证经营方式是否有效
 * @param {string} businessMode - 经营方式
 * @returns {boolean} 验证结果
 */
export function validateBusinessMode(businessMode) {
  return Object.values(BUSINESS_MODE).includes(businessMode)
}

/**
 * 验证计价类型是否有效
 * @param {string} pricingType - 计价类型
 * @returns {boolean} 验证结果
 */
export function validatePricingType(pricingType) {
  return Object.values(PRICING_TYPE).includes(pricingType)
}

/**
 * 验证采购入库单状态是否有效
 * @param {string} status - 状态
 * @returns {boolean} 验证结果
 */
export function validatePurchaseOrderStatus(status) {
  return Object.values(PURCHASE_ORDER_STATUS).includes(status)
}

/**
 * 根据计价类型验证价格设置的完整性
 * @param {string} pricingType - 计价类型
 * @param {object} priceData - 价格数据对象
 * @returns {object} 验证结果 { valid: boolean, message: string }
 */
export function validatePriceByType(pricingType, priceData) {
  const { retailPrice, unitPrice, piecePrice, referencePrice } = priceData
  
  switch (pricingType) {
    case PRICING_TYPE.NORMAL:
      if (!validatePrice(retailPrice)) {
        return { valid: false, message: '普通商品必须设置有效的零售价格' }
      }
      break
    case PRICING_TYPE.WEIGHT:
      if (!validatePrice(unitPrice)) {
        return { valid: false, message: '计重商品必须设置有效的单位价格' }
      }
      break
    case PRICING_TYPE.PIECE:
      if (!validatePrice(piecePrice)) {
        return { valid: false, message: '计件商品必须设置有效的单份价格' }
      }
      break
    case PRICING_TYPE.VARIABLE:
      // 不定价商品的参考价格是可选的
      if (referencePrice && !validatePrice(referencePrice)) {
        return { valid: false, message: '参考价格格式不正确' }
      }
      break
    default:
      return { valid: false, message: '无效的计价类型' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证经营方式与销售扣点的匹配性
 * @param {string} businessMode - 经营方式
 * @param {number|string} salesDeduction - 销售扣点
 * @returns {object} 验证结果 { valid: boolean, message: string }
 */
export function validateBusinessModeWithDeduction(businessMode, salesDeduction) {
  if (!validateBusinessMode(businessMode)) {
    return { valid: false, message: '无效的经营方式' }
  }
  
  if (businessMode === BUSINESS_MODE.PURCHASE_SALE) {
    // 购销模式不需要销售扣点
    return { valid: true, message: '' }
  } else {
    // 联营和代销模式需要销售扣点
    if (!validateSalesDeduction(salesDeduction)) {
      return { valid: false, message: '联营和代销模式必须设置有效的销售扣点' }
    }
  }
  
  return { valid: true, message: '' }
}

// Ant Design Vue 表单验证规则生成器

/**
 * 生成供应商编码验证规则
 * @returns {Array} Ant Design Vue 验证规则数组
 */
export function createSupplierCodeRules() {
  return [
    { required: true, message: '请输入供应商编码' },
    { 
      validator: (rule, value) => {
        if (value && !validateSupplierCode(value)) {
          return Promise.reject('供应商编码格式不正确（字母开头，3-20位字母数字下划线）')
        }
        return Promise.resolve()
      }
    }
  ]
}

/**
 * 生成商品编码验证规则
 * @returns {Array} Ant Design Vue 验证规则数组
 */
export function createProductCodeRules() {
  return [
    { required: true, message: '请输入商品编码' },
    { 
      validator: (rule, value) => {
        if (value && !validateProductCode(value)) {
          return Promise.reject('商品编码格式不正确（6-20位字母数字组合）')
        }
        return Promise.resolve()
      }
    }
  ]
}

/**
 * 生成价格验证规则
 * @param {boolean} required - 是否必填
 * @param {string} fieldName - 字段名称
 * @returns {Array} Ant Design Vue 验证规则数组
 */
export function createPriceRules(required = true, fieldName = '价格') {
  const rules = []
  
  if (required) {
    rules.push({ required: true, message: `请输入${fieldName}` })
  }
  
  rules.push({
    validator: (rule, value) => {
      if (value && !validatePrice(value)) {
        return Promise.reject(`${fieldName}格式不正确（必须为正数，最多2位小数）`)
      }
      return Promise.resolve()
    }
  })
  
  return rules
}

/**
 * 生成数量验证规则
 * @param {boolean} required - 是否必填
 * @returns {Array} Ant Design Vue 验证规则数组
 */
export function createQuantityRules(required = true) {
  const rules = []
  
  if (required) {
    rules.push({ required: true, message: '请输入数量' })
  }
  
  rules.push({
    validator: (rule, value) => {
      if (value && !validateQuantity(value)) {
        return Promise.reject('数量格式不正确（必须为正数，最多3位小数）')
      }
      return Promise.resolve()
    }
  })
  
  return rules
}

/**
 * 生成销售扣点验证规则
 * @param {boolean} required - 是否必填
 * @returns {Array} Ant Design Vue 验证规则数组
 */
export function createSalesDeductionRules(required = false) {
  const rules = []
  
  if (required) {
    rules.push({ required: true, message: '请输入销售扣点' })
  }
  
  rules.push({
    validator: (rule, value) => {
      if (value && !validateSalesDeduction(value)) {
        return Promise.reject('销售扣点格式不正确（0-100之间的数字，最多2位小数）')
      }
      return Promise.resolve()
    }
  })
  
  return rules
}