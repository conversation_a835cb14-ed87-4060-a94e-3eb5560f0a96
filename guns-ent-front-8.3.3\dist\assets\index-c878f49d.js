import o from"./index-807cd0f5.js";import{a as r,f as t}from"./index-18a1ea24.js";import"./list-51145fbc.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              */import"./SysDictTypeApi-1ce2cbe7.js";import"./detail-d04228d8.js";const k=Object.assign({name:"AuditNormal"},{__name:"index",setup(i){return(m,p)=>(r(),t(o,{apiUrl:"/auditLog/getNormalLog",fieldBusinessCode:"SYSTEM_AUDIT_DEVOPS"}))}});export{k as default};
