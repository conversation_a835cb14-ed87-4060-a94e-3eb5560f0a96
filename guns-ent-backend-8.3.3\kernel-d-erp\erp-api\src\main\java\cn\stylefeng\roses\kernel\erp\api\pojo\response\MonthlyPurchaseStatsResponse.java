package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 月度采购统计响应类
 *
 * <AUTHOR>
 * @since 2025/07/28 10:00
 */
@Data
public class MonthlyPurchaseStatsResponse {

    /**
     * 月份（格式：yyyy-MM）
     */
    @ChineseDescription("月份")
    private String month;

    /**
     * 订单数量
     */
    @ChineseDescription("订单数量")
    private Long orderCount;

    /**
     * 总金额
     */
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

    /**
     * 供应商数量
     */
    @ChineseDescription("供应商数量")
    private Long supplierCount;

}