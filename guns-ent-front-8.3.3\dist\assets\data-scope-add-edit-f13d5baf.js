import{r as u,s as E,L as H,o as J,X,a as d,f as p,w as o,d as v,c as C,F as R,e as U,g as $,t as k,b as G,ah as K,h as Q,m as Y,W as Z,J as ee,u as ae,l as le,H as te,M as se}from"./index-18a1ea24.js";import{_ as ne}from"./index-3a0e5c06.js";import{P as D}from"./PermissionApi-1ac20316.js";import{O as re}from"./OrgApi-021dd6dd.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */const ye={__name:"data-scope-add-edit",props:{visible:Boolean,data:Object,roleId:String,levelNumberList:Array},emits:["update:visible","done"],setup(F,{emit:h}){const m=F,I=h,f=u(!1),g=u(!1),S=u([{key:10,name:"\u4EC5\u672C\u4EBA\u6570\u636E"},{key:20,name:"\u672C\u90E8\u95E8\u6570\u636E"},{key:30,name:"\u672C\u90E8\u95E8\u53CA\u4EE5\u4E0B\u6570\u636E"},{key:31,name:"\u672C\u516C\u53F8\u53CA\u4EE5\u4E0B\u6570\u636E"},{key:32,name:"\u6307\u5B9A\u673A\u6784\u5C42\u7EA7\u53CA\u4EE5\u4E0B"},{key:40,name:"\u6307\u5B9A\u673A\u6784\u96C6\u5408\u6570\u636E"},{key:41,name:"\u6307\u5B9A\u673A\u6784\u53CA\u4EE5\u4E0B"},{key:50,name:"\u5168\u90E8\u6570\u636E"}]),e=u({roleId:m.roleId,defineOrgListName:""}),B=E({dataScopeType:[{required:!0,message:"\u8BF7\u9009\u62E9\u7C7B\u578B",type:"number",trigger:"change"}],defineOrgIdWrapper:[{required:!0,message:"\u8BF7\u9009\u62E9\u8303\u56F4",type:"string",trigger:"change"}],defineOrgListName:[{required:!0,message:"\u8BF7\u9009\u62E9\u8303\u56F4",type:"string",trigger:"blur"}],orgLevelCode:[{required:!0,message:"\u8BF7\u9009\u62E9\u5C42\u7EA7",type:"string",trigger:"change"}]}),y=u(null),b=u(""),N=u(!0),_=u(!1),L=u({selectOrgList:[]}),T=u([]),j=H(()=>a=>{let l="",s=m.levelNumberList.find(i=>i.value==a.levelNumber);return s&&(l=s.name),l});J(async()=>{x(),m.data?(g.value=!0,e.value=Object.assign({},m.data),e.value.dataScopeType==40&&(e.value.defineOrgListWrapper=e.value.defineOrgList.map((a,l)=>({bizId:a,name:e.value.defineOrgListWrapper[l]})),e.value.defineOrgListName=e.value.defineOrgListWrapper.map(a=>a.name).join(","))):g.value=!1});const x=async()=>{T.value=await re.organizationLevelList()},O=a=>{I("update:visible",a)},V=async()=>{await y.value.validate(),f.value=!0;let a;g.value?a=D.editRoleDataScope(e.value):a=D.addRoleDataScope(e.value),a.then(l=>{f.value=!1,Y.success(l.message),O(!1),I("done")}).catch(()=>{f.value=!1})},W=()=>{const{dataScopeType:a,defineOrgIdWrapper:l,defineOrgId:s,defineOrgListWrapper:i}=e.value;N.value=a!=40,a==40&&i?L.value.selectOrgList=i.map(r=>({bizId:r.bizId,name:r.name})):a==41&&s&&l&&(L.value.selectOrgList=[{bizId:s,name:l}]),_.value=!0},q=a=>{var s,i,r;const{dataScopeType:l}=e.value;if(l==40)e.value.defineOrgListName=(s=a.selectOrgList)==null?void 0:s.map(n=>n.name).join(","),e.value.defineOrgListWrapper=(i=a.selectOrgList)==null?void 0:i.map(n=>({bizId:n.bizId,name:n.name})),e.value.defineOrgList=(r=a.selectOrgList)==null?void 0:r.map(n=>n.bizId),y.value.validateFields(["defineOrgListName"]);else if(l==41){const n=a.selectOrgList[0]||{bizId:"",name:""};e.value.defineOrgId=n.bizId,e.value.defineOrgIdWrapper=n.name,y.value.validateFields(["defineOrgIdWrapper"])}};return X(()=>{var a;return(a=e.value)==null?void 0:a.dataScopeType},a=>{if(!a||a&&[40,41].includes(a))b.value="";else{let l=S.value.find(s=>s.key==a);b.value=l.name}},{deep:!0,immediate:!0}),(a,l)=>{const s=Z,i=ee,r=ae,n=le,A=te,M=ne,P=se;return d(),p(P,{width:524,maskClosable:!1,visible:m.visible,"confirm-loading":f.value,forceRender:!0,title:g.value?"\u7F16\u8F91\u6570\u636E\u6743\u9650":"\u65B0\u5EFA\u6570\u636E\u6743\u9650","body-style":{paddingBottom:"8px"},"onUpdate:visible":O,onOk:V,onClose:l[6]||(l[6]=c=>O(!1))},{default:o(()=>[v(A,{ref_key:"formRef",ref:y,model:e.value,rules:B,layout:"vertical"},{default:o(()=>{var c,w,z;return[v(r,{label:"\u7C7B\u578B:",name:"dataScopeType"},{default:o(()=>[v(i,{placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",style:{width:"100%"},value:e.value.dataScopeType,"onUpdate:value":l[0]||(l[0]=t=>e.value.dataScopeType=t),"allow-clear":""},{default:o(()=>[(d(!0),C(R,null,U(S.value,t=>(d(),p(s,{key:t.key,value:t.key},{default:o(()=>[$(k(t.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1}),((c=e.value)==null?void 0:c.dataScopeType)==40?(d(),p(r,{key:0,label:"\u8303\u56F4:",name:"defineOrgListName"},{default:o(()=>[v(n,{value:e.value.defineOrgListName,"onUpdate:value":l[1]||(l[1]=t=>e.value.defineOrgListName=t),placeholder:"\u8BF7\u9009\u62E9\u8303\u56F4",onFocus:W},null,8,["value"])]),_:1})):((w=e.value)==null?void 0:w.dataScopeType)==41?(d(),p(r,{key:1,label:"\u8303\u56F4:",name:"defineOrgIdWrapper"},{default:o(()=>[v(n,{value:e.value.defineOrgIdWrapper,"onUpdate:value":l[2]||(l[2]=t=>e.value.defineOrgIdWrapper=t),placeholder:"\u8BF7\u9009\u62E9\u8303\u56F4",onFocus:W},null,8,["value"])]),_:1})):((z=e.value)==null?void 0:z.dataScopeType)==32?(d(),p(r,{key:2,label:"\u5C42\u7EA7:",name:"orgLevelCode"},{default:o(()=>[v(i,{value:e.value.orgLevelCode,"onUpdate:value":l[3]||(l[3]=t=>e.value.orgLevelCode=t),style:{width:"100%"},placeholder:"\u8BF7\u9009\u62E9\u5C42\u7EA7"},{default:o(()=>[(d(!0),C(R,null,U(T.value,t=>(d(),p(s,{value:t.levelCode,key:t.levelCode},{default:o(()=>[G("span",{style:K({color:t.levelColor})},k(t.levelName)+"("+k(j.value(t))+")",5)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):(d(),p(r,{key:3,label:"\u8303\u56F4:"},{default:o(()=>[v(n,{value:b.value,"onUpdate:value":l[4]||(l[4]=t=>b.value=t),disabled:"",placeholder:"\u8BF7\u9009\u62E9"},null,8,["value"])]),_:1}))]}),_:1},8,["model","rules"]),_.value?(d(),p(M,{key:0,visible:_.value,"onUpdate:visible":l[5]||(l[5]=c=>_.value=c),title:"\u9009\u62E9\u673A\u6784",data:L.value,isRadio:N.value,showTab:["dept"],onDone:q},null,8,["visible","data","isRadio"])):Q("",!0)]),_:1},8,["visible","confirm-loading","title"])}}};export{ye as default};
