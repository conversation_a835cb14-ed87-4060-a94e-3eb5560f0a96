<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.customer.mapper.ErpCustomerRegionMapper">

    <!-- 根据区域ID查询关联的客户 -->
    <select id="getCustomersByRegionId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse">
        SELECT
            c.customer_id,
            c.customer_code,
            c.customer_name,
            c.customer_short_name,
            c.customer_type,
            c.customer_level,
            c.region_id,
            c.contact_person,
            c.contact_phone,
            c.contact_mobile,
            c.contact_email,
            c.contact_address,
            c.business_license_no,
            c.tax_no,
            c.bank_name,
            c.bank_account,
            c.credit_limit,
            c.used_credit,
            c.payment_terms,
            c.status,
            c.remark,
            c.create_time,
            c.create_user,
            c.update_time,
            c.update_user,
            r.region_name as regionName
        FROM
            erp_customer c
        LEFT JOIN
            erp_region r ON c.region_id = r.region_id
        WHERE
            c.customer_id IN (
                SELECT DISTINCT
                    cr.customer_id
                FROM
                    erp_customer_region cr
                WHERE
                    <if test="includeChildRegions and childRegionIds != null and childRegionIds.size > 0">
                        cr.region_id IN
                        <foreach collection="childRegionIds" item="regionId" open="(" separator="," close=")">
                            #{regionId}
                        </foreach>
                    </if>
                    <if test="!includeChildRegions">
                        cr.region_id = #{regionId}
                    </if>
            )
            AND c.del_flag = 'N'
        ORDER BY
            c.create_time DESC
    </select>

    <!-- 统计区域关联的客户数量 -->
    <select id="countCustomersByRegionId" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT c.customer_id)
        FROM
            erp_customer c
        WHERE
            c.customer_id IN (
                SELECT DISTINCT
                    cr.customer_id
                FROM
                    erp_customer_region cr
                WHERE
                    <if test="includeChildRegions and childRegionIds != null and childRegionIds.size > 0">
                        cr.region_id IN
                        <foreach collection="childRegionIds" item="regionId" open="(" separator="," close=")">
                            #{regionId}
                        </foreach>
                    </if>
                    <if test="!includeChildRegions">
                        cr.region_id = #{regionId}
                    </if>
            )
            AND c.del_flag = 'N'
    </select>

</mapper>