package cn.stylefeng.roses.kernel.impexp.user.enums;

import lombok.Getter;

/**
 * 这条记录将会以什么类型录入到数据库，包括：新增、修改、删除
 *
 * <AUTHOR>
 * @since 2024/2/10 10:25
 */
@Getter
public enum OperateTypeEnums {

    /**
     * 新增
     */
    ADD(1, "新增"),

    /**
     * 修改
     */
    EDIT(2, "修改"),

    /**
     * 删除
     */
    DELETE(3, "删除");

    private final Integer code;

    private final String message;

    OperateTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
