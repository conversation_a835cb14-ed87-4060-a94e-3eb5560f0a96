-- 商品分类简化优化 - 索引优化
-- 为erp_product表的category_id字段创建复合索引，提升分类关联查询性能

-- 检查并创建商品表的分类ID索引
-- 这个索引将显著提升按分类查询商品的性能
CREATE INDEX IF NOT EXISTS `idx_erp_product_category_id` ON `erp_product` (`category_id`);

-- 创建复合索引，包含分类ID、删除标记和状态，用于优化常见查询场景
-- 这个索引将优化以下查询：
-- 1. 按分类查询未删除的商品
-- 2. 按分类查询特定状态的商品
-- 3. 统计分类下的商品数量
CREATE INDEX IF NOT EXISTS `idx_erp_product_category_status` ON `erp_product` (`category_id`, `del_flag`, `status`);

-- 为商品分类表创建父级分类索引，优化树形结构查询
-- 这个索引将提升查询子分类的性能
CREATE INDEX IF NOT EXISTS `idx_erp_product_category_parent` ON `erp_product_category` (`parent_id`, `del_flag`);

-- 创建分类表的复合索引，优化分类列表查询
-- 这个索引将优化按层级和排序查询分类的性能
CREATE INDEX IF NOT EXISTS `idx_erp_product_category_level_sort` ON `erp_product_category` (`category_level`, `sort_order`, `del_flag`);

-- 为分类状态创建索引，优化启用状态的分类查询
CREATE INDEX IF NOT EXISTS `idx_erp_product_category_status` ON `erp_product_category` (`status`, `del_flag`);
