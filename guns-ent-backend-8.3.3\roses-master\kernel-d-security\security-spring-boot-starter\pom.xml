<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-security</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>security-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--xss模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-sdk-xss</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--threadLocal清除器-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-sdk-clear-threadlocal</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--图形验证码模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-sdk-captcha</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--count模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-sdk-count</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--国密加解密支持以及请求加密解密-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-sdk-request-encrypt-and-decode</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--黑白名单的sdk-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-sdk-black-white</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--缓存，可选依赖-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
