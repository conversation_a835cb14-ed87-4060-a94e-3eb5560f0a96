/**
 * 供应商状态管理
 * 
 * 管理供应商相关的状态和操作，包括：
 * - 供应商列表管理
 * - 当前供应商信息
 * - 搜索和筛选状态
 * - CRUD操作
 * - 经营方式验证
 * - 商品关联查询
 */
import { defineStore } from 'pinia';
import { SupplierApi } from '@/views/erp/supplier/api/SupplierApi';
import { message } from 'ant-design-vue';

export const useSupplierStore = defineStore({
  id: 'supplier',
  state: () => ({
    // 供应商列表数据
    suppliers: [],
    // 当前选中的供应商
    currentSupplier: null,
    // 加载状态
    loading: false,
    // 搜索查询条件
    searchQuery: '',
    // 经营方式筛选
    businessModeFilter: '',
    // 供应商状态筛选
    statusFilter: '',
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    // 供应商关联的商品列表
    supplierProducts: [],
    // 商品加载状态
    productsLoading: false
  }),
  
  getters: {
    /**
     * 获取筛选后的供应商列表
     */
    filteredSuppliers: (state) => {
      let filtered = [...state.suppliers];
      
      // 按搜索条件筛选
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(supplier => 
          supplier.supplierName?.toLowerCase().includes(query) ||
          supplier.supplierCode?.toLowerCase().includes(query) ||
          supplier.contactPerson?.toLowerCase().includes(query)
        );
      }
      
      // 按经营方式筛选
      if (state.businessModeFilter) {
        filtered = filtered.filter(supplier => 
          supplier.businessMode === state.businessModeFilter
        );
      }
      
      // 按状态筛选
      if (state.statusFilter) {
        filtered = filtered.filter(supplier => 
          supplier.status === state.statusFilter
        );
      }
      
      return filtered;
    },
    
    /**
     * 获取活跃的供应商列表（状态为ACTIVE）
     */
    activeSuppliers: (state) => {
      return state.suppliers.filter(supplier => supplier.status === 'ACTIVE');
    },
    
    /**
     * 获取非联营供应商列表（用于采购入库）
     */
    nonJointVentureSuppliers: (state) => {
      return state.suppliers.filter(supplier => 
        supplier.status === 'ACTIVE' && supplier.businessMode !== 'JOINT_VENTURE'
      );
    },
    
    /**
     * 获取经营方式统计
     */
    businessModeStats: (state) => {
      const stats = {
        PURCHASE_SALE: 0,
        JOINT_VENTURE: 0,
        CONSIGNMENT: 0
      };
      
      state.suppliers.forEach(supplier => {
        if (stats.hasOwnProperty(supplier.businessMode)) {
          stats[supplier.businessMode]++;
        }
      });
      
      return stats;
    }
  },
  
  actions: {
    /**
     * 获取供应商列表（分页）
     */
    async fetchSuppliers(params = {}) {
      this.loading = true;
      try {
        const queryParams = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          supplierName: this.searchQuery || undefined,
          businessMode: this.businessModeFilter || undefined,
          status: this.statusFilter || undefined,
          ...params
        };
        
        const result = await SupplierApi.findPage(queryParams);
        
        if (result && result.rows) {
          this.suppliers = result.rows;
          this.pagination.total = result.totalRows;
          this.pagination.current = result.pageNo;
          this.pagination.pageSize = result.pageSize;
        }
        
        return result;
      } catch (error) {
        console.error('获取供应商列表失败:', error);
        message.error('获取供应商列表失败');
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 获取所有供应商列表（不分页）
     */
    async fetchAllSuppliers(params = {}) {
      try {
        const result = await SupplierApi.findList(params);
        
        if (result) {
          // 如果是获取所有供应商，不更新分页状态
          return result;
        }
        
        return [];
      } catch (error) {
        console.error('获取所有供应商列表失败:', error);
        message.error('获取所有供应商列表失败');
        throw error;
      }
    },
    
    /**
     * 创建供应商
     */
    async createSupplier(supplierData) {
      try {
        const result = await SupplierApi.add(supplierData);
        
        if (result) {
          message.success('供应商创建成功');
          // 重新获取供应商列表
          await this.fetchSuppliers();
        }
        
        return result;
      } catch (error) {
        console.error('创建供应商失败:', error);
        message.error('创建供应商失败');
        throw error;
      }
    },
    
    /**
     * 更新供应商
     */
    async updateSupplier(supplierData) {
      try {
        const result = await SupplierApi.edit(supplierData);
        
        if (result) {
          message.success('供应商更新成功');
          // 重新获取供应商列表
          await this.fetchSuppliers();
          
          // 如果更新的是当前供应商，更新当前供应商信息
          if (this.currentSupplier && this.currentSupplier.supplierId === supplierData.supplierId) {
            await this.fetchSupplierDetail(supplierData.supplierId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('更新供应商失败:', error);
        message.error('更新供应商失败');
        throw error;
      }
    },
    
    /**
     * 删除供应商
     */
    async deleteSupplier(supplierId) {
      try {
        const result = await SupplierApi.delete({ supplierId });
        
        if (result) {
          message.success('供应商删除成功');
          // 重新获取供应商列表
          await this.fetchSuppliers();
          
          // 如果删除的是当前供应商，清空当前供应商
          if (this.currentSupplier && this.currentSupplier.supplierId === supplierId) {
            this.currentSupplier = null;
          }
        }
        
        return result;
      } catch (error) {
        console.error('删除供应商失败:', error);
        message.error('删除供应商失败');
        throw error;
      }
    },
    
    /**
     * 批量删除供应商
     */
    async batchDeleteSuppliers(supplierIds) {
      try {
        const result = await SupplierApi.batchDelete({ supplierIdList: supplierIds });
        
        if (result) {
          message.success(`成功删除 ${supplierIds.length} 个供应商`);
          // 重新获取供应商列表
          await this.fetchSuppliers();
          
          // 如果删除的包含当前供应商，清空当前供应商
          if (this.currentSupplier && supplierIds.includes(this.currentSupplier.supplierId)) {
            this.currentSupplier = null;
          }
        }
        
        return result;
      } catch (error) {
        console.error('批量删除供应商失败:', error);
        message.error('批量删除供应商失败');
        throw error;
      }
    },
    
    /**
     * 获取供应商详情
     */
    async fetchSupplierDetail(supplierId) {
      try {
        const result = await SupplierApi.detail({ supplierId });
        
        if (result) {
          this.currentSupplier = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取供应商详情失败:', error);
        message.error('获取供应商详情失败');
        throw error;
      }
    },
    
    /**
     * 更新供应商状态
     */
    async updateSupplierStatus(supplierId, status) {
      try {
        const result = await SupplierApi.updateStatus({ supplierId, status });
        
        if (result) {
          message.success('供应商状态更新成功');
          // 重新获取供应商列表
          await this.fetchSuppliers();
          
          // 如果更新的是当前供应商，更新当前供应商信息
          if (this.currentSupplier && this.currentSupplier.supplierId === supplierId) {
            this.currentSupplier.status = status;
          }
        }
        
        return result;
      } catch (error) {
        console.error('更新供应商状态失败:', error);
        message.error('更新供应商状态失败');
        throw error;
      }
    },
    
    /**
     * 校验供应商编码
     */
    async validateSupplierCode(supplierCode, supplierId = null) {
      try {
        const result = await SupplierApi.validateCode({ 
          supplierCode, 
          supplierId 
        });
        
        return result;
      } catch (error) {
        console.error('校验供应商编码失败:', error);
        throw error;
      }
    },
    
    /**
     * 获取供应商关联的商品列表
     */
    async fetchSupplierProducts(supplierId) {
      this.productsLoading = true;
      try {
        const result = await SupplierApi.getSupplierProducts({ supplierId });
        
        if (result) {
          this.supplierProducts = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取供应商商品列表失败:', error);
        message.error('获取供应商商品列表失败');
        throw error;
      } finally {
        this.productsLoading = false;
      }
    },
    
    /**
     * 校验供应商经营方式变更的影响
     */
    async validateBusinessModeChange(supplierId, businessMode) {
      try {
        const result = await SupplierApi.validateBusinessModeChange({ 
          supplierId, 
          businessMode 
        });
        
        return result;
      } catch (error) {
        console.error('校验经营方式变更失败:', error);
        message.error('校验经营方式变更失败');
        throw error;
      }
    },
    
    /**
     * 设置搜索条件
     */
    setSearchQuery(query) {
      this.searchQuery = query;
    },
    
    /**
     * 设置经营方式筛选
     */
    setBusinessModeFilter(businessMode) {
      this.businessModeFilter = businessMode;
    },
    
    /**
     * 设置状态筛选
     */
    setStatusFilter(status) {
      this.statusFilter = status;
    },
    
    /**
     * 设置分页信息
     */
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    
    /**
     * 设置当前供应商
     */
    setCurrentSupplier(supplier) {
      this.currentSupplier = supplier;
    },
    
    /**
     * 清空当前供应商
     */
    clearCurrentSupplier() {
      this.currentSupplier = null;
    },
    
    /**
     * 清空搜索和筛选条件
     */
    clearFilters() {
      this.searchQuery = '';
      this.businessModeFilter = '';
      this.statusFilter = '';
    },
    
    /**
     * 重置分页到第一页
     */
    resetPagination() {
      this.pagination.current = 1;
    },
    
    /**
     * 刷新供应商列表
     */
    async refreshSuppliers() {
      await this.fetchSuppliers();
    }
  }
});