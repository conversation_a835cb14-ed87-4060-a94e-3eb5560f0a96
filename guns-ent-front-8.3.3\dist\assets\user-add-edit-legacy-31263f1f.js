System.register(["./index-legacy-ee1db0c7.js","./user-form-legacy-37ed40c0.js","./UsersApi-legacy-88b5f949.js","./index-legacy-dba03026.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js","./index-legacy-198191c1.js","./index-legacy-c65a6a4e.js","./index-legacy-94a6fc23.js","./FileApi-legacy-f85a3060.js","./SysDictTypeApi-legacy-1047ef23.js"],(function(e,a){"use strict";var l,t,s,i,n,u,r,d,c,g,o,m,p;return{setters:[e=>{l=e.b3,t=e.L,s=e.r,i=e.o,n=e.cb,u=e.a,r=e.f,d=e.w,c=e.d,g=e.m,o=e.M},e=>{m=e.default},e=>{p=e.U},null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){e("default",{__name:"user-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const v=e,f=a,y=l(),j=t((()=>y.info.superAdminFlag)),O=s(!1),b=s(!1),h=s({userOrgList:[],userCertificateList:[],sex:"M",superAdminFlag:"N",statusFlag:1}),x=s(null);i((async()=>{v.data?(b.value=!0,F()):(h.value.userSort=await n("SYSTEM_HR_USER"),b.value=!1)}));const F=()=>{p.detail({userId:v.data.userId}).then((e=>{h.value=Object.assign({},e),e.userOrgDTOList&&e.userOrgDTOList.length>0?h.value.userOrgList=e.userOrgDTOList.map((e=>({mainFlag:e.mainFlag,positionId:e.positionId,statusFlag:e.statusFlag,positionName:e.positionName,orgId:e.deptId?e.deptId:e.companyId,orgName:e.deptName?e.deptName:e.companyName}))):h.value.userOrgList=[],e.userCertificateList.length>0&&h.value.userCertificateList.forEach((e=>{e.attachmentId&&(e.attachmentName=e.attachmentIdWrapper.name,e.attachmentUrl=e.attachmentIdWrapper.thumbUrl)}))}))},L=e=>{f("update:visible",e)},I=async()=>{x.value.$refs.formRef.validate().then((async e=>{if(e){if(h.value.userOrgList&&0==h.value.userOrgList.length)return g.warning("组织机构不能为空");if(!h.value.userOrgList.find((e=>"Y"==e.mainFlag)))return g.warning("必须有一个主要部门");if(await x.value.validAllEvent()){O.value=!0;let e=null;e=b.value?p.edit(h.value):p.add(h.value),e.then((async e=>{O.value=!1,g.success(e.message),L(!1),f("done")})).catch((()=>{O.value=!1}))}}}))};return(e,a)=>{const l=o;return u(),r(l,{width:1e3,maskClosable:!1,visible:v.visible,"confirm-loading":O.value,forceRender:!0,title:b.value?"编辑用户":"新建用户","body-style":{paddingBottom:"8px"},"onUpdate:visible":L,onOk:I,class:"common-modal",onClose:a[1]||(a[1]=e=>L(!1))},{default:d((()=>[c(m,{form:h.value,"onUpdate:form":a[0]||(a[0]=e=>h.value=e),ref_key:"userFormRef",ref:x,isUpdate:b.value,superAdminFlag:j.value},null,8,["form","isUpdate","superAdminFlag"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
