import{_ as W}from"./index-d0cfb2ce.js";import{_ as X}from"./index-02bf6f00.js";import{_ as Z,P as $,K as ee,r as w,L as F,N as oe,s as ne,k as L,a as i,c as I,d as n,w as o,b as d,g as a,t as P,h as z,O as U,Q as V,F as j,f as m,M as q,E as H,m as G,U as te,n as le,B as ae,I as se,p as ie,q as re,D as de,l as _e,V as ue,W as ce,J as me,u as pe,v as fe,G as ge,H as ve}from"./index-18a1ea24.js";/* empty css              */import{_ as we}from"./index-bb869875.js";import{R as J}from"./regionApi-2c103d88.js";import he from"./region-tree-db188f0d.js";import ke from"./addForm-92f83fb0.js";import ye from"./detailForm-ce9b8c2f.js";/* empty css              *//* empty css              *//* empty css              */import"./UniversalTree-6547889b.js";/* empty css              *//* empty css              *//* empty css              */const xe={name:"ErpRegion",components:{PlusOutlined:$,SmallDashOutlined:ee,regionTree:he,addForm:ke,detailForm:ye},setup(){const S=w(!1),e=w(!1),K=w(!1),t=w({}),f=w(null),h=w(),v=w(null),_=F(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),k=F(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),T=F(()=>oe()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24}),r=ne({parentId:void 0,searchText:void 0,status:void 0,regionLevel:void 0}),y=[{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"regionCode",title:"\u533A\u57DF\u7F16\u7801",width:140,ellipsis:!0,isShow:!0},{dataIndex:"regionName",title:"\u533A\u57DF\u540D\u79F0",width:160,ellipsis:!0,isShow:!0},{dataIndex:"parentName",title:"\u7236\u7EA7\u533A\u57DF",width:140,ellipsis:!0,isShow:!0},{dataIndex:"regionLevel",title:"\u533A\u57DF\u5C42\u7EA7",width:100,align:"center",isShow:!0},{dataIndex:"sortOrder",title:"\u6392\u5E8F\u53F7",width:80,align:"center",isShow:!0},{dataIndex:"status",title:"\u72B6\u6001",width:80,align:"center",isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}],u=async()=>{v.value.reload()},O=()=>{r.searchText="",r.status=void 0,r.regionLevel=void 0,r.parentId=void 0,h.value.currentSelectKeys=[],f.value=null,u()},N=()=>{S.value=!S.value},D=(l,g)=>{if(l.length>0){const b=g.selectedNodes[0];f.value=b,r.parentId=l[0],u()}else f.value=null,r.parentId=void 0,u()},M=l=>{l&&(f.value=l,r.parentId=l.regionId),C()},A=l=>{x(l)},p=l=>{u()},C=()=>{t.value={parentId:r.parentId,regionLevel:f.value?f.value.regionLevel+1:1},e.value=!0},x=l=>{t.value={...l},e.value=!0},R=async l=>{q.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u533A\u57DF\u5417?",icon:n(H),maskClosable:!0,onOk:async()=>{var b;const g=await J.delete({regionId:l.regionId});G.success(g.message),u(),(b=h.value)==null||b.reload()}})},B=({key:l})=>{l=="1"&&E()},E=()=>{if(v.value.selectedRowList&&v.value.selectedRowList.length==0)return G.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u533A\u57DF");q.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u533A\u57DF\u5417?",icon:n(H),maskClosable:!0,onOk:async()=>{var g;const l=await J.batchDelete({regionIds:v.value.selectedRowList});G.success(l.message),u(),(g=h.value)==null||g.reload()}})};return{tableRef:v,regionTreeRef:h,superSearch:S,where:r,columns:y,currentRecord:t,currentRegionInfo:f,showEdit:e,showDetailModal:K,labelCol:_,wrapperCol:k,spanCol:T,reload:u,clear:O,changeSuperSearch:N,onTreeSelect:D,onAddRegion:M,onEditRegion:A,onDeleteRegion:p,openAddModal:C,openEditModal:x,deleteRecord:R,moreClick:B,batchDelete:E,handleFormOk:()=>{var l;u(),(l=h.value)==null||l.reload()}}}},be={class:"guns-layout"},Ce={class:"guns-layout-sidebar width-100 p-t-12"},Re={class:"sidebar-content"},Ie={class:"guns-layout-content"},Se={class:"guns-layout"},Ee={class:"guns-layout-content-application"},Le={class:"content-main"},Te={class:"content-main-header"},Oe={class:"header-content"},Ne={class:"header-content-left"},De={key:0,class:"current-region-info"},Me={class:"header-content-right"},Ae={class:"content-main-body"},Be={class:"table-content"},Fe={key:0,class:"super-search",style:{"margin-top":"8px"}};function Pe(S,e,K,t,f,h){const v=we,_=te,k=le,T=L("plus-outlined"),r=ae,y=se,u=ie,O=re,N=L("small-dash-outlined"),D=de,M=_e,A=ue,p=ce,C=me,x=pe,R=fe,B=ge,E=ve,Y=X,l=W,g=L("add-form"),b=L("detail-form");return i(),I("div",be,[n(l,{width:"292px",cacheKey:"ERP_REGION_MANAGEMENT"},{content:o(()=>[d("div",Ie,[d("div",Se,[d("div",Ee,[d("div",Le,[d("div",Te,[d("div",Oe,[d("div",Ne,[n(k,{size:16},{default:o(()=>[t.currentRegionInfo?(i(),I("span",De,[e[6]||(e[6]=a(" \u5F53\u524D\u533A\u57DF\uFF1A")),n(_,{color:"blue"},{default:o(()=>[a(P(t.currentRegionInfo.regionName),1)]),_:1})])):z("",!0)]),_:1})]),d("div",Me,[n(k,{size:16},{default:o(()=>[n(r,{type:"primary",class:"border-radius",onClick:t.openAddModal},{default:o(()=>[n(T),e[7]||(e[7]=a(" \u65B0\u589E\u5B50\u533A\u57DF "))]),_:1,__:[7]},8,["onClick"]),n(D,null,{overlay:o(()=>[n(O,{onClick:t.moreClick},{default:o(()=>[n(u,{key:"1"},{default:o(()=>[n(y,{iconClass:"icon-opt-shanchu",color:"#60666b"}),e[8]||(e[8]=d("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[8]})]),_:1},8,["onClick"])]),default:o(()=>[n(r,{class:"border-radius"},{default:o(()=>[e[9]||(e[9]=a(" \u66F4\u591A ")),n(N)]),_:1,__:[9]})]),_:1})]),_:1})])])]),d("div",Ae,[d("div",Be,[n(Y,{columns:t.columns,where:t.where,fieldBusinessCode:"ERP_REGION_TABLE",showTableTool:"",showToolTotal:!1,rowId:"regionId",ref:"tableRef",url:"/erp/region/page"},{toolLeft:o(()=>[n(M,{value:t.where.searchText,"onUpdate:value":e[0]||(e[0]=s=>t.where.searchText=s),bordered:!1,allowClear:"",placeholder:"\u533A\u57DF\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:t.reload,style:{width:"240px"},class:"search-input"},{prefix:o(()=>[n(y,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"]),n(A,{type:"vertical",class:"divider"}),d("a",{onClick:e[1]||(e[1]=(...s)=>t.changeSuperSearch&&t.changeSuperSearch(...s))},P(t.superSearch?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:o(()=>[t.superSearch?(i(),I("div",Fe,[n(E,{model:t.where,labelCol:t.labelCol,"wrapper-col":t.wrapperCol},{default:o(()=>[n(B,{gutter:16},{default:o(()=>[n(R,U(V(t.spanCol)),{default:o(()=>[n(x,{label:"\u72B6\u6001:"},{default:o(()=>[n(C,{value:t.where.status,"onUpdate:value":e[2]||(e[2]=s=>t.where.status=s),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",style:{width:"100%"},allowClear:""},{default:o(()=>[n(p,{value:"Y"},{default:o(()=>e[10]||(e[10]=[a("\u542F\u7528")])),_:1,__:[10]}),n(p,{value:"N"},{default:o(()=>e[11]||(e[11]=[a("\u505C\u7528")])),_:1,__:[11]})]),_:1},8,["value"])]),_:1})]),_:1},16),n(R,U(V(t.spanCol)),{default:o(()=>[n(x,{label:"\u533A\u57DF\u5C42\u7EA7:"},{default:o(()=>[n(C,{value:t.where.regionLevel,"onUpdate:value":e[3]||(e[3]=s=>t.where.regionLevel=s),placeholder:"\u8BF7\u9009\u62E9\u5C42\u7EA7",style:{width:"100%"},allowClear:""},{default:o(()=>[n(p,{value:1},{default:o(()=>e[12]||(e[12]=[a("\u56FD\u5BB6")])),_:1,__:[12]}),n(p,{value:2},{default:o(()=>e[13]||(e[13]=[a("\u7701")])),_:1,__:[13]}),n(p,{value:3},{default:o(()=>e[14]||(e[14]=[a("\u5E02")])),_:1,__:[14]}),n(p,{value:4},{default:o(()=>e[15]||(e[15]=[a("\u533A\u53BF")])),_:1,__:[15]}),n(p,{value:5},{default:o(()=>e[16]||(e[16]=[a("\u5546\u5708")])),_:1,__:[16]})]),_:1},8,["value"])]),_:1})]),_:1},16),n(R,U(V(t.spanCol)),{default:o(()=>[n(x,{label:" ",class:"not-label"},{default:o(()=>[n(k,{size:16},{default:o(()=>[n(r,{class:"border-radius",onClick:t.reload,type:"primary"},{default:o(()=>e[17]||(e[17]=[a("\u67E5\u8BE2")])),_:1,__:[17]},8,["onClick"]),n(r,{class:"border-radius",onClick:t.clear},{default:o(()=>e[18]||(e[18]=[a("\u91CD\u7F6E")])),_:1,__:[18]},8,["onClick"])]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])])):z("",!0)]),bodyCell:o(({column:s,record:c})=>[s.dataIndex==="regionLevel"?(i(),I(j,{key:0},[c.regionLevel===1?(i(),m(_,{key:0,color:"red"},{default:o(()=>e[19]||(e[19]=[a("\u56FD\u5BB6")])),_:1,__:[19]})):c.regionLevel===2?(i(),m(_,{key:1,color:"orange"},{default:o(()=>e[20]||(e[20]=[a("\u7701")])),_:1,__:[20]})):c.regionLevel===3?(i(),m(_,{key:2,color:"yellow"},{default:o(()=>e[21]||(e[21]=[a("\u5E02")])),_:1,__:[21]})):c.regionLevel===4?(i(),m(_,{key:3,color:"green"},{default:o(()=>e[22]||(e[22]=[a("\u533A\u53BF")])),_:1,__:[22]})):c.regionLevel===5?(i(),m(_,{key:4,color:"blue"},{default:o(()=>e[23]||(e[23]=[a("\u5546\u5708")])),_:1,__:[23]})):(i(),m(_,{key:5,color:"default"},{default:o(()=>e[24]||(e[24]=[a("\u672A\u77E5")])),_:1,__:[24]}))],64)):s.dataIndex==="status"?(i(),I(j,{key:1},[c.status==="Y"?(i(),m(_,{key:0,color:"green"},{default:o(()=>e[25]||(e[25]=[a("\u542F\u7528")])),_:1,__:[25]})):c.status==="N"?(i(),m(_,{key:1,color:"red"},{default:o(()=>e[26]||(e[26]=[a("\u505C\u7528")])),_:1,__:[26]})):(i(),m(_,{key:2,color:"default"},{default:o(()=>[a(P(c.status),1)]),_:2},1024))],64)):s.key==="action"?(i(),m(k,{key:2,size:16},{default:o(()=>[n(y,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:Q=>t.openEditModal(c)},null,8,["onClick"]),n(y,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:Q=>t.deleteRecord(c)},null,8,["onClick"])]),_:2},1024)):z("",!0)]),_:1},8,["columns","where"])])])])])])])]),default:o(()=>[d("div",Ce,[d("div",Re,[n(v,{ref:"regionTreeRef",isShowEditIcon:!0,onTreeSelect:t.onTreeSelect,onAddRegion:t.onAddRegion,onEditRegion:t.onEditRegion,onDeleteRegion:t.onDeleteRegion},null,8,["onTreeSelect","onAddRegion","onEditRegion","onDeleteRegion"])])])]),_:1}),n(g,{visible:t.showEdit,"onUpdate:visible":e[4]||(e[4]=s=>t.showEdit=s),data:t.currentRecord,onOk:t.handleFormOk},null,8,["visible","data","onOk"]),n(b,{visible:t.showDetailModal,"onUpdate:visible":e[5]||(e[5]=s=>t.showDetailModal=s),data:t.currentRecord},null,8,["visible","data"])])}const oo=Z(xe,[["render",Pe],["__scopeId","data-v-4518f7f4"]]);export{oo as default};
