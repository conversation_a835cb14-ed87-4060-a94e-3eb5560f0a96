<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.apiauth1.mapper.ApiClientAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.manage.entity.ApiClientAuth">
		<id column="api_client_resource_id" property="apiClientResourceId" />
		<result column="api_client_id" property="apiClientId" />
		<result column="resource_code" property="resourceCode" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
	</resultMap>

	<sql id="Base_Column_List">
		api_client_resource_id,api_client_id,resource_code,create_time,create_user,update_time,update_user
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.manage.pojo.response.ApiClientAuthVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		ent_api_client_auth tbl
		WHERE
		<where>
        <if test="param.apiClientResourceId != null and param.apiClientResourceId != ''">
            and tbl.api_client_resource_id like concat('%',#{param.apiClientResourceId},'%')
        </if>
        <if test="param.apiClientId != null and param.apiClientId != ''">
            and tbl.api_client_id like concat('%',#{param.apiClientId},'%')
        </if>
        <if test="param.resourceCode != null and param.resourceCode != ''">
            and tbl.resource_code like concat('%',#{param.resourceCode},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
		</where>
	</select>

</mapper>
