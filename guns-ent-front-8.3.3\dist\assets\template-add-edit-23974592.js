import{r as o,o as b,a as _,f as g,w as y,d as h,m as k,M as x}from"./index-18a1ea24.js";import w from"./template-form-e6240596.js";import{T as u}from"./ThemeTemplateApi-ac0cf8e8.js";const M={__name:"template-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(v,{emit:p}){const i=v,f=p,t=o(!1),s=o(!1),a=o({}),d=o(null);b(()=>{i.data?(s.value=!0,a.value=Object.assign({},i.data)):s.value=!1});const n=l=>{f("update:visible",l)},c=async()=>{d.value.$refs.formRef.validate().then(async l=>{if(l){t.value=!0;let e=null;s.value?e=u.edit(a.value):e=u.add(a.value),e.then(async r=>{t.value=!1,k.success(r.message),n(!1),f("done",a.value)}).catch(()=>{t.value=!1})}})};return(l,e)=>{const r=x;return _(),g(r,{width:700,maskClosable:!1,visible:i.visible,"confirm-loading":t.value,forceRender:!0,title:s.value?"\u7F16\u8F91\u6A21\u677F":"\u65B0\u5EFA\u6A21\u677F","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":n,onOk:c,onClose:e[1]||(e[1]=m=>n(!1))},{default:y(()=>[h(w,{form:a.value,"onUpdate:form":e[0]||(e[0]=m=>a.value=m),ref_key:"templateFormRef",ref:d},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}};export{M as default};
