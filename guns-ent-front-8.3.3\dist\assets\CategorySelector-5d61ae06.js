import{_ as g,aP as f,as as p,r as u,X as y,o as m,a as b,c as _,d as C,m as i}from"./index-18a1ea24.js";/* empty css              */import{g as v}from"./productCategoryApi-39e417fd.js";const w=f({name:"CategorySelector",props:{value:{type:[Number,String],default:void 0},multiple:{type:Boolean,default:!1},placeholder:{type:String,default:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u5206\u7C7B"},disabled:{type:<PERSON><PERSON><PERSON>,default:!1},allowClear:{type:<PERSON><PERSON>an,default:!0}},emits:["update:value","change"],setup(a,{emit:l}){const d=p.SHOW_PARENT,o=u([]),n=u(a.multiple?[]:void 0),c=async()=>{try{const e=await v({onlyEnabled:!0});console.log("\u5206\u7C7B\u9009\u62E9\u5668API\u54CD\u5E94:",e),e&&Array.isArray(e)?o.value=t(e):(console.error("API\u54CD\u5E94\u683C\u5F0F\u9519\u8BEF:",e),i.error("\u83B7\u53D6\u5206\u7C7B\u6811\u5931\u8D25"))}catch(e){console.error("\u52A0\u8F7D\u5206\u7C7B\u6811\u51FA\u9519:",e),i.error("\u52A0\u8F7D\u5206\u7C7B\u6811\u51FA\u9519")}},t=e=>!e||!e.length?[]:e.map(r=>({...r,categoryId:String(r.categoryId),isLeaf:!r.hasChildren&&(!r.children||r.children.length===0),children:r.children?t(r.children):[]})),s=e=>{l("update:value",e),l("change",e)},h=(e,r)=>r.categoryName.toLowerCase().indexOf(e.toLowerCase())>=0;return y(()=>a.value,e=>{n.value=e},{immediate:!0}),m(()=>{c()}),{SHOW_PARENT:d,treeData:o,selectedValue:n,handleChange:s,filterTreeNode:h}}}),S={class:"category-selector"};function N(a,l,d,o,n,c){const t=p;return b(),_("div",S,[C(t,{value:a.selectedValue,"onUpdate:value":l[0]||(l[0]=s=>a.selectedValue=s),"tree-data":a.treeData,"tree-checkable":a.multiple,"show-checked-strategy":a.multiple?a.SHOW_PARENT:null,"field-names":{children:"children",label:"categoryName",value:"categoryId",key:"categoryId"},placeholder:a.placeholder,disabled:a.disabled,"allow-clear":a.allowClear,multiple:a.multiple,"show-search":!0,"filter-tree-node":a.filterTreeNode,style:{width:"100%"},onChange:a.handleChange},null,8,["value","tree-data","tree-checkable","show-checked-strategy","placeholder","disabled","allow-clear","multiple","filter-tree-node","onChange"])])}const I=g(w,[["render",N],["__scopeId","data-v-50dbb64b"]]);export{I as default};
