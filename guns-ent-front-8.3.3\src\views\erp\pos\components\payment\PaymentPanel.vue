<!--
  支付面板主组件
  
  重构后的支付面板，拆分为多个子组件，职责更加清晰
  使用Composables管理业务逻辑，组件专注于UI渲染
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <a-modal
    :open="visible"
    @update:open="handleVisibleChange"
    title="收银结算"
    width="600px"
    :closable="false"
    :maskClosable="false"
    :footer="null"
    class="payment-modal"
  >
    <div class="payment-panel">
      <!-- 订单信息汇总 -->
      <div class="order-summary">
        <div class="summary-header">
          <h4>订单信息</h4>
          <div class="order-items-count">共 {{ orderSummary.itemCount }} 件商品</div>
        </div>
        
        <div class="amount-details">
          <div class="amount-row">
            <span class="label">商品总额:</span>
            <span class="value">¥{{ formatPrice(orderSummary.totalAmount) }}</span>
          </div>
          <div class="amount-row" v-if="orderSummary.discountAmount > 0">
            <span class="label">优惠金额:</span>
            <span class="value discount">-¥{{ formatPrice(orderSummary.discountAmount) }}</span>
          </div>
          <div class="amount-row total">
            <span class="label">应付金额:</span>
            <span class="value">¥{{ formatPrice(orderSummary.finalAmount) }}</span>
          </div>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <h4>选择支付方式</h4>
        <div class="method-grid">
          <div
            v-for="method in paymentMethods"
            :key="method.value"
            class="method-card"
            :class="{ active: selectedPaymentMethod === method.value }"
            @click="selectPaymentMethod(method.value)"
          >
            <div class="method-icon">
              <icon-font :iconClass="method.icon" />
            </div>
            <div class="method-name">{{ method.label }}</div>
          </div>
        </div>
      </div>

      <!-- 支付方式特定组件 -->
      <div class="payment-content">
        <!-- 现金支付 -->
        <CashPayment
          v-if="selectedPaymentMethod === 'CASH'"
          :payment-amount="orderSummary.finalAmount"
          :initial-amount="orderSummary.finalAmount"
          :loading="loading"
          @confirm-payment="handleCashPayment"
          @cancel-payment="handleCancel"
        />
        
        <!-- 其他支付方式的占位符 -->
        <div v-else class="other-payment">
          <div class="payment-placeholder">
            <div class="placeholder-icon">
              <icon-font iconClass="icon-payment" />
            </div>
            <div class="placeholder-text">
              {{ getPaymentMethodName(selectedPaymentMethod) }} 支付功能开发中...
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="payment-actions" v-if="selectedPaymentMethod !== 'CASH'">
        <a-button size="large" @click="handleCancel" class="cancel-btn">
          取消
        </a-button>
        <a-button
          type="primary"
          size="large"
          @click="confirmPayment"
          :loading="loading"
          :disabled="!canConfirmPayment"
          class="confirm-btn"
        >
          {{ getConfirmButtonText() }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'
import CashPayment from './CashPayment.vue'

// 定义组件名称
defineOptions({
  name: 'PaymentPanel'
})

// 定义Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderInfo: {
    type: Object,
    default: () => ({})
  },
  memberInfo: {
    type: Object,
    default: null
  }
})

// 定义Emits
const emit = defineEmits([
  'update:visible',
  'payment-success',
  'payment-cancel'
])

// 响应式状态
const selectedPaymentMethod = ref('CASH')
const loading = ref(false)

// 支付方式配置
const paymentMethods = [
  { value: 'CASH', label: '现金支付', icon: 'icon-cash' },
  { value: 'WECHAT', label: '微信支付', icon: 'icon-wechat' },
  { value: 'ALIPAY', label: '支付宝', icon: 'icon-alipay' },
  { value: 'CARD', label: '银行卡', icon: 'icon-card' }
]

// 计算属性
const orderSummary = computed(() => {
  return {
    itemCount: props.orderInfo.itemCount || 0,
    totalAmount: props.orderInfo.totalAmount || 0,
    discountAmount: props.orderInfo.discountAmount || 0,
    finalAmount: props.orderInfo.finalAmount || 0
  }
})

const canConfirmPayment = computed(() => {
  return selectedPaymentMethod.value && orderSummary.value.finalAmount > 0
})

// 方法
const formatPrice = (price) => {
  return (price || 0).toFixed(2)
}

const selectPaymentMethod = (method) => {
  selectedPaymentMethod.value = method
}

const getPaymentMethodName = (method) => {
  const paymentMethod = paymentMethods.find(m => m.value === method)
  return paymentMethod ? paymentMethod.label : '未知支付方式'
}

const getConfirmButtonText = () => {
  if (loading.value) {
    return '处理中...'
  }
  
  switch (selectedPaymentMethod.value) {
    case 'CASH':
      return '确认收款'
    case 'WECHAT':
    case 'ALIPAY':
      return '确认支付'
    case 'CARD':
      return '确认刷卡'
    default:
      return '确认支付'
  }
}

const handleCashPayment = async (data) => {
  loading.value = true
  try {
    // 模拟支付处理
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('payment-success', {
      paymentMethod: 'CASH',
      paymentAmount: orderSummary.value.finalAmount,
      receivedAmount: data.receivedAmount,
      changeAmount: data.changeAmount
    })
    
    message.success('现金支付成功')
    closeModal()
  } catch (error) {
    message.error('支付失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const confirmPayment = async () => {
  if (!canConfirmPayment.value) {
    message.warning('请完善支付信息')
    return
  }
  
  loading.value = true
  try {
    // 模拟支付处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emit('payment-success', {
      paymentMethod: selectedPaymentMethod.value,
      paymentAmount: orderSummary.value.finalAmount
    })
    
    message.success('支付成功')
    closeModal()
  } catch (error) {
    message.error('支付失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('payment-cancel')
  closeModal()
}

const handleVisibleChange = (visible) => {
  emit('update:visible', visible)
}

const closeModal = () => {
  emit('update:visible', false)
  
  // 重置状态
  setTimeout(() => {
    selectedPaymentMethod.value = 'CASH'
    loading.value = false
  }, 300)
}

// 监听器
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      selectedPaymentMethod.value = 'CASH'
      loading.value = false
    }
  }
)
</script>

<style scoped>
.payment-modal :deep(.ant-modal-content) {
  border-radius: 12px;
  overflow: hidden;
}

.payment-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.payment-modal :deep(.ant-modal-title) {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.payment-panel {
  padding: 0;
}

/* 订单汇总 */
.order-summary {
  padding: 20px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.summary-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.order-items-count {
  font-size: 13px;
  color: #8c8c8c;
}

.amount-details {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.amount-row:last-child {
  margin-bottom: 0;
}

.amount-row.total {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 600;
}

.amount-row .label {
  color: #595959;
}

.amount-row .value {
  color: #262626;
  font-weight: 500;
}

.amount-row .value.discount {
  color: #52c41a;
}

.amount-row.total .value {
  color: #ff4d4f;
  font-size: 18px;
}

/* 支付方式选择 */
.payment-methods {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.payment-methods h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.method-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.method-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.method-card:hover {
  border-color: #1890ff;
  background: #f0f9ff;
}

.method-card.active {
  border-color: #1890ff;
  background: #e6f7ff;
}

.method-icon {
  font-size: 32px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.method-card.active .method-icon {
  color: #1890ff;
}

.method-name {
  font-size: 13px;
  color: #595959;
  text-align: center;
}

.method-card.active .method-name {
  color: #1890ff;
  font-weight: 500;
}

/* 支付内容区域 */
.payment-content {
  min-height: 200px;
}

.other-payment {
  padding: 40px 24px;
  text-align: center;
}

.payment-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.placeholder-icon {
  font-size: 48px;
  color: #d9d9d9;
}

.placeholder-text {
  font-size: 14px;
  color: #8c8c8c;
}

/* 操作按钮 */
.payment-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  flex: 1;
  height: 44px;
  font-size: 15px;
}

.confirm-btn {
  flex: 2;
  height: 44px;
  font-size: 15px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-modal :deep(.ant-modal-content) {
    margin: 0;
    max-width: 100vw;
    max-height: 100vh;
  }

  .method-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .payment-actions {
    gap: 10px;
  }

  .cancel-btn,
  .confirm-btn {
    height: 40px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .payment-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .confirm-btn {
    flex: none;
  }
}
</style>