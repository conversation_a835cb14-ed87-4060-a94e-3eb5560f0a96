System.register(["./index-legacy-ee1db0c7.js","./config-data-legacy-7a8805fa.js","./index-legacy-16f295ac.js","./ThemeTemplateFieldApi-legacy-437b0971.js"],(function(e,t){"use strict";var l,i,a,n,o,s,d,r;return{setters:[e=>{l=e.r,i=e.o,a=e.a,n=e.f,o=e.w,s=e.d,d=e.M},e=>{r=e.default},null,null],execute:function(){e("default",{__name:"template-config",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:t}){const u=e,f=t,c=l(!1),p=l(null);i((()=>{u.data&&p.value.openConfig(u.data.templateId)}));const g=e=>{f("update:visible",e)};return(e,t)=>{const l=d;return a(),n(l,{width:700,maskClosable:!1,visible:u.visible,"confirm-loading":c.value,forceRender:!0,title:"模板配置","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":g,footer:null,onClose:t[0]||(t[0]=e=>g(!1))},{default:o((()=>[s(r,{ref_key:"ConfigRef",ref:p},null,512)])),_:1},8,["visible","confirm-loading"])}}})}}}));
