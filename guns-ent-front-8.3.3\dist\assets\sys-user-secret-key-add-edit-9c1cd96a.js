import{R as t,r as n,a as p,f as v,w as S,d as b,m as U,M as g}from"./index-18a1ea24.js";import K from"./sys-user-secret-key-form-1f95dd3c.js";class _{static findPage(e){return t.getAndLoadData("/sysUserSecretKey/page",e)}static add(e){return t.post("/sysUserSecretKey/add",e)}static edit(e){return t.post("/sysUserSecretKey/edit",e)}static delete(e){return t.post("/sysUserSecretKey/delete",e)}static batchDelete(e){return t.post("/sysUserSecretKey/batchDelete",e)}static detail(e){return t.getAndLoadData("/sysUserSecretKey/detail",e)}}const h={__name:"sys-user-secret-key-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(i,{emit:e}){const u=i,c=e,a=n(!1),o=n({sysUserSecretKeySort:1e3,secretOnceFlag:"N"}),d=n(null),l=r=>{c("update:visible",r)},f=async()=>{d.value.$refs.formRef.validate().then(async r=>{r&&(a.value=!0,_.add(o.value).then(async s=>{a.value=!1,U.success(s.message),l(!1),c("done")}).catch(()=>{a.value=!1}))})};return(r,s)=>{const m=g;return p(),v(m,{width:700,maskClosable:!1,visible:u.visible,"confirm-loading":a.value,forceRender:!0,title:"\u65B0\u5EFA\u4E34\u65F6\u79D8\u94A5","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":l,onOk:f,onClose:s[1]||(s[1]=y=>l(!1))},{default:S(()=>[b(K,{form:o.value,"onUpdate:form":s[0]||(s[0]=y=>o.value=y),ref_key:"sysUserSecretKeyFormRef",ref:d},null,8,["form"])]),_:1},8,["visible","confirm-loading"])}}},x=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));export{_ as S,h as _,x as s};
