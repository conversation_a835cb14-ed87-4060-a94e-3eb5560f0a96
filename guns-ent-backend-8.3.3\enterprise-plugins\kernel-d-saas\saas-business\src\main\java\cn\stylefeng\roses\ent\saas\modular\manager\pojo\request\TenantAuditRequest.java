package cn.stylefeng.roses.ent.saas.modular.manager.pojo.request;

import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 租户开通的请求
 *
 * <AUTHOR>
 * @since 2024-02-22 19:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantAuditRequest extends BaseRequest {

    /**
     * 租户id
     */
    @NotEmpty(message = "租户id不能为空")
    @ChineseDescription("租户id")
    private List<Long> tenantIdList;

    /**
     * 租户开通时间
     */
    @ChineseDescription("租户开通时间")
    @NotNull(message = "租户开通时间不能为空")
    private Date activeDate;

    /**
     * 租户到期时间
     */
    @ChineseDescription("租户到期时间")
    @NotNull(message = "租户开通时间不能为空")
    private Date expireDate;

    /**
     * 租户和功能的绑定
     */
    @NotEmpty(message = "租户和功能的绑定不能为空")
    @ChineseDescription("租户和功能的绑定")
    private List<TenantLinkRequest> tenantLinkList;

}
