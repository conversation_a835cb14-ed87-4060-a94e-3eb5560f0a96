package cn.stylefeng.roses.kernel.micro.api.exception.enums;

import cn.stylefeng.roses.kernel.micro.api.constants.MicroConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 微服务的异常枚举
 *
 * <AUTHOR>
 * @date 2021/5/10 10:20
 */
@Getter
public enum MicroExceptionEnum implements AbstractExceptionEnum {

    /**
     * 微服务异常
     */
    MICRO_EXCEPTION(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "01", "微服务异常，具体信息：{}"),

    /**
     * 请求参数存在空值
     */
    REQUEST_EMPTY(RuleConstants.BUSINESS_ERROR_TYPE_CODE + MicroConstants.MICRO_EXCEPTION_STEP_CODE + "02", "请求参数{}为空，请检查请求参数");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    MicroExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
