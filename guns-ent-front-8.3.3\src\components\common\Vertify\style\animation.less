@import './variables.less';

.@{ym-prefix} {
    &anim {
        transition: all @ym-anim-duration ease;
        
        /** fade */
        &-fade-enter-active,
        &-fade-leave-active {
            opacity: 1;
        }

        &-fade-enter-from,
        &-fade-leave-to {
            opacity: 0
        }

        /** scale */
        &-scale-enter-active,
        &-scale-leave-active {
            opacity: 1;
            transform: scale(1);
        }

        &-scale-enter-from,
        &-scale-leave-to {
            transform: scale(0);
            opacity: 0
        }

        /** slide left / slide right */
        &-slide-enter-active,
        &-slide-leave-active,
        &-slide-right-enter-active,
        &-slide-right-leave-active {
            opacity: 1;
            transform: translateX(0);
        }

        &-slide-enter-from,
        &-slide-leave-to,
        &-slide-right-enter-from,
        &-slide-right-leave-to {
            transform: translateX(20%);
            opacity: 0
        }

        /** slide bottom */
        &-slide-bottom-enter-active,
        &-slide-bottom-leave-active {
            opacity: 1;
            transform: translateY(0);
        }

        &-slide-bottom-enter-from,
        &-slide-bottom-leave-to {
            transform: translateY(20%);
            opacity: 0
        }

        /** slide fall */
        &-slide-fall-enter-active,
        &-slide-fall-leave-active {
            opacity: 1;
            transform: translate(0%) translateZ(0) rotate(0deg);
        }

        &-slide-fall-enter-from,
        &-slide-fall-leave-to {
            transform: translate(30%) translateZ(660px) rotate(30deg);
            opacity: 0
        }

        /** newspaper */
        &-newspaper-enter-active,
        &-newspaper-leave-active {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        &-newspaper-enter-from,
        &-newspaper-leave-to {
            transform-style: preserve-3d;
            transform: scale(0) rotate(720deg);
            opacity: 0
        }

        /** sticky */
        &-sticky-enter-active,
        &-sticky-leave-active {
            opacity: 1;
            transform: translateY(0);
        }

        &-sticky-enter-from,
        &-sticky-leave-to {
            transform: translateY(-200%);
            opacity: 0
        }

        /** flip */
        &-flip-enter-active,
        &-flip-leave-active,
        &-flip-horizontal-enter-active,
        &-flip-horizontal-leave-active {
            opacity: 1;
            transform: rotateY(0deg);
        }

        &-flip-enter-from,
        &-flip-leave-to,
        &-flip-horizontal-enter-from,
        &-flip-horizontal-leave-to {
            transform-style: preserve-3d;
            transform: rotateY(-70deg);
            opacity: 0
        }

        &-flip-vertical-enter-active,
        &-flip-vertical-leave-active {
            opacity: 1;
            transform: rotateX(0deg);
        }

        &-flip-vertical-enter-from,
        &-flip-vertical-leave-to {
            transform-style: preserve-3d;
            transform: rotateX(-70deg);
            opacity: 0
        }

        /** fall */
        &-fall-enter-active,
        &-fall-leave-active {
            opacity: 1;
            transform: translateZ(0px) rotateX(0deg);
        }

        &-fall-enter-from,
        &-fall-leave-to {
            transform: translateZ(660px) rotateX(20deg);
            opacity: 0
        }

        /** rotate */
        &-rotate-enter-active,
        &-rotate-leave-active {
            transform: translateY(0%) rotateX(0deg);
            opacity: 1;
        }

        &-rotate-enter-from,
        &-rotate-leave-to {
            transform-style: preserve-3d;
            transform: translateY(100%) rotateX(90deg);
            transform-origin: 0 100%;
            opacity: 0
        }

        /** sign */
        &-sign-enter-active,
        &-sign-leave-active {
            transform: rotateX(0deg);
            opacity: 1;
        }

        &-sign-enter-from,
        &-sign-leave-to {
            transform-style: preserve-3d;
            transform: rotateX(-90deg);
            transform-origin: 50% 0;
            opacity: 0
        }

        /** slit */
        &-slit-enter-active {
            animation: ym-anim-slit @ym-anim-duration forwards ease-out;
        }

        &-slit-leave-active {
            animation: ym-anim-slit @ym-anim-duration forwards ease-out reverse;
        }

        /** shake */
        &-shake-enter-active {
            animation: ym-anim-modal-shake @ym-anim-duration forwards linear;
        }

        &-shake-leave-active {
            animation: ym-anim-modal-shake @ym-anim-duration forwards linear reverse;
        }

        /** route change */
        &-page-slide-enter-active,
        &-page-slide-leave-active {
            transition: all @ym-anim-duration linear;
        }

        &-page-slide-leave-from,
        &-page-slide-enter-to {
            transform: translateX(0);
            opacity: 1;
        }

        &-page-slide-enter-from {
            transform: translateX(100%);
            opacity: 0;
        }

        &-page-slide-leave-to {
            transform: translateX(-100%);
            opacity: 0;
        }

        /** breadcrumb change */
        &-breadcrumb-enter-active,
        &-breadcrumb-leave-active {
            transition: all @ym-anim-duration linear;
        }

        &-breadcrumb-leave-from,
        &-breadcrumb-enter-to {
            transform: translateX(0);
            opacity: 1;
        }

        &-breadcrumb-enter-from {
            transform: translateX(-32px);
            opacity: 0;
        }

        &-breadcrumb-leave-to {
            transform: translateX(32px);
            opacity: 0;
        }

        /** anchor */
        &-anchor-enter-active,
        &-anchor-leave-active {
            opacity: 1;
            transform: translateX(0);
        }

        &-anchor-enter-from,
        &-anchor-leave-to {
            opacity: 0;
            transform: translateX(5rem);
        }
    }
}

@keyframes ym-anim-wait {
    60% {
        -moz-transform: scale(0.75);
        -ms-transform: scale(0.75);
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }
}

@keyframes ym-anim-scan {
    0% {
        .properties(top, 6);
    }
    50% {
        .properties(top, 14);
    }
    100% {
        .properties(top, 6);
    }
}

@keyframes ym-anim-shake {
    0% {
        -webkit-transform: translate(0, 0) rotate(0);
        -moz-transform: translate(0, 0) rotate(0);
        -o-transform: translate(0, 0) rotate(0);
        -ms-transform: translate(0, 0) rotate(0);
        transform: translate(0, 0) rotate(0);
    }
    25% {
        -webkit-transform: translate(-12px, 0) rotate(0);
        -moz-transform: translate(-12px, 0) rotate(0);
        -o-transform: translate(-12px, 0) rotate(0);
        -ms-transform: translate(-12px, 0) rotate(0);
        transform: translate(-12px, 0) rotate(0);
    }
    50% {
        -webkit-transform: translate(0, 0) rotate(0);
        -moz-transform: translate(0, 0) rotate(0);
        -o-transform: translate(0, 0) rotate(0);
        -ms-transform: translate(0, 0) rotate(0);
        transform: translate(0, 0) rotate(0);
    }
    75% {
        -webkit-transform: translate(-12px, 0) rotate(0);
        -moz-transform: translate(-12px, 0) rotate(0);
        -o-transform: translate(-12px, 0) rotate(0);
        -ms-transform: translate(-12px, 0) rotate(0);
        transform: translate(-12px, 0) rotate(0);
    }
    100% {
        -webkit-transform: translate(0, 0) rotate(0);
        -moz-transform: translate(0, 0) rotate(0);
        -o-transform: translate(0, 0) rotate(0);
        -ms-transform: translate(0, 0) rotate(0);
        transform: translate(0, 0) rotate(0);
    }
}

@keyframes ym-anim-move {
    0% { transform: translate(1px, 1px) }
    33.33% { transform: translate(51px, 1px) }
    66.66% { transform: translate(21px, 51px) }
    100% { transform: translate(1px, 1px) }
}

@keyframes ym-anim-rotate {
    0% { transform: rotate(0) }
    100% { transform: rotate(360deg) }
}

@keyframes ym-captcha-modal-open {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(0, 0, 1);
		-moz-transform: scale3d(0, 0, 1);
		-o-transform: scale3d(0, 0, 1);
		-ms-transform: scale3d(0, 0, 1);
		transform: scale3d(0, 0, 1);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale3d(1, 1, 1);
		-moz-transform: scale3d(1, 1, 1);
		-o-transform: scale3d(1, 1, 1);
		-ms-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1);
	}
}

@keyframes ym-captcha-success {
	25% {
		-o-transform: (25deg);
		-moz-transform: rotate(25deg);
		-ms-transform: rotate(25deg);
		-webkit-transform: rotate(25deg);
		transform: rotate(25deg)
	}
	100% {
		-o-transform: rotate(-360deg);
		-moz-transform: rotate(-360deg);
		-ms-transform: rotate(-360deg);
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg)
	}
}

@keyframes ym-captcha-downtip {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes ym-anim-slit {
    0% {
        transform: translateZ(-3000px) rotateY(90deg);
        opacity: 1;
        animation-timing-function: ease-in;
    }
	50% {
        transform: translateZ(-250px) rotateY(89deg);
        opacity: 1;
        animation-timing-function: ease-in;
    }
    100% {
        transform: translateZ(0) rotateY(0deg);
        opacity: 1;
    }
}

@keyframes ym-anim-modal-shake {
	0% {
		opacity: 0;
		-webkit-transform: matrix3d(0.7, 0, 0, 0, 0, 0.7, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.7, 0, 0, 0, 0, 0.7, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	2.083333% {
		-webkit-transform: matrix3d(0.75266, 0, 0, 0, 0, 0.76342, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.75266, 0, 0, 0, 0, 0.76342, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	4.166667% {
		-webkit-transform: matrix3d(0.81071, 0, 0, 0, 0, 0.84545, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.81071, 0, 0, 0, 0, 0.84545, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	6.25% {
		-webkit-transform: matrix3d(0.86808, 0, 0, 0, 0, 0.9286, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.86808, 0, 0, 0, 0, 0.9286, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	8.333333% {
		-webkit-transform: matrix3d(0.92038, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.92038, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	10.416667% {
		-webkit-transform: matrix3d(0.96482, 0, 0, 0, 0, 1.05202, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.96482, 0, 0, 0, 0, 1.05202, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	12.5% {
		-webkit-transform: matrix3d(1, 0, 0, 0, 0, 1.08204, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1, 0, 0, 0, 0, 1.08204, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	14.583333% {
		-webkit-transform: matrix3d(1.02563, 0, 0, 0, 0, 1.09149, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.02563, 0, 0, 0, 0, 1.09149, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	16.666667% {
		-webkit-transform: matrix3d(1.04227, 0, 0, 0, 0, 1.08453, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.04227, 0, 0, 0, 0, 1.08453, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	18.75% {
		-webkit-transform: matrix3d(1.05102, 0, 0, 0, 0, 1.06666, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.05102, 0, 0, 0, 0, 1.06666, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	20.833333% {
		-webkit-transform: matrix3d(1.05334, 0, 0, 0, 0, 1.04355, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.05334, 0, 0, 0, 0, 1.04355, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	22.916667% {
		-webkit-transform: matrix3d(1.05078, 0, 0, 0, 0, 1.02012, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.05078, 0, 0, 0, 0, 1.02012, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	25% {
		-webkit-transform: matrix3d(1.04487, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.04487, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	27.083333% {
		-webkit-transform: matrix3d(1.03699, 0, 0, 0, 0, 0.98534, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.03699, 0, 0, 0, 0, 0.98534, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	29.166667% {
		-webkit-transform: matrix3d(1.02831, 0, 0, 0, 0, 0.97688, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.02831, 0, 0, 0, 0, 0.97688, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	31.25% {
		-webkit-transform: matrix3d(1.01973, 0, 0, 0, 0, 0.97422, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.01973, 0, 0, 0, 0, 0.97422, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	33.333333% {
		-webkit-transform: matrix3d(1.01191, 0, 0, 0, 0, 0.97618, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.01191, 0, 0, 0, 0, 0.97618, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	35.416667% {
		-webkit-transform: matrix3d(1.00526, 0, 0, 0, 0, 0.98122, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00526, 0, 0, 0, 0, 0.98122, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	37.5% {
		-webkit-transform: matrix3d(1, 0, 0, 0, 0, 0.98773, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1, 0, 0, 0, 0, 0.98773, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	39.583333% {
		-webkit-transform: matrix3d(0.99617, 0, 0, 0, 0, 0.99433, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99617, 0, 0, 0, 0, 0.99433, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	41.666667% {
		-webkit-transform: matrix3d(0.99368, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99368, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	43.75% {
		-webkit-transform: matrix3d(0.99237, 0, 0, 0, 0, 1.00413, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99237, 0, 0, 0, 0, 1.00413, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	45.833333% {
		-webkit-transform: matrix3d(0.99202, 0, 0, 0, 0, 1.00651, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99202, 0, 0, 0, 0, 1.00651, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	47.916667% {
		-webkit-transform: matrix3d(0.99241, 0, 0, 0, 0, 1.00726, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99241, 0, 0, 0, 0, 1.00726, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	50% {
		opacity: 1;
		-webkit-transform: matrix3d(0.99329, 0, 0, 0, 0, 1.00671, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99329, 0, 0, 0, 0, 1.00671, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	52.083333% {
		-webkit-transform: matrix3d(0.99447, 0, 0, 0, 0, 1.00529, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99447, 0, 0, 0, 0, 1.00529, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	54.166667% {
		-webkit-transform: matrix3d(0.99577, 0, 0, 0, 0, 1.00346, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99577, 0, 0, 0, 0, 1.00346, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	56.25% {
		-webkit-transform: matrix3d(0.99705, 0, 0, 0, 0, 1.0016, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99705, 0, 0, 0, 0, 1.0016, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	58.333333% {
		-webkit-transform: matrix3d(0.99822, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99822, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	60.416667% {
		-webkit-transform: matrix3d(0.99921, 0, 0, 0, 0, 0.99884, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99921, 0, 0, 0, 0, 0.99884, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	62.5% {
		-webkit-transform: matrix3d(1, 0, 0, 0, 0, 0.99816, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1, 0, 0, 0, 0, 0.99816, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	64.583333% {
		-webkit-transform: matrix3d(1.00057, 0, 0, 0, 0, 0.99795, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00057, 0, 0, 0, 0, 0.99795, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	66.666667% {
		-webkit-transform: matrix3d(1.00095, 0, 0, 0, 0, 0.99811, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00095, 0, 0, 0, 0, 0.99811, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	68.75% {
		-webkit-transform: matrix3d(1.00114, 0, 0, 0, 0, 0.99851, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00114, 0, 0, 0, 0, 0.99851, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	70.833333% {
		-webkit-transform: matrix3d(1.00119, 0, 0, 0, 0, 0.99903, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00119, 0, 0, 0, 0, 0.99903, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	72.916667% {
		-webkit-transform: matrix3d(1.00114, 0, 0, 0, 0, 0.99955, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00114, 0, 0, 0, 0, 0.99955, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	75% {
		-webkit-transform: matrix3d(1.001, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.001, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	77.083333% {
		-webkit-transform: matrix3d(1.00083, 0, 0, 0, 0, 1.00033, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00083, 0, 0, 0, 0, 1.00033, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	79.166667% {
		-webkit-transform: matrix3d(1.00063, 0, 0, 0, 0, 1.00052, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00063, 0, 0, 0, 0, 1.00052, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	81.25% {
		-webkit-transform: matrix3d(1.00044, 0, 0, 0, 0, 1.00058, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00044, 0, 0, 0, 0, 1.00058, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	83.333333% {
		-webkit-transform: matrix3d(1.00027, 0, 0, 0, 0, 1.00053, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00027, 0, 0, 0, 0, 1.00053, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	85.416667% {
		-webkit-transform: matrix3d(1.00012, 0, 0, 0, 0, 1.00042, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1.00012, 0, 0, 0, 0, 1.00042, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	87.5% {
		-webkit-transform: matrix3d(1, 0, 0, 0, 0, 1.00027, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1, 0, 0, 0, 0, 1.00027, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	89.583333% {
		-webkit-transform: matrix3d(0.99991, 0, 0, 0, 0, 1.00013, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99991, 0, 0, 0, 0, 1.00013, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	91.666667% {
		-webkit-transform: matrix3d(0.99986, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99986, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	93.75% {
		-webkit-transform: matrix3d(0.99983, 0, 0, 0, 0, 0.99991, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99983, 0, 0, 0, 0, 0.99991, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	95.833333% {
		-webkit-transform: matrix3d(0.99982, 0, 0, 0, 0, 0.99985, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99982, 0, 0, 0, 0, 0.99985, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	97.916667% {
		-webkit-transform: matrix3d(0.99983, 0, 0, 0, 0, 0.99984, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(0.99983, 0, 0, 0, 0, 0.99984, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}

	100% {
		opacity: 1;
		-webkit-transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
		transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
	}
}