package cn.stylefeng.roses.kernel.manage.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.manage.entity.ApiEndpoint;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiEndpointRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * API资源接口列表服务类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
public interface ApiEndpointService extends IService<ApiEndpoint> {

    /**
     * 新增API资源接口列表
     *
     * @param apiEndpointRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    void add(ApiEndpointRequest apiEndpointRequest);

    /**
     * 删除API资源接口列表
     *
     * @param apiEndpointRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    void del(ApiEndpointRequest apiEndpointRequest);

    /**
     * 批量删除
     *
     * <AUTHOR>
     * @since 2023/10/24 23:47
     */
    void batchDelete(ApiEndpointRequest apiEndpointRequest);

    /**
     * 编辑API资源接口列表
     *
     * @param apiEndpointRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    void edit(ApiEndpointRequest apiEndpointRequest);

    /**
     * 查询详情API资源接口列表
     *
     * @param apiEndpointRequest 请求参数
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    ApiEndpoint detail(ApiEndpointRequest apiEndpointRequest);

    /**
     * 获取API资源接口列表分页列表
     *
     * @param apiEndpointRequest 请求参数
     * @return PageResult<ApiEndpoint>   返回结果
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    PageResult<ApiEndpoint> findPage(ApiEndpointRequest apiEndpointRequest);

    /**
     * 获取接口下拉选择列表
     *
     * <AUTHOR>
     * @since 2023/10/24 23:36
     */
    List<ApiEndpoint> getUrlSelectList(ApiEndpointRequest apiEndpointRequest);

    /**
     * 获取所有的接口列表
     * <p>
     * 包含接口的详细信息
     *
     * <AUTHOR>
     * @since 2023/10/25 15:50
     */
    List<ApiEndpoint> getTotalEndpoint();

    /**
     * 获取所有接口的编码
     *
     * <AUTHOR>
     * @since 2023/10/25 15:50
     */
    List<String> getTotalEndpointCodeList();

}
