package cn.stylefeng.roses.ent.mobile.manage.pojo.common;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用选人和机构组件的请求
 *
 * <AUTHOR>
 * @since 2024-04-02 14:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgUserRequest extends BaseRequest {

    /**
     * 组织机构id
     */
    @ChineseDescription("组织机构id")
    @NotNull(message = "组织机构id不能为空")
    private Long orgId;

    /**
     * 选择类型：1-机构，2-人员
     */
    @ChineseDescription("选择类型：1-机构，2-人员")
    @NotNull(message = "选择类型不能为空")
    private Integer selectType;

}
