{"name": "new-project", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "node --max_old_space_size=8096 ./node_modules/vite/bin/vite.js build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^3.2.17", "axios": "^1.6.0", "cropperjs": "^1.5.12", "dayjs": "^1.11.10", "element-china-area-data": "^6.1.0", "gm-crypto": "^0.1.12", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "less": "^4.2.0", "lodash-es": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "^0.44.0", "nprogress": "^0.2.0", "@kangc/v-md-editor": "^2.3.15", "json-editor-vue3": "^1.0.8", "prismjs": "^1.29.0", "pinia": "^2.1.7", "tinymce": "^5.10.7", "vue": "^3.5.11", "viewerjs": "1.10.4", "vue3-lazyload": "^0.2.5-beta", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.1.9", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "vxe-table": "4.6.17", "xe-utils": "^3.5.14", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/test-utils": "^2.4.1", "consola": "3.2.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^22.1.0", "prettier": "^3.0.3", "terser": "^5.16.9", "unplugin-vue-components": "^0.24.1", "vite": "^4.4.11", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-style-import": "^2.0.0", "vitest": "^0.34.6", "vue-eslint-parser": "^9.1.1"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}}