import{_ as R,r as v,s as A,a as F,f as M,w as n,d as a,g as C,m as L,l as V,u as j,v as q,G as B,as as S,y as Y,W as H,J as D,$ as E,H as G,M as J}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{R as x}from"./regionApi-2c103d88.js";const P={name:"RegionEditForm",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","ok"],setup(b,{emit:l}){const f=v(),t=v(!1),c=v([]),y=v("1\u7EA7 - \u56FD\u5BB6"),i=A({regionId:null,regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),d={regionCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u533A\u57DF\u7F16\u7801",trigger:"blur"}],regionName:[{required:!0,message:"\u8BF7\u8F93\u5165\u533A\u57DF\u540D\u79F0",trigger:"blur"}],regionLevel:[{required:!0,message:"\u8BF7\u9009\u62E9\u533A\u57DF\u5C42\u7EA7",trigger:"change"}],status:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",trigger:"change"}]},s=e=>{l("update:visible",e),e||u()},u=()=>{var e;(e=f.value)==null||e.resetFields(),Object.assign(i,{regionId:null,regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),m()},w=e=>({1:"1\u7EA7 - \u56FD\u5BB6",2:"2\u7EA7 - \u7701",3:"3\u7EA7 - \u5E02",4:"4\u7EA7 - \u533A\u53BF",5:"5\u7EA7 - \u5546\u5708"})[e]||"".concat(e,"\u7EA7 - \u672A\u77E5"),h=e=>{if(!e)return 1;const r=(U,O)=>{for(const g of U){if(g.regionId===O)return g.regionLevel||1;if(g.children&&g.children.length>0){const T=r(g.children,O);if(T)return T}}return null},N=r(c.value,targetId);return N?Math.min(N+1,5):1},m=()=>{const e=h(i.parentId);i.regionLevel=e,y.value=w(e)},k=(e,r)=>r.title&&r.title.toLowerCase().includes(e.toLowerCase()),_=async()=>{try{let r=await x.findTree()||[];r=p(r),c.value=r}catch(e){console.error("\u52A0\u8F7D\u533A\u57DF\u6811\u5931\u8D25:",e),c.value=[]}},p=e=>Array.isArray(e)?e.filter(r=>!r||!r.regionId||!r.regionName?!1:(r.title=r.regionName,r.key=String(r.regionId),r.value=String(r.regionId),r.children&&Array.isArray(r.children)&&(r.children=p(r.children)),!0)):[],I=async e=>{try{await _();const r=await x.detail({regionId:e.regionId});Object.assign(i,r)}catch(r){console.error("\u83B7\u53D6\u533A\u57DF\u8BE6\u60C5\u5931\u8D25:",r),L.error("\u83B7\u53D6\u8BE6\u60C5\u5931\u8D25")}},o=async()=>{try{await f.value.validate(),t.value=!0,await x.edit(i),L.success("\u7F16\u8F91\u6210\u529F"),l("ok"),s(!1)}catch(e){console.error("\u7F16\u8F91\u533A\u57DF\u5931\u8D25:",e),L.error("\u7F16\u8F91\u5931\u8D25")}finally{t.value=!1}};return watch(()=>b.data,e=>{e&&Object.keys(e).length>0&&(Object.assign(i,e),m())},{immediate:!0}),watch(()=>b.visible,e=>{e&&_()}),watch(()=>i.parentId,()=>{m()}),{formRef:f,loading:t,form:i,rules:d,regionTreeData:c,regionLevelText:y,updateVisible:s,save:o,edit:I,filterTreeNode:k}}};function W(b,l,f,t,c,y){const i=V,d=j,s=q,u=B,w=S,h=Y,m=H,k=D,_=E,p=G,I=J;return F(),M(I,{title:"\u7F16\u8F91\u533A\u57DF",width:800,visible:f.visible,"confirm-loading":t.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":t.updateVisible,onOk:t.save},{default:n(()=>[a(p,{ref:"formRef",model:t.form,rules:t.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:n(()=>[a(u,{gutter:16},{default:n(()=>[a(s,{md:12,sm:24},{default:n(()=>[a(d,{label:"\u533A\u57DF\u7F16\u7801",name:"regionCode"},{default:n(()=>[a(i,{value:t.form.regionCode,"onUpdate:value":l[0]||(l[0]=o=>t.form.regionCode=o),placeholder:"\u8BF7\u8F93\u5165\u533A\u57DF\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),a(s,{md:12,sm:24},{default:n(()=>[a(d,{label:"\u533A\u57DF\u540D\u79F0",name:"regionName"},{default:n(()=>[a(i,{value:t.form.regionName,"onUpdate:value":l[1]||(l[1]=o=>t.form.regionName=o),placeholder:"\u8BF7\u8F93\u5165\u533A\u57DF\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(u,{gutter:16},{default:n(()=>[a(s,{md:12,sm:24},{default:n(()=>[a(d,{label:"\u7236\u7EA7\u533A\u57DF",name:"parentId"},{default:n(()=>[a(w,{value:t.form.parentId,"onUpdate:value":l[2]||(l[2]=o=>t.form.parentId=o),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":t.regionTreeData,placeholder:"\u8BF7\u9009\u62E9\u7236\u7EA7\u533A\u57DF","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":t.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])]),_:1})]),_:1}),a(s,{md:12,sm:24},{default:n(()=>[a(d,{label:"\u533A\u57DF\u5C42\u7EA7",name:"regionLevel"},{default:n(()=>[a(i,{value:t.regionLevelText,"onUpdate:value":l[3]||(l[3]=o=>t.regionLevelText=o),placeholder:"\u6839\u636E\u7236\u7EA7\u533A\u57DF\u81EA\u52A8\u8BBE\u7F6E",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(u,{gutter:16},{default:n(()=>[a(s,{md:12,sm:24},{default:n(()=>[a(d,{label:"\u6392\u5E8F\u53F7",name:"sortOrder"},{default:n(()=>[a(h,{value:t.form.sortOrder,"onUpdate:value":l[4]||(l[4]=o=>t.form.sortOrder=o),min:0,max:9999,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F\u53F7"},null,8,["value"])]),_:1})]),_:1}),a(s,{md:12,sm:24},{default:n(()=>[a(d,{label:"\u72B6\u6001",name:"status"},{default:n(()=>[a(k,{value:t.form.status,"onUpdate:value":l[5]||(l[5]=o=>t.form.status=o),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:n(()=>[a(m,{value:"Y"},{default:n(()=>l[7]||(l[7]=[C("\u542F\u7528")])),_:1,__:[7]}),a(m,{value:"N"},{default:n(()=>l[8]||(l[8]=[C("\u505C\u7528")])),_:1,__:[8]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),a(u,{gutter:16},{default:n(()=>[a(s,{span:24},{default:n(()=>[a(d,{label:"\u5907\u6CE8",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:n(()=>[a(_,{value:t.form.remark,"onUpdate:value":l[6]||(l[6]=o=>t.form.remark=o),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["visible","confirm-loading","onUpdate:visible","onOk"])}const Z=R(P,[["render",W]]);export{Z as default};
