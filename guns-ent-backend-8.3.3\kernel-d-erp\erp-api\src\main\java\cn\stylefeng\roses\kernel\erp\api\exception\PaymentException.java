package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;

/**
 * 支付相关异常
 *
 * <AUTHOR>
 * @since 2025/08/01 20:20
 */
public class PaymentException extends PosException {

    public PaymentException(AbstractExceptionEnum exception) {
        super(exception);
    }

    public PaymentException(AbstractExceptionEnum exception, Object... params) {
        super(exception, params);
    }

    /**
     * 支付失败异常
     */
    public static PaymentException failed(String orderNo, String reason) {
        return new PaymentException(ErpPosExceptionEnum.PAYMENT_FAILED, orderNo, reason);
    }

    /**
     * 支付金额不足异常
     */
    public static PaymentException insufficientAmount(String orderNo, String requiredAmount, String actualAmount) {
        return new PaymentException(ErpPosExceptionEnum.PAYMENT_AMOUNT_INSUFFICIENT, orderNo, requiredAmount, actualAmount);
    }

    /**
     * 支付方式不支持异常
     */
    public static PaymentException unsupportedMethod(String paymentMethod) {
        return new PaymentException(ErpPosExceptionEnum.PAYMENT_METHOD_NOT_SUPPORTED, paymentMethod);
    }
}
