/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.apiauth.api.exception.enums;

import cn.stylefeng.roses.kernel.apiauth.api.constants.ApiAuthConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * api授权的异常枚举
 *
 * <AUTHOR>
 * @date 2023-08-17 14:02:01
 */
@Getter
public enum ApiAuthExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询不到对应api授权
     */
    CANT_FIND_API_AUTH(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "01", "查询不到对应api授权，具体信息：{}"),

    /**
     * API认证失败，请求Token格式错误
     */
    API_AUTH_TOKEN_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "02", "API认证失败，请求Token格式错误"),

    /**
     * API认证失败，token中的payload格式不对
     */
    PAYLOAD_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "03", "API认证失败，Token Payload格式错误"),

    /**
     * API认证失败，Token Client获取错误
     */
    PAYLOAD_CLIENT_ID_CANT_GET(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "04", "API认证失败，Token Client获取错误"),

    /**
     * JWT Token错误
     */
    JWT_TOKEN_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "05", "API认证失败，JWT Token错误"),

    /**
     * API授权，权限异常，路径不合法
     */
    URL_ERROR_RES_NOT_FOUND(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "06", "API授权，权限异常，路径不合法"),

    /**
     * API授权，权限异常，应用无权访问接口
     */
    API_DONT_HAVE_AUTH(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "07", "API授权，权限异常，应用无权访问接口"),

    /**
     * API认证-请求token为空
     */
    TOKEN_IS_EMPTY(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "08", "API认证-请求token为空"),

    /**
     * Token过期
     */
    JWT_TOKEN_EXPIRED(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ApiAuthConstants.API_AUTH_EXCEPTION_STEP_CODE + "09", "API认证失败，JWT Token过期");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ApiAuthExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
