package cn.stylefeng.roses.kernel.pay.api.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单支付的请求
 *
 * <AUTHOR>
 * @since 2024/5/26 21:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderPayRequest extends BaseRequest {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    @ChineseDescription("订单id")
    private Long orderId;

}