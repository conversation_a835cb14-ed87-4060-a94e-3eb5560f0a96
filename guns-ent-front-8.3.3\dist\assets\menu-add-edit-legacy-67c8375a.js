System.register(["./index-legacy-ee1db0c7.js","./menu-form-legacy-0c3258cc.js","./MenuApi-legacy-240954bc.js","./index-legacy-45c79de7.js","./index-legacy-94a6fc23.js","./AppApi-legacy-f1c900da.js"],(function(e,a){"use strict";var n,t,l,u,d,i,s,m,r,o;return{setters:[e=>{n=e.r,t=e.o,l=e.a,u=e.f,d=e.w,i=e.d,s=e.m,m=e.M},e=>{r=e.default},e=>{o=e.M},null,null,null],execute:function(){e("default",{__name:"menu-add-edit",props:{visible:Boolean,data:Object,appId:String,menuParentId:String,menuParentName:String},emits:["update:visible","done"],setup(e,{emit:a}){const c=e,v=a,p=n(!1),f=n(!1),g=n({antdvVisible:"Y",menuSort:100,menuType:10,appId:c.appId,menuParentId:c.menuParentId,menuParentName:c.menuParentName}),y=n(null);t((()=>{c.data?(f.value=!0,b()):f.value=!1}));const b=()=>{o.detail({menuId:c.data.menuId}).then((e=>{g.value=Object.assign({},e),g.value.menuId=c.data.menuId}))},I=e=>{v("update:visible",e)},j=()=>{y.value.$refs.formRef.validate().then((async e=>{if(e){p.value=!0;let e=null;e=f.value?o.edit(g.value):o.add(g.value),e.then((async e=>{p.value=!1,s.success(e.message),I(!1),v("done")})).catch((()=>{p.value=!1}))}}))};return(e,a)=>{const n=m;return l(),u(n,{width:800,maskClosable:!1,visible:c.visible,"confirm-loading":p.value,forceRender:!0,title:f.value?"编辑菜单":"新建菜单","body-style":{paddingBottom:"8px"},"onUpdate:visible":I,onOk:j,class:"common-modal",onClose:a[1]||(a[1]=e=>I(!1))},{default:d((()=>[i(r,{form:g.value,"onUpdate:form":a[0]||(a[0]=e=>g.value=e),ref_key:"menuFormRef",ref:y,isUpdate:f.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
