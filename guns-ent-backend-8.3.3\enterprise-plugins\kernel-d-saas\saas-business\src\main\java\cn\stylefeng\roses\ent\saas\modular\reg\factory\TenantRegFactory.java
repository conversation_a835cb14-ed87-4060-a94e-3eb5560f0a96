package cn.stylefeng.roses.ent.saas.modular.reg.factory;

import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.reg.pojo.TenantRegRequest;
import cn.stylefeng.roses.kernel.auth.api.password.PasswordStoredEncryptApi;
import cn.stylefeng.roses.kernel.auth.api.pojo.password.SaltedEncryptResult;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;

/**
 * 租户创建
 *
 * <AUTHOR>
 * @since 2024-02-22 18:53
 */
public class TenantRegFactory {

    /**
     * 默认租户的图片
     */
    private static final Long TENANT_LOGO_DEFAULT = 1479753047148322818L;

    /**
     * 根据注册信息创建租户
     *
     * <AUTHOR>
     * @since 2024-02-22 18:53
     */
    public static Tenant createTenant(TenantRegRequest tenantRegRequest) {

        PasswordStoredEncryptApi passwordStoredEncryptApi = SpringUtil.getBean(PasswordStoredEncryptApi.class);

        Tenant tenant = new Tenant();
        tenant.setTenantName(tenantRegRequest.getTenantName());
        tenant.setTenantCode(tenantRegRequest.getTenantCode());
        tenant.setEmail(tenantRegRequest.getEmail());
        tenant.setSafePhone(tenantRegRequest.getSafePhone());

        // 密码要加盐
        SaltedEncryptResult saltedEncryptResult = passwordStoredEncryptApi.encryptWithSalt(tenantRegRequest.getPassword());
        tenant.setPassword(saltedEncryptResult.getEncryptPassword());
        tenant.setPasswordSalt(saltedEncryptResult.getPasswordSalt());

        tenant.setTenantLogo(tenantRegRequest.getTenantLogo());
        tenant.setCompanyName(tenantRegRequest.getCompanyName());

        // 注册时候，设置租户为未启用
        tenant.setStatusFlag(StatusEnum.DISABLE.getCode());

        // 设置租户为未激活
        tenant.setActiveFlag(YesOrNotEnum.N.getCode());

        // 默认租户的图片
        tenant.setTenantLogo(TENANT_LOGO_DEFAULT);

        return tenant;
    }

}
