package cn.stylefeng.roses.kernel.impexp.org.controller;

import cn.stylefeng.roses.kernel.impexp.org.pojo.EnsureImportOrgRequest;
import cn.stylefeng.roses.kernel.impexp.org.pojo.OrgImportPreviewResult;
import cn.stylefeng.roses.kernel.impexp.org.service.OrgImportService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 组织机构的导入控制器
 *
 * <AUTHOR>
 * @since 2024-02-18 16:36
 */
@RestController
@ApiResource(name = "组织机构的导入控制器")
@Slf4j
public class OrgImportController {

    @Resource
    private OrgImportService orgImportService;

    /**
     * 获取机构的导入模板
     *
     * <AUTHOR>
     * @since 2024-02-18 18:06
     */
    @GetResource(name = "获取机构的导入模板", path = "/orgImport/getExcelTemplate")
    public void getExcelTemplate() {
        orgImportService.downloadImportTemplate();
    }

    /**
     * 导入组织机构并获取预览数据
     *
     * <AUTHOR>
     * @since 2024-02-18 18:17
     */
    @PostResource(name = "导入组织机构并获取预览数据", path = "/orgImport/uploadAndGetPreviewData")
    public ResponseData<OrgImportPreviewResult> uploadAndGetPreviewData(@RequestPart("file") MultipartFile file) {
        OrgImportPreviewResult orgImportPreviewResult = orgImportService.uploadAndGetPreviewData(file);
        return new SuccessResponseData<>(orgImportPreviewResult);
    }

    /**
     * 确认导入组织机构
     *
     * <AUTHOR>
     * @since 2024/2/10 16:34
     */
    @PostResource(name = "确认导入组织机构", path = "/orgImport/ensureImport")
    public ResponseData<?> ensureImport(@RequestBody EnsureImportOrgRequest ensureImportOrgRequest) {
        orgImportService.ensureImportOrg(ensureImportOrgRequest);
        return new SuccessResponseData<>();
    }

}
