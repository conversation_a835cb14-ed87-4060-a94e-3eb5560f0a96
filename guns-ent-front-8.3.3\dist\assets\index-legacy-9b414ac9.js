System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js"],(function(e,a){"use strict";var l,t,n,i,d,o,u,s,c,p,r,v,f,m,h,y,g,b,x,C,I,_,k,w,B;return{setters:[e=>{l=e._,t=e.r,n=e.bh,i=e.bi,d=e.L,o=e.o,u=e.bj,s=e.X,c=e.k,p=e.a,r=e.f,v=e.w,f=e.b,m=e.d,h=e.t,y=e.h,g=e.g,b=e.m,x=e.B,C=e.n,I=e.bk,_=e.c,k=e.a0},null,null,e=>{w=e.F,B=e.a}],execute:function(){var a=document.createElement("style");a.textContent=".ant-upload-span[data-v-fd601ba8]{width:calc(100% - 75px)}.ant-upload-span .actions-icon[data-v-fd601ba8]{font-size:20px;cursor:pointer;color:rgba(0,0,0,.45)}.ant-upload-list-item[data-v-fd601ba8]{height:40px!important;line-height:40px}.ant-upload-list-item-name[data-v-fd601ba8]{font-size:16px;line-height:40px!important}.ant-upload-list-item-info .ant-upload-text-icon .anticon[data-v-fd601ba8]{top:12px!important}.ant-space[data-v-fd601ba8]{line-height:45px!important}\n",document.head.appendChild(a);const j={class:"ant-upload-list-item ant-upload-list-item-done ant-upload-list-item-list-type-text"},$={class:"ant-upload-list-item-info"},F={class:"ant-upload-span"},O={class:"ant-upload-text-icon"},S={class:"ant-upload-list-item-name"},z={class:"ant-upload-list-item-card-actions"},A={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},isDesgin:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(e,{emit:a}){const l=e,_=a,k=t([]),A=t(`${n}${w}?secretFlag=N`),D=t({Authorization:i()}),L=t([]),N=t(!1),E=d((()=>{var e;return!!(null!==(e=l.record)&&void 0!==e&&e.itemMultipleChoiceFlag||l.multiple)}));o((()=>{J(),M()}));const J=()=>{l.value?E.value?L.value=l.normal?l.value:JSON.parse(l.value):L.value=[l.value]:L.value=[]},M=()=>{var e;N.value||0!=(null===(e=L.value)||void 0===e?void 0:e.length)&&(B.getAntdVInfoBatch({fileIdList:L.value}).then((e=>{e.data.forEach(((e,a)=>{e.fileId=L.value[a]})),k.value=u(e.data)})),N.value=!1)},U=e=>{"done"===e.file.status&&(e.file.fileId=e.file.response.data.fileId,b.success(`${e.file.name} 图片上传成功`),E.value?L.value.push(e.file.response.data.fileId):L.value=[e.file.response.data.fileId],N.value=!0,P())},P=()=>{let e;var a;e=E.value?l.normal?L.value:JSON.stringify(L.value):null!==(a=L.value[0])&&void 0!==a?a:"",_("update:value",e),_("onChange",l.record)};return s((()=>l.value),(e=>{J(),M()}),{deep:!0}),(e,a)=>{const t=c("upload-outlined"),d=x,o=c("paper-clip-outlined"),u=c("fund-view-outlined"),s=c("cloud-download-outlined"),b=c("delete-outlined"),_=C,w=I;return p(),r(w,{name:"file",multiple:E.value,action:A.value,headers:D.value,"list-type":"file",maxCount:E.value?1e7:1,disabled:l.readonly||l.disabled,"file-list":k.value,"onUpdate:fileList":a[0]||(a[0]=e=>k.value=e),onChange:U},{itemRender:v((({file:e})=>[f("div",j,[f("div",$,[f("span",F,[f("div",O,[m(o,{class:"actions-icon"})]),f("a",S,h(e.name),1),f("span",z,[m(_,null,{default:v((()=>[m(u,{class:"actions-icon",title:"预览",onClick:a=>(e=>{let a=`${n}/documentPreview?fileId=${e.fileId}&token=${i()}`;window.open(a,"_blank")})(e)},null,8,["onClick"]),m(s,{class:"actions-icon",title:"下载",onClick:a=>(e=>{B.download({token:i(),fileId:e.fileId})})(e)},null,8,["onClick"]),l.isDesgin||!l.readonly&&!l.disabled?(p(),r(b,{key:0,onClick:a=>(e=>{let a=e.fileId;if(a){let e=k.value.findIndex((e=>e.fileId===a));-1!==e&&(k.value.splice(e,1),L.value.splice(e,1),N.value=!0,P())}})(e),title:"删除",class:"actions-icon"},null,8,["onClick"])):y("",!0)])),_:2},1024)])])])])])),default:v((()=>[l.isDesgin||!l.readonly&&!l.disabled?(p(),r(d,{key:0,type:"primary"},{default:v((()=>[m(t),a[1]||(a[1]=g(" 上传附件 "))])),_:1,__:[1]})):y("",!0)])),_:1},8,["multiple","action","headers","maxCount","disabled","file-list"])}}},D=l(A,[["__scopeId","data-v-fd601ba8"]]),L={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const a=t(""),l=t(!1),n=t(!1),i=t("请选择"),d=()=>{console.log(a.value)};return(e,t)=>{const o=D,u=k;return p(),_("div",L,[m(u,{title:"上传文件",bordered:!1},{default:v((()=>[m(o,{value:a.value,"onUpdate:value":t[0]||(t[0]=e=>a.value=e),disabled:l.value,readonly:n.value,onOnChange:d,placeholder:i.value},null,8,["value","disabled","readonly","placeholder"])])),_:1})])}}})}}}));
