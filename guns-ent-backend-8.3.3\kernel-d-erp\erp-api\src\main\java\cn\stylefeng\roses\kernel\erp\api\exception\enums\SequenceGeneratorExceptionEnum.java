package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 序列号生成器异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
@Getter
public enum SequenceGeneratorExceptionEnum implements AbstractExceptionEnum {

    /**
     * 序列配置不存在
     */
    SEQUENCE_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "序列配置不存在"),

    /**
     * 序列名称不能为空
     */
    SEQUENCE_NAME_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "序列名称不能为空"),

    /**
     * 序列名称已存在
     */
    SEQUENCE_NAME_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "序列名称已存在"),

    /**
     * 序列重置类型错误
     */
    SEQUENCE_RESET_TYPE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10004", "序列重置类型错误"),

    /**
     * 序列步长必须大于0
     */
    SEQUENCE_STEP_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10005", "序列步长必须大于0"),

    /**
     * 序列当前值不能为负数
     */
    SEQUENCE_CURRENT_VALUE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10006", "序列当前值不能为负数"),

    /**
     * 序列日期格式错误
     */
    SEQUENCE_DATE_FORMAT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10007", "序列日期格式错误");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    SequenceGeneratorExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
