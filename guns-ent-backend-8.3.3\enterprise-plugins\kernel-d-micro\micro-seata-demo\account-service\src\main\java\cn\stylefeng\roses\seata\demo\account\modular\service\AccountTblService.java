package cn.stylefeng.roses.seata.demo.account.modular.service;

import cn.stylefeng.roses.seata.demo.account.modular.entity.AccountTbl;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务类
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
public interface AccountTblService extends IService<AccountTbl> {

    /**
     * 更新用户余额
     *
     * <AUTHOR>
     * @date 2021/10/13 22:51
     */
    void updateMoney(String userId, int subMoney);

}