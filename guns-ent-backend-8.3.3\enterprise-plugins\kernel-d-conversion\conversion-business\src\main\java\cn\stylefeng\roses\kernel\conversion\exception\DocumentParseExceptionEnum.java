package cn.stylefeng.roses.kernel.conversion.exception;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 应用链接信息的分类异常相关枚举
 *
 * <AUTHOR>
 * @since 2023/03/27 15:44
 */
@Getter
public enum DocumentParseExceptionEnum implements AbstractExceptionEnum {

    /**
     * 不支持该文件的预览
     */
	EXCEPTION_FILE_TYPE_NOT_SUPPORT_VIEW(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "不支持该格式文件的预览"),

    /**
     * 文件流转换异常
     */
	EXCEPTION_FILE_IO_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "文件流转换异常"),

    /**
     * 文件流关闭异常
     */
	EXCEPTION_FILE_IO_CLOSE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "文件流关闭异常");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    DocumentParseExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}