<template>
  <!-- 新增编辑弹框 -->
  <a-modal
    :width="900"
    :maskClosable="false"
    :visible="props.visible"
    :confirm-loading="loading"
    :forceRender="true"
    :title="isUpdate ? '编辑第三方业务系统配置' : '新增第三方业务系统配置'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
    class="common-modal"
    @close="updateVisible(false)"
  >
    <sso-client-form v-model:form="form" ref="ssoClientFormRef" />
  </a-modal>
</template>

<script setup name="SsoClientAddEdit">
import {onMounted, ref} from 'vue';
import SsoClientForm from './sso-client-form.vue';
import {message} from 'ant-design-vue';
import {SsoClientApi} from '../api/SsoClientApi';
import {FileApi} from '@/views/system/backend/file/api/FileApi';

const props = defineProps({
  visible: Boolean,
  data: Object
});

const emits = defineEmits(['update:visible', 'done']);

// 弹框加载
const loading = ref(false);

// 是否是编辑状态
const isUpdate = ref(false);

// 表单数据
const form = ref({
  clientSort: 1000,
  loginPageType: 2,
  unifiedLogoutFlag: 'Y'
});

// ref
const ssoClientFormRef = ref(null);

onMounted(() => {
  if (props.data) {
    isUpdate.value = true;
    getDetail();
  } else {
    isUpdate.value = false;
  }
});

// 获取详情
const getDetail = () => {
  SsoClientApi.detail({ clientId: props.data.clientId }).then(res => {
    form.value = Object.assign({}, res);
    if (form.value.clientLogoFileId) {
      getFileListData(form.value.clientLogoFileId, 'clientLogoFileIdFileList');
    }
  });
};

// 获取文件详情列表
const getFileListData = (fileId, listName) => {
  FileApi.getAntdVInfoBatch({ fileIdList: [fileId] }).then(res => {
    ssoClientFormRef.value[listName] = res.data;
  });
};

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};

// 点击保存
const save = async () => {
  ssoClientFormRef.value.$refs.formRef.validate().then(async valid => {
    if (valid) {
      // 修改加载框为正在加载
      loading.value = true;

      let result = null;

      // 执行编辑或修改
      if (isUpdate.value) {
        result = SsoClientApi.edit(form.value);
      } else {
        result = SsoClientApi.add(form.value);
      }
      result
        .then(async result => {
          // 移除加载框
          loading.value = false;

          // 提示添加成功
          message.success(result.message);

          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          emits('done');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<style></style>
