package cn.stylefeng.roses.ent.mobile.manage.service;

import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserItem;
import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserRequest;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;

import java.util.List;

/**
 * 通用选择机构选择人员组件的接口
 *
 * <AUTHOR>
 * @since 2024-04-02 13:51
 */
public interface CommonSelectBusinessService {

    /**
     * 获取指定组织下机构和人员列表
     * <p>
     * 如果是选择机构，则不会返回人员
     * 如果是选择人员，则会同时返回组织机构和人员
     *
     * <AUTHOR>
     * @since 2024-04-02 14:04
     */
    List<OrgUserItem> commonGetOrgUserList(OrgUserRequest orgUserRequest);

    /**
     * 获取指定机构的子一级的机构列表
     *
     * <AUTHOR>
     * @since 2024-04-02 15:06
     */
    List<HrOrganization> getOrgList(Long orgId);

    /**
     * 获取指定机构下的人员和职务列表
     *
     * <AUTHOR>
     * @since 2024-04-02 15:05
     */
    List<SysUserOrg> getPersonAndPositionList(Long orgId);

    /**
     * 获取人员的详细信息
     *
     * <AUTHOR>
     * @since 2024-03-29 11:44
     */
    List<SysUser> getUserDetailList(String searchText, List<SysUserOrg> sysUserOrgList);

}
