import Request from '@/utils/request/request-util';

/**
 * 客户管理API
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
export class CustomerApi {
  
  /**
   * 新增客户
   * @param {Object} params 客户信息
   * @returns {Promise}
   */
  static add(params) {
    return Request.post('/erp/customer/add', params);
  }

  /**
   * 删除客户
   * @param {Object} params 包含customerId的参数
   * @returns {Promise}
   */
  static delete(params) {
    return Request.post('/erp/customer/delete', params);
  }

  /**
   * 批量删除客户
   * @param {Object} params 包含customerIdList的参数
   * @returns {Promise}
   */
  static batchDelete(params) {
    return Request.post('/erp/customer/batchDelete', params);
  }

  /**
   * 编辑客户
   * @param {Object} params 客户信息
   * @returns {Promise}
   */
  static edit(params) {
    return Request.post('/erp/customer/edit', params);
  }

  /**
   * 查询客户详情
   * @param {Object} params 包含customerId的参数
   * @returns {Promise}
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/customer/detail', params);
  }

  /**
   * 分页查询客户列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/customer/page', params);
  }

  /**
   * 查询客户列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findList(params) {
    return Request.getAndLoadData('/erp/customer/list', params);
  }

  /**
   * 更新客户状态
   * @param {Object} params 包含customerId和status的参数
   * @returns {Promise}
   */
  static updateStatus(params) {
    return Request.post('/erp/customer/updateStatus', params);
  }

  /**
   * 校验客户编码是否重复
   * @param {Object} params 包含customerCode和customerId的参数
   * @returns {Promise}
   */
  static validateCode(params) {
    return Request.getAndLoadData('/erp/customer/validateCode', params);
  }

  /**
   * 获取客户类型选项
   * @returns {Array}
   */
  static getCustomerTypeOptions() {
    return [
      { label: '企业', value: 'ENTERPRISE' },
      { label: '个人', value: 'INDIVIDUAL' },
      { label: '零售', value: 'RETAIL' }
    ];
  }

  /**
   * 获取客户等级选项
   * @returns {Array}
   */
  static getCustomerLevelOptions() {
    return [
      { label: '钻石', value: 'DIAMOND' },
      { label: '黄金', value: 'GOLD' },
      { label: '白银', value: 'SILVER' },
      { label: '青铜', value: 'BRONZE' }
    ];
  }

  /**
   * 获取客户状态选项
   * @returns {Array}
   */
  static getCustomerStatusOptions() {
    return [
      { label: '正常', value: 'ACTIVE' },
      { label: '停用', value: 'INACTIVE' },
      { label: '冻结', value: 'FROZEN' }
    ];
  }

  /**
   * 获取客户类型名称
   * @param {String} type 客户类型
   * @returns {String}
   */
  static getCustomerTypeName(type) {
    const options = CustomerApi.getCustomerTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取客户等级名称
   * @param {String} level 客户等级
   * @returns {String}
   */
  static getCustomerLevelName(level) {
    const options = CustomerApi.getCustomerLevelOptions();
    const option = options.find(item => item.value === level);
    return option ? option.label : level;
  }

  /**
   * 获取客户状态名称
   * @param {String} status 客户状态
   * @returns {String}
   */
  static getCustomerStatusName(status) {
    const options = CustomerApi.getCustomerStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? option.label : status;
  }

  /**
   * 获取状态标签颜色
   * @param {String} status 客户状态
   * @returns {String}
   */
  static getStatusTagColor(status) {
    switch (status) {
      case 'ACTIVE':
        return 'green';
      case 'INACTIVE':
        return 'orange';
      case 'FROZEN':
        return 'red';
      default:
        return 'default';
    }
  }

  /**
   * 获取客户等级标签颜色
   * @param {String} level 客户等级
   * @returns {String}
   */
  static getCustomerLevelTagColor(level) {
    switch (level) {
      case 'DIAMOND':
        return 'purple';
      case 'GOLD':
        return 'gold';
      case 'SILVER':
        return 'cyan';
      case 'BRONZE':
        return 'orange';
      default:
        return 'default';
    }
  }

  /**
   * 格式化金额显示
   * @param {Number} amount 金额
   * @returns {String}
   */
  static formatAmount(amount) {
    if (!amount) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  /**
   * 格式化账期显示
   * @param {Number} paymentTerms 账期天数
   * @returns {String}
   */
  static formatPaymentTerms(paymentTerms) {
    if (!paymentTerms) return '-';
    return `${paymentTerms}天`;
  }

  /**
   * 更新客户关联的区域
   * @param {Object} params 包含customerId和regionIds的参数
   * @returns {Promise}
   */
  static updateCustomerRegions(params) {
    return Request.post('/erp/customerRegion/updateCustomerRegions', params);
  }

  /**
   * 获取客户关联的区域列表
   * @param {Object} params 包含customerId的参数
   * @returns {Promise}
   */
  static getCustomerRegions(params) {
    return Request.getAndLoadData('/erp/customerRegion/getCustomerRegions', params);
  }

  /**
   * 根据区域筛选客户分页数据
   * @param {Object} params 查询参数，包含regionId和includeChildRegions
   * @returns {Promise}
   */
  static findPageByRegion(params) {
    return Request.getAndLoadData('/erp/customer/pageByRegion', params);
  }

}
