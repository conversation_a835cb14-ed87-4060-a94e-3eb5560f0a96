package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品分类管理实体类
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
@TableName(value = "erp_product_category", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ErpProductCategory extends BaseEntity {

    /**
     * 分类ID
     */
    @TableId(value = "category_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("分类ID")
    private Long categoryId;

    /**
     * 分类编码
     */
    @TableField("category_code")
    @ChineseDescription("分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @TableField("category_name")
    @ChineseDescription("分类名称")
    private String categoryName;

    /**
     * 父级分类ID
     */
    @TableField("parent_id")
    @ChineseDescription("父级分类ID")
    private Long parentId;

    /**
     * 分类层级（1-一级分类，2-二级分类，3-三级分类，4-四级分类，5-五级分类）
     */
    @TableField("category_level")
    @ChineseDescription("分类层级")
    private Integer categoryLevel;

    /**
     * 分类路径（用/分隔，如：1/2/3）
     */
    @TableField("category_path")
    @ChineseDescription("分类路径")
    private String categoryPath;

    /**
     * 排序号
     */
    @TableField("sort_order")
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;
}
