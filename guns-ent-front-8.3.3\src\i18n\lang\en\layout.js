/* 主框架 */
export default {
  home: 'Home',
  header: {
    profile: 'Profile',
    userinfo: 'UserInfo',
    password: 'Password',
    logout: 'SignOut'
  },
  footer: {
    website: 'Website',
    document: 'Document',
    authorization: 'Authorization',
    copyright: 'Copyright © 2021 Wuhan EClouds Technology Co., Ltd'
  },
  logout: {
    title: 'Confirm',
    message: 'Are you sure you want to log out?'
  },
  setting: {
    title: 'Theme Setting',
    sideStyles: {
      dark: 'Dark Sidebar',
      light: 'Light Sidebar'
    },
    headStyles: {
      light: 'Light Header',
      dark: 'Dark Header',
      primary: 'Primary Header'
    },
    layoutStyles: {
      side: 'Side Menu Layout',
      top: 'Top Menu Layout',
      mix: 'Mix Menu Layout'
    },
    colors: {
      default: 'Daybreak Blue',
      dust: 'Dust Blue',
      sunset: 'Sunset Orange',
      volcano: 'Volcano',
      purple: 'Golden Purple',
      cyan: 'Cyan',
      green: 'Polar Green',
      geekblue: 'Geek Blue'
    },
    darkMode: 'Dark Mode',
    layoutStyle: 'Navigation Mode',
    sideMenuStyle: 'Sidebar Double Menu',
    bodyFull: 'Body Fixed Width',
    other: 'Other Setting',
    fixedHeader: 'Fixed Header',
    fixedSidebar: 'Fixed Sidebar',
    fixedBody: 'Fixed Body',
    logoAutoSize: 'Logo In Header',
    styleResponsive: 'Responsive',
    colorfulIcon: 'Colorful Icon',
    sideUniqueOpen: 'Menu Unique Open',
    sideInitOpenAll: 'Menu Default Open All',
    weakMode: 'Weak Mode',
    showFooter: 'Show Footer',
    showTabs: 'Show Tabs',
    tabsConfig: 'Tabs Config',
    tabStyle: 'Tab Style',
    tabStyles: {
      default: 'Default',
      dot: 'Dot',
      card: 'Card'
    },
    transitionName: 'Transition',
    transitions: {
      slideRight: 'Slide Right',
      slideBottom: 'Slide Bottom',
      zoomIn: 'Zoom In',
      zoomOut: 'Zoom Out',
      fade: 'Fade'
    },
    reset: 'Reset',
    tips: 'It will remember your configuration the next time you open it.'
  }
};
