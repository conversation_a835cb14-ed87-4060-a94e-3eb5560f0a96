import{R as s,r as n,o as y,a as N,f as U,w as _,d as L,m as h,M as O}from"./index-18a1ea24.js";import S from"./notice-form-d80b9e4c.js";class u{static findPage(e){return s.getAndLoadData("/sysNotice/page",e)}static add(e){return s.post("/sysNotice/add",e)}static detail(e){return s.getAndLoadData("/sysNotice/detail",e)}static edit(e){return s.post("/sysNotice/edit",e)}static delete(e){return s.post("/sysNotice/delete",e)}static batchDelete(e){return s.post("/sysNotice/batchDelete",e)}static detail(e){return s.getAndLoadData("/sysNotice/detail",e)}static retractNotice(e){return s.post("/sysNotice/retractNotice",e)}static publishNotice(e){return s.post("/sysNotice/publishNotice",e)}}const D={__name:"notice-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(p,{emit:e}){const r=p,f=e,i=n(!1),l=n(!1),t=n({priorityLevel:"high",noticeUserScope:{pointUserList:[],pointOrgList:[]}}),v=n(null);y(()=>{r.data?(l.value=!0,b()):l.value=!1});const b=()=>{u.detail({noticeId:r.data.noticeId}).then(o=>{t.value=Object.assign({},o),t.value.noticeUserScope.pointOrgList||(t.value.noticeUserScope.pointOrgList=[]),t.value.noticeUserScope.pointUserList||(t.value.noticeUserScope.pointUserList=[])})},c=o=>{f("update:visible",o)},g=async()=>{v.value.$refs.formRef.validate().then(async o=>{if(o){i.value=!0;let a=null;l.value?a=u.edit(t.value):a=u.add(t.value),a.then(async d=>{i.value=!1,h.success(d.message),c(!1),f("done")}).catch(()=>{i.value=!1})}})};return(o,a)=>{const d=O;return N(),U(d,{width:800,maskClosable:!1,visible:r.visible,"confirm-loading":i.value,forceRender:!0,title:l.value?"\u7F16\u8F91\u901A\u77E5\u516C\u544A":"\u65B0\u5EFA\u901A\u77E5\u516C\u544A","body-style":{paddingBottom:"8px"},"onUpdate:visible":c,onOk:g,onClose:a[1]||(a[1]=m=>c(!1))},{default:_(()=>[L(S,{form:t.value,"onUpdate:form":a[0]||(a[0]=m=>t.value=m),ref_key:"noticeFormRef",ref:v},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}},A=Object.freeze(Object.defineProperty({__proto__:null,default:D},Symbol.toStringTag,{value:"Module"}));export{u as N,D as _,A as n};
