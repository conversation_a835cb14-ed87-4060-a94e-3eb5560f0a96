package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单实体类
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@TableName(value = "erp_purchase_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrder extends BaseEntity {

    /**
     * 入库单ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ChineseDescription("入库单ID")
    private Long id;

    /**
     * 入库单号
     */
    @TableField("order_no")
    @ChineseDescription("入库单号")
    private String orderNo;

    /**
     * 供应商ID
     */
    @TableField("supplier_id")
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 状态：DRAFT(草稿)、CONFIRMED(已确认)、COMPLETED(已完成)
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 总金额
     */
    @TableField("total_amount")
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

    /**
     * 付款方式
     */
    @TableField("payment_method")
    @ChineseDescription("付款方式")
    private String paymentMethod;

    /**
     * 付款账户
     */
    @TableField("payment_account")
    @ChineseDescription("付款账户")
    private String paymentAccount;

    /**
     * 订单日期
     */
    @TableField("order_date")
    @ChineseDescription("订单日期")
    private LocalDate orderDate;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 关联的采购入库单明细列表（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的采购入库单明细列表")
    private List<PurchaseOrderDetail> detailList;

    /**
     * 关联的供应商信息（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的供应商信息")
    private ErpSupplier supplier;

}