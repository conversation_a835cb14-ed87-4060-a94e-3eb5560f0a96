# UniversalTree 通用树结构组件

## 概述

UniversalTree 是一个基于 Vue 3 和 Ant Design Vue 的通用树结构组件，旨在统一 ERP 系统中各个管理模块的左侧树结构组件，确保用户界面的一致性和用户体验的统一性。

## 特性

- 🌳 **统一的树结构**: 提供一致的树形结构展示和交互体验
- 🔧 **高度可配置**: 支持灵活的配置选项，适应不同业务场景
- 🚀 **懒加载支持**: 支持大数据量的懒加载机制
- 🔍 **搜索功能**: 内置搜索功能，支持实时搜索和高亮显示
- 🎨 **样式统一**: 统一的样式规范，确保视觉一致性
- 🛠️ **操作支持**: 支持新增、编辑、删除等操作
- 📱 **响应式设计**: 支持移动端和桌面端的响应式布局
- 🧪 **完整测试**: 提供完整的单元测试覆盖

## 快速开始

### 基本用法

```vue
<template>
  <universal-tree
    :data-source="dataSource"
    :field-mapping="fieldMapping"
    :display-config="displayConfig"
    :interaction-config="interactionConfig"
    :action-config="actionConfig"
    @select="handleSelect"
    @add="handleAdd"
    @edit="handleEdit"
    @delete="handleDelete"
  />
</template>

<script setup>
import UniversalTree from '@/components/UniversalTree/UniversalTree.vue'
import { RegionApi } from '@/views/erp/region/api/regionApi'

const dataSource = {
  api: RegionApi.findTree,
  lazyLoadApi: RegionApi.findTreeWithLazy,
  searchParam: 'searchText',
  parentIdParam: 'parentId'
}

const fieldMapping = {
  key: 'regionId',
  title: 'regionName',
  children: 'children',
  hasChildren: 'hasChildren',
  level: 'regionLevel'
}

const displayConfig = {
  title: '区域管理',
  showHeader: true,
  showSearch: true,
  showAddButton: true,
  showEditIcons: true
}

const interactionConfig = {
  selectable: true,
  expandable: true,
  lazyLoad: true,
  defaultExpandLevel: 3
}

const actionConfig = {
  allowAdd: true,
  allowEdit: true,
  allowDelete: true
}

const handleSelect = (selectedKeys, selectedNodes) => {
  console.log('选中节点:', selectedKeys, selectedNodes)
}

const handleAdd = (parentNode) => {
  console.log('新增节点:', parentNode)
}

const handleEdit = (node) => {
  console.log('编辑节点:', node)
}

const handleDelete = (node) => {
  console.log('删除节点:', node)
}
</script>
```

### 使用预设配置

```vue
<template>
  <universal-tree v-bind="treeConfig" @select="handleSelect" />
</template>

<script setup>
import UniversalTree from '@/components/UniversalTree/UniversalTree.vue'
import { TreeConfigFactory } from '@/components/UniversalTree/configFactory.js'

// 使用区域管理预设配置
const treeConfig = TreeConfigFactory.createRegionConfig()

// 或者使用只读模式
// const treeConfig = TreeConfigFactory.createRegionReadOnlyConfig()

const handleSelect = (selectedKeys, selectedNodes) => {
  console.log('选中节点:', selectedKeys, selectedNodes)
}
</script>
```

## 配置选项

### DataSource 数据源配置

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| api | Function | 是 | - | 主要数据加载API |
| lazyLoadApi | Function | 否 | - | 懒加载API |
| searchParam | String | 否 | 'searchText' | 搜索参数名 |
| parentIdParam | String | 否 | 'parentId' | 父级ID参数名 |

### FieldMapping 字段映射配置

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| key | String | 是 | - | 节点唯一标识字段 |
| title | String | 是 | - | 节点显示文本字段 |
| children | String | 是 | - | 子节点字段 |
| hasChildren | String | 否 | - | 是否有子节点字段 |
| level | String | 否 | - | 层级字段 |

### DisplayConfig 显示配置

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| title | String | 否 | '' | 树结构标题 |
| showHeader | Boolean | 否 | true | 是否显示头部 |
| showSearch | Boolean | 否 | true | 是否显示搜索框 |
| searchPlaceholder | String | 否 | '请输入关键字搜索' | 搜索框占位符 |
| showAddButton | Boolean | 否 | false | 是否显示新增按钮 |
| showEditIcons | Boolean | 否 | false | 是否显示编辑图标 |
| showIcon | Boolean | 否 | false | 是否显示节点图标 |
| isSetWidth | Boolean | 否 | true | 是否设置固定宽度 |

### InteractionConfig 交互配置

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| selectable | Boolean | 否 | true | 是否可选择 |
| expandable | Boolean | 否 | true | 是否可展开 |
| lazyLoad | Boolean | 否 | false | 是否懒加载 |
| defaultExpandLevel | Number | 否 | 1 | 默认展开层级 |
| allowMultiSelect | Boolean | 否 | false | 是否允许多选 |

### ActionConfig 操作配置

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| allowAdd | Boolean | 否 | false | 是否允许新增 |
| allowEdit | Boolean | 否 | false | 是否允许编辑 |
| allowDelete | Boolean | 否 | false | 是否允许删除 |
| customActions | Array | 否 | [] | 自定义操作 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| select | (selectedKeys, selectedNodes) | 节点选择事件 |
| expand | (expandedKeys) | 节点展开事件 |
| search | (searchText) | 搜索事件 |
| add | (parentNode) | 新增事件 |
| edit | (node) | 编辑事件 |
| delete | (node) | 删除事件 |
| customAction | (actionKey, node) | 自定义操作事件 |
| load | (data) | 数据加载事件 |
| loadError | (error) | 加载错误事件 |

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| reload | - | - | 重新加载数据 |
| getSelectedNodes | - | Array | 获取选中的节点 |
| setSelectedKeys | (keys) | - | 设置选中的节点 |
| expandNodes | (keys) | - | 展开指定节点 |
| collapseNodes | (keys) | - | 收起指定节点 |

## 预设配置

### 区域管理树

```javascript
import { TreeConfigFactory } from '@/components/UniversalTree/configFactory.js'

const config = TreeConfigFactory.createRegionConfig()
```

### 产品分类管理树

```javascript
const config = TreeConfigFactory.createProductCategoryConfig()
```

### 只读模式树

```javascript
// 区域只读树
const config = TreeConfigFactory.createRegionReadOnlyConfig()

// 产品分类只读树
const config = TreeConfigFactory.createProductCategoryReadOnlyConfig()
```

### 页面专用配置

```javascript
// 商品管理页面配置
const config = TreeConfigFactory.createProductManagementConfig()

// 客户管理页面配置
const config = TreeConfigFactory.createCustomerManagementConfig()

// 供应商管理页面配置
const config = TreeConfigFactory.createSupplierManagementConfig()
```

## 错误处理

组件内置了完善的错误处理机制：

- **数据加载错误**: 显示错误提示和重试按钮
- **懒加载错误**: 节点级错误处理，支持重试
- **搜索错误**: 清空搜索结果，显示错误提示
- **操作错误**: 显示具体的操作失败原因
- **网络异常**: 提供离线模式支持

## 性能优化

- **懒加载**: 按需加载子节点数据
- **虚拟滚动**: 大数据量时的性能优化
- **缓存机制**: 缓存已加载的节点数据
- **防抖搜索**: 避免频繁的搜索请求

## 样式定制

组件使用统一的样式规范，支持主题定制：

```less
// 自定义主题变量
:root {
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --border-color: #d9d9d9;
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
}
```

## 测试

运行单元测试：

```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test UniversalTree.test.js

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 浏览器兼容性

- Chrome 63+
- Firefox 67+
- Safari 12+
- Edge 79+

## 更新日志

### v1.0.0 (2025-01-24)

- 🎉 初始版本发布
- ✨ 支持区域管理和产品分类管理
- ✨ 提供完整的配置选项和预设配置
- ✨ 支持懒加载和搜索功能
- ✨ 完善的错误处理机制
- ✨ 完整的单元测试覆盖

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。