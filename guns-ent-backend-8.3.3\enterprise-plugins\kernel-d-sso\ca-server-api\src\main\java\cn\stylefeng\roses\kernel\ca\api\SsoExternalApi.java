package cn.stylefeng.roses.kernel.ca.api;

import cn.stylefeng.roses.kernel.ca.api.pojo.external.request.*;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.response.SsoExternalCreateSessionResponse;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.response.SsoExternalDetectionResponse;

/**
 * 针对于外部单点打通的api
 * <p>
 * 如果以前系统有建设单独的单点，那新业务系统和旧业务系统打通就需要本接口实现
 * <p>
 * 多单点打通的前提是多个单点的的cookie可以在同一个域名下
 *
 * <AUTHOR>
 * @date 2021/2/23 10:58
 */
public interface SsoExternalApi {

    /**
     * 远程根据ca token获取是否有对应的单点登录会话
     * <p>
     * 如果存在对应会话，则返回用户的信息
     *
     * <AUTHOR>
     * @date 2021/2/20 16:20
     */
    SsoExternalDetectionResponse tokenDetection(SsoExternalDetectionRequest ssoExternalDetectionRequest);

    /**
     * 远程根据ca token踢掉登录用户
     *
     * <AUTHOR>
     * @date 2021/2/20 16:47
     */
    void kickOff(SsoExternalKickOffRequest ssoExternalKickOffRequest);

    /**
     * 远程创建sso会话（只创建会话，不写cookie）
     *
     * <AUTHOR>
     * @date 2021/2/20 16:47
     */
    SsoExternalCreateSessionResponse createSession(SsoExternalCreateSessionRequest ssoExternalCreateSessionRequest);

    /**
     * 刷新CaLoginUser对象的内容
     *
     * <AUTHOR>
     * @date 2021/3/8 10:49
     */
    void refreshSession(SsoExternalRefreshSessionRequest ssoExternalRefreshSessionRequest);

    /**
     * 根据帐号ID强迫当前帐号下线
     *
     * <AUTHOR>
     * @date 2021-4-2 16:50
     */
    void offlineCaByAccountId(SsoExternalAccountRequest ssoExternalAccountRequest);
}
