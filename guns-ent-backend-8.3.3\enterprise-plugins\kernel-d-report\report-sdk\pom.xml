<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-report</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>report-sdk</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--SaaS模块的api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>report-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--auth模块的api-->
        <!--获取当前登录用户的租户信息-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>auth-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--积木报表-->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot3-starter-fastjson2</artifactId>
            <version>1.8.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>
