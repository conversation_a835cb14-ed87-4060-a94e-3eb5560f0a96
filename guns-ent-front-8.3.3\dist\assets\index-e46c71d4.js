import{r as o,o as P,X as V,a as h,c as b,d as m,w as g,f as k,h as N,l as O,bf as R,a0 as F}from"./index-18a1ea24.js";/* empty css              */import{_ as L}from"./index-3a0e5c06.js";import{C as j}from"./CommonApi-27ae49e3.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const D={class:"wh100"},I={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(f,{emit:d}){const a=f,p=d,s=o(!0),r=o(!1),l=o([]),i=o(""),u=o(!1),c=o({selectPositionList:[]});P(()=>{var e;(e=a.record)!=null&&e.itemMultipleChoiceFlag||a.multiple?s.value=!1:s.value=!0,v()});const v=async()=>{if(a.value)if(s.value){if(r.value)return;const e=await j.getPositionName({positionId:a.value});e.data&&(l.value=[{id:a.value,name:e.data}])}else l.value=a.normal?l.value:JSON.parse(a.value);else l.value=[];r.value=!1,i.value=l.value.map(e=>e.name).join("\uFF1B")},y=()=>{var e,t;(e=l.value)!=null&&e.length?c.value.selectPositionList=(t=l.value)==null?void 0:t.map(n=>({bizId:n.id,name:n.name})):c.value.selectPositionList=[],u.value=!0},C=e=>{var t;l.value=(t=e.selectPositionList)==null?void 0:t.map(n=>({id:n.bizId,name:n.name})),i.value=e.selectPositionList.map(n=>n.name).join("\uFF1B"),r.value=!0,w()},w=()=>{let e=l.value.length>0?s.value?l.value[0].id:a.normal?l.value:JSON.stringify(l.value):"";p("update:value",e),p("onChange",a.record),S()},S=async()=>{var e;!a.normal&&((e=a.formRef)!=null&&e.validateFields)&&await a.formRef.validateFields([a.record.fieldCode])};return V(()=>a.value,e=>{v()},{deep:!0}),(e,t)=>{const n=O,x=L,B=R;return h(),b("div",D,[m(n,{value:i.value,"onUpdate:value":t[0]||(t[0]=_=>i.value=_),disabled:a.readonly||a.disabled,class:"w-full",placeholder:f.placeholder,onFocus:y},null,8,["value","disabled","placeholder"]),m(B,null,{default:g(()=>[u.value?(h(),k(x,{key:0,visible:u.value,"onUpdate:visible":t[1]||(t[1]=_=>u.value=_),data:c.value,showTab:["position"],changeHeight:!0,title:"\u8BF7\u9009\u62E9\u804C\u4F4D",isRadio:s.value,onDone:C},null,8,["visible","data","isRadio"])):N("",!0)]),_:1})])}}},U={class:"guns-body guns-body-card"},K={__name:"index",setup(f){const d=o(""),a=o(!1),p=o(!1),s=o("\u8BF7\u9009\u62E9"),r=()=>{console.log(d.value)};return(l,i)=>{const u=I,c=F;return h(),b("div",U,[m(c,{title:"\u804C\u4F4D\u9009\u62E9",bordered:!1},{default:g(()=>[m(u,{value:d.value,"onUpdate:value":i[0]||(i[0]=v=>d.value=v),disabled:a.value,readonly:p.value,onOnChange:r,placeholder:s.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{K as default};
