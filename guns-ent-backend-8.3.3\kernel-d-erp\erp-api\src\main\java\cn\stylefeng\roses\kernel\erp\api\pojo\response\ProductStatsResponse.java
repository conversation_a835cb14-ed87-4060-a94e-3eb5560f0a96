package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商品统计信息响应参数
 *
 * <AUTHOR>
 * @since 2025/07/28 16:00
 */
@Data
public class ProductStatsResponse {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 计价类型
     */
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 当前库存
     */
    @ChineseDescription("当前库存")
    private BigDecimal currentStock;

    /**
     * 总采购数量
     */
    @ChineseDescription("总采购数量")
    private BigDecimal totalPurchaseQuantity;

    /**
     * 总采购金额
     */
    @ChineseDescription("总采购金额")
    private BigDecimal totalPurchaseAmount;

    /**
     * 平均采购价格
     */
    @ChineseDescription("平均采购价格")
    private BigDecimal avgPurchasePrice;

    /**
     * 最后采购日期
     */
    @ChineseDescription("最后采购日期")
    private LocalDate lastPurchaseDate;

}