<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-s-dict</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dict-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--字典的业务-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>dict-business</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--城市的字典业务-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>dict-city-business</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--缓存配置-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
