System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./SupplierSelector-legacy-fba3813b.js","./InventoryApi-legacy-319e6456.js","./InventoryDetail-legacy-994a0a92.js","./InventoryHistory-legacy-6ae6f9d6.js","./InventoryAdjustModal-legacy-2a1676ea.js","./SetMinStockModal-legacy-05f003c9.js","./InventoryStatisticsModal-legacy-541704cd.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-510bfbb8.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./InventoryHistoryApi-legacy-e1cc044f.js","./index-legacy-c65a6a4e.js","./index-legacy-94a6fc23.js","./ProductSelector-legacy-b94adcaf.js","./ProductApi-legacy-33feae42.js","./productCategoryApi-legacy-247b2407.js"],(function(e,a){"use strict";var t,l,o,n,i,d,r,s,c,u,p,f,v,h,g,y,k,S,m,_,w,C,b,x,M,I,T,N,A,O,E,j,R,U,H,L,F,P,D,G,z,B,V,W,K;return{setters:[e=>{t=e._},e=>{l=e._,o=e.K,n=e.a1,i=e.r,d=e.L,r=e.N,s=e.s,c=e.o,u=e.k,p=e.a,f=e.c,v=e.b,h=e.d,g=e.w,y=e.g,k=e.t,S=e.O,m=e.Q,_=e.h,w=e.f,C=e.a2,b=e.m,x=e.n,M=e.I,I=e.p,T=e.q,N=e.B,A=e.D,O=e.l,E=e.V,j=e.u,R=e.v,U=e.W,H=e.J,L=e.G,F=e.H,P=e.U},null,e=>{D=e._},e=>{G=e.I},e=>{z=e.default},e=>{B=e.default},e=>{V=e.default},e=>{W=e.default},e=>{K=e.default},null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".divider[data-v-aaa7dfda]{height:20px;margin:0 12px}.product-info[data-v-aaa7dfda]{text-align:left}.product-name[data-v-aaa7dfda]{font-weight:500;color:#262626;margin-bottom:4px;line-height:1.4}.product-details[data-v-aaa7dfda]{font-size:12px;color:#8c8c8c;line-height:1.3}.product-code[data-v-aaa7dfda]{margin-right:8px;color:#1890ff}.product-barcode[data-v-aaa7dfda]{margin-left:8px;color:#52c41a}.supplier-name[data-v-aaa7dfda]{font-weight:500;color:#262626;margin-bottom:4px;line-height:1.4}.stock-info[data-v-aaa7dfda]{display:flex;align-items:center;line-height:1.4}.stock-unit[data-v-aaa7dfda]{margin-left:4px;color:#8c8c8c;font-size:12px}.stock-normal[data-v-aaa7dfda]{color:#52c41a;font-weight:500}.stock-warning[data-v-aaa7dfda]{color:#faad14;font-weight:500}.stock-danger[data-v-aaa7dfda]{color:#ff4d4f;font-weight:500}.min-stock[data-v-aaa7dfda]{color:#8c8c8c;font-size:12px}.total-value[data-v-aaa7dfda]{font-weight:500;color:#1890ff;text-align:right}.table-link[data-v-aaa7dfda]{color:#1890ff;cursor:pointer;text-decoration:none;transition:color .2s ease}.table-link[data-v-aaa7dfda]:hover{color:#40a9ff;text-decoration:underline}[data-v-aaa7dfda] .ant-table-tbody>tr:hover>td{background-color:#fafafa!important}[data-v-aaa7dfda] .ant-table-thead>tr>th{background-color:#fafafa!important;border-bottom:1px solid #f0f0f0;font-weight:500;color:#262626}[data-v-aaa7dfda] .ant-table-tbody>tr>td{border-bottom:1px solid #f5f5f5;padding:12px 16px;vertical-align:middle}\n",document.head.appendChild(a);const q={class:"guns-layout"},J={class:"guns-layout-content"},Q={class:"guns-layout"},Z={class:"guns-layout-content-application"},Y={class:"content-mian"},X={class:"content-mian-header"},$={class:"header-content"},ee={class:"header-content-left"},ae={class:"header-content-right"},te={class:"content-mian-body"},le={class:"table-content"},oe={key:0,class:"super-search",style:{"margin-top":"8px"}},ne={key:0,class:"product-info"},ie={class:"product-name"},de=["onClick"],re={class:"product-details"},se={class:"product-code"},ce={key:0,class:"product-barcode"},ue={key:1},pe={class:"supplier-name"},fe={key:3,class:"stock-info"},ve={class:"stock-unit"},he={key:4,class:"min-stock"},ge={key:5,class:"total-value"};e("default",l({name:"InventoryIndex",components:{SmallDashOutlined:o,DownOutlined:n,SupplierSelector:D,InventoryDetail:z,InventoryHistory:B,InventoryAdjustModal:V,SetMinStockModal:W,InventoryStatisticsModal:K},setup(){const e=i(!1),a=i(!1),t=i(!1),l=i(!1),o=i(!1),n=i(!1),u=i({}),p=i(null),f=d((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),v=d((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),h=d((()=>r()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),g=s({searchText:"",productName:"",productCode:"",supplierId:void 0,pricingType:void 0,stockStatus:void 0,businessModeList:["PURCHASE_SALE","CONSIGNMENT"]}),y=()=>{p.value.reload()},k=()=>{try{G.exportInventory(g),b.success("导出成功")}catch(e){b.error("导出失败："+(e.message||"未知错误"))}};return c((()=>{})),{superSearch:e,showDetailModal:a,showHistoryModal:t,showAdjustModal:l,showSetMinStockModal:o,showStatisticsModal:n,currentRecord:u,tableRef:p,labelCol:f,wrapperCol:v,spanCol:h,where:g,columns:[{title:"商品信息",key:"productInfo",width:250,fixed:"left"},{title:"供应商",key:"supplierInfo",width:180},{title:"计价类型",key:"pricingType",width:100},{title:"当前库存",key:"currentStock",width:120,align:"right"},{title:"预警值",key:"minStock",width:100,align:"right"},{title:"库存价值",key:"totalValue",width:120,align:"right"},{title:"库存状态",key:"stockStatus",width:100},{title:"更新时间",dataIndex:"updateTime",key:"updateTime",width:160},{title:"操作",key:"action",width:200,fixed:"right"}],changeSuperSearch:()=>{e.value=!e.value},reload:y,reset:()=>{g.searchText="",g.productName="",g.productCode="",g.supplierId=void 0,g.pricingType=void 0,g.stockStatus=void 0,y()},getBusinessModeColor:e=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"}[e]||"default"),getPricingTypeName:e=>({NORMAL:"普通",WEIGHT:"称重",PIECE:"计件",VARIABLE:"变价"}[e]||e),getPricingTypeColor:e=>({NORMAL:"blue",WEIGHT:"orange",PIECE:"green",VARIABLE:"purple"}[e]||"default"),getStockClass:(e,a)=>{const t=parseFloat(e)||0,l=parseFloat(a)||0;return t<=0?"stock-danger":t<=l?"stock-warning":"stock-normal"},getStockUnit:(e,a)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"件";default:return a||"个"}},formatStock:(e,a)=>{if(!e)return"0";const t="WEIGHT"===a?3:0;return parseFloat(e).toFixed(t)},formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",getStockStatusName:e=>({NORMAL:"正常",WARNING:"预警",OUT_OF_STOCK:"缺货",FROZEN:"冻结"}[e]||"未知"),getStockStatusColor:e=>({NORMAL:"green",WARNING:"orange",OUT_OF_STOCK:"red",FROZEN:"blue"}[e]||"default"),calculateStockStatus:e=>{if(e.stockStatus)return e.stockStatus;const a=parseFloat(e.currentStock||0),t=parseFloat(e.minStock||0);return a<=0?"OUT_OF_STOCK":t>0&&a<=t?"WARNING":"NORMAL"},showDetail:e=>{u.value={...e},a.value=!0},showHistory:e=>{u.value={...e},t.value=!0},handleShowHistoryFromDetail:e=>{u.value={...e},a.value=!1,t.value=!0},adjustStock:e=>{u.value={...e},l.value=!0},transferStock:async e=>{try{u.value={...e},b.info("库存调拨功能待实现")}catch(a){throw a}},initStock:async e=>{try{await G.initStock({productId:e.productId}),y()}catch(a){throw a}},openAdjustModal:()=>{u.value={},l.value=!0},openSetMinStockModal:e=>{u.value={...e},o.value=!0},moreClick:({key:e})=>{"1"===e?k():"2"===e&&(n.value=!0)},exportData:k,handleAdjustOk:()=>{l.value=!1,y()},handleSetMinStockOk:()=>{o.value=!1,y()}}}},[["render",function(e,a,l,o,n,i){const d=x,r=M,s=I,c=T,b=u("small-dash-outlined"),G=N,z=A,B=O,V=E,W=j,K=R,ye=D,ke=U,Se=H,me=L,_e=F,we=P,Ce=t,be=u("inventory-detail"),xe=u("inventory-history"),Me=u("inventory-adjust-modal"),Ie=u("set-min-stock-modal"),Te=u("inventory-statistics-modal");return p(),f("div",q,[v("div",J,[v("div",Q,[v("div",Z,[v("div",Y,[v("div",X,[v("div",$,[v("div",ee,[h(d,{size:16})]),v("div",ae,[h(d,{size:16},{default:g((()=>[h(z,null,{overlay:g((()=>[h(c,{onClick:o.moreClick},{default:g((()=>[h(s,{key:"1"},{default:g((()=>[h(r,{iconClass:"icon-opt-daochu",color:"#60666b"}),a[12]||(a[12]=v("span",null,"导出数据",-1))])),_:1,__:[12]}),h(s,{key:"2"},{default:g((()=>[h(r,{iconClass:"icon-opt-tongji",color:"#60666b"}),a[13]||(a[13]=v("span",null,"库存统计",-1))])),_:1,__:[13]})])),_:1},8,["onClick"])])),default:g((()=>[h(G,{class:"border-radius"},{default:g((()=>[a[14]||(a[14]=y(" 更多 ")),h(b)])),_:1,__:[14]})])),_:1})])),_:1})])])]),v("div",te,[v("div",le,[h(Ce,{columns:o.columns,where:o.where,fieldBusinessCode:"ERP_INVENTORY_TABLE",showTableTool:"",showToolTotal:!1,rowId:"productId",ref:"tableRef",url:"/erp/inventory/page",methods:"post"},{toolLeft:g((()=>[h(B,{value:o.where.searchText,"onUpdate:value":a[0]||(a[0]=e=>o.where.searchText=e),placeholder:"商品名称、编码、条形码（回车搜索）",onPressEnter:o.reload,bordered:!1,class:"search-input"},{prefix:g((()=>[h(r,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),h(V,{type:"vertical",class:"divider"}),v("a",{onClick:a[1]||(a[1]=(...e)=>o.changeSuperSearch&&o.changeSuperSearch(...e))},k(o.superSearch?"收起":"高级筛选"),1)])),toolBottom:g((()=>[o.superSearch?(p(),f("div",oe,[h(_e,{model:o.where,labelCol:o.labelCol,"wrapper-col":o.wrapperCol},{default:g((()=>[h(me,{gutter:16},{default:g((()=>[h(K,S(m(o.spanCol)),{default:g((()=>[h(W,{label:"商品名称:"},{default:g((()=>[h(B,{value:o.where.productName,"onUpdate:value":a[2]||(a[2]=e=>o.where.productName=e),placeholder:"请输入商品名称",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),h(K,S(m(o.spanCol)),{default:g((()=>[h(W,{label:"商品编码:"},{default:g((()=>[h(B,{value:o.where.productCode,"onUpdate:value":a[3]||(a[3]=e=>o.where.productCode=e),placeholder:"请输入商品编码",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),h(K,S(m(o.spanCol)),{default:g((()=>[h(W,{label:"供应商:"},{default:g((()=>[h(ye,{value:o.where.supplierId,"onUpdate:value":a[4]||(a[4]=e=>o.where.supplierId=e),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},placeholder:"请选择供应商",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),h(K,S(m(o.spanCol)),{default:g((()=>[h(W,{label:"计价类型:"},{default:g((()=>[h(Se,{value:o.where.pricingType,"onUpdate:value":a[5]||(a[5]=e=>o.where.pricingType=e),placeholder:"请选择计价类型",allowClear:""},{default:g((()=>[h(ke,{value:"NORMAL"},{default:g((()=>a[15]||(a[15]=[y("普通")]))),_:1,__:[15]}),h(ke,{value:"WEIGHT"},{default:g((()=>a[16]||(a[16]=[y("称重")]))),_:1,__:[16]}),h(ke,{value:"PIECE"},{default:g((()=>a[17]||(a[17]=[y("计件")]))),_:1,__:[17]}),h(ke,{value:"VARIABLE"},{default:g((()=>a[18]||(a[18]=[y("变价")]))),_:1,__:[18]})])),_:1},8,["value"])])),_:1})])),_:1},16),h(K,S(m(o.spanCol)),{default:g((()=>[h(W,{label:"库存状态:"},{default:g((()=>[h(Se,{value:o.where.stockStatus,"onUpdate:value":a[6]||(a[6]=e=>o.where.stockStatus=e),placeholder:"请选择库存状态",allowClear:""},{default:g((()=>[h(ke,{value:"NORMAL"},{default:g((()=>a[19]||(a[19]=[y("正常")]))),_:1,__:[19]}),h(ke,{value:"WARNING"},{default:g((()=>a[20]||(a[20]=[y("预警")]))),_:1,__:[20]}),h(ke,{value:"OUT_OF_STOCK"},{default:g((()=>a[21]||(a[21]=[y("缺货")]))),_:1,__:[21]})])),_:1},8,["value"])])),_:1})])),_:1},16),h(K,S(m(o.spanCol)),{default:g((()=>[h(W,{label:" ",colon:!1},{default:g((()=>[h(d,null,{default:g((()=>[h(G,{type:"primary",onClick:o.reload},{default:g((()=>a[22]||(a[22]=[y("搜索")]))),_:1,__:[22]},8,["onClick"]),h(G,{onClick:o.reset},{default:g((()=>a[23]||(a[23]=[y("重置")]))),_:1,__:[23]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):_("",!0)])),bodyCell:g((({column:e,record:a})=>["productInfo"===e.key?(p(),f("div",ne,[v("div",ie,[v("a",{onClick:e=>o.showDetail(a),class:"table-link"},k(a.productName),9,de)]),v("div",re,[v("span",se,k(a.productCode),1),a.barcode?(p(),f("span",ce,k(a.barcode),1)):_("",!0)])])):_("",!0),"supplierInfo"===e.key?(p(),f("div",ue,[v("div",pe,k(a.supplierName),1),a.businessModeName?(p(),w(we,{key:0,size:"small",color:o.getBusinessModeColor(a.businessMode)},{default:g((()=>[y(k(a.businessModeName),1)])),_:2},1032,["color"])):_("",!0)])):_("",!0),"pricingType"===e.key?(p(),w(we,{key:2,color:o.getPricingTypeColor(a.pricingType)},{default:g((()=>[y(k(o.getPricingTypeName(a.pricingType)),1)])),_:2},1032,["color"])):_("",!0),"currentStock"===e.key?(p(),f("div",fe,[v("span",{class:C(o.getStockClass(a.currentStock,a.minStock))},k(o.formatStock(a.currentStock,a.pricingType)),3),v("span",ve,k(o.getStockUnit(a.pricingType,a.unit)),1)])):_("",!0),"minStock"===e.key?(p(),f("span",he,k(o.formatStock(a.minStock,a.pricingType))+" "+k(o.getStockUnit(a.pricingType,a.unit)),1)):_("",!0),"totalValue"===e.key?(p(),f("span",ge,"¥"+k(o.formatAmount(a.totalValue)),1)):_("",!0),"stockStatus"===e.key?(p(),w(we,{key:6,color:o.getStockStatusColor(o.calculateStockStatus(a))},{default:g((()=>[y(k(o.getStockStatusName(o.calculateStockStatus(a))),1)])),_:2},1032,["color"])):_("",!0),"action"===e.key?(p(),w(d,{key:7,size:16},{default:g((()=>[h(r,{iconClass:"icon-opt-xiangqing","font-size":"24px",title:"查看详情",color:"#60666b",onClick:e=>o.showDetail(a)},null,8,["onClick"]),h(r,{iconClass:"icon-opt-shezhi","font-size":"24px",title:"设置预警值",color:"#60666b",onClick:e=>o.openSetMinStockModal(a)},null,8,["onClick"]),h(r,{iconClass:"icon-opt-xiangqing","font-size":"24px",title:"查看历史",color:"#60666b",onClick:e=>o.showHistory(a)},null,8,["onClick"])])),_:2},1024)):_("",!0)])),_:1},8,["columns","where"])])])])])])]),h(be,{visible:o.showDetailModal,"onUpdate:visible":a[7]||(a[7]=e=>o.showDetailModal=e),data:o.currentRecord,onShowHistory:o.handleShowHistoryFromDetail},null,8,["visible","data","onShowHistory"]),h(xe,{visible:o.showHistoryModal,"onUpdate:visible":a[8]||(a[8]=e=>o.showHistoryModal=e),data:o.currentRecord},null,8,["visible","data"]),h(Me,{visible:o.showAdjustModal,"onUpdate:visible":a[9]||(a[9]=e=>o.showAdjustModal=e),data:o.currentRecord,onOk:o.handleAdjustOk},null,8,["visible","data","onOk"]),h(Ie,{visible:o.showSetMinStockModal,"onUpdate:visible":a[10]||(a[10]=e=>o.showSetMinStockModal=e),data:o.currentRecord,onOk:o.handleSetMinStockOk},null,8,["visible","data","onOk"]),h(Te,{visible:o.showStatisticsModal,"onUpdate:visible":a[11]||(a[11]=e=>o.showStatisticsModal=e)},null,8,["visible"])])}],["__scopeId","data-v-aaa7dfda"]]))}}}));
