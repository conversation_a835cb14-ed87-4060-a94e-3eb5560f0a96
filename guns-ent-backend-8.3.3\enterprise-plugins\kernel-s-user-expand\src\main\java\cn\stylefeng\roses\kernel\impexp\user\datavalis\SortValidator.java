package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

import java.math.BigDecimal;

/**
 * 排序的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 23:45
 */
public class SortValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(originValue);
        }

        try {
            BigDecimal bigDecimal = new BigDecimal(originValue);
            return new ExcelLineParseResult(true, originValue, bigDecimal);
        } catch (Exception e) {
            return new ExcelLineParseResult(false, originValue, originValue, "排序数字格式不对");
        }
    }

}
