System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-16a1b89e.js","./index-legacy-676e1253.js","./index-legacy-efb51034.js","./index-legacy-45c79de7.js","./index-legacy-c65a6a4e.js","./InventoryAlertRecordApi-legacy-ebfe5eed.js","./AlertRecordDetail-legacy-fd19280e.js","./AlertRecordProcess-legacy-51e7bb1a.js","./index-legacy-e24582b9.js"],(function(e,a){"use strict";var l,t,r,d,o,n,c,s,u,i,h,_,v,m,p,f,g,y,R,S,T,b,C,k,O,x,w,I,E,P,D,N,L,A,V,Y,F,j,G,M,K;return{setters:[e=>{l=e._,t=e.ac,r=e.ad,d=e.ae,o=e.af,n=e.r,c=e.s,s=e.L,u=e.o,i=e.ag,h=e.k,_=e.a,v=e.c,m=e.d,p=e.w,f=e.g,g=e.t,y=e.b,R=e.ah,S=e.f,T=e.h,b=e.m,C=e.l,k=e.u,O=e.W,x=e.J,w=e.a6,I=e.B,E=e.n,P=e.H,D=e.a0,N=e.U,L=e.ai,A=e.i,V=e.z,Y=e.A,F=e.$,j=e.M},null,null,null,null,null,null,null,null,e=>{G=e.I},e=>{M=e.default},e=>{K=e.default},null],execute:function(){var a=document.createElement("style");a.textContent=".inventory-alert-record-container[data-v-ed5e37d3]{padding:16px}.search-card[data-v-ed5e37d3],.action-card[data-v-ed5e37d3],.table-card[data-v-ed5e37d3]{margin-bottom:16px}.search-form[data-v-ed5e37d3]{margin-bottom:-16px}\n",document.head.appendChild(a);const U={class:"inventory-alert-record-container"};e("default",l({name:"InventoryAlertRecordIndex",components:{SearchOutlined:t,ReloadOutlined:r,CheckOutlined:d,DownloadOutlined:o,AlertRecordDetail:M,AlertRecordProcess:K},setup(){const e=n(),a=n(!1),l=n([]),t=n([]),r=n(!1),d=n(!1),o=n(!1),h=n({}),_=n("RESOLVED"),v=n(""),m=c({productName:"",alertType:void 0,alertLevel:void 0,handleStatus:void 0,alertTimeRange:[]}),p=c({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}),f={selectedRowKeys:t,onChange:e=>{t.value=e},getCheckboxProps:e=>({disabled:"PENDING"!==e.handleStatus})},g=s((()=>t.value.length>0)),y=async()=>{try{a.value=!0;const e={current:p.current,size:p.pageSize,...m};m.alertTimeRange&&2===m.alertTimeRange.length&&(e.startTime=m.alertTimeRange[0].format("YYYY-MM-DD"),e.endTime=m.alertTimeRange[1].format("YYYY-MM-DD"));const t=await G.page(e);l.value=t.data.records,p.total=t.data.total}catch(e){console.error("获取预警记录失败:",e),b.error("获取预警记录失败")}finally{a.value=!1}};return u((()=>{y()})),{searchFormRef:e,loading:a,dataSource:l,selectedRowKeys:t,detailVisible:r,processVisible:d,batchProcessVisible:o,currentRecord:h,batchProcessType:_,batchProcessRemark:v,searchForm:m,pagination:p,columns:[{title:"商品名称",dataIndex:"productName",key:"productName",width:150},{title:"预警类型",dataIndex:"alertType",key:"alertType",width:100,slots:{customRender:"alertType"}},{title:"预警级别",dataIndex:"alertLevel",key:"alertLevel",width:100,slots:{customRender:"alertLevel"}},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:100,slots:{customRender:"currentStock"}},{title:"阈值",dataIndex:"thresholdValue",key:"thresholdValue",width:80},{title:"预警消息",dataIndex:"alertMessage",key:"alertMessage",width:200,ellipsis:!0},{title:"处理状态",dataIndex:"handleStatus",key:"handleStatus",width:100,slots:{customRender:"handleStatus"}},{title:"预警时间",dataIndex:"alertTime",key:"alertTime",width:150,slots:{customRender:"alertTime"}},{title:"操作",key:"action",width:150,fixed:"right",slots:{customRender:"action"}}],rowSelection:f,hasSelected:g,dayjs:i,handleSearch:()=>{p.current=1,y()},handleReset:()=>{e.value.resetFields(),p.current=1,y()},handleRefresh:()=>{y()},handleTableChange:e=>{p.current=e.current,p.pageSize=e.pageSize,y()},handleView:e=>{h.value=e,r.value=!0},handleProcess:e=>{h.value=e,d.value=!0},handleProcessSuccess:()=>{d.value=!1,y()},handleDelete:async e=>{try{await G.delete({id:e.id}),b.success("删除成功"),y()}catch(a){console.error("删除失败:",a),b.error("删除失败")}},handleBatchProcess:()=>{0!==t.value.length?o.value=!0:b.warning("请选择要处理的记录")},handleBatchProcessConfirm:async()=>{try{await G.batchHandle({idList:t.value,handleType:_.value,handleRemark:v.value}),b.success("批量处理成功"),o.value=!1,t.value=[],y()}catch(e){console.error("批量处理失败:",e),b.error("批量处理失败")}},handleExport:()=>{b.info("导出功能开发中...")},getAlertTypeColor:e=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"}[e]||"default"),getAlertTypeText:e=>({LOW_STOCK:"库存不足",ZERO_STOCK:"零库存",OVERSTOCK:"库存积压",EXPIRY:"临期预警"}[e]||e),getAlertLevelColor:e=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"}[e]||"default"),getAlertLevelText:e=>({CRITICAL:"紧急",WARNING:"警告",INFO:"提醒"}[e]||e),getHandleStatusColor:e=>({PENDING:"orange",RESOLVED:"green",IGNORED:"gray"}[e]||"default"),getHandleStatusText:e=>({PENDING:"待处理",RESOLVED:"已解决",IGNORED:"已忽略"}[e]||e)}}},[["render",function(e,a,l,t,r,d){const o=C,n=k,c=O,s=x,u=w,i=h("SearchOutlined"),b=I,G=h("ReloadOutlined"),M=E,K=P,z=D,H=h("CheckOutlined"),W=h("DownloadOutlined"),B=N,X=L,Z=A,J=V,$=Y,Q=F,q=j,ee=h("AlertRecordDetail"),ae=h("AlertRecordProcess");return _(),v("div",U,[m(z,{bordered:!1,class:"search-card"},{default:p((()=>[m(K,{ref:"searchFormRef",model:t.searchForm,layout:"inline",class:"search-form"},{default:p((()=>[m(n,{label:"商品名称",name:"productName"},{default:p((()=>[m(o,{value:t.searchForm.productName,"onUpdate:value":a[0]||(a[0]=e=>t.searchForm.productName=e),placeholder:"请输入商品名称","allow-clear":"",onPressEnter:t.handleSearch},null,8,["value","onPressEnter"])])),_:1}),m(n,{label:"预警类型",name:"alertType"},{default:p((()=>[m(s,{value:t.searchForm.alertType,"onUpdate:value":a[1]||(a[1]=e=>t.searchForm.alertType=e),placeholder:"请选择预警类型","allow-clear":"",style:{width:"150px"}},{default:p((()=>[m(c,{value:"LOW_STOCK"},{default:p((()=>a[10]||(a[10]=[f("库存不足")]))),_:1,__:[10]}),m(c,{value:"ZERO_STOCK"},{default:p((()=>a[11]||(a[11]=[f("零库存")]))),_:1,__:[11]}),m(c,{value:"OVERSTOCK"},{default:p((()=>a[12]||(a[12]=[f("库存积压")]))),_:1,__:[12]}),m(c,{value:"EXPIRY"},{default:p((()=>a[13]||(a[13]=[f("临期预警")]))),_:1,__:[13]})])),_:1},8,["value"])])),_:1}),m(n,{label:"预警级别",name:"alertLevel"},{default:p((()=>[m(s,{value:t.searchForm.alertLevel,"onUpdate:value":a[2]||(a[2]=e=>t.searchForm.alertLevel=e),placeholder:"请选择预警级别","allow-clear":"",style:{width:"120px"}},{default:p((()=>[m(c,{value:"CRITICAL"},{default:p((()=>a[14]||(a[14]=[f("紧急")]))),_:1,__:[14]}),m(c,{value:"WARNING"},{default:p((()=>a[15]||(a[15]=[f("警告")]))),_:1,__:[15]}),m(c,{value:"INFO"},{default:p((()=>a[16]||(a[16]=[f("提醒")]))),_:1,__:[16]})])),_:1},8,["value"])])),_:1}),m(n,{label:"处理状态",name:"handleStatus"},{default:p((()=>[m(s,{value:t.searchForm.handleStatus,"onUpdate:value":a[3]||(a[3]=e=>t.searchForm.handleStatus=e),placeholder:"请选择处理状态","allow-clear":"",style:{width:"120px"}},{default:p((()=>[m(c,{value:"PENDING"},{default:p((()=>a[17]||(a[17]=[f("待处理")]))),_:1,__:[17]}),m(c,{value:"RESOLVED"},{default:p((()=>a[18]||(a[18]=[f("已解决")]))),_:1,__:[18]}),m(c,{value:"IGNORED"},{default:p((()=>a[19]||(a[19]=[f("已忽略")]))),_:1,__:[19]})])),_:1},8,["value"])])),_:1}),m(n,{label:"预警时间",name:"alertTimeRange"},{default:p((()=>[m(u,{value:t.searchForm.alertTimeRange,"onUpdate:value":a[4]||(a[4]=e=>t.searchForm.alertTimeRange=e),format:"YYYY-MM-DD",placeholder:["开始日期","结束日期"]},null,8,["value"])])),_:1}),m(n,null,{default:p((()=>[m(M,null,{default:p((()=>[m(b,{type:"primary",onClick:t.handleSearch},{icon:p((()=>[m(i)])),default:p((()=>[a[20]||(a[20]=f(" 搜索 "))])),_:1,__:[20]},8,["onClick"]),m(b,{onClick:t.handleReset},{icon:p((()=>[m(G)])),default:p((()=>[a[21]||(a[21]=f(" 重置 "))])),_:1,__:[21]},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),m(z,{bordered:!1,class:"action-card"},{default:p((()=>[m(M,null,{default:p((()=>[m(b,{type:"primary",disabled:!t.hasSelected,onClick:t.handleBatchProcess},{icon:p((()=>[m(H)])),default:p((()=>[a[22]||(a[22]=f(" 批量处理 "))])),_:1,__:[22]},8,["disabled","onClick"]),m(b,{onClick:t.handleExport},{icon:p((()=>[m(W)])),default:p((()=>[a[23]||(a[23]=f(" 导出 "))])),_:1,__:[23]},8,["onClick"]),m(b,{onClick:t.handleRefresh},{icon:p((()=>[m(G)])),default:p((()=>[a[24]||(a[24]=f(" 刷新 "))])),_:1,__:[24]},8,["onClick"])])),_:1})])),_:1}),m(z,{bordered:!1,class:"table-card"},{default:p((()=>[m(Z,{columns:t.columns,"data-source":t.dataSource,loading:t.loading,pagination:t.pagination,"row-selection":t.rowSelection,scroll:{x:1200},onChange:t.handleTableChange},{alertType:p((({record:e})=>[m(B,{color:t.getAlertTypeColor(e.alertType)},{default:p((()=>[f(g(t.getAlertTypeText(e.alertType)),1)])),_:2},1032,["color"])])),alertLevel:p((({record:e})=>[m(B,{color:t.getAlertLevelColor(e.alertLevel)},{default:p((()=>[f(g(t.getAlertLevelText(e.alertLevel)),1)])),_:2},1032,["color"])])),currentStock:p((({record:e})=>[y("span",{style:R({color:e.currentStock<=e.thresholdValue?"#f5222d":"#52c41a"})},g(e.currentStock),5)])),handleStatus:p((({record:e})=>[m(B,{color:t.getHandleStatusColor(e.handleStatus)},{default:p((()=>[f(g(t.getHandleStatusText(e.handleStatus)),1)])),_:2},1032,["color"])])),alertTime:p((({record:e})=>[f(g(t.dayjs(e.alertTime).format("YYYY-MM-DD HH:mm:ss")),1)])),action:p((({record:e})=>[m(M,null,{default:p((()=>[m(b,{type:"link",size:"small",onClick:a=>t.handleView(e)},{default:p((()=>a[25]||(a[25]=[f(" 查看 ")]))),_:2,__:[25]},1032,["onClick"]),"PENDING"===e.handleStatus?(_(),S(b,{key:0,type:"link",size:"small",onClick:a=>t.handleProcess(e)},{default:p((()=>a[26]||(a[26]=[f(" 处理 ")]))),_:2,__:[26]},1032,["onClick"])):T("",!0),m(X,{title:"确定要删除这条预警记录吗？",onConfirm:a=>t.handleDelete(e)},{default:p((()=>[m(b,{type:"link",size:"small",danger:""},{default:p((()=>a[27]||(a[27]=[f(" 删除 ")]))),_:1,__:[27]})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1},8,["columns","data-source","loading","pagination","row-selection","onChange"])])),_:1}),m(q,{visible:t.batchProcessVisible,"onUpdate:visible":a[7]||(a[7]=e=>t.batchProcessVisible=e),title:"批量处理预警记录",onOk:t.handleBatchProcessConfirm},{default:p((()=>[m(K,{"label-col":{span:6},"wrapper-col":{span:16}},{default:p((()=>[m(n,{label:"处理操作"},{default:p((()=>[m($,{value:t.batchProcessType,"onUpdate:value":a[5]||(a[5]=e=>t.batchProcessType=e)},{default:p((()=>[m(J,{value:"RESOLVED"},{default:p((()=>a[28]||(a[28]=[f("标记为已解决")]))),_:1,__:[28]}),m(J,{value:"IGNORED"},{default:p((()=>a[29]||(a[29]=[f("标记为已忽略")]))),_:1,__:[29]})])),_:1},8,["value"])])),_:1}),m(n,{label:"处理备注"},{default:p((()=>[m(Q,{value:t.batchProcessRemark,"onUpdate:value":a[6]||(a[6]=e=>t.batchProcessRemark=e),placeholder:"请输入处理备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1},8,["visible","onOk"]),m(ee,{visible:t.detailVisible,"onUpdate:visible":a[8]||(a[8]=e=>t.detailVisible=e),"record-data":t.currentRecord},null,8,["visible","record-data"]),m(ae,{visible:t.processVisible,"onUpdate:visible":a[9]||(a[9]=e=>t.processVisible=e),"record-data":t.currentRecord,onSuccess:t.handleProcessSuccess},null,8,["visible","record-data","onSuccess"])])}],["__scopeId","data-v-ed5e37d3"]]))}}}));
