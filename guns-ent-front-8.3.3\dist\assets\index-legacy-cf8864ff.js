System.register(["./list-legacy-8a5bdd4a.js","./detail-legacy-b3571ce6.js","./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js","./index-legacy-c65a6a4e.js","./SysDictTypeApi-legacy-1047ef23.js"],(function(e,l){"use strict";var s,n,u,a,i,t,y;return{setters:[e=>{s=e.default},e=>{n=e.default},e=>{u=e.r,a=e.a,i=e.c,t=e.f,y=e.h},null,null,null,null,null,null,null,null,null,null],execute:function(){const l={class:"guns-layout"};e("default",{__name:"index",setup(e){const c=u("list"),d=u(""),g=e=>{c.value=e.type,d.value=e.businessLogId};return(e,u)=>(a(),i("div",l,["list"===c.value?(a(),t(s,{key:0,onUpdateType:g})):y("",!0),"detail"===c.value&&d.value?(a(),t(n,{key:1,"business-log-id":d.value,onUpdateType:g},null,8,["business-log-id"])):y("",!0)]))}})}}}));
