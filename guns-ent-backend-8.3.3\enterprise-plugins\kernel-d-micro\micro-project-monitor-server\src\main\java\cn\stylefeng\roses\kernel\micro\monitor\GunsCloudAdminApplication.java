package cn.stylefeng.roses.kernel.micro.monitor;

import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 监控中心启动类
 *
 * <AUTHOR>
 * @date 2021/5/12 19:42
 */
@SpringBootApplication
@EnableAdminServer
@EnableDiscoveryClient
public class GunsCloudAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(GunsCloudAdminApplication.class, args);
    }

}

