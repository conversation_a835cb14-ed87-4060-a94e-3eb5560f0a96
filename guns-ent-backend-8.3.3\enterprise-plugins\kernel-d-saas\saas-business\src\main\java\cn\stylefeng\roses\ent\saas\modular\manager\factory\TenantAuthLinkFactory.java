package cn.stylefeng.roses.ent.saas.modular.manager.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * 租户和功能包的绑定
 *
 * <AUTHOR>
 * @since 2024-01-21 17:54
 */
public class TenantAuthLinkFactory {

    /**
     * 初始化租户和功能包的绑定
     *
     * <AUTHOR>
     * @since 2024-01-21 17:54
     */
    public static List<TenantLink> createTenantPackageLink(Long tenantId, TenantRequest tenantRequest) {

        List<TenantLink> tenantPackageLinkList = new ArrayList<>();

        // 获取功能包的绑定信息
        List<TenantLinkRequest> tempList = tenantRequest.getTenantLinkList();
        if (ObjectUtil.isEmpty(tempList)) {
            return tenantPackageLinkList;
        }

        // 遍历绑定信息，创建绑定关系
        for (TenantLinkRequest tenantLinkRequest : tempList) {
            TenantLink tenantLink = new TenantLink();
            tenantLink.setTenantId(tenantId);
            tenantLink.setPackageId(tenantLinkRequest.getPackageId());
            tenantLink.setServiceEndTime(tenantLinkRequest.getServiceEndTime());
            tenantLink.setTrialFlag(tenantLinkRequest.getTrialFlag());
            tenantPackageLinkList.add(tenantLink);
        }

        return tenantPackageLinkList;
    }

}
