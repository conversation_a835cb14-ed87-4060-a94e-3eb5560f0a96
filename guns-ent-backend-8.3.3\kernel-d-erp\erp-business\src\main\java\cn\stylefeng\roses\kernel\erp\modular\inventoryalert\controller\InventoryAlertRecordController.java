package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRecordRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRecordResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.InventoryAlertRecordService;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 库存预警记录管理控制器
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@RestController
@ApiResource(name = "库存预警记录管理", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.ERP_INVENTORY_ALERT_MANAGE)
public class InventoryAlertRecordController {

    @Resource
    private InventoryAlertRecordService inventoryAlertRecordService;

    /**
     * 查询预警记录详情
     */
    @GetResource(name = "查询预警记录详情", path = "/erp/inventoryAlert/record/detail", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.VIEW_INVENTORY_ALERT_RECORD_DETAIL)
    public ResponseData<InventoryAlertRecordResponse> detail(@Validated(InventoryAlertRecordRequest.detail.class) 
                                                            InventoryAlertRecordRequest request) {
        InventoryAlertRecordResponse response = inventoryAlertRecordService.detail(request);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询预警记录
     */
    @GetResource(name = "分页查询预警记录", path = "/erp/inventoryAlert/record/page", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.PAGE_INVENTORY_ALERT_RECORD)
    public ResponseData<PageResult<InventoryAlertRecordResponse>> page(InventoryAlertRecordRequest request) {
        PageResult<InventoryAlertRecordResponse> result = inventoryAlertRecordService.findPage(request);
        return new SuccessResponseData<>(result);
    }

    /**
     * 处理预警记录
     */
    @PostResource(name = "处理预警记录", path = "/erp/inventoryAlert/record/handle", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.HANDLE_INVENTORY_ALERT_RECORD)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.HANDLE_INVENTORY_ALERT_RECORD)
    public ResponseData<?> handle(@RequestBody @Validated(InventoryAlertRecordRequest.handle.class) 
                                 InventoryAlertRecordRequest request) {
        inventoryAlertRecordService.handle(request);
        return new SuccessResponseData<>();
    }

    /**
     * 批量处理预警记录
     */
    @PostResource(name = "批量处理预警记录", path = "/erp/inventoryAlert/record/batchHandle", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.BATCH_HANDLE_INVENTORY_ALERT_RECORD)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.BATCH_HANDLE_INVENTORY_ALERT_RECORD)
    public ResponseData<?> batchHandle(@RequestBody @Validated(InventoryAlertRecordRequest.batchHandle.class) 
                                      InventoryAlertRecordRequest request) {
        inventoryAlertRecordService.batchHandle(request);
        return new SuccessResponseData<>();
    }

    /**
     * 忽略预警记录
     */
    @PostResource(name = "忽略预警记录", path = "/erp/inventoryAlert/record/ignore", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.IGNORE_INVENTORY_ALERT_RECORD)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.IGNORE_INVENTORY_ALERT_RECORD)
    public ResponseData<?> ignore(@RequestBody @Validated(InventoryAlertRecordRequest.handle.class) 
                                 InventoryAlertRecordRequest request) {
        inventoryAlertRecordService.ignore(request);
        return new SuccessResponseData<>();
    }

    /**
     * 获取预警统计数据
     */
    @GetResource(name = "获取预警统计数据", path = "/erp/inventoryAlert/record/statistics", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.VIEW_INVENTORY_ALERT_STATISTICS)
    public ResponseData<Map<String, Object>> getStatistics() {
        // 获取当前用户的租户ID
        Long tenantId = LoginContext.me().getLoginUser().getTenantId();
        Map<String, Object> statistics = inventoryAlertRecordService.getStatistics(tenantId);
        return new SuccessResponseData<>(statistics);
    }

    /**
     * 导出预警记录
     */
    @PostResource(name = "导出预警记录", path = "/erp/inventoryAlert/record/export", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.EXPORT_INVENTORY_ALERT_RECORD)
    @BizLog(logTypeCode = ErpPermissionCodeConstants.EXPORT_INVENTORY_ALERT_RECORD)
    public ResponseData<List<InventoryAlertRecordResponse>> exportRecords(@RequestBody InventoryAlertRecordRequest request) {
        List<InventoryAlertRecordResponse> records = inventoryAlertRecordService.exportRecords(request);
        return new SuccessResponseData<>(records);
    }

    /**
     * 查询最近的预警记录
     */
    @GetResource(name = "查询最近的预警记录", path = "/erp/inventoryAlert/record/recent")
    public ResponseData<List<InventoryAlertRecordResponse>> getRecentRecords() {
        // 获取当前用户的租户ID
        Long tenantId = LoginContext.me().getLoginUser().getTenantId();
        List<InventoryAlertRecordResponse> records = inventoryAlertRecordService.findRecentRecords(10, tenantId);
        return new SuccessResponseData<>(records);
    }

    /**
     * 根据商品ID查询预警记录
     */
    @GetResource(name = "根据商品ID查询预警记录", path = "/erp/inventoryAlert/record/byProduct")
    public ResponseData<List<InventoryAlertRecordResponse>> getRecordsByProduct(InventoryAlertRecordRequest request) {
        if (request.getProductIdFilter() == null) {
            return new SuccessResponseData<>(List.of());
        }
        
        // 设置分页参数查询该商品的预警记录
        request.setPageNo(1);
        request.setPageSize(50);
        PageResult<InventoryAlertRecordResponse> pageResult = inventoryAlertRecordService.findPage(request);
        return new SuccessResponseData<>(pageResult.getRows());
    }

    /**
     * 获取预警记录概览
     */
    @GetResource(name = "获取预警记录概览", path = "/erp/inventoryAlert/record/overview")
    public ResponseData<Map<String, Object>> getOverview() {
        // 获取当前用户的租户ID
        Long tenantId = LoginContext.me().getLoginUser().getTenantId();
        
        // 获取统计数据
        Map<String, Object> statistics = inventoryAlertRecordService.getStatistics(tenantId);
        
        // 获取最近的预警记录
        List<InventoryAlertRecordResponse> recentRecords = inventoryAlertRecordService.findRecentRecords(5, tenantId);
        
        // 组装概览数据
        Map<String, Object> overview = Map.of(
            "statistics", statistics,
            "recentRecords", recentRecords
        );
        
        return new SuccessResponseData<>(overview);
    }
}
