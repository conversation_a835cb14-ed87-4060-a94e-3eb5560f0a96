import{_ as z,r as v,a as d,c as g,b as c,d as h,w as r,g as x,F as L,e as N,t as P,f as U,h as A,aR as O,aS as q,m as G,C as H,i as J,a5 as M,S as Q}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */import{P as T}from"./package-add-edit-d6049334.js";import W from"./package-tree-4e68b971.js";/* empty css              */const X={class:"guns-layout"},Y={class:"guns-layout-sidebar p-t-12 bgColor",style:{width:"292px"}},Z={class:"sidebar-content"},$={class:"guns-layout-content",style:{width:"calc(100% - 292px)"}},ee={class:"guns-layout"},se={class:"guns-layout-content-application"},te={class:"content-mian"},ae={class:"content-mian-body"},ne={class:"table-content"},ce={key:0,class:"use-content"},oe={class:"content-header"},ie={class:"content-bottom"},le={class:"bottom-item-name"},de={class:"title"},ue={class:"table"},he=Object.assign({name:"TenantPackage"},{__name:"index",setup(re){const _=v(""),o=v(null),p=v(!1),y=v([]),B=v([{title:"\u9875\u9762",width:200,dataIndex:"page"},{title:"\u529F\u80FD",dataIndex:"use"}]),E=s=>{_.value=s,s?K():o.value=null},K=()=>{y.value=[],p.value=!0,T.getPackageAuth({packageId:_.value}).then(s=>{w(s.appPermissionList),o.value=s}).finally(()=>p.value=!1)},w=s=>{(s==null?void 0:s.length)>0&&s.forEach(e=>{var t;((t=e==null?void 0:e.children)==null?void 0:t.length)>0&&(y.value.push(e.nodeId),w(e.children))})},R=(s,e)=>{m(s,e,"appPermissionList",!0)},V=(s,e)=>{m(s,e,"",!0,!0)},D=(s,e,t)=>{m(s,e,"",!0,!0,t)},F=(s,e,t)=>{m(s,e,"",!1,!0,t)},m=(s,e,t,a,b,u)=>{e.checked=s.target.checked;const n=I([u]);a&&S(s.target.checked,t?e[t]:[e]),u&&(n.find(k=>k.checked==!1)?u.checked=!1:u.checked=!0),b&&(o.value.appPermissionList.find(k=>k.checked==!1)?o.value.checked=!1:o.value.checked=!0),j(e)},I=s=>{const e=[];function t(a){a!=null&&a.leafFlag&&(e.push(a),a!=null&&a.functionList&&Array.isArray(a.functionList)&&e.push(...a.functionList)),a!=null&&a.children&&Array.isArray(a.children)&&a.children.forEach(t)}return s.forEach(t),e},j=s=>{p.value=!0;let e={checked:s.checked,packageId:_.value,nodeId:s.nodeId?s.nodeId:"",permissionNodeType:s.permissionNodeType};T.setPackagePermission(e).then(t=>{G.success(t.message)}).finally(()=>p.value=!1)},S=(s,e)=>{e&&e.length>0&&e.forEach(t=>{t.checked=s,t.children&&t.children.length>0&&S(s,t.children)})};return(s,e)=>{const t=H,a=J,b=M,u=Q;return d(),g("div",X,[c("div",Y,[c("div",Z,[h(W,{onTreeSelect:E})])]),c("div",$,[c("div",ee,[c("div",se,[c("div",te,[c("div",ae,[c("div",ne,[h(u,{spinning:p.value,delay:100},{default:r(()=>[_.value&&o.value?(d(),g("div",ce,[c("div",oe,[h(t,{checked:o.value.checked,"onUpdate:checked":e[0]||(e[0]=n=>o.value.checked=n),onClick:e[1]||(e[1]=n=>R(n,o.value))},{default:r(()=>e[3]||(e[3]=[x("\u6240\u6709\u6743\u9650")])),_:1,__:[3]},8,["checked"])]),c("div",ie,[(d(!0),g(L,null,N(o.value.appPermissionList,(n,k)=>(d(),g("div",{class:"bottom-item",key:k},[c("div",le,[c("span",de,"\u5E94\u7528\uFF1A"+P(n.nodeName),1),h(t,{checked:n.checked,"onUpdate:checked":l=>n.checked=l,onClick:l=>V(l,n)},{default:r(()=>e[4]||(e[4]=[x("\u5168\u9009")])),_:2,__:[4]},1032,["checked","onUpdate:checked","onClick"])]),c("div",ue,[h(a,{dataSource:n.children,columns:B.value,expandedRowKeys:y.value,"onUpdate:expandedRowKeys":e[2]||(e[2]=l=>y.value=l),pagination:!1,checkStrictly:!0,rowKey:"nodeId",bordered:"",size:"small"},{bodyCell:r(({column:l,record:f})=>[l.dataIndex==="page"?(d(),U(t,{key:0,checked:f.checked,"onUpdate:checked":i=>f.checked=i,onChange:i=>D(i,f,n)},{default:r(()=>[x(P(f.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"])):l.dataIndex==="use"?(d(!0),g(L,{key:1},N(f.functionList,i=>(d(),U(t,{checked:i.checked,"onUpdate:checked":C=>i.checked=C,onChange:C=>F(C,i,n),key:i.nodeId},{default:r(()=>[x(P(i.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"]))),128)):A("",!0)]),_:2},1032,["dataSource","columns","expandedRowKeys"])])]))),128))])])):A("",!0),O(h(b,{class:"right-empty"},null,512),[[q,!_.value&&!o.value]])]),_:1},8,["spinning"])])])])])])])])}}}),me=z(he,[["__scopeId","data-v-be3357bc"]]);export{me as default};
