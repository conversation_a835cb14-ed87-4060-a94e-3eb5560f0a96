<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.supplier.mapper.ErpSupplierMapper">

    <!-- 供应商基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier">
        <id column="supplier_id" property="supplierId"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="supplier_type" property="supplierType"/>
        <result column="region_id" property="regionId"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_mobile" property="contactMobile"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="contact_address" property="contactAddress"/>
        <result column="business_license_no" property="businessLicenseNo"/>
        <result column="tax_no" property="taxNo"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_account" property="bankAccount"/>
        <result column="credit_level" property="creditLevel"/>
        <result column="status" property="status"/>
        <result column="business_mode" property="businessMode"/>
        <result column="sales_deduction" property="salesDeduction"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 供应商统计信息结果映射 -->
    <resultMap id="SupplierStatsResultMap" type="cn.stylefeng.roses.kernel.erp.api.pojo.response.SupplierStatsResponse">
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="business_mode" property="businessMode"/>
        <result column="product_count" property="productCount"/>
        <result column="total_purchase_amount" property="totalPurchaseAmount"/>
        <result column="avg_purchase_price" property="avgPurchasePrice"/>
        <result column="last_purchase_date" property="lastPurchaseDate"/>
    </resultMap>

    <!-- 根据经营方式查询供应商 -->
    <select id="findByBusinessMode" resultMap="BaseResultMap">
        SELECT *
        FROM erp_supplier
        WHERE business_mode = #{businessMode}
          AND del_flag = 'N'
          AND status = 'ACTIVE'
        ORDER BY supplier_name
    </select>

    <!-- 根据经营方式和销售扣点范围查询供应商 -->
    <select id="findByBusinessModeAndDeductionRange" resultMap="BaseResultMap">
        SELECT *
        FROM erp_supplier
        WHERE business_mode = #{businessMode}
          AND del_flag = 'N'
          AND status = 'ACTIVE'
        <if test="minDeduction != null">
            AND sales_deduction >= #{minDeduction}
        </if>
        <if test="maxDeduction != null">
            AND sales_deduction &lt;= #{maxDeduction}
        </if>
        ORDER BY sales_deduction, supplier_name
    </select>

    <!-- 查询有关联商品的供应商 -->
    <select id="findSuppliersWithProducts" resultMap="BaseResultMap">
        SELECT DISTINCT s.*
        FROM erp_supplier s
        INNER JOIN erp_product p ON s.supplier_id = p.supplier_id
        WHERE s.del_flag = 'N'
          AND p.del_flag = 'N'
        <if test="businessMode != null and businessMode != ''">
            AND s.business_mode = #{businessMode}
        </if>
        <if test="status != null and status != ''">
            AND s.status = #{status}
        </if>
        ORDER BY s.supplier_name
    </select>

    <!-- 查询没有关联商品的供应商 -->
    <select id="findSuppliersWithoutProducts" resultMap="BaseResultMap">
        SELECT s.*
        FROM erp_supplier s
        LEFT JOIN erp_product p ON s.supplier_id = p.supplier_id AND p.del_flag = 'N'
        WHERE s.del_flag = 'N'
          AND p.supplier_id IS NULL
        <if test="businessMode != null and businessMode != ''">
            AND s.business_mode = #{businessMode}
        </if>
        <if test="status != null and status != ''">
            AND s.status = #{status}
        </if>
        ORDER BY s.supplier_name
    </select>

    <!-- 统计供应商关联的商品数量 -->
    <select id="countProductsBySupplier" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM erp_product
        WHERE supplier_id = #{supplierId}
          AND del_flag = 'N'
    </select>

    <!-- 获取供应商统计信息 -->
    <select id="getSupplierStats" resultMap="SupplierStatsResultMap">
        SELECT 
            s.supplier_id,
            s.supplier_name,
            s.business_mode,
            COUNT(DISTINCT p.product_id) as product_count,
            COALESCE(SUM(pod.total_price), 0) as total_purchase_amount,
            COALESCE(AVG(pod.unit_price), 0) as avg_purchase_price,
            MAX(po.order_date) as last_purchase_date
        FROM erp_supplier s
        LEFT JOIN erp_product p ON s.supplier_id = p.supplier_id AND p.del_flag = 'N'
        LEFT JOIN erp_purchase_order_detail pod ON p.product_id = pod.product_id
        LEFT JOIN erp_purchase_order po ON pod.order_id = po.id AND po.status = 'COMPLETED'
        WHERE s.del_flag = 'N'
        <if test="supplierId != null">
            AND s.supplier_id = #{supplierId}
        </if>
        <if test="businessMode != null and businessMode != ''">
            AND s.business_mode = #{businessMode}
        </if>
        GROUP BY s.supplier_id, s.supplier_name, s.business_mode
        ORDER BY s.supplier_name
    </select>

    <!-- 根据区域查询供应商（支持多个区域） -->
    <select id="findByRegionIds" resultMap="BaseResultMap">
        SELECT *
        FROM erp_supplier
        WHERE region_id IN
        <foreach collection="regionIds" item="regionId" open="(" separator="," close=")">
            #{regionId}
        </foreach>
        AND del_flag = 'N'
        <if test="businessMode != null and businessMode != ''">
            AND business_mode = #{businessMode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY supplier_name
    </select>

    <!-- 模糊搜索供应商（支持编码、名称、联系人） -->
    <select id="searchSuppliers" resultMap="BaseResultMap">
        SELECT *
        FROM erp_supplier
        WHERE del_flag = 'N'
        <if test="keyword != null and keyword != ''">
            AND (
                supplier_code LIKE CONCAT('%', #{keyword}, '%')
                OR supplier_name LIKE CONCAT('%', #{keyword}, '%')
                OR supplier_short_name LIKE CONCAT('%', #{keyword}, '%')
                OR contact_person LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="businessMode != null and businessMode != ''">
            AND business_mode = #{businessMode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY 
            CASE WHEN supplier_code = #{keyword} THEN 1 ELSE 2 END,
            CASE WHEN supplier_name = #{keyword} THEN 1 ELSE 2 END,
            supplier_name
    </select>

    <!-- 批量更新供应商经营方式 -->
    <update id="batchUpdateBusinessMode">
        UPDATE erp_supplier 
        SET business_mode = #{businessMode},
            sales_deduction = #{salesDeduction},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE supplier_id IN
        <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
            #{supplierId}
        </foreach>
        AND del_flag = 'N'
    </update>

</mapper>