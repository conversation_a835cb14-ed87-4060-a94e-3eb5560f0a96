package cn.stylefeng.roses.kernel.micro.feign.example;

import cn.hutool.core.lang.Dict;
import cn.stylefeng.roses.kernel.micro.feign.example.modular.ExampleConsumer;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

import jakarta.annotation.Resource;

/**
 * feign示例服务消费者
 *
 * <AUTHOR>
 * @date 2021/5/12 19:42
 */
@SpringBootApplication(scanBasePackages = "cn.stylefeng", exclude = DataSourceAutoConfiguration.class)
@EnableFeignClients(
        basePackages = {"cn.stylefeng.roses.kernel.micro.feign.example.modular", "cn.stylefeng.roses.kernel.micro.core.auth.consumer"})
public class GunsFeignConsumerApplication {

    @Resource(type = ExampleConsumer.class)
    private ExampleConsumer systemAppConsumer;

    public static void main(String[] args) {
        SpringApplication.run(GunsFeignConsumerApplication.class, args);
    }

    @Bean
    public CommandLineRunner commandLineRunner() throws InterruptedException {
        return args -> {
            for (int i = 0; i < 1000; i++) {
                Thread.sleep(3000L);
                Dict dict = new Dict();
                dict.put("test", "test");
                System.err.println(systemAppConsumer.success(dict));
            }
        };
    }

}

