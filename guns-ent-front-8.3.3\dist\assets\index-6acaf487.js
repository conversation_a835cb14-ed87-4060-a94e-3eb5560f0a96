import{r as o,L as S,o as B,R as F,bd as D,X as k,a as h,f as C,w as g,c as T,F as I,e as V,g as O,t as N,at as J,W as R,J as j,d as _,a0 as A}from"./index-18a1ea24.js";/* empty css              */const M={__name:"index",props:{value:{type:[String,Array],default:void 0},record:{type:Object,default:{}},formData:{type:Object,default:{}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},readonly:{type:Boolean,default:!1},isDesgin:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},dataSources:{type:Number,default:1},backendUrl:{type:String,default:""},valueType:{type:Number,default:1},dataList:{type:Array,default:[]}},emits:["update:value"],setup(f,{emit:m}){const e=f,y=m,t=o(null),u=o([]),d=o(""),b=S(()=>{var a;return!((a=e.record)!=null&&a.itemMultipleChoiceFlag||e.multiple)}),n=S(()=>{var a,l;return(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.dataFromType?e.record.formItemSelect.dataFromType:e.dataSources?e.dataSources:1}),p=S(()=>{var a,l,r,c;return(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.valueType?(c=(r=e.record)==null?void 0:r.formItemSelect)==null?void 0:c.valueType:e.valueType?e.valueType:1}),v=S(()=>{var a,l,r,c;return(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.backendUrl?(c=(r=e.record)==null?void 0:r.formItemSelect)==null?void 0:c.backendUrl:e.backendUrl?e.backendUrl:""});B(()=>{var a,l;(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.linkFieldCode&&(d.value=e.record.formItemSelect.linkFieldCode),e.dataSources==2&&(u.value=e.dataList),s(),L()});const s=a=>{if(e.isDesgin||n.value!=1||!v.value)return;let l={searchText:a};d.value&&(l[d.value]=e.formData[d.value],l.valueType=p.value),F.get(v.value,l).then(r=>{e.backendUrl?u.value=r.data:e.record.dictList=r.data})},U=D(a=>{s(a)},500),x=(a,l)=>l.label.toLowerCase().indexOf(a.toLowerCase())>=0,w=()=>{var l;let a="";b.value?a=t.value:a=e.normal?t.value:((l=t.value)==null?void 0:l.length)===0?"":JSON.stringify(t.value),y("update:value",a)},L=()=>{b.value?t.value=e.value:t.value=e.value?e.normal?e.value:JSON.parse(e.value):[]};return k(()=>e.formData[d.value],a=>{a&&s()},{deep:!0}),k(()=>e.value,a=>{L()},{deep:!0}),k(()=>{var a;return(a=e.record)==null?void 0:a.dictList},a=>{a&&(u.value=a)},{deep:!0,immediate:!0}),(a,l)=>{const r=R,c=j;return h(),C(c,{"show-search":"",allowClear:"",value:t.value,"onUpdate:value":l[0]||(l[0]=i=>t.value=i),onChange:w,style:{width:"100%"},placeholder:f.placeholder,disabled:f.readonly||f.disabled,mode:b.value?"":"multiple","filter-option":n.value==1?!1:x,onSearch:J(U)},{default:g(()=>[(h(!0),T(I,null,V(u.value,i=>(h(),C(r,{value:p.value==1?i.id:i.code,key:i.id,label:i.name},{default:g(()=>[O(N(i.name),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["value","placeholder","disabled","mode","filter-option","onSearch"])}}},q={class:"guns-body guns-body-card"},X={__name:"index",setup(f){const m=o(null),e=o(null),y=o(!1),t=o(!1),u=o("\u8BF7\u9009\u62E9"),d=o([{id:"1",name:"\u5F20\u4E09"},{id:"2",name:"\u674E\u56DB"}]);return(b,n)=>{const p=M,v=A;return h(),T("div",q,[_(v,{title:"\u81EA\u5B9A\u4E49\u9009\u62E9-\u81EA\u5B9A\u4E49\u586B\u5145\u6570\u636E",bordered:!1},{default:g(()=>[_(p,{value:m.value,"onUpdate:value":n[0]||(n[0]=s=>m.value=s),disabled:y.value,readonly:t.value,dataList:d.value,dataSources:2,multiple:!1,placeholder:u.value,style:{width:"300px"}},null,8,["value","disabled","readonly","dataList","placeholder"])]),_:1}),_(v,{title:"\u81EA\u5B9A\u4E49\u9009\u62E9-\u52A8\u6001\u63A5\u53E3\uFF08\u53EA\u652F\u6301get\uFF09",bordered:!1},{default:g(()=>[_(p,{value:e.value,"onUpdate:value":n[1]||(n[1]=s=>e.value=s),disabled:y.value,readonly:t.value,dataSources:1,backendUrl:"/my/user/selectList",multiple:!1,placeholder:u.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{X as default};
