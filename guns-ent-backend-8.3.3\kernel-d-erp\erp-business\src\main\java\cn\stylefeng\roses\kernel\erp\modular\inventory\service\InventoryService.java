package cn.stylefeng.roses.kernel.erp.modular.inventory.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryOperationRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValueResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 库存管理Service接口
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
public interface InventoryService extends IService<Inventory> {

    /**
     * 分页查询库存列表
     */
    PageResult<InventoryResponse> findPage(InventoryQueryRequest inventoryQueryRequest);

    /**
     * 查询库存列表
     */
    List<InventoryResponse> findList(InventoryQueryRequest inventoryQueryRequest);

    /**
     * 查询商品库存详情
     */
    InventoryResponse detail(Long productId);

    /**
     * 查询预警库存列表
     */
    List<InventoryResponse> findWarningList(InventoryQueryRequest inventoryQueryRequest);

    /**
     * 查询缺货商品列表
     */
    List<InventoryResponse> findOutOfStockList(InventoryQueryRequest inventoryQueryRequest);

    /**
     * 查询库存价值统计
     */
    InventoryValueResponse getInventoryValue(InventoryQueryRequest inventoryQueryRequest);

    /**
     * 设置库存预警值
     */
    void setMinStock(InventoryRequest inventoryRequest);

    /**
     * 库存调整
     */
    void adjustInventory(InventoryOperationRequest inventoryOperationRequest);

    /**
     * 初始化商品库存
     */
    void initInventory(InventoryRequest inventoryRequest);

    /**
     * 批量初始化商品库存
     */
    void batchInitInventory(List<InventoryRequest> inventoryRequestList);

    /**
     * 库存操作（内部方法，供其他模块调用）
     */
    void operateInventory(InventoryOperationRequest inventoryOperationRequest);

    /**
     * 检查商品是否有库存记录
     */
    boolean hasInventoryRecord(Long productId);

    /**
     * 获取商品当前库存数量
     */
    java.math.BigDecimal getCurrentStock(Long productId);

    /**
     * 检查库存是否充足
     */
    boolean checkStockSufficient(Long productId, java.math.BigDecimal requiredQuantity);

}