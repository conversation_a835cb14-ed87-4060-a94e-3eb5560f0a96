package cn.stylefeng.roses.kernel.micro.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.micro.api.constants.MicroConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * 微服务模块的异常
 *
 * <AUTHOR>
 * @date 2021/5/10 10:19
 */
public class MicroException extends ServiceException {

    public MicroException(AbstractExceptionEnum exception) {
        super(MicroConstants.MICRO_MODULE_NAME, exception);
    }

    public MicroException(AbstractExceptionEnum exception, Object... params) {
        super(MicroConstants.MICRO_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

}
