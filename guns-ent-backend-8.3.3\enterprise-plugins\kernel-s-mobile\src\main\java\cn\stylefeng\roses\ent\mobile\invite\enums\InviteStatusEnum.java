package cn.stylefeng.roses.ent.mobile.invite.enums;

import lombok.Getter;

/**
 * 审核状态：10-待审核，20-通过，30-拒绝
 *
 * <AUTHOR>
 * @since 2024-04-08 19:32
 */
@Getter
public enum InviteStatusEnum {

    /**
     * 待审核
     */
    WAIT(10, "待审核"),

    /**
     * 通过
     */
    PASS(20, "通过"),

    /**
     * 拒绝
     */
    JUJUE(30, "拒绝");

    private final Integer code;

    private final String message;

    InviteStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
