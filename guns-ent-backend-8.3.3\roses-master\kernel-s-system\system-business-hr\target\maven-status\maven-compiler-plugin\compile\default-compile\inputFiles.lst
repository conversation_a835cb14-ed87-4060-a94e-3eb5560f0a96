D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\data\controller\BizDataProviderController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\data\controller\DemoTreeDataController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\data\factory\DemoDataCreateFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\data\pojo\UserRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\data\service\BizDataProviderService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\cache\orginfo\SysOrgInfoMemoryCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\cache\orginfo\SysOrgInfoRedisCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\cache\subflag\clear\OrgSubFlagClearListener.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\cache\subflag\SysOrgSubFlagMemoryCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\cache\subflag\SysOrgSubFlagRedisCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\constants\OrgConstants.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\controller\CommonOrgController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\controller\HomeOrgStatController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\controller\HrOrganizationController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\controller\HrOrgApproverController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\controller\OrganizationLevelController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\entity\HrOrganization.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\entity\HrOrgApprover.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\enums\HrOrgApproverExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\enums\OrganizationLevelExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\factory\OrganizationFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\factory\OrgApproverFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\factory\OrgConditionFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\mapper\HrOrganizationMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\mapper\HrOrgApproverMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\mapper\OrganizationLevelMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\request\CommonOrgTreeRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\request\HrOrganizationRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\request\HrOrgApproverRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\response\ApproverBindUserItem.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\response\CommonOrgTreeResponse.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\response\HomeCompanyInfo.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\pojo\response\OrganizationLevelVo.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\service\HrOrganizationService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\service\HrOrgApproverService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\service\impl\HrOrganizationServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\service\impl\HrOrgApproverServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\service\impl\OrganizationLevelServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\org\service\OrganizationLevelService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\cache\PositionMemoryCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\cache\PositionRedisCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\constants\PositionConstants.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\controller\CommonPositionController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\controller\HrPositionController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\entity\HrPosition.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\enums\HrPositionExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\mapper\HrPositionMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\pojo\request\HrPositionRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\service\HrPositionService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\position\service\impl\HrPositionServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\cache\username\clear\UserInfoClearListener.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\cache\username\UserInfoMemoryCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\cache\username\UserInfoRedisCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\cache\userrole\clear\UserRoleClearListener.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\cache\userrole\UserRoleMemoryCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\cache\userrole\UserRoleRedisCache.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\constants\UserConstants.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\controller\CommonUserController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\controller\NewRoleAssignController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\controller\PersonalInfoController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\controller\SysUserController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\controller\UserRoleAssignPageController.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUser.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUserCertificate.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUserDataScope.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUserGroup.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUserGroupDetail.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUserOrg.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\entity\SysUserRole.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserCertificateExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserDataScopeExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserGroupDetailExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserGroupExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserOrgExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\enums\SysUserRoleExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\factory\RoleAssignFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\factory\RoleAssignV2Factory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\factory\SysUserCreateFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\factory\UserDataScopeFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\factory\UserOrgFactory.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserCertificateMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserDataScopeMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserGroupDetailMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserGroupMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserOrgMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\mapper\SysUserRoleMapper.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserCertificateRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserDataScopeRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserGroupDetailRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserGroupRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserOrgRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\request\SysUserRoleRequest.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\response\PersonalInfo.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\response\SysUserCertificateResponse.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\pojo\response\SysUserCertificateVo.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysRoleAssignServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysRoleAssignV2ServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserCertificateServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserDataScopeServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserGroupDetailServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserGroupServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserOrgServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserRoleServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\impl\SysUserServiceImpl.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysRoleAssignService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysRoleAssignV2Service.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserCertificateService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserDataScopeService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserGroupDetailService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserGroupService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserOrgService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserRoleService.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-system\system-business-hr\src\main\java\cn\stylefeng\roses\kernel\sys\modular\user\service\SysUserService.java
