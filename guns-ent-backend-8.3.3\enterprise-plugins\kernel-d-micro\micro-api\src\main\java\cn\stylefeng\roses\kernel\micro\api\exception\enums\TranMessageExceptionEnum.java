package cn.stylefeng.roses.kernel.micro.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 消息服务异常集合
 *
 * <AUTHOR>
 * @date 2018年4月18日 22:51:31
 */
@Getter
public enum TranMessageExceptionEnum implements AbstractExceptionEnum {

    REQUEST_EMPTY("600", "请求为空"),
    QUEUE_CANT_EMPTY("600", "消息队列不能为空"),
    MESSAGE_ID_CANT_EMPTY("601", "消息id不能为空"),
    MESSAGE_BODY_CANT_EMPTY("602", "消息body不能为空"),
    CANT_FIND_MESSAGE("603", "查找不到消息"),
    MESSAGE_NUMBER_WRONG("604", "消息数量错误"),
    MESSAGE_QUEUE_ERROR("605", "消息队列服务器处理异常"),
    MESSAGE_TYPE_ERROR("606", "消息接收到的格式错误，非TEXT类型");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    TranMessageExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
