package cn.stylefeng.roses.kernel.erp.modular.supplier.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpSupplierConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpSupplierExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.BusinessModeChangeValidationResponse;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.SupplierBusinessModeRuleService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 供应商经营方式业务规则服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/27 21:55
 */
@Slf4j
@Service
public class SupplierBusinessModeRuleServiceImpl implements SupplierBusinessModeRuleService {

    /**
     * 销售扣点的最小值（0%）
     */
    private static final BigDecimal MIN_SALES_DEDUCTION = BigDecimal.ZERO;

    /**
     * 销售扣点的最大值（100%）
     */
    private static final BigDecimal MAX_SALES_DEDUCTION = new BigDecimal("100.00");

    @Override
    public boolean validateBusinessMode(String businessMode) {
        if (StrUtil.isBlank(businessMode)) {
            return false;
        }

        return ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE.equals(businessMode) ||
                ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE.equals(businessMode) ||
                ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(businessMode);
    }

    @Override
    public boolean validateSalesDeduction(String businessMode, BigDecimal salesDeduction) {
        // 购销模式不需要销售扣点
        if (ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE.equals(businessMode)) {
            return salesDeduction == null;
        }

        // 联营和代销模式需要销售扣点
        if (ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE.equals(businessMode) ||
                ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(businessMode)) {
            
            if (salesDeduction == null) {
                return false;
            }

            // 销售扣点必须在0-100之间
            return salesDeduction.compareTo(MIN_SALES_DEDUCTION) >= 0 &&
                    salesDeduction.compareTo(MAX_SALES_DEDUCTION) <= 0;
        }

        return false;
    }

    @Override
    public BusinessModeChangeValidationResponse validateBusinessModeChange(ErpSupplier currentSupplier, String newBusinessMode) {
        BusinessModeChangeValidationResponse response = new BusinessModeChangeValidationResponse();
        
        String currentBusinessMode = currentSupplier.getBusinessMode();
        
        // 如果经营方式没有变化，直接返回可以变更
        if (StrUtil.equals(currentBusinessMode, newBusinessMode)) {
            response.setCanChange(true);
            response.setAffectedProductCount(0);
            response.setAffectedProducts(CollUtil.newArrayList());
            response.setDescription("经营方式未发生变化");
            return response;
        }

        // 验证新的经营方式是否有效
        if (!this.validateBusinessMode(newBusinessMode)) {
            response.setCanChange(false);
            response.setErrorMessage("无效的经营方式：" + newBusinessMode);
            response.setDescription("经营方式只能为：购销、联营、代销");
            return response;
        }

        // TODO: 检查供应商下是否有关联商品
        // 这里需要查询商品表中supplier_id等于当前供应商ID的商品数量
        // 暂时设置为0，等商品模块扩展完成后再实现
        int affectedProductCount = 0;
        
        response.setCanChange(true);
        response.setAffectedProductCount(affectedProductCount);
        response.setAffectedProducts(CollUtil.newArrayList());
        
        // 根据经营方式变更类型给出不同的提示
        String changeDescription = this.getBusinessModeChangeDescription(currentBusinessMode, newBusinessMode);
        response.setDescription(changeDescription);
        
        if (affectedProductCount > 0) {
            response.setWarningMessage("变更经营方式将影响 " + affectedProductCount + " 个商品的库存管理策略");
        }
        
        return response;
    }

    @Override
    public boolean canCreatePurchaseOrder(ErpSupplier supplier) {
        if (supplier == null || StrUtil.isBlank(supplier.getBusinessMode())) {
            return false;
        }

        // 只有购销和代销模式可以创建采购入库单
        return ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE.equals(supplier.getBusinessMode()) ||
                ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(supplier.getBusinessMode());
    }

    @Override
    public boolean needInventoryManagement(ErpSupplier supplier) {
        if (supplier == null || StrUtil.isBlank(supplier.getBusinessMode())) {
            return false;
        }

        // 只有购销和代销模式需要库存管理
        return ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE.equals(supplier.getBusinessMode()) ||
                ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(supplier.getBusinessMode());
    }

    @Override
    public String getBusinessModeDescription(String businessMode) {
        if (StrUtil.isBlank(businessMode)) {
            return "未知经营方式";
        }

        switch (businessMode) {
            case ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE:
                return "购销模式：需要采购入库，管理库存，按采购价结算";
            case ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE:
                return "联营模式：不需要采购入库，不管理库存，商品归属供应商，按销售扣点结算";
            case ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT:
                return "代销模式：需要采购入库，管理库存，按实际销售扣点结算";
            default:
                return "未知经营方式";
        }
    }

    @Override
    public boolean needSalesDeduction(String businessMode) {
        if (StrUtil.isBlank(businessMode)) {
            return false;
        }

        // 联营和代销模式需要销售扣点
        return ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE.equals(businessMode) ||
                ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT.equals(businessMode);
    }

    @Override
    public void validateSupplierBusinessModeParams(ErpSupplier supplier) {
        if (supplier == null) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_NOT_EXIST);
        }

        String businessMode = supplier.getBusinessMode();
        BigDecimal salesDeduction = supplier.getSalesDeduction();

        // 验证经营方式
        if (!this.validateBusinessMode(businessMode)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_BUSINESS_MODE_ERROR);
        }

        // 验证销售扣点
        if (!this.validateSalesDeduction(businessMode, salesDeduction)) {
            if (this.needSalesDeduction(businessMode)) {
                throw new ServiceException(ErpSupplierConstants.SUPPLIER_MODULE_NAME, 
                        ErpSupplierExceptionEnum.SUPPLIER_BUSINESS_MODE_ERROR.getErrorCode(), 
                        "销售扣点设置错误，" + this.getBusinessModeDescription(businessMode) + "需要设置销售扣点（0-100%）");
            } else {
                throw new ServiceException(ErpSupplierConstants.SUPPLIER_MODULE_NAME, 
                        ErpSupplierExceptionEnum.SUPPLIER_BUSINESS_MODE_ERROR.getErrorCode(), 
                        "销售扣点设置错误，" + this.getBusinessModeDescription(businessMode) + "不需要设置销售扣点");
            }
        }

        log.info("供应商经营方式参数验证通过，供应商ID：{}，经营方式：{}，销售扣点：{}", 
                supplier.getSupplierId(), businessMode, salesDeduction);
    }

    /**
     * 获取经营方式变更的描述信息
     *
     * @param currentMode 当前经营方式
     * @param newMode 新经营方式
     * @return 变更描述
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    private String getBusinessModeChangeDescription(String currentMode, String newMode) {
        StringBuilder description = new StringBuilder();
        description.append("经营方式变更：");
        description.append(this.getBusinessModeName(currentMode));
        description.append(" → ");
        description.append(this.getBusinessModeName(newMode));
        
        // 添加变更影响说明
        description.append("\n\n变更影响：");
        
        // 库存管理变更
        boolean currentNeedInventory = this.needInventoryManagement(createTempSupplier(currentMode));
        boolean newNeedInventory = this.needInventoryManagement(createTempSupplier(newMode));
        
        if (currentNeedInventory != newNeedInventory) {
            if (newNeedInventory) {
                description.append("\n• 将启用库存管理功能");
            } else {
                description.append("\n• 将停用库存管理功能");
            }
        }
        
        // 采购入库变更
        boolean currentCanPurchase = this.canCreatePurchaseOrder(createTempSupplier(currentMode));
        boolean newCanPurchase = this.canCreatePurchaseOrder(createTempSupplier(newMode));
        
        if (currentCanPurchase != newCanPurchase) {
            if (newCanPurchase) {
                description.append("\n• 将允许创建采购入库单");
            } else {
                description.append("\n• 将不允许创建采购入库单");
            }
        }
        
        // 销售扣点变更
        boolean currentNeedDeduction = this.needSalesDeduction(currentMode);
        boolean newNeedDeduction = this.needSalesDeduction(newMode);
        
        if (currentNeedDeduction != newNeedDeduction) {
            if (newNeedDeduction) {
                description.append("\n• 需要设置销售扣点");
            } else {
                description.append("\n• 不再需要销售扣点");
            }
        }
        
        return description.toString();
    }

    /**
     * 获取经营方式名称
     *
     * @param businessMode 经营方式
     * @return 经营方式名称
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    private String getBusinessModeName(String businessMode) {
        switch (businessMode) {
            case ErpSupplierConstants.BUSINESS_MODE_PURCHASE_SALE:
                return "购销";
            case ErpSupplierConstants.BUSINESS_MODE_JOINT_VENTURE:
                return "联营";
            case ErpSupplierConstants.BUSINESS_MODE_CONSIGNMENT:
                return "代销";
            default:
                return "未知";
        }
    }

    /**
     * 创建临时供应商对象用于测试
     *
     * @param businessMode 经营方式
     * @return 临时供应商对象
     * <AUTHOR>
     * @since 2025/07/27 21:55
     */
    private ErpSupplier createTempSupplier(String businessMode) {
        ErpSupplier supplier = new ErpSupplier();
        supplier.setBusinessMode(businessMode);
        return supplier;
    }

}