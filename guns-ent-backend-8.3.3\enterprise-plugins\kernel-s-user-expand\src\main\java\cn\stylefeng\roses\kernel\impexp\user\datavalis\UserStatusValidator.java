package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.sys.api.enums.user.UserStatusEnum;

/**
 * 状态的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 23:39
 */
public class UserStatusValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(false, originValue, originValue, "状态不能为空值");
        }

        if (UserStatusEnum.ENABLE.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, UserStatusEnum.ENABLE.getCode());
        } else if (UserStatusEnum.DISABLE.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, UserStatusEnum.DISABLE.getCode());
        } else {
            return new ExcelLineParseResult(false, originValue, originValue, "用户状态不正确");
        }
    }

}
