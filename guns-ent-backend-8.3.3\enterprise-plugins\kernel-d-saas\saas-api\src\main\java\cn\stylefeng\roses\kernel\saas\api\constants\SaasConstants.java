package cn.stylefeng.roses.kernel.saas.api.constants;

/**
 * SaaS模块
 *
 * <AUTHOR>
 * @date 2021/2/18 13:56
 */
public interface SaasConstants {

    /**
     * SaaS模块名
     */
    String SAAS_SERVER_MODULE_NAME = "kernel-d-saas";

    /**
     * 异常枚举的步进值
     */
    String SAAS_SERVER_EXCEPTION_STEP_CODE = "31";

    /**
     * 设置默认职务
     */
    String TENANT_DEFAULT_POSITION_NAME = "员工";

    /**
     * 设置默认职务编码
     */
    String TENANT_DEFAULT_POSITION_CODE = "employee";

    /**
     * 租户管理的菜单编码
     */
    String TENANT_MENU_CODE = "TENANT";

    /**
     * 管理员名称
     */
    String TENANT_ADMIN_NAME = "管理员";

    /**
     * 租户超级管理员的角色编码
     */
    String TENANT_ADMIN_ROLE_CODE = "tenant_admin_role";

    /**
     * 租户基础角色的名称
     */
    String TENANT_BASE_ROLE_NAME = "基础角色";

}
