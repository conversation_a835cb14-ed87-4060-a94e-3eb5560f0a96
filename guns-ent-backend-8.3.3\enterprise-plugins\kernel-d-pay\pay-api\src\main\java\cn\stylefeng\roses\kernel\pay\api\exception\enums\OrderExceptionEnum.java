package cn.stylefeng.roses.kernel.pay.api.exception.enums;

import cn.stylefeng.roses.kernel.pay.api.constants.PayConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 订单异常相关枚举
 *
 * <AUTHOR>
 * @date 2021/06/23 22:28
 */
@Getter
public enum OrderExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    ORDER_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "01", "查询结果不存在"),

    /**
     * 存在待支付订单
     */
    WAIT_TO_PAY_ORDER(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "02", "存在待支付订单，请先完成支付"),

    /**
     * 下单失败，支付系统问题
     */
    ORDER_PAY_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "03", "下单失败，运营商网络错误！"),

    /**
     * 支付回掉失败
     */
    ORDER_NOTIFY_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "04", "支付回掉失败，信息校验异常，具体信息：{}"),

    /**
     * 该订单不是自己的订单，无法操作
     */
    ORDER_OPERATE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "05", "该订单不是自己的订单，无法操作"),

    /**
     * 订单删除失败
     */
    ORDER_DELETE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "06", "订单删除失败，非法订单状态"),

    /**
     * 取消订单失败，已完成的订单不能删除
     */
    FINISH_ORDER_CANT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "07", "取消订单失败，已完成的订单不能删除"),

    /**
     * 订单更新失败，根据回调状态，修改订单状态失败
     */
    ORDER_UPDATE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + PayConstants.PAY_EXCEPTION_STEP_CODE + "08", "订单更新失败，根据回调状态，修改订单状态失败");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    OrderExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
