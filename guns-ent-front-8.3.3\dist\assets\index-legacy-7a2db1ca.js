System.register(["./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-f13ba0d3.js","./CustomerApi-legacy-7faff1b0.js","./CustomerEdit-legacy-20c55318.js","./CustomerDetail-legacy-825e17eb.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./UniversalTree-legacy-6dcdf778.js","./regionApi-legacy-73888494.js","./index-legacy-0d30ef09.js","./index-legacy-94a6fc23.js","./index-legacy-e24582b9.js"],(function(e,a){"use strict";var t,l,o,s,n,d,r,i,u,c,p,h,b,g,m,v,f,x,w,y,C,_,T,k,S,I,L,N,E,R,j,O,D,<PERSON>,<PERSON>,<PERSON>,z,A,B,G,K,F,H,J,q;return{setters:[e=>{t=e._},e=>{l=e._},e=>{o=e._,s=e.P,n=e.K,d=e.r,r=e.L,i=e.N,u=e.s,c=e.k,p=e.a,h=e.c,b=e.d,g=e.w,m=e.b,v=e.g,f=e.t,x=e.h,w=e.O,y=e.Q,C=e.F,_=e.e,T=e.f,k=e.M,S=e.E,I=e.m,L=e.U,N=e.n,E=e.B,R=e.I,j=e.p,O=e.q,D=e.D,P=e.l,M=e.V,U=e.W,z=e.J,A=e.u,B=e.v,G=e.G,K=e.H},null,e=>{F=e._},e=>{H=e.C},e=>{J=e.default},e=>{q=e.default},null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".guns-layout .table-toolbar[data-v-b66e25ba]{margin-bottom:16px;padding:16px 0;border-bottom:1px solid #f0f0f0}.guns-layout .table-toolbar .toolbar-left[data-v-b66e25ba]{display:flex;align-items:center;gap:16px}.guns-layout .table-toolbar .toolbar-left .search-input .ant-input[data-v-b66e25ba]{border-radius:6px}.guns-layout .table-toolbar .toolbar-left a[data-v-b66e25ba]{color:#1890ff;cursor:pointer}.guns-layout .table-toolbar .toolbar-left a[data-v-b66e25ba]:hover{color:#40a9ff}.guns-layout .advanced-search[data-v-b66e25ba]{padding:16px;background-color:#fafafa;border-radius:6px;margin-bottom:16px}.guns-layout .advanced-search .ant-form-item[data-v-b66e25ba]{margin-bottom:0}.guns-layout[data-v-b66e25ba]{height:100%;width:100%;margin:0;padding:0}.guns-layout-content[data-v-b66e25ba],.guns-layout-content-application[data-v-b66e25ba]{width:100%;height:100%;display:flex;flex-direction:column;margin:0;padding:0;box-sizing:border-box}.sidebar-content[data-v-b66e25ba]{height:100%;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08)}.content-main[data-v-b66e25ba]{width:100%;height:100%;display:flex;flex-direction:column;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08);margin:0;padding:0;box-sizing:border-box}.content-main-header[data-v-b66e25ba]{padding:16px 24px;border-bottom:1px solid #f0f0f0;background:#fafafa;border-radius:6px 6px 0 0}.header-content[data-v-b66e25ba]{display:flex;justify-content:space-between;align-items:center}.current-region-info[data-v-b66e25ba]{font-size:14px;color:#666}.content-main-body[data-v-b66e25ba]{flex:1;padding:16px 24px;overflow:auto;width:100%;box-sizing:border-box}.table-content[data-v-b66e25ba]{width:100%;height:100%;display:flex;flex-direction:column;box-sizing:border-box}[data-v-b66e25ba] .guns-split-panel-body{width:100%!important;flex:1!important;overflow:hidden!important}[data-v-b66e25ba] .guns-split-panel{width:100%!important}\n",document.head.appendChild(a);const Q={class:"guns-layout"},V={class:"guns-layout-sidebar width-100 p-t-12"},W={class:"sidebar-content"},$={class:"guns-layout-content"},X={class:"guns-layout"},Y={class:"guns-layout-content-application"},Z={class:"content-main"},ee={class:"content-main-header"},ae={class:"header-content"},te={class:"header-content-left"},le={key:0,class:"current-region-info"},oe={class:"header-content-right"},se={class:"content-main-body"},ne={class:"table-content"},de={key:0,class:"super-search",style:{"margin-top":"8px"}};e("default",o({name:"CustomerIndex",components:{PlusOutlined:s,SmallDashOutlined:n,CustomerEdit:J,CustomerDetail:q,RegionTree:F},setup(){const e=d(!1),a=d(!1),t=d(!1),l=d({}),o=d(null),s=d([]),n=d(null),c=r((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),p=r((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),h=r((()=>i()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),g=u({searchText:"",customerType:void 0,customerLevel:void 0,status:void 0,contactPerson:void 0,contactPhone:void 0}),m=H.getCustomerTypeOptions(),v=H.getCustomerLevelOptions(),f=H.getCustomerStatusOptions(),x=()=>{n.value&&n.value.reload()},w=()=>{var e;const a=null===(e=n.value)||void 0===e?void 0:e.getSelectedRows();a&&0!==a.length?k.confirm({title:"提示",content:`确定要删除选中的 ${a.length} 条数据吗？`,icon:b(S),maskClosable:!0,onOk:()=>{const e=a.map((e=>e.customerId));return H.batchDelete({customerIdList:e}).then((()=>{I.success("删除成功"),x()})).catch((e=>{I.error(e.message||"删除失败")}))}}):I.warning("请选择要删除的数据")};return{tableRef:n,regionTreeRef:o,superSearch:e,where:g,columns:[{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"customerCode",title:"客户编码",width:140,ellipsis:!0,isShow:!0},{dataIndex:"customerName",title:"客户名称",width:200,ellipsis:!0,isShow:!0},{dataIndex:"customerShortName",title:"客户简称",width:150,ellipsis:!0,isShow:!0},{dataIndex:"customerType",title:"客户类型",width:120,align:"center",isShow:!0},{dataIndex:"customerLevel",title:"客户等级",width:120,align:"center",isShow:!0},{dataIndex:"contactPerson",title:"联系人",width:100,ellipsis:!0,isShow:!0},{dataIndex:"contactPhone",title:"联系电话",width:120,ellipsis:!0,isShow:!0},{dataIndex:"contactMobile",title:"手机号码",width:120,ellipsis:!0,isShow:!0},{dataIndex:"status",title:"状态",width:100,align:"center",isShow:!0},{dataIndex:"createTime",title:"创建时间",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}],currentRecord:l,selectedRegionNodes:s,showEdit:a,showDetailModal:t,customerTypeOptions:m,customerLevelOptions:v,statusOptions:f,labelCol:c,wrapperCol:p,spanCol:h,reload:x,clear:()=>{Object.keys(g).forEach((e=>{g[e]="searchText"===e?"":void 0})),x()},changeSuperSearch:()=>{e.value=!e.value},handleRegionTreeSelect:(e,a)=>{console.log("区域树选择:",e,a),a&&a.selectedNodes&&a.selectedNodes.length>0?(s.value=a.selectedNodes,g.regionId=e[0]):(s.value=[],g.regionId=void 0),x()},handleRegionTreeLoaded:e=>{console.log("区域树数据加载完成:",e)},openAddModal:()=>{l.value={},a.value=!0},openEditModal:e=>{l.value={...e},a.value=!0},showDetail:e=>{l.value={...e},t.value=!0},remove:e=>{k.confirm({title:"提示",content:"确定要删除该客户吗？",icon:b(S),maskClosable:!0,onOk:()=>H.delete({customerId:e.customerId}).then((()=>{I.success("删除成功"),x()})).catch((e=>{I.error(e.message||"删除失败")}))})},moreClick:({key:e})=>{"1"===e&&w()},batchDelete:w,getCustomerTypeName:e=>H.getCustomerTypeName(e),getCustomerLevelName:e=>H.getCustomerLevelName(e),getCustomerStatusName:e=>H.getCustomerStatusName(e),getStatusTagColor:e=>H.getStatusTagColor(e),getCustomerLevelTagColor:e=>H.getCustomerLevelTagColor(e)}}},[["render",function(e,a,o,s,n,d){const r=F,i=L,u=N,k=c("plus-outlined"),S=E,I=R,H=j,J=O,q=c("small-dash-outlined"),re=D,ie=P,ue=M,ce=U,pe=z,he=A,be=B,ge=G,me=K,ve=l,fe=t,xe=c("customer-edit"),we=c("customer-detail");return p(),h("div",Q,[b(fe,{width:"292px",cacheKey:"ERP_CUSTOMER_MANAGEMENT"},{content:g((()=>[m("div",$,[m("div",X,[m("div",Y,[m("div",Z,[m("div",ee,[m("div",ae,[m("div",te,[b(u,{size:16},{default:g((()=>[s.selectedRegionNodes.length>0?(p(),h("span",le,[a[9]||(a[9]=v(" 当前区域：")),b(i,{color:"blue"},{default:g((()=>[v(f(s.selectedRegionNodes[0].regionName),1)])),_:1})])):x("",!0)])),_:1})]),m("div",oe,[b(u,{size:16},{default:g((()=>[b(S,{type:"primary",class:"border-radius",onClick:s.openAddModal},{default:g((()=>[b(k),a[10]||(a[10]=v(" 新增客户 "))])),_:1,__:[10]},8,["onClick"]),b(re,null,{overlay:g((()=>[b(J,{onClick:s.moreClick},{default:g((()=>[b(H,{key:"1"},{default:g((()=>[b(I,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[11]||(a[11]=m("span",null,"批量删除",-1))])),_:1,__:[11]})])),_:1},8,["onClick"])])),default:g((()=>[b(S,{class:"border-radius"},{default:g((()=>[a[12]||(a[12]=v(" 更多 ")),b(q)])),_:1,__:[12]})])),_:1})])),_:1})])])]),m("div",se,[m("div",ne,[b(ve,{columns:s.columns,where:s.where,fieldBusinessCode:"ERP_CUSTOMER_TABLE",showTableTool:"",showToolTotal:!1,rowId:"customerId",ref:"tableRef",url:"/erp/customer/page"},{toolLeft:g((()=>[b(ie,{value:s.where.searchText,"onUpdate:value":a[0]||(a[0]=e=>s.where.searchText=e),bordered:!1,allowClear:"",placeholder:"客户名称、编码（回车搜索）",onPressEnter:s.reload,style:{width:"240px"},class:"search-input"},{prefix:g((()=>[b(I,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),b(ue,{type:"vertical",class:"divider"}),m("a",{onClick:a[1]||(a[1]=(...e)=>s.changeSuperSearch&&s.changeSuperSearch(...e))},f(s.superSearch?"收起":"高级筛选"),1)])),toolBottom:g((()=>[s.superSearch?(p(),h("div",de,[b(me,{model:s.where,labelCol:s.labelCol,"wrapper-col":s.wrapperCol},{default:g((()=>[b(ge,{gutter:16},{default:g((()=>[b(be,w(y(s.spanCol)),{default:g((()=>[b(he,{label:"客户类型:"},{default:g((()=>[b(pe,{value:s.where.customerType,"onUpdate:value":a[2]||(a[2]=e=>s.where.customerType=e),placeholder:"请选择客户类型",style:{width:"100%"},allowClear:""},{default:g((()=>[(p(!0),h(C,null,_(s.customerTypeOptions,(e=>(p(),T(ce,{key:e.value,value:e.value},{default:g((()=>[v(f(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),b(be,w(y(s.spanCol)),{default:g((()=>[b(he,{label:"客户等级:"},{default:g((()=>[b(pe,{value:s.where.customerLevel,"onUpdate:value":a[3]||(a[3]=e=>s.where.customerLevel=e),placeholder:"请选择客户等级",style:{width:"100%"},allowClear:""},{default:g((()=>[(p(!0),h(C,null,_(s.customerLevelOptions,(e=>(p(),T(ce,{key:e.value,value:e.value},{default:g((()=>[v(f(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),b(be,w(y(s.spanCol)),{default:g((()=>[b(he,{label:"状态:"},{default:g((()=>[b(pe,{value:s.where.status,"onUpdate:value":a[4]||(a[4]=e=>s.where.status=e),placeholder:"请选择状态",style:{width:"100%"},allowClear:""},{default:g((()=>[(p(!0),h(C,null,_(s.statusOptions,(e=>(p(),T(ce,{key:e.value,value:e.value},{default:g((()=>[v(f(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16)])),_:1}),b(ge,{gutter:16},{default:g((()=>[b(be,w(y(s.spanCol)),{default:g((()=>[b(he,{label:"联系人:"},{default:g((()=>[b(ie,{value:s.where.contactPerson,"onUpdate:value":a[5]||(a[5]=e=>s.where.contactPerson=e),placeholder:"请输入联系人",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),b(be,w(y(s.spanCol)),{default:g((()=>[b(he,{label:"联系电话:"},{default:g((()=>[b(ie,{value:s.where.contactPhone,"onUpdate:value":a[6]||(a[6]=e=>s.where.contactPhone=e),placeholder:"请输入联系电话",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),b(be,w(y(s.spanCol)),{default:g((()=>[b(he,{label:" ",class:"not-label"},{default:g((()=>[b(u,{size:16},{default:g((()=>[b(S,{class:"border-radius",onClick:s.reload,type:"primary"},{default:g((()=>a[13]||(a[13]=[v("查询")]))),_:1,__:[13]},8,["onClick"]),b(S,{class:"border-radius",onClick:s.clear},{default:g((()=>a[14]||(a[14]=[v("重置")]))),_:1,__:[14]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):x("",!0)])),bodyCell:g((({column:e,record:a})=>["customerType"===e.dataIndex?(p(),T(i,{key:0},{default:g((()=>[v(f(s.getCustomerTypeName(a.customerType)),1)])),_:2},1024)):"customerLevel"===e.dataIndex?(p(),T(i,{key:1,color:s.getCustomerLevelTagColor(a.customerLevel)},{default:g((()=>[v(f(s.getCustomerLevelName(a.customerLevel)),1)])),_:2},1032,["color"])):"status"===e.dataIndex?(p(),T(i,{key:2,color:s.getStatusTagColor(a.status)},{default:g((()=>[v(f(s.getCustomerStatusName(a.status)),1)])),_:2},1032,["color"])):"action"===e.key?(p(),T(u,{key:3,size:16},{default:g((()=>[b(I,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>s.openEditModal(a)},null,8,["onClick"]),b(I,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>s.remove(a)},null,8,["onClick"])])),_:2},1024)):x("",!0)])),_:1},8,["columns","where"])])])])])])])])),default:g((()=>[m("div",V,[m("div",W,[b(r,{ref:"regionTreeRef","show-badge":!0,onTreeSelect:s.handleRegionTreeSelect,onTreeDataLoaded:s.handleRegionTreeLoaded},null,8,["onTreeSelect","onTreeDataLoaded"])])])])),_:1}),b(xe,{visible:s.showEdit,"onUpdate:visible":a[7]||(a[7]=e=>s.showEdit=e),data:s.currentRecord,onDone:s.reload},null,8,["visible","data","onDone"]),b(we,{visible:s.showDetailModal,"onUpdate:visible":a[8]||(a[8]=e=>s.showDetailModal=e),data:s.currentRecord},null,8,["visible","data"])])}],["__scopeId","data-v-b66e25ba"]]))}}}));
