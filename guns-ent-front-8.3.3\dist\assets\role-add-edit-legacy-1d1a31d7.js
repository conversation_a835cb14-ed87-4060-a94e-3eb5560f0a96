System.register(["./index-legacy-ee1db0c7.js","./role-form-legacy-d39b6ac5.js"],(function(e,t){"use strict";var a,l,o,r,n,s,i,d,u,c,p,v,y;return{setters:[e=>{a=e.R,l=e.b3,o=e.r,r=e.L,n=e.o,s=e.cb,i=e.a,d=e.f,u=e.w,c=e.d,p=e.m,v=e.M},e=>{y=e.default}],execute:function(){class t{static findPage(e){return a.getAndLoadData("/sysRole/page",e)}static add(e){return a.post("/sysRole/add",e)}static edit(e){return a.post("/sysRole/edit",e)}static delete(e){return a.post("/sysRole/delete",e)}static batchDelete(e){return a.post("/sysRole/batchDelete",e)}static detail(e){return a.getAndLoadData("/sysRole/detail",e)}}e("R",t);const m=e("_",{__name:"role-add-edit",props:{visible:Boolean,data:Object,roleType:[String,Number],roleCategoryId:String,companyData:Object},emits:["update:visible","done"],setup(e,{emit:a}){const m=l(),f=e,g=a,b=o(!1),R=o(!1),_=o({roleType:void 0,statusFlag:1,roleCategoryId:f.roleCategoryId}),I=o(null),S=r((()=>m.info.superAdminFlag)),h=r((()=>{let e=m.info.userOrgInfoList.filter((e=>e.currentSelectFlag));if(e.length>0)return e[0]}));n((async()=>{if(f.data)R.value=!0,O();else{_.value.roleSort=await s("SYSTEM_BASE_ROLE"),R.value=!1,_.value.roleType=f.roleType||20;let e=f.companyData?f.companyData:h.value;null!=e&&e.companyId&&20==_.value.roleType&&(_.value.roleCompanyId=null==e?void 0:e.companyId,_.value.roleCompanyIdWrapper=null==e?void 0:e.companyName)}}));const O=()=>{t.detail({roleId:f.data.roleId}).then((e=>{_.value=Object.assign({},e)}))},j=e=>{g("update:visible",e)},C=async()=>{I.value.$refs.formRef.validate().then((async e=>{if(e){b.value=!0;let e=null;e=R.value?t.edit(_.value):t.add(_.value),e.then((async e=>{b.value=!1,p.success(e.message),j(!1),g("done")})).catch((()=>{b.value=!1}))}}))};return(e,t)=>{const a=v;return i(),d(a,{width:700,maskClosable:!1,visible:f.visible,"confirm-loading":b.value,forceRender:!0,title:R.value?"编辑角色":"新建角色","body-style":{paddingBottom:"8px"},"onUpdate:visible":j,onOk:C,onClose:t[1]||(t[1]=e=>j(!1))},{default:u((()=>[c(y,{form:_.value,"onUpdate:form":t[0]||(t[0]=e=>_.value=e),ref_key:"roleFormRef",ref:I,superAdminFlag:S.value},null,8,["form","superAdminFlag"])])),_:1},8,["visible","confirm-loading","title"])}}}),f=Object.freeze(Object.defineProperty({__proto__:null,default:m},Symbol.toStringTag,{value:"Module"}));e("r",f)}}}));
