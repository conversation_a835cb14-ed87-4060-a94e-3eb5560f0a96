/**
 * 通用树结构组件入口文件
 * 
 * <AUTHOR>
 * @since 2025/01/24
 */

import UniversalTree from './UniversalTree.vue'

// 导出组件
export default UniversalTree

// 导出类型定义
export * from './types'

// 导出预设配置
export * from './configs'

// 导出工具函数
export * from './utils'

// 导出配置工厂
export * from './configFactory'

// 为了支持全局注册，提供install方法
export const install = (app) => {
  app.component('UniversalTree', UniversalTree)
}

// 组件信息
export const componentInfo = {
  name: 'UniversalTree',
  version: '1.0.0',
  description: '通用树结构组件，支持多种配置和交互模式'
}