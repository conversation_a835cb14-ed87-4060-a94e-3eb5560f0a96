import{_ as S,r as M,s as V,X as H,L as R,a as c,f as w,w as n,d as t,g as s,b as A,t as r,c as y,h as d,F as N,a2 as O,m as L,B as W,a3 as q,Y as G,Z as Y,a0 as j,W as J,J as K,u as X,v as Z,l as $,y as ee,G as te,H as ae,i as ne,a4 as ie,M as le}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{P as oe}from"./PurchaseApi-38cc3d1a.js";const se={name:"ReceiveInboundModal",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(P,{emit:l}){const m=M(null),i=M(null),h=M(!1),o=V({id:null,paymentMethod:"",paymentAccount:"",actualTotalAmount:null,receiveRemark:"",detailList:[]}),Q={},T={paymentMethod:[{required:!0,message:"\u8BF7\u9009\u62E9\u4ED8\u6B3E\u65B9\u5F0F",trigger:"change"}]},g=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:200,fixed:"left"},{title:"\u8BA2\u5355\u6570\u91CF",key:"orderQuantity",width:100,align:"center"},{title:"\u5165\u5E93\u6570\u91CF",key:"receiveQuantity",width:120,align:"center"},{title:"\u5DEE\u5F02\u6570\u91CF",key:"diffQuantity",width:100,align:"center"},{title:"\u5355\u4EF7",key:"unitPrice",width:100,align:"right"},{title:"\u5165\u5E93\u91D1\u989D",key:"receiveAmount",width:120,align:"right"},{title:"\u5907\u6CE8",key:"remark",width:150}];H(()=>P.data,e=>{e&&e.id&&(o.id=e.id,o.paymentMethod="",o.paymentAccount="",o.actualTotalAmount=e.totalAmount||null,o.receiveRemark="",o.detailList=(e.detailList||[]).map(a=>({...a,orderQuantity:a.quantity,receiveQuantity:a.quantity,diffQuantity:0,receiveAmount:a.totalPrice,remark:""})))},{immediate:!0});const I=R(()=>o.detailList?o.detailList.length:0),p=R(()=>!o.detailList||o.detailList.length===0?0:o.detailList.reduce((e,a)=>e+(parseFloat(a.orderQuantity)||0),0)),_=R(()=>!o.detailList||o.detailList.length===0?0:o.detailList.reduce((e,a)=>e+(parseFloat(a.receiveQuantity)||0),0)),D=R(()=>!o.detailList||o.detailList.length===0?0:o.detailList.reduce((e,a)=>e+(parseFloat(a.receiveAmount)||0),0));return{formRef:m,receiveFormRef:i,loading:h,receiveData:o,rules:Q,receiveRules:T,columns:g,productCount:I,orderTotalQuantity:p,receiveTotalQuantity:_,receiveTotalAmount:D,getPrecision:e=>{switch(e){case"WEIGHT":return 3;case"NORMAL":case"PIECE":case"VARIABLE":default:return 0}},getStep:e=>{switch(e){case"WEIGHT":return .001;case"NORMAL":case"PIECE":case"VARIABLE":default:return 1}},getQuantityUnit:e=>{switch(e.pricingType){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return e.unit||"\u4E2A"}},formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",formatDiffQuantity:e=>{const a=parseFloat(e)||0;return a>=0?"+".concat(a):"".concat(a)},getDiffQuantityClass:e=>{const a=parseFloat(e)||0;return a>0?"diff-positive":a<0?"diff-negative":"diff-zero"},onReceiveQuantityChange:(e,a)=>{const C=parseFloat(e.orderQuantity)||0,f=parseFloat(e.receiveQuantity)||0,z=parseFloat(e.unitPrice)||0;e.diffQuantity=f-C,e.receiveAmount=f*z},handleCancel:()=>{l("update:visible",!1)},handleReceive:()=>{i.value.validate().then(()=>m.value.validate()).then(()=>{if(!o.id){L.error("\u5165\u5E93\u5355\u4FE1\u606F\u4E0D\u5B8C\u6574");return}h.value=!0,oe.receive({id:o.id,paymentMethod:o.paymentMethod,paymentAccount:o.paymentAccount,actualTotalAmount:o.actualTotalAmount,receiveRemark:o.receiveRemark}).then(()=>{L.success("\u5165\u5E93\u6210\u529F"),l("ok")}).catch(e=>{L.error("\u5165\u5E93\u5931\u8D25\uFF1A"+(e.message||"\u672A\u77E5\u9519\u8BEF"))}).finally(()=>{h.value=!1})}).catch(()=>{L.error("\u8BF7\u5B8C\u5584\u5165\u5E93\u4FE1\u606F")})}}}},re={class:"receive-inbound-content"},ue={class:"order-no"},ce={key:0,class:"product-info"},de={class:"product-name"},me={class:"product-details"},_e={class:"product-code"},fe={key:0,class:"product-spec"},pe={key:5,class:"receive-amount"};function ve(P,l,m,i,h,o){const Q=W,T=q,g=G,I=Y,p=j,_=J,D=K,v=X,u=Z,b=$,x=ee,E=te,F=ae,U=ne,k=ie,B=le;return c(),w(B,{visible:m.visible,title:"\u6267\u884C\u5165\u5E93\u64CD\u4F5C",width:1e3,maskClosable:!1,onCancel:i.handleCancel},{footer:n(()=>[t(Q,{onClick:i.handleCancel},{default:n(()=>l[4]||(l[4]=[s("\u53D6\u6D88")])),_:1,__:[4]},8,["onClick"]),t(Q,{type:"primary",loading:i.loading,onClick:i.handleReceive},{default:n(()=>l[5]||(l[5]=[s(" \u786E\u8BA4\u5165\u5E93 ")])),_:1,__:[5]},8,["loading","onClick"])]),default:n(()=>[A("div",re,[t(T,{message:"\u5165\u5E93\u64CD\u4F5C\u63D0\u9192",description:"\u6267\u884C\u5165\u5E93\u64CD\u4F5C\u540E\uFF0C\u5546\u54C1\u5E93\u5B58\u5C06\u4F1A\u589E\u52A0\uFF0C\u5165\u5E93\u5355\u72B6\u6001\u53D8\u4E3A\u5DF2\u5B8C\u6210\uFF0C\u8BF7\u4ED4\u7EC6\u6838\u5BF9\u5165\u5E93\u6570\u91CF\u3002",type:"info","show-icon":"",style:{"margin-bottom":"16px"}}),t(p,{title:"\u5165\u5E93\u5355\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[t(I,{column:3,bordered:"",size:"small"},{default:n(()=>[t(g,{label:"\u5165\u5E93\u5355\u53F7"},{default:n(()=>[A("span",ue,r(m.data.orderNo),1)]),_:1}),t(g,{label:"\u4F9B\u5E94\u5546"},{default:n(()=>[s(r(m.data.supplierName),1)]),_:1}),t(g,{label:"\u8BA2\u5355\u65E5\u671F"},{default:n(()=>[s(r(m.data.orderDate),1)]),_:1})]),_:1})]),_:1}),t(p,{title:"\u5165\u5E93\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[t(F,{ref:"receiveFormRef",model:i.receiveData,rules:i.receiveRules,"label-col":{span:6},"wrapper-col":{span:18}},{default:n(()=>[t(E,{gutter:16},{default:n(()=>[t(u,{span:12},{default:n(()=>[t(v,{label:"\u4ED8\u6B3E\u65B9\u5F0F",name:"paymentMethod"},{default:n(()=>[t(D,{value:i.receiveData.paymentMethod,"onUpdate:value":l[0]||(l[0]=e=>i.receiveData.paymentMethod=e),placeholder:"\u8BF7\u9009\u62E9\u4ED8\u6B3E\u65B9\u5F0F"},{default:n(()=>[t(_,{value:"CASH"},{default:n(()=>l[6]||(l[6]=[s("\u73B0\u91D1")])),_:1,__:[6]}),t(_,{value:"BANK_TRANSFER"},{default:n(()=>l[7]||(l[7]=[s("\u94F6\u884C\u8F6C\u8D26")])),_:1,__:[7]}),t(_,{value:"ALIPAY"},{default:n(()=>l[8]||(l[8]=[s("\u652F\u4ED8\u5B9D")])),_:1,__:[8]}),t(_,{value:"WECHAT"},{default:n(()=>l[9]||(l[9]=[s("\u5FAE\u4FE1\u652F\u4ED8")])),_:1,__:[9]}),t(_,{value:"CREDIT"},{default:n(()=>l[10]||(l[10]=[s("\u8D4A\u8D26")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:n(()=>[t(v,{label:"\u4ED8\u6B3E\u8D26\u6237",name:"paymentAccount"},{default:n(()=>[t(b,{value:i.receiveData.paymentAccount,"onUpdate:value":l[1]||(l[1]=e=>i.receiveData.paymentAccount=e),placeholder:"\u8BF7\u8F93\u5165\u4ED8\u6B3E\u8D26\u6237"},null,8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:n(()=>[t(v,{label:"\u5B9E\u9645\u603B\u91D1\u989D",name:"actualTotalAmount"},{default:n(()=>[t(x,{value:i.receiveData.actualTotalAmount,"onUpdate:value":l[2]||(l[2]=e=>i.receiveData.actualTotalAmount=e),min:0,precision:2,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u5B9E\u9645\u603B\u91D1\u989D"},{addonBefore:n(()=>l[11]||(l[11]=[s("\xA5")])),_:1},8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:n(()=>[t(v,{label:"\u5165\u5E93\u5907\u6CE8",name:"receiveRemark"},{default:n(()=>[t(b,{value:i.receiveData.receiveRemark,"onUpdate:value":l[3]||(l[3]=e=>i.receiveData.receiveRemark=e),placeholder:"\u8BF7\u8F93\u5165\u5165\u5E93\u5907\u6CE8"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1}),t(p,{title:"\u5165\u5E93\u660E\u7EC6",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[t(F,{ref:"formRef",model:i.receiveData,rules:i.rules},{default:n(()=>[t(U,{columns:i.columns,"data-source":i.receiveData.detailList,pagination:!1,size:"small",bordered:"",scroll:{y:400}},{bodyCell:n(({column:e,record:a,index:C})=>[e.key==="productInfo"?(c(),y("div",ce,[A("div",de,r(a.productName),1),A("div",me,[A("span",_e,r(a.productCode),1),a.specification?(c(),y("span",fe,r(a.specification),1)):d("",!0)])])):d("",!0),e.key==="orderQuantity"?(c(),y(N,{key:1},[s(r(a.orderQuantity)+" "+r(i.getQuantityUnit(a)),1)],64)):d("",!0),e.key==="receiveQuantity"?(c(),w(v,{key:2,name:["detailList",C,"receiveQuantity"],rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u5165\u5E93\u6570\u91CF"},{type:"number",min:0,message:"\u5165\u5E93\u6570\u91CF\u4E0D\u80FD\u5C0F\u4E8E0"}],style:{"margin-bottom":"0"}},{default:n(()=>[t(x,{value:a.receiveQuantity,"onUpdate:value":f=>a.receiveQuantity=f,min:0,precision:i.getPrecision(a.pricingType),step:i.getStep(a.pricingType),style:{width:"100%"},onChange:f=>i.onReceiveQuantityChange(a,C)},{addonAfter:n(()=>[s(r(i.getQuantityUnit(a)),1)]),_:2},1032,["value","onUpdate:value","precision","step","onChange"])]),_:2},1032,["name"])):d("",!0),e.key==="diffQuantity"?(c(),y("span",{key:3,class:O(i.getDiffQuantityClass(a.diffQuantity))},r(i.formatDiffQuantity(a.diffQuantity))+" "+r(i.getQuantityUnit(a)),3)):d("",!0),e.key==="unitPrice"?(c(),y(N,{key:4},[s(" \xA5"+r(i.formatAmount(a.unitPrice)),1)],64)):d("",!0),e.key==="receiveAmount"?(c(),y("span",pe,"\xA5"+r(i.formatAmount(a.receiveAmount)),1)):d("",!0),e.key==="remark"?(c(),w(b,{key:6,value:a.remark,"onUpdate:value":f=>a.remark=f,placeholder:"\u5165\u5E93\u5907\u6CE8",size:"small"},null,8,["value","onUpdate:value"])):d("",!0)]),_:1},8,["columns","data-source"])]),_:1},8,["model","rules"])]),_:1}),t(p,{title:"\u5165\u5E93\u6C47\u603B",size:"small"},{default:n(()=>[t(E,{gutter:16},{default:n(()=>[t(u,{span:6},{default:n(()=>[t(k,{title:"\u5546\u54C1\u79CD\u7C7B",value:i.productCount,suffix:"\u79CD"},null,8,["value"])]),_:1}),t(u,{span:6},{default:n(()=>[t(k,{title:"\u8BA2\u5355\u603B\u91CF",value:i.orderTotalQuantity,suffix:"\u4EF6"},null,8,["value"])]),_:1}),t(u,{span:6},{default:n(()=>[t(k,{title:"\u5165\u5E93\u603B\u91CF",value:i.receiveTotalQuantity,suffix:"\u4EF6"},null,8,["value"])]),_:1}),t(u,{span:6},{default:n(()=>[t(k,{title:"\u5165\u5E93\u91D1\u989D",value:i.receiveTotalAmount,prefix:"\xA5",precision:2},null,8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["visible","onCancel"])}const Le=S(se,[["render",ve],["__scopeId","data-v-dd8968e8"]]);export{Le as default};
