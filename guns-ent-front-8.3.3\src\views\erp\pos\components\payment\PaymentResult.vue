<!--
  支付结果组件
  
  显示支付结果，包括成功、失败、取消等状态
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="payment-result">
    <!-- 结果头部 -->
    <div class="result-header">
      <div class="result-icon" :class="resultClass">
        <check-circle-outlined v-if="status === 'success'" />
        <close-circle-outlined v-else-if="status === 'failed'" />
        <exclamation-circle-outlined v-else-if="status === 'cancelled'" />
        <clock-circle-outlined v-else />
      </div>
      
      <div class="result-title">{{ resultTitle }}</div>
      <div class="result-message" v-if="message">{{ message }}</div>
    </div>
    
    <!-- 支付详情 -->
    <div class="payment-details" v-if="paymentInfo">
      <div class="details-title">
        <icon-font iconClass="icon-detail" />
        <span>支付详情</span>
      </div>
      
      <div class="details-content">
        <!-- 支付方式 -->
        <div class="detail-row">
          <span class="detail-label">支付方式:</span>
          <span class="detail-value">
            {{ getPaymentMethodName(paymentInfo.paymentMethod) }}
          </span>
        </div>
        
        <!-- 支付金额 -->
        <div class="detail-row">
          <span class="detail-label">支付金额:</span>
          <span class="detail-value amount">¥{{ formatPrice(paymentInfo.amount) }}</span>
        </div>
        
        <!-- 实收金额（现金支付） -->
        <div class="detail-row" v-if="paymentInfo.receivedAmount">
          <span class="detail-label">实收金额:</span>
          <span class="detail-value">¥{{ formatPrice(paymentInfo.receivedAmount) }}</span>
        </div>
        
        <!-- 找零金额（现金支付） -->
        <div class="detail-row" v-if="paymentInfo.changeAmount && paymentInfo.changeAmount > 0">
          <span class="detail-label">找零金额:</span>
          <span class="detail-value change">¥{{ formatPrice(paymentInfo.changeAmount) }}</span>
        </div>
        
        <!-- 交易流水号 -->
        <div class="detail-row" v-if="paymentInfo.transactionId">
          <span class="detail-label">交易流水:</span>
          <span class="detail-value transaction-id">
            {{ paymentInfo.transactionId }}
          </span>
        </div>
        
        <!-- 支付时间 -->
        <div class="detail-row" v-if="paymentInfo.paymentTime">
          <span class="detail-label">支付时间:</span>
          <span class="detail-value">{{ formatDateTime(paymentInfo.paymentTime) }}</span>
        </div>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div class="error-details" v-if="status === 'failed' && errorInfo">
      <div class="error-title">
        <icon-font iconClass="icon-error" />
        <span>错误详情</span>
      </div>
      
      <div class="error-content">
        <div class="error-code" v-if="errorInfo.code">
          错误代码: {{ errorInfo.code }}
        </div>
        <div class="error-message">
          {{ errorInfo.message || '支付失败，请重试' }}
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="result-actions">
      <!-- 成功状态按钮 -->
      <template v-if="status === 'success'">
        <a-button 
          v-if="showPrintButton"
          @click="handlePrint"
          :loading="printing"
          class="action-btn"
        >
          <template #icon>
            <icon-font iconClass="icon-print" />
          </template>
          打印小票
        </a-button>
        
        <a-button 
          type="primary"
          @click="handleComplete"
          class="action-btn"
        >
          <template #icon>
            <check-outlined />
          </template>
          完成
        </a-button>
      </template>
      
      <!-- 失败状态按钮 -->
      <template v-else-if="status === 'failed'">
        <a-button 
          @click="handleRetry"
          :loading="retrying"
          class="action-btn"
        >
          <template #icon>
            <reload-outlined />
          </template>
          重试支付
        </a-button>
        
        <a-button 
          @click="handleCancel"
          class="action-btn"
        >
          <template #icon>
            <close-outlined />
          </template>
          取消订单
        </a-button>
      </template>
      
      <!-- 其他状态按钮 -->
      <template v-else>
        <a-button 
          @click="handleCancel"
          class="action-btn"
        >
          <template #icon>
            <close-outlined />
          </template>
          关闭
        </a-button>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import IconFont from '@/components/common/IconFont/index.vue'

// 定义组件名称
defineOptions({
  name: 'PaymentResult'
})

// 定义Props
const props = defineProps({
  status: {
    type: String,
    required: true,
    validator: (value) => ['success', 'failed', 'cancelled', 'processing'].includes(value)
  },
  paymentInfo: {
    type: Object,
    default: null
  },
  errorInfo: {
    type: Object,
    default: null
  },
  message: {
    type: String,
    default: ''
  },
  showPrintButton: {
    type: Boolean,
    default: true
  }
})

// 定义Emits
const emit = defineEmits([
  'complete',
  'retry',
  'cancel',
  'print'
])

// 响应式状态
const printing = ref(false)
const retrying = ref(false)

// 计算属性
const resultClass = computed(() => {
  return {
    'success': props.status === 'success',
    'failed': props.status === 'failed',
    'cancelled': props.status === 'cancelled',
    'processing': props.status === 'processing'
  }
})

const resultTitle = computed(() => {
  switch (props.status) {
    case 'success':
      return '支付成功'
    case 'failed':
      return '支付失败'
    case 'cancelled':
      return '支付已取消'
    case 'processing':
      return '支付处理中'
    default:
      return '未知状态'
  }
})

// 方法
const formatPrice = (price) => {
  return (price || 0).toFixed(2)
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN')
}

const getPaymentMethodName = (method) => {
  const methods = {
    'CASH': '现金支付',
    'WECHAT': '微信支付',
    'ALIPAY': '支付宝',
    'CARD': '银行卡'
  }
  return methods[method] || method
}

const handleComplete = () => {
  emit('complete', {
    paymentInfo: props.paymentInfo
  })
}

const handleRetry = async () => {
  retrying.value = true
  try {
    emit('retry', {
      paymentInfo: props.paymentInfo,
      errorInfo: props.errorInfo
    })
  } finally {
    retrying.value = false
  }
}

const handleCancel = () => {
  emit('cancel', {
    paymentInfo: props.paymentInfo
  })
}

const handlePrint = async () => {
  printing.value = true
  try {
    emit('print', {
      paymentInfo: props.paymentInfo
    })
    message.success('小票打印中...')
  } finally {
    printing.value = false
  }
}
</script>

<style scoped>
.payment-result {
  padding: 24px;
  background: #fff;
  text-align: center;
}

/* 结果头部 */
.result-header {
  margin-bottom: 24px;
}

.result-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.result-icon.success {
  color: #52c41a;
}

.result-icon.failed {
  color: #ff4d4f;
}

.result-icon.cancelled {
  color: #faad14;
}

.result-icon.processing {
  color: #1890ff;
}

.result-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.result-message {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.5;
}

/* 支付详情 */
.payment-details {
  margin-bottom: 24px;
  text-align: left;
}

.details-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.details-content {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #595959;
  min-width: 80px;
}

.detail-value {
  color: #262626;
  font-weight: 500;
}

.detail-value.amount {
  color: #ff4d4f;
  font-size: 16px;
  font-weight: 600;
}

.detail-value.change {
  color: #52c41a;
  font-weight: 600;
}

.transaction-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 错误详情 */
.error-details {
  margin-bottom: 24px;
  text-align: left;
}

.error-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
}

.error-content {
  background: #fff2f0;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #ff4d4f;
}

.error-code {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
}

.error-message {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.action-btn {
  min-width: 120px;
  height: 44px;
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-result {
    padding: 20px 16px;
  }
  
  .result-icon {
    font-size: 48px;
    margin-bottom: 12px;
  }
  
  .result-title {
    font-size: 18px;
  }
  
  .result-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-btn {
    width: 100%;
    height: 40px;
  }
}

/* 动画效果 */
.result-icon {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>