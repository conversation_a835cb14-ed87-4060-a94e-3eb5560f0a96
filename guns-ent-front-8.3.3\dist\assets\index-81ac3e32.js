import{_ as V}from"./index-02bf6f00.js";import{r as d,o as $,k as x,a as r,c as y,b as t,d as s,w as l,g as z,t as M,h as _,f as w,M as U,E as A,m as C,n as O,B as R,I as D,l as L}from"./index-18a1ea24.js";import{S as f}from"./timer-form-e94ed0df.js";import F from"./timer-add-edit-ef9b9735.js";/* empty css              *//* empty css              *//* empty css              */const P={class:"guns-layout"},q={class:"guns-layout-content"},G={class:"guns-layout"},H={class:"guns-layout-content-application"},J={class:"content-mian"},K={class:"content-mian-header"},Q={class:"header-content"},W={class:"header-content-left"},X={class:"header-content-right"},Y={class:"content-mian-body"},Z={class:"table-content"},ee=["onClick"],de=Object.assign({name:"SystemTimer"},{__name:"index",setup(te){const S=d([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"timerName",title:"\u4EFB\u52A1\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"params",title:"\u53C2\u6570",width:100,isShow:!0},{dataIndex:"cron",title:"cron\u8868\u8FBE\u5F0F",ellipsis:!0,width:200,isShow:!0},{dataIndex:"actionClass",title:"\u4EFB\u52A1class",ellipsis:!0,width:100,isShow:!0},{dataIndex:"remark",title:"\u5907\u6CE8\u4FE1\u606F",ellipsis:!0,width:150,isShow:!0},{dataIndex:"jobStatus",title:"\u72B6\u6001",width:150,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:80,isShow:!0}]),b=d(null),p=d({timerName:""}),k=d(null),u=d(!1);$(()=>{});const m=()=>{b.value.reload()},h=n=>{k.value=n,u.value=!0},g=n=>{U.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u5B9A\u65F6\u4EFB\u52A1\u5417?",icon:s(A),maskClosable:!0,onOk:async()=>{const e=await f.delete({timerId:n.timerId});C.success(e.message),m()}})},I=async n=>{const e=n.timerId;let a={};n.jobStatus==1?a=await f.start({timerId:e}):a=await f.stop({timerId:e}),C.success(a.message),m()};return(n,e)=>{const a=O,T=x("plus-outlined"),j=R,v=D,B=L,E=x("vxe-switch"),N=V;return r(),y("div",P,[t("div",q,[t("div",G,[t("div",H,[t("div",J,[t("div",K,[t("div",Q,[t("div",W,[s(a,{size:16})]),t("div",X,[s(a,{size:16},{default:l(()=>[s(j,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=o=>h())},{default:l(()=>[s(T),e[3]||(e[3]=z("\u65B0\u5EFA"))]),_:1,__:[3]})]),_:1})])])]),t("div",Y,[t("div",Z,[s(N,{columns:S.value,where:p.value,rowId:"timerId",ref_key:"tableRef",ref:b,rowSelection:!1,url:"/sysTimers/page",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"TIMER_TABLE"},{toolLeft:l(()=>[s(B,{value:p.value.searchText,"onUpdate:value":e[1]||(e[1]=o=>p.value.searchText=o),placeholder:"\u4EFB\u52A1\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",bordered:!1,onPressEnter:m,class:"search-input"},{prefix:l(()=>[s(v,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:l(({column:o,record:i})=>[o.dataIndex=="timerName"?(r(),y("a",{key:0,onClick:c=>h(i)},M(i.timerName),9,ee)):_("",!0),o.dataIndex==="jobStatus"?(r(),w(E,{key:1,modelValue:i.jobStatus,"onUpdate:modelValue":c=>i.jobStatus=c,"open-value":1,"close-value":2,"open-label":"\u8FD0\u884C","close-label":"\u505C\u6B62",onChange:c=>I(i)},null,8,["modelValue","onUpdate:modelValue","onChange"])):_("",!0),o.key=="action"?(r(),w(a,{key:2,size:16},{default:l(()=>[s(v,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:c=>h(i)},null,8,["onClick"]),s(v,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:c=>g(i)},null,8,["onClick"])]),_:2},1024)):_("",!0)]),_:1},8,["columns","where"])])])])])])]),u.value?(r(),w(F,{key:0,visible:u.value,"onUpdate:visible":e[2]||(e[2]=o=>u.value=o),data:k.value,onDone:m},null,8,["visible","data"])):_("",!0)])}}});export{de as default};
