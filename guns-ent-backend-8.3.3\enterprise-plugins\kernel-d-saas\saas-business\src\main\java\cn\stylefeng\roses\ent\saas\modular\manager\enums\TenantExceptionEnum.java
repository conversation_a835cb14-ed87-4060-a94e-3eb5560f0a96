package cn.stylefeng.roses.ent.saas.modular.manager.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 租户信息异常相关枚举
 *
 * <AUTHOR>
 * @date 2023/08/30 17:04
 */
@Getter
public enum TenantExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    TENANT_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "查询结果不存在"),

    /**
     * 租户不存在
     */
    TENANT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "租户不存在"),

    /**
     * 不能删除根租户
     */
    CANNOT_DELETE_ROOT_TENANT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "不能删除根租户"),

    /**
     * 不能修改根租户的信息
     */
    CANNOT_EDIT_ROOT_TENANT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10004", "不能修改根租户的信息"),

    /**
     * 租户注册邮箱验证码错误
     */
    EMAIL_NOT_RIGHT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10005", "租户注册邮箱验证码错误");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    TenantExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}