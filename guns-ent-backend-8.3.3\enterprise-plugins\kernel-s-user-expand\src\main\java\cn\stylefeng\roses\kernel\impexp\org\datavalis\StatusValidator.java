package cn.stylefeng.roses.kernel.impexp.org.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;

/**
 * 通用状态校验
 *
 * <AUTHOR>
 * @since 2024-02-18 18:48
 */
public class StatusValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(false, originValue, originValue, "状态不能为空值");
        }

        if (StatusEnum.ENABLE.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, StatusEnum.ENABLE.getCode());
        } else if (StatusEnum.DISABLE.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, StatusEnum.DISABLE.getCode());
        } else {
            return new ExcelLineParseResult(false, originValue, originValue, "状态的值不正确");
        }
    }

}
