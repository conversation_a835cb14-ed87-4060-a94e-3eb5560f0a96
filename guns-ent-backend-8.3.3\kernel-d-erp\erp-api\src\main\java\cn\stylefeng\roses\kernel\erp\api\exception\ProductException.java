package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;

/**
 * 商品相关异常
 *
 * <AUTHOR>
 * @since 2025/08/01 20:10
 */
public class ProductException extends PosException {

    public ProductException(AbstractExceptionEnum exception) {
        super(exception);
    }

    public ProductException(AbstractExceptionEnum exception, Object... params) {
        super(exception, params);
    }

    /**
     * 商品不存在异常
     */
    public static ProductException notFound(Long productId) {
        return new ProductException(ErpPosExceptionEnum.PRODUCT_NOT_AVAILABLE, productId);
    }

    /**
     * 商品已停用异常
     */
    public static ProductException inactive(String productName) {
        return new ProductException(ErpPosExceptionEnum.PRODUCT_NOT_AVAILABLE, productName);
    }

    /**
     * 商品库存不足异常
     */
    public static ProductException outOfStock(String productName, Integer currentStock, Integer requiredQuantity) {
        return new ProductException(ErpPosExceptionEnum.PRODUCT_STOCK_INSUFFICIENT, productName, currentStock, requiredQuantity);
    }

    /**
     * 商品数量无效异常
     */
    public static ProductException invalidQuantity(String productName, Integer quantity) {
        return new ProductException(ErpPosExceptionEnum.PRODUCT_QUANTITY_INVALID, productName, quantity);
    }
}
