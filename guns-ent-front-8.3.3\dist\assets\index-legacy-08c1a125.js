System.register(["./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-f13ba0d3.js","./regionApi-legacy-73888494.js","./region-tree-legacy-3ef1c39d.js","./addForm-legacy-e45f6d10.js","./detailForm-legacy-b08e00b8.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./UniversalTree-legacy-6dcdf778.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./index-legacy-e24582b9.js"],(function(e,a){"use strict";var l,t,o,n,d,i,r,s,c,u,f,g,p,v,h,b,x,_,y,m,w,k,C,R,I,S,E,L,T,j,O,z,D,N,A,<PERSON>,F,U,<PERSON>,B,<PERSON>,K,<PERSON>,Y,q;return{setters:[e=>{l=e._},e=>{t=e._},e=>{o=e._,n=e.P,d=e.K,i=e.r,r=e.L,s=e.N,c=e.s,u=e.k,f=e.a,g=e.c,p=e.d,v=e.w,h=e.b,b=e.g,x=e.t,_=e.h,y=e.O,m=e.Q,w=e.F,k=e.f,C=e.M,R=e.E,I=e.m,S=e.U,E=e.n,L=e.B,T=e.I,j=e.p,O=e.q,z=e.D,D=e.l,N=e.V,A=e.W,M=e.J,F=e.u,U=e.v,P=e.G,B=e.H},null,e=>{G=e._},e=>{K=e.R},e=>{J=e.default},e=>{Y=e.default},e=>{q=e.default},null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".guns-layout .table-toolbar[data-v-4518f7f4]{margin-bottom:16px;padding:16px 0;border-bottom:1px solid #f0f0f0}.guns-layout .table-toolbar .toolbar-left[data-v-4518f7f4]{display:flex;align-items:center;gap:16px}.guns-layout .table-toolbar .toolbar-left .search-input .ant-input[data-v-4518f7f4]{border-radius:6px}.guns-layout .table-toolbar .toolbar-left a[data-v-4518f7f4]{color:#1890ff;cursor:pointer}.guns-layout .table-toolbar .toolbar-left a[data-v-4518f7f4]:hover{color:#40a9ff}.guns-layout .advanced-search[data-v-4518f7f4]{padding:16px;background-color:#fafafa;border-radius:6px;margin-bottom:16px}.guns-layout .advanced-search .ant-form-item[data-v-4518f7f4]{margin-bottom:0}.guns-layout[data-v-4518f7f4]{height:100%;width:100%;margin:0;padding:0}.guns-layout-content[data-v-4518f7f4],.guns-layout-content-application[data-v-4518f7f4]{width:100%;height:100%;display:flex;flex-direction:column;margin:0;padding:0;box-sizing:border-box}.sidebar-content[data-v-4518f7f4]{height:100%;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08)}.content-main[data-v-4518f7f4]{width:100%;height:100%;display:flex;flex-direction:column;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08);margin:0;padding:0;box-sizing:border-box}.content-main-header[data-v-4518f7f4]{padding:16px 24px;border-bottom:1px solid #f0f0f0;background:#fafafa;border-radius:6px 6px 0 0}.header-content[data-v-4518f7f4]{display:flex;justify-content:space-between;align-items:center}.current-region-info[data-v-4518f7f4]{font-size:14px;color:#666}.content-main-body[data-v-4518f7f4]{flex:1;padding:16px 24px;overflow:auto;width:100%;box-sizing:border-box}.table-content[data-v-4518f7f4]{width:100%;height:100%;display:flex;flex-direction:column;box-sizing:border-box}.table-pagination[data-v-4518f7f4]{margin-top:16px;text-align:right}.region-table[data-v-4518f7f4],.region-table[data-v-4518f7f4] .ant-table,.region-table[data-v-4518f7f4] .ant-table-container,.region-table[data-v-4518f7f4] .ant-table-content,.region-table[data-v-4518f7f4] .ant-table-body{width:100%!important}[data-v-4518f7f4] .guns-split-panel-body{width:100%!important;flex:1!important;overflow:hidden!important}[data-v-4518f7f4] .guns-split-panel{width:100%!important}\n",document.head.appendChild(a);const H={class:"guns-layout"},Q={class:"guns-layout-sidebar width-100 p-t-12"},V={class:"sidebar-content"},W={class:"guns-layout-content"},$={class:"guns-layout"},X={class:"guns-layout-content-application"},Z={class:"content-main"},ee={class:"content-main-header"},ae={class:"header-content"},le={class:"header-content-left"},te={key:0,class:"current-region-info"},oe={class:"header-content-right"},ne={class:"content-main-body"},de={class:"table-content"},ie={key:0,class:"super-search",style:{"margin-top":"8px"}};e("default",o({name:"ErpRegion",components:{PlusOutlined:n,SmallDashOutlined:d,regionTree:J,addForm:Y,detailForm:q},setup(){const e=i(!1),a=i(!1),l=i(!1),t=i({}),o=i(null),n=i(),d=i(null),u=r((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),f=r((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),g=r((()=>s()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),v=c({parentId:void 0,searchText:void 0,status:void 0,regionLevel:void 0}),h=async()=>{d.value.reload()},b=()=>{t.value={parentId:v.parentId,regionLevel:o.value?o.value.regionLevel+1:1},a.value=!0},x=e=>{t.value={...e},a.value=!0},_=()=>{if(d.value.selectedRowList&&0==d.value.selectedRowList.length)return I.warning("请选择需要删除的区域");C.confirm({title:"提示",content:"确定要删除选中的区域吗?",icon:p(R),maskClosable:!0,onOk:async()=>{var e;const a=await K.batchDelete({regionIds:d.value.selectedRowList});I.success(a.message),h(),null===(e=n.value)||void 0===e||e.reload()}})};return{tableRef:d,regionTreeRef:n,superSearch:e,where:v,columns:[{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"regionCode",title:"区域编码",width:140,ellipsis:!0,isShow:!0},{dataIndex:"regionName",title:"区域名称",width:160,ellipsis:!0,isShow:!0},{dataIndex:"parentName",title:"父级区域",width:140,ellipsis:!0,isShow:!0},{dataIndex:"regionLevel",title:"区域层级",width:100,align:"center",isShow:!0},{dataIndex:"sortOrder",title:"排序号",width:80,align:"center",isShow:!0},{dataIndex:"status",title:"状态",width:80,align:"center",isShow:!0},{dataIndex:"createTime",title:"创建时间",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}],currentRecord:t,currentRegionInfo:o,showEdit:a,showDetailModal:l,labelCol:u,wrapperCol:f,spanCol:g,reload:h,clear:()=>{v.searchText="",v.status=void 0,v.regionLevel=void 0,v.parentId=void 0,n.value.currentSelectKeys=[],o.value=null,h()},changeSuperSearch:()=>{e.value=!e.value},onTreeSelect:(e,a)=>{if(e.length>0){const l=a.selectedNodes[0];o.value=l,v.parentId=e[0],h()}else o.value=null,v.parentId=void 0,h()},onAddRegion:e=>{e&&(o.value=e,v.parentId=e.regionId),b()},onEditRegion:e=>{x(e)},onDeleteRegion:e=>{h()},openAddModal:b,openEditModal:x,deleteRecord:async e=>{C.confirm({title:"提示",content:"确定要删除选中的区域吗?",icon:p(R),maskClosable:!0,onOk:async()=>{var a;const l=await K.delete({regionId:e.regionId});I.success(l.message),h(),null===(a=n.value)||void 0===a||a.reload()}})},moreClick:({key:e})=>{"1"==e&&_()},batchDelete:_,handleFormOk:()=>{var e;h(),null===(e=n.value)||void 0===e||e.reload()}}}},[["render",function(e,a,o,n,d,i){const r=G,s=S,c=E,C=u("plus-outlined"),R=L,I=T,K=j,J=O,Y=u("small-dash-outlined"),q=z,re=D,se=N,ce=A,ue=M,fe=F,ge=U,pe=P,ve=B,he=t,be=l,xe=u("add-form"),_e=u("detail-form");return f(),g("div",H,[p(be,{width:"292px",cacheKey:"ERP_REGION_MANAGEMENT"},{content:v((()=>[h("div",W,[h("div",$,[h("div",X,[h("div",Z,[h("div",ee,[h("div",ae,[h("div",le,[p(c,{size:16},{default:v((()=>[n.currentRegionInfo?(f(),g("span",te,[a[6]||(a[6]=b(" 当前区域：")),p(s,{color:"blue"},{default:v((()=>[b(x(n.currentRegionInfo.regionName),1)])),_:1})])):_("",!0)])),_:1})]),h("div",oe,[p(c,{size:16},{default:v((()=>[p(R,{type:"primary",class:"border-radius",onClick:n.openAddModal},{default:v((()=>[p(C),a[7]||(a[7]=b(" 新增子区域 "))])),_:1,__:[7]},8,["onClick"]),p(q,null,{overlay:v((()=>[p(J,{onClick:n.moreClick},{default:v((()=>[p(K,{key:"1"},{default:v((()=>[p(I,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[8]||(a[8]=h("span",null,"批量删除",-1))])),_:1,__:[8]})])),_:1},8,["onClick"])])),default:v((()=>[p(R,{class:"border-radius"},{default:v((()=>[a[9]||(a[9]=b(" 更多 ")),p(Y)])),_:1,__:[9]})])),_:1})])),_:1})])])]),h("div",ne,[h("div",de,[p(he,{columns:n.columns,where:n.where,fieldBusinessCode:"ERP_REGION_TABLE",showTableTool:"",showToolTotal:!1,rowId:"regionId",ref:"tableRef",url:"/erp/region/page"},{toolLeft:v((()=>[p(re,{value:n.where.searchText,"onUpdate:value":a[0]||(a[0]=e=>n.where.searchText=e),bordered:!1,allowClear:"",placeholder:"区域名称、编码（回车搜索）",onPressEnter:n.reload,style:{width:"240px"},class:"search-input"},{prefix:v((()=>[p(I,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),p(se,{type:"vertical",class:"divider"}),h("a",{onClick:a[1]||(a[1]=(...e)=>n.changeSuperSearch&&n.changeSuperSearch(...e))},x(n.superSearch?"收起":"高级筛选"),1)])),toolBottom:v((()=>[n.superSearch?(f(),g("div",ie,[p(ve,{model:n.where,labelCol:n.labelCol,"wrapper-col":n.wrapperCol},{default:v((()=>[p(pe,{gutter:16},{default:v((()=>[p(ge,y(m(n.spanCol)),{default:v((()=>[p(fe,{label:"状态:"},{default:v((()=>[p(ue,{value:n.where.status,"onUpdate:value":a[2]||(a[2]=e=>n.where.status=e),placeholder:"请选择状态",style:{width:"100%"},allowClear:""},{default:v((()=>[p(ce,{value:"Y"},{default:v((()=>a[10]||(a[10]=[b("启用")]))),_:1,__:[10]}),p(ce,{value:"N"},{default:v((()=>a[11]||(a[11]=[b("停用")]))),_:1,__:[11]})])),_:1},8,["value"])])),_:1})])),_:1},16),p(ge,y(m(n.spanCol)),{default:v((()=>[p(fe,{label:"区域层级:"},{default:v((()=>[p(ue,{value:n.where.regionLevel,"onUpdate:value":a[3]||(a[3]=e=>n.where.regionLevel=e),placeholder:"请选择层级",style:{width:"100%"},allowClear:""},{default:v((()=>[p(ce,{value:1},{default:v((()=>a[12]||(a[12]=[b("国家")]))),_:1,__:[12]}),p(ce,{value:2},{default:v((()=>a[13]||(a[13]=[b("省")]))),_:1,__:[13]}),p(ce,{value:3},{default:v((()=>a[14]||(a[14]=[b("市")]))),_:1,__:[14]}),p(ce,{value:4},{default:v((()=>a[15]||(a[15]=[b("区县")]))),_:1,__:[15]}),p(ce,{value:5},{default:v((()=>a[16]||(a[16]=[b("商圈")]))),_:1,__:[16]})])),_:1},8,["value"])])),_:1})])),_:1},16),p(ge,y(m(n.spanCol)),{default:v((()=>[p(fe,{label:" ",class:"not-label"},{default:v((()=>[p(c,{size:16},{default:v((()=>[p(R,{class:"border-radius",onClick:n.reload,type:"primary"},{default:v((()=>a[17]||(a[17]=[b("查询")]))),_:1,__:[17]},8,["onClick"]),p(R,{class:"border-radius",onClick:n.clear},{default:v((()=>a[18]||(a[18]=[b("重置")]))),_:1,__:[18]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):_("",!0)])),bodyCell:v((({column:e,record:l})=>["regionLevel"===e.dataIndex?(f(),g(w,{key:0},[1===l.regionLevel?(f(),k(s,{key:0,color:"red"},{default:v((()=>a[19]||(a[19]=[b("国家")]))),_:1,__:[19]})):2===l.regionLevel?(f(),k(s,{key:1,color:"orange"},{default:v((()=>a[20]||(a[20]=[b("省")]))),_:1,__:[20]})):3===l.regionLevel?(f(),k(s,{key:2,color:"yellow"},{default:v((()=>a[21]||(a[21]=[b("市")]))),_:1,__:[21]})):4===l.regionLevel?(f(),k(s,{key:3,color:"green"},{default:v((()=>a[22]||(a[22]=[b("区县")]))),_:1,__:[22]})):5===l.regionLevel?(f(),k(s,{key:4,color:"blue"},{default:v((()=>a[23]||(a[23]=[b("商圈")]))),_:1,__:[23]})):(f(),k(s,{key:5,color:"default"},{default:v((()=>a[24]||(a[24]=[b("未知")]))),_:1,__:[24]}))],64)):"status"===e.dataIndex?(f(),g(w,{key:1},["Y"===l.status?(f(),k(s,{key:0,color:"green"},{default:v((()=>a[25]||(a[25]=[b("启用")]))),_:1,__:[25]})):"N"===l.status?(f(),k(s,{key:1,color:"red"},{default:v((()=>a[26]||(a[26]=[b("停用")]))),_:1,__:[26]})):(f(),k(s,{key:2,color:"default"},{default:v((()=>[b(x(l.status),1)])),_:2},1024))],64)):"action"===e.key?(f(),k(c,{key:2,size:16},{default:v((()=>[p(I,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>n.openEditModal(l)},null,8,["onClick"]),p(I,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>n.deleteRecord(l)},null,8,["onClick"])])),_:2},1024)):_("",!0)])),_:1},8,["columns","where"])])])])])])])])),default:v((()=>[h("div",Q,[h("div",V,[p(r,{ref:"regionTreeRef",isShowEditIcon:!0,onTreeSelect:n.onTreeSelect,onAddRegion:n.onAddRegion,onEditRegion:n.onEditRegion,onDeleteRegion:n.onDeleteRegion},null,8,["onTreeSelect","onAddRegion","onEditRegion","onDeleteRegion"])])])])),_:1}),p(xe,{visible:n.showEdit,"onUpdate:visible":a[4]||(a[4]=e=>n.showEdit=e),data:n.currentRecord,onOk:n.handleFormOk},null,8,["visible","data","onOk"]),p(_e,{visible:n.showDetailModal,"onUpdate:visible":a[5]||(a[5]=e=>n.showDetailModal=e),data:n.currentRecord},null,8,["visible","data"])])}],["__scopeId","data-v-4518f7f4"]]))}}}));
