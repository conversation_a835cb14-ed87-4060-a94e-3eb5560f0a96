package cn.stylefeng.roses.kernel.ca.server.modular.manage.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 单点登录客户端异常相关枚举
 *
 * <AUTHOR>
 * @since 2023/11/05 09:28
 */
@Getter
public enum SsoClientExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    SSO_CLIENT_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "查询结果不存在"),

    /**
     * 自定义登录界面地址不能为空
     */
    CUSTOM_LOGIN_URL_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "自定义登录界面地址不能为空");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    SsoClientExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
