import{_ as Y}from"./index-cd3d6e23.js";import{C as $,_ as W}from"./index-02bf6f00.js";import{r as l,o as j,k as I,a as i,c as h,b as n,d as t,w as o,g as b,F as q,h as u,f as g,M as E,E as B,m as C,I as P,l as G,B as H,n as Q,p as X,q as Z,D as ee}from"./index-18a1ea24.js";import{_ as te,S as O}from"./sys-user-secret-key-add-edit-9c1cd96a.js";/* empty css              *//* empty css              *//* empty css              */import"./sys-user-secret-key-form-1f95dd3c.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";/* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              */import"./print-4e42e756.js";const se={class:"guns-layout"},ne={class:"guns-layout-content"},oe={class:"guns-layout"},ae={class:"guns-layout-content-application"},le={class:"content-mian"},ie={class:"content-mian-header"},re={class:"header-content"},ue={class:"header-content-left"},ce={class:"header-content-right"},de={class:"content-mian-body"},_e={class:"table-content"},pe={key:0},me={key:1},De=Object.assign({name:"SysUserSecretKey"},{__name:"index",setup(ve){const d=l([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:s})=>r.value.tableIndex+s},{dataIndex:"secretKeyName",title:"\u79D8\u94A5\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"keyUserName",title:"\u6240\u5C5E\u4EBA",ellipsis:!0,width:200,isShow:!0},{dataIndex:"keyAccount",title:"\u6240\u5C5E\u4EBA\u8D26\u53F7",ellipsis:!0,width:200,isShow:!0},{dataIndex:"secretExpirationTime",title:"\u79D8\u94A5\u8FC7\u671F\u65F6\u95F4",ellipsis:!0,width:200,isShow:!0},{dataIndex:"secretOnceFlag",title:"\u4E00\u6B21\u6027\u79D8\u94A5",ellipsis:!0,width:200,isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",ellipsis:!0,width:200,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,fixed:"right",isShow:!0}]),r=l(null),_=l({searchText:""}),p=l(!1),k=l(null),m=l(!1),S=l("SYS_USER_SECRET_KEY");j(()=>{U()});const U=()=>{$.getUserConfig({fieldBusinessCode:S.value}).then(s=>{s.tableWidthJson&&(d.value=JSON.parse(s.tableWidthJson))})},D=({key:s})=>{s=="1"?p.value=!0:s=="2"&&T()},c=()=>{r.value.reload()},K=()=>{_.value.searchText="",c()},N=s=>{k.value=s,m.value=!0},R=s=>{E.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u4E34\u65F6\u79D8\u94A5\u5417?",icon:t(B),maskClosable:!0,onOk:async()=>{const e=await O.delete({userSecretKeyId:s.userSecretKeyId});C.success(e.message),c()}})},T=()=>{if(r.value.selectedRowList&&r.value.selectedRowList.length==0)return C.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u4E34\u65F6\u79D8\u94A5");E.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u4E34\u65F6\u79D8\u94A5\u5417?",icon:t(B),maskClosable:!0,onOk:async()=>{const s=await O.batchDelete({batchDeleteIdList:r.value.selectedRowList});C.success(s.message),c()}})};return(s,e)=>{const v=P,F=G,f=H,y=Q,z=I("plus-outlined"),x=X,A=Z,L=I("small-dash-outlined"),M=ee,V=W,J=Y;return i(),h("div",se,[n("div",ne,[n("div",oe,[n("div",ae,[n("div",le,[n("div",ie,[n("div",re,[n("div",ue,[t(y,{size:16},{default:o(()=>[t(F,{value:_.value.searchText,"onUpdate:value":e[0]||(e[0]=a=>_.value.searchText=a),placeholder:"\u8F93\u5165\u540D\u79F0\u6216\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:c,class:"search-input"},{prefix:o(()=>[t(v,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),t(f,{class:"border-radius",onClick:K},{default:o(()=>e[5]||(e[5]=[b("\u91CD\u7F6E")])),_:1,__:[5]})]),_:1})]),n("div",ce,[t(y,{size:16},{default:o(()=>[t(f,{type:"primary",class:"border-radius",onClick:e[1]||(e[1]=a=>N())},{default:o(()=>[t(z),e[6]||(e[6]=b("\u65B0\u5EFA"))]),_:1,__:[6]}),t(M,null,{overlay:o(()=>[t(A,{onClick:D},{default:o(()=>[t(x,{key:"1"},{default:o(()=>[t(v,{iconClass:"icon-opt-zidingyilie",color:"#60666b"}),e[7]||(e[7]=n("span",null,"\u81EA\u5B9A\u4E49\u5217",-1))]),_:1,__:[7]}),n("div",null,[t(x,{key:"2"},{default:o(()=>[t(v,{iconClass:"icon-opt-shanchu",color:"#60666b"}),e[8]||(e[8]=n("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[8]})])]),_:1})]),default:o(()=>[t(f,{class:"border-radius"},{default:o(()=>[e[9]||(e[9]=b(" \u66F4\u591A ")),t(L)]),_:1,__:[9]})]),_:1})]),_:1})])])]),n("div",de,[n("div",_e,[t(V,{columns:d.value,where:_.value,rowId:"userSecretKeyId",ref_key:"tableRef",ref:r,url:"/sysUserSecretKey/page"},{bodyCell:o(({column:a,record:w})=>[a.dataIndex=="secretOnceFlag"?(i(),h(q,{key:0},[w.secretOnceFlag=="Y"?(i(),h("span",pe,"\u662F")):u("",!0),w.secretOnceFlag=="N"?(i(),h("span",me,"\u5426")):u("",!0)],64)):u("",!0),a.key=="action"?(i(),g(y,{key:1,size:16},{default:o(()=>[t(v,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:he=>R(w)},null,8,["onClick"])]),_:2},1024)):u("",!0)]),_:1},8,["columns","where"])])])])])])]),p.value?(i(),g(J,{key:0,visible:p.value,"onUpdate:visible":e[2]||(e[2]=a=>p.value=a),data:d.value,onDone:e[3]||(e[3]=a=>d.value=a),fieldBusinessCode:S.value},null,8,["visible","data","fieldBusinessCode"])):u("",!0),m.value?(i(),g(te,{key:1,visible:m.value,"onUpdate:visible":e[4]||(e[4]=a=>m.value=a),data:k.value,onDone:c},null,8,["visible","data"])):u("",!0)])}}});export{De as default};
