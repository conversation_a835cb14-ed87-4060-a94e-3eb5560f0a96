import{R as r,r as d,s as x,o as T,k as q,a as S,f as w,w as n,d as a,h as M,m as z,l as P,u as $,v as j,G as D,H as E,M as G}from"./index-18a1ea24.js";import{_ as H}from"./index-3a0e5c06.js";class Y{static getUserAssignList(l){return r.get("/sysRoleAssign/getUserAssignList",l)}static changeRoleSelect(l){return r.post("/sysRoleAssign/changeRoleSelect",l)}static changeStatus(l){return r.post("/sysRoleAssign/changeStatus",l)}static removeUserOrgBind(l){return r.post("/sysRoleAssign/removeUserOrgBind",l)}static addUserOrgBind(l){return r.post("/sysRoleAssign/addUserOrgBind",l)}static deleteAllOrgBind(l){return r.post("/sysRoleAssign/deleteAllOrgBind",l)}static disableAllOrg(l){return r.post("/sysRoleAssign/disableAllOrg",l)}static syncOtherOrgStatusAndBusinessRole(l){return r.post("/sysRoleAssign/syncOtherOrgStatusAndBusinessRole",l)}}const J={__name:"add-org",props:{visible:Boolean,userId:String},emits:["update:visible","done"],setup(b,{emit:l}){const O=b,y=l,p=d(!1),s=d({userId:O.userId,mainFlag:"N",statusFlag:1}),A=d(),N=x({orgName:[{required:!0,message:"\u8BF7\u9009\u62E9\u673A\u6784",type:"string",trigger:"change"}],positionName:[{required:!0,message:"\u8BF7\u9009\u62E9\u804C\u52A1",type:"string",trigger:"change"}],mainFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E3B\u8981\u90E8\u95E8"}]}),g=d(!1),c=d(""),m=d([]),_=d({});T(()=>{});const f=t=>{y("update:visible",t)},R=t=>{m.value=[t],t=="dept"?(c.value="\u673A\u6784\u9009\u62E9",U("orgId","orgName","selectOrgList")):(c.value="\u804C\u4F4D\u9009\u62E9",U("positionId","positionName","selectPositionList")),g.value=!0},U=(t,e,u)=>{s.value[t]&&s.value[e]?_.value[u]=[{bizId:s.value[t],name:s.value[e]}]:_.value[u]=[]},I=t=>{g.value=!1,m.value[0]=="dept"?B(t,"orgId","orgName","selectOrgList"):m.value[0]=="position"&&B(t,"positionId","positionName","selectPositionList")},B=(t,e,u,i)=>{t[i]&&t[i].length>0?(s.value[e]=t[i][0].bizId,s.value[u]=t[i][0].name):(s.value[e]="",s.value[u]="")},V=async()=>{await A.value.validate(),p.value=!0,Y.addUserOrgBind(s.value).then(t=>{p.value=!1,z.success(t.message),f(!1),y("done")}).catch(()=>{p.value=!1})};return(t,e)=>{const u=P,i=$,v=j,F=q("vxe-switch"),C=D,h=E,k=H,L=G;return S(),w(L,{width:600,maskClosable:!1,visible:O.visible,"confirm-loading":p.value,forceRender:!0,title:"\u65B0\u589E\u673A\u6784","body-style":{paddingBottom:"8px"},"onUpdate:visible":f,onOk:V,onClose:e[7]||(e[7]=o=>f(!1))},{default:n(()=>[a(h,{ref_key:"formRef",ref:A,model:s.value,labelCol:{span:4},rules:N},{default:n(()=>[a(C,{gutter:16},{default:n(()=>[a(v,{span:24},{default:n(()=>[a(i,{label:"\u673A\u6784",name:"orgName"},{default:n(()=>[a(u,{value:s.value.orgName,"onUpdate:value":e[0]||(e[0]=o=>s.value.orgName=o),placeholder:"\u8BF7\u9009\u62E9\u673A\u6784",onFocus:e[1]||(e[1]=o=>R("dept"))},null,8,["value"])]),_:1})]),_:1}),a(v,{span:24},{default:n(()=>[a(i,{label:"\u804C\u52A1",name:"positionName"},{default:n(()=>[a(u,{value:s.value.positionName,"onUpdate:value":e[2]||(e[2]=o=>s.value.positionName=o),placeholder:"\u8BF7\u9009\u62E9\u804C\u4F4D",onFocus:e[3]||(e[3]=o=>R("position"))},null,8,["value"])]),_:1})]),_:1}),a(v,{span:12},{default:n(()=>[a(i,{label:"\u4E3B\u8981\u90E8\u95E8:",name:"mainFlag",labelCol:{span:8}},{default:n(()=>[a(F,{modelValue:s.value.mainFlag,"onUpdate:modelValue":e[4]||(e[4]=o=>s.value.mainFlag=o),"open-value":"Y","close-value":"N"},null,8,["modelValue"])]),_:1})]),_:1}),a(v,{span:12},{default:n(()=>[a(i,{label:"\u662F\u5426\u542F\u7528",name:"statusFlag",labelCol:{span:8}},{default:n(()=>[a(F,{modelValue:s.value.statusFlag,"onUpdate:modelValue":e[5]||(e[5]=o=>s.value.statusFlag=o),"open-value":1,"close-value":2},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),g.value?(S(),w(k,{key:0,visible:g.value,"onUpdate:visible":e[6]||(e[6]=o=>g.value=o),title:c.value,data:_.value,showTab:m.value,onDone:I},null,8,["visible","title","data","showTab"])):M("",!0)]),_:1},8,["visible","confirm-loading"])}}},W=Object.freeze(Object.defineProperty({__proto__:null,default:J},Symbol.toStringTag,{value:"Module"}));export{Y as E,J as _,W as a};
