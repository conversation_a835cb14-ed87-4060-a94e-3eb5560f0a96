package cn.stylefeng.roses.ent.saas.modular.auth.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 租户和功能包的关联
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@TableName("ent_tenant_link")
@Data
@EqualsAndHashCode(callSuper = true)
public class TenantLink extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "tenant_link_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键id")
    private Long tenantLinkId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 功能包id
     */
    @TableField("package_id")
    @ChineseDescription("功能包id")
    private Long packageId;

    /**
     * 服务结束时间，空则是长期
     */
    @TableField("service_end_time")
    @ChineseDescription("服务结束时间，空则是长期")
    private Date serviceEndTime;

    /**
     * 是否为试用：Y-试用，N-非试用
     */
    @TableField("trial_flag")
    @ChineseDescription("是否为试用：Y-试用，N-非试用")
    private String trialFlag;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;

}
