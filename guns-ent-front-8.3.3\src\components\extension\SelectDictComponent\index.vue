<template>
  <div class="wh100">
    <a-input
      v-model:value="dataValueName"
      :disabled="props.readonly || props.disabled"
      class="w-full"
      :placeholder="placeholder"
      @focus="inputClick"
    />

    <a-form-item-rest>
      <!-- 选择组件 -->
      <Selection
        v-model:visible="visibleSelection"
        v-if="visibleSelection"
        :data="selectedData"
        :showTab="['dict']"
        :changeHeight="true"
        title="请选择字典"
        :isRadio="isRadio"
        @done="closeSelection"
      />
    </a-form-item-rest>
  </div>
</template>

<script setup name="SelectDictComponent">
import { ref, onMounted, watch } from 'vue';
import { CommonApi } from '@/api/CommonApi';

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  record: {
    type: Object,
    default: {}
  },
  //是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  },
  // ref
  formRef: {
    type: Object,
    default: null
  },
  // 是否正常保存，不是转json格式
  normal: {
    type: Boolean,
    default: false
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:value', 'onChange']);

// 是否单选
const isRadio = ref(true);

// 是否自己改变值
const isOwnChange = ref(false);

// 选中的值
const dataValue = ref([]);
// 名称
const dataValueName = ref('');

// 是否显示弹框
const visibleSelection = ref(false);

// 选择总数据
const selectedData = ref({
  selectDictList: []
});

onMounted(() => {
  if (props.record?.itemMultipleChoiceFlag || props.multiple) {
    isRadio.value = false;
  } else {
    isRadio.value = true;
  }
  setDataValue();
});

// 设置值
const setDataValue = async () => {
  if (props.value) {
    if (isRadio.value) {
      if (isOwnChange.value) return;
      const res = await CommonApi.getDictName({ dictId: props.value });
      if (res.data) {
        dataValue.value = [{ id: props.value, name: res.data }];
      }
    } else {
      dataValue.value = props.normal ? dataValue.value : JSON.parse(props.value);
    }
  } else {
    dataValue.value = [];
  }
  isOwnChange.value = false;
  dataValueName.value = dataValue.value.map(item => item.name).join('；');
};

// 输入框点击
const inputClick = () => {
  if (dataValue.value?.length) {
    selectedData.value.selectDictList = dataValue.value?.map(item => {
      return { bizId: item.id, name: item.name };
    });
  } else {
    selectedData.value.selectDictList = [];
  }

  visibleSelection.value = true;
};

// 关闭弹框
const closeSelection = data => {
  dataValue.value = data.selectDictList?.map(item => {
    return { id: item.bizId, name: item.name };
  });
  dataValueName.value = data.selectDictList.map(item => item.name).join('；');
  isOwnChange.value = true;
  dataValueChange();
};

// 更改值
const dataValueChange = () => {
  let valueData =
    dataValue.value.length > 0
      ? isRadio.value
        ? dataValue.value[0].id
        : props.normal
          ? dataValue.value
          : JSON.stringify(dataValue.value)
      : '';
  emits('update:value', valueData);
  emits('onChange', props.record);
  checkField();
};

// 校验必填
const checkField = async () => {
  if (!props.normal && props.formRef?.validateFields) {
    await props.formRef.validateFields([props.record.fieldCode]);
  }
};

watch(
  () => props.value,
  val => {
    setDataValue();
  },
  { deep: true }
);
</script>

<style></style>
