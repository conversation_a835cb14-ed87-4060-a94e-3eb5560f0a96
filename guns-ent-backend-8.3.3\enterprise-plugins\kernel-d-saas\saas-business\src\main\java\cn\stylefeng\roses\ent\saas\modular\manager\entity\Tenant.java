package cn.stylefeng.roses.ent.saas.modular.manager.entity;

import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink;
import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseExpandFieldEntity;
import cn.stylefeng.roses.kernel.file.api.format.FileUrlFormatProcess;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.annotation.SimpleFieldFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 租户信息实例类
 *
 * <AUTHOR>
 * @date 2023/08/30 17:04
 */
@TableName(value = "ent_tenant", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class Tenant extends BaseExpandFieldEntity {

    /**
     * 主键id
     */
    @TableId(value = "tenant_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键id")
    private Long tenantId;

    /**
     * 租户唯一标识
     */
    @TableField("tenant_code")
    @ChineseDescription("租户唯一标识")
    private String tenantCode;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    @ChineseDescription("租户名称")
    private String tenantName;

    /**
     * 租户logo，存储文件id
     */
    @TableField("tenant_logo")
    @ChineseDescription("租户logo，存储文件id")
    @SimpleFieldFormat(processClass = FileUrlFormatProcess.class)
    private Long tenantLogo;

    /**
     * 公司名称
     */
    @TableField("company_name")
    @ChineseDescription("公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @TableField("company_address")
    @ChineseDescription("公司地址")
    private String companyAddress;

    /**
     * 统一社会信用代码
     */
    @TableField("company_social_code")
    @ChineseDescription("统一社会信用代码")
    private String companySocialCode;

    /**
     * 注册邮箱
     */
    @TableField("email")
    @ChineseDescription("注册邮箱")
    private String email;

    /**
     * 安全手机（注册时的手机号）
     */
    @TableField("safe_phone")
    @ChineseDescription("安全手机（注册时的手机号）")
    private String safePhone;

    /**
     * 状态：1-启用，2-禁用
     */
    @TableField("status_flag")
    @ChineseDescription("状态：1-启用，2-禁用")
    private Integer statusFlag;

    /**
     * 激活状态：Y-已激活，N-未激活
     */
    @TableField("active_flag")
    @ChineseDescription("激活状态：Y-已激活，N-未激活")
    private String activeFlag;

    /**
     * 租户开通时间
     */
    @TableField("active_date")
    @ChineseDescription("租户开通时间")
    private Date activeDate;

    /**
     * 租户到期时间
     */
    @TableField("expire_date")
    @ChineseDescription("租户到期时间")
    private Date expireDate;

    /**
     * 租户注册时候的超级密码
     */
    @TableField("password")
    @ChineseDescription("租户注册时候的超级密码")
    private String password;

    /**
     * 租户注册时候的超级密码盐
     */
    @TableField("password_salt")
    @ChineseDescription("租户注册时候的超级密码盐")
    private String passwordSalt;

    //-------------------------------非实体字段-------------------------------
    //-------------------------------非实体字段-------------------------------
    //-------------------------------非实体字段-------------------------------

    /**
     * 租户下的开通用户数量
     */
    @TableField(exist = false)
    private Long tenantUserCount;

    /**
     * 租户下的开通的组织机构数量
     */
    @TableField(exist = false)
    private Long tenantOrgCount;

    /**
     * 租户绑定的功能包
     */
    @TableField(exist = false)
    private List<TenantLink> tenantLinkList;

}
