package cn.stylefeng.guns.modular.erp;

import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import org.springframework.web.bind.annotation.RestController;

/**
 * ERP模块临时测试控制器
 *
 * <AUTHOR>
 * @since 2025/07/17 21:30
 */
@RestController
@ApiResource(name = "ERP模块临时测试")
public class ErpTestController {

    /**
     * 测试ERP模块是否正常工作
     *
     * <AUTHOR>
     * @since 2025/07/17 21:30
     */
    @GetResource(name = "测试ERP模块", path = "/erp/test1", requiredLogin = false)
    public ResponseData<String> testErpModule() {
        return new SuccessResponseData<>("ERP模块正常工作！");
    }

    /**
     * 获取ERP模块版本信息
     *
     * <AUTHOR>
     * @since 2025/07/17 21:30
     */
    @GetResource(name = "获取ERP模块版本", path = "/erp/version1", requiredLogin = false)
    public ResponseData<String> getErpVersion() {
        return new SuccessResponseData<>("ERP模块版本：8.3.3");
    }

}
