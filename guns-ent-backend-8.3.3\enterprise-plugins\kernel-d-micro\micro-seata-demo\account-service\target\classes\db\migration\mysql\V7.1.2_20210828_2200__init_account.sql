CREATE TABLE `sys_config`  (
  `config_id` bigint(20) NOT NULL COMMENT '主键',
  `config_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `config_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '属性编码',
  `config_value` varchar(3500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '属性值',
  `sys_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Y' COMMENT '是否是系统参数：Y-是，N-否',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `status_flag` tinyint(4) NULL DEFAULT 1 COMMENT '状态：1-正常，2-停用',
  `group_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '常量所属分类的编码，来自于“常量的分类”字典',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`config_id`) USING BTREE,
  UNIQUE INDEX `code_unique`(`config_code`) USING BTREE COMMENT '配置编码唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '参数配置' ROW_FORMAT = Dynamic;

INSERT INTO `sys_config` VALUES (33, 'Druid监控界面的url映射', 'SYS_DRUID_URL_MAPPINGS', '/druid/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (34, 'Druid控制台账号', 'SYS_DRUID_ACCOUNT', 'admin', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (35, 'Druid控制台账号密码', 'SYS_DRUID_PASSWORD', '123456', 'Y', '默认是空串，为空会让程序自动创建一个随机密码', 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (36, 'Druid控制台的监控数据是否可以重置清零', 'SYS_DRUID_RESET_ENABLE', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (37, 'druid web url统计的拦截范围', 'SYS_DRUID_WEB_STAT_FILTER_URL_PATTERN', '/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (38, 'druid web url统计的排除拦截表达式', 'SYS_DRUID_WEB_STAT_FILTER_EXCLUSIONS', '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (39, 'druid web url统计的session统计开关', 'SYS_DRUID_WEB_STAT_FILTER_SESSION_STAT_ENABLE', 'false', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (40, 'druid web url统计的session名称', 'SYS_DRUID_WEB_STAT_FILTER_PRINCIPAL_SESSION_NAME', 'Authorization', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (41, 'druid web url统计的session最大监控数', 'SYS_DRUID_WEB_STAT_FILTER_SESSION_STAT_MAX_COUNT', '1000', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (42, 'druid web url统计的cookie名称', 'SYS_DRUID_WEB_STAT_FILTER_PRINCIPAL_COOKIE_NAME', 'Authorization', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);
INSERT INTO `sys_config` VALUES (43, 'druid web url统计的是否开启监控单个url调用的sql列表', 'SYS_DRUID_WEB_STAT_FILTER_PROFILE_ENABLE', 'true', 'Y', NULL, 1, 'sys_config', 'N', NULL, NULL, NULL, NULL);

CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) NOT NULL,
  `context` varchar(128) NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime NOT NULL,
  `log_modified` datetime NOT NULL,
  `ext` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `account_tbl`;
CREATE TABLE `account_tbl` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` varchar(255) DEFAULT NULL COMMENT '用户id',
  `money` int(11) DEFAULT '0' COMMENT '用户余额',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `account_tbl`(`id`, `user_id`, `money`) VALUES (1, 'U100001', 10000);
