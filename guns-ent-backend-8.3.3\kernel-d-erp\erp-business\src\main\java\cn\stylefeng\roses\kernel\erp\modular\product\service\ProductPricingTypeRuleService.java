package cn.stylefeng.roses.kernel.erp.modular.product.service;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse;

import java.math.BigDecimal;

/**
 * 商品计价类型业务规则服务接口
 *
 * <AUTHOR>
 * @since 2025/07/27 23:05
 */
public interface ProductPricingTypeRuleService {

    /**
     * 验证计价类型是否有效
     *
     * @param pricingType 计价类型
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    boolean validatePricingType(String pricingType);

    /**
     * 验证商品价格设置是否符合计价类型要求
     *
     * @param product 商品信息
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    boolean validateProductPricing(ErpProduct product);

    /**
     * 验证计价类型变更的影响
     *
     * @param currentProduct 当前商品信息
     * @param newPricingType 新的计价类型
     * @return 变更影响验证结果
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    PricingTypeChangeValidationResponse validatePricingTypeChange(ErpProduct currentProduct, String newPricingType);

    /**
     * 获取计价类型的描述信息
     *
     * @param pricingType 计价类型
     * @return 描述信息
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    String getPricingTypeDescription(String pricingType);

    /**
     * 检查计价类型是否需要零售价格
     *
     * @param pricingType 计价类型
     * @return 是否需要零售价格
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    boolean needRetailPrice(String pricingType);

    /**
     * 检查计价类型是否需要单位价格
     *
     * @param pricingType 计价类型
     * @return 是否需要单位价格
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    boolean needUnitPrice(String pricingType);

    /**
     * 检查计价类型是否需要单份价格
     *
     * @param pricingType 计价类型
     * @return 是否需要单份价格
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    boolean needPiecePrice(String pricingType);

    /**
     * 检查计价类型是否允许设置参考价格
     *
     * @param pricingType 计价类型
     * @return 是否允许设置参考价格
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    boolean allowReferencePrice(String pricingType);

    /**
     * 验证商品计价类型相关参数的完整性
     *
     * @param product 商品信息
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    void validateProductPricingTypeParams(ErpProduct product);

    /**
     * 计算商品的显示价格（根据计价类型）
     *
     * @param product 商品信息
     * @return 显示价格
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    BigDecimal calculateDisplayPrice(ErpProduct product);

    /**
     * 获取计价类型的价格标签
     *
     * @param pricingType 计价类型
     * @return 价格标签
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    String getPriceLabel(String pricingType);

}