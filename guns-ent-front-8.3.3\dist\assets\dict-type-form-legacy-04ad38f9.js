System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js"],(function(e,l){"use strict";var a,t,d,u,r,s,o,m,f,i,c,p,n,y,_,g;return{setters:[e=>{a=e.s,t=e.a,d=e.f,u=e.w,r=e.d,s=e.g,o=e.h,m=e.l,f=e.u,i=e.v,c=e.z,p=e.A,n=e.y,y=e.$,_=e.G,g=e.H},null],execute:function(){e("default",{__name:"dict-type-form",props:{form:Object,isUpdate:Boolean},setup(e){const l=e,v=a({dictTypeName:[{required:!0,message:"请输入字典类型名称",type:"string",trigger:"blur"}],dictTypeCode:[{required:!0,message:"请输入字典类型编码",type:"string",trigger:"blur"}],dictTypeSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}],dictTypeClass:[{required:!0,message:"请选择字典类型",type:"number",trigger:"change"}],statusFlag:[{required:!0,message:"请选择字典类型状态",type:"number",trigger:"change"}]});return(a,T)=>{const b=m,C=f,h=i,x=c,U=p,w=n,q=y,S=_,B=g;return t(),d(B,{ref:"formRef",model:e.form,rules:v,layout:"vertical"},{default:u((()=>[r(S,{gutter:20},{default:u((()=>[r(h,{xs:24,sm:24,md:12},{default:u((()=>[r(C,{label:"字典类型名称:",name:"dictTypeName"},{default:u((()=>[r(b,{value:e.form.dictTypeName,"onUpdate:value":T[0]||(T[0]=l=>e.form.dictTypeName=l),"allow-clear":"",placeholder:"请输入字典类型名称"},null,8,["value"])])),_:1})])),_:1}),r(h,{xs:24,sm:24,md:12},{default:u((()=>[r(C,{label:"字典类型编码:",name:"dictTypeCode"},{default:u((()=>[r(b,{value:e.form.dictTypeCode,"onUpdate:value":T[1]||(T[1]=l=>e.form.dictTypeCode=l),"allow-clear":"",placeholder:"请输入字典类型编码",disabled:l.isUpdate},null,8,["value","disabled"])])),_:1})])),_:1}),r(h,{xs:24,sm:24,md:12},{default:u((()=>[r(C,{label:"字典类型:",name:"dictTypeClass"},{default:u((()=>[r(U,{value:e.form.dictTypeClass,"onUpdate:value":T[2]||(T[2]=l=>e.form.dictTypeClass=l)},{default:u((()=>[r(x,{value:1},{default:u((()=>T[7]||(T[7]=[s("系统类型")]))),_:1,__:[7]}),r(x,{value:2},{default:u((()=>T[8]||(T[8]=[s("业务类型")]))),_:1,__:[8]})])),_:1},8,["value"])])),_:1})])),_:1}),r(h,{xs:24,sm:24,md:12},{default:u((()=>[r(C,{label:"字典类型状态:",name:"statusFlag"},{default:u((()=>[r(U,{value:e.form.statusFlag,"onUpdate:value":T[3]||(T[3]=l=>e.form.statusFlag=l)},{default:u((()=>[r(x,{value:1},{default:u((()=>T[9]||(T[9]=[s("启用")]))),_:1,__:[9]}),r(x,{value:2},{default:u((()=>T[10]||(T[10]=[s("禁用")]))),_:1,__:[10]})])),_:1},8,["value"])])),_:1})])),_:1}),r(h,{xs:24,sm:24,md:12},{default:u((()=>[r(C,{label:"排序:",name:"dictTypeSort"},{default:u((()=>[r(w,{value:e.form.dictTypeSort,"onUpdate:value":T[4]||(T[4]=l=>e.form.dictTypeSort=l),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),2==e.form.dictTypeClass?(t(),d(h,{key:0,xs:24,sm:24,md:12},{default:u((()=>[r(C,{label:"业务编码:",name:"dictTypeBusCode"},{default:u((()=>[r(b,{value:e.form.dictTypeBusCode,"onUpdate:value":T[5]||(T[5]=l=>e.form.dictTypeBusCode=l),"allow-clear":"",placeholder:"请输入业务编码"},null,8,["value"])])),_:1})])),_:1})):o("",!0),r(h,{span:24},{default:u((()=>[r(C,{label:"备注"},{default:u((()=>[r(q,{value:e.form.dictTypeDesc,"onUpdate:value":T[6]||(T[6]=l=>e.form.dictTypeDesc=l),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
