package cn.stylefeng.roses.kernel.websocket.redis.action;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;
import cn.stylefeng.roses.kernel.websocket.api.utils.WebSocketUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 发送用户消息的Action执行
 *
 * <AUTHOR>
 * @since 2024-01-15 15:51
 */
public class SendMessageAction implements Action {

    @Override
    public void doMessage(WebSocketManagerApi manager, JSONObject object) {
        if (!object.containsKey(USER_ID)) {
            return;
        }
        if (!object.containsKey(MESSAGE)) {
            return;
        }

        String userId = object.getString(USER_ID);

        List<WebSocketDTO> list = manager.getList(userId);
        if (ObjectUtil.isEmpty(list)) {
            return;
        }

        for (WebSocketDTO webSocket : list) {
            WebSocketUtil.sendMessageAsync(webSocket.getSession(), object.getString(MESSAGE));
        }
    }

}
