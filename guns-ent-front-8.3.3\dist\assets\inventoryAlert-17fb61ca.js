class l{static getAlertTypeOptions(){return[{label:"\u5E93\u5B58\u4E0D\u8DB3",value:"LOW_STOCK",color:"orange"},{label:"\u96F6\u5E93\u5B58",value:"ZERO_STOCK",color:"red"},{label:"\u5E93\u5B58\u79EF\u538B",value:"OVERSTOCK",color:"blue"},{label:"\u4E34\u671F\u9884\u8B66",value:"EXPIRY",color:"purple"}]}static getAlertLevelOptions(){return[{label:"\u7D27\u6025",value:"CRITICAL",color:"red"},{label:"\u8B66\u544A",value:"WARNING",color:"orange"},{label:"\u63D0\u9192",value:"INFO",color:"blue"}]}static getTargetTypeOptions(){return[{label:"\u5355\u4E2A\u5546\u54C1",value:"PRODUCT"},{label:"\u5546\u54C1\u5206\u7C7B",value:"CATEGORY"},{label:"\u5168\u90E8\u5546\u54C1",value:"ALL"}]}static getThresholdTypeOptions(){return[{label:"\u6570\u91CF",value:"QUANTITY"},{label:"\u767E\u5206\u6BD4",value:"PERCENTAGE"},{label:"\u5929\u6570",value:"DAYS"}]}static getComparisonOperatorOptions(){return[{label:"\u5C0F\u4E8E\u7B49\u4E8E",value:"LTE"},{label:"\u5C0F\u4E8E",value:"LT"},{label:"\u5927\u4E8E\u7B49\u4E8E",value:"GTE"},{label:"\u5927\u4E8E",value:"GT"},{label:"\u7B49\u4E8E",value:"EQ"}]}static getNotificationMethodOptions(){return[{label:"\u7CFB\u7EDF\u901A\u77E5",value:"SYSTEM"},{label:"\u90AE\u4EF6\u901A\u77E5",value:"EMAIL"},{label:"\u77ED\u4FE1\u901A\u77E5",value:"SMS"},{label:"\u5FAE\u4FE1\u901A\u77E5",value:"WECHAT"}]}static getStatusOptions(){return[{label:"\u5F85\u5904\u7406",value:"PENDING",color:"orange"},{label:"\u5904\u7406\u4E2D",value:"PROCESSING",color:"blue"},{label:"\u5DF2\u89E3\u51B3",value:"RESOLVED",color:"green"},{label:"\u5DF2\u5FFD\u7565",value:"IGNORED",color:"gray"}]}static getAlertTypeName(e){const t=l.getAlertTypeOptions().find(o=>o.value===e);return t?t.label:e}static getAlertTypeInfo(e){const t=l.getAlertTypeOptions().find(o=>o.value===e);return t?{name:t.label,color:t.color}:{name:e,color:"default"}}static getAlertLevelInfo(e){const t=l.getAlertLevelOptions().find(o=>o.value===e);return t?{name:t.label,color:t.color}:{name:e,color:"default"}}static getStatusInfo(e){const t=l.getStatusOptions().find(o=>o.value===e);return t?{name:t.label,color:t.color}:{name:e,color:"default"}}static getTargetTypeName(e){const t=l.getTargetTypeOptions().find(o=>o.value===e);return t?t.label:e}static getThresholdTypeName(e){const t=l.getThresholdTypeOptions().find(o=>o.value===e);return t?t.label:e}static getComparisonOperatorName(e){const t=l.getComparisonOperatorOptions().find(o=>o.value===e);return t?t.label:e}static formatThresholdValue(e,a){if(e==null)return"-";switch(a){case"PERCENTAGE":return"".concat(e,"%");case"DAYS":return"".concat(e,"\u5929");case"QUANTITY":default:return e.toString()}}static getAlertLevelIcon(e){switch(e){case"CRITICAL":return"exclamation-circle";case"WARNING":return"warning";case"INFO":return"info-circle";default:return"question-circle"}}static getAlertTypeIcon(e){switch(e){case"LOW_STOCK":return"arrow-down";case"ZERO_STOCK":return"stop";case"OVERSTOCK":return"arrow-up";case"EXPIRY":return"clock-circle";default:return"alert"}}static validateRuleConfig(e){const a=[];return(e.targetType==="PRODUCT"||e.targetType==="CATEGORY")&&!e.targetId&&a.push("\u8BF7\u9009\u62E9\u76EE\u6807\u5BF9\u8C61"),(e.thresholdValue==null||e.thresholdValue<0)&&a.push("\u9608\u503C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E0"),e.ruleType==="EXPIRY"&&e.thresholdType!=="DAYS"&&a.push("\u4E34\u671F\u9884\u8B66\u7684\u9608\u503C\u7C7B\u578B\u5FC5\u987B\u662F\u5929\u6570"),(e.checkFrequency==null||e.checkFrequency<1||e.checkFrequency>1440)&&a.push("\u68C0\u67E5\u9891\u7387\u5FC5\u987B\u57281-1440\u5206\u949F\u4E4B\u95F4"),a}static generateRuleDescription(e){const a=l.getAlertTypeName(e.ruleType),t=l.getTargetTypeName(e.targetType),o=l.formatThresholdValue(e.thresholdValue,e.thresholdType),r=l.getComparisonOperatorName(e.comparisonOperator);return"".concat(t,"\u7684").concat(a,"\u9884\u8B66\uFF0C\u5F53\u5E93\u5B58").concat(r).concat(o,"\u65F6\u89E6\u53D1")}}export{l as I};
