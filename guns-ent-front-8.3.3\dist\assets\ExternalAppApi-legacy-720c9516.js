System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var a;return{setters:[t=>{a=t.R}],execute:function(){t("E",class{static findPage(t){return a.getAndLoadData("/apiClient/page",t)}static add(t){return a.post("/apiClient/add",t)}static edit(t){return a.post("/apiClient/edit",t)}static delete(t){return a.post("/apiClient/delete",t)}static batchDelete(t){return a.post("/apiClient/batchDelete",t)}static detail(t){return a.getAndLoadData("/apiClient/detail",t)}static list(t){return a.getAndLoadData("/apiClient/list",t)}static changeStatus(t){return a.post("/apiClient/changeStatus",t)}static randomRsaKey(t){return a.getAndLoadData("/apiClient/randomRsaKey",t)}})}}}));
