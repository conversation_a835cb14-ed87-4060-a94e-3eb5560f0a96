package cn.stylefeng.roses.kernel.conversion.api.exception.enums;

import cn.stylefeng.roses.kernel.conversion.api.constants.ConversionConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 文档转化异常
 *
 * <AUTHOR>
 * @date 2021/8/26 14:10
 */
@Getter
public enum ConversionExceptionEnum implements AbstractExceptionEnum {

    /**
     * 文档转化错误
     */
    CONVERSION_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ConversionConstants.CONVERSION_EXCEPTION_STEP_CODE + "01", "文档转化异常！");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ConversionExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
