<template>
  <div v-html="highlightedCode" class="code-highlight"></div>
</template>

<script setup name="GunsCode">
import Prism from 'prismjs';
// 引入CSS
import 'prismjs/themes/prism.css';
// 引入Prism核心功能与你需要高亮的语言
import 'prismjs/components/prism-core.min.js';
import 'prismjs/components/prism-clike.min.js';
import 'prismjs/components/prism-javascript.min.js';
import { computed } from 'vue';

const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'javascript'
  }
});

const highlightedCode = computed(() => {
  return Prism.highlight(props.code, Prism.languages[props.language], props.language);
});
</script>

<style lang="less" scoped>
.code-highlight {
  overflow-x: auto;
  white-space: pre;
  white-space: pre-wrap; /* CSS3 */
  white-space: -moz-pre-wrap; /* Firefox */
  white-space: -pre-wrap; /* Opera <7 */
  white-space: -o-pre-wrap; /* Opera 7 */
  word-wrap: break-word; /* IE */
  word-break: break-all; /* IE */
}
</style>
