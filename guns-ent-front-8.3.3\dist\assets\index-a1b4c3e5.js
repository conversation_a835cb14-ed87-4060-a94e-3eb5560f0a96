import{r as lt,o as le,aK as he,a as J,c as ht,aR as ue,aS as ce,b as fe,F as de,e as me,h as ne,d as xt,w as It,g as ve,f as ge,B as pe,a0 as be}from"./index-18a1ea24.js";/* empty css              */import{_ as we}from"./logo-102893b8.js";/*!
 * Viewer.js v1.10.4
 * https://fengyuanchen.github.io/viewerjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2022-02-13T08:40:00.127Z
 */function Nt(a,t){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);t&&(e=e.filter(function(n){return Object.getOwnPropertyDescriptor(a,n).enumerable})),i.push.apply(i,e)}return i}function Tt(a){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?Nt(Object(i),!0).forEach(function(e){Te(a,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):Nt(Object(i)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})}return a}function bt(a){"@babel/helpers - typeof";return bt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bt(a)}function ye(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function At(a,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,e.key,e)}}function Ee(a,t,i){return t&&At(a.prototype,t),i&&At(a,i),Object.defineProperty(a,"prototype",{writable:!1}),a}function Te(a,t,i){return t in a?Object.defineProperty(a,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[t]=i,a}var Ot={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},_e='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>',vt=typeof window<"u"&&typeof window.document<"u",F=vt?window:{},B=vt&&F.document.documentElement?"ontouchstart"in F.document.documentElement:!1,_t=vt?"PointerEvent"in F:!1,m="viewer",ut="move",ae="switch",G="zoom",at="".concat(m,"-active"),Se="".concat(m,"-close"),ct="".concat(m,"-fade"),wt="".concat(m,"-fixed"),De="".concat(m,"-fullscreen"),Ct="".concat(m,"-fullscreen-exit"),P="".concat(m,"-hide"),xe="".concat(m,"-hide-md-down"),Ie="".concat(m,"-hide-sm-down"),Ne="".concat(m,"-hide-xs-down"),R="".concat(m,"-in"),Q="".concat(m,"-invisible"),U="".concat(m,"-loading"),Ae="".concat(m,"-move"),Lt="".concat(m,"-open"),W="".concat(m,"-show"),_="".concat(m,"-transition"),j="click",yt="dblclick",kt="dragstart",zt="focusin",Rt="keydown",L="load",dt="error",Oe=B?"touchend touchcancel":"mouseup",Ce=B?"touchmove":"mousemove",Le=B?"touchstart":"mousedown",Vt=_t?"pointerdown":Le,Ft=_t?"pointermove":Ce,Pt=_t?"pointerup pointercancel":Oe,Mt="resize",C="transitionend",Yt="wheel",Wt="ready",Xt="show",Ht="shown",qt="hide",Bt="hidden",Ut="view",tt="viewed",jt="move",Kt="moved",Zt="rotate",Gt="rotated",$t="scale",Jt="scaled",Qt="zoom",te="zoomed",ee="play",ie="stop",mt="".concat(m,"Action"),St=/\s\s*/,rt=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function et(a){return typeof a=="string"}var ke=Number.isNaN||F.isNaN;function D(a){return typeof a=="number"&&!ke(a)}function H(a){return typeof a>"u"}function K(a){return bt(a)==="object"&&a!==null}var ze=Object.prototype.hasOwnProperty;function $(a){if(!K(a))return!1;try{var t=a.constructor,i=t.prototype;return t&&i&&ze.call(i,"isPrototypeOf")}catch(e){return!1}}function b(a){return typeof a=="function"}function w(a,t){if(a&&b(t))if(Array.isArray(a)||D(a.length)){var i=a.length,e;for(e=0;e<i&&t.call(a,a[e],e,a)!==!1;e+=1);}else K(a)&&Object.keys(a).forEach(function(n){t.call(a,a[n],n,a)});return a}var A=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return K(t)&&e.length>0&&e.forEach(function(r){K(r)&&Object.keys(r).forEach(function(s){t[s]=r[s]})}),t},Re=/^(?:width|height|left|top|marginLeft|marginTop)$/;function k(a,t){var i=a.style;w(t,function(e,n){Re.test(n)&&D(e)&&(e+="px"),i[n]=e})}function Ve(a){return et(a)?a.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):a}function X(a,t){return!a||!t?!1:a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function f(a,t){if(!(!a||!t)){if(D(a.length)){w(a,function(e){f(e,t)});return}if(a.classList){a.classList.add(t);return}var i=a.className.trim();i?i.indexOf(t)<0&&(a.className="".concat(i," ").concat(t)):a.className=t}}function v(a,t){if(!(!a||!t)){if(D(a.length)){w(a,function(i){v(i,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function it(a,t,i){if(t){if(D(a.length)){w(a,function(e){it(e,t,i)});return}i?f(a,t):v(a,t)}}var Fe=/([a-z\d])([A-Z])/g;function Dt(a){return a.replace(Fe,"$1-$2").toLowerCase()}function q(a,t){return K(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(Dt(t)))}function Et(a,t,i){K(i)?a[t]=i:a.dataset?a.dataset[t]=i:a.setAttribute("data-".concat(Dt(t)),i)}var re=function(){var a=!1;if(vt){var t=!1,i=function(){},e=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(r){t=r}});F.addEventListener("test",i,e),F.removeEventListener("test",i,e)}return a}();function y(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=i;t.trim().split(St).forEach(function(r){if(!re){var s=a.listeners;s&&s[r]&&s[r][i]&&(n=s[r][i],delete s[r][i],Object.keys(s[r]).length===0&&delete s[r],Object.keys(s).length===0&&delete a.listeners)}a.removeEventListener(r,n,e)})}function d(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=i;t.trim().split(St).forEach(function(r){if(e.once&&!re){var s=a.listeners,o=s===void 0?{}:s;n=function(){delete o[r][i],a.removeEventListener(r,n,e);for(var l=arguments.length,c=new Array(l),u=0;u<l;u++)c[u]=arguments[u];i.apply(a,c)},o[r]||(o[r]={}),o[r][i]&&a.removeEventListener(r,o[r][i],e),o[r][i]=n,a.listeners=o}a.addEventListener(r,n,e)})}function S(a,t,i,e){var n;return b(Event)&&b(CustomEvent)?n=new CustomEvent(t,Tt({bubbles:!0,cancelable:!0,detail:i},e)):(n=document.createEvent("CustomEvent"),n.initCustomEvent(t,!0,!0,i)),a.dispatchEvent(n)}function Pe(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}function ft(a){var t=a.rotate,i=a.scaleX,e=a.scaleY,n=a.translateX,r=a.translateY,s=[];D(n)&&n!==0&&s.push("translateX(".concat(n,"px)")),D(r)&&r!==0&&s.push("translateY(".concat(r,"px)")),D(t)&&t!==0&&s.push("rotate(".concat(t,"deg)")),D(i)&&i!==1&&s.push("scaleX(".concat(i,")")),D(e)&&e!==1&&s.push("scaleY(".concat(e,")"));var o=s.length?s.join(" "):"none";return{WebkitTransform:o,msTransform:o,transform:o}}function Me(a){return et(a)?decodeURIComponent(a.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}var pt=F.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(F.navigator.userAgent);function se(a,t,i){var e=document.createElement("img");if(a.naturalWidth&&!pt)return i(a.naturalWidth,a.naturalHeight),e;var n=document.body||document.documentElement;return e.onload=function(){i(e.width,e.height),pt||n.removeChild(e)},w(t.inheritedAttributes,function(r){var s=a.getAttribute(r);s!==null&&e.setAttribute(r,s)}),e.src=a.src,pt||(e.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",n.appendChild(e)),e}function st(a){switch(a){case 2:return Ne;case 3:return Ie;case 4:return xe;default:return""}}function Ye(a){var t=Tt({},a),i=[];return w(a,function(e,n){delete t[n],w(t,function(r){var s=Math.abs(e.startX-r.startX),o=Math.abs(e.startY-r.startY),h=Math.abs(e.endX-r.endX),l=Math.abs(e.endY-r.endY),c=Math.sqrt(s*s+o*o),u=Math.sqrt(h*h+l*l),g=(u-c)/c;i.push(g)})}),i.sort(function(e,n){return Math.abs(e)<Math.abs(n)}),i[0]}function ot(a,t){var i=a.pageX,e=a.pageY,n={endX:i,endY:e};return t?n:Tt({timeStamp:Date.now(),startX:i,startY:e},n)}function We(a){var t=0,i=0,e=0;return w(a,function(n){var r=n.startX,s=n.startY;t+=r,i+=s,e+=1}),t/=e,i/=e,{pageX:t,pageY:i}}var Xe={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var t=this.element.ownerDocument,i=t.body||t.documentElement;this.body=i,this.scrollbarWidth=window.innerWidth-t.documentElement.clientWidth,this.initialBodyPaddingRight=i.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(i).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var t=this.options,i=this.parent,e;t.inline&&(e={width:Math.max(i.offsetWidth,t.minWidth),height:Math.max(i.offsetHeight,t.minHeight)},this.parentData=e),(this.fulled||!e)&&(e=this.containerData),this.viewerData=A({},e)},renderViewer:function(){this.options.inline&&!this.fulled&&k(this.viewer,this.viewerData)},initList:function(){var t=this,i=this.element,e=this.options,n=this.list,r=[];n.innerHTML="",w(this.images,function(s,o){var h=s.src,l=s.alt||Me(h),c=t.getImageURL(s);if(h||c){var u=document.createElement("li"),g=document.createElement("img");w(e.inheritedAttributes,function(T){var p=s.getAttribute(T);p!==null&&g.setAttribute(T,p)}),g.src=h||c,g.alt=l,g.setAttribute("data-original-url",c||h),u.setAttribute("data-index",o),u.setAttribute("data-viewer-action","view"),u.setAttribute("role","button"),e.keyboard&&u.setAttribute("tabindex",0),u.appendChild(g),n.appendChild(u),r.push(u)}}),this.items=r,w(r,function(s){var o=s.firstElementChild,h,l;Et(o,"filled",!0),e.loading&&f(s,U),d(o,L,h=function(u){y(o,dt,l),e.loading&&v(s,U),t.loadImage(u)},{once:!0}),d(o,dt,l=function(){y(o,L,h),e.loading&&v(s,U)},{once:!0})}),e.transition&&d(i,tt,function(){f(n,_)},{once:!0})},renderList:function(){var t=this.index,i=this.items[t];if(i){var e=i.nextElementSibling,n=parseInt(window.getComputedStyle(e||i).marginLeft,10),r=i.offsetWidth,s=r+n;k(this.list,A({width:s*this.length-n},ft({translateX:(this.viewerData.width-r)/2-s*t})))}},resetList:function(){var t=this.list;t.innerHTML="",v(t,_),k(t,ft({translateX:0}))},initImage:function(t){var i=this,e=this.options,n=this.image,r=this.viewerData,s=this.footer.offsetHeight,o=r.width,h=Math.max(r.height-s,s),l=this.imageData||{},c;this.imageInitializing={abort:function(){c.onload=null}},c=se(n,e,function(u,g){var T=u/g,p=o,O=h;i.imageInitializing=!1,h*T>o?O=o/T:p=h*T,p=Math.min(p*.9,u),O=Math.min(O*.9,g);var N=(o-p)/2,I=(h-O)/2,x={left:N,top:I,x:N,y:I,width:p,height:O,oldRatio:1,ratio:p/u,aspectRatio:T,naturalWidth:u,naturalHeight:g},E=A({},x);e.rotatable&&(x.rotate=l.rotate||0,E.rotate=0),e.scalable&&(x.scaleX=l.scaleX||1,x.scaleY=l.scaleY||1,E.scaleX=1,E.scaleY=1),i.imageData=x,i.initialImageData=E,t&&t()})},renderImage:function(t){var i=this,e=this.image,n=this.imageData;if(k(e,A({width:n.width,height:n.height,marginLeft:n.x,marginTop:n.y},ft(n))),t)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&X(e,_)){var r=function(){i.imageRendering=!1,t()};this.imageRendering={abort:function(){y(e,C,r)}},d(e,C,r,{once:!0})}else t()},resetImage:function(){if(this.viewing||this.viewed){var t=this.image;this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null}}},He={bind:function(){var t=this.options,i=this.viewer,e=this.canvas,n=this.element.ownerDocument;d(i,j,this.onClick=this.click.bind(this)),d(i,kt,this.onDragStart=this.dragstart.bind(this)),d(e,Vt,this.onPointerDown=this.pointerdown.bind(this)),d(n,Ft,this.onPointerMove=this.pointermove.bind(this)),d(n,Pt,this.onPointerUp=this.pointerup.bind(this)),d(n,Rt,this.onKeyDown=this.keydown.bind(this)),d(window,Mt,this.onResize=this.resize.bind(this)),t.zoomable&&t.zoomOnWheel&&d(i,Yt,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleOnDblclick&&d(e,yt,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var t=this.options,i=this.viewer,e=this.canvas,n=this.element.ownerDocument;y(i,j,this.onClick),y(i,kt,this.onDragStart),y(e,Vt,this.onPointerDown),y(n,Ft,this.onPointerMove),y(n,Pt,this.onPointerUp),y(n,Rt,this.onKeyDown),y(window,Mt,this.onResize),t.zoomable&&t.zoomOnWheel&&y(i,Yt,this.onWheel,{passive:!1,capture:!0}),t.toggleOnDblclick&&y(e,yt,this.onDblclick)}},qe={click:function(t){var i=this.options,e=this.imageData,n=t.target,r=q(n,mt);switch(!r&&n.localName==="img"&&n.parentElement.localName==="li"&&(n=n.parentElement,r=q(n,mt)),B&&t.isTrusted&&n===this.canvas&&clearTimeout(this.clickCanvasTimeout),r){case"mix":this.played?this.stop():i.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.hide();break;case"view":this.view(q(n,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(i.loop);break;case"play":this.play(i.fullscreen);break;case"next":this.next(i.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-e.scaleX||-1);break;case"flip-vertical":this.scaleY(-e.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(t){t.preventDefault(),this.viewed&&t.target===this.image&&(B&&t.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(t.isTrusted?t:t.detail&&t.detail.originalEvent))},load:function(){var t=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var i=this.element,e=this.options,n=this.image,r=this.index,s=this.viewerData;v(n,Q),e.loading&&v(this.canvas,U),n.style.cssText="height:0;"+"margin-left:".concat(s.width/2,"px;")+"margin-top:".concat(s.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage(function(){it(n,Ae,e.movable),it(n,_,e.transition),t.renderImage(function(){t.viewed=!0,t.viewing=!1,b(e.viewed)&&d(i,tt,e.viewed,{once:!0}),S(i,tt,{originalImage:t.images[r],index:r,image:n},{cancelable:!1})})})},loadImage:function(t){var i=t.target,e=i.parentNode,n=e.offsetWidth||30,r=e.offsetHeight||50,s=!!q(i,"filled");se(i,this.options,function(o,h){var l=o/h,c=n,u=r;r*l>n?s?c=r*l:u=n/l:s?u=n/l:c=r*l,k(i,A({width:c,height:u},ft({translateX:(n-c)/2,translateY:(r-u)/2})))})},keydown:function(t){var i=this.options;if(i.keyboard){var e=t.keyCode||t.which||t.charCode;switch(e){case 13:this.viewer.contains(t.target)&&this.click(t);break}if(this.fulled)switch(e){case 27:this.played?this.stop():i.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.prev(i.loop);break;case 38:t.preventDefault(),this.zoom(i.zoomRatio,!0);break;case 39:this.next(i.loop);break;case 40:t.preventDefault(),this.zoom(-i.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle());break}}},dragstart:function(t){t.target.localName==="img"&&t.preventDefault()},pointerdown:function(t){var i=this.options,e=this.pointers,n=t.buttons,r=t.button;if(!(!this.viewed||this.showing||this.viewing||this.hiding||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(D(n)&&n!==1||D(r)&&r!==0||t.ctrlKey))){t.preventDefault(),t.changedTouches?w(t.changedTouches,function(o){e[o.identifier]=ot(o)}):e[t.pointerId||0]=ot(t);var s=i.movable?ut:!1;i.zoomOnTouch&&i.zoomable&&Object.keys(e).length>1?s=G:i.slideOnTouch&&(t.pointerType==="touch"||t.type==="touchstart")&&this.isSwitchable()&&(s=ae),i.transition&&(s===ut||s===G)&&v(this.image,_),this.action=s}},pointermove:function(t){var i=this.pointers,e=this.action;!this.viewed||!e||(t.preventDefault(),t.changedTouches?w(t.changedTouches,function(n){A(i[n.identifier]||{},ot(n,!0))}):A(i[t.pointerId||0]||{},ot(t,!0)),this.change(t))},pointerup:function(t){var i=this,e=this.options,n=this.action,r=this.pointers,s;t.changedTouches?w(t.changedTouches,function(o){s=r[o.identifier],delete r[o.identifier]}):(s=r[t.pointerId||0],delete r[t.pointerId||0]),n&&(t.preventDefault(),e.transition&&(n===ut||n===G)&&f(this.image,_),this.action=!1,B&&n!==G&&s&&Date.now()-s.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),e.toggleOnDblclick&&this.viewed&&t.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout(function(){S(i.image,yt,{originalEvent:t})},50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout(function(){i.imageClicked=!1},500)):(this.imageClicked=!1,e.backdrop&&e.backdrop!=="static"&&t.target===this.canvas&&(this.clickCanvasTimeout=setTimeout(function(){S(i.canvas,j,{originalEvent:t})},50)))))},resize:function(){var t=this;if(!(!this.isShown||this.hiding)&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage()}),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement)){this.stop();return}w(this.player.getElementsByTagName("img"),function(i){d(i,L,t.loadImage.bind(t),{once:!0}),S(i,L)})}},wheel:function(t){var i=this;if(this.viewed&&(t.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50);var e=Number(this.options.zoomRatio)||.1,n=1;t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,!0,t)}}},Be={show:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.element,e=this.options;if(e.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(t),this;if(b(e.show)&&d(i,Xt,e.show,{once:!0}),S(i,Xt)===!1||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n=this.viewer;if(v(n,P),n.setAttribute("role","dialog"),n.setAttribute("aria-labelledby",this.title.id),n.setAttribute("aria-modal",!0),n.removeAttribute("aria-hidden"),e.transition&&!t){var r=this.shown.bind(this);this.transitioning={abort:function(){y(n,C,r),v(n,R)}},f(n,_),n.initialOffsetWidth=n.offsetWidth,d(n,C,r,{once:!0}),f(n,R)}else f(n,R),this.shown();return this},hide:function(){var t=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.element,n=this.options;if(n.inline||this.hiding||!(this.isShown||this.showing))return this;if(b(n.hide)&&d(e,qt,n.hide,{once:!0}),S(e,qt)===!1)return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var r=this.viewer,s=this.image,o=function(){v(r,R),t.hidden()};if(n.transition&&!i){var h=function c(u){u&&u.target===r&&(y(r,C,c),t.hidden())},l=function(){X(r,_)?(d(r,C,h),v(r,R)):o()};this.transitioning={abort:function(){t.viewed&&X(s,_)?y(s,C,l):X(r,_)&&y(r,C,h)}},this.viewed&&X(s,_)?(d(s,C,l,{once:!0}),this.zoomTo(0,!1,null,!0)):l()}else o();return this},view:function(){var t=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.initialViewIndex;if(i=Number(i)||0,this.hiding||this.played||i<0||i>=this.length||this.viewed&&i===this.index)return this;if(!this.isShown)return this.index=i,this.show();this.viewing&&this.viewing.abort();var e=this.element,n=this.options,r=this.title,s=this.canvas,o=this.items[i],h=o.querySelector("img"),l=q(h,"originalUrl"),c=h.getAttribute("alt"),u=document.createElement("img");if(w(n.inheritedAttributes,function(N){var I=h.getAttribute(N);I!==null&&u.setAttribute(N,I)}),u.src=l,u.alt=c,b(n.view)&&d(e,Ut,n.view,{once:!0}),S(e,Ut,{originalImage:this.images[i],index:i,image:u})===!1||!this.isShown||this.hiding||this.played)return this;var g=this.items[this.index];g&&(v(g,at),g.removeAttribute("aria-selected")),f(o,at),o.setAttribute("aria-selected",!0),n.focus&&o.focus(),this.image=u,this.viewed=!1,this.index=i,this.imageData={},f(u,Q),n.loading&&f(s,U),s.innerHTML="",s.appendChild(u),this.renderList(),r.innerHTML="";var T=function(){var I=t.imageData,x=Array.isArray(n.title)?n.title[1]:n.title;r.innerHTML=Ve(b(x)?x.call(t,u,I):"".concat(c," (").concat(I.naturalWidth," \xD7 ").concat(I.naturalHeight,")"))},p,O;return d(e,tt,T,{once:!0}),this.viewing={abort:function(){y(e,tt,T),u.complete?t.imageRendering?t.imageRendering.abort():t.imageInitializing&&t.imageInitializing.abort():(u.src="",y(u,L,p),t.timeout&&clearTimeout(t.timeout))}},u.complete?this.load():(d(u,L,p=function(){y(u,dt,O),t.load()},{once:!0}),d(u,dt,O=function(){y(u,L,p),t.timeout&&(clearTimeout(t.timeout),t.timeout=!1),v(u,Q),n.loading&&v(t.canvas,U)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(function(){v(u,Q),t.timeout=!1},1e3)),this},prev:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.index-1;return i<0&&(i=t?this.length-1:0),this.view(i),this},next:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.length-1,e=this.index+1;return e>i&&(e=t?0:i),this.view(e),this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData;return this.moveTo(H(t)?t:e.x+Number(t),H(i)?i:e.y+Number(i)),this},moveTo:function(t){var i=this,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,r=this.element,s=this.options,o=this.imageData;if(t=Number(t),e=Number(e),this.viewed&&!this.played&&s.movable){var h=o.x,l=o.y,c=!1;if(D(t)?c=!0:t=h,D(e)?c=!0:e=l,c){if(b(s.move)&&d(r,jt,s.move,{once:!0}),S(r,jt,{x:t,y:e,oldX:h,oldY:l,originalEvent:n})===!1)return this;o.x=t,o.y=e,o.left=t,o.top=e,this.moving=!0,this.renderImage(function(){i.moving=!1,b(s.moved)&&d(r,Kt,s.moved,{once:!0}),S(r,Kt,{x:t,y:e,oldX:h,oldY:l,originalEvent:n},{cancelable:!1})})}}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo:function(t){var i=this,e=this.element,n=this.options,r=this.imageData;if(t=Number(t),D(t)&&this.viewed&&!this.played&&n.rotatable){var s=r.rotate;if(b(n.rotate)&&d(e,Zt,n.rotate,{once:!0}),S(e,Zt,{degree:t,oldDegree:s})===!1)return this;r.rotate=t,this.rotating=!0,this.renderImage(function(){i.rotating=!1,b(n.rotated)&&d(e,Gt,n.rotated,{once:!0}),S(e,Gt,{degree:t,oldDegree:s},{cancelable:!1})})}return this},scaleX:function(t){return this.scale(t,this.imageData.scaleY),this},scaleY:function(t){return this.scale(this.imageData.scaleX,t),this},scale:function(t){var i=this,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,n=this.element,r=this.options,s=this.imageData;if(t=Number(t),e=Number(e),this.viewed&&!this.played&&r.scalable){var o=s.scaleX,h=s.scaleY,l=!1;if(D(t)?l=!0:t=o,D(e)?l=!0:e=h,l){if(b(r.scale)&&d(n,$t,r.scale,{once:!0}),S(n,$t,{scaleX:t,scaleY:e,oldScaleX:o,oldScaleY:h})===!1)return this;s.scaleX=t,s.scaleY=e,this.scaling=!0,this.renderImage(function(){i.scaling=!1,b(r.scaled)&&d(n,Jt,r.scaled,{once:!0}),S(n,Jt,{scaleX:t,scaleY:e,oldScaleX:o,oldScaleY:h},{cancelable:!1})})}}return this},zoom:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,n=this.imageData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(n.width*t/n.naturalWidth,i,e),this},zoomTo:function(t){var i=this,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,s=this.element,o=this.options,h=this.pointers,l=this.imageData,c=l.x,u=l.y,g=l.width,T=l.height,p=l.naturalWidth,O=l.naturalHeight;if(t=Math.max(0,t),D(t)&&this.viewed&&!this.played&&(r||o.zoomable)){if(!r){var N=Math.max(.01,o.minZoomRatio),I=Math.min(100,o.maxZoomRatio);t=Math.min(Math.max(t,N),I)}if(n)switch(n.type){case"wheel":o.zoomRatio>=.055&&t>.95&&t<1.05&&(t=1);break;case"pointermove":case"touchmove":case"mousemove":t>.99&&t<1.01&&(t=1);break}var x=p*t,E=O*t,nt=x-g,M=E-T,z=l.ratio;if(b(o.zoom)&&d(s,Qt,o.zoom,{once:!0}),S(s,Qt,{ratio:t,oldRatio:z,originalEvent:n})===!1)return this;if(this.zooming=!0,n){var Y=Pe(this.viewer),Z=h&&Object.keys(h).length>0?We(h):{pageX:n.pageX,pageY:n.pageY};l.x-=nt*((Z.pageX-Y.left-c)/g),l.y-=M*((Z.pageY-Y.top-u)/T)}else l.x-=nt/2,l.y-=M/2;l.left=l.x,l.top=l.y,l.width=x,l.height=E,l.oldRatio=z,l.ratio=t,this.renderImage(function(){i.zooming=!1,b(o.zoomed)&&d(s,te,o.zoomed,{once:!0}),S(s,te,{ratio:t,oldRatio:z,originalEvent:n},{cancelable:!1})}),e&&this.tooltip()}return this},play:function(){var t=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(!this.isShown||this.played)return this;var e=this.element,n=this.options;if(b(n.play)&&d(e,ee,n.play,{once:!0}),S(e,ee)===!1)return this;var r=this.player,s=this.loadImage.bind(this),o=[],h=0,l=0;if(this.played=!0,this.onLoadWhenPlay=s,i&&this.requestFullscreen(i),f(r,W),w(this.items,function(u,g){var T=u.querySelector("img"),p=document.createElement("img");p.src=q(T,"originalUrl"),p.alt=T.getAttribute("alt"),p.referrerPolicy=T.referrerPolicy,h+=1,f(p,ct),it(p,_,n.transition),X(u,at)&&(f(p,R),l=g),o.push(p),d(p,L,s,{once:!0}),r.appendChild(p)}),D(n.interval)&&n.interval>0){var c=function u(){t.playing=setTimeout(function(){v(o[l],R),l+=1,l=l<h?l:0,f(o[l],R),u()},n.interval)};h>1&&c()}return this},stop:function(){var t=this;if(!this.played)return this;var i=this.element,e=this.options;if(b(e.stop)&&d(i,ie,e.stop,{once:!0}),S(i,ie)===!1)return this;var n=this.player;return this.played=!1,clearTimeout(this.playing),w(n.getElementsByTagName("img"),function(r){y(r,L,t.onLoadWhenPlay)}),v(n,W),n.innerHTML="",this.exitFullscreen(),this},full:function(){var t=this,i=this.options,e=this.viewer,n=this.image,r=this.list;return!this.isShown||this.played||this.fulled||!i.inline?this:(this.fulled=!0,this.open(),f(this.button,Ct),i.transition&&(v(r,_),this.viewed&&v(n,_)),f(e,wt),e.setAttribute("role","dialog"),e.setAttribute("aria-labelledby",this.title.id),e.setAttribute("aria-modal",!0),e.removeAttribute("style"),k(e,{zIndex:i.zIndex}),i.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=A({},this.containerData),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){f(n,_),f(r,_)},0)})}),this)},exit:function(){var t=this,i=this.options,e=this.viewer,n=this.image,r=this.list;return!this.isShown||this.played||!this.fulled||!i.inline?this:(this.fulled=!1,this.close(),v(this.button,Ct),i.transition&&(v(r,_),this.viewed&&v(n,_)),i.focus&&this.clearEnforceFocus(),e.removeAttribute("role"),e.removeAttribute("aria-labelledby"),e.removeAttribute("aria-modal"),v(e,wt),k(e,{zIndex:i.zIndexInline}),this.viewerData=A({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){f(n,_),f(r,_)},0)})}),this)},tooltip:function(){var t=this,i=this.options,e=this.tooltipBox,n=this.imageData;return!this.viewed||this.played||!i.tooltip?this:(e.textContent="".concat(Math.round(n.ratio*100),"%"),this.tooltipping?clearTimeout(this.tooltipping):i.transition?(this.fading&&S(e,C),f(e,W),f(e,ct),f(e,_),e.removeAttribute("aria-hidden"),e.initialOffsetWidth=e.offsetWidth,f(e,R)):(f(e,W),e.removeAttribute("aria-hidden")),this.tooltipping=setTimeout(function(){i.transition?(d(e,C,function(){v(e,W),v(e,ct),v(e,_),e.setAttribute("aria-hidden",!0),t.fading=!1},{once:!0}),v(e,R),t.fading=!0):(v(e,W),e.setAttribute("aria-hidden",!0)),t.tooltipping=!1},1e3),this)},toggle:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return this.imageData.ratio===1?this.zoomTo(this.imageData.oldRatio,!0,t):this.zoomTo(1,!0,t),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=A({},this.initialImageData),this.renderImage()),this},update:function(){var t=this,i=this.element,e=this.options,n=this.isImg;if(n&&!i.parentNode)return this.destroy();var r=[];if(w(n?[i]:i.querySelectorAll("img"),function(l){b(e.filter)?e.filter.call(t,l)&&r.push(l):t.getImageURL(l)&&r.push(l)}),!r.length)return this;if(this.images=r,this.length=r.length,this.ready){var s=[];if(w(this.items,function(l,c){var u=l.querySelector("img"),g=r[c];g&&u?(g.src!==u.src||g.alt!==u.alt)&&s.push(c):s.push(c)}),k(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var o=s.indexOf(this.index);if(o>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-o,this.length-1),0));else{var h=this.items[this.index];f(h,at),h.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var t=this.element,i=this.options;return t[m]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),i.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):i.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),i.inline||y(t,j,this.onStart),t[m]=void 0,this):this}},Ue={getImageURL:function(t){var i=this.options.url;return et(i)?i=t.getAttribute(i):b(i)?i=i.call(this,t):i="",i},enforceFocus:function(){var t=this;this.clearEnforceFocus(),d(document,zt,this.onFocusin=function(i){var e=t.viewer,n=i.target;n!==document&&n!==e&&!e.contains(n)&&(n.getAttribute("tabindex")===null||n.getAttribute("aria-modal")!=="true")&&e.focus()})},clearEnforceFocus:function(){this.onFocusin&&(y(document,zt,this.onFocusin),this.onFocusin=null)},open:function(){var t=this.body;f(t,Lt),t.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px")},close:function(){var t=this.body;v(t,Lt),t.style.paddingRight=this.initialBodyPaddingRight},shown:function(){var t=this.element,i=this.options,e=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,i.focus&&(e.focus(),this.enforceFocus()),b(i.shown)&&d(t,Ht,i.shown,{once:!0}),S(t,Ht)!==!1&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var t=this.element,i=this.options,e=this.viewer;i.fucus&&this.clearEnforceFocus(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),f(e,P),e.removeAttribute("role"),e.removeAttribute("aria-labelledby"),e.removeAttribute("aria-modal"),e.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(b(i.hidden)&&d(t,Bt,i.hidden,{once:!0}),S(t,Bt,null,{cancelable:!1}))},requestFullscreen:function(t){var i=this.element.ownerDocument;if(this.fulled&&!(i.fullscreenElement||i.webkitFullscreenElement||i.mozFullScreenElement||i.msFullscreenElement)){var e=i.documentElement;e.requestFullscreen?$(t)?e.requestFullscreen(t):e.requestFullscreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):e.mozRequestFullScreen?e.mozRequestFullScreen():e.msRequestFullscreen&&e.msRequestFullscreen()}},exitFullscreen:function(){var t=this.element.ownerDocument;this.fulled&&(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&(t.exitFullscreen?t.exitFullscreen():t.webkitExitFullscreen?t.webkitExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.msExitFullscreen&&t.msExitFullscreen())},change:function(t){var i=this.options,e=this.pointers,n=e[Object.keys(e)[0]];if(n){var r=n.endX-n.startX,s=n.endY-n.startY;switch(this.action){case ut:this.move(r,s,t);break;case G:this.zoom(Ye(e),!1,t);break;case ae:{this.action="switched";var o=Math.abs(r);o>1&&o>Math.abs(s)&&(this.pointers={},r>1?this.prev(i.loop):r<-1&&this.next(i.loop));break}}w(e,function(h){h.startX=h.endX,h.startY=h.endY})}},isSwitchable:function(){var t=this.imageData,i=this.viewerData;return this.length>1&&t.x>=0&&t.y>=0&&t.width<=i.width&&t.height<=i.height}},je=F.Viewer,Ke=function(a){return function(){return a+=1,a}}(-1),oe=function(){function a(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ye(this,a),!t||t.nodeType!==1)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=A({},Ot,$(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.id=Ke(),this.init()}return Ee(a,[{key:"init",value:function(){var i=this,e=this.element,n=this.options;if(!e[m]){e[m]=this,n.focus&&!n.keyboard&&(n.focus=!1);var r=e.localName==="img",s=[];if(w(r?[e]:e.querySelectorAll("img"),function(l){b(n.filter)?n.filter.call(i,l)&&s.push(l):i.getImageURL(l)&&s.push(l)}),this.isImg=r,this.length=s.length,this.images=s,this.initBody(),H(document.createElement(m).style.transition)&&(n.transition=!1),n.inline){var o=0,h=function(){if(o+=1,o===i.length){var c;i.initializing=!1,i.delaying={abort:function(){clearTimeout(c)}},c=setTimeout(function(){i.delaying=!1,i.build()},0)}};this.initializing={abort:function(){w(s,function(c){c.complete||y(c,L,h)})}},w(s,function(l){l.complete?h():d(l,L,h,{once:!0})})}else d(e,j,this.onStart=function(l){var c=l.target;c.localName==="img"&&(!b(n.filter)||n.filter.call(i,c))&&i.view(i.images.indexOf(c))})}}},{key:"build",value:function(){if(!this.ready){var i=this.element,e=this.options,n=i.parentNode,r=document.createElement("div");r.innerHTML=_e;var s=r.querySelector(".".concat(m,"-container")),o=s.querySelector(".".concat(m,"-title")),h=s.querySelector(".".concat(m,"-toolbar")),l=s.querySelector(".".concat(m,"-navbar")),c=s.querySelector(".".concat(m,"-button")),u=s.querySelector(".".concat(m,"-canvas"));if(this.parent=n,this.viewer=s,this.title=o,this.toolbar=h,this.navbar=l,this.button=c,this.canvas=u,this.footer=s.querySelector(".".concat(m,"-footer")),this.tooltipBox=s.querySelector(".".concat(m,"-tooltip")),this.player=s.querySelector(".".concat(m,"-player")),this.list=s.querySelector(".".concat(m,"-list")),s.id="".concat(m).concat(this.id),o.id="".concat(m,"Title").concat(this.id),f(o,e.title?st(Array.isArray(e.title)?e.title[0]:e.title):P),f(l,e.navbar?st(e.navbar):P),it(c,P,!e.button),e.keyboard&&c.setAttribute("tabindex",0),e.backdrop&&(f(s,"".concat(m,"-backdrop")),!e.inline&&e.backdrop!=="static"&&Et(u,mt,"hide")),et(e.className)&&e.className&&e.className.split(St).forEach(function(E){f(s,E)}),e.toolbar){var g=document.createElement("ul"),T=$(e.toolbar),p=rt.slice(0,3),O=rt.slice(7,9),N=rt.slice(9);T||f(h,st(e.toolbar)),w(T?e.toolbar:rt,function(E,nt){var M=T&&$(E),z=T?Dt(nt):E,Y=M&&!H(E.show)?E.show:E;if(!(!Y||!e.zoomable&&p.indexOf(z)!==-1||!e.rotatable&&O.indexOf(z)!==-1||!e.scalable&&N.indexOf(z)!==-1)){var Z=M&&!H(E.size)?E.size:E,gt=M&&!H(E.click)?E.click:E,V=document.createElement("li");e.keyboard&&V.setAttribute("tabindex",0),V.setAttribute("role","button"),f(V,"".concat(m,"-").concat(z)),b(gt)||Et(V,mt,z),D(Y)&&f(V,st(Y)),["small","large"].indexOf(Z)!==-1?f(V,"".concat(m,"-").concat(Z)):z==="play"&&f(V,"".concat(m,"-large")),b(gt)&&d(V,j,gt),g.appendChild(V)}}),h.appendChild(g)}else f(h,P);if(!e.rotatable){var I=h.querySelectorAll('li[class*="rotate"]');f(I,Q),w(I,function(E){h.appendChild(E)})}if(e.inline)f(c,De),k(s,{zIndex:e.zIndexInline}),window.getComputedStyle(n).position==="static"&&k(n,{position:"relative"}),n.insertBefore(s,i.nextSibling);else{f(c,Se),f(s,wt),f(s,ct),f(s,P),k(s,{zIndex:e.zIndex});var x=e.container;et(x)&&(x=i.ownerDocument.querySelector(x)),x||(x=this.body),x.appendChild(s)}if(e.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,b(e.ready)&&d(i,Wt,e.ready,{once:!0}),S(i,Wt)===!1){this.ready=!1;return}this.ready&&e.inline&&this.view(this.index)}}}],[{key:"noConflict",value:function(){return window.Viewer=je,a}},{key:"setDefaults",value:function(i){A(Ot,$(i)&&i)}}]),a}();A(oe.prototype,Xe,He,qe,Be,Ue);const Ze={key:0},Ge={id:"viewerjs"},$e=["src"],Je={__name:"index",props:{url:{type:String,default:""},visible:{type:Boolean,default:!1},urlList:Array,currentIndex:{type:Number,default:0}},emits:["close"],setup(a,{emit:t}){const i=a,e=t,n=lt([]);le(()=>{i.url?n.value.push(i.url):i.urlList&&(n.value=i.urlList),n.value.length>0&&r()});const r=()=>{he(()=>{const s=document.getElementById("viewerjs"),o=new oe(s,{title:!1,navbar:!0,hide:()=>{o.destroy(),e("close",!1)}});o.view(i.currentIndex),o.show()})};return(s,o)=>a.visible?(J(),ht("div",Ze,[ue(fe("div",Ge,[(J(!0),ht(de,null,me(n.value,(h,l)=>(J(),ht("img",{src:h,key:l},null,8,$e))),128))],512),[[ce,!1]])])):ne("",!0)}},Qe={class:"guns-body guns-body-card"},ni={__name:"index",setup(a){const t=lt(""),i=lt(0),e=lt(!1),n=()=>{e.value=!1,t.value=[],currentIndex.value=0},r=()=>{t.value=[we],i.value=0,e.value=!0};return(s,o)=>{const h=pe,l=be,c=Je;return J(),ht("div",Qe,[xt(l,{title:"\u56FE\u7247\u9884\u89C8",bordered:!1},{default:It(()=>[xt(h,{onClick:r},{default:It(()=>o[0]||(o[0]=[ve("\u70B9\u51FB\u9884\u89C8")])),_:1,__:[0]})]),_:1}),e.value?(J(),ge(c,{key:0,visible:e.value,urlList:t.value,currentIndex:i.value,onClose:n},null,8,["visible","urlList","currentIndex"])):ne("",!0)])}}};export{ni as default};
