<template>
  <a-modal
    :visible="visible"
    title="库存详情"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="inventory-detail-content">
      <!-- 商品基本信息 -->
      <a-card title="商品信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="商品名称">
            <span class="product-name">{{ data.productName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="商品编码">
            {{ data.productCode }}
          </a-descriptions-item>
          <a-descriptions-item label="条形码">
            {{ data.barcode || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品分类">
            {{ data.categoryName }}
          </a-descriptions-item>
          <a-descriptions-item label="计价类型">
            <a-tag :color="getPricingTypeColor(data.pricingType)">
              {{ getPricingTypeName(data.pricingType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="商品单位">
            {{ getStockUnit(data.pricingType, data.unit) }}
          </a-descriptions-item>
          <a-descriptions-item label="规格">
            {{ data.specification || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="零售价">
            ¥{{ formatAmount(data.retailPrice) }}
          </a-descriptions-item>
          <a-descriptions-item label="商品状态">
            <a-tag :color="data.status === 'Y' ? 'green' : 'red'">
              {{ data.status === 'Y' ? '启用' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 供应商信息 -->
      <a-card title="供应商信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="供应商名称">
            {{ data.supplierName }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商编码">
            {{ data.supplierCode }}
          </a-descriptions-item>
          <a-descriptions-item label="经营方式">
            <a-tag :color="getBusinessModeColor(data.businessMode)">
              {{ data.businessModeName }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ data.supplierPhone || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 库存信息 -->
      <a-card title="库存信息" size="small" style="margin-bottom: 16px">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="当前库存" 
              :value="formatStock(data.currentStock, data.pricingType)"
              :suffix="getStockUnit(data.pricingType, data.unit)"
              :value-style="getStockValueStyle(data.currentStock, data.minStock)"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="预警值" 
              :value="formatStock(data.minStock, data.pricingType)"
              :suffix="getStockUnit(data.pricingType, data.unit)"
              :value-style="{ color: '#8c8c8c' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="平均成本" 
              :value="data.avgCost"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="库存价值" 
              :value="data.totalValue"
              prefix="¥"
              :precision="2"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 库存状态 -->
      <a-card title="库存状态" size="small" style="margin-bottom: 16px">
        <div class="stock-status-info">
          <a-tag 
            :color="getStockStatusColor(data.stockStatus)" 
            style="font-size: 14px; padding: 8px 16px;"
          >
            <icon-font 
              v-if="data.stockStatus === 'WARNING'" 
              iconClass="icon-opt-jinggao" 
              style="margin-right: 8px;"
            />
            <icon-font 
              v-if="data.stockStatus === 'OUT_OF_STOCK'" 
              iconClass="icon-opt-quehuo" 
              style="margin-right: 8px;"
            />
            {{ getStockStatusName(data.stockStatus) }}
          </a-tag>
          <span class="stock-status-desc">
            {{ getStockStatusDescription(data.stockStatus, data.currentStock, data.minStock) }}
          </span>
        </div>
      </a-card>

      <!-- 最近库存变动 -->
      <a-card title="最近库存变动" size="small">
        <template #extra>
          <a-button type="link" size="small" @click="showAllHistory">
            查看全部历史
          </a-button>
        </template>
        
        <a-table
          :columns="historyColumns"
          :data-source="recentHistory"
          :pagination="false"
          size="small"
          :loading="historyLoading"
        >
          <template #bodyCell="{ column, record }">
            <!-- 操作类型 -->
            <template v-if="column.key === 'operationType'">
              <a-tag :color="getOperationTypeColor(record.operationType)">
                {{ getOperationTypeName(record.operationType) }}
              </a-tag>
            </template>

            <!-- 数量变化 -->
            <template v-if="column.key === 'quantity'">
              <span :class="getQuantityChangeClass(record.quantityChange, record.operationType)">
                {{ formatQuantityChange(record.quantityChange, record.operationType) }}
                {{ getStockUnit(data.pricingType, data.unit) }}
              </span>
            </template>

            <!-- 操作前库存 -->
            <template v-if="column.key === 'beforeStock'">
              {{ formatStock(record.stockBefore, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </template>

            <!-- 操作后库存 -->
            <template v-if="column.key === 'afterStock'">
              {{ formatStock(record.stockAfter, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </template>

            <!-- 操作时间 -->
            <template v-if="column.key === 'operationTime'">
              {{ record.operationTime }}
            </template>

            <!-- 操作人 -->
            <template v-if="column.key === 'operatorName'">
              {{ record.operatorName || record.operationUserName || '-' }}
            </template>

            <!-- 备注 -->
            <template v-if="column.key === 'remark'">
              {{ record.remark || '-' }}
            </template>
          </template>
        </a-table>

        <!-- 空状态 -->
        <div v-if="!recentHistory || recentHistory.length === 0" class="empty-history">
          <a-empty description="暂无库存变动记录" />
        </div>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { InventoryHistoryApi } from '../api/InventoryHistoryApi';

export default {
  name: 'InventoryDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'showHistory'],
  setup(props, { emit }) {
    const historyLoading = ref(false);
    const recentHistory = ref([]);

    // 历史记录表格列定义
    const historyColumns = [
      {
        title: '操作类型',
        key: 'operationType',
        width: 100
      },
      {
        title: '数量变化',
        key: 'quantity',
        width: 120,
        align: 'right'
      },
      {
        title: '操作前',
        key: 'beforeStock',
        width: 100,
        align: 'right'
      },
      {
        title: '操作后',
        key: 'afterStock',
        width: 100,
        align: 'right'
      },
      {
        title: '操作时间',
        key: 'operationTime',
        width: 160
      },
      {
        title: '操作人',
        key: 'operatorName',
        width: 100
      },
      {
        title: '备注',
        key: 'remark',
        width: 150,
        ellipsis: true
      }
    ];

    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      if (visible && props.data.productId) {
        loadRecentHistory();
      }
    });

    // 加载最近库存变动记录
    const loadRecentHistory = async () => {
      historyLoading.value = true;
      try {
        const response = await InventoryHistoryApi.productHistory({
          productId: props.data.productId,
          pageSize: 3 // 只显示最近3条记录
        });
        
        // 处理响应数据
        if (response && response.success !== false) {
          // 如果response直接是数组，使用response；否则使用response.data
          const historyData = Array.isArray(response) ? response : (response.data || []);
          
          // 处理数据格式，确保字段映射正确
          let processedData = historyData.map(item => ({
            ...item,
            // 确保字段名称正确映射
            quantity: item.quantityChange,
            beforeStock: item.stockBefore,
            afterStock: item.stockAfter,
            operatorName: item.operationUserName || item.operatorName || '未知操作员',
            // 格式化操作时间
            operationTime: formatDateTime(item.operationTime)
          }));
          
          // 在前端限制为3条记录
          recentHistory.value = processedData.slice(0, 3);
        } else {
          recentHistory.value = [];
        }
      } catch (error) {
        recentHistory.value = [];
      } finally {
        historyLoading.value = false;
      }
    };

    // 获取计价类型名称
    const getPricingTypeName = (pricingType) => {
      const typeMap = {
        'NORMAL': '普通',
        'WEIGHT': '称重',
        'PIECE': '计件',
        'VARIABLE': '变价'
      };
      return typeMap[pricingType] || pricingType;
    };

    // 获取计价类型颜色
    const getPricingTypeColor = (pricingType) => {
      const colorMap = {
        'NORMAL': 'blue',
        'WEIGHT': 'orange',
        'PIECE': 'green',
        'VARIABLE': 'purple'
      };
      return colorMap[pricingType] || 'default';
    };

    // 获取经营方式颜色
    const getBusinessModeColor = (businessMode) => {
      const colorMap = {
        'PURCHASE_SALE': 'blue',
        'JOINT_VENTURE': 'orange',
        'CONSIGNMENT': 'green'
      };
      return colorMap[businessMode] || 'default';
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-';
      
      // 如果是字符串，尝试转换为Date对象
      const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime;
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) return dateTime;
      
      // 格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    // 获取库存值样式
    const getStockValueStyle = (currentStock, minStock) => {
      const current = parseFloat(currentStock) || 0;
      const min = parseFloat(minStock) || 0;
      
      if (current <= 0) return { color: '#ff4d4f' };
      if (current <= min) return { color: '#faad14' };
      return { color: '#52c41a' };
    };

    // 获取库存状态名称
    const getStockStatusName = (status) => {
      const statusMap = {
        'NORMAL': '库存正常',
        'WARNING': '库存预警',
        'OUT_OF_STOCK': '库存不足'
      };
      return statusMap[status] || status;
    };

    // 获取库存状态颜色
    const getStockStatusColor = (status) => {
      const colorMap = {
        'NORMAL': 'green',
        'WARNING': 'orange',
        'OUT_OF_STOCK': 'red'
      };
      return colorMap[status] || 'default';
    };

    // 获取库存状态描述
    const getStockStatusDescription = (status, currentStock, minStock) => {
      const current = parseFloat(currentStock) || 0;
      const min = parseFloat(minStock) || 0;
      
      switch (status) {
        case 'WARNING':
          return `当前库存 ${current} 已低于预警值 ${min}，建议及时补货`;
        case 'OUT_OF_STOCK':
          return '当前库存为0，请尽快补货';
        case 'NORMAL':
        default:
          return '库存充足，无需补货';
      }
    };

    // 获取操作类型名称
    const getOperationTypeName = (operationType) => {
      return InventoryHistoryApi.getOperationTypeName(operationType);
    };

    // 获取操作类型颜色
    const getOperationTypeColor = (operationType) => {
      return InventoryHistoryApi.getOperationTypeColor(operationType);
    };

    // 格式化数量变化
    const formatQuantityChange = (quantity, operationType) => {
      return InventoryHistoryApi.formatQuantityChange(quantity, operationType);
    };

    // 获取数量变化样式类
    const getQuantityChangeClass = (quantity, operationType) => {
      const numQuantity = parseFloat(quantity) || 0;
      switch (operationType) {
        case 'IN':
          return 'quantity-increase';
        case 'OUT':
        case 'SALE':
          return 'quantity-decrease';
        case 'ADJUST':
          return numQuantity >= 0 ? 'quantity-increase' : 'quantity-decrease';
        case 'SET_ALERT':
          return '';
        default:
          return '';
      }
    };

    // 查看全部历史
    const showAllHistory = () => {
      // 关闭当前详情弹窗
      emit('update:visible', false);
      // 触发显示历史记录弹窗
      emit('showHistory', props.data);
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    return {
      historyLoading,
      recentHistory,
      historyColumns,
      getPricingTypeName,
      getPricingTypeColor,
      getBusinessModeColor,
      getStockUnit,
      formatStock,
      formatAmount,
      formatDateTime,
      getStockValueStyle,
      getStockStatusName,
      getStockStatusColor,
      getStockStatusDescription,
      getOperationTypeName,
      getOperationTypeColor,
      formatQuantityChange,
      getQuantityChangeClass,
      showAllHistory,
      handleCancel
    };
  }
};
</script>

<style scoped>
.inventory-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.product-name {
  font-weight: 500;
  color: #1890ff;
}

.stock-status-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stock-status-desc {
  color: #8c8c8c;
  font-size: 14px;
}

.quantity-increase {
  color: #52c41a;
  font-weight: 500;
}

.quantity-decrease {
  color: #ff4d4f;
  font-weight: 500;
}

.empty-history {
  padding: 40px 0;
  text-align: center;
}

.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
}

.ant-statistic-content {
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}
</style>
