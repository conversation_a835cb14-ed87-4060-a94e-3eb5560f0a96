package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.erp.api.exception.InventoryAlertExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRecord;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRecordRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRecordResponse;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper.InventoryAlertRecordMapper;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service.InventoryAlertRecordService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存预警记录服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Slf4j
@Service
public class InventoryAlertRecordServiceImpl extends ServiceImpl<InventoryAlertRecordMapper, InventoryAlertRecord>
        implements InventoryAlertRecordService {

    @Resource
    private InventoryAlertRecordMapper inventoryAlertRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(InventoryAlertRecordRequest request) {
        // 检查是否已存在未处理的相同预警
        if (hasUnresolvedAlert(request.getRuleId(), request.getProductId())) {
            log.debug("已存在未处理的相同预警，跳过创建。规则ID：{}，商品ID：{}", 
                     request.getRuleId(), request.getProductId());
            return;
        }

        InventoryAlertRecord entity = new InventoryAlertRecord();
        BeanUtil.copyProperties(request, entity);
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus("PENDING");
        }
        if (entity.getNotificationSent() == null) {
            entity.setNotificationSent("N");
        }
        if (entity.getAlertTime() == null) {
            entity.setAlertTime(new Date());
        }
        
        // 生成建议操作
        generateSuggestedAction(entity);
        
        this.save(entity);
        log.info("创建预警记录成功，规则ID：{}，商品ID：{}", request.getRuleId(), request.getProductId());
    }

    @Override
    public InventoryAlertRecordResponse detail(InventoryAlertRecordRequest request) {
        InventoryAlertRecord entity = this.queryInventoryAlertRecord(request);
        InventoryAlertRecordResponse response = new InventoryAlertRecordResponse();
        BeanUtil.copyProperties(entity, response);
        return response;
    }

    @Override
    public PageResult<InventoryAlertRecordResponse> findPage(InventoryAlertRecordRequest request) {
        // 设置默认分页参数，避免空指针异常
        Integer pageNo = request.getPageNo() != null ? request.getPageNo() : 1;
        Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 10;

        Page<InventoryAlertRecordResponse> page = new Page<>(pageNo, pageSize);
        Page<InventoryAlertRecordResponse> result = inventoryAlertRecordMapper.selectRecordPage(page, request);
        return PageResultFactory.createPageResult(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(InventoryAlertRecordRequest request) {
        InventoryAlertRecord entity = this.queryInventoryAlertRecord(request);
        
        // 检查记录是否已处理
        if ("RESOLVED".equals(entity.getStatus()) || "IGNORED".equals(entity.getStatus())) {
            throw new ServiceException(InventoryAlertExceptionEnum.RECORD_ALREADY_HANDLED);
        }
        
        String newStatus = "RESOLVE".equals(request.getHandleAction()) ? "RESOLVED" : "IGNORED";
        entity.setStatus(newStatus);
        entity.setHandlerUser(LoginContext.me().getLoginUser().getUserId());
        entity.setHandleTime(new Date());
        entity.setHandleRemark(request.getHandleRemark());
        
        this.updateById(entity);
        log.info("处理预警记录成功，记录ID：{}，处理动作：{}", request.getId(), request.getHandleAction());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchHandle(InventoryAlertRecordRequest request) {
        List<Long> idList = request.getIdList();
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        
        String newStatus = "RESOLVE".equals(request.getHandleAction()) ? "RESOLVED" : "IGNORED";
        Long handlerUser = LoginContext.me().getLoginUser().getUserId();
        
        int updateCount = inventoryAlertRecordMapper.batchUpdateStatus(
            idList, newStatus, handlerUser, request.getHandleRemark());
        
        log.info("批量处理预警记录成功，处理数量：{}，处理动作：{}", updateCount, request.getHandleAction());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ignore(InventoryAlertRecordRequest request) {
        request.setHandleAction("IGNORE");
        this.handle(request);
    }

    @Override
    public Map<String, Object> getStatistics(Long tenantId) {
        return inventoryAlertRecordMapper.selectStatistics(tenantId);
    }

    @Override
    public List<InventoryAlertRecordResponse> exportRecords(InventoryAlertRecordRequest request) {
        // 设置大的分页参数来获取所有数据
        request.setPageNo(1);
        request.setPageSize(10000);
        
        PageResult<InventoryAlertRecordResponse> pageResult = this.findPage(request);
        return pageResult.getRows();
    }

    @Override
    public boolean hasUnresolvedAlert(Long ruleId, Long productId) {
        int count = inventoryAlertRecordMapper.countUnresolvedAlert(ruleId, productId);
        return count > 0;
    }

    @Override
    public List<InventoryAlertRecord> findByProductId(Long productId, String status) {
        return inventoryAlertRecordMapper.selectByProductId(productId, status);
    }

    @Override
    public List<InventoryAlertRecord> findByRuleId(Long ruleId, String status) {
        return inventoryAlertRecordMapper.selectByRuleId(ruleId, status);
    }

    @Override
    public List<InventoryAlertRecordResponse> findRecentRecords(int limit, Long tenantId) {
        return inventoryAlertRecordMapper.selectRecentRecords(limit, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredRecords(int days) {
        List<Long> expiredIds = inventoryAlertRecordMapper.selectRecordsToCleanup(days);
        if (CollUtil.isNotEmpty(expiredIds)) {
            int deleteCount = this.baseMapper.deleteBatchIds(expiredIds);
            log.info("清理过期预警记录成功，清理数量：{}", deleteCount);
            return deleteCount;
        }
        return 0;
    }

    @Override
    public void generateSuggestedAction(InventoryAlertRecord record) {
        String alertType = record.getAlertType();
        BigDecimal currentStock = record.getCurrentStock();
        
        switch (alertType) {
            case "LOW_STOCK":
            case "ZERO_STOCK":
                record.setSuggestedAction("建议立即补货");
                // TODO: 计算建议补货数量
                // TODO: 推荐供应商
                break;
            case "OVERSTOCK":
                record.setSuggestedAction("建议促销或调配库存");
                break;
            case "EXPIRY":
                record.setSuggestedAction("建议优先销售或处理临期商品");
                break;
            default:
                record.setSuggestedAction("请及时处理");
        }
    }

    /**
     * 查询预警记录
     */
    private InventoryAlertRecord queryInventoryAlertRecord(InventoryAlertRecordRequest request) {
        InventoryAlertRecord entity = this.getById(request.getId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new ServiceException(InventoryAlertExceptionEnum.RECORD_NOT_FOUND);
        }
        return entity;
    }
}
