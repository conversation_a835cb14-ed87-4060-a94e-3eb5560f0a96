import t from"./ProductWithCategory-f6bf941a.js";import{_ as r,k as p,a as i,f as m}from"./index-18a1ea24.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              */import"./SupplierSelector-e8033f79.js";/* empty css              */import"./SupplierApi-6b9315dd.js";import"./UniversalTree-6547889b.js";import"./ProductApi-52d42f8e.js";import"./ProductEdit-68ff2007.js";/* empty css              */import"./CategorySelector-5d61ae06.js";/* empty css              */import"./productCategoryApi-39e417fd.js";import"./ProductDetail-4a98f590.js";/* empty css              */const e={name:"ProductIndex",components:{ProductWithCategory:t}};function c(n,a,s,_,d,f){const o=p("product-with-category");return i(),m(o)}const A=r(e,[["render",c]]);export{A as default};
