System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./ExternalAppApi-legacy-720c9516.js"],(function(t,e){"use strict";var a,n,l,d,i,c,o,s,h,u,r,g,b,v,p,m,C,k,x,y,f,F;return{setters:[t=>{a=t.R,n=t._,l=t.r,d=t.o,i=t.a,c=t.c,o=t.b,s=t.d,h=t.w,u=t.F,r=t.e,g=t.f,b=t.g,v=t.t,p=t.h,m=t.m,C=t.C,k=t.i,x=t.S,y=t.j,f=t.T},null,null,null,t=>{F=t.E}],execute:function(){var e=document.createElement("style");e.textContent=".left-tab[data-v-8b847a37]{height:100%}[data-v-8b847a37] .left-tab .ant-tabs-tab-active{background:rgba(24,144,255,.1)}[data-v-8b847a37] .right-tab .ant-tabs-tab-active{background:#fff}[data-v-8b847a37] .left-tab .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn,[data-v-8b847a37] .left-tab .ant-tabs-tab-btn{min-width:70px;text-align:center}.right[data-v-8b847a37]{width:100%;height:100%;overflow:hidden}[data-v-8b847a37] .ant-tabs-content{height:100%}.right-top[data-v-8b847a37]{margin-top:-10px;height:62px}.right-bottom[data-v-8b847a37]{width:100%;height:calc(100% - 62px);overflow:hidden}.use-content[data-v-8b847a37]{width:100%;height:100%}.use-content .content-header[data-v-8b847a37]{height:22px}.use-content .content-bottom[data-v-8b847a37]{width:100%;margin-top:10px;overflow-y:auto;height:calc(100% - 32px)}.use-content .content-bottom .bottom-item[data-v-8b847a37]{margin-bottom:20px}.use-content .content-bottom .bottom-item .bottom-item-name .title[data-v-8b847a37]{font-size:16px;font-weight:700;margin-right:10px}[data-v-8b847a37] .ant-table-thead th{text-align:center}[data-v-8b847a37] .ant-table-tbody .ant-table-cell{padding:8px 8px 8px 20px!important}\n",document.head.appendChild(e);class w{static getBindResult(t){return a.getAndLoadData("/apiClientAuth/getBindResult",t)}static bind(t){return a.post("/apiClientAuth/bind",t)}}const _={class:"guns-layout"},I={class:"guns-layout-content"},A={class:"guns-layout"},j={class:"guns-layout-content-application"},E={class:"content-mian"},R={class:"content-mian-body"},z={class:"table-content"},K={key:0,class:"right"},L={class:"right-bottom"},N={key:0,class:"use-content"},S={class:"content-header"},U={class:"content-bottom"},B={class:"table"};t("default",n(Object.assign({name:"Authorization"},{__name:"index",setup(t){const e=l([]),a=l(""),n=l(!1),T=l(null),D=l([{title:"",width:60,dataIndex:"page"},{title:"接口名称",align:"center",dataIndex:"resourceName"},{title:"接口编码",align:"center",dataIndex:"resourceCode"},{title:"接口路径",dataIndex:"url"},{title:"请求方式",align:"center",dataIndex:"httpMethod"}]);d((()=>{M()}));const M=()=>{F.list().then((t=>{e.value=t,t&&t.length&&(a.value=t[0].apiClientId,O())}))},O=()=>{n.value=!0,w.getBindResult({apiClientId:a.value}).then((t=>{T.value=t})).finally((()=>n.value=!1))},q=t=>{O()},G=(t,e,l)=>{n.value=!0;let d={checkedFlag:t,selectType:l,apiClientId:a.value};"2"==l&&(d.resourceCode=e.resourceCode),w.bind(d).then((t=>{m.success(t.message)})).finally((()=>n.value=!1))},H=(t,e)=>{e&&e.length>0&&e.forEach((e=>{e.apiCheckedFlag=t,e.children&&e.children.length>0&&H(t,e.children)}))};return(t,l)=>{const d=C,m=k,F=x,w=y,M=f;return i(),c("div",_,[o("div",I,[o("div",A,[o("div",j,[o("div",E,[o("div",R,[o("div",z,[s(M,{activeKey:a.value,"onUpdate:activeKey":l[2]||(l[2]=t=>a.value=t),"tab-position":"left",animated:"",onChange:q,class:"left-tab"},{default:h((()=>[(i(!0),c(u,null,r(e.value,(t=>(i(),g(w,{key:t.apiClientId,tab:t.apiClientName},{default:h((()=>[a.value?(i(),c("div",K,[s(F,{spinning:n.value,delay:100},{default:h((()=>[o("div",L,[T.value?(i(),c("div",N,[o("div",S,[s(d,{checked:T.value.totalAuthCheckedFlag,"onUpdate:checked":l[0]||(l[0]=t=>T.value.totalAuthCheckedFlag=t),onClick:l[1]||(l[1]=t=>((t,e)=>{H(t.target.checked,e.apiEndpointCheckedFlagList),G(t.target.checked,e,"1")})(t,T.value))},{default:h((()=>l[3]||(l[3]=[b("所有接口权限")]))),_:1,__:[3]},8,["checked"])]),o("div",U,[o("div",B,[s(m,{dataSource:T.value.apiEndpointCheckedFlagList,columns:D.value,pagination:!1,rowKey:"apiClientResourceId",bordered:"",size:"small",childrenColumnName:"other"},{bodyCell:h((({column:t,record:e})=>["page"===t.dataIndex?(i(),g(d,{key:0,checked:e.apiCheckedFlag,"onUpdate:checked":t=>e.apiCheckedFlag=t,onChange:t=>{return a=t,(n=e).apiCheckedFlag=a.target.checked,T.value.apiEndpointCheckedFlagList.find((t=>0==t.apiCheckedFlag))?T.value.totalAuthCheckedFlag=!1:T.value.totalAuthCheckedFlag=!0,void G(a.target.checked,n,"2");var a,n}},{default:h((()=>[b(v(e.nodeName),1)])),_:2},1032,["checked","onUpdate:checked","onChange"])):p("",!0)])),_:1},8,["dataSource","columns"])])])])):p("",!0)])])),_:1},8,["spinning"])])):p("",!0)])),_:2},1032,["tab"])))),128))])),_:1},8,["activeKey"])])])])])])])])}}}),[["__scopeId","data-v-8b847a37"]]))}}}));
