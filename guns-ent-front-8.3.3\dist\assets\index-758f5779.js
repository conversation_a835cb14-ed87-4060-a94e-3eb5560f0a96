import{_ as ve,r as o,cc as me,o as _e,X as se,aK as he,k as ge,a as u,c as _,b as l,d,w as v,g as G,ah as le,F as q,e as X,a2 as J,t as Q,f as m,h as f,aM as K,M as ye,E as fe,m as ae,I as ke,l as xe,B as be,n as Ce,bg as Ie,a5 as Te,S as we}from"./index-18a1ea24.js";/* empty css              */import{M as R}from"./MenuApi-47485f5a.js";import Me from"./use-stand-af624c0e.js";import Ne from"./menu-add-edit-f9a4e6c0.js";import"./menu-form-cc583187.js";/* empty css              *//* empty css              */import"./AppApi-4e70edf8.js";const Le="/assets/avatar-6ddecb5a.png";const Pe={class:"guns-layout"},Ee={class:"guns-layout-content"},Se={class:"guns-layout"},Ae={class:"guns-layout-content-application"},Be={class:"content-mian",style:{overflow:"hidden"}},De={class:"content-mian-header"},ze={class:"header-content"},je={class:"header-content-left"},Ke={class:"header-content-right"},Oe={class:"content-mian-body"},Ue={class:"table-content",id:"content"},$e={id:"pageUl",type:"circle"},We={class:"left-header"},He=["src"],Fe={key:1,src:Le,alt:"",class:"img"},Ve={class:"app-item-right"},Ge={class:"app-item-name"},qe={class:"app-item-remark"},Xe={class:"masks"},Je={class:"menu-tree"},Qe={class:"tree-edit"},Re=["title"],Ye=Object.assign({name:"AuthMenu"},{__name:"index",setup(Ze){const N=o({searchText:""}),L=o([]),I=o(!1),ie=o(0),b=o(0),C=o(1),T=o(1),Y=o(0),Z=o(0),P=o(0),ee=o(0),O=o(Object),E=o(Object),S=o(!1),w=o(null),A=o(!1),ne=o(""),U=o(""),$=o(""),B=o(0),W=o(0),D=o([]),H=o([]),te=o(!0);me(()=>{M()}),_e(()=>{window.onresize=()=>(()=>{B.value=document.body.clientWidth,W.value=document.body.clientHeight})(),F()}),se(()=>B.value,t=>{oe(),he(()=>{F()})},{deep:!0}),se(()=>W.value,t=>{F()},{deep:!0});const oe=()=>{C.value=1,T.value=1,P.value=0,V(C.value),D.value=[];let t=parseInt((B.value-10)/420);t<1&&(t=1);for(var e=[],a=0;a<L.value.length;a+=t)e.push(L.value.slice(a,a+t));if(e&&e.length>0&&e.forEach((k,p)=>{D.value[p]={index:p,children:k}}),te.value){let k=L.value.map(h=>h.openMenuIdList),p=[].concat(...k);H.value=[...p]}te.value=!1},F=()=>{ie.value=document.getElementById("content").clientWidth,b.value=document.getElementById("content").clientHeight,O.value=document.getElementById("main"),E.value=document.getElementsByTagName("div");for(let t=0;t<E.value.length;t++)E.value[t].className=="page"&&(E.value[t].style.height=b.value+"px");ee.value=document.querySelectorAll(".page").length,navigator.userAgent.toLowerCase().indexOf("firefox")!=-1?document.addEventListener("DOMMouseScroll",z,!1):document.addEventListener?document.addEventListener("mousewheel",z,!1):document.attachEvent?document.attachEvent("onmousewheel",z):document.onmousewheel=z},z=t=>{if(t.target.className=="page"){Y.value=new Date().getTime();let e=t.detail||-t.wheelDelta;Y.value-Z.value>500&&(e>0&&parseInt(O.value.offsetTop)>=-(b.value*(ee.value-2))?(C.value++,V(C.value)):e<0&&parseInt(O.value.offsetTop)<0&&(C.value--,V(C.value)),Z.value=new Date().getTime())}},V=t=>{if(t!=T.value){let e=t-T.value;P.value=P.value-e*b.value,T.value=t}},M=(t=!1)=>{I.value=!0,R.getAppMenuGroupDetail({searchText:N.value.searchText}).then(e=>{L.value=e,B.value=document.body.clientWidth,W.value=document.body.clientHeight,t&&oe()}).finally(()=>I.value=!1)},ue=()=>{N.value.searchText="",M()},j=(t,e,a)=>{ne.value=t?t.appId:"",a=="add"?(w.value=null,U.value=e?e.menuId:"-1",$.value=e?e.menuName:"\u6839\u8282\u70B9"):(w.value=e,U.value=e.menuId,$.value=e.menuName),A.value=!0},ce=t=>{w.value=t,S.value=!0},de=t=>{ye.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5417?",icon:d(fe),maskClosable:!0,onOk:()=>{I.value=!0,R.delete({menuId:t.menuId}).then(e=>{ae.success(e.message),M(!0)}).finally(()=>I.value=!1)}})},re=(t,e)=>{const a=e.node.eventKey,k=e.dragNode.eventKey,p=e.node.pos.split("-"),h=e.dropPosition-Number(p[p.length-1]),g=(i,s,c)=>{i.forEach((n,x,pe)=>{if(n.nodeId===s)return c(n,x,pe);if(n.children)return g(n.children,s,c)})},y=[...t.menuList];let r={};if(g(y,k,(i,s,c)=>{c.splice(s,1),r=i}),!e.dropToGap)r.menuParentId=e.node.nodeId,r.nodeParentId=e.node.nodeId,g(y,a,i=>{i.children=i.children||[],i.children.push(r)});else if((e.node.children||[]).length>0&&e.node.expanded&&h===1)r.menuParentId=e.node.nodeId,r.nodeParentId=e.node.nodeId,g(y,a,i=>{i.children=i.children||[],i.children.unshift(r)});else{r.menuParentId=e.node.nodeParentId,r.nodeParentId=e.node.nodeParentId;let i=[],s=0;g(y,a,(c,n,x)=>{i=x,s=n}),h===-1?i.splice(s,0,r):i.splice(s+1,0,r)}t.menuList=[...y],R.updateMenuTree({updateMenuTree:y}).then(i=>{ae.success(i.message)})};return(t,e)=>{const a=ke,k=xe,p=be,h=Ce,g=ge("plus-outlined"),y=Ie,r=Te,i=we;return u(),_("div",Pe,[l("div",Ee,[l("div",Se,[l("div",Ae,[l("div",Be,[l("div",De,[l("div",ze,[l("div",je,[d(h,{size:16},{default:v(()=>[d(k,{value:N.value.searchText,"onUpdate:value":e[0]||(e[0]=s=>N.value.searchText=s),placeholder:"\u83DC\u5355\u540D\u79F0\u3001\u83DC\u5355\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:M,class:"search-input"},{prefix:v(()=>[d(a,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),d(p,{class:"border-radius",onClick:ue},{default:v(()=>e[6]||(e[6]=[G("\u91CD\u7F6E")])),_:1,__:[6]})]),_:1})]),l("div",Ke,[d(h,{size:16},{default:v(()=>[d(p,{type:"primary",class:"border-radius",onClick:e[1]||(e[1]=s=>j("","","add"))},{default:v(()=>[d(g),e[7]||(e[7]=G("\u65B0\u5EFA\u83DC\u5355"))]),_:1,__:[7]})]),_:1})])])]),l("div",Oe,[l("div",Ue,[d(i,{tip:"Loading...",spinning:I.value,delay:100},{default:v(()=>[l("div",{id:"wrap",style:le({height:b.value+"px"})},[l("ul",$e,[(u(!0),_(q,null,X(D.value,s=>(u(),_("li",{id:"pageUlLi1",class:J(["pageUlLi",{active:T.value==s.index+1}]),key:s.index}," \xA0 ",2))),128))]),l("div",{id:"main",style:le({top:P.value+"px",height:b.value+"px"})},[(u(!0),_(q,null,X(D.value,s=>(u(),_("div",{id:"page1",class:"page",key:s.index},[(u(!0),_(q,null,X(s.children,c=>(u(),_("div",{class:"box-shadow menu-item",key:c.appId},[l("div",We,[c.appIconWrapper?(u(),_("img",{key:0,src:c.appIconWrapper,alt:"",class:"img"},null,8,He)):(u(),_("img",Fe)),l("div",Ve,[l("div",Ge,Q(c.appName),1),l("div",qe,Q(c.remark),1)]),l("div",Xe,[d(p,{type:"primary",class:"add",onClick:n=>j(c,null,"add")},{default:v(()=>[d(g),e[8]||(e[8]=G("\u65B0\u5EFA\u83DC\u5355"))]),_:2,__:[8]},1032,["onClick"])])]),l("div",Je,[c.menuList&&c.menuList.length>0?(u(),m(y,{key:0,draggable:"",onDrop:n=>re(c,n),"show-icon":!0,expandedKeys:H.value,"onUpdate:expandedKeys":e[2]||(e[2]=n=>H.value=n),"tree-data":c.menuList,fieldNames:{children:"children",title:"menuName",key:"menuId",value:"menuId"}},{icon:v(n=>[n.menuType==10?(u(),m(a,{key:0,iconClass:"icon-menu-type-backend",fontSize:"24px",color:"#594d9c"})):f("",!0),n.menuType==20?(u(),m(a,{key:1,iconClass:"icon-menu-type-single-page",fontSize:"24px",color:"green"})):f("",!0),n.menuType==30?(u(),m(a,{key:2,fontSize:"24px",iconClass:"icon-menu-type-inner-link",color:"var(--primary-color)"})):f("",!0),n.menuType==40?(u(),m(a,{key:3,iconClass:"icon-menu-type-waibulianjie",color:"#e37445",fontSize:"24px"})):f("",!0),n.menuType==50?(u(),m(a,{key:4,iconClass:"icon-menu-yingyongguanli",color:"#e37445",fontSize:"24px"})):f("",!0)]),title:v(n=>[l("span",Qe,[l("span",{class:J((n.menuType==10||n.menuType==20)&&n.children&&n.children.length==0?"edit-titles":"edit-title"),title:n.menuName},Q(n.menuName),11,Re),l("span",{class:J([10,20,50].includes(n.menuType)&&n.children&&n.children.length==0?"edit-icons":"edit-icon")},[d(h,null,{default:v(()=>[d(a,{title:"\u7F16\u8F91",iconClass:"icon-opt-bianji",color:"var(--primary-color)",onClick:K(x=>j(c,n,"edit"),["stop"])},null,8,["onClick"]),[10,20,50].includes(n.menuType)&&n.children&&n.children.length==0?(u(),m(a,{key:0,iconClass:"icon-menu-yingyongguanli",color:"var(--primary-color)",onClick:K(x=>ce(n),["stop"]),title:"\u529F\u80FD\u7EF4\u62A4"},null,8,["onClick"])):f("",!0),d(a,{iconClass:"icon-opt-tianjia",color:"var(--primary-color)",title:"\u65B0\u589E",onClick:K(x=>j(c,n,"add"),["stop"])},null,8,["onClick"]),d(a,{iconClass:"icon-opt-shanchu",title:"\u5220\u9664",color:"red",onClick:K(x=>de(n),["stop"])},null,8,["onClick"])]),_:2},1024)],2)])]),_:2},1032,["onDrop","expandedKeys","tree-data"])):(u(),m(r,{key:1,class:"empty"}))])]))),128))]))),128))],4)],4)]),_:1},8,["spinning"])])])])])])]),S.value?(u(),m(Me,{key:0,visible:S.value,"onUpdate:visible":e[3]||(e[3]=s=>S.value=s),data:w.value},null,8,["visible","data"])):f("",!0),A.value?(u(),m(Ne,{key:1,visible:A.value,"onUpdate:visible":e[4]||(e[4]=s=>A.value=s),data:w.value,onDone:e[5]||(e[5]=s=>M(!0)),appId:ne.value,menuParentId:U.value,menuParentName:$.value},null,8,["visible","data","appId","menuParentId","menuParentName"])):f("",!0)])}}}),dn=ve(Ye,[["__scopeId","data-v-fdd939ba"]]);export{dn as default};
