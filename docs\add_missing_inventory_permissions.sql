/*
 补充缺失的库存管理权限配置
 
 Date: 30/07/2025 23:00:00
 
 问题：库存详情接口权限校验失败 - ERP_INVENTORY_MANAGE 权限缺失
 解决：只添加缺失的库存管理基础权限
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===========================
-- 1. 创建库存管理主菜单（如果不存在）
-- ===========================

INSERT IGNORE INTO `sys_menu` VALUES 
(1816000000000000100, 1815000000000000010, '[-1],[1815000000000000010],', '库存管理', 'ERP_INVENTORY', 1815000000000000001, 10107.00, 1, '库存查询、库存调整、库存统计等功能', 10, '/erp/inventory', '/erp/inventory/index', 'icon-menu-kucun', NULL, NULL, 'Y', NULL, NULL, NULL, 1, 'N', NOW(), 1, NOW(), 1);

-- ===========================
-- 2. 创建关键的Controller级别权限
-- ===========================

-- 库存管理界面权限（这是Controller需要的关键权限）
INSERT IGNORE INTO `sys_menu_options` VALUES 
(1816000000000000200, 1815000000000000001, 1816000000000000100, '库存管理界面', 'ERP_INVENTORY_MANAGE', NOW(), 1, NOW(), 1);

-- 库存历史查询权限（解决你遇到的具体问题）
INSERT IGNORE INTO `sys_menu_options` VALUES 
(1816000000000000220, 1815000000000000001, 1816000000000000100, '查询商品库存历史', 'PRODUCT_INVENTORY_HISTORY', NOW(), 1, NOW(), 1);

-- ===========================
-- 3. 为超级管理员分配权限
-- ===========================

-- 分配菜单权限
INSERT IGNORE INTO `sys_role_menu` VALUES 
(1816000000000000300, 1339550467939639299, 1815000000000000001, 1816000000000000100, NOW(), 1, NOW(), 1);

-- 分配功能权限
INSERT IGNORE INTO `sys_role_menu_options` VALUES 
(1816000000000000400, 1339550467939639299, 1815000000000000001, 1816000000000000100, 1816000000000000200, NOW(), 1, NOW(), 1),
(1816000000000000420, 1339550467939639299, 1815000000000000001, 1816000000000000100, 1816000000000000220, NOW(), 1, NOW(), 1);

-- ===========================
-- 4. 验证权限配置
-- ===========================

SELECT '=== 库存管理权限补充验证 ===' as info;

-- 检查关键权限是否创建
SELECT 'Key Permission:' as type, menu_option_id, option_name, option_code 
FROM sys_menu_options 
WHERE option_code = 'ERP_INVENTORY_MANAGE';

-- 检查超级管理员是否有权限
SELECT 'Admin Permission:' as type, role_id, menu_option_id 
FROM sys_role_menu_options 
WHERE role_id = 1339550467939639299 AND menu_option_id = 1816000000000000200;

SET FOREIGN_KEY_CHECKS = 1;

/*
使用说明：

1. 这个脚本只添加缺失的库存管理基础权限
2. 执行后重新登录系统即可解决权限问题
3. 如果你不是超级管理员，请替换角色ID：
   
   -- 查询你的角色ID
   SELECT u.account, ur.role_id FROM sys_user u 
   JOIN sys_user_role ur ON u.user_id = ur.user_id 
   WHERE u.account = 'your_username';
   
   -- 为你的角色分配权限（替换YOUR_ROLE_ID）
   INSERT IGNORE INTO sys_role_menu VALUES 
   (1816000000000000300 + YOUR_ROLE_ID, YOUR_ROLE_ID, 1815000000000000001, 1816000000000000100, NOW(), 1, NOW(), 1);
   
   INSERT IGNORE INTO sys_role_menu_options VALUES 
   (1816000000000000400 + YOUR_ROLE_ID, YOUR_ROLE_ID, 1815000000000000001, 1816000000000000100, 1816000000000000200, NOW(), 1, NOW(), 1);

4. 执行完成后必须重新登录系统！
*/
