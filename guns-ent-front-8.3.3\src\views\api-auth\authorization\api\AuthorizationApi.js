import Request from '@/utils/request/request-util';

/**
 * api认证 -接口授权api
 *
 */
export class AuthorizationApi {
  /**
   * 获取API客户端和资源绑定关系列表
   * @param {*} params
   * @returns
   */
  static getBindResult(params) {
    return Request.getAndLoadData('/apiClientAuth/getBindResult', params);
  }

  /**
   * api客户端和资源绑定
   * @param {*} params
   * @returns
   */
  static bind(params) {
    return Request.post('/apiClientAuth/bind', params);
  }
}
