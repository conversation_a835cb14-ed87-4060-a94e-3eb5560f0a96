package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购入库单状态统计响应类
 *
 * <AUTHOR>
 * @since 2025/07/28 10:00
 */
@Data
public class PurchaseOrderStatusCountResponse {

    /**
     * 状态
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 数量
     */
    @ChineseDescription("数量")
    private Long count;

    /**
     * 总金额
     */
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

}