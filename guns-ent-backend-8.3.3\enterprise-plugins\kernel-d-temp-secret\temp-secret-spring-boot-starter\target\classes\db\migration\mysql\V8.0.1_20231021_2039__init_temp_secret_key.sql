CREATE TABLE `sys_user_secret_key`  (
  `user_secret_key_id` bigint NOT NULL COMMENT '秘钥id',
  `secret_key_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥名称',
  `user_id` bigint NOT NULL COMMENT '所属用户id',
  `secret_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥值，经过MD5加密',
  `secret_key_salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '秘钥值的密码盐',
  `secret_expiration_time` datetime(0) NULL DEFAULT NULL COMMENT '秘钥过期时间',
  `secret_once_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '秘钥是否使用一次后删除：Y-是，N-否',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`user_secret_key_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户临时秘钥' ROW_FORMAT = Dynamic;

INSERT INTO `sys_menu`(`menu_id`, `menu_parent_id`, `menu_pids`, `menu_name`, `menu_code`, `app_id`, `menu_sort`, `status_flag`, `remark`, `menu_type`, `antdv_router`, `antdv_component`, `antdv_icon`, `antdv_link_url`, `antdv_active_url`, `antdv_visible`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1720008907121758209, -1, '[-1],', '临时秘钥', 'linshimiyao', 1671406745336016898, 105.00, 1, NULL, 10, '/system/temporaryKey', '/temporary-key/index', 'icon-menu-linshimiyao', NULL, NULL, 'Y', NULL, 3, 'N', '2023-11-02 17:24:14', 1339550467939639299, '2023-11-02 17:24:40', 1339550467939639299);
