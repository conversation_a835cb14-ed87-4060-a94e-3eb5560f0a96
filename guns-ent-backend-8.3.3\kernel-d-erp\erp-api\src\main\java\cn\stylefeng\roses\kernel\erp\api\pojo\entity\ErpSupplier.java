package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseBusinessEntity;
import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商主档案实体类
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
@TableName(value = "erp_supplier", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ErpSupplier extends BaseEntity {

    /**
     * 供应商ID
     */
    @TableId(value = "supplier_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @TableField("supplier_code")
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 供应商简称
     */
    @TableField("supplier_short_name")
    @ChineseDescription("供应商简称")
    private String supplierShortName;

    /**
     * 供应商类型（ENTERPRISE-企业，INDIVIDUAL-个体）
     */
    @TableField("supplier_type")
    @ChineseDescription("供应商类型")
    private String supplierType;

    /**
     * 所属区域ID
     */
    @TableField("region_id")
    @ChineseDescription("所属区域ID")
    private Long regionId;

    /**
     * 联系人
     */
    @TableField("contact_person")
    @ChineseDescription("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    @ChineseDescription("联系电话")
    private String contactPhone;

    /**
     * 手机号码
     */
    @TableField("contact_mobile")
    @ChineseDescription("手机号码")
    private String contactMobile;

    /**
     * 邮箱地址
     */
    @TableField("contact_email")
    @ChineseDescription("邮箱地址")
    private String contactEmail;

    /**
     * 联系地址
     */
    @TableField("contact_address")
    @ChineseDescription("联系地址")
    private String contactAddress;

    /**
     * 营业执照号
     */
    @TableField("business_license_no")
    @ChineseDescription("营业执照号")
    private String businessLicenseNo;

    /**
     * 税务登记号
     */
    @TableField("tax_no")
    @ChineseDescription("税务登记号")
    private String taxNo;

    /**
     * 开户银行
     */
    @TableField("bank_name")
    @ChineseDescription("开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @TableField("bank_account")
    @ChineseDescription("银行账号")
    private String bankAccount;

    /**
     * 信用等级（A-优秀，B-良好，C-一般，D-较差）
     */
    @TableField("credit_level")
    @ChineseDescription("信用等级")
    private String creditLevel;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，BLACKLIST-黑名单）
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 经营方式：PURCHASE_SALE(购销)、JOINT_VENTURE(联营)、CONSIGNMENT(代销)
     */
    @TableField("business_mode")
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 销售扣点（联营和代销使用）
     */
    @TableField("sales_deduction")
    @ChineseDescription("销售扣点")
    private BigDecimal salesDeduction;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

}
