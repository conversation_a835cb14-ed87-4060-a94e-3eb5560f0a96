package cn.stylefeng.roses.kernel.sync.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

/**
 * 用户和组织机构关联的信息
 *
 * <AUTHOR>
 * @since 2023/10/30 13:59
 */
@Data
public class UserOrgSyncVo {

    /**
     * 关联关系id
     */
    @ChineseDescription("关联关系id")
    private String userOrgId;

    /**
     * 用户id
     */
    @ChineseDescription("用户id")
    private String userId;

    /**
     * 所属机构id
     */
    @ChineseDescription("所属机构id")
    private String orgId;

    /**
     * 职位id
     */
    @ChineseDescription("职位id")
    private String positionId;

    /**
     * 是否是主部门：Y-是，N-不是
     */
    @ChineseDescription("是否是主部门：Y-是，N-不是")
    private String mainFlag;

}
