System.register(["./index-legacy-ee1db0c7.js","./TenantApi-legacy-40853d2f.js"],(function(e,a){"use strict";var t,n,l,d,o,i,u,s,c,v,r,m,b,f,p,h,g,x,_,y,k,w,C;return{setters:[e=>{t=e._,n=e.r,l=e.o,d=e.X,o=e.a,i=e.f,u=e.w,s=e.b,c=e.d,v=e.g,r=e.t,m=e.aR,b=e.c,f=e.F,p=e.e,h=e.aS,g=e.bK,x=e.u,_=e.v,y=e.G,k=e.H,w=e.ch},e=>{C=e.T}],execute:function(){var a=document.createElement("style");a.textContent="[data-v-a75d6508] .ant-drawer-title{color:#262626;font-size:18px;font-weight:500}.top[data-v-a75d6508]{display:flex;align-items:center;height:40px;line-height:40px;margin-bottom:14px}.top .username[data-v-a75d6508]{margin-left:8px;font-size:20px;font-weight:500;color:#43505e}.content[data-v-a75d6508]{width:100%;overflow-y:auto;overflow-x:hidden}.content .content-item[data-v-a75d6508]{width:100%;height:100%}[data-v-a75d6508] .ant-checkbox-wrapper-checked .ant-checkbox-disabled .ant-checkbox-inner{--disabled-bg: var(--primary-color);--disabled-color: #fff}[data-v-a75d6508] .ant-checkbox-disabled+span{--disabled-color: black}\n",document.head.appendChild(a);const T={class:"top"},j={class:"username"},z={class:"content"},N={class:"content-item"},I={class:"content-item"},K={__name:"tenant-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const t=e,K=a,O=n("1"),R=n([{key:"1",name:"基础信息",icon:"icon-tab-baseinfo"},{key:"2",name:"统计信息",icon:"icon-menu-zuzhijiagou"}]),S=n({}),D=n([{name:"租户名称",value:"tenantName"},{name:"租户编码",value:"tenantCode"},{name:"注册邮箱",value:"email"},{name:"注册电话",value:"safePhone"},{name:"性别",value:"sex"},{name:"租户开通时间",value:"activeDate"},{name:"租户到期时间",value:"expireDate"},{name:"创建时间",value:"createTime"},{name:"上次更新时间",value:"updateTime"}]),L=n([{name:"租户用户数",value:"tenantUserCount"},{name:"租户下公司数量",value:"tenantOrgCount"}]);l((()=>{A()})),d((()=>t.data),(e=>{e&&A()}),{deep:!0});const A=()=>{C.detail({tenantId:t.data.tenantId}).then((e=>{S.value=Object.assign({},e)}))},B=e=>{O.value=e},E=e=>{let a=e;return e&&e.length>1&&(a=e.substr(0,1)),a};return(e,a)=>{const n=g,l=x,d=_,C=y,A=k,F=w;return o(),i(F,{width:800,visible:t.visible,title:"租户信息",onClose:a[0]||(a[0]=e=>{K("update:visible",!1)}),isShowTab:!0,activeKey:O.value,tabList:R.value,onTabChange:B},{top:u((()=>[s("div",T,[c(n,{style:{"background-color":"#6f9ae7"}},{default:u((()=>[v(r(E(S.value.tenantName)),1)])),_:1}),s("span",j,r(S.value.tenantName),1)])])),default:u((()=>[s("div",z,[m(s("div",N,[c(A,{ref:"formRef",model:S.value,"label-col":{span:8}},{default:u((()=>[c(C,{gutter:16},{default:u((()=>[(o(!0),b(f,null,p(D.value,((e,a)=>(o(),i(d,{span:12,key:a},{default:u((()=>[c(l,{label:e.name},{default:u((()=>[s("span",null,r(S.value[e.value]),1)])),_:2},1032,["label"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["model"])],512),[[h,"1"==O.value]]),m(s("div",I,[c(A,{ref:"formRef",model:S.value,"label-col":{span:8}},{default:u((()=>[c(C,{gutter:16},{default:u((()=>[(o(!0),b(f,null,p(L.value,((e,a)=>(o(),i(d,{span:12,key:a},{default:u((()=>[c(l,{label:e.name},{default:u((()=>[s("span",null,r(S.value[e.value]),1)])),_:2},1032,["label"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["model"])],512),[[h,"2"==O.value]])])])),_:1},8,["visible","activeKey","tabList"])}}};e("default",t(K,[["__scopeId","data-v-a75d6508"]]))}}}));
