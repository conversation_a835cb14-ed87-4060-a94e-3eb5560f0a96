System.register(["./index-legacy-ee1db0c7.js","./index-legacy-16f295ac.js","./index-legacy-45c79de7.js","./index-legacy-94a6fc23.js","./inventoryAlert-legacy-c3ff7cb2.js"],(function(e,a){"use strict";var t,l,n,r,i,o,d,c,s,g,u,p,f,m,h,b,v,y,_,x,D,k,w,C,S,P,N,U,R,T,E;return{setters:[e=>{t=e._,l=e.r,n=e.s,r=e.o,i=e.a,o=e.c,d=e.d,c=e.w,s=e.g,g=e.b,u=e.F,p=e.h,f=e.m,m=e.a8,h=e.V,b=e.a9,v=e.u,y=e.y,_=e.l,x=e.aa,D=e.J,k=e.W,w=e.B,C=e.n,S=e.H,P=e.a0,N=e.C,U=e.ab,R=e.$,T=e.M},null,null,null,e=>{E=e.I}],execute:function(){var a=document.createElement("style");a.textContent='.ant-page-header{box-sizing:border-box;margin:0;color:var(--text-color);font-size:14px;font-variant:tabular-nums;line-height:1.5715;list-style:none;font-feature-settings:"tnum";position:relative;padding:16px 24px;background-color:var(--component-background)}.ant-page-header-ghost{background-color:var(--page-header-ghost-bg)}.ant-page-header.has-breadcrumb{padding-top:12px}.ant-page-header.has-footer{padding-bottom:0}.ant-page-header-back{margin-right:16px;font-size:16px;line-height:1}.ant-page-header-back-button{color:var(--primary-color);text-decoration:none;outline:none;transition:color .3s;color:var(--page-header-back-color);cursor:pointer}.ant-page-header-back-button:focus,.ant-page-header-back-button:hover{color:var(--primary-5)}.ant-page-header-back-button:active{color:var(--primary-7)}.ant-page-header .ant-divider-vertical{height:14px;margin:0 12px;vertical-align:middle}.ant-breadcrumb+.ant-page-header-heading{margin-top:8px}.ant-page-header-heading{display:flex;justify-content:space-between}.ant-page-header-heading-left{display:flex;align-items:center;margin:4px 0;overflow:hidden}.ant-page-header-heading-title{margin-right:12px;margin-bottom:0;color:var(--heading-color);font-weight:600;font-size:20px;line-height:32px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.ant-page-header-heading .ant-avatar{margin-right:12px}.ant-page-header-heading-sub-title{margin-right:12px;color:var(--text-color-secondary);font-size:14px;line-height:1.5715;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.ant-page-header-heading-extra{margin:4px 0;white-space:nowrap}.ant-page-header-heading-extra>*{margin-left:12px;white-space:unset}.ant-page-header-heading-extra>*:first-child{margin-left:0}.ant-page-header-content{padding-top:12px}.ant-page-header-footer{margin-top:16px}.ant-page-header-footer .ant-tabs>.ant-tabs-nav{margin:0}.ant-page-header-footer .ant-tabs>.ant-tabs-nav:before{border:none}.ant-page-header-footer .ant-tabs .ant-tabs-tab{padding-top:8px;padding-bottom:8px;font-size:16px}.ant-page-header-compact .ant-page-header-heading{flex-wrap:wrap}.ant-page-header-rtl{direction:rtl}.ant-page-header-rtl .ant-page-header-back{float:right;margin-right:0;margin-left:16px}.ant-page-header-rtl .ant-page-header-heading-title,.ant-page-header-rtl .ant-page-header-heading .ant-avatar{margin-right:0;margin-left:12px}.ant-page-header-rtl .ant-page-header-heading-sub-title{float:right;margin-right:0;margin-left:12px}.ant-page-header-rtl .ant-page-header-heading-tags{float:right}.ant-page-header-rtl .ant-page-header-heading-extra{float:left}.ant-page-header-rtl .ant-page-header-heading-extra>*{margin-right:12px;margin-left:0}.ant-page-header-rtl .ant-page-header-heading-extra>*:first-child{margin-right:0}.ant-page-header-rtl .ant-page-header-footer .ant-tabs-bar .ant-tabs-nav{float:right}.inventory-alert-config-container[data-v-64475a7c]{padding:16px}.config-card[data-v-64475a7c]{margin-top:16px}.form-item-desc[data-v-64475a7c]{color:#999;font-size:12px;margin-top:4px}[data-v-64475a7c] .ant-divider-horizontal.ant-divider-with-text-left{margin:24px 0 16px}[data-v-64475a7c] .ant-form-item{margin-bottom:24px}\n',document.head.appendChild(a);const I={class:"inventory-alert-config-container"};e("default",t({name:"InventoryAlertConfigIndex",setup(){const e=l(),a=l(),t=l(!1),i=l(!1),o=l(["SYSTEM"]),d=l("这是一条测试预警通知消息"),c=n({enableAlert:!0,globalCheckFrequency:30,maxCheckProducts:1e3,alertRecordRetentionDays:90,enableEmailNotify:!1,smtpServer:"",smtpPort:587,senderEmail:"",emailPassword:"",defaultRecipients:[],enableSmsNotify:!1,smsProvider:"",smsAccessKey:"",smsSecretKey:"",smsTemplateId:"",defaultPhones:[],alertDeduplicationTime:60,batchProcessSize:100,enableDebugMode:!1}),s=n({globalCheckFrequency:[{required:!0,message:"请输入全局检查频率",trigger:"blur"},{type:"number",min:1,max:1440,message:"检查频率必须在1-1440分钟之间",trigger:"blur"}],maxCheckProducts:[{required:!0,message:"请输入最大检查商品数",trigger:"blur"},{type:"number",min:100,max:1e4,message:"最大检查商品数必须在100-10000之间",trigger:"blur"}],alertRecordRetentionDays:[{required:!0,message:"请输入预警记录保留天数",trigger:"blur"},{type:"number",min:7,max:365,message:"保留天数必须在7-365天之间",trigger:"blur"}],smtpServer:[{validator:(e,a)=>c.enableEmailNotify&&!a?Promise.reject("请输入SMTP服务器地址"):Promise.resolve(),trigger:"blur"}],senderEmail:[{validator:(e,a)=>c.enableEmailNotify&&!a?Promise.reject("请输入发件人邮箱"):a&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)?Promise.reject("请输入正确的邮箱格式"):Promise.resolve(),trigger:"blur"}]}),g=async()=>{try{const e=await E.getConfig();Object.assign(c,e.data)}catch(e){console.error("获取配置失败:",e),f.error("获取配置失败")}};return r((()=>{g()})),{formRef:e,importFileRef:a,submitLoading:t,testNotifyVisible:i,testNotifyTypes:o,testMessage:d,configData:c,rules:s,handleSubmit:async()=>{try{await e.value.validate(),t.value=!0,await E.updateConfig(c),f.success("配置保存成功")}catch(a){console.error("保存配置失败:",a),f.error("保存配置失败")}finally{t.value=!1}},handleReset:()=>{g()},handleTestNotify:()=>{i.value=!0},handleTestNotifyConfirm:async()=>{try{await E.testNotify({types:o.value,message:d.value}),f.success("测试通知发送成功"),i.value=!1}catch(e){console.error("测试通知失败:",e),f.error("测试通知失败")}},handleExportConfig:()=>{try{const e=new Blob([JSON.stringify(c,null,2)],{type:"application/json"}),a=URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download=`库存预警配置_${(new Date).getTime()}.json`,t.click(),URL.revokeObjectURL(a),f.success("配置导出成功")}catch(e){console.error("导出配置失败:",e),f.error("导出配置失败")}},handleImportConfig:()=>{a.value.click()},handleImportFileChange:e=>{const a=e.target.files[0];if(!a)return;const t=new FileReader;t.onload=e=>{try{const a=JSON.parse(e.target.result);Object.assign(c,a),f.success("配置导入成功")}catch(a){console.error("导入配置失败:",a),f.error("配置文件格式错误")}},t.readAsText(a),e.target.value=""}}}},[["render",function(e,a,t,l,n,r){const f=m,E=h,M=b,j=v,A=y,F=_,z=x,K=D,q=k,L=w,O=C,V=S,J=P,W=N,Y=U,$=R,B=T;return i(),o("div",I,[d(f,{title:"库存预警配置","sub-title":"配置库存预警系统的全局参数和通知设置"}),d(J,{bordered:!1,class:"config-card"},{default:c((()=>[d(V,{ref:"formRef",model:l.configData,rules:l.rules,"label-col":{span:6},"wrapper-col":{span:12},onFinish:l.handleSubmit},{default:c((()=>[d(E,{orientation:"left"},{default:c((()=>a[23]||(a[23]=[s("基础配置")]))),_:1,__:[23]}),d(j,{label:"启用预警检查",name:"enableAlert"},{default:c((()=>[d(M,{checked:l.configData.enableAlert,"onUpdate:checked":a[0]||(a[0]=e=>l.configData.enableAlert=e),"checked-children":"启用","un-checked-children":"禁用"},null,8,["checked"]),a[24]||(a[24]=g("div",{class:"form-item-desc"}," 控制是否启用自动预警检查功能 ",-1))])),_:1,__:[24]}),d(j,{label:"全局检查频率",name:"globalCheckFrequency"},{default:c((()=>[d(A,{value:l.configData.globalCheckFrequency,"onUpdate:value":a[1]||(a[1]=e=>l.configData.globalCheckFrequency=e),min:1,max:1440,style:{width:"200px"},"addon-after":"分钟"},null,8,["value"]),a[25]||(a[25]=g("div",{class:"form-item-desc"}," 全局预警检查频率，单位：分钟（1-1440分钟） ",-1))])),_:1,__:[25]}),d(j,{label:"最大检查商品数",name:"maxCheckProducts"},{default:c((()=>[d(A,{value:l.configData.maxCheckProducts,"onUpdate:value":a[2]||(a[2]=e=>l.configData.maxCheckProducts=e),min:100,max:1e4,style:{width:"200px"},"addon-after":"个"},null,8,["value"]),a[26]||(a[26]=g("div",{class:"form-item-desc"}," 单次检查的最大商品数量，避免系统负载过高 ",-1))])),_:1,__:[26]}),d(j,{label:"预警记录保留天数",name:"alertRecordRetentionDays"},{default:c((()=>[d(A,{value:l.configData.alertRecordRetentionDays,"onUpdate:value":a[3]||(a[3]=e=>l.configData.alertRecordRetentionDays=e),min:7,max:365,style:{width:"200px"},"addon-after":"天"},null,8,["value"]),a[27]||(a[27]=g("div",{class:"form-item-desc"}," 预警记录在系统中的保留天数，超期自动清理 ",-1))])),_:1,__:[27]}),d(E,{orientation:"left"},{default:c((()=>a[28]||(a[28]=[s("通知配置")]))),_:1,__:[28]}),d(j,{label:"启用邮件通知",name:"enableEmailNotify"},{default:c((()=>[d(M,{checked:l.configData.enableEmailNotify,"onUpdate:checked":a[4]||(a[4]=e=>l.configData.enableEmailNotify=e),"checked-children":"启用","un-checked-children":"禁用"},null,8,["checked"])])),_:1}),l.configData.enableEmailNotify?(i(),o(u,{key:0},[d(j,{label:"SMTP服务器",name:"smtpServer"},{default:c((()=>[d(F,{value:l.configData.smtpServer,"onUpdate:value":a[5]||(a[5]=e=>l.configData.smtpServer=e),placeholder:"请输入SMTP服务器地址"},null,8,["value"])])),_:1}),d(j,{label:"SMTP端口",name:"smtpPort"},{default:c((()=>[d(A,{value:l.configData.smtpPort,"onUpdate:value":a[6]||(a[6]=e=>l.configData.smtpPort=e),min:1,max:65535,style:{width:"200px"}},null,8,["value"])])),_:1}),d(j,{label:"发件人邮箱",name:"senderEmail"},{default:c((()=>[d(F,{value:l.configData.senderEmail,"onUpdate:value":a[7]||(a[7]=e=>l.configData.senderEmail=e),placeholder:"请输入发件人邮箱"},null,8,["value"])])),_:1}),d(j,{label:"邮箱密码",name:"emailPassword"},{default:c((()=>[d(z,{value:l.configData.emailPassword,"onUpdate:value":a[8]||(a[8]=e=>l.configData.emailPassword=e),placeholder:"请输入邮箱密码或授权码"},null,8,["value"])])),_:1}),d(j,{label:"默认收件人",name:"defaultRecipients"},{default:c((()=>[d(K,{value:l.configData.defaultRecipients,"onUpdate:value":a[9]||(a[9]=e=>l.configData.defaultRecipients=e),mode:"tags",placeholder:"请输入收件人邮箱，支持多个",style:{width:"100%"}},null,8,["value"]),a[29]||(a[29]=g("div",{class:"form-item-desc"}," 默认的预警通知收件人，可输入多个邮箱地址 ",-1))])),_:1,__:[29]})],64)):p("",!0),d(j,{label:"启用短信通知",name:"enableSmsNotify"},{default:c((()=>[d(M,{checked:l.configData.enableSmsNotify,"onUpdate:checked":a[10]||(a[10]=e=>l.configData.enableSmsNotify=e),"checked-children":"启用","un-checked-children":"禁用"},null,8,["checked"])])),_:1}),l.configData.enableSmsNotify?(i(),o(u,{key:1},[d(j,{label:"短信服务商",name:"smsProvider"},{default:c((()=>[d(K,{value:l.configData.smsProvider,"onUpdate:value":a[11]||(a[11]=e=>l.configData.smsProvider=e),placeholder:"请选择短信服务商"},{default:c((()=>[d(q,{value:"ALIYUN"},{default:c((()=>a[30]||(a[30]=[s("阿里云")]))),_:1,__:[30]}),d(q,{value:"TENCENT"},{default:c((()=>a[31]||(a[31]=[s("腾讯云")]))),_:1,__:[31]}),d(q,{value:"HUAWEI"},{default:c((()=>a[32]||(a[32]=[s("华为云")]))),_:1,__:[32]})])),_:1},8,["value"])])),_:1}),d(j,{label:"AccessKey",name:"smsAccessKey"},{default:c((()=>[d(F,{value:l.configData.smsAccessKey,"onUpdate:value":a[12]||(a[12]=e=>l.configData.smsAccessKey=e),placeholder:"请输入AccessKey"},null,8,["value"])])),_:1}),d(j,{label:"SecretKey",name:"smsSecretKey"},{default:c((()=>[d(z,{value:l.configData.smsSecretKey,"onUpdate:value":a[13]||(a[13]=e=>l.configData.smsSecretKey=e),placeholder:"请输入SecretKey"},null,8,["value"])])),_:1}),d(j,{label:"短信模板ID",name:"smsTemplateId"},{default:c((()=>[d(F,{value:l.configData.smsTemplateId,"onUpdate:value":a[14]||(a[14]=e=>l.configData.smsTemplateId=e),placeholder:"请输入短信模板ID"},null,8,["value"])])),_:1}),d(j,{label:"默认接收手机号",name:"defaultPhones"},{default:c((()=>[d(K,{value:l.configData.defaultPhones,"onUpdate:value":a[15]||(a[15]=e=>l.configData.defaultPhones=e),mode:"tags",placeholder:"请输入手机号，支持多个",style:{width:"100%"}},null,8,["value"]),a[33]||(a[33]=g("div",{class:"form-item-desc"}," 默认的预警通知接收手机号，可输入多个 ",-1))])),_:1,__:[33]})],64)):p("",!0),d(E,{orientation:"left"},{default:c((()=>a[34]||(a[34]=[s("高级配置")]))),_:1,__:[34]}),d(j,{label:"预警去重时间",name:"alertDeduplicationTime"},{default:c((()=>[d(A,{value:l.configData.alertDeduplicationTime,"onUpdate:value":a[16]||(a[16]=e=>l.configData.alertDeduplicationTime=e),min:1,max:1440,style:{width:"200px"},"addon-after":"分钟"},null,8,["value"]),a[35]||(a[35]=g("div",{class:"form-item-desc"}," 相同商品的预警去重时间，避免频繁发送重复预警 ",-1))])),_:1,__:[35]}),d(j,{label:"批量处理大小",name:"batchProcessSize"},{default:c((()=>[d(A,{value:l.configData.batchProcessSize,"onUpdate:value":a[17]||(a[17]=e=>l.configData.batchProcessSize=e),min:10,max:1e3,style:{width:"200px"},"addon-after":"条"},null,8,["value"]),a[36]||(a[36]=g("div",{class:"form-item-desc"}," 批量处理预警记录的单次处理数量 ",-1))])),_:1,__:[36]}),d(j,{label:"启用调试模式",name:"enableDebugMode"},{default:c((()=>[d(M,{checked:l.configData.enableDebugMode,"onUpdate:checked":a[18]||(a[18]=e=>l.configData.enableDebugMode=e),"checked-children":"启用","un-checked-children":"禁用"},null,8,["checked"]),a[37]||(a[37]=g("div",{class:"form-item-desc"}," 启用后会记录详细的调试日志，便于问题排查 ",-1))])),_:1,__:[37]}),d(j,{"wrapper-col":{offset:6,span:12}},{default:c((()=>[d(O,null,{default:c((()=>[d(L,{type:"primary","html-type":"submit",loading:l.submitLoading},{default:c((()=>a[38]||(a[38]=[s(" 保存配置 ")]))),_:1,__:[38]},8,["loading"]),d(L,{onClick:l.handleReset},{default:c((()=>a[39]||(a[39]=[s(" 重置 ")]))),_:1,__:[39]},8,["onClick"]),d(L,{onClick:l.handleTestNotify},{default:c((()=>a[40]||(a[40]=[s(" 测试通知 ")]))),_:1,__:[40]},8,["onClick"]),d(L,{onClick:l.handleExportConfig},{default:c((()=>a[41]||(a[41]=[s(" 导出配置 ")]))),_:1,__:[41]},8,["onClick"]),d(L,{onClick:l.handleImportConfig},{default:c((()=>a[42]||(a[42]=[s(" 导入配置 ")]))),_:1,__:[42]},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model","rules","onFinish"])])),_:1}),d(B,{visible:l.testNotifyVisible,"onUpdate:visible":a[21]||(a[21]=e=>l.testNotifyVisible=e),title:"测试通知",onOk:l.handleTestNotifyConfirm},{default:c((()=>[d(V,{"label-col":{span:6},"wrapper-col":{span:16}},{default:c((()=>[d(j,{label:"通知类型"},{default:c((()=>[d(Y,{value:l.testNotifyTypes,"onUpdate:value":a[19]||(a[19]=e=>l.testNotifyTypes=e)},{default:c((()=>[d(W,{value:"EMAIL",disabled:!l.configData.enableEmailNotify},{default:c((()=>a[43]||(a[43]=[s(" 邮件通知 ")]))),_:1,__:[43]},8,["disabled"]),d(W,{value:"SMS",disabled:!l.configData.enableSmsNotify},{default:c((()=>a[44]||(a[44]=[s(" 短信通知 ")]))),_:1,__:[44]},8,["disabled"]),d(W,{value:"SYSTEM"},{default:c((()=>a[45]||(a[45]=[s("系统消息")]))),_:1,__:[45]})])),_:1},8,["value"])])),_:1}),d(j,{label:"测试消息"},{default:c((()=>[d($,{value:l.testMessage,"onUpdate:value":a[20]||(a[20]=e=>l.testMessage=e),placeholder:"请输入测试消息内容",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1},8,["visible","onOk"]),g("input",{ref:"importFileRef",type:"file",accept:".json",style:{display:"none"},onChange:a[22]||(a[22]=(...e)=>l.handleImportFileChange&&l.handleImportFileChange(...e))},null,544)])}],["__scopeId","data-v-64475a7c"]]))}}}));
