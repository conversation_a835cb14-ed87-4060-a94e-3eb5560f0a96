package cn.stylefeng.roses.ent.mobile.manage.enums;

import lombok.Getter;

/**
 * 通讯录元素类型：1-公司，2-部门，3-人员
 *
 * <AUTHOR>
 * @since 2024/3/21 22:34
 */
@Getter
public enum AddressBookTypeEnum {

    /**
     * 公司
     */
    COMPANY(1, "公司"),

    /**
     * 部门
     */
    DEPT(2, "部门"),

    /**
     * 人员
     */
    USER(3, "人员");

    private final Integer code;

    private final String message;

    AddressBookTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
