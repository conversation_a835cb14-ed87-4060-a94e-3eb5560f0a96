package cn.stylefeng.roses.kernel.micro.loadbalancer;

import com.alibaba.nacos.client.naming.utils.Chooser;
import com.alibaba.nacos.client.naming.utils.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.DefaultResponse;
import org.springframework.cloud.client.loadbalancer.EmptyResponse;
import org.springframework.cloud.client.loadbalancer.Request;
import org.springframework.cloud.client.loadbalancer.Response;
import org.springframework.cloud.loadbalancer.core.NoopServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于nacos权重的客户端负载均衡策略
 *
 * <AUTHOR>
 * @date 2021/5/15 14:42
 */
@Slf4j
public class NacosWeightedLoadBalancer implements ReactorServiceInstanceLoadBalancer {

    private static final String PACKAGE_NAME = "cn.stylefeng";

    private final String serviceId;
    private final ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider;

    public NacosWeightedLoadBalancer(ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider, String serviceId) {
        this.serviceId = serviceId;
        this.serviceInstanceListSupplierProvider = serviceInstanceListSupplierProvider;
    }

    @Override
    public Mono<Response<ServiceInstance>> choose(Request request) {
        ServiceInstanceListSupplier supplier = serviceInstanceListSupplierProvider
                .getIfAvailable(NoopServiceInstanceListSupplier::new);
        return supplier.get().next().map(this::getInstanceResponse);
    }

    /**
     * 权重计算，获取计算出的service实例
     *
     * <AUTHOR>
     * @date 2021/5/15 14:48
     */
    private Response<ServiceInstance> getInstanceResponse(List<ServiceInstance> instances) {
        if (instances.isEmpty()) {
            log.warn("No servers available for service: " + this.serviceId);
            return new EmptyResponse();
        }
        ServiceInstance instance = getHostByRandomWeight(instances);
        return new DefaultResponse(instance);
    }

    /**
     * 通过传入的service多实例列表，进行权重计算，返回最终计算出的service实例
     *
     * <AUTHOR>
     * @date 2021/5/15 14:45
     */
    protected ServiceInstance getHostByRandomWeight(List<ServiceInstance> serviceInstances) {
        if (serviceInstances == null || serviceInstances.size() == 0) {
            log.debug("serviceInstances == null || serviceInstances.size() == 0");
            return null;
        }
        Chooser<String, ServiceInstance> instanceChooser = new Chooser<>(PACKAGE_NAME);
        List<Pair<ServiceInstance>> hostsWithWeight = serviceInstances.stream().map(serviceInstance -> new Pair<>(serviceInstance, getWeight(serviceInstance))).collect(Collectors.toList());
        instanceChooser.refresh(hostsWithWeight);
        return instanceChooser.randomWithWeight();
    }

    /**
     * 获取对应instance的权重
     *
     * <AUTHOR>
     * @date 2021/5/15 14:44
     */
    protected double getWeight(ServiceInstance serviceInstance) {
        return Double.parseDouble(serviceInstance.getMetadata().get("nacos.weight"));
    }

}