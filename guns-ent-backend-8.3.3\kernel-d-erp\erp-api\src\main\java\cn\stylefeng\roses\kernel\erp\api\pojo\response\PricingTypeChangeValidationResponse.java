package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.util.List;

/**
 * 计价类型变更验证响应
 *
 * <AUTHOR>
 * @since 2025/07/27 23:05
 */
@Data
public class PricingTypeChangeValidationResponse {

    /**
     * 是否可以变更
     */
    @ChineseDescription("是否可以变更")
    private Boolean canChange;

    /**
     * 影响的库存记录数量
     */
    @ChineseDescription("影响的库存记录数量")
    private Integer affectedInventoryCount;

    /**
     * 影响的销售记录数量
     */
    @ChineseDescription("影响的销售记录数量")
    private Integer affectedSalesCount;

    /**
     * 需要重新设置的价格字段
     */
    @ChineseDescription("需要重新设置的价格字段")
    private List<String> requiredPriceFields;

    /**
     * 警告信息
     */
    @ChineseDescription("警告信息")
    private String warningMessage;

    /**
     * 详细说明
     */
    @ChineseDescription("详细说明")
    private String description;

    /**
     * 错误信息
     */
    @ChineseDescription("错误信息")
    private String errorMessage;

    /**
     * 错误列表
     */
    @ChineseDescription("错误列表")
    private List<String> errors;

    /**
     * 警告列表
     */
    @ChineseDescription("警告列表")
    private List<String> warnings;

    /**
     * 建议信息
     */
    @ChineseDescription("建议信息")
    private String suggestion;

}