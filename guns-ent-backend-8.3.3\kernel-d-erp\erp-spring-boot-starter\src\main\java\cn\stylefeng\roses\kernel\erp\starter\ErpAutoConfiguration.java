package cn.stylefeng.roses.kernel.erp.starter;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * ERP模块自动配置
 *
 * <AUTHOR>
 * @since 2025/07/17 20:55
 */
@AutoConfiguration
@ComponentScan(basePackages = {
        "cn.stylefeng.roses.kernel.erp.modular"
})
@MapperScan(basePackages = {
        "cn.stylefeng.roses.kernel.erp.modular.*.mapper"
})
public class ErpAutoConfiguration {

    /**
     * ERP模块配置Bean
     *
     * @return ERP配置
     * <AUTHOR>
     * @since 2025/07/20 14:30
     */
    @Bean
    @ConditionalOnMissingBean(ErpModuleConfig.class)
    public ErpModuleConfig erpModuleConfig() {
        return new ErpModuleConfig();
    }

}
