import{N as f}from"./constants-2fa70699.js";class h{static formatCurrency(t,o={}){const{currency:e="\xA5",precision:r=f.AMOUNT_PRECISION,showSymbol:a=!0,showThousandsSeparator:n=!0}=o;if(typeof t!="number"||isNaN(t))return a?"".concat(e,"0.00"):"0.00";let c=(Math.round(t*Math.pow(10,r))/Math.pow(10,r)).toFixed(r);if(n){const i=c.split(".");i[0]=i[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),c=i.join(".")}return a?"".concat(e).concat(c):c}static formatPercentage(t,o={}){const{precision:e=f.DISCOUNT_PRECISION,showSymbol:r=!0}=o;if(typeof t!="number"||isNaN(t))return r?"0.00%":"0.00";const a=(t*100).toFixed(e);return r?"".concat(a,"%"):a}static formatQuantity(t,o={}){const{precision:e=f.QUANTITY_PRECISION,unit:r="",showUnit:a=!1}=o;if(typeof t!="number"||isNaN(t))return a&&r?"0".concat(r):"0";const n=parseFloat(t.toFixed(e)).toString();return a&&r?"".concat(n).concat(r):n}static formatDiscountDisplay(t,o,e={}){const r=this.formatCurrency(t,e),a=this.formatCurrency(o,e),n=t-o,s=this.formatCurrency(n,e);return o>0?"".concat(r," - ").concat(a," = ").concat(s):r}static formatChangeDisplay(t,o,e={}){const r=Math.max(0,t-o),a=this.formatCurrency(t,e),n=this.formatCurrency(o,e),s=this.formatCurrency(r,e);return"\u5B9E\u6536: ".concat(a," | \u5E94\u4ED8: ").concat(n," | \u627E\u96F6: ").concat(s)}}export{h as A};
