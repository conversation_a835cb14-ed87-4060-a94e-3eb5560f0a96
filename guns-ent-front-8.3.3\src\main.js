import { createApp } from 'vue';
import store from './store';
import i18n from './i18n';
import App from './App.vue';
import router from './router';
import './styles/index.less';
import Antd from 'ant-design-vue';
import './assets/iconfont/iconfont';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css'
import directive from './directive/index';
import permission from './utils/permission';
import * as antIcons from '@ant-design/icons-vue';

// json-edit-vue3
import "jsoneditor";

import VueMarkdownEditor from '@kangc/v-md-editor';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js';
import '@kangc/v-md-editor/lib/theme/style/vuepress.css';
import Prism from 'prismjs';
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
// 引入你所使用的主题 此处以 github 主题为例
import githubTheme from '@kangc/v-md-editor/lib/theme/github';
import '@kangc/v-md-editor/lib/theme/style/github.css';
// highlightjs
import hljs from 'highlight.js';

VMdPreview.use(githubTheme, {
  Hljs: hljs,
});
VueMarkdownEditor.use(vuepressTheme, {
  Prism,
});

const app = createApp(App);

app.use(store);
app.use(i18n);
app.use(router);
app.use(permission);
app.use(VXETable);
app.use(VMdPreview);
app.use(VueMarkdownEditor);

directive(app);

app.use(Antd).mount('#app');

// 注册图标组件到全局
Object.keys(antIcons).forEach(key => {
  app.component(key, antIcons[key]);
});
app.config.globalProperties.$antIcons = antIcons;
