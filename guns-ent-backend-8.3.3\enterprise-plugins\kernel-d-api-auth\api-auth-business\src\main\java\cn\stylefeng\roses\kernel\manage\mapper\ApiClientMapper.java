package cn.stylefeng.roses.kernel.manage.mapper;

import cn.stylefeng.roses.kernel.manage.entity.ApiClient;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiClientRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.ApiClientVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API客户端 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
public interface ApiClientMapper extends BaseMapper<ApiClient> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    List<ApiClientVo> customFindList(@Param("page") Page page, @Param("param") ApiClientRequest request);

}
