# 区域整合功能集成测试文档

## 概述

本文档描述了区域管理与供应商、客户管理系统整合功能的集成测试。这些测试验证了系统需求文档中定义的所有功能需求。

## 测试覆盖范围

### 需求覆盖

| 需求编号 | 需求描述 | 测试类 | 测试方法 |
|---------|---------|--------|---------|
| 1.1-1.4 | 供应商-区域关联管理 | SupplierRegionIntegrationTest | testSupplierRegionAssociation |
| 1.5 | 区域删除前检查关联供应商 | RegionIntegrationTest | testRegionCRUD |
| 2.1-2.4 | 客户-区域关联管理 | CustomerRegionIntegrationTest | testCustomerRegionAssociation |
| 2.5 | 区域删除前检查关联客户 | RegionIntegrationTest | testRegionCRUD |
| 3.1-3.2 | 区域树结构显示 | RegionFilterIntegrationTest | testRegionTreeInFiltering |
| 3.3 | 区域筛选功能 | RegionFilterIntegrationTest | testFilterSuppliersByChildRegion, testFilterCustomersByChildRegion |
| 3.4 | 父节点级联查询 | RegionFilterIntegrationTest | testFilterByParentRegionWithCascade |
| 3.5 | 取消选择显示全部数据 | RegionFilterIntegrationTest | testShowAllDataWhenNoRegionSelected |
| 4.1-4.2 | 数据库关联设计 | 所有测试类 | 数据库操作验证 |
| 4.3-4.4 | 查询性能优化 | 所有测试类 | 分页查询测试 |
| 4.5 | 数据完整性 | 所有测试类 | 级联操作测试 |

### 测试类说明

#### 1. RegionIntegrationTest
- **功能**: 区域管理基础功能测试
- **测试内容**:
  - 区域CRUD操作
  - 区域树形结构查询
  - 区域分页查询
  - 区域编码重复校验
  - 区域状态更新
  - 区域选择器数据查询

#### 2. SupplierRegionIntegrationTest
- **功能**: 供应商-区域关联功能测试
- **测试内容**:
  - 供应商关联区域功能
  - 根据区域查询供应商
  - 统计区域关联的供应商数量
  - 供应商信息填充区域信息
  - 供应商区域关联的级联操作

#### 3. CustomerRegionIntegrationTest
- **功能**: 客户-区域关联功能测试
- **测试内容**:
  - 客户关联区域功能
  - 根据区域查询客户
  - 统计区域关联的客户数量
  - 客户区域关联的级联操作
  - 多客户关联同一区域

#### 4. RegionFilterIntegrationTest
- **功能**: 区域筛选功能测试
- **测试内容**:
  - 通过子区域筛选供应商/客户数据
  - 通过父区域筛选数据（级联查询子节点）
  - 区域树结构在筛选中的应用
  - 取消区域选择显示全部数据
  - 区域关联数量统计

## 运行测试

### 环境要求

- Java 17+
- Maven 3.6+
- Spring Boot 3.2.10

### 运行方式

#### 方式1: 使用脚本运行

**Windows:**
```bash
run-integration-tests.bat
```

**Linux/Mac:**
```bash
chmod +x run-integration-tests.sh
./run-integration-tests.sh
```

#### 方式2: 使用Maven命令

```bash
# 运行所有集成测试
mvn clean test -Dtest=RegionIntegrationTestSuite -Dspring.profiles.active=test

# 运行单个测试类
mvn test -Dtest=RegionIntegrationTest -Dspring.profiles.active=test
mvn test -Dtest=SupplierRegionIntegrationTest -Dspring.profiles.active=test
mvn test -Dtest=CustomerRegionIntegrationTest -Dspring.profiles.active=test
mvn test -Dtest=RegionFilterIntegrationTest -Dspring.profiles.active=test
```

#### 方式3: 在IDE中运行

1. 导入项目到IDE（IntelliJ IDEA或Eclipse）
2. 确保测试配置文件 `application-test.yml` 在classpath中
3. 右键点击测试类或测试套件，选择"Run"

### 测试配置

测试使用H2内存数据库，配置文件位于：
- `src/test/resources/application-test.yml`

主要配置：
- 数据源：H2内存数据库
- 事务：自动回滚
- 日志：DEBUG级别
- JPA：自动创建表结构

## 测试数据

测试使用程序化创建的测试数据，每个测试方法都会：
1. 在 `@BeforeEach` 中创建必要的测试数据
2. 执行测试逻辑
3. 通过 `@Transactional` 注解自动回滚数据

### 测试数据结构

- **区域数据**: 创建父子区域结构用于测试树形查询和级联筛选
- **供应商数据**: 创建多个供应商用于测试关联功能
- **客户数据**: 创建多个客户用于测试关联功能
- **关联数据**: 建立供应商/客户与区域的多对多关联关系

## 预期结果

### 成功标准

所有测试通过时，表示以下功能正常：

1. ✅ 区域管理基础功能完整
2. ✅ 供应商-区域关联功能正常
3. ✅ 客户-区域关联功能正常
4. ✅ 区域筛选功能正确
5. ✅ 数据完整性得到保证
6. ✅ 性能查询优化有效

### 测试报告

测试完成后会生成：
- Maven Surefire测试报告：`target/surefire-reports/`
- 控制台输出包含详细的测试执行信息

## 故障排除

### 常见问题

1. **数据库连接问题**
   - 确保H2数据库依赖已添加
   - 检查 `application-test.yml` 配置

2. **依赖注入失败**
   - 确保所有必要的Spring Boot Starter已添加
   - 检查组件扫描路径

3. **测试数据问题**
   - 检查 `@BeforeEach` 方法中的数据创建逻辑
   - 确保事务配置正确

4. **断言失败**
   - 检查业务逻辑实现是否符合需求
   - 验证数据库表结构和关联关系

### 调试建议

1. 启用SQL日志查看数据库操作
2. 使用断点调试测试方法
3. 检查H2控制台查看数据状态
4. 查看详细的异常堆栈信息

## 维护说明

### 添加新测试

1. 在相应的测试类中添加新的测试方法
2. 使用 `@Test` 和 `@DisplayName` 注解
3. 遵循AAA模式（Arrange-Act-Assert）
4. 更新本文档的测试覆盖表

### 修改现有测试

1. 确保修改不影响其他测试
2. 更新相关的测试数据和断言
3. 运行完整测试套件验证
4. 更新文档说明

## 联系信息

如有问题，请联系开发团队或查看项目文档。