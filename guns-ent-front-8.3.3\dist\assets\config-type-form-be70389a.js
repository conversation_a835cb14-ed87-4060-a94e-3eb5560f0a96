import{s as i,a as p,f as _,w as o,d as e,l as d,u as g,v as y,y as v,G as T,H as b}from"./index-18a1ea24.js";/* empty css              */const N={__name:"config-type-form",props:{form:Object},setup(n){const m=i({configTypeName:[{required:!0,message:"\u8BF7\u8F93\u5165\u914D\u7F6E\u5206\u7C7B\u540D\u79F0",type:"string",trigger:"blur"}],configTypeCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u914D\u7F6E\u5206\u7C7B\u7F16\u7801",type:"string",trigger:"blur"}],configTypeSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u6392\u5E8F",type:"number",trigger:"blur"}]});return(w,t)=>{const u=d,l=g,r=y,f=v,s=T,c=b;return p(),_(c,{ref:"formRef",model:n.form,rules:m,layout:"vertical"},{default:o(()=>[e(s,{gutter:20},{default:o(()=>[e(r,{xs:24,sm:24,md:12},{default:o(()=>[e(l,{label:"\u914D\u7F6E\u5206\u7C7B\u540D\u79F0:",name:"configTypeName"},{default:o(()=>[e(u,{value:n.form.configTypeName,"onUpdate:value":t[0]||(t[0]=a=>n.form.configTypeName=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u914D\u7F6E\u5206\u7C7B\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:o(()=>[e(l,{label:"\u914D\u7F6E\u5206\u7C7B\u7F16\u7801:",name:"configTypeCode"},{default:o(()=>[e(u,{value:n.form.configTypeCode,"onUpdate:value":t[1]||(t[1]=a=>n.form.configTypeCode=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u914D\u7F6E\u5206\u7C7B\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:o(()=>[e(l,{label:"\u6392\u5E8F:",name:"configTypeSort"},{default:o(()=>[e(f,{value:n.form.configTypeSort,"onUpdate:value":t[2]||(t[2]=a=>n.form.configTypeSort=a),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{N as default};
