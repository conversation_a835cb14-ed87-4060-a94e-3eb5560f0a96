System.register(["./constants-legacy-2a31d63c.js"],(function(t,r){"use strict";var e;return{setters:[t=>{e=t.N}],execute:function(){t("A",class{static formatCurrency(t,r={}){const{currency:n="¥",precision:s=e.AMOUNT_PRECISION,showSymbol:o=!0,showThousandsSeparator:i=!0}=r;if("number"!=typeof t||isNaN(t))return o?`${n}0.00`:"0.00";let a=(Math.round(t*Math.pow(10,s))/Math.pow(10,s)).toFixed(s);if(i){const t=a.split(".");t[0]=t[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),a=t.join(".")}return o?`${n}${a}`:a}static formatPercentage(t,r={}){const{precision:n=e.DISCOUNT_PRECISION,showSymbol:s=!0}=r;if("number"!=typeof t||isNaN(t))return s?"0.00%":"0.00";const o=(100*t).toFixed(n);return s?`${o}%`:o}static formatQuantity(t,r={}){const{precision:n=e.QUANTITY_PRECISION,unit:s="",showUnit:o=!1}=r;if("number"!=typeof t||isNaN(t))return o&&s?`0${s}`:"0";const i=parseFloat(t.toFixed(n)).toString();return o&&s?`${i}${s}`:i}static formatDiscountDisplay(t,r,e={}){const n=this.formatCurrency(t,e),s=this.formatCurrency(r,e),o=t-r,i=this.formatCurrency(o,e);return r>0?`${n} - ${s} = ${i}`:n}static formatChangeDisplay(t,r,e={}){const n=Math.max(0,t-r);return`实收: ${this.formatCurrency(t,e)} | 应付: ${this.formatCurrency(r,e)} | 找零: ${this.formatCurrency(n,e)}`}})}}}));
