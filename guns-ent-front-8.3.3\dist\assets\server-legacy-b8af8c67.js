System.register(["./index-legacy-ee1db0c7.js","./index-legacy-e24582b9.js","./index-legacy-45c79de7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,a){"use strict";var t,s,l,d,o,n,c,m,u,r,i,b,y,f,p,v,g;return{setters:[e=>{t=e.R,s=e._,l=e.s,d=e.o,o=e.a,n=e.c,c=e.b,m=e.d,u=e.w,r=e.g,i=e.t,b=e.i,y=e.a0,f=e.v,p=e.G,v=e.Y,g=e.Z},null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent="[data-v-440b7f5e] .ant-card-head{border-bottom:0px}.bottom-1[data-v-440b7f5e]{border-bottom:1px solid rgba(197,207,209,.6)}.block-space-top[data-v-440b7f5e]{padding:10px 0 0}.block-space-right[data-v-440b7f5e]{border-right:1px solid rgba(197,207,209,.6);padding:0 5px 0 0}[data-v-440b7f5e] .ant-card-body{padding:12px 24px 0}.block-space-left[data-v-440b7f5e]{padding:0 0 0 5px}.card-width[data-v-440b7f5e]{width:100%}.guns-body[data-v-440b7f5e]{overflow-y:auto}.guns-body .ant-row[data-v-440b7f5e],.guns-body .ant-col[data-v-440b7f5e]{height:auto}[data-v-440b7f5e] .ant-descriptions-item{padding:0}[data-v-440b7f5e] .ant-descriptions-item-container{padding:16px 0;margin:0 16px;border-bottom:1px solid rgba(197,207,209,.6)}[data-v-440b7f5e] .ant-descriptions-item-label{width:172px}[data-v-440b7f5e] .b-r-1{border-right:1px solid rgba(197,207,209,.6)!important}@media screen and (max-width: 768px){[data-v-440b7f5e] .ant-descriptions-item-container{flex-direction:column}[data-v-440b7f5e] .b-r-1{border-right-width:0!important}}\n",document.head.appendChild(a);class x{static getSystemInfo(e){return t.getAndLoadData("/getSystemInfo",e)}}const I={class:"guns-layout"},_={class:"guns-layout-content"},k={class:"guns-layout"},j={class:"guns-layout-content-application"},h={class:"content-mian"},w={class:"content-mian-body"},D={class:"table-content"};e("default",s({__name:"server",setup(e){const a=l({cpuColumns:[{title:"属性",dataIndex:"key"},{title:"值",dataIndex:"value"}],memoryColumns:[{title:"属性",dataIndex:"key"},{title:"内存",dataIndex:"value"},{title:"jvm",dataIndex:"jvm"}],diskColumns:[{title:"盘符路径",dataIndex:"dirName"},{title:"文件系统",dataIndex:"sysTypeName"},{title:"盘符类型",dataIndex:"typeName"},{title:"总大小",dataIndex:"total"},{title:"可用大小",dataIndex:"free"},{title:"已用大小",dataIndex:"used"},{title:"已用百分比",dataIndex:"usage"}],cpuData:[],memoryData:[],diskData:[],systemInfo:{cpu:{},mem:{},jvm:{},sys:{},sysFiles:[]}});return d((async()=>{let e=await x.getSystemInfo();a.systemInfo=e,a.cpuData[0]={key:"核心数",value:e.cpu.cpuNum},a.cpuData[1]={key:"用户使用率",value:e.cpu.used},a.cpuData[2]={key:"系统使用率",value:e.cpu.sys},a.cpuData[3]={key:"当前空闲率",value:e.cpu.free},a.memoryData[0]={key:"总内存",value:e.mem.total,jvm:e.jvm.total},a.memoryData[1]={key:"已用内存",value:e.mem.used,jvm:e.jvm.used},a.memoryData[2]={key:"剩余内存",value:e.mem.free,jvm:e.jvm.free},a.memoryData[3]={key:"使用率",value:e.mem.usage,jvm:e.jvm.usage}})),(e,t)=>{const s=b,l=y,d=f,x=p,C=v,N=g;return o(),n("div",I,[c("div",_,[c("div",k,[c("div",j,[c("div",h,[c("div",w,[c("div",D,[m(x,{class:"bottom-1"},{default:u((()=>[m(d,{xs:24,sm:24,md:12,class:"block-space-right"},{default:u((()=>[m(l,{title:"CPU信息",class:"card-width",bordered:!1},{default:u((()=>[m(s,{"row-key":"key",columns:a.cpuColumns,"data-source":a.cpuData,pagination:!1},null,8,["columns","data-source"])])),_:1})])),_:1}),m(d,{xs:24,sm:24,md:12,class:"block-space-left"},{default:u((()=>[m(l,{title:"内存信息",class:"card-width",bordered:!1},{default:u((()=>[m(s,{"row-key":"key",columns:a.memoryColumns,"data-source":a.memoryData,pagination:!1},null,8,["columns","data-source"])])),_:1})])),_:1})])),_:1}),m(x,{class:"bottom-1"},{default:u((()=>[m(d,{span:24,class:"block-space-top"},{default:u((()=>[m(l,{title:"JAVA虚拟机信息",class:"card-width",bordered:!1},{default:u((()=>[m(N,{column:2},{default:u((()=>[m(C,{label:"jvm名称",class:"b-r-1"},{default:u((()=>[r(i(a.systemInfo.jvm.name),1)])),_:1}),m(C,{label:"java版本"},{default:u((()=>[r(i(a.systemInfo.jvm.version),1)])),_:1}),m(C,{label:"启动时间",class:"b-r-1"},{default:u((()=>[r(i(a.systemInfo.jvm.startTime),1)])),_:1}),m(C,{label:"运行时长"},{default:u((()=>[r(i(a.systemInfo.jvm.runTime),1)])),_:1}),m(C,{label:"安装路径",span:2},{default:u((()=>[r(i(a.systemInfo.jvm.home),1)])),_:1}),m(C,{label:"项目路径"},{default:u((()=>[r(i(a.systemInfo.sys.userDir),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),m(x,{class:"bottom-1"},{default:u((()=>[m(d,{span:24,class:"block-space-top"},{default:u((()=>[m(l,{title:"服务器信息",class:"card-width",bordered:!1},{default:u((()=>[m(N,{column:2},{default:u((()=>[m(C,{label:"服务器名称",class:"b-r-1"},{default:u((()=>[r(i(a.systemInfo.sys.computerName),1)])),_:1}),m(C,{label:"操作系统"},{default:u((()=>[r(i(a.systemInfo.sys.osName),1)])),_:1}),m(C,{label:"服务器IP",class:"b-r-1"},{default:u((()=>[r(i(a.systemInfo.sys.computerIp),1)])),_:1}),m(C,{label:"系统架构"},{default:u((()=>[r(i(a.systemInfo.sys.osArch),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),m(x,{class:"bottom-1"},{default:u((()=>[m(d,{span:24,class:"block-space-top"},{default:u((()=>[m(l,{title:"磁盘信息",class:"card-width",bordered:!1},{default:u((()=>[m(s,{"row-key":"dirName",columns:a.diskColumns,scroll:{x:"max-content"},"data-source":a.systemInfo.sysFiles,pagination:!1},null,8,["columns","data-source"])])),_:1})])),_:1})])),_:1})])])])])])])])}}},[["__scopeId","data-v-440b7f5e"]]))}}}));
