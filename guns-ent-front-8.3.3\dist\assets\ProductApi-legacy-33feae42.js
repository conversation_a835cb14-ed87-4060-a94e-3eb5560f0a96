System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var a;return{setters:[e=>{a=e.R}],execute:function(){class t{static add(e){return a.post("/erp/product/add",e)}static delete(e){return a.post("/erp/product/delete",e)}static batchDelete(e){return a.post("/erp/product/batchDelete",e)}static edit(e){return a.post("/erp/product/edit",e)}static detail(e){return a.getAndLoadData("/erp/product/detail",e)}static findPage(e){return a.getAndLoadData("/erp/product/page",e)}static findList(e){return a.getAndLoadData("/erp/product/list",e)}static updateStatus(e){return a.post("/erp/product/updateStatus",e)}static validateCode(e){return a.getAndLoadData("/erp/product/validateCode",e)}static validateBarcode(e){return a.getAndLoadData("/erp/product/validateBarcode",e)}static getProductStatusOptions(){return[{label:"正常",value:"ACTIVE"},{label:"停用",value:"INACTIVE"},{label:"停产",value:"DISCONTINUED"}]}static getPricingTypeOptions(){return[{label:"普通",value:"NORMAL"},{label:"计重",value:"WEIGHT"},{label:"计件",value:"PIECE"},{label:"不定价",value:"VARIABLE"}]}static getCommonUnitOptions(){return[{label:"个",value:"个"},{label:"件",value:"件"},{label:"盒",value:"盒"},{label:"包",value:"包"},{label:"袋",value:"袋"},{label:"瓶",value:"瓶"},{label:"罐",value:"罐"},{label:"桶",value:"桶"},{label:"箱",value:"箱"},{label:"台",value:"台"},{label:"套",value:"套"},{label:"副",value:"副"},{label:"双",value:"双"},{label:"对",value:"对"},{label:"张",value:"张"},{label:"本",value:"本"},{label:"支",value:"支"},{label:"根",value:"根"},{label:"条",value:"条"},{label:"米",value:"米"},{label:"千克",value:"千克"},{label:"克",value:"克"},{label:"升",value:"升"},{label:"毫升",value:"毫升"}]}static getProductStatusName(e){const a=t.getProductStatusOptions().find((t=>t.value===e));return a?a.label:e}static getPricingTypeName(e){const a=t.getPricingTypeOptions().find((t=>t.value===e));return a?a.label:e}static getStatusTagColor(e){switch(e){case"ACTIVE":return"green";case"INACTIVE":return"orange";case"DISCONTINUED":return"red";default:return"default"}}static getPricingTypeTagColor(e){switch(e){case"NORMAL":return"blue";case"WEIGHT":return"green";case"PIECE":return"orange";case"VARIABLE":return"purple";default:return"default"}}static formatWeight(e){return e?`${e}kg`:"-"}static formatVolume(e){return e?`${e}m³`:"-"}static formatShelfLife(e){return e?`${e}天`:"-"}static getProductsBySupplier(e){return a.getAndLoadData("/erp/product/listBySupplier",e)}static validatePricingTypeChange(e){return a.getAndLoadData("/erp/product/validatePricingTypeChange",e)}static formatPrice(e,t){if(!e)return"-";switch(t){case"NORMAL":default:return`¥${e.toFixed(2)}`;case"WEIGHT":return`¥${e.toFixed(2)}/kg`;case"PIECE":return`¥${e.toFixed(2)}/份`;case"VARIABLE":return`参考价 ¥${e.toFixed(2)}`}}static getCategoryTree(){return a.getAndLoadData("/erp/productCategory/tree")}}e("P",t)}}}));
