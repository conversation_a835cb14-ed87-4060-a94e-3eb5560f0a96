package cn.stylefeng.roses.ent.mobile.invite.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邀请用户实例类
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
@TableName("sys_invite_user")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysInviteUser extends BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "invite_user_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键")
    private Long inviteUserId;

    /**
     * 邀请机构id
     */
    @TableField("org_id")
    @ChineseDescription("邀请机构id")
    private Long orgId;

    /**
     * 来自谁的邀请
     */
    @TableField("from_user_id")
    @ChineseDescription("来自谁的邀请")
    private Long fromUserId;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    @ChineseDescription("真实姓名")
    private String realName;

    /**
     * 用户手机号
     */
    @TableField("phone_number")
    @ChineseDescription("用户手机号")
    private String phoneNumber;

    /**
     * 手机号验证码
     */
    @TableField("phone_validate_number")
    @ChineseDescription("手机号验证码")
    private String phoneValidateNumber;

    /**
     * 申请加入理由
     */
    @TableField("apply_reason")
    @ChineseDescription("申请加入理由")
    private String applyReason;

    /**
     * 审核状态：10-待审核，20-通过，30-拒绝
     */
    @TableField("invite_status")
    @ChineseDescription("审核状态：10-待审核，20-通过，30-拒绝")
    private Integer inviteStatus;

    /**
     * 是否删除：Y-删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("是否删除：Y-删除，N-未删除")
    @TableLogic
    private String delFlag;

}
