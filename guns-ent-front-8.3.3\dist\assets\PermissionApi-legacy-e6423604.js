System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var o;return{setters:[e=>{o=e.R}],execute:function(){e("P",class{static getRoleCategoryAndRoleTree(e){return o.getAndLoadData("/permission/getRoleCategoryAndRoleTree",e)}static getRoleBindPermission(e){return o.getAndLoadData("/permission/getRoleBindPermission",e)}static getRoleList(e){return o.getAndLoadData("/permission/getRoleList",e)}static updateRoleBindPermission(e){return o.post("/permission/updateRoleBindPermission",e)}static getRoleDataScopePageList(e){return o.getAndLoadData("/roleDataScope/getRoleDataScopePageList",e)}static addRoleDataScope(e){return o.post("/roleDataScope/addRoleDataScope",e)}static editRoleDataScope(e){return o.post("/roleDataScope/editRoleDataScope",e)}static roleDataScopeDelete(e){return o.post("/roleDataScope/delete",e)}static getRoleLimit(e){return o.getAndLoadData("/roleLimit/getRoleLimit",e)}static bindRoleLimit(e){return o.post("/roleLimit/bindRoleLimit",e)}})}}}));
