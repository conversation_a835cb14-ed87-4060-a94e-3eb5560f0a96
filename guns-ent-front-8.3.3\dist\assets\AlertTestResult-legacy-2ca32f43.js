System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./index-legacy-45c79de7.js","./index-legacy-510bfbb8.js"],(function(e,t){"use strict";var l,a,r,s,o,n,d,u,c,i,m,y,p,v,h,f,x,g,C,k,R,_,T,b,I,w;return{setters:[e=>{l=e._,a=e.af,r=e.L,s=e.k,o=e.a,n=e.f,d=e.w,u=e.b,c=e.d,i=e.g,m=e.t,y=e.ah,p=e.c,v=e.h,h=e.m,f=e.a4,x=e.v,g=e.G,C=e.a0,k=e.U,R=e.i,_=e.a5,T=e.a7,b=e.B,I=e.n,w=e.M},null,null,null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".test-result-container[data-v-b6a7e185]{max-height:70vh;overflow-y:auto}.summary-card[data-v-b6a7e185],.result-card[data-v-b6a7e185],.detail-card[data-v-b6a7e185]{margin-bottom:16px}.no-alert[data-v-b6a7e185]{text-align:center;padding:40px 0}.action-buttons[data-v-b6a7e185]{text-align:right;padding-top:16px;border-top:1px solid #f0f0f0}[data-v-b6a7e185] .ant-statistic-title{font-size:12px}[data-v-b6a7e185] .ant-statistic-content{font-size:16px}\n",document.head.appendChild(t);const L={name:"AlertTestResult",components:{DownloadOutlined:a},props:{visible:{type:Boolean,default:!1},testResult:{type:Object,default:()=>({})}},emits:["update:visible","cancel"],setup(e,{emit:t}){const l=r((()=>e.testResult.summary||{totalRules:0,alertCount:0,checkedProducts:0,duration:0})),a=r((()=>e.testResult.alerts||[])),s=r((()=>e.testResult.details||[]));return{summary:l,alertResults:a,checkDetails:s,alertColumns:[{title:"商品名称",dataIndex:"productName",key:"productName",width:150},{title:"预警类型",dataIndex:"alertType",key:"alertType",width:100,slots:{customRender:"alertType"}},{title:"预警级别",dataIndex:"alertLevel",key:"alertLevel",width:100,slots:{customRender:"alertLevel"}},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:100,slots:{customRender:"currentStock"}},{title:"阈值",dataIndex:"thresholdValue",key:"thresholdValue",width:80,slots:{customRender:"thresholdValue"}},{title:"预警消息",dataIndex:"alertMessage",key:"alertMessage"}],detailColumns:[{title:"规则名称",dataIndex:"ruleName",key:"ruleName",width:150,slots:{customRender:"ruleName"}},{title:"检查状态",dataIndex:"status",key:"status",width:100,slots:{customRender:"status"}},{title:"检查商品数",dataIndex:"checkedCount",key:"checkedCount",width:100},{title:"触发预警数",dataIndex:"alertCount",key:"alertCount",width:100},{title:"检查耗时",dataIndex:"duration",key:"duration",width:100,customRender:({text:e})=>`${e}ms`},{title:"备注",dataIndex:"remark",key:"remark"}],getAlertLevelColor:e=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"}[e]||"default"),getAlertLevelText:e=>({CRITICAL:"紧急",WARNING:"警告",INFO:"提醒"}[e]||e),getAlertTypeColor:e=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"}[e]||"default"),getAlertTypeText:e=>({LOW_STOCK:"库存不足",ZERO_STOCK:"零库存",OVERSTOCK:"库存积压",EXPIRY:"临期预警"}[e]||e),handleCancel:()=>{t("cancel")},handleExport:()=>{try{const e={summary:l.value,alerts:a.value,details:s.value,exportTime:(new Date).toLocaleString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),r=URL.createObjectURL(t),o=document.createElement("a");o.href=r,o.download=`预警检查结果_${(new Date).getTime()}.json`,o.click(),URL.revokeObjectURL(r),h.success("导出成功")}catch(e){console.error("导出失败:",e),h.error("导出失败")}}}}},O={class:"test-result-container"},A={key:0,class:"no-alert"},S={class:"action-buttons"};e("default",l(L,[["render",function(e,t,l,a,r,h){const L=f,N=x,j=g,E=C,z=k,V=R,D=s("a-icon"),K=_,U=T,M=s("DownloadOutlined"),P=b,W=I,B=w;return o(),n(B,{title:"预警检查结果",visible:l.visible,width:900,footer:null,onCancel:a.handleCancel},{default:d((()=>[u("div",O,[c(E,{title:"检查概要",size:"small",class:"summary-card"},{default:d((()=>[c(j,{gutter:16},{default:d((()=>[c(N,{span:6},{default:d((()=>[c(L,{title:"检查规则数",value:a.summary.totalRules,"value-style":{color:"#1890ff"}},null,8,["value"])])),_:1}),c(N,{span:6},{default:d((()=>[c(L,{title:"触发预警数",value:a.summary.alertCount,"value-style":{color:a.summary.alertCount>0?"#f5222d":"#52c41a"}},null,8,["value","value-style"])])),_:1}),c(N,{span:6},{default:d((()=>[c(L,{title:"检查商品数",value:a.summary.checkedProducts,"value-style":{color:"#722ed1"}},null,8,["value"])])),_:1}),c(N,{span:6},{default:d((()=>[c(L,{title:"检查耗时",value:a.summary.duration,suffix:"ms","value-style":{color:"#13c2c2"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(E,{title:"预警结果",size:"small",class:"result-card"},{default:d((()=>[c(V,{columns:a.alertColumns,"data-source":a.alertResults,pagination:!1,scroll:{y:300},size:"small"},{alertLevel:d((({record:e})=>[c(z,{color:a.getAlertLevelColor(e.alertLevel)},{default:d((()=>[i(m(a.getAlertLevelText(e.alertLevel)),1)])),_:2},1032,["color"])])),alertType:d((({record:e})=>[c(z,{color:a.getAlertTypeColor(e.alertType)},{default:d((()=>[i(m(a.getAlertTypeText(e.alertType)),1)])),_:2},1032,["color"])])),currentStock:d((({record:e})=>[u("span",{style:y({color:e.currentStock<=e.thresholdValue?"#f5222d":"#52c41a"})},m(e.currentStock),5)])),thresholdValue:d((({record:e})=>[u("span",null,m(e.thresholdValue),1)])),_:1},8,["columns","data-source"]),0===a.alertResults.length?(o(),p("div",A,[c(K,{description:"未发现预警情况"},{image:d((()=>[c(D,{type:"check-circle",style:{"font-size":"48px",color:"#52c41a"}})])),_:1})])):v("",!0)])),_:1}),c(E,{title:"检查详情",size:"small",class:"detail-card"},{default:d((()=>[c(V,{columns:a.detailColumns,"data-source":a.checkDetails,pagination:!1,scroll:{y:200},size:"small"},{status:d((({record:e})=>[c(z,{color:e.hasAlert?"red":"green"},{default:d((()=>[i(m(e.hasAlert?"触发预警":"正常"),1)])),_:2},1032,["color"])])),ruleName:d((({record:e})=>[c(U,{title:e.ruleDescription},{default:d((()=>[i(m(e.ruleName),1)])),_:2},1032,["title"])])),_:1},8,["columns","data-source"])])),_:1}),u("div",S,[c(W,null,{default:d((()=>[c(P,{onClick:a.handleExport},{icon:d((()=>[c(M)])),default:d((()=>[t[0]||(t[0]=i(" 导出结果 "))])),_:1,__:[0]},8,["onClick"]),c(P,{type:"primary",onClick:a.handleCancel},{default:d((()=>t[1]||(t[1]=[i(" 关闭 ")]))),_:1,__:[1]},8,["onClick"])])),_:1})])])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-b6a7e185"]]))}}}));
