System.register(["./ProductWithCategory-legacy-fa36b2c6.js","./index-legacy-ee1db0c7.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./SupplierSelector-legacy-fba3813b.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js","./UniversalTree-legacy-6dcdf778.js","./ProductApi-legacy-33feae42.js","./ProductEdit-legacy-b01b9be9.js","./index-legacy-94a6fc23.js","./CategorySelector-legacy-c3396c33.js","./index-legacy-9a185ac3.js","./productCategoryApi-legacy-247b2407.js","./ProductDetail-legacy-048ea5d4.js","./index-legacy-e24582b9.js"],(function(e,l){"use strict";var n,c,t,u,a;return{setters:[e=>{n=e.default},e=>{c=e._,t=e.k,u=e.a,a=e.f},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){e("default",c({name:"ProductIndex",components:{ProductWithCategory:n}},[["render",function(e,l,n,c,r,s){const i=t("product-with-category");return u(),a(i)}]]))}}}));
