package cn.stylefeng.roses.kernel.ca.server.modular.sso.pojo;

import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 基于token方式单向单点的请求
 *
 * <AUTHOR>
 * @date 2022/3/28 17:27
 */
@Data
public class SsoTokenSingleRequest extends BaseRequest {

    /**
     * 单点应用客户端id
     */
    @NotNull(message = "应用id不能为空", groups = detail.class)
    private Long clientId;

}
