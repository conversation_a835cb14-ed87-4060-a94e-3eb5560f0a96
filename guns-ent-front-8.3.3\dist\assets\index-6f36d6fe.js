import{_ as j}from"./index-02bf6f00.js";import{r,bh as L,bi as N,o as q,k as X,a as n,c,b as l,d as o,w as t,t as Y,h as s,f as u,F as B,g as p,M as G,E as H,m as k,n as J,B as K,bk as Q,I as W,l as Z,cd as ee,U as te}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{F as O,a as z}from"./FileApi-418f4d35.js";import oe from"./file-detail-35ea172d.js";/* empty css              *//* empty css              *//* empty css              */const ne={class:"guns-layout"},ie={class:"guns-layout-content"},se={class:"guns-layout"},le={class:"guns-layout-content-application"},ae={class:"content-mian"},de={class:"content-mian-header"},re={class:"header-content"},ue={class:"header-content-left"},ce={class:"header-content-right"},_e={class:"content-mian-body"},pe={class:"table-content"},fe=["onClick"],me={key:0},he={key:1},Ue=Object.assign({name:"File"},{__name:"index",setup(ge){const $=r([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,fixed:"left",hideInSetting:!0},{dataIndex:"fileId",title:"\u6587\u4EF6id",ellipsis:!0,width:150,isShow:!0},{dataIndex:"fileOriginName",title:"\u6587\u4EF6\u540D\u79F0",width:100,ellipsis:!0,isShow:!0},{dataIndex:"fileUrl",title:"\u56FE\u7247\u9884\u89C8",ellipsis:!0,width:80,isShow:!0},{dataIndex:"fileLocation",title:"\u5B58\u50A8\u4F4D\u7F6E",ellipsis:!0,width:100,isShow:!0},{dataIndex:"secretFlag",title:"\u662F\u5426\u673A\u5BC6",width:80,isShow:!0},{title:"\u6587\u4EF6\u5927\u5C0F",width:80,ellipsis:!0,dataIndex:"fileSizeInfo",isShow:!0},{title:"\u6587\u4EF6\u540E\u7F00",width:80,ellipsis:!0,dataIndex:"fileSuffix",isShow:!0},{title:"\u521B\u5EFA\u65F6\u95F4",width:120,dataIndex:"createTime",isShow:!0},{title:"\u521B\u5EFA\u4EBA",isShow:!0,ellipsis:!0,width:60,dataIndex:"createUserName"},{key:"action",title:"\u64CD\u4F5C",width:60,fixed:"right",isShow:!0}]),x=r(null),h=r({fileOriginName:""}),y=r(null),f=r(!1),T=r("".concat(L).concat(O,"?secretFlag=N")),E=r("".concat(L).concat(O,"?secretFlag=N&fileLocation=5")),I=r({Authorization:N()});q(()=>{});const g=()=>{x.value.reload()},b=a=>{y.value=a,f.value=!0},M=a=>{G.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6587\u4EF6\u5417?",icon:o(H),maskClosable:!0,onOk:async()=>{const e=await z.delete({fileCode:a.fileCode});k.success(e.message),g()}})},A=a=>{const e=a.size/1024/1024<=1;return e||k.error("\u4E0A\u4F20\u56FE\u7247\u4E0D\u80FD\u8D85\u8FC71MB"),e},C=({file:a})=>{a.response&&(k.success("\u4E0A\u4F20\u6210\u529F"),g())},D=a=>{z.download({fileId:a.fileId,secretFlag:a.secretFlag,token:N()})};return(a,e)=>{const v=J,F=X("CloudUploadOutlined"),U=K,S=Q,m=W,P=Z,V=ee,_=te,R=j;return n(),c("div",ne,[l("div",ie,[l("div",se,[l("div",le,[l("div",ae,[l("div",de,[l("div",re,[l("div",ue,[o(v,{size:16})]),l("div",ce,[o(v,{size:16},{default:t(()=>[o(S,{name:"file",multiple:!0,action:T.value,headers:I.value,onChange:C,showUploadList:!1},{default:t(()=>[o(U,{type:"primary",class:"border-radius"},{icon:t(()=>[o(F)]),default:t(()=>[e[2]||(e[2]=l("span",null,"\u4E0A\u4F20\u6587\u4EF6",-1))]),_:1,__:[2]})]),_:1},8,["action","headers"]),o(S,{name:"file",multiple:!0,action:E.value,headers:I.value,"before-upload":A,onChange:C,showUploadList:!1},{default:t(()=>[o(U,{class:"border-radius"},{icon:t(()=>[o(F)]),default:t(()=>[e[3]||(e[3]=l("span",null,"\u4E0A\u4F20\u5230\u6570\u636E\u5E93\uFF081MB\u9650\u5236\uFF09",-1))]),_:1,__:[3]})]),_:1},8,["action","headers"])]),_:1})])])]),l("div",_e,[l("div",pe,[o(R,{columns:$.value,where:h.value,rowId:"fileId",ref_key:"tableRef",ref:x,rowSelection:!1,url:"/sysFileInfo/fileInfoListPage",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"FILE_TABLE"},{toolLeft:t(()=>[o(P,{value:h.value.fileOriginName,"onUpdate:value":e[0]||(e[0]=d=>h.value.fileOriginName=d),placeholder:"\u6587\u4EF6\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:g,class:"search-input",bordered:!1},{prefix:t(()=>[o(m,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:t(({column:d,record:i})=>[d.dataIndex=="fileOriginName"?(n(),c("a",{key:0,onClick:w=>b(i)},Y(i.fileOriginName),9,fe)):s("",!0),d.dataIndex==="fileUrl"?(n(),u(V,{key:1,width:30,src:i.fileUrl},null,8,["src"])):s("",!0),d.dataIndex==="fileLocation"?(n(),c(B,{key:2},[i.fileLocation===1?(n(),u(_,{key:0,color:"orange"},{default:t(()=>e[4]||(e[4]=[p("\u963F\u91CC\u4E91")])),_:1,__:[4]})):s("",!0),i.fileLocation===2?(n(),u(_,{key:1,color:"blue"},{default:t(()=>e[5]||(e[5]=[p("\u817E\u8BAF\u4E91")])),_:1,__:[5]})):s("",!0),i.fileLocation===3?(n(),u(_,{key:2,color:"red"},{default:t(()=>e[6]||(e[6]=[p("minio")])),_:1,__:[6]})):s("",!0),i.fileLocation===4?(n(),u(_,{key:3,color:"green"},{default:t(()=>e[7]||(e[7]=[p("\u672C\u5730")])),_:1,__:[7]})):s("",!0),i.fileLocation===5?(n(),u(_,{key:4,color:"cyan"},{default:t(()=>e[8]||(e[8]=[p("\u6570\u636E\u5E93")])),_:1,__:[8]})):s("",!0)],64)):s("",!0),d.dataIndex==="secretFlag"?(n(),c(B,{key:3},[i.secretFlag=="Y"?(n(),c("span",me,"\u662F")):s("",!0),i.secretFlag=="N"?(n(),c("span",he,"\u5426")):s("",!0)],64)):s("",!0),d.key=="action"?(n(),u(v,{key:4,size:16},{default:t(()=>[o(m,{iconClass:"icon-opt-xiazai","font-size":"24px",color:"#60666b",title:"\u4E0B\u8F7D",onClick:w=>D(i)},null,8,["onClick"]),o(m,{iconClass:"icon-opt-xiangqing","font-size":"24px",color:"#60666b",title:"\u8BE6\u60C5",onClick:w=>b(i)},null,8,["onClick"]),o(m,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:w=>M(i)},null,8,["onClick"])]),_:2},1024)):s("",!0)]),_:1},8,["columns","where"])])])])])])]),f.value?(n(),u(oe,{key:0,visible:f.value,"onUpdate:visible":e[1]||(e[1]=d=>f.value=d),data:y.value},null,8,["visible","data"])):s("",!0)])}}});export{Ue as default};
