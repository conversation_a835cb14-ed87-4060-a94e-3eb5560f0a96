<template>
  <a-modal
    :title="formTitle"
    :visible="visible"
    :width="800"
    :confirm-loading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="规则名称" name="ruleName">
        <a-input
          v-model:value="formData.ruleName"
          placeholder="请输入规则名称"
          :maxlength="100"
        />
      </a-form-item>

      <a-form-item label="预警类型" name="alertType">
        <a-select
          v-model:value="formData.alertType"
          placeholder="请选择预警类型"
          @change="handleAlertTypeChange"
        >
          <a-select-option value="LOW_STOCK">库存不足</a-select-option>
          <a-select-option value="ZERO_STOCK">零库存</a-select-option>
          <a-select-option value="OVERSTOCK">库存积压</a-select-option>
          <a-select-option value="EXPIRY">临期预警</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="目标类型" name="targetType">
        <a-select
          v-model:value="formData.targetType"
          placeholder="请选择目标类型"
          @change="handleTargetTypeChange"
        >
          <a-select-option value="ALL">全部商品</a-select-option>
          <a-select-option value="PRODUCT">指定商品</a-select-option>
          <a-select-option value="CATEGORY">商品分类</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="formData.targetType === 'PRODUCT'"
        label="目标商品"
        name="targetId"
      >
        <a-select
          v-model:value="formData.targetId"
          placeholder="请选择商品"
          show-search
          :filter-option="filterOption"
        >
          <a-select-option
            v-for="product in productOptions"
            :key="product.id"
            :value="product.id"
          >
            {{ product.name }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="formData.targetType === 'CATEGORY'"
        label="目标分类"
        name="targetId"
      >
        <a-tree-select
          v-model:value="formData.targetId"
          placeholder="请选择商品分类"
          :tree-data="categoryOptions"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
        />
      </a-form-item>

      <a-form-item label="预警级别" name="alertLevel">
        <a-select
          v-model:value="formData.alertLevel"
          placeholder="请选择预警级别"
        >
          <a-select-option value="CRITICAL">紧急</a-select-option>
          <a-select-option value="WARNING">警告</a-select-option>
          <a-select-option value="INFO">提醒</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="阈值类型" name="thresholdType">
        <a-select
          v-model:value="formData.thresholdType"
          placeholder="请选择阈值类型"
          @change="handleThresholdTypeChange"
        >
          <a-select-option value="QUANTITY">数量</a-select-option>
          <a-select-option value="PERCENTAGE">百分比</a-select-option>
          <a-select-option value="DAYS">天数</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="阈值" name="thresholdValue">
        <a-input-number
          v-model:value="formData.thresholdValue"
          :min="0"
          :precision="formData.thresholdType === 'PERCENTAGE' ? 2 : 0"
          :max="formData.thresholdType === 'PERCENTAGE' ? 100 : undefined"
          style="width: 100%"
          placeholder="请输入阈值"
        />
        <div class="ant-form-item-explain">
          <span v-if="formData.thresholdType === 'QUANTITY'">
            当库存数量小于等于此值时触发预警
          </span>
          <span v-else-if="formData.thresholdType === 'PERCENTAGE'">
            当库存数量占最大库存的百分比小于等于此值时触发预警
          </span>
          <span v-else-if="formData.thresholdType === 'DAYS'">
            当商品距离过期天数小于等于此值时触发预警
          </span>
        </div>
      </a-form-item>

      <a-form-item label="比较操作符" name="comparisonOperator">
        <a-select
          v-model:value="formData.comparisonOperator"
          placeholder="请选择比较操作符"
        >
          <a-select-option value="LTE">小于等于 (≤)</a-select-option>
          <a-select-option value="LT">小于 (<)</a-select-option>
          <a-select-option value="GTE">大于等于 (≥)</a-select-option>
          <a-select-option value="GT">大于 (>)</a-select-option>
          <a-select-option value="EQ">等于 (=)</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="检查频率" name="checkFrequency">
        <a-input-number
          v-model:value="formData.checkFrequency"
          :min="1"
          :max="1440"
          style="width: 100%"
          placeholder="请输入检查频率（分钟）"
        />
        <div class="ant-form-item-explain">
          检查频率，单位：分钟（1-1440分钟）
        </div>
      </a-form-item>

      <a-form-item label="是否启用" name="isEnabled">
        <a-switch
          v-model:checked="formData.isEnabled"
          checked-children="启用"
          un-checked-children="禁用"
        />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :rows="3"
          :maxlength="500"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

export default {
  name: 'AlertRuleForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    productOptions: {
      type: Array,
      default: () => []
    },
    categoryOptions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'submit', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref();
    const confirmLoading = ref(false);

    // 创建本地的表单数据副本
    const formData = ref({
      id: null,
      ruleName: '',
      alertType: '',
      targetType: 'ALL',
      targetId: null,
      alertLevel: 'WARNING',
      thresholdType: 'QUANTITY',
      thresholdValue: null,
      comparisonOperator: 'LTE',
      isEnabled: 'Y',
      notificationMethods: ['SYSTEM'],
      notificationUsers: [],
      checkFrequency: 30,
      remark: ''
    });

    const formTitle = computed(() => {
      return formData.value.id ? '编辑预警规则' : '新增预警规则';
    });

    // 监听props变化，更新本地数据
    watch(() => props.formData, (newData) => {
      if (newData) {
        Object.assign(formData.value, {
          id: newData.id || null,
          ruleName: newData.ruleName || '',
          alertType: newData.alertType || '',
          targetType: newData.targetType || 'ALL',
          targetId: newData.targetId || null,
          alertLevel: newData.alertLevel || 'WARNING',
          thresholdType: newData.thresholdType || 'QUANTITY',
          thresholdValue: newData.thresholdValue || null,
          comparisonOperator: newData.comparisonOperator || 'LTE',
          isEnabled: newData.isEnabled || 'Y',
          notificationMethods: newData.notificationMethods || ['SYSTEM'],
          notificationUsers: newData.notificationUsers || [],
          checkFrequency: newData.checkFrequency || 30,
          remark: newData.remark || ''
        });
      } else {
        // 重置表单
        Object.assign(formData.value, {
          id: null,
          ruleName: '',
          alertType: '',
          targetType: 'ALL',
          targetId: null,
          alertLevel: 'WARNING',
          thresholdType: 'QUANTITY',
          thresholdValue: null,
          comparisonOperator: 'LTE',
          isEnabled: 'Y',
          notificationMethods: ['SYSTEM'],
          notificationUsers: [],
          checkFrequency: 30,
          remark: ''
        });
      }
    }, { immediate: true, deep: true });

    const rules = reactive({
      ruleName: [
        { required: true, message: '请输入规则名称', trigger: 'blur' },
        { max: 100, message: '规则名称不能超过100个字符', trigger: 'blur' }
      ],
      alertType: [
        { required: true, message: '请选择预警类型', trigger: 'change' }
      ],
      targetType: [
        { required: true, message: '请选择目标类型', trigger: 'change' }
      ],
      targetId: [
        {
          validator: (rule, value) => {
            if (formData.value.targetType !== 'ALL' && !value) {
              return Promise.reject('请选择目标');
            }
            return Promise.resolve();
          },
          trigger: 'change'
        }
      ],
      alertLevel: [
        { required: true, message: '请选择预警级别', trigger: 'change' }
      ],
      thresholdType: [
        { required: true, message: '请选择阈值类型', trigger: 'change' }
      ],
      thresholdValue: [
        { required: true, message: '请输入阈值', trigger: 'blur' },
        { type: 'number', min: 0, message: '阈值不能小于0', trigger: 'blur' }
      ],
      comparisonOperator: [
        { required: true, message: '请选择比较操作符', trigger: 'change' }
      ],
      checkFrequency: [
        { required: true, message: '请输入检查频率', trigger: 'blur' },
        { type: 'number', min: 1, max: 1440, message: '检查频率必须在1-1440分钟之间', trigger: 'blur' }
      ]
    });

    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        confirmLoading.value = true;
        emit('submit', formData.value);
      } catch (error) {
        console.error('表单验证失败:', error);
      } finally {
        confirmLoading.value = false;
      }
    };

    const handleCancel = () => {
      emit('update:visible', false);
      emit('cancel');
    };

    const handleAlertTypeChange = (value) => {
      // 根据预警类型调整默认设置
      if (value === 'ZERO_STOCK') {
        formData.value.thresholdType = 'QUANTITY';
        formData.value.thresholdValue = 0;
        formData.value.comparisonOperator = 'EQ';
      } else if (value === 'EXPIRY') {
        formData.value.thresholdType = 'DAYS';
        formData.value.comparisonOperator = 'LTE';
      }
    };

    const handleTargetTypeChange = (value) => {
      // 清空目标ID
      formData.value.targetId = null;
    };

    const handleThresholdTypeChange = (value) => {
      // 重置阈值
      formData.value.thresholdValue = null;
    };

    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    return {
      formRef,
      formData,
      confirmLoading,
      formTitle,
      rules,
      handleSubmit,
      handleCancel,
      handleAlertTypeChange,
      handleTargetTypeChange,
      handleThresholdTypeChange,
      filterOption
    };
  }
};
</script>

<style scoped>
.ant-form-item-explain {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
</style>
