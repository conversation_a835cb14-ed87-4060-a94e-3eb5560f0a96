package cn.stylefeng.roses.kernel.ca.api.pojo.external.request;

import cn.stylefeng.roses.kernel.ca.api.pojo.external.BaseSsoExternalApiRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 外部系统将登录人踢下线的请求
 *
 * <AUTHOR>
 * @date 2021/1/21 13:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SsoExternalKickOffRequest extends BaseSsoExternalApiRequest {

    /**
     * 单点登录ca token的具体值
     */
    @NotBlank(message = "caToken不能为空")
    private String caToken;

}
