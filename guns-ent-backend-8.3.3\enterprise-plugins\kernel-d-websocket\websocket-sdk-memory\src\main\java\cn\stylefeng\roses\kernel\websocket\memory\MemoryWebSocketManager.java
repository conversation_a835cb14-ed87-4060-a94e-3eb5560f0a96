package cn.stylefeng.roses.kernel.websocket.memory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketEvent;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import cn.stylefeng.roses.kernel.websocket.api.constants.WebSocketEventConstants;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketTransferDTO;
import cn.stylefeng.roses.kernel.websocket.api.utils.WebSocketUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于内存的websocket管理器
 *
 * <AUTHOR>
 * @since 2024/1/15 0:44
 */
public class MemoryWebSocketManager implements WebSocketManagerApi, ApplicationContextAware {

    /**
     * 用来发布业务上的事件
     */
    private ApplicationContext applicationContext;

    /**
     * 用户token和socket连接的对应关系
     * <p>
     * key是用户token，value是这个人对应的socket连接信息
     */
    private final Map<String, WebSocketDTO> connections = new ConcurrentHashMap<>(100);

    /**
     * 用户所有的token标识
     * <p>
     * key是用户id，value是这个用户对应的所有socket连接的标识
     */
    private final Map<String, Set<String>> userIdTotalIdentifier = new ConcurrentHashMap<>(100);

    @Override
    public WebSocketDTO get(String identifier) {
        return connections.get(identifier);
    }

    @Override
    public List<WebSocketDTO> getList(String userId) {
        return getUserTotalConnection(userId);
    }

    @Override
    public void put(String identifier, WebSocketDTO webSocket) {
        connections.put(identifier, webSocket);

        Set<String> userIdIdentifierList = userIdTotalIdentifier.computeIfAbsent(String.valueOf(webSocket.getUserId()), k -> Collections.synchronizedSet(new HashSet<>()));
        userIdIdentifierList.add(identifier);

        // 发送连接事件，交给业务监听方
        applicationContext.publishEvent(new WebSocketEvent(webSocket, WebSocketEventConstants.EVENT_TYPE_OPEN));
    }

    @Override
    public void remove(String identifier) {
        WebSocketDTO removedWebSocket = connections.remove(identifier);

        // 发送关闭事件，交给业务监听方
        if (null != removedWebSocket) {
            userIdTotalIdentifier.get(String.valueOf(removedWebSocket.getUserId())).remove(identifier);
            applicationContext.publishEvent(new WebSocketEvent(removedWebSocket, WebSocketEventConstants.EVENT_TYPE_CLOSE));
            removedWebSocket.closeSession();
        }
    }

    @Override
    public Map<String, WebSocketDTO> localWebSocketMap() {
        return Collections.unmodifiableMap(connections);
    }

    @Override
    public int size() {
        Map<String, Set<String>> userIdTotalTokens = Collections.unmodifiableMap(userIdTotalIdentifier);
        return (int) userIdTotalTokens.entrySet().stream().filter(e -> !e.getValue().isEmpty()).count();
    }

    @Override
    public void sendMessage(String userId, String message) {
        sendMessageObject(userId, message, null);
    }

    @Override
    public void sendMessageObject(String userId, String message, Object messageObject) {
        List<WebSocketDTO> userWebSockets = getList(userId);
        if (ObjectUtil.isNotEmpty(userWebSockets)) {
            for (WebSocketDTO webSocket : userWebSockets) {
                WebSocketUtil.sendMessageAsync(webSocket.getSession(), new WebSocketTransferDTO(WebSocketEventConstants.EVENT_TYPE_MESSAGE, message, messageObject).toString());
            }
        }
    }

    @Override
    public void broadcast(String message) {
        localWebSocketMap().values().forEach(webSocket -> WebSocketUtil.sendMessageAsync(webSocket.getSession(), message));
    }

    @Override
    public void onMessage(String identifier, String message) {
        WebSocketDTO webSocket = connections.get(identifier);

        // 发布一下消息事件，交给业务监听方
        if (webSocket != null) {
            applicationContext.publishEvent(new WebSocketEvent(webSocket, WebSocketEventConstants.EVENT_TYPE_MESSAGE, message));
        }
    }

    /**
     * 获取某个账号下的所有的连接
     *
     * <AUTHOR>
     * @since 2024-01-15 10:40
     */
    private List<WebSocketDTO> getUserTotalConnection(String userId) {
        Set<String> userIdIdentifierList = userIdTotalIdentifier.get(userId);
        List<WebSocketDTO> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(userIdIdentifierList)) {
            userIdIdentifierList.forEach(identifier -> {
                WebSocketDTO ws = connections.get(identifier);
                if (ws != null) {
                    list.add(ws);
                }
            });
        }
        return list;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

}
