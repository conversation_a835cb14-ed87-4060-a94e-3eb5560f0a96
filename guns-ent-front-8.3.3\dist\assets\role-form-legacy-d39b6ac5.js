System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-9a185ac3.js","./index-legacy-94a6fc23.js","./RoleTypeApi-legacy-6008d05b.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var a,r,o,d,t,u,m,n,s,p,i,f,y,g,v,c,_,C,b,I,x;return{setters:[e=>{a=e.s,r=e.r,o=e.X,d=e.a,t=e.f,u=e.w,m=e.d,n=e.g,s=e.h,p=e.l,i=e.u,f=e.v,y=e.y,g=e.z,v=e.A,c=e.as,_=e.$,C=e.G,b=e.H},e=>{I=e._},null,null,e=>{x=e.R},null,null,null,null,null,null,null],execute:function(){e("default",{__name:"role-form",props:{form:Object,superAdminFlag:Boolean},setup(e){const l=e,h=a({roleName:[{required:!0,message:"请输入角色名称",type:"string",trigger:"blur"}],roleCode:[{required:!0,message:"请输入角色编码",type:"string",trigger:"blur"}],roleSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}],statusFlag:[{required:!0,message:"请选择状态",type:"number",trigger:"change"}],roleType:[{required:!0,message:"请选择角色类型",type:"number",trigger:"change"}],roleCategoryId:[{required:!0,message:"请选择角色分类",type:"string",trigger:"change"}],roleCompanyIdWrapper:[{required:!0,message:"请选择所属公司",type:"string",trigger:"change"}]}),w=r(!1),j=r({selectCompanyList:[]}),T=r([]),F=()=>{j.value.selectCompanyList=[{bizId:l.form.roleCompanyId,name:l.form.roleCompanyIdWrapper}],w.value=!0},U=e=>{const{bizId:a,name:r}=e.selectCompanyList[0];l.form.roleCompanyId=a,l.form.roleCompanyIdWrapper=r,l.form.roleCategoryId=void 0,N()},k=({target:e})=>{l.form.roleCompanyId=null,l.form.roleCompanyIdWrapper=null,l.form.roleCategoryId=void 0},N=async()=>{var e;const a=await x.treeList({categoryType:l.form.roleType,companyId:null===(e=l.form)||void 0===e?void 0:e.roleCompanyId});T.value=a.data};return o((()=>{var e;return null===(e=l.form)||void 0===e?void 0:e.roleType}),(e=>{[15].includes(e)&&N()}),{deep:!0,immediate:!0}),o((()=>{var e,a;return[null===(e=l.form)||void 0===e?void 0:e.roleCompanyId,null===(a=l.form)||void 0===a?void 0:a.roleType]}),(e=>{e[0]&&e[1]?N():T.value=[]}),{deep:!0,immediate:!0}),(a,r)=>{const o=p,x=i,N=f,q=y,A=g,W=v,S=c,L=_,z=C,R=I,B=b;return d(),t(B,{ref:"formRef",model:e.form,rules:h,layout:"vertical"},{default:u((()=>[m(z,{gutter:20},{default:u((()=>[m(N,{xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"角色名称:",name:"roleName"},{default:u((()=>[m(o,{value:e.form.roleName,"onUpdate:value":r[0]||(r[0]=l=>e.form.roleName=l),"allow-clear":"",placeholder:"请输入角色名称"},null,8,["value"])])),_:1})])),_:1}),m(N,{xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"角色编码:",name:"roleCode"},{default:u((()=>[m(o,{value:e.form.roleCode,"onUpdate:value":r[1]||(r[1]=l=>e.form.roleCode=l),"allow-clear":"",placeholder:"请输入角色编码"},null,8,["value"])])),_:1})])),_:1}),m(N,{xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"排序:",name:"roleSort"},{default:u((()=>[m(q,{value:e.form.roleSort,"onUpdate:value":r[2]||(r[2]=l=>e.form.roleSort=l),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),m(N,{xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"状态:",name:"statusFlag"},{default:u((()=>[m(W,{value:e.form.statusFlag,"onUpdate:value":r[3]||(r[3]=l=>e.form.statusFlag=l)},{default:u((()=>[m(A,{value:1},{default:u((()=>r[9]||(r[9]=[n("启用")]))),_:1,__:[9]}),m(A,{value:2},{default:u((()=>r[10]||(r[10]=[n("禁用")]))),_:1,__:[10]})])),_:1},8,["value"])])),_:1})])),_:1}),m(N,{xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"角色类型:",name:"roleType"},{default:u((()=>[m(W,{value:e.form.roleType,"onUpdate:value":r[4]||(r[4]=l=>e.form.roleType=l),onChange:k},{default:u((()=>[l.superAdminFlag?(d(),t(A,{key:0,value:10},{default:u((()=>r[11]||(r[11]=[n("系统角色")]))),_:1,__:[11]})):s("",!0),l.superAdminFlag?(d(),t(A,{key:1,value:15},{default:u((()=>r[12]||(r[12]=[n("业务角色")]))),_:1,__:[12]})):s("",!0),m(A,{value:20},{default:u((()=>r[13]||(r[13]=[n("公司角色")]))),_:1,__:[13]})])),_:1},8,["value"])])),_:1})])),_:1}),20==e.form.roleType?(d(),t(N,{key:0,xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"所属公司:",name:"roleCompanyIdWrapper"},{default:u((()=>[m(o,{value:e.form.roleCompanyIdWrapper,"onUpdate:value":r[5]||(r[5]=l=>e.form.roleCompanyIdWrapper=l),onFocus:F,disabled:!l.superAdminFlag,placeholder:"请选择所属公司"},null,8,["value","disabled"])])),_:1})])),_:1})):s("",!0),15==e.form.roleType||20==e.form.roleType&&e.form.roleCompanyId?(d(),t(N,{key:1,xs:24,sm:24,md:12},{default:u((()=>[m(x,{label:"角色分类:",name:"roleCategoryId"},{default:u((()=>[m(S,{value:e.form.roleCategoryId,"onUpdate:value":r[6]||(r[6]=l=>e.form.roleCategoryId=l),style:{width:"100%"},showSearch:"","tree-data":T.value,treeNodeFilterProp:"roleCategoryName","dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择角色分类",fieldNames:{children:"children",label:"roleCategoryName",key:"id",value:"id"},"allow-clear":"","tree-default-expand-all":""},null,8,["value","tree-data"])])),_:1})])),_:1})):s("",!0),m(N,{span:24},{default:u((()=>[m(x,{label:"备注"},{default:u((()=>[m(L,{value:e.form.remark,"onUpdate:value":r[7]||(r[7]=l=>e.form.remark=l),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1}),w.value?(d(),t(R,{key:0,visible:w.value,"onUpdate:visible":r[8]||(r[8]=e=>w.value=e),title:"选择公司",data:j.value,showTab:["company"],onDone:U},null,8,["visible","data"])):s("",!0)])),_:1},8,["model","rules"])}}})}}}));
