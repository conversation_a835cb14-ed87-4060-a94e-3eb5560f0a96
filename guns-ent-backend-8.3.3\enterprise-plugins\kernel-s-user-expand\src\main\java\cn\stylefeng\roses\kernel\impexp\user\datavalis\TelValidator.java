package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 电话号码的验证
 *
 * <AUTHOR>
 * @since 2024/2/6 23:07
 */
public class TelValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(originValue);
        }

        Pattern pattern = null, pattern1 = null;
        Matcher matcher = null;
        boolean result = false;
        pattern = Pattern.compile("^[0][1-9]{2,3}-[0-9]{5,10}$");  // 验证带区号的
        pattern1 = Pattern.compile("^[1-9]{1}[0-9]{5,8}$");         // 验证没有区号的
        if (originValue.length() > 9) {
            matcher = pattern.matcher(originValue);
            result = matcher.matches();
        } else {
            matcher = pattern1.matcher(originValue);
            result = matcher.matches();
        }

        if (result) {
            return new ExcelLineParseResult(originValue);
        } else {
            return new ExcelLineParseResult(false, originValue, originValue, "电话号码格式不正确");
        }
    }

}
