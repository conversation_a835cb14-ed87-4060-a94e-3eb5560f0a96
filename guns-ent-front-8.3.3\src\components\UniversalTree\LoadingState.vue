<template>
  <div class="tree-loading-state">
    <!-- 初始加载状态 -->
    <div v-if="loadingType === 'initial'" class="loading-container initial-loading">
      <a-spin size="large" :spinning="true">
        <div class="loading-content">
          <div class="loading-icon">
            <loading-outlined />
          </div>
          <div class="loading-text">{{ loadingText || '正在加载数据...' }}</div>
          <div class="loading-progress" v-if="showProgress">
            <a-progress 
              :percent="progress" 
              :show-info="false" 
              size="small"
              :stroke-color="progressColor"
            />
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 懒加载状态 -->
    <div v-else-if="loadingType === 'lazy'" class="loading-container lazy-loading">
      <a-spin size="small" :spinning="true">
        <div class="lazy-loading-content">
          <loading-outlined />
          <span class="lazy-loading-text">{{ loadingText || '加载中...' }}</span>
        </div>
      </a-spin>
    </div>

    <!-- 搜索加载状态 -->
    <div v-else-if="loadingType === 'search'" class="loading-container search-loading">
      <a-spin size="small" :spinning="true">
        <div class="search-loading-content">
          <search-outlined />
          <span class="search-loading-text">{{ loadingText || '搜索中...' }}</span>
        </div>
      </a-spin>
    </div>

    <!-- 刷新加载状态 -->
    <div v-else-if="loadingType === 'refresh'" class="loading-container refresh-loading">
      <a-spin size="small" :spinning="true">
        <div class="refresh-loading-content">
          <reload-outlined />
          <span class="refresh-loading-text">{{ loadingText || '刷新中...' }}</span>
        </div>
      </a-spin>
    </div>

    <!-- 骨架屏加载 -->
    <div v-else-if="loadingType === 'skeleton'" class="loading-container skeleton-loading">
      <div class="skeleton-tree">
        <div 
          v-for="(item, index) in skeletonItems" 
          :key="index" 
          class="skeleton-item"
          :style="{ paddingLeft: `${item.level * 24}px` }"
        >
          <a-skeleton-button 
            :active="true" 
            size="small" 
            shape="round"
            :style="{ width: `${item.width}px`, height: '24px' }"
          />
        </div>
      </div>
    </div>

    <!-- 默认加载状态 -->
    <div v-else class="loading-container default-loading">
      <a-spin :spinning="true" :tip="loadingText || '加载中...'">
        <div class="default-loading-placeholder"></div>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  LoadingOutlined, 
  SearchOutlined, 
  ReloadOutlined 
} from '@ant-design/icons-vue'

// 定义组件名称
defineOptions({
  name: 'TreeLoadingState'
})

// 定义Props
const props = defineProps({
  loadingType: {
    type: String,
    default: 'initial',
    validator: (value) => ['initial', 'lazy', 'search', 'refresh', 'skeleton', 'default'].includes(value)
  },
  loadingText: {
    type: String,
    default: ''
  },
  showProgress: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  progressColor: {
    type: String,
    default: '#1890ff'
  },
  skeletonCount: {
    type: Number,
    default: 8
  }
})

// 计算骨架屏项目
const skeletonItems = computed(() => {
  const items = []
  for (let i = 0; i < props.skeletonCount; i++) {
    items.push({
      level: Math.floor(Math.random() * 3), // 0-2层级
      width: 80 + Math.random() * 120 // 80-200px宽度
    })
  }
  return items
})
</script>

<style scoped lang="less">
.tree-loading-state {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-container {
    width: 100%;
    
    &.initial-loading {
      min-height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .loading-content {
        text-align: center;
        
        .loading-icon {
          font-size: 32px;
          color: #1890ff;
          margin-bottom: 16px;
        }
        
        .loading-text {
          font-size: 14px;
          color: #666;
          margin-bottom: 16px;
          display: block;
        }
        
        .loading-progress {
          width: 200px;
          margin: 0 auto;
        }
      }
    }
    
    &.lazy-loading {
      padding: 8px 16px;
      
      .lazy-loading-content {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #666;
      }
    }
    
    &.search-loading {
      padding: 8px 16px;
      
      .search-loading-content {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #1890ff;
      }
    }
    
    &.refresh-loading {
      padding: 8px 16px;
      
      .refresh-loading-content {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #52c41a;
      }
    }
    
    &.skeleton-loading {
      padding: 16px;
      
      .skeleton-tree {
        .skeleton-item {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    &.default-loading {
      min-height: 100px;
      
      .default-loading-placeholder {
        height: 100px;
      }
    }
  }
}

// 加载动画
.loading-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 旋转动画
.loading-icon,
.lazy-loading-content > .anticon,
.search-loading-content > .anticon,
.refresh-loading-content > .anticon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tree-loading-state {
    .loading-container {
      &.initial-loading {
        min-height: 150px;
        
        .loading-content {
          .loading-icon {
            font-size: 24px;
            margin-bottom: 12px;
          }
          
          .loading-text {
            font-size: 12px;
            margin-bottom: 12px;
          }
          
          .loading-progress {
            width: 150px;
          }
        }
      }
      
      &.skeleton-loading {
        padding: 12px;
      }
    }
  }
}
</style>
</content>
</invoke>