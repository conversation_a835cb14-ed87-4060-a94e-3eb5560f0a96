System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./SupplierSelector-legacy-fba3813b.js","./ProductApi-legacy-33feae42.js","./CategorySelector-legacy-c3396c33.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-9a185ac3.js","./productCategoryApi-legacy-247b2407.js"],(function(e,l){"use strict";var a,r,t,u,d,i,n,o,c,m,p,s,f,g,v,_,b,y,h,P,C,I,T,A,E;return{setters:[e=>{a=e._,r=e.s,t=e.r,u=e.X,d=e.k,i=e.a,n=e.f,o=e.w,c=e.d,m=e.g,p=e.h,s=e.m,f=e.l,g=e.u,v=e.v,_=e.G,b=e.W,y=e.J,h=e.y,P=e.$,C=e.H,I=e.M},null,e=>{T=e._},e=>{A=e.P},e=>{E=e.default},null,null,null,null],execute:function(){var l=document.createElement("style");l.textContent=".ant-form-item[data-v-88d1f7f7]{margin-bottom:16px}.ant-form-item-label[data-v-88d1f7f7]{font-weight:500}.ant-input[data-v-88d1f7f7],.ant-select[data-v-88d1f7f7]{border-radius:4px}@media (max-width: 768px){[data-v-88d1f7f7] .ant-modal{width:95%!important;margin:10px auto}[data-v-88d1f7f7] .ant-form-item-label{text-align:left!important}}\n",document.head.appendChild(l);const U={name:"ProductAdd",components:{SupplierSelector:T,CategorySelector:E},props:{visible:Boolean},emits:["update:visible","done"],setup(e,{emit:l}){const a=r({pricingType:"NORMAL",status:"ACTIVE",retailPrice:null,unitPrice:null,piecePrice:null,referencePrice:null}),d=t(null),i=t(!1),n=r({productCode:[{required:!0,message:"请输入商品编码",trigger:"blur"},{max:50,message:"商品编码不能超过50个字符",trigger:"blur"}],productName:[{required:!0,message:"请输入商品名称",trigger:"blur"},{max:200,message:"商品名称不能超过200个字符",trigger:"blur"}],categoryId:[{required:!0,message:"请选择商品分类",trigger:"change"}],supplierId:[{required:!0,message:"请选择供应商",trigger:"change"}],pricingType:[{required:!0,message:"请选择计价类型",trigger:"change"}]}),o=e=>{l("update:visible",e)},c=e=>{switch(delete n.retailPrice,delete n.unitPrice,delete n.piecePrice,delete n.referencePrice,e){case"NORMAL":n.retailPrice=[{required:!0,message:"请输入零售价格",trigger:"blur",type:"number"}];break;case"WEIGHT":n.unitPrice=[{required:!0,message:"请输入单位价格",trigger:"blur",type:"number"}];break;case"PIECE":n.piecePrice=[{required:!0,message:"请输入单份价格",trigger:"blur",type:"number"}]}};return u((()=>e.visible),(e=>{e&&(Object.keys(a).forEach((e=>{"pricingType"===e?a[e]="NORMAL":"status"===e?a[e]="ACTIVE":e.includes("Price")?a[e]=null:a[e]=void 0})),d.value&&d.value.resetFields(),c("NORMAL"))}),{immediate:!0}),{form:a,formRef:d,loading:i,rules:n,updateVisible:o,handleSupplierChange:(e,l)=>{console.log("供应商变更:",e,l)},handlePricingTypeChange:e=>{console.log("计价类型变更:",e),a.retailPrice=null,a.unitPrice=null,a.piecePrice=null,a.referencePrice=null,c(e)},save:async()=>{try{await d.value.validate(),i.value=!0;const e={...a};await A.add(e),s.success("新增商品成功"),o(!1),l("done")}catch(e){console.error("新增商品失败:",e),s.error(e.message||"新增失败")}finally{i.value=!1}}}}};e("default",a(U,[["render",function(e,l,a,r,t,u){const s=f,A=g,E=v,U=_,N=d("category-selector"),w=T,S=b,x=y,k=h,R=P,j=C,O=I;return i(),n(O,{visible:a.visible,title:"新增商品",width:800,"confirm-loading":r.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":r.updateVisible,onOk:r.save},{default:o((()=>[c(j,{ref:"formRef",model:r.form,rules:r.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:o((()=>[c(U,{gutter:16},{default:o((()=>[c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"商品编码",name:"productCode"},{default:o((()=>[c(s,{value:r.form.productCode,"onUpdate:value":l[0]||(l[0]=e=>r.form.productCode=e),placeholder:"请输入商品编码"},null,8,["value"])])),_:1})])),_:1}),c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"商品名称",name:"productName"},{default:o((()=>[c(s,{value:r.form.productName,"onUpdate:value":l[1]||(l[1]=e=>r.form.productName=e),placeholder:"请输入商品名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:16},{default:o((()=>[c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"商品简称",name:"productShortName"},{default:o((()=>[c(s,{value:r.form.productShortName,"onUpdate:value":l[2]||(l[2]=e=>r.form.productShortName=e),placeholder:"请输入商品简称"},null,8,["value"])])),_:1})])),_:1}),c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"商品分类",name:"categoryId"},{default:o((()=>[c(N,{value:r.form.categoryId,"onUpdate:value":l[3]||(l[3]=e=>r.form.categoryId=e)},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:16},{default:o((()=>[c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"供应商",name:"supplierId"},{default:o((()=>[c(w,{value:r.form.supplierId,"onUpdate:value":l[4]||(l[4]=e=>r.form.supplierId=e),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},onChange:r.handleSupplierChange},null,8,["value","onChange"])])),_:1})])),_:1}),c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"计价类型",name:"pricingType"},{default:o((()=>[c(x,{value:r.form.pricingType,"onUpdate:value":l[5]||(l[5]=e=>r.form.pricingType=e),placeholder:"请选择计价类型",onChange:r.handlePricingTypeChange},{default:o((()=>[c(S,{value:"NORMAL"},{default:o((()=>l[16]||(l[16]=[m("普通商品")]))),_:1,__:[16]}),c(S,{value:"WEIGHT"},{default:o((()=>l[17]||(l[17]=[m("计重商品")]))),_:1,__:[17]}),c(S,{value:"PIECE"},{default:o((()=>l[18]||(l[18]=[m("计件商品")]))),_:1,__:[18]}),c(S,{value:"VARIABLE"},{default:o((()=>l[19]||(l[19]=[m("不定价商品")]))),_:1,__:[19]})])),_:1},8,["value","onChange"])])),_:1})])),_:1})])),_:1}),r.form.pricingType?(i(),n(U,{key:0,gutter:16},{default:o((()=>["NORMAL"===r.form.pricingType?(i(),n(E,{key:0,md:12,sm:24},{default:o((()=>[c(A,{label:"零售价格",name:"retailPrice"},{default:o((()=>[c(k,{value:r.form.retailPrice,"onUpdate:value":l[6]||(l[6]=e=>r.form.retailPrice=e),placeholder:"请输入零售价格",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):p("",!0),"WEIGHT"===r.form.pricingType?(i(),n(E,{key:1,md:12,sm:24},{default:o((()=>[c(A,{label:"单位价格",name:"unitPrice"},{default:o((()=>[c(k,{value:r.form.unitPrice,"onUpdate:value":l[7]||(l[7]=e=>r.form.unitPrice=e),placeholder:"请输入单位价格(每公斤)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):p("",!0),"PIECE"===r.form.pricingType?(i(),n(E,{key:2,md:12,sm:24},{default:o((()=>[c(A,{label:"单份价格",name:"piecePrice"},{default:o((()=>[c(k,{value:r.form.piecePrice,"onUpdate:value":l[8]||(l[8]=e=>r.form.piecePrice=e),placeholder:"请输入单份价格",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):p("",!0),"VARIABLE"===r.form.pricingType?(i(),n(E,{key:3,md:12,sm:24},{default:o((()=>[c(A,{label:"参考价格",name:"referencePrice"},{default:o((()=>[c(k,{value:r.form.referencePrice,"onUpdate:value":l[9]||(l[9]=e=>r.form.referencePrice=e),placeholder:"请输入参考价格(可选)",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1})):p("",!0)])),_:1})):p("",!0),c(U,{gutter:16},{default:o((()=>[c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"规格",name:"specification"},{default:o((()=>[c(s,{value:r.form.specification,"onUpdate:value":l[10]||(l[10]=e=>r.form.specification=e),placeholder:"请输入商品规格"},null,8,["value"])])),_:1})])),_:1}),c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"单位",name:"unit"},{default:o((()=>[c(s,{value:r.form.unit,"onUpdate:value":l[11]||(l[11]=e=>r.form.unit=e),placeholder:"请输入商品单位"},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:16},{default:o((()=>[c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"品牌",name:"brand"},{default:o((()=>[c(s,{value:r.form.brand,"onUpdate:value":l[12]||(l[12]=e=>r.form.brand=e),placeholder:"请输入商品品牌"},null,8,["value"])])),_:1})])),_:1}),c(E,{md:12,sm:24},{default:o((()=>[c(A,{label:"状态",name:"status"},{default:o((()=>[c(x,{value:r.form.status,"onUpdate:value":l[13]||(l[13]=e=>r.form.status=e),placeholder:"请选择状态"},{default:o((()=>[c(S,{value:"ACTIVE"},{default:o((()=>l[20]||(l[20]=[m("启用")]))),_:1,__:[20]}),c(S,{value:"INACTIVE"},{default:o((()=>l[21]||(l[21]=[m("禁用")]))),_:1,__:[21]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:16},{default:o((()=>[c(E,{span:24},{default:o((()=>[c(A,{label:"商品描述",name:"description","label-col":{span:3},"wrapper-col":{span:21}},{default:o((()=>[c(R,{value:r.form.description,"onUpdate:value":l[14]||(l[14]=e=>r.form.description=e),placeholder:"请输入商品描述",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:16},{default:o((()=>[c(E,{span:24},{default:o((()=>[c(A,{label:"备注",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:o((()=>[c(R,{value:r.form.remark,"onUpdate:value":l[15]||(l[15]=e=>r.form.remark=e),placeholder:"请输入备注",rows:2},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading","onUpdate:visible","onOk"])}],["__scopeId","data-v-88d1f7f7"]]))}}}));
