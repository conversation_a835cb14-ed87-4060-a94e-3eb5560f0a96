System.register(["./index-legacy-ee1db0c7.js","./index-legacy-53580278.js"],(function(e,t){"use strict";var a,n,o,i,d,r,c,s,u,l,m,b,v,f,p,g,y,_,h,x,k,w;return{setters:[e=>{a=e._,n=e.b3,o=e.r,i=e.L,d=e.o,r=e.bv,c=e.a,s=e.c,u=e.d,l=e.w,m=e.b,b=e.f,v=e.at,f=e.h,p=e.g,g=e.aR,y=e.aI,_=e.aJ,h=e.p,x=e.bq,k=e.q},e=>{w=e._}],execute:function(){var C=document.createElement("style");C.textContent=".sidebar-content .ant-menu-inline[data-v-5c23138a]{border-right:0}.sidebar-content .ant-menu[data-v-5c23138a]{--component-background: #fff !important;--background-color-light: #fff !important}.sidebar-content .ant-menu-inline .ant-menu-item[data-v-5c23138a]:after{border-right:0}.sidebar-content .ant-menu-submenu-selected[data-v-5c23138a]{color:#999}.sidebar-content .ant-menu-submenu-title[data-v-5c23138a]{font-size:14px;padding-left:10px!important;color:#60666b}.sidebar-content .ant-menu-submenu-title[data-v-5c23138a]:hover{color:#60666b}.sidebar-content .ant-menu-item-selected[data-v-5c23138a]{color:#0f56d7!important;border-radius:4px;font-weight:700!important;background:rgba(207,221,247,.35)!important}.sidebar-content .ant-menu-item-selected .iconfont[data-v-5c23138a]{color:#0f56d7!important;font-weight:400!important}.sidebar-content .ant-menu-item[data-v-5c23138a]:active{color:#0f56d7!important;border-radius:4px;font-weight:500!important;background:rgba(207,221,247,.35)!important}.sidebar-content .ant-menu-item:active .iconfont[data-v-5c23138a]{color:#0f56d7!important}.sidebar-content .ant-menu-item[data-v-5c23138a]:hover{background:#f3f3f3;border-radius:4px}.sidebar-content .ant-menu-item[data-v-5c23138a]{font-weight:400;color:#43505e;padding-left:20px!important}.sidebar-content .ant-menu-submenu[data-v-5c23138a]{border-top:1px solid #eee}.sidebar-content .ant-menu-submenu[data-v-5c23138a]:first-child{border-top:1px solid #fbfbfb}.sidebar-content .ant-menu-submenu-title[data-v-5c23138a]:active{background:#fbfbfb!important}.sidebar-content .ant-menu-submenu-arrow[data-v-5c23138a]{display:none}\n",document.head.appendChild(C);const I={class:"guns-layout"},S={class:"guns-layout-sidebar width-100"},F={class:"sidebar-content"},O={class:"sidebar-content"},j={class:"guns-layout-content",style:{padding:"0"}},N={class:"guns-layout"};e("default",a(Object.assign({name:"NewSysConfig"},{__name:"index",setup(e){const a=n(),C=y((()=>_((()=>t.import("./index-legacy-a908b8e0.js")),void 0))),E=y((()=>_((()=>t.import("./file-config-legacy-d473ea49.js")),void 0))),G=o(["1"]),L=o([]),K=o(""),q=i((()=>{var e;return null!==(e=a.authorities)&&void 0!==e?e:[]})),Y=({key:e})=>{K.value=e},z=()=>{G.value=["1"]};return d((()=>{let e="";L.value=[],q.value.includes("SYS_CONFIG")?e="1":q.value.includes("FILE_CONFIG")&&(e="2"),e&&(L.value=[e],Y({key:e}))})),(e,t)=>{const a=h,n=x,o=k,i=w,d=r("permission");return c(),s("div",I,[u(i,{width:"292px"},{content:l((()=>[m("div",j,[m("div",N,["1"==K.value?(c(),b(v(C),{key:0,style:{"flex-direction":"row"}})):f("",!0),"2"==K.value?(c(),b(v(E),{key:1})):f("",!0)])])])),default:l((()=>[m("div",S,[m("div",F,[m("div",O,[u(o,{selectedKeys:L.value,"onUpdate:selectedKeys":t[0]||(t[0]=e=>L.value=e),class:"sidebar-menu",mode:"inline","open-keys":G.value,onSelect:Y,onOpenChange:z},{default:l((()=>[u(n,{key:"1"},{title:l((()=>t[1]||(t[1]=[p("配置管理")]))),default:l((()=>[g((c(),s("div",null,[u(a,{key:"1"},{default:l((()=>t[2]||(t[2]=[p(" 系统配置 ")]))),_:1,__:[2]})])),[[d,["SYS_CONFIG"]]]),g((c(),s("div",null,[u(a,{key:"2"},{default:l((()=>t[3]||(t[3]=[p(" 文件配置 ")]))),_:1,__:[3]})])),[[d,["FILE_CONFIG"]]])])),_:1})])),_:1},8,["selectedKeys","open-keys"])])])])])),_:1})])}}}),[["__scopeId","data-v-5c23138a"]]))}}}));
