<!--
  现金支付组件
  
  处理现金支付流程，包括金额输入、找零计算等功能
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cash-payment">
    <!-- 现金输入区域 -->
    <div class="cash-input-section">
      <div class="input-group">
        <div class="input-label">
          <icon-font iconClass="icon-cash" />
          实收金额
        </div>
        <a-input-number
          v-model:value="receivedAmount"
          :min="0"
          :precision="2"
          :step="0.01"
          size="large"
          placeholder="请输入实收金额"
          class="cash-input"
          @change="handleAmountChange"
          @pressEnter="handleConfirm"
        />
      </div>

      <!-- 支付信息显示 -->
      <div class="payment-info">
        <div class="info-item">
          <span class="info-label">应付金额:</span>
          <span class="info-value">¥{{ formatPrice(paymentAmount) }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">实收金额:</span>
          <span class="info-value received">¥{{ formatPrice(receivedAmount) }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">找零金额:</span>
          <span class="info-value">¥{{ formatPrice(changeAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- 快捷金额按钮 -->
    <div class="quick-amounts">
      <div class="quick-title">
        <icon-font iconClass="icon-quick" />
        快捷金额
      </div>
      <div class="amount-buttons">
        <a-button
          v-for="amount in quickAmounts"
          :key="amount"
          size="small"
          @click="setReceivedAmount(amount)"
          class="quick-amount-btn"
        >
          ¥{{ amount }}
        </a-button>
        <a-button
          size="small"
          @click="setReceivedAmount(paymentAmount)"
          class="quick-amount-btn exact"
        >
          刚好
        </a-button>
        <a-button
          size="small"
          @click="clearAmount"
          class="quick-amount-btn clear"
        >
          清空
        </a-button>
      </div>
    </div>

    <!-- 找零显示 -->
    <div class="change-display" v-if="changeAmount > 0">
      <div class="change-header">
        <icon-font iconClass="icon-change" />
        找零信息
      </div>
      <div class="change-content">
        <div class="change-amount">
          <span class="change-label">找零金额:</span>
          <span class="change-value">¥{{ formatPrice(changeAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="cash-actions">
      <a-button
        size="large"
        @click="handleCancel"
        class="action-btn cancel"
      >
        取消
      </a-button>
      <a-button
        type="primary"
        size="large"
        @click="handleConfirm"
        :disabled="!canConfirm"
        :loading="loading"
        class="action-btn confirm"
      >
        <template #icon>
          <check-outlined />
        </template>
        确认收款
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import IconFont from '@/components/common/IconFont/index.vue'

// 定义组件名称
defineOptions({
  name: 'CashPayment'
})

// 定义Props
const props = defineProps({
  paymentAmount: {
    type: Number,
    required: true
  },
  initialAmount: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'confirm-payment',
  'cancel-payment',
  'amount-change'
])

// 响应式状态
const receivedAmount = ref(0)

// 快捷金额选项
const quickAmounts = [10, 20, 50, 100, 200, 500]

// 计算属性
const changeAmount = computed(() => {
  return Math.max(0, receivedAmount.value - props.paymentAmount)
})

const canConfirm = computed(() => {
  return receivedAmount.value >= props.paymentAmount
})

// 方法
const formatPrice = (price) => {
  return (price || 0).toFixed(2)
}

const setReceivedAmount = (amount) => {
  receivedAmount.value = amount
  handleAmountChange(amount)
}

const clearAmount = () => {
  receivedAmount.value = 0
  handleAmountChange(0)
}

const handleAmountChange = (value) => {
  emit('amount-change', {
    receivedAmount: value,
    changeAmount: changeAmount.value,
    canConfirm: canConfirm.value
  })
}

const handleConfirm = () => {
  if (!canConfirm.value) {
    message.warning('请输入正确的实收金额')
    return
  }
  
  emit('confirm-payment', {
    receivedAmount: receivedAmount.value,
    changeAmount: changeAmount.value
  })
}

const handleCancel = () => {
  emit('cancel-payment')
}

// 监听器
watch(
  () => props.initialAmount,
  (newAmount) => {
    if (newAmount > 0) {
      setReceivedAmount(newAmount)
    }
  },
  { immediate: true }
)

watch(
  () => props.paymentAmount,
  (newAmount) => {
    if (receivedAmount.value < newAmount) {
      setReceivedAmount(newAmount)
    }
  }
)
</script>

<style scoped>
.cash-payment {
  padding: 20px 24px;
  background: #f9f9f9;
  border-top: 1px solid #f0f0f0;
}

.cash-input-section {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 12px;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.cash-input {
  width: 100%;
}

.payment-info {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.info-label {
  color: #595959;
}

.info-value {
  font-weight: 600;
  color: #262626;
}

.info-value.received {
  color: #1890ff;
}

.quick-amounts {
  margin-bottom: 20px;
}

.quick-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 13px;
  color: #595959;
}

.amount-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-amount-btn {
  min-width: 60px;
  height: 32px;
  border-radius: 6px;
  font-size: 12px;
}

.quick-amount-btn.exact {
  background: #52c41a;
  border-color: #52c41a;
  color: #fff;
}

.quick-amount-btn.exact:hover {
  background: #73d13d;
  border-color: #73d13d;
}

.quick-amount-btn.clear {
  background: #ff7875;
  border-color: #ff7875;
  color: #fff;
}

.quick-amount-btn.clear:hover {
  background: #ff9c99;
  border-color: #ff9c99;
}

.change-display {
  margin-bottom: 20px;
  background: #e6f7ff;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
  overflow: hidden;
}

.change-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: #bae7ff;
  font-size: 13px;
  font-weight: 500;
  color: #0050b3;
}

.change-content {
  padding: 16px;
}

.change-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.change-label {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.change-value {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
}

.cash-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 44px;
  font-size: 14px;
  font-weight: 500;
}

.action-btn.confirm {
  flex: 2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cash-payment {
    padding: 16px 20px;
  }
  
  .payment-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .amount-buttons {
    gap: 6px;
  }
  
  .quick-amount-btn {
    min-width: 50px;
    height: 28px;
    font-size: 11px;
  }
  
  .change-value {
    font-size: 18px;
  }
  
  .cash-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-btn {
    height: 40px;
  }
}

/* 动画效果 */
.change-display {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>