<template>
  <a-modal
    :title="isUpdate ? '编辑客户' : '新增客户'"
    :width="900"
    :visible="visible"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="客户编码" name="customerCode">
            <a-input v-model:value="form.customerCode" placeholder="请输入客户编码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="客户名称" name="customerName">
            <a-input v-model:value="form.customerName" placeholder="请输入客户名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="客户简称" name="customerShortName">
            <a-input v-model:value="form.customerShortName" placeholder="请输入客户简称" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="客户类型" name="customerType">
            <a-select v-model:value="form.customerType" placeholder="请选择客户类型">
              <a-select-option v-for="item in customerTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="客户等级" name="customerLevel">
            <a-select v-model:value="form.customerLevel" placeholder="请选择客户等级">
              <a-select-option v-for="item in customerLevelOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="联系人" name="contactPerson">
            <a-input v-model:value="form.contactPerson" placeholder="请输入联系人" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="联系电话" name="contactPhone">
            <a-input v-model:value="form.contactPhone" placeholder="请输入联系电话" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="手机号码" name="contactMobile">
            <a-input v-model:value="form.contactMobile" placeholder="请输入手机号码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="邮箱地址" name="contactEmail">
            <a-input v-model:value="form.contactEmail" placeholder="请输入邮箱地址" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="联系地址" name="contactAddress" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.contactAddress" placeholder="请输入联系地址" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="营业执照号" name="businessLicenseNo">
            <a-input v-model:value="form.businessLicenseNo" placeholder="请输入营业执照号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="税务登记号" name="taxNo">
            <a-input v-model:value="form.taxNo" placeholder="请输入税务登记号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="开户银行" name="bankName">
            <a-input v-model:value="form.bankName" placeholder="请输入开户银行" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="银行账号" name="bankAccount">
            <a-input v-model:value="form.bankAccount" placeholder="请输入银行账号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="信用额度" name="creditLimit">
            <a-input-number 
              v-model:value="form.creditLimit" 
              placeholder="请输入信用额度" 
              :min="0" 
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="账期天数" name="paymentTerms">
            <a-input-number 
              v-model:value="form.paymentTerms" 
              placeholder="请输入账期天数" 
              :min="0" 
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="关联区域" name="regionIds" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <region-selector
              ref="regionSelectorRef"
              v-model="form.regionIds"
              placeholder="请选择客户服务的区域"
              @change="handleRegionChange"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CustomerApi } from '../api/CustomerApi';
import RegionSelector from '@/components/common/RegionSelector/index.vue';

export default {
  name: 'CustomerEdit',
  components: {
    RegionSelector
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  emits: ['update:visible', 'done'],
  setup(props, { emit }) {
    // 表单数据
    const form = reactive({});
    const formRef = ref(null);
    const regionSelectorRef = ref(null);
    const loading = ref(false);

    // 是否是修改
    const isUpdate = ref(false);

    // 选项数据
    const customerTypeOptions = CustomerApi.getCustomerTypeOptions();
    const customerLevelOptions = CustomerApi.getCustomerLevelOptions();
    const statusOptions = CustomerApi.getCustomerStatusOptions();

    // 表单验证规则
    const rules = reactive({
      customerCode: [
        { required: true, message: '请输入客户编码', trigger: 'blur' },
        { max: 50, message: '客户编码不能超过50个字符', trigger: 'blur' }
      ],
      customerName: [
        { required: true, message: '请输入客户名称', trigger: 'blur' },
        { max: 200, message: '客户名称不能超过200个字符', trigger: 'blur' }
      ],
      customerType: [
        { required: true, message: '请选择客户类型', trigger: 'change' }
      ],
      contactEmail: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      contactPhone: [
        { pattern: /^[0-9-()（）\s]+$/, message: '请输入正确的电话号码', trigger: 'blur' }
      ],
      contactMobile: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      creditLimit: [
        { type: 'number', min: 0, message: '信用额度不能小于0', trigger: 'blur' }
      ],
      paymentTerms: [
        { type: 'number', min: 0, message: '账期天数不能小于0', trigger: 'blur' }
      ]
    });

    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 保存
    const save = async () => {
      try {
        await formRef.value.validate();
        loading.value = true;
        
        // 准备提交的数据
        const submitData = { ...form };
        
        // 保存客户基本信息
        const saveMethod = isUpdate.value ? CustomerApi.edit : CustomerApi.add;
        const result = await saveMethod(submitData);
        
        // 如果是新增，需要从返回结果中获取客户ID
        const customerId = isUpdate.value ? form.customerId : result.data;
        
        // 更新客户关联的区域
        if (customerId) {
          await CustomerApi.updateCustomerRegions({
            customerId: customerId,
            regionIds: form.regionIds || []
          });
        }
        
        message.success('保存成功');
        updateVisible(false);
        emit('done');
      } catch (e) {
        console.error('保存客户失败:', e);
        message.error(e.message || '保存失败');
      } finally {
        loading.value = false;
      }
    };

    // 回显数据
    watch(
      () => props.visible,
      async (visible) => {
        if (visible) {
          if (props.data?.customerId) {
            isUpdate.value = true;
            Object.assign(form, props.data);
            
            // 如果是编辑模式，需要获取客户关联的区域数据
            try {
              loading.value = true;
              const result = await CustomerApi.getCustomerRegions({ customerId: props.data.customerId });
              if (result && Array.isArray(result)) {
                // 将区域ID数组设置到表单中
                form.regionIds = result.map(item => item.regionId);
              } else {
                form.regionIds = [];
              }
            } catch (error) {
              console.error('获取客户关联区域失败:', error);
              message.error('获取客户关联区域失败');
              form.regionIds = [];
            } finally {
              loading.value = false;
            }
          } else {
            isUpdate.value = false;
            Object.keys(form).forEach((key) => {
              form[key] = undefined;
            });
            // 设置默认值
            form.customerType = 'ENTERPRISE';
            form.customerLevel = 'BRONZE';
            form.status = 'ACTIVE';
            form.paymentTerms = 30;
            form.creditLimit = 0;
            form.regionIds = [];

            // 重置 RegionSelector 组件状态
            if (regionSelectorRef.value && regionSelectorRef.value.resetState) {
              regionSelectorRef.value.resetState();
            }
          }
        }
      },
      { immediate: true }
    );

    // 区域选择变化处理
    const handleRegionChange = (value, regions) => {
      console.log('客户区域选择变化:', value, regions);
      // 更新表单中的区域数据
      form.regionIds = value;
    };

    return {
      form,
      formRef,
      regionSelectorRef,
      loading,
      isUpdate,
      customerTypeOptions,
      customerLevelOptions,
      statusOptions,
      rules,
      updateVisible,
      save,
      handleRegionChange
    };
  }
};
</script>
