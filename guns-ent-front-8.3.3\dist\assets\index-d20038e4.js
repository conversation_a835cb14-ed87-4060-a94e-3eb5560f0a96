import{_ as j,r as n,bh as $,bi as C,L as J,o as R,bj as T,X,k as f,a as h,f as b,w as v,b as p,d,t as z,h as F,g as q,m as G,B as H,n as K,bk as Q,c as W,a0 as Y}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{F as Z,a as V}from"./FileApi-418f4d35.js";const ee={class:"ant-upload-list-item ant-upload-list-item-done ant-upload-list-item-list-type-text"},ae={class:"ant-upload-list-item-info"},le={class:"ant-upload-span"},te={class:"ant-upload-text-icon"},ne={class:"ant-upload-list-item-name"},oe={class:"ant-upload-list-item-card-actions"},se={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},isDesgin:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(I,{emit:r}){const a=I,m=r,o=n([]),y=n("".concat($).concat(Z,"?secretFlag=N")),k=n({Authorization:C()}),l=n([]),u=n(!1),s=J(()=>{var e;return!!((e=a.record)!=null&&e.itemMultipleChoiceFlag||a.multiple)});R(()=>{g(),x()});const g=()=>{a.value?s.value?l.value=a.normal?a.value:JSON.parse(a.value):l.value=[a.value]:l.value=[]},x=()=>{var e;u.value||((e=l.value)==null?void 0:e.length)!=0&&(V.getAntdVInfoBatch({fileIdList:l.value}).then(t=>{t.data.forEach((i,_)=>{i.fileId=l.value[_]}),o.value=T(t.data)}),u.value=!1)},N=e=>{e.file.status==="done"&&(e.file.fileId=e.file.response.data.fileId,G.success("".concat(e.file.name," \u56FE\u7247\u4E0A\u4F20\u6210\u529F")),s.value?l.value.push(e.file.response.data.fileId):l.value=[e.file.response.data.fileId],u.value=!0,w())},w=()=>{var t;let e;s.value?e=a.normal?l.value:JSON.stringify(l.value):e=(t=l.value[0])!=null?t:"",m("update:value",e),m("onChange",a.record)},U=e=>{let t="".concat($,"/documentPreview?fileId=").concat(e.fileId,"&token=").concat(C());window.open(t,"_blank")},A=e=>{V.download({token:C(),fileId:e.fileId})},D=e=>{let t=e.fileId;if(t){let i=o.value.findIndex(_=>_.fileId===t);i!==-1&&(o.value.splice(i,1),l.value.splice(i,1),u.value=!0,w())}};return X(()=>a.value,e=>{g(),x()},{deep:!0}),(e,t)=>{const i=f("upload-outlined"),_=H,L=f("paper-clip-outlined"),O=f("fund-view-outlined"),S=f("cloud-download-outlined"),E=f("delete-outlined"),M=K,P=Q;return h(),b(P,{name:"file",multiple:s.value,action:y.value,headers:k.value,"list-type":"file",maxCount:s.value?1e7:1,disabled:a.readonly||a.disabled,"file-list":o.value,"onUpdate:fileList":t[0]||(t[0]=c=>o.value=c),onChange:N},{itemRender:v(({file:c})=>[p("div",ee,[p("div",ae,[p("span",le,[p("div",te,[d(L,{class:"actions-icon"})]),p("a",ne,z(c.name),1),p("span",oe,[d(M,null,{default:v(()=>[d(O,{class:"actions-icon",title:"\u9884\u89C8",onClick:B=>U(c)},null,8,["onClick"]),d(S,{class:"actions-icon",title:"\u4E0B\u8F7D",onClick:B=>A(c)},null,8,["onClick"]),a.isDesgin||!(a.readonly||a.disabled)?(h(),b(E,{key:0,onClick:B=>D(c),title:"\u5220\u9664",class:"actions-icon"},null,8,["onClick"])):F("",!0)]),_:2},1024)])])])])]),default:v(()=>[a.isDesgin||!(a.readonly||a.disabled)?(h(),b(_,{key:0,type:"primary"},{default:v(()=>[d(i),t[1]||(t[1]=q(" \u4E0A\u4F20\u9644\u4EF6 "))]),_:1,__:[1]})):F("",!0)]),_:1},8,["multiple","action","headers","maxCount","disabled","file-list"])}}},ie=j(se,[["__scopeId","data-v-fd601ba8"]]),de={class:"guns-body guns-body-card"},_e={__name:"index",setup(I){const r=n(""),a=n(!1),m=n(!1),o=n("\u8BF7\u9009\u62E9"),y=()=>{console.log(r.value)};return(k,l)=>{const u=ie,s=Y;return h(),W("div",de,[d(s,{title:"\u4E0A\u4F20\u6587\u4EF6",bordered:!1},{default:v(()=>[d(u,{value:r.value,"onUpdate:value":l[0]||(l[0]=g=>r.value=g),disabled:a.value,readonly:m.value,onOnChange:y,placeholder:o.value},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{_e as default};
