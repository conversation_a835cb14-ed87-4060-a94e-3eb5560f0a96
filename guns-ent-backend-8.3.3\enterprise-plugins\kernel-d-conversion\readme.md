# office转化模块，doc转pdf等

## 转化工具来源于互联网破解版，仅供学习参考使用

## 使用方法：

1. 执行mvn install命令将核心包install到本地maven仓库

aspose的包在`_files/readme.md`用百度云盘下载。

将破解好的aspose放到`_files`目录后，在`_files`目录下执行如下命令：

```shell
mvn install:install-file -Dfile=.\aspose-cells-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-cells -Dversion=23.3 -Dpackaging=jar
mvn install:install-file -Dfile=.\aspose-pdf-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-pdf -Dversion=23.3 -Dpackaging=jar
mvn install:install-file -Dfile=.\aspose-slides-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-slides -Dversion=23.3 -Dpackaging=jar
mvn install:install-file -Dfile=.\aspose-words-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-words -Dversion=23.3 -Dpackaging=jar
```

推送到远程仓库参考如下：

```shell
mvn deploy:deploy-file -Dfile=.\aspose-cells-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-cells -Dversion=23.3 -Dpackaging=jar -DrepositoryId=company-hosted -Durl=http://***********:8081/repository/company-hosted/
mvn deploy:deploy-file -Dfile=.\aspose-pdf-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-pdf -Dversion=23.3 -Dpackaging=jar -DrepositoryId=company-hosted -Durl=http://***********:8081/repository/company-hosted/
mvn deploy:deploy-file -Dfile=.\aspose-slides-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-slides -Dversion=23.3 -Dpackaging=jar -DrepositoryId=company-hosted -Durl=http://***********:8081/repository/company-hosted/
mvn deploy:deploy-file -Dfile=.\aspose-words-23.3.jar -DgroupId=my.aspose -DartifactId=aspose-words -Dversion=23.3 -Dpackaging=jar -DrepositoryId=company-hosted -Durl=http://***********:8081/repository/company-hosted/
```

2. 在Guns项目中引用此插件

```xml
  <dependency>
    <groupId>cn.stylefeng.roses</groupId>
    <artifactId>conversion-business</artifactId>
    <version>${roses.version}</version>
</dependency>
```

3. 调用各种conversion-business的util即可使用，如下

```java
public static void main(String[] args) throws IOException {
    DocUtil.toImage("D:\\tmp\\1.docx", "D:\\tmp\\1.jpg");
    DocUtil.toPdf("D:\\tmp\\1.docx", "D:\\tmp\\1.pdf");
}
```

## 常见问题：

### linux部署程序，转化文档乱码

这是因为linux系统下没安装中文字体。

1、开启 root 权限

输入指令： su root 不行时，需要先输入指令： sudo passwd root

根据步骤输入密码，然后再重新输入指令： su root

2、将字体文件先放到某一个文件夹下

然后进入文件夹输入指令： cp simsun.ttc /usr/share/fonts/ ，依次输入下面的指令：

mkfontscale //字体扩展

mkfontdir //新增字体目录

fc-cache-fv //刷新缓存

3、查看安装的字体文件

查看系统中的字体：

指令：fc-list

查看系统中的中文字体：

指令：fc-list：lang=zh

### jdk17 项目启动，使用spire office报错

jdk 17项目有些功能报错了 cannot access class sun.security.action.GetPropertyAction (in module java.base)

java启动项目要带如下参数：

```
java --add-exports java.base/sun.security.action=ALL-UNNAMED -jar xxx.jar
```
