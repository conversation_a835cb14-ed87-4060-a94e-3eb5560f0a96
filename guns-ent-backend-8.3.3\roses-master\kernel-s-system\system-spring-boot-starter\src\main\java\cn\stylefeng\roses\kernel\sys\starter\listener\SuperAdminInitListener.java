/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.sys.starter.listener;

import cn.stylefeng.roses.kernel.rule.listener.ApplicationReadyListener;
import cn.stylefeng.roses.kernel.sys.api.constants.SysConstants;
import cn.stylefeng.roses.kernel.sys.starter.init.InitAdminService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * 项目启动后初始化超级管理员
 *
 * <AUTHOR>
 * @since 2020/12/17 21:44
 */
@Component
@Slf4j
public class SuperAdminInitListener extends ApplicationReadyListener implements Ordered {

    @Resource
    private InitAdminService initAdminService;

    @Override
    public void eventCallback(ApplicationStartedEvent event) {
        long startTime = System.currentTimeMillis();
        initAdminService.initSuperAdmin();
        log.info("初始化超级管理员权限完成，耗时：{}ms", (System.currentTimeMillis() - startTime));
    }

    @Override
    public int getOrder() {
        return SysConstants.SUPER_ADMIN_INIT_LISTENER_SORT;
    }

}
