System.register(["./index-legacy-ee1db0c7.js","./index-legacy-510bfbb8.js","./index-legacy-45c79de7.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./SupplierSelector-legacy-fba3813b.js","./PurchaseApi-legacy-77810512.js","./InboundDetailTable-legacy-c1973558.js","./ProductSelectorModal-legacy-749b3af7.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-94a6fc23.js","./ProductApi-legacy-33feae42.js","./productCategoryApi-legacy-247b2407.js"],(function(e,a){"use strict";var t,l,i,r,d,u,n,o,s,c,p,f,m,g,v,y,h,b,D,_,C,x,P,S,L,I,j,k,w,Y;return{setters:[e=>{t=e._,l=e.P,i=e.r,r=e.s,d=e.ag,u=e.L,n=e.X,o=e.k,s=e.a,c=e.f,p=e.w,f=e.d,m=e.g,g=e.t,v=e.m,y=e.B,h=e.l,b=e.u,D=e.v,_=e.al,C=e.G,x=e.$,P=e.a0,S=e.a4,L=e.H,I=e.M},null,null,null,null,e=>{j=e._},e=>{k=e.P},e=>{w=e.default},e=>{Y=e.default},null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".ant-card[data-v-0ca7e8a4]{border-radius:6px}.ant-card-head-title[data-v-0ca7e8a4]{font-weight:500}.ant-statistic[data-v-0ca7e8a4]{text-align:center}.ant-statistic-title[data-v-0ca7e8a4]{color:#8c8c8c;font-size:14px}.ant-statistic-content[data-v-0ca7e8a4]{color:#262626;font-size:20px;font-weight:500}\n",document.head.appendChild(a);const M={name:"InboundForm",components:{PlusOutlined:l,SupplierSelector:j,InboundDetailTable:w,ProductSelectorModal:Y},props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:a}){const t=i(null),l=i(!1),o=i(!1),s=r({id:null,orderNo:"",supplierId:null,orderDate:d().format("YYYY-MM-DD"),remark:"",detailList:[]}),c=u((()=>!(!e.data||!e.data.id))),p=u((()=>s.detailList?s.detailList.length:0)),f=u((()=>s.detailList&&0!==s.detailList.length?s.detailList.reduce(((e,a)=>e+(parseFloat(a.quantity)||0)),0):0)),m=u((()=>s.detailList&&0!==s.detailList.length?s.detailList.reduce(((e,a)=>e+(parseFloat(a.quantity)||0)*(parseFloat(a.unitPrice)||0)),0):0)),g=u((()=>0===f.value?0:m.value/f.value));n((()=>e.data),(e=>{e&&Object.keys(e).length>0&&Object.assign(s,{id:e.id,orderNo:e.orderNo,supplierId:e.supplierId,orderDate:e.orderDate,remark:e.remark,detailList:e.detailList||[]})}),{immediate:!0}),n((()=>e.visible),(async e=>{e&&(c.value||(y(),await h()))}));const y=()=>{Object.assign(s,{id:null,orderNo:"",supplierId:null,orderDate:d().format("YYYY-MM-DD"),remark:"",detailList:[]}),t.value&&t.value.clearValidate()},h=async()=>{try{const e=await k.generateOrderNo();e.success&&(s.orderNo=e.data)}catch(e){console.error("生成入库单号失败:",e)}};return{formRef:t,loading:l,showProductSelector:o,formData:s,rules:{supplierId:[{required:!0,message:"请选择供应商",trigger:"change"}],orderDate:[{required:!0,message:"请选择订单日期",trigger:"change"}]},isEdit:c,productCount:p,totalQuantity:f,totalAmount:m,averagePrice:g,onSupplierChange:e=>{s.detailList=[]},onDetailChange:e=>{s.detailList=e},addProduct:()=>{s.supplierId?o.value=!0:v.warning("请先选择供应商")},onProductSelect:e=>{e.forEach((e=>{s.detailList.find((a=>a.productId===e.productId))||s.detailList.push({productId:e.productId,productCode:e.productCode,productName:e.productName,specification:e.specification,unit:e.unit,pricingType:e.pricingType,quantity:1,unitPrice:e.retailPrice||0,totalPrice:e.retailPrice||0,remark:""})})),o.value=!1},handleCancel:()=>{a("update:visible",!1)},handleSubmit:()=>{t.value.validate().then((async()=>{if(s.detailList&&0!==s.detailList.length){l.value=!0;try{const e={...s,totalAmount:m.value};c.value?(await k.edit(e),v.success("更新成功")):(await k.add(e),v.success("保存成功")),a("ok")}catch(e){v.error((c.value?"更新":"保存")+"失败："+(e.message||"未知错误"))}finally{l.value=!1}}else v.warning("请添加商品明细")}))}}}};e("default",t(M,[["render",function(e,a,t,l,i,r){const d=y,u=h,n=b,v=D,k=j,w=_,Y=C,M=x,N=P,A=o("plus-outlined"),E=o("inbound-detail-table"),U=S,q=L,O=o("product-selector-modal"),z=I;return s(),c(z,{visible:t.visible,title:l.isEdit?"编辑入库单":"新建入库单",width:1200,maskClosable:!1,onCancel:l.handleCancel},{footer:p((()=>[f(d,{onClick:l.handleCancel},{default:p((()=>a[6]||(a[6]=[m("取消")]))),_:1,__:[6]},8,["onClick"]),f(d,{type:"primary",loading:l.loading,onClick:l.handleSubmit},{default:p((()=>[m(g(l.isEdit?"更新":"保存"),1)])),_:1},8,["loading","onClick"])])),default:p((()=>[f(q,{ref:"formRef",model:l.formData,rules:l.rules,layout:"vertical"},{default:p((()=>[f(N,{title:"基本信息",size:"small",style:{"margin-bottom":"16px"}},{default:p((()=>[f(Y,{gutter:16},{default:p((()=>[f(v,{span:8},{default:p((()=>[f(n,{label:"入库单号",name:"orderNo"},{default:p((()=>[f(u,{value:l.formData.orderNo,"onUpdate:value":a[0]||(a[0]=e=>l.formData.orderNo=e),placeholder:"系统自动生成",disabled:""},null,8,["value"])])),_:1})])),_:1}),f(v,{span:8},{default:p((()=>[f(n,{label:"供应商",name:"supplierId",required:""},{default:p((()=>[f(k,{value:l.formData.supplierId,"onUpdate:value":a[1]||(a[1]=e=>l.formData.supplierId=e),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},onChange:l.onSupplierChange},null,8,["value","onChange"])])),_:1})])),_:1}),f(v,{span:8},{default:p((()=>[f(n,{label:"订单日期",name:"orderDate",required:""},{default:p((()=>[f(w,{value:l.formData.orderDate,"onUpdate:value":a[2]||(a[2]=e=>l.formData.orderDate=e),style:{width:"100%"},format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD"},null,8,["value"])])),_:1})])),_:1})])),_:1}),f(Y,{gutter:16},{default:p((()=>[f(v,{span:24},{default:p((()=>[f(n,{label:"备注",name:"remark"},{default:p((()=>[f(M,{value:l.formData.remark,"onUpdate:value":a[3]||(a[3]=e=>l.formData.remark=e),placeholder:"请输入备注信息",rows:3,maxlength:500,showCount:""},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1}),f(N,{title:"商品明细",size:"small",style:{"margin-bottom":"16px"}},{extra:p((()=>[f(d,{type:"primary",size:"small",onClick:l.addProduct,disabled:!l.formData.supplierId},{default:p((()=>[f(A),a[7]||(a[7]=m(" 添加商品 "))])),_:1,__:[7]},8,["onClick","disabled"])])),default:p((()=>[f(E,{value:l.formData.detailList,"onUpdate:value":a[4]||(a[4]=e=>l.formData.detailList=e),"supplier-id":l.formData.supplierId,onChange:l.onDetailChange},null,8,["value","supplier-id","onChange"])])),_:1}),f(N,{title:"汇总信息",size:"small"},{default:p((()=>[f(Y,{gutter:16},{default:p((()=>[f(v,{span:6},{default:p((()=>[f(U,{title:"商品种类",value:l.productCount,suffix:"种"},null,8,["value"])])),_:1}),f(v,{span:6},{default:p((()=>[f(U,{title:"总数量",value:l.totalQuantity,suffix:"件"},null,8,["value"])])),_:1}),f(v,{span:6},{default:p((()=>[f(U,{title:"总金额",value:l.totalAmount,prefix:"¥",precision:2},null,8,["value"])])),_:1}),f(v,{span:6},{default:p((()=>[f(U,{title:"平均单价",value:l.averagePrice,prefix:"¥",precision:2},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]),f(O,{visible:l.showProductSelector,"onUpdate:visible":a[5]||(a[5]=e=>l.showProductSelector=e),"supplier-id":l.formData.supplierId,onSelect:l.onProductSelect},null,8,["visible","supplier-id","onSelect"])])),_:1},8,["visible","title","onCancel"])}],["__scopeId","data-v-0ca7e8a4"]]))}}}));
