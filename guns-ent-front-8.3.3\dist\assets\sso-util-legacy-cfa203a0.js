System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var o,n,c;return{setters:[t=>{o=t.c8,n=t.c9,c=t.R}],execute:function(){t("S",class{static redirectDetection(){window.location.href=`${o}/sso/detection?clientId=${n}&ssoCallback=${encodeURIComponent(window.location.href)}`}static activateByLoginCode(t,e,n){window.location.href=`${o}/sso/activateByLoginCode?clientId=${t}&ssoCallback=${e}&ssoLoginCode=${n}`}static tokenExchange(t){return c.postAndLoadData("/loginByCaToken",{token:t})}static getUrlParam(t){let e=`(^|&)${t}=([^&]*)(&|$)`,o=window.location.search.substr(1).match(e);return null!=o?unescape(o[2]):null}static ssoLogoutRedirect(){window.location.href=`${o}/sso/logout?clientId=${n}`}})}}}));
