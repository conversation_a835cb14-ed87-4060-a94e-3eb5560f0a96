package cn.stylefeng.roses.kernel.ca.server.core.threadlocal;

import cn.stylefeng.roses.kernel.rule.threadlocal.RemoveThreadLocalApi;
import org.springframework.stereotype.Component;

/**
 * 清除CaClientId的临时缓存
 *
 * <AUTHOR>
 * @since 2023/11/5 15:19
 */
@Component
public class RemoveCaClientIdHolder implements RemoveThreadLocalApi {

    @Override
    public void removeThreadLocalAction() {
        CaClientHolder.remove();
    }

}
