<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>com.javaguns</groupId>
        <artifactId>javaguns-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.javaguns.ent</groupId>
    <artifactId>enterprise-plugins</artifactId>
    <version>8.3.3</version>

    <packaging>pom</packaging>

    <name>enterprise-plugins</name>
    <description>企业版全插件</description>

    <modules>
        <module>kernel-d-api-auth</module>
        <module>kernel-d-conversion</module>
        <module>kernel-d-license</module>
        <module>kernel-d-micro</module>
        <module>kernel-d-oauth2</module>
        <module>kernel-d-pay</module>
        <module>kernel-d-saas</module>
        <module>kernel-d-sanyuan</module>
        <module>kernel-d-sharding-jdbc</module>
        <module>kernel-d-sso</module>
        <module>kernel-d-temp-secret</module>
        <module>kernel-d-websocket</module>
        <module>kernel-s-ent-integration</module>
        <module>kernel-s-mobile</module>
        <module>kernel-s-user-expand</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <roses.version>8.3.3</roses.version>
        <guns.ent.version>8.3.3</guns.ent.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <sharding.version>4.1.1</sharding.version>
        <spring.boot.admin>3.2.3</spring.boot.admin>
        <seata.version>1.4.2</seata.version>
        <jtds.version>1.3.1</jtds.version>
        <pgsql.version>42.2.5</pgsql.version>
        <oracle.version>11.2.0.3</oracle.version>
        <dm.driver.version>8.1.1.49</dm.driver.version>
        <just.auth.version>1.16.5</just.auth.version>
        <zxing.version>3.5.3</zxing.version>
        <aspose.version>23.3</aspose.version>
    </properties>

    <dependencies>

        <!-- 开发规则 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>kernel-a-rule</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <release>17</release>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/webapp</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <developers>
        <developer>
            <name>fengshuonan</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <distributionManagement>
        <snapshotRepository>
            <id>company-hosted</id>
            <url>http://************:8081/repository/company-hosted</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </snapshotRepository>
        <repository>
            <id>company-hosted</id>
            <url>http://************:8081/repository/company-hosted</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </distributionManagement>

</project>
