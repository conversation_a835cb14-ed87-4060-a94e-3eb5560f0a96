System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js"],(function(e,l){"use strict";var a,t,r,u,d,f,i,o,n,s,m,p,g,c,_,v,y;return{setters:[e=>{a=e.s,t=e.L,r=e.a,u=e.f,d=e.w,f=e.d,i=e.g,o=e.h,n=e.l,s=e.u,m=e.v,p=e.W,g=e.J,c=e.y,_=e.$,v=e.G,y=e.H},null],execute:function(){e("default",{__name:"attr-form",props:{form:Object,isUpdate:Boolean},setup(e){const l=e,b=a({fieldName:[{required:!0,message:"请输入属性名称",type:"string",trigger:"blur"}],fieldCode:[{required:!0,message:"请输入属性编码",type:"string",trigger:"blur"}],fieldType:[{required:!0,message:"请输入属性类型",type:"string",trigger:"blur"}],fieldRequired:[{required:!0,message:"请选择是否必填",type:"string",trigger:"blur"}],fieldLength:[{message:"请输入属性长度",type:"number",trigger:"blur"}],fieldDescription:[{message:"请输入属性描述",type:"string",trigger:"blur"}]}),h=t((()=>"file"!==l.form.fieldType));return(l,a)=>{const t=n,w=s,q=m,U=p,R=g,C=c,L=_,N=v,T=y;return r(),u(T,{ref:"formRef",model:e.form,rules:b,layout:"vertical"},{default:d((()=>[f(N,{gutter:20},{default:d((()=>[f(q,{span:24},{default:d((()=>[f(w,{label:"属性名称:",name:"fieldName"},{default:d((()=>[f(t,{value:e.form.fieldName,"onUpdate:value":a[0]||(a[0]=l=>e.form.fieldName=l),placeholder:"请输入属性名称","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),f(q,{span:24},{default:d((()=>[f(w,{label:"属性编码:",name:"fieldCode"},{default:d((()=>[f(t,{value:e.form.fieldCode,"onUpdate:value":a[1]||(a[1]=l=>e.form.fieldCode=l),placeholder:"请输入属性编码","allow-clear":"",autocomplete:"off",disabled:e.isUpdate},null,8,["value","disabled"])])),_:1})])),_:1}),f(q,{span:24},{default:d((()=>[f(w,{label:"属性类型:",name:"fieldType"},{default:d((()=>[f(R,{value:e.form.fieldType,"onUpdate:value":a[2]||(a[2]=l=>e.form.fieldType=l),placeholder:"请选择属性类型","allow-clear":"",autocomplete:"off"},{default:d((()=>[f(U,{value:"string"},{default:d((()=>a[6]||(a[6]=[i("字符类型")]))),_:1,__:[6]}),f(U,{value:"file"},{default:d((()=>a[7]||(a[7]=[i("文件类型")]))),_:1,__:[7]})])),_:1},8,["value"])])),_:1})])),_:1}),f(q,{span:24},{default:d((()=>[f(w,{label:"是否必填",name:"fieldRequired"},{default:d((()=>[f(R,{value:e.form.fieldRequired,"onUpdate:value":a[3]||(a[3]=l=>e.form.fieldRequired=l),placeholder:"请选择是否必填","allow-clear":"",autocomplete:"off"},{default:d((()=>[f(U,{value:"Y"},{default:d((()=>a[8]||(a[8]=[i("必填")]))),_:1,__:[8]}),f(U,{value:"N"},{default:d((()=>a[9]||(a[9]=[i("非必填")]))),_:1,__:[9]})])),_:1},8,["value"])])),_:1})])),_:1}),f(q,{span:24},{default:d((()=>[h.value?(r(),u(w,{key:0,label:"属性长度",name:"fieldLength"},{default:d((()=>[f(C,{value:e.form.fieldLength,"onUpdate:value":a[4]||(a[4]=l=>e.form.fieldLength=l),style:{width:"100%"},palceholder:"请输入属性长度","allow-clear":"",autocomplete:""},null,8,["value"])])),_:1})):o("",!0)])),_:1}),f(q,{span:24},{default:d((()=>[f(w,{label:"属性描述",name:"fieldDescription"},{default:d((()=>[f(L,{value:e.form.fieldDescription,"onUpdate:value":a[5]||(a[5]=l=>e.form.fieldDescription=l),placeholder:"请输入属性描述","auto-size":{minRows:3,maxRows:5}},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
