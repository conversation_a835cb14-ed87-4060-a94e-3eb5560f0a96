package cn.stylefeng.roses.kernel.pay.api.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品信息实例类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@TableName("shop_goods")
@Data
@EqualsAndHashCode(callSuper = true)
public class Goods extends BaseEntity {

    /**
     * 商品id
     */
    @TableId(value = "goods_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("商品id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    @ChineseDescription("商品名称")
    private String goodsName;

    /**
     * 商品简介
     */
    @TableField("goods_desc")
    @ChineseDescription("商品简介")
    private String goodsDesc;

    /**
     * 商品封面图片，url全路径
     */
    @TableField("goods_pic")
    @ChineseDescription("商品封面图片，url全路径")
    private String goodsPic;

    /**
     * 详情介绍的url，url全路径
     */
    @TableField("detail_url")
    @ChineseDescription("详情介绍的url，url全路径")
    private String detailUrl;

    /**
     * 商品标价
     */
    @TableField("price")
    @ChineseDescription("商品标价")
    private BigDecimal price;

    /**
     * 商品原价
     */
    @TableField("original_price")
    @ChineseDescription("商品原价")
    private BigDecimal originalPrice;

    /**
     * 商品有效月数，-1为永久
     */
    @TableField("expiry_month")
    @ChineseDescription("商品有效月数，-1为永久")
    private Integer expiryMonth;

    /**
     * 状态：1-发布，2-下架
     */
    @TableField("goods_status")
    @ChineseDescription("状态：1-发布，2-下架")
    private Integer goodsStatus;

    /**
     * 排序号
     */
    @TableField("fld_sort")
    @ChineseDescription("排序号")
    private BigDecimal fldSort;

    /**
     * 是否删除：Y-删除，N-未删除
     */
    @TableField("del_flag")
    @ChineseDescription("是否删除：Y-删除，N-未删除")
    private String delFlag;

}
