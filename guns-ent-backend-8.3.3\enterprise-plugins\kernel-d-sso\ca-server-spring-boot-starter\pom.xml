<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-sso</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ca-server-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--sso服务端sdk-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>ca-server-business</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--默认使用redis-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

        <!--groovy的自动配置-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>groovy-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>
    </dependencies>

</project>
