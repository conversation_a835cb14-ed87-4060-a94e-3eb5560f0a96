CREATE TABLE `oauth_user_info`  (
  `oauth_id` bigint NOT NULL COMMENT '主键id',
  `user_id` bigint NOT NULL COMMENT '用户主键id',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `blog` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户网址',
  `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在公司',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '位置',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户备注（各平台中的用户个人介绍）',
  `gender` int NULL DEFAULT NULL COMMENT '性别，1-男，0-女',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户来源',
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户授权的token',
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方平台的用户唯一di',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建用户',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '更新用户',
  PRIMARY KEY (`oauth_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方用户信息表' ROW_FORMAT = Dynamic;

UPDATE `sys_config` SET `config_value` = concat(config_value, '/oauth2/detection/*,/oauth2/callback/*,/oauth2/avatar,') WHERE `config_code` = 'SYS_NONE_SECURITY_URLS';

INSERT INTO `sys_hr_organization`(`org_id`, `org_parent_id`, `org_pids`, `org_name`, `org_short_name`, `org_code`, `org_sort`, `status_flag`, `org_type`, `tax_no`, `remark`, `org_level`, `master_org_id`, `master_org_parent_id`, `expand_field`, `version_flag`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`, `tenant_id`) VALUES (1692777932217495554, -1, '[-1],', 'OAuth2默认机构', NULL, 'default_org', 1000.00, 1, 1, NULL, 'OAuth2默认机构', NULL, NULL, NULL, NULL, 0, 'N', '2023-08-19 13:58:04', 1339550467939639299, NULL, NULL, NULL);