import{R as t}from"./index-18a1ea24.js";class s{static add(e){return t.post("/erp/supplier/add",e)}static delete(e){return t.post("/erp/supplier/delete",e)}static batchDelete(e){return t.post("/erp/supplier/batchDelete",e)}static edit(e){return t.post("/erp/supplier/edit",e)}static detail(e){return t.getAndLoadData("/erp/supplier/detail",e)}static findPage(e){return t.getAndLoadData("/erp/supplier/page",e)}static findList(e){return t.getAndLoadData("/erp/supplier/list",e)}static updateStatus(e){return t.post("/erp/supplier/updateStatus",e)}static validateCode(e){return t.getAndLoadData("/erp/supplier/validateCode",e)}static getSupplierRegions(e){return t.getAndLoadData("/erp/supplierRegion/getSupplierRegions",e)}static updateSupplierRegions(e){return t.post("/erp/supplierRegion/updateSupplierRegions",e)}static findSuppliersByRegion(e){return t.getAndLoadData("/erp/supplierRegion/findSuppliersByRegion",e)}static getSupplierTypeOptions(){return[{label:"\u4F01\u4E1A",value:"ENTERPRISE"},{label:"\u4E2A\u4F53",value:"INDIVIDUAL"}]}static getBusinessModeOptions(){return[{label:"\u8D2D\u9500",value:"PURCHASE_SALE"},{label:"\u8054\u8425",value:"JOINT_VENTURE"},{label:"\u4EE3\u9500",value:"CONSIGNMENT"}]}static getSupplierStatusOptions(){return[{label:"\u6B63\u5E38",value:"ACTIVE"},{label:"\u505C\u7528",value:"INACTIVE"},{label:"\u9ED1\u540D\u5355",value:"BLACKLIST"}]}static getCreditLevelOptions(){return[{label:"\u4F18\u79C0",value:"A"},{label:"\u826F\u597D",value:"B"},{label:"\u4E00\u822C",value:"C"},{label:"\u8F83\u5DEE",value:"D"}]}static getSupplierTypeName(e){const a=s.getSupplierTypeOptions().find(r=>r.value===e);return a?a.label:e}static getBusinessModeName(e){const a=s.getBusinessModeOptions().find(r=>r.value===e);return a?a.label:e}static getSupplierStatusName(e){const a=s.getSupplierStatusOptions().find(r=>r.value===e);return a?a.label:e}static getCreditLevelName(e){const a=s.getCreditLevelOptions().find(r=>r.value===e);return a?a.label:e}static getStatusTagColor(e){switch(e){case"ACTIVE":return"green";case"INACTIVE":return"orange";case"BLACKLIST":return"red";default:return"default"}}static getCreditLevelTagColor(e){switch(e){case"A":return"green";case"B":return"blue";case"C":return"orange";case"D":return"red";default:return"default"}}static getBusinessModeTagColor(e){switch(e){case"PURCHASE_SALE":return"blue";case"JOINT_VENTURE":return"green";case"CONSIGNMENT":return"orange";default:return"default"}}static getSupplierProducts(e){return t.getAndLoadData("/erp/supplier/products",e)}static validateBusinessModeChange(e){return t.getAndLoadData("/erp/supplier/validateBusinessModeChange",e)}}export{s as S};
