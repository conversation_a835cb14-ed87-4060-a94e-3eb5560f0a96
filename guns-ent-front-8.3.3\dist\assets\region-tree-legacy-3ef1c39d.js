System.register(["./regionApi-legacy-73888494.js","./UniversalTree-legacy-6dcdf778.js","./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js"],(function(e,o){"use strict";var t,n,i,d,l,a,r,s,c,g,u;return{setters:[e=>{t=e.R},e=>{n=e.U},e=>{i=e._,d=e.r,l=e.L,a=e.a,r=e.f,s=e.M,c=e.d,g=e.E,u=e.m},null],execute:function(){const o=Object.assign({name:"RegionTree"},{__name:"region-tree",props:{isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["treeSelect","addRegion","editRegion","deleteRegion"],setup(e,{expose:o,emit:i}){const h=e,v=i,S=d(),f={api:t.findTree,lazyLoadApi:t.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},p={key:"regionId",title:"regionName",children:"children",hasChildren:"hasChildren",level:"regionLevel"},y=l((()=>({title:"区域管理",showHeader:h.isSetWidth,showSearch:!0,searchPlaceholder:"请输入区域名称，回车搜索",showAddButton:h.isShowEditIcon,showEditIcons:h.isShowEditIcon,showIcon:!1,isSetWidth:h.isSetWidth}))),w={selectable:!0,expandable:!0,lazyLoad:!0,defaultExpandLevel:3,allowMultiSelect:!1},I=l((()=>({allowAdd:h.isShowEditIcon,allowEdit:h.isShowEditIcon,allowDelete:h.isShowEditIcon}))),E=e=>{const{keys:o,nodes:t}=e;v("treeSelect",o,{selectedNodes:t})},m=e=>{},x=e=>{},R=e=>{v("addRegion",e)},T=e=>{v("editRegion",e)},L=e=>{s.confirm({title:"确认删除",content:`确定要删除区域"${e.regionName}"吗？`,icon:c(g),okText:"确定",cancelText:"取消",onOk:()=>t.delete({regionId:e.regionId}).then((()=>{u.success("删除成功"),v("deleteRegion",e),j()})).catch((e=>{console.error("删除区域失败:",e),u.error("删除失败")}))})},N=e=>{},_=e=>{console.error("区域树数据加载失败:",e)},j=()=>{var e;null===(e=S.value)||void 0===e||e.reload()},k=j,A=l((()=>{var e;return(null===(e=S.value)||void 0===e?void 0:e.getSelectedNodes())||[]}));return o({reload:j,reloadRegionTreeData:k,getSelectedNodes:()=>{var e;return null===(e=S.value)||void 0===e?void 0:e.getSelectedNodes()},setSelectedKeys:e=>{var o;null===(o=S.value)||void 0===o||o.setSelectedKeys(e)},currentSelectKeys:A}),(e,o)=>(a(),r(n,{ref_key:"universalTreeRef",ref:S,"data-source":f,"field-mapping":p,"display-config":y.value,"interaction-config":w,"action-config":I.value,onSelect:E,onExpand:m,onSearch:x,onAdd:R,onEdit:T,onDelete:L,onLoad:N,onLoadError:_},null,8,["display-config","action-config"]))}});e("default",i(o,[["__scopeId","data-v-d2d1dbc9"]]))}}}));
