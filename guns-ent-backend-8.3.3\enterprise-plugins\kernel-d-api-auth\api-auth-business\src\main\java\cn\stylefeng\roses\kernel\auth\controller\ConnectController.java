package cn.stylefeng.roses.kernel.auth.controller;

import cn.stylefeng.roses.kernel.apiauth.api.annotations.ApiAuth;
import cn.stylefeng.roses.kernel.auth.pojo.AuthTokenRequest;
import cn.stylefeng.roses.kernel.auth.pojo.AuthTokenResponse;
import cn.stylefeng.roses.kernel.auth.service.AuthTokenService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接入认证的控制器
 *
 * <AUTHOR>
 * @since 2023/10/25 22:14
 */
@RestController
@ApiResource(name = "接入认证的控制器")
public class ConnectController {

    @Resource
    private AuthTokenService authTokenService;

    /**
     * api客户端接入-获取token
     *
     * <AUTHOR>
     * @since 2023/10/25 22:14
     */
    @GetResource(name = "api客户端接入-获取token", path = "/apiAuth/generateAuthToken", requiredLogin = false)
    public ResponseData<AuthTokenResponse> generateAuthToken(AuthTokenRequest authTokenRequest) {
        return new SuccessResponseData<>(authTokenService.generateAuthToken(authTokenRequest));
    }

    /**
     * 测试API认证访问的接口，如果成功会返回一个success
     *
     * <AUTHOR>
     * @since 2023/10/26 16:09
     */
    @PostResource(name = "测试API认证访问的接口", path = "/apiAuth/test", requiredLogin = false)
    @ApiAuth
    public ResponseData<String> test() {
        return new SuccessResponseData<>("test success");
    }

}
