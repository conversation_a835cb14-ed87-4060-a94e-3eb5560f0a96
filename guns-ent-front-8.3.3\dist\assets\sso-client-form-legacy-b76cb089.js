System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-198191c1.js","./print-legacy-bf2789b6.js","./FileApi-legacy-f85a3060.js"],(function(e,l){"use strict";var a,t,r,i,o,u,n,s,p,g,f,d,m,c,v,y,_,b,h,U,x,w,j,L,k,C,F,T,S,I;return{setters:[e=>{a=e.s,t=e.r,r=e.bh,i=e.bi,o=e.k,u=e.a,n=e.f,s=e.w,p=e.d,g=e.h,f=e.g,d=e.b,m=e.m,c=e.bk,v=e.l,y=e.u,_=e.v,b=e.z,h=e.A,U=e.a7,x=e.B,w=e.x,j=e.y,L=e.$,k=e.G,C=e.M,F=e.H},null,null,e=>{T=e.u},e=>{S=e.F,I=e.a}],execute:function(){const l=["src"];e("default",{__name:"sso-client-form",props:{form:Object},setup(e,{expose:q}){const N=e,z=a({clientName:[{required:!0,message:"请输入应用名称",type:"string",trigger:"blur"}],loginPageType:[{required:!0,message:"请输入登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面",type:"number",trigger:"blur"}],unifiedLogoutFlag:[{required:!0,message:"请输入是否统一退出：Y-是，N-否",type:"string",trigger:"blur"}],ssoCallbackUrl:[{required:!0,message:"请输入回调业务地址，单点登录到业务端时，跳转到业务端的地址",type:"string",trigger:"blur"}],customLoginUrl:[{required:!0,message:"请输入应用登录地址",type:"string",trigger:"blur"}],ssoLogoutUrl:[{required:!0,message:"请输入退出地址，从认证中心退出后，通知业务端的地址",type:"string",trigger:"blur"}],caTokenSecret:[{required:!0,message:"请输入加密和解密的密钥，针对单点到业务系统的token（对称加密）",type:"string",trigger:"blur"}]}),D=t(`${r}${S}?secretFlag=N`),P=t({Authorization:i()}),A=t(!1),$=t(null),B=t([]),G=e=>{const l="image/jpeg"===e.type||"image/jpg"===e.type||"image/png"===e.type||"image/tif"===e.type||"image/jfif"===e.type||"image/webp"===e.type||"image/pjp"===e.type||"image/apng"===e.type||"image/pjpeg"===e.type||"image/avif"===e.type||"image/ico"===e.type||"image/tiff"===e.type||"image/bmp"===e.type||"image/xbm"===e.type||"image/jxl"===e.type||"image/svgz"===e.type||"image/gif"===e.type||"image/svg"===e.type;if(!l)return m.error("只能上传图片!"),c.LIST_IGNORE;const a=e.size/1024/1024<5;return a||m.error("图片大小不能超过5MB!"),l&&a},M=async e=>{$.value=e.url||e.preview||e.thumbUrl,A.value=!0},O=e=>{let l=e.response?e.response.data.fileId:e.uid;I.download({token:i(),fileId:l})},R=()=>{N.form.caTokenSecret=T()};return q({clientLogoFileIdFileList:B}),(a,t)=>{const r=v,i=y,T=_,S=o("plus-outlined"),I=c,q=b,Y=h,E=o("question-circle-outlined"),H=U,J=x,K=w,Q=j,V=L,W=k,X=C,Z=F;return u(),n(Z,{ref:"formRef",model:e.form,rules:z,layout:"vertical"},{default:s((()=>[p(W,{gutter:20},{default:s((()=>[p(T,{span:12},{default:s((()=>[p(i,{label:"应用名称:",name:"clientName"},{default:s((()=>[p(r,{value:e.form.clientName,"onUpdate:value":t[0]||(t[0]=l=>e.form.clientName=l),"allow-clear":"",placeholder:"请输入应用名称"},null,8,["value"])])),_:1})])),_:1}),p(T,{span:12},{default:s((()=>[p(i,{label:"应用图标:",name:"clientLogoFileId"},{default:s((()=>[p(I,{name:"file",multiple:!1,action:D.value,"file-list":B.value,"onUpdate:fileList":t[1]||(t[1]=e=>B.value=e),"default-file-list":B.value,maxCount:1,"list-type":"picture-card",headers:P.value,"before-upload":G,accept:".jpeg,.jpg,.png,.tif,.jfif,.webp,.pjp,.apng,.pjpeg,.avif,.ico,.tiff,.bmp,.xbm,.jxl,.jpeg,.svgz,.gif,.svg",onPreview:M,onDownload:O,onChange:t[2]||(t[2]=e=>{return l=e,B.value,a="clientLogoFileId",void("done"===l.file.status?(l.file,m.success(`${l.file.name} 上传成功`),N.form[a]=l.file.response.data.fileId):"error"===l.file.status&&m.error(`${l.file.name} 上传失败`));var l,a}),showUploadList:{showDownloadIcon:!0}},{default:s((()=>[0==B.value.length?(u(),n(S,{key:0,style:{"font-size":"28px","font-weight":"200"}})):g("",!0)])),_:1},8,["action","file-list","default-file-list","headers"])])),_:1})])),_:1}),p(T,{span:12},{default:s((()=>[p(i,{label:"登录地址类型:",name:"loginPageType"},{default:s((()=>[p(Y,{value:e.form.loginPageType,"onUpdate:value":t[3]||(t[3]=l=>e.form.loginPageType=l)},{default:s((()=>[p(q,{value:1},{default:s((()=>t[12]||(t[12]=[f("应用自定义登录界面")]))),_:1,__:[12]}),p(q,{value:2},{default:s((()=>t[13]||(t[13]=[f("使用CA服务统一登录界面")]))),_:1,__:[13]})])),_:1},8,["value"])])),_:1})])),_:1}),p(T,{span:12},{default:s((()=>[p(i,{label:"是否统一退出:",name:"unifiedLogoutFlag"},{default:s((()=>[p(Y,{value:e.form.unifiedLogoutFlag,"onUpdate:value":t[4]||(t[4]=l=>e.form.unifiedLogoutFlag=l)},{default:s((()=>[p(q,{value:"Y"},{default:s((()=>t[14]||(t[14]=[f("是")]))),_:1,__:[14]}),p(q,{value:"N"},{default:s((()=>t[15]||(t[15]=[f("否")]))),_:1,__:[15]})])),_:1},8,["value"])])),_:1})])),_:1}),1==e.form.loginPageType?(u(),n(T,{key:0,span:24},{default:s((()=>[p(i,{name:"customLoginUrl"},{label:s((()=>[t[17]||(t[17]=d("span",{style:{"margin-right":"10px"}},"应用登录地址:",-1)),p(H,null,{title:s((()=>t[16]||(t[16]=[f("应用登录的地址，针对自定义登录界面 ")]))),default:s((()=>[p(E,{style:{"margin-top":"2px"}})])),_:1})])),default:s((()=>[p(r,{value:e.form.customLoginUrl,"onUpdate:value":t[5]||(t[5]=l=>e.form.customLoginUrl=l),"allow-clear":"",placeholder:"请输入应用登录地址"},null,8,["value"])])),_:1})])),_:1})):g("",!0),p(T,{span:24},{default:s((()=>[p(i,{name:"ssoCallbackUrl"},{label:s((()=>[t[19]||(t[19]=d("span",{style:{"margin-right":"10px"}},"业务回调地址:",-1)),p(H,null,{title:s((()=>t[18]||(t[18]=[f("单点登录到业务端时，跳转到业务端的地址 ")]))),default:s((()=>[p(E,{style:{"margin-top":"2px"}})])),_:1})])),default:s((()=>[p(r,{value:e.form.ssoCallbackUrl,"onUpdate:value":t[6]||(t[6]=l=>e.form.ssoCallbackUrl=l),"allow-clear":"",placeholder:"请输入回调业务地址"},null,8,["value"])])),_:1})])),_:1}),p(T,{span:24},{default:s((()=>[p(i,{name:"ssoLogoutUrl"},{label:s((()=>[t[21]||(t[21]=d("span",{style:{"margin-right":"10px"}},"退出地址:",-1)),p(H,null,{title:s((()=>t[20]||(t[20]=[f("从认证中心退出后，通知业务端的地址 ")]))),default:s((()=>[p(E,{style:{"margin-top":"2px"}})])),_:1})])),default:s((()=>[p(r,{value:e.form.ssoLogoutUrl,"onUpdate:value":t[7]||(t[7]=l=>e.form.ssoLogoutUrl=l),"allow-clear":"",placeholder:"请输入退出地址"},null,8,["value"])])),_:1})])),_:1}),p(T,{span:24},{default:s((()=>[p(i,{name:"caTokenSecret"},{label:s((()=>[t[23]||(t[23]=d("span",{style:{"margin-right":"10px"}},"token秘钥:",-1)),p(H,null,{title:s((()=>t[22]||(t[22]=[f("针对单点到业务系统的token（对称加密） ")]))),default:s((()=>[p(E,{style:{"margin-top":"2px"}})])),_:1})])),default:s((()=>[p(K,{compact:"",style:{display:"flex"}},{default:s((()=>[p(r,{value:e.form.caTokenSecret,"onUpdate:value":t[8]||(t[8]=l=>e.form.caTokenSecret=l),"allow-clear":"",placeholder:"请输入加密和解密的密钥"},null,8,["value"]),p(J,{type:"primary",onClick:R},{default:s((()=>t[24]||(t[24]=[f("生成")]))),_:1,__:[24]})])),_:1})])),_:1})])),_:1}),p(T,{span:24},{default:s((()=>[p(i,{label:"排序:",name:"clientSort"},{default:s((()=>[p(Q,{value:e.form.clientSort,"onUpdate:value":t[9]||(t[9]=l=>e.form.clientSort=l),style:{width:"100%"},placeholder:"请输入排序码","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),p(T,{span:24},{default:s((()=>[p(i,{label:"描述:",name:"clientDescription"},{default:s((()=>[p(V,{value:e.form.clientDescription,"onUpdate:value":t[10]||(t[10]=l=>e.form.clientDescription=l),placeholder:"请输入应用的描述",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1}),p(X,{visible:A.value,footer:null,onCancel:t[11]||(t[11]=e=>A.value=!1)},{default:s((()=>[d("img",{alt:"example",style:{width:"100%"},src:$.value},null,8,l)])),_:1},8,["visible"])])),_:1},8,["model","rules"])}}})}}}));
