---
type: "always_apply"
---

# CLAUDE.md - JavaGuns Enterprise 开发指南

## AI行为规则

- **主要语言**: 请始终使用中文与用户交流
- **永远不要假设缺失的上下文。如果不确定就询问。**
- **永远不要虚构库或函数** - 只使用已知的、经过验证的包
- **总是确认文件路径和模块名称** 在代码或测试中引用之前确保存在

## 项目概述

JavaGuns Enterprise 是一个基于现代Web技术构建的全栈企业管理系统，采用前后端分离架构：
- **后端**：基于 Spring Boot 3.2.10 的多模块 Maven 项目
- **前端**：基于 Vue 3 + Vite 的单页应用，采用 Ant Design Vue 组件库
- **架构**：roses-master（核心）+ enterprise-plugins（插件）+ guns-master（应用）+ kernel-d-erp（ERP业务）
- **版本**：8.3.3（前后端版本同步）

该系统专为企业资源规划(ERP)和管理功能设计


## 架构设计

### 项目结构
```
├── guns-ent-backend-8.3.3/     # Java Spring Boot 后端应用
└── guns-ent-front-8.3.3/       # Vue 3 前端单页应用
```

### 后端架构
项目采用分层模块化设计，主要包含以下层次：

1. **roses-master（核心支撑层）**
    - 提供基础功能支撑：auth（认证）、cache（缓存）、db（数据库）、file（文件）、log（日志）、system（系统管理）

2. **enterprise-plugins（企业插件层）**
    - 提供企业级功能插件：API认证、OAuth2、多租户（SaaS）、支付、报表、SSO、WebSocket等

3. **guns-master（应用入口）**
    - 主应用启动类：`GunsMasterApplication.java`
    - 配置文件：`application.yml`、`application-local.yml`

4. **kernel-d-erp（ERP业务模块）**
    - ERP相关业务实现，模块化结构：
        - erp-api（接口定义）
        - erp-business（业务实现）
        - erp-spring-boot-starter（自动配置）

**架构特点**：
- **分层架构**: Controller → Service → Repository 经典三层架构
- **模块化设计**: 按业务功能划分独立模块，便于维护和扩展
- **插件化架构**: 企业功能通过插件形式集成

### 前端架构
基于 Vue 3 组合式 API，采用以下结构：

```
src/
├── api/          # 通用API接口定义
│   └── erp/      # ERP API接口统一管理
├── components/   # 通用组件
│   └── erp/      # ERP通用组件
├── layouts/      # 布局组件
├── router/       # 路由配置
├── stores/       # Pinia状态管理
├── utils/        # 工具函数
│   └── erp/      # ERP工具函数
└── views/        # 页面组件
    ├── system/   # 系统管理模块
    └── erp/      # ERP业务模块
```

#### ERP模块目录结构规范

ERP模块采用分层模块化设计，与后端 `kernel-d-erp` 模块保持一致的业务划分：

```
src/
├── api/erp/                   # ERP API接口统一管理
│   ├── index.js              # 统一导出所有ERP API
│   ├── supplier.js           # 各业务模块API接口定义
│   └── [其他模块].js
│
├── components/erp/            # ERP通用组件库
│   ├── index.js              # 统一导出所有ERP组件
│   ├── SupplierSelector.vue  # 跨模块复用的业务组件
│   └── [其他组件].vue
│
├── utils/erp/                 # ERP工具函数库
│   ├── index.js              # 统一导出所有工具函数
│   ├── constants.js          # ERP业务常量定义
│   ├── formatter.js          # 数据格式化工具
│   └── [其他工具].js
│
└── views/erp/                 # ERP页面模块
    ├── supplier/              # 供应商管理模块（示例）
    │   ├── api/              # 模块特定的API逻辑处理
    │   ├── components/       # 模块专用UI组件
    │   └── index.vue         # 模块主页面
    └── [其他业务模块]/        # customer、product、inventory等
```

**业务模块**：customer（客户）、inventory（库存）、product（产品）、productCategory（产品分类）、purchase（采购）、region（区域）、sequence（序列号）、supplier（供应商）

**ERP模块设计原则**：

1. **前后端模块对应**: 前端ERP模块与后端 `kernel-d-erp/erp-business/src/main/java/cn/stylefeng/roses/kernel/erp/modular/` 下的业务模块一一对应
2. **统一目录结构**: 每个ERP业务模块都采用 `api/`、`components/`、`index.vue` 的标准结构
3. **分层API管理**:
    - 全局级：`src/api/erp/` 统一管理所有ERP API接口
    - 模块级：`src/views/erp/[module]/api/` 管理模块特定的API逻辑
4. **组件复用**:
    - 通用组件：`src/components/erp/` 存放跨模块复用的ERP组件
    - 专用组件：各模块 `components/` 目录存放模块特定组件
5. **工具函数分层**: `src/utils/erp/` 提供ERP业务相关的通用工具函数

**架构特点**：
- **组件化开发**: 基于Vue 3 Composition API的组件化架构
- **模块化路由**: Vue Router实现的单页应用路由管理
- **集中状态管理**: Pinia提供的响应式状态管理
- **自动化构建**: Vite提供的快速构建和热更新
- **代码规范**: ESLint + Prettier确保代码质量和风格统一
- **业务模块化**: ERP功能按业务领域划分，便于维护和扩展

### 数据库设计
- 系统管理、角色菜单等数据表：`sql\sys.sql`
- ERP数据表：`sql\erp.sql`
- 多租户、API等数据表：`sql\ent.sql`
- 数据库连接配置在 `application-local.yml`
- 数据库表设计不满足需求时可按照需求自行设计

## 开发规范

### 代码结构与模块化

#### 后端代码规范
- **永远不要创建超过500行代码的文件** 如果文件接近此限制，通过拆分为模块或辅助文件进行重构
- **将代码组织为清晰分离的模块**，按功能或职责分组
- **遵循Spring Boot分层架构**: Controller → Service → Mapper
- **Mapper.xml 文件目录结构规范**：
    - **推荐结构**：`src/main/resources/mapping/[功能模块名]/XxxMapper.xml`
    - **功能模块示例**：productcategoryrelation、supplier、customer 等
    - **MyBatis Plus 配置**：`mapper-locations: classpath*:cn/stylefeng/**/mapping/*.xml,classpath*:mapping/**/*.xml`

#### 前端代码规范
- **永远不要创建超过500行代码的文件** 如果文件接近此限制，通过拆分为模块或辅助文件进行重构
- **组件化开发**: 基于Vue 3 Composition API的组件化架构
- **遵循项目目录结构**: src/api、src/components、src/views、src/stores
- **使用清晰一致的导入** 在包内优先使用相对导入
- 创建测试页面时请在 `guns-ent-front-8.3.3/src/views/extension/` 目录下创建，这样确保页面可以通过系统的扩展组件菜单正常访问，不受权限限制。

##### ERP模块开发规范

**目录结构规范**：
- **API接口管理**:
    - 全局ERP API：`src/api/erp/[模块名].js` - 统一管理所有ERP模块的API接口
    - 模块级API：`src/views/erp/[模块名]/api/` - 处理模块特定的API逻辑和数据处理
- **组件组织**:
    - 通用ERP组件：`src/components/erp/` - 跨模块复用的业务组件
    - 模块专用组件：`src/views/erp/[模块名]/components/` - 模块特定的UI组件
- **工具函数**: `src/utils/erp/` - ERP业务相关的通用工具函数

**命名规范**：
- **API文件**: 使用camelCase命名，如 `supplier.js`、`productCategory.js`
- **组件文件**: 使用PascalCase命名，如 `SupplierSelector.vue`、`ProductForm.vue`
- **模块目录**: 使用camelCase命名，与后端模块名保持一致

**模块对应关系**：
- 前端ERP模块必须与后端 `kernel-d-erp/erp-business/modular/` 下的模块一一对应
- 模块命名保持前后端一致：customer、inventory、product、productCategory、purchase、region、sequence、supplier

**开发约定**：
- **统一的模块结构**: 每个ERP模块都必须包含 `api/`、`components/`、`index.vue` 三个标准目录/文件
- **API接口分层**: 全局API负责接口定义，模块API负责业务逻辑处理
- **组件复用优先**: 优先使用 `src/components/erp/` 中的通用组件，避免重复开发
- **工具函数复用**: 使用 `src/utils/erp/` 中的通用工具函数处理ERP业务逻辑

### 编码约定

#### 后端约定
- **使用Java 17特性** 和Spring Boot 3.2.10最佳实践
- **遵循RESTful API设计** 统一的响应格式
- **使用MyBatis Plus** 进行数据访问
- **中文注释** 为每个类和方法编写中文文档注释

#### 前端约定
- **使用Vue 3 Composition API** 和TypeScript
- **遵循Ant Design Vue** 组件设计规范
- **使用Pinia** 进行状态管理
- **中文注释** 为组件和复杂逻辑添加中文注释

### 测试与可靠性

#### 后端测试
- **总是为新功能创建JUnit单元测试** (函数、类、路由等)
- **更新任何逻辑后**，检查现有单元测试是否需要更新，如需要则更新
- **测试应位于src/test/java目录** 镜像主应用结构
- **至少包含**: 1个预期使用测试、1个边界情况、1个失败情况

#### 前端测试
- **为新组件和功能创建Vitest单元测试**
- **测试应位于对应的__tests__目录或.test.js文件**
- **包含组件渲染测试、用户交互测试、状态管理测试**

### 文档与可解释性
- **更新README.md** 当添加新功能、依赖变更或设置步骤修改时
- **注释非显而易见的代码** 确保中级开发者能够理解
- **编写复杂逻辑时**，添加内联`# 原因:`注释解释为什么，而不仅仅是什么

## 开发指南

### 常用开发命令

#### 后端开发
```bash
# 编译特定模块（推荐）
# erp模块下有需要编译的直接编译kernel-d-erp模块
cd guns-ent-backend-8.3.3/[模块名]
mvn clean compile -DskipTests

# 运行主应用
cd guns-ent-backend-8.3.3/guns-master
mvn spring-boot:run

# 运行测试
mvn test

# 打包应用程序
mvn clean package -DskipTests
```

#### 前端开发
```bash
# 进入前端目录并安装依赖
cd guns-ent-front-8.3.3
yarn install

# 启动开发服务器 (http://localhost:9000)
yarn run dev

# 构建生产版本
yarn run build

# 运行测试和代码检查
yarn run test:unit
yarn run lint
```

### 开发注意事项

1. **API代理配置**：前端开发时，API请求会代理到后端服务，配置在 `vite.config.js` 中
2. **多环境配置**：
    - 后端：通过 Spring profiles 管理（local、dev、prod）
    - 前端：通过 `.env.development` 和 `.env.production` 管理
3. **模块依赖**：新增功能时注意模块间的依赖关系，避免循环依赖
4. **插件启用**：企业版插件通过配置文件控制是否启用，注意检查相关配置
5. **数据库迁移**：数据库变更需要创建 Flyway 迁移脚本，放置在对应模块的 `db/migration` 目录
6. **编译策略**：优先编译特定模块，避免不必要的编译时间

#### ERP模块开发注意事项

7. **前后端模块对应**：新增ERP功能时，确保前端模块与后端 `kernel-d-erp` 模块保持一致的命名和结构
9. **组件复用**：优先使用 `src/components/erp/` 中的通用组件，避免在各模块中重复开发相似功能
10. **ERP数据表**：ERP相关的数据库变更需要更新 `sql/erp.sql` 文件
11. **业务常量**：ERP业务相关的常量定义统一放在 `src/utils/erp/constants.js` 中
12. **路由配置**：ERP模块的路由需要按照业务模块进行分组，便于权限控制和菜单管理

### 端口配置
- 后端API服务: `localhost:8080`
- 前端开发服务器: `localhost:9000`
