package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存历史响应参数
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryHistoryResponse extends BaseResponse {

    /**
     * 历史ID
     */
    @ChineseDescription("历史ID")
    private Long id;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品简称
     */
    @ChineseDescription("商品简称")
    private String productShortName;

    /**
     * 条形码
     */
    @ChineseDescription("条形码")
    private String barcode;

    /**
     * 基本单位
     */
    @ChineseDescription("基本单位")
    private String unit;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 操作类型：IN(入库)、OUT(出库)、ADJUST(调整)、SALE(销售)
     */
    @ChineseDescription("操作类型")
    private String operationType;

    /**
     * 操作类型名称
     */
    @ChineseDescription("操作类型名称")
    private String operationTypeName;

    /**
     * 数量变化
     */
    @ChineseDescription("数量变化")
    private BigDecimal quantityChange;

    /**
     * 操作前库存
     */
    @ChineseDescription("操作前库存")
    private BigDecimal stockBefore;

    /**
     * 操作后库存
     */
    @ChineseDescription("操作后库存")
    private BigDecimal stockAfter;

    /**
     * 关联单据ID
     */
    @ChineseDescription("关联单据ID")
    private Long referenceId;

    /**
     * 关联单据类型：PURCHASE_ORDER(采购入库单)、SALE_ORDER(销售单)、ADJUST_ORDER(调整单)
     */
    @ChineseDescription("关联单据类型")
    private String referenceType;

    /**
     * 关联单据类型名称
     */
    @ChineseDescription("关联单据类型名称")
    private String referenceTypeName;

    /**
     * 关联单据号
     */
    @ChineseDescription("关联单据号")
    private String referenceNo;

    /**
     * 操作时间
     */
    @ChineseDescription("操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作人员ID
     */
    @ChineseDescription("操作人员ID")
    private Long operationUser;



    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

}