package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存历史查询请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryHistoryQueryRequest extends BaseRequest {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 操作类型
     */
    @ChineseDescription("操作类型")
    private String operationType;

    /**
     * 操作类型列表
     */
    @ChineseDescription("操作类型列表")
    private List<String> operationTypeList;

    /**
     * 关联单据类型
     */
    @ChineseDescription("关联单据类型")
    private String referenceType;

    /**
     * 关联单据类型列表
     */
    @ChineseDescription("关联单据类型列表")
    private List<String> referenceTypeList;

    /**
     * 关联单据ID
     */
    @ChineseDescription("关联单据ID")
    private Long referenceId;

    /**
     * 操作开始时间
     */
    @ChineseDescription("操作开始时间")
    private LocalDateTime startTime;

    /**
     * 操作结束时间
     */
    @ChineseDescription("操作结束时间")
    private LocalDateTime endTime;

    /**
     * 操作人员ID
     */
    @ChineseDescription("操作人员ID")
    private Long operationUser;



}