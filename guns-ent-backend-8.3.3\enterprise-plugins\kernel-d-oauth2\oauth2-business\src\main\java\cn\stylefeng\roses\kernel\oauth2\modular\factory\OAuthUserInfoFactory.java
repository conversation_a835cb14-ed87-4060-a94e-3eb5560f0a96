package cn.stylefeng.roses.kernel.oauth2.modular.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.stylefeng.roses.kernel.oauth2.modular.entity.OauthUserInfo;
import me.zhyd.oauth.model.AuthUser;

/**
 * oauth绑定记录
 *
 * <AUTHOR>
 * @date 2019/6/9 19:02
 */
public class OAuthUserInfoFactory {

    /**
     * 创建oauth绑定
     *
     * <AUTHOR>
     * @date 2019/6/9 19:03
     */
    public static OauthUserInfo createOAuthUserInfo(Long userId, AuthUser oauthUser) {
        OauthUserInfo oauthUserInfo = new OauthUserInfo();

        BeanUtil.copyProperties(oauthUser, oauthUserInfo, CopyOptions.create().ignoreError());

        //设置openId和第三方源
        oauthUserInfo.setUuid(oauthUser.getUuid());
        oauthUserInfo.setSource(oauthUser.getSource());

        //设置本系统地用户id
        oauthUserInfo.setUserId(userId);

        return oauthUserInfo;
    }

}
