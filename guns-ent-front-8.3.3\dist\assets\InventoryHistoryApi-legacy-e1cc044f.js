System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.R}],execute:function(){class e{static findPage(t){return r.getAndLoadData("/erp/inventory/history/page",t)}static findList(t){return r.getAndLoadData("/erp/inventory/history/list",t)}static productHistory(t){return r.getAndLoadData("/erp/inventory/history/product",t)}static detail(t){return r.getAndLoadData("/erp/inventory/history/detail",t)}static statistics(t){return r.getAndLoadData("/erp/inventory/history/statistics",t)}static getOperationTypeOptions(){return[{label:"入库",value:"IN"},{label:"出库",value:"OUT"},{label:"调整",value:"ADJUST"},{label:"销售",value:"SALE"},{label:"设置预警",value:"SET_ALERT"}]}static getOperationTypeName(t){const r=e.getOperationTypeOptions().find((e=>e.value===t));return r?r.label:t}static getOperationTypeColor(t){switch(t){case"IN":return"green";case"OUT":return"red";case"ADJUST":return"blue";case"SALE":return"orange";case"SET_ALERT":return"purple";default:return"default"}}static formatQuantityChange(t,e){if(!t&&0!==t)return"0";const r=parseFloat(t)||0,a=Math.abs(r).toFixed(3);switch(e){case"IN":return`+${a}`;case"OUT":case"SALE":return`-${a}`;case"ADJUST":default:return r>=0?`+${a}`:`-${a}`;case"SET_ALERT":return a}}static formatAmount(t){return t?parseFloat(t).toFixed(2):"0.00"}static exportHistory(t){return r.download("/erp/inventory/history/export",t)}}t("I",e)}}}));
