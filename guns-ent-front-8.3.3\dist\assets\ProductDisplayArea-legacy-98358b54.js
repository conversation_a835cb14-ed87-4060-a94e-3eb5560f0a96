System.register(["./index-legacy-ee1db0c7.js"],(function(a,e){"use strict";var t,d,o,r,i,l,c,s,n,p,u,f,v,g,b,x,m,h,y,w,k,_,C,z,j,A,N,I;return{setters:[a=>{t=a._,d=a.r,o=a.L,r=a.a,i=a.c,l=a.b,c=a.d,s=a.w,n=a.F,p=a.e,u=a.f,f=a.at,v=a.ac,g=a.g,b=a.I,x=a.t,m=a.h,h=a.b1,y=a.aM,w=a.m,k=a.j,_=a.T,C=a.l,z=a.W,j=a.J,A=a.B,N=a.a5,I=a.S}],execute:function(){var e=document.createElement("style");e.textContent=".product-display-area[data-v-d6652b4a]{height:100%;display:flex;flex-direction:column;background:#fff;border-radius:8px;overflow:hidden}.category-section[data-v-d6652b4a]{padding:12px 16px;border-bottom:1px solid #f0f0f0;background:#fafafa}.product-filter-section[data-v-d6652b4a]{padding:12px 16px;border-bottom:1px solid #f0f0f0}.filter-row[data-v-d6652b4a]{display:flex;gap:12px;align-items:center}.search-wrapper[data-v-d6652b4a]{flex:1}.search-input[data-v-d6652b4a]{max-width:300px}.filter-select[data-v-d6652b4a]{width:150px}.product-grid-section[data-v-d6652b4a]{flex:1;padding:16px;overflow-y:auto}.grid-container[data-v-d6652b4a]{display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:16px}.product-card[data-v-d6652b4a]{border:1px solid #f0f0f0;border-radius:8px;overflow:hidden;transition:all .3s ease;cursor:pointer}.product-card[data-v-d6652b4a]:hover{border-color:#1890ff;box-shadow:0 4px 12px rgba(24,144,255,.15);transform:translateY(-2px)}.product-image[data-v-d6652b4a]{height:120px;background:#f5f5f5;display:flex;align-items:center;justify-content:center;overflow:hidden}.product-image img[data-v-d6652b4a]{width:100%;height:100%;object-fit:cover}.image-placeholder[data-v-d6652b4a]{color:#bfbfbf;font-size:32px}.product-info[data-v-d6652b4a]{padding:12px}.product-name[data-v-d6652b4a]{font-size:14px;font-weight:500;color:#262626;line-height:1.4;margin-bottom:8px;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.price-info[data-v-d6652b4a]{margin-bottom:8px}.current-price[data-v-d6652b4a]{font-size:16px;font-weight:600;color:#ff4d4f}.stock-info[data-v-d6652b4a]{font-size:12px;color:#8c8c8c}.product-actions[data-v-d6652b4a]{padding:12px;border-top:1px solid #f0f0f0}.add-btn[data-v-d6652b4a]{width:100%;height:32px;font-size:12px}.empty-state[data-v-d6652b4a],.loading-state[data-v-d6652b4a]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:300px;text-align:center}.loading-text[data-v-d6652b4a]{margin-top:16px;font-size:14px;color:#8c8c8c}@media (max-width: 768px){.grid-container[data-v-d6652b4a]{grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:12px}.filter-row[data-v-d6652b4a]{flex-direction:column;gap:8px;align-items:stretch}.search-input[data-v-d6652b4a]{max-width:none}}\n",document.head.appendChild(e);const L={class:"product-display-area"},S={class:"category-section"},E={class:"category-tabs"},K={class:"product-filter-section"},P={class:"filter-row"},U={class:"search-wrapper"},B={class:"filter-item"},D={class:"product-grid-section"},F={class:"grid-container"},T=["onClick"],J={class:"product-card"},M={class:"product-image"},O=["src","alt"],W={key:1,class:"image-placeholder"},Y={class:"product-info"},$=["title"],q={class:"price-info"},G={class:"current-price"},H={key:0,class:"stock-info"},Q={class:"product-actions"},R={key:0,class:"empty-state"},V={key:1,class:"loading-state"},X=Object.assign({name:"ProductDisplayArea"},{__name:"ProductDisplayArea",props:{categories:{type:Array,default:()=>[]},products:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["productAdd","productSelect","categoryChange","search","filterChange"],setup(a,{emit:e}){const t=a,X=e,Z=d("all"),aa=d(""),ea=d("");o((()=>{let a=t.products;if("all"!==Z.value&&(a=a.filter((a=>a.categoryId===Z.value))),aa.value){const e=aa.value.toLowerCase();a=a.filter((a=>a.productName.toLowerCase().includes(e)||a.productCode&&a.productCode.toLowerCase().includes(e)))}return a}));const ta=a=>{Z.value=a,X("categoryChange",a)},da=()=>{X("search",aa.value)},oa=a=>{aa.value=a.target.value,X("search",aa.value)},ra=a=>{X("filterChange",{type:"price",value:a})},ia=a=>{a.target.style.display="none"};return(e,t)=>{const d=k,o=_,la=C,ca=z,sa=j,na=A,pa=N,ua=I;return r(),i("div",L,[l("div",S,[l("div",E,[c(o,{activeKey:Z.value,"onUpdate:activeKey":t[0]||(t[0]=a=>Z.value=a),type:"card",size:"small",onTabClick:ta,class:"pos-category-tabs"},{default:s((()=>[c(d,{key:"all",tab:"全部商品"}),(r(!0),i(n,null,p(a.categories,(a=>(r(),u(d,{key:a.categoryId,tab:a.categoryName},null,8,["tab"])))),128))])),_:1},8,["activeKey"])])]),l("div",K,[l("div",P,[l("div",U,[c(la,{value:aa.value,"onUpdate:value":t[1]||(t[1]=a=>aa.value=a),placeholder:"搜索商品名称、编码、条形码",class:"search-input",onPressEnter:da,onChange:oa,allowClear:""},{prefix:s((()=>[c(f(v))])),_:1},8,["value"])]),l("div",B,[c(sa,{value:ea.value,"onUpdate:value":t[2]||(t[2]=a=>ea.value=a),placeholder:"选择价格范围",class:"filter-select",onChange:ra,allowClear:""},{default:s((()=>[c(ca,{value:"0-50"},{default:s((()=>t[3]||(t[3]=[g("0-50元")]))),_:1,__:[3]}),c(ca,{value:"50-100"},{default:s((()=>t[4]||(t[4]=[g("50-100元")]))),_:1,__:[4]}),c(ca,{value:"100-200"},{default:s((()=>t[5]||(t[5]=[g("100-200元")]))),_:1,__:[5]}),c(ca,{value:"200+"},{default:s((()=>t[6]||(t[6]=[g("200元以上")]))),_:1,__:[6]})])),_:1},8,["value"])])])]),l("div",D,[l("div",F,[(r(!0),i(n,null,p(a.products,(a=>{return r(),i("div",{key:a.productId,class:"product-item",onClick:e=>(a=>{X("productSelect",a)})(a)},[l("div",J,[l("div",M,[a.image?(r(),i("img",{key:0,src:a.image,alt:a.productName,onError:ia},null,40,O)):(r(),i("div",W,[c(b,{iconClass:"icon-product"})]))]),l("div",Y,[l("div",{class:"product-name",title:a.productName},x(a.productName),9,$),l("div",q,[l("div",G," ¥"+x((e=a.price,(e||0).toFixed(2))),1)]),void 0!==a.stock?(r(),i("div",H," 库存: "+x(a.stock),1)):m("",!0)]),l("div",Q,[c(na,{type:"primary",size:"small",disabled:a.stock<=0,onClick:y((e=>(a=>{a.stock<=0?w.warning("商品已缺货"):(X("productAdd",a),w.success(`已添加 ${a.productName} 到购物车`))})(a)),["stop"]),class:"add-btn"},{icon:s((()=>[c(f(h))])),default:s((()=>[t[7]||(t[7]=g(" 加入购物车 "))])),_:2,__:[7]},1032,["disabled","onClick"])])])],8,T);var e})),128))]),a.loading||0!==a.products.length?m("",!0):(r(),i("div",R,[c(pa,{description:"暂无商品"})])),a.loading?(r(),i("div",V,[c(ua,{size:"large"}),t[8]||(t[8]=l("div",{class:"loading-text"},"正在加载商品...",-1))])):m("",!0)])])}}});a("default",t(X,[["__scopeId","data-v-d6652b4a"]]))}}}));
