package cn.stylefeng.roses.ent.saas.modular.manager.factory;

import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantAuditRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;

/**
 * 租户审核的创建工厂
 *
 * <AUTHOR>
 * @since 2024-02-22 19:44
 */
public class TenantAuditFactory {

    /**
     * 创建初始化需要的租户请求参数
     *
     * <AUTHOR>
     * @since 2024-02-22 19:44
     */
    public static TenantRequest createInitTenantRequest(Tenant tenantInfo, TenantAuditRequest tenantAuditRequest) {

        TenantRequest tenantRequest = new TenantRequest();

        // 设置租户名称
        tenantRequest.setTenantName(tenantInfo.getTenantName());

        // 设置邮箱
        tenantRequest.setEmail(tenantInfo.getEmail());

        // 设置logo
        tenantRequest.setTenantLogo(tenantInfo.getTenantLogo());

        // 设置电话
        tenantRequest.setSafePhone(tenantInfo.getSafePhone());

        // 设置公司名称
        tenantRequest.setCompanyName(tenantInfo.getCompanyName());

        // 设置租户编码
        tenantRequest.setTenantCode(tenantInfo.getTenantCode());

        // 设置关联的功能包列表
        tenantRequest.setTenantLinkList(tenantAuditRequest.getTenantLinkList());

        return tenantRequest;
    }

}
