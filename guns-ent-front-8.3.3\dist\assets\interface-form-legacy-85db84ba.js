System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var a,r,o,l,d,u,n,s,c,i,p,f,m,h,v;return{setters:[e=>{a=e.R,r=e._,o=e.s,l=e.r,d=e.a,u=e.f,n=e.w,s=e.d,c=e.b,i=e.J,p=e.u,f=e.v,m=e.l,h=e.G,v=e.H}],execute:function(){var t=document.createElement("style");t.textContent=".card-title[data-v-75419228]{width:100%;border-left:5px solid;border-color:var(--primary-color);padding-left:10px;margin-bottom:20px}\n",document.head.appendChild(t);class _{static findPage(e){return a.getAndLoadData("/apiEndpoint/page",e)}static add(e){return a.post("/apiEndpoint/add",e)}static edit(e){return a.post("/apiEndpoint/edit",e)}static delete(e){return a.post("/apiEndpoint/delete",e)}static batchDelete(e){return a.post("/apiEndpoint/batchDelete",e)}static detail(e){return a.getAndLoadData("/apiEndpoint/detail",e)}static list(e){return a.get("/apiEndpoint/getUrlSelectList",e)}}e("I",_);const b={__name:"interface-form",props:{form:Object,isUpdate:Boolean},setup(e,{expose:t}){const a=e,r=o({resourceCode:[{required:!0,message:"请选择接口路径",type:"string",trigger:"change"}]}),b=l([]),g=e=>{_.list({searchText:e}).then((e=>{b.value=e.data.map((e=>({...e,value:e.resourceCode,label:e.url+" ("+e.resourceName+")"})))}))},C=e=>{if(e){let t=b.value.find((t=>t.resourceCode==e));t&&(a.form.resourceName=t.resourceName,a.form.resourceCode=t.resourceCode,a.form.httpMethod=t.httpMethod)}else a.form.resourceName="",a.form.resourceCode="",a.form.httpMethod=""};return t({setResourceData:e=>{b.value=[{value:e.resourceCode,label:e.url+" ("+e.resourceName+")"}]}}),(t,a)=>{const o=i,l=p,_=f,w=m,y=h,x=v;return d(),u(x,{ref:"formRef",model:e.form,rules:r,layout:"vertical"},{default:n((()=>[s(y,{gutter:20},{default:n((()=>[s(_,{span:24},{default:n((()=>[s(l,{label:"接口路径:",name:"resourceCode"},{default:n((()=>[s(o,{value:e.form.resourceCode,"onUpdate:value":a[0]||(a[0]=t=>e.form.resourceCode=t),"show-search":"",placeholder:"请选择接口路径",style:{width:"100%"},"default-active-first-option":!1,"show-arrow":!1,"filter-option":!1,"not-found-content":null,options:b.value,onSearch:g,onChange:C},null,8,["value","options"])])),_:1})])),_:1}),s(_,{span:24},{default:n((()=>a[4]||(a[4]=[c("div",{class:"card-title"},"详细信息",-1)]))),_:1,__:[4]}),s(_,{span:12},{default:n((()=>[s(l,{label:"接口名称:",name:"resourceName"},{default:n((()=>[s(w,{value:e.form.resourceName,"onUpdate:value":a[1]||(a[1]=t=>e.form.resourceName=t),"allow-clear":"",placeholder:"请输入接口名称",disabled:""},null,8,["value"])])),_:1})])),_:1}),s(_,{span:12},{default:n((()=>[s(l,{label:"接口编码:",name:"resourceCode"},{default:n((()=>[s(w,{value:e.form.resourceCode,"onUpdate:value":a[2]||(a[2]=t=>e.form.resourceCode=t),"allow-clear":"",placeholder:"请输入接口编码",disabled:""},null,8,["value"])])),_:1})])),_:1}),s(_,{span:12},{default:n((()=>[s(l,{label:"接口请求方式:",name:"httpMethod"},{default:n((()=>[s(w,{value:e.form.httpMethod,"onUpdate:value":a[3]||(a[3]=t=>e.form.httpMethod=t),"allow-clear":"",placeholder:"请输入接口请求方式",disabled:""},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}},g=e("a",r(b,[["__scopeId","data-v-75419228"]])),C=Object.freeze(Object.defineProperty({__proto__:null,default:g},Symbol.toStringTag,{value:"Module"}));e("i",C)}}}));
