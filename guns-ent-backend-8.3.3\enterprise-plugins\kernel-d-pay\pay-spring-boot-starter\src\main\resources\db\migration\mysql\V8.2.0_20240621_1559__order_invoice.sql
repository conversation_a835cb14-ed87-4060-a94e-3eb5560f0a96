ALTER TABLE `shop_order`
ADD COLUMN `invoice_flag` char(1) NULL DEFAULT 'N' COMMENT '是否已经开票：Y-开了，N-未开' AFTER `open_id`,
ADD COLUMN `version_flag` bigint NULL DEFAULT NULL COMMENT '乐观锁' AFTER `invoice_flag`,
ADD COLUMN `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '删除标记：Y-已删除，N-未删除' AFTER `version_flag`;

CREATE TABLE `shop_order_invoice` (
  `order_invoice_id` bigint NOT NULL COMMENT '订单发票id',
  `order_id_list` json NOT NULL COMMENT '订单id集合，直接存json形式的集合',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `invoice_title` varchar(200) NOT NULL COMMENT '发票抬头',
  `taxpayer_no` varchar(200) NOT NULL COMMENT '发票纳税人识别号',
  `address` varchar(200) DEFAULT NULL COMMENT '单位地址',
  `phone` varchar(100) DEFAULT NULL COMMENT '单位电话',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户行',
  `bank_account_no` varchar(200) DEFAULT NULL COMMENT '开户行账号',
  `send_email` varchar(255) NOT NULL COMMENT '发送邮箱信息',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总开票金额',
  `description` varchar(2000) DEFAULT NULL COMMENT '备注信息',
  `order_invoice_status` tinyint(4) DEFAULT NULL COMMENT '订单开票状态：1-申请中，2-已开票',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`order_invoice_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='订单开票记录';