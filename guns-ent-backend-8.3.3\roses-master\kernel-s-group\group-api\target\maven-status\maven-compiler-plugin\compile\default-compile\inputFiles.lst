D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\callback\GroupNameCallbackApi.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\constants\GroupConstants.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\exception\enums\GroupExceptionEnum.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\exception\GroupException.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\GroupApi.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\GroupConditionApi.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\pojo\SysGroupDTO.java
D:\hyProject\guns-ent-backend-8.3.3\roses-master\kernel-s-group\group-api\src\main\java\cn\stylefeng\roses\kernel\group\api\pojo\SysGroupRequest.java
