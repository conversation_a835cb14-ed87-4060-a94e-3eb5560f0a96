package cn.stylefeng.roses.ent.saas.modular.manager.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;
import cn.stylefeng.roses.kernel.auth.api.password.PasswordStoredEncryptApi;
import cn.stylefeng.roses.kernel.auth.api.pojo.password.SaltedEncryptResult;
import cn.stylefeng.roses.kernel.rule.constants.SymbolConstant;
import cn.stylefeng.roses.kernel.rule.constants.TreeConstants;
import cn.stylefeng.roses.kernel.rule.enums.SexEnum;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.enums.permission.DataScopeTypeEnum;
import cn.stylefeng.roses.kernel.saas.api.constants.SaasConstants;
import cn.stylefeng.roses.kernel.sys.api.constants.SysConstants;
import cn.stylefeng.roses.kernel.sys.api.enums.org.OrgTypeEnum;
import cn.stylefeng.roses.kernel.sys.api.enums.role.RoleTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.position.entity.HrPosition;
import cn.stylefeng.roses.kernel.sys.modular.role.entity.SysRole;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import java.math.BigDecimal;

/**
 * 租户相关基本信息创建工厂
 *
 * <AUTHOR>
 * @since 2023/9/7 10:02
 */
public class TenantFactory {

    /**
     * 创建租户的管理员
     *
     * <AUTHOR>
     * @since 2023/9/7 10:03
     */
    public static SysUser createTenantAdminUser(TenantRequest tenantRequest, String calculatedPassword, String calculatedSalt) {

        SysUser sysUser = new SysUser();

        // 设置用户id
        sysUser.setUserId(IdWorker.getId());

        // 设置用户密码密码为MD5加盐，【2024年2月22日新增】如果有计算过的密码和盐则不需要单独再计算
        if (ObjectUtil.isNotEmpty(calculatedPassword) && ObjectUtil.isNotEmpty(calculatedSalt)) {
            sysUser.setPassword(calculatedPassword);
            sysUser.setPasswordSalt(calculatedSalt);
        } else {
            PasswordStoredEncryptApi encryptApi = SpringUtil.getBean(PasswordStoredEncryptApi.class);
            SaltedEncryptResult result = encryptApi.encryptWithSalt(tenantRequest.getPassword());
            sysUser.setPassword(result.getEncryptPassword());
            sysUser.setPasswordSalt(result.getPasswordSalt());
        }

        // 设置用户名称为：公司名称+管理员
        sysUser.setRealName(tenantRequest.getTenantName() + SaasConstants.TENANT_ADMIN_NAME);
        sysUser.setNickName(tenantRequest.getTenantName() + SaasConstants.TENANT_ADMIN_NAME);

        // 设置用户账号为租户的邮箱
        sysUser.setAccount(tenantRequest.getEmail());
        sysUser.setEmail(tenantRequest.getEmail());

        // 设置用户头像为用户上传的租户标识
        sysUser.setAvatar(tenantRequest.getTenantLogo());

        // 设置用户性别男
        sysUser.setSex(SexEnum.M.getCode());

        // 设置用户的电话
        sysUser.setPhone(tenantRequest.getSafePhone());

        // 设置是否为超级管理员用户
        sysUser.setSuperAdminFlag(YesOrNotEnum.N.getCode());

        // 设置用户为启用状态
        sysUser.setStatusFlag(StatusEnum.ENABLE.getCode());

        // 设置用户排序为0
        sysUser.setUserSort(new BigDecimal("0"));

        return sysUser;
    }

    /**
     * 创建租户的总公司
     *
     * <AUTHOR>
     * @since 2023/9/7 10:38
     */
    public static HrOrganization createTenantHrOrganization(TenantRequest tenantRequest) {

        HrOrganization hrOrganization = new HrOrganization();

        // 设置组织id
        hrOrganization.setOrgId(IdWorker.getId());

        // 设置组织机构的父级id信息
        hrOrganization.setOrgParentId(TreeConstants.DEFAULT_PARENT_ID);

        // 设置父级ids
        hrOrganization.setOrgPids(SymbolConstant.LEFT_SQUARE_BRACKETS + TreeConstants.DEFAULT_PARENT_ID + SymbolConstant.RIGHT_SQUARE_BRACKETS + SymbolConstant.COMMA);

        // 设置组织名称为租户的公司名称
        if (ObjectUtil.isNotEmpty(tenantRequest.getCompanyName())) {
            hrOrganization.setOrgName(tenantRequest.getCompanyName());
        } else {
            hrOrganization.setOrgName(tenantRequest.getTenantName());
        }

        // 设置组织机构编码为统一社会信用
        if (ObjectUtil.isNotEmpty(tenantRequest.getCompanySocialCode())) {
            hrOrganization.setOrgCode(tenantRequest.getCompanySocialCode());
        } else {
            hrOrganization.setOrgCode(tenantRequest.getTenantCode());
        }

        // 设置排序为0
        hrOrganization.setOrgSort(new BigDecimal("0"));

        // 设置为启用
        hrOrganization.setStatusFlag(StatusEnum.ENABLE.getCode());

        // 公司类型设置为：公司
        hrOrganization.setOrgType(OrgTypeEnum.COMPANY.getCode());

        // 设置税号
        hrOrganization.setTaxNo(tenantRequest.getCompanySocialCode());

        return hrOrganization;
    }

    /**
     * 创建租户职务
     *
     * <AUTHOR>
     * @since 2023/9/7 11:28
     */
    public static HrPosition createTenantPosition() {

        HrPosition hrPosition = new HrPosition();

        // 设置职务id
        hrPosition.setPositionId(IdWorker.getId());

        // 设置职务名称为：员工
        hrPosition.setPositionName(SaasConstants.TENANT_DEFAULT_POSITION_NAME);

        // 设置职务编码为：员工
        hrPosition.setPositionCode(SaasConstants.TENANT_DEFAULT_POSITION_CODE);

        // 设置职务排序为0
        hrPosition.setPositionSort(new BigDecimal("0"));

        // 设置职务状态为启用
        hrPosition.setStatusFlag(StatusEnum.ENABLE.getCode());

        return hrPosition;
    }

    /**
     * 创建租户的角色
     *
     * <AUTHOR>
     * @since 2023/9/7 11:22
     */
    public static SysRole createTenantRole(TenantRequest tenantRequest, HrOrganization tenantHrOrganization) {

        SysRole sysRole = new SysRole();

        // 设置角色id
        sysRole.setRoleId(IdWorker.getId());

        // 设置角色名称为：租户名称+管理员
        sysRole.setRoleName(tenantRequest.getTenantName() + SaasConstants.TENANT_ADMIN_NAME);

        // 设置角色编码为固定值
        sysRole.setRoleCode(SaasConstants.TENANT_ADMIN_ROLE_CODE);

        // 设置角色排序为0
        sysRole.setRoleSort(new BigDecimal("0"));

        // 数据范围设置为全部数据
        sysRole.setDataScopeType(DataScopeTypeEnum.ALL.getCode());

        // 设置角色状态为启用
        sysRole.setStatusFlag(StatusEnum.ENABLE.getCode());

        // 系统角色标识，系统角色不能被删除
        sysRole.setRoleType(RoleTypeEnum.SYSTEM_ROLE.getCode());
        sysRole.setRoleCompanyId(tenantHrOrganization.getOrgId());

        return sysRole;
    }

    /**
     * 创建租户的基本角色，这个角色的编码为SysConstants.DEFAULT_ROLE_CODE，创建用户时候，会默认将用户赋值这个角色
     *
     * <AUTHOR>
     * @since 2023/9/7 11:22
     */
    public static SysRole createBaseTenantRole(TenantRequest tenantRequest, HrOrganization tenantHrOrganization) {

        SysRole sysRole = new SysRole();

        // 设置角色id
        sysRole.setRoleId(IdWorker.getId());

        // 设置角色名称为：租户名称+管理员
        sysRole.setRoleName(tenantRequest.getTenantName() + SaasConstants.TENANT_BASE_ROLE_NAME);

        // 设置角色编码为固定值
        sysRole.setRoleCode(SysConstants.DEFAULT_ROLE_CODE);

        // 设置角色排序为0
        sysRole.setRoleSort(new BigDecimal("1"));

        // 数据范围自己
        sysRole.setDataScopeType(DataScopeTypeEnum.SELF.getCode());

        // 设置角色状态为启用
        sysRole.setStatusFlag(StatusEnum.ENABLE.getCode());

        // 改为公司类型角色
        sysRole.setRoleType(RoleTypeEnum.COMPANY_ROLE.getCode());
        sysRole.setRoleCompanyId(tenantHrOrganization.getOrgId());

        return sysRole;
    }

}
