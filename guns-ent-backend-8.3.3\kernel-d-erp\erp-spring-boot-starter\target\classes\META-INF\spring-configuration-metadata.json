{"groups": [{"name": "roses.erp", "type": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig"}], "properties": [{"name": "roses.erp.cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": true}, {"name": "roses.erp.cache-expire-seconds", "type": "java.lang.Long", "description": "缓存过期时间（秒）", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": 3600}, {"name": "roses.erp.data-permission-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据权限控制", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": true}, {"name": "roses.erp.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用ERP模块", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": true}, {"name": "roses.erp.module-description", "type": "java.lang.String", "description": "ERP模块描述", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": "ERP企业资源规划管理模块，提供供应商、客户、商品、区域等基础数据管理功能"}, {"name": "roses.erp.module-name", "type": "java.lang.String", "description": "ERP模块名称", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": "ERP企业资源规划"}, {"name": "roses.erp.module-version", "type": "java.lang.String", "description": "ERP模块版本", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": "1.0.0"}, {"name": "roses.erp.operate-log-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用操作日志记录", "sourceType": "cn.stylefeng.roses.kernel.erp.starter.ErpModuleConfig", "defaultValue": true}], "hints": []}