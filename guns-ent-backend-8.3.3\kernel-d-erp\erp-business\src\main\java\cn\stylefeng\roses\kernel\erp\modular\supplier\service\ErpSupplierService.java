package cn.stylefeng.roses.kernel.erp.modular.supplier.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProductCategory;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.BusinessModeChangeValidationResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 供应商主档案Service接口
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
public interface ErpSupplierService extends IService<ErpSupplier> {

    /**
     * 新增供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    void add(ErpSupplierRequest erpSupplierRequest);

    /**
     * 删除供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    void del(ErpSupplierRequest erpSupplierRequest);

    /**
     * 批量删除供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    void batchDelete(ErpSupplierRequest erpSupplierRequest);

    /**
     * 编辑供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    void edit(ErpSupplierRequest erpSupplierRequest);

    /**
     * 查询供应商详情
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 供应商详情
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    ErpSupplierResponse detail(ErpSupplierRequest erpSupplierRequest);

    /**
     * 分页查询供应商列表
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 供应商分页列表
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    PageResult<ErpSupplierResponse> findPage(ErpSupplierRequest erpSupplierRequest);

    /**
     * 查询供应商列表
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 供应商列表
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    List<ErpSupplierResponse> findList(ErpSupplierRequest erpSupplierRequest);

    /**
     * 更新供应商状态
     *
     * @param erpSupplierRequest 供应商请求参数
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    void updateStatus(ErpSupplierRequest erpSupplierRequest);

    /**
     * 校验供应商编码是否重复
     *
     * @param supplierCode 供应商编码
     * @param supplierId   供应商ID（编辑时传入，新增时为null）
     * @return 是否重复
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    boolean validateSupplierCodeRepeat(String supplierCode, Long supplierId);

    /**
     * 校验供应商是否可以删除
     *
     * @param supplierId 供应商ID
     * @return 是否可以删除
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    boolean validateCanDelete(Long supplierId);

    /**
     * 校验供应商是否可以停用
     *
     * @param supplierId 供应商ID
     * @return 是否可以停用
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    boolean validateCanInactive(Long supplierId);

    /**
     * 获取供应商关联的商品列表
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 商品列表
     * <AUTHOR>
     * @since 2025/07/27 21:52
     */
    List<ErpProductResponse> getSupplierProducts(ErpSupplierRequest erpSupplierRequest);

    /**
     * 校验供应商经营方式变更的影响
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 验证结果
     * <AUTHOR>
     * @since 2025/07/27 21:52
     */
    BusinessModeChangeValidationResponse validateBusinessModeChange(ErpSupplierRequest erpSupplierRequest);

}
