package cn.stylefeng.roses.ent.saas.modular.reg.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.manager.enums.TenantExceptionEnum;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantService;
import cn.stylefeng.roses.ent.saas.modular.reg.factory.TenantRegFactory;
import cn.stylefeng.roses.ent.saas.modular.reg.pojo.TenantRegRequest;
import cn.stylefeng.roses.ent.saas.modular.reg.service.TenantRegService;
import cn.stylefeng.roses.kernel.auth.api.exception.AuthException;
import cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi;
import cn.stylefeng.roses.kernel.email.api.MailSenderApi;
import cn.stylefeng.roses.kernel.email.api.pojo.SendMailParam;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.security.api.DragCaptchaApi;
import cn.stylefeng.roses.kernel.security.api.ImageCaptchaApi;
import cn.stylefeng.roses.kernel.validator.api.exception.enums.ValidatorExceptionEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 租户注册业务实现类
 *
 * <AUTHOR>
 * @since 2024-02-22 17:39
 */
@Service
public class TenantRegServiceImpl implements TenantRegService {

    @Resource
    private DragCaptchaApi dragCaptchaApi;

    @Resource
    private ImageCaptchaApi imageCaptchaApi;

    @Resource(name = "defaultStringCacheOperator")
    private CacheOperatorApi<String> defaultStringCacheOperator;

    @Resource
    private MailSenderApi mailSenderApi;

    @Resource
    private TenantService tenantService;

    @Override
    public void sendVerifyCodeEmail(String verKey, String verCode, String email) {

        // 1. 校验拖拽验证码是否正确
        this.validateCaptcha(verKey, verCode);

        // 2. 生成一个随机数字符串，存储在缓存中，120秒过期
        String randomCode = RandomUtil.randomString(6);
        defaultStringCacheOperator.put(verKey, randomCode, 120L);

        // 3. 发送邮件
        SendMailParam sendMailParam = new SendMailParam();
        sendMailParam.setTitle("【租户注册】邮箱验证码");
        sendMailParam.setContent("您的验证码是：" + randomCode);
        sendMailParam.setTos(ListUtil.toList(email));
        mailSenderApi.sendMail(sendMailParam);
    }

    @Override
    public void submitTenantReg(TenantRegRequest tenantRegRequest) {

        // 1. 校验邮箱的验证码是否正确
        this.validateEmailCode(tenantRegRequest.getVerKey(), tenantRegRequest.getEmailValidateCode());

        // 2. 创建租户信息
        Tenant tenant = TenantRegFactory.createTenant(tenantRegRequest);

        // 3. 保存租户信息
        tenantService.save(tenant);
    }

    /**
     * 验证码是否正确
     *
     * <AUTHOR>
     * @since 2021/7/6 15:07
     */
    private void validateCaptcha(String verKey, String verCode) {
        if (StrUtil.isEmpty(verKey) || StrUtil.isEmpty(verCode)) {
            throw new AuthException(ValidatorExceptionEnum.CAPTCHA_EMPTY);
        }
        if (!dragCaptchaApi.validateCaptcha(verKey, Convert.toInt(verCode))) {
            throw new AuthException(ValidatorExceptionEnum.DRAG_CAPTCHA_ERROR);
        }
    }

    /**
     * 校验邮箱验证码是否正确
     *
     * <AUTHOR>
     * @since 2024-02-22 18:52
     */
    private void validateEmailCode(String verKey, String requestEmailCode) {
        String rightEmailCode = defaultStringCacheOperator.get(verKey);
        if (StrUtil.isNotEmpty(rightEmailCode) && rightEmailCode.equals(requestEmailCode)) {
            return;
        } else {
            throw new ServiceException(TenantExceptionEnum.EMAIL_NOT_RIGHT);
        }
    }

}