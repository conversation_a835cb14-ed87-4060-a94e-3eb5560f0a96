<template>
  <div class="guns-layout">
    <!-- 左右分栏布局 -->
    <guns-split-layout width="292px" cacheKey="ERP_PRODUCT_CATEGORY_MANAGEMENT">
      <!-- 左侧分类树 -->
      <div class="guns-layout-sidebar width-100 p-t-12">
        <div class="sidebar-content">
          <product-category-tree
            ref="categoryTreeRef"
            :isShowEditIcon="true"
            @treeSelect="onTreeSelect"
            @addCategory="onAddCategory"
            @editCategory="onEditCategory"
            @deleteCategory="onDeleteCategory"
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <template #content>
        <div class="guns-layout-content">
          <div class="guns-layout">
            <div class="guns-layout-content-application">
              <div class="content-main">
                <!-- 头部操作区 -->
                <div class="content-main-header">
                  <div class="header-content">
                    <div class="header-content-left">
                      <a-space :size="16">
                        <span class="current-category-info" v-if="currentCategoryInfo">
                          当前分类：<a-tag color="blue">{{ currentCategoryInfo.categoryName }}</a-tag>
                        </span>
                      </a-space>
                    </div>
                    <div class="header-content-right">
                      <a-space :size="16">
                        <a-button type="primary" class="border-radius" @click="openAddModal()">
                          <plus-outlined />
                          新增子分类
                        </a-button>
                        <a-dropdown>
                          <template #overlay>
                            <a-menu @click="moreClick">
                              <a-menu-item key="1">
                                <icon-font iconClass="icon-opt-shanchu" color="#60666b" />
                                <span>批量删除</span>
                              </a-menu-item>
                            </a-menu>
                          </template>
                          <a-button class="border-radius">
                            更多
                            <small-dash-outlined />
                          </a-button>
                        </a-dropdown>
                      </a-space>
                    </div>
                  </div>
                </div>

                <!-- 主体内容区 -->
                <div class="content-main-body">
                  <div class="table-content">
                    <common-table
                      :columns="columns"
                      :where="where"
                      fieldBusinessCode="ERP_PRODUCT_CATEGORY_TABLE"
                      showTableTool
                      :showToolTotal="false"
                      rowId="categoryId"
                      ref="tableRef"
                      url="/erp/productCategory/page"
                    >
                      <template #toolLeft>
                        <a-input
                          v-model:value="where.searchText"
                          placeholder="分类名称、编码（回车搜索）"
                          allowClear
                          @pressEnter="reload"
                          style="width: 240px"
                          class="search-input"
                          :bordered="false"
                        >
                          <template #prefix>
                            <icon-font iconClass="icon-opt-search" />
                          </template>
                        </a-input>
                        <a-divider type="vertical" class="divider" />
                        <a @click="toggleAdvanced">{{ advanced ? '收起' : '高级筛选' }}</a>
                      </template>
                      <template #toolBottom>
                        <div class="super-search" style="margin-top: 8px" v-if="advanced">
                          <a-form :model="where" :labelCol="labelCol" :wrapper-col="wrapperCol">
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label="状态:">
                                  <a-select v-model:value="where.status" placeholder="请选择状态" style="width: 100%" allowClear>
                                    <a-select-option value="Y">启用</a-select-option>
                                    <a-select-option value="N">停用</a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="分类层级:">
                                  <a-select v-model:value="where.categoryLevel" placeholder="请选择层级" style="width: 100%" allowClear>
                                    <a-select-option :value="1">一级分类</a-select-option>
                                    <a-select-option :value="2">二级分类</a-select-option>
                                    <a-select-option :value="3">三级分类</a-select-option>
                                    <a-select-option :value="4">四级分类</a-select-option>
                                    <a-select-option :value="5">五级分类</a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label=" " class="not-label">
                                  <a-space :size="16">
                                    <a-button class="border-radius" @click="reload" type="primary">查询</a-button>
                                    <a-button class="border-radius" @click="clear">重置</a-button>
                                  </a-space>
                                </a-form-item>
                              </a-col>
                            </a-row>
                          </a-form>
                        </div>
                      </template>
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'categoryLevel'">
                          <a-tag v-if="record.categoryLevel === 1" color="red">一级分类</a-tag>
                          <a-tag v-else-if="record.categoryLevel === 2" color="orange">二级分类</a-tag>
                          <a-tag v-else-if="record.categoryLevel === 3" color="yellow">三级分类</a-tag>
                          <a-tag v-else-if="record.categoryLevel === 4" color="green">四级分类</a-tag>
                          <a-tag v-else-if="record.categoryLevel === 5" color="blue">五级分类</a-tag>
                          <a-tag v-else color="default">未知</a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'status'">
                          <a-tag v-if="record.status === 'Y'" color="green">启用</a-tag>
                          <a-tag v-else-if="record.status === 'N'" color="red">停用</a-tag>
                          <a-tag v-else color="default">{{ record.status }}</a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'categoryName'">
                          {{ record.categoryName }}
                        </template>
                        <template v-else-if="column.key === 'action'">
                          <a-space :size="16">
                            <icon-font
                              iconClass="icon-opt-bianji"
                              font-size="24px"
                              title="编辑"
                              color="#60666b"
                              @click="openEditModal(record)"
                            />
                            <icon-font
                              iconClass="icon-opt-shanchu"
                              font-size="24px"
                              title="删除"
                              color="#60666b"
                              @click="deleteRecord(record)"
                            />
                          </a-space>
                        </template>
                      </template>
                    </common-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </guns-split-layout>

    <!-- 新增/编辑弹窗 -->
    <addForm
      v-model:visible="showEdit"
      :data="currentRecord"
      @ok="handleFormOk"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, h, createVNode, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SmallDashOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import { ProductCategoryApi } from './api/productCategoryApi'
import productCategoryTree from './components/product-category-tree.vue'
import addForm from './components/addForm.vue'
import { isMobile } from '@/utils/common/util'

export default {
  name: 'ErpProductCategory',
  components: {
    PlusOutlined,
    SmallDashOutlined,
    productCategoryTree,
    addForm,
  },
  setup() {
    const advanced = ref(false)
    const showEdit = ref(false)
    const currentRecord = ref({})
    const currentCategoryInfo = ref(null)
    // ref
    const tableRef = ref(null)
    const categoryTreeRef = ref()

    // 响应式计算属性
    const labelCol = computed(() => {
      return { xxl: 7, xl: 7, lg: 5, md: 7, sm: 4 }
    })

    const wrapperCol = computed(() => {
      return { xxl: 17, xl: 17, lg: 19, md: 17, sm: 20 }
    })

    const spanCol = computed(() => {
      if (isMobile()) {
        return { xxl: 6, xl: 8, lg: 12, md: 24, sm: 24, xs: 24 }
      }
      return { xxl: 6, xl: 8, lg: 24, md: 24, sm: 24, xs: 24 }
    })

    // 查询参数
    const where = reactive({
      parentId: undefined,
      searchText: '',
      status: undefined,
      categoryLevel: undefined
    })

    // 表格列定义
    const columns = [
      {
        key: 'index',
        title: '序号',
        width: 48,
        align: 'center',
        isShow: true,
        hideInSetting: true
      },
      {
        dataIndex: 'categoryCode',
        title: '分类编码',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'categoryName',
        title: '分类名称',
        width: 160,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'parentName',
        title: '父级分类',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'categoryLevel',
        title: '分类层级',
        width: 100,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'sortOrder',
        title: '排序号',
        width: 80,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'status',
        title: '状态',
        width: 80,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'createTime',
        title: '创建时间',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        key: 'action',
        title: '操作',
        width: 100,
        isShow: true
      }
    ]

    // 加载数据
    const reload = async () => {
      tableRef.value.reload()
    }

    // 清除搜索条件
    const clear = () => {
      where.searchText = ''
      where.status = undefined
      where.categoryLevel = undefined
      where.parentId = undefined
      categoryTreeRef.value.currentSelectKeys = []
      currentCategoryInfo.value = null
      reload()
    }

    // 切换高级搜索
    const toggleAdvanced = () => {
      advanced.value = !advanced.value
    }

    // 树节点选择事件
    const onTreeSelect = (selectedKeys, metadata) => {
      if (selectedKeys.length > 0) {
        const selectedNode = metadata.selectedNodes[0]
        currentCategoryInfo.value = selectedNode
        where.parentId = selectedKeys[0]
        reload()
      } else {
        currentCategoryInfo.value = null
        where.parentId = undefined
        reload()
      }
    }

    // 从树组件添加分类
    const onAddCategory = (parentNode) => {
      if (parentNode) {
        currentCategoryInfo.value = parentNode
        where.parentId = parentNode.categoryId
      }
      openAddModal()
    }

    // 从树组件编辑分类
    const onEditCategory = (node) => {
      openEditModal(node)
    }

    // 从树组件删除分类（树组件已处理删除逻辑，这里只需要刷新表格）
    const onDeleteCategory = (node) => {
      // 树组件已经处理了删除操作，这里只需要刷新表格数据
      reload()
    }


    // 打开新增弹窗
    const openAddModal = () => {
      currentRecord.value = {
        parentId: where.parentId,
        categoryLevel: currentCategoryInfo.value ? currentCategoryInfo.value.categoryLevel + 1 : 1
      }
      showEdit.value = true
    }

    // 打开编辑弹窗
    const openEditModal = (record) => {
      currentRecord.value = { ...record }
      showEdit.value = true
    }

    // 删除记录
    const deleteRecord = async (record) => {
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的分类吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          const res = await ProductCategoryApi.delete({ categoryId: record.categoryId })
          message.success(res.message)
          reload()
          categoryTreeRef.value?.reload()
        }
      })
    }

    // 更多点击
    const moreClick = ({ key }) => {
      if (key == '1') {
        batchDelete()
      }
    }

    // 批量删除
    const batchDelete = () => {
      if (tableRef.value.selectedRowList && tableRef.value.selectedRowList.length == 0) {
        return message.warning('请选择需要删除的分类')
      }
      Modal.confirm({
        title: '提示',
        content: '确定要删除选中的分类吗?',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: async () => {
          const res = await ProductCategoryApi.batchDelete({ categoryIds: tableRef.value.selectedRowList })
          message.success(res.message)
          reload()
          categoryTreeRef.value?.reload()
        }
      })
    }

    // 表单操作成功回调
    const handleFormOk = () => {
      reload()
      // 刷新树
      categoryTreeRef.value?.reload()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      // 不需要直接加载，common-table 会自动加载
    })

    return {
      tableRef,
      categoryTreeRef,
      advanced,
      where,
      columns,
      currentRecord,
      currentCategoryInfo,
      showEdit,
      labelCol,
      wrapperCol,
      spanCol,
      reload,
      clear,
      toggleAdvanced,
      onTreeSelect,
      onAddCategory,
      onEditCategory,
      onDeleteCategory,
      openAddModal,
      openEditModal,
      deleteRecord,
      moreClick,
      batchDelete,
      handleFormOk
    }
  }
}
</script>

<style lang="less" scoped>
.guns-layout {
  .table-toolbar {
    margin-bottom: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-input {
        .ant-input {
          border-radius: 6px;
        }
      }

      a {
        color: #1890ff;
        cursor: pointer;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  .advanced-search {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;

    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

.guns-layout {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.guns-layout-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.guns-layout-content-application {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.sidebar-content {
  height: 100%;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.content-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.content-main-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-category-info {
  font-size: 14px;
  color: #666;
}

.content-main-body {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
  width: 100%;
  box-sizing: border-box;
}

.table-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.table-pagination {
  margin-top: 16px;
  text-align: right;
}

.product-category-table {
  width: 100% !important;

  :deep(.ant-table) {
    width: 100% !important;
  }

  :deep(.ant-table-container) {
    width: 100% !important;
  }

  :deep(.ant-table-content) {
    width: 100% !important;
  }

  :deep(.ant-table-body) {
    width: 100% !important;
  }
}

// 确保GunsSplitLayout组件的右侧内容区域完全铺满
:deep(.guns-split-panel-body) {
  width: 100% !important;
  flex: 1 !important;
  overflow: hidden !important;
}

:deep(.guns-split-panel) {
  width: 100% !important;
}
</style>
