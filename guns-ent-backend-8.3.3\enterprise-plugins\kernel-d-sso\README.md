# CA统一认证中心

## sso实现原理

![](./_images/dc7c3898.png)

## sso服务端启动流程

在mysql库中，新建一个库，例如，guns-sso

![](./_images/949af927.png)

打开application-local.yml，修改mysql和redis配置

![](./_images/92d671a6.png)

之后启动SsoApplication即可，因为flyway会自动初始化sql，所以不用手动初始化sql脚本。

![](_images/dfa26670.png)

默认单点服务依赖redis，所以另外也要启动一个Redis服务，并在application-local.yml配置上redis服务器地址。

![](_images/5c512513.png)

![](_images/1a0899ef.png)

## 表结构说明

单点登录服务端，表结构如下：

![](_images/86e535c5.png)

一般我们只需要关注三个表即可。

第一个表是ca_client表，是单点登录客户端表。当你有10个业务系统需要做单点时，那这张表就要有10条记录。也就是1个记录对应一个单点客户端。

第二个是sys_config表，是参数配置表。单点服务端的一些配置在这个表里维护。config_code是CA_开头的，都是单点登录服务端的配置。

![](_images/60c36945.png)

第三个是sys_user表，单点登录的用户表。这个表其实和系统管理业务的用户表一样，这个表的作用其实是单点登录的时候校验用户密码，获取用户信息用的。如果不用这个表的话也可以，那校验密码，获取用户信息的时候需要调用远程接口。

## 客户端对接单点登录

### 1.ca_client表增加客户端记录

这个表需要配置单点客户端的相关信息。

![](_images/7b293420.png)

需要注意的是`user_data_secret`字符串，需要128位或者256位（16字节或者32字节）进行Base64编码，可以用下面代码生成。

```java
public static void main(String[] args) {
    byte[] bytes = RandomUtil.randomBytes(32);
    String encode = Base64.encode(bytes);
    System.out.println(encode);
}
```

注意：

sso_callback_url需要配置为前端的/sso路由

sso_login_url需要配置为/login路由

### 2.前端项目配置修改

打开setting.js文件，修改ssoFlag为true，打开SSO。

![](_images/5bb8dcaf.png)

### 3.后端项目配置修改

打开application.yml，修改sso.openFlag=true，打开SSO。

![](_images/f42b95fe.png)

## 演示使用

### 1.项目启动

启动sso服务端，启动前端项目，启动后端项目

### 2.打开前端  http://localhost:8000

可以看到请求记录，未登录状态下，回去sso服务端探测是否有全局会话，没有全局会话会跳转到登录界面。

![](_images/986c64f1.png)

点击登录后，会访问sso服务器，创建全局会话

![](_images/017fc54b.png)
