package cn.stylefeng.roses.kernel.oauth2.modular.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方用户信息表
 *
 * <AUTHOR>
 * @date 2022/7/1 15:11
 */
@EqualsAndHashCode(callSuper = true)
@TableName("oauth_user_info")
@Data
public class OauthUserInfo extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "oauth_id", type = IdType.ASSIGN_ID)
    private Long oauthId;

    /**
     * 用户主键id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 用户网址
     */
    @TableField("blog")
    private String blog;

    /**
     * 所在公司
     */
    @TableField("company")
    private String company;

    /**
     * 位置
     */
    @TableField("location")
    private String location;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 用户备注（各平台中的用户个人介绍）
     */
    @TableField("remark")
    private String remark;

    /**
     * 性别，1-男，0-女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 用户来源
     */
    @TableField("source")
    private String source;

    /**
     * 用户授权的token
     */
    @TableField("token")
    private String token;

    /**
     * 第三方平台的用户唯一di
     */
    @TableField("uuid")
    private String uuid;

}
