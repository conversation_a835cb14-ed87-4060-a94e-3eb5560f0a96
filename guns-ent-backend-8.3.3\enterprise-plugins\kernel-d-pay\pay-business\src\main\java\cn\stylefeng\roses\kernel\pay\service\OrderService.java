package cn.stylefeng.roses.kernel.pay.service;

import cn.stylefeng.roses.kernel.pay.api.OrderApi;
import cn.stylefeng.roses.kernel.pay.api.entity.Order;
import cn.stylefeng.roses.kernel.pay.pojo.NotifyRequest;
import cn.stylefeng.roses.kernel.pay.pojo.OrderDTO;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderBuyRequest;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderRequest;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 订单服务类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
public interface OrderService extends IService<Order>, OrderApi {

    /**
     * 创建订单
     *
     * <AUTHOR>
     * @date 2021/6/23 22:48
     */
    Order createOrder(OrderBuyRequest orderBuyRequest);

    /**
     * 支付成功的回调
     *
     * <AUTHOR>
     * @date 2021/6/23 22:52
     */
    void payNotify(NotifyRequest notifyRequest);

    /**
     * 查询用户个人的订单列表
     *
     * @param orderRequest 请求参数
     * @return List<Order>   返回结果
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    List<OrderDTO> findUserOrderList(OrderRequest orderRequest);

    /**
     * 查询详情
     *
     * @param orderRequest 请求参数
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    Order detail(OrderRequest orderRequest);

    /**
     * 取消订单
     *
     * <AUTHOR>
     * @date 2021/6/29 9:27
     */
    void cancelOrder(OrderRequest orderRequest);

    /**
     * 删除订单
     *
     * <AUTHOR>
     * @date 2021/6/29 9:28
     */
    void deleteOrder(OrderRequest orderRequest);

    /**
     * 获取订单的状态
     *
     * <AUTHOR>
     * @since 2024/6/20 17:54
     */
    Integer queryOrderStatus(OrderRequest orderRequest);

    /**
     * 批量修改订单发票的状态
     *
     * <AUTHOR>
     * @since 2024/6/24 16:06
     */
    void batchUpdateOrderInvoiceStatus(List<Long> orderIds, YesOrNotEnum yesOrNotEnum);

}
