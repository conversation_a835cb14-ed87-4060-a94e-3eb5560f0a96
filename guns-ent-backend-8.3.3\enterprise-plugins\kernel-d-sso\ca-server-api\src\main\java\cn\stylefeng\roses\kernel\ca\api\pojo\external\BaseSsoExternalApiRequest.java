package cn.stylefeng.roses.kernel.ca.api.pojo.external;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 外部单点登录控制器请求的基类
 * <p>
 * 外部单点请求需要进行jwt token安全验证
 *
 * <AUTHOR>
 * @date 2021/2/20 16:24
 */
@Data
public class BaseSsoExternalApiRequest {

    /**
     * 请求token，为jwt token
     */
    @NotBlank(message = "jwt token不能为空")
    private String securityToken;

    /**
     * 客户端标识，代表是从cbd单点还是从集采单点请求过来的
     */
    @NotBlank(message = "clientType不能为空")
    private String clientType;

}
