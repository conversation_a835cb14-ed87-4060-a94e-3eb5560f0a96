package cn.stylefeng.roses.kernel.impexp.user.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.UserExcelImportParse;
import cn.stylefeng.roses.kernel.rule.enums.SexEnum;
import cn.stylefeng.roses.kernel.sys.api.enums.user.UserStatusEnum;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户导出数据的创建
 *
 * <AUTHOR>
 * @since 2024/2/13 21:53
 */
public class UserExportFactory {

    /**
     * 创建导出用户需要的数据结构
     *
     * <AUTHOR>
     * @since 2024/2/13 21:53
     */
    public static List<UserExcelImportParse> createUserExportData(List<SysUser> sysUserList) {

        if (ObjectUtil.isEmpty(sysUserList)) {
            return new ArrayList<>();
        }

        List<UserExcelImportParse> userExcelImportParses = new ArrayList<>();

        int number = 1;

        for (SysUser sysUser : sysUserList) {
            UserExcelImportParse userExcelImportParse = new UserExcelImportParse();

            // 转化用户id
            userExcelImportParse.setUserId(String.valueOf(sysUser.getUserId()));

            // 转化序号
            userExcelImportParse.setNumber(String.valueOf(number));
            number++;

            // 转化账号
            userExcelImportParse.setAccount(sysUser.getAccount());

            // 转化姓名
            userExcelImportParse.setRealName(sysUser.getRealName());
            userExcelImportParse.setNickName(sysUser.getNickName());

            // 转化账号
            userExcelImportParse.setAccount(sysUser.getAccount());

            // 转化生日
            if (ObjectUtil.isNotEmpty(sysUser.getBirthday())) {
                userExcelImportParse.setBirthday(DateUtil.formatDate(sysUser.getBirthday()));
            }

            // 转化性别
            if (ObjectUtil.isNotEmpty(sysUser.getSex())) {
                userExcelImportParse.setSex(SexEnum.codeToMessage(sysUser.getSex()));
            }

            // 转化邮箱
            userExcelImportParse.setEmail(sysUser.getEmail());

            // 转化手机
            userExcelImportParse.setPhone(sysUser.getPhone());

            // 转化电话
            userExcelImportParse.setTel(sysUser.getTel());

            // 转化状态
            userExcelImportParse.setStatusFlag(UserStatusEnum.getCodeMessage(sysUser.getStatusFlag()));

            // 转化排序
            if (ObjectUtil.isNotEmpty(sysUser.getUserSort())) {
                userExcelImportParse.setUserSort(sysUser.getUserSort().toPlainString());
            }

            // 转化用户工号
            userExcelImportParse.setEmployeeNumber(sysUser.getEmployeeNumber());

            // 转化对接外部主数据的用户id
            userExcelImportParse.setMasterUserId(sysUser.getMasterUserId());

            userExcelImportParses.add(userExcelImportParse);
        }

        return userExcelImportParses;
    }

}
