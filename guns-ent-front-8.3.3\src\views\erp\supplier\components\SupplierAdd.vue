<template>


  <a-modal
    :visible="visible"
    title="新增供应商"
    :width="800"
    :confirm-loading="loading"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="供应商编码" name="supplierCode">
            <a-input v-model:value="form.supplierCode" placeholder="请输入供应商编码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="供应商名称" name="supplierName">
            <a-input v-model:value="form.supplierName" placeholder="请输入供应商名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="供应商简称" name="supplierShortName">
            <a-input v-model:value="form.supplierShortName" placeholder="请输入供应商简称" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="供应商类型" name="supplierType">
            <a-select v-model:value="form.supplierType" placeholder="请选择供应商类型">
              <a-select-option v-for="item in supplierTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="联系人" name="contactPerson">
            <a-input v-model:value="form.contactPerson" placeholder="请输入联系人" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="联系电话" name="contactPhone">
            <a-input v-model:value="form.contactPhone" placeholder="请输入联系电话" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="手机号码" name="contactMobile">
            <a-input v-model:value="form.contactMobile" placeholder="请输入手机号码" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="邮箱地址" name="contactEmail">
            <a-input v-model:value="form.contactEmail" placeholder="请输入邮箱地址" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="联系地址" name="contactAddress" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.contactAddress" placeholder="请输入联系地址" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="营业执照号" name="businessLicenseNo">
            <a-input v-model:value="form.businessLicenseNo" placeholder="请输入营业执照号" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="税务登记号" name="taxNo">
            <a-input v-model:value="form.taxNo" placeholder="请输入税务登记号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="开户银行" name="bankName">
            <a-input v-model:value="form.bankName" placeholder="请输入开户银行" />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="银行账号" name="bankAccount">
            <a-input v-model:value="form.bankAccount" placeholder="请输入银行账号" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 经营方式和销售扣点 -->
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="经营方式" name="businessMode">
            <div style="display: flex; align-items: center; gap: 8px;">
              <a-select v-model:value="form.businessMode" placeholder="请选择经营方式" @change="handleBusinessModeChange" style="flex: 1;">
                <a-select-option value="PURCHASE_SALE">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>购销</span>
                    <a-tag color="blue" size="small">采购销售</a-tag>
                  </div>
                </a-select-option>
                <a-select-option value="JOINT_VENTURE">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>联营</span>
                    <a-tag color="green" size="small">联合经营</a-tag>
                  </div>
                </a-select-option>
                <a-select-option value="CONSIGNMENT">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>代销</span>
                    <a-tag color="orange" size="small">代理销售</a-tag>
                  </div>
                </a-select-option>
              </a-select>
              <a-tooltip>
                <template #title>
                  <div>
                    <div style="margin-bottom: 8px;"><strong>购销：</strong>需要采购入库，管理库存，按采购价结算。适用于传统的采购销售模式。</div>
                    <div style="margin-bottom: 8px;"><strong>联营：</strong>不需要采购入库，不管理库存，商品归属于供应商，按销售扣点结算。适用于品牌专柜等场景。</div>
                    <div><strong>代销：</strong>需要采购入库，管理库存，按实际销售扣点结算。适用于代理销售模式。</div>
                  </div>
                </template>
                <question-circle-outlined style="color: #1890ff; cursor: pointer;" />
              </a-tooltip>
            </div>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24" v-if="form.businessMode === 'JOINT_VENTURE' || form.businessMode === 'CONSIGNMENT'">
          <a-form-item label="销售扣点" name="salesDeduction">
            <a-input-number
              v-model:value="form.salesDeduction"
              :min="0"
              :max="100"
              :precision="2"
              :step="0.1"
              placeholder="请输入销售扣点"
              style="width: 100%"
            >
              <template #addonAfter>%</template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="信用等级" name="creditLevel">
            <a-select v-model:value="form.creditLevel" placeholder="请选择信用等级">
              <a-select-option v-for="item in creditLevelOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="关联区域" name="regionIds" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <region-selector
              ref="regionSelectorRef"
              v-model="form.regionIds"
              placeholder="请选择供应商服务的区域"
              @change="handleRegionChange"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { SupplierApi } from '../api/SupplierApi';
import RegionSelector from '@/components/common/RegionSelector/index.vue';

export default {
  name: 'SupplierAdd',
  components: {
    RegionSelector,
    QuestionCircleOutlined
  },
  props: {
    // 弹窗是否打开
    visible: Boolean
  },
  emits: ['update:visible', 'done'],
  setup(props, { emit }) {
    // 表单数据
    const form = reactive({
      supplierType: 'ENTERPRISE',
      businessMode: 'PURCHASE_SALE',
      salesDeduction: null,
      creditLevel: 'C',
      status: 'ACTIVE',
      regionIds: []
    });
    
    const formRef = ref(null);
    const regionSelectorRef = ref(null);
    const loading = ref(false);

    // 选项数据
    const supplierTypeOptions = SupplierApi.getSupplierTypeOptions();
    const statusOptions = SupplierApi.getSupplierStatusOptions();
    const creditLevelOptions = SupplierApi.getCreditLevelOptions();

    // 表单验证规则
    const rules = reactive({
      supplierCode: [
        { required: true, message: '请输入供应商编码', trigger: 'blur' },
        { max: 50, message: '供应商编码不能超过50个字符', trigger: 'blur' }
      ],
      supplierName: [
        { required: true, message: '请输入供应商名称', trigger: 'blur' },
        { max: 200, message: '供应商名称不能超过200个字符', trigger: 'blur' }
      ],
      supplierType: [
        { required: true, message: '请选择供应商类型', trigger: 'change' }
      ],
      businessMode: [
        { required: true, message: '请选择经营方式', trigger: 'change' }
      ],
      contactEmail: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      contactPhone: [
        { pattern: /^[0-9-()（）\s]+$/, message: '请输入正确的电话号码', trigger: 'blur' }
      ],
      contactMobile: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      salesDeduction: [
        { 
          validator: (rule, value) => {
            if ((form.businessMode === 'JOINT_VENTURE' || form.businessMode === 'CONSIGNMENT') && (value === null || value === undefined)) {
              return Promise.reject('请输入销售扣点');
            }
            if (value !== null && value !== undefined && (value < 0 || value > 100)) {
              return Promise.reject('销售扣点必须在0-100之间');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ]
    });

    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 经营方式变更处理
    const handleBusinessModeChange = (businessMode) => {
      console.log('经营方式变更:', businessMode);
      // 如果切换到购销模式，清空销售扣点
      if (businessMode === 'PURCHASE_SALE') {
        form.salesDeduction = null;
      }
    };

    // 销售扣点变更处理
    const handleSalesDeductionChange = (salesDeduction) => {
      console.log('销售扣点变更:', salesDeduction);
    };

    // 保存
    const save = async () => {
      try {
        await formRef.value.validate();
        loading.value = true;
        
        // 准备提交的数据
        const submitData = { ...form };
        
        // 保存供应商基本信息
        const result = await SupplierApi.add(submitData);
        
        // 获取新增的供应商ID
        const supplierId = result.data;
        
        // 更新供应商关联的区域
        if (supplierId && form.regionIds && form.regionIds.length > 0) {
          await SupplierApi.updateSupplierRegions({
            supplierId: supplierId,
            regionIds: form.regionIds
          });
        }
        
        message.success('新增供应商成功');
        updateVisible(false);
        emit('done');
      } catch (e) {
        console.error('新增供应商失败:', e);
        message.error(e.message || '新增失败');
      } finally {
        loading.value = false;
      }
    };

    // 重置表单
    const resetForm = () => {
      Object.keys(form).forEach((key) => {
        if (key === 'supplierType') {
          form[key] = 'ENTERPRISE';
        } else if (key === 'businessMode') {
          form[key] = 'PURCHASE_SALE';
        } else if (key === 'creditLevel') {
          form[key] = 'C';
        } else if (key === 'status') {
          form[key] = 'ACTIVE';
        } else if (key === 'regionIds') {
          form[key] = [];
        } else if (key === 'salesDeduction') {
          form[key] = null;
        } else {
          form[key] = undefined;
        }
      });

      // 重置表单验证状态
      if (formRef.value) {
        formRef.value.resetFields();
      }

      // 重置 RegionSelector 组件状态
      if (regionSelectorRef.value && regionSelectorRef.value.resetState) {
        regionSelectorRef.value.resetState();
      }
    };

    // 监听弹窗显示状态
    watch(
      () => props.visible,
      (visible) => {
        if (visible) {
          resetForm();
        }
      },
      { immediate: true }
    );

    // 区域选择变化处理
    const handleRegionChange = (value, regions) => {
      console.log('供应商区域选择变化:', value, regions);
      form.regionIds = value;
    };

    return {
      form,
      formRef,
      regionSelectorRef,
      loading,
      supplierTypeOptions,
      statusOptions,
      creditLevelOptions,
      rules,
      updateVisible,
      handleBusinessModeChange,
      handleSalesDeductionChange,
      save,
      handleRegionChange
    };
  }
};
</script>

<style scoped>
/* 表单样式优化 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label {
  font-weight: 500;
}

/* 区域选择器样式 */
.ant-select-multiple .ant-select-selection-item {
  background: #f6f6f6;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

/* 文本域样式 */
.ant-input {
  border-radius: 4px;
}

.ant-select {
  border-radius: 4px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  :deep(.ant-modal) {
    width: 95% !important;
    margin: 10px auto;
  }
  
  :deep(.ant-form-item-label) {
    text-align: left !important;
  }
}
</style>