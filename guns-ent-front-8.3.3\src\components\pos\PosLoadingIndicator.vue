<template>
  <div class="pos-loading-indicator">
    <!-- 全局加载遮罩 -->
    <div 
      v-if="showGlobalLoading && hasAnyLoading" 
      class="global-loading-overlay"
      :class="{ 'with-backdrop': showBackdrop }"
    >
      <div class="loading-content">
        <a-spin 
          :spinning="true" 
          size="large"
          :tip="globalLoadingMessage"
        >
          <div class="loading-details">
            <!-- 进度条 -->
            <div v-if="showProgress && currentProgress > 0" class="progress-section">
              <a-progress 
                :percent="currentProgress" 
                :status="progressStatus"
                :show-info="true"
                :stroke-width="8"
              />
            </div>
            
            <!-- 操作列表 -->
            <div v-if="showOperationList && activeOperations.length > 0" class="operations-list">
              <div class="operations-title">正在进行的操作：</div>
              <div 
                v-for="operation in activeOperations" 
                :key="operation.key"
                class="operation-item"
              >
                <div class="operation-info">
                  <span class="operation-message">{{ operation.message }}</span>
                  <span class="operation-duration">{{ formatDuration(operation.startTime) }}</span>
                </div>
                <div v-if="operation.showProgress" class="operation-progress">
                  <a-progress 
                    :percent="operation.progress" 
                    size="small"
                    :show-info="false"
                  />
                </div>
                <a-button 
                  v-if="operation.cancelable"
                  type="text" 
                  size="small"
                  @click="cancelOperation(operation.key)"
                >
                  取消
                </a-button>
              </div>
            </div>
            
            <!-- 取消按钮 -->
            <div v-if="showCancelButton && hasCancelableOperations" class="cancel-section">
              <a-button type="default" @click="cancelAllOperations">
                取消所有操作
              </a-button>
            </div>
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 局部加载指示器 -->
    <div v-if="showLocalLoading" class="local-loading-indicators">
      <div 
        v-for="operation in visibleLocalOperations" 
        :key="operation.key"
        class="local-loading-item"
        :class="`loading-${operation.key.replace('pos_', '')}`"
      >
        <a-spin :spinning="true" size="small">
          <span class="loading-text">{{ operation.message }}</span>
        </a-spin>
      </div>
    </div>

    <!-- 浮动加载提示 -->
    <transition name="fade">
      <div 
        v-if="showFloatingTip && longestOperation" 
        class="floating-loading-tip"
        :class="{ 'warning': isLongRunning }"
      >
        <a-icon type="loading" spin />
        <span class="tip-message">{{ longestOperation.message }}</span>
        <span class="tip-duration">{{ formatDuration(longestOperation.startTime) }}</span>
        <a-button 
          v-if="longestOperation.cancelable"
          type="text" 
          size="small"
          @click="cancelOperation(longestOperation.key)"
        >
          取消
        </a-button>
      </div>
    </transition>
  </div>
</template>

<script>
import { computed, defineComponent } from 'vue';
import { usePosLoadingState } from '@/composables/useLoadingState';

export default defineComponent({
  name: 'PosLoadingIndicator',
  props: {
    // 显示模式
    mode: {
      type: String,
      default: 'auto', // auto, global, local, floating
      validator: value => ['auto', 'global', 'local', 'floating', 'all'].includes(value)
    },
    // 是否显示背景遮罩
    showBackdrop: {
      type: Boolean,
      default: true
    },
    // 是否显示进度条
    showProgress: {
      type: Boolean,
      default: true
    },
    // 是否显示操作列表
    showOperationList: {
      type: Boolean,
      default: true
    },
    // 是否显示取消按钮
    showCancelButton: {
      type: Boolean,
      default: true
    },
    // 长时间运行阈值（毫秒）
    longRunningThreshold: {
      type: Number,
      default: 10000 // 10秒
    },
    // 过滤显示的操作
    includeOperations: {
      type: Array,
      default: () => []
    },
    // 排除显示的操作
    excludeOperations: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const loadingState = usePosLoadingState();

    // 计算属性
    const showGlobalLoading = computed(() => {
      return ['auto', 'global', 'all'].includes(props.mode);
    });

    const showLocalLoading = computed(() => {
      return ['auto', 'local', 'all'].includes(props.mode);
    });

    const showFloatingTip = computed(() => {
      return ['auto', 'floating', 'all'].includes(props.mode);
    });

    const activeOperations = computed(() => {
      let operations = loadingState.getActiveLoadings();
      
      // 过滤操作
      if (props.includeOperations.length > 0) {
        operations = operations.filter(op => props.includeOperations.includes(op.key));
      }
      
      if (props.excludeOperations.length > 0) {
        operations = operations.filter(op => !props.excludeOperations.includes(op.key));
      }
      
      return operations;
    });

    const visibleLocalOperations = computed(() => {
      return activeOperations.value.filter(op => 
        !['pos_init_data', 'pos_process_payment'].includes(op.key)
      );
    });

    const globalLoadingMessage = computed(() => {
      const operations = activeOperations.value;
      if (operations.length === 0) return '加载中...';
      if (operations.length === 1) return operations[0].message;
      return `正在执行 ${operations.length} 个操作...`;
    });

    const currentProgress = computed(() => {
      const operations = activeOperations.value.filter(op => op.showProgress);
      if (operations.length === 0) return 0;
      
      const totalProgress = operations.reduce((sum, op) => sum + op.progress, 0);
      return Math.round(totalProgress / operations.length);
    });

    const progressStatus = computed(() => {
      if (currentProgress.value >= 100) return 'success';
      if (currentProgress.value >= 80) return 'active';
      return 'normal';
    });

    const hasCancelableOperations = computed(() => {
      return activeOperations.value.some(op => op.cancelable);
    });

    const longestOperation = computed(() => {
      return loadingState.longestRunningOperation.value;
    });

    const isLongRunning = computed(() => {
      if (!longestOperation.value) return false;
      return longestOperation.value.duration > props.longRunningThreshold;
    });

    // 方法
    const formatDuration = (startTime) => {
      const duration = Date.now() - startTime;
      const seconds = Math.floor(duration / 1000);
      
      if (seconds < 60) {
        return `${seconds}秒`;
      } else {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}分${remainingSeconds}秒`;
      }
    };

    const cancelOperation = (key) => {
      loadingState.cancelLoading(key);
    };

    const cancelAllOperations = () => {
      activeOperations.value.forEach(op => {
        if (op.cancelable) {
          loadingState.cancelLoading(op.key);
        }
      });
    };

    return {
      // 状态
      hasAnyLoading: loadingState.hasAnyLoading,
      
      // 计算属性
      showGlobalLoading,
      showLocalLoading,
      showFloatingTip,
      activeOperations,
      visibleLocalOperations,
      globalLoadingMessage,
      currentProgress,
      progressStatus,
      hasCancelableOperations,
      longestOperation,
      isLongRunning,
      
      // 方法
      formatDuration,
      cancelOperation,
      cancelAllOperations
    };
  }
});
</script>

<style scoped>
.pos-loading-indicator {
  position: relative;
}

.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.global-loading-overlay.with-backdrop {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.loading-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 500px;
}

.loading-details {
  margin-top: 16px;
}

.progress-section {
  margin-bottom: 16px;
}

.operations-list {
  margin-bottom: 16px;
}

.operations-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.operation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.operation-item:last-child {
  border-bottom: none;
}

.operation-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.operation-message {
  font-size: 14px;
  color: #333;
}

.operation-duration {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.operation-progress {
  width: 100px;
  margin: 0 12px;
}

.cancel-section {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.local-loading-indicators {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.local-loading-item {
  background: white;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 3px solid #1890ff;
}

.loading-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.floating-loading-tip {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border-radius: 6px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  border-left: 4px solid #1890ff;
}

.floating-loading-tip.warning {
  border-left-color: #faad14;
}

.tip-message {
  font-size: 14px;
  color: #333;
}

.tip-duration {
  font-size: 12px;
  color: #999;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>
