System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.R}],execute:function(){t("I",class{static page(t){return r.getAndLoadData("/erp/inventoryAlert/record/page",t)}static findPage(t){return this.page(t)}static detail(t){return r.getAndLoadData("/erp/inventoryAlert/record/detail",t)}static handle(t){return r.post("/erp/inventoryAlert/record/handle",t)}static batchHandle(t){return r.post("/erp/inventoryAlert/record/batchHandle",t)}static ignore(t){return r.post("/erp/inventoryAlert/record/ignore",t)}static getStatistics(t){return r.getAndLoadData("/erp/inventoryAlert/record/statistics",t)}static exportRecords(t){return r.downLoad("/erp/inventoryAlert/record/export",t)}static getRecentRecords(t){return r.getAndLoadData("/erp/inventoryAlert/record/recent",t)}static getRecordsByProduct(t){return r.getAndLoadData("/erp/inventoryAlert/record/byProduct",t)}static getOverview(t){return r.getAndLoadData("/erp/inventoryAlert/record/overview",t)}static delete(t){return r.post("/erp/inventoryAlert/record/delete",t)}static getHistory(t){return r.getAndLoadData("/erp/inventoryAlert/record/history",t)}})}}}));
