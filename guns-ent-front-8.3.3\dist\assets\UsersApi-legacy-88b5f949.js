System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var s;return{setters:[t=>{s=t.R}],execute:function(){t("U",class{static findPage(t){return s.getAndLoadData("/sysUser/page",t)}static getResetPassword(t){return s.getAndLoadData("/sysUser/getResetPassword",t)}static add(t){return s.post("/sysUser/add",t)}static edit(t){return s.post("/sysUser/edit",t)}static delete(t){return s.post("/sysUser/delete",t)}static batchDelete(t){return s.post("/sysUser/batchDelete",t)}static detail(t){return s.getAndLoadData("/sysUser/detail",t)}static list(t){return s.getAndLoadData("/sysUser/list",t)}static resetPassword(t){return s.post("/sysUser/resetPassword",t)}static updateStatus(t){return s.post("/sysUser/updateStatus",t)}static bindRoles(t){return s.post("/sysUser/bindRoles",t)}static roleList(t){return s.getAndLoadData("/sysRole/list",t)}static ExportUser(t){return s.downLoad("/user/ExportUser",t)}static getExcelTemplate(t){return s.downLoad("/userImport/getExcelTemplate",t)}static uploadAndGetPreviewData(t){return s.post("/userImport/uploadAndGetPreviewData",t)}static ensureImport(t){return s.post("/userImport/ensureImport",t)}static getUserOrgList(t){return s.getAndLoadData("/sysRoleAssign/v2/getUserOrgList",t)}static getCompanyBusinessRoleTree(t){return s.get("/sysRoleAssign/v2/getCompanyBusinessRoleTree",t)}static getCompanyRoleTree(t){return s.get("/sysRoleAssign/v2/getCompanyRoleTree",t)}static changeRoleSelect(t){return s.post("/sysRoleAssign/changeRoleSelect",t)}})}}}));
