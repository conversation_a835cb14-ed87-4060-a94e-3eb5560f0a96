System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./AppApi-legacy-f1c900da.js","./app-add-edit-legacy-f2be2288.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./app-form-legacy-591d3698.js","./index-legacy-94a6fc23.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js"],(function(e,a){"use strict";var l,t,s,n,i,o,d,c,u,p,r,h,v,y,g,m,w,x,_,f,I,b,k,C,A,S,T,j,E,P;return{setters:[e=>{l=e._},e=>{t=e._,s=e.r,n=e.b3,i=e.L,o=e.o,d=e.k,c=e.bv,u=e.a,p=e.c,r=e.b,h=e.d,v=e.w,y=e.aR,g=e.f,m=e.g,w=e.t,x=e.h,_=e.M,f=e.E,I=e.m,b=e.n,k=e.B,C=e.I,A=e.p,S=e.q,T=e.D,j=e.l},e=>{E=e.A},e=>{P=e.default},null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".appIconWrapper[data-v-ecda94dc]{width:22px;height:22px}\n",document.head.appendChild(a);const D={class:"guns-layout"},L={class:"guns-layout-content"},F={class:"guns-layout"},R={class:"guns-layout-content-application"},U={class:"content-mian"},z={class:"content-mian-header"},W={class:"header-content"},B={class:"header-content-left"},V={class:"header-content-right"},N={class:"content-mian-body"},O={class:"table-content"},q=["onClick"],M=["src"];e("default",t(Object.assign({name:"AuthRole"},{__name:"index",setup(e){const a=s([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"appName",title:"应用名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"appCode",title:"应用编码",width:100,isShow:!0},{dataIndex:"appIconWrapper",title:"应用图标",width:100,isShow:!0},{dataIndex:"statusFlag",title:"状态",width:100,isShow:!0},{dataIndex:"appSort",title:"排序",width:100,isShow:!0},{dataIndex:"createTime",title:"创建时间",width:150,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}]),t=s(null),Z=s({searchText:""}),G=s(null),H=s(!1),J=n(),K=i((()=>!J.authorities.find((e=>"UPDATE_USER_STATUS"==e))));o((()=>{}));const Q=({key:e})=>{"1"==e&&$()},X=()=>{t.value.reload()},Y=e=>{G.value=e,H.value=!0},$=()=>{if(t.value.selectedRowList&&0==t.value.selectedRowList.length)return I.warning("请选择需要删除的应用");_.confirm({title:"提示",content:"确定要删除选中的应用吗?",icon:h(f),maskClosable:!0,onOk:async()=>{const e=await E.batchDelete({appIdList:t.value.selectedRowList});I.success(e.message),X()}})};return(e,s)=>{const n=b,i=d("plus-outlined"),o=k,J=C,$=A,ee=S,ae=d("small-dash-outlined"),le=T,te=j,se=d("vxe-switch"),ne=l,ie=c("permission");return u(),p("div",D,[r("div",L,[r("div",F,[r("div",R,[r("div",U,[r("div",z,[r("div",W,[r("div",B,[h(n,{size:16})]),r("div",V,[h(n,{size:16},{default:v((()=>[y((u(),g(o,{type:"primary",class:"border-radius",onClick:s[0]||(s[0]=e=>Y())},{default:v((()=>[h(i),s[3]||(s[3]=m("新建"))])),_:1,__:[3]})),[[ie,["ADD_APP"]]]),h(le,null,{overlay:v((()=>[h(ee,{onClick:Q},{default:v((()=>[y((u(),p("div",null,[h($,{key:"1"},{default:v((()=>[h(J,{iconClass:"icon-opt-shanchu",color:"#60666b"}),s[4]||(s[4]=r("span",null,"批量删除",-1))])),_:1,__:[4]})])),[[ie,["DELETE_APP"]]])])),_:1})])),default:v((()=>[h(o,{class:"border-radius"},{default:v((()=>[s[5]||(s[5]=m(" 更多 ")),h(ae)])),_:1,__:[5]})])),_:1})])),_:1})])])]),r("div",N,[r("div",O,[h(ne,{columns:a.value,where:Z.value,showToolTotal:!1,showTableTool:"",rowId:"appId",ref_key:"tableRef",ref:t,url:"/sysApp/page",fieldBusinessCode:"APP_TABLE"},{toolLeft:v((()=>[h(te,{value:Z.value.searchText,"onUpdate:value":s[1]||(s[1]=e=>Z.value.searchText=e),placeholder:"应用名称、编码（回车搜索）",onPressEnter:X,bordered:!1,style:{width:"240px"},class:"search-input"},{prefix:v((()=>[h(J,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:v((({column:e,record:a})=>["appName"==e.dataIndex?(u(),p("a",{key:0,onClick:e=>Y(a)},w(a.appName),9,q)):x("",!0),"appIconWrapper"==e.dataIndex?(u(),p("img",{key:1,src:a.appIconWrapper,alt:"",class:"appIconWrapper"},null,8,M)):x("",!0),"statusFlag"==e.dataIndex?(u(),g(se,{key:2,modelValue:a.statusFlag,"onUpdate:modelValue":e=>a.statusFlag=e,"open-value":1,"close-value":2,onChange:e=>(e=>{E.updateStatus({appId:e.appId,statusFlag:e.statusFlag}).then((e=>{I.success(e.message)}))})(a),disabled:K.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])):x("",!0),"action"==e.key?(u(),g(n,{key:3,size:16},{default:v((()=>[y(h(J,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>Y(a)},null,8,["onClick"]),[[ie,["EDIT_APP"]]]),y(h(J,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{_.confirm({title:"提示",content:"确定要删除选中的应用吗?",icon:h(f),maskClosable:!0,onOk:async()=>{const a=await E.delete({appId:e.appId});I.success(a.message),X()}})})(a)},null,8,["onClick"]),[[ie,["DELETE_APP"]]])])),_:2},1024)):x("",!0)])),_:1},8,["columns","where"])])])])])])]),H.value?(u(),g(P,{key:0,visible:H.value,"onUpdate:visible":s[2]||(s[2]=e=>H.value=e),data:G.value,onDone:X},null,8,["visible","data"])):x("",!0)])}}}),[["__scopeId","data-v-ecda94dc"]]))}}}));
