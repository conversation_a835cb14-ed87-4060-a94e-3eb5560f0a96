package cn.stylefeng.roses.kernel.erp.modular.region.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.modular.region.service.ErpRegionService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 区域管理控制器
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
@RestController
@ApiResource(name = "区域管理", requiredPermission = true, requirePermissionCode = "ERP_REGION")
public class ErpRegionController {

    @Resource
    private ErpRegionService erpRegionService;

    /**
     * 新增区域
     */
    @PostResource(name = "新增区域", path = "/erp/region/add")
    public ResponseData<?> add(@RequestBody @Validated(ErpRegionRequest.add.class) ErpRegionRequest erpRegionRequest) {
        erpRegionService.add(erpRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除区域
     */
    @PostResource(name = "删除区域", path = "/erp/region/delete")
    public ResponseData<?> delete(@RequestBody @Validated(ErpRegionRequest.delete.class) ErpRegionRequest erpRegionRequest) {
        erpRegionService.del(erpRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑区域
     */
    @PostResource(name = "编辑区域", path = "/erp/region/edit")
    public ResponseData<?> edit(@RequestBody @Validated(ErpRegionRequest.edit.class) ErpRegionRequest erpRegionRequest) {
        erpRegionService.edit(erpRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查询区域详情
     */
    @GetResource(name = "查询区域详情", path = "/erp/region/detail")
    public ResponseData<ErpRegionResponse> detail(@Validated(ErpRegionRequest.detail.class) ErpRegionRequest erpRegionRequest) {
        ErpRegionResponse response = erpRegionService.detail(erpRegionRequest);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询区域列表
     */
    @GetResource(name = "分页查询区域列表", path = "/erp/region/page", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.PAGE_REGION)
    public ResponseData<PageResult<ErpRegionResponse>> page(ErpRegionRequest erpRegionRequest) {
        PageResult<ErpRegionResponse> pageResult = erpRegionService.findPage(erpRegionRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询区域列表
     */
    @GetResource(name = "查询区域列表", path = "/erp/region/list", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.LIST_REGION)
    public ResponseData<List<ErpRegionResponse>> list(ErpRegionRequest erpRegionRequest) {
        List<ErpRegionResponse> responseList = erpRegionService.findList(erpRegionRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 查询区域树形结构
     */
    @GetResource(name = "查询区域树形结构", path = "/erp/region/tree", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.TREE_REGION)
    public ResponseData<List<ErpRegionResponse>> tree(ErpRegionRequest erpRegionRequest) {
        List<ErpRegionResponse> responseList = erpRegionService.findTree(erpRegionRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 更新区域状态
     */
    @PostResource(name = "更新区域状态", path = "/erp/region/updateStatus")
    public ResponseData<?> updateStatus(@RequestBody @Validated(ErpRegionRequest.updateStatus.class) ErpRegionRequest erpRegionRequest) {
        erpRegionService.updateStatus(erpRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 校验区域编码是否重复
     */
    @GetResource(name = "校验区域编码", path = "/erp/region/validateCode")
    public ResponseData<Boolean> validateCode(ErpRegionRequest erpRegionRequest) {
        boolean isRepeat = erpRegionService.validateRegionCodeRepeat(
                erpRegionRequest.getRegionCode(),
                erpRegionRequest.getRegionId()
        );
        return new SuccessResponseData<>(!isRepeat);
    }

    /**
     * 按父级ID分页查询子区域列表
     */
    @GetResource(name = "按父级ID分页查询子区域列表", path = "/erp/region/listByParent")
    public ResponseData<PageResult<ErpRegionResponse>> listByParent(ErpRegionRequest erpRegionRequest) {
        PageResult<ErpRegionResponse> pageResult = erpRegionService.findPageByParent(erpRegionRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 获取区域树形结构（支持懒加载）
     */
    @GetResource(name = "获取区域树形结构（懒加载）", path = "/erp/region/treeWithLazy")
    public ResponseData<List<ErpRegionResponse>> treeWithLazy(ErpRegionRequest erpRegionRequest) {
        List<ErpRegionResponse> responseList = erpRegionService.findTreeWithLazy(erpRegionRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 批量删除区域
     */
    @PostResource(name = "批量删除区域", path = "/erp/region/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody @Validated(ErpRegionRequest.batchDelete.class) ErpRegionRequest erpRegionRequest) {
        erpRegionService.batchDelete(erpRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 获取区域选择器数据（用于下拉选择）
     */
    @GetResource(name = "获取区域选择器数据", path = "/erp/region/selector")
    public ResponseData<List<ErpRegionResponse>> selector(ErpRegionRequest erpRegionRequest) {
        List<ErpRegionResponse> responseList = erpRegionService.findSelector(erpRegionRequest);
        return new SuccessResponseData<>(responseList);
    }
    
    /**
     * 获取区域关联的供应商和客户数量
     */
    @GetResource(name = "获取区域关联数量", path = "/erp/region/relationCount")
    public ResponseData<ErpRegionResponse> getRegionRelationCount(@Validated(ErpRegionRequest.detail.class) ErpRegionRequest erpRegionRequest) {
        ErpRegionResponse response = erpRegionService.getRegionRelationCount(erpRegionRequest);
        return new SuccessResponseData<>(response);
    }

}
