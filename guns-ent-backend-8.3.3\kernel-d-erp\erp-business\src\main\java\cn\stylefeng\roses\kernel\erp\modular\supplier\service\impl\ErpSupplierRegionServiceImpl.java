package cn.stylefeng.roses.kernel.erp.modular.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpRegionConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpSupplierExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpRegion;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplierRegion;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierRegionResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.modular.region.service.ErpRegionService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.mapper.ErpSupplierMapper;
import cn.stylefeng.roses.kernel.erp.modular.supplier.mapper.ErpSupplierRegionMapper;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierRegionService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 供应商-区域关联Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/22 16:00
 */
@Service
public class ErpSupplierRegionServiceImpl extends ServiceImpl<ErpSupplierRegionMapper, ErpSupplierRegion> implements ErpSupplierRegionService {

    @Resource
    private ErpSupplierRegionMapper erpSupplierRegionMapper;

    @Resource
    private ErpSupplierMapper erpSupplierMapper;

    @Resource
    private ErpRegionService erpRegionService;

    @Override
    public List<ErpRegionResponse> getSupplierRegions(ErpSupplierRegionRequest erpSupplierRegionRequest) {
        // 校验供应商是否存在
        this.checkSupplierExists(erpSupplierRegionRequest.getSupplierId());

        // 查询供应商关联的区域ID列表
        List<Long> regionIds = erpSupplierRegionMapper.getRegionIdsBySupplierId(erpSupplierRegionRequest.getSupplierId());

        if (CollUtil.isEmpty(regionIds)) {
            return new ArrayList<>();
        }

        // 查询区域详细信息
        List<ErpRegionResponse> regionList = new ArrayList<>();
        for (Long regionId : regionIds) {
            ErpRegionRequest regionRequest = new ErpRegionRequest();
            regionRequest.setRegionId(regionId);
            ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);
            regionList.add(regionResponse);
        }

        return regionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierRegions(ErpSupplierRegionRequest erpSupplierRegionRequest) {
        Long supplierId = erpSupplierRegionRequest.getSupplierId();
        List<Long> regionIds = erpSupplierRegionRequest.getRegionIds();

        // 校验供应商是否存在
        this.checkSupplierExists(supplierId);

        // 校验区域是否存在
        this.checkRegionsExist(regionIds);

        // 删除原有关联
        LambdaQueryWrapper<ErpSupplierRegion> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(ErpSupplierRegion::getSupplierId, supplierId);
        this.remove(deleteWrapper);

        // 添加新的关联
        if (CollUtil.isNotEmpty(regionIds)) {
            List<ErpSupplierRegion> supplierRegionList = new ArrayList<>();
            Long currentUserId = LoginContext.me().getLoginUser().getUserId();
            Date now = new Date();

            for (Long regionId : regionIds) {
                ErpSupplierRegion supplierRegion = new ErpSupplierRegion();
                supplierRegion.setSupplierId(supplierId);
                supplierRegion.setRegionId(regionId);
                supplierRegion.setCreateUser(currentUserId);
                supplierRegion.setCreateTime(now);
                supplierRegionList.add(supplierRegion);
            }

            this.saveBatch(supplierRegionList);
        }
    }

    @Override
    public PageResult<ErpSupplierResponse> getSuppliersByRegion(ErpSupplierRegionRequest erpSupplierRegionRequest) {
        Long regionId = erpSupplierRegionRequest.getRegionId();

        // 校验区域是否存在
        this.checkRegionExists(regionId);

        // 查询区域信息，获取区域路径
        ErpRegionRequest regionRequest = new ErpRegionRequest();
        regionRequest.setRegionId(regionId);
        ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);

        // 是否包含子区域
        Boolean includeChildRegions = erpSupplierRegionRequest.getIncludeChildRegions() != null ? 
                erpSupplierRegionRequest.getIncludeChildRegions() : true;
        List<Long> childRegionIds = new ArrayList<>();
        childRegionIds.add(regionId);

        // 如果包含子区域，查询所有子区域ID
        if (includeChildRegions) {
            // 查询所有区域
            ErpRegionRequest allRegionRequest = new ErpRegionRequest();
            List<ErpRegionResponse> allRegions = erpRegionService.findList(allRegionRequest);

            // 找出所有子区域
            for (ErpRegionResponse region : allRegions) {
                if (region.getRegionPath() != null && region.getRegionPath().startsWith(regionResponse.getRegionPath() + "/")) {
                    childRegionIds.add(region.getRegionId());
                }
            }
        }

        // 分页查询
        Page<ErpSupplierResponse> page = PageFactory.defaultPage();
        List<ErpSupplierResponse> supplierList = erpSupplierRegionMapper.getSuppliersByRegionId(
                page, regionId, includeChildRegions, childRegionIds);

        // 填充供应商扩展信息
        for (ErpSupplierResponse supplier : supplierList) {
            this.fillSupplierExtInfo(supplier);
        }

        return PageResultFactory.createPageResult(supplierList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public Long countSuppliersByRegion(ErpSupplierRegionRequest erpSupplierRegionRequest) {
        Long regionId = erpSupplierRegionRequest.getRegionId();

        // 校验区域是否存在
        this.checkRegionExists(regionId);

        // 查询区域信息，获取区域路径
        ErpRegionRequest regionRequest = new ErpRegionRequest();
        regionRequest.setRegionId(regionId);
        ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);

        // 是否包含子区域
        Boolean includeChildRegions = erpSupplierRegionRequest.getIncludeChildRegions() != null ? 
                erpSupplierRegionRequest.getIncludeChildRegions() : true;
        List<Long> childRegionIds = new ArrayList<>();
        childRegionIds.add(regionId);

        // 如果包含子区域，查询所有子区域ID
        if (includeChildRegions) {
            // 查询所有区域
            ErpRegionRequest allRegionRequest = new ErpRegionRequest();
            List<ErpRegionResponse> allRegions = erpRegionService.findList(allRegionRequest);

            // 找出所有子区域
            for (ErpRegionResponse region : allRegions) {
                if (region.getRegionPath() != null && region.getRegionPath().startsWith(regionResponse.getRegionPath() + "/")) {
                    childRegionIds.add(region.getRegionId());
                }
            }
        }

        // 统计供应商数量
        return erpSupplierRegionMapper.countSuppliersByRegionId(regionId, includeChildRegions, childRegionIds);
    }

    @Override
    public void fillSupplierRegionInfo(ErpSupplierResponse response) {
        try {
            ErpSupplierRegionRequest regionRequest = new ErpSupplierRegionRequest();
            regionRequest.setSupplierId(response.getSupplierId());
            List<ErpRegionResponse> regions = this.getSupplierRegions(regionRequest);

            if (regions != null && !regions.isEmpty()) {
                // 设置区域ID列表（Long类型）
                List<Long> regionIds = regions.stream()
                        .map(ErpRegionResponse::getRegionId)
                        .collect(Collectors.toList());
                response.setRegionIds(regionIds);

                // 设置完整的区域信息列表
                response.setRegionList(regions);
            }
        } catch (Exception e) {
            // 如果获取区域信息失败，记录日志但不影响主流程
            log.warn("获取供应商区域信息失败: supplierId={"+response.getSupplierId()+"}, error={"+e.getMessage()+"}");
        }
    }

    /**
     * 校验供应商是否存在
     */
    private void checkSupplierExists(Long supplierId) {
        ErpSupplier supplier = erpSupplierMapper.selectById(supplierId);
        if (ObjectUtil.isNull(supplier)) {
            throw new ServiceException(ErpSupplierExceptionEnum.SUPPLIER_NOT_EXIST);
        }
    }

    /**
     * 校验区域是否存在
     */
    private void checkRegionExists(Long regionId) {
        ErpRegionRequest regionRequest = new ErpRegionRequest();
        regionRequest.setRegionId(regionId);
        try {
            erpRegionService.detail(regionRequest);
        } catch (Exception e) {
            throw new ServiceException("erp-region-module", "区域不存在", "区域ID: " + regionId + " 不存在");
        }
    }

    /**
     * 校验多个区域是否存在
     */
    private void checkRegionsExist(List<Long> regionIds) {
        if (CollUtil.isEmpty(regionIds)) {
            return;
        }

        for (Long regionId : regionIds) {
            this.checkRegionExists(regionId);
        }
    }

    /**
     * 填充供应商扩展信息
     */
    private void fillSupplierExtInfo(ErpSupplierResponse supplier) {
        // 填充供应商类型名称
        if ("ENTERPRISE".equals(supplier.getSupplierType())) {
            supplier.setSupplierTypeName("企业");
        } else if ("INDIVIDUAL".equals(supplier.getSupplierType())) {
            supplier.setSupplierTypeName("个体");
        }

        // 填充信用等级名称
        switch (supplier.getCreditLevel()) {
            case "A":
                supplier.setCreditLevelName("优秀");
                break;
            case "B":
                supplier.setCreditLevelName("良好");
                break;
            case "C":
                supplier.setCreditLevelName("一般");
                break;
            case "D":
                supplier.setCreditLevelName("较差");
                break;
            default:
                supplier.setCreditLevelName("未知");
                break;
        }

        // 填充状态名称
        switch (supplier.getStatus()) {
            case "ACTIVE":
                supplier.setStatusName("正常");
                break;
            case "INACTIVE":
                supplier.setStatusName("停用");
                break;
            case "BLACKLIST":
                supplier.setStatusName("黑名单");
                break;
            default:
                supplier.setStatusName("未知");
                break;
        }

        // 填充区域信息
        if (ObjectUtil.isNotNull(supplier.getRegionId())) {
            ErpRegionRequest regionRequest = new ErpRegionRequest();
            regionRequest.setRegionId(supplier.getRegionId());
            try {
                ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);
                supplier.setRegionName(regionResponse.getRegionName());
            } catch (Exception e) {
                // 忽略异常，区域可能已被删除
                supplier.setRegionName("未知区域");
            }
        }

        // 查询供应商关联的区域
        List<Long> regionIds = erpSupplierRegionMapper.getRegionIdsBySupplierId(supplier.getSupplierId());
        supplier.setRegionIds(regionIds);

        if (CollUtil.isNotEmpty(regionIds)) {
            List<ErpRegionResponse> regionList = new ArrayList<>();
            for (Long regionId : regionIds) {
                ErpRegionRequest regionRequest = new ErpRegionRequest();
                regionRequest.setRegionId(regionId);
                try {
                    ErpRegionResponse regionResponse = erpRegionService.detail(regionRequest);
                    regionList.add(regionResponse);
                } catch (Exception e) {
                    // 忽略异常，区域可能已被删除
                }
            }
            supplier.setRegionList(regionList);
        }
    }
}