package cn.stylefeng.roses.kernel.micro.feign.example.modular;

import cn.hutool.core.lang.Dict;
import cn.stylefeng.roses.kernel.micro.api.FeignExampleApi;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 示例provider
 *
 * <AUTHOR>
 * @date 2021/5/17 14:49
 */
@RestController
public class ExampleProviderController implements FeignExampleApi {

    @Override
    public ResponseData<?> success(@RequestBody Dict dict) {
        return new SuccessResponseData<>();
    }

    @Override
    public ResponseData<?> error(@RequestBody Dict dict) {
        throw new ServiceException("module-name", "500", "请求错误");
    }

}
