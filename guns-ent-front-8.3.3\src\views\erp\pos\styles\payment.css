/* 支付面板样式 */

/* 支付模态框 */
.payment-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.payment-modal .ant-modal-header {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.payment-modal .ant-modal-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.payment-modal .ant-modal-close {
  color: #fff;
  opacity: 0.8;
}

.payment-modal .ant-modal-close:hover {
  opacity: 1;
}

.payment-modal .ant-modal-body {
  padding: 24px;
}

/* 订单信息区域 */
.order-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.order-summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.order-item-name {
  font-size: 14px;
  color: #262626;
  flex: 1;
}

.order-item-quantity {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0 12px;
}

.order-item-price {
  font-size: 14px;
  font-weight: 600;
  color: #ff4d4f;
  font-family: 'Courier New', monospace;
}

.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  margin-top: 12px;
  border-top: 2px solid #e8e8e8;
  font-size: 16px;
  font-weight: 600;
}

.order-total-label {
  color: #262626;
}

.order-total-amount {
  color: #ff4d4f;
  font-size: 18px;
  font-family: 'Courier New', monospace;
}

/* 支付方式选择 */
.payment-methods {
  margin-bottom: 24px;
}

.payment-methods-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

.payment-method-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.payment-method-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.payment-method-item:hover {
  border-color: #1890ff;
  background: #f0f9ff;
}

.payment-method-item.selected {
  border-color: #1890ff;
  background: #e6f7ff;
}

.payment-method-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #1890ff;
}

.payment-method-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  text-align: center;
}

/* 支付金额输入 */
.payment-amount {
  margin-bottom: 24px;
}

.payment-amount-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

.amount-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.amount-input {
  flex: 1;
  height: 48px;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  font-family: 'Courier New', monospace;
}

.quick-amount-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.quick-amount-btn {
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #666;
  transition: all 0.3s ease;
}

.quick-amount-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 找零显示 */
.change-display {
  background: #f0f9ff;
  border: 1px solid #e6f7ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.change-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.change-amount {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  font-family: 'Courier New', monospace;
}

.change-amount.negative {
  color: #ff4d4f;
}

/* 支付按钮 */
.payment-actions {
  display: flex;
  gap: 12px;
}

.payment-cancel-btn {
  flex: 1;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
}

.payment-confirm-btn {
  flex: 2;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border: none;
  color: #fff;
  transition: all 0.3s ease;
}

.payment-confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #389e0d 0%, #52c41a 100%);
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.payment-confirm-btn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-modal .ant-modal-body {
    padding: 16px;
  }

  .payment-method-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-amount-buttons {
    grid-template-columns: repeat(3, 1fr);
  }

  .payment-actions {
    flex-direction: column;
  }

  .payment-cancel-btn,
  .payment-confirm-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .payment-modal .ant-modal-header {
    padding: 16px 20px;
  }

  .payment-modal .ant-modal-body {
    padding: 12px;
  }

  .payment-method-grid {
    grid-template-columns: 1fr;
  }

  .quick-amount-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .amount-input {
    height: 44px;
    font-size: 16px;
  }

  .change-amount {
    font-size: 20px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .payment-method-item:hover {
    border-color: #f0f0f0;
    background: #fff;
  }

  .payment-method-item.selected:hover {
    border-color: #1890ff;
    background: #e6f7ff;
  }

  .quick-amount-btn:hover {
    border-color: #d9d9d9;
    color: #666;
  }

  .payment-confirm-btn:hover:not(:disabled) {
    transform: none;
    box-shadow: none;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .payment-method-item,
  .quick-amount-btn,
  .payment-confirm-btn {
    transition: none;
  }

  .payment-confirm-btn:hover:not(:disabled) {
    transform: none;
  }
}
