<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-validator</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>validator-api</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- 数据库操作的api -->
        <!-- 如果用TableUniqueValueValidator会去查库，校验字段是否已经存在该值 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

    </dependencies>

</project>
