@import '../../../layout/style/themes/default.less';

.guns-split-panel {
  display: flex;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex: auto;
  height: 100%;
  min-height: 0px;
  position: relative;
  --guns-split-size: 252px;
  --guns-split-space: 16px;

  // 侧边容器
  & > .guns-split-panel-wrap {
    flex-shrink: 0;
    box-sizing: border-box;
    width: calc(var(--guns-split-size) + var(--guns-split-space));
    display: flex;
    justify-content: flex-end;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;

    // 侧边
    & > .guns-split-panel-side {
      flex-shrink: 0;
      display: flex;
      width: 100%;
      box-sizing: border-box;
      position: relative;
    }

    // 间距
    & > .guns-split-panel-space {
      flex-shrink: 0;
      width: var(--guns-split-space);
      box-sizing: border-box;
      position: relative;

      // 拉伸线
      .guns-split-resize-line {
        width: 12px;
        height: 100%;
        position: absolute;
        left: -6px;
        z-index: 4;
        cursor: e-resize;

        &::after {
          content: '';
          width: 3px;
          height: 100%;
          display: block;
          margin: 0 auto;
        }

        &:hover::after {
          background: @primary-color;
        }
      }
    }
  }

  // 内容
  & > .guns-split-panel-body {
    flex: 1;
    display: flex;
    overflow: auto;
    box-sizing: border-box;
    position: relative;
  }

  // 折叠按钮
  & > .guns-split-collapse-button {
    width: 12px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    position: absolute;
    left: var(--guns-split-size);
    top: 50%;
    margin-top: -24px;
    border-radius: 0 4px 4px 0;
    background: @border-color-split;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    z-index: 3;

    .guns-split-collapse-icon {
      font-size: 12px;
      color: @text-color-secondary;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: scaleX(1);
    }

    &:hover {
      background: @border-color-base;
    }
  }

  // 折叠状态
  &.is-collapse {
    & > .guns-split-panel-wrap {
      width: 0 !important;
      pointer-events: none;
      opacity: 0;
    }

    & > .guns-split-collapse-button {
      left: 0;

      .guns-split-collapse-icon {
        transform: scaleX(-1);
      }
    }
  }

  // 垂直
  &.is-vertical {
    flex-direction: column;

    & > .guns-split-panel-wrap {
      flex-direction: column;
      height: calc(var(--guns-split-size) + var(--guns-split-space));
      width: auto;

      & > .guns-split-panel-side {
        height: var(--guns-split-size);
        width: auto;
      }

      & > .guns-split-panel-space {
        height: var(--guns-split-space);
        width: auto;

        .guns-split-resize-line {
          width: 100%;
          height: 12px;
          left: auto;
          top: -6px;
          cursor: n-resize;

          &::after {
            width: 100%;
            height: 3px;
            margin: 4px 0 0 0;
          }
        }
      }
    }

    & > .guns-split-collapse-button {
      width: 48px;
      height: 14px;
      line-height: 14px;
      top: var(--guns-split-size);
      left: 50%;
      margin-left: -24px;
      margin-top: 0;
      border-radius: 0 0 4px 4px;

      .guns-split-collapse-icon {
        transform: scaleY(1);
      }
    }

    &.is-collapse {
      & > .guns-split-panel-wrap {
        width: auto !important;
        height: 0 !important;
      }

      & > .guns-split-collapse-button {
        top: 0;

        .guns-split-collapse-icon {
          transform: scaleY(-1);
        }
      }
    }
  }

  // 反向
  &.is-reverse {
    flex-direction: row-reverse;

    & > .guns-split-panel-wrap {
      flex-direction: row-reverse;

      & > .guns-split-panel-space .guns-split-resize-line {
        left: auto;
        right: -6px;
      }
    }

    & > .guns-split-collapse-button {
      left: auto;
      right: var(--guns-split-size);
      border-radius: 4px 0 0 4px;

      .guns-split-collapse-icon {
        transform: scaleX(-1);
      }
    }

    &.is-collapse > .guns-split-collapse-button {
      right: 0;

      .guns-split-collapse-icon {
        transform: scaleX(1);
      }
    }

    &.is-vertical {
      flex-direction: column-reverse;

      & > .guns-split-panel-wrap {
        flex-direction: column-reverse;

        & > .guns-split-panel-space .guns-split-resize-line {
          top: auto;
          right: auto;
          bottom: -6px;
        }
      }

      & > .guns-split-collapse-button {
        left: 50%;
        top: auto;
        bottom: var(--guns-split-size);
        border-radius: 4px 4px 0 0;

        .guns-split-collapse-icon {
          transform: scaleY(-1);
        }
      }

      &.is-collapse > .guns-split-collapse-button {
        bottom: 0;

        .guns-split-collapse-icon {
          transform: scaleY(1);
        }
      }
    }
  }

  // 拉伸状态
  &.is-resizing {
    user-select: none;

    & > .guns-split-panel-wrap,
    & > .guns-split-collapse-button {
      transition: none;
    }
  }

  // 遮罩层
  .guns-split-panel-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    display: none;
    z-index: 2;
  }
}

/* 小屏幕样式 */
@media screen and (max-width: 768px) {
  .guns-split-panel.is-responsive:not(.is-vertical) {
    &:not(.is-collapse) {
      overflow: hidden !important;
    }

    & > .guns-split-panel-wrap {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;

      & > .guns-split-panel-side {
        background: @component-background;
        z-index: 3;
      }
    }

    & > .guns-split-panel-body {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    & > .guns-split-panel-mask {
      display: block;
    }

    &:not(.is-collapse) {
      & > .guns-split-panel-mask {
        left: var(--guns-split-size);
        background: @modal-mask-bg;
        pointer-events: all;
      }

      & > .guns-split-panel-body {
        transform: translateX(calc(var(--guns-split-size) + var(--guns-split-space)));
        z-index: 1;
      }
    }

    // 反向
    &.is-reverse {
      & > .guns-split-panel-wrap {
        right: 0;
        left: auto;
      }

      &:not(.is-collapse) {
        & > .guns-split-panel-mask {
          left: 0;
          right: var(--guns-split-size);
        }

        & > .guns-split-panel-body {
          transform: translateX(calc(0px - var(--guns-split-size) - var(--guns-split-space)));
        }
      }
    }
  }
}
