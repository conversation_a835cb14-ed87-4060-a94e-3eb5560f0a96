package cn.stylefeng.roses.kernel.devops.starter.cache;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.stylefeng.roses.kernel.ca.api.pojo.CaClientInfo;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import cn.stylefeng.roses.kernel.ca.server.core.loginuser.logincode.MemoryLoginCodeCache;
import cn.stylefeng.roses.kernel.ca.server.core.session.cache.clienttoken.MemoryClientTokenCache;
import cn.stylefeng.roses.kernel.ca.server.core.session.cache.logintoken.MemoryCaLoginTokenCache;
import cn.stylefeng.roses.kernel.ca.server.core.session.cache.loginuser.MemoryCaLoginUserCache;
import cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi;
import cn.stylefeng.roses.kernel.cache.api.constants.CacheConstants;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Set;

/**
 * 统一认证服务端的配置（基于内存缓存）
 *
 * <AUTHOR>
 * @date 2022/11/16 20:26
 */
@Configuration
@ConditionalOnMissingClass("org.springframework.data.redis.connection.RedisConnectionFactory")
public class CaServerMemoryCacheAutoConfiguration {

    /**
     * loginCode缓存
     * <p>
     * key：    SSO LoginCode，是一个临时的密码校验成功的一个凭证
     * value：  CaLoginUser，单点登录用户信息的一个标识
     *
     * <AUTHOR>
     * @date 2021/1/29 10:10
     */
    @Bean("loginCodeCache")
    public CacheOperatorApi<CaLoginUser> memoryLoginCodeCache() {
        TimedCache<String, CaLoginUser> timedCache = CacheUtil.newTimedCache(CacheConstants.NONE_EXPIRED_TIME);
        return new MemoryLoginCodeCache(timedCache);
    }

    /**
     * 存放token的缓存
     * <p>
     * key：    用户id
     * value：  用户的所有CAID，用户在一个机器登录一次单点就会有一个CAID
     *
     * <AUTHOR>
     * @date 2021/1/29 10:10
     */
    @Bean("caLoginTokenCache")
    public CacheOperatorApi<Set<String>> memoryCaLoginTokenCache() {
        TimedCache<String, Set<String>> timedCache = CacheUtil.newTimedCache(CacheConstants.NONE_EXPIRED_TIME);
        return new MemoryCaLoginTokenCache(timedCache);
    }

    /**
     * 存放当前登录用户的缓存
     * <p>
     * key：    CAID
     * value：  CAID对用的用户登录信息
     *
     * <AUTHOR>
     * @date 2021/1/29 10:10
     */
    @Bean("caLoginUserCache")
    public CacheOperatorApi<CaLoginUser> memoryCaLoginUserCache() {
        TimedCache<String, CaLoginUser> timedCache = CacheUtil.newTimedCache(CacheConstants.NONE_EXPIRED_TIME);
        return new MemoryCaLoginUserCache(timedCache);
    }

    /**
     * 本缓存为了存用户一次登录后，单点过的所有业务系统的token
     * <p>
     * key：   CAID，用户单点成功的后在单点服务器的唯一标识
     * value： 用户所有在业务端单点的token，一般这个缓存是用在单点退出时候清除业务缓存
     *
     * <AUTHOR>
     * @date 2022/5/20 10:17
     */
    @Bean("clientTokenCache")
    public CacheOperatorApi<Set<CaClientInfo>> memoryClientTokenCache() {
        TimedCache<String, Set<CaClientInfo>> timedCache = CacheUtil.newTimedCache(CacheConstants.NONE_EXPIRED_TIME);
        return new MemoryClientTokenCache(timedCache);
    }

}
