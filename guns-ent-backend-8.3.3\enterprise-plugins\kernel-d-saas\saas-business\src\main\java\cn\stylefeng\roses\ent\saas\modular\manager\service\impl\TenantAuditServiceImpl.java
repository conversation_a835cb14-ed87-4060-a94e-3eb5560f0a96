package cn.stylefeng.roses.ent.saas.modular.manager.service.impl;

import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.manager.enums.TenantExceptionEnum;
import cn.stylefeng.roses.ent.saas.modular.manager.factory.TenantAuditFactory;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantAuditRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantAuditService;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantService;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 租户审核业务
 *
 * <AUTHOR>
 * @since 2024-02-22 19:33
 */
@Service
public class TenantAuditServiceImpl implements TenantAuditService {

    @Resource
    private TenantService tenantService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditTenant(TenantAuditRequest tenantAuditRequest) {

        // 获取租户id
        List<Long> tenantIdList = tenantAuditRequest.getTenantIdList();

        // 遍历每个租户，分别开通租户的功能
        for (Long tenantId : tenantIdList) {

            // 更新租户的基本信息
            Tenant tenant = this.updateBaseInfo(tenantId, tenantAuditRequest);

            // 初始化一个租户请求类，填充初始化租户时候的参数
            TenantRequest tenantRequest = TenantAuditFactory.createInitTenantRequest(tenant, tenantAuditRequest);

            // 初始化租户
            tenantService.initTenant(tenant.getTenantId(), tenantRequest, tenant.getPassword(), tenant.getPasswordSalt());
        }

    }

    /**
     * 更新租户的基本信息
     *
     * <AUTHOR>
     * @since 2024-02-22 19:53
     */
    private Tenant updateBaseInfo(Long tenantId, TenantAuditRequest tenantAuditRequest) {

        Tenant tenant = tenantService.getById(tenantId);

        if (tenant == null) {
            throw new ServiceException(TenantExceptionEnum.TENANT_NOT_EXISTED);
        }

        // 更新租户状态为启用
        tenant.setStatusFlag(StatusEnum.ENABLE.getCode());

        // 更新租户状态为激活
        tenant.setActiveFlag(YesOrNotEnum.Y.getCode());

        // 更新租户的开通日期和到期日期
        tenant.setActiveDate(tenantAuditRequest.getActiveDate());
        tenant.setExpireDate(tenantAuditRequest.getExpireDate());

        this.tenantService.updateById(tenant);

        return tenant;
    }

}