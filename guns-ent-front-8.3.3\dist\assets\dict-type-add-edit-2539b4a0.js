import{R as i,r as d,o as b,cb as g,a as T,f as S,w as x,d as B,m as M,M as R}from"./index-18a1ea24.js";import U from"./dict-type-form-84b780cc.js";class u{static add(e){return i.post("/dictType/add",e)}static edit(e){return i.post("/dictType/edit",e)}static delete(e){return i.post("/dictType/delete",e)}static detail(e){return i.getAndLoadData("/dictType/detail",e)}static list(e){return i.getAndLoadData("/dictType/list",e)}}const h={__name:"dict-type-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(p,{emit:e}){const n=p,f=e,o=d(!1),l=d(!1),a=d({statusFlag:1,dictTypeClass:1}),v=d(null);b(async()=>{n.data?(l.value=!0,y()):(a.value.dictTypeSort=await g("SYSTEM_BASE_DICT_TYPE"),l.value=!1)});const y=()=>{u.detail({dictTypeId:n.data.dictTypeId}).then(s=>{a.value=Object.assign({},s)})},r=s=>{f("update:visible",s)},_=async()=>{v.value.$refs.formRef.validate().then(async s=>{if(s){o.value=!0;let t=null;l.value?t=u.edit(a.value):t=u.add(a.value),t.then(async c=>{o.value=!1,M.success(c.message),r(!1),f("done")}).catch(()=>{o.value=!1})}})};return(s,t)=>{const c=R;return T(),S(c,{width:700,maskClosable:!1,visible:n.visible,"confirm-loading":o.value,forceRender:!0,title:l.value?"\u7F16\u8F91\u5B57\u5178\u7C7B\u578B":"\u65B0\u5EFA\u5B57\u5178\u7C7B\u578B","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":r,onOk:_,onClose:t[1]||(t[1]=m=>r(!1))},{default:x(()=>[B(U,{form:a.value,"onUpdate:form":t[0]||(t[0]=m=>a.value=m),ref_key:"typeFormRef",ref:v,isUpdate:l.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}},A=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));export{u as S,h as _,A as d};
