System.register(["./index-legacy-ee1db0c7.js","./tenant-form-legacy-6fd2a4d3.js","./TenantApi-legacy-40853d2f.js","./time-util-legacy-296598c1.js","./FileApi-legacy-f85a3060.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./index-legacy-198191c1.js"],(function(e,t){"use strict";var a,l,n,i,s,o,d,u,v,c,r,f,g;return{setters:[e=>{a=e.r,l=e.o,n=e.a,i=e.f,s=e.w,o=e.d,d=e.m,u=e.M},e=>{v=e.default},e=>{c=e.T},e=>{r=e.g,f=e.G},e=>{g=e.a},null,null,null],execute:function(){e("default",{__name:"tenant-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:t}){const m=e,p=t,y=a(!1),L=a(!1),b=a({iconList:[],statusFlag:1,activeDate:r()+" 00:00:00",expireDate:f(r(),1)+" 00:00:00",tenantLinkList:[]}),j=a(null);l((()=>{m.data?(L.value=!0,h()):L.value=!1}));const h=()=>{c.detail({tenantId:m.data.tenantId}).then((e=>{b.value=Object.assign({},e),b.value.iconList=[],x("iconList",e.tenantLogo),b.value.tenantLinkList||(b.value.tenantLinkList=[])}))},x=(e,t)=>{g.getAntdVInfo({fileId:t}).then((a=>{a.uid=t,b.value[e]=[a]}))},k=e=>{p("update:visible",e)},I=async()=>{j.value.$refs.formRef.validate().then((async e=>{if(e){var t,a,l,n;null!==(t=b.value.iconList)&&void 0!==t&&t.length&&(b.value.tenantLogo=null!==(a=b.value.iconList[0])&&void 0!==a&&null!==(a=a.response)&&void 0!==a&&null!==(a=a.data)&&void 0!==a&&a.fileId?null===(l=b.value.iconList[0])||void 0===l||null===(l=l.response)||void 0===l||null===(l=l.data)||void 0===l?void 0:l.fileId:null===(n=b.value.iconList[0])||void 0===n?void 0:n.uid),y.value=!0;let e=null;e=L.value?c.edit(b.value):c.add(b.value),e.then((async e=>{y.value=!1,d.success(e.message),k(!1),p("done")})).catch((()=>{y.value=!1}))}}))};return(e,t)=>{const a=u;return n(),i(a,{width:900,maskClosable:!1,visible:m.visible,"confirm-loading":y.value,forceRender:!0,title:L.value?"编辑租户":"新建租户","body-style":{paddingBottom:"8px"},"onUpdate:visible":k,onOk:I,class:"common-modal",onClose:t[1]||(t[1]=e=>k(!1))},{default:s((()=>[o(v,{form:b.value,"onUpdate:form":t[0]||(t[0]=e=>b.value=e),ref_key:"tenantFormRef",ref:j,isUpdate:L.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
