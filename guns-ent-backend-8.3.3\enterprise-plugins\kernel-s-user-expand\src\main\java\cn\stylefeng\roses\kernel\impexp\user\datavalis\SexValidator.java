package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.rule.enums.SexEnum;

/**
 * 性别的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 22:46
 */
public class SexValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(false, originValue, originValue, "性别不能为空");
        }

        // 如果是男
        if (SexEnum.M.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, SexEnum.M.getCode());
        }

        // 如果是女
        else if (SexEnum.F.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, SexEnum.F.getCode());
        }

        // 其他
        else {
            return new ExcelLineParseResult(false, originValue, originValue, "性别格式不对，请输入：男或女");
        }
    }

}
