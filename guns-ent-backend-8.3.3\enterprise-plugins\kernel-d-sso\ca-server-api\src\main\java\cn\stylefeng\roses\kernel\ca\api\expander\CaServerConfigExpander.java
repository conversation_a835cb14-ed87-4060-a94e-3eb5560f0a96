package cn.stylefeng.roses.kernel.ca.api.expander;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.config.api.context.ConfigContext;

/**
 * 统一认证中心相关的配置
 *
 * <AUTHOR>
 * @date 2021/1/21 17:06
 */
public class CaServerConfigExpander {

    /**
     * CA认证服务，缓存用户标识的Cookie的key名称
     *
     * <AUTHOR>
     * @date 2021/1/21 17:06
     */
    public static String getCookieName() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_COOKIE_NAME", String.class, CaServerConstants.CA_COOKIE_NAME);
    }

    /**
     * 统一认证中心保存用户信息的cookie的过期时间
     * <p>
     * 此时间一般大于CA用户会话时间，但是真正CA统一认证中心会话的过期时间以会话过期时间为主
     *
     * <AUTHOR>
     * @date 2021/1/23 16:04
     */
    public static Long getCookieExpireSeconds() {
        return ConfigContext.me()
                .getSysConfigValueWithDefault("CA_COOKIE_EXPIRED_SECONDS", Long.class, CaServerConstants.CA_COOKIE_EXPIRED_SECONDS);
    }

    /**
     * 获取cookie的domain
     *
     * <AUTHOR>
     * @date 2021/3/9 15:11
     */
    public static String getCookieDomain() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_COOKIE_DOMAIN", String.class, StrUtil.EMPTY);
    }

    /**
     * CA会话的过期时间
     *
     * <AUTHOR>
     * @date 2021/1/23 16:04
     */
    public static Long getCaSessionExpireSeconds() {
        return ConfigContext.me()
                .getSysConfigValueWithDefault("CA_SESSION_EXPIRED_SECONDS", Long.class, CaServerConstants.CA_SESSION_EXPIRED_SECONDS);
    }

    /**
     * redirect到客户端应用时，url上携带的token参数的名称
     *
     * <AUTHOR>
     * @date 2021/1/22 10:30
     */
    public static String getCallbackUrlTokenFieldName() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_CALLBACK_URL_TOKEN_FIELD_NAME", String.class,
                CaServerConstants.CA_CALLBACK_URL_TOKEN_FIELD_NAME);
    }

    /**
     * redirect到客户端应用时，url上携带的 错误码 参数的名称
     *
     * <AUTHOR>
     * @date 2021/1/22 10:30
     */
    public static String getCallbackUrlErrorCodeFieldName() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_CALLBACK_URL_ERROR_CODE_FIELD_NAME", String.class,
                CaServerConstants.CA_CALLBACK_URL_ERROR_CODE_FIELD_NAME);
    }

    /**
     * redirect到统一的登录界面的url
     *
     * <AUTHOR>
     * @date 2021/1/22 10:30
     */
    public static String getUnifyLoginUrl() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_UNIFY_LOGIN_URL", String.class, CaServerConstants.CA_UNIFY_LOGIN_URL);
    }

    /**
     * redirect到错误提示界面的url
     *
     * <AUTHOR>
     * @date 2021/1/22 10:30
     */
    public static String getErrorViewUrl() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_ERROR_VIEW_URL", String.class, CaServerConstants.CA_ERROR_VIEW_URL);
    }

    /**
     * redirect到错误提示界面携带跳转url的参数名
     *
     * <AUTHOR>
     * @date 2021/1/22 10:30
     */
    public static String getErrorViewRedirectUrlFieldName() {
        return ConfigContext.me().getSysConfigValueWithDefault("CA_ERROR_VIEW_REDIRECT_URL_FIELD_NAME", String.class,
                CaServerConstants.CA_ERROR_VIEW_REDIRECT_URL_FIELD_NAME);
    }

}
