package cn.stylefeng.roses.ent.saas.modular.auth.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.validator.api.validators.unique.TableUniqueValue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 租户-功能包封装类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantPackageRequest extends BaseRequest {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("主键id")
    private Long packageId;

    /**
     * 功能包名称
     */
    @NotBlank(message = "功能包名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("功能包名称")
    @TableUniqueValue(message = "功能包名称存在重复", groups = {add.class, edit.class},
            tableName = "ent_tenant_package",
            columnName = "package_name",
            idFieldName = "package_id",
            excludeLogicDeleteItems = true)
    private String packageName;

    /**
     * 功能包价格
     */
    @NotNull(message = "功能包价格不能为空", groups = {add.class, edit.class})
    @ChineseDescription("功能包价格")
    private BigDecimal packagePrice;

    /**
     * 批量删除用的id集合
     */
    @NotNull(message = "批量删除id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("批量删除用的id集合")
    private List<Long> batchDeleteIdList;

}
