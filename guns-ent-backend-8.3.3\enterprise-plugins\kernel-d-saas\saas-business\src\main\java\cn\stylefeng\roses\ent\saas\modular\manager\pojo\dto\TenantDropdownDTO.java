package cn.stylefeng.roses.ent.saas.modular.manager.pojo.dto;

import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.util.List;

/**
 * 租户下拉列表
 *
 * <AUTHOR>
 * @since 2024-02-23 14:09
 */
@Data
public class TenantDropdownDTO {

    /**
     * 是否是下拉方式选择租户列表
     * <p>
     * 如果是true-则是下拉，false-则是手动输入租户编码方式登录
     */
    @ChineseDescription("是否是下拉方式选择租户列表")
    private Boolean selectFlag;

    /**
     * 下拉租户列表
     */
    @ChineseDescription("下拉租户列表")
    private List<Tenant> tenantList;

}
