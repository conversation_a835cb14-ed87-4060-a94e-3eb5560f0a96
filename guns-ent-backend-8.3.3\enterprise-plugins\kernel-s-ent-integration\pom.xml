<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>enterprise-plugins</artifactId>
        <version>8.3.3</version>
    </parent>

    <artifactId>kernel-s-ent-integration</artifactId>
    <packaging>pom</packaging>

    <dependencies>

        <!-- API认证相关功能 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>api-auth-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 单点登录相关的功能 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>ca-server-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- SaaS多租户 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>saas-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 三员管理 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>sanyuan-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 临时密码功能支持 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>temp-secret-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- WebSocket功能支持 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>websocket-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 用户导入导出、机构的导入导出 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>kernel-s-user-expand</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>



    </dependencies>

</project>
