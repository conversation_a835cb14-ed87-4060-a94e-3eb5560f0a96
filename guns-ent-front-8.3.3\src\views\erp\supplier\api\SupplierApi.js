import Request from '@/utils/request/request-util';

/**
 * 供应商管理API
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
export class SupplierApi {
  
  /**
   * 新增供应商
   * @param {Object} params 供应商信息
   * @returns {Promise}
   */
  static add(params) {
    return Request.post('/erp/supplier/add', params);
  }

  /**
   * 删除供应商
   * @param {Object} params 包含supplierId的参数
   * @returns {Promise}
   */
  static delete(params) {
    return Request.post('/erp/supplier/delete', params);
  }

  /**
   * 批量删除供应商
   * @param {Object} params 包含supplierIdList的参数
   * @returns {Promise}
   */
  static batchDelete(params) {
    return Request.post('/erp/supplier/batchDelete', params);
  }

  /**
   * 编辑供应商
   * @param {Object} params 供应商信息
   * @returns {Promise}
   */
  static edit(params) {
    return Request.post('/erp/supplier/edit', params);
  }

  /**
   * 查询供应商详情
   * @param {Object} params 包含supplierId的参数
   * @returns {Promise}
   */
  static detail(params) {
    return Request.getAndLoadData('/erp/supplier/detail', params);
  }

  /**
   * 分页查询供应商列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findPage(params) {
    return Request.getAndLoadData('/erp/supplier/page', params);
  }

  /**
   * 查询供应商列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static findList(params) {
    return Request.getAndLoadData('/erp/supplier/list', params);
  }

  /**
   * 更新供应商状态
   * @param {Object} params 包含supplierId和status的参数
   * @returns {Promise}
   */
  static updateStatus(params) {
    return Request.post('/erp/supplier/updateStatus', params);
  }

  /**
   * 校验供应商编码是否重复
   * @param {Object} params 包含supplierCode和supplierId的参数
   * @returns {Promise}
   */
  static validateCode(params) {
    return Request.getAndLoadData('/erp/supplier/validateCode', params);
  }

  /**
   * 获取供应商关联的区域列表
   * @param {Object} params 包含supplierId的参数
   * @returns {Promise}
   */
  static getSupplierRegions(params) {
    return Request.getAndLoadData('/erp/supplierRegion/getSupplierRegions', params);
  }

  /**
   * 更新供应商关联的区域
   * @param {Object} params 包含supplierId和regionIds的参数
   * @returns {Promise}
   */
  static updateSupplierRegions(params) {
    return Request.post('/erp/supplierRegion/updateSupplierRegions', params);
  }
  
  /**
   * 根据区域ID查询供应商列表
   * @param {Object} params 包含regionId的参数
   * @returns {Promise}
   */
  static findSuppliersByRegion(params) {
    return Request.getAndLoadData('/erp/supplierRegion/findSuppliersByRegion', params);
  }

  /**
   * 获取供应商类型选项
   * @returns {Array}
   */
  static getSupplierTypeOptions() {
    return [
      { label: '企业', value: 'ENTERPRISE' },
      { label: '个体', value: 'INDIVIDUAL' }
    ];
  }

  /**
   * 获取经营方式选项
   * @returns {Array}
   */
  static getBusinessModeOptions() {
    return [
      { label: '购销', value: 'PURCHASE_SALE' },
      { label: '联营', value: 'JOINT_VENTURE' },
      { label: '代销', value: 'CONSIGNMENT' }
    ];
  }

  /**
   * 获取供应商状态选项
   * @returns {Array}
   */
  static getSupplierStatusOptions() {
    return [
      { label: '正常', value: 'ACTIVE' },
      { label: '停用', value: 'INACTIVE' },
      { label: '黑名单', value: 'BLACKLIST' }
    ];
  }

  /**
   * 获取信用等级选项
   * @returns {Array}
   */
  static getCreditLevelOptions() {
    return [
      { label: '优秀', value: 'A' },
      { label: '良好', value: 'B' },
      { label: '一般', value: 'C' },
      { label: '较差', value: 'D' }
    ];
  }

  /**
   * 获取供应商类型名称
   * @param {String} type 供应商类型
   * @returns {String}
   */
  static getSupplierTypeName(type) {
    const options = SupplierApi.getSupplierTypeOptions();
    const option = options.find(item => item.value === type);
    return option ? option.label : type;
  }

  /**
   * 获取经营方式名称
   * @param {String} businessMode 经营方式
   * @returns {String}
   */
  static getBusinessModeName(businessMode) {
    const options = SupplierApi.getBusinessModeOptions();
    const option = options.find(item => item.value === businessMode);
    return option ? option.label : businessMode;
  }

  /**
   * 获取供应商状态名称
   * @param {String} status 供应商状态
   * @returns {String}
   */
  static getSupplierStatusName(status) {
    const options = SupplierApi.getSupplierStatusOptions();
    const option = options.find(item => item.value === status);
    return option ? option.label : status;
  }

  /**
   * 获取信用等级名称
   * @param {String} level 信用等级
   * @returns {String}
   */
  static getCreditLevelName(level) {
    const options = SupplierApi.getCreditLevelOptions();
    const option = options.find(item => item.value === level);
    return option ? option.label : level;
  }

  /**
   * 获取状态标签颜色
   * @param {String} status 供应商状态
   * @returns {String}
   */
  static getStatusTagColor(status) {
    switch (status) {
      case 'ACTIVE':
        return 'green';
      case 'INACTIVE':
        return 'orange';
      case 'BLACKLIST':
        return 'red';
      default:
        return 'default';
    }
  }

  /**
   * 获取信用等级标签颜色
   * @param {String} level 信用等级
   * @returns {String}
   */
  static getCreditLevelTagColor(level) {
    switch (level) {
      case 'A':
        return 'green';
      case 'B':
        return 'blue';
      case 'C':
        return 'orange';
      case 'D':
        return 'red';
      default:
        return 'default';
    }
  }

  /**
   * 获取经营方式标签颜色
   * @param {String} businessMode 经营方式
   * @returns {String}
   */
  static getBusinessModeTagColor(businessMode) {
    switch (businessMode) {
      case 'PURCHASE_SALE':
        return 'blue';
      case 'JOINT_VENTURE':
        return 'green';
      case 'CONSIGNMENT':
        return 'orange';
      default:
        return 'default';
    }
  }

  /**
   * 获取供应商关联的商品列表
   * @param {Object} params 包含supplierId的参数
   * @returns {Promise}
   */
  static getSupplierProducts(params) {
    return Request.getAndLoadData('/erp/supplier/products', params);
  }

  /**
   * 校验供应商经营方式变更的影响
   * @param {Object} params 包含supplierId和businessMode的参数
   * @returns {Promise}
   */
  static validateBusinessModeChange(params) {
    return Request.getAndLoadData('/erp/supplier/validateBusinessModeChange', params);
  }

}
