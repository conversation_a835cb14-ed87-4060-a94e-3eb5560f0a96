package cn.stylefeng.roses.kernel.manage.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.manage.entity.ApiClient;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiClientRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.KeyPair;
import cn.stylefeng.roses.kernel.manage.service.ApiClientService;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * API客户端控制器
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@RestController
@ApiResource(name = "API客户端")
public class ApiClientController {

    @Resource
    private ApiClientService apiClientService;

    /**
     * 获取API客户端列表（带分页）
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @GetResource(name = "获取API客户端列表（带分页）", path = "/apiClient/page")
    public ResponseData<PageResult<ApiClient>> page(ApiClientRequest apiClientRequest) {
        return new SuccessResponseData<>(apiClientService.findPage(apiClientRequest));
    }

    /**
     * 获取API客户端列表
     * <p>
     * 一般用在接口授权界面左侧的客户端列表的获取
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @GetResource(name = "获取API客户端列表", path = "/apiClient/list")
    public ResponseData<List<ApiClient>> list(ApiClientRequest apiClientRequest) {
        return new SuccessResponseData<>(apiClientService.findList(apiClientRequest));
    }

    /**
     * 添加API客户端
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "添加API客户端", path = "/apiClient/add")
    public ResponseData<ApiClient> add(@RequestBody @Validated(ApiClientRequest.add.class) ApiClientRequest apiClientRequest) {
        apiClientService.add(apiClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除API客户端
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "删除API客户端", path = "/apiClient/delete")
    public ResponseData<?> delete(@RequestBody @Validated(ApiClientRequest.delete.class) ApiClientRequest apiClientRequest) {
        apiClientService.del(apiClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除API客户端
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "批量删除API客户端", path = "/apiClient/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody @Validated(BaseRequest.batchDelete.class) ApiClientRequest apiClientRequest) {
        apiClientService.batchDelete(apiClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑API客户端
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @PostResource(name = "编辑API客户端", path = "/apiClient/edit")
    public ResponseData<?> edit(@RequestBody @Validated(ApiClientRequest.edit.class) ApiClientRequest apiClientRequest) {
        apiClientService.edit(apiClientRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看API客户端详情
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    @GetResource(name = "查看API客户端详情", path = "/apiClient/detail")
    public ResponseData<ApiClient> detail(@Validated(ApiClientRequest.detail.class) ApiClientRequest apiClientRequest) {
        return new SuccessResponseData<>(apiClientService.detail(apiClientRequest));
    }

    /**
     * 生成一个随机的公钥私钥对
     *
     * <AUTHOR>
     * @since 2023/10/24 21:07
     */
    @GetResource(name = "生成一个随机的公钥私钥对", path = "/apiClient/randomRsaKey")
    public ResponseData<KeyPair> randomRsaKey() {
        return new SuccessResponseData<>(apiClientService.randomRsaKey());
    }

    /**
     * 启用禁用客户端
     *
     * <AUTHOR>
     * @since 2023/10/24 21:20
     */
    @PostResource(name = "启用禁用客户端", path = "/apiClient/changeStatus")
    public ResponseData<?> changeStatus(@RequestBody @Validated(BaseRequest.updateStatus.class) ApiClientRequest apiClientRequest) {
        apiClientService.updateStatus(apiClientRequest);
        return new SuccessResponseData<>();
    }

}
