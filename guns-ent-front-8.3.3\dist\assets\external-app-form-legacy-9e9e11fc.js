System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./print-legacy-bf2789b6.js","./ExternalAppApi-legacy-720c9516.js"],(function(e,a){"use strict";var l,t,r,i,n,u,p,o,d,f,s,m,c,_,v,y,C,g,b,x;return{setters:[e=>{l=e._,t=e.s,r=e.a,i=e.f,n=e.w,u=e.d,p=e.g,o=e.b,d=e.l,f=e.u,s=e.v,m=e.B,c=e.x,_=e.y,v=e.z,y=e.A,C=e.G,g=e.H},null,e=>{b=e.u},e=>{x=e.E}],execute:function(){var a=document.createElement("style");a.textContent=".card-title[data-v-ac0676f8]{width:100%;border-left:5px solid;border-color:var(--primary-color);padding-left:10px;margin-bottom:20px}\n",document.head.appendChild(a);const S={__name:"external-app-form",props:{form:Object,isUpdate:Boolean},setup(e){const a=e,l=t({apiClientName:[{required:!0,message:"请输入应用名称",type:"string",trigger:"blur"}],apiClientCode:[{required:!0,message:"请输入应用编码",type:"string",trigger:"blur"}],apiClientSecret:[{required:!0,message:"请输入认证秘钥",type:"string",trigger:"blur"}],apiClientTokenExpiration:[{required:!0,message:"请输入认证秘钥过期时间",type:"number",trigger:"blur"}],apiClientSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}],apiClientStatus:[{required:!0,message:"请选择状态",type:"number",trigger:"change"}]}),S=()=>{a.form.apiClientSecret=b(16)},h=async()=>{const e=await x.randomRsaKey();a.form.apiPublicKey=e.publicKey,a.form.apiPrivateKey=e.privateKey};return(t,b)=>{const x=d,w=f,K=s,U=m,P=c,E=_,k=v,q=y,j=C,N=g;return r(),i(N,{ref:"formRef",model:e.form,rules:l,layout:"vertical"},{default:n((()=>[u(j,{gutter:20},{default:n((()=>[u(K,{span:12},{default:n((()=>[u(w,{label:"应用名称:",name:"apiClientName"},{default:n((()=>[u(x,{value:e.form.apiClientName,"onUpdate:value":b[0]||(b[0]=a=>e.form.apiClientName=a),"allow-clear":"",placeholder:"请输入应用名称"},null,8,["value"])])),_:1})])),_:1}),u(K,{span:12},{default:n((()=>[u(w,{label:"应用编码:",name:"apiClientCode"},{default:n((()=>[u(x,{value:e.form.apiClientCode,"onUpdate:value":b[1]||(b[1]=a=>e.form.apiClientCode=a),"allow-clear":"",placeholder:"请输入应用编码",disabled:a.isUpdate},null,8,["value","disabled"])])),_:1})])),_:1}),u(K,{span:24},{default:n((()=>[u(w,{label:"认证秘钥:",name:"apiClientSecret"},{default:n((()=>[u(P,{compact:"",style:{display:"flex"}},{default:n((()=>[u(x,{value:e.form.apiClientSecret,"onUpdate:value":b[2]||(b[2]=a=>e.form.apiClientSecret=a),"allow-clear":"",placeholder:"请输入认证秘钥"},null,8,["value"]),u(U,{type:"primary",onClick:S},{default:n((()=>b[8]||(b[8]=[p("点击生成")]))),_:1,__:[8]})])),_:1})])),_:1})])),_:1}),u(K,{span:24},{default:n((()=>[u(w,{label:"认证秘钥过期时间(秒):",name:"apiClientTokenExpiration"},{default:n((()=>[u(E,{value:e.form.apiClientTokenExpiration,"onUpdate:value":b[3]||(b[3]=a=>e.form.apiClientTokenExpiration=a),min:0,style:{width:"100%"},placeholder:"请输入认证秘钥过期时间","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),u(K,{span:12},{default:n((()=>[u(w,{label:"状态:",name:"apiClientStatus"},{default:n((()=>[u(q,{value:e.form.apiClientStatus,"onUpdate:value":b[4]||(b[4]=a=>e.form.apiClientStatus=a)},{default:n((()=>[u(k,{value:1},{default:n((()=>b[9]||(b[9]=[p("启用")]))),_:1,__:[9]}),u(k,{value:2},{default:n((()=>b[10]||(b[10]=[p("禁用")]))),_:1,__:[10]})])),_:1},8,["value"])])),_:1})])),_:1}),u(K,{span:12},{default:n((()=>[u(w,{label:"排序:",name:"apiClientSort"},{default:n((()=>[u(E,{value:e.form.apiClientSort,"onUpdate:value":b[5]||(b[5]=a=>e.form.apiClientSort=a),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),u(K,{span:24},{default:n((()=>b[11]||(b[11]=[o("div",{class:"card-title"},"传输加密",-1)]))),_:1,__:[11]}),u(K,{span:24},{default:n((()=>[u(w,{label:"公钥:",name:"apiPublicKey"},{default:n((()=>[u(P,{compact:"",style:{display:"flex"}},{default:n((()=>[u(x,{value:e.form.apiPublicKey,"onUpdate:value":b[6]||(b[6]=a=>e.form.apiPublicKey=a),"allow-clear":"",placeholder:"请输入公钥"},null,8,["value"]),u(U,{type:"primary",onClick:h},{default:n((()=>b[12]||(b[12]=[p("点击生成")]))),_:1,__:[12]})])),_:1})])),_:1})])),_:1}),u(K,{span:24},{default:n((()=>[u(w,{label:"私钥:",name:"apiPrivateKey"},{default:n((()=>[u(x,{value:e.form.apiPrivateKey,"onUpdate:value":b[7]||(b[7]=a=>e.form.apiPrivateKey=a),"allow-clear":"",placeholder:"请输入私钥"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}};e("default",l(S,[["__scopeId","data-v-ac0676f8"]]))}}}));
