/**
 * ERP业务计价相关工具函数
 * 
 * 提供商品计价、价格计算等相关功能
 */

import { PRICING_TYPE, PRECISION_CONFIG } from './constants'

/**
 * 根据计价类型获取价格字段名称
 * @param {string} pricingType - 计价类型
 * @returns {string} 价格字段名称
 */
export function getPriceFieldByType(pricingType) {
  switch (pricingType) {
    case PRICING_TYPE.NORMAL:
      return 'retailPrice'
    case PRICING_TYPE.WEIGHT:
      return 'unitPrice'
    case PRICING_TYPE.PIECE:
      return 'piecePrice'
    case PRICING_TYPE.VARIABLE:
      return 'referencePrice'
    default:
      return 'retailPrice'
  }
}

/**
 * 根据计价类型获取价格标签
 * @param {string} pricingType - 计价类型
 * @returns {string} 价格标签
 */
export function getPriceLabelByType(pricingType) {
  switch (pricingType) {
    case PRICING_TYPE.NORMAL:
      return '零售价格'
    case PRICING_TYPE.WEIGHT:
      return '单位价格（元/kg）'
    case PRICING_TYPE.PIECE:
      return '单份价格'
    case PRICING_TYPE.VARIABLE:
      return '参考价格'
    default:
      return '价格'
  }
}

/**
 * 根据计价类型获取数量标签
 * @param {string} pricingType - 计价类型
 * @returns {string} 数量标签
 */
export function getQuantityLabelByType(pricingType) {
  switch (pricingType) {
    case PRICING_TYPE.NORMAL:
      return '数量（个）'
    case PRICING_TYPE.WEIGHT:
      return '重量（kg）'
    case PRICING_TYPE.PIECE:
      return '份数'
    case PRICING_TYPE.VARIABLE:
      return '数量'
    default:
      return '数量'
  }
}

/**
 * 根据计价类型获取数量精度
 * @param {string} pricingType - 计价类型
 * @returns {number} 数量精度
 */
export function getQuantityPrecisionByType(pricingType) {
  switch (pricingType) {
    case PRICING_TYPE.WEIGHT:
      return PRECISION_CONFIG.WEIGHT
    case PRICING_TYPE.NORMAL:
    case PRICING_TYPE.PIECE:
    case PRICING_TYPE.VARIABLE:
    default:
      return PRECISION_CONFIG.QUANTITY
  }
}

/**
 * 根据计价类型和数量计算总价
 * @param {string} pricingType - 计价类型
 * @param {number} quantity - 数量
 * @param {object} priceData - 价格数据对象
 * @returns {number} 总价
 */
export function calculateTotalPrice(pricingType, quantity, priceData) {
  if (!quantity || quantity <= 0) {
    return 0
  }
  
  const priceField = getPriceFieldByType(pricingType)
  const unitPrice = priceData[priceField]
  
  if (!unitPrice || unitPrice <= 0) {
    return 0
  }
  
  const total = Number(quantity) * Number(unitPrice)
  return Number(total.toFixed(PRECISION_CONFIG.PRICE))
}

/**
 * 计算采购入库单总金额
 * @param {Array} orderDetails - 入库单明细数组
 * @returns {number} 总金额
 */
export function calculateOrderTotalAmount(orderDetails) {
  if (!Array.isArray(orderDetails) || orderDetails.length === 0) {
    return 0
  }
  
  const total = orderDetails.reduce((sum, detail) => {
    const itemTotal = calculateTotalPrice(
      detail.pricingType, 
      detail.quantity, 
      { [getPriceFieldByType(detail.pricingType)]: detail.unitPrice }
    )
    return sum + itemTotal
  }, 0)
  
  return Number(total.toFixed(PRECISION_CONFIG.PRICE))
}

/**
 * 验证价格是否在合理范围内
 * @param {number} price - 价格
 * @param {number} minPrice - 最小价格，默认0.01
 * @param {number} maxPrice - 最大价格，默认99999.99
 * @returns {boolean} 验证结果
 */
export function validatePriceRange(price, minPrice = 0.01, maxPrice = 99999.99) {
  const numPrice = Number(price)
  return !isNaN(numPrice) && numPrice >= minPrice && numPrice <= maxPrice
}

/**
 * 验证数量是否在合理范围内
 * @param {number} quantity - 数量
 * @param {string} pricingType - 计价类型
 * @param {number} minQuantity - 最小数量，默认0.001
 * @param {number} maxQuantity - 最大数量，默认99999.999
 * @returns {boolean} 验证结果
 */
export function validateQuantityRange(quantity, pricingType, minQuantity = 0.001, maxQuantity = 99999.999) {
  const numQuantity = Number(quantity)
  if (isNaN(numQuantity) || numQuantity < minQuantity || numQuantity > maxQuantity) {
    return false
  }
  
  // 检查精度
  const precision = getQuantityPrecisionByType(pricingType)
  const decimalPlaces = (numQuantity.toString().split('.')[1] || '').length
  return decimalPlaces <= precision
}

/**
 * 格式化价格输入（移除非数字字符，保留小数点）
 * @param {string} input - 输入字符串
 * @returns {string} 格式化后的字符串
 */
export function formatPriceInput(input) {
  if (!input) return ''
  
  // 只保留数字和一个小数点
  let formatted = input.replace(/[^\d.]/g, '')
  
  // 确保只有一个小数点
  const parts = formatted.split('.')
  if (parts.length > 2) {
    formatted = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // 限制小数位数
  if (parts.length === 2 && parts[1].length > PRECISION_CONFIG.PRICE) {
    formatted = parts[0] + '.' + parts[1].substring(0, PRECISION_CONFIG.PRICE)
  }
  
  return formatted
}

/**
 * 格式化数量输入
 * @param {string} input - 输入字符串
 * @param {string} pricingType - 计价类型
 * @returns {string} 格式化后的字符串
 */
export function formatQuantityInput(input, pricingType) {
  if (!input) return ''
  
  // 只保留数字和一个小数点
  let formatted = input.replace(/[^\d.]/g, '')
  
  // 确保只有一个小数点
  const parts = formatted.split('.')
  if (parts.length > 2) {
    formatted = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // 根据计价类型限制小数位数
  const precision = getQuantityPrecisionByType(pricingType)
  if (parts.length === 2 && parts[1].length > precision) {
    formatted = parts[0] + '.' + parts[1].substring(0, precision)
  }
  
  return formatted
}

/**
 * 计算库存价值
 * @param {number} quantity - 库存数量
 * @param {number} avgCost - 平均成本
 * @returns {number} 库存价值
 */
export function calculateInventoryValue(quantity, avgCost) {
  if (!quantity || !avgCost || quantity <= 0 || avgCost <= 0) {
    return 0
  }
  
  const value = Number(quantity) * Number(avgCost)
  return Number(value.toFixed(PRECISION_CONFIG.PRICE))
}

/**
 * 计算销售扣点金额
 * @param {number} salesAmount - 销售金额
 * @param {number} deductionRate - 扣点率（百分比）
 * @returns {number} 扣点金额
 */
export function calculateDeductionAmount(salesAmount, deductionRate) {
  if (!salesAmount || !deductionRate || salesAmount <= 0 || deductionRate <= 0) {
    return 0
  }
  
  const deduction = Number(salesAmount) * (Number(deductionRate) / 100)
  return Number(deduction.toFixed(PRECISION_CONFIG.PRICE))
}

/**
 * 计算实际结算金额（销售金额 - 扣点金额）
 * @param {number} salesAmount - 销售金额
 * @param {number} deductionRate - 扣点率（百分比）
 * @returns {number} 实际结算金额
 */
export function calculateSettlementAmount(salesAmount, deductionRate) {
  const deductionAmount = calculateDeductionAmount(salesAmount, deductionRate)
  const settlement = Number(salesAmount) - deductionAmount
  return Number(settlement.toFixed(PRECISION_CONFIG.PRICE))
}

/**
 * 根据计价类型生成价格输入组件的配置
 * @param {string} pricingType - 计价类型
 * @returns {object} 组件配置对象
 */
export function getPriceInputConfig(pricingType) {
  return {
    field: getPriceFieldByType(pricingType),
    label: getPriceLabelByType(pricingType),
    placeholder: `请输入${getPriceLabelByType(pricingType)}`,
    precision: PRECISION_CONFIG.PRICE,
    min: 0.01,
    max: 99999.99,
    required: pricingType !== PRICING_TYPE.VARIABLE // 不定价商品的参考价格不是必填的
  }
}

/**
 * 根据计价类型生成数量输入组件的配置
 * @param {string} pricingType - 计价类型
 * @returns {object} 组件配置对象
 */
export function getQuantityInputConfig(pricingType) {
  return {
    label: getQuantityLabelByType(pricingType),
    placeholder: `请输入${getQuantityLabelByType(pricingType)}`,
    precision: getQuantityPrecisionByType(pricingType),
    min: 0.001,
    max: 99999.999
  }
}