import{_ as j,r as h,s as F,X as I,a as M,f as V,w as o,d as t,g as T,m as A,l as q,u as P,v as S,G as B,as as Y,y as H,W as R,J as E,$ as G,H as J,M as W}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{P as x}from"./productCategoryApi-39e417fd.js";const X={name:"ProductCategoryAddForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(i,{emit:l}){const v=h(),r=h(!1),f=h([]),g=h("1\u7EA7 - \u4E00\u7EA7\u5206\u7C7B"),s=F({categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),u={categoryCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5206\u7C7B\u7F16\u7801",trigger:"blur"}],categoryName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",trigger:"blur"}],categoryLevel:[{required:!0,message:"\u8BF7\u9009\u62E9\u5206\u7C7B\u5C42\u7EA7",trigger:"change"}],status:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",trigger:"change"}]},d=e=>{l("update:visible",e),e||m()},m=()=>{var e;(e=v.value)==null||e.resetFields(),Object.assign(s,{categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),g.value="1\u7EA7 - \u4E00\u7EA7\u5206\u7C7B"},y=e=>({1:"1\u7EA7 - \u4E00\u7EA7\u5206\u7C7B",2:"2\u7EA7 - \u4E8C\u7EA7\u5206\u7C7B",3:"3\u7EA7 - \u4E09\u7EA7\u5206\u7C7B",4:"4\u7EA7 - \u56DB\u7EA7\u5206\u7C7B",5:"5\u7EA7 - \u4E94\u7EA7\u5206\u7C7B"})[e]||"".concat(e,"\u7EA7 - \u672A\u77E5"),k=e=>{if(!e)return 1;const a=(U,N)=>{for(const p of U){if(p.categoryId===N)return p.categoryLevel||1;if(p.children&&p.children.length>0){const O=a(p.children,N);if(O)return O}}return null},c=a(f.value,e);return c?Math.min(c+1,5):1},_=()=>{const e=k(s.parentId);s.categoryLevel=e,g.value=y(e)},w=(e,a)=>a.title&&a.title.toLowerCase().includes(e.toLowerCase()),C=async()=>{try{let a=await x.findTree()||[];a=b(a),s.categoryId?f.value=L(a,s.categoryId):f.value=a}catch(e){console.error("\u52A0\u8F7D\u5206\u7C7B\u6811\u5931\u8D25:",e),f.value=[]}},b=e=>Array.isArray(e)?e.filter(a=>!a||!a.categoryId||!a.categoryName?!1:(a.title=a.categoryName,a.key=String(a.categoryId),a.value=String(a.categoryId),a.children&&Array.isArray(a.children)&&(a.children=b(a.children)),!0)):[],L=(e,a)=>e.filter(c=>c.categoryId===a?!1:(c.children&&c.children.length>0&&(c.children=L(c.children,a)),!0)),n=async()=>{try{await v.value.validate(),r.value=!0;const e=!!s.categoryId,a=e?x.edit:x.add,c=e?"\u7F16\u8F91\u6210\u529F":"\u65B0\u589E\u6210\u529F";await a(s),A.success(c),l("ok"),d(!1)}catch(e){console.error("\u4FDD\u5B58\u5206\u7C7B\u5931\u8D25:",e),A.error("\u4FDD\u5B58\u5931\u8D25")}finally{r.value=!1}};return I(()=>i.data,e=>{e&&Object.keys(e).length>0&&(Object.assign(s,e),e.categoryLevel?g.value=y(e.categoryLevel):_())},{immediate:!0}),I(()=>i.visible,e=>{e?(C(),i.data&&Object.keys(i.data).length>0&&(Object.assign(s,i.data),setTimeout(()=>{i.data.categoryLevel?g.value=y(i.data.categoryLevel):_()},100))):m()}),I(()=>s.parentId,()=>{_()}),{formRef:v,loading:r,form:s,rules:u,categoryTreeData:f,categoryLevelText:g,updateVisible:d,save:n,filterTreeNode:w}}};function z(i,l,v,r,f,g){const s=q,u=P,d=S,m=B,y=Y,k=H,_=R,w=E,C=G,b=J,L=W;return M(),V(L,{title:r.form.categoryId?"\u7F16\u8F91\u4EA7\u54C1\u5206\u7C7B":"\u65B0\u589E\u4EA7\u54C1\u5206\u7C7B",visible:v.visible,"confirm-loading":r.loading,width:800,onOk:r.save,onCancel:l[7]||(l[7]=n=>r.updateVisible(!1))},{default:o(()=>[t(b,{ref:"formRef",model:r.form,rules:r.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:o(()=>[t(m,{gutter:16},{default:o(()=>[t(d,{md:12,sm:24},{default:o(()=>[t(u,{label:"\u5206\u7C7B\u7F16\u7801",name:"categoryCode"},{default:o(()=>[t(s,{value:r.form.categoryCode,"onUpdate:value":l[0]||(l[0]=n=>r.form.categoryCode=n),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u7F16\u7801","allow-clear":""},null,8,["value"])]),_:1})]),_:1}),t(d,{md:12,sm:24},{default:o(()=>[t(u,{label:"\u5206\u7C7B\u540D\u79F0",name:"categoryName"},{default:o(()=>[t(s,{value:r.form.categoryName,"onUpdate:value":l[1]||(l[1]=n=>r.form.categoryName=n),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0","allow-clear":""},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(m,{gutter:16},{default:o(()=>[t(d,{md:12,sm:24},{default:o(()=>[t(u,{label:"\u7236\u7EA7\u5206\u7C7B",name:"parentId"},{default:o(()=>[t(y,{value:r.form.parentId,"onUpdate:value":l[2]||(l[2]=n=>r.form.parentId=n),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":r.categoryTreeData,placeholder:"\u8BF7\u9009\u62E9\u7236\u7EA7\u5206\u7C7B","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":r.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])]),_:1})]),_:1}),t(d,{md:12,sm:24},{default:o(()=>[t(u,{label:"\u5206\u7C7B\u5C42\u7EA7",name:"categoryLevel"},{default:o(()=>[t(s,{value:r.categoryLevelText,"onUpdate:value":l[3]||(l[3]=n=>r.categoryLevelText=n),placeholder:"\u6839\u636E\u7236\u7EA7\u5206\u7C7B\u81EA\u52A8\u8BBE\u7F6E",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(m,{gutter:16},{default:o(()=>[t(d,{md:12,sm:24},{default:o(()=>[t(u,{label:"\u6392\u5E8F\u53F7",name:"sortOrder"},{default:o(()=>[t(k,{value:r.form.sortOrder,"onUpdate:value":l[4]||(l[4]=n=>r.form.sortOrder=n),placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F\u53F7",min:0,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),t(d,{md:12,sm:24},{default:o(()=>[t(u,{label:"\u72B6\u6001",name:"status"},{default:o(()=>[t(w,{value:r.form.status,"onUpdate:value":l[5]||(l[5]=n=>r.form.status=n),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:o(()=>[t(_,{value:"Y"},{default:o(()=>l[8]||(l[8]=[T("\u542F\u7528")])),_:1,__:[8]}),t(_,{value:"N"},{default:o(()=>l[9]||(l[9]=[T("\u505C\u7528")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(m,{gutter:16},{default:o(()=>[t(d,{span:24},{default:o(()=>[t(u,{label:"\u5907\u6CE8",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:o(()=>[t(C,{value:r.form.remark,"onUpdate:value":l[6]||(l[6]=n=>r.form.remark=n),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","visible","confirm-loading","onOk"])}const $=j(X,[["render",z],["__scopeId","data-v-459c9ad7"]]);export{$ as default};
