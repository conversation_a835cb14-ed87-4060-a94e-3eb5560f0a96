package cn.stylefeng.roses.ent.saas.modular.auth.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 租户-功能包授权范围封装类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantPackageAuthRequest extends BaseRequest {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("主键")
    private Long packageAuthId;

    /**
     * 功能包id
     */
    @NotNull(message = "功能包id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("功能包id")
    private Long packageId;

    /**
     * 功能包绑定权限类型：1-菜单，2-功能
     */
    @NotNull(message = "功能包绑定权限类型：1-菜单，2-功能不能为空", groups = {add.class, edit.class})
    @ChineseDescription("功能包绑定权限类型：1-菜单，2-功能")
    private Integer limitType;

    /**
     * 业务id，为菜单id或菜单功能id
     */
    @NotNull(message = "业务id，为菜单id或菜单功能id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("业务id，为菜单id或菜单功能id")
    private Long businessId;


    /**
     * 批量删除用的id集合
     */
    @NotNull(message = "批量删除id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("批量删除用的id集合")
    private List<Long> batchDeleteIdList;

}
