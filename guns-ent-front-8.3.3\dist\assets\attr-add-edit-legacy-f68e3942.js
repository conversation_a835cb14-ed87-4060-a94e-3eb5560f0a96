System.register(["./index-legacy-ee1db0c7.js","./attr-form-legacy-083a2ae8.js","./ThemeTemplateFieldApi-legacy-437b0971.js","./index-legacy-94a6fc23.js"],(function(e,a){"use strict";var t,l,i,s,n,o,u,d,r,v;return{setters:[e=>{t=e.r,l=e.o,i=e.a,s=e.f,n=e.w,o=e.d,u=e.m,d=e.M},e=>{r=e.default},e=>{v=e.T},null],execute:function(){e("default",{__name:"attr-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const c=e,f=a,m=t(!1),p=t(!1),b=t({positionSort:1e3}),g=t(null);l((()=>{c.data?(p.value=!0,b.value=Object.assign({},c.data)):p.value=!1}));const y=e=>{f("update:visible",e)},h=async()=>{g.value.$refs.formRef.validate().then((async e=>{if(e){m.value=!0;let e=null;e=p.value?v.edit(b.value):v.add(b.value),e.then((async e=>{m.value=!1,u.success(e.message),y(!1),f("done")})).catch((()=>{m.value=!1}))}}))};return(e,a)=>{const t=d;return i(),s(t,{width:700,maskClosable:!1,visible:c.visible,"confirm-loading":m.value,forceRender:!0,title:p.value?"编辑属性":"新建属性","body-style":{paddingBottom:"8px",height:"600px",overflowY:"auto"},"onUpdate:visible":y,onOk:h,onClose:a[1]||(a[1]=e=>y(!1))},{default:n((()=>[o(r,{form:b.value,"onUpdate:form":a[0]||(a[0]=e=>b.value=e),ref_key:"attrFormRef",ref:g,isUpdate:p.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
