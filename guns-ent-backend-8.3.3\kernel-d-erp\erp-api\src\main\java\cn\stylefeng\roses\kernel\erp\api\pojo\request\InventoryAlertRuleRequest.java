package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 库存预警规则请求参数
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertRuleRequest extends BaseRequest {

    /**
     * 预警规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = {edit.class, delete.class, detail.class, updateStatus.class})
    @ChineseDescription("预警规则ID")
    private Long id;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空", groups = {add.class, edit.class})
    @Size(max = 100, message = "规则名称长度不能超过100个字符", groups = {add.class, edit.class})
    @ChineseDescription("规则名称")
    private String ruleName;

    /**
     * 预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)
     */
    @NotBlank(message = "预警类型不能为空", groups = {add.class, edit.class})
    @ChineseDescription("预警类型")
    private String ruleType;

    /**
     * 目标类型：PRODUCT(单个商品)、CATEGORY(商品分类)、ALL(全部商品)
     */
    @NotBlank(message = "目标类型不能为空", groups = {add.class, edit.class})
    @ChineseDescription("目标类型")
    private String targetType;

    /**
     * 目标ID（商品ID或分类ID，ALL类型时为NULL）
     */
    @ChineseDescription("目标ID")
    private Long targetId;

    /**
     * 预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)
     */
    @NotBlank(message = "预警级别不能为空", groups = {add.class, edit.class})
    @ChineseDescription("预警级别")
    private String alertLevel;

    /**
     * 阈值类型：QUANTITY(数量)、PERCENTAGE(百分比)、DAYS(天数)
     */
    @NotBlank(message = "阈值类型不能为空", groups = {add.class, edit.class})
    @ChineseDescription("阈值类型")
    private String thresholdType;

    /**
     * 阈值（根据阈值类型确定含义）
     */
    @NotNull(message = "阈值不能为空", groups = {add.class, edit.class})
    @DecimalMin(value = "0", message = "阈值不能小于0", groups = {add.class, edit.class})
    @ChineseDescription("阈值")
    private BigDecimal thresholdValue;

    /**
     * 比较操作符：LTE(小于等于)、LT(小于)、GTE(大于等于)、GT(大于)、EQ(等于)
     */
    @NotBlank(message = "比较操作符不能为空", groups = {add.class, edit.class})
    @ChineseDescription("比较操作符")
    private String comparisonOperator;

    /**
     * 是否启用（Y-启用，N-停用）
     */
    @ChineseDescription("是否启用")
    private String isEnabled;

    /**
     * 通知方式：SYSTEM(系统通知)、EMAIL(邮件)、SMS(短信)、WECHAT(微信)，多个用逗号分隔
     */
    @ChineseDescription("通知方式")
    private String notificationMethods;

    /**
     * 通知用户ID列表，用逗号分隔
     */
    @ChineseDescription("通知用户ID列表")
    private String notificationUsers;

    /**
     * 检查频率（分钟）
     */
    @Min(value = 1, message = "检查频率不能小于1分钟", groups = {add.class, edit.class})
    @ChineseDescription("检查频率")
    private Integer checkFrequency;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    // ========== 查询条件 ==========

    /**
     * 搜索文本
     */
    @ChineseDescription("搜索文本")
    private String searchText;

    /**
     * 预警类型过滤
     */
    @ChineseDescription("预警类型过滤")
    private String ruleTypeFilter;

    /**
     * 预警级别过滤
     */
    @ChineseDescription("预警级别过滤")
    private String alertLevelFilter;

    /**
     * 启用状态过滤
     */
    @ChineseDescription("启用状态过滤")
    private String isEnabledFilter;

    /**
     * 批量删除的ID列表
     */
    @NotEmpty(message = "删除的ID列表不能为空", groups = {batchDelete.class})
    @ChineseDescription("批量删除的ID列表")
    private List<Long> idList;

    /**
     * 更新状态分组
     */
    public @interface updateStatus {
    }

    /**
     * 批量删除分组
     */
    public @interface batchDelete {
    }
}
