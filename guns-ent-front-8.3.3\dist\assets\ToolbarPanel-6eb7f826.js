import t from"./FunctionButton-592779e0.js";/* empty css               */import{_ as r,a as d,c as l,b as i,d as n}from"./index-18a1ea24.js";const m={class:"toolbar-panel"},_={class:"function-buttons"},p=Object.assign({name:"ToolbarPanel"},{__name:"ToolbarPanel",props:{suspendedOrdersCount:{type:Number,default:0}},emits:["showSuspendedOrders","memberManagement"],setup(o,{emit:s}){const e=s,a=()=>{e("showSuspendedOrders")},c=()=>{e("memberManagement")};return(u,b)=>(d(),l("div",m,[i("div",_,[n(t,{title:"\u6302\u5355\u5217\u8868",icon:"icon-suspend",type:"primary",badge:o.suspendedOrdersCount,onClick:a},null,8,["badge"]),n(t,{title:"\u4F1A\u5458\u7BA1\u7406",icon:"icon-member",type:"default",onClick:c})])]))}}),y=r(p,[["__scopeId","data-v-d1c23fcc"]]);export{y as default};
