import{_ as N}from"./index-02bf6f00.js";import{r as d,o as S,k as B,a as s,c,b as t,d as o,w as a,g as E,t as R,h as l,F as z,f as y,M as F,E as M,m as $,n as q,B as A,I as V,l as D}from"./index-18a1ea24.js";import{T as L}from"./ThemeTemplateFieldApi-b2a7ece4.js";import O from"./attr-add-edit-54dcba23.js";/* empty css              *//* empty css              *//* empty css              */import"./attr-form-2d967afb.js";/* empty css              */const P={class:"guns-layout"},U={class:"guns-layout-content"},j={class:"guns-layout"},H={class:"guns-layout-content-application"},Y={class:"content-mian"},G={class:"content-mian-header"},J={class:"header-content"},K={class:"header-content-left"},Q={class:"header-content-right"},W={class:"content-mian-body"},X={class:"table-content"},Z=["onClick"],ee={key:0},te={key:1},_e={__name:"index",setup(oe){const x=d([{key:"index",title:"\u5E8F\u53F7",width:60,align:"center",isShow:!0,hideInSetting:!0},{title:"\u5C5E\u6027\u540D\u79F0",isShow:!0,dataIndex:"fieldName"},{title:"\u5C5E\u6027\u7F16\u7801",isShow:!0,dataIndex:"fieldCode"},{title:"\u5C5E\u6027\u7C7B\u578B",isShow:!0,dataIndex:"fieldType"},{title:"\u662F\u5426\u5FC5\u586B",isShow:!0,width:100,dataIndex:"fieldRequired"},{title:"\u5C5E\u6027\u957F\u5EA6",isShow:!0,width:100,dataIndex:"fieldLength"},{title:"\u5C5E\u6027\u63CF\u8FF0",isShow:!0,dataIndex:"fieldDescription"},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}]),v=d(null),_=d({fieldName:""}),w=d(null),r=d(!1);S(()=>{});const p=()=>{v.value.reload()},m=u=>{w.value=u,r.value=!0},b=u=>{F.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u5C5E\u6027\u5417?",icon:o(M),maskClosable:!0,onOk:async()=>{const e=await L.del({fieldId:u.fieldId});$.success(e.message),p()}})};return(u,e)=>{const f=q,C=B("plus-outlined"),g=A,h=V,I=D,T=N;return s(),c("div",P,[t("div",U,[t("div",j,[t("div",H,[t("div",Y,[t("div",G,[t("div",J,[t("div",K,[o(f,{size:16})]),t("div",Q,[o(f,{size:16},{default:a(()=>[o(g,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=n=>m())},{default:a(()=>[o(C),e[3]||(e[3]=E("\u65B0\u5EFA"))]),_:1,__:[3]})]),_:1})])])]),t("div",W,[t("div",X,[o(T,{columns:x.value,where:_.value,rowId:"fieldId",ref_key:"tableRef",ref:v,rowSelection:!1,url:"/sysThemeTemplateField/findPage",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"THMEM_ATTR_TABLE"},{toolLeft:a(()=>[o(I,{value:_.value.fieldName,"onUpdate:value":e[1]||(e[1]=n=>_.value.fieldName=n),placeholder:"\u4E3B\u9898\u5C5E\u6027\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:p,class:"search-input",bordered:!1,style:{width:"240px"}},{prefix:a(()=>[o(h,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:a(({column:n,record:i})=>[n.dataIndex=="fieldName"?(s(),c("a",{key:0,onClick:k=>m(i)},R(i.fieldName),9,Z)):l("",!0),n.dataIndex=="fieldRequired"?(s(),c(z,{key:1},[i.fieldRequired=="Y"?(s(),c("span",ee,"\u662F")):l("",!0),i.fieldRequired=="N"?(s(),c("span",te,"\u5426")):l("",!0)],64)):l("",!0),n.key=="action"?(s(),y(f,{key:2,size:16},{default:a(()=>[o(h,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:k=>m(i)},null,8,["onClick"]),o(h,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:k=>b(i)},null,8,["onClick"])]),_:2},1024)):l("",!0)]),_:1},8,["columns","where"])])])])])])]),r.value?(s(),y(O,{key:0,visible:r.value,"onUpdate:visible":e[2]||(e[2]=n=>r.value=n),data:w.value,onDone:p},null,8,["visible","data"])):l("",!0)])}}};export{_e as default};
