/* FunctionButton组件样式覆盖 */
.function-card {
  position: relative;
  border-radius: 8px;
  padding: 6px 10px !important; /* 减少内边距，使按钮更紧凑 */
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 36px !important; /* 调整按钮高度，紧贴字体内容 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 浅色系按钮样式 */
.function-card-default,
.function-card-primary,
.function-card-success,
.function-card-warning,
.function-card-danger {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%) !important;
  color: #1890ff !important;
  border: 1px solid #d6e4ff !important;
}

.function-card-default:hover:not(.function-card-disabled),
.function-card-primary:hover:not(.function-card-disabled),
.function-card-success:hover:not(.function-card-disabled),
.function-card-warning:hover:not(.function-card-disabled),
.function-card-danger:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #e6f3ff 0%, #d6e4ff 100%) !important;
  border-color: #adc6ff !important;
  transform: translateY(-1px);
}

/* 调整图标和文字大小 */
.card-icon {
  font-size: 16px !important; /* 调整图标大小 */
  margin-bottom: 2px !important;
}

.card-title {
  font-size: 14px !important; /* 调整字体大小 */
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
} 