# Mysql数据库
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************
    username: root
    password: 123456
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    sentinel:
      transport:
        dashboard: localhost:8999
    stream:
      rocketmq:
        binder:
          name-server: 127.0.0.1:9876
      bindings:
        # 用于发送消息的通道
        order-output:
          destination: order-topic
          group: order-producer-group