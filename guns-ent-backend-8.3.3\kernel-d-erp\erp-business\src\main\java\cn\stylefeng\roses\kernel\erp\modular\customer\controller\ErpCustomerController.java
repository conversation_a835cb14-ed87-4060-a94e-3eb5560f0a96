package cn.stylefeng.roses.kernel.erp.modular.customer.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpCustomerRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;
import cn.stylefeng.roses.kernel.erp.modular.customer.service.ErpCustomerService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 客户主档案控制器
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
@RestController
@ApiResource(name = "客户主档案管理", requiredPermission = true, requirePermissionCode = "ERP_CUSTOMER")
public class ErpCustomerController {

    @Resource
    private ErpCustomerService erpCustomerService;

    /**
     * 新增客户
     */
    @PostResource(name = "新增客户", path = "/erp/customer/add")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_CUSTOMER_MANAGE)
    public ResponseData<?> add(@RequestBody @Validated(ErpCustomerRequest.add.class) ErpCustomerRequest erpCustomerRequest) {
        erpCustomerService.add(erpCustomerRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除客户
     */
    @PostResource(name = "删除客户", path = "/erp/customer/delete")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_CUSTOMER_MANAGE)
    public ResponseData<?> delete(@RequestBody @Validated(ErpCustomerRequest.delete.class) ErpCustomerRequest erpCustomerRequest) {
        erpCustomerService.del(erpCustomerRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除客户
     */
    @PostResource(name = "批量删除客户", path = "/erp/customer/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody ErpCustomerRequest erpCustomerRequest) {
        erpCustomerService.batchDelete(erpCustomerRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑客户
     */
    @PostResource(name = "编辑客户", path = "/erp/customer/edit")
    @BizLog(logTypeCode = ErpPermissionCodeConstants.ERP_CUSTOMER_MANAGE)
    public ResponseData<?> edit(@RequestBody @Validated(ErpCustomerRequest.edit.class) ErpCustomerRequest erpCustomerRequest) {
        erpCustomerService.edit(erpCustomerRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查询客户详情
     */
    @GetResource(name = "查询客户详情", path = "/erp/customer/detail")
    public ResponseData<ErpCustomerResponse> detail(@Validated(ErpCustomerRequest.detail.class) ErpCustomerRequest erpCustomerRequest) {
        ErpCustomerResponse response = erpCustomerService.detail(erpCustomerRequest);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询客户列表
     */
    @GetResource(name = "分页查询客户列表", path = "/erp/customer/page", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.PAGE_CUSTOMER)
    public ResponseData<PageResult<ErpCustomerResponse>> page(ErpCustomerRequest erpCustomerRequest) {
        PageResult<ErpCustomerResponse> pageResult = erpCustomerService.findPage(erpCustomerRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询客户列表
     */
    @GetResource(name = "查询客户列表", path = "/erp/customer/list", requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.LIST_CUSTOMER)
    public ResponseData<List<ErpCustomerResponse>> list(ErpCustomerRequest erpCustomerRequest) {
        List<ErpCustomerResponse> responseList = erpCustomerService.findList(erpCustomerRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 更新客户状态
     */
    @PostResource(name = "更新客户状态", path = "/erp/customer/updateStatus")
    public ResponseData<?> updateStatus(@RequestBody @Validated(ErpCustomerRequest.updateStatus.class) ErpCustomerRequest erpCustomerRequest) {
        erpCustomerService.updateStatus(erpCustomerRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 校验客户编码是否重复
     */
    @GetResource(name = "校验客户编码", path = "/erp/customer/validateCode")
    public ResponseData<Boolean> validateCode(ErpCustomerRequest erpCustomerRequest) {
        boolean isRepeat = erpCustomerService.validateCustomerCodeRepeat(
                erpCustomerRequest.getCustomerCode(), 
                erpCustomerRequest.getCustomerId()
        );
        return new SuccessResponseData<>(!isRepeat);
    }

}
