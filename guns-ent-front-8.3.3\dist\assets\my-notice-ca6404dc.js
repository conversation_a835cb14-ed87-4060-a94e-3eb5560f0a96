import{_ as H}from"./index-02bf6f00.js";import{_ as O,aP as J,s as K,bu as Q,k as W,a,c as g,d as n,w as s,aR as X,F as R,e as Y,f as y,g as i,t as h,aS as Z,b as V,aM as ee,p as te,q as ne,D as se,b2 as oe,r as m,h as u,bL as ae,bM as C,M,E as $,m as x,I as le,l as ie,n as re,B as de,a7 as ue,U as ce}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */const _e=J({name:"DropDown",props:{list:{type:Array,default:()=>[]},dropName:{type:String,default:""},keyValue:{type:String,default:""},keyName:{type:String,default:""}},setup(o,f){const k=K({downName:""}),b=({key:c})=>{k.downName=o.list.filter(_=>_[o.keyValue]==c)[0][o.keyName],f.emit("dropClick",c)},d=c=>{k.downName=c};return{...Q(k),changeDropname:d,dropClick:b}}}),pe={class:"work-down"};function me(o,f,k,b,d,c){const _=W("DownOutlined"),N=te,v=ne,r=se;return a(),g("div",pe,[n(r,{getPopupContainer:w=>w.parentNode||o.document.body,trigger:["click"]},{overlay:s(()=>{var w;return[X(n(v,{onClick:o.dropClick},{default:s(()=>[(a(!0),g(R,null,Y(o.list,I=>(a(),y(N,{key:I[o.keyValue]},{default:s(()=>[i(h(I[o.keyName]),1)]),_:2},1024))),128))]),_:1},8,["onClick"]),[[Z,((w=o.list)==null?void 0:w.length)>0]])]}),default:s(()=>[V("a",{onClick:f[0]||(f[0]=ee(()=>{},["prevent"])),class:"down-title"},[i(h(o.downName?o.downName:o.dropName)+" ",1),n(_,{style:{color:"#898E91","margin-left":"8px"}})])]),_:1},8,["getPopupContainer"])])}const ye=O(_e,[["render",me],["__scopeId","data-v-32ff4719"]]);const ge={class:"my-notice"},fe={class:"my-notice-header"},ke={class:"my-notice-body"},ve=["onClick"],we=["onClick"],he={__name:"my-notice",setup(o){const f=oe(),k=m([{id:"high",name:"\u9AD8"},{id:"middle",name:"\u4E2D"},{id:"low",name:"\u4F4E"}]),b=m([{id:0,name:"\u672A\u8BFB"},{id:1,name:"\u5DF2\u8BFB"}]),d=m({searchText:"",priorityLevel:"",readFlag:void 0}),c=m([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>_.value.tableIndex+e},{dataIndex:"messageTitle",title:"\u901A\u77E5\u6807\u9898",align:"center",ellipsis:!0,width:200,isShow:!0},{dataIndex:"priorityLevel",title:"\u4F18\u5148\u7EA7",width:100,align:"center",isShow:!0},{dataIndex:"messageSendTime",title:"\u53D1\u5E03\u6D88\u606F\u65F6\u95F4",align:"center",ellipsis:!0,width:150,isShow:!0},{key:"action",title:"\u64CD\u4F5C",align:"center",width:100,isShow:!0}]),_=m(null),N=m(null),v=m(!1),r=()=>{_.value.reload()},w=e=>{d.value.priorityLevel=e,r()},I=e=>{d.value.readFlag=e,r()},F=e=>{e!=null&&e.messageUrl?(e.readFlag==0&&C.setRead({messageId:e.messageId}).then(t=>{}),f.push(e.messageUrl)):(N.value=e,v.value=!0)},z=e=>{M.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6570\u636E\u5417?",icon:n($),maskClosable:!0,onOk:async()=>{const t=await C.delete({messageId:e.messageId});x.success(t.message),r()}})},U=e=>{C.setRead({messageId:e.messageId}).then(t=>{x.success(t.message),r()})},P=()=>{M.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u6E05\u7A7A\u5168\u90E8\u6570\u636E\u5417?",icon:n($),maskClosable:!0,onOk:async()=>{C.cleanMyMessage().then(e=>{x.success(e.message),r()})}})},q=()=>{M.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5168\u90E8\u5DF2\u8BFB\u5417?",icon:n($),maskClosable:!0,onOk:async()=>{C.setTotalRead().then(e=>{x.success(e.message),r()})}})};return(e,t)=>{const S=le,j=ie,E=ye,T=re,A=de,B=ue,D=ce,G=H;return a(),g("div",ge,[V("div",fe,[n(T,{size:16},{default:s(()=>[n(j,{value:d.value.searchText,"onUpdate:value":t[0]||(t[0]=p=>d.value.searchText=p),placeholder:"\u901A\u77E5\u6807\u9898\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:r,style:{width:"200px"}},{prefix:s(()=>[n(S,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),n(E,{list:k.value,dropName:"\u4F18\u5148\u7EA7",keyValue:"id",keyName:"name",onDropClick:w},null,8,["list"]),n(E,{list:b.value,dropName:"\u72B6\u6001",keyValue:"id",keyName:"name",onDropClick:I},null,8,["list"])]),_:1}),n(T,{size:16},{default:s(()=>[n(A,{danger:"",class:"border-radius",onClick:P},{default:s(()=>t[2]||(t[2]=[i("\u6E05\u7A7A\u6D88\u606F")])),_:1,__:[2]}),n(A,{class:"border-radius",type:"primary",ghost:"",onClick:q},{default:s(()=>t[3]||(t[3]=[i("\u5168\u90E8\u5DF2\u8BFB")])),_:1,__:[3]})]),_:1})]),V("div",ke,[n(G,{columns:c.value,rowSelection:!1,where:d.value,bordered:"",rowId:"noticeId",ref_key:"tableRef",ref:_,url:"/sysMessage/page"},{bodyCell:s(({column:p,record:l})=>[p.dataIndex=="messageTitle"?(a(),g(R,{key:0},[l.readFlag==0?(a(),g("a",{key:0,onClick:L=>F(l),style:{"font-weight":"bold"}},[n(B,null,{title:s(()=>[i(h(l.messageTitle),1)]),default:s(()=>[i(" "+h(l.messageTitle),1)]),_:2},1024)],8,ve)):(a(),g("span",{key:1,onClick:L=>F(l),style:{cursor:"pointer"}},[n(B,null,{title:s(()=>[i(h(l.messageTitle),1)]),default:s(()=>[i(" "+h(l.messageTitle),1)]),_:2},1024)],8,we))],64)):u("",!0),p.dataIndex=="priorityLevel"?(a(),g(R,{key:1},[l.priorityLevel=="high"?(a(),y(D,{key:0,color:"red"},{default:s(()=>t[4]||(t[4]=[i("\u9AD8")])),_:1,__:[4]})):u("",!0),l.priorityLevel=="middle"?(a(),y(D,{key:1,color:"orange"},{default:s(()=>t[5]||(t[5]=[i("\u4E2D")])),_:1,__:[5]})):u("",!0),l.priorityLevel=="low"?(a(),y(D,{key:2,color:"blue"},{default:s(()=>t[6]||(t[6]=[i("\u4F4E")])),_:1,__:[6]})):u("",!0)],64)):u("",!0),p.key=="action"?(a(),y(T,{key:2,size:16},{default:s(()=>[l.readFlag==0?(a(),y(S,{key:0,iconClass:"icon-opt-yidu","font-size":"24px",title:"\u5DF2\u8BFB",color:"#60666b",onClick:L=>U(l)},null,8,["onClick"])):u("",!0),n(S,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:L=>z(l)},null,8,["onClick"])]),_:2},1024)):u("",!0)]),_:1},8,["columns","where"])]),v.value?(a(),y(ae,{key:0,visible:v.value,"onUpdate:visible":t[1]||(t[1]=p=>v.value=p),data:N.value,onDone:r},null,8,["visible","data"])):u("",!0)])}}},Te=O(he,[["__scopeId","data-v-78c40047"]]);export{Te as default};
