package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;

/**
 * 库存相关异常
 *
 * <AUTHOR>
 * @since 2025/08/01 20:30
 */
public class StockException extends PosException {

    public StockException(AbstractExceptionEnum exception) {
        super(exception);
    }

    public StockException(AbstractExceptionEnum exception, Object... params) {
        super(exception, params);
    }

    /**
     * 库存不足异常
     */
    public static StockException insufficient(String productName, Integer currentStock, Integer requiredQuantity) {
        return new StockException(ErpPosExceptionEnum.PRODUCT_STOCK_INSUFFICIENT, productName, currentStock, requiredQuantity);
    }
}
