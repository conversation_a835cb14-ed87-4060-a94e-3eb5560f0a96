System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-dba03026.js","./CommonApi-legacy-1cfbfce8.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var a,n,u,i,t,d,o,s,v,r,c,p,y,m;return{setters:[e=>{a=e.r,n=e.o,u=e.X,i=e.a,t=e.c,d=e.d,o=e.w,s=e.f,v=e.h,r=e.l,c=e.bf,p=e.a0},null,e=>{y=e._},e=>{m=e.C},null,null,null,null,null,null,null],execute:function(){const l={class:"wh100"},g={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"请选择"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(e,{emit:p}){const g=e,f=p,b=a(!0),h=a(!1),j=a([]),x=a(""),w=a(!1),C=a({selectPositionList:[]});n((()=>{var e;null!==(e=g.record)&&void 0!==e&&e.itemMultipleChoiceFlag||g.multiple?b.value=!1:b.value=!0,_()}));const _=async()=>{if(g.value)if(b.value){if(h.value)return;const e=await m.getPositionName({positionId:g.value});e.data&&(j.value=[{id:g.value,name:e.data}])}else j.value=g.normal?j.value:JSON.parse(g.value);else j.value=[];h.value=!1,x.value=j.value.map((e=>e.name)).join("；")},O=()=>{var e,l;null!==(e=j.value)&&void 0!==e&&e.length?C.value.selectPositionList=null===(l=j.value)||void 0===l?void 0:l.map((e=>({bizId:e.id,name:e.name}))):C.value.selectPositionList=[],w.value=!0},P=e=>{var l;j.value=null===(l=e.selectPositionList)||void 0===l?void 0:l.map((e=>({id:e.bizId,name:e.name}))),x.value=e.selectPositionList.map((e=>e.name)).join("；"),h.value=!0,B()},B=()=>{let e=j.value.length>0?b.value?j.value[0].id:g.normal?j.value:JSON.stringify(j.value):"";f("update:value",e),f("onChange",g.record),F()},F=async()=>{var e;!g.normal&&null!==(e=g.formRef)&&void 0!==e&&e.validateFields&&await g.formRef.validateFields([g.record.fieldCode])};return u((()=>g.value),(e=>{_()}),{deep:!0}),(a,n)=>{const u=r,p=y,m=c;return i(),t("div",l,[d(u,{value:x.value,"onUpdate:value":n[0]||(n[0]=e=>x.value=e),disabled:g.readonly||g.disabled,class:"w-full",placeholder:e.placeholder,onFocus:O},null,8,["value","disabled","placeholder"]),d(m,null,{default:o((()=>[w.value?(i(),s(p,{key:0,visible:w.value,"onUpdate:visible":n[1]||(n[1]=e=>w.value=e),data:C.value,showTab:["position"],changeHeight:!0,title:"请选择职位",isRadio:b.value,onDone:P},null,8,["visible","data","isRadio"])):v("",!0)])),_:1})])}}},f={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const l=a(""),n=a(!1),u=a(!1),s=a("请选择"),v=()=>{console.log(l.value)};return(e,a)=>{const r=g,c=p;return i(),t("div",f,[d(c,{title:"职位选择",bordered:!1},{default:o((()=>[d(r,{value:l.value,"onUpdate:value":a[0]||(a[0]=e=>l.value=e),disabled:n.value,readonly:u.value,onOnChange:v,placeholder:s.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])])),_:1})])}}})}}}));
