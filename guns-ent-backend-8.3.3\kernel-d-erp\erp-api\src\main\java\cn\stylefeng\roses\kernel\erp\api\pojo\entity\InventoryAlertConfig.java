package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存预警配置实体类
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@TableName("erp_inventory_alert_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryAlertConfig extends BaseEntity {

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 配置键
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置描述
     */
    @TableField("config_desc")
    private String configDesc;

    /**
     * 配置类型：STRING(字符串)、NUMBER(数字)、BOOLEAN(布尔)、JSON(JSON对象)
     */
    @TableField("config_type")
    private String configType;

    /**
     * 是否系统配置（Y-系统，N-用户）
     */
    @TableField("is_system")
    private String isSystem;

    /**
     * 删除标记（Y-已删除，N-未删除）
     */
    @TableField("del_flag")
    private String delFlag;
}
