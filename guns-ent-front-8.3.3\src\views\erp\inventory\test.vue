<template>
  <div class="test-page">
    <h1>库存管理测试页面</h1>
    <p>这是一个测试页面，用于验证库存管理功能是否正常工作。</p>
    
    <a-card title="功能测试" style="margin-top: 20px;">
      <a-space direction="vertical" style="width: 100%;">
        <a-button type="primary" @click="testLoadInventory">
          测试加载库存列表
        </a-button>
        
        <a-button @click="testLoadInventoryValue">
          测试加载库存价值统计
        </a-button>
        
        <a-button @click="testLoadWarningList">
          测试加载预警商品列表
        </a-button>
        
        <a-button @click="testLoadOutOfStockList">
          测试加载缺货商品列表
        </a-button>
        
        <a-button @click="testLoadInventoryHistory">
          测试加载库存历史记录
        </a-button>
        
        <a-button @click="openMainPage">
          打开库存管理主页面
        </a-button>
      </a-space>
    </a-card>

    <a-card title="测试结果" style="margin-top: 20px;" v-if="testResults.length > 0">
      <div v-for="(result, index) in testResults" :key="index" style="margin-bottom: 10px;">
        <a-tag :color="result.success ? 'green' : 'red'">
          {{ result.success ? '成功' : '失败' }}
        </a-tag>
        {{ result.message }}
      </div>
    </a-card>

    <!-- 库存管理主页面组件 -->
    <inventory-index v-if="showMainPage" />
  </div>
</template>

<script>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryApi } from './api/InventoryApi';
import { InventoryHistoryApi } from './api/InventoryHistoryApi';
import InventoryIndex from './index.vue';

export default {
  name: 'InventoryTest',
  components: {
    InventoryIndex
  },
  setup() {
    const testResults = ref([]);
    const showMainPage = ref(false);

    const addTestResult = (success, message) => {
      testResults.value.push({ success, message });
    };

    // 测试加载库存列表
    const testLoadInventory = async () => {
      try {
        const response = await InventoryApi.findPage({
          pageNo: 1,
          pageSize: 10,
          businessModeList: ['PURCHASE_SALE', 'CONSIGNMENT']
        });
        if (response.success) {
          addTestResult(true, `加载库存列表成功，共 ${response.data.total} 条记录`);
          message.success('加载库存列表成功');
        } else {
          addTestResult(false, `加载库存列表失败: ${response.message}`);
          message.error('加载库存列表失败');
        }
      } catch (error) {
        addTestResult(false, `加载库存列表异常: ${error.message}`);
        message.error('加载库存列表异常: ' + error.message);
      }
    };

    // 测试加载库存价值统计
    const testLoadInventoryValue = async () => {
      try {
        const response = await InventoryApi.inventoryValue({
          businessModeList: ['PURCHASE_SALE', 'CONSIGNMENT']
        });
        if (response.success) {
          const data = response.data;
          addTestResult(true, `加载库存价值统计成功: 总商品${data.totalProducts}种，总价值¥${data.totalValue}`);
          message.success('加载库存价值统计成功');
        } else {
          addTestResult(false, `加载库存价值统计失败: ${response.message}`);
          message.error('加载库存价值统计失败');
        }
      } catch (error) {
        addTestResult(false, `加载库存价值统计异常: ${error.message}`);
        message.error('加载库存价值统计异常: ' + error.message);
      }
    };

    // 测试加载预警商品列表
    const testLoadWarningList = async () => {
      try {
        const response = await InventoryApi.warningList({
          pageNo: 1,
          pageSize: 10
        });
        if (response.success) {
          addTestResult(true, `加载预警商品列表成功，共 ${response.data.total || response.data.length} 条记录`);
          message.success('加载预警商品列表成功');
        } else {
          addTestResult(false, `加载预警商品列表失败: ${response.message}`);
          message.error('加载预警商品列表失败');
        }
      } catch (error) {
        addTestResult(false, `加载预警商品列表异常: ${error.message}`);
        message.error('加载预警商品列表异常: ' + error.message);
      }
    };

    // 测试加载缺货商品列表
    const testLoadOutOfStockList = async () => {
      try {
        const response = await InventoryApi.outOfStockList({
          pageNo: 1,
          pageSize: 10
        });
        if (response.success) {
          addTestResult(true, `加载缺货商品列表成功，共 ${response.data.total || response.data.length} 条记录`);
          message.success('加载缺货商品列表成功');
        } else {
          addTestResult(false, `加载缺货商品列表失败: ${response.message}`);
          message.error('加载缺货商品列表失败');
        }
      } catch (error) {
        addTestResult(false, `加载缺货商品列表异常: ${error.message}`);
        message.error('加载缺货商品列表异常: ' + error.message);
      }
    };

    // 测试加载库存历史记录
    const testLoadInventoryHistory = async () => {
      try {
        const response = await InventoryHistoryApi.findPage({
          pageNo: 1,
          pageSize: 10
        });
        if (response.success) {
          addTestResult(true, `加载库存历史记录成功，共 ${response.data.total} 条记录`);
          message.success('加载库存历史记录成功');
        } else {
          addTestResult(false, `加载库存历史记录失败: ${response.message}`);
          message.error('加载库存历史记录失败');
        }
      } catch (error) {
        addTestResult(false, `加载库存历史记录异常: ${error.message}`);
        message.error('加载库存历史记录异常: ' + error.message);
      }
    };

    // 打开主页面
    const openMainPage = () => {
      showMainPage.value = !showMainPage.value;
      message.info(showMainPage.value ? '显示库存管理主页面' : '隐藏库存管理主页面');
    };

    return {
      testResults,
      showMainPage,
      testLoadInventory,
      testLoadInventoryValue,
      testLoadWarningList,
      testLoadOutOfStockList,
      testLoadInventoryHistory,
      openMainPage
    };
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #1890ff;
  margin-bottom: 20px;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
