package cn.stylefeng.roses.kernel.micro.starter;

import cn.stylefeng.roses.kernel.micro.core.sentinel.RestfulUrlCleaner;
import cn.stylefeng.roses.kernel.micro.core.sentinel.SentinelBlockHandler;
import cn.stylefeng.roses.kernel.micro.core.sentinel.SentinelOriginParser;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Sentinel配置
 *
 * <AUTHOR>
 * @date 2020/2/11 21:20
 */
@Configuration
public class SentinelAutoConfiguration {

    @Bean
    public RestfulUrlCleaner restfulUrlCleaner() {
        return new RestfulUrlCleaner();
    }

    @Bean
    public SentinelBlockHandler sentinelBlockHandler() {
        return new SentinelBlockHandler();
    }

    @Bean
    public SentinelOriginParser sentinelOriginParser() {
        return new SentinelOriginParser();
    }

}
