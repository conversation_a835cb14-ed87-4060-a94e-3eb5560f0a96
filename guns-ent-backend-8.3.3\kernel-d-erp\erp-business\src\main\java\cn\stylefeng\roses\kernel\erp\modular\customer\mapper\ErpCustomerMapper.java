package cn.stylefeng.roses.kernel.erp.modular.customer.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpCustomer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 客户Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
public interface ErpCustomerMapper extends BaseMapper<ErpCustomer> {

    /**
     * 根据客户编码查询客户
     *
     * @param customerCode 客户编码
     * @return 客户信息
     */
    @Select("SELECT * FROM erp_customer WHERE customer_code = #{customerCode} AND del_flag = 'N'")
    ErpCustomer getByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 统计区域关联的客户数量（主表关联）
     *
     * @param regionId 区域ID
     * @return 客户数量
     */
    @Select("SELECT COUNT(1) FROM erp_customer WHERE region_id = #{regionId} AND del_flag = 'N'")
    Long countByRegionId(@Param("regionId") Long regionId);
}