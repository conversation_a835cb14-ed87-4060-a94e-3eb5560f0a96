<template>
  <a-modal
    title="客户详情"
    :width="800"
    :visible="visible"
    :footer="null"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="客户编码">
        {{ data.customerCode }}
      </a-descriptions-item>
      <a-descriptions-item label="客户名称">
        {{ data.customerName }}
      </a-descriptions-item>
      <a-descriptions-item label="客户简称">
        {{ data.customerShortName || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="客户类型">
        <a-tag>{{ getCustomerTypeName(data.customerType) }}</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="客户等级">
        <a-tag :color="getCustomerLevelTagColor(data.customerLevel)">
          {{ getCustomerLevelName(data.customerLevel) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag :color="getStatusTagColor(data.status)">
          {{ getCustomerStatusName(data.status) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="联系人">
        {{ data.contactPerson || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="联系电话">
        {{ data.contactPhone || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="手机号码">
        {{ data.contactMobile || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="邮箱地址">
        {{ data.contactEmail || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="联系地址" :span="2">
        {{ data.contactAddress || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="营业执照号">
        {{ data.businessLicenseNo || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="税务登记号">
        {{ data.taxNo || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="开户银行">
        {{ data.bankName || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="银行账号">
        {{ data.bankAccount || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="信用额度">
        <span style="color: #1890ff; font-weight: bold;">
          {{ formatAmount(data.creditLimit) }}
        </span>
      </a-descriptions-item>
      <a-descriptions-item label="已用额度">
        <span style="color: #f5222d; font-weight: bold;">
          {{ formatAmount(data.usedCredit) }}
        </span>
      </a-descriptions-item>
      <a-descriptions-item label="可用额度">
        <span style="color: #52c41a; font-weight: bold;">
          {{ formatAmount(data.availableCredit) }}
        </span>
      </a-descriptions-item>
      <a-descriptions-item label="账期天数">
        {{ formatPaymentTerms(data.paymentTerms) }}
      </a-descriptions-item>
      <a-descriptions-item label="关联区域" :span="2">
        <div v-if="regionList && regionList.length > 0" class="region-tags">
          <a-tag v-for="region in regionList" :key="region.regionId" color="blue">
            {{ region.regionName }}
          </a-tag>
        </div>
        <span v-else>-</span>
      </a-descriptions-item>
      <a-descriptions-item label="备注" :span="2">
        {{ data.remark || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">
        {{ data.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="更新时间">
        {{ data.updateTime }}
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CustomerApi } from '../api/CustomerApi';

export default {
  name: 'CustomerDetail',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 详情数据
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    // 区域列表数据
    const regionList = ref([]);

    // 更新弹窗状态
    const updateVisible = (value) => {
      emit('update:visible', value);
    };

    // 加载客户关联的区域数据
    const loadCustomerRegions = async (customerId) => {
      if (!customerId) {
        regionList.value = [];
        return;
      }

      try {
        const result = await CustomerApi.getCustomerRegions({ customerId });
        regionList.value = result || [];
      } catch (error) {
        console.error('获取客户关联区域失败:', error);
        message.error('获取客户关联区域失败');
        regionList.value = [];
      }
    };

    // 监听数据变化，加载区域信息
    watch(
      () => props.data?.customerId,
      (customerId) => {
        if (customerId && props.visible) {
          loadCustomerRegions(customerId);
        }
      },
      { immediate: true }
    );

    // 监听弹窗显示状态
    watch(
      () => props.visible,
      (visible) => {
        if (visible && props.data?.customerId) {
          loadCustomerRegions(props.data.customerId);
        }
      }
    );

    // 获取名称和颜色的方法（使用箭头函数保持this上下文）
    const getCustomerTypeName = (type) => CustomerApi.getCustomerTypeName(type);
    const getCustomerLevelName = (level) => CustomerApi.getCustomerLevelName(level);
    const getCustomerStatusName = (status) => CustomerApi.getCustomerStatusName(status);
    const getStatusTagColor = (status) => CustomerApi.getStatusTagColor(status);
    const getCustomerLevelTagColor = (level) => CustomerApi.getCustomerLevelTagColor(level);
    const formatAmount = (amount) => CustomerApi.formatAmount(amount);
    const formatPaymentTerms = (terms) => CustomerApi.formatPaymentTerms(terms);

    return {
      regionList,
      updateVisible,
      getCustomerTypeName,
      getCustomerLevelName,
      getCustomerStatusName,
      getStatusTagColor,
      getCustomerLevelTagColor,
      formatAmount,
      formatPaymentTerms
    };
  }
};
</script>

<style scoped>
.region-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.region-tags .ant-tag {
  margin: 0;
}
</style>
