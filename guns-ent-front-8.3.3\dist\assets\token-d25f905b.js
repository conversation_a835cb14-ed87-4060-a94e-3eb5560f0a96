import{_ as r,aP as c,bZ as i,b_ as f,a as p,f as _,bY as o,ca as s,S as u}from"./index-18a1ea24.js";import{S as m}from"./sso-util-3b0c5c46.js";const l=c({name:"TokenLogin",setup(){const n=m.getUrlParam("userToken"),t=()=>{let e=s.substring(0,s.length-1);o.query&&o.query.from?window.location.href=e+String(o.query.from):window.location.href=e};i(n,!0),f(),t()}});function d(n,t,e,g,k,S){const a=u;return p(),_(a)}const T=r(l,[["render",d]]);export{T as default};
