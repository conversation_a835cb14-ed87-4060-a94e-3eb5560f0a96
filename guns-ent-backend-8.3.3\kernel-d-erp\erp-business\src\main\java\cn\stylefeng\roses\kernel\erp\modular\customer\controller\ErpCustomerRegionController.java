package cn.stylefeng.roses.kernel.erp.modular.customer.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpCustomerRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.modular.customer.service.ErpCustomerRegionService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 客户-区域关联控制器
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
@RestController
@ApiResource(name = "客户-区域关联")
public class ErpCustomerRegionController {

    @Resource
    private ErpCustomerRegionService erpCustomerRegionService;

    /**
     * 获取客户关联的区域列表
     */
    @GetResource(name = "获取客户关联的区域列表", path = "/erp/customerRegion/getCustomerRegions")
    public ResponseData<List<ErpRegionResponse>> getCustomerRegions(
            @Validated(ErpCustomerRegionRequest.getCustomerRegions.class) ErpCustomerRegionRequest erpCustomerRegionRequest) {
        List<ErpRegionResponse> result = erpCustomerRegionService.getCustomerRegions(erpCustomerRegionRequest);
        return new SuccessResponseData<>(result);
    }

    /**
     * 更新客户关联的区域
     */
    @PostResource(name = "更新客户关联的区域", path = "/erp/customerRegion/updateCustomerRegions")
    public ResponseData<Void> updateCustomerRegions(
            @RequestBody @Validated(ErpCustomerRegionRequest.updateCustomerRegions.class) ErpCustomerRegionRequest erpCustomerRegionRequest) {
        erpCustomerRegionService.updateCustomerRegions(erpCustomerRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 根据区域ID查询关联的客户
     */
    @GetResource(name = "根据区域ID查询关联的客户", path = "/erp/customerRegion/getCustomersByRegion")
    public ResponseData<PageResult<ErpCustomerResponse>> getCustomersByRegion(
            @Validated(ErpCustomerRegionRequest.getCustomersByRegion.class) ErpCustomerRegionRequest erpCustomerRegionRequest) {
        PageResult<ErpCustomerResponse> result = erpCustomerRegionService.getCustomersByRegion(erpCustomerRegionRequest);
        return new SuccessResponseData<>(result);
    }

    /**
     * 统计区域关联的客户数量
     */
    @GetResource(name = "统计区域关联的客户数量", path = "/erp/customerRegion/countCustomersByRegion")
    public ResponseData<Long> countCustomersByRegion(
            @Validated(ErpCustomerRegionRequest.countCustomersByRegion.class) ErpCustomerRegionRequest erpCustomerRegionRequest) {
        Long count = erpCustomerRegionService.countCustomersByRegion(erpCustomerRegionRequest);
        return new SuccessResponseData<>(count);
    }
}