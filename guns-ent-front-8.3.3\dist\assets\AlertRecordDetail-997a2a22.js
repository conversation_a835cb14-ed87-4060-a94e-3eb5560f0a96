import{_ as k,r as v,X as A,a as b,f as C,w as e,b as T,d as a,g as n,t as r,ah as G,h as I,ag as H,Y as V,U as w,Z as P,a0 as Y,i as z,B as M,n as B,M as K}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{I as W}from"./InventoryAlertRecordApi-19b95203.js";const Q={name:"AlertRecordDetail",props:{visible:{type:Boolean,default:!1},recordData:{type:Object,default:()=>({})}},emits:["update:visible","process"],setup(m,{emit:_}){const l=v([]),o=v(!1),x=[{title:"\u9884\u8B66\u65F6\u95F4",dataIndex:"alertTime",key:"alertTime",width:150,slots:{customRender:"alertTime"}},{title:"\u9884\u8B66\u7EA7\u522B",dataIndex:"alertLevel",key:"alertLevel",width:100,slots:{customRender:"alertLevel"}},{title:"\u5F53\u524D\u5E93\u5B58",dataIndex:"currentStock",key:"currentStock",width:100},{title:"\u5904\u7406\u72B6\u6001",dataIndex:"handleStatus",key:"handleStatus",width:100,slots:{customRender:"handleStatus"}},{title:"\u9884\u8B66\u6D88\u606F",dataIndex:"alertMessage",key:"alertMessage",ellipsis:!0}],S=()=>{_("update:visible",!1)},d=()=>{_("process",m.recordData)},i=t=>t?H(t).format("YYYY-MM-DD HH:mm:ss"):"-",f=t=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"})[t]||"default",u=t=>({LOW_STOCK:"\u5E93\u5B58\u4E0D\u8DB3",ZERO_STOCK:"\u96F6\u5E93\u5B58",OVERSTOCK:"\u5E93\u5B58\u79EF\u538B",EXPIRY:"\u4E34\u671F\u9884\u8B66"})[t]||t,g=t=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"})[t]||"default",D=t=>({CRITICAL:"\u7D27\u6025",WARNING:"\u8B66\u544A",INFO:"\u63D0\u9192"})[t]||t,h=t=>({PENDING:"orange",RESOLVED:"green",IGNORED:"gray"})[t]||"default",y=t=>({PENDING:"\u5F85\u5904\u7406",RESOLVED:"\u5DF2\u89E3\u51B3",IGNORED:"\u5DF2\u5FFD\u7565"})[t]||t,s=t=>({RESOLVE:"green",IGNORE:"gray"})[t]||"default",E=t=>({RESOLVE:"\u89E3\u51B3",IGNORE:"\u5FFD\u7565"})[t]||t,O=t=>({QUANTITY:"\u6570\u91CF",PERCENTAGE:"\u767E\u5206\u6BD4",DAYS:"\u5929\u6570"})[t]||t,L=t=>({LTE:"\u5C0F\u4E8E\u7B49\u4E8E",LT:"\u5C0F\u4E8E",GTE:"\u5927\u4E8E\u7B49\u4E8E",GT:"\u5927\u4E8E",EQ:"\u7B49\u4E8E"})[t]||t,N=t=>({LTE:"\u2264",LT:"<",GTE:"\u2265",GT:">",EQ:"="})[t]||t,R=async()=>{if(m.recordData.productId)try{o.value=!0;const t=await W.getHistory({productId:m.recordData.productId,limit:10});l.value=t.data||[]}catch(t){console.error("\u83B7\u53D6\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:",t)}finally{o.value=!1}};return A(()=>m.visible,t=>{t&&m.recordData.id&&R()}),{historyData:l,historyLoading:o,historyColumns:x,handleCancel:S,handleProcess:d,formatDateTime:i,getAlertTypeColor:f,getAlertTypeText:u,getAlertLevelColor:g,getAlertLevelText:D,getHandleStatusColor:h,getHandleStatusText:y,getHandleTypeColor:s,getHandleTypeText:E,getThresholdTypeText:O,getComparisonOperatorText:L,getComparisonOperatorSymbol:N}}},U={class:"record-detail-container"},X={class:"alert-message"},Z={class:"trigger-condition"},j={class:"handle-remark"},F={class:"action-buttons"};function q(m,_,l,o,x,S){const d=V,i=w,f=P,u=Y,g=z,D=M,h=B,y=K;return b(),C(y,{title:"\u9884\u8B66\u8BB0\u5F55\u8BE6\u60C5",visible:l.visible,width:800,footer:null,onCancel:o.handleCancel},{default:e(()=>[T("div",U,[a(u,{title:"\u57FA\u672C\u4FE1\u606F",size:"small",class:"detail-card"},{default:e(()=>[a(f,{column:2,bordered:""},{default:e(()=>[a(d,{label:"\u9884\u8B66ID"},{default:e(()=>[n(r(l.recordData.id),1)]),_:1}),a(d,{label:"\u89C4\u5219\u540D\u79F0"},{default:e(()=>[n(r(l.recordData.ruleName),1)]),_:1}),a(d,{label:"\u5546\u54C1\u540D\u79F0"},{default:e(()=>[n(r(l.recordData.productName),1)]),_:1}),a(d,{label:"\u5546\u54C1\u7F16\u7801"},{default:e(()=>[n(r(l.recordData.productCode),1)]),_:1}),a(d,{label:"\u9884\u8B66\u7C7B\u578B"},{default:e(()=>[a(i,{color:o.getAlertTypeColor(l.recordData.alertType)},{default:e(()=>[n(r(o.getAlertTypeText(l.recordData.alertType)),1)]),_:1},8,["color"])]),_:1}),a(d,{label:"\u9884\u8B66\u7EA7\u522B"},{default:e(()=>[a(i,{color:o.getAlertLevelColor(l.recordData.alertLevel)},{default:e(()=>[n(r(o.getAlertLevelText(l.recordData.alertLevel)),1)]),_:1},8,["color"])]),_:1}),a(d,{label:"\u9884\u8B66\u65F6\u95F4"},{default:e(()=>[n(r(o.formatDateTime(l.recordData.alertTime)),1)]),_:1}),a(d,{label:"\u5904\u7406\u72B6\u6001"},{default:e(()=>[a(i,{color:o.getHandleStatusColor(l.recordData.handleStatus)},{default:e(()=>[n(r(o.getHandleStatusText(l.recordData.handleStatus)),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),a(u,{title:"\u5E93\u5B58\u4FE1\u606F",size:"small",class:"detail-card"},{default:e(()=>[a(f,{column:2,bordered:""},{default:e(()=>[a(d,{label:"\u5F53\u524D\u5E93\u5B58"},{default:e(()=>[T("span",{style:G({color:l.recordData.currentStock<=l.recordData.thresholdValue?"#f5222d":"#52c41a",fontWeight:"bold"})},r(l.recordData.currentStock),5)]),_:1}),a(d,{label:"\u6700\u5C0F\u5E93\u5B58"},{default:e(()=>[n(r(l.recordData.minStock||"-"),1)]),_:1}),a(d,{label:"\u6700\u5927\u5E93\u5B58"},{default:e(()=>[n(r(l.recordData.maxStock||"-"),1)]),_:1}),a(d,{label:"\u9608\u503C"},{default:e(()=>[n(r(l.recordData.thresholdValue),1)]),_:1}),a(d,{label:"\u9608\u503C\u7C7B\u578B"},{default:e(()=>[n(r(o.getThresholdTypeText(l.recordData.thresholdType)),1)]),_:1}),a(d,{label:"\u6BD4\u8F83\u64CD\u4F5C\u7B26"},{default:e(()=>[n(r(o.getComparisonOperatorText(l.recordData.comparisonOperator)),1)]),_:1})]),_:1})]),_:1}),a(u,{title:"\u9884\u8B66\u8BE6\u60C5",size:"small",class:"detail-card"},{default:e(()=>[a(f,{column:1,bordered:""},{default:e(()=>[a(d,{label:"\u9884\u8B66\u6D88\u606F"},{default:e(()=>[T("div",X,r(l.recordData.alertMessage),1)]),_:1}),a(d,{label:"\u89E6\u53D1\u6761\u4EF6"},{default:e(()=>[T("div",Z," \u5F53\u524D\u5E93\u5B58 ("+r(l.recordData.currentStock)+") "+r(o.getComparisonOperatorSymbol(l.recordData.comparisonOperator))+" \u9608\u503C ("+r(l.recordData.thresholdValue)+") ",1)]),_:1})]),_:1})]),_:1}),l.recordData.handleStatus!=="PENDING"?(b(),C(u,{key:0,title:"\u5904\u7406\u4FE1\u606F",size:"small",class:"detail-card"},{default:e(()=>[a(f,{column:2,bordered:""},{default:e(()=>[a(d,{label:"\u5904\u7406\u4EBA"},{default:e(()=>[n(r(l.recordData.handleUserName||"-"),1)]),_:1}),a(d,{label:"\u5904\u7406\u65F6\u95F4"},{default:e(()=>[n(r(o.formatDateTime(l.recordData.handleTime)),1)]),_:1}),a(d,{label:"\u5904\u7406\u65B9\u5F0F",span:2},{default:e(()=>[a(i,{color:o.getHandleTypeColor(l.recordData.handleType)},{default:e(()=>[n(r(o.getHandleTypeText(l.recordData.handleType)),1)]),_:1},8,["color"])]),_:1}),a(d,{label:"\u5904\u7406\u5907\u6CE8",span:2},{default:e(()=>[T("div",j,r(l.recordData.handleRemark||"\u65E0"),1)]),_:1})]),_:1})]),_:1})):I("",!0),a(u,{title:"\u76F8\u5173\u5386\u53F2\u8BB0\u5F55",size:"small",class:"detail-card"},{default:e(()=>[a(g,{columns:o.historyColumns,"data-source":o.historyData,pagination:!1,loading:o.historyLoading,size:"small"},{alertLevel:e(({record:s})=>[a(i,{color:o.getAlertLevelColor(s.alertLevel)},{default:e(()=>[n(r(o.getAlertLevelText(s.alertLevel)),1)]),_:2},1032,["color"])]),handleStatus:e(({record:s})=>[a(i,{color:o.getHandleStatusColor(s.handleStatus)},{default:e(()=>[n(r(o.getHandleStatusText(s.handleStatus)),1)]),_:2},1032,["color"])]),alertTime:e(({record:s})=>[n(r(o.formatDateTime(s.alertTime)),1)]),_:1},8,["columns","data-source","loading"])]),_:1}),T("div",F,[a(h,null,{default:e(()=>[l.recordData.handleStatus==="PENDING"?(b(),C(D,{key:0,type:"primary",onClick:o.handleProcess},{default:e(()=>_[0]||(_[0]=[n(" \u5904\u7406\u9884\u8B66 ")])),_:1,__:[0]},8,["onClick"])):I("",!0),a(D,{onClick:o.handleCancel},{default:e(()=>_[1]||(_[1]=[n(" \u5173\u95ED ")])),_:1,__:[1]},8,["onClick"])]),_:1})])])]),_:1},8,["visible","onCancel"])}const re=k(Q,[["render",q],["__scopeId","data-v-c06001de"]]);export{re as default};
