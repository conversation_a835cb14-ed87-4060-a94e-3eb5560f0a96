package cn.stylefeng.roses.kernel.micro.starter;

import cn.stylefeng.roses.kernel.auth.api.LoginUserApi;
import cn.stylefeng.roses.kernel.micro.core.auth.LoginUserMicroImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微服务当前登录用户认证的配置
 *
 * <AUTHOR>
 * @date 2021/9/28 17:40
 */
@Configuration
public class MicroAuthAutoConfiguration {

    /**
     * 获取当前登录用户的api
     *
     * <AUTHOR>
     * @date 2021/9/28 17:40
     */
    @Bean
    @ConditionalOnMissingBean(LoginUserApi.class)
    public LoginUserApi loginUserApi() {
        return new LoginUserMicroImpl();
    }

}
