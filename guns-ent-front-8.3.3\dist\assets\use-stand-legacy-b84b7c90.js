System.register(["./index-legacy-ee1db0c7.js","./MenuApi-legacy-240954bc.js"],(function(e,t){"use strict";var a,l,n,o,i,s,d,u,c,r,p,v,m,f,h,x,b,y,g,C;return{setters:[e=>{a=e._,l=e.r,n=e.o,o=e.k,i=e.a,s=e.f,d=e.w,u=e.b,c=e.t,r=e.d,p=e.g,v=e.h,m=e.m,f=e.M,h=e.E,x=e.I,b=e.l,y=e.B,g=e.n},e=>{C=e.M}],execute:function(){var t=document.createElement("style");t.textContent=".stand-box[data-v-2432af0a]{width:100%;height:100%;padding-bottom:20px}.stand-header[data-v-2432af0a]{font-size:16px}.title[data-v-2432af0a]{margin-right:10px;font-size:16px}.search[data-v-2432af0a]{width:100%;display:flex;margin:20px 0;justify-content:space-between}.search .search-input[data-v-2432af0a]{width:300px;border-radius:5px}[data-v-2432af0a] .vxe-icon-edit{display:none}\n",document.head.appendChild(t);const w={class:"stand-box"},_={class:"stand-header"},k={class:"search"},I={class:"table"},z={__name:"use-stand",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:t}){const a=e,z=t,E=l(!1),V=l(""),O=l([]),U=l(null);n((()=>{a.data&&j()}));const j=()=>{E.value=!0,C.optionList({menuId:a.data.menuId,searchText:V.value}).then((e=>{O.value=e})).finally((()=>E.value=!1))},N=e=>{z("update:visible",e)},R=()=>{V.value="",j()},B=async()=>{let e={optionName:"",optionCode:"",menuId:a.data.menuId};O.value.push(e);const t=U.value;t&&await t.setEditRow(e)},M=e=>{const t=U.value;if(t)return t.isEditByRow(e)};return(e,t)=>{const l=x,n=b,z=y,A=g,D=o("plus-outlined"),S=o("vxe-column"),T=o("vxe-input"),q=o("save-outlined"),G=o("close-outlined"),L=o("vxe-table"),P=f;return i(),s(P,{width:800,maskClosable:!1,visible:a.visible,"confirm-loading":E.value,forceRender:!0,title:"功能维护",footer:null,"body-style":{paddingBottom:"8px"},"onUpdate:visible":N,onClose:t[1]||(t[1]=e=>N(!1))},{default:d((()=>[u("div",w,[u("div",_,[t[2]||(t[2]=u("span",{class:"title"},"当前菜单：",-1)),u("span",null,c(a.data.menuName),1)]),u("div",k,[r(A,{size:16},{default:d((()=>[r(n,{value:V.value,"onUpdate:value":t[0]||(t[0]=e=>V.value=e),placeholder:"菜单名称、菜单编码（回车搜索）",onPressEnter:j,class:"search-input"},{prefix:d((()=>[r(l,{iconClass:"icon-opt-search"})])),_:1},8,["value"]),r(z,{class:"border-radius",onClick:R},{default:d((()=>t[3]||(t[3]=[p("重置")]))),_:1,__:[3]})])),_:1}),r(z,{type:"primary",class:"border-radius",onClick:B},{default:d((()=>[r(D),t[4]||(t[4]=p("新建"))])),_:1,__:[4]})]),u("div",I,[r(L,{border:"","show-overflow":"","keep-source":"",ref_key:"xTableRef",ref:U,loading:E.value,data:O.value,height:"500","edit-config":{trigger:"manual",mode:"row",autoClear:!1,showStatus:!0}},{default:d((()=>[r(S,{type:"seq",width:"60",title:"序号",align:"center"}),r(S,{field:"optionName",title:"功能名称","edit-render":{},align:"center"},{edit:d((({row:e})=>[r(T,{modelValue:e.optionName,"onUpdate:modelValue":t=>e.optionName=t,type:"text"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),r(S,{field:"optionCode",title:"功能编码","edit-render":{},align:"center"},{edit:d((({row:e})=>[r(T,{modelValue:e.optionCode,"onUpdate:modelValue":t=>e.optionCode=t,type:"text"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),r(S,{title:"操作",align:"center"},{default:d((({row:e})=>[M(e)?(i(),s(A,{key:0,size:16},{default:d((()=>[r(q,{onClick:t=>(e=>{e.menuOptionId?C.optionEdit(e).then((e=>{m.success(e.message),j()})):C.optionAdd(e).then((e=>{m.success(e.message),j()}));const t=U.value;t&&t.clearEdit()})(e),style:{"font-size":"20px"},title:"保存"},null,8,["onClick"]),r(G,{onClick:t=>(async e=>{const t=U.value;t&&(await t.clearEdit(),await t.revertData(e))})(e),style:{"font-size":"20px"},title:"取消"},null,8,["onClick"])])),_:2},1024)):(i(),s(A,{key:1,size:16},{default:d((()=>[r(l,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:t=>(async e=>{const t=U.value;t&&await t.setEditRow(e)})(e)},null,8,["onClick"]),e.menuOptionId?(i(),s(l,{key:0,iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:t=>(e=>{f.confirm({title:"提示",content:"确定要删除吗?",icon:r(h),maskClosable:!0,onOk:()=>{E.value=!0,C.optionDelete({menuOptionId:e.menuOptionId}).then((e=>{m.success(e.message),j()})).finally((()=>E.value=!1))}})})(e)},null,8,["onClick"])):v("",!0)])),_:2},1024))])),_:1})])),_:1},8,["loading","data"])])])])),_:1},8,["visible","confirm-loading"])}}};e("default",a(z,[["__scopeId","data-v-2432af0a"]]))}}}));
