package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 供应商-区域关联实体类
 *
 * <AUTHOR>
 * @since 2025/07/22 16:00
 */
@TableName(value = "erp_supplier_region", autoResultMap = true)
@Data
public class ErpSupplierRegion {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 供应商ID
     */
    @TableField("supplier_id")
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 区域ID
     */
    @TableField("region_id")
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ChineseDescription("创建时间")
    private Date createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    @ChineseDescription("创建人ID")
    private Long createUser;
}