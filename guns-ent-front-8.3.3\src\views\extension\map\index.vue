<template>
  <div class="guns-body guns-body-card">
    <a-card title="地图选择" :bordered="false">
      <a-button class="ele-btn-icon" @click="openMapPicker"> 打开地图位置选择器 </a-button>
      <div style="margin-top: 12px">选择位置: {{ result.location }}</div>
      <div style="margin-top: 12px">详细地址: {{ result.address }}</div>
      <div style="margin-top: 12px">经 纬 度 : {{ result.lngAndLat }}</div>
      <map-picker :need-city="true" v-model:visible="visible" :search-type="0" @done="onChoose" />
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

const visible = ref(false);

/* 打开位置选择 */
const openMapPicker = () => {
  visible.value = true;
};

// 选择结果
const result = reactive({
  location: '',
  address: '',
  lngAndLat: ''
});

/* 地图选择后回调 */
const onChoose = location => {
  console.log(location);
  result.location = `${location.city?.province}/${location.city?.city}/${location.city?.district}`;
  result.address = `${location.name} ${location.address}`;
  result.lngAndLat = `${location.lng},${location.lat}`;
  visible.value = false;
};
</script>

<style></style>
