System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js"],(function(e,l){"use strict";var r,a,o,t,u,f,n,m,d,c,i;return{setters:[e=>{r=e.s,a=e.a,o=e.f,t=e.w,u=e.d,f=e.l,n=e.u,m=e.v,d=e.y,c=e.G,i=e.H},null],execute:function(){e("default",{__name:"config-type-form",props:{form:Object},setup(e){const l=r({configTypeName:[{required:!0,message:"请输入配置分类名称",type:"string",trigger:"blur"}],configTypeCode:[{required:!0,message:"请输入配置分类编码",type:"string",trigger:"blur"}],configTypeSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}]});return(r,s)=>{const g=f,p=n,y=m,v=d,T=c,_=i;return a(),o(_,{ref:"formRef",model:e.form,rules:l,layout:"vertical"},{default:t((()=>[u(T,{gutter:20},{default:t((()=>[u(y,{xs:24,sm:24,md:12},{default:t((()=>[u(p,{label:"配置分类名称:",name:"configTypeName"},{default:t((()=>[u(g,{value:e.form.configTypeName,"onUpdate:value":s[0]||(s[0]=l=>e.form.configTypeName=l),"allow-clear":"",placeholder:"请输入配置分类名称"},null,8,["value"])])),_:1})])),_:1}),u(y,{xs:24,sm:24,md:12},{default:t((()=>[u(p,{label:"配置分类编码:",name:"configTypeCode"},{default:t((()=>[u(g,{value:e.form.configTypeCode,"onUpdate:value":s[1]||(s[1]=l=>e.form.configTypeCode=l),"allow-clear":"",placeholder:"请输入配置分类编码"},null,8,["value"])])),_:1})])),_:1}),u(y,{xs:24,sm:24,md:12},{default:t((()=>[u(p,{label:"排序:",name:"configTypeSort"},{default:t((()=>[u(v,{value:e.form.configTypeSort,"onUpdate:value":s[2]||(s[2]=l=>e.form.configTypeSort=l),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
