# sharding-jdbc数据库读写分离

## 集成过程

### 1. 本插件install到本地maven仓库

### 2. 在pom中加入sharding的依赖

```xml
<!--读写分离插件-->
<dependency>
    <groupId>cn.stylefeng.roses</groupId>
    <artifactId>sharding-spring-boot-starter</artifactId>
    <version>${roses.kernel.version}</version>
</dependency>
```

### 3. Guns启动类中排除其他的DataSource装载

```java
@Slf4j
@SpringBootApplication(scanBasePackages = {"cn.stylefeng"}, exclude = {FlywayAutoConfiguration.class, GunsDataSourceAutoConfiguration.class, SpringBootConfiguration.class, GunsDataSourceContainerAutoConfiguration.class})
public class GunsApplication {

    public static void main(String[] args) {
        SpringApplication.run(GunsApplication.class, args);
        log.info(GunsApplication.class.getSimpleName() + " is success!");
    }

}
```

### 4. yml中加入对主从的配置

```yml
# Mysql数据库
spring:
  # 这里仍需要，读取sys_config表的配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************
    username: root
    password: 123456
  shardingsphere:
    enabled: true
    props:
      sql:
        show: false
    datasource:
      names: master,slave1,slave2
      master:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: jdbc:mysql://************:3306/guns?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=CONVERT_TO_NULL&useSSL=false&serverTimezone=CTT&nullCatalogMeansCurrent=true
        username: root
        password: 123456
      slave1:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: jdbc:mysql://************:3306/guns?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=CONVERT_TO_NULL&useSSL=false&serverTimezone=CTT&nullCatalogMeansCurrent=true
        username: root
        password: 123456
      slave2:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***********************************************************************************************************************************************************************************************
        username: root
        password: 123456
    masterslave:
      name: guns-master-slave
      master-data-source-name: master
      slave-data-source-names: slave1,slave2
      # 负载均衡算法：ROUND_ROBIN-轮询算法，RANDOM-随机访问算法
      load-balance-algorithm-type: RANDOM
```