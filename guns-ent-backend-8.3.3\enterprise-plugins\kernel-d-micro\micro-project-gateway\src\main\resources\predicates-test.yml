spring:
  application:
    name: guns-cloud-gateway
  main:
    allow-circular-references: true
  profiles:
    active: local
  cloud:
    nacos:
      discovery:
        enabled: true
        register-enabled: true
        watch-delay: 1000
    gateway:
      routes:
        # 系统管理的路由
        - id: cloud-system
          uri: lb://guns
          predicates:
            - Path=/guns/**
          filters:
            - ApiAuth
            - RewritePath=/guns/(?<segment>.*), /$\{segment}

        # 根据Cookie进行路由
        # 请求中携带Guns-Cookie的cookie，值为haha的时候，匹配路由
        - id: cookie_router
          uri: https://baidu.com
          predicates:
            - Cookie=Guns-Cookie, haha

        # 根据Header进行路由
        # 请求中携带X-Request-Id的header，值为数字的时候，匹配路由
        - id: header_route
          uri: https://sohu.com
          predicates:
            - Header=X-Request-Id, \d+

        # 根据请求时间进行路由，超过指定时间
        # 请求时间在2024-07-05 9:00之后，匹配路由
        - id: after_route
          uri: https://baidu.com
          predicates:
            - After=2024-07-05T09:00:00.000+08:00[Asia/Shanghai]

        # 根据请求时间进行路由，在指定时间之前
        # 请求时间在2024-07-05 11:00之前，匹配路由
        - id: before_route
          uri: https://baidu.com
          predicates:
            - Before=2024-07-05T11:00:00.000+08:00[Asia/Shanghai]

        # 指定时间之间
        - id: between_route
          uri: https://baidu.com
          predicates:
            - Between=2024-07-05T14:00:00.000+08:00[Asia/Shanghai], 2024-07-05T15:00:00.000+08:00[Asia/Shanghai]

        # 根据请求的Host配置进行，支持ant风格的匹配
        # 请求中带Header=Host: **.website1.com或者**.website2.com的时候，匹配路由
        - id: host_route
          uri: https://163.com
          predicates:
            - Host=**.website1.com,**.website2.com

        # 根据请求的Http Method配置进行
        - id: method_route
          uri: https://baidu.com
          predicates:
            - Method=GET,POST

        # 携带param指定请求参数和值的路由，支持正则匹配
        - id: query_route
          uri: https://baidu.com
          predicates:
            - Query=searchText, gree.

        # 根据远程请求的ip地址访问，可以是携带子网掩码的ip地址
        - id: remoteaddr_route
          uri: https://baidu.com
          predicates:
            - RemoteAddr=***********,localhost,127.0.0.1,*************

        # 根据权重进行轮询匹配
        - id: weight_high
          uri: https://baidu.com
          predicates:
            - Weight=group1, 8
        - id: weight_low
          uri: https://163.com
          predicates:
            - Weight=group1, 2

        # 根据请求头中的X-Forwarded-For进行路由
        - id: xforwarded_remoteaddr_route
          uri: https://baidu.com
          predicates:
            - XForwardedRemoteAddr=***********/24

# feign远程调用配置
feign:
  sentinel:
    enabled: true
  client:
    config:
      # 全局配置
      default:
        # NONE不记录任何日志--BASIC仅请求方法URL,状态码执行时间等--HEADERS在BASIC基础上记录header等--FULL记录所有
        loggerLevel: full
  httpclient:
    # 让feign使用apache httpclient做请求；而不是默认的urlConnection
    enabled: true
    # feign的最大连接数
    max-connections: 200
    # feign单个路径的最大连接数
    max-connections-per-route: 50

# actuator配置，给spring boot admin监控用
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always