package cn.stylefeng.roses.seata.demo.account.modular.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 账户信息
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
@TableName("account_tbl")
@Data
public class AccountTbl {

    /**
     * 账号id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户余额
     */
    @TableField("money")
    private Integer money;

}