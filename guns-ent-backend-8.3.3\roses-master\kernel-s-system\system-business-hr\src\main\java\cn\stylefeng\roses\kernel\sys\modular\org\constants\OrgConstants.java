/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.sys.modular.org.constants;

/**
 * 组织机构的常量
 *
 * <AUTHOR>
 * @since 2023/6/28 22:44
 */
public interface OrgConstants {

    /**
     * 缓存前缀：组织机构是否包含下级的标识
     */
    String SYS_ORG_SUB_FLAG_CACHE_PREFIX = "SYS:ORG:HAVE_SUB_FLAG:";

    /**
     * 没有上级机构时候的上级机构名称
     */
    String NONE_PARENT_ORG = "无上级机构";

    /**
     * 添加组织机构的事件监听
     */
    String ADD_ORG_EVENT = "ADD_ORG_EVENT";

    /**
     * 修改组织机构的事件监听
     */
    String EDIT_ORG_EVENT = "EDIT_ORG_EVENT";

    /**
     * 删除组织机构的事件监听
     */
    String DELETE_ORG_EVENT = "DELETE_ORG_EVENT";

    //-------------------------------组织机构详情缓存-------------------------------

    /**
     * 组织机构详情的缓存
     */
    String ORG_INFO_CACHE_PREFIX = "SYS:ORG:INFO:";

}
