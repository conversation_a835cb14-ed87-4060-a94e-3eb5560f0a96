<template>
  <div class="tree-error-handler">
    <!-- 数据加载错误 -->
    <div v-if="error && error.type === 'DATA_LOAD_ERROR'" class="error-container">
      <a-result
        status="error"
        :title="error.title || '数据加载失败'"
        :sub-title="error.message || '无法获取树结构数据，请检查网络连接或稍后重试'"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="handleRetry">
              <reload-outlined />
              重新加载
            </a-button>
            <a-button @click="handleRefresh">
              <sync-outlined />
              刷新页面
            </a-button>
          </a-space>
        </template>
      </a-result>
    </div>

    <!-- 懒加载错误 -->
    <div v-else-if="error && error.type === 'LAZY_LOAD_ERROR'" class="error-container lazy-error">
      <a-alert
        :message="error.title || '子节点加载失败'"
        :description="error.message || '无法加载子节点数据，请重试'"
        type="warning"
        show-icon
        closable
        @close="handleClose"
      >
        <template #action>
          <a-button size="small" type="primary" @click="handleRetryLazy">
            重试
          </a-button>
        </template>
      </a-alert>
    </div>

    <!-- 搜索错误 -->
    <div v-else-if="error && error.type === 'SEARCH_ERROR'" class="error-container search-error">
      <a-alert
        :message="error.title || '搜索失败'"
        :description="error.message || '搜索过程中发生错误，请重新搜索'"
        type="error"
        show-icon
        closable
        @close="handleClose"
      >
        <template #action>
          <a-button size="small" @click="handleClearSearch">
            清空搜索
          </a-button>
        </template>
      </a-alert>
    </div>

    <!-- 操作错误 -->
    <div v-else-if="error && error.type === 'ACTION_ERROR'" class="error-container action-error">
      <a-alert
        :message="error.title || '操作失败'"
        :description="error.message || '执行操作时发生错误'"
        type="error"
        show-icon
        closable
        @close="handleClose"
      />
    </div>

    <!-- 网络错误 -->
    <div v-else-if="error && error.type === 'NETWORK_ERROR'" class="error-container network-error">
      <a-result
        status="500"
        :title="error.title || '网络连接异常'"
        :sub-title="error.message || '请检查网络连接后重试'"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="handleRetry">
              <reload-outlined />
              重新连接
            </a-button>
            <a-button @click="handleOfflineMode" v-if="supportOffline">
              <disconnect-outlined />
              离线模式
            </a-button>
          </a-space>
        </template>
      </a-result>
    </div>

    <!-- 通用错误 -->
    <div v-else-if="error" class="error-container generic-error">
      <a-alert
        :message="error.title || '发生错误'"
        :description="error.message || '系统发生未知错误，请联系管理员'"
        type="error"
        show-icon
        closable
        @close="handleClose"
      >
        <template #action>
          <a-button size="small" @click="handleRetry">
            重试
          </a-button>
        </template>
      </a-alert>
    </div>
  </div>
</template>

<script setup>
import { defineEmits } from 'vue'
import { 
  ReloadOutlined, 
  SyncOutlined, 
  DisconnectOutlined 
} from '@ant-design/icons-vue'

// 定义组件名称
defineOptions({
  name: 'TreeErrorHandler'
})

// 定义Props
const props = defineProps({
  error: {
    type: Object,
    default: null
  },
  supportOffline: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits([
  'retry',
  'refresh', 
  'close',
  'clear-search',
  'offline-mode',
  'retry-lazy'
])

// 事件处理方法
const handleRetry = () => {
  emit('retry')
}

const handleRefresh = () => {
  emit('refresh')
  // 刷新页面
  window.location.reload()
}

const handleClose = () => {
  emit('close')
}

const handleClearSearch = () => {
  emit('clear-search')
}

const handleOfflineMode = () => {
  emit('offline-mode')
}

const handleRetryLazy = () => {
  emit('retry-lazy')
}
</script>

<style scoped lang="less">
.tree-error-handler {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .error-container {
    width: 100%;
    padding: 16px;
    
    &.lazy-error,
    &.search-error,
    &.action-error {
      position: absolute;
      top: 16px;
      left: 16px;
      right: 16px;
      z-index: 1000;
    }
    
    &.network-error,
    &.generic-error {
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 错误提示动画
.error-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tree-error-handler {
    .error-container {
      padding: 12px;
      
      &.lazy-error,
      &.search-error,
      &.action-error {
        top: 12px;
        left: 12px;
        right: 12px;
      }
    }
  }
}
</style>
</content>
</invoke>