System.register(["./productCategoryApi-legacy-247b2407.js","./UniversalTree-legacy-6dcdf778.js","./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js"],(function(e,t){"use strict";var o,a,r,d,l,n,c,i,s,y,u;return{setters:[e=>{o=e.P},e=>{a=e.U},e=>{r=e._,d=e.r,l=e.L,n=e.a,c=e.f,i=e.M,s=e.d,y=e.E,u=e.m},null],execute:function(){const t=Object.assign({name:"ProductCategoryTree"},{__name:"product-category-tree",props:{isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["treeSelect","addCategory","editCategory","deleteCategory"],setup(e,{expose:t,emit:r}){const g=e,h=r,v=d(),S={api:o.findTree,lazyLoadApi:o.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},p={key:"categoryId",title:"categoryName",children:"children",hasChildren:"hasChildren",level:"categoryLevel"},f=l((()=>({title:"产品分类",showHeader:g.isSetWidth,showSearch:!0,searchPlaceholder:"请输入分类名称，回车搜索",showAddButton:g.isShowEditIcon,showEditIcons:g.isShowEditIcon,showIcon:!1,isSetWidth:g.isSetWidth}))),w={selectable:!0,expandable:!0,lazyLoad:!0,defaultExpandLevel:2,allowMultiSelect:!1},I=l((()=>({allowAdd:g.isShowEditIcon,allowEdit:g.isShowEditIcon,allowDelete:g.isShowEditIcon}))),E=e=>{const{keys:t,nodes:o}=e;h("treeSelect",t,{selectedNodes:o})},m=e=>{},C=e=>{},x=e=>{h("addCategory",e)},T=e=>{h("editCategory",e)},L=e=>{i.confirm({title:"确认删除",content:`确定要删除分类"${e.categoryName}"吗？`,icon:s(y),okText:"确定",cancelText:"取消",onOk:()=>o.delete({categoryId:e.categoryId}).then((()=>{u.success("删除成功"),h("deleteCategory",e),j()})).catch((e=>{console.error("删除分类失败:",e),u.error("删除失败")}))})},N=e=>{},_=e=>{console.error("产品分类树数据加载失败:",e)},j=()=>{var e;null===(e=v.value)||void 0===e||e.reload()},k=j,A=l((()=>{var e;return(null===(e=v.value)||void 0===e?void 0:e.getSelectedNodes())||[]}));return t({reload:j,reloadCategoryTreeData:k,getSelectedNodes:()=>{var e;return null===(e=v.value)||void 0===e?void 0:e.getSelectedNodes()},setSelectedKeys:e=>{var t;null===(t=v.value)||void 0===t||t.setSelectedKeys(e)},currentSelectKeys:A}),(e,t)=>(n(),c(a,{ref_key:"universalTreeRef",ref:v,"data-source":S,"field-mapping":p,"display-config":f.value,"interaction-config":w,"action-config":I.value,onSelect:E,onExpand:m,onSearch:C,onAdd:x,onEdit:T,onDelete:L,onLoad:N,onLoadError:_},null,8,["display-config","action-config"]))}});e("default",r(t,[["__scopeId","data-v-14cde36e"]]))}}}));
