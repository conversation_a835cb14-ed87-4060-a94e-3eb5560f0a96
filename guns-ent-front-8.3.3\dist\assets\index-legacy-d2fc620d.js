System.register(["./index-legacy-ee1db0c7.js"],(function(e,a){"use strict";var n,o,i,t,p,l,r,s,c,d,u,m,g,v,f,y,h,k,b,A,M,w,x,S,I,U,L,C;return{setters:[e=>{e.ay,n=e.az,o=e._,i=e.r,t=e.X,p=e.o,l=e.bs,r=e.k,s=e.a,c=e.f,d=e.w,u=e.b,m=e.d,g=e.g,v=e.ah,f=e.c,y=e.F,h=e.a2,k=e.h,b=e.e,A=e.t,M=e.m,w=e.aK,x=e.bt,S=e.B,I=e.S,U=e.L,L=e.aH,C=e.M}],execute:function(){var a=document.createElement("style");a.textContent='.ant-select-auto-complete{box-sizing:border-box;margin:0;padding:0;color:var(--text-color);font-size:14px;font-variant:tabular-nums;line-height:1.5715;list-style:none;font-feature-settings:"tnum"}.ant-select-auto-complete .ant-select-clear{right:13px}.guns-map-picker-header[data-v-e27826be]{display:flex;align-items:center;padding:8px 14px}.guns-map-picker-header .guns-map-picker-header-search[data-v-e27826be]{flex:1;padding-right:16px}.guns-map-picker-header .ant-select-auto-complete[data-v-e27826be]{width:100%;max-width:220px}.guns-map-picker-header .guns-map-picker-search-icon[data-v-e27826be]{color:var(--text-color-secondary)}.guns-map-picker-body[data-v-e27826be]{display:flex;border-bottom-left-radius:2px;border-bottom-right-radius:2px;overflow:hidden}.guns-map-picker-body .guns-map-picker-main[data-v-e27826be]{flex:1;position:relative}.guns-map-picker-body .guns-map-picker-main-plus[data-v-e27826be]{color:#1890ff;font-size:12px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.guns-map-picker-body .guns-map-picker-main-icon[data-v-e27826be]{width:26px;position:absolute;left:50%;bottom:50%;margin-left:-13px}.guns-map-picker-body .guns-map-picker-poi-list[data-v-e27826be]{width:300px;overflow:auto;border-top:1px solid hsla(0,0%,60%,.15)}.guns-map-picker-body .guns-map-picker-poi-item[data-v-e27826be]{padding:8px 30px 8px 44px;border-bottom:1px solid hsla(0,0%,60%,.15);position:relative;cursor:pointer}.guns-map-picker-body .guns-map-picker-poi-item[data-v-e27826be]:hover{background:hsla(0,0%,60%,.05)}.guns-map-picker-body .guns-map-picker-poi-item-icon[data-v-e27826be]{position:absolute;top:50%;left:14px;transform:translateY(-50%);font-size:18px;opacity:.4}.guns-map-picker-body .guns-map-picker-poi-item-title[data-v-e27826be]{font-size:14px}.guns-map-picker-body .guns-map-picker-poi-item-address[data-v-e27826be]{font-size:12px;margin-top:2px;opacity:.6}.guns-map-picker-body .guns-map-picker-poi-item-check[data-v-e27826be]{position:absolute;top:50%;right:7px;transform:translateY(-50%);color:#1890ff;font-size:14px;display:none}.guns-map-picker-body .guns-map-picker-poi-item.active .guns-map-picker-poi-item-check[data-v-e27826be]{display:block}.guns-map-picker-body .guns-map-picker-anim-bounce[data-v-e27826be]{animation:eleMapPickerAnimBounce-e27826be .5s;animation-direction:alternate}@keyframes eleMapPickerAnimBounce-e27826be{0%,60%,75%,90%,to{transition-timing-function:cubic-bezier(.215,.61,.355,1)}0%,to{transform:translateZ(0)}25%{transform:translate3d(0,-10px,0)}50%{transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,-10px,0)}}@media screen and (max-width: 768px){.guns-map-picker-responsive.guns-map-picker-dialog[data-v-e27826be]{top:0;padding:0}.guns-map-picker-responsive .guns-map-picker-body[data-v-e27826be]{display:block}.guns-map-picker-responsive .guns-map-picker-body .guns-map-picker-main>div[data-v-e27826be]{height:250px!important}.guns-map-picker-responsive .guns-map-picker-body .guns-map-picker-poi-list[data-v-e27826be]{width:auto;height:calc(100vh - 370px)!important}}\n',document.head.appendChild(a);var P={exports:{}};!function(e,a){e.exports=function(){function e(e){var o=[];return e.AMapUI&&o.push(a(e.AMapUI)),e.Loca&&o.push(n(e.Loca)),Promise.all(o)}function a(e){return new Promise((function(a,n){var i=[];if(e.plugins)for(var r=0;r<e.plugins.length;r+=1)-1==t.AMapUI.plugins.indexOf(e.plugins[r])&&i.push(e.plugins[r]);if(p.AMapUI===o.failed)n("前次请求 AMapUI 失败");else if(p.AMapUI===o.notload){p.AMapUI=o.loading,t.AMapUI.version=e.version||t.AMapUI.version,r=t.AMapUI.version;var s=document.body||document.head,c=document.createElement("script");c.type="text/javascript",c.src="https://webapi.amap.com/ui/"+r+"/main.js",c.onerror=function(e){p.AMapUI=o.failed,n("请求 AMapUI 失败")},c.onload=function(){if(p.AMapUI=o.loaded,i.length)window.AMapUI.loadUI(i,(function(){for(var e=0,n=i.length;e<n;e++){var o=i[e].split("/").slice(-1)[0];window.AMapUI[o]=arguments[e]}for(a();l.AMapUI.length;)l.AMapUI.splice(0,1)[0]()}));else for(a();l.AMapUI.length;)l.AMapUI.splice(0,1)[0]()},s.appendChild(c)}else p.AMapUI===o.loaded?e.version&&e.version!==t.AMapUI.version?n("不允许多个版本 AMapUI 混用"):i.length?window.AMapUI.loadUI(i,(function(){for(var e=0,n=i.length;e<n;e++){var o=i[e].split("/").slice(-1)[0];window.AMapUI[o]=arguments[e]}a()})):a():e.version&&e.version!==t.AMapUI.version?n("不允许多个版本 AMapUI 混用"):l.AMapUI.push((function(e){e?n(e):i.length?window.AMapUI.loadUI(i,(function(){for(var e=0,n=i.length;e<n;e++){var o=i[e].split("/").slice(-1)[0];window.AMapUI[o]=arguments[e]}a()})):a()}))}))}function n(e){return new Promise((function(a,n){if(p.Loca===o.failed)n("前次请求 Loca 失败");else if(p.Loca===o.notload){p.Loca=o.loading,t.Loca.version=e.version||t.Loca.version;var i=t.Loca.version,r=t.AMap.version.startsWith("2"),s=i.startsWith("2");if(r&&!s||!r&&s)n("JSAPI 与 Loca 版本不对应！！");else{r=t.key,s=document.body||document.head;var c=document.createElement("script");c.type="text/javascript",c.src="https://webapi.amap.com/loca?v="+i+"&key="+r,c.onerror=function(e){p.Loca=o.failed,n("请求 AMapUI 失败")},c.onload=function(){for(p.Loca=o.loaded,a();l.Loca.length;)l.Loca.splice(0,1)[0]()},s.appendChild(c)}}else p.Loca===o.loaded?e.version&&e.version!==t.Loca.version?n("不允许多个版本 Loca 混用"):a():e.version&&e.version!==t.Loca.version?n("不允许多个版本 Loca 混用"):l.Loca.push((function(e){e?n(e):n()}))}))}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var o,i;(i=o||(o={})).notload="notload",i.loading="loading",i.loaded="loaded",i.failed="failed";var t={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},p={AMap:o.notload,AMapUI:o.notload,Loca:o.notload},l={AMap:[],AMapUI:[],Loca:[]},r=[],s=function(e){"function"==typeof e&&(p.AMap===o.loaded?e(window.AMap):r.push(e))};return{load:function(a){return new Promise((function(n,i){if(p.AMap==o.failed)i("");else if(p.AMap==o.notload){var l=a.key,c=a.version,d=a.plugins;l?(window.AMap&&"lbs.amap.com"!==location.host&&i("禁止多种API加载方式混用"),t.key=l,t.AMap.version=c||t.AMap.version,t.AMap.plugins=d||t.AMap.plugins,p.AMap=o.loading,c=document.body||document.head,window.___onAPILoaded=function(t){if(delete window.___onAPILoaded,t)p.AMap=o.failed,i(t);else for(p.AMap=o.loaded,e(a).then((function(){n(window.AMap)})).catch(i);r.length;)r.splice(0,1)[0]()},(d=document.createElement("script")).type="text/javascript",d.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+t.AMap.version+"&key="+l+"&plugin="+t.AMap.plugins.join(","),d.onerror=function(e){p.AMap=o.failed,i(e)},c.appendChild(d)):i("请填写key")}else if(p.AMap==o.loaded)if(a.key&&a.key!==t.key)i("多个不一致的 key");else if(a.version&&a.version!==t.AMap.version)i("不允许多个版本 JSAPI 混用");else{if(l=[],a.plugins)for(c=0;c<a.plugins.length;c+=1)-1==t.AMap.plugins.indexOf(a.plugins[c])&&l.push(a.plugins[c]);l.length?window.AMap.plugin(l,(function(){e(a).then((function(){n(window.AMap)})).catch(i)})):e(a).then((function(){n(window.AMap)})).catch(i)}else if(a.key&&a.key!==t.key)i("多个不一致的 key");else if(a.version&&a.version!==t.AMap.version)i("不允许多个版本 JSAPI 混用");else{var u=[];if(a.plugins)for(c=0;c<a.plugins.length;c+=1)-1==t.AMap.plugins.indexOf(a.plugins[c])&&u.push(a.plugins[c]);s((function(){u.length?window.AMap.plugin(u,(function(){e(a).then((function(){n(window.AMap)})).catch(i)})):e(a).then((function(){n(window.AMap)})).catch(i)}))}}))},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,t={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},p={AMap:o.notload,AMapUI:o.notload,Loca:o.notload},l={AMap:[],AMapUI:[],Loca:[]}}}}()}(P);const _=n(P.exports),z={class:"guns-map-picker-header"},B={class:"guns-map-picker-header-search"},T={class:"guns-map-picker-body"},E={class:"guns-map-picker-main"},N=["src"],K=["onClick"],R={class:"guns-map-picker-poi-item-title"},j={key:0,class:"guns-map-picker-poi-item-address"},O="guns-map-picker-main-icon",Z={__name:"map-view",props:{height:{type:String,default:"450px"},center:Array,zoom:{type:Number,default:11},chooseZoom:{type:Number,default:17},poiSize:{type:Number,default:30},poiType:{type:String,default:""},poiKeywords:{type:String,default:""},poiRadius:{type:Number,default:1e3},needCity:Boolean,forceChoose:{type:Boolean,default:!0},suggestionCity:{type:String,default:"全国"},searchType:{type:Number,default:0,validator:e=>[0,1].includes(e)},searchPlaceholder:String,markerSrc:{type:String,default:"https://3gimg.qq.com/lightmap/components/locationPicker2/image/marker.png"},mapKey:String,mapVersion:{type:String,default:"2.0"},mapStyle:String,darkMode:Boolean},emits:["done","map-done"],setup(e,{emit:a}){const n=e,o=a,U=i(null),L=i(!1),C=i(!1),P=i(!1),Z=i([]),q=i([]),J=i([O]),$=i("");let V="",W=null,Y=!1,D=null,F=null,H=null,X=null;const G=()=>{n.mapKey&&!D&&_.load({key:n.mapKey,version:n.mapVersion,plugins:["AMap.PlaceSearch","AMap.AutoComplete"]}).then((e=>{ue(),H=new e.AutoComplete({city:n.suggestionCity}),F=new e.PlaceSearch({type:n.poiType,pageSize:n.poiSize,pageIndex:1});const a=n.mapStyle?n.mapStyle:n.darkMode?"amap://styles/dark":void 0;D=new e.Map(U.value,{zoom:n.zoom,center:n.center,resizeEnable:!0,mapStyle:a}),D.on("complete",(()=>{L.value=!1;const{lng:e,lat:a}=D.getCenter();oe(e,a)})),D.on("moveend",(()=>{if(Y)Y=!1;else if(0===n.searchType){pe();const{lng:e,lat:a}=D.getCenter();oe(e,a)}})),X=new e.Marker({icon:new e.Icon({image:n.markerSrc,size:new e.Size(26,36.5),imageSize:new e.Size(26,36.5)}),offset:new e.Pixel(-13,-36.5)}),o("map-done",D)})).catch((e=>{console.error(e)}))},Q=e=>{e&&V!==e&&(V=e,0!==n.searchType&&(C.value=!0),ne(e).then((e=>{0!==n.searchType?(Z.value=e,C.value=!1,le()):q.value=e})).catch((e=>{console.error(e),C.value=!1})))},ee=(e,a)=>{Z.value.length&&Z.value[0].name===a.name||(Z.value=[{...a,selected:!0},...Z.value.map((e=>({...e,selected:!1})))]),se(a.location.lng,a.location.lat,n.chooseZoom),W=a},ae=()=>{const e=te();if(!e)return n.forceChoose?void M.error("请点击列表选中位置"):(P.value=!0,void ce(n.needCity).then((e=>{P.value=!1,o("done",e)})).catch((e=>{console.error(e),P.value=!1,o("done",{})})));const a={...e.location,name:e.name,address:e.address||""};n.needCity?(P.value=!0,se(a.lng,a.lat),ce(!0).then((({city:e})=>{P.value=!1,a.city=e,o("done",a)})).catch((e=>{console.error(e),P.value=!1,o("done",a)}))):o("done",a)},ne=e=>new Promise(((a,n)=>{H?H.search(e,((e,n)=>{if(null==n||!n.tips)return void a([]);const o=n.tips.filter((e=>!!e.location)).map((e=>{const a=`${e.name}(${e.district})`;return{...e,label:a,value:a,key:e.id||a,address:Array.isArray(e.address)?e.address[0]:e.address}}));a(o)})):n(new Error("AutoComplete instance is null"))})),oe=(e,a)=>{C.value=!0,ie(e,a).then((e=>{W?0===e.length||e[0].name!==W.name?Z.value=[{...W,selected:!0},...e]:Z.value=e.map(((e,a)=>({...e,selected:0===a}))):Z.value=e,C.value=!1})).catch((e=>{console.error(e),C.value=!1,Z.value=[]}))},ie=(e,a)=>new Promise(((o,i)=>{F?F.searchNearBy(n.poiKeywords,[e,a],n.poiRadius,((e,a)=>{var n;if("complete"===e&&null!=a&&null!==(n=a.poiList)&&void 0!==n&&n.pois){const e=a.poiList.pois.filter((e=>!!e.location)).map(((e,a)=>({...e,key:e.id||`${e.name}-${a}`})));o(e)}else"no_data"===e?o([]):i(new Error(e))})):i(new Error("PlaceSearch instance is null"))})),te=()=>Z.value.find((e=>e.selected)),pe=()=>{J.value=[O],w((()=>{setTimeout((()=>{J.value=[O,"guns-map-picker-anim-bounce"]}),0)}))},le=()=>{X&&D&&D.remove(X)},re=(e,a)=>{X?D?null!=e&&null!=a?(X.setPosition([e,a]),D.add(X)):le():console.error("map instance is null"):console.error("centerMarker is null")},se=(e,a,n)=>{D&&null!=e&&null!=a&&(null==n?D.setCenter([e,a]):D.setZoomAndCenter(n,[e,a]))},ce=e=>new Promise(((a,n)=>{if(!D)return void n(new Error("map instance is null"));const o=D.getCenter();e?D.getCity((e=>{o.city=e,a(o)})):a(o)})),de=e=>{D&&("boolean"==typeof e?e?D.setMapStyle("amap://styles/dark"):D.setMapStyle("amap://styles/normal"):e&&D.setMapStyle(e))},ue=()=>{D&&D.destroy(),X=null,F=null,H=null,D=null,Z.value=[],q.value=[],$.value="",V="",W=null,Y=!1};return t((()=>n.darkMode),(e=>{n.mapStyle||de(e)})),t((()=>n.mapStyle),(e=>{e&&de(e)})),t((()=>n.searchType),(e=>{if($.value="",q.value=[],W=null,V="",le(),1===e){const e=te();if(e){const{lng:a,lat:n}=e.location;re(a,n)}}})),t((()=>n.mapKey),(()=>{ue(),G()})),p((()=>{G()})),l((()=>{ue()})),(a,o)=>{const i=x,t=S,p=r("PlusOutlined"),l=r("EnvironmentOutlined"),M=r("CheckCircleOutlined"),w=I,_=I;return s(),c(_,{spinning:L.value},{default:d((()=>[u("div",z,[u("div",B,[m(i,{"allow-clear":"",value:$.value,"onUpdate:value":o[0]||(o[0]=e=>$.value=e),options:q.value,placeholder:e.searchPlaceholder,onSelect:ee,onSearch:Q},null,8,["value","options","placeholder"])]),m(t,{type:"primary",loading:P.value,onClick:ae},{default:d((()=>o[1]||(o[1]=[g(" 确定 ")]))),_:1,__:[1]},8,["loading"])]),u("div",T,[u("div",E,[u("div",{ref_key:"mapRef",ref:U,style:v({height:e.height})},null,4),0===e.searchType?(s(),f(y,{key:0},[m(p,{class:"guns-map-picker-main-plus"}),u("img",{class:h(J.value),src:e.markerSrc,alt:""},null,10,N)],64)):k("",!0)]),m(w,{spinning:C.value},{default:d((()=>[u("div",{class:"guns-map-picker-poi-list",style:v({height:e.height})},[(s(!0),f(y,null,b(Z.value,(e=>(s(),f("div",{key:e.key,class:h(["guns-map-picker-poi-item",{active:e.selected}]),onClick:a=>(e=>{Y=!0,Z.value=Z.value.map((a=>({...a,selected:a===e})));const{lng:a,lat:o}=e.location;se(a,o,n.chooseZoom),0===n.searchType?pe():re(a,o)})(e)},[m(l,{class:"guns-map-picker-poi-item-icon"}),u("div",R,A(e.name),1),e.address?(s(),f("div",j,A(e.address),1)):k("",!0),m(M,{class:"guns-map-picker-poi-item-check"})],10,K)))),128))],4)])),_:1},8,["spinning"])])])),_:1},8,["spinning"])}}},q=o(Z,[["__scopeId","data-v-e27826be"]]);e("_",{__name:"index",props:{visible:Boolean,title:String,width:{type:String,default:"780px"},height:{type:String,default:"450px"},center:Array,zoom:{type:Number,default:11},chooseZoom:{type:Number,default:17},poiSize:{type:Number,default:30},poiType:{type:String,default:""},poiKeywords:{type:String,default:""},poiRadius:{type:Number,default:1e3},needCity:Boolean,forceChoose:{type:Boolean,default:!0},suggestionCity:{type:String,default:"全国"},searchType:{type:Number,default:0,validator:e=>[0,1].includes(e)},searchPlaceholder:{type:String,default:"请输入关键字搜索"},markerSrc:{type:String,default:"https://3gimg.qq.com/lightmap/components/locationPicker2/image/marker.png"},mapKey:String,mapVersion:{type:String,default:"2.0"},mapStyle:String,darkMode:Boolean},emits:["update:visible","done"],setup(e,{expose:a,emit:n}){const o=e,t=n,l=i(!1),r=i(!1),u=U((()=>o.mapKey||"698cdf3e7e17e75732d2659001e21660"));p((()=>{}));const g=e=>{t("update:visible",e)},v=e=>{t("done",e),g(!1)};return a({mapRef:r}),(e,a)=>{const n=C;return s(),c(n,{width:o.width,maskClosable:!1,visible:o.visible,"confirm-loading":l.value,forceRender:!0,title:"选择位置","body-style":{padding:0},"onUpdate:visible":g,footer:"",class:"common-modal",onClose:a[0]||(a[0]=e=>g(!1))},{default:d((()=>[m(q,L(o,{ref_key:"mapRef",ref:r,"map-key":u.value,onDone:v}),null,16,["map-key"])])),_:1},8,["width","visible","confirm-loading"])}}})}}}));
