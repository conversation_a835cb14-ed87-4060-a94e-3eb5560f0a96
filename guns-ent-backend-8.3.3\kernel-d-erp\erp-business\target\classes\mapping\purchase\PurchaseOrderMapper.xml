<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.purchase.mapper.PurchaseOrderMapper">

    <!-- 分页查询采购入库单列表 -->
    <select id="selectPurchaseOrderPage" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderResponse">
        SELECT
            po.id,
            po.order_no,
            po.supplier_id,
            po.status,
            po.total_amount,
            po.payment_method,
            po.payment_account,
            po.order_date,
            po.create_time,
            po.update_time,
            s.supplier_code,
            s.supplier_name,
            s.business_mode,
            s.sales_deduction
        FROM
            erp_purchase_order po
        LEFT JOIN
            erp_supplier s ON po.supplier_id = s.supplier_id
        <where>
            <if test="request.supplierId != null">
                AND po.supplier_id = #{request.supplierId}
            </if>
            <if test="request.status != null and request.status != ''">
                AND po.status = #{request.status}
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                AND po.order_no LIKE CONCAT('%', #{request.orderNo}, '%')
            </if>
            <if test="request.supplierName != null and request.supplierName != ''">
                AND s.supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
            </if>
            <if test="request.startDate != null">
                AND po.order_date >= #{request.startDate}
            </if>
            <if test="request.endDate != null">
                AND po.order_date &lt;= #{request.endDate}
            </if>
        </where>
        ORDER BY po.create_time DESC
    </select>

    <!-- 根据供应商ID统计采购金额 -->
    <select id="getTotalAmountBySupplier" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(po.total_amount), 0)
        FROM
            erp_purchase_order po
        WHERE
            po.supplier_id = #{supplierId}
            AND po.status = 'COMPLETED'
            <if test="startDate != null">
                AND po.order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND po.order_date &lt;= #{endDate}
            </if>
    </select>

    <!-- 统计各状态的采购入库单数量 -->
    <select id="getOrderCountByStatus" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderStatusCountResponse">
        SELECT
            status,
            COUNT(1) as count,
            COALESCE(SUM(total_amount), 0) as totalAmount
        FROM
            erp_purchase_order
        <where>
            <if test="supplierId != null">
                AND supplier_id = #{supplierId}
            </if>
            <if test="startDate != null">
                AND order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND order_date &lt;= #{endDate}
            </if>
        </where>
        GROUP BY status
    </select>

    <!-- 获取供应商的最近采购记录 -->
    <select id="getRecentOrdersBySupplier" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderResponse">
        SELECT
            po.id,
            po.order_no,
            po.supplier_id,
            po.status,
            po.total_amount,
            po.payment_method,
            po.payment_account,
            po.order_date,
            po.create_time,
            po.update_time,
            s.supplier_code,
            s.supplier_name,
            s.business_mode
        FROM
            erp_purchase_order po
        LEFT JOIN
            erp_supplier s ON po.supplier_id = s.supplier_id
        WHERE
            po.supplier_id = #{supplierId}
        ORDER BY po.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取月度采购统计 -->
    <select id="getMonthlyPurchaseStats" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.MonthlyPurchaseStatsResponse">
        SELECT
            DATE_FORMAT(po.order_date, '%Y-%m') as month,
            COUNT(1) as orderCount,
            COALESCE(SUM(po.total_amount), 0) as totalAmount,
            COUNT(DISTINCT po.supplier_id) as supplierCount
        FROM
            erp_purchase_order po
        WHERE
            po.status = 'COMPLETED'
            <if test="startDate != null">
                AND po.order_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND po.order_date &lt;= #{endDate}
            </if>
        GROUP BY DATE_FORMAT(po.order_date, '%Y-%m')
        ORDER BY month DESC
    </select>

</mapper>