<template>
  <a-modal
    title="区域详情"
    :width="800"
    :visible="visible"
    :footer="null"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="区域编码">
        {{ form.regionCode || '无' }}
      </a-descriptions-item>
      <a-descriptions-item label="区域名称">
        {{ form.regionName || '无' }}
      </a-descriptions-item>
      <a-descriptions-item label="区域层级">
        <a-tag v-if="form.regionLevel === 1" color="red">国家</a-tag>
        <a-tag v-else-if="form.regionLevel === 2" color="orange">省</a-tag>
        <a-tag v-else-if="form.regionLevel === 3" color="yellow">市</a-tag>
        <a-tag v-else-if="form.regionLevel === 4" color="green">区县</a-tag>
        <a-tag v-else-if="form.regionLevel === 5" color="blue">商圈</a-tag>
        <a-tag v-else color="default">未知</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="父级区域">
        {{ form.parentRegionName || '无' }}
      </a-descriptions-item>
      <a-descriptions-item label="排序号">
        {{ form.sortOrder || 0 }}
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag v-if="form.status === 'Y'" color="green">启用</a-tag>
        <a-tag v-else-if="form.status === 'N'" color="red">停用</a-tag>
        <a-tag v-else color="default">{{ form.status || '未知' }}</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="备注" :span="2">
        {{ form.remark || '无' }}
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { RegionApi } from '../api/regionApi'

export default {
  name: 'RegionDetailForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const form = reactive({})

    const updateVisible = (visible) => {
      emit('update:visible', visible)
      if (!visible) {
        Object.keys(form).forEach(key => {
          delete form[key]
        })
      }
    }

    const detail = async (record) => {
      try {
        const res = await RegionApi.detail({ regionId: record.regionId })
        Object.assign(form, res)
      } catch (error) {
        console.error('获取区域详情失败:', error)
        message.error('获取详情失败')
      }
    }

    // 监听props.data变化，用于显示详情
    watch(() => props.data, (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        // 如果传入的数据有regionId，则调用API获取完整详情
        if (newData.regionId) {
          detail(newData)
        } else {
          Object.assign(form, newData)
        }
      }
    }, { immediate: true })

    // 监听visible变化
    watch(() => props.visible, (visible) => {
      if (visible && props.data && props.data.regionId) {
        detail(props.data)
      }
    })

    return {
      form,
      updateVisible,
      detail
    }
  }
}
</script>
