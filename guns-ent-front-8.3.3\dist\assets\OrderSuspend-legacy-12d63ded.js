System.register(["./index-legacy-ee1db0c7.js","./pos-legacy-fe4fee5f.js","./OrderSummary-legacy-e7630f7e.js","./SuspendedOrderItem-legacy-38b299a9.js","./common-legacy-b1e2d362.js","./formatter-legacy-97503ee9.js","./constants-legacy-2a31d63c.js"],(function(e,a){"use strict";var d,s,t,r,l,n,o,u,c,i,p,v,f,m,y,x,g,b,h,w,k,_,O,S,C,j,I,E;return{setters:[e=>{d=e._,s=e.L,t=e.r,r=e.o,l=e.a,n=e.f,o=e.w,u=e.b,c=e.d,i=e.g,p=e.at,v=e.aW,f=e.aX,m=e.h,y=e.c,x=e.t,g=e.ad,b=e.F,h=e.e,w=e.a5,k=e.m,_=e.I,O=e.B,S=e.M,C=e.aY},e=>{j=e.u},e=>{I=e.default},e=>{E=e.default},null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".order-suspend[data-v-33c81e21]{height:100%;display:flex;flex-direction:column}.suspend-header[data-v-33c81e21]{display:flex;align-items:center;justify-content:space-between;padding:16px 20px 12px;border-bottom:1px solid #f0f0f0}.suspend-title[data-v-33c81e21]{margin:0;font-size:16px;font-weight:600;color:#262626;display:flex;align-items:center;gap:8px}.suspend-actions[data-v-33c81e21]{display:flex;align-items:center;gap:8px}.suspend-btn[data-v-33c81e21]{background:#722ed1;border-color:#722ed1}.suspend-btn[data-v-33c81e21]:hover:not(:disabled){background:#9254de;border-color:#9254de}.clear-btn[data-v-33c81e21]{color:#ff4d4f}.clear-btn[data-v-33c81e21]:hover:not(:disabled){color:#ff7875;background:#fff2f0}.current-order[data-v-33c81e21]{padding:16px 20px;background:#f8f9fa;border-bottom:1px solid #f0f0f0}.suspended-orders[data-v-33c81e21]{flex:1;display:flex;flex-direction:column;overflow:hidden}.orders-header[data-v-33c81e21]{display:flex;align-items:center;justify-content:space-between;padding:16px 20px 8px}.orders-title[data-v-33c81e21]{font-size:14px;font-weight:500;color:#262626}.orders-list[data-v-33c81e21]{flex:1;overflow-y:auto;padding:0 20px 20px}.empty-orders[data-v-33c81e21]{flex:1;display:flex;align-items:center;justify-content:center;padding:40px 20px}.empty-tip[data-v-33c81e21]{margin-top:8px;color:#8c8c8c;font-size:12px}.suspend-drawer .drawer-content[data-v-33c81e21],.suspend-drawer .order-suspend[data-v-33c81e21]{height:100%;display:flex;flex-direction:column}.suspend-drawer .suspended-orders[data-v-33c81e21]{flex:1;overflow-y:auto}.suspend-placeholder[data-v-33c81e21]{height:200px;display:flex;align-items:center;justify-content:center;background:#fafafa;border-radius:8px;border:1px dashed #d9d9d9}\n",document.head.appendChild(a);const z={class:"order-suspend drawer-content pos-card"},M={class:"suspend-header"},D={class:"suspend-title"},R={class:"suspend-actions"},P={key:0,class:"current-order"},A={class:"suspended-orders"},B={class:"orders-header"},L={class:"orders-title"},T={key:0,class:"orders-list"},U={key:1,class:"empty-orders"},F={key:1,class:"order-suspend pos-card"},G={class:"suspend-placeholder"},N=Object.assign({name:"OrderSuspend"},{__name:"OrderSuspend",props:{visible:{type:Boolean,default:void 0}},emits:["orderSuspended","orderResumed","orderDeleted","update:visible"],setup(e,{expose:a,emit:d}){const N=e,W=d,X=s({get:()=>N.visible,set:e=>W("update:visible",e)}),Y=t(!1),$=t(!1),q=t(!1),H=t(""),J=t(""),K=t("确定"),Q=t(!1),V=t(null),Z=j(),ee=s((()=>Z.cartItemCount)),ae=s((()=>Z.totalAmount)),de=s((()=>Z.hasCartItems));s((()=>Z.hasMember));const se=s((()=>Z.currentMember)),te=s((()=>Z.suspendedOrders)),re=s((()=>Z.hasSuspendedOrders)),le=s((()=>de.value&&!Y.value)),ne=async()=>{if(le.value)try{Y.value=!0,await Z.suspendCurrentOrder()&&(W("orderSuspended"),k.success("订单已挂起"))}catch(e){console.error("挂起订单失败:",e),k.error("挂起订单失败，请重试")}finally{Y.value=!1}else k.warning("当前没有可挂起的订单")},oe=e=>{H.value="恢复订单",J.value="恢复此订单将清空当前购物车，是否继续？",K.value="恢复订单",V.value={type:"resume",suspendId:e},q.value=!0},ue=e=>{H.value="删除订单",J.value="确定要删除这个挂起的订单吗？此操作不可恢复。",K.value="删除",V.value={type:"delete",suspendId:e},q.value=!0},ce=()=>{H.value="清理过期订单",J.value="确定要清理所有过期的挂起订单吗？",K.value="清理",V.value={type:"clearExpired"},q.value=!0},ie=async()=>{try{$.value=!0,await new Promise((e=>setTimeout(e,500))),k.success("刷新完成")}catch(e){console.error("刷新挂起订单失败:",e),k.error("刷新失败，请重试")}finally{$.value=!1}},pe=async()=>{if(V.value)try{Q.value=!0;const{type:e,suspendId:a}=V.value;switch(e){case"resume":await Z.resumeSuspendedOrder(a),W("orderResumed",a),k.success("订单已恢复");break;case"delete":await Z.deleteSuspendedOrder(a),W("orderDeleted",a),k.success("订单已删除");break;case"clearExpired":await Z.clearExpiredSuspendedOrders(),k.success("过期订单已清理")}q.value=!1}catch(e){console.error("操作失败:",e),k.error("操作失败，请重试")}finally{Q.value=!1,V.value=null}},ve=()=>{q.value=!1,V.value=null};return r((()=>{})),a({suspendCurrentOrder:ne,resumeOrder:oe,deleteOrder:ue,refreshSuspendedOrders:ie}),(a,d)=>{const s=_,t=O,r=w,k=S,j=C;return void 0!==e.visible?(l(),n(j,{key:0,open:X.value,"onUpdate:open":d[1]||(d[1]=e=>X.value=e),title:"挂单管理",placement:"right",width:"500",class:"suspend-drawer"},{default:o((()=>[u("div",z,[u("div",M,[u("h3",D,[c(s,{iconClass:"icon-suspend"}),d[2]||(d[2]=i(" 挂单管理 "))]),u("div",R,[c(t,{type:"primary",disabled:!le.value,onClick:ne,loading:Y.value,class:"suspend-btn"},{icon:o((()=>[c(p(v))])),default:o((()=>[d[3]||(d[3]=i(" 挂起当前订单 "))])),_:1,__:[3]},8,["disabled","loading"]),re.value?(l(),n(t,{key:0,type:"text",onClick:ce,class:"clear-btn"},{icon:o((()=>[c(p(f))])),default:o((()=>[d[4]||(d[4]=i(" 清理过期 "))])),_:1,__:[4]})):m("",!0)])]),de.value?(l(),y("div",P,[c(I,{itemCount:ee.value,amount:ae.value,member:se.value},null,8,["itemCount","amount","member"])])):m("",!0),u("div",A,[u("div",B,[u("span",L,"挂起订单 ("+x(te.value.length)+")",1),te.value.length>0?(l(),n(t,{key:0,type:"text",size:"small",onClick:ie,loading:$.value},{icon:o((()=>[c(p(g))])),default:o((()=>[d[5]||(d[5]=i(" 刷新 "))])),_:1,__:[5]},8,["loading"])):m("",!0)]),te.value.length>0?(l(),y("div",T,[(l(!0),y(b,null,h(te.value,(e=>(l(),n(E,{key:e.suspendId,order:e,onResume:oe,onDelete:ue},null,8,["order"])))),128))])):(l(),y("div",U,[c(r,{description:"暂无挂起订单",image:p(w).PRESENTED_IMAGE_SIMPLE},{default:o((()=>d[6]||(d[6]=[u("p",{class:"empty-tip"},"当前没有挂起的订单",-1)]))),_:1,__:[6]},8,["image"])]))]),c(k,{open:q.value,"onUpdate:open":d[0]||(d[0]=e=>q.value=e),title:H.value,"ok-text":K.value,"cancel-text":"取消",onOk:pe,onCancel:ve,"confirm-loading":Q.value},{default:o((()=>[u("p",null,x(J.value),1)])),_:1},8,["open","title","ok-text","confirm-loading"])])])),_:1},8,["open"])):(l(),y("div",F,[u("div",G,[c(r,{description:"请点击挂单列表按钮查看挂单"})])]))}}});e("default",d(N,[["__scopeId","data-v-33c81e21"]]))}}}));
