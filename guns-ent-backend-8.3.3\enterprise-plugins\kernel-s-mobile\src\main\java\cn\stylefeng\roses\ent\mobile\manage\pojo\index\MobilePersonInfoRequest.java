package cn.stylefeng.roses.ent.mobile.manage.pojo.index;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 移动端-更新个人信息的请求
 *
 * <AUTHOR>
 * @since 2024/3/21 21:10
 */
@Data
public class MobilePersonInfoRequest {

    /**
     * 用户头像id
     */
    @ChineseDescription("用户头像id")
    @NotNull(message = "用户头像id不能为空")
    private Long avatar;

    /**
     * 真实姓名
     */
    @ChineseDescription("真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    /**
     * 性别：M-男，F-女
     */
    @ChineseDescription("性别：M-男，F-女")
    @NotBlank(message = "性别不能为空")
    private String sex;

    /**
     * 邮箱
     */
    @ChineseDescription("邮箱")
    @NotBlank(message = "邮箱不能为空")
    private String email;

}
