package cn.stylefeng.roses.kernel.pay.factory;

import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.pay.api.enums.OrderInvoiceStatusEnum;
import cn.stylefeng.roses.kernel.pay.entity.OrderInvoice;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderInvoiceRequest;

import java.math.BigDecimal;

/**
 * 订单发票创建
 *
 * <AUTHOR>
 * @since 2024/6/24 15:41
 */
public class OrderInvoiceFactory {

    /**
     * 创建订单发票
     *
     * <AUTHOR>
     * @since 2024/6/24 15:41
     */
    public static OrderInvoice createOrderInvoice(OrderInvoiceRequest orderInvoiceRequest, BigDecimal totalAmount) {
        OrderInvoice orderInvoice = new OrderInvoice();

        orderInvoice.setOrderIdList(orderInvoiceRequest.getOrderIdList());
        orderInvoice.setInvoiceTitle(orderInvoiceRequest.getInvoiceTitle());
        orderInvoice.setTaxpayerNo(orderInvoiceRequest.getTaxpayerNo());
        orderInvoice.setAddress(orderInvoiceRequest.getAddress());
        orderInvoice.setPhone(orderInvoiceRequest.getPhone());
        orderInvoice.setBankName(orderInvoiceRequest.getBankName());
        orderInvoice.setBankAccountNo(orderInvoiceRequest.getBankAccountNo());
        orderInvoice.setSendEmail(orderInvoiceRequest.getSendEmail());
        orderInvoice.setDescription(orderInvoiceRequest.getDescription());

        // 设置发票总金额
        orderInvoice.setTotalAmount(totalAmount);

        // 设置发票申请人为当前用户
        orderInvoice.setUserId(LoginContext.me().getLoginUser().getUserId());

        // 设置发票为申请装状态
        orderInvoice.setOrderInvoiceStatus(OrderInvoiceStatusEnum.APPLYING.getCode());

        return orderInvoice;
    }

}
