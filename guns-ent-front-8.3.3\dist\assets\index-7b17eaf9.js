import{r as o,o as B,X as V,a as h,c as b,d as m,w as g,f as k,h as N,l as O,bf as R,a0 as F}from"./index-18a1ea24.js";/* empty css              */import{_ as L}from"./index-3a0e5c06.js";import{C as j}from"./CommonApi-27ae49e3.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const I={class:"wh100"},U={__name:"index",props:{value:{type:String,default:""},record:{type:Object,default:{}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(f,{emit:d}){const a=f,p=d,s=o(!0),c=o(!1),l=o([]),u=o(""),i=o(!1),r=o({selectDictList:[]});B(()=>{var e;(e=a.record)!=null&&e.itemMultipleChoiceFlag||a.multiple?s.value=!1:s.value=!0,v()});const v=async()=>{if(a.value)if(s.value){if(c.value)return;const e=await j.getDictName({dictId:a.value});e.data&&(l.value=[{id:a.value,name:e.data}])}else l.value=a.normal?l.value:JSON.parse(a.value);else l.value=[];c.value=!1,u.value=l.value.map(e=>e.name).join("\uFF1B")},y=()=>{var e,t;(e=l.value)!=null&&e.length?r.value.selectDictList=(t=l.value)==null?void 0:t.map(n=>({bizId:n.id,name:n.name})):r.value.selectDictList=[],i.value=!0},C=e=>{var t;l.value=(t=e.selectDictList)==null?void 0:t.map(n=>({id:n.bizId,name:n.name})),u.value=e.selectDictList.map(n=>n.name).join("\uFF1B"),c.value=!0,D()},D=()=>{let e=l.value.length>0?s.value?l.value[0].id:a.normal?l.value:JSON.stringify(l.value):"";p("update:value",e),p("onChange",a.record),w()},w=async()=>{var e;!a.normal&&((e=a.formRef)!=null&&e.validateFields)&&await a.formRef.validateFields([a.record.fieldCode])};return V(()=>a.value,e=>{v()},{deep:!0}),(e,t)=>{const n=O,S=L,x=R;return h(),b("div",I,[m(n,{value:u.value,"onUpdate:value":t[0]||(t[0]=_=>u.value=_),disabled:a.readonly||a.disabled,class:"w-full",placeholder:f.placeholder,onFocus:y},null,8,["value","disabled","placeholder"]),m(x,null,{default:g(()=>[i.value?(h(),k(S,{key:0,visible:i.value,"onUpdate:visible":t[1]||(t[1]=_=>i.value=_),data:r.value,showTab:["dict"],changeHeight:!0,title:"\u8BF7\u9009\u62E9\u5B57\u5178",isRadio:s.value,onDone:C},null,8,["visible","data","isRadio"])):N("",!0)]),_:1})])}}},z={class:"guns-body guns-body-card"},P={__name:"index",setup(f){const d=o(""),a=o(!1),p=o(!1),s=o("\u8BF7\u9009\u62E9"),c=()=>{console.log(d.value)};return(l,u)=>{const i=U,r=F;return h(),b("div",z,[m(r,{title:"\u5B57\u5178\u9009\u62E9",bordered:!1},{default:g(()=>[m(i,{value:d.value,"onUpdate:value":u[0]||(u[0]=v=>d.value=v),disabled:a.value,readonly:p.value,onOnChange:c,placeholder:s.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{P as default};
