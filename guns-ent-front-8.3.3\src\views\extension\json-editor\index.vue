<template>
  <div class="guns-body guns-body-card">
    <a-card title="json编辑器" :bordered="false">
      <!-- ['tree', 'code', 'form', 'text', 'view'] -->
      <json-editor-vue :mode-list="['tree', 'code', 'form', 'text', 'view']" style="height: 400px" v-model="selectValue" />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import JsonEditorVue from 'json-editor-vue3';

// 选中的值
const selectValue = ref('');
</script>

<style></style>
