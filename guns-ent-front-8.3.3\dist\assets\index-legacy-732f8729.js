System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./ThemeTemplateFieldApi-legacy-437b0971.js","./attr-add-edit-legacy-f68e3942.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./attr-form-legacy-083a2ae8.js","./index-legacy-94a6fc23.js"],(function(e,l){"use strict";var i,t,a,n,s,d,o,c,u,r,f,y,h,v,m,p,w,x,g,k,b,C,I;return{setters:[e=>{i=e._},e=>{t=e.r,a=e.o,n=e.k,s=e.a,d=e.c,o=e.b,c=e.d,u=e.w,r=e.g,f=e.t,y=e.h,h=e.F,v=e.f,m=e.M,p=e.E,w=e.m,x=e.n,g=e.B,k=e.I,b=e.l},e=>{C=e.T},e=>{I=e.default},null,null,null,null,null],execute:function(){const l={class:"guns-layout"},T={class:"guns-layout-content"},_={class:"guns-layout"},S={class:"guns-layout-content-application"},j={class:"content-mian"},N={class:"content-mian-header"},R={class:"header-content"},z={class:"header-content-left"},q={class:"header-content-right"},B={class:"content-mian-body"},E={class:"table-content"},M=["onClick"],A={key:0},F={key:1};e("default",{__name:"index",setup(e){const L=t([{key:"index",title:"序号",width:60,align:"center",isShow:!0,hideInSetting:!0},{title:"属性名称",isShow:!0,dataIndex:"fieldName"},{title:"属性编码",isShow:!0,dataIndex:"fieldCode"},{title:"属性类型",isShow:!0,dataIndex:"fieldType"},{title:"是否必填",isShow:!0,width:100,dataIndex:"fieldRequired"},{title:"属性长度",isShow:!0,width:100,dataIndex:"fieldLength"},{title:"属性描述",isShow:!0,dataIndex:"fieldDescription"},{key:"action",title:"操作",width:100,isShow:!0}]),D=t(null),P=t({fieldName:""}),U=t(null),H=t(!1);a((()=>{}));const K=()=>{D.value.reload()},O=e=>{U.value=e,H.value=!0};return(e,t)=>{const a=x,Y=n("plus-outlined"),G=g,J=k,Q=b,V=i;return s(),d("div",l,[o("div",T,[o("div",_,[o("div",S,[o("div",j,[o("div",N,[o("div",R,[o("div",z,[c(a,{size:16})]),o("div",q,[c(a,{size:16},{default:u((()=>[c(G,{type:"primary",class:"border-radius",onClick:t[0]||(t[0]=e=>O())},{default:u((()=>[c(Y),t[3]||(t[3]=r("新建"))])),_:1,__:[3]})])),_:1})])])]),o("div",B,[o("div",E,[c(V,{columns:L.value,where:P.value,rowId:"fieldId",ref_key:"tableRef",ref:D,rowSelection:!1,url:"/sysThemeTemplateField/findPage",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"THMEM_ATTR_TABLE"},{toolLeft:u((()=>[c(Q,{value:P.value.fieldName,"onUpdate:value":t[1]||(t[1]=e=>P.value.fieldName=e),placeholder:"主题属性名称（回车搜索）",onPressEnter:K,class:"search-input",bordered:!1,style:{width:"240px"}},{prefix:u((()=>[c(J,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:u((({column:e,record:l})=>["fieldName"==e.dataIndex?(s(),d("a",{key:0,onClick:e=>O(l)},f(l.fieldName),9,M)):y("",!0),"fieldRequired"==e.dataIndex?(s(),d(h,{key:1},["Y"==l.fieldRequired?(s(),d("span",A,"是")):y("",!0),"N"==l.fieldRequired?(s(),d("span",F,"否")):y("",!0)],64)):y("",!0),"action"==e.key?(s(),v(a,{key:2,size:16},{default:u((()=>[c(J,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>O(l)},null,8,["onClick"]),c(J,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{m.confirm({title:"提示",content:"确定要删除选中的属性吗?",icon:c(p),maskClosable:!0,onOk:async()=>{const l=await C.del({fieldId:e.fieldId});w.success(l.message),K()}})})(l)},null,8,["onClick"])])),_:2},1024)):y("",!0)])),_:1},8,["columns","where"])])])])])])]),H.value?(s(),v(I,{key:0,visible:H.value,"onUpdate:visible":t[2]||(t[2]=e=>H.value=e),data:U.value,onDone:K},null,8,["visible","data"])):y("",!0)])}}})}}}));
