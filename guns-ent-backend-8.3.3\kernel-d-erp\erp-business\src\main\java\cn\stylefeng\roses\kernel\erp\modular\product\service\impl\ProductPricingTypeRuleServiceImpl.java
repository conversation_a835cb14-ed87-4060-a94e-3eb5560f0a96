package cn.stylefeng.roses.kernel.erp.modular.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpProductConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpProductExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse;
import cn.stylefeng.roses.kernel.erp.modular.product.service.ProductPricingTypeRuleService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品计价类型业务规则服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/27 23:05
 */
@Slf4j
@Service
public class ProductPricingTypeRuleServiceImpl implements ProductPricingTypeRuleService {

    @Override
    public boolean validatePricingType(String pricingType) {
        if (StrUtil.isBlank(pricingType)) {
            return false;
        }

        for (String validType : ErpProductConstants.ALL_PRICING_TYPES) {
            if (validType.equals(pricingType)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean validateProductPricing(ErpProduct product) {
        if (product == null || StrUtil.isBlank(product.getPricingType())) {
            return false;
        }

        String pricingType = product.getPricingType();
        
        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                // 普通商品必须设置零售价格
                return product.getRetailPrice() != null && product.getRetailPrice().compareTo(BigDecimal.ZERO) > 0;
                
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                // 计重商品必须设置单位价格
                return product.getUnitPrice() != null && product.getUnitPrice().compareTo(BigDecimal.ZERO) > 0;
                
            case ErpProductConstants.PRICING_TYPE_PIECE:
                // 计件商品必须设置单份价格
                return product.getPiecePrice() != null && product.getPiecePrice().compareTo(BigDecimal.ZERO) > 0;
                
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                // 不定价商品可以设置参考价格，但不是必须的
                return true;
                
            default:
                return false;
        }
    }

    @Override
    public PricingTypeChangeValidationResponse validatePricingTypeChange(ErpProduct currentProduct, String newPricingType) {
        PricingTypeChangeValidationResponse response = new PricingTypeChangeValidationResponse();
        
        String currentPricingType = currentProduct.getPricingType();
        
        // 如果计价类型没有变化，直接返回可以变更
        if (StrUtil.equals(currentPricingType, newPricingType)) {
            response.setCanChange(true);
            response.setAffectedInventoryCount(0);
            response.setAffectedSalesCount(0);
            response.setRequiredPriceFields(CollUtil.newArrayList());
            response.setDescription("计价类型未发生变化");
            return response;
        }

        // 验证新的计价类型是否有效
        if (!this.validatePricingType(newPricingType)) {
            response.setCanChange(false);
            response.setErrorMessage("无效的计价类型：" + newPricingType);
            response.setDescription("计价类型只能为：普通、计重、计件、不定价");
            return response;
        }

        // TODO: 检查商品是否有库存记录和销售记录
        // 这里需要查询库存表和销售表中product_id等于当前商品ID的记录数量
        // 暂时设置为0，等相关模块扩展完成后再实现
        int affectedInventoryCount = 0;
        int affectedSalesCount = 0;
        
        response.setCanChange(true);
        response.setAffectedInventoryCount(affectedInventoryCount);
        response.setAffectedSalesCount(affectedSalesCount);
        
        // 确定需要重新设置的价格字段
        List<String> requiredPriceFields = this.getRequiredPriceFields(newPricingType);
        response.setRequiredPriceFields(requiredPriceFields);
        
        // 根据计价类型变更类型给出不同的提示
        String changeDescription = this.getPricingTypeChangeDescription(currentPricingType, newPricingType);
        response.setDescription(changeDescription);
        
        if (affectedInventoryCount > 0 || affectedSalesCount > 0) {
            response.setWarningMessage(String.format("变更计价类型将影响 %d 条库存记录和 %d 条销售记录", 
                    affectedInventoryCount, affectedSalesCount));
        }
        
        return response;
    }

    @Override
    public String getPricingTypeDescription(String pricingType) {
        if (StrUtil.isBlank(pricingType)) {
            return "未知计价类型";
        }

        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                return "普通商品：按固定零售价格销售";
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                return "计重商品：按重量计价，需要称重后计算价格";
            case ErpProductConstants.PRICING_TYPE_PIECE:
                return "计件商品：按件数计价，每件价格固定";
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                return "不定价商品：价格可变，销售时确定价格";
            default:
                return "未知计价类型";
        }
    }

    @Override
    public boolean needRetailPrice(String pricingType) {
        return ErpProductConstants.PRICING_TYPE_NORMAL.equals(pricingType);
    }

    @Override
    public boolean needUnitPrice(String pricingType) {
        return ErpProductConstants.PRICING_TYPE_WEIGHT.equals(pricingType);
    }

    @Override
    public boolean needPiecePrice(String pricingType) {
        return ErpProductConstants.PRICING_TYPE_PIECE.equals(pricingType);
    }

    @Override
    public boolean allowReferencePrice(String pricingType) {
        // 所有计价类型都允许设置参考价格
        return this.validatePricingType(pricingType);
    }

    @Override
    public void validateProductPricingTypeParams(ErpProduct product) {
        if (product == null) {
            throw new ServiceException(ErpProductConstants.PRODUCT_MODULE_NAME, 
                    ErpProductExceptionEnum.PRODUCT_NOT_EXIST.getErrorCode(), 
                    "商品信息不能为空");
        }

        String pricingType = product.getPricingType();

        // 验证计价类型
        if (!this.validatePricingType(pricingType)) {
            throw new ServiceException(ErpProductConstants.PRODUCT_MODULE_NAME, 
                    ErpProductExceptionEnum.PRODUCT_PRICING_TYPE_ERROR);
        }

        // 验证价格设置
        if (!this.validateProductPricing(product)) {
            String errorMessage = this.getPricingValidationErrorMessage(pricingType);
            throw new ServiceException(ErpProductConstants.PRODUCT_MODULE_NAME, 
                    ErpProductExceptionEnum.PRODUCT_RETAIL_PRICE_REQUIRED.getErrorCode(), 
                    errorMessage);
        }

        // 验证参考价格格式
        if (product.getReferencePrice() != null && product.getReferencePrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(ErpProductConstants.PRODUCT_MODULE_NAME, 
                    ErpProductExceptionEnum.PRODUCT_REFERENCE_PRICE_FORMAT_ERROR);
        }

        log.info("商品计价类型参数验证通过，商品ID：{}，计价类型：{}，零售价：{}，单位价：{}，单份价：{}，参考价：{}", 
                product.getProductId(), pricingType, product.getRetailPrice(), 
                product.getUnitPrice(), product.getPiecePrice(), product.getReferencePrice());
    }

    @Override
    public BigDecimal calculateDisplayPrice(ErpProduct product) {
        if (product == null || StrUtil.isBlank(product.getPricingType())) {
            return BigDecimal.ZERO;
        }

        String pricingType = product.getPricingType();
        
        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                return product.getRetailPrice() != null ? product.getRetailPrice() : BigDecimal.ZERO;
                
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                return product.getUnitPrice() != null ? product.getUnitPrice() : BigDecimal.ZERO;
                
            case ErpProductConstants.PRICING_TYPE_PIECE:
                return product.getPiecePrice() != null ? product.getPiecePrice() : BigDecimal.ZERO;
                
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                return product.getReferencePrice() != null ? product.getReferencePrice() : BigDecimal.ZERO;
                
            default:
                return BigDecimal.ZERO;
        }
    }

    @Override
    public String getPriceLabel(String pricingType) {
        if (StrUtil.isBlank(pricingType)) {
            return "价格";
        }

        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                return "零售价";
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                return "单位价";
            case ErpProductConstants.PRICING_TYPE_PIECE:
                return "单份价";
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                return "参考价";
            default:
                return "价格";
        }
    }

    /**
     * 获取计价类型变更的描述信息
     *
     * @param currentType 当前计价类型
     * @param newType 新计价类型
     * @return 变更描述
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    private String getPricingTypeChangeDescription(String currentType, String newType) {
        StringBuilder description = new StringBuilder();
        description.append("计价类型变更：");
        description.append(this.getPricingTypeName(currentType));
        description.append(" → ");
        description.append(this.getPricingTypeName(newType));
        
        // 添加变更影响说明
        description.append("\n\n变更影响：");
        
        // 价格字段变更
        List<String> currentRequiredFields = this.getRequiredPriceFields(currentType);
        List<String> newRequiredFields = this.getRequiredPriceFields(newType);
        
        if (!currentRequiredFields.equals(newRequiredFields)) {
            description.append("\n• 需要重新设置价格字段：");
            description.append(String.join("、", newRequiredFields));
        }
        
        // 计价方式变更
        description.append("\n• 计价方式：");
        description.append(this.getPricingTypeDescription(newType));
        
        return description.toString();
    }

    /**
     * 获取计价类型名称
     *
     * @param pricingType 计价类型
     * @return 计价类型名称
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    private String getPricingTypeName(String pricingType) {
        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                return "普通";
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                return "计重";
            case ErpProductConstants.PRICING_TYPE_PIECE:
                return "计件";
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                return "不定价";
            default:
                return "未知";
        }
    }

    /**
     * 获取计价类型需要的价格字段
     *
     * @param pricingType 计价类型
     * @return 需要的价格字段列表
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    private List<String> getRequiredPriceFields(String pricingType) {
        List<String> fields = new ArrayList<>();
        
        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                fields.add("零售价");
                break;
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                fields.add("单位价");
                break;
            case ErpProductConstants.PRICING_TYPE_PIECE:
                fields.add("单份价");
                break;
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                fields.add("参考价（可选）");
                break;
        }
        
        return fields;
    }

    /**
     * 获取价格验证错误信息
     *
     * @param pricingType 计价类型
     * @return 错误信息
     * <AUTHOR>
     * @since 2025/07/27 23:05
     */
    private String getPricingValidationErrorMessage(String pricingType) {
        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                return "普通商品必须设置零售价格";
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                return "计重商品必须设置单位价格";
            case ErpProductConstants.PRICING_TYPE_PIECE:
                return "计件商品必须设置单份价格";
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                return "不定价商品价格设置有误";
            default:
                return "商品价格设置不符合计价类型要求";
        }
    }

}