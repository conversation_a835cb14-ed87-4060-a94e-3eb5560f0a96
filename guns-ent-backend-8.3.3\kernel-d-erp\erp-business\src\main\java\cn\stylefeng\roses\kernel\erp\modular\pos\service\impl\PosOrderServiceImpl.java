package cn.stylefeng.roses.kernel.erp.modular.pos.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem;
import cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosOrderMapper;
import cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosOrderItemMapper;
import cn.stylefeng.roses.kernel.erp.api.exception.ErpException;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.erp.modular.pos.service.PosOrderService;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * POS订单服务实现类
 *
 * <AUTHOR>
 * @since 2025/08/01 10:30
 */
@Service
public class PosOrderServiceImpl extends ServiceImpl<PosOrderMapper, PosOrder> implements PosOrderService {

    @Resource
    private PosOrderMapper posOrderMapper;

    @Resource
    private PosOrderItemMapper posOrderItemMapper;

    // 订单状态常量
    private static final String ORDER_STATUS_PENDING = "PENDING";
    private static final String ORDER_STATUS_PAID = "PAID";
    private static final String ORDER_STATUS_CANCELLED = "CANCELLED";
    private static final String ORDER_STATUS_REFUNDED = "REFUNDED";

    // 支付状态常量
    private static final String PAYMENT_STATUS_UNPAID = "UNPAID";
    private static final String PAYMENT_STATUS_PAID = "PAID";
    private static final String PAYMENT_STATUS_PARTIAL = "PARTIAL";
    private static final String PAYMENT_STATUS_REFUNDED = "REFUNDED";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrder(PosOrder posOrder, List<PosOrderItem> orderItems) {
        // 参数校验
        if (ObjectUtil.isNull(posOrder)) {
            throw new ErpException(ErpPosExceptionEnum.CART_IS_EMPTY);
        }
        if (ObjectUtil.isEmpty(orderItems)) {
            throw new ErpException(ErpPosExceptionEnum.CART_IS_EMPTY);
        }

        // 生成订单号
        if (StrUtil.isBlank(posOrder.getOrderNo())) {
            posOrder.setOrderNo(generateOrderNo());
        }

        // 设置默认状态
        if (StrUtil.isBlank(posOrder.getOrderStatus())) {
            posOrder.setOrderStatus(ORDER_STATUS_PENDING);
        }
        if (StrUtil.isBlank(posOrder.getPaymentStatus())) {
            posOrder.setPaymentStatus(PAYMENT_STATUS_UNPAID);
        }

        // 计算订单总金额
        BigDecimal totalAmount = calculateTotalAmount(orderItems);
        posOrder.setTotalAmount(totalAmount);

        // 计算实付金额（总金额 - 折扣金额）
        BigDecimal discountAmount = ObjectUtil.defaultIfNull(posOrder.getDiscountAmount(), BigDecimal.ZERO);
        posOrder.setFinalAmount(totalAmount.subtract(discountAmount));

        // 保存订单
        this.save(posOrder);

        // 保存订单项
        for (PosOrderItem orderItem : orderItems) {
            orderItem.setOrderId(posOrder.getOrderId());
            posOrderItemMapper.insert(orderItem);
        }

        return posOrder.getOrderId();
    }

    @Override
    public PosOrder getOrderById(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return null;
        }
        return this.getById(orderId);
    }

    @Override
    public PosOrder getOrderByNo(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }
        LambdaQueryWrapper<PosOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosOrder::getOrderNo, orderNo);
        return this.getOne(wrapper);
    }

    @Override
    public List<PosOrderItem> getOrderItems(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return null;
        }
        LambdaQueryWrapper<PosOrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosOrderItem::getOrderId, orderId);
        return posOrderItemMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(Long orderId, String orderStatus) {
        if (ObjectUtil.isNull(orderId) || StrUtil.isBlank(orderStatus)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_STATUS_ERROR);
        }

        LambdaUpdateWrapper<PosOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosOrder::getOrderId, orderId)
               .set(PosOrder::getOrderStatus, orderStatus);
        this.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePaymentStatus(Long orderId, String paymentStatus, String paymentMethod) {
        if (ObjectUtil.isNull(orderId) || StrUtil.isBlank(paymentStatus)) {
            throw new ErpException(ErpPosExceptionEnum.PAYMENT_FAILED);
        }

        LambdaUpdateWrapper<PosOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosOrder::getOrderId, orderId)
               .set(PosOrder::getPaymentStatus, paymentStatus);
        
        if (StrUtil.isNotBlank(paymentMethod)) {
            wrapper.set(PosOrder::getPaymentMethod, paymentMethod);
        }

        // 如果支付成功，同时更新订单状态为已支付
        if (PAYMENT_STATUS_PAID.equals(paymentStatus)) {
            wrapper.set(PosOrder::getOrderStatus, ORDER_STATUS_PAID);
        }

        this.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long orderId, String reason) {
        if (ObjectUtil.isNull(orderId)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_NOT_EXIST);
        }

        // 校验订单是否可以取消
        if (!validateCanCancel(orderId)) {
            throw new ErpException(ErpPosExceptionEnum.ORDER_STATUS_ERROR);
        }

        LambdaUpdateWrapper<PosOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosOrder::getOrderId, orderId)
               .set(PosOrder::getOrderStatus, ORDER_STATUS_CANCELLED)
               .set(PosOrder::getPaymentStatus, PAYMENT_STATUS_UNPAID);
        
        if (StrUtil.isNotBlank(reason)) {
            wrapper.set(PosOrder::getRemark, reason);
        }

        this.update(wrapper);
    }

    @Override
    public PageResult<PosOrder> findOrderPage(Integer pageNo, Integer pageSize, 
                                              String orderStatus, String paymentStatus, Long cashierId) {
        LambdaQueryWrapper<PosOrder> wrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(orderStatus)) {
            wrapper.eq(PosOrder::getOrderStatus, orderStatus);
        }
        if (StrUtil.isNotBlank(paymentStatus)) {
            wrapper.eq(PosOrder::getPaymentStatus, paymentStatus);
        }
        if (ObjectUtil.isNotNull(cashierId)) {
            wrapper.eq(PosOrder::getCashierId, cashierId);
        }
        
        wrapper.orderByDesc(PosOrder::getCreateTime);

        Page<PosOrder> page = new Page<>(pageNo, pageSize);
        Page<PosOrder> resultPage = this.page(page, wrapper);
        
        return PageResultFactory.createPageResult(resultPage);
    }

    @Override
    public List<PosOrder> getTodayOrdersByCashier(Long cashierId) {
        if (ObjectUtil.isNull(cashierId)) {
            return null;
        }

        LambdaQueryWrapper<PosOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PosOrder::getCashierId, cashierId)
               .ge(PosOrder::getCreateTime, DateUtil.beginOfDay(DateUtil.date()))
               .le(PosOrder::getCreateTime, DateUtil.endOfDay(DateUtil.date()))
               .orderByDesc(PosOrder::getCreateTime);

        return this.list(wrapper);
    }

    @Override
    public String generateOrderNo() {
        // 生成格式：POS + yyyyMMddHHmmss + 3位随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 900) + 100; // 100-999的随机数
        return "POS" + timestamp + random;
    }

    @Override
    public boolean validateCanCancel(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return false;
        }

        PosOrder order = this.getById(orderId);
        if (ObjectUtil.isNull(order)) {
            return false;
        }

        // 只有待支付状态的订单可以取消
        return ORDER_STATUS_PENDING.equals(order.getOrderStatus()) && 
               PAYMENT_STATUS_UNPAID.equals(order.getPaymentStatus());
    }

    @Override
    public boolean validateCanRefund(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return false;
        }

        PosOrder order = this.getById(orderId);
        if (ObjectUtil.isNull(order)) {
            return false;
        }

        // 只有已支付状态的订单可以退款
        return ORDER_STATUS_PAID.equals(order.getOrderStatus()) && 
               PAYMENT_STATUS_PAID.equals(order.getPaymentStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrderItem(Long orderId, PosOrderItem orderItem) {
        if (ObjectUtil.isNull(orderId) || ObjectUtil.isNull(orderItem)) {
            throw new ErpException(ErpPosExceptionEnum.CART_IS_EMPTY);
        }

        orderItem.setOrderId(orderId);
        posOrderItemMapper.insert(orderItem);

        // 重新计算订单金额
        recalculateOrderAmount(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderItemQuantity(Long itemId, BigDecimal quantity) {
        if (ObjectUtil.isNull(itemId) || ObjectUtil.isNull(quantity)) {
            throw new ErpException(ErpPosExceptionEnum.PRODUCT_QUANTITY_INVALID);
        }

        PosOrderItem orderItem = posOrderItemMapper.selectById(itemId);
        if (ObjectUtil.isNull(orderItem)) {
            throw new ErpException(ErpPosExceptionEnum.PRODUCT_NOT_AVAILABLE);
        }

        // 更新数量和小计金额
        BigDecimal totalPrice = orderItem.getUnitPrice().multiply(quantity);
        
        LambdaUpdateWrapper<PosOrderItem> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosOrderItem::getItemId, itemId)
               .set(PosOrderItem::getQuantity, quantity)
               .set(PosOrderItem::getTotalPrice, totalPrice);
        
        posOrderItemMapper.update(null, wrapper);

        // 重新计算订单金额
        recalculateOrderAmount(orderItem.getOrderId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeOrderItem(Long itemId) {
        if (ObjectUtil.isNull(itemId)) {
            throw new ErpException(ErpPosExceptionEnum.PRODUCT_NOT_AVAILABLE);
        }

        PosOrderItem orderItem = posOrderItemMapper.selectById(itemId);
        if (ObjectUtil.isNull(orderItem)) {
            throw new ErpException(ErpPosExceptionEnum.PRODUCT_NOT_AVAILABLE);
        }

        Long orderId = orderItem.getOrderId();
        posOrderItemMapper.deleteById(itemId);

        // 重新计算订单金额
        recalculateOrderAmount(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateOrderAmount(Long orderId) {
        if (ObjectUtil.isNull(orderId)) {
            return;
        }

        // 查询订单项
        List<PosOrderItem> orderItems = getOrderItems(orderId);
        if (ObjectUtil.isEmpty(orderItems)) {
            return;
        }

        // 计算总金额
        BigDecimal totalAmount = calculateTotalAmount(orderItems);

        // 获取当前订单信息
        PosOrder order = this.getById(orderId);
        if (ObjectUtil.isNull(order)) {
            return;
        }

        // 计算实付金额
        BigDecimal discountAmount = ObjectUtil.defaultIfNull(order.getDiscountAmount(), BigDecimal.ZERO);
        BigDecimal finalAmount = totalAmount.subtract(discountAmount);

        // 更新订单金额
        LambdaUpdateWrapper<PosOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PosOrder::getOrderId, orderId)
               .set(PosOrder::getTotalAmount, totalAmount)
               .set(PosOrder::getFinalAmount, finalAmount);
        
        this.update(wrapper);
    }

    /**
     * 计算订单项总金额
     *
     * @param orderItems 订单项列表
     * @return 总金额
     */
    private BigDecimal calculateTotalAmount(List<PosOrderItem> orderItems) {
        return orderItems.stream()
                .map(PosOrderItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}