import{_ as B,r as A,s as M,X as R,a as C,f as T,w as e,d as a,g as u,b as S,t as d,a2 as h,h as Q,c as V,m as G,B as H,a3 as z,Y as K,Z as D,a0 as L,u as W,W as X,J,v as Y,y as Z,G as $,$ as tt,H as et,M as at}from"./index-18a1ea24.js";/* empty css              */import{_ as O}from"./ProductSelector-22648d1a.js";/* empty css              *//* empty css              *//* empty css              */import"./ProductApi-52d42f8e.js";const nt={name:"InventoryAdjustModal",components:{ProductSelector:O},props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(b,{emit:n}){const s=A(null),t=A(!1),_=A({}),r=M({productId:null,adjustType:"INCREASE",adjustQuantity:null,adjustReason:"STOCKTAKING",unitCost:null,remark:""}),i=M({show:!1,beforeStock:0,afterStock:0,quantityChange:0,valueChange:0}),I={productId:[{required:!0,message:"\u8BF7\u9009\u62E9\u5546\u54C1",trigger:"change"}],adjustType:[{required:!0,message:"\u8BF7\u9009\u62E9\u8C03\u6574\u7C7B\u578B",trigger:"change"}],adjustQuantity:[{required:!0,message:"\u8BF7\u8F93\u5165\u8C03\u6574\u6570\u91CF",trigger:"blur"},{type:"number",min:.001,message:"\u8C03\u6574\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0",trigger:"blur"}],adjustReason:[{required:!0,message:"\u8BF7\u9009\u62E9\u8C03\u6574\u539F\u56E0",trigger:"change"}]};R(()=>b.data,o=>{o&&o.productId&&(_.value={...o},r.productId=o.productId,f())},{immediate:!0}),R([()=>r.adjustType,()=>r.adjustQuantity,()=>r.unitCost],()=>{f()}),R(()=>b.visible,o=>{o&&(b.data.productId||m())});const m=()=>{Object.assign(r,{productId:null,adjustType:"INCREASE",adjustQuantity:null,adjustReason:"STOCKTAKING",unitCost:null,remark:""}),_.value={},i.show=!1,s.value&&s.value.clearValidate()},E=(o,l)=>{l?(_.value={...l},f()):(_.value={},i.show=!1)},f=()=>{if(!_.value.productId||!r.adjustQuantity){i.show=!1;return}const o=parseFloat(_.value.currentStock)||0,l=parseFloat(r.adjustQuantity)||0,y=parseFloat(r.unitCost)||0;let v=o,k=0;switch(r.adjustType){case"INCREASE":v=o+l,k=l;break;case"DECREASE":v=Math.max(0,o-l),k=-l;break;case"SET":v=l,k=l-o;break}i.show=!0,i.beforeStock=o,i.afterStock=v,i.quantityChange=k,i.valueChange=y>0?k*y:0};return{formRef:s,loading:t,selectedProduct:_,adjustForm:r,adjustPreview:i,rules:I,onProductChange:E,getMinQuantity:()=>(r.adjustType==="DECREASE",.001),getPrecision:()=>_.value.pricingType==="WEIGHT"?3:0,getStep:()=>_.value.pricingType==="WEIGHT"?.001:1,getQuantityPlaceholder:()=>{switch(r.adjustType){case"INCREASE":return"\u8BF7\u8F93\u5165\u589E\u52A0\u6570\u91CF";case"DECREASE":return"\u8BF7\u8F93\u5165\u51CF\u5C11\u6570\u91CF";case"SET":return"\u8BF7\u8F93\u5165\u8BBE\u7F6E\u6570\u91CF";default:return"\u8BF7\u8F93\u5165\u6570\u91CF"}},getStockUnit:(o,l)=>{switch(o){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return l||"\u4E2A"}},formatStock:(o,l)=>{if(!o)return"0";const y=l==="WEIGHT"?3:0;return parseFloat(o).toFixed(y)},formatAmount:o=>o?parseFloat(o).toFixed(2):"0.00",getStockClass:(o,l)=>{const y=parseFloat(o)||0,v=parseFloat(l)||0;return y<=0?"stock-danger":y<=v?"stock-warning":"stock-normal"},formatQuantityChange:o=>{const l=parseFloat(o)||0;return l>=0?"+".concat(l):"".concat(l)},getQuantityChangeClass:o=>(parseFloat(o)||0)>=0?"quantity-increase":"quantity-decrease",formatValueChange:o=>{const l=parseFloat(o)||0;return l>=0?"+\xA5".concat(l.toFixed(2)):"-\xA5".concat(Math.abs(l).toFixed(2))},getValueChangeClass:o=>(parseFloat(o)||0)>=0?"value-increase":"value-decrease",handleCancel:()=>{n("update:visible",!1)},handleSubmit:()=>{s.value.validate().then(async()=>{t.value=!0;try{const o={productId:r.productId,adjustType:r.adjustType,adjustQuantity:r.adjustQuantity,adjustReason:r.adjustReason,unitCost:r.unitCost,remark:r.remark};G.success("\u5E93\u5B58\u8C03\u6574\u6210\u529F"),n("ok")}catch(o){G.error("\u5E93\u5B58\u8C03\u6574\u5931\u8D25\uFF1A"+(o.message||"\u672A\u77E5\u9519\u8BEF"))}finally{t.value=!1}})}}}},ot={class:"inventory-adjust-content"},lt={class:"product-name"},rt={key:1};function st(b,n,s,t,_,r){const i=H,I=z,m=K,E=D,f=L,N=O,p=W,g=X,P=J,j=Y,F=Z,w=$,x=tt,q=et,U=at;return C(),T(U,{visible:s.visible,title:"\u5E93\u5B58\u8C03\u6574",width:800,maskClosable:!1,onCancel:t.handleCancel},{footer:e(()=>[a(i,{onClick:t.handleCancel},{default:e(()=>n[6]||(n[6]=[u("\u53D6\u6D88")])),_:1,__:[6]},8,["onClick"]),a(i,{type:"primary",loading:t.loading,onClick:t.handleSubmit},{default:e(()=>n[7]||(n[7]=[u(" \u786E\u8BA4\u8C03\u6574 ")])),_:1,__:[7]},8,["loading","onClick"])]),default:e(()=>[S("div",ot,[a(I,{message:"\u5E93\u5B58\u8C03\u6574\u63D0\u9192",description:"\u5E93\u5B58\u8C03\u6574\u5C06\u76F4\u63A5\u5F71\u54CD\u5546\u54C1\u5E93\u5B58\u6570\u91CF\u548C\u4EF7\u503C\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C\u3002\u8C03\u6574\u540E\u7684\u5E93\u5B58\u53D8\u5316\u5C06\u8BB0\u5F55\u5728\u5E93\u5B58\u5386\u53F2\u4E2D\u3002",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}}),s.data.productId?(C(),T(f,{key:0,title:"\u5546\u54C1\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:e(()=>[a(E,{column:2,bordered:"",size:"small"},{default:e(()=>[a(m,{label:"\u5546\u54C1\u540D\u79F0"},{default:e(()=>[S("span",lt,d(s.data.productName),1)]),_:1}),a(m,{label:"\u5546\u54C1\u7F16\u7801"},{default:e(()=>[u(d(s.data.productCode),1)]),_:1}),a(m,{label:"\u5F53\u524D\u5E93\u5B58"},{default:e(()=>[S("span",{class:h(t.getStockClass(s.data.currentStock,s.data.minStock))},d(t.formatStock(s.data.currentStock,s.data.pricingType))+" "+d(t.getStockUnit(s.data.pricingType,s.data.unit)),3)]),_:1}),a(m,{label:"\u5E93\u5B58\u4EF7\u503C"},{default:e(()=>[u(" \xA5"+d(t.formatAmount(s.data.totalValue)),1)]),_:1})]),_:1})]),_:1})):Q("",!0),a(f,{title:"\u8C03\u6574\u4FE1\u606F",size:"small"},{default:e(()=>[a(q,{ref:"formRef",model:t.adjustForm,rules:t.rules,layout:"vertical"},{default:e(()=>[s.data.productId?Q("",!0):(C(),T(p,{key:0,label:"\u9009\u62E9\u5546\u54C1",name:"productId",required:""},{default:e(()=>[a(N,{value:t.adjustForm.productId,"onUpdate:value":n[0]||(n[0]=c=>t.adjustForm.productId=c),filter:{businessModeList:["PURCHASE_SALE","CONSIGNMENT"]},onChange:t.onProductChange},null,8,["value","onChange"])]),_:1})),a(w,{gutter:16},{default:e(()=>[a(j,{span:12},{default:e(()=>[a(p,{label:"\u8C03\u6574\u7C7B\u578B",name:"adjustType",required:""},{default:e(()=>[a(P,{value:t.adjustForm.adjustType,"onUpdate:value":n[1]||(n[1]=c=>t.adjustForm.adjustType=c),placeholder:"\u8BF7\u9009\u62E9\u8C03\u6574\u7C7B\u578B"},{default:e(()=>[a(g,{value:"INCREASE"},{default:e(()=>n[8]||(n[8]=[u("\u589E\u52A0\u5E93\u5B58")])),_:1,__:[8]}),a(g,{value:"DECREASE"},{default:e(()=>n[9]||(n[9]=[u("\u51CF\u5C11\u5E93\u5B58")])),_:1,__:[9]}),a(g,{value:"SET"},{default:e(()=>n[10]||(n[10]=[u("\u8BBE\u7F6E\u5E93\u5B58")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1})]),_:1}),a(j,{span:12},{default:e(()=>[a(p,{label:"\u8C03\u6574\u6570\u91CF",name:"adjustQuantity",required:""},{default:e(()=>[a(F,{value:t.adjustForm.adjustQuantity,"onUpdate:value":n[2]||(n[2]=c=>t.adjustForm.adjustQuantity=c),min:t.getMinQuantity(),precision:t.getPrecision(),step:t.getStep(),style:{width:"100%"},placeholder:t.getQuantityPlaceholder()},{addonAfter:e(()=>[u(d(t.getStockUnit(t.selectedProduct.pricingType,t.selectedProduct.unit)),1)]),_:1},8,["value","min","precision","step","placeholder"])]),_:1})]),_:1})]),_:1}),a(w,{gutter:16},{default:e(()=>[a(j,{span:12},{default:e(()=>[a(p,{label:"\u8C03\u6574\u539F\u56E0",name:"adjustReason",required:""},{default:e(()=>[a(P,{value:t.adjustForm.adjustReason,"onUpdate:value":n[3]||(n[3]=c=>t.adjustForm.adjustReason=c),placeholder:"\u8BF7\u9009\u62E9\u8C03\u6574\u539F\u56E0"},{default:e(()=>[a(g,{value:"STOCKTAKING"},{default:e(()=>n[11]||(n[11]=[u("\u76D8\u70B9\u8C03\u6574")])),_:1,__:[11]}),a(g,{value:"DAMAGE"},{default:e(()=>n[12]||(n[12]=[u("\u5546\u54C1\u635F\u574F")])),_:1,__:[12]}),a(g,{value:"LOSS"},{default:e(()=>n[13]||(n[13]=[u("\u5546\u54C1\u4E22\u5931")])),_:1,__:[13]}),a(g,{value:"EXPIRE"},{default:e(()=>n[14]||(n[14]=[u("\u5546\u54C1\u8FC7\u671F")])),_:1,__:[14]}),a(g,{value:"RETURN"},{default:e(()=>n[15]||(n[15]=[u("\u9000\u8D27\u5165\u5E93")])),_:1,__:[15]}),a(g,{value:"OTHER"},{default:e(()=>n[16]||(n[16]=[u("\u5176\u4ED6\u539F\u56E0")])),_:1,__:[16]})]),_:1},8,["value"])]),_:1})]),_:1}),a(j,{span:12},{default:e(()=>[a(p,{label:"\u5355\u4F4D\u6210\u672C",name:"unitCost"},{default:e(()=>[a(F,{value:t.adjustForm.unitCost,"onUpdate:value":n[4]||(n[4]=c=>t.adjustForm.unitCost=c),min:0,precision:2,step:.01,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u5355\u4F4D\u6210\u672C\uFF08\u53EF\u9009\uFF09"},{addonBefore:e(()=>n[17]||(n[17]=[u("\xA5")])),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),a(p,{label:"\u8C03\u6574\u5907\u6CE8",name:"remark"},{default:e(()=>[a(x,{value:t.adjustForm.remark,"onUpdate:value":n[5]||(n[5]=c=>t.adjustForm.remark=c),placeholder:"\u8BF7\u8F93\u5165\u8C03\u6574\u5907\u6CE8",rows:3,maxlength:200,showCount:""},null,8,["value"])]),_:1})]),_:1},8,["model","rules"]),t.adjustPreview.show?(C(),T(f,{key:0,title:"\u8C03\u6574\u9884\u89C8",size:"small",style:{"margin-top":"16px",background:"#fafafa"}},{default:e(()=>[a(E,{column:2,size:"small"},{default:e(()=>[a(m,{label:"\u8C03\u6574\u524D\u5E93\u5B58"},{default:e(()=>[u(d(t.formatStock(t.adjustPreview.beforeStock,t.selectedProduct.pricingType))+" "+d(t.getStockUnit(t.selectedProduct.pricingType,t.selectedProduct.unit)),1)]),_:1}),a(m,{label:"\u8C03\u6574\u540E\u5E93\u5B58"},{default:e(()=>[S("span",{class:h(t.getStockClass(t.adjustPreview.afterStock,t.selectedProduct.minStock))},d(t.formatStock(t.adjustPreview.afterStock,t.selectedProduct.pricingType))+" "+d(t.getStockUnit(t.selectedProduct.pricingType,t.selectedProduct.unit)),3)]),_:1}),a(m,{label:"\u6570\u91CF\u53D8\u5316"},{default:e(()=>[S("span",{class:h(t.getQuantityChangeClass(t.adjustPreview.quantityChange))},d(t.formatQuantityChange(t.adjustPreview.quantityChange))+" "+d(t.getStockUnit(t.selectedProduct.pricingType,t.selectedProduct.unit)),3)]),_:1}),a(m,{label:"\u4EF7\u503C\u53D8\u5316"},{default:e(()=>[t.adjustPreview.valueChange?(C(),V("span",{key:0,class:h(t.getValueChangeClass(t.adjustPreview.valueChange))},d(t.formatValueChange(t.adjustPreview.valueChange)),3)):(C(),V("span",rt,"-"))]),_:1})]),_:1})]),_:1})):Q("",!0)]),_:1})])]),_:1},8,["visible","onCancel"])}const vt=B(nt,[["render",st],["__scopeId","data-v-5ee8647b"]]);export{vt as default};
