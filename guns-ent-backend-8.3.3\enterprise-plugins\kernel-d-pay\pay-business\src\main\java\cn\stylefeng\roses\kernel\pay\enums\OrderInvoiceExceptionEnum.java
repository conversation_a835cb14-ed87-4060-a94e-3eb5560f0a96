package cn.stylefeng.roses.kernel.pay.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 订单开票记录异常相关枚举
 *
 * <AUTHOR>
 * @since 2024/06/21 16:49
 */
@Getter
public enum OrderInvoiceExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    ORDER_INVOICE_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "查询结果不存在"),

    /**
     * 某些发票已经申请过发票，无法再次申请
     */
    ALREADY_APPLY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "某些发票已经申请过发票，无法再次申请"),

    /**
     * 查询不到用户的指定订单
     */
    CANT_FIND_ORDER(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10003", "查询不到用户的指定订单");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    OrderInvoiceExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
