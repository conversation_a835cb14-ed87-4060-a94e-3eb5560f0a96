System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./PurchaseApi-legacy-77810512.js"],(function(t,e){"use strict";var a,l,o,n,i,r,d,c,u,s,f,m,p,y,g,h,v,b,k,_,C,x,w,R,P,j;return{setters:[t=>{a=t._,l=t.r,o=t.s,n=t.L,i=t.a,r=t.f,d=t.w,c=t.d,u=t.g,s=t.b,f=t.t,m=t.c,p=t.h,y=t.F,g=t.m,h=t.B,v=t.a3,b=t.Y,k=t.Z,_=t.a0,C=t.i,x=t.$,w=t.u,R=t.H,P=t.M},null,null,null,null,null,t=>{j=t.P}],execute:function(){var e=document.createElement("style");e.textContent=".confirm-inbound-content[data-v-2f82949e]{max-height:70vh;overflow-y:auto}.order-no[data-v-2f82949e]{font-weight:500;color:#1890ff}.total-amount[data-v-2f82949e]{font-weight:500;color:#f5222d;font-size:16px}.product-info[data-v-2f82949e]{text-align:left}.product-name[data-v-2f82949e]{font-weight:500;color:#262626;margin-bottom:4px}.product-code[data-v-2f82949e]{font-size:12px;color:#8c8c8c}.total-price[data-v-2f82949e]{font-weight:500;color:#1890ff}.ant-alert[data-v-2f82949e],.ant-card[data-v-2f82949e]{border-radius:6px}.ant-descriptions-item-label[data-v-2f82949e]{font-weight:500}\n",document.head.appendChild(e);const L={name:"ConfirmInboundModal",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(t,{emit:e}){const a=l(null),i=l(!1),r=o({confirmRemark:""}),d=n((()=>t.data.detailList?t.data.detailList.length:0)),c=n((()=>t.data.detailList&&0!==t.data.detailList.length?t.data.detailList.reduce(((t,e)=>t+(parseFloat(e.quantity)||0)),0):0));return{formRef:a,loading:i,confirmData:r,rules:{},columns:[{title:"商品信息",key:"productInfo",width:200},{title:"数量",key:"quantity",width:100,align:"center"},{title:"单价",key:"unitPrice",width:100,align:"right"},{title:"总价",key:"totalPrice",width:120,align:"right"}],productCount:d,totalQuantity:c,getQuantityUnit:t=>{switch(t.pricingType){case"WEIGHT":return"kg";case"PIECE":return"件";default:return t.unit||"个"}},formatAmount:t=>t?parseFloat(t).toFixed(2):"0.00",handleCancel:()=>{r.confirmRemark="",e("update:visible",!1)},handleConfirm:()=>{t.data.id?(i.value=!0,j.confirm({id:t.data.id,confirmRemark:r.confirmRemark}).then((()=>{g.success("确认成功"),r.confirmRemark="",e("ok")})).catch((t=>{g.error("确认失败："+(t.message||"未知错误"))})).finally((()=>{i.value=!1}))):g.error("入库单信息不完整")}}}},z={class:"confirm-inbound-content"},A={class:"order-no"},I={class:"total-amount"},D={key:0,class:"product-info"},q={class:"product-name"},E={class:"product-code"},F={key:3,class:"total-price"};t("default",a(L,[["render",function(t,e,a,l,o,n){const g=h,j=v,L=b,Q=k,M=_,N=C,U=x,B=w,H=R,T=P;return i(),r(T,{visible:a.visible,title:"确认入库单",width:800,maskClosable:!1,onCancel:l.handleCancel},{footer:d((()=>[c(g,{onClick:l.handleCancel},{default:d((()=>e[1]||(e[1]=[u("取消")]))),_:1,__:[1]},8,["onClick"]),c(g,{type:"primary",loading:l.loading,onClick:l.handleConfirm},{default:d((()=>e[2]||(e[2]=[u(" 确认入库单 ")]))),_:1,__:[2]},8,["loading","onClick"])])),default:d((()=>[s("div",z,[c(j,{message:"确认提醒",description:"确认后入库单将不能再修改，请仔细核对入库单信息。",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}}),c(M,{title:"入库单信息",size:"small",style:{"margin-bottom":"16px"}},{default:d((()=>[c(Q,{column:2,bordered:"",size:"small"},{default:d((()=>[c(L,{label:"入库单号"},{default:d((()=>[s("span",A,f(a.data.orderNo),1)])),_:1}),c(L,{label:"供应商"},{default:d((()=>[u(f(a.data.supplierName),1)])),_:1}),c(L,{label:"订单日期"},{default:d((()=>[u(f(a.data.orderDate),1)])),_:1}),c(L,{label:"总金额"},{default:d((()=>[s("span",I,"¥"+f(l.formatAmount(a.data.totalAmount)),1)])),_:1}),c(L,{label:"商品种类"},{default:d((()=>[u(f(l.productCount)+" 种 ",1)])),_:1}),c(L,{label:"总数量"},{default:d((()=>[u(f(l.totalQuantity)+" 件 ",1)])),_:1})])),_:1})])),_:1}),c(M,{title:"商品明细",size:"small",style:{"margin-bottom":"16px"}},{default:d((()=>[c(N,{columns:l.columns,"data-source":a.data.detailList||[],pagination:!1,size:"small",bordered:"",scroll:{y:300}},{bodyCell:d((({column:t,record:e})=>["productInfo"===t.key?(i(),m("div",D,[s("div",q,f(e.productName),1),s("div",E,f(e.productCode),1)])):p("",!0),"quantity"===t.key?(i(),m(y,{key:1},[u(f(e.quantity)+" "+f(l.getQuantityUnit(e)),1)],64)):p("",!0),"unitPrice"===t.key?(i(),m(y,{key:2},[u(" ¥"+f(l.formatAmount(e.unitPrice)),1)],64)):p("",!0),"totalPrice"===t.key?(i(),m("span",F,"¥"+f(l.formatAmount(e.totalPrice)),1)):p("",!0)])),_:1},8,["columns","data-source"])])),_:1}),c(M,{title:"确认信息",size:"small"},{default:d((()=>[c(H,{ref:"formRef",model:l.confirmData,rules:l.rules,layout:"vertical"},{default:d((()=>[c(B,{label:"确认备注",name:"confirmRemark"},{default:d((()=>[c(U,{value:l.confirmData.confirmRemark,"onUpdate:value":e[0]||(e[0]=t=>l.confirmData.confirmRemark=t),placeholder:"请输入确认备注（可选）",rows:3,maxlength:200,showCount:""},null,8,["value"])])),_:1})])),_:1},8,["model","rules"])])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-2f82949e"]]))}}}));
