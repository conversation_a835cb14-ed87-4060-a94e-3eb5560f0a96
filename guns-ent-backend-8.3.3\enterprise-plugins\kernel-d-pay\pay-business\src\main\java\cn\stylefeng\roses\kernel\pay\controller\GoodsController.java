package cn.stylefeng.roses.kernel.pay.controller;

import cn.stylefeng.roses.kernel.pay.api.entity.Goods;
import cn.stylefeng.roses.kernel.pay.pojo.request.GoodsRequest;
import cn.stylefeng.roses.kernel.pay.service.GoodsService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品信息控制器
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@RestController
@ApiResource(name = "商品信息")
public class GoodsController {

    @Resource
    private GoodsService goodsService;

    /**
     * 查看商品信息详情
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    @GetResource(name = "查看商品信息详情", path = "/goods/detail")
    public ResponseData<Goods> detail(@Validated(GoodsRequest.detail.class) GoodsRequest goodsRequest) {
        return new SuccessResponseData<>(goodsService.detail(goodsRequest));
    }

    /**
     * 获取商品信息列表
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    @GetResource(name = "获取商品信息列表", path = "/goods/list")
    public ResponseData<List<Goods>> list(GoodsRequest goodsRequest) {
        return new SuccessResponseData<>(goodsService.findList(goodsRequest));
    }

}
