System.register(["./index-legacy-ee1db0c7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./regionApi-legacy-73888494.js"],(function(e,l){"use strict";var t,a,o,r,i,d,n,s,u,f,_,c,g,m,b;return{setters:[e=>{t=e._,a=e.s,o=e.X,r=e.a,i=e.f,d=e.w,n=e.d,s=e.g,u=e.t,f=e.m,_=e.Y,c=e.U,g=e.Z,m=e.M},null,null,e=>{b=e.R}],execute:function(){const l={name:"RegionDetailForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{emit:l}){const t=a({}),r=async e=>{try{const l=await b.detail({regionId:e.regionId});Object.assign(t,l)}catch(l){console.error("获取区域详情失败:",l),f.error("获取详情失败")}};return o((()=>e.data),(e=>{e&&Object.keys(e).length>0&&(e.regionId?r(e):Object.assign(t,e))}),{immediate:!0}),o((()=>e.visible),(l=>{l&&e.data&&e.data.regionId&&r(e.data)})),{form:t,updateVisible:e=>{l("update:visible",e),e||Object.keys(t).forEach((e=>{delete t[e]}))},detail:r}}};e("default",t(l,[["render",function(e,l,t,a,o,f){const b=_,y=c,v=g,p=m;return r(),i(p,{title:"区域详情",width:800,visible:t.visible,footer:null,"onUpdate:visible":a.updateVisible},{default:d((()=>[n(v,{column:2,bordered:""},{default:d((()=>[n(b,{label:"区域编码"},{default:d((()=>[s(u(a.form.regionCode||"无"),1)])),_:1}),n(b,{label:"区域名称"},{default:d((()=>[s(u(a.form.regionName||"无"),1)])),_:1}),n(b,{label:"区域层级"},{default:d((()=>[1===a.form.regionLevel?(r(),i(y,{key:0,color:"red"},{default:d((()=>l[0]||(l[0]=[s("国家")]))),_:1,__:[0]})):2===a.form.regionLevel?(r(),i(y,{key:1,color:"orange"},{default:d((()=>l[1]||(l[1]=[s("省")]))),_:1,__:[1]})):3===a.form.regionLevel?(r(),i(y,{key:2,color:"yellow"},{default:d((()=>l[2]||(l[2]=[s("市")]))),_:1,__:[2]})):4===a.form.regionLevel?(r(),i(y,{key:3,color:"green"},{default:d((()=>l[3]||(l[3]=[s("区县")]))),_:1,__:[3]})):5===a.form.regionLevel?(r(),i(y,{key:4,color:"blue"},{default:d((()=>l[4]||(l[4]=[s("商圈")]))),_:1,__:[4]})):(r(),i(y,{key:5,color:"default"},{default:d((()=>l[5]||(l[5]=[s("未知")]))),_:1,__:[5]}))])),_:1}),n(b,{label:"父级区域"},{default:d((()=>[s(u(a.form.parentRegionName||"无"),1)])),_:1}),n(b,{label:"排序号"},{default:d((()=>[s(u(a.form.sortOrder||0),1)])),_:1}),n(b,{label:"状态"},{default:d((()=>["Y"===a.form.status?(r(),i(y,{key:0,color:"green"},{default:d((()=>l[6]||(l[6]=[s("启用")]))),_:1,__:[6]})):"N"===a.form.status?(r(),i(y,{key:1,color:"red"},{default:d((()=>l[7]||(l[7]=[s("停用")]))),_:1,__:[7]})):(r(),i(y,{key:2,color:"default"},{default:d((()=>[s(u(a.form.status||"未知"),1)])),_:1}))])),_:1}),n(b,{label:"备注",span:2},{default:d((()=>[s(u(a.form.remark||"无"),1)])),_:1})])),_:1})])),_:1},8,["visible","onUpdate:visible"])}]]))}}}));
