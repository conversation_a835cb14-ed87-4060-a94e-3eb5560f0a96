package cn.stylefeng.roses.ent.mobile.manage.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * 移动端相关业务异常
 *
 * <AUTHOR>
 * @since 2024/3/24 23:43
 */
public class MobileException extends ServiceException {

    public MobileException(AbstractExceptionEnum exception, Object... params) {
        super("kernel-s-mobile", exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public MobileException(AbstractExceptionEnum exception) {
        super("kernel-s-mobile", exception);
    }

}
