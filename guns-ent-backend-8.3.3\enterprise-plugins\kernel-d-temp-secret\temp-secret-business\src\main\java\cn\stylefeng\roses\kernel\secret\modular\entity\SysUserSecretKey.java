package cn.stylefeng.roses.kernel.secret.modular.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户临时秘钥实例类
 *
 * <AUTHOR>
 * @date 2022/03/22 11:33
 */
@TableName("sys_user_secret_key")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserSecretKey extends BaseEntity {

    /**
     * 秘钥id
     */
    @TableId(value = "user_secret_key_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("秘钥id")
    private Long userSecretKeyId;

    /**
     * 秘钥名称
     */
    @TableField("secret_key_name")
    @ChineseDescription("秘钥名称")
    private String secretKeyName;

    /**
     * 所属用户id
     */
    @TableField("user_id")
    @ChineseDescription("所属用户id")
    private Long userId;

    /**
     * 秘钥值，密码加密后的MD5值
     */
    @TableField("secret_key")
    @ChineseDescription("秘钥值，密码加密后的MD5值")
    private String secretKey;

    /**
     * 秘钥值的MD5加密盐
     */
    @TableField("secret_key_salt")
    @ChineseDescription("秘钥值的MD5加密盐")
    private String secretKeySalt;

    /**
     * 秘钥过期时间
     */
    @TableField("secret_expiration_time")
    @ChineseDescription("秘钥过期时间")
    private Date secretExpirationTime;

    /**
     * 秘钥是否使用一次后删除：Y-是，N-否
     */
    @TableField("secret_once_flag")
    @ChineseDescription("秘钥是否使用一次后删除：Y-是，N-否")
    private String secretOnceFlag;

}
