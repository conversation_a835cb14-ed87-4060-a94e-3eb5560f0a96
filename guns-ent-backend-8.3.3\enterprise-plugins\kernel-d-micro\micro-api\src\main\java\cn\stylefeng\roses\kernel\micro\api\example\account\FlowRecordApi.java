package cn.stylefeng.roses.kernel.micro.api.example.account;

import cn.stylefeng.roses.kernel.micro.api.example.account.model.FlowRecord;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 流水记录的接口
 *
 * <AUTHOR>
 * @date 2021/5/18 13:58
 */
@RequestMapping("/api/flowRecord")
public interface FlowRecordApi {

    /**
     * 根据订单id查询流水记录
     */
    @RequestMapping("/findOrderFlowRecord")
    FlowRecord findOrderFlowRecord(@RequestParam("orderId") Long orderId);

}
