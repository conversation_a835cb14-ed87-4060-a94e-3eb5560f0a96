# API请求的前缀，走这个前缀的会被转发到后台，打包到tomcat这里要改为/
VITE_API_PREFIX=/api

# 后台接口的地址，这个地址只有在开发环境用，上线后用nginx转发
VITE_API_URL=https://vue3.javaguns.com/api

# sso单点服务器的地址，上线后需要改为线上的单点服务地址
VITE_APP_SSO_HOST=http://localhost:8888

# sso单点客户端id，上线后需要改为线上的客户端id
VITE_APP_SSO_ID=1187303628294164583

# oauth2服务端地址，用在点击首页的第三方登录logo
VITE_APP_BACKEND_HOST=http://www.javaguns.com/api

# 富文本图片预览前缀路径
VITE_APP_TINYMCE_EDITOR_IMG_PREFIX_URL=''
