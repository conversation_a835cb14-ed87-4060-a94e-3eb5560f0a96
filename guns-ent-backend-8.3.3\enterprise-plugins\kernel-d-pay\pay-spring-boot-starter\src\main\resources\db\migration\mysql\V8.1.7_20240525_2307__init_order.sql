CREATE TABLE `shop_goods`  (
  `goods_id` bigint NOT NULL COMMENT '商品id',
  `goods_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `goods_desc` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品简介',
  `goods_pic` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品封面图片，url全路径',
  `detail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情介绍的url，url全路径',
  `price` decimal(10, 2) NOT NULL COMMENT '商品标价',
  `original_price` decimal(10, 2) NOT NULL COMMENT '商品原价',
  `expiry_month` int NOT NULL DEFAULT 0 COMMENT '商品有效月数，-1为永久',
  `goods_status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-发布，2-下架',
  `fld_sort` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '排序号',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否删除：Y-删除，N-未删除',
  PRIMARY KEY (`goods_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品信息' ROW_FORMAT = Dynamic;

CREATE TABLE `shop_order` (
  `order_id` bigint NOT NULL COMMENT '订单id',
  `order_number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号，时间戳加6位随机字符串',
  `user_id` bigint NOT NULL COMMENT '订单所属用户id',
  `goods_id` bigint DEFAULT NULL COMMENT '商品id',
  `goods_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
  `business_params` json DEFAULT NULL COMMENT '业务的参数',
  `business_process_class` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务处理的类，用在成功支付后回调',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '商品原价',
  `pay_price` decimal(10,2) DEFAULT NULL COMMENT '实付金额',
  `state` int NOT NULL DEFAULT '0' COMMENT '状态，1待支付、2已完成、3已取消、4已退款',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'YunGouOS系统单号',
  `pay_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付单号',
  `pay_channel` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付渠道：alipay，wxpay',
  `sign` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付时的签名',
  `open_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户openId',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单';

CREATE TABLE `shop_user_expiry`  (
  `expiry_id` bigint NOT NULL COMMENT '主键id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `goods_id` bigint NOT NULL COMMENT '商品id',
  `expiry_date` datetime NOT NULL COMMENT '商品到期时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_user` bigint NULL DEFAULT NULL COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否删除：Y-删除，N-未删除',
  PRIMARY KEY (`expiry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户商品到期时间' ROW_FORMAT = Dynamic;