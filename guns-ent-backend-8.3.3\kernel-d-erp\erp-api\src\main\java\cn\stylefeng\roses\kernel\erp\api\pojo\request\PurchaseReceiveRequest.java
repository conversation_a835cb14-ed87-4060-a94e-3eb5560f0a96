package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 采购入库单入库请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseReceiveRequest extends BaseRequest {

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空")
    @ChineseDescription("入库单ID")
    private Long id;

    /**
     * 实际入库总金额
     */
    @ChineseDescription("实际入库总金额")
    private BigDecimal actualTotalAmount;

    /**
     * 付款方式
     */
    @NotBlank(message = "付款方式不能为空")
    @ChineseDescription("付款方式")
    private String paymentMethod;

    /**
     * 付款账户
     */
    @ChineseDescription("付款账户")
    private String paymentAccount;

    /**
     * 入库备注
     */
    @ChineseDescription("入库备注")
    private String receiveRemark;

}