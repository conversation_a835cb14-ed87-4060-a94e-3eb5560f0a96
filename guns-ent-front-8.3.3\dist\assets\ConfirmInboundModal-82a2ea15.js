import{_ as B,r as A,s as N,L as I,a as u,f as D,w as a,d as e,g as c,b as f,t as n,c as g,h as k,F as L,m as C,B as F,a3 as M,Y as z,Z as E,a0 as Q,i as V,$ as q,u as U,H as T,M as H}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{P as O}from"./PurchaseApi-38cc3d1a.js";const j={name:"ConfirmInboundModal",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(i,{emit:l}){const s=A(null),t=A(!1),_=N({confirmRemark:""}),v={},p=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:200},{title:"\u6570\u91CF",key:"quantity",width:100,align:"center"},{title:"\u5355\u4EF7",key:"unitPrice",width:100,align:"right"},{title:"\u603B\u4EF7",key:"totalPrice",width:120,align:"right"}],b=I(()=>i.data.detailList?i.data.detailList.length:0),r=I(()=>!i.data.detailList||i.data.detailList.length===0?0:i.data.detailList.reduce((o,h)=>o+(parseFloat(h.quantity)||0),0));return{formRef:s,loading:t,confirmData:_,rules:v,columns:p,productCount:b,totalQuantity:r,getQuantityUnit:o=>{switch(o.pricingType){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return o.unit||"\u4E2A"}},formatAmount:o=>o?parseFloat(o).toFixed(2):"0.00",handleCancel:()=>{_.confirmRemark="",l("update:visible",!1)},handleConfirm:()=>{if(!i.data.id){C.error("\u5165\u5E93\u5355\u4FE1\u606F\u4E0D\u5B8C\u6574");return}t.value=!0,O.confirm({id:i.data.id,confirmRemark:_.confirmRemark}).then(()=>{C.success("\u786E\u8BA4\u6210\u529F"),_.confirmRemark="",l("ok")}).catch(o=>{C.error("\u786E\u8BA4\u5931\u8D25\uFF1A"+(o.message||"\u672A\u77E5\u9519\u8BEF"))}).finally(()=>{t.value=!1})}}}},G={class:"confirm-inbound-content"},S={class:"order-no"},W={class:"total-amount"},Y={key:0,class:"product-info"},Z={class:"product-name"},J={class:"product-code"},K={key:3,class:"total-price"};function X(i,l,s,t,_,v){const p=F,b=M,r=z,w=E,y=Q,x=V,R=q,o=U,h=T,P=H;return u(),D(P,{visible:s.visible,title:"\u786E\u8BA4\u5165\u5E93\u5355",width:800,maskClosable:!1,onCancel:t.handleCancel},{footer:a(()=>[e(p,{onClick:t.handleCancel},{default:a(()=>l[1]||(l[1]=[c("\u53D6\u6D88")])),_:1,__:[1]},8,["onClick"]),e(p,{type:"primary",loading:t.loading,onClick:t.handleConfirm},{default:a(()=>l[2]||(l[2]=[c(" \u786E\u8BA4\u5165\u5E93\u5355 ")])),_:1,__:[2]},8,["loading","onClick"])]),default:a(()=>[f("div",G,[e(b,{message:"\u786E\u8BA4\u63D0\u9192",description:"\u786E\u8BA4\u540E\u5165\u5E93\u5355\u5C06\u4E0D\u80FD\u518D\u4FEE\u6539\uFF0C\u8BF7\u4ED4\u7EC6\u6838\u5BF9\u5165\u5E93\u5355\u4FE1\u606F\u3002",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}}),e(y,{title:"\u5165\u5E93\u5355\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:a(()=>[e(w,{column:2,bordered:"",size:"small"},{default:a(()=>[e(r,{label:"\u5165\u5E93\u5355\u53F7"},{default:a(()=>[f("span",S,n(s.data.orderNo),1)]),_:1}),e(r,{label:"\u4F9B\u5E94\u5546"},{default:a(()=>[c(n(s.data.supplierName),1)]),_:1}),e(r,{label:"\u8BA2\u5355\u65E5\u671F"},{default:a(()=>[c(n(s.data.orderDate),1)]),_:1}),e(r,{label:"\u603B\u91D1\u989D"},{default:a(()=>[f("span",W,"\xA5"+n(t.formatAmount(s.data.totalAmount)),1)]),_:1}),e(r,{label:"\u5546\u54C1\u79CD\u7C7B"},{default:a(()=>[c(n(t.productCount)+" \u79CD ",1)]),_:1}),e(r,{label:"\u603B\u6570\u91CF"},{default:a(()=>[c(n(t.totalQuantity)+" \u4EF6 ",1)]),_:1})]),_:1})]),_:1}),e(y,{title:"\u5546\u54C1\u660E\u7EC6",size:"small",style:{"margin-bottom":"16px"}},{default:a(()=>[e(x,{columns:t.columns,"data-source":s.data.detailList||[],pagination:!1,size:"small",bordered:"",scroll:{y:300}},{bodyCell:a(({column:d,record:m})=>[d.key==="productInfo"?(u(),g("div",Y,[f("div",Z,n(m.productName),1),f("div",J,n(m.productCode),1)])):k("",!0),d.key==="quantity"?(u(),g(L,{key:1},[c(n(m.quantity)+" "+n(t.getQuantityUnit(m)),1)],64)):k("",!0),d.key==="unitPrice"?(u(),g(L,{key:2},[c(" \xA5"+n(t.formatAmount(m.unitPrice)),1)],64)):k("",!0),d.key==="totalPrice"?(u(),g("span",K,"\xA5"+n(t.formatAmount(m.totalPrice)),1)):k("",!0)]),_:1},8,["columns","data-source"])]),_:1}),e(y,{title:"\u786E\u8BA4\u4FE1\u606F",size:"small"},{default:a(()=>[e(h,{ref:"formRef",model:t.confirmData,rules:t.rules,layout:"vertical"},{default:a(()=>[e(o,{label:"\u786E\u8BA4\u5907\u6CE8",name:"confirmRemark"},{default:a(()=>[e(R,{value:t.confirmData.confirmRemark,"onUpdate:value":l[0]||(l[0]=d=>t.confirmData.confirmRemark=d),placeholder:"\u8BF7\u8F93\u5165\u786E\u8BA4\u5907\u6CE8\uFF08\u53EF\u9009\uFF09",rows:3,maxlength:200,showCount:""},null,8,["value"])]),_:1})]),_:1},8,["model","rules"])]),_:1})])]),_:1},8,["visible","onCancel"])}const it=B(j,[["render",X],["__scopeId","data-v-2f82949e"]]);export{it as default};
