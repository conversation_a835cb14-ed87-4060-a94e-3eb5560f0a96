<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.ent.mobile.invite.mapper.SysInviteUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.ent.mobile.invite.entity.SysInviteUser">
		<id column="invite_user_id" property="inviteUserId" />
		<result column="org_id" property="orgId" />
		<result column="from_user_id" property="fromUserId" />
		<result column="real_name" property="realName" />
		<result column="phone_number" property="phoneNumber" />
		<result column="phone_validate_number" property="phoneValidateNumber" />
		<result column="apply_reason" property="applyReason" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		invite_user_id,org_id,from_user_id,real_name,phone_number,phone_validate_number,apply_reason,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteUserVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		sys_invite_user tbl
		WHERE
		<where>
        <if test="param.inviteUserId != null and param.inviteUserId != ''">
            and tbl.invite_user_id like concat('%',#{param.inviteUserId},'%')
        </if>
        <if test="param.orgId != null and param.orgId != ''">
            and tbl.org_id like concat('%',#{param.orgId},'%')
        </if>
        <if test="param.fromUserId != null and param.fromUserId != ''">
            and tbl.from_user_id like concat('%',#{param.fromUserId},'%')
        </if>
        <if test="param.realName != null and param.realName != ''">
            and tbl.real_name like concat('%',#{param.realName},'%')
        </if>
        <if test="param.phoneNumber != null and param.phoneNumber != ''">
            and tbl.phone_number like concat('%',#{param.phoneNumber},'%')
        </if>
        <if test="param.phoneValidateNumber != null and param.phoneValidateNumber != ''">
            and tbl.phone_validate_number like concat('%',#{param.phoneValidateNumber},'%')
        </if>
        <if test="param.applyReason != null and param.applyReason != ''">
            and tbl.apply_reason like concat('%',#{param.applyReason},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
