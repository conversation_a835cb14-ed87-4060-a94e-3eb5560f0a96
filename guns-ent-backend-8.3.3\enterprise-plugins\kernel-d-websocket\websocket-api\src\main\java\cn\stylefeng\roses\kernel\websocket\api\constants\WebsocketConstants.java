/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.websocket.api.constants;

/**
 * WebSocket通信业务
 *
 * <AUTHOR>
 * @date 2024-01-10 21:50:36
 */
public interface WebsocketConstants {

    /**
     * WebSocket通信模块名称
     */
    String WEBSOCKET_MODULE_NAME = "kernel-s-websocket";

    /**
     * 异常枚举的步进值
     */
    String WEBSOCKET_EXCEPTION_STEP_CODE = "99";

    /**
     * 路径标识：目前使用token来代表
     */
    String PATH_PARAM_IDENTIFIER = "identifier";

    /**
     * 路径标识参数，随机字符串
     */
    String PATH_RANDOM_STRING = "randomString";

    /**
     * 标识连接的分隔符
     */
    String IDENTIFIER_CONCAT = "@";

    /**
     * websocket管理器，在Spring容器中的名字
     */
    String WEBSOCKET_MANAGER_NAME = "webSocketManager";

    /**
     * 心跳内容，请求
     */
    String PING_CONTENT = "ping";

    /**
     * 心跳内容，响应
     */
    String PONG_CONTENT = "pong";

}
