import{_ as ae,r as u,o as te,k as R,a as i,c as _,d as o,w as n,b as t,F as U,e as F,f as V,a2 as ne,aM as le,g as v,t as B,aR as L,aS as A,h as N,m as p,M as b,E as k,v as re,I as ie,l as ce,a0 as de,a5 as ue,S as _e,B as pe,n as ge,C as ve,G as he}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import me from"./org-tree-7bb24e1b.js";import{U as z}from"./UsersApi-ec2041f8.js";import{_ as fe,E as g}from"./add-org-978f1e3a.js";/* empty css              */import"./OrgApi-021dd6dd.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              */const Ce={class:"guns-body"},be={class:"box bgColor box-shadow"},ke={class:"search"},we={class:"user-content"},ye={class:"user-list"},xe={style:{"margin-top":"10px"}},Oe={class:"box bgColor box-shadow power-data"},Se={class:"power-header"},Ue={class:"power-body"},Fe=["onClick"],Ve={class:"role-list"},Be={key:0},Le={key:1},Ae={class:"delete-role"},Ne={class:"power-add-icon"},Te={class:"box bgColor box-shadow"},$e=Object.assign({name:"StructureEmpower"},{__name:"index",setup(Ee){const h=u({pageNo:1,pageSize:40,orgIdCondition:"",searchText:""}),w=u(!1),m=u([]),r=u(""),y=u(!1),f=u([]),C=u(!1);te(()=>{x()});const D=(s,e)=>{h.value.orgIdCondition=s[0],x()},x=()=>{w.value=!0,z.findPage(h.value).then(s=>{m.value=s.rows}).finally(()=>w.value=!1)},P=s=>{z.updateStatus({userId:s.userId,statusFlag:s.statusFlag}).then(e=>{p.success(e.message)})},j=s=>{r.value!=s.userId&&(r.value=s.userId,c())},c=()=>{r.value&&(y.value=!0,g.getUserAssignList({userId:r.value}).then(s=>{f.value=s.data}).finally(()=>y.value=!1))},G=()=>{f.value.length!=0&&b.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u7981\u7528\u5168\u90E8\u673A\u6784\u5417?",icon:o(k),maskClosable:!0,onOk:async()=>{let s=await g.disableAllOrg({userId:r.value});p.success(s.message),c()}})},W=()=>{f.value.length!=0&&b.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5168\u90E8\u673A\u6784\u5417?",icon:o(k),maskClosable:!0,onOk:async()=>{let s=await g.deleteAllOrgBind({userId:r.value});p.success(s.message),c()}})},q=s=>{b.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u540C\u6B65\u5230\u5176\u4ED6\u516C\u53F8\u5417?",icon:o(k),maskClosable:!0,onOk:async()=>{let e={userId:r.value,statusFlag:s.enableThisOrg,...s},d=await g.syncOtherOrgStatusAndBusinessRole(e);p.success(d.message),c()}})},H=s=>{b.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u7528\u6237\u7ED1\u5B9A\u7684\u673A\u6784\u5417?",icon:o(k),maskClosable:!0,onOk:async()=>{let e=await g.removeUserOrgBind({userId:r.value,orgId:s.orgId});p.success(e.message),c()}})},J=s=>{g.changeStatus({userId:r.value,orgId:s.orgId,checkedFlag:s.enableThisOrg}).then(e=>{p.success(e.message),c()})},K=(s,e)=>{g.changeRoleSelect({userId:r.value,orgId:s.orgId,...e}).then(d=>{p.success(d.message),c()})},Q=()=>{C.value=!0};return(s,e)=>{const d=re,T=ie,X=ce,$=R("vxe-switch"),O=de,E=ue,I=_e,S=pe,Y=ge,Z=ve,ee=R("plus-outlined"),se=he;return i(),_("div",Ce,[o(se,{gutter:12},{default:n(()=>[o(d,{class:"power-org"},{default:n(()=>[o(me,{onTreeSelect:D,ref:"orgTreeRef",isSetWidth:!1,style:{padding:"12px"}},null,512)]),_:1}),o(d,{class:"power-user"},{default:n(()=>[t("div",be,[t("div",ke,[o(X,{value:h.value.searchText,"onUpdate:value":e[0]||(e[0]=a=>h.value.searchText=a),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22","allow-clear":"",onPressEnter:x},{prefix:n(()=>[o(T,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),t("div",we,[o(I,{tip:"Loading...",spinning:w.value,delay:100},{default:n(()=>[t("div",ye,[(i(!0),_(U,null,F(m.value,a=>(i(),V(O,{title:a.realName,class:ne([[{"user-active":r.value==a.userId}],"user-card"]),hoverable:"",key:a.userId,onClick:l=>j(a)},{extra:n(()=>[e[2]||(e[2]=t("span",null,"\u7528\u6237\u72B6\u6001:",-1)),o($,{modelValue:a.statusFlag,"onUpdate:modelValue":l=>a.statusFlag=l,"open-value":1,"close-value":2,onChange:le(l=>P(a),["stop"])},null,8,["modelValue","onUpdate:modelValue","onChange"])]),default:n(()=>[t("div",null,[e[3]||(e[3]=t("span",null,"\u5DE5\u53F7\uFF1A",-1)),v(" "+B(a.employeeNumber),1)]),t("div",xe,[e[4]||(e[4]=t("span",null,"\u7535\u8BDD\uFF1A",-1)),v(" "+B(a.phone),1)])]),_:2},1032,["title","class","onClick"]))),128)),L(o(E,{class:"empty"},null,512),[[A,m.value&&m.value.length==0]])])]),_:1},8,["spinning"])])])]),_:1}),o(d,{class:"power-content"},{default:n(()=>[o(I,{tip:"Loading...",spinning:y.value,delay:100,style:{width:"100%"}},{default:n(()=>[L(t("div",Oe,[t("div",Se,[o(Y,{style:{"margin-left":"10px"}},{default:n(()=>[o(S,{danger:"",class:"border-radius",onClick:G},{default:n(()=>e[5]||(e[5]=[v("\u5168\u90E8\u7981\u7528")])),_:1,__:[5]}),o(S,{danger:"",class:"border-radius",onClick:W},{default:n(()=>e[6]||(e[6]=[v("\u5168\u90E8\u5220\u9664")])),_:1,__:[6]})]),_:1})]),t("div",Ue,[(i(!0),_(U,null,F(f.value,a=>(i(),V(O,{title:a.orgName,class:"power-card",hoverable:"",key:a.orgId},{extra:n(()=>[t("a",{onClick:l=>q(a)},"\u540C\u6B65\u5230\u5176\u4ED6\u516C\u53F8",8,Fe)]),default:n(()=>[t("div",Ve,[t("div",null,[e[7]||(e[7]=t("span",null,"\u542F\u7528\u672C\u673A\u6784\uFF1A",-1)),o($,{modelValue:a.enableThisOrg,"onUpdate:modelValue":l=>a.enableThisOrg=l,"open-value":!0,"close-value":!1,onChange:l=>J(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),(i(!0),_(U,null,F(a.roleBindItemList,(l,oe)=>(i(),_("div",{key:oe},[o(Z,{checked:l.checkedFlag,"onUpdate:checked":M=>l.checkedFlag=M,onChange:M=>K(a,l)},{default:n(()=>[v(B(l.roleName)+" ",1),l.roleType==10?(i(),_("span",Be,"\uFF08\u7CFB\u7EDF\u89D2\u8272\uFF09")):N("",!0),l.roleType==15?(i(),_("span",Le,"\uFF08\u4E1A\u52A1\u89D2\u8272\uFF09")):N("",!0)]),_:2},1032,["checked","onUpdate:checked","onChange"])]))),128))]),t("div",Ae,[o(S,{type:"primary",danger:"",shape:"circle",onClick:l=>H(a)},{default:n(()=>[o(T,{iconClass:"icon-opt-shanchu","font-size":"20px",title:"\u5220\u9664",color:"#fff"})]),_:2},1032,["onClick"])])]),_:2},1032,["title"]))),128)),o(O,{class:"power-card power-add",hoverable:"",onClick:Q},{default:n(()=>[t("div",Ne,[o(ee,{class:"plus-outlined"})])]),_:1})])],512),[[A,r.value]]),L(t("div",Te,[o(E,{class:"power-empty"})],512),[[A,!r.value]])]),_:1},8,["spinning"])]),_:1})]),_:1}),C.value?(i(),V(fe,{key:0,visible:C.value,"onUpdate:visible":e[1]||(e[1]=a=>C.value=a),onDone:c,userId:r.value},null,8,["visible","userId"])):N("",!0)])}}}),Qe=ae($e,[["__scopeId","data-v-9fccb346"]]);export{Qe as default};
