package cn.stylefeng.roses.ent.saas.modular.auth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink;
import cn.stylefeng.roses.ent.saas.modular.auth.enums.TenantLinkExceptionEnum;
import cn.stylefeng.roses.ent.saas.modular.auth.mapper.TenantLinkMapper;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantLinkService;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户-功能包业务实现层
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@Service
public class TenantLinkServiceImpl extends ServiceImpl<TenantLinkMapper, TenantLink> implements TenantLinkService {

    @Override
    public void add(TenantLinkRequest tenantLinkRequest) {
        TenantLink tenantLink = new TenantLink();
        BeanUtil.copyProperties(tenantLinkRequest, tenantLink);
        this.save(tenantLink);
    }

    @Override
    public void del(TenantLinkRequest tenantLinkRequest) {
        TenantLink tenantLink = this.queryTenantLink(tenantLinkRequest);
        this.removeById(tenantLink.getTenantLinkId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(TenantLinkRequest tenantLinkRequest) {
        this.removeByIds(tenantLinkRequest.getBatchDeleteIdList());
    }

    @Override
    public void edit(TenantLinkRequest tenantLinkRequest) {
        TenantLink tenantLink = this.queryTenantLink(tenantLinkRequest);
        BeanUtil.copyProperties(tenantLinkRequest, tenantLink);
        this.updateById(tenantLink);
    }

    @Override
    public TenantLink detail(TenantLinkRequest tenantLinkRequest) {
        return this.queryTenantLink(tenantLinkRequest);
    }

    @Override
    public PageResult<TenantLink> findPage(TenantLinkRequest tenantLinkRequest) {
        LambdaQueryWrapper<TenantLink> wrapper = createWrapper(tenantLinkRequest);
        Page<TenantLink> pageList = this.page(PageFactory.defaultPage(), wrapper);
        return PageResultFactory.createPageResult(pageList);
    }

    @Override
    public void removeByPackageId(Long packageId) {
        if (ObjectUtil.isEmpty(packageId)) {
            return;
        }
        LambdaQueryWrapper<TenantLink> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantLink::getPackageId, packageId);
        this.remove(queryWrapper);
    }

    @Override
    public void removeByTenantId(Long tenantId) {
        if (ObjectUtil.isEmpty(tenantId)) {
            return;
        }
        LambdaQueryWrapper<TenantLink> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantLink::getTenantId, tenantId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantAuthPackageLink(Long tenantId, List<TenantLink> tenantLinkList) {

        // 先删除掉原有的租户和功能包的关联
        this.removeByTenantId(tenantId);

        // 批量插入新的关联
        this.saveBatch(tenantLinkList);
    }

    @Override
    public List<Long> getPackageTenantIdList(Long packageId) {
        TenantLinkRequest tenantLinkRequest = new TenantLinkRequest();
        tenantLinkRequest.setPackageId(packageId);
        LambdaQueryWrapper<TenantLink> wrapper = this.createWrapper(tenantLinkRequest);
        wrapper.select(TenantLink::getTenantId);
        List<TenantLink> list = this.list(wrapper);

        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(TenantLink::getTenantId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getTenantPackageIdList(Long tenantId) {
        TenantLinkRequest tenantLinkRequest = new TenantLinkRequest();
        tenantLinkRequest.setTenantId(tenantId);
        LambdaQueryWrapper<TenantLink> wrapper = this.createWrapper(tenantLinkRequest);
        wrapper.select(TenantLink::getPackageId);
        List<TenantLink> list = this.list(wrapper);

        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(TenantLink::getPackageId).collect(Collectors.toList());
    }

    @Override
    public List<TenantLink> findList(TenantLinkRequest tenantLinkRequest) {
        LambdaQueryWrapper<TenantLink> wrapper = this.createWrapper(tenantLinkRequest);
        return this.list(wrapper);
    }

    /**
     * 获取信息
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    private TenantLink queryTenantLink(TenantLinkRequest tenantLinkRequest) {
        TenantLink tenantLink = this.getById(tenantLinkRequest.getTenantLinkId());
        if (ObjectUtil.isEmpty(tenantLink)) {
            throw new ServiceException(TenantLinkExceptionEnum.TENANT_LINK_NOT_EXISTED);
        }
        return tenantLink;
    }

    /**
     * 创建查询wrapper
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    private LambdaQueryWrapper<TenantLink> createWrapper(TenantLinkRequest tenantLinkRequest) {
        LambdaQueryWrapper<TenantLink> queryWrapper = new LambdaQueryWrapper<>();

        // 根据租户id查询
        Long tenantId = tenantLinkRequest.getTenantId();
        queryWrapper.eq(ObjectUtil.isNotNull(tenantId), TenantLink::getTenantId, tenantId);

        // 根据功能包id查询绑定的租户信息
        Long packageId = tenantLinkRequest.getPackageId();
        if (ObjectUtil.isNotEmpty(packageId)) {
            queryWrapper.eq(TenantLink::getPackageId, packageId);
        }

        return queryWrapper;
    }

}
