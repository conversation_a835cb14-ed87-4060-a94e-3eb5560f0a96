package cn.stylefeng.roses.kernel.erp.modular.inventory.service;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryOperationRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryValidationResponse;

import java.math.BigDecimal;

/**
 * 库存管理业务规则服务接口
 *
 * <AUTHOR>
 * @since 2025/07/28 11:30
 */
public interface InventoryBusinessRuleService {

    /**
     * 验证库存操作类型是否有效
     *
     * @param operationType 操作类型
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    boolean validateOperationType(String operationType);

    /**
     * 验证关联单据类型是否有效
     *
     * @param referenceType 关联单据类型
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    boolean validateReferenceType(String referenceType);

    /**
     * 验证库存操作请求
     *
     * @param request 库存操作请求
     * @return 验证结果
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    InventoryValidationResponse validateInventoryOperation(InventoryOperationRequest request);

    /**
     * 检查商品是否需要库存管理
     *
     * @param product 商品信息
     * @param supplier 供应商信息
     * @return 是否需要库存管理
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    boolean needInventoryManagement(ErpProduct product, ErpSupplier supplier);

    /**
     * 检查库存是否充足
     *
     * @param inventory 库存信息
     * @param requiredQuantity 需要数量
     * @return 是否充足
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    boolean isInventorySufficient(Inventory inventory, BigDecimal requiredQuantity);

    /**
     * 检查库存是否需要预警
     *
     * @param inventory 库存信息
     * @return 是否需要预警
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    boolean needInventoryWarning(Inventory inventory);

    /**
     * 计算库存总价值
     *
     * @param currentStock 当前库存数量
     * @param avgCost 平均成本
     * @return 库存总价值
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    BigDecimal calculateTotalValue(BigDecimal currentStock, BigDecimal avgCost);

    /**
     * 计算新的平均成本
     *
     * @param currentStock 当前库存数量
     * @param currentAvgCost 当前平均成本
     * @param inQuantity 入库数量
     * @param inCost 入库成本
     * @return 新的平均成本
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    BigDecimal calculateNewAvgCost(BigDecimal currentStock, BigDecimal currentAvgCost, 
                                   BigDecimal inQuantity, BigDecimal inCost);

    /**
     * 获取库存状态
     *
     * @param inventory 库存信息
     * @return 库存状态
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    String getInventoryStatus(Inventory inventory);

    /**
     * 获取库存状态描述
     *
     * @param status 库存状态
     * @return 状态描述
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    String getInventoryStatusDescription(String status);

    /**
     * 验证库存参数的完整性
     *
     * @param inventory 库存信息
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    void validateInventoryParams(Inventory inventory);

    /**
     * 验证库存操作数量
     *
     * @param operationType 操作类型
     * @param quantity 操作数量
     * @param currentStock 当前库存
     * @return 是否有效
     * <AUTHOR>
     * @since 2025/07/28 11:30
     */
    boolean validateOperationQuantity(String operationType, BigDecimal quantity, BigDecimal currentStock);

}