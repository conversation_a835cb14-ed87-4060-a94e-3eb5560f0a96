package cn.stylefeng.roses.ent.mobile.manage.controller;

import cn.stylefeng.roses.ent.mobile.manage.pojo.config.ChangePhoneRequest;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.SendPhoneCodeRequest;
import cn.stylefeng.roses.ent.mobile.manage.service.MobileSystemConfigService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 移动端系统设置
 *
 * <AUTHOR>
 * @since 2024/3/24 22:52
 */
@RestController
@ApiResource(name = "移动端系统设置")
public class MobileSystemConfigController {

    @Resource
    private MobileSystemConfigService mobileSystemConfigService;

    /**
     * 移动端-更换手机-发送验证码
     *
     * <AUTHOR>
     * @since 2024/3/20 23:10
     */
    @GetResource(name = "移动端-更换手机-发送验证码", path = "/mobile/config/sendChangePhoneCode")
    public ResponseData<?> sendChangePhoneCode(@Validated SendPhoneCodeRequest changePhoneRequest) {
        mobileSystemConfigService.sendChangePhoneCode(changePhoneRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 确认更换手机号
     *
     * <AUTHOR>
     * @since 2024/3/24 23:32
     */
    @PostResource(name = "确认更换手机号", path = "/mobile/config/ensureChangePhone")
    public ResponseData<?> ensureChangePhone(@RequestBody @Validated ChangePhoneRequest changePhoneRequest) {
        mobileSystemConfigService.ensureChangePhone(changePhoneRequest);
        return new SuccessResponseData<>();
    }

}
