package cn.stylefeng.roses.kernel.websocket.api.pojo;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * websocket传输的数据对象
 *
 * <AUTHOR>
 * @since 2024-01-15 11:00
 */
@Getter
@Setter
public class WebSocketTransferDTO implements Serializable {

    /**
     * 消息类型
     */
    private String res_type;

    /**
     * 具体的消息
     */
    private String res_msg;

    /**
     * 消息的具体内容
     */
    private Object res_data;

    /**
     * 响应的事件
     */
    private Date res_time;

    public WebSocketTransferDTO() {
    }

    public WebSocketTransferDTO(String resType, String resMsg, Object resData) {
        res_msg = resMsg;
        res_type = resType;
        res_data = resData;
        res_time = new Date();
    }

    public String toString() {
        return JSON.toJSONString(this);
    }

}
