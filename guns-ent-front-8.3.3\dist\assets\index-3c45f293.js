import{R as g,am as oe,_ as ie,ac as ce,ad as de,P as ue,an as he,E as N,ao as G,ap as H,aq as j,s as _e,r as x,L as F,o as pe,ag as ge,k as L,a as u,c as C,b,d as a,w as s,F as E,e as J,f as w,g as i,t as f,ar as ye,h as Q,m as h,M as W,l as ve,v as me,W as fe,J as Ae,U as ke,B as Ce,n as we,G as Te,a9 as Re,i as Le}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{I as D}from"./InventoryAlertRecordApi-19b95203.js";import{I as A}from"./inventoryAlert-17fb61ca.js";import be from"./AlertRuleForm-2825bcd1.js";import Se from"./AlertTestResult-8ae7f38d.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */class v{static add(e){return g.post("/erp/inventoryAlert/rule/add",e)}static edit(e){return g.post("/erp/inventoryAlert/rule/edit",e)}static delete(e){return g.post("/erp/inventoryAlert/rule/delete",e)}static batchDelete(e){return g.post("/erp/inventoryAlert/rule/batchDelete",e)}static detail(e){return g.getAndLoadData("/erp/inventoryAlert/rule/detail",e)}static findPage(e){return g.getAndLoadData("/erp/inventoryAlert/rule/page",e)}static findList(e){return g.getAndLoadData("/erp/inventoryAlert/rule/list",e)}static updateStatus(e){return g.post("/erp/inventoryAlert/rule/updateStatus",e)}static testRule(e){return g.post("/erp/inventoryAlert/rule/test",e)}static executeCheck(){return g.post("/erp/inventoryAlert/rule/executeCheck")}}const Oe=oe("inventoryAlert",{state:()=>({ruleList:[],ruleTotal:0,ruleLoading:!1,recordList:[],recordTotal:0,recordLoading:!1,statistics:{totalAlerts:0,criticalAlerts:0,warningAlerts:0,infoAlerts:0,pendingAlerts:0,processingAlerts:0,resolvedAlerts:0,ignoredAlerts:0},currentRule:null,currentRecord:null,productOptions:[],categoryOptions:[],userOptions:[],recentRecords:[],overview:null}),getters:{enabledRulesCount:l=>l.ruleList.filter(e=>e.isEnabled==="Y").length,pendingAlertsCount:l=>l.statistics.pendingAlerts||0,criticalAlertsCount:l=>l.statistics.criticalAlerts||0,totalAlertsCount:l=>l.statistics.totalAlerts||0,resolveRate:l=>{const e=l.statistics.totalAlerts||0,d=l.statistics.resolvedAlerts||0;return e>0?Math.round(d/e*100):0}},actions:{async loadRuleList(l={}){this.ruleLoading=!0;try{const e=await v.findPage(l);this.ruleList=e.rows||[],this.ruleTotal=e.totalRows||0}catch(e){console.error("\u52A0\u8F7D\u9884\u8B66\u89C4\u5219\u5217\u8868\u5931\u8D25:",e),this.ruleList=[],this.ruleTotal=0}finally{this.ruleLoading=!1}},async loadRecordList(l={}){this.recordLoading=!0;try{const e=await D.findPage(l);this.recordList=e.rows||[],this.recordTotal=e.totalRows||0}catch(e){console.error("\u52A0\u8F7D\u9884\u8B66\u8BB0\u5F55\u5217\u8868\u5931\u8D25:",e),this.recordList=[],this.recordTotal=0}finally{this.recordLoading=!1}},async loadStatistics(l={}){try{const e=await D.getStatistics(l);this.statistics={totalAlerts:e.total_alerts||0,criticalAlerts:e.critical_alerts||0,warningAlerts:e.warning_alerts||0,infoAlerts:e.info_alerts||0,pendingAlerts:e.pending_alerts||0,processingAlerts:e.processing_alerts||0,resolvedAlerts:e.resolved_alerts||0,ignoredAlerts:e.ignored_alerts||0}}catch(e){console.error("\u52A0\u8F7D\u9884\u8B66\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:",e)}},async loadRecentRecords(l={}){try{const e=await D.getRecentRecords(l);this.recentRecords=e||[]}catch(e){console.error("\u52A0\u8F7D\u6700\u8FD1\u9884\u8B66\u8BB0\u5F55\u5931\u8D25:",e),this.recentRecords=[]}},async loadOverview(l={}){try{const e=await D.getOverview(l);this.overview=e,e.statistics&&(this.statistics={totalAlerts:e.statistics.total_alerts||0,criticalAlerts:e.statistics.critical_alerts||0,warningAlerts:e.statistics.warning_alerts||0,infoAlerts:e.statistics.info_alerts||0,pendingAlerts:e.statistics.pending_alerts||0,processingAlerts:e.statistics.processing_alerts||0,resolvedAlerts:e.statistics.resolved_alerts||0,ignoredAlerts:e.statistics.ignored_alerts||0}),e.recentRecords&&(this.recentRecords=e.recentRecords)}catch(e){console.error("\u52A0\u8F7D\u6982\u89C8\u6570\u636E\u5931\u8D25:",e)}},setCurrentRule(l){this.currentRule=l},setCurrentRecord(l){this.currentRecord=l},updateRuleStatus(l,e){const d=this.ruleList.find(t=>t.id===l);d&&(d.isEnabled=e)},updateRecordStatus(l,e){const d=this.recordList.find(m=>m.id===l);d&&(d.status=e,d.handleTime=new Date);const t=this.recentRecords.find(m=>m.id===l);t&&(t.status=e,t.handleTime=new Date)},clearData(){this.ruleList=[],this.ruleTotal=0,this.recordList=[],this.recordTotal=0,this.currentRule=null,this.currentRecord=null,this.recentRecords=[],this.overview=null},async refreshData(){await Promise.all([this.loadStatistics(),this.loadRecentRecords(),this.loadRuleList({pageNo:1,pageSize:10}),this.loadRecordList({pageNo:1,pageSize:10,statusFilter:"PENDING"})])},async refreshOverview(){await this.loadOverview()}}});const Ie={name:"InventoryAlertRuleIndex",components:{SearchOutlined:ce,ReloadOutlined:de,PlusOutlined:ue,PlayCircleOutlined:he,ExclamationCircleOutlined:N,WarningOutlined:G,InfoCircleOutlined:H,QuestionCircleOutlined:j,AlertRuleForm:be,AlertTestResult:Se},setup(){const l=Oe(),e=_e({searchText:"",ruleTypeFilter:void 0,alertLevelFilter:void 0,isEnabledFilter:void 0}),d=x(!1),t=x(!1),m=x(null),V=x([]),p=x([]),P=A.getAlertTypeOptions(),k=A.getAlertLevelOptions();A.getTargetTypeOptions();const T=[{title:"\u89C4\u5219\u540D\u79F0",dataIndex:"ruleName",key:"ruleName",width:200,ellipsis:!0},{title:"\u9884\u8B66\u7C7B\u578B",dataIndex:"ruleType",key:"ruleType",width:120},{title:"\u9884\u8B66\u7EA7\u522B",dataIndex:"alertLevel",key:"alertLevel",width:100},{title:"\u76EE\u6807\u7C7B\u578B",dataIndex:"targetType",key:"targetType",width:120},{title:"\u9608\u503C",dataIndex:"thresholdValue",key:"thresholdValue",width:100},{title:"\u68C0\u67E5\u9891\u7387(\u5206\u949F)",dataIndex:"checkFrequency",key:"checkFrequency",width:120},{title:"\u542F\u7528\u72B6\u6001",dataIndex:"isEnabled",key:"isEnabled",width:100},{title:"\u6700\u540E\u68C0\u67E5\u65F6\u95F4",dataIndex:"lastCheckTime",key:"lastCheckTime",width:160},{title:"\u64CD\u4F5C",key:"action",width:200,fixed:"right"}],S=F(()=>l.ruleList),O=F(()=>l.ruleLoading),_=F(()=>l.ruleTotal),z=F(()=>({current:1,pageSize:20,total:_.value,showSizeChanger:!0,showQuickJumper:!0,showTotal:r=>"\u5171 ".concat(r," \u6761\u8BB0\u5F55")})),Y=F(()=>({selectedRowKeys:p.value,onChange:r=>{p.value=r}})),y=(r={})=>{const o={pageNo:1,pageSize:20,...e,...r};l.loadRuleList(o)},I=()=>{y()},B=()=>{Object.keys(e).forEach(r=>{e[r]=void 0}),y()},M=()=>{m.value=null,d.value=!0},U=async r=>{try{const o=await v.detail({id:r.id});m.value=o.data,d.value=!0}catch(o){console.error("\u83B7\u53D6\u9884\u8B66\u89C4\u5219\u8BE6\u60C5\u5931\u8D25:",o),h.error("\u83B7\u53D6\u8BE6\u60C5\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}},q=r=>{W.confirm({title:"\u786E\u8BA4\u5220\u9664",content:'\u786E\u5B9A\u8981\u5220\u9664\u9884\u8B66\u89C4\u5219"'.concat(r.ruleName,'"\u5417\uFF1F'),icon:N,onOk:async()=>{try{await v.delete({id:r.id}),h.success("\u5220\u9664\u6210\u529F"),y()}catch(o){h.error("\u5220\u9664\u5931\u8D25")}}})},K=()=>{if(p.value.length===0){h.warning("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u8BB0\u5F55");return}W.confirm({title:"\u786E\u8BA4\u6279\u91CF\u5220\u9664",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ".concat(p.value.length," \u6761\u9884\u8B66\u89C4\u5219\u5417\uFF1F"),icon:N,onOk:async()=>{try{await v.batchDelete({idList:p.value}),h.success("\u6279\u91CF\u5220\u9664\u6210\u529F"),n(),y()}catch(r){h.error("\u6279\u91CF\u5220\u9664\u5931\u8D25")}}})},n=()=>{p.value=[]},c=async(r,o)=>{try{await v.updateStatus({id:r.id,isEnabled:o?"Y":"N"}),h.success("\u72B6\u6001\u66F4\u65B0\u6210\u529F"),l.updateRuleStatus(r.id,o?"Y":"N")}catch(se){h.error("\u72B6\u6001\u66F4\u65B0\u5931\u8D25")}},R=async r=>{try{const o=await v.testRule({id:r.id});V.value=o,t.value=!0}catch(o){h.error("\u6D4B\u8BD5\u5931\u8D25")}},X=async()=>{try{await v.executeCheck(),h.success("\u9884\u8B66\u68C0\u67E5\u5DF2\u5F00\u59CB\u6267\u884C")}catch(r){h.error("\u6267\u884C\u9884\u8B66\u68C0\u67E5\u5931\u8D25")}},Z=r=>{y({pageNo:r.current,pageSize:r.pageSize})},$=async r=>{try{r.id?(await v.edit(r),h.success("\u7F16\u8F91\u9884\u8B66\u89C4\u5219\u6210\u529F")):(await v.add(r),h.success("\u65B0\u589E\u9884\u8B66\u89C4\u5219\u6210\u529F")),d.value=!1,y()}catch(o){console.error("\u4FDD\u5B58\u9884\u8B66\u89C4\u5219\u5931\u8D25:",o),h.error("\u4FDD\u5B58\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}},ee=()=>{d.value=!1},te=r=>A.getAlertTypeInfo(r),le=r=>A.getAlertLevelInfo(r),re=r=>A.getTargetTypeName(r),ae=(r,o)=>A.formatThresholdValue(r,o),ne=r=>{const o=A.getAlertLevelIcon(r);return{"exclamation-circle":N,warning:G,"info-circle":H,"question-circle":j}[o]||j};return pe(()=>{y()}),{searchForm:e,formVisible:d,testVisible:t,currentRule:m,testResult:V,selectedRowKeys:p,alertTypeOptions:P,alertLevelOptions:k,columns:T,ruleList:S,ruleLoading:O,pagination:z,rowSelection:Y,handleSearch:I,handleReset:B,handleAdd:M,handleEdit:U,handleDelete:q,handleBatchDelete:K,clearSelection:n,handleStatusChange:c,handleTest:R,handleExecuteCheck:X,handleTableChange:Z,handleFormSubmit:$,handleFormCancel:ee,getAlertTypeInfo:te,getAlertLevelInfo:le,getTargetTypeName:re,formatThresholdValue:ae,getAlertLevelIcon:ne,dayjs:ge}}},xe={class:"guns-layout"},Fe={class:"guns-layout-content"},Ee={class:"content-main"},Ne={class:"content-main-body"},De={class:"search-area"},Ve={class:"table-area"},Pe={key:0,class:"batch-actions"};function ze(l,e,d,t,m,V){const p=L("SearchOutlined"),P=ve,k=me,T=fe,S=Ae,O=ke,_=Ce,z=L("ReloadOutlined"),Y=L("PlusOutlined"),y=L("PlayCircleOutlined"),I=we,B=Te,M=Re,U=Le,q=L("alert-rule-form"),K=L("alert-test-result");return u(),C("div",xe,[b("div",Fe,[b("div",Ee,[b("div",Ne,[b("div",De,[a(B,{gutter:16},{default:s(()=>[a(k,{span:6},{default:s(()=>[a(P,{value:t.searchForm.searchText,"onUpdate:value":e[0]||(e[0]=n=>t.searchForm.searchText=n),placeholder:"\u89C4\u5219\u540D\u79F0","allow-clear":"",onPressEnter:t.handleSearch},{prefix:s(()=>[a(p)]),_:1},8,["value","onPressEnter"])]),_:1}),a(k,{span:4},{default:s(()=>[a(S,{value:t.searchForm.ruleTypeFilter,"onUpdate:value":e[1]||(e[1]=n=>t.searchForm.ruleTypeFilter=n),placeholder:"\u9884\u8B66\u7C7B\u578B","allow-clear":"",onChange:t.handleSearch},{default:s(()=>[(u(!0),C(E,null,J(t.alertTypeOptions,n=>(u(),w(T,{key:n.value,value:n.value},{default:s(()=>[i(f(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange"])]),_:1}),a(k,{span:4},{default:s(()=>[a(S,{value:t.searchForm.alertLevelFilter,"onUpdate:value":e[2]||(e[2]=n=>t.searchForm.alertLevelFilter=n),placeholder:"\u9884\u8B66\u7EA7\u522B","allow-clear":"",onChange:t.handleSearch},{default:s(()=>[(u(!0),C(E,null,J(t.alertLevelOptions,n=>(u(),w(T,{key:n.value,value:n.value},{default:s(()=>[a(O,{color:n.color},{default:s(()=>[i(f(n.label),1)]),_:2},1032,["color"])]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange"])]),_:1}),a(k,{span:4},{default:s(()=>[a(S,{value:t.searchForm.isEnabledFilter,"onUpdate:value":e[3]||(e[3]=n=>t.searchForm.isEnabledFilter=n),placeholder:"\u542F\u7528\u72B6\u6001","allow-clear":"",onChange:t.handleSearch},{default:s(()=>[a(T,{value:"Y"},{default:s(()=>e[6]||(e[6]=[i("\u542F\u7528")])),_:1,__:[6]}),a(T,{value:"N"},{default:s(()=>e[7]||(e[7]=[i("\u505C\u7528")])),_:1,__:[7]})]),_:1},8,["value","onChange"])]),_:1}),a(k,{span:6},{default:s(()=>[a(I,null,{default:s(()=>[a(_,{type:"primary",onClick:t.handleSearch},{default:s(()=>[a(p),e[8]||(e[8]=i(" \u641C\u7D22 "))]),_:1,__:[8]},8,["onClick"]),a(_,{onClick:t.handleReset},{default:s(()=>[a(z),e[9]||(e[9]=i(" \u91CD\u7F6E "))]),_:1,__:[9]},8,["onClick"]),a(_,{type:"primary",onClick:t.handleAdd},{default:s(()=>[a(Y),e[10]||(e[10]=i(" \u65B0\u589E\u89C4\u5219 "))]),_:1,__:[10]},8,["onClick"]),a(_,{onClick:t.handleExecuteCheck},{default:s(()=>[a(y),e[11]||(e[11]=i(" \u6267\u884C\u68C0\u67E5 "))]),_:1,__:[11]},8,["onClick"])]),_:1})]),_:1})]),_:1})]),b("div",Ve,[a(U,{columns:t.columns,"data-source":t.ruleList,loading:t.ruleLoading,pagination:t.pagination,"row-key":"id","row-selection":t.rowSelection,onChange:t.handleTableChange},{bodyCell:s(({column:n,record:c})=>[n.key==="ruleType"?(u(),w(O,{key:0,color:t.getAlertTypeInfo(c.ruleType).color},{default:s(()=>[i(f(t.getAlertTypeInfo(c.ruleType).name),1)]),_:2},1032,["color"])):n.key==="alertLevel"?(u(),w(O,{key:1,color:t.getAlertLevelInfo(c.alertLevel).color},{icon:s(()=>[(u(),w(ye(t.getAlertLevelIcon(c.alertLevel))))]),default:s(()=>[i(" "+f(t.getAlertLevelInfo(c.alertLevel).name),1)]),_:2},1032,["color"])):n.key==="targetType"?(u(),C(E,{key:2},[i(f(t.getTargetTypeName(c.targetType)),1)],64)):n.key==="thresholdValue"?(u(),C(E,{key:3},[i(f(t.formatThresholdValue(c.thresholdValue,c.thresholdType)),1)],64)):n.key==="isEnabled"?(u(),w(M,{key:4,checked:c.isEnabled==="Y",onChange:R=>t.handleStatusChange(c,R)},null,8,["checked","onChange"])):n.key==="lastCheckTime"?(u(),C(E,{key:5},[i(f(c.lastCheckTime?t.dayjs(c.lastCheckTime).format("YYYY-MM-DD HH:mm:ss"):"-"),1)],64)):n.key==="action"?(u(),w(I,{key:6},{default:s(()=>[a(_,{type:"link",size:"small",onClick:R=>t.handleEdit(c)},{default:s(()=>e[12]||(e[12]=[i(" \u7F16\u8F91 ")])),_:2,__:[12]},1032,["onClick"]),a(_,{type:"link",size:"small",onClick:R=>t.handleTest(c)},{default:s(()=>e[13]||(e[13]=[i(" \u6D4B\u8BD5 ")])),_:2,__:[13]},1032,["onClick"]),a(_,{type:"link",size:"small",danger:"",onClick:R=>t.handleDelete(c)},{default:s(()=>e[14]||(e[14]=[i(" \u5220\u9664 ")])),_:2,__:[14]},1032,["onClick"])]),_:2},1024)):Q("",!0)]),_:1},8,["columns","data-source","loading","pagination","row-selection","onChange"])]),t.selectedRowKeys.length>0?(u(),C("div",Pe,[a(I,null,{default:s(()=>[b("span",null,"\u5DF2\u9009\u62E9 "+f(t.selectedRowKeys.length)+" \u9879",1),a(_,{danger:"",onClick:t.handleBatchDelete},{default:s(()=>e[15]||(e[15]=[i(" \u6279\u91CF\u5220\u9664 ")])),_:1,__:[15]},8,["onClick"]),a(_,{onClick:t.clearSelection},{default:s(()=>e[16]||(e[16]=[i(" \u53D6\u6D88\u9009\u62E9 ")])),_:1,__:[16]},8,["onClick"])]),_:1})])):Q("",!0)])])]),a(q,{visible:t.formVisible,"onUpdate:visible":e[4]||(e[4]=n=>t.formVisible=n),"form-data":t.currentRule,onSubmit:t.handleFormSubmit,onCancel:t.handleFormCancel},null,8,["visible","form-data","onSubmit","onCancel"]),a(K,{visible:t.testVisible,"onUpdate:visible":e[5]||(e[5]=n=>t.testVisible=n),data:t.testResult},null,8,["visible","data"])])}const Ze=ie(Ie,[["render",ze],["__scopeId","data-v-4673e48d"]]);export{Ze as default};
