package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderRequest extends BaseRequest {

    /**
     * 入库单ID
     */
    @NotNull(message = "入库单ID不能为空", groups = {edit.class, delete.class, detail.class, confirm.class, receive.class})
    @ChineseDescription("入库单ID")
    private Long id;

    /**
     * 入库单号
     */
    @ChineseDescription("入库单号")
    private String orderNo;

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空", groups = {add.class, edit.class})
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 状态：DRAFT(草稿)、CONFIRMED(已确认)、COMPLETED(已完成)
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 总金额
     */
    @DecimalMin(value = "0", message = "总金额必须大于等于0", groups = {add.class, edit.class})
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

    /**
     * 付款方式
     */
    @ChineseDescription("付款方式")
    private String paymentMethod;

    /**
     * 付款账户
     */
    @ChineseDescription("付款账户")
    private String paymentAccount;

    /**
     * 订单日期
     */
    @NotNull(message = "订单日期不能为空", groups = {add.class, edit.class})
    @ChineseDescription("订单日期")
    private LocalDate orderDate;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 采购入库单明细列表
     */
    @NotEmpty(message = "采购入库单明细不能为空", groups = {add.class, edit.class})
    @ChineseDescription("采购入库单明细列表")
    private List<PurchaseOrderDetailRequest> detailList;

    /**
     * 入库单ID列表（批量操作用）
     */
    @ChineseDescription("入库单ID列表")
    private List<Long> idList;

    /**
     * 供应商名称（查询条件）
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 状态列表（查询条件）
     */
    @ChineseDescription("状态列表")
    private List<String> statusList;

    /**
     * 开始日期（查询条件）
     */
    @ChineseDescription("开始日期")
    private LocalDate startDate;

    /**
     * 结束日期（查询条件）
     */
    @ChineseDescription("结束日期")
    private LocalDate endDate;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：确认
     */
    public @interface confirm {
    }

    /**
     * 参数校验分组：入库
     */
    public @interface receive {
    }

}