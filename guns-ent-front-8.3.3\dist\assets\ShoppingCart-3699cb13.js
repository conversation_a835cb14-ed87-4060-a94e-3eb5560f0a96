import{R as k,r as G,L as j,o as ae,aL as re,M as ee,m as d,_ as se,a as P,c as F,at as R,f as Y,h as J,b as X,F as ne,e as oe,d as ie,w as ce,a5 as Z}from"./index-18a1ea24.js";import{u as te}from"./pos-8a64eaa6.js";import{P as le,a as ue}from"./performance-monitor-659a8228.js";import{N as O,P as me,S as pe}from"./constants-2fa70699.js";import de from"./CartHeader-ed27259f.js";import fe from"./CartItem-343bbd45.js";import ge from"./CartSummary-686b7994.js";class ${static createApiWrapper(e,t={}){const{context:a="Cart API\u8C03\u7528",showMessage:l=!0,showNotification:c=!1,retryOptions:p={maxRetries:2,retryDelay:1e3}}=t,A=ue.measureApiCall(a,e);return le.wrapApiCall(A,{showMessage:l,showNotification:c,context:a,retryOptions:p})}static async checkInventory(e,t){const a=()=>k.post("/erp/pos/product/checkStock",{productId:e,quantity:t});return this.createApiWrapper(a,{context:"\u68C0\u67E5\u5546\u54C1\u5E93\u5B58",showMessage:!0,retryOptions:{maxRetries:3,retryDelay:500}})()}static async batchCheckInventory(e){const t=()=>k.post("/erp/pos/product/batchCheckStock",{items:e});return this.createApiWrapper(t,{context:"\u6279\u91CF\u68C0\u67E5\u5546\u54C1\u5E93\u5B58",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async getProductDetail(e){const t=()=>k.get("/erp/pos/product/detail",e);return this.createApiWrapper(t,{context:"\u83B7\u53D6\u5546\u54C1\u8BE6\u60C5",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getProductByBarcode(e){const t=()=>k.get("/erp/pos/product/barcode",{barcode:e});return this.createApiWrapper(t,{context:"\u6839\u636E\u6761\u5F62\u7801\u83B7\u53D6\u5546\u54C1",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async getProductsBatch(e){const t=()=>k.post("/erp/pos/product/batch",{productIds:e});return this.createApiWrapper(t,{context:"\u6279\u91CF\u83B7\u53D6\u5546\u54C1\u4FE1\u606F",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async searchProducts(e={}){const t=()=>k.get("/erp/pos/products/search",{...e,limit:e.limit||20});return this.createApiWrapper(t,{context:"\u641C\u7D22\u5546\u54C1",showMessage:!1,retryOptions:{maxRetries:1,retryDelay:300}})()}static async saveCartState(e){const t=()=>k.post("/erp/pos/cart/suspend",{...e,suspendTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u6302\u5355\u4FDD\u5B58",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async restoreCartState(e){const t=()=>k.get("/erp/pos/cart/restore/".concat(e));return this.createApiWrapper(t,{context:"\u6062\u590D\u6302\u5355",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getSuspendedCarts(e={}){const t=()=>k.get("/erp/pos/cart/suspended",{...e,limit:e.limit||50});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u6302\u5355\u5217\u8868",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async deleteSuspendedCart(e){const t=()=>k.delete("/erp/pos/cart/suspended/".concat(e));return this.createApiWrapper(t,{context:"\u5220\u9664\u6302\u5355",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async clearExpiredCarts(e=24){const t=()=>k.post("/erp/pos/cart/clearExpired",{expireHours:e});return this.createApiWrapper(t,{context:"\u6E05\u7406\u8FC7\u671F\u6302\u5355",showMessage:!1,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async validateCart(e){const t=()=>k.post("/erp/pos/cart/validate",e);return this.createApiWrapper(t,{context:"\u9A8C\u8BC1\u8D2D\u7269\u8F66",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async calculateCartTotal(e){const t=()=>k.post("/erp/pos/cart/calculate",e);return this.createApiWrapper(t,{context:"\u8BA1\u7B97\u8D2D\u7269\u8F66\u91D1\u989D",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:300}})()}static async applyCoupon(e){const t=()=>k.post("/erp/pos/cart/applyCoupon",e);return this.createApiWrapper(t,{context:"\u5E94\u7528\u4F18\u60E0\u5238",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}static async removeCoupon(e){const t=()=>k.post("/erp/pos/cart/removeCoupon",e);return this.createApiWrapper(t,{context:"\u79FB\u9664\u4F18\u60E0\u5238",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:500}})()}}class Q{static add(e,t){const a=Math.pow(10,O.AMOUNT_PRECISION);return Math.round((e+t)*a)/a}static subtract(e,t){const a=Math.pow(10,O.AMOUNT_PRECISION);return Math.round((e-t)*a)/a}static multiply(e,t){const a=Math.pow(10,O.AMOUNT_PRECISION);return Math.round(e*t*a)/a}static divide(e,t){if(t===0)throw new Error("\u9664\u6570\u4E0D\u80FD\u4E3A\u96F6");const a=Math.pow(10,O.AMOUNT_PRECISION);return Math.round(e/t*a)/a}static round(e,t=O.AMOUNT_PRECISION){const a=Math.pow(10,t);return Math.round(e*a)/a}}class W{static calculateSubtotal(e,t){if(e<0||t<0)throw new Error("\u4EF7\u683C\u548C\u6570\u91CF\u4E0D\u80FD\u4E3A\u8D1F\u6570");return Q.multiply(e,t)}static calculateTotal(e){if(!Array.isArray(e))throw new Error("\u8D2D\u7269\u8F66\u5546\u54C1\u5217\u8868\u5FC5\u987B\u662F\u6570\u7EC4");return e.reduce((t,a)=>{const l=this.calculateSubtotal(a.price||0,a.quantity||0);return Q.add(t,l)},0)}static calculateTotalQuantity(e){if(!Array.isArray(e))throw new Error("\u8D2D\u7269\u8F66\u5546\u54C1\u5217\u8868\u5FC5\u987B\u662F\u6570\u7EC4");return e.reduce((t,a)=>Q.add(t,a.quantity||0),0)}static calculateItemCount(e){return Array.isArray(e)?e.length:0}static calculateAveragePrice(e){if(!Array.isArray(e)||e.length===0)return 0;const t=this.calculateTotal(e),a=this.calculateTotalQuantity(e);return a===0?0:Q.divide(t,a)}}class Oe{static calculateChange(e,t){if(e<0||t<0)throw new Error("\u91D1\u989D\u4E0D\u80FD\u4E3A\u8D1F\u6570");const a=Q.subtract(e,t);return Math.max(0,a)}static calculateChangeDenominations(e,t=[100,50,20,10,5,1,.5,.1]){if(e<0)throw new Error("\u627E\u96F6\u91D1\u989D\u4E0D\u80FD\u4E3A\u8D1F\u6570");const a={};let l=e;for(const c of t)if(l>=c){const p=Math.floor(l/c);a[c]=p,l=Q.subtract(l,Q.multiply(c,p))}return a}}class L{static validateNumber(e,t={}){const{allowZero:a=!0,allowNegative:l=!1,min:c,max:p}=t;return typeof e!="number"||isNaN(e)||!isFinite(e)?{isValid:!1,message:"\u5FC5\u987B\u662F\u6709\u6548\u7684\u6570\u5B57"}:!a&&e===0?{isValid:!1,message:"\u4E0D\u80FD\u4E3A0"}:!l&&e<0?{isValid:!1,message:"\u4E0D\u80FD\u4E3A\u8D1F\u6570"}:typeof c=="number"&&e<c?{isValid:!1,message:"\u4E0D\u80FD\u5C0F\u4E8E".concat(c)}:typeof p=="number"&&e>p?{isValid:!1,message:"\u4E0D\u80FD\u5927\u4E8E".concat(p)}:{isValid:!0,message:""}}static validateString(e,t={}){const{required:a=!0,minLength:l,maxLength:c,pattern:p,patternMessage:A="\u683C\u5F0F\u4E0D\u6B63\u786E"}=t;if(typeof e!="string"){if(a)return{isValid:!1,message:"\u5FC5\u987B\u662F\u5B57\u7B26\u4E32"};if(e==null)return{isValid:!0,message:""}}const y=String(e||"");return a&&y.trim().length===0?{isValid:!1,message:"\u4E0D\u80FD\u4E3A\u7A7A"}:typeof l=="number"&&y.length<l?{isValid:!1,message:"\u957F\u5EA6\u4E0D\u80FD\u5C11\u4E8E".concat(l,"\u4E2A\u5B57\u7B26")}:typeof c=="number"&&y.length>c?{isValid:!1,message:"\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7".concat(c,"\u4E2A\u5B57\u7B26")}:p instanceof RegExp&&y.length>0&&!p.test(y)?{isValid:!1,message:A}:{isValid:!0,message:""}}static validateArray(e,t={}){const{required:a=!0,minLength:l,maxLength:c}=t;if(!Array.isArray(e)){if(a)return{isValid:!1,message:"\u5FC5\u987B\u662F\u6570\u7EC4"};if(e==null)return{isValid:!0,message:""}}const p=Array.isArray(e)?e:[];return a&&p.length===0?{isValid:!1,message:"\u4E0D\u80FD\u4E3A\u7A7A"}:typeof l=="number"&&p.length<l?{isValid:!1,message:"\u81F3\u5C11\u9700\u8981".concat(l,"\u4E2A\u5143\u7D20")}:typeof c=="number"&&p.length>c?{isValid:!1,message:"\u6700\u591A\u53EA\u80FD\u6709".concat(c,"\u4E2A\u5143\u7D20")}:{isValid:!0,message:""}}}class z{static validateProduct(e){if(!e||typeof e!="object")return{isValid:!1,message:"\u5546\u54C1\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A"};const t=L.validateString(e.id,{required:!0,minLength:1,maxLength:50});if(!t.isValid)return{isValid:!1,message:"\u5546\u54C1ID".concat(t.message)};const a=L.validateString(e.name,{required:!0,minLength:1,maxLength:100});if(!a.isValid)return{isValid:!1,message:"\u5546\u54C1\u540D\u79F0".concat(a.message)};const l=L.validateNumber(e.price,{allowZero:!1,allowNegative:!1,min:.01,max:999999.99});return l.isValid?e.pricingType&&!Object.values(me).includes(e.pricingType)?{isValid:!1,message:"\u65E0\u6548\u7684\u8BA1\u4EF7\u7C7B\u578B"}:{isValid:!0,message:""}:{isValid:!1,message:"\u5546\u54C1\u4EF7\u683C".concat(l.message)}}static validateQuantity(e,t={}){const{maxQuantity:a=1e3,precision:l=O.QUANTITY_PRECISION}=t,c=L.validateNumber(e,{allowZero:!1,allowNegative:!1,min:.001,max:a});return c.isValid?(e.toString().split(".")[1]||"").length>l?{isValid:!1,message:"\u6570\u91CF\u7CBE\u5EA6\u4E0D\u80FD\u8D85\u8FC7".concat(l,"\u4F4D\u5C0F\u6570")}:{isValid:!0,message:""}:{isValid:!1,message:"\u5546\u54C1\u6570\u91CF".concat(c.message)}}static validateCartItem(e){if(!e||typeof e!="object")return{isValid:!1,message:"\u8D2D\u7269\u8F66\u9879\u4E0D\u80FD\u4E3A\u7A7A"};const t=this.validateProduct(e);if(!t.isValid)return t;const a=this.validateQuantity(e.quantity);if(!a.isValid)return a;if(typeof e.subtotal=="number"){const l=e.price*e.quantity,c=.01;if(Math.abs(e.subtotal-l)>c)return{isValid:!1,message:"\u5C0F\u8BA1\u91D1\u989D\u8BA1\u7B97\u9519\u8BEF"}}return{isValid:!0,message:""}}static validateCart(e){const t=L.validateArray(e,{required:!0,minLength:1,maxLength:O.MAX_CART_ITEMS});if(!t.isValid)return{isValid:!1,message:"\u8D2D\u7269\u8F66".concat(t.message)};for(let c=0;c<e.length;c++){const p=this.validateCartItem(e[c]);if(!p.isValid)return{isValid:!1,message:"\u7B2C".concat(c+1,"\u4E2A\u5546\u54C1").concat(p.message)}}const a=e.map(c=>c.id),l=[...new Set(a)];return a.length!==l.length?{isValid:!1,message:"\u8D2D\u7269\u8F66\u4E2D\u5B58\u5728\u91CD\u590D\u5546\u54C1"}:{isValid:!0,message:""}}}class _e{static validatePaymentAmount(e,t={}){const{maxAmount:a=999999.99}=t;return L.validateNumber(e,{allowZero:!1,allowNegative:!1,min:.01,max:a})}static validateCashPayment(e){if(!e||typeof e!="object")return{isValid:!1,message:"\u652F\u4ED8\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"};const t=this.validatePaymentAmount(e.payableAmount);if(!t.isValid)return{isValid:!1,message:"\u5E94\u4ED8\u91D1\u989D".concat(t.message)};const a=this.validatePaymentAmount(e.receivedAmount);return a.isValid?e.receivedAmount<e.payableAmount?{isValid:!1,message:"\u5B9E\u6536\u91D1\u989D\u4E0D\u80FD\u5C11\u4E8E\u5E94\u4ED8\u91D1\u989D"}:{isValid:!0,message:""}:{isValid:!1,message:"\u5B9E\u6536\u91D1\u989D".concat(a.message)}}static validateQrCodePayment(e){if(!e||typeof e!="object")return{isValid:!1,message:"\u652F\u4ED8\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"};const t=this.validatePaymentAmount(e.payableAmount);return t.isValid?["WECHAT","ALIPAY"].includes(e.paymentMethod)?{isValid:!0,message:""}:{isValid:!1,message:"\u65E0\u6548\u7684\u626B\u7801\u652F\u4ED8\u65B9\u5F0F"}:{isValid:!1,message:"\u5E94\u4ED8\u91D1\u989D".concat(t.message)}}static validateBankCardPayment(e){if(!e||typeof e!="object")return{isValid:!1,message:"\u652F\u4ED8\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"};const t=this.validatePaymentAmount(e.payableAmount);if(!t.isValid)return{isValid:!1,message:"\u5E94\u4ED8\u91D1\u989D".concat(t.message)};const a=L.validateString(e.cardNo,{required:!0,minLength:16,maxLength:19,pattern:/^\d+$/,patternMessage:"\u94F6\u884C\u5361\u53F7\u53EA\u80FD\u5305\u542B\u6570\u5B57"});return a.isValid?this.validateLuhn(e.cardNo)?{isValid:!0,message:""}:{isValid:!1,message:"\u94F6\u884C\u5361\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E"}:{isValid:!1,message:"\u94F6\u884C\u5361\u53F7".concat(a.message)}}static validateLuhn(e){if(typeof e!="string"||!/^\d+$/.test(e))return!1;let t=0,a=!1;for(let l=e.length-1;l>=0;l--){let c=parseInt(e.charAt(l),10);a&&(c*=2,c>9&&(c=c%10+1)),t+=c,a=!a}return t%10===0}}function ye(v={}){const e=G({enableAutoBackup:!0,backupInterval:3e4,maxBackupCount:10,enableCrashDetection:!0,enableRecoveryPrompt:!0,compressionEnabled:!0,encryptionEnabled:!1,...v}),t=G({isRecovering:!1,hasRecoverableData:!1,lastBackupTime:null,backupCount:0,recoveryHistory:[]}),a={CART:"cart",ORDER:"order",MEMBER:"member",PAYMENT:"payment",SETTINGS:"settings",SESSION:"session"};let l=null,c=null;const p=j(()=>t.value.hasRecoverableData&&!t.value.isRecovering),A=j(()=>{const{lastBackupTime:s,backupCount:r}=t.value,o=s?Date.now()-s:null;return{lastBackupTime:s,backupCount:r,timeSinceBackup:o,isStale:o&&o>e.value.backupInterval*2}}),y=(s,r=0)=>"".concat(pe.DATA_BACKUP_PREFIX,"_").concat(s,"_").concat(r),D=s=>{if(!e.value.compressionEnabled)return JSON.stringify(s);try{return JSON.stringify(s).replace(/\\s+/g," ").trim()}catch(r){return console.warn("\u6570\u636E\u538B\u7F29\u5931\u8D25:",r),JSON.stringify(s)}},_=s=>{try{return JSON.parse(s)}catch(r){return console.warn("\u6570\u636E\u89E3\u538B\u5931\u8D25:",r),null}},H=s=>{if(!e.value.encryptionEnabled)return s;try{return btoa(s)}catch(r){return console.warn("\u6570\u636E\u52A0\u5BC6\u5931\u8D25:",r),s}},N=s=>{if(!e.value.encryptionEnabled)return s;try{return atob(s)}catch(r){return console.warn("\u6570\u636E\u89E3\u5BC6\u5931\u8D25:",r),s}},I=async(s,r,o={})=>{try{const{skipValidation:m=!1,metadata:f={}}=o;if(!m&&!K(s,r))throw new Error("\u6570\u636E\u9A8C\u8BC1\u5931\u8D25: ".concat(s));const g={type:s,data:r,timestamp:Date.now(),version:"2.0",checksum:q(r),metadata:{userAgent:navigator.userAgent,url:window.location.href,sessionId:u(),...f}},b=D(g),V=H(b);await E(s);const T=y(s,0);return localStorage.setItem(T,V),t.value.lastBackupTime=Date.now(),t.value.backupCount++,t.value.hasRecoverableData=!0,console.log("\u2705 \u6570\u636E\u5907\u4EFD\u6210\u529F: ".concat(s)),!0}catch(m){return console.error("\u274C \u6570\u636E\u5907\u4EFD\u5931\u8D25: ".concat(s),m),!1}},B=async(s,r={})=>{try{const{backupIndex:o=0,skipValidation:m=!1,maxAge:f=null}=r,g=y(s,o),b=localStorage.getItem(g);if(!b)return console.warn("\u6CA1\u6709\u627E\u5230\u5907\u4EFD\u6570\u636E: ".concat(s)),null;const V=N(b),T=_(V);if(!T)throw new Error("\u6570\u636E\u89E3\u6790\u5931\u8D25");if(f&&Date.now()-T.timestamp>f)return console.warn("\u5907\u4EFD\u6570\u636E\u5DF2\u8FC7\u671F: ".concat(s)),null;if(!m&&q(T.data)!==T.checksum)throw new Error("\u6570\u636E\u6821\u9A8C\u5931\u8D25");return t.value.recoveryHistory.unshift({type:s,timestamp:Date.now(),backupTimestamp:T.timestamp,success:!0}),t.value.recoveryHistory.length>50&&(t.value.recoveryHistory=t.value.recoveryHistory.slice(0,50)),console.log("\u2705 \u6570\u636E\u6062\u590D\u6210\u529F: ".concat(s)),T.data}catch(o){return console.error("\u274C \u6570\u636E\u6062\u590D\u5931\u8D25: ".concat(s),o),t.value.recoveryHistory.unshift({type:s,timestamp:Date.now(),success:!1,error:o.message}),null}},E=async s=>{try{const r=e.value.maxBackupCount;for(let m=r-1;m>0;m--){const f=y(s,m-1),g=y(s,m),b=localStorage.getItem(f);b&&localStorage.setItem(g,b)}const o=y(s,r);localStorage.removeItem(o)}catch(r){console.warn("\u5907\u4EFD\u8F6E\u8F6C\u5931\u8D25:",r)}},K=(s,r)=>{if(!r||typeof r!="object")return!1;switch(s){case a.CART:return Array.isArray(r.items)&&typeof r.total=="number";case a.ORDER:return r.orderId&&Array.isArray(r.items);case a.MEMBER:return r.memberId||r.memberInfo;case a.PAYMENT:return r.method&&typeof r.amount=="number";case a.SETTINGS:return typeof r=="object";case a.SESSION:return r.sessionId&&r.timestamp;default:return!0}},q=s=>{const r=JSON.stringify(s);let o=0;for(let m=0;m<r.length;m++){const f=r.charCodeAt(m);o=(o<<5)-o+f,o=o&o}return o.toString(36)},u=()=>{let s=sessionStorage.getItem("pos_session_id");return s||(s="session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),sessionStorage.setItem("pos_session_id",s)),s},i=async()=>{const s={};for(const r of Object.values(a)){const o=y(r,0),m=localStorage.getItem(o);if(m)try{const f=N(m),g=_(f);g&&g.timestamp&&(s[r]={timestamp:g.timestamp,age:Date.now()-g.timestamp,size:m.length})}catch(f){console.warn("\u68C0\u67E5\u5907\u4EFD\u6570\u636E\u5931\u8D25: ".concat(r),f)}}return t.value.hasRecoverableData=Object.keys(s).length>0,s},n=s=>{if(!e.value.enableRecoveryPrompt)return;const r=Object.keys(s);if(r.length===0)return;const o={[a.CART]:"\u8D2D\u7269\u8F66",[a.ORDER]:"\u8BA2\u5355",[a.MEMBER]:"\u4F1A\u5458\u4FE1\u606F",[a.PAYMENT]:"\u652F\u4ED8\u4FE1\u606F",[a.SETTINGS]:"\u8BBE\u7F6E",[a.SESSION]:"\u4F1A\u8BDD"},m=r.map(f=>{const g=s[f],b=o[f]||f,V=new Date(g.timestamp).toLocaleString();return"".concat(b," (").concat(V,")")}).join("\\n");ee.confirm({title:"\u53D1\u73B0\u53EF\u6062\u590D\u7684\u6570\u636E",content:"\u68C0\u6D4B\u5230\u4EE5\u4E0B\u6570\u636E\u53EF\u4EE5\u6062\u590D\uFF1A\\n\\n".concat(m,"\\n\\n\u662F\u5426\u8981\u6062\u590D\u8FD9\u4E9B\u6570\u636E\uFF1F"),okText:"\u6062\u590D",cancelText:"\u5FFD\u7565",onOk:async()=>{await S(r)},onCancel:()=>{d.info("\u5DF2\u5FFD\u7565\u6570\u636E\u6062\u590D")}})},S=async s=>{t.value.isRecovering=!0;try{const r=[];for(const f of s){const g=await B(f);r.push({type:f,data:g,success:!!g})}const o=r.filter(f=>f.success).length,m=r.length-o;return o>0&&d.success("\u6210\u529F\u6062\u590D ".concat(o," \u9879\u6570\u636E")),m>0&&d.warning("".concat(m," \u9879\u6570\u636E\u6062\u590D\u5931\u8D25")),r}catch(r){return console.error("\u6279\u91CF\u6062\u590D\u5931\u8D25:",r),d.error("\u6570\u636E\u6062\u590D\u5931\u8D25"),[]}finally{t.value.isRecovering=!1}},x=s=>{!e.value.enableAutoBackup||l||(l=setInterval(async()=>{try{const r=await s();r&&(r.cart&&await I(a.CART,r.cart),r.order&&await I(a.ORDER,r.order),r.member&&await I(a.MEMBER,r.member),r.payment&&await I(a.PAYMENT,r.payment),r.settings&&await I(a.SETTINGS,r.settings))}catch(r){console.warn("\u81EA\u52A8\u5907\u4EFD\u5931\u8D25:",r)}},e.value.backupInterval),console.log("\u{1F504} \u81EA\u52A8\u5907\u4EFD\u5DF2\u542F\u7528"))},h=()=>{l&&(clearInterval(l),l=null,console.log("\u23F9\uFE0F \u81EA\u52A8\u5907\u4EFD\u5DF2\u505C\u6B62"))},M=()=>{if(!e.value.enableCrashDetection)return;const s="pos_heartbeat",r=()=>{localStorage.setItem(s,Date.now().toString())};r(),c=setInterval(r,5e3);const o=localStorage.getItem(s);o&&Date.now()-parseInt(o)>3e4&&(console.warn("\u68C0\u6D4B\u5230\u5F02\u5E38\u9000\u51FA\uFF0C\u68C0\u67E5\u53EF\u6062\u590D\u6570\u636E"),setTimeout(async()=>{const f=await i();Object.keys(f).length>0&&n(f)},1e3)),window.addEventListener("beforeunload",()=>{localStorage.removeItem(s)})},C=(s=null)=>{try{if(s){for(let r=0;r<e.value.maxBackupCount;r++){const o=y(s,r);localStorage.removeItem(o)}console.log("\u{1F9F9} \u5DF2\u6E05\u9664 ".concat(s," \u7C7B\u578B\u7684\u6240\u6709\u5907\u4EFD"))}else{for(const r of Object.values(a))for(let o=0;o<e.value.maxBackupCount;o++){const m=y(r,o);localStorage.removeItem(m)}t.value.hasRecoverableData=!1,t.value.backupCount=0,t.value.lastBackupTime=null,console.log("\u{1F9F9} \u5DF2\u6E05\u9664\u6240\u6709\u5907\u4EFD\u6570\u636E")}}catch(r){console.error("\u6E05\u9664\u5907\u4EFD\u6570\u636E\u5931\u8D25:",r)}},w=()=>{const s={totalBackups:0,totalSize:0,typeStats:{},oldestBackup:null,newestBackup:null};for(const r of Object.values(a)){const o={count:0,size:0,timestamps:[]};for(let m=0;m<e.value.maxBackupCount;m++){const f=y(r,m),g=localStorage.getItem(f);if(g){o.count++,o.size+=g.length;try{const b=N(g),V=_(b);V&&V.timestamp&&o.timestamps.push(V.timestamp)}catch(b){}}}if(o.count>0){s.typeStats[r]=o,s.totalBackups+=o.count,s.totalSize+=o.size;const m=Math.min(...o.timestamps),f=Math.max(...o.timestamps);(!s.oldestBackup||m<s.oldestBackup)&&(s.oldestBackup=m),(!s.newestBackup||f>s.newestBackup)&&(s.newestBackup=f)}}return s},U=(s="json")=>{const r={};for(const o of Object.values(a)){const m=[];for(let f=0;f<e.value.maxBackupCount;f++){const g=y(o,f),b=localStorage.getItem(g);if(b)try{const V=N(b),T=_(V);T&&m.push(T)}catch(V){console.warn("\u5BFC\u51FA\u5907\u4EFD\u6570\u636E\u5931\u8D25: ".concat(g),V)}}m.length>0&&(r[o]=m)}if(s==="csv"){const o=[];o.push("Type,Timestamp,Size,Checksum");for(const[m,f]of Object.entries(r))f.forEach(g=>{const b=JSON.stringify(g.data).length;o.push("".concat(m,",").concat(new Date(g.timestamp).toISOString(),",").concat(b,",").concat(g.checksum))});return o.join("\\n")}return JSON.stringify({exportTime:new Date().toISOString(),backups:r,statistics:w()},null,2)};return ae(async()=>{await i(),M()}),re(()=>{h(),c&&(clearInterval(c),c=null)}),{recoveryState:t,config:e,canRecover:p,backupStatus:A,backupTypes:a,saveData:I,restoreData:B,checkRecoverableData:i,showRecoveryPrompt:n,performBatchRecovery:S,setupAutoBackup:x,stopAutoBackup:h,clearAllBackups:C,getBackupStatistics:w,exportBackupData:U,saveCartState:s=>I(a.CART,s),restoreCartState:()=>B(a.CART),clearBackup:()=>C(a.CART)}}function he(){const v=te();ye();const e=j(()=>v.cartItems||[]),t=j(()=>{try{const u=W.calculateTotal(e.value),i=v.discountAmount||0,n=Math.max(0,u-i);return{totalAmount:Number(u.toFixed(2)),discountAmount:Number(i.toFixed(2)),finalAmount:Number(n.toFixed(2)),itemCount:W.calculateItemCount(e.value),totalQuantity:W.calculateTotalQuantity(e.value)}}catch(u){return console.error("\u8BA1\u7B97\u8D2D\u7269\u8F66\u603B\u91D1\u989D\u5931\u8D25:",u),{totalAmount:0,discountAmount:0,finalAmount:0,itemCount:0,totalQuantity:0}}}),a=j(()=>e.value.length===0),l=j(()=>!a.value&&t.value.finalAmount>0),c=j(()=>({itemCount:t.value.itemCount,totalQuantity:t.value.totalQuantity,totalAmount:t.value.totalAmount,discountAmount:t.value.discountAmount,finalAmount:t.value.finalAmount,averagePrice:e.value.length>0?W.calculateAveragePrice(e.value):0})),p=G(!1),A=async(u,i=1)=>{try{p.value=!0;const n={...u,id:String(u.id||""),name:u.name||u.productName||"\u672A\u77E5\u5546\u54C1"},S=z.validateProduct(n);if(!S.isValid)return d.error(S.message),!1;const x=z.validateQuantity(i);if(!x.isValid)return d.error(x.message),!1;if(e.value.length>=O.MAX_CART_ITEMS)return d.error("\u8D2D\u7269\u8F66\u5546\u54C1\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7".concat(O.MAX_CART_ITEMS,"\u4EF6")),!1;const h=await $.checkInventory(n.id,i);if(!h.available)return d.error('\u5546\u54C1"'.concat(n.name,'"\u5E93\u5B58\u4E0D\u8DB3\uFF0C\u5F53\u524D\u5E93\u5B58\uFF1A').concat(h.stock)),!1;const M=e.value.findIndex(C=>C.id===n.id);if(M!==-1){const C=e.value[M],w=C.quantity+i,U=await $.checkInventory(n.id,w);return U.available?await y(n.id,w):(d.error('\u5546\u54C1"'.concat(n.name,'"\u5E93\u5B58\u4E0D\u8DB3\uFF0C\u5F53\u524D\u5E93\u5B58\uFF1A').concat(U.stock,"\uFF0C\u5DF2\u6DFB\u52A0\uFF1A").concat(C.quantity)),!1)}else{const C={id:n.id,name:n.name,barcode:n.barcode||"",price:n.price,quantity:i,subtotal:W.calculateSubtotal(n.price,i),unit:n.unit||"\u4EF6",categoryId:n.categoryId||"",categoryName:n.categoryName||"",specifications:n.specifications||"",image:n.image||"",pricingType:n.pricingType||"NORMAL",addedAt:new Date().toISOString()},w=z.validateCartItem(C);return w.isValid?(v.addCartItem(C),await E(),d.success('\u5DF2\u6DFB\u52A0"'.concat(n.name,'"\u5230\u8D2D\u7269\u8F66')),!0):(d.error(w.message),!1)}}catch(n){return console.error("\u6DFB\u52A0\u5546\u54C1\u5230\u8D2D\u7269\u8F66\u5931\u8D25:",n),d.error("\u6DFB\u52A0\u5546\u54C1\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{p.value=!1}},y=async(u,i)=>{try{p.value=!0;const n=String(u||""),S=z.validateQuantity(i);if(!S.isValid)return d.error(S.message),!1;const x=e.value.find(w=>w.id===n);if(!x)return d.error("\u5546\u54C1\u4E0D\u5B58\u5728"),!1;if(i===0)return await D(n);const h=await $.checkInventory(n,i);if(!h.available)return d.error('\u5546\u54C1"'.concat(x.name,'"\u5E93\u5B58\u4E0D\u8DB3\uFF0C\u5F53\u524D\u5E93\u5B58\uFF1A').concat(h.stock)),!1;const M={...x,quantity:i,subtotal:W.calculateSubtotal(x.price,i),updatedAt:new Date().toISOString()},C=z.validateCartItem(M);return C.isValid?(v.updateCartItem(n,M),await E(),!0):(d.error(C.message),!1)}catch(n){return console.error("\u66F4\u65B0\u5546\u54C1\u6570\u91CF\u5931\u8D25:",n),d.error("\u66F4\u65B0\u6570\u91CF\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{p.value=!1}},D=async u=>{try{const i=String(u||""),n=e.value.find(S=>S.id===i);return n?(v.removeCartItem(i),await E(),d.success('\u5DF2\u79FB\u9664"'.concat(n.name,'"')),!0):(d.error("\u5546\u54C1\u4E0D\u5B58\u5728"),!1)}catch(i){return console.error("\u79FB\u9664\u5546\u54C1\u5931\u8D25:",i),d.error("\u79FB\u9664\u5546\u54C1\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}},_=async()=>{try{return a.value?(d.info("\u8D2D\u7269\u8F66\u5DF2\u7ECF\u662F\u7A7A\u7684"),!0):(v.clearCart(),await E(),d.success("\u8D2D\u7269\u8F66\u5DF2\u6E05\u7A7A"),!0)}catch(u){return console.error("\u6E05\u7A7A\u8D2D\u7269\u8F66\u5931\u8D25:",u),d.error("\u6E05\u7A7A\u8D2D\u7269\u8F66\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}},H=async u=>{const i={success:0,failed:0,errors:[]};try{p.value=!0;const n=u.map(({product:h,quantity:M})=>({productId:String(h.id||""),quantity:M||1})),S=await $.batchCheckInventory(n),x=new Map(S.map(h=>[String(h.productId),h]));for(const{product:h,quantity:M=1}of u)try{const C=String(h.id||""),w=x.get(C);if(!w||!w.available){const s=h.name||h.productName||"\u672A\u77E5\u5546\u54C1";i.failed++,i.errors.push('\u5546\u54C1"'.concat(s,'"\u5E93\u5B58\u4E0D\u8DB3'));continue}if(await A(h,M))i.success++;else{const s=h.name||h.productName||"\u672A\u77E5\u5546\u54C1";i.failed++,i.errors.push('\u6DFB\u52A0\u5546\u54C1"'.concat(s,'"\u5931\u8D25'))}}catch(C){const w=h.name||h.productName||"\u672A\u77E5\u5546\u54C1";i.failed++,i.errors.push('\u6DFB\u52A0\u5546\u54C1"'.concat(w,'"\u5931\u8D25: ').concat(C.message))}return i.success>0&&d.success("\u6210\u529F\u6DFB\u52A0".concat(i.success,"\u4EF6\u5546\u54C1")),i.failed>0&&d.warning("".concat(i.failed,"\u4EF6\u5546\u54C1\u6DFB\u52A0\u5931\u8D25")),i}catch(n){return console.error("\u6279\u91CF\u6DFB\u52A0\u5546\u54C1\u5931\u8D25:",n),d.error("\u6279\u91CF\u6DFB\u52A0\u5546\u54C1\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),{success:0,failed:u.length,errors:[n.message]}}finally{p.value=!1}},N=async()=>{try{const u=z.validateCart(e.value);return u.isValid?await $.validateCart({items:e.value}):u}catch(u){return console.error("\u9A8C\u8BC1\u8D2D\u7269\u8F66\u5931\u8D25:",u),{isValid:!1,message:"\u9A8C\u8BC1\u8D2D\u7269\u8F66\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"}}},I=async(u,i=null)=>{try{if(p.value=!0,a.value)return d.error("\u8D2D\u7269\u8F66\u4E3A\u7A7A\uFF0C\u65E0\u6CD5\u4F7F\u7528\u4F18\u60E0\u5238"),!1;const n=await $.applyCoupon({items:e.value,couponCode:u,member:i});return n.success?(v.updateDiscountAmount(n.discountAmount),d.success("\u4F18\u60E0\u5238\u5E94\u7528\u6210\u529F\uFF0C\u4F18\u60E0".concat(n.discountAmount,"\u5143")),!0):(d.error(n.message||"\u4F18\u60E0\u5238\u5E94\u7528\u5931\u8D25"),!1)}catch(n){return console.error("\u5E94\u7528\u4F18\u60E0\u5238\u5931\u8D25:",n),d.error("\u5E94\u7528\u4F18\u60E0\u5238\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{p.value=!1}},B=async u=>{try{p.value=!0;const i=await $.removeCoupon({items:e.value,couponId:u});if(i.success){const n=Math.max(0,v.discountAmount-i.removedDiscount);return v.updateDiscountAmount(n),d.success("\u4F18\u60E0\u5238\u5DF2\u79FB\u9664"),!0}else return d.error("\u79FB\u9664\u4F18\u60E0\u5238\u5931\u8D25"),!1}catch(i){return console.error("\u79FB\u9664\u4F18\u60E0\u5238\u5931\u8D25:",i),d.error("\u79FB\u9664\u4F18\u60E0\u5238\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),!1}finally{p.value=!1}},E=async()=>(console.log("\u6570\u636E\u5907\u4EFD\u529F\u80FD\u5DF2\u7981\u7528"),!0);return{items:e,cartTotal:t,cartSummary:c,isEmpty:a,canCheckout:l,loading:p,addItem:A,updateQuantity:y,removeItem:D,clearCart:_,batchAddItems:H,validateCart:N,applyCoupon:I,removeCoupon:B,addItemByBarcode:async(u,i=1)=>{try{if(p.value=!0,!u||u.trim()==="")return d.error("\u8BF7\u8F93\u5165\u5546\u54C1\u6761\u7801"),!1;const n=await $.getProductByBarcode(u.trim());return n?await A(n,i):(d.error("\u672A\u627E\u5230\u5BF9\u5E94\u7684\u5546\u54C1"),!1)}catch(n){return console.error("\u6839\u636E\u6761\u7801\u6DFB\u52A0\u5546\u54C1\u5931\u8D25:",n),d.error("\u5546\u54C1\u6761\u7801\u65E0\u6548\u6216\u5546\u54C1\u4E0D\u5B58\u5728"),!1}finally{p.value=!1}},saveCartBackup:E,restoreCartBackup:async()=>(console.log("\u6570\u636E\u6062\u590D\u529F\u80FD\u5DF2\u7981\u7528"),!1)}}const ve={class:"shopping-cart"},Ce={class:"cart-content"},be={key:0,class:"cart-items"},Se={key:1,class:"empty-cart"},we=Object.assign({name:"ShoppingCart"},{__name:"ShoppingCart",props:{currentMember:{type:Object,default:null}},emits:["checkout","suspend-order","item-change"],setup(v,{emit:e}){const t=v,a=e;te();const{items:l,cartTotal:c,cartSummary:p,isEmpty:A,canCheckout:y,loading:D,updateQuantity:_,removeItem:H,clearCart:N}=he(),I=async(u,i)=>{await _(u,i)&&a("item-change",{type:"update",itemId:u,quantity:i})},B=async u=>{await H(u)&&a("item-change",{type:"remove",itemId:u})},E=()=>{ee.confirm({title:"\u786E\u8BA4\u6E05\u7A7A\u8D2D\u7269\u8F66",content:"\u786E\u5B9A\u8981\u6E05\u7A7A\u8D2D\u7269\u8F66\u4E2D\u7684\u6240\u6709\u5546\u54C1\u5417\uFF1F",okText:"\u786E\u8BA4\u6E05\u7A7A",cancelText:"\u53D6\u6D88",okType:"danger",onOk:async()=>{await N()&&a("item-change",{type:"clear"})}})},K=()=>{y.value&&a("checkout",{items:l.value,total:c.value,member:t.currentMember})},q=()=>{A.value||a("suspend-order",{items:l.value,total:c.value,member:t.currentMember})};return(u,i)=>{const n=Z;return P(),F("div",ve,[R(A)?J("",!0):(P(),Y(de,{key:0,"item-count":R(p).itemCount,"total-quantity":R(p).totalQuantity,onClearCart:E},null,8,["item-count","total-quantity"])),X("div",Ce,[R(A)?(P(),F("div",Se,[ie(n,{description:"\u8D2D\u7269\u8F66\u4E3A\u7A7A",image:R(Z).PRESENTED_IMAGE_SIMPLE},{default:ce(()=>i[0]||(i[0]=[X("p",{class:"empty-tip"},"\u8BF7\u9009\u62E9\u5546\u54C1\u6DFB\u52A0\u5230\u8D2D\u7269\u8F66",-1)])),_:1,__:[0]},8,["image"])])):(P(),F("div",be,[(P(!0),F(ne,null,oe(R(l),S=>(P(),Y(fe,{key:S.id,item:S,loading:R(D),onUpdateQuantity:I,onRemoveItem:B},null,8,["item","loading"]))),128))]))]),R(A)?J("",!0):(P(),Y(ge,{key:1,"cart-total":R(c),member:v.currentMember,"can-checkout":R(y),loading:R(D),onCheckout:K,onSuspendOrder:q},null,8,["cart-total","member","can-checkout","loading"]))])}}}),ke=se(we,[["__scopeId","data-v-9dcb7d34"]]),Ne=Object.freeze(Object.defineProperty({__proto__:null,default:ke},Symbol.toStringTag,{value:"Module"}));export{Oe as C,_e as P,ke as S,Ne as a,he as u};
