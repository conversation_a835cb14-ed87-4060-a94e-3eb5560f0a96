package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpPosExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;

/**
 * 订单相关异常
 *
 * <AUTHOR>
 * @since 2025/08/01 20:15
 */
public class OrderException extends PosException {

    public OrderException(AbstractExceptionEnum exception) {
        super(exception);
    }

    public OrderException(AbstractExceptionEnum exception, Object... params) {
        super(exception, params);
    }

    /**
     * 订单不存在异常
     */
    public static OrderException notFound(Long orderId) {
        return new OrderException(ErpPosExceptionEnum.ORDER_NOT_EXIST, orderId);
    }

    /**
     * 订单已支付异常
     */
    public static OrderException alreadyPaid(String orderNo) {
        return new OrderException(ErpPosExceptionEnum.ORDER_ALREADY_PAID, orderNo);
    }

    /**
     * 订单状态错误异常
     */
    public static OrderException statusError(String orderNo, String currentStatus) {
        return new OrderException(ErpPosExceptionEnum.ORDER_STATUS_ERROR, orderNo, currentStatus);
    }

    /**
     * 订单金额错误异常
     */
    public static OrderException amountError(String orderNo, String expectedAmount, String actualAmount) {
        return new OrderException(ErpPosExceptionEnum.ORDER_AMOUNT_ERROR, orderNo, expectedAmount, actualAmount);
    }
}
