package cn.stylefeng.roses.kernel.erp.modular.customer.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpCustomerRegion;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 客户-区域关联Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
public interface ErpCustomerRegionMapper extends BaseMapper<ErpCustomerRegion> {

    /**
     * 根据客户ID查询关联的区域ID列表
     *
     * @param customerId 客户ID
     * @return 区域ID列表
     */
    @Select("SELECT region_id FROM erp_customer_region WHERE customer_id = #{customerId}")
    List<Long> getRegionIdsByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据区域ID查询关联的客户
     *
     * @param page 分页参数
     * @param regionId 区域ID
     * @param includeChildRegions 是否包含子区域
     * @param childRegionIds 子区域ID列表
     * @return 客户列表
     */
    List<ErpCustomerResponse> getCustomersByRegionId(Page<ErpCustomerResponse> page,
                                                    @Param("regionId") Long regionId,
                                                    @Param("includeChildRegions") Boolean includeChildRegions,
                                                    @Param("childRegionIds") List<Long> childRegionIds);

    /**
     * 统计区域关联的客户数量
     *
     * @param regionId 区域ID
     * @param includeChildRegions 是否包含子区域
     * @param childRegionIds 子区域ID列表
     * @return 客户数量
     */
    Long countCustomersByRegionId(@Param("regionId") Long regionId,
                                 @Param("includeChildRegions") Boolean includeChildRegions,
                                 @Param("childRegionIds") List<Long> childRegionIds);
}