System.register(["./UniversalTree-legacy-6dcdf778.js","./regionApi-legacy-73888494.js","./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var a,l,o,d,n,r,i;return{setters:[e=>{a=e.U},e=>{l=e.R},e=>{o=e._,d=e.r,n=e.L,r=e.a,i=e.f}],execute:function(){const t=Object.assign({name:"RegionTree"},{__name:"index",props:{showBadge:{type:Boolean,default:!1},isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["tree-select","tree-data-loaded"],setup(e,{expose:t,emit:o}){const s=e,c=o,u=d(),h={api:l.findTree,lazyLoadApi:l.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},v={key:"regionId",title:"regionName",children:"children",hasChildren:"hasChildren",level:"regionLevel"},p=n((()=>({title:"区域筛选",showHeader:s.isSetWidth,showSearch:!0,searchPlaceholder:"请输入区域名称搜索",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:s.isSetWidth}))),g={selectable:!0,expandable:!0,lazyLoad:!0,defaultExpandLevel:3,allowMultiSelect:!1},y={allowAdd:!1,allowEdit:!1,allowDelete:!1},f=e=>{const{keys:t,nodes:a}=e;c("tree-select",t,{selectedNodes:a})},S=e=>{},w=e=>{},m=e=>{c("tree-data-loaded",e)},x=e=>{console.error("区域树数据加载失败:",e)};return t({reload:()=>{var e;null===(e=u.value)||void 0===e||e.reload()},getSelectedNodes:()=>{var e;return null===(e=u.value)||void 0===e?void 0:e.getSelectedNodes()},setSelectedKeys:e=>{var t;null===(t=u.value)||void 0===t||t.setSelectedKeys(e)},selectedKeys:n((()=>{var e;return(null===(e=u.value)||void 0===e?void 0:e.getSelectedNodes())||[]}))}),(e,t)=>(r(),i(a,{ref_key:"universalTreeRef",ref:u,"data-source":h,"field-mapping":v,"display-config":p.value,"interaction-config":g,"action-config":y,onSelect:f,onExpand:S,onSearch:w,onLoad:m,onLoadError:x},null,8,["display-config"]))}});e("_",o(t,[["__scopeId","data-v-7c17cb78"]]))}}}));
