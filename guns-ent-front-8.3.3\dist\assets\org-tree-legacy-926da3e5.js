System.register(["./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js"],(function(e,t){"use strict";var a,d,n,r,i,o,l,s,c,p,h,v,g,b,u,x,f,y,m,w,k,C,S,I,O;return{setters:[e=>{a=e._,d=e.r,n=e.o,r=e.k,i=e.a,o=e.c,l=e.b,s=e.f,c=e.h,p=e.d,h=e.w,v=e.I,g=e.aR,b=e.t,u=e.aM,x=e.aS,f=e.M,y=e.E,m=e.m,w=e.l,k=e.n,C=e.bg,S=e.a5,I=e.S},null,e=>{O=e.O}],execute:function(){var t=document.createElement("style");t.textContent=".left-header[data-v-2b14d4a7]{height:30px;line-height:30px;display:flex;justify-content:space-between;align-items:center;color:#505050;font-size:14px;font-weight:400;margin-bottom:16px}.left-header .left-header-title[data-v-2b14d4a7]{color:#60666b;font-size:14px;font-weight:400}.left-header .header-add[data-v-2b14d4a7]{font-size:14px;cursor:pointer;padding:5px}.left-header .header-add[data-v-2b14d4a7]:hover{background:#e9f3f8}.search[data-v-2b14d4a7]{height:36px;border-radius:5px;margin-bottom:16px}.search-input[data-v-2b14d4a7]{border-radius:4px}.tree-content[data-v-2b14d4a7]{width:100%;height:calc(100% - 90px);overflow:hidden}[data-v-2b14d4a7] .ant-spin-container{height:100%}.left-tree[data-v-2b14d4a7]{height:calc(100% - 10px)!important;overflow-y:auto!important;overflow-x:hidden!important}[data-v-2b14d4a7]::-webkit-scrollbar{width:12px!important}.tree-edit[data-v-2b14d4a7],.not-tree-edit[data-v-2b14d4a7]{width:100%;display:inline-block;position:relative}.tree-edit .edit-title[data-v-2b14d4a7],.not-tree-edit .edit-title[data-v-2b14d4a7]{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tree-edit .edit-icon[data-v-2b14d4a7],.not-tree-edit .edit-icon[data-v-2b14d4a7]{display:none;width:40px;position:absolute;right:10px}.tree-edit:hover .edit-icon[data-v-2b14d4a7],.not-tree-edit:hover .edit-icon[data-v-2b14d4a7]{display:inline-block}.tree-edit:hover .edit-title[data-v-2b14d4a7],.not-tree-edit:hover .edit-title[data-v-2b14d4a7]{width:calc(100% - 50px)}.not-tree-edit:hover .edit-title[data-v-2b14d4a7]{width:100%}[data-v-2b14d4a7] .ant-tree .ant-tree-node-content-wrapper{height:38px!important;line-height:38px!important;display:inherit!important}[data-v-2b14d4a7] .ant-tree-switcher{line-height:38px!important}[data-v-2b14d4a7] .ant-tree-switcher .ant-tree-switcher-icon{font-size:14px!important}[data-v-2b14d4a7] .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle{height:38px!important;line-height:38px!important;margin-right:8px}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before{border-radius:4px;background:rgba(207,221,247,.35)!important}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{color:#0f56d7;font-weight:500}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tree-iconEle .iconfont{color:#0f56d7!important}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before{background:rgba(207,221,247,.35)!important;border-radius:4px}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{color:#000;font-weight:500}[data-v-2b14d4a7] .ant-tree-treenode:not(:last-child){margin-bottom:8px}[data-v-2b14d4a7] .ant-tree-indent-unit{width:10px!important}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode:before{bottom:0!important}[data-v-2b14d4a7] .ant-tree .ant-tree-treenode{padding:0 12px}[data-v-2b14d4a7] .guns-table-tool .guns-tool{display:none}.img[data-v-2b14d4a7]{width:24px;height:22px;margin-top:-4px}.svg-img[data-v-2b14d4a7]{width:24px;height:22px;margin-top:8px}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode{height:38px!important;line-height:38px!important}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button{display:inline;display:flex;top:0}[data-v-2b14d4a7] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button-first{display:inline;display:flex;top:0;margin-right:150px}[data-v-2b14d4a7] .ant-tree-node-content-wrapper{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 0 0 4px}[data-v-2b14d4a7] .ant-tree-title{width:calc(100% - 32px)}.empty[data-v-2b14d4a7]{margin-top:50%}[data-v-2b14d4a7] .ant-card-body,[data-v-2b14d4a7] .ant-spin-nested-loading,[data-v-2b14d4a7] .ant-spin-container{height:100%}[data-v-2b14d4a7] .ant-tree{background-color:#fff!important}.edit-icon[data-v-2b14d4a7]{width:60px!important}.tree-edit:hover .edit-title[data-v-2b14d4a7]{width:calc(100% - 70px)!important}\n",document.head.appendChild(t);const K={class:"box bgColor box-shadow"},E={key:0,class:"left-header"},_={class:"search"},L={class:"tree-content"},T={class:"left-tree"},j={key:0,class:"tree-edit"},z=["title"],N={class:"edit-icon"},U=["title"],P={__name:"org-tree",props:{isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["treeSelect","treeData","addOrg","editOrg","deleteOrg"],setup(e,{expose:t,emit:a}){const P=e,R=a,B=d(""),D=d(!1),M=d([]),W=d([]),A=d([]),F=d([]);n((()=>{q()}));const q=()=>{D.value=!0,O.tree({searchText:B.value}).then((e=>{B.value&&(A.value=e.data.expandOrgIdList);const t=X(e.data.orgTreeList);M.value=t})).finally((()=>D.value=!1))},G=(e,t)=>{R("treeSelect",e,t)},H=()=>{F.value=[],B.value||q()},J=e=>{R("addOrg",e)},Q=e=>(F.value.push(e.eventKey),new Promise((t=>{O.tree({orgParentId:e.dataRef.orgId}).then((t=>{const a=X(t.data.orgTreeList);e.dataRef.children=a,M.value=[...M.value]})).finally((()=>{t()}))}))),V=()=>{D.value=!0,O.tree({searchText:B.value,indexOrgIdList:A.value}).then((e=>{const t=X(e.data.orgTreeList);M.value=[...t]})).finally((()=>D.value=!1))},X=e=>(e&&e.length>0?e.forEach((e=>{e.haveSubOrgFlag?e.isLeaf=!1:e.isLeaf=!0,e.children&&e.children.length>0&&(e.children=X(e.children))})):e=[],e);return t({currentSelectKeys:W,reloadOrgTreeData:V}),(t,a)=>{const d=r("plus-outlined"),n=w,X=k,Y=C,Z=S,$=I;return i(),o("div",K,[P.isSetWidth?(i(),o("div",E,[a[5]||(a[5]=l("span",{class:"left-header-title"},"所属组织",-1)),l("span",null,[e.isShowEditIcon?(i(),s(d,{key:0,class:"header-add",onClick:a[0]||(a[0]=e=>J())})):c("",!0)])])):c("",!0),l("div",_,[p(n,{value:B.value,"onUpdate:value":a[1]||(a[1]=e=>B.value=e),placeholder:"请输入机构名称，回车搜索","allow-clear":"",onPressEnter:q,onChange:H},{prefix:h((()=>[p(v,{iconClass:"icon-opt-search"})])),_:1},8,["value"])]),l("div",L,[p($,{tip:"Loading...",spinning:D.value,delay:100},{default:h((()=>[g(l("div",T,[p(Y,{"show-icon":!0,selectedKeys:W.value,"onUpdate:selectedKeys":a[2]||(a[2]=e=>W.value=e),expandedKeys:A.value,"onUpdate:expandedKeys":a[3]||(a[3]=e=>A.value=e),loadedKeys:F.value,"onUpdate:loadedKeys":a[4]||(a[4]=e=>F.value=e),onSelect:G,"load-data":Q,"tree-data":M.value,fieldNames:{children:"children",title:"orgName",key:"orgId",value:"orgId"}},{icon:h((e=>[1==e.orgType?(i(),s(v,{key:0,"icon-class":"icon-nav-gongsi",color:"#43505e",fontSize:"24px"})):c("",!0),2==e.orgType?(i(),s(v,{key:1,"icon-class":"icon-tree-dept",color:"#43505e",fontSize:"24px"})):c("",!0)])),title:h((t=>[e.isShowEditIcon?(i(),o("span",j,[l("span",{class:"edit-title",title:t.orgName},b(t.orgName),9,z),l("span",N,[p(X,null,{default:h((()=>[p(v,{iconClass:"icon-opt-tianjia",color:"var(--primary-color)",onClick:u((e=>J(t)),["stop"])},null,8,["onClick"]),p(v,{iconClass:"icon-opt-bianji",color:"var(--primary-color)",onClick:u((e=>(e=>{R("editOrg",e)})(t)),["stop"])},null,8,["onClick"]),p(v,{iconClass:"icon-opt-shanchu",color:"red",onClick:u((e=>(e=>{f.confirm({title:"提示",content:"确定要删除吗?",icon:p(y),maskClosable:!0,onOk:()=>{D.value=!0,O.delete({orgId:e.orgId}).then((t=>{m.success(t.message),V(),R("deleteOrg",e)})).finally((()=>D.value=!1))}})})(t)),["stop"])},null,8,["onClick"])])),_:2},1024)])])):(i(),o("span",{key:1,class:"tree-title",title:t.orgName},b(t.orgName),9,U))])),_:1},8,["selectedKeys","expandedKeys","loadedKeys","tree-data"])],512),[[x,M.value&&M.value.length>0]]),g(p(Z,{class:"empty"},null,512),[[x,M.value&&0==M.value.length]])])),_:1},8,["spinning"])])])}}};e("default",a(P,[["__scopeId","data-v-2b14d4a7"]]))}}}));
