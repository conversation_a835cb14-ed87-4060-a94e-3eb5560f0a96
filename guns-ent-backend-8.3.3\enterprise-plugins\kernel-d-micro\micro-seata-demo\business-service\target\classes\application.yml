server:
  port: 18081
  max-http-header-size: 10240

spring:
  application:
    name: business-service
  profiles:
    active: local
  servlet:
    multipart:
      max-request-size: 200MB
      max-file-size: 200MB
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    locale: zh_CN
    serialization:
      indent_output: false
  flyway:
    enabled: true
    locations: classpath:db/migration/mysql
    # 当迁移时发现目标schema非空，而且带有没有元数据的表时，是否自动执行基准迁移
    baseline-on-migrate: true
    # 是否允许无序的迁移 开发环境最好开启, 生产环境关闭
    out-of-order: true
    # 关闭占位符替换，因为插入的sql里边可能包含${}关键字
    placeholder-replacement: false
  main:
    allow-circular-references: true

# 资源扫描
scanner:
  open: false

# 日志记录
sys-log:
  # db-数据库，file-文件
  type: db
  file-save-path: _sys_logs

# feign远程调用配置
feign:
  sentinel:
    enabled: true
  client:
    config:
      # 全局配置
      default:
        # NONE不记录任何日志--BASIC仅请求方法URL,状态码执行时间等--HEADERS在BASIC基础上记录header等--FULL记录所有
        loggerLevel: full
        connectTimeout: 500  #连接超时时间
        readTimeout: 5000    #连接超时时间
        errorDecoder: cn.stylefeng.roses.kernel.micro.core.feign.GunsFeignErrorDecoder
        requestInterceptors:
          - cn.stylefeng.roses.kernel.micro.core.feign.GunsFeignHeaderProcessInterceptor
  httpclient:
    # 让feign使用apache httpclient做请求；而不是默认的urlConnection
    enabled: true
    # feign的最大连接数
    max-connections: 200
    # feign单个路径的最大连接数
    max-connections-per-route: 50