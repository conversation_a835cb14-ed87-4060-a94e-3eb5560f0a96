<template>
  <universal-tree
    ref="universalTreeRef"
    :data-source="dataSourceConfig"
    :field-mapping="fieldMappingConfig"
    :display-config="displayConfig"
    :interaction-config="interactionConfig"
    :action-config="actionConfig"
    @select="handleSelect"
    @expand="handleExpand"
    @search="handleSearch"
    @load="handleLoad"
    @loadError="handleLoadError"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import UniversalTree from '@/components/UniversalTree/UniversalTree.vue'
import { ProductCategoryApi } from '@/views/erp/productCategory/api/productCategoryApi'

// 定义组件名称
defineOptions({
  name: 'CategoryTree'
})

// 定义Props
const props = defineProps({
  lazyLoad: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['select'])

// 组件引用
const universalTreeRef = ref()

// 数据源配置
const dataSourceConfig = {
  api: ProductCategoryApi.findTree,
  lazyLoadApi: ProductCategoryApi.findTreeWithLazy,
  searchParam: 'searchText',
  parentIdParam: 'parentId'
}

// 字段映射配置
const fieldMappingConfig = {
  key: 'categoryId',
  title: 'categoryName',
  children: 'children',
  hasChildren: 'hasChildren',
  level: 'categoryLevel'
}

// 显示配置（只读模式，用于商品管理页面）
const displayConfig = {
  title: '产品分类',
  showHeader: false,
  showSearch: true,
  searchPlaceholder: '请输入分类名称搜索',
  showAddButton: false,
  showEditIcons: false,
  showIcon: false,
  isSetWidth: true
}

// 交互配置
const interactionConfig = computed(() => ({
  selectable: true,
  expandable: true,
  lazyLoad: props.lazyLoad,
  defaultExpandLevel: 2,
  allowMultiSelect: false
}))

// 操作配置（只读模式）
const actionConfig = {
  allowAdd: false,
  allowEdit: false,
  allowDelete: false
}

// 事件处理方法
const handleSelect = (selectedKeys, selectedNodes) => {
  if (selectedKeys.length > 0) {
    const selectedNode = selectedNodes[0]
    emit('select', selectedKeys[0], selectedNode.categoryName)
  } else {
    // 如果取消选择，则显示全部商品
    emit('select', null)
  }
}

const handleExpand = (expandedKeys) => {
  // 可以在这里处理展开事件，如果需要的话
}

const handleSearch = (searchText) => {
  // 搜索事件已经在UniversalTree内部处理
}

const handleLoad = (data) => {
  // 数据加载成功事件
}

const handleLoadError = (error) => {
  console.error('产品分类树数据加载失败:', error)
}

// 暴露给父组件的方法
const reload = () => {
  universalTreeRef.value?.reload()
}

const getSelectedNodes = () => {
  return universalTreeRef.value?.getSelectedNodes()
}

const setSelectedKeys = (keys) => {
  universalTreeRef.value?.setSelectedKeys(keys)
}

// 为了向后兼容，保留原有的属性名
const selectedKeys = computed(() => universalTreeRef.value?.getSelectedNodes() || [])

// 暴露方法给父组件
defineExpose({
  reload,
  getSelectedNodes,
  setSelectedKeys,
  selectedKeys
})
</script>

<style scoped lang="less">
// 样式已经在UniversalTree组件中统一处理
</style>