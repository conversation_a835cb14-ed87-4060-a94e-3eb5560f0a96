System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./index-legacy-c65a6a4e.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./InventoryHistoryApi-legacy-e1cc044f.js"],(function(e,t){"use strict";var a,o,r,n,i,l,s,d,c,p,u,y,g,f,m,h,k,v,T,C,S,w,_,b,N,x,F,U,z,A,O,R,H;return{setters:[e=>{a=e._,o=e.r,r=e.s,n=e.X,i=e.m,l=e.a,s=e.f,d=e.w,c=e.b,p=e.d,u=e.t,y=e.g,g=e.a2,f=e.c,m=e.F,h=e.e,k=e.h,v=e.Y,T=e.Z,C=e.a0,S=e.W,w=e.J,_=e.u,b=e.a6,N=e.l,x=e.B,F=e.n,U=e.H,z=e.U,A=e.a7,O=e.i,R=e.M},null,null,null,null,null,null,null,e=>{H=e.I}],execute:function(){var t=document.createElement("style");t.textContent=".inventory-history-content[data-v-49676f49]{max-height:70vh;overflow-y:auto}.product-name[data-v-49676f49]{font-weight:500;color:#1890ff}.record-count[data-v-49676f49]{color:#8c8c8c;font-size:12px}.stock-normal[data-v-49676f49]{color:#52c41a;font-weight:500}.stock-warning[data-v-49676f49]{color:#faad14;font-weight:500}.stock-danger[data-v-49676f49]{color:#ff4d4f;font-weight:500}.quantity-increase[data-v-49676f49]{color:#52c41a;font-weight:500}.quantity-decrease[data-v-49676f49]{color:#ff4d4f;font-weight:500}.value-increase[data-v-49676f49]{color:#52c41a;font-weight:500}.value-decrease[data-v-49676f49]{color:#ff4d4f;font-weight:500}\n",document.head.appendChild(t);const I={name:"InventoryHistory",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{emit:t}){const a=o(!1),l=o([]),s=o([]),d=r({productId:null,operationType:void 0,operatorName:"",startTime:void 0,endTime:void 0}),c=r({current:1,pageSize:20,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}),p=H.getOperationTypeOptions();n((()=>e.visible),(t=>{t&&e.data.productId&&(d.productId=e.data.productId,y(),u())}));const u=async()=>{a.value=!0;try{const e={...d,pageNo:c.current,pageSize:c.pageSize},t=await H.findPage(e);if(t&&!1!==t.success){let e=[],a=0;t.rows?(e=t.rows,a=t.totalRows||0):t.data&&t.data.rows?(e=t.data.rows,a=t.data.totalRows||0):t.data&&t.data.records?(e=t.data.records,a=t.data.total||0):Array.isArray(t.data)?(e=t.data,a=e.length):Array.isArray(t)&&(e=t,a=e.length),l.value=e.map((e=>({...e,quantity:e.quantityChange,beforeStock:e.stockBefore,afterStock:e.stockAfter,operatorName:e.operationUserName||e.operatorName||(e.operationUser?`操作员${e.operationUser}`:"系统操作"),operationTime:g(e.operationTime)}))),c.total=a,t.pageNo?(c.current=t.pageNo,c.pageSize=t.pageSize):t.data&&t.data.pageNo&&(c.current=t.data.pageNo,c.pageSize=t.data.pageSize)}else l.value=[],c.total=0}catch(e){i.error("加载库存历史失败："+(e.message||"未知错误")),l.value=[],c.total=0}finally{a.value=!1}},y=()=>{d.operationType=void 0,d.operatorName="",d.startTime=void 0,d.endTime=void 0,s.value=[],c.current=1},g=e=>{if(!e)return"-";const t="string"==typeof e?new Date(e):e;return isNaN(t.getTime())?e:`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}:${String(t.getSeconds()).padStart(2,"0")}`};return{loading:a,historyList:l,dateRange:s,searchForm:d,pagination:c,operationTypeOptions:p,columns:[{title:"操作类型",key:"operationType",width:100,fixed:"left"},{title:"数量变化",key:"quantity",width:120,align:"right"},{title:"操作前库存",key:"beforeStock",width:120,align:"right"},{title:"操作后库存",key:"afterStock",width:120,align:"right"},{title:"关联单据",key:"referenceType",width:120},{title:"操作时间",key:"operationTime",width:160},{title:"操作人",key:"operatorName",width:100},{title:"备注",key:"remark",width:200,ellipsis:!0}],loadHistory:u,handleTableChange:e=>{c.current=e.current,c.pageSize=e.pageSize,u()},onDateRangeChange:e=>{e&&2===e.length?(d.startTime=e[0],d.endTime=e[1]):(d.startTime=void 0,d.endTime=void 0),u()},resetSearch:y,exportHistory:()=>{try{H.exportHistory({...d,productId:e.data.productId}),i.success("导出成功")}catch(t){i.error("导出失败："+(t.message||"未知错误"))}},getStockUnit:(e,t)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"件";default:return t||"个"}},formatStock:(e,t)=>{if(!e)return"0";const a="WEIGHT"===t?3:0;return parseFloat(e).toFixed(a)},formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",getStockClass:(e,t)=>{const a=parseFloat(e)||0,o=parseFloat(t)||0;return a<=0?"stock-danger":a<=o?"stock-warning":"stock-normal"},getOperationTypeName:e=>H.getOperationTypeName(e),getOperationTypeColor:e=>H.getOperationTypeColor(e),formatQuantityChange:(e,t)=>H.formatQuantityChange(e,t),getQuantityChangeClass:(e,t)=>{const a=parseFloat(e)||0;switch(t){case"IN":return"quantity-increase";case"OUT":case"SALE":return"quantity-decrease";case"ADJUST":return a>=0?"quantity-increase":"quantity-decrease";default:return""}},formatValueChange:e=>{const t=parseFloat(e)||0;return t>=0?`+¥${t.toFixed(2)}`:`-¥${Math.abs(t).toFixed(2)}`},getValueChangeClass:e=>(parseFloat(e)||0)>=0?"value-increase":"value-decrease",getReferenceTypeName:e=>({PURCHASE_ORDER:"采购入库单",SALE_ORDER:"销售单",ADJUST_ORDER:"调整单",MANUAL:"手动操作"}[e]||e),handleCancel:()=>{t("update:visible",!1)}}}},E={class:"inventory-history-content"},q={class:"product-name"},D={class:"record-count"},j={key:0},$={key:1},M={key:1},V={key:0},L={key:1},Q={key:2},Y={key:1};e("default",a(I,[["render",function(e,t,a,o,r,n){const i=v,H=T,I=C,P=S,B=w,J=_,W=b,G=N,X=x,Z=F,K=U,ee=z,te=A,ae=O,oe=R;return l(),s(oe,{visible:a.visible,title:"库存变动历史",width:1200,footer:null,onCancel:o.handleCancel},{default:d((()=>[c("div",E,[p(I,{title:"商品信息",size:"small",style:{"margin-bottom":"16px"}},{default:d((()=>[p(H,{column:4,bordered:"",size:"small"},{default:d((()=>[p(i,{label:"商品名称"},{default:d((()=>[c("span",q,u(a.data.productName),1)])),_:1}),p(i,{label:"商品编码"},{default:d((()=>[y(u(a.data.productCode),1)])),_:1}),p(i,{label:"当前库存"},{default:d((()=>[c("span",{class:g(o.getStockClass(a.data.currentStock,a.data.minStock))},u(o.formatStock(a.data.currentStock,a.data.pricingType))+" "+u(o.getStockUnit(a.data.pricingType,a.data.unit)),3)])),_:1}),p(i,{label:"预警值"},{default:d((()=>[y(u(o.formatStock(a.data.minStock,a.data.pricingType))+" "+u(o.getStockUnit(a.data.pricingType,a.data.unit)),1)])),_:1})])),_:1})])),_:1}),p(I,{title:"筛选条件",size:"small",style:{"margin-bottom":"16px"}},{default:d((()=>[p(K,{layout:"inline",model:o.searchForm},{default:d((()=>[p(J,{label:"操作类型"},{default:d((()=>[p(B,{value:o.searchForm.operationType,"onUpdate:value":t[0]||(t[0]=e=>o.searchForm.operationType=e),placeholder:"请选择操作类型",allowClear:"",style:{width:"120px"},onChange:o.loadHistory},{default:d((()=>[(l(!0),f(m,null,h(o.operationTypeOptions,(e=>(l(),s(P,{key:e.value,value:e.value},{default:d((()=>[y(u(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","onChange"])])),_:1}),p(J,{label:"操作时间"},{default:d((()=>[p(W,{value:o.dateRange,"onUpdate:value":t[1]||(t[1]=e=>o.dateRange=e),style:{width:"240px"},placeholder:["开始时间","结束时间"],format:"YYYY-MM-DD",onChange:o.onDateRangeChange},null,8,["value","onChange"])])),_:1}),p(J,{label:"操作人"},{default:d((()=>[p(G,{value:o.searchForm.operatorName,"onUpdate:value":t[2]||(t[2]=e=>o.searchForm.operatorName=e),placeholder:"请输入操作人",allowClear:"",style:{width:"120px"},onPressEnter:o.loadHistory},null,8,["value","onPressEnter"])])),_:1}),p(J,null,{default:d((()=>[p(Z,null,{default:d((()=>[p(X,{type:"primary",onClick:o.loadHistory},{default:d((()=>t[3]||(t[3]=[y("搜索")]))),_:1,__:[3]},8,["onClick"]),p(X,{onClick:o.resetSearch},{default:d((()=>t[4]||(t[4]=[y("重置")]))),_:1,__:[4]},8,["onClick"]),p(X,{onClick:o.exportHistory},{default:d((()=>t[5]||(t[5]=[y("导出")]))),_:1,__:[5]},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),p(I,{title:"变动记录",size:"small"},{extra:d((()=>[p(Z,null,{default:d((()=>[c("span",D,"共 "+u(o.pagination.total)+" 条记录",1)])),_:1})])),default:d((()=>[p(ae,{columns:o.columns,"data-source":o.historyList,pagination:o.pagination,loading:o.loading,size:"small",bordered:"","row-key":"id",onChange:o.handleTableChange},{bodyCell:d((({column:e,record:t})=>["operationType"===e.key?(l(),s(ee,{key:0,color:o.getOperationTypeColor(t.operationType)},{default:d((()=>[y(u(o.getOperationTypeName(t.operationType)),1)])),_:2},1032,["color"])):k("",!0),"quantity"===e.key?(l(),f("span",{key:1,class:g(o.getQuantityChangeClass(t.quantityChange,t.operationType))},u(o.formatQuantityChange(t.quantityChange,t.operationType))+" "+u(o.getStockUnit(a.data.pricingType,a.data.unit)),3)):k("",!0),"beforeStock"===e.key?(l(),f(m,{key:2},[y(u(o.formatStock(t.stockBefore,a.data.pricingType))+" "+u(o.getStockUnit(a.data.pricingType,a.data.unit)),1)],64)):k("",!0),"afterStock"===e.key?(l(),f(m,{key:3},[y(u(o.formatStock(t.stockAfter,a.data.pricingType))+" "+u(o.getStockUnit(a.data.pricingType,a.data.unit)),1)],64)):k("",!0),"unitCost"===e.key?(l(),f(m,{key:4},[t.unitCost?(l(),f("span",j,"¥"+u(o.formatAmount(t.unitCost)),1)):(l(),f("span",$,"-"))],64)):k("",!0),"totalValueChange"===e.key?(l(),f(m,{key:5},[t.totalValueChange?(l(),f("span",{key:0,class:g(o.getValueChangeClass(t.totalValueChange))},u(o.formatValueChange(t.totalValueChange)),3)):(l(),f("span",M,"-"))],64)):k("",!0),"referenceType"===e.key?(l(),f(m,{key:6},[t.referenceTypeName?(l(),f("span",V,u(t.referenceTypeName),1)):t.referenceType?(l(),f("span",L,u(o.getReferenceTypeName(t.referenceType)),1)):(l(),f("span",Q,"-"))],64)):k("",!0),"operationTime"===e.key?(l(),f(m,{key:7},[y(u(t.operationTime),1)],64)):k("",!0),"operatorName"===e.key?(l(),f(m,{key:8},[y(u(t.operationUserName||t.operatorName||"-"),1)],64)):k("",!0),"remark"===e.key?(l(),f(m,{key:9},[t.remark&&t.remark.length>20?(l(),s(te,{key:0,title:t.remark},{default:d((()=>[y(u(t.remark.substring(0,20))+"... ",1)])),_:2},1032,["title"])):(l(),f("span",Y,u(t.remark||"-"),1))],64)):k("",!0)])),_:1},8,["columns","data-source","pagination","loading","onChange"])])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-49676f49"]]))}}}));
