/**
 * POS购物车状态管理
 * 
 * 管理购物车商品、数量、价格计算等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineStore } from 'pinia'
import { ref, computed, shallowRef } from 'vue'
import { message } from 'ant-design-vue'

export const useCartStore = defineStore('pos-cart', () => {
  // ==================== 状态定义 ====================
  
  // 使用shallowRef优化大数组的响应性能
  const cartItems = shallowRef([])
  
  // 金额相关状态
  const totalAmount = ref(0)
  const discountAmount = ref(0)
  const finalAmount = ref(0)
  
  // 批量更新标志，用于减少响应式更新次数
  const isBatchUpdating = ref(false)
  
  // ==================== 计算属性 ====================
  
  // 购物车商品总数量
  const cartItemCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })
  
  // 是否有商品在购物车中
  const hasCartItems = computed(() => {
    return cartItems.value.length > 0
  })
  
  // 购物车摘要信息
  const cartSummary = computed(() => ({
    itemCount: cartItemCount.value,
    totalAmount: formatAmount(totalAmount.value),
    discountAmount: formatAmount(discountAmount.value),
    finalAmount: formatAmount(finalAmount.value),
    hasItems: hasCartItems.value
  }))
  
  // ==================== 私有方法 ====================
  
  /**
   * 格式化金额显示
   * @param {number} amount - 金额
   * @returns {string} 格式化后的金额字符串
   */
  const formatAmount = (amount) => {
    if (typeof amount !== 'number') {
      return '0.00'
    }
    return amount.toFixed(2)
  }
  
  /**
   * 计算购物车总金额
   */
  const calculateTotal = () => {
    if (isBatchUpdating.value) {
      return // 批量更新时跳过计算
    }
    
    try {
      // 计算商品总金额
      totalAmount.value = cartItems.value.reduce((total, item) => {
        return total + (item.totalPrice || 0)
      }, 0)
      
      // 实付金额 = 总金额 - 折扣金额
      finalAmount.value = Math.max(0, totalAmount.value - discountAmount.value)
      
    } catch (error) {
      console.error('计算总金额失败:', error)
    }
  }
  
  /**
   * 触发响应式更新
   */
  const triggerUpdate = () => {
    // 触发shallowRef的更新
    cartItems.value = [...cartItems.value]
    calculateTotal()
  }
  
  /**
   * 验证商品数据
   * @param {Object} product - 商品信息
   * @returns {boolean} 是否有效
   */
  const validateProduct = (product) => {
    if (!product) {
      message.error('商品信息无效')
      return false
    }
    
    if (!product.productId) {
      message.error('商品ID不能为空')
      return false
    }
    
    if (!product.productName) {
      message.error('商品名称不能为空')
      return false
    }
    
    const price = product.price || product.retailPrice || product.unitPrice
    if (!price || price <= 0) {
      message.error('商品价格无效')
      return false
    }
    
    return true
  }
  
  /**
   * 检查库存
   * @param {Object} product - 商品信息
   * @param {number} quantity - 需要的数量
   * @returns {boolean} 库存是否充足
   */
  const checkStock = (product, quantity) => {
    if (product.stock !== undefined && product.stock < quantity) {
      message.warning(`商品 ${product.productName} 库存不足，当前库存：${product.stock}`)
      return false
    }
    return true
  }
  
  // ==================== 公共方法 ====================
  
  /**
   * 添加商品到购物车
   * @param {Object} product - 商品信息
   * @param {number} quantity - 数量，默认为1
   * @returns {boolean} 是否添加成功
   */
  const addToCart = (product, quantity = 1) => {
    try {
      // 验证商品数据
      if (!validateProduct(product)) {
        return false
      }
      
      // 检查库存
      if (!checkStock(product, quantity)) {
        return false
      }
      
      // 检查商品是否已在购物车中
      const existingItemIndex = cartItems.value.findIndex(
        item => item.productId === product.productId
      )
      
      if (existingItemIndex > -1) {
        // 如果商品已存在，增加数量
        const existingItem = cartItems.value[existingItemIndex]
        const newQuantity = existingItem.quantity + quantity
        
        if (!checkStock(product, newQuantity)) {
          return false
        }
        
        existingItem.quantity = newQuantity
        existingItem.totalPrice = existingItem.unitPrice * newQuantity
      } else {
        // 添加新商品到购物车
        const unitPrice = product.price || product.retailPrice || product.unitPrice
        const newItem = {
          productId: product.productId,
          productName: product.productName,
          productCode: product.productCode,
          unitPrice: unitPrice,
          quantity: quantity,
          totalPrice: unitPrice * quantity,
          unit: product.unit,
          pricingType: product.pricingType,
          retailPrice: product.retailPrice,
          piecePrice: product.piecePrice,
          referencePrice: product.referencePrice,
          imageUrl: product.imageUrl,
          addTime: Date.now() // 添加时间戳
        }
        
        cartItems.value.push(newItem)
      }
      
      // 触发更新
      triggerUpdate()
      
      return true
      
    } catch (error) {
      console.error('添加商品到购物车失败:', error)
      message.error('添加商品失败，请重试')
      return false
    }
  }
  
  /**
   * 从购物车移除商品
   * @param {number} productId - 商品ID
   * @returns {boolean} 是否移除成功
   */
  const removeFromCart = (productId) => {
    try {
      const index = cartItems.value.findIndex(item => item.productId === productId)
      if (index > -1) {
        const removedItem = cartItems.value.splice(index, 1)[0]
        triggerUpdate()
        message.success(`已移除 ${removedItem.productName}`)
        return true
      }
      return false
    } catch (error) {
      console.error('移除商品失败:', error)
      message.error('移除商品失败，请重试')
      return false
    }
  }
  
  /**
   * 更新购物车中商品的数量
   * @param {number} productId - 商品ID
   * @param {number} quantity - 新数量
   * @returns {boolean} 是否更新成功
   */
  const updateQuantity = (productId, quantity) => {
    try {
      if (quantity <= 0) {
        return removeFromCart(productId)
      }
      
      const item = cartItems.value.find(item => item.productId === productId)
      if (item) {
        item.quantity = quantity
        item.totalPrice = item.unitPrice * quantity
        triggerUpdate()
        return true
      }
      return false
    } catch (error) {
      console.error('更新商品数量失败:', error)
      message.error('更新数量失败，请重试')
      return false
    }
  }
  
  /**
   * 批量更新购物车商品
   * @param {Array} updates - 更新数据数组 [{productId, quantity}]
   * @returns {boolean} 是否更新成功
   */
  const batchUpdateQuantities = (updates) => {
    try {
      isBatchUpdating.value = true
      
      let hasChanges = false
      
      updates.forEach(({ productId, quantity }) => {
        const item = cartItems.value.find(item => item.productId === productId)
        if (item) {
          if (quantity <= 0) {
            const index = cartItems.value.findIndex(item => item.productId === productId)
            if (index > -1) {
              cartItems.value.splice(index, 1)
              hasChanges = true
            }
          } else {
            item.quantity = quantity
            item.totalPrice = item.unitPrice * quantity
            hasChanges = true
          }
        }
      })
      
      isBatchUpdating.value = false
      
      if (hasChanges) {
        triggerUpdate()
        message.success('批量更新成功')
      }
      
      return hasChanges
    } catch (error) {
      isBatchUpdating.value = false
      console.error('批量更新失败:', error)
      message.error('批量更新失败，请重试')
      return false
    }
  }
  
  /**
   * 清空购物车
   */
  const clearCart = () => {
    try {
      cartItems.value = []
      totalAmount.value = 0
      discountAmount.value = 0
      finalAmount.value = 0
      message.success('购物车已清空')
    } catch (error) {
      console.error('清空购物车失败:', error)
      message.error('清空购物车失败')
    }
  }
  
  /**
   * 设置折扣金额
   * @param {number} amount - 折扣金额
   */
  const setDiscountAmount = (amount) => {
    try {
      discountAmount.value = Math.max(0, amount)
      calculateTotal()
    } catch (error) {
      console.error('设置折扣金额失败:', error)
    }
  }
  
  /**
   * 获取商品详情
   * @param {number} productId - 商品ID
   * @returns {Object|null} 商品详情
   */
  const getCartItem = (productId) => {
    return cartItems.value.find(item => item.productId === productId) || null
  }
  
  /**
   * 获取购物车数据快照（用于备份）
   * @returns {Object} 购物车数据快照
   */
  const getCartSnapshot = () => {
    return {
      cartItems: JSON.parse(JSON.stringify(cartItems.value)),
      totalAmount: totalAmount.value,
      discountAmount: discountAmount.value,
      finalAmount: finalAmount.value,
      timestamp: Date.now()
    }
  }
  
  /**
   * 从快照恢复购物车数据
   * @param {Object} snapshot - 购物车数据快照
   * @returns {boolean} 是否恢复成功
   */
  const restoreFromSnapshot = (snapshot) => {
    try {
      if (!snapshot || !Array.isArray(snapshot.cartItems)) {
        return false
      }
      
      cartItems.value = snapshot.cartItems
      totalAmount.value = snapshot.totalAmount || 0
      discountAmount.value = snapshot.discountAmount || 0
      finalAmount.value = snapshot.finalAmount || 0
      
      triggerUpdate()
      return true
    } catch (error) {
      console.error('从快照恢复购物车失败:', error)
      return false
    }
  }
  
  /**
   * 验证购物车数据完整性
   * @returns {Object} 验证结果
   */
  const validateCart = () => {
    const issues = []
    
    cartItems.value.forEach((item, index) => {
      if (!item.productId) {
        issues.push(`第${index + 1}个商品缺少ID`)
      }
      if (!item.productName) {
        issues.push(`第${index + 1}个商品缺少名称`)
      }
      if (!item.unitPrice || item.unitPrice <= 0) {
        issues.push(`第${index + 1}个商品价格无效`)
      }
      if (!item.quantity || item.quantity <= 0) {
        issues.push(`第${index + 1}个商品数量无效`)
      }
      if (item.totalPrice !== item.unitPrice * item.quantity) {
        issues.push(`第${index + 1}个商品总价计算错误`)
      }
    })
    
    return {
      isValid: issues.length === 0,
      issues
    }
  }
  
  /**
   * 修复购物车数据
   * @returns {boolean} 是否修复成功
   */
  const repairCart = () => {
    try {
      let hasRepairs = false
      
      cartItems.value.forEach(item => {
        // 修复总价计算
        const correctTotalPrice = item.unitPrice * item.quantity
        if (item.totalPrice !== correctTotalPrice) {
          item.totalPrice = correctTotalPrice
          hasRepairs = true
        }
        
        // 修复数量
        if (item.quantity <= 0) {
          item.quantity = 1
          item.totalPrice = item.unitPrice
          hasRepairs = true
        }
      })
      
      if (hasRepairs) {
        triggerUpdate()
        message.info('购物车数据已修复')
      }
      
      return hasRepairs
    } catch (error) {
      console.error('修复购物车数据失败:', error)
      return false
    }
  }
  
  // ==================== 返回接口 ====================
  
  return {
    // 状态
    cartItems,
    totalAmount,
    discountAmount,
    finalAmount,
    isBatchUpdating,
    
    // 计算属性
    cartItemCount,
    hasCartItems,
    cartSummary,
    
    // 方法
    addToCart,
    removeFromCart,
    updateQuantity,
    batchUpdateQuantities,
    clearCart,
    setDiscountAmount,
    getCartItem,
    getCartSnapshot,
    restoreFromSnapshot,
    validateCart,
    repairCart,
    
    // 工具方法
    formatAmount,
    calculateTotal
  }
})