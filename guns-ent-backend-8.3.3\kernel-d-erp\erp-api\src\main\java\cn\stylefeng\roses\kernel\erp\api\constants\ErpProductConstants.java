package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * 商品模块常量
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
public interface ErpProductConstants {

    /**
     * 商品模块名称
     */
    String PRODUCT_MODULE_NAME = "erp-product";

    /**
     * 商品模块前缀
     */
    String PRODUCT_MODULE_PREFIX = "ERP_PRODUCT";

    /**
     * 商品状态：正常
     */
    String PRODUCT_STATUS_ACTIVE = "ACTIVE";

    /**
     * 商品状态：停用
     */
    String PRODUCT_STATUS_INACTIVE = "INACTIVE";

    /**
     * 商品状态：停产
     */
    String PRODUCT_STATUS_DISCONTINUED = "DISCONTINUED";

    /**
     * 默认商品状态
     */
    String DEFAULT_PRODUCT_STATUS = PRODUCT_STATUS_ACTIVE;

    /**
     * 默认基本单位
     */
    String DEFAULT_UNIT = "个";

    /**
     * 常用单位
     */
    String[] COMMON_UNITS = {"个", "件", "盒", "包", "袋", "瓶", "罐", "桶", "箱", "台", "套", "副", "双", "对", "张", "本", "支", "根", "条", "米", "千克", "克", "升", "毫升"};

    /**
     * 计价类型：普通商品
     */
    String PRICING_TYPE_NORMAL = "NORMAL";

    /**
     * 计价类型：计重商品
     */
    String PRICING_TYPE_WEIGHT = "WEIGHT";

    /**
     * 计价类型：计件商品
     */
    String PRICING_TYPE_PIECE = "PIECE";

    /**
     * 计价类型：不定价商品
     */
    String PRICING_TYPE_VARIABLE = "VARIABLE";

    /**
     * 默认计价类型
     */
    String DEFAULT_PRICING_TYPE = PRICING_TYPE_NORMAL;

    /**
     * 所有计价类型
     */
    String[] ALL_PRICING_TYPES = {PRICING_TYPE_NORMAL, PRICING_TYPE_WEIGHT, PRICING_TYPE_PIECE, PRICING_TYPE_VARIABLE};

}
