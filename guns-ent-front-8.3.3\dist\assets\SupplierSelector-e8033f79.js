import{r as i,_ as E,L as g,X as I,o as O,aK as V,a as _,f as v,w as h,c as B,F as P,e as A,b as S,t as y,d as L,g as x,U as J,W as U,J as k}from"./index-18a1ea24.js";/* empty css              */import{S as F}from"./SupplierApi-6b9315dd.js";const b={data:i([]),loading:i(!1),lastFetchTime:0,cacheTimeout:5*60*1e3,loadPromise:null,async fetchSuppliers(s=!1){const n=Date.now();return!s&&this.data.value.length>0&&n-this.lastFetchTime<this.cacheTimeout?this.data.value:this.loadPromise?this.loadPromise:(this.loadPromise=(async()=>{try{this.loading.value=!0;const a=await F.findList({status:"ACTIVE"});return this.data.value=a||[],this.lastFetchTime=n,this.data.value}catch(a){throw console.error("\u52A0\u8F7D\u4F9B\u5E94\u5546\u5217\u8868\u5931\u8D25:",a),a}finally{this.loading.value=!1,this.loadPromise=null}})(),this.loadPromise)},clearCache(){this.data.value=[],this.lastFetchTime=0,this.loadPromise=null}},R={name:"SupplierSelector",props:{value:{type:[String,Number],default:void 0},allowClear:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},filter:{type:Object,default:()=>({})}},emits:["update:value","change"],setup(s,{emit:n}){const a=i(!1),t=i([]),u=i(""),c=i(!1),d=g({get:()=>s.value,set:e=>{n("update:value",e)}}),f=g(()=>{let e=t.value;if(s.filter.businessMode){const l=Array.isArray(s.filter.businessMode)?s.filter.businessMode:[s.filter.businessMode];e=e.filter(r=>l.includes(r.businessMode))}if(s.filter.includeJointVenture||(e=e.filter(l=>l.businessMode!=="JOINT_VENTURE")),u.value){const l=u.value.toLowerCase();e=e.filter(r=>r.supplierName.toLowerCase().includes(l)||r.supplierCode.toLowerCase().includes(l))}return e}),p=e=>({PURCHASE_SALE:"\u8D2D\u9500",JOINT_VENTURE:"\u8054\u8425",CONSIGNMENT:"\u4EE3\u9500"})[e]||e,o=e=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"})[e]||"default",C=(e,l)=>!0,N=(e,l)=>{const r=t.value.find(T=>T.supplierId===e);n("change",e,r)},w=e=>{u.value=e},M=async()=>{try{a.value=!0;const e=await b.fetchSuppliers(!0);t.value=e||[]}catch(e){console.error("\u5237\u65B0\u4F9B\u5E94\u5546\u5217\u8868\u5931\u8D25:",e)}finally{a.value=!1}},m=async()=>{if(!a.value)try{a.value=!0;const e=await b.fetchSuppliers();t.value=e||[],c.value=!0}catch(e){console.error("\u52A0\u8F7D\u4F9B\u5E94\u5546\u5217\u8868\u5931\u8D25:",e),t.value=[]}finally{a.value=!1}};return I(()=>s.filter,(e,l)=>{c.value&&JSON.stringify(e)!==JSON.stringify(l)&&m()},{deep:!0}),O(()=>{V(()=>{m()})}),{loading:a,selectedValue:d,filteredSuppliers:f,getBusinessModeName:p,getBusinessModeColor:o,filterOption:C,handleChange:N,handleSearch:w,refreshSuppliers:M}}},z={class:"supplier-option"},D={class:"supplier-name"};function G(s,n,a,t,u,c){const d=J,f=U,p=k;return _(),v(p,{value:t.selectedValue,"onUpdate:value":n[0]||(n[0]=o=>t.selectedValue=o),placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",loading:t.loading,allowClear:a.allowClear,disabled:a.disabled,"show-search":"","filter-option":t.filterOption,onChange:t.handleChange,onSearch:t.handleSearch},{default:h(()=>[(_(!0),B(P,null,A(t.filteredSuppliers,o=>(_(),v(f,{key:o.supplierId,value:o.supplierId,disabled:o.disabled},{default:h(()=>[S("div",z,[S("span",D,y(o.supplierName),1),L(d,{color:t.getBusinessModeColor(o.businessMode),size:"small",class:"business-mode-tag"},{default:h(()=>[x(y(t.getBusinessModeName(o.businessMode)),1)]),_:2},1032,["color"])])]),_:2},1032,["value","disabled"]))),128))]),_:1},8,["value","loading","allowClear","disabled","filter-option","onChange","onSearch"])}const W=E(R,[["render",G],["__scopeId","data-v-8bd89d78"]]);export{W as _};
