<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-micro</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>micro-project-tran-message</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- 微服务核心包 -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>micro-spring-boot-starter</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- 数据库操作 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库驱动,可根据自己需要自行删减-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>

        <!--系统配置-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>config-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- rocket mq -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        </dependency>

        <!-- web应用程序 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- spring boot配置处理 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
