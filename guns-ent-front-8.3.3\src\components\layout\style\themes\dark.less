/** 暗黑主题 */
@import './default.less';
@import 'ant-design-vue/es/style/themes/dark.less';

// 滚动条
@scrollbar-thumb-color: #484848;
@scrollbar-thumb-hover-color: #5b5b5b;

// 侧栏
@sidebar-light-shadow: 0 4px 4px rgba(0, 0, 0, 0.6);
@sidebar-dark-shadow: 0 4px 4px rgba(0, 0, 0, 0.6);

// 顶栏
@header-light-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
@header-dark-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
@header-tool-hover-bg: rgba(255, 255, 255, 0.05);
@header-dark-tool-hover-bg: rgba(255, 255, 255, 0.05);

// logo
@logo-light-shadow: 0 3px 4px rgba(0, 0, 0, 0.6);
@logo-dark-shadow: 0 3px 4px rgba(0, 0, 0, 0.6);
