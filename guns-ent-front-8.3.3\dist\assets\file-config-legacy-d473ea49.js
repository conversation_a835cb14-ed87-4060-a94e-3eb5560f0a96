System.register(["./index-legacy-ee1db0c7.js"],(function(e,a){"use strict";var l,t,i,n,o,u,s,f,r,d,c,v,g,p,h,m,y,_,x,b;return{setters:[e=>{l=e.R,t=e._,i=e.r,n=e.s,o=e.o,u=e.a,s=e.c,f=e.b,r=e.d,d=e.w,c=e.g,v=e.f,g=e.h,p=e.m,h=e.z,m=e.A,y=e.l,_=e.u,x=e.H,b=e.B}],execute:function(){var a=document.createElement("style");a.textContent=".file-config[data-v-4115efb5]{width:50%;height:300px;border:1px solid #eee;border-radius:4px;margin:16px;padding:12px 16px;box-shadow:0 0 4px #eee}.file-config-title[data-v-4115efb5]{font-size:16px;font-weight:700}.file-config-remark[data-v-4115efb5]{color:#ccc;margin-top:10px}.file-config-type[data-v-4115efb5]{margin:20px 0}\n",document.head.appendChild(a);class C{static getFileConfig(){return l.get("/new/sysConfig/getFileConfig")}static updateFileConfig(e){return l.post("/new/sysConfig/updateFileConfig",e)}}const F={class:"file-config"},S={class:"file-config-type"},w={__name:"file-config",setup(e){const a=i({fileSaveType:null,localFileSavePath:null}),l=n({localFileSavePath:[{required:!0,type:"string",message:"请输入存储目录路径",trigger:"blur"}]}),t=i();o((()=>{w()}));const w=()=>{C.getFileConfig().then((e=>{a.value=e.data}))},P=()=>{10==a.value.fileSaveType&&(a.value.localFileSavePath=null)},k=async()=>{await t.value.validate(),C.updateFileConfig(a.value).then((e=>{p.success(e.message),w()}))};return(e,i)=>{const n=h,o=m,p=y,C=_,w=x,T=b;return u(),s("div",F,[i[5]||(i[5]=f("div",{class:"file-config-title"},"文件存储方式",-1)),i[6]||(i[6]=f("div",{class:"file-config-remark"},"配置上传的文档存储在什么地方",-1)),f("div",S,[r(o,{value:a.value.fileSaveType,"onUpdate:value":i[0]||(i[0]=e=>a.value.fileSaveType=e),name:"sex",onChange:P},{default:d((()=>[r(n,{value:10},{default:d((()=>i[2]||(i[2]=[c("本地，存储到默认路径（jar所在目录）")]))),_:1,__:[2]}),r(n,{value:11},{default:d((()=>i[3]||(i[3]=[c("存储到指定路径下（需要配置linux和windows的路径）")]))),_:1,__:[3]})])),_:1},8,["value"])]),r(w,{model:a.value,rules:l,ref_key:"formRef",ref:t},{default:d((()=>[10!=a.value.fileSaveType?(u(),v(C,{key:0,label:"存储目录路径",name:"localFileSavePath"},{default:d((()=>[r(p,{value:a.value.localFileSavePath,"onUpdate:value":i[1]||(i[1]=e=>a.value.localFileSavePath=e),placeholder:"输入应用服务器完整目录结构，必须目录已存在",style:{width:"80%"}},null,8,["value"])])),_:1})):g("",!0)])),_:1},8,["model","rules"]),r(T,{type:"primary",class:"border-radius",onClick:k},{default:d((()=>i[4]||(i[4]=[c("保存")]))),_:1,__:[4]})])}}};e("default",t(w,[["__scopeId","data-v-4115efb5"]]))}}}));
