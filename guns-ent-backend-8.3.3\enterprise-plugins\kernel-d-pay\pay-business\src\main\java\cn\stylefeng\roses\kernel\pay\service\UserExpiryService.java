package cn.stylefeng.roses.kernel.pay.service;

import cn.stylefeng.roses.kernel.pay.api.entity.UserExpiry;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户商品到期时间服务类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
public interface UserExpiryService extends IService<UserExpiry> {

    /**
     * 增加用户对某个商品的到期时间
     *
     * @param userId  用户id
     * @param goodsId 商品id
     * @param days    天数
     * <AUTHOR>
     * @since 2024/5/26 0:05
     */
    void addUserExpiryDays(Long userId, Long goodsId, int days);

    /**
     * 增加用户对某个商品的到期时间
     *
     * @param userId  用户id
     * @param goodsId 商品id
     * @param months  月数
     * <AUTHOR>
     * @since 2024/5/26 0:05
     */
    void addUserExpiryMonths(Long userId, Long goodsId, int months);

}
