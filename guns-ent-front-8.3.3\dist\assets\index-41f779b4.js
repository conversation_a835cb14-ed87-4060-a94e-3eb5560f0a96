import{_ as K}from"./index-cd3d6e23.js";import{C as Q,_ as X}from"./index-02bf6f00.js";import{_ as Y,r as d,o as Z,k as y,a as i,c as x,b as a,d as n,w as o,g as I,t as E,h as u,f as m,F as ee,M as F,E as V,m as C,I as te,l as ne,n as se,B as ae,p as oe,q as le,D as ie}from"./index-18a1ea24.js";import{T as D}from"./TenantApi-e23e3174.js";import de from"./tenant-detail-3a81cf05.js";import ue from"./tenant-add-edit-644d381d.js";/* empty css              *//* empty css              *//* empty css              */import"./tenant-form-a280149b.js";/* empty css              *//* empty css              *//* empty css              */import"./FileApi-418f4d35.js";import"./time-util-d1d9a3df.js";const ce={class:"guns-layout"},re={class:"guns-layout-content"},_e={class:"guns-layout"},pe={class:"guns-layout-content-application"},me={class:"content-mian"},ve={class:"content-mian-header"},he={class:"header-content"},fe={class:"header-content-left"},ge={class:"header-content-right"},we={class:"content-mian-body"},xe={class:"table-content"},Ce=["onClick"],be=["src"],ke=Object.assign({name:"TenantManage"},{__name:"index",setup(ye){const v=d([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:t})=>c.value.tableIndex+t},{dataIndex:"tenantName",title:"\u79DF\u6237\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"tenantCode",title:"\u79DF\u6237\u7F16\u7801",width:100,isShow:!0},{dataIndex:"tenantLogoWrapper",title:"\u79DF\u6237logo",width:100,isShow:!0},{dataIndex:"companyName",title:"\u516C\u53F8",width:100,isShow:!0},{dataIndex:"email",title:"\u7533\u8BF7\u4EBA\u90AE\u7BB1",width:100,isShow:!0},{dataIndex:"safePhone",title:"\u7533\u8BF7\u4EBA\u7535\u8BDD",width:100,isShow:!0},{dataIndex:"statusFlag",title:"\u72B6\u6001",width:100,isShow:!0},{dataIndex:"activeDate",title:"\u79DF\u6237\u751F\u6548\u65F6\u95F4",width:100,isShow:!0},{dataIndex:"expireDate",title:"\u79DF\u6237\u5230\u671F\u65F6\u95F4",width:100,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:80,isShow:!0}]),c=d(null),b=d({searchText:""}),h=d(!1),f=d(null),g=d(!1),w=d(!1),S=d("TENANT_MANAGEMENT");Z(()=>{L()});const L=()=>{Q.getUserConfig({fieldBusinessCode:S.value}).then(t=>{t.tableWidthJson&&(v.value=JSON.parse(t.tableWidthJson))})},M=({key:t})=>{t=="1"?h.value=!0:t=="2"&&A()},r=()=>{c.value.reload()},N=t=>{f.value=t,g.value=!0},U=t=>{f.value=t,w.value=!0},z=t=>{F.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u79DF\u6237\u5417?",icon:n(V),maskClosable:!0,onOk:async()=>{const e=await D.delete({tenantId:t.tenantId});C.success(e.message),r()}})},A=()=>{if(c.value.selectedRowList&&c.value.selectedRowList.length==0)return C.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u79DF\u6237");F.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u79DF\u6237\u5417?",icon:n(V),maskClosable:!0,onOk:async()=>{const t=await D.batchDelete({tenantIdList:c.value.selectedRowList});C.success(t.message),r()}})},R=t=>{D.edit(t).then(e=>{C.success(e.message),r()})},W=t=>{let e="";return t&&(e=t.substr(0,10)),e};return(t,e)=>{const _=te,$=ne,k=se,O=y("plus-outlined"),T=ae,B=oe,J=le,j=y("small-dash-outlined"),P=ie,q=y("vxe-switch"),G=X,H=K;return i(),x("div",ce,[a("div",re,[a("div",_e,[a("div",pe,[a("div",me,[a("div",ve,[a("div",he,[a("div",fe,[n(k,{size:16},{default:o(()=>[n($,{value:b.value.searchText,"onUpdate:value":e[0]||(e[0]=s=>b.value.searchText=s),placeholder:"\u79DF\u6237\u540D\u79F0\u3001\u79DF\u6237\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:r,class:"search-input"},{prefix:o(()=>[n(_,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),_:1})]),a("div",ge,[n(k,{size:16},{default:o(()=>[n(T,{type:"primary",class:"border-radius",onClick:e[1]||(e[1]=s=>N())},{default:o(()=>[n(O),e[6]||(e[6]=I("\u65B0\u5EFA"))]),_:1,__:[6]}),n(P,null,{overlay:o(()=>[n(J,{onClick:M},{default:o(()=>[n(B,{key:"1"},{default:o(()=>[n(_,{iconClass:"icon-opt-zidingyilie",color:"#60666b"}),e[7]||(e[7]=a("span",null,"\u81EA\u5B9A\u4E49\u5217",-1))]),_:1,__:[7]}),a("div",null,[n(B,{key:"2"},{default:o(()=>[n(_,{iconClass:"icon-opt-shanchu",color:"#60666b"}),e[8]||(e[8]=a("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[8]})])]),_:1})]),default:o(()=>[n(T,{class:"border-radius"},{default:o(()=>[e[9]||(e[9]=I(" \u66F4\u591A ")),n(j)]),_:1,__:[9]})]),_:1})]),_:1})])])]),a("div",we,[a("div",xe,[n(G,{columns:v.value,where:b.value,rowId:"tenantId",ref_key:"tableRef",ref:c,url:"/tenant/page"},{bodyCell:o(({column:s,record:l})=>[s.dataIndex=="tenantName"?(i(),x("a",{key:0,onClick:p=>U(l)},E(l.tenantName),9,Ce)):u("",!0),s.dataIndex=="tenantLogoWrapper"?(i(),x("img",{key:1,src:l.tenantLogoWrapper,alt:"",class:"appIconWrapper"},null,8,be)):u("",!0),s.dataIndex=="statusFlag"?(i(),m(q,{key:2,modelValue:l.statusFlag,"onUpdate:modelValue":p=>l.statusFlag=p,"open-value":1,"close-value":2,onChange:p=>R(l)},null,8,["modelValue","onUpdate:modelValue","onChange"])):u("",!0),s.dataIndex=="activeDate"||s.dataIndex=="expireDate"?(i(),x(ee,{key:3},[I(E(W(l[s.dataIndex])),1)],64)):u("",!0),s.key=="action"?(i(),m(k,{key:4,size:16},{default:o(()=>[n(_,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:p=>N(l)},null,8,["onClick"]),n(_,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:p=>z(l)},null,8,["onClick"])]),_:2},1024)):u("",!0)]),_:1},8,["columns","where"])])])])])])]),h.value?(i(),m(H,{key:0,visible:h.value,"onUpdate:visible":e[2]||(e[2]=s=>h.value=s),data:v.value,onDone:e[3]||(e[3]=s=>v.value=s),fieldBusinessCode:S.value},null,8,["visible","data","fieldBusinessCode"])):u("",!0),g.value?(i(),m(ue,{key:1,visible:g.value,"onUpdate:visible":e[4]||(e[4]=s=>g.value=s),data:f.value,onDone:r},null,8,["visible","data"])):u("",!0),w.value?(i(),m(de,{key:2,visible:w.value,"onUpdate:visible":e[5]||(e[5]=s=>w.value=s),data:f.value,onDone:r},null,8,["visible","data"])):u("",!0)])}}}),We=Y(ke,[["__scopeId","data-v-58d50984"]]);export{We as default};
