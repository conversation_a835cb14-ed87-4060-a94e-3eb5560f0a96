package cn.stylefeng.roses.kernel.sync.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 组织机构数据同步的响应信息
 *
 * <AUTHOR>
 * @since 2023/10/30 10:32
 */
@Data
public class OrganizationSyncVo {

    /**
     * 主键，组织机构唯一id
     */
    @ChineseDescription("主键，组织机构唯一id")
    private String orgId;

    /**
     * 组织机构父级id
     */
    @ChineseDescription("组织机构父级id")
    private String orgParentId;

    /**
     * 组织机构父级id集合
     */
    @ChineseDescription("组织机构父级id集合")
    private String orgPids;

    /**
     * 组织机构名称
     */
    @ChineseDescription("组织机构名称")
    private String orgName;

    /**
     * 组织机构简称
     */
    @ChineseDescription("组织机构简称")
    private String orgShortName;

    /**
     * 组织机构编码
     */
    @ChineseDescription("组织机构编码")
    private String orgCode;

    /**
     * 排序
     */
    @ChineseDescription("排序")
    private BigDecimal orgSort;

    /**
     * 状态：1-启用，2-禁用
     */
    @ChineseDescription("状态：1-启用，2-禁用")
    private Integer statusFlag;

    /**
     * 组织机构类型：1-公司，2-部门
     */
    @ChineseDescription("组织机构类型：1-公司，2-部门")
    private Integer orgType;

    /**
     * 税号
     */
    @ChineseDescription("税号")
    private String taxNo;

    /**
     * 描述
     */
    @ChineseDescription("描述")
    private String remark;

    /**
     * 组织机构层级
     */
    @ChineseDescription("组织机构层级")
    private Integer orgLevel;

    /**
     * 租户id
     */
    @ChineseDescription("租户id")
    private Long tenantId;

}
