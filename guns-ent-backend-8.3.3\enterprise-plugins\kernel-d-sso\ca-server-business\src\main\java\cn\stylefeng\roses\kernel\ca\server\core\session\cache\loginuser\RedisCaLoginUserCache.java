package cn.stylefeng.roses.kernel.ca.server.core.session.cache.loginuser;

import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import cn.stylefeng.roses.kernel.cache.redis.AbstractRedisCacheOperator;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 基于redis的登录用户缓存
 * <p>
 * key：    CAID
 * value：  CAID对用的用户登录信息
 *
 * <AUTHOR>
 * @date 2020/12/24 19:16
 */
public class RedisCaLoginUserCache extends AbstractRedisCacheOperator<CaLoginUser> {

    public RedisCaLoginUserCache(RedisTemplate<String, CaLoginUser> redisTemplate) {
        super(redisTemplate);
    }

    @Override
    public String getCommonKeyPrefix() {
        return CaServerConstants.CA_USER_TOKEN_CACHE_PREFIX;
    }

}
