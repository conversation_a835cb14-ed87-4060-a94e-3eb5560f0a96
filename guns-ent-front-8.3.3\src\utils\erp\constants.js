/**
 * ERP业务常量定义
 * 
 * 定义ERP业务中使用的各种常量、枚举值和配置项
 */

// 供应商经营方式枚举
export const BUSINESS_MODE = {
  PURCHASE_SALE: 'PURCHASE_SALE',  // 购销
  JOINT_VENTURE: 'JOINT_VENTURE',  // 联营
  CONSIGNMENT: 'CONSIGNMENT'       // 代销
}

// 供应商经营方式文本映射
export const BUSINESS_MODE_TEXT = {
  [BUSINESS_MODE.PURCHASE_SALE]: '购销',
  [BUSINESS_MODE.JOINT_VENTURE]: '联营',
  [BUSINESS_MODE.CONSIGNMENT]: '代销'
}

// 供应商经营方式颜色映射（用于标签显示）
export const BUSINESS_MODE_COLOR = {
  [BUSINESS_MODE.PURCHASE_SALE]: 'blue',
  [BUSINESS_MODE.JOINT_VENTURE]: 'green',
  [BUSINESS_MODE.CONSIGNMENT]: 'orange'
}

// 商品计价类型枚举
export const PRICING_TYPE = {
  NORMAL: 'NORMAL',      // 普通商品
  WEIGHT: 'WEIGHT',      // 计重商品
  PIECE: 'PIECE',        // 计件商品
  VARIABLE: 'VARIABLE'   // 不定价商品
}

// 商品计价类型文本映射
export const PRICING_TYPE_TEXT = {
  [PRICING_TYPE.NORMAL]: '普通商品',
  [PRICING_TYPE.WEIGHT]: '计重商品',
  [PRICING_TYPE.PIECE]: '计件商品',
  [PRICING_TYPE.VARIABLE]: '不定价商品'
}

// 商品计价类型颜色映射
export const PRICING_TYPE_COLOR = {
  [PRICING_TYPE.NORMAL]: 'default',
  [PRICING_TYPE.WEIGHT]: 'processing',
  [PRICING_TYPE.PIECE]: 'success',
  [PRICING_TYPE.VARIABLE]: 'warning'
}

// 采购入库单状态枚举
export const PURCHASE_ORDER_STATUS = {
  DRAFT: 'DRAFT',           // 草稿
  CONFIRMED: 'CONFIRMED',   // 已确认
  COMPLETED: 'COMPLETED'    // 已完成
}

// 采购入库单状态文本映射
export const PURCHASE_ORDER_STATUS_TEXT = {
  [PURCHASE_ORDER_STATUS.DRAFT]: '草稿',
  [PURCHASE_ORDER_STATUS.CONFIRMED]: '已确认',
  [PURCHASE_ORDER_STATUS.COMPLETED]: '已完成'
}

// 采购入库单状态颜色映射
export const PURCHASE_ORDER_STATUS_COLOR = {
  [PURCHASE_ORDER_STATUS.DRAFT]: 'default',
  [PURCHASE_ORDER_STATUS.CONFIRMED]: 'processing',
  [PURCHASE_ORDER_STATUS.COMPLETED]: 'success'
}

// 库存状态枚举
export const INVENTORY_STATUS = {
  NORMAL: 'NORMAL',           // 正常
  WARNING: 'WARNING',         // 预警
  OUT_OF_STOCK: 'OUT_OF_STOCK' // 缺货
}

// 库存状态文本映射
export const INVENTORY_STATUS_TEXT = {
  [INVENTORY_STATUS.NORMAL]: '正常',
  [INVENTORY_STATUS.WARNING]: '预警',
  [INVENTORY_STATUS.OUT_OF_STOCK]: '缺货'
}

// 库存状态颜色映射
export const INVENTORY_STATUS_COLOR = {
  [INVENTORY_STATUS.NORMAL]: 'success',
  [INVENTORY_STATUS.WARNING]: 'warning',
  [INVENTORY_STATUS.OUT_OF_STOCK]: 'error'
}

// 付款方式枚举
export const PAYMENT_METHOD = {
  CASH: 'CASH',                    // 现金
  BANK_TRANSFER: 'BANK_TRANSFER', // 银行转账
  CREDIT: 'CREDIT'                // 赊账
}

// 付款方式文本映射
export const PAYMENT_METHOD_TEXT = {
  [PAYMENT_METHOD.CASH]: '现金',
  [PAYMENT_METHOD.BANK_TRANSFER]: '银行转账',
  [PAYMENT_METHOD.CREDIT]: '赊账'
}

// 库存操作类型枚举
export const INVENTORY_OPERATION_TYPE = {
  IN: 'IN',           // 入库
  OUT: 'OUT',         // 出库
  ADJUST: 'ADJUST'    // 调整
}

// 库存操作类型文本映射
export const INVENTORY_OPERATION_TYPE_TEXT = {
  [INVENTORY_OPERATION_TYPE.IN]: '入库',
  [INVENTORY_OPERATION_TYPE.OUT]: '出库',
  [INVENTORY_OPERATION_TYPE.ADJUST]: '调整'
}

// 数值精度配置
export const PRECISION_CONFIG = {
  PRICE: 2,        // 价格精度（小数点后2位）
  QUANTITY: 3,     // 数量精度（小数点后3位）
  WEIGHT: 3,       // 重量精度（小数点后3位）
  PERCENTAGE: 2    // 百分比精度（小数点后2位）
}

// 默认分页配置
export const DEFAULT_PAGE_CONFIG = {
  PAGE_SIZE: 20,           // 默认每页条数
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'], // 每页条数选项
  SHOW_SIZE_CHANGER: true, // 显示每页条数选择器
  SHOW_QUICK_JUMPER: true  // 显示快速跳转
}