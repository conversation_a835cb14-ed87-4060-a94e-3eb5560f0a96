package cn.stylefeng.roses.kernel.micro.core.feign;

import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * Feign拦截器，为每个Feign请求添加初始请求带的Header
 * <p>
 * 会过滤掉Accept属性
 *
 * <AUTHOR>
 * @date 2018-05-07-下午7:25
 */
@Slf4j
public class GunsFeignHeaderProcessInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {

        // 获取当前请求的http请求上下文
        HttpServletRequest request = null;
        try {
            request = HttpServletUtil.getRequest();
        } catch (Exception exception) {
            // 获取不到请求类，则当前进行的不是http请求
            return;
        }

        if (request == null) {
            if (log.isDebugEnabled()) {
                log.debug("被调环境中不存在request对象，则不往header里添加当前请求环境的header!");
            }
            return;
        } else {
            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();

                    //过滤掉Accept Header
                    if (name.equalsIgnoreCase("Accept")) {
                        continue;
                    }

                    String values = request.getHeader(name);
                    requestTemplate.header(name, values);
                }
            }
        }
        this.addOtherHeaders(requestTemplate);
    }

    public void addOtherHeaders(RequestTemplate requestTemplate) {

    }
}
