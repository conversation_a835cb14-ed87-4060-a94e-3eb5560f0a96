System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./timer-form-legacy-0d0c4784.js","./timer-add-edit-legacy-733ecd4e.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,t){"use strict";var a,l,s,i,n,o,c,d,r,u,m,h,v,y,w,p,b,x,g,f,k,I;return{setters:[e=>{a=e._},e=>{l=e.r,s=e.o,i=e.k,n=e.a,o=e.c,c=e.b,d=e.d,r=e.w,u=e.g,m=e.t,h=e.h,v=e.f,y=e.M,w=e.E,p=e.m,b=e.n,x=e.B,g=e.I,f=e.l},e=>{k=e.S},e=>{I=e.default},null,null,null],execute:function(){const t={class:"guns-layout"},S={class:"guns-layout-content"},C={class:"guns-layout"},j={class:"guns-layout-content-application"},_={class:"content-mian"},T={class:"content-mian-header"},z={class:"header-content"},B={class:"header-content-left"},E={class:"header-content-right"},N={class:"content-mian-body"},U={class:"table-content"},V=["onClick"];e("default",Object.assign({name:"SystemTimer"},{__name:"index",setup(e){const L=l([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"timerName",title:"任务名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"params",title:"参数",width:100,isShow:!0},{dataIndex:"cron",title:"cron表达式",ellipsis:!0,width:200,isShow:!0},{dataIndex:"actionClass",title:"任务class",ellipsis:!0,width:100,isShow:!0},{dataIndex:"remark",title:"备注信息",ellipsis:!0,width:150,isShow:!0},{dataIndex:"jobStatus",title:"状态",width:150,isShow:!0},{key:"action",title:"操作",width:80,isShow:!0}]),M=l(null),O=l({timerName:""}),R=l(null),A=l(!1);s((()=>{}));const D=()=>{M.value.reload()},P=e=>{R.value=e,A.value=!0};return(e,l)=>{const s=b,Z=i("plus-outlined"),q=x,F=g,G=f,H=i("vxe-switch"),J=a;return n(),o("div",t,[c("div",S,[c("div",C,[c("div",j,[c("div",_,[c("div",T,[c("div",z,[c("div",B,[d(s,{size:16})]),c("div",E,[d(s,{size:16},{default:r((()=>[d(q,{type:"primary",class:"border-radius",onClick:l[0]||(l[0]=e=>P())},{default:r((()=>[d(Z),l[3]||(l[3]=u("新建"))])),_:1,__:[3]})])),_:1})])])]),c("div",N,[c("div",U,[d(J,{columns:L.value,where:O.value,rowId:"timerId",ref_key:"tableRef",ref:M,rowSelection:!1,url:"/sysTimers/page",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"TIMER_TABLE"},{toolLeft:r((()=>[d(G,{value:O.value.searchText,"onUpdate:value":l[1]||(l[1]=e=>O.value.searchText=e),placeholder:"任务名称（回车搜索）",bordered:!1,onPressEnter:D,class:"search-input"},{prefix:r((()=>[d(F,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:r((({column:e,record:t})=>["timerName"==e.dataIndex?(n(),o("a",{key:0,onClick:e=>P(t)},m(t.timerName),9,V)):h("",!0),"jobStatus"===e.dataIndex?(n(),v(H,{key:1,modelValue:t.jobStatus,"onUpdate:modelValue":e=>t.jobStatus=e,"open-value":1,"close-value":2,"open-label":"运行","close-label":"停止",onChange:e=>(async e=>{const t=e.timerId;let a={};a=1==e.jobStatus?await k.start({timerId:t}):await k.stop({timerId:t}),p.success(a.message),D()})(t)},null,8,["modelValue","onUpdate:modelValue","onChange"])):h("",!0),"action"==e.key?(n(),v(s,{key:2,size:16},{default:r((()=>[d(F,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>P(t)},null,8,["onClick"]),d(F,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{y.confirm({title:"提示",content:"确定要删除选中的定时任务吗?",icon:d(w),maskClosable:!0,onOk:async()=>{const t=await k.delete({timerId:e.timerId});p.success(t.message),D()}})})(t)},null,8,["onClick"])])),_:2},1024)):h("",!0)])),_:1},8,["columns","where"])])])])])])]),A.value?(n(),v(I,{key:0,visible:A.value,"onUpdate:visible":l[2]||(l[2]=e=>A.value=e),data:R.value,onDone:D},null,8,["visible","data"])):h("",!0)])}}}))}}}));
