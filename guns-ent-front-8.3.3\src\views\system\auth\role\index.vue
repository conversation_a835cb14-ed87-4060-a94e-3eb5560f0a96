<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <div class="content-mian-header">
              <div class="header-content">
                <div class="header-content-left">
                  <a-space :size="16">
                    <a-tabs v-model:activeKey="where.roleType" class="devops-tabs" @change="roleTypeChange">
                      <a-tab-pane key="" tab="全部" />
                      <a-tab-pane :key="10" tab="系统角色" />
                      <a-tab-pane :key="15" tab="业务角色" />
                      <a-tab-pane :key="20" tab="公司角色" />
                    </a-tabs>
                  </a-space>
                </div>
                <div class="header-content-right">
                  <a-space :size="16">
                    <a-button type="primary" class="border-radius" @click="openAddEdit()" v-permission="['ADD_ROLE']"
                    ><plus-outlined />新建</a-button
                    >
                    <a-dropdown>
                      <template #overlay>
                        <a-menu @click="moreClick">
                          <div v-permission="['EDIT_ROLE']">
                            <a-menu-item key="1">
                              <icon-font iconClass="icon-opt-shanchu" color="#60666b" />
                              <span>批量删除</span>
                            </a-menu-item>
                          </div>
                        </a-menu>
                      </template>
                      <a-button class="border-radius">
                        更多
                        <small-dash-outlined />
                      </a-button>
                    </a-dropdown>
                  </a-space>
                </div>
              </div>
            </div>
            <div class="content-mian-body">
              <div class="table-content">
                <guns-split-layout
                  :width="['', 10].includes(where.roleType) ? '0px' : '292px'"
                  :allowCollapse="['', 10].includes(where.roleType) ? false : true"
                  :resizable="['', 10].includes(where.roleType) ? false : true"
                >
                  <RoleTree
                    v-if="[15, 20].includes(where.roleType)"
                    :roleType="where.roleType"
                    @treeSelect="treeSelect"
                    @delRoleType="delRoleType"
                    @getCompanyData="getCompanyData"
                  />
                  <template #content>
                    <div :class="['role-table', { 'no-radius': [15, 20].includes(where.roleType) }]">
                      <common-table
                        :columns="columns"
                        :where="where"
                        :showToolTotal="false"
                        showTableTool
                        rowId="roleId"
                        ref="tableRef"
                        url="/sysRole/page"
                        fieldBusinessCode="ROLE_TABLE"
                      >
                        <template #toolLeft>
                          <a-input
                            v-model:value="where.searchText"
                            placeholder="名称、编码（回车搜索）"
                            @pressEnter="reload"
                            class="search-input"
                            :bordered="false"
                          >
                            <template #prefix>
                              <icon-font iconClass="icon-opt-search" />
                            </template>
                          </a-input>
                        </template>
                        <template #bodyCell="{ column, record }">
                          <!-- 姓名 -->
                          <template v-if="column.dataIndex == 'roleName'">
                            <a @click="openAddEdit(record)">{{ record.roleName }}</a>
                          </template>
                          <!-- 类型 -->
                          <template v-if="column.dataIndex == 'roleType'">
                            <span v-if="record.roleType == 10">系统角色</span>
                            <span v-if="record.roleType == 15">业务角色</span>
                            <span v-if="record.roleType == 20">公司角色</span>
                          </template>
                          <!-- 操作 -->
                          <template v-if="column.key == 'action'">
                            <a-space :size="16">
                              <icon-font
                                iconClass="icon-opt-bianji"
                                font-size="24px"
                                title="编辑"
                                color="#60666b"
                                v-permission="['DELETE_ROLE']"
                                @click="openAddEdit(record)"
                              />
                              <icon-font
                                iconClass="icon-opt-shanchu"
                                font-size="24px"
                                title="删除"
                                color="#60666b"
                                v-permission="['EDIT_ROLE']"
                                @click="remove(record)"
                              />
                            </a-space>
                          </template>
                        </template>
                      </common-table>
                    </div>
                  </template>
                </guns-split-layout>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增编辑弹框 -->
    <RoleAddEdit
      v-model:visible="showEdit"
      v-if="showEdit"
      :roleType="where.roleType"
      :roleCategoryId="where.roleCategoryId"
      :data="current"
      @done="reload"
      :companyData="companyData"
    />
  </div>
</template>

<script setup name="AuthRole">
import { RoleApi } from './api/RoleApi';
import { ref, createVNode, onMounted, defineAsyncComponent } from 'vue';
import { message, Modal } from 'ant-design-vue/es';
import RoleAddEdit from './components/role-add-edit.vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

const RoleTree = defineAsyncComponent(() => import('./components/role-tree.vue'));

defineOptions({
  name: 'AuthRole'
});

// 表格配置
const columns = ref([
  {
    key: 'index',
    title: '序号',
    width: 48,
    align: 'center',
    isShow: true,
    hideInSetting: true
  },
  {
    dataIndex: 'roleName',
    title: '角色名称',
    ellipsis: true,
    width: 200,
    isShow: true
  },
  {
    dataIndex: 'roleType',
    title: '角色类型',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'roleCompanyIdWrapper',
    title: '角色所属公司',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'roleCode',
    title: '角色编码',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'roleSort',
    title: '排序',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'createTime',
    title: '创建时间',
    width: 150,
    isShow: true
  },
  {
    key: 'action',
    title: '操作',
    width: 100,
    isShow: true
  }
]);
// ref
const tableRef = ref(null);

// 搜索条件
const where = ref({
  searchText: '',
  roleType: '',
  roleCategoryId: undefined
});
// 当前行数据
const current = ref(null);
// 是否显示新增编辑弹框
const showEdit = ref(false);

const companyData = ref(null);

onMounted(() => {});

// 更多点击
const moreClick = ({ key }) => {
  if (key == '1') {
    batchDelete();
  }
};

const treeSelect = node => {
  where.value.roleCategoryId = node.id;
  reload();
};

const getCompanyData = data => {
  if (where.value.roleType == 20) {
    companyData.value = data;
    where.value.roleCompanyId = data?.companyId;
    reload();
  }
};

// 删除分类
const delRoleType = node => {
  if (where.value.roleCategoryId == node.id) {
    where.value.roleCategoryId = undefined;
    reload();
  }
};

// 点击搜索
const reload = () => {
  tableRef.value.reload();
};

const roleTypeChange = () => {
  where.value.roleCategoryId = undefined;
  where.value.roleCompanyId = undefined;
  if (where.value.roleType != 20) reload();
};

// 新增编辑点击
const openAddEdit = record => {
  current.value = record;
  showEdit.value = true;
};

// 删除单个
const remove = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选中的角色吗?',
    icon: createVNode(ExclamationCircleOutlined),
    maskClosable: true,
    onOk: async () => {
      const res = await RoleApi.delete({ roleId: record.roleId });
      message.success(res.message);
      reload();
    }
  });
};

// 批量删除
const batchDelete = () => {
  if (tableRef.value.selectedRowList && tableRef.value.selectedRowList.length == 0) {
    return message.warning('请选择需要删除的角色');
  }
  Modal.confirm({
    title: '提示',
    content: '确定要删除选中的角色吗?',
    icon: createVNode(ExclamationCircleOutlined),
    maskClosable: true,
    onOk: async () => {
      const res = await RoleApi.batchDelete({ roleIdList: tableRef.value.selectedRowList });
      message.success(res.message);
      reload();
    }
  });
};
</script>

<style scoped lang="less">
.role-table {
  width: 100%;
  height: 100%;
}
:deep(.no-radius) {
  .table-tool {
    border-top-left-radius: 0px;
  }
}
</style>
