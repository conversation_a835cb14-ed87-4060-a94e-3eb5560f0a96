package cn.stylefeng.roses.kernel.sync.controller;

import cn.stylefeng.roses.kernel.apiauth.api.annotations.ApiAuth;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import cn.stylefeng.roses.kernel.sync.pojo.OrganizationSyncVo;
import cn.stylefeng.roses.kernel.sync.pojo.PositionSyncVo;
import cn.stylefeng.roses.kernel.sync.pojo.UserOrgSyncVo;
import cn.stylefeng.roses.kernel.sync.pojo.UserSyncVo;
import cn.stylefeng.roses.kernel.sync.service.SyncOrganizationService;
import cn.stylefeng.roses.kernel.sync.service.SyncPositionService;
import cn.stylefeng.roses.kernel.sync.service.SyncUserOrgService;
import cn.stylefeng.roses.kernel.sync.service.SyncUserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人力资源-分批同步数据
 *
 * <AUTHOR>
 * @since 2023/10/29 22:52
 */
@RestController
@ApiResource(name = "人力资源-分批同步数据")
public class HrSyncBatchController {

    @Resource
    private SyncOrganizationService syncOrganizationService;

    @Resource
    private SyncPositionService syncPositionService;

    @Resource
    private SyncUserService syncUserService;

    @Resource
    private SyncUserOrgService syncUserOrgService;

    /**
     * 按批次同步组织机构数据
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "按批次同步组织机构数据", path = "/hrSyncBatch/organization", requiredLogin = false)
    @ApiAuth
    public ResponseData<?> organization(@RequestBody BaseRequest baseRequest) {
        PageResult<OrganizationSyncVo> pageOrganization = syncOrganizationService.getPageOrganization(baseRequest);
        return new SuccessResponseData<>(pageOrganization);
    }

    /**
     * 按批次同步职位信息
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "按批次同步职位信息", path = "/hrSyncBatch/position", requiredLogin = false)
    @ApiAuth
    public ResponseData<PageResult<PositionSyncVo>> position(@RequestBody BaseRequest baseRequest) {
        PageResult<PositionSyncVo> pagePosition = syncPositionService.getPagePosition(baseRequest);
        return new SuccessResponseData<>(pagePosition);
    }

    /**
     * 按批次同步用户数据
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "按批次同步用户数据", path = "/hrSyncBatch/user", requiredLogin = false)
    @ApiAuth
    public ResponseData<PageResult<UserSyncVo>> user(@RequestBody BaseRequest baseRequest) {
        PageResult<UserSyncVo> pageUsers = syncUserService.getPageUsers(baseRequest);
        return new SuccessResponseData<>(pageUsers);
    }

    /**
     * 按批次同步用户和组织机构关联数据
     *
     * <AUTHOR>
     * @since 2023/10/29 22:47
     */
    @PostResource(name = "按批次同步用户和组织机构关联数据", path = "/hrSyncBatch/userOrgLink", requiredLogin = false)
    @ApiAuth
    public ResponseData<PageResult<UserOrgSyncVo>> userOrgLink(@RequestBody BaseRequest baseRequest) {
        PageResult<UserOrgSyncVo> pageUserOrgs = syncUserOrgService.getPageUserOrgs(baseRequest);
        return new SuccessResponseData<>(pageUserOrgs);
    }

}
