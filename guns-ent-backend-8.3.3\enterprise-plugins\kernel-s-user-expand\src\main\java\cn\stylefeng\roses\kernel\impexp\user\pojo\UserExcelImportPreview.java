package cn.stylefeng.roses.kernel.impexp.user.pojo;

import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用在excel导入的实体
 * <p>
 * 一共15列，每个字段都有对应的映射关系
 *
 * <AUTHOR>
 * @since 2024-02-04 14:46
 */
@Data
public class UserExcelImportPreview {

    /**
     * 主键
     */
    @ExcelProperty(index = 13)
    @ChineseDescription("主键")
    private ExcelLineParseResult userId;

    /**
     * excel导入的序号
     */
    @ExcelProperty(index = 0)
    @ChineseDescription("excel导入的序号")
    private ExcelLineParseResult number;

    /**
     * 姓名
     */
    @ExcelProperty(index = 1)
    @ChineseDescription("姓名")
    private ExcelLineParseResult realName;

    /**
     * 昵称
     */
    @ExcelProperty(index = 2)
    @ChineseDescription("昵称")
    private ExcelLineParseResult nickName;

    /**
     * 账号
     */
    @ExcelProperty(index = 3)
    @ChineseDescription("账号")
    private ExcelLineParseResult account;

    /**
     * 密码
     */
    @ExcelProperty(index = 4)
    @ChineseDescription("密码")
    private ExcelLineParseResult password;

    /**
     * 生日，格式YYYY-MM-DD
     */
    @ExcelProperty(index = 5)
    @ChineseDescription("生日")
    private ExcelLineParseResult birthday;

    /**
     * 性别：男和女
     */
    @ExcelProperty(index = 6)
    @ChineseDescription("性别：男和女")
    private ExcelLineParseResult sex;

    /**
     * 邮箱
     */
    @ExcelProperty(index = 7)
    @ChineseDescription("邮箱")
    private ExcelLineParseResult email;

    /**
     * 手机
     */
    @ExcelProperty(index = 8)
    @ChineseDescription("手机")
    private ExcelLineParseResult phone;

    /**
     * 电话
     */
    @ExcelProperty(index = 9)
    @ChineseDescription("电话")
    private ExcelLineParseResult tel;

    /**
     * 状态-正常和冻结
     */
    @ExcelProperty(index = 10)
    @ChineseDescription("状态-正常和冻结")
    private ExcelLineParseResult statusFlag;

    /**
     * 用户的排序
     */
    @ExcelProperty(index = 11)
    @ChineseDescription("用户的排序")
    private ExcelLineParseResult userSort;

    /**
     * 用户工号
     */
    @ExcelProperty(index = 12)
    @ChineseDescription("用户工号")
    private ExcelLineParseResult employeeNumber;

    /**
     * 对接外部主数据的用户id
     */
    @ExcelProperty(index = 14)
    @ChineseDescription("对接外部主数据的用户id")
    private ExcelLineParseResult masterUserId;

    /**
     * 这条记录将会以什么类型录入到数据库，包括：新增、修改、删除
     */
    @ChineseDescription("这条记录将会以什么类型录入到数据库，包括：新增、修改、删除")
    private ExcelLineParseResult operateType;

}
