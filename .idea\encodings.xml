<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/api-auth-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-api-auth/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/conversion-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/conversion-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/conversion-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/conversion-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/conversion-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-conversion/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/erp-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-erp/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/license-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/license-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/license-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/license-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/license-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/license-sdk/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-license/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-feign-consumer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-feign-consumer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-feign-consumer/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-feign-provider/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-feign-provider/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-feign-provider/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-gateway/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-monitor-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-monitor-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-monitor-server/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-tran-message/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-tran-message/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-project-tran-message/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-sdk-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-sdk-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-sdk-core/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-sdk-loadbalancer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-sdk-loadbalancer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-sdk-loadbalancer/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/account-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/account-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/account-service/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/business-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/business-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/business-service/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/order-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/order-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/order-service/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/storage-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/storage-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-seata-demo/storage-service/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/micro-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-micro/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/oauth2-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-oauth2/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-sdk/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/pay-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-pay/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/saas-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-saas/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/sanyuan-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sanyuan/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/sharding-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/sharding-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/sharding-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/sharding-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/sharding-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/sharding-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sharding-jdbc/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/ca-server-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-sso/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-business/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-temp-secret/temp-secret-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-api/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-sdk-memory/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-sdk-memory/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-sdk-memory/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-sdk-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-sdk-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-sdk-redis/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-d-websocket/websocket-spring-boot-starter/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-ent-integration/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-ent-integration/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-ent-integration/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-mobile/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-mobile/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-mobile/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-user-expand/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/kernel-s-user-expand/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/enterprise-plugins/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/guns-master/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/guns-master/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/guns-master/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-api/src/main/java/cn/stylefeng/roses/kernel/erp/api/modular/inventory/constants/InventoryConstants.java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-api/src/main/java/cn/stylefeng/roses/kernel/erp/api/modular/inventory/exception/InventoryExceptionEnum.java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/erp-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/kernel-d-erp/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-a-rule/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-a-rule/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/auth-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/auth-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/auth-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/auth-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/auth-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/auth-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-auth/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-sdk-memory/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-sdk-memory/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-sdk-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-sdk-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-spring-boot-starter-memory/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-spring-boot-starter-memory/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-spring-boot-starter-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/cache-spring-boot-starter-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-cache/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-sdk-map/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-sdk-map/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-sdk-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-sdk-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/config-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-config/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-sdk-flyway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-sdk-flyway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-sdk-mp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-sdk-mp/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/db-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-db/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/ds-container-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-ds-container/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-sdk-aliyun/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-sdk-aliyun/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-sdk-java/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-sdk-java/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/email-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-email/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/event-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/event-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/event-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/event-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/event-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/event-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-event/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-aliyun/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-aliyun/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-local/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-local/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-minio/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-minio/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-qingyun/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-qingyun/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-tencent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-sdk-tencent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/file-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-file/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/groovy-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/groovy-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/groovy-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/groovy-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/groovy-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/groovy-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-groovy/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/i18n-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-i18n/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-jwt/jwt-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-jwt/jwt-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-jwt/jwt-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-jwt/jwt-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-jwt/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-jwt/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business-login-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business-login-log/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business-requestapi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business-requestapi/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/log-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-log/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-office/office-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-office/office-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-office/office-sdk-excel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-office/office-sdk-excel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-office/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-office/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/pinyin-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/pinyin-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/pinyin-sdk-pinyin4j/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/pinyin-sdk-pinyin4j/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/pinyin-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/pinyin-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-pinyin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/scanner-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/scanner-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/scanner-sdk-scanner/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/scanner-sdk-scanner/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/scanner-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/scanner-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-scanner/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-allow-cors/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-allow-cors/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-black-white/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-black-white/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-captcha/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-captcha/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-clear-threadlocal/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-clear-threadlocal/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-count/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-count/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-guomi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-guomi/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-request-encrypt-and-decode/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-request-encrypt-and-decode/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-xss/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-sdk-xss/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/security-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-business-validation/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-business-validation/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-sdk-aliyun/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-sdk-aliyun/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-sdk-tencent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-sdk-tencent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/sms-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-sms/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-sdk-hutool/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-sdk-hutool/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-timer/timer-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-tree/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-tree/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/validator-api-table-unique/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/validator-api-table-unique/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/validator-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/validator-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/validator-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-validator/validator-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-field-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-field-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-d-wrapper/wrapper-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/monitor-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/monitor-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/monitor-business-system-info/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/monitor-business-system-info/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/monitor-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/monitor-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-o-monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/demo-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/demo-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/demo-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/demo-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/demo-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/demo-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-demo/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-city-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-city-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/dict-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-dict/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/group-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/group-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/group-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/group-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/group-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/group-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-group/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/stat-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/stat-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/stat-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/stat-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/stat-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-stat/stat-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-business-hr/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-business-hr/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-business-permission/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-business-permission/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-business-portal/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-business-portal/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-system/system-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/user-favorite-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/user-favorite-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/user-favorite-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/user-favorite-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/user-favorite-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/kernel-s-user-favorite/user-favorite-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/roses-master/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/guns-ent-backend-8.3.3/src/main/resources" charset="UTF-8" />
  </component>
</project>