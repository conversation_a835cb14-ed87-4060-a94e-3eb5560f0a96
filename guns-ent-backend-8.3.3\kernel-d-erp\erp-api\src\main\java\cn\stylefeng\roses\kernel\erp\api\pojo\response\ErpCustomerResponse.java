package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户响应参数
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpCustomerResponse extends BaseResponse {

    /**
     * 客户ID
     */
    @ChineseDescription("客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @ChineseDescription("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ChineseDescription("客户名称")
    private String customerName;

    /**
     * 客户简称
     */
    @ChineseDescription("客户简称")
    private String customerShortName;

    /**
     * 客户类型（ENTERPRISE-企业，INDIVIDUAL-个人，RETAIL-零售）
     */
    @ChineseDescription("客户类型")
    private String customerType;

    /**
     * 客户类型名称
     */
    @ChineseDescription("客户类型名称")
    private String customerTypeName;

    /**
     * 客户等级（DIAMOND-钻石，GOLD-黄金，SILVER-白银，BRONZE-青铜）
     */
    @ChineseDescription("客户等级")
    private String customerLevel;

    /**
     * 客户等级名称
     */
    @ChineseDescription("客户等级名称")
    private String customerLevelName;

    /**
     * 所属区域ID
     */
    @ChineseDescription("所属区域ID")
    private Long regionId;

    /**
     * 所属区域名称
     */
    @ChineseDescription("所属区域名称")
    private String regionName;

    /**
     * 联系人
     */
    @ChineseDescription("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @ChineseDescription("联系电话")
    private String contactPhone;

    /**
     * 手机号码
     */
    @ChineseDescription("手机号码")
    private String contactMobile;

    /**
     * 邮箱地址
     */
    @ChineseDescription("邮箱地址")
    private String contactEmail;

    /**
     * 联系地址
     */
    @ChineseDescription("联系地址")
    private String contactAddress;

    /**
     * 营业执照号
     */
    @ChineseDescription("营业执照号")
    private String businessLicenseNo;

    /**
     * 税务登记号
     */
    @ChineseDescription("税务登记号")
    private String taxNo;

    /**
     * 开户银行
     */
    @ChineseDescription("开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @ChineseDescription("银行账号")
    private String bankAccount;

    /**
     * 信用额度
     */
    @ChineseDescription("信用额度")
    private BigDecimal creditLimit;

    /**
     * 已用额度
     */
    @ChineseDescription("已用额度")
    private BigDecimal usedCredit;

    /**
     * 可用额度（信用额度 - 已用额度）
     */
    @ChineseDescription("可用额度")
    private BigDecimal availableCredit;

    /**
     * 账期天数
     */
    @ChineseDescription("账期天数")
    private Integer paymentTerms;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，FROZEN-冻结）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 状态名称
     */
    @ChineseDescription("状态名称")
    private String statusName;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 客户关联的区域ID列表
     */
    @ChineseDescription("客户关联的区域ID列表")
    private List<Long> regionIds;

    /**
     * 客户关联的区域信息列表
     */
    @ChineseDescription("客户关联的区域信息列表")
    private List<ErpRegionResponse> regionList;
}