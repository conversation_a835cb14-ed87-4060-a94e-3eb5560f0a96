package cn.stylefeng.roses.kernel.conversion.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.conversion.api.constants.ConversionConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * 文档转化异常
 *
 * <AUTHOR>
 * @date 2021/8/26 14:11
 */
public class ConversionException extends ServiceException {

    public ConversionException(AbstractExceptionEnum exception, Object... params) {
        super(ConversionConstants.CONVERSION_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public ConversionException(AbstractExceptionEnum exception) {
        super(ConversionConstants.CONVERSION_MODULE_NAME, exception);
    }

}
