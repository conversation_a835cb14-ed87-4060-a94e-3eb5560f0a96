package cn.stylefeng.roses.seata.demo.storage.modular.service;

import io.seata.rm.tcc.api.BusinessActionContext;
import io.seata.rm.tcc.api.BusinessActionContextParameter;
import io.seata.rm.tcc.api.LocalTCC;
import io.seata.rm.tcc.api.TwoPhaseBusinessAction;

/**
 * TCC方式实现扣减库存的方法
 *
 * <AUTHOR>
 * @date 2021/10/13 21:36
 */
@LocalTCC
public interface TccSubStorageService {

    /**
     * TCC方式扣减库存主方法
     * <p>
     * TwoPhaseBusinessAction注解的name属性是tcc的bean名称，全局唯一
     * BusinessActionContextParameter注解 传递参数到二阶段中
     *
     * <AUTHOR>
     * @date 2021/10/13 21:39
     */
    @TwoPhaseBusinessAction(name = "subStorageBegin", commitMethod = "subStorageConfirm", rollbackMethod = "subStorageCancel")
    void subStorageTry(@BusinessActionContextParameter(paramName = "commodityCode") String commodityCode,
                       @BusinessActionContextParameter(paramName = "count") int count);

    /**
     * 确认方法
     *
     * <AUTHOR>
     * @date 2021/10/13 21:44
     */
    boolean subStorageConfirm(BusinessActionContext context);

    /**
     * 回滚方法
     *
     * <AUTHOR>
     * @date 2021/10/13 21:44
     */
    boolean subStorageCancel(BusinessActionContext context);

}