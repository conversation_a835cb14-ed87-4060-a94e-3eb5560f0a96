<template>
  <div class="guns-body guns-body-card">
    <a-card title="机构选择" :bordered="false">
      <SelectOrgComponent
        v-model:value="selectValue"
        :disabled="disabled"
        :readonly="readonly"
        @onChange="onChange"
        :placeholder="placeholder"
        style="width: 300px"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 选中的值
const selectValue = ref('');
// 是否禁用
const disabled = ref(false);

// 是否只读
const readonly = ref(false);

// 选择提示
const placeholder = ref('请选择');

// 值改变
const onChange = () => {
  console.log(selectValue.value);
};
</script>

<style></style>
