package cn.stylefeng.roses.kernel.pay.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单开票记录实例类
 *
 * <AUTHOR>
 * @since 2024/06/21 16:49
 */
@TableName(value = "shop_order_invoice", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderInvoice extends BaseEntity {

    /**
     * 订单发票id
     */
    @TableId(value = "order_invoice_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("订单发票id")
    private Long orderInvoiceId;

    /**
     * 订单id集合，直接存json形式的集合
     */
    @TableField(value = "order_id_list", typeHandler = JacksonTypeHandler.class)
    @ChineseDescription("订单id集合，直接存json形式的集合")
    private List<Long> orderIdList;

    /**
     * 所属用户id
     */
    @TableField(value = "user_id")
    @ChineseDescription("所属用户id")
    private Long userId;

    /**
     * 发票抬头
     */
    @TableField("invoice_title")
    @ChineseDescription("发票抬头")
    private String invoiceTitle;

    /**
     * 发票纳税人识别号
     */
    @TableField("taxpayer_no")
    @ChineseDescription("发票纳税人识别号")
    private String taxpayerNo;

    /**
     * 单位地址
     */
    @TableField("address")
    @ChineseDescription("单位地址")
    private String address;

    /**
     * 单位电话
     */
    @TableField("phone")
    @ChineseDescription("单位电话")
    private String phone;

    /**
     * 开户行
     */
    @TableField("bank_name")
    @ChineseDescription("开户行")
    private String bankName;

    /**
     * 开户行账号
     */
    @TableField("bank_account_no")
    @ChineseDescription("开户行账号")
    private String bankAccountNo;

    /**
     * 发送邮箱信息
     */
    @TableField("send_email")
    @ChineseDescription("发送邮箱信息")
    private String sendEmail;

    /**
     * 备注信息
     */
    @TableField("description")
    @ChineseDescription("备注信息")
    private String description;

    /**
     * 开票金额
     */
    @TableField("total_amount")
    @ChineseDescription("开票金额")
    private BigDecimal totalAmount;

    /**
     * 订单开票状态：1-申请中，2-已开票
     */
    @TableField("order_invoice_status")
    @ChineseDescription("订单开票状态：1-申请中，2-已开票")
    private Integer orderInvoiceStatus;

}