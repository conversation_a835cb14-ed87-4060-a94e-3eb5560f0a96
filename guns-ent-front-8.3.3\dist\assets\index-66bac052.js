import{R as j,_ as Pe,r as g,s as be,o as Me,bs as Le,m as H,bP as Ke,bZ as Ue,b_ as Ce,k as ke,a as z,f as Y,w as d,d as r,b$ as Ve,b as u,ah as N,at as h,c as I,h as S,a2 as B,g as Z,a7 as Ze,M as Je,H as Re,bz as Qe,b2 as Ne,c0 as Ie,c1 as Se,bA as Oe,L as Fe,bR as De,aL as je,bi as He,t as Q,c2 as Ge,c3 as Ye,c4 as Be,c5 as Xe,a0 as _e,aR as Ae,aS as ye,O as fe,Q as ve,F as $e,e as et,c6 as Ee,c7 as tt,bD as qe,bF as at,aH as st,bH as ot,A as lt,W as nt,J as rt,l as it,u as dt,aa as ut,C as ct,B as mt}from"./index-18a1ea24.js";/* empty css              *//* empty css              */class gt{isMobile(){const t=navigator.userAgent,a=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"],s=t.indexOf("Safari")!==-1&&t.indexOf("Version")!==-1,i=t.indexOf("iPhone")!==-1&&t.indexOf("Version")!==-1,e=s&&!i&&"ontouchend"in document;let y=!1;if(e)y=!0;else for(let C=0,q=a.length;C<q;C++)if(t.indexOf(a[C])>0){y=!0;break}return y}formatEmpty(t,a){return this.isEmpty(t)?a!=null?a:"-":t}isEmpty(t,a=!1){let s=t===null||t==""||typeof t>"u";return a&&(s=this.formatEmpty(t)),s}isValid(t){return t!=null&&t!==""}isNumber(t){return typeof t=="number"&&isFinite(t)}getPasswordStrength(t){const a={lower:/[a-z]/,upper:/[A-Z]/,number:/[\d]/,character:/[~!@#$%^&*()_+=\-.,]/};let s=0;return a.lower.test(t)&&s++,a.upper.test(t)&&s++,a.number.test(t)&&s++,a.character.test(t)&&s++,s}random(){return((1+Math.random())*65536|0).toString(16).substring(1)}randomNumberInRange(t,a){return Math.round(Math.random()*(a-t)+t)}uid(t=!1,a){let s=(this.random()+this.random()+this.random()+this.random()+this.random()+this.random()+this.random()+this.random()).toLocaleUpperCase();return a&&(s=a+s),t?s.toUpperCase():s.toLowerCase()}replaceUrlParams(t,a){if(Object.keys(a).length>0){for(const s in a)if(a.hasOwnProperty(s)){const i=new RegExp("{"+s+"}","gi");t=t.replace(i,a[s])}}return t}px2Rem(t,a=16){return t&&Math.round(t/a*100)/100}convert2Rem(t){return L.isNumber(t)?"".concat(this.px2Rem(parseInt(t.toString())),"rem"):t?/%/g.test(t.toString())?t:"".concat(this.px2Rem(parseInt(t.toString())),"rem"):null}colorHex2Rgba(t,a=1){if(t){if(/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t)){if(t.length===4){let e="#";for(let y=1;y<4;y++)e+=t.slice(y,y+1).concat(t.slice(y,y+1));t=e}const i=[];for(let e=1;e<7;e+=2)i.push(parseInt("0x"+t.slice(e,e+2)));return"rgba(".concat(i.join(","),", ").concat(a,")")}}else return t}colorRgb2Hex(t){if(t){const a=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;if(/^(rgb|RGB)/.test(t)){const s=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let i="#";for(let e=0;e<s.length;e++){let y=Number(s[e]).toString(16);y==="0"&&(y+=y),i+=y}return i.length!==7&&(i=t),i}else if(a.test(t)){const s=t.replace(/#/,"").split("");if(s.length===6)return t;if(s.length===3){let i="#";for(let e=0;e<s.length;e+=1)i+=s[e]+s[e];return i}}}else return t}raf(t){return window.requestAnimationFrame(t)||window.setTimeout(t,1e3/60)}caf(t){window.cancelAnimationFrame(t)}getElementActualTopOrLeft(t,a="top"){let s=a==="left"?t.offsetLeft:t.offsetTop,i=t.offsetParent;for(;i!==null;)s+=a==="left"?i.offsetLeft:i.offsetTop,i=i.offsetParent;return s}scrollTop(t,a=0,s,i=800,e){const y=Math.abs(a-s),C=Math.ceil(y/i*50);let q;function T(v,b,V,P){if(v===b){q&&P.caf(q),e&&e();return}let R=v+V>b?b:v+V;v>b&&(R=v-V<b?b:v-V),t===window?window.scrollTo(R,R):t.scrollTop=R,q=P.raf(()=>T(R,b,V,P))}T(a,s,C,this)}back2top(t=0,a=1e3){const s=t!=null?t:document.documentElement.scrollTop||document.body.scrollTop;this.scrollTop(document.body,s,0,a)}on(t,a,s,i){document.addEventListener?t&&a&&s&&t.addEventListener(a,s,i):t&&a&&s&&t.attachEvent("on".concat(a),s)}off(t,a,s,i){document.addEventListener?t&&a&&s&&t.removeEventListener(a,s,i):t&&a&&s&&t.detachEvent("on".concat(a),s)}htmlEncode(t){let a=document.createElement("div");a.textContent!==null?a.textContent=t:a.innerText=t;const s=a.innerHTML;return a=null,s}}const L=new gt,ht="data:image/jpeg;base64,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";class pt{static getDragCaptcha(t){return j.get("/getDragCaptcha",t)}}class we{static add(t){return j.post("/sysTenant/register",t)}static edit(t){return j.post("/sysTenant/edit",t)}static delete(t){return j.post("/sysTenant/delete",t)}static async dropDownList(){return await j.getAndLoadData("/tenant/tenantDropdown")}static async sendEmail(t){return await j.post("/tenant/sendEmail",t)}static async submitTenantReg(t){return await j.post("/tenant/submitTenantReg",t)}}const At={class:"ym-captcha-modal-wrap"},yt={class:"ym-captcha-modal-embed"},ft={key:0,class:"ym-captcha-modal-loading"},xt={class:"ym-captcha-modal-loading-spinner"},vt={class:"load"},bt={class:"ym-captcha-modal-info"},Kt=["width","height"],wt=["width","height"],zt=["innerHTML"],kt={class:"ym-captcha-modal-panel"},Rt={class:"ym-captcha-modal-panel-action"},Et={__name:"index",props:{visible:Boolean,image:{type:String,default:void 0},position:{type:Object,default:void 0},mask:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!1},themeColor:{type:String,default:void 0},bgColor:{type:String,default:void 0},boxShadow:{type:Boolean,default:!0},boxShadowColor:{type:String,default:void 0},boxShadowBlur:{type:Number,default:6},maxTries:{type:Number,default:5},verifyParams:{type:Object,default:{}},verifyMethod:{type:String,default:"post"},verifyAction:{type:String,default:void 0},isRegister:{type:Boolean,default:!1},validate:Function,form:Object,ssoClientId:String,ssoCallback:String,goHome:Function},emits:["update:visible","done"],setup(X,{emit:t}){var ce;const a=X,s=t,i=g(!1),e=be({loading:!0,background:ht,ctx:{image:null,block:null},elements:{slider:null,block:null},coordinate:{x:0,y:0,offset:6},size:{width:374,height:200},block:{size:42,radius:8,PI:Math.PI,real:0},drag:{moving:!1,originX:0,originY:0,offset:0},time:{start:null,end:null},check:{tries:(ce=a.maxTries)!=null?ce:5,num:0,correct:!1,show:!1,tip:null,being:!1,value:null},_background:null}),y=g(),C=g(),q=g(),T=g(),v=g(),b=g(),V=g({mobile:/^1[3456789]\d{9}$/,url:/^((https|http|ftp|rtsp|mms)?:\/\/)(([0-9A-Za-z_!~*'().&=+$%-]+: )?[0-9A-Za-z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9A-Za-z_!~*'()-]+.)*([0-9A-Za-z][0-9A-Za-z-]{0,61})?[0-9A-Za-z].[A-Za-z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9A-Za-z_!~*'().;?:@&=+$,%#-]+)+\/?)$/,password:/^[A-Za-z0-9~!@#$%^&*()_+=\-.,]{6,32}$/,username:/^[a-zA-Z]{1}([a-zA-Z0-9]|[_]){3,15}$/,email:/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/}),P=g(!1),R=g({});Me(()=>{E(!0)});const E=(n=!1)=>{pt.getDragCaptcha().then(l=>{if(R.value=l.data,e._background="data:image/jpeg;base64,"+l.data.srcImage,n)p();else{e.loading=!0,G(),e.drag.moving=!1;const m=v.value;m.width=e.size.width,e.ctx.image.clearRect(0,0,e.size.width,e.size.height),e.ctx.block.clearRect(0,0,e.size.width,e.size.height),O()}})},p=()=>{f()},f=()=>{e.elements={slider:q.value,block:v.value},e.block.real=e.block.size+e.block.radius*2+2,G(),_(),L.on(e.elements.slider,"pointerdown",M),L.on(e.elements.slider,"touchstart",M),L.on(e.elements.slider,"pointermove",K),L.on(e.elements.slider,"touchmove",K),re()},_=()=>{const n=T.value,l=v.value,m=n?n.getContext("2d",{willReadFrequently:!0}):null,k=l?l.getContext("2d",{willReadFrequently:!0}):null;e.ctx={image:m,block:k},V.value.url.test(e._background)?xe(O):O()},xe=n=>{const l=new Image,m=document.createElement("canvas"),k=m.getContext("2d",{willReadFrequently:!0});l.crossOrigin="",l.src=e._background,l.onload=()=>{m.width=e.size.width,m.height=e.size.height,k.drawImage(l,0,0,e.size.width,e.size.height),e._background=m.toDataURL(),n&&n()}},W=n=>{s("update:visible",n)},G=()=>{var n;e.check={tries:(n=a.maxTries)!=null?n:5,num:0,being:!1,value:null,correct:!1,tip:"\u62D6\u52A8\u6ED1\u5757\u5C06\u60AC\u6D6E\u56FE\u50CF\u6B63\u786E\u62FC\u5408",show:!1}},O=()=>{const n=new Image,l=new Image;n.src=e._background,l.src="data:image/jpeg;base64,"+R.value.cutImage,n.onload=()=>$(n,"image"),l.onload=()=>$(l,"block")},$=(n,l)=>{l=="image"?e.ctx.image&&(e.ctx.image.drawImage(n,0,0,e.size.width,e.size.height),e.ctx.image.beginPath(),e.ctx.image.fillStyle="#FFF",e.ctx.image.shadowColor="transparent",e.ctx.image.shadowBlur=0,e.ctx.image.font="bold 24px MicrosoftYaHei",e.ctx.image.fillText("\u62D6\u52A8\u6ED1\u5757\u62FC\u5408\u56FE\u7247",12,30),e.ctx.image.font="16px MicrosoftYaHei",e.ctx.image.fillText("\u5C31\u80FD\u9A8C\u8BC1\u6210\u529F\u54E6",12,55),e.ctx.image.closePath()):e.ctx.block&&(e.ctx.block.drawImage(n,0,0,50,50),v.value.style.top=R.value.locationY+"px"),e.loading=!1};Le(()=>{J()});const J=()=>{L.off(e.elements.slider,"pointerdown",M),L.off(e.elements.slider,"touchstart",M),L.off(e.elements.slider,"pointermove",K),L.off(e.elements.slider,"touchmove",K),document.removeEventListener("mousedown",ee),document.removeEventListener("mousemove",F),document.removeEventListener("mouseup",ie)},ne=(n,l=null)=>{const m=n.getBoundingClientRect();return l&&m[l]?m[l]:m},re=()=>{document.addEventListener("mousedown",ee),document.addEventListener("mousemove",F),document.addEventListener("mouseup",ie)},ee=n=>{e.drag.moving&&(P.value=!0,M(n))},F=n=>{e.drag.moving&&P.value&&K(n)},ie=n=>{P.value&&(e.check.being=!1,P.value=!1,D())},M=n=>{const l=n.clientX||n.touches[0].clientX,m=ne(C.value),k=ne(q.value);e.drag.originX=Math.round(m.left*10)/10,e.drag.originY=Math.round(m.top*10)/10,e.drag.offset=Math.round((l-k.left)*10)/10,e.drag.moving=!0,e.time.start=Date.now()},K=n=>{if(!e.drag.moving||e.check.being)return;const l=n.clientX||n.touches[0].clientX,m=Math.round((l-e.drag.originX-e.drag.offset)*10)/10;if(m<0||m+54>=e.size.width)return!1;e.elements.slider.style.left="".concat(m,"px"),e.elements.block.style.left="".concat(m,"px"),e.check.value=m},D=()=>{e.drag.moving&&(e.time.end=Date.now(),te())},te=async()=>{const n=Math.round(e.check.value+e.coordinate.offset);if(e.check.being)return;e.check.being=!0;let l={verKey:R.value.key,verCode:n};a.isRegister?ae(l):se(l)},ae=n=>{i.value=!0,we.sendEmail({...n,email:a.form.email}).then(l=>{H.success("\u83B7\u53D6\u6210\u529F!"),s("done",n),e.check.being=!1,J(),ue("success")}).catch(()=>{E(),de()}).finally(()=>i.value=!1)},se=n=>{a.validate().then(()=>{i.value=!0;let l=JSON.parse(JSON.stringify(a.form));l=Object.assign(l,n),Ke.login(l).then(m=>{var k;H.success("\u767B\u5F55\u6210\u529F"),Ue((k=m==null?void 0:m.data)==null?void 0:k.token,!0),Ce(),e.check.being=!1,J(),ue("success"),a.goHome()}).catch(m=>{E(),de()}).finally(()=>{i.value=!1})})},de=()=>{e.elements.slider.style.left=0,e.elements.block.style.left=0,e.drag.originX=0,e.drag.originY=0},ue=(n,l)=>{n=="frequently"?H.warning("\u5DF2\u8FDE\u7EED\u9519\u8BEF\u8FBE ".concat(e.check.num," \u6B21\uFF0C\u8BF7\u7A0D\u5019\u518D\u8BD5")):W(!1)};return(n,l)=>{const m=ke("CloseCircleOutlined"),k=Ze,A=ke("ReloadOutlined"),o=Je;return z(),Y(o,{width:400,maskClosable:!1,visible:a.visible,"confirm-loading":i.value,forceRender:!0,"body-style":{padding:"0"},"onUpdate:visible":W,footer:null,style:{top:"50%","margin-top":"-231px"},closable:!1,onClose:l[1]||(l[1]=w=>W(!1))},{default:d(()=>[r(Ve,{name:"ym-anim-scale",appear:!0},{default:d(()=>{var w,me,ge,he,U,x,oe,pe;return[u("div",{class:"ym-captcha-modal-content",ref_key:"contentRef",ref:y,style:N({borderColor:(w=a.themeColor)!=null?w:null,background:(me=a.bgColor)!=null?me:null,boxShadow:a.boxShadow&&(a.boxShadowColor||a.themeColor)?"0 0 ".concat(h(L).convert2Rem(a.boxShadowBlur)," ").concat(a.boxShadowColor||a.themeColor):null})},[u("div",At,[u("div",yt,[e.loading?(z(),I("div",ft,[u("div",xt,[u("div",vt,[u("div",null,[u("div",null,[u("div",{style:N({borderColor:(ge=a.themeColor)!=null?ge:null})},null,4),u("div",{style:N({background:(he=a.themeColor)!=null?he:null})},null,4)])])])]),l[2]||(l[2]=u("div",{class:"ym-captcha-modal-loading-tip"},"\u6B63\u5728\u52A0\u8F7D\u9A8C\u8BC1\u7801 \xB7\xB7\xB7",-1))])):S("",!0),u("div",bt,[u("canvas",{width:e.size.width,height:e.size.height,ref_key:"imageRef",ref:T},null,8,Kt),u("canvas",{width:e.size.width,height:e.size.height,ref_key:"blockRef",ref:v},null,8,wt)]),u("div",{class:B("ym-captcha-modal-result ".concat(e.check.correct?"ym-captcha-modal-result-success":"ym-captcha-modal-result-error")),ref_key:"resultRef",ref:b,innerHTML:e.check.tip},null,10,zt)]),u("div",{ref_key:"sliderRef",ref:C,class:B("ym-captcha-modal-slider".concat(e.drag.moving?" ym-captcha-modal-slider-moving":""))},[u("div",{class:"ym-captcha-modal-slider-track",style:N({borderColor:(U=a.themeColor)!=null?U:null})},[u("span",{class:B("ym-captcha-modal-slider-track-tip".concat(e.drag.moving?" hide":""))},"\u62D6\u52A8\u5DE6\u8FB9\u6ED1\u5757\u5B8C\u6210\u4E0A\u65B9\u62FC\u56FE",2)],4),u("div",{class:"ym-captcha-modal-slider-btn",style:N({borderColor:(x=a.themeColor)!=null?x:null}),ref_key:"sliderBtnRef",ref:q},[u("div",{class:"ym-captcha-modal-slider-btn-icon",style:N({borderColor:(oe=a.themeColor)!=null?oe:null})},[l[3]||(l[3]=u("div",{class:"ym-captcha-modal-slider-btn-vertical"},null,-1)),u("div",{class:"ym-captcha-modal-slider-btn-horizontal",style:N({borderColor:(pe=a.themeColor)!=null?pe:null})},null,4)],4)],4)],2)]),u("div",kt,[u("div",Rt,[r(k,{overlayClassName:"ym-captcha-modal-tooltip",color:a.themeColor},{title:d(()=>l[4]||(l[4]=[Z("\u5173\u95ED\u9A8C\u8BC1")])),default:d(()=>[r(m,{onClick:l[0]||(l[0]=le=>W(!1))})]),_:1},8,["color"]),r(k,{overlayClassName:"ym-captcha-modal-tooltip",color:a.themeColor},{title:d(()=>l[5]||(l[5]=[Z("\u5237\u65B0\u9A8C\u8BC1")])),default:d(()=>[r(A,{onClick:E})]),_:1},8,["color"])])])],4)]}),_:1})]),_:1},8,["visible","confirm-loading"])}}},qt=Pe(Et,[["__scopeId","data-v-232b93bd"]]);const Pt={key:0,class:"login-info"},Mt={class:"login-title"},Ut={class:"login-subtitle"},Ct={key:0,class:"login-title"},Tt={key:1,class:"login-subtitle"},Wt={class:"login-body"},Lt={style:{"font-size":"24px","margin-bottom":"18px","font-weight":"bold"}},Vt={class:"register"},Zt={class:"login-input-group"},Jt=["src"],Qt={class:"login-input-group"},Nt={class:"register"},It={__name:"index",setup(X){const t=Re.useForm,a=Qe(),{currentRoute:s}=Ne(),{query:i}=Ie(),{t:e}=Se.useI18n(),{styleResponsive:y}=Oe(a),C=i==null?void 0:i.clientId,q=i==null?void 0:i.ssoCallback,T=g(Ge),v=g([]);let b=g({gunsMgrLoginBackgroundImg:"",gunsMgrFooterText:"",gunsMgrBeiUrl:"",gunsMgrBeiNo:"",gunsMgrName:"",gunsSubTitle:""});const V=g(null),P=g(Ye),R=g(Be),E=g(!1),p=be({account:"",password:"",verKey:"",verCode:"",tenantCode:null,rememberMe:!1}),f=be({}),_=g(""),xe=g(""),W=g(!1),G=g(!1),O=g(null),$=g(!1);let J=g({account:[{required:!0,message:e("login.username"),type:"string",trigger:"blur"}],password:[{required:!0,message:e("login.password"),type:"string",trigger:"blur"}],tenantCode:[{required:!1,message:"\u79DF\u6237\u4E0D\u80FD\u4E3A\u7A7A",type:"string",trigger:["blur","change"]}]});P.value&&(J.value.verCode=[{required:!0,message:e("login.code"),type:"string",trigger:"blur"}]);let ne=g({tenantName:[{required:!0,message:"\u79DF\u6237\u540D\u79F0",type:"string",trigger:"blur"}],tenantCode:[{required:!0,message:"\u79DF\u6237\u7F16\u7801",type:"string",trigger:"blur"}],email:[{required:!0,message:"\u90AE\u7BB1",type:"string",trigger:"blur"}],emailValidateCode:[{required:!0,message:"\u9A8C\u8BC1\u7801",type:"string",trigger:"blur"}],safePhone:[{required:!0,message:"\u7535\u8BDD",type:"string",trigger:"blur"}],password:[{required:!0,message:"\u5BC6\u7801",type:"string",trigger:"blur"}]});const{clearValidate:re,validate:ee,validateInfos:F}=t(p,Fe(()=>J.value));let ie=De();const M=g("1"),K=g(!1),D=g(!1);Me(async()=>{var o;if(te(),window.addEventListener("resize",te),T.value){const w=await we.dropDownList();$.value=w.selectFlag,v.value=(o=w==null?void 0:w.tenantList)!=null?o:[]}let A=await ie.loadThemeInfo();b.value=A});const te=()=>{window.innerWidth<=680?D.value=!0:D.value=!1};je(()=>{window.removeEventListener("resize",te)});const ae=()=>{const{query:A}=h(s);Xe(A.from)},se=()=>{ee().then(()=>{R.value?(G.value=!1,W.value=!0):ue()})},de=({target:A})=>{A.value=="1"?(J.value.tenantCode[0].required=!1,p!=null&&p.tenantCode&&delete p.tenantCode):A.value=="2"&&(p.tenantCode=null,J.value.tenantCode[0].required=!0),re()},ue=()=>{E.value=!0;let A=JSON.parse(JSON.stringify(p));Ke.login(A).then(o=>{var w;H.success("\u767B\u5F55\u6210\u529F"),Ue((w=o==null?void 0:o.data)==null?void 0:w.token,!0),Ce(),ae()}).finally(()=>{E.value=!1})},ce=()=>{Ke.getCaptcha().then(A=>{_.value=A.data.verImage,xe.value=A.data.verKey,p.verKey=A.data.verKey,re()}).catch(A=>{H.error(A.message)})};He()&&ae();const n=()=>{O.value.clearValidate(),K.value=!0},l=async()=>{const{email:A}=f;if(!A)return H.warning("\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A!");G.value=!0,W.value=!0},m=A=>{f.verKey=A.verKey},k=()=>{O.value.validate().then(()=>{we.submitTenantReg(f).then(async()=>{H.success("\u6CE8\u518C\u6210\u529F,\u7B49\u5F85\u7BA1\u7406\u5458\u5BA1\u6838!"),K.value=!1})})};return(A,o)=>{const w=ot,me=lt,ge=nt,he=rt,U=it,x=dt,oe=ut,pe=ct,le=mt,ze=Re,Te=_e,We=qt;return z(),I("div",{class:B(["login-wrapper",{"login-wrapper-responsive":h(y)}]),ref_key:"loginPage",ref:V},[D.value?S("",!0):(z(),I("div",Pt,[u("h1",Mt,Q(h(b).gunsMgrName),1),u("h4",Ut,Q(h(b).gunsSubTitle),1)])),r(Te,{class:"login-card",bordered:!1},{default:d(()=>[u("div",{class:B(["login-cover",{"pick-center":K.value}])},[D.value?(z(),I("h1",Ct,Q(h(b).gunsMgrName),1)):S("",!0),D.value?(z(),I("h4",Tt,Q(h(b).gunsSubTitle),1)):S("",!0)],2),u("div",Wt,[u("h4",Lt,Q(K.value?"\u79DF\u6237\u6CE8\u518C":h(e)("login.title")),1),Ae(r(me,{value:M.value,"onUpdate:value":o[0]||(o[0]=c=>M.value=c),size:"default",class:"radio-group",onChange:de},{default:d(()=>[u("div",{class:B(["tab-active",M.value=="1"?"tab1":"tab2"])},null,2),r(w,{value:"1"},{default:d(()=>o[15]||(o[15]=[Z("\u5BC6\u7801\u767B\u5F55")])),_:1,__:[15]}),r(w,{value:"2"},{default:d(()=>o[16]||(o[16]=[Z("\u79DF\u6237\u767B\u5F55")])),_:1,__:[16]})]),_:1},8,["value"]),[[ye,T.value&&!K.value]]),Ae(r(ze,{class:"login-form",style:N({marginTop:T.value?"":"50px"})},{default:d(()=>[Ae(r(x,fe(ve(h(F).tenantCode)),{default:d(()=>[$.value?(z(),Y(he,{key:0,"allow-clear":"",size:"large",value:p.tenantCode,"onUpdate:value":o[1]||(o[1]=c=>p.tenantCode=c),placeholder:"\u8BF7\u9009\u62E9\u79DF\u6237",class:"border-radius"},{default:d(()=>[(z(!0),I($e,null,et(v.value,c=>(z(),Y(ge,{key:c.tenantCode,value:c.tenantCode},{default:d(()=>[Z(Q(c.tenantName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])):(z(),Y(U,{key:1,size:"large","allow-clear":"",value:p.tenantCode,"onUpdate:value":o[2]||(o[2]=c=>p.tenantCode=c),class:"border-radius",placeholder:"\u8BF7\u8F93\u5165\u79DF\u6237"},null,8,["value"]))]),_:1},16),[[ye,M.value=="2"]]),r(x,fe(ve(h(F).account)),{default:d(()=>[r(U,{"allow-clear":"",size:"large",onKeydown:Ee(se,["enter"]),value:p.account,"onUpdate:value":o[3]||(o[3]=c=>p.account=c),placeholder:h(e)("login.username"),class:"border-radius"},{prefix:d(()=>[r(h(tt))]),_:1},8,["value","placeholder"])]),_:1},16),r(x,fe(ve(h(F).password)),{default:d(()=>[r(oe,{onKeydown:Ee(se,["enter"]),size:"large",value:p.password,"onUpdate:value":o[4]||(o[4]=c=>p.password=c),placeholder:h(e)("login.password"),class:"border-radius"},{prefix:d(()=>[r(h(qe))]),_:1},8,["value","placeholder"])]),_:1},16),r(x,null,{default:d(()=>[u("div",Vt,[r(pe,{checked:p.rememberMe,"onUpdate:checked":o[5]||(o[5]=c=>p.rememberMe=c)},{default:d(()=>o[17]||(o[17]=[Z(" 7\u5929\u514D\u767B\u9646 ")])),_:1,__:[17]},8,["checked"]),T.value&&M.value=="2"?(z(),I("a",{key:0,onClick:n},"\u79DF\u6237\u6CE8\u518C")):S("",!0)])]),_:1}),r(x,null,{default:d(()=>[r(le,{block:"",size:"large",type:"primary",loading:E.value,onClick:se,class:"border-radius",style:{height:"40px","margin-top":"20px"}},{default:d(()=>[Z(Q(E.value?h(e)("login.loading"):h(e)("login.login")),1)]),_:1},8,["loading"])]),_:1}),P.value?(z(),Y(x,fe(st({key:0},h(F).verCode)),{default:d(()=>[u("div",Zt,[r(U,{"allow-clear":"",size:"large",value:p.verCode,"onUpdate:value":o[6]||(o[6]=c=>p.verCode=c),placeholder:h(e)("login.code"),class:"border-radius"},{prefix:d(()=>[r(h(at))]),_:1},8,["value","placeholder"]),r(le,{class:"login-captcha",onClick:ce},{default:d(()=>[_.value?(z(),I("img",{key:0,src:_.value,alt:""},null,8,Jt)):S("",!0)]),_:1})])]),_:1},16)):S("",!0)]),_:1},8,["style"]),[[ye,!K.value]]),Ae(r(ze,{class:"login-form",model:f,rules:h(ne),ref_key:"registerRef",ref:O,style:{"margin-top":"40px"}},{default:d(()=>[r(x,{name:"tenantName"},{default:d(()=>[r(U,{"allow-clear":"",size:"large",value:f.tenantName,"onUpdate:value":o[7]||(o[7]=c=>f.tenantName=c),placeholder:"\u79DF\u6237\u540D\u79F0",class:"border-radius"},null,8,["value"])]),_:1}),r(x,{name:"tenantCode"},{default:d(()=>[r(U,{"allow-clear":"",size:"large",value:f.tenantCode,"onUpdate:value":o[8]||(o[8]=c=>f.tenantCode=c),placeholder:"\u79DF\u6237\u7F16\u7801",class:"border-radius"},null,8,["value"])]),_:1}),r(x,{name:"email"},{default:d(()=>[r(U,{"allow-clear":"",size:"large",value:f.email,"onUpdate:value":o[9]||(o[9]=c=>f.email=c),placeholder:"\u90AE\u7BB1",class:"border-radius"},null,8,["value"])]),_:1}),r(x,{name:"emailValidateCode"},{default:d(()=>[u("div",Qt,[r(U,{"allow-clear":"",size:"large",value:f.emailValidateCode,"onUpdate:value":o[10]||(o[10]=c=>f.emailValidateCode=c),placeholder:"\u9A8C\u8BC1\u7801",class:"border-radius"},null,8,["value"]),r(le,{class:"login-captcha",size:"large",type:"primary",onClick:l,loading:E.value},{default:d(()=>o[18]||(o[18]=[Z("\u83B7\u53D6\u9A8C\u8BC1\u7801")])),_:1,__:[18]},8,["loading"])])]),_:1}),r(x,{name:"safePhone"},{default:d(()=>[r(U,{"allow-clear":"",size:"large",value:f.safePhone,"onUpdate:value":o[11]||(o[11]=c=>f.safePhone=c),placeholder:"\u7535\u8BDD",class:"border-radius"},null,8,["value"])]),_:1}),r(x,{name:"password"},{default:d(()=>[r(oe,{size:"large",value:f.password,"onUpdate:value":o[12]||(o[12]=c=>f.password=c),placeholder:"\u5BC6\u7801",class:"border-radius"},{prefix:d(()=>[r(h(qe))]),_:1},8,["value"])]),_:1}),r(x,null,{default:d(()=>[u("div",Nt,[o[19]||(o[19]=u("span",null,null,-1)),u("a",{onClick:o[13]||(o[13]=c=>K.value=!1)},"\u8FD4\u56DE\u767B\u5F55")])]),_:1}),r(x,null,{default:d(()=>[r(le,{block:"",size:"large",type:"primary",loading:E.value,onClick:k,class:"border-radius",style:{height:"40px"}},{default:d(()=>[Z(Q(E.value?"\u6CE8\u518C\u4E2D":"\u6CE8\u518C"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"]),[[ye,K.value]])])]),_:1}),W.value?(z(),Y(We,{key:1,visible:W.value,"onUpdate:visible":o[14]||(o[14]=c=>W.value=c),validate:h(ee),form:K.value?f:p,ssoClientId:h(C),ssoCallback:h(q),goHome:ae,onDone:m,isRegister:G.value},null,8,["visible","validate","form","ssoClientId","ssoCallback","isRegister"])):S("",!0)],2)}}},Dt=Pe(It,[["__scopeId","data-v-06bf5abc"]]);export{Dt as default};
