package cn.stylefeng.roses.kernel.ca.server.core.loginuser.logincode;

import cn.hutool.cache.impl.TimedCache;
import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import cn.stylefeng.roses.kernel.cache.memory.AbstractMemoryCacheOperator;


/**
 * 基于内存的登录loginCode缓存
 * <p>
 * key：    SSO LoginCode，是一个临时的密码校验成功的一个凭证
 * value：  CaLoginUser，单点登录用户信息的一个标识
 *
 * <AUTHOR>
 * @date 2021/1/28 14:25
 */
public class MemoryLoginCodeCache extends AbstractMemoryCacheOperator<CaLoginUser> {

    public MemoryLoginCodeCache(TimedCache<String, CaLoginUser> timedCache) {
        super(timedCache);
    }

    @Override
    public String getCommonKeyPrefix() {
        return CaServerConstants.CA_LOGIN_CODE_CACHE_PREFIX;
    }

}
