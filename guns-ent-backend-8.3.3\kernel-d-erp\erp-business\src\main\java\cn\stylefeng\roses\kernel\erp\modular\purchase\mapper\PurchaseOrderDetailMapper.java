package cn.stylefeng.roses.kernel.erp.modular.purchase.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PurchaseOrderDetail;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PurchaseOrderDetailResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ProductPurchaseHistoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ProductPurchaseStatsResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.SupplierProductStatsResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单明细Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
public interface PurchaseOrderDetailMapper extends BaseMapper<PurchaseOrderDetail> {

    /**
     * 根据入库单ID查询明细列表
     *
     * @param orderId 入库单ID
     * @return 明细列表
     */
    @Select("SELECT * FROM erp_purchase_order_detail WHERE order_id = #{orderId}")
    List<PurchaseOrderDetail> getByOrderId(@Param("orderId") Long orderId);

    /**
     * 统计商品关联的采购入库单明细数量
     *
     * @param productId 商品ID
     * @return 明细数量
     */
    @Select("SELECT COUNT(1) FROM erp_purchase_order_detail WHERE product_id = #{productId}")
    Long countByProductId(@Param("productId") Long productId);

    /**
     * 根据入库单ID删除明细
     *
     * @param orderId 入库单ID
     * @return 删除数量
     */
    int deleteByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据入库单ID查询明细列表（包含商品信息）
     *
     * @param orderId 入库单ID
     * @return 明细列表（包含商品信息）
     */
    List<PurchaseOrderDetailResponse> getDetailsByOrderIdWithProduct(@Param("orderId") Long orderId);

    /**
     * 根据商品ID查询采购历史
     *
     * @param productId 商品ID
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 采购历史列表
     */
    List<ProductPurchaseHistoryResponse> getPurchaseHistoryByProduct(@Param("productId") Long productId,
                                                                   @Param("startDate") LocalDate startDate,
                                                                   @Param("endDate") LocalDate endDate);

    /**
     * 统计商品的采购数量和金额
     *
     * @param productId 商品ID（可选）
     * @param supplierId 供应商ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 商品采购统计列表
     */
    List<ProductPurchaseStatsResponse> getProductPurchaseStats(@Param("productId") Long productId,
                                                             @Param("supplierId") Long supplierId,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * 获取供应商的商品采购明细统计
     *
     * @param supplierId 供应商ID（可选）
     * @param productId 商品ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 供应商商品统计列表
     */
    List<SupplierProductStatsResponse> getSupplierProductStats(@Param("supplierId") Long supplierId,
                                                             @Param("productId") Long productId,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * 批量删除明细
     *
     * @param orderIds 入库单ID列表
     * @return 删除数量
     */
    int deleteByOrderIds(@Param("orderIds") List<Long> orderIds);

}