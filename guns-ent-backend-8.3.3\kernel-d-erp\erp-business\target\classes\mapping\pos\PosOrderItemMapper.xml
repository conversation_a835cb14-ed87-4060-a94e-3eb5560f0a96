<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosOrderItemMapper">

    <!-- 根据订单ID查询订单项 -->
    <select id="findByOrderId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem">
        SELECT * FROM pos_order_item
        WHERE order_id = #{orderId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据商品ID查询订单项 -->
    <select id="findByProductId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem">
        SELECT poi.* FROM pos_order_item poi
        INNER JOIN pos_order po ON poi.order_id = po.order_id
        WHERE poi.product_id = #{productId}
        AND po.del_flag = 'N'
        <if test="startTime != null">
            AND poi.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND poi.create_time &lt;= #{endTime}
        </if>
        ORDER BY poi.create_time DESC
    </select>

    <!-- 统计商品销售情况 -->
    <select id="getProductSalesStats" resultType="java.util.Map">
        SELECT 
            poi.product_id,
            poi.product_name,
            poi.product_code,
            poi.pricing_type,
            COUNT(*) as order_count,
            SUM(poi.quantity) as total_quantity,
            SUM(poi.total_price) as total_sales_amount,
            AVG(poi.unit_price) as avg_unit_price
        FROM pos_order_item poi
        INNER JOIN pos_order po ON poi.order_id = po.order_id
        WHERE poi.create_time >= #{startTime}
        AND poi.create_time &lt;= #{endTime}
        AND po.del_flag = 'N'
        AND po.order_status = 'PAID'
        <if test="productId != null">
            AND poi.product_id = #{productId}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND poi.pricing_type = #{pricingType}
        </if>
        GROUP BY poi.product_id, poi.product_name, poi.product_code, poi.pricing_type
        ORDER BY total_sales_amount DESC
    </select>

    <!-- 获取热销商品排行 -->
    <select id="getTopSellingProducts" resultType="java.util.Map">
        SELECT 
            poi.product_id,
            poi.product_name,
            poi.product_code,
            poi.pricing_type,
            poi.unit,
            COUNT(*) as order_count,
            SUM(poi.quantity) as total_quantity,
            SUM(poi.total_price) as total_sales_amount
        FROM pos_order_item poi
        INNER JOIN pos_order po ON poi.order_id = po.order_id
        WHERE poi.create_time >= #{startTime}
        AND poi.create_time &lt;= #{endTime}
        AND po.del_flag = 'N'
        AND po.order_status = 'PAID'
        GROUP BY poi.product_id, poi.product_name, poi.product_code, poi.pricing_type, poi.unit
        ORDER BY total_quantity DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量删除订单项 -->
    <delete id="batchDeleteByOrderIds">
        DELETE FROM pos_order_item
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

</mapper>