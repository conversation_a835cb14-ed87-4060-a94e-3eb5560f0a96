package cn.stylefeng.roses.kernel.websocket.redis;

import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import cn.stylefeng.roses.kernel.websocket.api.constants.WebsocketConstants;
import cn.stylefeng.roses.kernel.websocket.redis.action.Action;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * Redis
 *
 * <AUTHOR>
 * @since 2024-01-15 15:52
 */
@Getter
@Slf4j
public class DefaultRedisReceiver implements RedisReceiver {

    @Override
    public void receiveMessage(String message) {
        log.info("接收到redis消息：" + message);

        // 消息中带有action标识的字段，说明是websocket的消息
        JSONObject object = JSONObject.parseObject(JSONObject.parse(message).toString());
        if (!object.containsKey(Action.ACTION)) {
            return;
        }

        // action的name
        String actionName = object.getString(Action.ACTION);
        Action action = getAction(actionName);

        // 执行具体的业务处理
        action.doMessage(getWebSocketManager(), object);
    }

    /**
     * 获取容器中的对应的处理器
     *
     * <AUTHOR>
     * @since 2024-01-15 15:32
     */
    protected Action getAction(String actionName) {
        return SpringUtil.getBean(actionName, Action.class);
    }

    /**
     * 获取会话管理器
     *
     * <AUTHOR>
     * @since 2024-01-15 15:32
     */
    protected WebSocketManagerApi getWebSocketManager() {
        return SpringUtil.getBean(WebsocketConstants.WEBSOCKET_MANAGER_NAME, WebSocketManagerApi.class);
    }

}