System.register([],(function(t,e){"use strict";return{execute:function(){t({G:function(t,e){var n=t.split("-"),r=n[0],a=n[1],s=n[2],g=new Date(r,a,0);g=g.getDate();var u=r,p=parseInt(a)+parseInt(e);p>12&&(u=parseInt(u)+parseInt(parseInt(p)/12==0?1:parseInt(p)/12),p=parseInt(p)%12);var D=s,i=new Date(u,p,0);return i=i.getDate(),D>i&&(D=i),p<10&&(p="0"+p),u+"-"+p+"-"+D},g:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1,r=t.getDate()<10?"0"+t.getDate():t.getDate();return e+"-"+n+"-"+r}})}}}));
