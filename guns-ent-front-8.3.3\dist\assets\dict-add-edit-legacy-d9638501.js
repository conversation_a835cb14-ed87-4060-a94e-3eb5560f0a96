System.register(["./index-legacy-ee1db0c7.js","./dict-form-legacy-b0d53802.js","./SysDictApi-legacy-38907c3b.js","./index-legacy-94a6fc23.js"],(function(e,t){"use strict";var a,i,d,l,s,c,n,u,o,r,v;return{setters:[e=>{a=e.r,i=e.o,d=e.cb,l=e.a,s=e.f,c=e.w,n=e.d,u=e.m,o=e.M},e=>{r=e.default},e=>{v=e.S},null],execute:function(){e("default",{__name:"dict-add-edit",props:{visible:Boolean,data:Object,dictTypeId:String,dictTypeName:String},emits:["update:visible","done"],setup(e,{emit:t}){const p=e,y=t,f=a(!1),m=a(!1),g=a({statusFlag:1,dictParentId:"-1",dictTypeId:p.dictTypeId,dictTypeName:p.dictTypeName}),b=a(null),I=a([]);i((async()=>{T(),p.data?(m.value=!0,h()):(g.value.dictSort=await d("SYSTEM_BASE_DICT"),m.value=!1)}));const T=()=>{v.tree({dictTypeId:p.dictTypeId}).then((e=>{I.value=[{dictId:"-1",dictName:"根节点",children:e}]}))},h=()=>{v.detail({dictId:p.data.dictId}).then((e=>{g.value=Object.assign({},e)}))},S=e=>{y("update:visible",e)},j=async()=>{b.value.$refs.formRef.validate().then((async e=>{if(e){f.value=!0;let e=null;e=m.value?v.edit(g.value):v.add(g.value),e.then((async e=>{f.value=!1,u.success(e.message),S(!1),y("done")})).catch((()=>{f.value=!1}))}}))};return(e,t)=>{const a=o;return l(),s(a,{width:800,maskClosable:!1,visible:p.visible,"confirm-loading":f.value,forceRender:!0,style:{top:"40px"},title:m.value?"编辑字典":"新建字典","body-style":{paddingBottom:"8px",height:"600px",overflowY:"auto"},"onUpdate:visible":S,onOk:j,onClose:t[1]||(t[1]=e=>S(!1))},{default:c((()=>[n(r,{form:g.value,"onUpdate:form":t[0]||(t[0]=e=>g.value=e),ref_key:"dictFormRef",ref:b,isUpdate:m.value,dictList:I.value},null,8,["form","isUpdate","dictList"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
