System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-d2fc620d.js"],(function(e,l){"use strict";var a,u,n,d,t,o,s,r,v,c,i,p;return{setters:[e=>{a=e.r,u=e.o,n=e.X,d=e.a,t=e.c,o=e.d,s=e.f,r=e.h,v=e.l,c=e.w,i=e.a0},null,e=>{p=e._}],execute:function(){const l={class:"wh100"},y={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"请选择"},readonly:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(e,{emit:c}){const i=e,y=c,b=a(""),f=a(!1);u((()=>{b.value=i.value}));const g=e=>{const{name:l}=e;b.value=l,h()};n((()=>i.value),(e=>{b.value=e}),{deep:!0});const h=()=>{y("update:value",b.value),y("onChange",i.record)},x=()=>{f.value=!0};return(a,u)=>{const n=v,c=p;return d(),t("div",l,[o(n,{value:b.value,"onUpdate:value":u[0]||(u[0]=e=>b.value=e),disabled:e.readonly||e.disabled,allowClear:"",class:"w-full",onFocus:x,placeholder:e.placeholder},null,8,["value","disabled","placeholder"]),f.value?(d(),s(c,{key:0,visible:f.value,"onUpdate:visible":u[1]||(u[1]=e=>f.value=e),onDone:g,"need-city":!0},null,8,["visible"])):r("",!0)])}}},b={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const l=a(""),u=a(!1),n=a(!1),s=a("请选择"),r=()=>{console.log(l.value)};return(e,a)=>{const v=y,p=i;return d(),t("div",b,[o(p,{title:"地图选择(input)",bordered:!1},{default:c((()=>[o(v,{value:l.value,"onUpdate:value":a[0]||(a[0]=e=>l.value=e),disabled:u.value,placeholder:s.value,readonly:n.value,onOnChange:r,style:{width:"300px"}},null,8,["value","disabled","placeholder","readonly"])])),_:1})])}}})}}}));
