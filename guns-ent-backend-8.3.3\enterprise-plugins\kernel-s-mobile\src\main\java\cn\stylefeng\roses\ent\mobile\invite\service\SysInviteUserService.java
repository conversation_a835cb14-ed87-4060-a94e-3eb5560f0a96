package cn.stylefeng.roses.ent.mobile.invite.service;

import cn.stylefeng.roses.ent.mobile.invite.entity.SysInviteUser;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteDetailRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteUserRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteDetail;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteUserVo;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.SendPhoneCodeRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 邀请用户服务类
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
public interface SysInviteUserService extends IService<SysInviteUser> {

    /**
     * 发送短信验证码
     *
     * <AUTHOR>
     * @since 2024-04-08 19:41
     */
    void sendPhoneValidateCode(SendPhoneCodeRequest changePhoneRequest);

    /**
     * 新增邀请用户
     *
     * @param sysInviteUserRequest 请求参数
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    void submitApply(SysInviteUserRequest sysInviteUserRequest);

    /**
     * 查询详情邀请用户
     *
     * @param sysInviteUserRequest 请求参数
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    SysInviteUser detail(SysInviteUserRequest sysInviteUserRequest);

    /**
     * 获取邀请用户列表
     *
     * @param sysInviteUserRequest 请求参数
     * @return List<SysInviteUser>  返回结果
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    List<SysInviteUserVo> findList(SysInviteUserRequest sysInviteUserRequest);

    /**
     * 获取邀请加入界面，邀请人和邀请加入公司的信息
     *
     * <AUTHOR>
     * @since 2024-04-08 18:41
     */
    SysInviteDetail getInviteDetail(SysInviteDetailRequest sysInviteDetailRequest);

    /**
     * 同意加入申请
     *
     * <AUTHOR>
     * @since 2024-04-09 12:40
     */
    void agreeApply(SysInviteUserRequest sysInviteUserRequest);

    /**
     * 不同意某人的申请
     *
     * <AUTHOR>
     * @since 2024-04-09 12:45
     */
    void disAgreeApply(SysInviteUserRequest sysInviteUserRequest);

}
