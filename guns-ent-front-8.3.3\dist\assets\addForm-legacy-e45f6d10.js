System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./regionApi-legacy-73888494.js"],(function(e,a){"use strict";var r,l,t,n,o,d,i,u,s,g,m,f,v,c,p,h,b,y,_,I,w,k;return{setters:[e=>{r=e._,l=e.r,t=e.s,n=e.X,o=e.a,d=e.f,i=e.w,u=e.d,s=e.g,g=e.m,m=e.l,f=e.u,v=e.v,c=e.G,p=e.as,h=e.y,b=e.W,y=e.J,_=e.$,I=e.H,w=e.M},null,null,e=>{k=e.R}],execute:function(){const a={name:"RegionAddForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:a}){const r=l(),o=l(!1),d=l([]),i=l("1级 - 国家"),u=t({regionId:null,regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),s=e=>{a("update:visible",e),e||m()},m=()=>{var e;null===(e=r.value)||void 0===e||e.resetFields(),Object.assign(u,{regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),v()},f=e=>({1:"1级 - 国家",2:"2级 - 省",3:"3级 - 市",4:"4级 - 区县",5:"5级 - 商圈"}[e]||`${e}级 - 未知`),v=()=>{const e=(e=>{if(!e)return 1;const a=(e,r)=>{for(const l of e){if(l.regionId===r)return l.regionLevel||1;if(l.children&&l.children.length>0){const e=a(l.children,r);if(e)return e}}return null},r=a(d.value,e);return r?Math.min(r+1,5):1})(u.parentId);u.regionLevel=e,i.value=f(e)},c=e=>Array.isArray(e)?e.filter((e=>!!(e&&e.regionId&&e.regionName)&&(e.title=e.regionName,e.key=String(e.regionId),e.value=String(e.regionId),e.children&&Array.isArray(e.children)&&(e.children=c(e.children)),!0))):[],p=(e,a)=>e.filter((e=>e.regionId!==a&&(e.children&&e.children.length>0&&(e.children=p(e.children,a)),!0)));return n((()=>e.data),(e=>{e&&Object.keys(e).length>0&&(Object.assign(u,e),e.regionLevel?i.value=f(e.regionLevel):v())}),{immediate:!0}),n((()=>e.visible),(a=>{a?((async()=>{try{let e=await k.findTree()||[];e=c(e),u.regionId?d.value=p(e,u.regionId):d.value=e}catch(e){console.error("加载区域树失败:",e),d.value=[]}})(),e.data&&Object.keys(e.data).length>0&&(Object.assign(u,e.data),setTimeout((()=>{e.data.regionLevel?i.value=f(e.data.regionLevel):v()}),100))):m()})),n((()=>u.parentId),(()=>{v()})),{formRef:r,loading:o,form:u,rules:{regionCode:[{required:!0,message:"请输入区域编码",trigger:"blur"}],regionName:[{required:!0,message:"请输入区域名称",trigger:"blur"}],regionLevel:[{required:!0,message:"请选择区域层级",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},regionTreeData:d,regionLevelText:i,updateVisible:s,save:async()=>{try{await r.value.validate(),o.value=!0;const e=!!u.regionId,l=e?k.edit:k.add,t=e?"编辑成功":"新增成功";await l(u),g.success(t),a("ok"),s(!1)}catch(e){console.error("保存区域失败:",e),g.error("保存失败")}finally{o.value=!1}},filterTreeNode:(e,a)=>a.title&&a.title.toLowerCase().includes(e.toLowerCase())}}};e("default",r(a,[["render",function(e,a,r,l,t,n){const g=m,k=f,L=v,O=c,x=p,N=h,j=b,C=y,T=_,U=I,A=w;return o(),d(A,{title:l.form.regionId?"编辑区域":"新增区域",width:800,visible:r.visible,"confirm-loading":l.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":l.updateVisible,onOk:l.save},{default:i((()=>[u(U,{ref:"formRef",model:l.form,rules:l.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:i((()=>[u(O,{gutter:16},{default:i((()=>[u(L,{md:12,sm:24},{default:i((()=>[u(k,{label:"区域编码",name:"regionCode"},{default:i((()=>[u(g,{value:l.form.regionCode,"onUpdate:value":a[0]||(a[0]=e=>l.form.regionCode=e),placeholder:"请输入区域编码"},null,8,["value"])])),_:1})])),_:1}),u(L,{md:12,sm:24},{default:i((()=>[u(k,{label:"区域名称",name:"regionName"},{default:i((()=>[u(g,{value:l.form.regionName,"onUpdate:value":a[1]||(a[1]=e=>l.form.regionName=e),placeholder:"请输入区域名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),u(O,{gutter:16},{default:i((()=>[u(L,{md:12,sm:24},{default:i((()=>[u(k,{label:"父级区域",name:"parentId"},{default:i((()=>[u(x,{value:l.form.parentId,"onUpdate:value":a[2]||(a[2]=e=>l.form.parentId=e),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":l.regionTreeData,placeholder:"请选择父级区域","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":l.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])])),_:1})])),_:1}),u(L,{md:12,sm:24},{default:i((()=>[u(k,{label:"区域层级",name:"regionLevel"},{default:i((()=>[u(g,{value:l.regionLevelText,"onUpdate:value":a[3]||(a[3]=e=>l.regionLevelText=e),placeholder:"根据父级区域自动设置",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),u(O,{gutter:16},{default:i((()=>[u(L,{md:12,sm:24},{default:i((()=>[u(k,{label:"排序号",name:"sortOrder"},{default:i((()=>[u(N,{value:l.form.sortOrder,"onUpdate:value":a[4]||(a[4]=e=>l.form.sortOrder=e),min:0,max:9999,style:{width:"100%"},placeholder:"请输入排序号"},null,8,["value"])])),_:1})])),_:1}),u(L,{md:12,sm:24},{default:i((()=>[u(k,{label:"状态",name:"status"},{default:i((()=>[u(C,{value:l.form.status,"onUpdate:value":a[5]||(a[5]=e=>l.form.status=e),placeholder:"请选择状态"},{default:i((()=>[u(j,{value:"Y"},{default:i((()=>a[7]||(a[7]=[s("启用")]))),_:1,__:[7]}),u(j,{value:"N"},{default:i((()=>a[8]||(a[8]=[s("停用")]))),_:1,__:[8]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),u(O,{gutter:16},{default:i((()=>[u(L,{span:24},{default:i((()=>[u(k,{label:"备注",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:i((()=>[u(T,{value:l.form.remark,"onUpdate:value":a[6]||(a[6]=e=>l.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","visible","confirm-loading","onUpdate:visible","onOk"])}]]))}}}));
