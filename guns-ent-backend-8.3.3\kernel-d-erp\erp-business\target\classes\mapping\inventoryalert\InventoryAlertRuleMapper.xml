<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper.InventoryAlertRuleMapper">

    <!-- 分页查询预警规则 -->
    <select id="selectRulePage" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRuleResponse">
        SELECT 
            r.id,
            r.rule_name,
            r.rule_type,
            r.target_type,
            r.target_id,
            r.alert_level,
            r.threshold_type,
            r.threshold_value,
            r.comparison_operator,
            r.is_enabled,
            r.notification_methods,
            r.notification_users,
            r.check_frequency,
            r.last_check_time,
            r.remark,
            r.create_time,
            r.create_user,
            r.update_time,
            r.update_user,
            CASE r.target_type
                WHEN 'PRODUCT' THEN p.product_name
                WHEN 'CATEGORY' THEN pc.category_name
                ELSE '全部商品'
            END AS target_name
        FROM erp_inventory_alert_rule r
        LEFT JOIN erp_product p ON r.target_type = 'PRODUCT' AND r.target_id = p.product_id AND p.del_flag = 'N'
        LEFT JOIN erp_product_category pc ON r.target_type = 'CATEGORY' AND r.target_id = pc.category_id AND pc.del_flag = 'N'
        WHERE r.del_flag = 'N'
        <if test="request.searchText != null and request.searchText != ''">
            AND r.rule_name LIKE CONCAT('%', #{request.searchText}, '%')
        </if>
        <if test="request.ruleTypeFilter != null and request.ruleTypeFilter != ''">
            AND r.rule_type = #{request.ruleTypeFilter}
        </if>
        <if test="request.alertLevelFilter != null and request.alertLevelFilter != ''">
            AND r.alert_level = #{request.alertLevelFilter}
        </if>
        <if test="request.isEnabledFilter != null and request.isEnabledFilter != ''">
            AND r.is_enabled = #{request.isEnabledFilter}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据目标类型和目标ID查询规则 -->
    <select id="selectByTarget" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule">
        SELECT * FROM erp_inventory_alert_rule 
        WHERE del_flag = 'N' AND is_enabled = 'Y'
        AND (
            (target_type = #{targetType} AND target_id = #{targetId})
            OR target_type = 'ALL'
        )
        ORDER BY alert_level DESC, create_time ASC
    </select>

    <!-- 查询指定商品的预警规则 -->
    <select id="selectByProductId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule">
        SELECT * FROM erp_inventory_alert_rule 
        WHERE del_flag = 'N' AND is_enabled = 'Y'
        AND (
            (target_type = 'PRODUCT' AND target_id = #{productId})
            OR (target_type = 'CATEGORY' AND target_id IN (
                SELECT category_id FROM erp_product WHERE product_id = #{productId} AND del_flag = 'N'
            ))
            OR target_type = 'ALL'
        )
        ORDER BY alert_level DESC, create_time ASC
    </select>

    <!-- 查询指定分类的预警规则 -->
    <select id="selectByCategoryId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule">
        SELECT * FROM erp_inventory_alert_rule 
        WHERE del_flag = 'N' AND is_enabled = 'Y'
        AND (
            (target_type = 'CATEGORY' AND target_id = #{categoryId})
            OR target_type = 'ALL'
        )
        ORDER BY alert_level DESC, create_time ASC
    </select>

</mapper>
