System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-198191c1.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./index-legacy-94a6fc23.js","./FileApi-legacy-f85a3060.js","./SysDictTypeApi-legacy-1047ef23.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js"],(function(e,a){"use strict";var l,t,o,i,d,r,u,n,s,m,c,f,p,g,v,h,_,y,w,x,b,U,N,V,F,C,I,j,k,L,O,A,T,q,D;return{setters:[e=>{l=e._,t=e.s,o=e.r,i=e.o,d=e.k,r=e.a,u=e.f,n=e.w,s=e.d,m=e.g,c=e.h,f=e.b,p=e.c,g=e.F,v=e.e,h=e.t,_=e.cj,y=e.m,w=e.l,x=e.u,b=e.v,U=e.z,N=e.A,V=e.aa,F=e.y,C=e.al,I=e.B,j=e.I,k=e.bk,L=e.a7,O=e.G,A=e.H},e=>{T=e._},null,null,null,null,e=>{q=e.a},e=>{D=e.S},null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".card-title[data-v-e59fe4cd]{width:100%;border-left:5px solid;border-color:var(--primary-color);padding-left:10px;margin-bottom:20px}[data-v-e59fe4cd] .vxe-icon-edit{display:none}.marginT10[data-v-e59fe4cd]{margin-top:10px;margin-bottom:20px}.delete[data-v-e59fe4cd]{width:20px;color:red;cursor:pointer}.filename[data-v-e59fe4cd]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block}\n",document.head.appendChild(a);const Y={key:0,style:{width:"100%",display:"flex","align-items":"center"}},z={class:"filename"},E=["onClick"],M={__name:"user-form",props:{form:Object,isUpdate:Boolean,superAdminFlag:{type:Boolean,default:!1}},setup(e,{expose:a}){const l=e,M=t({realName:[{required:!0,message:"请输入姓名",type:"string",trigger:"blur"}],account:[{required:!0,message:"请输入账号",type:"string",trigger:"blur"}],sex:[{required:!0,message:"请选择性别",type:"string",trigger:"change"}],superAdminFlag:[{required:!0,message:"请选择是否是超级管理员",type:"string",trigger:"change"}],password:[{required:!0,message:"请输入密码",type:"string",trigger:"blur"}],statusFlag:[{required:!0,message:"请选择用户状态",type:"number",trigger:"change"}]}),S=o(null),B=o(null),H=o(!1),P=o(""),R=o([]),K=o(void 0),G=o({}),J=o([]),Q=o({orgName:[{required:!0,message:"请选择机构"}],positionName:[{required:!0,message:"请选择职务"}],mainFlag:[{required:!0,message:"请选择主要部门"}]});i((()=>{W()}));const W=async()=>{J.value=await D.getDictListByParams({dictTypeId:"1722790763315597314"})},X=async()=>{let e=!1;const a=S.value;return e=!(await a.validate(!0)),e},Z=async()=>{if(await X()){let e={mainFlag:0==l.form.userOrgList.length?"Y":"N",statusFlag:1,orgId:"",orgName:"",positionId:"",positionName:""};l.form.userOrgList.push(e)}},$=(e,a,l,t)=>{e[a]&&e[l]?G.value[t]=[{bizId:e[a],name:e[l]}]:G.value[t]=[]},ee=e=>{H.value=!1,"dept"==R.value[0]?ae(e,"orgId","orgName","selectOrgList"):"position"==R.value[0]&&ae(e,"positionId","positionName","selectPositionList")},ae=(e,a,t,o)=>{null!=K.value&&(e[o]&&e[o].length>0?(l.form.userOrgList[K.value][a]=e[o][0].bizId,l.form.userOrgList[K.value][t]=e[o][0].name):(l.form.userOrgList[K.value][a]="",l.form.userOrgList[K.value][t]=""))},le=()=>{l.form.userCertificateList.push({certificateType:"",certificateNo:"",issuingAuthority:"",dateIssued:"",dateExpires:null,attachmentId:"",attachmentName:"",attachmentUrl:""})};return a({validAllEvent:X}),(a,t)=>{const o=w,i=x,D=b,W=U,X=N,ae=V,te=F,oe=C,ie=d("plus-outlined"),de=I,re=d("vxe-column"),ue=d("vxe-input"),ne=d("vxe-switch"),se=j,me=d("vxe-table"),ce=d("vxe-option"),fe=d("vxe-select"),pe=d("CloudUploadOutlined"),ge=k,ve=L,he=d("delete-outlined"),_e=O,ye=T,we=A;return r(),u(we,{ref:"formRef",model:e.form,rules:M,layout:"vertical"},{default:n((()=>[s(_e,{gutter:20},{default:n((()=>[s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"姓名:",name:"realName"},{default:n((()=>[s(o,{value:e.form.realName,"onUpdate:value":t[0]||(t[0]=a=>e.form.realName=a),"allow-clear":"",placeholder:"请输入姓名"},null,8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"账号:",name:"account"},{default:n((()=>[s(o,{value:e.form.account,"onUpdate:value":t[1]||(t[1]=a=>e.form.account=a),"allow-clear":"",placeholder:"请输入账号"},null,8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"性别:",name:"sex"},{default:n((()=>[s(X,{value:e.form.sex,"onUpdate:value":t[2]||(t[2]=a=>e.form.sex=a)},{default:n((()=>[s(W,{value:"M"},{default:n((()=>t[12]||(t[12]=[m("男")]))),_:1,__:[12]}),s(W,{value:"F"},{default:n((()=>t[13]||(t[13]=[m("女")]))),_:1,__:[13]})])),_:1},8,["value"])])),_:1})])),_:1}),l.superAdminFlag?(r(),u(D,{key:0,xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"是否是超级管理员:",name:"superAdminFlag"},{default:n((()=>[s(X,{value:e.form.superAdminFlag,"onUpdate:value":t[3]||(t[3]=a=>e.form.superAdminFlag=a)},{default:n((()=>[s(W,{value:"Y"},{default:n((()=>t[14]||(t[14]=[m("是")]))),_:1,__:[14]}),s(W,{value:"N"},{default:n((()=>t[15]||(t[15]=[m("否")]))),_:1,__:[15]})])),_:1},8,["value"])])),_:1})])),_:1})):c("",!0),e.isUpdate?c("",!0):(r(),u(D,{key:1,xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"密码:",name:"password"},{default:n((()=>[s(ae,{value:e.form.password,"onUpdate:value":t[4]||(t[4]=a=>e.form.password=a),placeholder:"请输入密码",autocomplete:"new-password"},null,8,["value"])])),_:1})])),_:1})),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"用户状态:",name:"statusFlag"},{default:n((()=>[s(X,{value:e.form.statusFlag,"onUpdate:value":t[5]||(t[5]=a=>e.form.statusFlag=a)},{default:n((()=>[s(W,{value:1},{default:n((()=>t[16]||(t[16]=[m("启用")]))),_:1,__:[16]}),s(W,{value:2},{default:n((()=>t[17]||(t[17]=[m("禁用")]))),_:1,__:[17]})])),_:1},8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"用户排序:",name:"userSort"},{default:n((()=>[s(te,{value:e.form.userSort,"onUpdate:value":t[6]||(t[6]=a=>e.form.userSort=a),placeholder:"请输入排序","allow-clear":"",autocomplete:"off",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"手机号:",name:"phone"},{default:n((()=>[s(o,{value:e.form.phone,"onUpdate:value":t[7]||(t[7]=a=>e.form.phone=a),"allow-clear":"",placeholder:"请输入手机号"},null,8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"邮箱:",name:"email"},{default:n((()=>[s(o,{value:e.form.email,"onUpdate:value":t[8]||(t[8]=a=>e.form.email=a),"allow-clear":"",placeholder:"请输入邮箱",autocomplete:"new-password"},null,8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"出生日期:",name:"birthday"},{default:n((()=>[s(oe,{value:e.form.birthday,"onUpdate:value":t[9]||(t[9]=a=>e.form.birthday=a),"value-format":"YYYY-MM-DD",placeholder:"请选择出生日期",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),s(D,{xs:24,sm:24,md:12},{default:n((()=>[s(i,{label:"人员工号:",name:"employeeNumber"},{default:n((()=>[s(o,{value:e.form.employeeNumber,"onUpdate:value":t[10]||(t[10]=a=>e.form.employeeNumber=a),"allow-clear":"",placeholder:"请输入人员工号"},null,8,["value"])])),_:1})])),_:1}),s(D,{span:24},{default:n((()=>t[18]||(t[18]=[f("div",{class:"card-title"},"组织机构信息",-1)]))),_:1,__:[18]}),s(D,{span:24},{default:n((()=>[s(de,{type:"primary",class:"border-radius",onClick:Z},{default:n((()=>[s(ie),t[19]||(t[19]=m(" 添加机构"))])),_:1,__:[19]})])),_:1}),s(D,{span:24,class:"marginT10"},{default:n((()=>[s(me,{border:"","show-overflow":"",data:e.form.userOrgList,"row-config":{useKey:!0},"edit-rules":Q.value,"column-config":{resizable:!0},"edit-config":{trigger:"click",mode:"cell"},"max-height":"600",ref_key:"xTableRef",ref:S},{default:n((()=>[s(re,{type:"seq",width:"60",title:"序号",align:"center"}),s(re,{field:"orgName",title:"机构名称","min-width":"150",align:"center"},{default:n((({row:e,rowIndex:a})=>[s(ue,{modelValue:e.orgName,"onUpdate:modelValue":a=>e.orgName=a,type:"text",onFocus:l=>((e,a)=>{K.value=a,$(e,"orgId","orgName","selectOrgList"),R.value=["dept"],P.value="机构选择",H.value=!0})(e,a)},null,8,["modelValue","onUpdate:modelValue","onFocus"])])),_:1}),s(re,{field:"positionName",title:"职位",width:"200",align:"center"},{default:n((({row:e,rowIndex:a})=>[s(ue,{modelValue:e.positionName,"onUpdate:modelValue":a=>e.positionName=a,type:"text",placeholder:"请输入职位",onFocus:l=>((e,a)=>{K.value=a,$(e,"positionId","positionName","selectPositionList"),R.value=["position"],P.value="职位选择",H.value=!0})(e,a)},null,8,["modelValue","onUpdate:modelValue","onFocus"])])),_:1}),s(re,{field:"mainFlag",title:"主要部门",width:"100",align:"center"},{default:n((({row:e,rowIndex:a})=>[s(ne,{modelValue:e.mainFlag,"onUpdate:modelValue":a=>e.mainFlag=a,"open-value":"Y","close-value":"N",onChange:t=>((e,a)=>{if("Y"==e.mainFlag)l.form.userOrgList.forEach(((e,l)=>{a!=l&&(e.mainFlag="N")}));else if("N"==e.mainFlag&&!l.form.userOrgList.find((e=>"Y"==e.mainFlag)))return e.mainFlag="Y",y.warning("必须有一个主要部门")})(e,a)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),s(re,{field:"statusFlag",title:"是否启用",width:"100",align:"center"},{default:n((({row:e})=>[s(ne,{modelValue:e.statusFlag,"onUpdate:modelValue":a=>e.statusFlag=a,"open-value":1,"close-value":2},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(re,{title:"操作",width:"100",align:"center"},{default:n((({row:e})=>[s(se,{iconClass:"icon-opt-shanchu",color:"#60666b","font-size":"24px",onClick:a=>(async e=>{"confirm"===await _.modal.confirm("您确定要删除该数据?")&&S.value.remove(e);const a=S.value.getTableData().tableData;l.form.userOrgList=a})(e)},null,8,["onClick"])])),_:1})])),_:1},8,["data","edit-rules"])])),_:1}),s(D,{span:24},{default:n((()=>t[20]||(t[20]=[f("div",{class:"card-title"},"证书信息",-1)]))),_:1,__:[20]}),s(D,{span:24},{default:n((()=>[s(de,{type:"primary",class:"border-radius",onClick:le},{default:n((()=>[s(ie),t[21]||(t[21]=m(" 添加证书"))])),_:1,__:[21]})])),_:1}),s(D,{span:24,style:{"margin-top":"10px"}},{default:n((()=>[s(me,{border:"","show-overflow":"",data:e.form.userCertificateList,"row-config":{useKey:!0},"column-config":{resizable:!0},"max-height":"600",ref_key:"certificateRef",ref:B},{default:n((()=>[s(re,{type:"seq",width:"60",title:"序号",align:"center"}),s(re,{field:"certificateType",title:"证书类型",width:"200",align:"center"},{default:n((({row:e})=>[s(fe,{modelValue:e.certificateType,"onUpdate:modelValue":a=>e.certificateType=a,transfer:"",placeholder:"请选择证书类型"},{default:n((()=>[(r(!0),p(g,null,v(J.value,((e,a)=>(r(),u(ce,{key:a,value:e.dictId,label:e.dictName},null,8,["value","label"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),s(re,{field:"certificateNo",title:"证书编号",width:"200",align:"center"},{default:n((({row:e})=>[s(ue,{modelValue:e.certificateNo,"onUpdate:modelValue":a=>e.certificateNo=a,type:"text",placeholder:"请输入证书编号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(re,{field:"issuingAuthority",title:"发证机构名称",width:"200",align:"center"},{default:n((({row:e})=>[s(ue,{modelValue:e.issuingAuthority,"onUpdate:modelValue":a=>e.issuingAuthority=a,type:"text",placeholder:"请输入发证机构名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(re,{field:"dateIssued",title:"发证日期",width:"150",align:"center"},{default:n((({row:e})=>[s(ue,{modelValue:e.dateIssued,"onUpdate:modelValue":a=>e.dateIssued=a,type:"date",valueFormat:"yyyy-MM-dd HH:mm:ss",placeholder:"请选择发证日期",transfer:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(re,{field:"dateExpires",title:"到期日期",width:"150",align:"center"},{default:n((({row:e})=>[s(ue,{modelValue:e.dateExpires,"onUpdate:modelValue":a=>e.dateExpires=a,valueFormat:"yyyy-MM-dd HH:mm:ss",type:"date",placeholder:"请选择到期日期",transfer:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(re,{field:"attachmentId",title:"附件",width:"150",align:"center"},{default:n((({row:e})=>[s(ge,{name:"file",multiple:!1,maxCount:1,accept:".jpeg,.jpg,.png,.tif,.jfif,.webp,.pjp,.apng,.pjpeg,.avif,.ico,.tiff,.bmp,.xbm,.jxl,.jpeg,.svgz,.gif,.svg,.pdf",beforeUpload:a=>((e,a)=>{const t=new FormData;return t.append("file",e),q.commonUpload("N",t).then((e=>{a.attachmentName=e.data.fileOriginName,a.attachmentId=e.data.fileId,a.attachmentUrl=e.data.fileUrl})),l.form.userCertificateList=[...l.form.userCertificateList],!1})(a,e),showUploadList:!1},{default:n((()=>[e.attachmentId?c("",!0):(r(),u(de,{key:0,type:"primary"},{icon:n((()=>[s(pe)])),default:n((()=>[t[22]||(t[22]=f("span",null,"上传文件",-1))])),_:1,__:[22]}))])),_:2},1032,["beforeUpload"]),e.attachmentId?(r(),p("div",Y,[f("span",z,[f("a",{onClick:e=>(e=>{const{href:a}=router.resolve({path:record.attachmentUrl});window.open(a,"_blank")})()},[s(ve,null,{title:n((()=>[m(h(e.attachmentName),1)])),default:n((()=>[m(" "+h(e.attachmentName),1)])),_:2},1024)],8,E)]),s(he,{class:"delete",onClick:a=>(e=>{e.attachmentName="",e.attachmentId="",e.attachmentUrl="",l.form.userCertificateList=[...l.form.userCertificateList]})(e)},null,8,["onClick"])])):c("",!0)])),_:1}),s(re,{title:"操作",width:"100",align:"center",fixed:"right"},{default:n((({row:e})=>[s(se,{iconClass:"icon-opt-shanchu",color:"#60666b","font-size":"24px",onClick:a=>(async e=>{"confirm"===await _.modal.confirm("您确定要删除该数据?")&&B.value.remove(e);const a=B.value.getTableData().tableData;l.form.userCertificateList=a})(e)},null,8,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1}),H.value?(r(),u(ye,{key:0,visible:H.value,"onUpdate:visible":t[11]||(t[11]=e=>H.value=e),title:P.value,data:G.value,showTab:R.value,onDone:ee},null,8,["visible","title","data","showTab"])):c("",!0)])),_:1},8,["model","rules"])}}};e("default",l(M,[["__scopeId","data-v-e59fe4cd"]]))}}}));
