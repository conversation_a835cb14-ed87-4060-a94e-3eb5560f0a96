System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./PermissionApi-legacy-e6423604.js","./OrgApi-legacy-c15eac58.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js"],(function(e,a){"use strict";var l,i,d,t,u,n,r,v,s,o,p,g,c,m,y,f,b,O,L,k,I,h,S,_,j,W,N;return{setters:[e=>{l=e.r,i=e.s,d=e.L,t=e.o,u=e.X,n=e.a,r=e.f,v=e.w,s=e.d,o=e.c,p=e.F,g=e.e,c=e.g,m=e.t,y=e.b,f=e.ah,b=e.h,O=e.m,L=e.W,k=e.J,I=e.u,h=e.l,S=e.H,_=e.M},e=>{j=e._},e=>{W=e.P},e=>{N=e.O},null,null,null,null,null,null],execute:function(){e("default",{__name:"data-scope-add-edit",props:{visible:Boolean,data:Object,roleId:String,levelNumberList:Array},emits:["update:visible","done"],setup(e,{emit:a}){const T=e,x=a,z=l(!1),w=l(!1),C=l([{key:10,name:"仅本人数据"},{key:20,name:"本部门数据"},{key:30,name:"本部门及以下数据"},{key:31,name:"本公司及以下数据"},{key:32,name:"指定机构层级及以下"},{key:40,name:"指定机构集合数据"},{key:41,name:"指定机构及以下"},{key:50,name:"全部数据"}]),U=l({roleId:T.roleId,defineOrgListName:""}),F=i({dataScopeType:[{required:!0,message:"请选择类型",type:"number",trigger:"change"}],defineOrgIdWrapper:[{required:!0,message:"请选择范围",type:"string",trigger:"change"}],defineOrgListName:[{required:!0,message:"请选择范围",type:"string",trigger:"blur"}],orgLevelCode:[{required:!0,message:"请选择层级",type:"string",trigger:"change"}]}),R=l(null),q=l(""),A=l(!0),B=l(!1),D=l({selectOrgList:[]}),H=l([]),P=d((()=>e=>{let a="",l=T.levelNumberList.find((a=>a.value==e.levelNumber));return l&&(a=l.name),a}));t((async()=>{E(),T.data?(w.value=!0,U.value=Object.assign({},T.data),40==U.value.dataScopeType&&(U.value.defineOrgListWrapper=U.value.defineOrgList.map(((e,a)=>({bizId:e,name:U.value.defineOrgListWrapper[a]}))),U.value.defineOrgListName=U.value.defineOrgListWrapper.map((e=>e.name)).join(","))):w.value=!1}));const E=async()=>{H.value=await N.organizationLevelList()},J=e=>{x("update:visible",e)},M=async()=>{let e;await R.value.validate(),z.value=!0,e=w.value?W.editRoleDataScope(U.value):W.addRoleDataScope(U.value),e.then((e=>{z.value=!1,O.success(e.message),J(!1),x("done")})).catch((()=>{z.value=!1}))},X=()=>{const{dataScopeType:e,defineOrgIdWrapper:a,defineOrgId:l,defineOrgListWrapper:i}=U.value;A.value=40!=e,40==e&&i?D.value.selectOrgList=i.map((e=>({bizId:e.bizId,name:e.name}))):41==e&&l&&a&&(D.value.selectOrgList=[{bizId:l,name:a}]),B.value=!0},G=e=>{const{dataScopeType:a}=U.value;var l,i,d;if(40==a)U.value.defineOrgListName=null===(l=e.selectOrgList)||void 0===l?void 0:l.map((e=>e.name)).join(","),U.value.defineOrgListWrapper=null===(i=e.selectOrgList)||void 0===i?void 0:i.map((e=>({bizId:e.bizId,name:e.name}))),U.value.defineOrgList=null===(d=e.selectOrgList)||void 0===d?void 0:d.map((e=>e.bizId)),R.value.validateFields(["defineOrgListName"]);else if(41==a){const a=e.selectOrgList[0]||{bizId:"",name:""};U.value.defineOrgId=a.bizId,U.value.defineOrgIdWrapper=a.name,R.value.validateFields(["defineOrgIdWrapper"])}};return u((()=>{var e;return null===(e=U.value)||void 0===e?void 0:e.dataScopeType}),(e=>{if(!e||e&&[40,41].includes(e))q.value="";else{let a=C.value.find((a=>a.key==e));q.value=a.name}}),{deep:!0,immediate:!0}),(e,a)=>{const l=L,i=k,d=I,t=h,u=S,O=j,W=_;return n(),r(W,{width:524,maskClosable:!1,visible:T.visible,"confirm-loading":z.value,forceRender:!0,title:w.value?"编辑数据权限":"新建数据权限","body-style":{paddingBottom:"8px"},"onUpdate:visible":J,onOk:M,onClose:a[6]||(a[6]=e=>J(!1))},{default:v((()=>[s(u,{ref_key:"formRef",ref:R,model:U.value,rules:F,layout:"vertical"},{default:v((()=>{var e,u,b;return[s(d,{label:"类型:",name:"dataScopeType"},{default:v((()=>[s(i,{placeholder:"请选择类型",style:{width:"100%"},value:U.value.dataScopeType,"onUpdate:value":a[0]||(a[0]=e=>U.value.dataScopeType=e),"allow-clear":""},{default:v((()=>[(n(!0),o(p,null,g(C.value,(e=>(n(),r(l,{key:e.key,value:e.key},{default:v((()=>[c(m(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1}),40==(null===(e=U.value)||void 0===e?void 0:e.dataScopeType)?(n(),r(d,{key:0,label:"范围:",name:"defineOrgListName"},{default:v((()=>[s(t,{value:U.value.defineOrgListName,"onUpdate:value":a[1]||(a[1]=e=>U.value.defineOrgListName=e),placeholder:"请选择范围",onFocus:X},null,8,["value"])])),_:1})):41==(null===(u=U.value)||void 0===u?void 0:u.dataScopeType)?(n(),r(d,{key:1,label:"范围:",name:"defineOrgIdWrapper"},{default:v((()=>[s(t,{value:U.value.defineOrgIdWrapper,"onUpdate:value":a[2]||(a[2]=e=>U.value.defineOrgIdWrapper=e),placeholder:"请选择范围",onFocus:X},null,8,["value"])])),_:1})):32==(null===(b=U.value)||void 0===b?void 0:b.dataScopeType)?(n(),r(d,{key:2,label:"层级:",name:"orgLevelCode"},{default:v((()=>[s(i,{value:U.value.orgLevelCode,"onUpdate:value":a[3]||(a[3]=e=>U.value.orgLevelCode=e),style:{width:"100%"},placeholder:"请选择层级"},{default:v((()=>[(n(!0),o(p,null,g(H.value,(e=>(n(),r(l,{value:e.levelCode,key:e.levelCode},{default:v((()=>[y("span",{style:f({color:e.levelColor})},m(e.levelName)+"("+m(P.value(e))+")",5)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})):(n(),r(d,{key:3,label:"范围:"},{default:v((()=>[s(t,{value:q.value,"onUpdate:value":a[4]||(a[4]=e=>q.value=e),disabled:"",placeholder:"请选择"},null,8,["value"])])),_:1}))]})),_:1},8,["model","rules"]),B.value?(n(),r(O,{key:0,visible:B.value,"onUpdate:visible":a[5]||(a[5]=e=>B.value=e),title:"选择机构",data:D.value,isRadio:A.value,showTab:["dept"],onDone:G},null,8,["visible","data","isRadio"])):b("",!0)])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
