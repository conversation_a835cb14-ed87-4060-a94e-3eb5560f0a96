package cn.stylefeng.roses.kernel.impexp.user.controller;

import cn.stylefeng.roses.kernel.impexp.user.pojo.EnsureImportUserRequest;
import cn.stylefeng.roses.kernel.impexp.user.pojo.UserImportPreviewResult;
import cn.stylefeng.roses.kernel.impexp.user.service.UserImportService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 导入用户的控制器
 *
 * <AUTHOR>
 * @since 2024-02-04 12:11
 */
@RestController
@ApiResource(name = "导入用户的控制器")
@Slf4j
public class UserImportController {

    @Resource
    private UserImportService userImportService;

    /**
     * 获取导入的Excel模板
     *
     * <AUTHOR>
     * @since 2024-02-04 13:41
     */
    @GetResource(name = "获取导入的Excel模板", path = "/userImport/getExcelTemplate")
    public void getExcelTemplate() {
        userImportService.downloadImportTemplate();
    }

    /**
     * 导入Excel并获取导入预览数据
     *
     * <AUTHOR>
     * @since 2024-02-04 13:41
     */
    @PostResource(name = "导入Excel并获取导入预览数据", path = "/userImport/uploadAndGetPreviewData")
    public ResponseData<UserImportPreviewResult> uploadAndGetPreviewData(@RequestPart("file") MultipartFile file) {
        UserImportPreviewResult sysUsers = userImportService.uploadAndGetPreviewData(file);
        return new SuccessResponseData<>(sysUsers);
    }

    /**
     * 确认导入Excel数据
     *
     * <AUTHOR>
     * @since 2024/2/10 16:34
     */
    @PostResource(name = "确认导入Excel数据", path = "/userImport/ensureImport")
    public ResponseData<?> ensureImport(@RequestBody List<EnsureImportUserRequest> ensureImportUserRequest) {
        userImportService.ensureImportUser(ensureImportUserRequest);
        return new SuccessResponseData<>();
    }

}
