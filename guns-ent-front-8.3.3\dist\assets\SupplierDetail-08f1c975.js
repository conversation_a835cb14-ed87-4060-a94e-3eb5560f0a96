import{_ as E,r as I,X as V,m as M,a as c,f as y,w as e,d as t,g as n,t as o,c as r,F as A,e as D,b as v,h as w,Y as R,U as O,S as U,Z as j,j as z,n as F,i as G,a5 as H,T as W,M as X}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{S as _}from"./SupplierApi-6b9315dd.js";const Y={name:"SupplierDetail",props:{visible:Boolean,data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(m,{emit:f}){const a=I(!1),s=I([]),b=I([]),T=I(!1),i=[{title:"\u5546\u54C1\u7F16\u7801",dataIndex:"productCode",width:120,ellipsis:!0},{title:"\u5546\u54C1\u540D\u79F0",dataIndex:"productName",width:200,ellipsis:!0},{title:"\u8BA1\u4EF7\u7C7B\u578B",dataIndex:"pricingType",width:100,align:"center"},{title:"\u96F6\u552E\u4EF7\u683C",dataIndex:"retailPrice",width:100,align:"right"},{title:"\u72B6\u6001",dataIndex:"status",width:80,align:"center"},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createTime",width:140,ellipsis:!0}],u=l=>{f("update:visible",l)},h=l=>_.getSupplierTypeName(l),N=l=>_.getBusinessModeName(l),C=l=>_.getSupplierStatusName(l),S=l=>_.getCreditLevelName(l),k=l=>_.getStatusTagColor(l),P=l=>_.getBusinessModeTagColor(l),x=l=>_.getCreditLevelTagColor(l),L=l=>({NORMAL:"\u666E\u901A\u5546\u54C1",WEIGHT:"\u8BA1\u91CD\u5546\u54C1",PIECE:"\u8BA1\u4EF6\u5546\u54C1",VARIABLE:"\u4E0D\u5B9A\u4EF7\u5546\u54C1"})[l]||l,p=l=>({NORMAL:"blue",WEIGHT:"green",PIECE:"orange",VARIABLE:"purple"})[l]||"default";V(()=>m.visible,async l=>{var d;l&&((d=m.data)!=null&&d.supplierId)?await Promise.all([g(),B()]):(s.value=[],b.value=[])});const g=async()=>{var l;if((l=m.data)!=null&&l.supplierId){a.value=!0;try{const d=await _.getSupplierRegions({supplierId:m.data.supplierId});d&&Array.isArray(d)?s.value=d:s.value=[]}catch(d){console.error("\u83B7\u53D6\u4F9B\u5E94\u5546\u5173\u8054\u533A\u57DF\u5931\u8D25:",d),M.error("\u83B7\u53D6\u4F9B\u5E94\u5546\u5173\u8054\u533A\u57DF\u5931\u8D25"),s.value=[]}finally{a.value=!1}}},B=async()=>{var l;if((l=m.data)!=null&&l.supplierId){T.value=!0;try{const d=await _.getSupplierProducts({supplierId:m.data.supplierId});d&&Array.isArray(d)?b.value=d:b.value=[]}catch(d){console.error("\u83B7\u53D6\u4F9B\u5E94\u5546\u5173\u8054\u5546\u54C1\u5931\u8D25:",d),M.error("\u83B7\u53D6\u4F9B\u5E94\u5546\u5173\u8054\u5546\u54C1\u5931\u8D25"),b.value=[]}finally{T.value=!1}}};return{loading:a,regionList:s,supplierProducts:b,productsLoading:T,productColumns:i,updateVisible:u,getSupplierTypeName:h,getBusinessModeName:N,getSupplierStatusName:C,getCreditLevelName:S,getStatusTagColor:k,getBusinessModeTagColor:P,getCreditLevelTagColor:x,getPricingTypeName:L,getPricingTypeColor:p}}},Z={key:0},q={key:1,class:"text-muted"},J={key:0},K={key:1,class:"region-tags"},Q={key:2,class:"empty-regions"},$={class:"products-section"},ee={class:"products-header"},te={class:"products-content"},ae={key:0},le={key:0},se={key:1,class:"text-muted"},oe={key:1,class:"empty-products"};function ie(m,f,a,s,b,T){const i=R,u=O,h=U,N=j,C=z,S=F,k=G,P=H,x=W,L=X;return c(),y(L,{title:"\u4F9B\u5E94\u5546\u8BE6\u60C5",width:1e3,visible:a.visible,footer:null,"onUpdate:visible":s.updateVisible},{default:e(()=>[t(x,{"default-active-key":"basic",type:"card"},{default:e(()=>[t(C,{key:"basic",tab:"\u57FA\u672C\u4FE1\u606F"},{default:e(()=>[t(N,{column:2,bordered:""},{default:e(()=>[t(i,{label:"\u4F9B\u5E94\u5546\u7F16\u7801"},{default:e(()=>[n(o(a.data.supplierCode),1)]),_:1}),t(i,{label:"\u4F9B\u5E94\u5546\u540D\u79F0"},{default:e(()=>[n(o(a.data.supplierName),1)]),_:1}),t(i,{label:"\u4F9B\u5E94\u5546\u7B80\u79F0"},{default:e(()=>[n(o(a.data.supplierShortName||"-"),1)]),_:1}),t(i,{label:"\u4F9B\u5E94\u5546\u7C7B\u578B"},{default:e(()=>[t(u,null,{default:e(()=>[n(o(s.getSupplierTypeName(a.data.supplierType)),1)]),_:1})]),_:1}),t(i,{label:"\u7ECF\u8425\u65B9\u5F0F"},{default:e(()=>[t(u,{color:s.getBusinessModeTagColor(a.data.businessMode)},{default:e(()=>[n(o(s.getBusinessModeName(a.data.businessMode)),1)]),_:1},8,["color"])]),_:1}),t(i,{label:"\u9500\u552E\u6263\u70B9"},{default:e(()=>[a.data.salesDeduction!==null&&a.data.salesDeduction!==void 0?(c(),r("span",Z,o(a.data.salesDeduction)+"% ",1)):(c(),r("span",q,"-"))]),_:1}),t(i,{label:"\u8054\u7CFB\u4EBA"},{default:e(()=>[n(o(a.data.contactPerson||"-"),1)]),_:1}),t(i,{label:"\u8054\u7CFB\u7535\u8BDD"},{default:e(()=>[n(o(a.data.contactPhone||"-"),1)]),_:1}),t(i,{label:"\u624B\u673A\u53F7\u7801"},{default:e(()=>[n(o(a.data.contactMobile||"-"),1)]),_:1}),t(i,{label:"\u90AE\u7BB1\u5730\u5740"},{default:e(()=>[n(o(a.data.contactEmail||"-"),1)]),_:1}),t(i,{label:"\u8054\u7CFB\u5730\u5740",span:2},{default:e(()=>[n(o(a.data.contactAddress||"-"),1)]),_:1}),t(i,{label:"\u8425\u4E1A\u6267\u7167\u53F7"},{default:e(()=>[n(o(a.data.businessLicenseNo||"-"),1)]),_:1}),t(i,{label:"\u7A0E\u52A1\u767B\u8BB0\u53F7"},{default:e(()=>[n(o(a.data.taxNo||"-"),1)]),_:1}),t(i,{label:"\u5F00\u6237\u94F6\u884C"},{default:e(()=>[n(o(a.data.bankName||"-"),1)]),_:1}),t(i,{label:"\u94F6\u884C\u8D26\u53F7"},{default:e(()=>[n(o(a.data.bankAccount||"-"),1)]),_:1}),t(i,{label:"\u4FE1\u7528\u7B49\u7EA7"},{default:e(()=>[t(u,{color:s.getCreditLevelTagColor(a.data.creditLevel)},{default:e(()=>[n(o(s.getCreditLevelName(a.data.creditLevel)),1)]),_:1},8,["color"])]),_:1}),t(i,{label:"\u72B6\u6001"},{default:e(()=>[t(u,{color:s.getStatusTagColor(a.data.status)},{default:e(()=>[n(o(s.getSupplierStatusName(a.data.status)),1)]),_:1},8,["color"])]),_:1}),t(i,{label:"\u5173\u8054\u533A\u57DF",span:2},{default:e(()=>[s.loading?(c(),r("div",J,[t(h,{size:"small"})])):s.regionList.length>0?(c(),r("div",K,[(c(!0),r(A,null,D(s.regionList,p=>(c(),y(u,{key:p.regionId,color:"blue"},{default:e(()=>[n(o(p.regionName),1)]),_:2},1024))),128))])):(c(),r("div",Q,f[0]||(f[0]=[v("span",{class:"empty-text"},"\u6682\u65E0\u5173\u8054\u533A\u57DF",-1)])))]),_:1}),t(i,{label:"\u5907\u6CE8",span:2},{default:e(()=>[n(o(a.data.remark||"-"),1)]),_:1}),t(i,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:e(()=>[n(o(a.data.createTime),1)]),_:1}),t(i,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:e(()=>[n(o(a.data.updateTime),1)]),_:1})]),_:1})]),_:1}),t(C,{key:"products",tab:"\u5173\u8054\u5546\u54C1"},{default:e(()=>[v("div",$,[v("div",ee,[t(S,null,{default:e(()=>[f[1]||(f[1]=v("span",{class:"products-title"},"\u5173\u8054\u5546\u54C1\u5217\u8868",-1)),s.supplierProducts.length>0?(c(),y(u,{key:0,color:"blue"},{default:e(()=>[n(" \u5171 "+o(s.supplierProducts.length)+" \u4E2A\u5546\u54C1 ",1)]),_:1})):w("",!0)]),_:1,__:[1]})]),v("div",te,[t(h,{spinning:s.productsLoading},{default:e(()=>[s.supplierProducts.length>0?(c(),r("div",ae,[t(k,{columns:s.productColumns,"data-source":s.supplierProducts,pagination:!1,size:"small",scroll:{y:300}},{bodyCell:e(({column:p,record:g})=>[p.dataIndex==="pricingType"?(c(),y(u,{key:0,color:s.getPricingTypeColor(g.pricingType)},{default:e(()=>[n(o(s.getPricingTypeName(g.pricingType)),1)]),_:2},1032,["color"])):p.dataIndex==="retailPrice"?(c(),r(A,{key:1},[g.retailPrice?(c(),r("span",le,"\uFFE5"+o(g.retailPrice),1)):(c(),r("span",se,"-"))],64)):p.dataIndex==="status"?(c(),y(u,{key:2,color:g.status==="ACTIVE"?"green":"red"},{default:e(()=>[n(o(g.status==="ACTIVE"?"\u6B63\u5E38":"\u505C\u7528"),1)]),_:2},1032,["color"])):w("",!0)]),_:1},8,["columns","data-source"])])):(c(),r("div",oe,[t(P,{description:"\u6682\u65E0\u5173\u8054\u5546\u54C1"})]))]),_:1},8,["spinning"])])])]),_:1})]),_:1})]),_:1},8,["visible","onUpdate:visible"])}const ge=E(Y,[["render",ie],["__scopeId","data-v-16ea259b"]]);export{ge as default};
