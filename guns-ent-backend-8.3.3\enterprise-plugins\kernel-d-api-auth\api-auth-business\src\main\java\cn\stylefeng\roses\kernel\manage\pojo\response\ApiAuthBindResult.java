package cn.stylefeng.roses.kernel.manage.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.util.List;

/**
 * API客户端接口权限绑定的结果
 *
 * <AUTHOR>
 * @since 2023/10/25 15:16
 */
@Data
public class ApiAuthBindResult {

    /**
     * 所有权限是否选中
     */
    @ChineseDescription("所有权限是否选中")
    private Boolean totalAuthCheckedFlag;

    /**
     * api接口资源是否选中的状态
     */
    @ChineseDescription("api接口资源是否选中的状态")
    private List<ApiEndpointCheckedFlag> apiEndpointCheckedFlagList;

}
