package cn.stylefeng.roses.kernel.ca.server.core.token;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.ca.api.CaLoginUserEncryptApi;
import cn.stylefeng.roses.kernel.ca.api.exception.CaServerException;
import cn.stylefeng.roses.kernel.ca.api.exception.enums.CaServerExceptionEnum;


/**
 * 加密解密用户的api创建
 *
 * <AUTHOR>
 * @date 2021/1/21 19:25
 */
public class CaLoginUserEncryptApiFactory {

    /**
     * 加密解密用户的api创建
     *
     * <AUTHOR>
     * @date 2021/1/21 19:25
     */
    public static CaLoginUserEncryptApi createEncryptApi(String secret) {

        if (ObjectUtil.hasEmpty(secret)) {
            throw new CaServerException(CaServerExceptionEnum.AES_SECRET_IS_NULL);
        }

        return new AesCaLoginUserEncrypt(secret);
    }

}
