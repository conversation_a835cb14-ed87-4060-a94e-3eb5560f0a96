package cn.stylefeng.roses.kernel.erp.modular.supplier.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpPermissionCodeConstants;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.BusinessModeChangeValidationResponse;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 供应商主档案控制器
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
@RestController
@ApiResource(name = "供应商主档案管理")
public class ErpSupplierController {

    @Resource
    private ErpSupplierService erpSupplierService;

    /**
     * 新增供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @PostResource(name = "新增供应商", path = "/erp/supplier/add")
    public ResponseData<?> add(@RequestBody @Validated(ErpSupplierRequest.add.class) ErpSupplierRequest erpSupplierRequest) {
        erpSupplierService.add(erpSupplierRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @PostResource(name = "删除供应商", path = "/erp/supplier/delete")
    public ResponseData<?> delete(@RequestBody @Validated(ErpSupplierRequest.delete.class) ErpSupplierRequest erpSupplierRequest) {
        erpSupplierService.del(erpSupplierRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 批量删除供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @PostResource(name = "批量删除供应商", path = "/erp/supplier/batchDelete")
    public ResponseData<?> batchDelete(@RequestBody ErpSupplierRequest erpSupplierRequest) {
        erpSupplierService.batchDelete(erpSupplierRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑供应商
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @PostResource(name = "编辑供应商", path = "/erp/supplier/edit")
    public ResponseData<?> edit(@RequestBody @Validated(ErpSupplierRequest.edit.class) ErpSupplierRequest erpSupplierRequest) {
        erpSupplierService.edit(erpSupplierRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查询供应商详情
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 供应商详情
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @GetResource(name = "查询供应商详情", path = "/erp/supplier/detail")
    public ResponseData<ErpSupplierResponse> detail(@Validated(ErpSupplierRequest.detail.class) ErpSupplierRequest erpSupplierRequest) {
        ErpSupplierResponse response = erpSupplierService.detail(erpSupplierRequest);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询供应商列表
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 供应商分页列表
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @GetResource(name = "分页查询供应商列表", path = "/erp/supplier/page"/*, requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.PAGE_SUPPLIER*/)
    public ResponseData<PageResult<ErpSupplierResponse>> page(ErpSupplierRequest erpSupplierRequest) {
        PageResult<ErpSupplierResponse> pageResult = erpSupplierService.findPage(erpSupplierRequest);
        return new SuccessResponseData<>(pageResult);
    }

    /**
     * 查询供应商列表
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 供应商列表
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @GetResource(name = "查询供应商列表", path = "/erp/supplier/list"/*, requiredPermission = true, requirePermissionCode = ErpPermissionCodeConstants.LIST_SUPPLIER*/)
    public ResponseData<List<ErpSupplierResponse>> list(ErpSupplierRequest erpSupplierRequest) {
        List<ErpSupplierResponse> responseList = erpSupplierService.findList(erpSupplierRequest);
        return new SuccessResponseData<>(responseList);
    }

    /**
     * 更新供应商状态
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 操作结果
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @PostResource(name = "更新供应商状态", path = "/erp/supplier/updateStatus")
    public ResponseData<?> updateStatus(@RequestBody @Validated(ErpSupplierRequest.updateStatus.class) ErpSupplierRequest erpSupplierRequest) {
        erpSupplierService.updateStatus(erpSupplierRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 校验供应商编码是否重复
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 校验结果
     * <AUTHOR>
     * @since 2025/07/20 10:00
     */
    @GetResource(name = "校验供应商编码", path = "/erp/supplier/validateCode")
    public ResponseData<Boolean> validateCode(ErpSupplierRequest erpSupplierRequest) {
        boolean isRepeat = erpSupplierService.validateSupplierCodeRepeat(
                erpSupplierRequest.getSupplierCode(), 
                erpSupplierRequest.getSupplierId()
        );
        return new SuccessResponseData<>(!isRepeat);
    }

    /**
     * 获取供应商关联的商品列表
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 商品列表
     * <AUTHOR>
     * @since 2025/07/27 21:50
     */
    @GetResource(name = "获取供应商商品", path = "/erp/supplier/products")
    public ResponseData<List<ErpProductResponse>> getSupplierProducts(ErpSupplierRequest erpSupplierRequest) {
        List<ErpProductResponse> products = erpSupplierService.getSupplierProducts(erpSupplierRequest);
        return new SuccessResponseData<>(products);
    }

    /**
     * 校验供应商经营方式变更的影响
     *
     * @param erpSupplierRequest 供应商请求参数
     * @return 验证结果
     * <AUTHOR>
     * @since 2025/07/27 21:50
     */
    @GetResource(name = "校验经营方式变更", path = "/erp/supplier/validateBusinessModeChange")
    public ResponseData<BusinessModeChangeValidationResponse> validateBusinessModeChange(ErpSupplierRequest erpSupplierRequest) {
        BusinessModeChangeValidationResponse validation = erpSupplierService.validateBusinessModeChange(erpSupplierRequest);
        return new SuccessResponseData<>(validation);
    }

}
