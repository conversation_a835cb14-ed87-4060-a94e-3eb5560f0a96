import{_ as v}from"./index-02bf6f00.js";import{r as c,o as y,a as t,c as _,b as o,d as m,w as s,F as p,f as l,g as d,h as a,I as b,l as S,U as T}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */const k={class:"guns-layout"},F={class:"guns-layout-content"},q={class:"guns-layout"},N={class:"guns-layout-content-application"},L={class:"content-mian"},P={class:"content-mian-body"},B={class:"table-content"},Y=Object.assign({name:"AuthResource"},{__name:"index",setup(C){const g=c([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"resourceName",title:"\u8D44\u6E90\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"url",title:"\u8BF7\u6C42\u5730\u5740",width:100,isShow:!0},{dataIndex:"className",title:"\u63A7\u5236\u5668\u7C7B",width:100,isShow:!0},{dataIndex:"httpMethod",title:"HTTP\u8BF7\u6C42",width:100,isShow:!0},{dataIndex:"requiredLoginFlag",title:"\u9700\u8981\u767B\u5F55",width:100,isShow:!0},{dataIndex:"requiredPermissionFlag",title:"\u9700\u8981\u9274\u6743",width:100,isShow:!0},{dataIndex:"ipAddress",title:"\u4E0A\u6B21\u6C47\u62A5IP",width:100,isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:150,isShow:!0}]),h=c(null),u=c({searchText:""});y(()=>{});const w=()=>{h.value.reload()};return(E,e)=>{const f=b,x=S,n=T,I=v;return t(),_("div",k,[o("div",F,[o("div",q,[e[5]||(e[5]=o("div",{class:"guns-layout-content-header"},"\u8D44\u6E90",-1)),o("div",N,[o("div",L,[o("div",P,[o("div",B,[m(I,{columns:g.value,where:u.value,rowId:"resourceId",ref_key:"tableRef",ref:h,showToolTotal:!1,showTableTool:"",rowSelection:!1,url:"resource/pageList",fieldBusinessCode:"RESOURCE_TABLE"},{toolLeft:s(()=>[m(x,{value:u.value.searchText,"onUpdate:value":e[0]||(e[0]=i=>u.value.searchText=i),placeholder:"\u8D44\u6E90\u540D\u79F0\u3001\u8DEF\u5F84\u3001\u7C7B\u540D\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:w,class:"search-input",style:{width:"300px"},bordered:!1},{prefix:s(()=>[m(f,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:s(({column:i,record:r})=>[i.dataIndex=="requiredLoginFlag"?(t(),_(p,{key:0},[r.requiredLoginFlag=="Y"?(t(),l(n,{key:0,color:"blue"},{default:s(()=>e[1]||(e[1]=[d("\u662F")])),_:1,__:[1]})):a("",!0),r.requiredLoginFlag=="N"?(t(),l(n,{key:1,color:"orange"},{default:s(()=>e[2]||(e[2]=[d("\u5426")])),_:1,__:[2]})):a("",!0)],64)):a("",!0),i.dataIndex=="requiredPermissionFlag"?(t(),_(p,{key:1},[r.requiredPermissionFlag=="Y"?(t(),l(n,{key:0,color:"blue"},{default:s(()=>e[3]||(e[3]=[d("\u662F")])),_:1,__:[3]})):a("",!0),r.requiredPermissionFlag=="N"?(t(),l(n,{key:1,color:"orange"},{default:s(()=>e[4]||(e[4]=[d("\u5426")])),_:1,__:[4]})):a("",!0)],64)):a("",!0)]),_:1},8,["columns","where"])])])])])])])])}}});export{Y as default};
