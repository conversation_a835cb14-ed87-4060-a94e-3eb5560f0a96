import{R as e}from"./index-18a1ea24.js";class o{static findPage(t){return e.postAndLoadData("/erp/inventory/page",t)}static findList(t){return e.getAndLoadData("/erp/inventory/list",t)}static detail(t){return e.getAndLoadData("/erp/inventory/detail",t)}static warningList(t){return e.getAndLoadData("/erp/inventory/warning",t)}static outOfStockList(t){return e.getAndLoadData("/erp/inventory/outOfStock",t)}static inventoryValue(t){return e.getAndLoadData("/erp/inventory/value",t)}static setMinStock(t){return e.post("/erp/inventory/setMinStock",t)}static initInventory(t){return e.post("/erp/inventory/init",t)}static adjustInventory(t){return e.post("/erp/inventory/adjust",t)}static batchAdjustInventory(t){return e.post("/erp/inventory/batchAdjust",t)}static setInventoryAlert(t){return e.post("/erp/inventory/setAlert",t)}static getChangeTypeOptions(){return[{label:"\u5165\u5E93",value:"INBOUND"},{label:"\u51FA\u5E93",value:"OUTBOUND"},{label:"\u8C03\u6574",value:"ADJUSTMENT"},{label:"\u76D8\u70B9",value:"STOCKTAKING"},{label:"\u635F\u8017",value:"LOSS"},{label:"\u9000\u8D27",value:"RETURN"}]}static getInventoryStatusOptions(){return[{label:"\u6B63\u5E38",value:"NORMAL"},{label:"\u9884\u8B66",value:"WARNING"},{label:"\u7F3A\u8D27",value:"OUT_OF_STOCK"},{label:"\u6EDE\u9500",value:"SLOW_MOVING"},{label:"\u8FC7\u671F",value:"EXPIRED"}]}static getAdjustReasonOptions(){return[{label:"\u76D8\u70B9\u5DEE\u5F02",value:"STOCKTAKING_DIFF"},{label:"\u7CFB\u7EDF\u9519\u8BEF",value:"SYSTEM_ERROR"},{label:"\u5546\u54C1\u635F\u574F",value:"DAMAGED"},{label:"\u5546\u54C1\u8FC7\u671F",value:"EXPIRED"},{label:"\u5176\u4ED6\u539F\u56E0",value:"OTHER"}]}static getChangeTypeName(t){const r=o.getChangeTypeOptions().find(a=>a.value===t);return r?r.label:t}static getInventoryStatusName(t){const r=o.getInventoryStatusOptions().find(a=>a.value===t);return r?r.label:t}static getAdjustReasonName(t){const r=o.getAdjustReasonOptions().find(a=>a.value===t);return r?r.label:t}static getInventoryStatusTagColor(t){switch(t){case"NORMAL":return"green";case"WARNING":return"orange";case"OUT_OF_STOCK":return"red";case"SLOW_MOVING":return"blue";case"EXPIRED":return"red";default:return"default"}}static getChangeTypeTagColor(t){switch(t){case"INBOUND":return"green";case"OUTBOUND":return"blue";case"ADJUSTMENT":return"orange";case"STOCKTAKING":return"purple";case"LOSS":return"red";case"RETURN":return"cyan";default:return"default"}}static formatQuantity(t,n){return t==null?"-":"".concat(t).concat(n||"")}static formatValue(t){return t?"\xA5".concat(t.toFixed(2)):"\xA50.00"}static isStockWarning(t,n){return t<=n}static isOutOfStock(t){return t<=0}static calculateTurnoverRate(t,n){return!n||n===0?0:(t/n).toFixed(2)}static exportInventory(t){return e.downLoad("/erp/inventory/export",t)}static exportInventoryAlerts(t){return e.downLoad("/erp/inventory/exportAlerts",t)}static exportInventoryChanges(t){return e.downLoad("/erp/inventory/exportChanges",t)}}export{o as I};
