const b='<object id="WebBrowser" classid="clsid:8856F961-340A-11D0-A96B-00C04FD705A2" width="0" height="0"></object>',u="guns-printer-style",f="guns-printer-set",y="guns-printer-loading",d="guns-printer-printing",a="guns-printer-hide",c="guns-printer-hide-none";function E(t=8){const o="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";let n="p_";for(let s=0;s<t;s++)n+=o.charAt(Math.floor(Math.random()*o.length));return n}function g(){return!!window.ActiveXObject||"ActiveXObject"in window}function w(t=!1){return"\n    @media print {\n      html, body {\n        padding: 0;\n        margin: 0;\n      }\n    }\n\n    /* \u6253\u5370\u65F6\u4E0D\u663E\u793A\u7684\u5143\u7D20 */\n    .".concat(a,".").concat(d," {\n      visibility: hidden !important;\n    }\n    .").concat(a," {\n      ").concat(t?"visibility: hidden !important;":"","\n    }\n    .").concat(a,".").concat(d,".").concat(c,",\n    .").concat(a,".").concat(c).concat(t?"":"-no"," {\n      display: none !important;\n    }\n\n    /* \u8868\u683C\u6837\u5F0F */\n    .guns-printer-table {\n      width: 100%;\n      border-collapse: collapse;\n      border: none;\n    }\n    .guns-printer-table td, .guns-printer-table th {\n      color: #333;\n      padding: 9px 15px;\n      border: 1px solid #333;\n      word-break: break-all;\n    }\n\n    /* loading \u6837\u5F0F */\n    #").concat(y," {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: hsla(0, 0%, 100%, .9);\n      z-index: 19000000;\n    }\n    #").concat(y,':after {\n      content: "";\n      width: 40px;\n      height: 40px;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      margin: -20px auto auto -20px;\n      border: 2px solid #3296FA;\n      border-right-color: transparent;\n      border-bottom-color: transparent;\n      border-radius: 50%;\n      animation: guns-printer-loading-anim .8s linear infinite;\n    }\n    @keyframes guns-printer-loading-anim {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n\n    /* \u5E26\u9875\u7709\u9875\u811A\u9875\u9762\u6837\u5F0F */\n    .guns-printer-table-page {\n      width: 100%;\n      border-collapse: collapse;\n      border: none;\n    }\n    .guns-printer-table-page td {\n      padding: 0;\n      border: none;\n    }\n  ')}function m(){if(!document.getElementById(u)){const t=document.createElement("style");t.id=u,t.setAttribute("type","text/css"),t.innerHTML=w(),document.body.appendChild(t)}}function A(t,o){var s,r;if(Array.prototype.forEach.call(document.getElementsByClassName(a),i=>{i!=null&&i.classList&&i.classList.add(d)}),!t)return;const n=(Array==null?void 0:Array.isArray(t))||((s=NodeList==null?void 0:NodeList.prototype)==null?void 0:s.isPrototypeOf(t))||((r=HTMLCollection==null?void 0:HTMLCollection.prototype)==null?void 0:r.isPrototypeOf(t));Array.prototype.forEach.call(n?t:[t],i=>{typeof i=="string"?Array.prototype.forEach.call(document.querySelectorAll(i),e=>{e!=null&&e.classList&&(e.classList.add(a),e.classList.add(d),o&&e.classList.add(c))}):i!=null&&i.classList&&(i.classList.add(a),i.classList.add(d),o&&i.classList.add(c))})}function L(t){var n,s;if(Array.prototype.forEach.call(document.getElementsByClassName(a),r=>{r!=null&&r.classList&&r.classList.remove(d)}),!t)return;const o=(Array==null?void 0:Array.isArray(t))||((n=NodeList==null?void 0:NodeList.prototype)==null?void 0:n.isPrototypeOf(t))||((s=HTMLCollection==null?void 0:HTMLCollection.prototype)==null?void 0:s.isPrototypeOf(t));Array.prototype.forEach.call(o?t:[t],r=>{typeof r=="string"?Array.prototype.forEach.call(document.querySelectorAll(r),i=>{i!=null&&i.classList&&(i.classList.remove(a),i.classList.remove(d),i.classList.remove(c))}):r!=null&&r.classList&&(r.classList.remove(a),r.classList.remove(d),r.classList.remove(c))})}function B(t={}){var i;window.focus(),m();const o=document.getElementById(f);o&&o.parentNode&&o.parentNode.removeChild(o);const n=[];if(typeof t.horizontal=="boolean"&&n.push("size: ".concat(t.horizontal?"landscape":"portrait",";")),t.margin!=null&&n.push("margin: ".concat(t.margin,";")),n){const e=document.createElement("style");e.id=f,e.setAttribute("type","text/css"),e.setAttribute("media","print"),e.innerHTML="@page { ".concat(n.join("")," }"),document.body.appendChild(e)}A(t.hide,t.isHideNone);const s=document.title;t.title&&(document.title=t.title);let r;if(t.blank){if(r=window.open("","_blank"),r){r.focus();const e=r.document;if(e){e.open();let l="<!DOCTYPE html>"+((i=document.getElementsByTagName("html")[0])==null?void 0:i.outerHTML);l=l.replace(/<script/g,'<div style="display:none;" ').replace(/<\/script>/g,"</div>");const p=function(h){l=l.replace(/<\/html>/,"".concat(h,"</html>"))};t.iePreview!==!1&&g()?(document.getElementById("WebBrowser")||p(b),p("\n          <script>\n            window.onload = function() {\n              try {\n                window.WebBrowser.ExecWB(7,1);\n              } catch(e) {\n                console.error(e);\n                window.print();\n              }\n              ".concat(t.close!==!1?"window.close();":"","\n            }\n          <\/script>\n          "))):p("\n          <script>\n            window.onload = function() {\n              window.print();\n              ".concat(t.close!==!1?"window.close();":"","\n            }\n          <\/script>\n          ")),e.write(l),e.close()}}}else if(r=window,t.iePreview!==!1&&g()){if(!document.getElementById("WebBrowser")){const e=document.createElement("object");e.id="WebBrowser",e.setAttribute("classid","clsid:8856F961-340A-11D0-A96B-00C04FD705A2"),e.style.display="none",document.body.appendChild(e)}try{window.WebBrowser.ExecWB(7,1)}catch(e){console.error(e),r.print()}}else r.print();return t.title&&(document.title=s),L(t.hide),r}export{B as p,E as u};
