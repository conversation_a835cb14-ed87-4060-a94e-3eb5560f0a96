package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 客户主档案实体类
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
@TableName(value = "erp_customer", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ErpCustomer extends BaseEntity {

    /**
     * 客户ID
     */
    @TableId(value = "customer_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    @ChineseDescription("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    @ChineseDescription("客户名称")
    private String customerName;

    /**
     * 客户简称
     */
    @TableField("customer_short_name")
    @ChineseDescription("客户简称")
    private String customerShortName;

    /**
     * 客户类型（ENTERPRISE-企业，INDIVIDUAL-个人，RETAIL-零售）
     */
    @TableField("customer_type")
    @ChineseDescription("客户类型")
    private String customerType;

    /**
     * 客户等级（DIAMOND-钻石，GOLD-黄金，SILVER-白银，BRONZE-青铜）
     */
    @TableField("customer_level")
    @ChineseDescription("客户等级")
    private String customerLevel;

    /**
     * 所属区域ID
     */
    @TableField("region_id")
    @ChineseDescription("所属区域ID")
    private Long regionId;

    /**
     * 联系人
     */
    @TableField("contact_person")
    @ChineseDescription("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    @ChineseDescription("联系电话")
    private String contactPhone;

    /**
     * 手机号码
     */
    @TableField("contact_mobile")
    @ChineseDescription("手机号码")
    private String contactMobile;

    /**
     * 邮箱地址
     */
    @TableField("contact_email")
    @ChineseDescription("邮箱地址")
    private String contactEmail;

    /**
     * 联系地址
     */
    @TableField("contact_address")
    @ChineseDescription("联系地址")
    private String contactAddress;

    /**
     * 营业执照号
     */
    @TableField("business_license_no")
    @ChineseDescription("营业执照号")
    private String businessLicenseNo;

    /**
     * 税务登记号
     */
    @TableField("tax_no")
    @ChineseDescription("税务登记号")
    private String taxNo;

    /**
     * 开户银行
     */
    @TableField("bank_name")
    @ChineseDescription("开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @TableField("bank_account")
    @ChineseDescription("银行账号")
    private String bankAccount;

    /**
     * 信用额度
     */
    @TableField("credit_limit")
    @ChineseDescription("信用额度")
    private BigDecimal creditLimit;

    /**
     * 已用额度
     */
    @TableField("used_credit")
    @ChineseDescription("已用额度")
    private BigDecimal usedCredit;

    /**
     * 账期天数
     */
    @TableField("payment_terms")
    @ChineseDescription("账期天数")
    private Integer paymentTerms;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，FROZEN-冻结）
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;
}