<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <div class="content-mian-body">
              <div class="table-content">
                <a-tabs v-model:activeKey="activeKey" tab-position="left" animated @change="leftChange" class="left-tab">
                  <a-tab-pane :key="appItem.apiClientId" :tab="appItem.apiClientName" v-for="appItem in appList">
                    <div v-if="activeKey" class="right">
                      <a-spin :spinning="authLoading" :delay="100">
                        <div class="right-bottom">
                          <div v-if="permissionData" class="use-content">
                            <div class="content-header">
                              <a-checkbox
                                v-model:checked="permissionData.totalAuthCheckedFlag"
                                @click="el => allPermissionChange(el, permissionData)"
                              >所有接口权限</a-checkbox
                              >
                            </div>
                            <div class="content-bottom">
                              <div class="table">
                                <a-table
                                  :dataSource="permissionData.apiEndpointCheckedFlagList"
                                  :columns="columns"
                                  :pagination="false"
                                  rowKey="apiClientResourceId"
                                  bordered
                                  size="small"
                                  childrenColumnName="other"
                                >
                                  <template #bodyCell="{ column, record }">
                                    <template v-if="column.dataIndex === 'page'">
                                      <a-checkbox v-model:checked="record.apiCheckedFlag" @change="pageChange($event, record)">
                                        {{ record.nodeName }}
                                      </a-checkbox>
                                    </template>
                                  </template>
                                </a-table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </a-spin>
                    </div>
                  </a-tab-pane>
                </a-tabs>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Authorization">
import { message } from 'ant-design-vue/es';
import { ref, onMounted } from 'vue';
import { ExternalAppApi } from '../external-app/api/ExternalAppApi';
import { AuthorizationApi } from './api/AuthorizationApi';

defineOptions({
  name: 'Authorization',
})

// 应用列表
const appList = ref([]);
// 当前激活tab
const activeKey = ref('');
// 加载动画
const authLoading = ref(false);
// 功能列表数据
const permissionData = ref(null);
// 表格配置
const columns = ref([
  {
    title: '',
    width: 60,
    dataIndex: 'page'
  },
  {
    title: '接口名称',
    align: 'center',
    dataIndex: 'resourceName'
  },
  {
    title: '接口编码',
    align: 'center',
    dataIndex: 'resourceCode'
  },
  {
    title: '接口路径',
    dataIndex: 'url'
  },
  {
    title: '请求方式',
    align: 'center',
    dataIndex: 'httpMethod'
  }
]);

onMounted(() => {
  getAllAppList();
});

// 获取角色列表
const getAllAppList = () => {
  ExternalAppApi.list().then(res => {
    appList.value = res;
    if (res && res.length) {
      activeKey.value = res[0].apiClientId;
      getAppBindPermission();
    }
  });
};

// 获取角色绑定的权限列表
const getAppBindPermission = () => {
  authLoading.value = true;
  AuthorizationApi.getBindResult({ apiClientId: activeKey.value })
    .then(res => {
      permissionData.value = res;
    })
    .finally(() => (authLoading.value = false));
};

// 左侧角色切换
const leftChange = key => {
  getAppBindPermission();
};

// 所有权限改变
const allPermissionChange = (el, data) => {
  setCheckout(el.target.checked, data.apiEndpointCheckedFlagList);
  savePermission(el.target.checked, data, '1');
};

// 页面选中改变
const pageChange = (el, data) => {
  data.apiCheckedFlag = el.target.checked;
  if (permissionData.value.apiEndpointCheckedFlagList.find(item => item.apiCheckedFlag == false)) {
    permissionData.value.totalAuthCheckedFlag = false;
  } else {
    permissionData.value.totalAuthCheckedFlag = true;
  }
  savePermission(el.target.checked, data, '2');
};

// 保存功能权限
const savePermission = (checked, data, type) => {
  authLoading.value = true;
  let params = {
    checkedFlag: checked,
    selectType: type,
    apiClientId: activeKey.value
  };
  if (type == '2') {
    params.resourceCode = data.resourceCode;
  }
  AuthorizationApi.bind(params)
    .then(res => {
      message.success(res.message);
    })
    .finally(() => (authLoading.value = false));
};

// 设置选中
const setCheckout = (checked, list) => {
  if (list && list.length > 0) {
    list.forEach(item => {
      item.apiCheckedFlag = checked;
      if (item.children && item.children.length > 0) {
        setCheckout(checked, item.children);
      }
    });
  }
};
</script>

<style scoped lang="less">
.left-tab {
  height: 100%;
}

:deep(.left-tab .ant-tabs-tab-active) {
  background: rgba(24, 144, 255, 0.1);
}
:deep(.right-tab .ant-tabs-tab-active) {
  background: #fff;
}
:deep(.left-tab .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  min-width: 70px;
  text-align: center;
}
:deep(.left-tab .ant-tabs-tab-btn) {
  min-width: 70px;
  text-align: center;
}
.right {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
:deep(.ant-tabs-content) {
  height: 100%;
}
.right-top {
  margin-top: -10px;
  height: 62px;
}
.right-bottom {
  width: 100%;
  height: calc(100% - 62px);
  overflow: hidden;
}
.use-content {
  width: 100%;
  height: 100%;
  .content-header {
    height: 22px;
  }
  .content-bottom {
    width: 100%;
    margin-top: 10px;
    overflow-y: auto;
    height: calc(100% - 32px);
    .bottom-item {
      margin-bottom: 20px;
      .bottom-item-name {
        .title {
          font-size: 16px;
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
  }
}
:deep(.ant-table-thead th) {
  text-align: center;
}
:deep(.ant-table-tbody .ant-table-cell) {
  padding: 8px 8px 8px 20px !important;
}
</style>
