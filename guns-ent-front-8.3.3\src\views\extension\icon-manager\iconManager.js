/**
 * JavaGuns Enterprise ERP 图标管理工具
 * 
 * <AUTHOR>
 * @since 2025/07/31
 */

/**
 * 系统可用图标列表
 * 从 iconfont.css 中提取的所有可用图标
 */
export const AVAILABLE_ICONS = {
  // 操作类图标
  OPERATION: {
    'icon-opt-bianji': '编辑',
    'icon-opt-shanchu': '删除', 
    'icon-opt-xiangqing': '详情/查看',
    'icon-opt-shezhi': '设置',
    'icon-opt-search': '搜索',
    'icon-opt-shuaxin': '刷新',
    'icon-opt-tianjia': '添加',
    'icon-opt-daochu': '导出',
    'icon-opt-daoru': '导入',
    'icon-opt-fuzhi': '复制',
    'icon-opt-shoucang': '收藏',
    'icon-opt-yishoucang': '已收藏',
    'icon-opt-fenpeijuese': '分配角色',
    'icon-opt-ch<PERSON><PERSON><PERSON>ma': '重置密码',
    'icon-opt-tongyi': '同意',
    'icon-opt-jujue': '拒绝',
    'icon-opt-chehui': '撤回',
    'icon-opt-tuihui': '退回',
    'icon-opt-fabu': '发布',
    'icon-opt-yulan': '预览',
    'icon-opt-yidu': '已读',
    'icon-opt-qiehuan': '切换',
    'icon-opt-fangda': '放大',
    'icon-opt-suoxiao': '缩小',
    'icon-opt-zidingyilie': '自定义列'
  },

  // 菜单类图标
  MENU: {
    'icon-menu-rizhichakan': '日志查看',
    'icon-nav-zhuye': '主页',
    'icon-nav-gongsi': '公司'
  },

  // 用户操作图标
  USER: {
    'icon-opt-export-user': '导出用户',
    'icon-opt-import-user': '导入用户',
    'icon-xiala-xiugaitouxiang': '修改头像',
    'icon-xiala-tuichudenglu': '退出登录'
  }
};

/**
 * 图标映射表 - 将常用功能映射到可用图标
 */
export const ICON_MAPPING = {
  // 基础操作
  'view': 'icon-opt-xiangqing',      // 查看 -> 详情
  'detail': 'icon-opt-xiangqing',    // 详情 -> 详情
  'edit': 'icon-opt-bianji',         // 编辑 -> 编辑
  'delete': 'icon-opt-shanchu',      // 删除 -> 删除
  'add': 'icon-opt-tianjia',         // 添加 -> 添加
  'create': 'icon-opt-tianjia',      // 创建 -> 添加
  'setting': 'icon-opt-shezhi',      // 设置 -> 设置
  'config': 'icon-opt-shezhi',       // 配置 -> 设置
  'search': 'icon-opt-search',       // 搜索 -> 搜索
  'refresh': 'icon-opt-shuaxin',     // 刷新 -> 刷新
  'export': 'icon-opt-daochu',       // 导出 -> 导出
  'import': 'icon-opt-daoru',        // 导入 -> 导入
  'copy': 'icon-opt-fuzhi',          // 复制 -> 复制
  
  // 业务操作
  'confirm': 'icon-opt-tongyi',      // 确认 -> 同意
  'approve': 'icon-opt-tongyi',      // 审批 -> 同意
  'reject': 'icon-opt-jujue',        // 拒绝 -> 拒绝
  'cancel': 'icon-opt-chehui',       // 取消 -> 撤回
  'publish': 'icon-opt-fabu',        // 发布 -> 发布
  'preview': 'icon-opt-yulan',       // 预览 -> 预览
  
  // ERP特定操作
  'receive': 'icon-opt-tianjia',     // 入库 -> 添加
  'inbound': 'icon-opt-tianjia',     // 入库 -> 添加
  'outbound': 'icon-opt-daochu',     // 出库 -> 导出
  'transfer': 'icon-opt-qiehuan',    // 调拨 -> 切换
  'adjust': 'icon-opt-shezhi',       // 调整 -> 设置
  'check': 'icon-opt-xiangqing',     // 盘点 -> 详情
  'freeze': 'icon-opt-suoxiao',      // 冻结 -> 缩小
  'unfreeze': 'icon-opt-fangda',     // 解冻 -> 放大
};

/**
 * Ant Design Vue 图标备选方案
 */
export const ANT_ICON_FALLBACK = {
  'view': 'EyeOutlined',
  'detail': 'FileTextOutlined', 
  'edit': 'EditOutlined',
  'delete': 'DeleteOutlined',
  'add': 'PlusOutlined',
  'create': 'PlusCircleOutlined',
  'setting': 'SettingOutlined',
  'config': 'ToolOutlined',
  'search': 'SearchOutlined',
  'refresh': 'ReloadOutlined',
  'export': 'ExportOutlined',
  'import': 'ImportOutlined',
  'copy': 'CopyOutlined',
  'confirm': 'CheckOutlined',
  'approve': 'CheckCircleOutlined',
  'reject': 'CloseCircleOutlined',
  'cancel': 'UndoOutlined',
  'publish': 'SendOutlined',
  'preview': 'EyeOutlined',
  'receive': 'InboxOutlined',
  'inbound': 'DownloadOutlined',
  'outbound': 'UploadOutlined',
  'transfer': 'SwapOutlined',
  'adjust': 'AdjustOutlined',
  'check': 'AuditOutlined',
  'freeze': 'PauseCircleOutlined',
  'unfreeze': 'PlayCircleOutlined'
};

/**
 * 检查图标是否存在
 * @param {string} iconClass 图标类名
 * @returns {boolean} 是否存在
 */
export function checkIconExists(iconClass) {
  // 检查是否在可用图标列表中
  for (const category of Object.values(AVAILABLE_ICONS)) {
    if (category[iconClass]) {
      return true;
    }
  }
  return false;
}

/**
 * 获取推荐的图标
 * @param {string} action 操作类型
 * @returns {object} 推荐的图标信息
 */
export function getRecommendedIcon(action) {
  const iconClass = ICON_MAPPING[action];
  const antIcon = ANT_ICON_FALLBACK[action];
  
  return {
    iconFont: iconClass,
    antIcon: antIcon,
    exists: iconClass ? checkIconExists(iconClass) : false,
    description: getIconDescription(iconClass)
  };
}

/**
 * 获取图标描述
 * @param {string} iconClass 图标类名
 * @returns {string} 图标描述
 */
export function getIconDescription(iconClass) {
  for (const category of Object.values(AVAILABLE_ICONS)) {
    if (category[iconClass]) {
      return category[iconClass];
    }
  }
  return '未知图标';
}

/**
 * 获取所有可用图标的扁平列表
 * @returns {Array} 图标列表
 */
export function getAllAvailableIcons() {
  const icons = [];
  for (const [categoryName, category] of Object.entries(AVAILABLE_ICONS)) {
    for (const [iconClass, description] of Object.entries(category)) {
      icons.push({
        category: categoryName,
        iconClass,
        description
      });
    }
  }
  return icons;
}

/**
 * 搜索图标
 * @param {string} keyword 关键词
 * @returns {Array} 匹配的图标列表
 */
export function searchIcons(keyword) {
  const allIcons = getAllAvailableIcons();
  const lowerKeyword = keyword.toLowerCase();
  
  return allIcons.filter(icon => 
    icon.iconClass.toLowerCase().includes(lowerKeyword) ||
    icon.description.toLowerCase().includes(lowerKeyword)
  );
}

/**
 * 生成图标使用建议
 * @param {string} missingIcon 缺失的图标
 * @returns {object} 使用建议
 */
export function generateIconSuggestion(missingIcon) {
  // 尝试从图标名称推断功能
  let suggestedAction = 'view'; // 默认
  
  if (missingIcon.includes('chakan') || missingIcon.includes('view')) {
    suggestedAction = 'view';
  } else if (missingIcon.includes('bianji') || missingIcon.includes('edit')) {
    suggestedAction = 'edit';
  } else if (missingIcon.includes('shanchu') || missingIcon.includes('delete')) {
    suggestedAction = 'delete';
  } else if (missingIcon.includes('tianjia') || missingIcon.includes('add')) {
    suggestedAction = 'add';
  } else if (missingIcon.includes('queren') || missingIcon.includes('confirm')) {
    suggestedAction = 'confirm';
  } else if (missingIcon.includes('ruku') || missingIcon.includes('inbound')) {
    suggestedAction = 'receive';
  }
  
  const recommendation = getRecommendedIcon(suggestedAction);
  
  return {
    missingIcon,
    suggestedAction,
    recommendation,
    alternatives: searchIcons(suggestedAction)
  };
}
