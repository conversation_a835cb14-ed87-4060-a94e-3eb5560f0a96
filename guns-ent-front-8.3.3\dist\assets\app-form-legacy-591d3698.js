System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js"],(function(e,a){"use strict";var l,t,r,i,p,o,s,u,f,m,n,d,g,c,y,v,b,_,h,w,j,x,U,L,C,F;return{setters:[e=>{l=e.s,t=e.bh,r=e.bi,i=e.r,p=e.k,o=e.a,s=e.f,u=e.w,f=e.d,m=e.h,n=e.g,d=e.b,g=e.m,c=e.bk,y=e.l,v=e.u,b=e.v,_=e.y,h=e.z,w=e.A,j=e.$,x=e.G,U=e.M,L=e.H},null,null,e=>{C=e.F,F=e.a}],execute:function(){const a=["src"];e("default",{__name:"app-form",props:{form:Object},setup(e){const I=e,k=l({fileUploadUrl:`${t}${C}?secretFlag=N`,filePreviewUrl:`${t}/sysFileInfo/public/preview?fileId=`,headers:{Authorization:r()}}),z=i(!1),N=i(null),S=l({appName:[{required:!0,message:"请输入应用名称",type:"string",trigger:"blur"}],appCode:[{required:!0,message:"请输入应用编码",type:"string",trigger:"blur"}],appSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}],statusFlag:[{required:!0,message:"请选择状态",type:"number",trigger:"change"}],iconList:[{required:!0,message:"请上传应用图标",type:"array",trigger:"blur"}]}),$=e=>{const a="image/jpeg"===e.type||"image/jpg"===e.type||"image/png"===e.type||"image/tif"===e.type||"image/jfif"===e.type||"image/webp"===e.type||"image/pjp"===e.type||"image/apng"===e.type||"image/pjpeg"===e.type||"image/avif"===e.type||"image/ico"===e.type||"image/tiff"===e.type||"image/bmp"===e.type||"image/xbm"===e.type||"image/jxl"===e.type||"image/svgz"===e.type||"image/gif"===e.type||"image/svg"===e.type;if(!a)return g.error("只能上传图片!"),c.LIST_IGNORE;const l=e.size/1024/1024<5;return l||g.error("图片大小不能超过5MB!"),a&&l},q=async e=>{N.value=e.url||e.preview||e.thumbUrl,z.value=!0},A=e=>{let a=e.response?e.response.data.fileId:e.uid;F.download({token:r(),fileId:a})};return(l,t)=>{const r=y,i=v,C=b,F=p("plus-outlined"),D=c,G=_,M=h,O=w,P=j,R=x,B=U,E=L;return o(),s(E,{ref:"formRef",model:e.form,rules:S,layout:"vertical"},{default:u((()=>[f(R,{gutter:20},{default:u((()=>[f(C,{xs:24,sm:24,md:12},{default:u((()=>[f(i,{label:"应用名称:",name:"appName"},{default:u((()=>[f(r,{value:e.form.appName,"onUpdate:value":t[0]||(t[0]=a=>e.form.appName=a),"allow-clear":"",placeholder:"请输入应用名称"},null,8,["value"])])),_:1})])),_:1}),f(C,{xs:24,sm:24,md:12},{default:u((()=>[f(i,{label:"应用编码:",name:"appCode"},{default:u((()=>[f(r,{value:e.form.appCode,"onUpdate:value":t[1]||(t[1]=a=>e.form.appCode=a),"allow-clear":"",placeholder:"请输入应用编码"},null,8,["value"])])),_:1})])),_:1}),f(C,{xs:24,sm:24,md:12},{default:u((()=>[f(i,{label:"应用图标:",name:"iconList"},{default:u((()=>[f(D,{name:"file",multiple:!1,"file-list":e.form.iconList,"onUpdate:fileList":t[2]||(t[2]=a=>e.form.iconList=a),"default-file-list":e.form.iconList,maxCount:1,action:k.fileUploadUrl,"list-type":"picture-card",headers:k.headers,"before-upload":$,accept:".jpeg,.jpg,.png,.tif,.jfif,.webp,.pjp,.apng,.pjpeg,.avif,.ico,.tiff,.bmp,.xbm,.jxl,.jpeg,.svgz,.gif,.svg",onPreview:q,onDownload:A,onChange:t[3]||(t[3]=e=>((e,a)=>{"done"===e.file.status?(I.form[a]=[e.file],g.success(`${e.file.name} 图片上传成功`)):"error"===e.file.status&&g.error(`${e.file.name} 图片上传失败`)})(e,"iconList")),showUploadList:{showDownloadIcon:!0}},{default:u((()=>[0==e.form.iconList.length?(o(),s(F,{key:0,style:{"font-size":"28px","font-weight":"200"}})):m("",!0)])),_:1},8,["file-list","default-file-list","action","headers"])])),_:1})])),_:1}),f(C,{xs:24,sm:24,md:12},{default:u((()=>[f(i,{label:"排序:",name:"appSort"},{default:u((()=>[f(G,{value:e.form.appSort,"onUpdate:value":t[4]||(t[4]=a=>e.form.appSort=a),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),f(C,{xs:24,sm:24,md:12},{default:u((()=>[f(i,{label:"状态:",name:"statusFlag"},{default:u((()=>[f(O,{value:e.form.statusFlag,"onUpdate:value":t[5]||(t[5]=a=>e.form.statusFlag=a)},{default:u((()=>[f(M,{value:1},{default:u((()=>t[8]||(t[8]=[n("启用")]))),_:1,__:[8]}),f(M,{value:2},{default:u((()=>t[9]||(t[9]=[n("禁用")]))),_:1,__:[9]})])),_:1},8,["value"])])),_:1})])),_:1}),f(C,{span:24},{default:u((()=>[f(i,{label:"备注"},{default:u((()=>[f(P,{value:e.form.remark,"onUpdate:value":t[6]||(t[6]=a=>e.form.remark=a),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1}),f(B,{visible:z.value,footer:null,onCancel:t[7]||(t[7]=e=>z.value=!1)},{default:u((()=>[d("img",{alt:"example",style:{width:"100%"},src:N.value},null,8,a)])),_:1},8,["visible"])])),_:1},8,["model","rules"])}}})}}}));
