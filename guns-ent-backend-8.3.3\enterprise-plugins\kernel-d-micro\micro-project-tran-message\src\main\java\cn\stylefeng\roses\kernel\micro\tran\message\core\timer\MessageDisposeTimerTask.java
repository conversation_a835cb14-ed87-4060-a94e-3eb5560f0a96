package cn.stylefeng.roses.kernel.micro.tran.message.core.timer;

import cn.stylefeng.roses.kernel.micro.tran.message.modular.service.impl.SendingMessageChecker;
import cn.stylefeng.roses.kernel.micro.tran.message.modular.service.impl.WaitingConfirmMessageChecker;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 消息定时检查计划任务
 *
 * <AUTHOR>
 * @date 2018-05-07 23:10
 */
@Component
public class MessageDisposeTimerTask {

    @Resource
    private SendingMessageChecker sendingMessageChecker;

    @Resource
    private WaitingConfirmMessageChecker waitingConfirmMessageChecker;

    /**
     * 定时检查“待确认”但已超时的消息
     *
     * <AUTHOR>
     * @date 2021/5/18 14:01
     */
    @Scheduled(fixedRate = 10000)
    public void checkWaitingConfirmTimeOutMessages() {
        waitingConfirmMessageChecker.checkMessages();
    }

    /**
     * 定时检查“发送中”但超时没有被成功消费确认的消息
     *
     * <AUTHOR>
     * @date 2021/5/18 14:01
     */
    @Scheduled(fixedRate = 10000)
    public void checkSendingTimeOutMessage() {
        sendingMessageChecker.checkMessages();
    }
}
