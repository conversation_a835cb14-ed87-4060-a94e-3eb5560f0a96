<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>enterprise-plugins</artifactId>
        <version>8.3.3</version>
    </parent>

    <artifactId>kernel-d-sharding-jdbc</artifactId>
    <packaging>pom</packaging>

    <name>kernel-d-sharding-jdbc</name>
    <description>数据库读写分离插件</description>

    <modules>
        <module>sharding-api</module>
        <module>sharding-spring-boot-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- sharding-jdbc组件 -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
                <version>${sharding.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
