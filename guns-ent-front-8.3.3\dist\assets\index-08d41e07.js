import{_ as je,r as u,b3 as Be,L as O,o as Fe,k as We,a as s,c as o,b as i,d,w as r,f as h,F as w,e as z,a2 as ye,h as c,t as m,aR as me,aS as ke,g as I,ah as $e,at as Me,aI as Ge,aJ as He,aK as Ve,M as Je,E as qe,m as ge,I as Qe,be as Xe,l as Ye,x as Ze,bg as et,a5 as tt,j as nt,T as at,C as st,i as lt,n as ot,S as it}from"./index-18a1ea24.js";import{_ as ct}from"./index-3a0e5c06.js";import{_ as ut}from"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{P as A}from"./PermissionApi-1ac20316.js";import"./index-d0cfb2ce.js";/* empty css              */import"./OrgApi-021dd6dd.js";const dt={class:"guns-layout"},rt={class:"guns-layout-content"},pt={class:"ten-height bg-white permission"},vt={class:"permission-left"},_t={class:"permission-left-header"},ft={class:"permission-left-header-left"},ht={class:"role-list"},yt=["onClick","title"],mt={style:{"margin-left":"5px"}},kt={class:"role-list-title"},gt={key:0,class:"iconfont icon-tab-xitongjuese role-list-icon",title:"\u7CFB\u7EDF\u89D2\u8272"},Ct={key:1,class:"iconfont icon-tab-yewujuese role-list-icon",title:"\u4E1A\u52A1\u89D2\u8272"},bt={key:2,class:"iconfont icon-tab-gongsijuese role-list-icon",title:"\u516C\u53F8\u89D2\u8272"},Lt={key:1,class:"role-list-system-title"},xt={class:"permission-left-body"},wt={class:"permission-right"},It={key:0,class:"right"},St={class:"right-top"},Nt={class:"right-bottom"},Rt={key:0,class:"use-content"},Tt={class:"content-header"},Dt={class:"content-bottom"},Et={class:"bottom-item-name"},Ut={class:"title"},At={class:"table"},Kt={key:1},Pt={key:1,class:"data-power"},Ot={key:1,class:"td-item"},zt={class:"td-label"},jt={class:"td-btn"},Bt={key:0},Ft={key:1},Wt={key:2,class:"use-content"},$t={class:"content-header"},Mt={class:"content-bottom"},Gt={class:"bottom-item-name"},Ht={class:"title"},Vt={class:"table"},Jt={key:1},qt=Object.assign({name:"Premission"},{__name:"index",setup(Qt){const Ce=Ge(()=>He(()=>import("./data-scope-add-edit-f13d5baf.js"),["assets/data-scope-add-edit-f13d5baf.js","assets/index-18a1ea24.js","assets/index-747cb573.css","assets/index-3a0e5c06.js","assets/index-d0cfb2ce.js","assets/index-c7a076d8.css","assets/index-02bf6f00.js","assets/index-62f54e5b.css","assets/index-3a72e44e.css","assets/index-7acfe497.css","assets/index-27dc9b3a.css","assets/OrgApi-021dd6dd.js","assets/index-20a27405.css","assets/index-9785b617.css","assets/PermissionApi-1ac20316.js"])),j=u([]),be=u([{roleId:10,name:"\u7CFB\u7EDF\u89D2\u8272"},{roleId:15,name:"\u4E1A\u52A1\u89D2\u8272"},{roleId:20,name:"\u516C\u53F8\u89D2\u8272"}]),V=u(!1),y=u(20),B=u(!1),F=u([]),ee=u([]),C=u(""),k=u("use"),T=u(!1),b=u(null),D=u([]),te=u({selectCompanyList:[]}),ne=u([{title:"\u9875\u9762",width:300,dataIndex:"page"},{title:"\u529F\u80FD",dataIndex:"use"}]),L=u(null),K=Be(),S=u(""),Le=u([{title:"\u5E8F\u53F7",width:40,key:"index",align:"center"},{title:"\u7C7B\u578B",width:100,dataIndex:"dataScopeTypeWrapper"},{title:"\u8303\u56F4",width:100,dataIndex:"defineOrgIdWrapper"},{title:"\u521B\u5EFA\u4EBA",width:100,dataIndex:"createUserWrapper"},{title:"\u521B\u5EFA\u65F6\u95F4",width:100,dataIndex:"createTime"}]),W=u({roleId:""}),ae=u(null),$=u(!1),se=u(null),le=u([{value:1,name:"\u4E00\u7EA7"},{value:2,name:"\u4E8C\u7EA7"},{value:3,name:"\u4E09\u7EA7"},{value:4,name:"\u56DB\u7EA7"},{value:5,name:"\u4E94\u7EA7"},{value:6,name:"\u516D\u7EA7"},{value:7,name:"\u4E03\u7EA7"},{value:8,name:"\u516B\u7EA7"},{value:9,name:"\u4E5D\u7EA7"},{value:10,name:"\u5341\u7EA7"}]),J=O(()=>t=>!!K.authorities.find(e=>e==t)),P=O(()=>K.info.superAdminFlag),xe=O(()=>{let e=K.info.userOrgInfoList.filter(a=>a.currentSelectFlag);if(e.length>0)return e[0]}),we=O(()=>t=>{let e="";return t!=null&&t.defineOrgListWrapper&&(e=t.defineOrgListWrapper.join(",")),e}),Ie=O(()=>t=>{let e="";if(t!=null&&t.organizationLevel){let a=le.value.find(l=>l.value==t.organizationLevel.levelNumber);e="".concat(t.organizationLevel.levelName,"(").concat(a.name,")")}return e}),N=u({});Fe(()=>{var t;N.value=xe.value,S.value=(t=N.value)==null?void 0:t.companyName,q()});const q=()=>{var a;let t={roleType:y.value};y.value==20&&(t.roleCompanyId=(a=N.value)==null?void 0:a.companyId);let e;y.value==15?e=A.getRoleCategoryAndRoleTree():e=A.getRoleList(t),e.then(l=>{let x=null;y.value==15?(j.value=oe(l),x=ie(l)):(j.value=l,x=(l==null?void 0:l.length)>0?l[0]:null),x?(C.value=x.roleId,F.value=[x.roleId],K.authorities.find(g=>g==="CHANGE_ROLE_PERMISSION")?(k.value="use",G("use")):K.authorities.find(g=>g==="CHANGE_ROLE_DATA_SCOPE")?(k.value="data",G("data")):K.authorities.find(g=>g==="CHANGE_ROLE_BIND_LIMIT")&&(k.value="range",G("range"))):(C.value="",F.value=[],b.value=[],L.value=[])})},oe=t=>((t==null?void 0:t.length)>0&&t.forEach(e=>{var a;e.roleName=e.roleTreeNodeName,e.roleId=e.roleTreeNodeId,e.nodeType==1&&(e.disabled=!0),((a=e.children)==null?void 0:a.length)>0&&(e.children=oe(e.children))}),t),ie=t=>{for(const e of t){if(e.nodeType===2)return e;if(e.children&&e.children.length>0){const a=ie(e.children);if(a)return a}}return null},Se=t=>{t.roleId!=y.value&&(t.roleId==10?S.value="\u7CFB\u7EDF\u89D2\u8272":t.roleId==15?S.value="\u4E1A\u52A1\u89D2\u8272":S.value=N.value.companyName,y.value=t.roleId,q()),V.value=!1},ce=()=>{P.value&&y.value==20&&(te.value.selectCompanyList=[{bizId:N.value.companyId,name:N.value.companyName}],B.value=!0)},Ne=t=>{N.value=t.selectCompanyList.map(e=>({companyName:e.name,companyId:e.bizId}))[0],S.value=N.value.companyName,q()},ue=()=>{D.value=[],T.value=!0,A.getRoleBindPermission({roleId:C.value}).then(t=>{t&&(Q(t.appPermissionList),b.value=t)}).finally(()=>T.value=!1)},Q=t=>{(t==null?void 0:t.length)>0&&t.forEach(e=>{var a;((a=e==null?void 0:e.children)==null?void 0:a.length)>0&&(D.value.push(e.nodeId),Q(e.children))})},M=()=>{W.value.roleId=C.value,Ve(()=>{ae.value.reload()})},de=t=>{se.value=t,$.value=!0},Re=t=>{Je.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6570\u636E\u6743\u9650\u5417?",icon:d(qe),maskClosable:!0,onOk:async()=>{const e=await A.roleDataScopeDelete({roleDataScopeId:t.roleDataScopeId});ge.success(e.message),M()}})},Te=()=>{D.value=[],T.value=!0,A.getRoleLimit({roleId:C.value}).then(t=>{Q(t.appPermissionList),L.value=t}).finally(()=>T.value=!1)},G=t=>{t=="data"?M():t=="range"?Te():t=="use"&&ue()},De=(t,{node:e})=>{e.roleId!=C.value&&(C.value=e.roleId,k.value="use",ue())},re=(t,e)=>{H(t,e,"appPermissionList",!0)},H=(t,e,a,l,x,g)=>{e.checked=t.target.checked;const Y=Ee([g]);l&&X(t.target.checked,a?e[a]:[e]),g&&(Y.find(E=>E.checked==!1)?g.checked=!1:g.checked=!0),x&&(k.value=="use"?b.value.appPermissionList.find(E=>E.checked==!1)?b.value.checked=!1:b.value.checked=!0:k.value=="range"&&(L.value.appPermissionList.find(E=>E.checked==!1)?L.value.checked=!1:L.value.checked=!0)),Ue(e)},Ee=t=>{const e=[];function a(l){l!=null&&l.leafFlag&&(e.push(l),l!=null&&l.functionList&&Array.isArray(l.functionList)&&e.push(...l.functionList)),l!=null&&l.children&&Array.isArray(l.children)&&l.children.forEach(a)}return t.forEach(a),e},pe=(t,e)=>{H(t,e,"",!0,!0)},ve=(t,e,a)=>{H(t,e,"",!0,!0,a)},_e=(t,e,a)=>{H(t,e,"",!1,!0,a)},Ue=t=>{T.value=!0;let e={checked:t.checked,nodeId:t.nodeId?t.nodeId:"",permissionNodeType:t.permissionNodeType,roleId:C.value},a;k.value=="use"?a=A.updateRoleBindPermission(e):k.value=="range"&&(a=A.bindRoleLimit(e)),a.then(l=>{ge.success(l.message)}).finally(()=>T.value=!1)},X=(t,e)=>{e&&e.length>0&&e.forEach(a=>{a.checked=t,a.functionList&&a.functionList.length>0&&X(t,a.functionList),a.children&&a.children.length>0&&X(t,a.children)})};return(t,e)=>{const a=Qe,l=We("down-outlined"),x=Xe,g=Ye,Y=Ze,E=et,fe=tt,Z=nt,Ae=at,R=st,he=lt,Ke=ot,Pe=ut,Oe=it,ze=ct;return s(),o("div",dt,[i("div",rt,[i("div",pt,[i("div",vt,[i("div",_t,[d(Y,{compact:""},{default:r(()=>[i("div",ft,[P.value?(s(),h(x,{key:0,trigger:"click",placement:"bottomLeft",visible:V.value,"onUpdate:visible":e[0]||(e[0]=n=>V.value=n),overlayClassName:"role-popover"},{content:r(()=>[i("div",ht,[(s(!0),o(w,null,z(be.value,n=>(s(),o("div",{class:ye(["role-list-item",[{"role-active":n.roleId==y.value}]]),key:n.roleId,onClick:f=>Se(n),title:n.name},[n.roleId==10?(s(),h(a,{key:0,iconClass:"icon-tab-xitongjuese",color:"var(--primary-color)","font-size":"24px"})):c("",!0),n.roleId==15?(s(),h(a,{key:1,iconClass:"icon-tab-yewujuese",color:"var(--primary-color)","font-size":"24px"})):c("",!0),n.roleId==20?(s(),h(a,{key:2,iconClass:"icon-tab-gongsijuese",color:"var(--primary-color)","font-size":"24px"})):c("",!0),i("span",mt,m(n.name),1)],10,yt))),128))])]),default:r(()=>[i("span",kt,[y.value==10?(s(),o("i",gt)):c("",!0),y.value==15?(s(),o("i",Ct)):c("",!0),y.value==20?(s(),o("i",bt)):c("",!0),i("span",null,m(y.value==10?"\u7CFB\u7EDF":y.value==15?"\u4E1A\u52A1":"\u516C\u53F8"),1),P.value?(s(),h(l,{key:3,class:"role-list-down"})):c("",!0)])]),_:1},8,["visible"])):(s(),o("span",Lt,[e[15]||(e[15]=i("i",{class:"iconfont icon-tab-gongsijuese role-list-icon",title:"\u516C\u53F8\u89D2\u8272"},null,-1)),e[16]||(e[16]=i("span",null,"\u516C\u53F8",-1)),d(l,{class:"role-list-down"})]))]),d(g,{value:S.value,"onUpdate:value":e[1]||(e[1]=n=>S.value=n),title:S.value,onClick:ce,class:ye(y.value==20&&P.value?"role-list-input":"system-input")},{suffix:r(()=>[y.value==20&&P.value?(s(),h(l,{key:0,class:"input-down",onClick:ce})):c("",!0)]),_:1},8,["value","title","class"])]),_:1})]),i("div",xt,[d(E,{"show-icon":y.value==15,selectedKeys:F.value,"onUpdate:selectedKeys":e[2]||(e[2]=n=>F.value=n),expandedKeys:ee.value,"onUpdate:expandedKeys":e[3]||(e[3]=n=>ee.value=n),onSelect:De,"tree-data":j.value,fieldNames:{children:"children",title:"roleName",key:"roleId",value:"roleId"}},{icon:r(n=>[(n==null?void 0:n.nodeType)==1?(s(),h(a,{key:0,"icon-class":"icon-tree-wenjianjia",color:"#43505e",fontSize:"24px"})):c("",!0),(n==null?void 0:n.nodeType)==2?(s(),h(a,{key:1,"icon-class":"icon-menu-juese",color:"#43505e",fontSize:"24px"})):c("",!0)]),_:1},8,["show-icon","selectedKeys","expandedKeys","tree-data"]),me(d(fe,{class:"empty"},null,512),[[ke,j.value.length==0]])])]),i("div",wt,[C.value?(s(),o("div",It,[d(Oe,{spinning:T.value,delay:100},{default:r(()=>[i("div",St,[d(Ae,{activeKey:k.value,"onUpdate:activeKey":e[4]||(e[4]=n=>k.value=n),animated:"",class:"right-tab",onChange:G},{default:r(()=>[J.value("CHANGE_ROLE_PERMISSION")?(s(),h(Z,{key:"use",tab:"\u529F\u80FD\u6743\u9650"})):c("",!0),J.value("CHANGE_ROLE_DATA_SCOPE")?(s(),h(Z,{key:"data",tab:"\u6570\u636E\u6743\u9650"})):c("",!0),J.value("CHANGE_ROLE_BIND_LIMIT")?(s(),h(Z,{key:"range",tab:"\u6743\u9650\u8303\u56F4"})):c("",!0)]),_:1},8,["activeKey"])]),i("div",Nt,[k.value=="use"&&b.value?(s(),o("div",Rt,[i("div",Tt,[d(R,{checked:b.value.checked,"onUpdate:checked":e[5]||(e[5]=n=>b.value.checked=n),onClick:e[6]||(e[6]=n=>re(n,b.value))},{default:r(()=>e[17]||(e[17]=[I("\u6240\u6709\u6743\u9650")])),_:1,__:[17]},8,["checked"])]),i("div",Dt,[(s(!0),o(w,null,z(b.value.appPermissionList,(n,f)=>(s(),o("div",{class:"bottom-item",key:f},[i("div",Et,[i("span",Ut,"\u5E94\u7528\uFF1A"+m(n.nodeName),1),d(R,{checked:n.checked,"onUpdate:checked":_=>n.checked=_,onClick:_=>pe(_,n)},{default:r(()=>e[18]||(e[18]=[I("\u5168\u9009")])),_:2,__:[18]},1032,["checked","onUpdate:checked","onClick"])]),i("div",At,[d(he,{dataSource:n.children,columns:ne.value,expandedRowKeys:D.value,"onUpdate:expandedRowKeys":e[7]||(e[7]=_=>D.value=_),pagination:!1,checkStrictly:!0,rowKey:"nodeId",bordered:"",size:"small"},{bodyCell:r(({column:_,record:v})=>[_.dataIndex==="page"?(s(),o(w,{key:0},[v.leafFlag?(s(),h(R,{key:0,checked:v.checked,"onUpdate:checked":p=>v.checked=p,onChange:p=>ve(p,v,n)},{default:r(()=>[I(m(v.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"])):(s(),o("span",Kt,m(v.nodeName),1))],64)):_.dataIndex==="use"&&v.functionList?(s(!0),o(w,{key:1},z(v.functionList,p=>(s(),h(R,{checked:p.checked,"onUpdate:checked":U=>p.checked=U,onChange:U=>_e(U,p,n),key:p.nodeId},{default:r(()=>[I(m(p.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"]))),128)):c("",!0)]),_:2},1032,["dataSource","columns","expandedRowKeys"])])]))),128))])])):c("",!0),k.value=="data"?(s(),o("div",Pt,[d(Pe,{columns:Le.value,ref_key:"tableRef",ref:ae,where:W.value,rowKey:"roleDataScopeId",size:"middle",isInit:!1,rowSelection:!1,showToolTotal:!1,url:"/roleDataScope/getRoleDataScopePageList"},{toolLeft:r(()=>[d(g,{value:W.value.searchText,"onUpdate:value":e[8]||(e[8]=n=>W.value.searchText=n),bordered:!1,allowClear:"",size:"small",placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",onPressEnter:M,style:{width:"220px"}},{prefix:r(()=>[d(a,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),toolRight:r(()=>[d(a,{iconClass:"icon-opt-tianjia","font-size":"24px",title:"\u65B0\u589E",color:"#60666b",onClick:e[9]||(e[9]=n=>de())})]),bodyCell:r(({column:n,record:f,index:_})=>{var v;return[(n==null?void 0:n.key)=="index"?(s(),o(w,{key:0},[I(m(_+1),1)],64)):c("",!0),(n==null?void 0:n.dataIndex)=="dataScopeTypeWrapper"?(s(),o("div",Ot,[i("div",zt,m(f.dataScopeTypeWrapper),1),i("div",jt,[d(Ke,null,{default:r(()=>[d(a,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:p=>de(f)},null,8,["onClick"]),d(a,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:p=>Re(f)},null,8,["onClick"])]),_:2},1024)])])):c("",!0),(n==null?void 0:n.dataIndex)=="defineOrgIdWrapper"?(s(),o(w,{key:2},[f.dataScopeType==41?(s(),o("span",Bt,m(f.defineOrgIdWrapper),1)):c("",!0),f.dataScopeType==40?(s(),o("span",Ft,m(we.value(f)),1)):c("",!0),f.dataScopeType==32?(s(),o("span",{key:2,style:$e({color:(v=f==null?void 0:f.organizationLevel)==null?void 0:v.levelColor})},m(Ie.value(f)),5)):c("",!0)],64)):c("",!0)]}),_:1},8,["columns","where"])])):c("",!0),k.value=="range"&&L.value?(s(),o("div",Wt,[i("div",$t,[d(R,{checked:L.value.checked,"onUpdate:checked":e[10]||(e[10]=n=>L.value.checked=n),onClick:e[11]||(e[11]=n=>re(n,L.value))},{default:r(()=>e[19]||(e[19]=[I("\u6240\u6709\u6743\u9650")])),_:1,__:[19]},8,["checked"])]),i("div",Mt,[(s(!0),o(w,null,z(L.value.appPermissionList,(n,f)=>(s(),o("div",{class:"bottom-item",key:f},[i("div",Gt,[i("span",Ht,"\u5E94\u7528\uFF1A"+m(n.nodeName),1),d(R,{checked:n.checked,"onUpdate:checked":_=>n.checked=_,onClick:_=>pe(_,n)},{default:r(()=>e[20]||(e[20]=[I("\u5168\u9009")])),_:2,__:[20]},1032,["checked","onUpdate:checked","onClick"])]),i("div",Vt,[d(he,{dataSource:n.children,columns:ne.value,pagination:!1,rowKey:"nodeId",bordered:"",expandedRowKeys:D.value,"onUpdate:expandedRowKeys":e[12]||(e[12]=_=>D.value=_),checkStrictly:!0,size:"small"},{bodyCell:r(({column:_,record:v})=>[_.dataIndex==="page"?(s(),o(w,{key:0},[v.leafFlag?(s(),h(R,{key:0,checked:v.checked,"onUpdate:checked":p=>v.checked=p,onChange:p=>ve(p,v,n)},{default:r(()=>[I(m(v.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"])):(s(),o("span",Jt,m(v.nodeName),1))],64)):_.dataIndex==="use"&&v.functionList?(s(!0),o(w,{key:1},z(v.functionList,p=>(s(),h(R,{checked:p.checked,"onUpdate:checked":U=>p.checked=U,onChange:U=>_e(U,p,n),key:p.nodeId},{default:r(()=>[I(m(p.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"]))),128)):c("",!0)]),_:2},1032,["dataSource","columns","expandedRowKeys"])])]))),128))])])):c("",!0)])]),_:1},8,["spinning"])])):c("",!0),me(d(fe,{class:"right-empty"},null,512),[[ke,!C.value]])])])]),B.value?(s(),h(ze,{key:0,visible:B.value,"onUpdate:visible":e[13]||(e[13]=n=>B.value=n),title:"\u9009\u62E9\u516C\u53F8",data:te.value,showTab:["company"],onDone:Ne},null,8,["visible","data"])):c("",!0),$.value?(s(),h(Me(Ce),{key:1,visible:$.value,"onUpdate:visible":e[14]||(e[14]=n=>$.value=n),data:se.value,levelNumberList:le.value,onDone:M,roleId:C.value},null,8,["visible","data","levelNumberList","roleId"])):c("",!0)])}}}),un=je(qt,[["__scopeId","data-v-59f45816"]]);export{un as default};
