System.register(["./index-legacy-ee1db0c7.js","./external-app-form-legacy-9e9e11fc.js","./ExternalAppApi-legacy-720c9516.js","./index-legacy-94a6fc23.js","./print-legacy-bf2789b6.js"],(function(e,a){"use strict";var t,l,i,n,s,o,u,d,r,p;return{setters:[e=>{t=e.r,l=e.o,i=e.a,n=e.f,s=e.w,o=e.d,u=e.m,d=e.M},e=>{r=e.default},e=>{p=e.E},null,null],execute:function(){e("default",{__name:"external-app-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const v=e,c=a,f=t(!1),m=t(!1),g=t({apiClientStatus:1,apiClientSort:100,apiClientTokenExpiration:7200}),y=t(null);l((()=>{v.data?(m.value=!0,b()):m.value=!1}));const b=()=>{p.detail({apiClientId:v.data.apiClientId}).then((e=>{g.value=Object.assign({},e)}))},x=e=>{c("update:visible",e)},C=async()=>{y.value.$refs.formRef.validate().then((async e=>{if(e){f.value=!0;let e=null;e=m.value?p.edit(g.value):p.add(g.value),e.then((async e=>{f.value=!1,u.success(e.message),x(!1),c("done")})).catch((()=>{f.value=!1}))}}))};return(e,a)=>{const t=d;return i(),n(t,{width:700,maskClosable:!1,visible:v.visible,"confirm-loading":f.value,forceRender:!0,title:m.value?"编辑外部应用":"新建外部应用","body-style":{paddingBottom:"8px",height:"610px",overflowY:"auto"},"onUpdate:visible":x,onOk:C,onClose:a[1]||(a[1]=e=>x(!1))},{default:s((()=>[o(r,{form:g.value,"onUpdate:form":a[0]||(a[0]=e=>g.value=e),ref_key:"appFormRef",ref:y,isUpdate:m.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}})}}}));
