System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./ProductSelector-legacy-b94adcaf.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./ProductApi-legacy-33feae42.js"],(function(e,t){"use strict";var a,u,l,d,n,r,o,s,i,c,p,g,f,m,v,y,h,_,C,j,k,b,S,w,T,E,P,I,F,R;return{setters:[e=>{a=e._,u=e.r,l=e.s,d=e.X,n=e.a,r=e.f,o=e.w,s=e.d,i=e.g,c=e.b,p=e.t,g=e.a2,f=e.h,m=e.c,v=e.m,y=e.B,h=e.a3,_=e.Y,C=e.Z,j=e.a0,k=e.u,b=e.W,S=e.J,w=e.v,T=e.y,E=e.G,P=e.$,I=e.H,F=e.M},null,e=>{R=e._},null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".inventory-adjust-content[data-v-5ee8647b]{max-height:70vh;overflow-y:auto}.product-name[data-v-5ee8647b]{font-weight:500;color:#1890ff}.stock-normal[data-v-5ee8647b]{color:#52c41a;font-weight:500}.stock-warning[data-v-5ee8647b]{color:#faad14;font-weight:500}.stock-danger[data-v-5ee8647b]{color:#ff4d4f;font-weight:500}.quantity-increase[data-v-5ee8647b]{color:#52c41a;font-weight:500}.quantity-decrease[data-v-5ee8647b]{color:#ff4d4f;font-weight:500}.value-increase[data-v-5ee8647b]{color:#52c41a;font-weight:500}.value-decrease[data-v-5ee8647b]{color:#ff4d4f;font-weight:500}\n",document.head.appendChild(t);const A={name:"InventoryAdjustModal",components:{ProductSelector:R},props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:t}){const a=u(null),n=u(!1),r=u({}),o=l({productId:null,adjustType:"INCREASE",adjustQuantity:null,adjustReason:"STOCKTAKING",unitCost:null,remark:""}),s=l({show:!1,beforeStock:0,afterStock:0,quantityChange:0,valueChange:0});d((()=>e.data),(e=>{e&&e.productId&&(r.value={...e},o.productId=e.productId,c())}),{immediate:!0}),d([()=>o.adjustType,()=>o.adjustQuantity,()=>o.unitCost],(()=>{c()})),d((()=>e.visible),(t=>{t&&(e.data.productId||i())}));const i=()=>{Object.assign(o,{productId:null,adjustType:"INCREASE",adjustQuantity:null,adjustReason:"STOCKTAKING",unitCost:null,remark:""}),r.value={},s.show=!1,a.value&&a.value.clearValidate()},c=()=>{if(!r.value.productId||!o.adjustQuantity)return void(s.show=!1);const e=parseFloat(r.value.currentStock)||0,t=parseFloat(o.adjustQuantity)||0,a=parseFloat(o.unitCost)||0;let u=e,l=0;switch(o.adjustType){case"INCREASE":u=e+t,l=t;break;case"DECREASE":u=Math.max(0,e-t),l=-t;break;case"SET":u=t,l=t-e}s.show=!0,s.beforeStock=e,s.afterStock=u,s.quantityChange=l,s.valueChange=a>0?l*a:0};return{formRef:a,loading:n,selectedProduct:r,adjustForm:o,adjustPreview:s,rules:{productId:[{required:!0,message:"请选择商品",trigger:"change"}],adjustType:[{required:!0,message:"请选择调整类型",trigger:"change"}],adjustQuantity:[{required:!0,message:"请输入调整数量",trigger:"blur"},{type:"number",min:.001,message:"调整数量必须大于0",trigger:"blur"}],adjustReason:[{required:!0,message:"请选择调整原因",trigger:"change"}]},onProductChange:(e,t)=>{t?(r.value={...t},c()):(r.value={},s.show=!1)},getMinQuantity:()=>(o.adjustType,.001),getPrecision:()=>"WEIGHT"===r.value.pricingType?3:0,getStep:()=>"WEIGHT"===r.value.pricingType?.001:1,getQuantityPlaceholder:()=>{switch(o.adjustType){case"INCREASE":return"请输入增加数量";case"DECREASE":return"请输入减少数量";case"SET":return"请输入设置数量";default:return"请输入数量"}},getStockUnit:(e,t)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"件";default:return t||"个"}},formatStock:(e,t)=>{if(!e)return"0";const a="WEIGHT"===t?3:0;return parseFloat(e).toFixed(a)},formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",getStockClass:(e,t)=>{const a=parseFloat(e)||0,u=parseFloat(t)||0;return a<=0?"stock-danger":a<=u?"stock-warning":"stock-normal"},formatQuantityChange:e=>{const t=parseFloat(e)||0;return t>=0?`+${t}`:`${t}`},getQuantityChangeClass:e=>(parseFloat(e)||0)>=0?"quantity-increase":"quantity-decrease",formatValueChange:e=>{const t=parseFloat(e)||0;return t>=0?`+¥${t.toFixed(2)}`:`-¥${Math.abs(t).toFixed(2)}`},getValueChangeClass:e=>(parseFloat(e)||0)>=0?"value-increase":"value-decrease",handleCancel:()=>{t("update:visible",!1)},handleSubmit:()=>{a.value.validate().then((async()=>{n.value=!0;try{o.productId,o.adjustType,o.adjustQuantity,o.adjustReason,o.unitCost,o.remark,v.success("库存调整成功"),t("ok")}catch(e){v.error("库存调整失败："+(e.message||"未知错误"))}finally{n.value=!1}}))}}}},Q={class:"inventory-adjust-content"},x={class:"product-name"},q={key:1};e("default",a(A,[["render",function(e,t,a,u,l,d){const v=y,A=h,U=_,N=C,G=j,M=R,O=k,H=b,K=S,V=w,z=T,W=E,$=P,D=I,L=F;return n(),r(L,{visible:a.visible,title:"库存调整",width:800,maskClosable:!1,onCancel:u.handleCancel},{footer:o((()=>[s(v,{onClick:u.handleCancel},{default:o((()=>t[6]||(t[6]=[i("取消")]))),_:1,__:[6]},8,["onClick"]),s(v,{type:"primary",loading:u.loading,onClick:u.handleSubmit},{default:o((()=>t[7]||(t[7]=[i(" 确认调整 ")]))),_:1,__:[7]},8,["loading","onClick"])])),default:o((()=>[c("div",Q,[s(A,{message:"库存调整提醒",description:"库存调整将直接影响商品库存数量和价值，请谨慎操作。调整后的库存变化将记录在库存历史中。",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}}),a.data.productId?(n(),r(G,{key:0,title:"商品信息",size:"small",style:{"margin-bottom":"16px"}},{default:o((()=>[s(N,{column:2,bordered:"",size:"small"},{default:o((()=>[s(U,{label:"商品名称"},{default:o((()=>[c("span",x,p(a.data.productName),1)])),_:1}),s(U,{label:"商品编码"},{default:o((()=>[i(p(a.data.productCode),1)])),_:1}),s(U,{label:"当前库存"},{default:o((()=>[c("span",{class:g(u.getStockClass(a.data.currentStock,a.data.minStock))},p(u.formatStock(a.data.currentStock,a.data.pricingType))+" "+p(u.getStockUnit(a.data.pricingType,a.data.unit)),3)])),_:1}),s(U,{label:"库存价值"},{default:o((()=>[i(" ¥"+p(u.formatAmount(a.data.totalValue)),1)])),_:1})])),_:1})])),_:1})):f("",!0),s(G,{title:"调整信息",size:"small"},{default:o((()=>[s(D,{ref:"formRef",model:u.adjustForm,rules:u.rules,layout:"vertical"},{default:o((()=>[a.data.productId?f("",!0):(n(),r(O,{key:0,label:"选择商品",name:"productId",required:""},{default:o((()=>[s(M,{value:u.adjustForm.productId,"onUpdate:value":t[0]||(t[0]=e=>u.adjustForm.productId=e),filter:{businessModeList:["PURCHASE_SALE","CONSIGNMENT"]},onChange:u.onProductChange},null,8,["value","onChange"])])),_:1})),s(W,{gutter:16},{default:o((()=>[s(V,{span:12},{default:o((()=>[s(O,{label:"调整类型",name:"adjustType",required:""},{default:o((()=>[s(K,{value:u.adjustForm.adjustType,"onUpdate:value":t[1]||(t[1]=e=>u.adjustForm.adjustType=e),placeholder:"请选择调整类型"},{default:o((()=>[s(H,{value:"INCREASE"},{default:o((()=>t[8]||(t[8]=[i("增加库存")]))),_:1,__:[8]}),s(H,{value:"DECREASE"},{default:o((()=>t[9]||(t[9]=[i("减少库存")]))),_:1,__:[9]}),s(H,{value:"SET"},{default:o((()=>t[10]||(t[10]=[i("设置库存")]))),_:1,__:[10]})])),_:1},8,["value"])])),_:1})])),_:1}),s(V,{span:12},{default:o((()=>[s(O,{label:"调整数量",name:"adjustQuantity",required:""},{default:o((()=>[s(z,{value:u.adjustForm.adjustQuantity,"onUpdate:value":t[2]||(t[2]=e=>u.adjustForm.adjustQuantity=e),min:u.getMinQuantity(),precision:u.getPrecision(),step:u.getStep(),style:{width:"100%"},placeholder:u.getQuantityPlaceholder()},{addonAfter:o((()=>[i(p(u.getStockUnit(u.selectedProduct.pricingType,u.selectedProduct.unit)),1)])),_:1},8,["value","min","precision","step","placeholder"])])),_:1})])),_:1})])),_:1}),s(W,{gutter:16},{default:o((()=>[s(V,{span:12},{default:o((()=>[s(O,{label:"调整原因",name:"adjustReason",required:""},{default:o((()=>[s(K,{value:u.adjustForm.adjustReason,"onUpdate:value":t[3]||(t[3]=e=>u.adjustForm.adjustReason=e),placeholder:"请选择调整原因"},{default:o((()=>[s(H,{value:"STOCKTAKING"},{default:o((()=>t[11]||(t[11]=[i("盘点调整")]))),_:1,__:[11]}),s(H,{value:"DAMAGE"},{default:o((()=>t[12]||(t[12]=[i("商品损坏")]))),_:1,__:[12]}),s(H,{value:"LOSS"},{default:o((()=>t[13]||(t[13]=[i("商品丢失")]))),_:1,__:[13]}),s(H,{value:"EXPIRE"},{default:o((()=>t[14]||(t[14]=[i("商品过期")]))),_:1,__:[14]}),s(H,{value:"RETURN"},{default:o((()=>t[15]||(t[15]=[i("退货入库")]))),_:1,__:[15]}),s(H,{value:"OTHER"},{default:o((()=>t[16]||(t[16]=[i("其他原因")]))),_:1,__:[16]})])),_:1},8,["value"])])),_:1})])),_:1}),s(V,{span:12},{default:o((()=>[s(O,{label:"单位成本",name:"unitCost"},{default:o((()=>[s(z,{value:u.adjustForm.unitCost,"onUpdate:value":t[4]||(t[4]=e=>u.adjustForm.unitCost=e),min:0,precision:2,step:.01,style:{width:"100%"},placeholder:"请输入单位成本（可选）"},{addonBefore:o((()=>t[17]||(t[17]=[i("¥")]))),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),s(O,{label:"调整备注",name:"remark"},{default:o((()=>[s($,{value:u.adjustForm.remark,"onUpdate:value":t[5]||(t[5]=e=>u.adjustForm.remark=e),placeholder:"请输入调整备注",rows:3,maxlength:200,showCount:""},null,8,["value"])])),_:1})])),_:1},8,["model","rules"]),u.adjustPreview.show?(n(),r(G,{key:0,title:"调整预览",size:"small",style:{"margin-top":"16px",background:"#fafafa"}},{default:o((()=>[s(N,{column:2,size:"small"},{default:o((()=>[s(U,{label:"调整前库存"},{default:o((()=>[i(p(u.formatStock(u.adjustPreview.beforeStock,u.selectedProduct.pricingType))+" "+p(u.getStockUnit(u.selectedProduct.pricingType,u.selectedProduct.unit)),1)])),_:1}),s(U,{label:"调整后库存"},{default:o((()=>[c("span",{class:g(u.getStockClass(u.adjustPreview.afterStock,u.selectedProduct.minStock))},p(u.formatStock(u.adjustPreview.afterStock,u.selectedProduct.pricingType))+" "+p(u.getStockUnit(u.selectedProduct.pricingType,u.selectedProduct.unit)),3)])),_:1}),s(U,{label:"数量变化"},{default:o((()=>[c("span",{class:g(u.getQuantityChangeClass(u.adjustPreview.quantityChange))},p(u.formatQuantityChange(u.adjustPreview.quantityChange))+" "+p(u.getStockUnit(u.selectedProduct.pricingType,u.selectedProduct.unit)),3)])),_:1}),s(U,{label:"价值变化"},{default:o((()=>[u.adjustPreview.valueChange?(n(),m("span",{key:0,class:g(u.getValueChangeClass(u.adjustPreview.valueChange))},p(u.formatValueChange(u.adjustPreview.valueChange)),3)):(n(),m("span",q,"-"))])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-5ee8647b"]]))}}}));
