package cn.stylefeng.roses.kernel.ca.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 统一认证异常枚举
 *
 * <AUTHOR>
 * @date 2021/1/20 16:42
 */
@Getter
public enum CaServerExceptionEnum implements AbstractExceptionEnum {

    /**
     * clientId为空
     */
    CLIENT_IS_NULL("B3001", "客户端id参数为空，请携带clientId参数"),

    /**
     * client信息不存在
     */
    CLIENT_NOT_EXIST("B3002", "业务客户端不存在，请传递正确clientId参数"),

    /**
     * Cookie获取的用户信息为空
     */
    COOKIE_IS_NULL("B3003", "当前用户不存在cookie信息"),

    /**
     * CA用户session过期
     */
    CA_SESSION_EXPIRED("B3004", "统一认证中心获取不到用户的会话信息，token={}"),

    /**
     * 创建客户端token过程中，jwt秘钥或jwt过期时间是空
     */
    JWT_SECRET_OR_EXPIRED_TIME_NULL("B3005", "创建客户端token过程中，jwt秘钥或jwt过期时间是空"),

    /**
     * 创建客户端token过程中，加密解密数据的秘钥为空
     */
    AES_SECRET_IS_NULL("B3006", "创建客户端token过程中，加密解密数据的秘钥为空"),

    /**
     * 无法创建回调url，请求参数没有带callbackUrl并且数据库中callbackUrl为空
     */
    CALLBACK_URL_NULL("B3007", "无法创建回调url，请求参数没有带callbackUrl并且数据库中callbackUrl为空"),

    /**
     * 登录失败，用户状态不正常
     */
    USER_STATUS_NOT_ENABLE("B3008", "登录失败，用户状态不正常，用户状态为：{}"),

    /**
     * 登录失败，账号或密码错误
     */
    USER_PASSWORD_WRONG("B3009", "登录失败，账号或密码错误"),

    /**
     * 登录失败，用户信息为空
     */
    CA_USER_INFO_IS_NULL("B3010", "登录失败，用户信息获取为空"),

    /**
     * 登录失败，loginCode对应用户信息获取不到
     */
    LOGIN_CODE_USER_IS_NULL("B3011", "登录失败，loginCode对应用户信息获取不到"),

    /**
     * 用户正常退出的标识，这个枚举值，一般用在用户正常退出后，返回登录页时url后边的的参数 ?errorCode=本枚举errorCode
     */
    SUCCESS_LOGOUT("B3012", "用户正常退出"),

    /**
     * 外部系统调用单点操作接口，token为空
     */
    SECURITY_TOKEN_IS_EMPTY("B3013", "securityToken不能为空"),

    /**
     * 外部系统调用单点操作接口，token错误或token过期
     */
    SECURITY_TOKEN_IS_WRONG("B3014", "securityToken错误或过期");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    CaServerExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
