package cn.stylefeng.roses.kernel.conversion.util;

import cn.hutool.core.io.FileUtil;
import com.aspose.slides.Presentation;
import com.aspose.slides.SaveFormat;

import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStream;

/**
 * ppt工具类
 *
 * <AUTHOR>
 * @since 2023/10/20 22:58
 */
public class PptUtil {

    /**
     * ppt转化为pdf，读入字节，输出到流中
     *
     * @param inputFileBytes 原有文件字节数组
     * <AUTHOR>
     * @since 2023/10/20 22:48
     */
    public static void toPdfByBytes(byte[] inputFileBytes, String suffix, OutputStream outputStream) {
        // 创建一个 Presentation 实例
        Presentation presentation = new Presentation(new ByteArrayInputStream(inputFileBytes));
        // 将文档保存为 PDF
        presentation.save(outputStream, SaveFormat.Pdf);
    }

    public static void main(String[] args) throws FileNotFoundException {
        PptUtil.toPdfByBytes(FileUtil.readBytes(
                        "C:\\Users\\<USER>\\Pictures\\演示.pptx"),
                "ppt",
                new FileOutputStream("C:\\Users\\<USER>\\Pictures\\test.pdf"));
    }

}
