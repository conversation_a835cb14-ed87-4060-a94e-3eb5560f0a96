package cn.stylefeng.roses.kernel.impexp.org.service;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.impexp.org.exceptions.OrgImportExceptionEnum;
import cn.stylefeng.roses.kernel.impexp.org.factory.OrgImportValidateFactory;
import cn.stylefeng.roses.kernel.impexp.org.factory.OrgInfoFactory;
import cn.stylefeng.roses.kernel.impexp.org.pojo.EnsureImportOrgItem;
import cn.stylefeng.roses.kernel.impexp.org.pojo.EnsureImportOrgRequest;
import cn.stylefeng.roses.kernel.impexp.org.pojo.OrgExcelImportPreview;
import cn.stylefeng.roses.kernel.impexp.org.pojo.OrgImportPreviewResult;
import cn.stylefeng.roses.kernel.impexp.org.pojo.base.OrgExcelImportParse;
import cn.stylefeng.roses.kernel.impexp.user.enums.OperateTypeEnums;
import cn.stylefeng.roses.kernel.impexp.user.exceptions.UserImportExceptionEnum;
import cn.stylefeng.roses.kernel.office.api.OfficeExcelApi;
import cn.stylefeng.roses.kernel.rule.constants.TreeConstants;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import cn.stylefeng.roses.kernel.rule.util.ResponseRenderUtil;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组织机构的导入业务
 *
 * <AUTHOR>
 * @since 2024-02-18 16:36
 */
@Service
@Slf4j
public class OrgImportService {

    @Resource
    private OfficeExcelApi officeExcelApi;

    @Resource
    private HrOrganizationService hrOrganizationService;

    /**
     * 下载机构的导入模板
     *
     * <AUTHOR>
     * @since 2024-02-18 18:06
     */
    public void downloadImportTemplate() {
        InputStream stream = ResourceUtil.getStream("org-import-template.xlsx");

        FastByteArrayOutputStream read = null;
        ServletOutputStream outputStream = null;
        try {
            read = IoUtil.read(stream);
            outputStream = HttpServletUtil.getResponse().getOutputStream();

            // 设置excel文件流
            ResponseRenderUtil.setRenderExcelHeader(HttpServletUtil.getResponse(), "机构导入模板");

            read.writeTo(outputStream);
        } catch (IOException e) {
            log.error("获取导入的Excel模板失败！", e);
        } finally {
            IoUtil.close(read);
            IoUtil.close(outputStream);
        }
    }

    /**
     * 上传excel并获取预览数据
     *
     * <AUTHOR>
     * @since 2024-02-18 18:17
     */
    public OrgImportPreviewResult uploadAndGetPreviewData(MultipartFile file) {

        // 1. 先解析Excel数据，转换为实体类
        List<OrgExcelImportParse> orgExcelImportParses = null;
        try {
            orgExcelImportParses = officeExcelApi.easyReadToList(file.getInputStream(), 2, OrgExcelImportParse.class);
        } catch (IOException e) {
            throw new ServiceException(UserImportExceptionEnum.PARSE_EXCEL_ERROR);
        }

        // 2. 开始校验数据，返回给前端校验后的结果
        List<OrgExcelImportPreview> validateResult = OrgImportValidateFactory.createValidateResult(orgExcelImportParses);

        // 3. 判断里边是否有错误数据，如果有错误数据，则设置标识为false
        String jsonString = JSON.toJSONString(validateResult);
        boolean hasError = jsonString.contains("\"validateResult\":false");

        OrgImportPreviewResult orgImportPreviewResult = new OrgImportPreviewResult();
        orgImportPreviewResult.setTotalSuccess(!hasError);
        orgImportPreviewResult.setPreviewData(validateResult);
        return orgImportPreviewResult;
    }

    /**
     * 确认导入组织机构
     *
     * <AUTHOR>
     * @since 2024/2/18 22:57
     */
    public void ensureImportOrg(EnsureImportOrgRequest ensureImportOrgRequest) {

        if (ObjectUtil.isEmpty(ensureImportOrgRequest) || ObjectUtil.isEmpty(ensureImportOrgRequest.getImportOrgItemList())) {
            return;
        }

        // 给组织机构的pid和pids进行赋值
        this.fillPid(ensureImportOrgRequest);

        // 针对新增的机构进行业务处理
        List<EnsureImportOrgItem> needToAdd = ensureImportOrgRequest.getImportOrgItemList().stream().filter(i -> OperateTypeEnums.ADD.getCode().equals(i.getOperateType()))
                .collect(Collectors.toList());
        this.doAddOrgBatch(needToAdd);

        // 针对修改的机构进行业务处理
        List<EnsureImportOrgItem> needToUpdate = ensureImportOrgRequest.getImportOrgItemList().stream().filter(i -> OperateTypeEnums.EDIT.getCode().equals(i.getOperateType()))
                .collect(Collectors.toList());
        this.doUpdateOrgBatch(needToUpdate);
    }

    /**
     * 给pid进行赋值，方便进行新增操作
     *
     * <AUTHOR>
     * @since 2024/2/19 0:14
     */
    private void fillPid(EnsureImportOrgRequest ensureImportOrgRequest) {

        // 获取当前导入的这批机构属于哪个机构下，并找出这个机构的pid和pids
        Long belongOrgId = ensureImportOrgRequest.getBelongOrgId();
        if (belongOrgId == null) {
            throw new ServiceException(OrgImportExceptionEnum.BELONG_ORG_ID_NOT_EXIST);
        }

        // 获取这个机构id父级id集合
        HrOrganization belongOrgInfo = null;
        if (belongOrgId.equals(TreeConstants.DEFAULT_PARENT_ID)) {
            belongOrgInfo = OrgInfoFactory.createRootParentOrgInfo();
        } else {
            belongOrgInfo = this.hrOrganizationService.getById(belongOrgId);
            if (ObjectUtil.isEmpty(belongOrgInfo)) {
                throw new ServiceException(OrgImportExceptionEnum.BELONG_ORG_NOT_EXIST);
            }
        }

        // 被导入的机构集合
        List<EnsureImportOrgItem> importOrgItemList = ensureImportOrgRequest.getImportOrgItemList();

        // 针对新增的机构，key是机构名称，value是机构id
        HashMap<String, Long> orgNameOrgIdMap = new HashMap<>();

        // 1. 如果是新增的机构，则每个机构都生成一个主键id
        List<EnsureImportOrgItem> needToAdd = importOrgItemList.stream().filter(i -> OperateTypeEnums.ADD.getCode().equals(i.getOperateType())).collect(Collectors.toList());
        for (EnsureImportOrgItem ensureImportOrgItem : needToAdd) {
            ensureImportOrgItem.setOrgId(IdWorker.getId());
            orgNameOrgIdMap.put(ensureImportOrgItem.getOrgName(), ensureImportOrgItem.getOrgId());
        }

        // 2. 设置pid属性
        for (EnsureImportOrgItem ensureImportOrgItem : needToAdd) {
            if (StrUtil.isEmpty(ensureImportOrgItem.getParentOrgName())) {
                ensureImportOrgItem.setOrgParentId(belongOrgId);
                ensureImportOrgItem.setOrgPids(belongOrgInfo.getOrgPids() + "[" + belongOrgId + "],");
            } else {
                Long parentOrgId = orgNameOrgIdMap.get(ensureImportOrgItem.getParentOrgName());
                ensureImportOrgItem.setOrgParentId(parentOrgId);
            }
        }

        // 3. 设置pids属性
        this.setPids(needToAdd, belongOrgId, belongOrgInfo.getOrgPids());
    }

    /**
     * 根据指定的父级id，设置这个父级id的所有子集的pids
     * <p>
     * 可以递归调用
     *
     * <AUTHOR>
     * @since 2024-02-19 11:35
     */
    private void setPids(List<EnsureImportOrgItem> needToAdd, Long parentOrgId, String parentOrgPids) {
        for (EnsureImportOrgItem ensureImportOrgItem : needToAdd) {
            if (ensureImportOrgItem.getOrgParentId().equals(parentOrgId)) {
                ensureImportOrgItem.setOrgPids(parentOrgPids + "[" + parentOrgId + "],");
                this.setPids(needToAdd, ensureImportOrgItem.getOrgId(), ensureImportOrgItem.getOrgPids());
            }
        }
    }


    /**
     * 批量新增组织机构
     *
     * <AUTHOR>
     * @since 2024/2/18 23:47
     */
    private void doAddOrgBatch(List<EnsureImportOrgItem> needToAdd) {
        List<HrOrganization> orgList = OrgInfoFactory.createOrgInfo(needToAdd);
        if (ObjectUtil.isNotEmpty(orgList)) {
            hrOrganizationService.quickBatchSaveOrg(orgList);
        }
    }

    /**
     * 批量更新组织机构信息
     *
     * <AUTHOR>
     * @since 2024/2/18 23:57
     */
    private void doUpdateOrgBatch(List<EnsureImportOrgItem> needToUpdate) {
        // 只放开修改组织机构名称、简称、编码、排序、状态、类型、税号、备注、对外主数据id
        // 其他的都保留原有的值
        for (EnsureImportOrgItem ensureImportOrgItem : needToUpdate) {
            HrOrganization hrOrganization = hrOrganizationService.getById(ensureImportOrgItem.getOrgId());
            if (ObjectUtil.isNotEmpty(hrOrganization)) {
                hrOrganization.setOrgName(ensureImportOrgItem.getOrgName());
                hrOrganization.setOrgShortName(ensureImportOrgItem.getOrgShortName());
                hrOrganization.setOrgCode(ensureImportOrgItem.getOrgCode());
                hrOrganization.setOrgSort(ensureImportOrgItem.getOrgSort());
                hrOrganization.setStatusFlag(ensureImportOrgItem.getStatusFlag());
                hrOrganization.setOrgType(ensureImportOrgItem.getOrgType());
                hrOrganization.setTaxNo(ensureImportOrgItem.getTaxNo());
                hrOrganization.setRemark(ensureImportOrgItem.getRemark());
                hrOrganization.setMasterOrgId(ensureImportOrgItem.getMasterOrgId());
                hrOrganizationService.updateById(hrOrganization);
            }
        }
    }

}
