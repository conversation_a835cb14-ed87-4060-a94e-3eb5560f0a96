package cn.stylefeng.roses.kernel.websocket.api.endpoint;


import cn.stylefeng.roses.kernel.websocket.api.constants.WebsocketConstants;
import cn.stylefeng.roses.kernel.websocket.api.encoder.FastjsonObjectEncoder;
import cn.stylefeng.roses.kernel.websocket.api.endpoint.base.BaseWebSocketEndpoint;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static cn.stylefeng.roses.kernel.websocket.api.constants.WebsocketConstants.PATH_PARAM_IDENTIFIER;

/**
 * WebSocket接入控制
 *
 * <AUTHOR>
 * @since 2024/1/14 23:08
 */
@Component
@ServerEndpoint(value = "/ws/message/{identifier}/{randomString}", configurator = GetHttpSessionConfigurator.class, encoders = FastjsonObjectEncoder.class)
@Slf4j
public class WebSocketEndpoint extends BaseWebSocketEndpoint {

    /**
     * websocket接入的配置
     *
     * @param session      websocket会话
     * @param identifier   路径参数，一版用来传递token，用户的回话token
     * @param randomString 随机字符串，用来区分同一个用户的多个连接
     * <AUTHOR>
     * @since 2024/1/14 23:08
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(PATH_PARAM_IDENTIFIER) String identifier, @PathParam(WebsocketConstants.PATH_RANDOM_STRING) String randomString) {
        try {
            connect(session, identifier, randomString);
        } catch (Exception e) {
            log.error("建立webSocket出错:", e);
        }
    }

    /**
     * 接收到客户端websocket消息时的处理
     *
     * @param message    消息内容
     * @param session    websocket会话
     * @param identifier 一版用来传递token，用户的回话token
     * <AUTHOR>
     * @since 2024/1/15 0:13
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam(PATH_PARAM_IDENTIFIER) String identifier,
                          @PathParam(WebsocketConstants.PATH_RANDOM_STRING) String randomString) {
        try {
            // 获取真实的标识
            identifier = identifier + WebsocketConstants.IDENTIFIER_CONCAT + randomString;

            receiveMessage(identifier, message, session);
        } catch (Exception e) {
            log.error("接收消息出错:", e);
        }
    }

    /**
     * websocket关闭时的处理
     *
     * @param session    websocket会话
     * @param identifier 一版用来传递token，用户的回话token
     * <AUTHOR>
     * @since 2024/1/15 0:14
     */
    @OnClose
    public void onClose(Session session, @PathParam(PATH_PARAM_IDENTIFIER) String identifier, @PathParam(WebsocketConstants.PATH_RANDOM_STRING) String randomString) {
        try {
            // 获取真实的标识
            identifier = identifier + WebsocketConstants.IDENTIFIER_CONCAT + randomString;

            disconnect(identifier);
        } catch (Exception e) {
            log.error("关闭webSocket出错:", e);
        }
    }

    /**
     * websocket异常时的处理
     *
     * @param throwable  异常
     * @param identifier 一版用来传递token，用户的回话token
     * <AUTHOR>
     * @since 2024/1/15 0:14
     */
    @OnError
    public void onError(Throwable throwable, @PathParam(PATH_PARAM_IDENTIFIER) String identifier, @PathParam(WebsocketConstants.PATH_RANDOM_STRING) String randomString) {

        // 获取真实的标识
        identifier = identifier + WebsocketConstants.IDENTIFIER_CONCAT + randomString;

        log.info("websocket发生异常：, identifier = " + identifier);
        log.error(throwable.getMessage(), throwable);
        disconnect(identifier);
    }

}
