package cn.stylefeng.roses.sanyuan.modular.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.log.api.BizLogServiceApi;
import cn.stylefeng.roses.kernel.log.api.pojo.business.SysLogBusinessRequest;
import cn.stylefeng.roses.kernel.log.api.pojo.entity.SysLogBusiness;
import cn.stylefeng.roses.kernel.sys.api.SysRoleServiceApi;
import cn.stylefeng.roses.sanyuan.api.constants.SanyuanConstants;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日常运维的日志业务控制器
 *
 * <AUTHOR>
 * @since 2023/10/10 14:15
 */
@Service
public class AuditLogService {

    @Resource
    private BizLogServiceApi bizLogServiceApi;

    @Resource
    private SysRoleServiceApi sysRoleServiceApi;

    /**
     * 获取日常运维日志列表
     *
     * <AUTHOR>
     * @since 2023/10/10 14:19
     */
    public PageResult<SysLogBusiness> findNormalLogPage(SysLogBusinessRequest sysLogBusinessRequest) {

        // 获取系统管理员的角色编码
        String roleCode = SanyuanConstants.SYS_ADMIN_ROLE_CODE;

        // 获取对应角色的所有功能权限标识，也就是日志记录的typeCode标识
        List<String> menuOptions = sysRoleServiceApi.getRoleMenuOptionsByRoleId(roleCode);

        // 获取这些typeCode对应的日志记录
        return bizLogServiceApi.getOperateLogByLogType(menuOptions, sysLogBusinessRequest);
    }

    /**
     * 获取安全操作日志列表
     *
     * <AUTHOR>
     * @since 2023/10/10 14:46
     */
    public PageResult<SysLogBusiness> getSecurityLog(SysLogBusinessRequest sysLogBusinessRequest) {

        // 获取安全管理员角色编码
        String roleCode = SanyuanConstants.SECURITY_ADMIN_ROLE_CODE;

        // 获取对应角色的所有功能权限标识，也就是日志记录的typeCode标识
        List<String> menuOptions = sysRoleServiceApi.getRoleMenuOptionsByRoleId(roleCode);

        // 获取这些typeCode对应的日志记录
        return bizLogServiceApi.getOperateLogByLogType(menuOptions, sysLogBusinessRequest);
    }

}
