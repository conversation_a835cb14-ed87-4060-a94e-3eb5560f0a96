System.register(["./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js","./MenuApi-legacy-240954bc.js","./use-stand-legacy-b84b7c90.js","./menu-add-edit-legacy-67c8375a.js","./menu-form-legacy-0c3258cc.js","./index-legacy-45c79de7.js","./index-legacy-94a6fc23.js","./AppApi-legacy-f1c900da.js"],(function(e,t){"use strict";var a,d,n,i,l,o,r,s,c,p,u,v,h,m,f,g,b,x,y,w,k,I,C,T,_,j,E,z,N,P,L,M,S,D;return{setters:[e=>{a=e._,d=e.r,n=e.cc,i=e.o,l=e.X,o=e.aK,r=e.k,s=e.a,c=e.c,p=e.b,u=e.d,v=e.w,h=e.g,m=e.ah,f=e.F,g=e.e,b=e.a2,x=e.t,y=e.f,w=e.h,k=e.aM,I=e.M,C=e.E,T=e.m,_=e.I,j=e.l,E=e.B,z=e.n,N=e.bg,P=e.a5,L=e.S},null,e=>{M=e.M},e=>{S=e.default},e=>{D=e.default},null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".menu-item[data-v-fdd939ba]{border:1px solid #ccc;width:350px;height:100%;margin-right:20px;border-radius:4px}.menu-item .left-header[data-v-fdd939ba]{padding:10px;height:85px;display:flex;align-items:center;position:relative;border-bottom:1px solid #ccc}.menu-item .left-header .img[data-v-fdd939ba]{width:60px;height:60px;margin-right:20px}.menu-item .left-header .app-item-right[data-v-fdd939ba]{flex:1;width:calc(100% - 75px);height:100%;display:flex;flex-direction:column;justify-content:space-around}.menu-item .left-header .app-item-right .app-item-name[data-v-fdd939ba]{font-size:18px;font-weight:700;color:#000}.menu-item .left-header .app-item-right .app-item-remark[data-v-fdd939ba]{color:#a6a6a6;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.menu-item .left-header .masks[data-v-fdd939ba]{display:none;position:absolute;top:0;right:0;left:0;bottom:0;height:100%;text-align:center;background-color:rgba(153,153,153,.65)}.menu-item .left-header .masks .add[data-v-fdd939ba]{margin-top:30px}.menu-item .left-header:hover .masks[data-v-fdd939ba]{display:block}.menu-item .menu-tree[data-v-fdd939ba]{width:100%;padding:0 10px 10px;height:calc(100% - 105px);overflow-y:auto!important;overflow-x:hidden!important}#wrap[data-v-fdd939ba]{overflow:hidden;width:100%}#main[data-v-fdd939ba]{position:relative;transition:top .2s}.page[data-v-fdd939ba]{width:100%;margin:0;padding-bottom:1px;height:100%;display:flex}#pageUl[data-v-fdd939ba]{position:fixed;right:10px;bottom:50%;z-index:999}.active[data-v-fdd939ba]{color:red}.tree-title[data-v-fdd939ba]{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tree-edit[data-v-fdd939ba]{width:100%;display:inline-block;position:relative}.tree-edit .edit-title[data-v-fdd939ba],.tree-edit .edit-titles[data-v-fdd939ba]{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tree-edit .edit-icon[data-v-fdd939ba]{display:none;width:40px;position:absolute;right:30px!important}.tree-edit .edit-icons[data-v-fdd939ba]{display:none;width:40px;position:absolute;right:55px}.tree-edit:hover .edit-icon[data-v-fdd939ba],.tree-edit:hover .edit-icons[data-v-fdd939ba]{display:inline-block}.tree-edit:hover .edit-title[data-v-fdd939ba]{width:calc(100% - 70px)}.tree-edit:hover .edit-titles[data-v-fdd939ba]{width:calc(100% - 100px)}.left-header[data-v-fdd939ba]{height:30px;line-height:30px;display:flex;justify-content:space-between;align-items:center;color:#505050;font-size:14px;font-weight:400;margin-bottom:16px}.left-header .left-header-title[data-v-fdd939ba]{color:#60666b;font-size:14px;font-weight:400}.left-header .header-add[data-v-fdd939ba]{font-size:14px;cursor:pointer;padding:5px}.left-header .header-add[data-v-fdd939ba]:hover{background:#e9f3f8}.search[data-v-fdd939ba]{height:36px;border-radius:5px;margin-bottom:16px}.search-input[data-v-fdd939ba]{border-radius:4px}.tree-content[data-v-fdd939ba]{width:100%;height:calc(100% - 90px);overflow:hidden}[data-v-fdd939ba] .ant-spin-container{height:100%}.left-tree[data-v-fdd939ba]{height:calc(100% - 10px)!important;overflow-y:auto!important;overflow-x:hidden!important}[data-v-fdd939ba]::-webkit-scrollbar{width:12px!important}.tree-edit[data-v-fdd939ba],.not-tree-edit[data-v-fdd939ba]{width:100%;display:inline-block;position:relative}.tree-edit .edit-title[data-v-fdd939ba],.not-tree-edit .edit-title[data-v-fdd939ba]{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tree-edit .edit-icon[data-v-fdd939ba],.not-tree-edit .edit-icon[data-v-fdd939ba]{display:none;width:40px;position:absolute;right:10px}.tree-edit:hover .edit-icon[data-v-fdd939ba],.not-tree-edit:hover .edit-icon[data-v-fdd939ba]{display:inline-block}.tree-edit:hover .edit-title[data-v-fdd939ba],.not-tree-edit:hover .edit-title[data-v-fdd939ba]{width:calc(100% - 50px)}.not-tree-edit:hover .edit-title[data-v-fdd939ba]{width:100%}[data-v-fdd939ba] .ant-tree .ant-tree-node-content-wrapper{height:38px!important;line-height:38px!important;display:inherit!important}[data-v-fdd939ba] .ant-tree-switcher{line-height:38px!important}[data-v-fdd939ba] .ant-tree-switcher .ant-tree-switcher-icon{font-size:14px!important}[data-v-fdd939ba] .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle{height:38px!important;line-height:38px!important;margin-right:8px}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before{border-radius:4px;background:rgba(207,221,247,.35)!important}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{color:#0f56d7;font-weight:500}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tree-iconEle .iconfont{color:#0f56d7!important}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before{background:rgba(207,221,247,.35)!important;border-radius:4px}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{color:#000;font-weight:500}[data-v-fdd939ba] .ant-tree-treenode:not(:last-child){margin-bottom:8px}[data-v-fdd939ba] .ant-tree-indent-unit{width:10px!important}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode:before{bottom:0!important}[data-v-fdd939ba] .ant-tree .ant-tree-treenode{padding:0 12px}[data-v-fdd939ba] .guns-table-tool .guns-tool{display:none}.img[data-v-fdd939ba]{width:24px;height:22px;margin-top:-4px}.svg-img[data-v-fdd939ba]{width:24px;height:22px;margin-top:8px}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode{height:38px!important;line-height:38px!important}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button{display:inline;display:flex;top:0}[data-v-fdd939ba] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button-first{display:inline;display:flex;top:0;margin-right:150px}[data-v-fdd939ba] .ant-tree-node-content-wrapper{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 0 0 4px}[data-v-fdd939ba] .ant-tree-title{width:calc(100% - 32px)}.empty[data-v-fdd939ba]{margin-top:50%}[data-v-fdd939ba] .ant-card-body,[data-v-fdd939ba] .ant-spin-nested-loading,[data-v-fdd939ba] .ant-spin-container{height:100%}[data-v-fdd939ba] .ant-tree{background-color:#fff!important}.content-mian-body[data-v-fdd939ba]{height:calc(100% - 52px)!important}@media screen and (max-width: 768px){.menu-item[data-v-fdd939ba]{width:340px;margin-right:0}}\n",document.head.appendChild(t);const U={class:"guns-layout"},A={class:"guns-layout-content"},K={class:"guns-layout"},O={class:"guns-layout-content-application"},B={class:"content-mian",style:{overflow:"hidden"}},W={class:"content-mian-header"},G={class:"header-content"},H={class:"header-content-left"},F={class:"header-content-right"},q={class:"content-mian-body"},X={class:"table-content",id:"content"},J={id:"pageUl",type:"circle"},Q={class:"left-header"},R=["src"],V={key:1,src:"/assets/avatar-6ddecb5a.png",alt:"",class:"img"},Y={class:"app-item-right"},Z={class:"app-item-name"},$={class:"app-item-remark"},ee={class:"masks"},te={class:"menu-tree"},ae={class:"tree-edit"},de=["title"],ne=Object.assign({name:"AuthMenu"},{__name:"index",setup(e){const t=d({searchText:""}),a=d([]),ne=d(!1),ie=d(0),le=d(0),oe=d(1),re=d(1),se=d(0),ce=d(0),pe=d(0),ue=d(0),ve=d(Object),he=d(Object),me=d(!1),fe=d(null),ge=d(!1),be=d(""),xe=d(""),ye=d(""),we=d(0),ke=d(0),Ie=d([]),Ce=d([]),Te=d(!0);n((()=>{Ne()})),i((()=>{window.onresize=()=>(we.value=document.body.clientWidth,void(ke.value=document.body.clientHeight)),je()})),l((()=>we.value),(e=>{_e(),o((()=>{je()}))}),{deep:!0}),l((()=>ke.value),(e=>{je()}),{deep:!0});const _e=()=>{oe.value=1,re.value=1,pe.value=0,ze(oe.value),Ie.value=[];let e=parseInt((we.value-10)/420);e<1&&(e=1);for(var t=[],d=0;d<a.value.length;d+=e)t.push(a.value.slice(d,d+e));if(t&&t.length>0&&t.forEach(((e,t)=>{Ie.value[t]={index:t,children:e}})),Te.value){let e=a.value.map((e=>e.openMenuIdList)),t=[].concat(...e);Ce.value=[...t]}Te.value=!1},je=()=>{ie.value=document.getElementById("content").clientWidth,le.value=document.getElementById("content").clientHeight,ve.value=document.getElementById("main"),he.value=document.getElementsByTagName("div");for(let e=0;e<he.value.length;e++)"page"==he.value[e].className&&(he.value[e].style.height=le.value+"px");ue.value=document.querySelectorAll(".page").length,-1!=navigator.userAgent.toLowerCase().indexOf("firefox")?document.addEventListener("DOMMouseScroll",Ee,!1):document.addEventListener?document.addEventListener("mousewheel",Ee,!1):document.attachEvent?document.attachEvent("onmousewheel",Ee):document.onmousewheel=Ee},Ee=e=>{if("page"==e.target.className){se.value=(new Date).getTime();let t=e.detail||-e.wheelDelta;se.value-ce.value>500&&(t>0&&parseInt(ve.value.offsetTop)>=-le.value*(ue.value-2)?(oe.value++,ze(oe.value)):t<0&&parseInt(ve.value.offsetTop)<0&&(oe.value--,ze(oe.value)),ce.value=(new Date).getTime())}},ze=e=>{if(e!=re.value){let t=e-re.value;pe.value=pe.value-t*le.value,re.value=e}},Ne=(e=!1)=>{ne.value=!0,M.getAppMenuGroupDetail({searchText:t.value.searchText}).then((t=>{a.value=t,we.value=document.body.clientWidth,ke.value=document.body.clientHeight,e&&_e()})).finally((()=>ne.value=!1))},Pe=()=>{t.value.searchText="",Ne()},Le=(e,t,a)=>{be.value=e?e.appId:"","add"==a?(fe.value=null,xe.value=t?t.menuId:"-1",ye.value=t?t.menuName:"根节点"):(fe.value=t,xe.value=t.menuId,ye.value=t.menuName),ge.value=!0};return(e,a)=>{const d=_,n=j,i=E,l=z,o=r("plus-outlined"),ie=N,oe=P,se=L;return s(),c("div",U,[p("div",A,[p("div",K,[p("div",O,[p("div",B,[p("div",W,[p("div",G,[p("div",H,[u(l,{size:16},{default:v((()=>[u(n,{value:t.value.searchText,"onUpdate:value":a[0]||(a[0]=e=>t.value.searchText=e),placeholder:"菜单名称、菜单编码（回车搜索）",onPressEnter:Ne,class:"search-input"},{prefix:v((()=>[u(d,{iconClass:"icon-opt-search"})])),_:1},8,["value"]),u(i,{class:"border-radius",onClick:Pe},{default:v((()=>a[6]||(a[6]=[h("重置")]))),_:1,__:[6]})])),_:1})]),p("div",F,[u(l,{size:16},{default:v((()=>[u(i,{type:"primary",class:"border-radius",onClick:a[1]||(a[1]=e=>Le("","","add"))},{default:v((()=>[u(o),a[7]||(a[7]=h("新建菜单"))])),_:1,__:[7]})])),_:1})])])]),p("div",q,[p("div",X,[u(se,{tip:"Loading...",spinning:ne.value,delay:100},{default:v((()=>[p("div",{id:"wrap",style:m({height:le.value+"px"})},[p("ul",J,[(s(!0),c(f,null,g(Ie.value,(e=>(s(),c("li",{id:"pageUlLi1",class:b(["pageUlLi",{active:re.value==e.index+1}]),key:e.index},"   ",2)))),128))]),p("div",{id:"main",style:m({top:pe.value+"px",height:le.value+"px"})},[(s(!0),c(f,null,g(Ie.value,(e=>(s(),c("div",{id:"page1",class:"page",key:e.index},[(s(!0),c(f,null,g(e.children,(e=>(s(),c("div",{class:"box-shadow menu-item",key:e.appId},[p("div",Q,[e.appIconWrapper?(s(),c("img",{key:0,src:e.appIconWrapper,alt:"",class:"img"},null,8,R)):(s(),c("img",V)),p("div",Y,[p("div",Z,x(e.appName),1),p("div",$,x(e.remark),1)]),p("div",ee,[u(i,{type:"primary",class:"add",onClick:t=>Le(e,null,"add")},{default:v((()=>[u(o),a[8]||(a[8]=h("新建菜单"))])),_:2,__:[8]},1032,["onClick"])])]),p("div",te,[e.menuList&&e.menuList.length>0?(s(),y(ie,{key:0,draggable:"",onDrop:t=>((e,t)=>{const a=t.node.eventKey,d=t.dragNode.eventKey,n=t.node.pos.split("-"),i=t.dropPosition-Number(n[n.length-1]),l=(e,t,a)=>{e.forEach(((e,d,n)=>e.nodeId===t?a(e,d,n):e.children?l(e.children,t,a):void 0))},o=[...e.menuList];let r={};if(l(o,d,((e,t,a)=>{a.splice(t,1),r=e})),t.dropToGap)if((t.node.children||[]).length>0&&t.node.expanded&&1===i)r.menuParentId=t.node.nodeId,r.nodeParentId=t.node.nodeId,l(o,a,(e=>{e.children=e.children||[],e.children.unshift(r)}));else{r.menuParentId=t.node.nodeParentId,r.nodeParentId=t.node.nodeParentId;let e=[],d=0;l(o,a,((t,a,n)=>{e=n,d=a})),-1===i?e.splice(d,0,r):e.splice(d+1,0,r)}else r.menuParentId=t.node.nodeId,r.nodeParentId=t.node.nodeId,l(o,a,(e=>{e.children=e.children||[],e.children.push(r)}));e.menuList=[...o],M.updateMenuTree({updateMenuTree:o}).then((e=>{T.success(e.message)}))})(e,t),"show-icon":!0,expandedKeys:Ce.value,"onUpdate:expandedKeys":a[2]||(a[2]=e=>Ce.value=e),"tree-data":e.menuList,fieldNames:{children:"children",title:"menuName",key:"menuId",value:"menuId"}},{icon:v((e=>[10==e.menuType?(s(),y(d,{key:0,iconClass:"icon-menu-type-backend",fontSize:"24px",color:"#594d9c"})):w("",!0),20==e.menuType?(s(),y(d,{key:1,iconClass:"icon-menu-type-single-page",fontSize:"24px",color:"green"})):w("",!0),30==e.menuType?(s(),y(d,{key:2,fontSize:"24px",iconClass:"icon-menu-type-inner-link",color:"var(--primary-color)"})):w("",!0),40==e.menuType?(s(),y(d,{key:3,iconClass:"icon-menu-type-waibulianjie",color:"#e37445",fontSize:"24px"})):w("",!0),50==e.menuType?(s(),y(d,{key:4,iconClass:"icon-menu-yingyongguanli",color:"#e37445",fontSize:"24px"})):w("",!0)])),title:v((t=>[p("span",ae,[p("span",{class:b(10!=t.menuType&&20!=t.menuType||!t.children||0!=t.children.length?"edit-title":"edit-titles"),title:t.menuName},x(t.menuName),11,de),p("span",{class:b([10,20,50].includes(t.menuType)&&t.children&&0==t.children.length?"edit-icons":"edit-icon")},[u(l,null,{default:v((()=>[u(d,{title:"编辑",iconClass:"icon-opt-bianji",color:"var(--primary-color)",onClick:k((a=>Le(e,t,"edit")),["stop"])},null,8,["onClick"]),[10,20,50].includes(t.menuType)&&t.children&&0==t.children.length?(s(),y(d,{key:0,iconClass:"icon-menu-yingyongguanli",color:"var(--primary-color)",onClick:k((e=>(e=>{fe.value=e,me.value=!0})(t)),["stop"]),title:"功能维护"},null,8,["onClick"])):w("",!0),u(d,{iconClass:"icon-opt-tianjia",color:"var(--primary-color)",title:"新增",onClick:k((a=>Le(e,t,"add")),["stop"])},null,8,["onClick"]),u(d,{iconClass:"icon-opt-shanchu",title:"删除",color:"red",onClick:k((e=>(e=>{I.confirm({title:"提示",content:"确定要删除吗?",icon:u(C),maskClosable:!0,onOk:()=>{ne.value=!0,M.delete({menuId:e.menuId}).then((e=>{T.success(e.message),Ne(!0)})).finally((()=>ne.value=!1))}})})(t)),["stop"])},null,8,["onClick"])])),_:2},1024)],2)])])),_:2},1032,["onDrop","expandedKeys","tree-data"])):(s(),y(oe,{key:1,class:"empty"}))])])))),128))])))),128))],4)],4)])),_:1},8,["spinning"])])])])])])]),me.value?(s(),y(S,{key:0,visible:me.value,"onUpdate:visible":a[3]||(a[3]=e=>me.value=e),data:fe.value},null,8,["visible","data"])):w("",!0),ge.value?(s(),y(D,{key:1,visible:ge.value,"onUpdate:visible":a[4]||(a[4]=e=>ge.value=e),data:fe.value,onDone:a[5]||(a[5]=e=>Ne(!0)),appId:be.value,menuParentId:xe.value,menuParentName:ye.value},null,8,["visible","data","appId","menuParentId","menuParentName"])):w("",!0)])}}});e("default",a(ne,[["__scopeId","data-v-fdd939ba"]]))}}}));
