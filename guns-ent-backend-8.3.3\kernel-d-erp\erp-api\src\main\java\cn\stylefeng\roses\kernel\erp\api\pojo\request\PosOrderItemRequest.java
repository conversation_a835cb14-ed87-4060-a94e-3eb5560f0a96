package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * POS订单项请求参数
 *
 * <AUTHOR>
 * @since 2025/08/01 16:00
 */
@Data
public class PosOrderItemRequest {

    /**
     * 订单项ID
     */
    @ChineseDescription("订单项ID")
    private Long itemId;

    /**
     * 订单ID
     */
    @ChineseDescription("订单ID")
    private Long orderId;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 单价
     */
    @ChineseDescription("单价")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    @ChineseDescription("数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal quantity;

    /**
     * 小计金额
     */
    @ChineseDescription("小计金额")
    private BigDecimal totalPrice;

    /**
     * 单位
     */
    @ChineseDescription("单位")
    private String unit;

    /**
     * 计价类型
     */
    @ChineseDescription("计价类型")
    private String pricingType;

}