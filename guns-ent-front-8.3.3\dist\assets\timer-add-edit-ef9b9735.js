import{r as l,o as b,a as _,f as g,w as y,d as k,m as x,M as h}from"./index-18a1ea24.js";import{_ as w,S as m}from"./timer-form-e94ed0df.js";const C={__name:"timer-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(v,{emit:c}){const i=v,f=c,s=l(!1),t=l(!1),a=l({positionSort:1e3}),d=l(null);b(()=>{i.data?(t.value=!0,a.value=Object.assign({},i.data)):t.value=!1});const n=o=>{f("update:visible",o)},p=async()=>{d.value.$refs.formRef.validate().then(async o=>{if(o){s.value=!0;let e=null;t.value?e=m.edit(a.value):e=m.add(a.value),e.then(async r=>{s.value=!1,x.success(r.message),n(!1),f("done")}).catch(()=>{s.value=!1})}})};return(o,e)=>{const r=h;return _(),g(r,{width:700,maskClosable:!1,visible:i.visible,"confirm-loading":s.value,forceRender:!0,title:t.value?"\u7F16\u8F91\u5B9A\u65F6\u4EFB\u52A1":"\u65B0\u5EFA\u5B9A\u65F6\u4EFB\u52A1","body-style":{paddingBottom:"8px",height:"550px",overflowY:"auto"},"onUpdate:visible":n,onOk:p,onClose:e[1]||(e[1]=u=>n(!1))},{default:y(()=>[k(w,{form:a.value,"onUpdate:form":e[0]||(e[0]=u=>a.value=u),ref_key:"timerFormRef",ref:d},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}};export{C as default};
