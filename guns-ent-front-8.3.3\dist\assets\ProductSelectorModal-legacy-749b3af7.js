System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./ProductApi-legacy-33feae42.js","./productCategoryApi-legacy-247b2407.js"],(function(e,a){"use strict";var t,o,r,c,n,l,i,d,s,p,g,u,y,h,f,v,b,x,m,w,k,C,I,_,S,T,P,z,A,E,L,R;return{setters:[e=>{t=e._,o=e.r,r=e.s,c=e.L,n=e.X,l=e.m,i=e.a,d=e.f,s=e.w,p=e.d,g=e.g,u=e.t,y=e.b,h=e.c,f=e.h,v=e.a2,b=e.B,x=e.aO,m=e.S,w=e.v,k=e.I,C=e.l,I=e.W,_=e.J,S=e.n,T=e.G,P=e.U,z=e.i,A=e.ba,E=e.M},null,null,null,null,e=>{L=e.P},e=>{R=e.P}],execute:function(){var a=document.createElement("style");a.textContent=".product-selector-content[data-v-155bc2a7]{height:600px}.category-tree-container[data-v-155bc2a7]{height:100%;border-right:1px solid #f0f0f0;padding-right:16px}.category-tree-header[data-v-155bc2a7]{padding:8px 0;border-bottom:1px solid #f0f0f0;margin-bottom:12px}.category-tree-header .tree-title[data-v-155bc2a7]{margin:0;font-size:16px;font-weight:500;color:#262626}.category-tree-content[data-v-155bc2a7]{height:calc(100% - 50px);overflow-y:auto}.category-title[data-v-155bc2a7]{display:flex;align-items:center;justify-content:space-between;width:100%}.product-count[data-v-155bc2a7]{font-size:12px;color:#8c8c8c;margin-left:4px}.product-category-tree[data-v-155bc2a7] .ant-tree-treenode{padding:4px 0}.product-category-tree[data-v-155bc2a7] .ant-tree-node-content-wrapper{padding:4px 8px;border-radius:4px;transition:all .2s ease}.product-category-tree[data-v-155bc2a7] .ant-tree-node-content-wrapper:hover{background-color:#f5f5f5}.product-category-tree[data-v-155bc2a7] .ant-tree-node-content-wrapper.ant-tree-node-selected{background-color:#e6f7ff;color:#1890ff}.product-category-tree[data-v-155bc2a7] .ant-tree-iconEle{margin-right:8px;color:#8c8c8c}.product-category-tree[data-v-155bc2a7] .ant-tree-switcher:hover{color:#1890ff}.product-content-container[data-v-155bc2a7]{height:600px;display:flex;flex-direction:column}.search-area[data-v-155bc2a7]{margin-bottom:16px;padding:12px 16px;background:#fafafa;border-radius:6px;flex-shrink:0}.product-list[data-v-155bc2a7],.product-list[data-v-155bc2a7] .ant-table-wrapper{flex:1;display:flex;flex-direction:column;min-height:0}.product-list[data-v-155bc2a7] .ant-table{flex:1;display:flex;flex-direction:column}.product-list[data-v-155bc2a7] .ant-table-container{flex:1;overflow:auto;min-height:300px}.product-list[data-v-155bc2a7] .ant-table-tbody{overflow-y:auto}.pagination-container[data-v-155bc2a7]{flex-shrink:0;margin-top:16px;padding:12px 16px;text-align:right;border-top:1px solid #f0f0f0;background:#fff}.product-info[data-v-155bc2a7]{display:flex;flex-direction:column}.product-name[data-v-155bc2a7]{font-weight:500;color:#262626;margin-bottom:4px}.product-details[data-v-155bc2a7]{display:flex;gap:8px;font-size:12px;color:#8c8c8c}.product-code[data-v-155bc2a7]{color:#1890ff}.product-spec[data-v-155bc2a7]{color:#52c41a}.price-text[data-v-155bc2a7]{font-weight:500;color:#1890ff}.stock-normal[data-v-155bc2a7]{color:#52c41a}.stock-warning[data-v-155bc2a7]{color:#faad14}.stock-danger[data-v-155bc2a7]{color:#ff4d4f}.amount-cell[data-v-155bc2a7]{text-align:right;font-weight:500}[data-v-155bc2a7] .ant-tree{background:transparent}[data-v-155bc2a7] .ant-tree .ant-tree-node-content-wrapper{padding:4px 8px;border-radius:4px;transition:all .2s}[data-v-155bc2a7] .ant-tree .ant-tree-node-content-wrapper:hover{background-color:#f5f5f5}[data-v-155bc2a7] .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected{background-color:#e6f7ff;color:#1890ff}[data-v-155bc2a7] .ant-tree .ant-tree-title{font-size:13px}@media (max-width: 1200px){.category-tree-container[data-v-155bc2a7]{display:none}.product-content-container[data-v-155bc2a7]{span:24}}\n",document.head.appendChild(a);const N={name:"ProductSelectorModal",props:{visible:{type:Boolean,default:!1},supplierId:{type:[String,Number],default:null}},emits:["update:visible","select"],setup(e,{emit:a}){const t=o(!1),i=o(!1),d=o([]),s=o([]),p=o([]),g=o([]),u=o([]),y=r({searchText:"",categoryId:void 0,pricingType:void 0,supplierId:null}),h=r({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}),f=c((()=>({selectedRowKeys:g.value.map((e=>e.productId)),onChange:(e,a)=>{console.log("行选择变化:",{selectedRowKeys:e,selectedRows:a}),g.value=a},getCheckboxProps:e=>(console.log("商品复选框状态检查:",{productId:e.productId,status:e.status}),{disabled:!1})})));n((()=>e.supplierId),(a=>{y.supplierId=a,e.visible&&a&&b()})),n((()=>e.visible),(a=>{console.log("弹窗显示状态变化:",a),a&&(g.value=[],y.supplierId=e.supplierId,console.log("开始加载分类树和商品数据，供应商ID:",e.supplierId),v(),b())})),n((()=>p.value),(e=>{console.log("分类树数据变化:",{"数据长度":e.length,"第一个节点":e[0],"完整数据":e})}),{deep:!0});const v=async()=>{i.value=!0;try{console.log("开始加载商品分类树");const e=await R.findTree({status:"Y"});if(console.log("商品分类树API响应:",e),e&&Array.isArray(e)){const a=e=>{const t={...e,key:e.key||e.categoryId,title:e.title||e.categoryName};return e.children&&Array.isArray(e.children)&&(t.children=e.children.map(a)),t},t={key:"ALL_CATEGORIES",title:"全部分类",categoryId:null,categoryName:"全部分类",children:e.map(a),productCount:null,isLeaf:!1};p.value=[t],u.value=["ALL_CATEGORIES"],console.log("商品分类树加载成功，节点数量:",e.length),console.log("原始API数据:",e),console.log("处理后的树数据:",p.value)}else console.warn("商品分类树数据格式错误:",e),p.value=[]}catch(e){console.error("加载商品分类树失败:",e),l.error("加载商品分类失败："+(e.message||"未知错误")),p.value=[]}finally{i.value=!1}},b=async()=>{if(e.supplierId){t.value=!0;try{console.log("开始加载商品列表，参数:",{supplierId:e.supplierId,categoryId:y.categoryId,searchText:y.searchText,pricingType:y.pricingType,pageNo:h.current,pageSize:h.pageSize});const a={supplierId:e.supplierId,pageNo:h.current,pageSize:h.pageSize};y.categoryId&&(a.categoryId=y.categoryId),y.searchText&&(a.searchText=y.searchText),y.pricingType&&(a.pricingType=y.pricingType);const t=await L.findPage(a);console.log("商品列表API响应:",t),t&&t.rows?(d.value=t.rows,h.total=t.totalRows,h.current=t.pageNo,h.pageSize=t.pageSize,console.log("商品列表加载成功，总数:",t.totalRows,"当前页:",t.rows.length)):(console.warn("商品数据格式错误:",t),d.value=[],h.total=0)}catch(a){console.error("加载商品列表失败:",a),l.error("加载商品列表失败："+(a.message||"未知错误"))}finally{t.value=!1}}else d.value=[]};return{loading:t,categoryLoading:i,products:d,categories:s,categoryTreeData:p,selectedProducts:g,selectedCategoryKeys:u,searchParams:y,pagination:h,columns:[{title:"商品信息",key:"productInfo",width:250},{title:"分类",key:"categoryName",width:120},{title:"计价类型",key:"pricingType",width:100},{title:"零售价",key:"retailPrice",width:100,align:"right"},{title:"库存",key:"stockQuantity",width:100,align:"right"}],rowSelection:f,loadProducts:b,handlePageChange:(e,a)=>{h.current=e,h.pageSize=a,b()},handlePageSizeChange:(e,a)=>{h.current=1,h.pageSize=a,b()},handleCategorySelect:(e,a)=>{if(console.log("分类树选择:",{selectedKeys:e,info:a}),e.length>0){const t=e[0];if("ALL_CATEGORIES"===t)y.categoryId=null;else{const e=a.node;y.categoryId=e.categoryId||t}u.value=e,console.log("设置分类筛选条件:",y.categoryId),h.current=1,b()}},handleSearch:()=>{h.current=1,b()},handleReset:()=>{y.searchText="",y.pricingType=void 0,h.current=1,b()},getPricingTypeName:e=>({NORMAL:"普通",WEIGHT:"称重",PIECE:"计件",VARIABLE:"变价"}[e]||e),getPricingTypeColor:e=>({NORMAL:"blue",WEIGHT:"orange",PIECE:"green",VARIABLE:"purple"}[e]||"default"),formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",getStockClass:e=>{const a=parseFloat(e)||0;return a<=0?"stock-danger":a<=10?"stock-warning":"stock-normal"},handleCancel:()=>{a("update:visible",!1)},handleConfirm:()=>{0!==g.value.length?a("select",g.value):l.warning("请选择商品")}}}},j={class:"product-selector-content"},K={class:"category-tree-content"},G={class:"category-title"},O={class:"search-area"},U={class:"product-list"},Q={key:0,class:"product-info"},B={class:"product-name"},M={class:"product-details"},F={class:"product-code"},V={key:0,class:"product-spec"},W={key:3,class:"price-text"},D={class:"pagination-container"};e("default",t(N,[["render",function(e,a,t,o,r,c){const n=b,l=x,L=m,R=w,N=k,H=C,J=I,q=_,X=S,Y=T,$=P,Z=z,ee=A,ae=E;return i(),d(ae,{visible:t.visible,title:"选择商品",width:1200,maskClosable:!1,onCancel:o.handleCancel},{footer:s((()=>[p(n,{onClick:o.handleCancel},{default:s((()=>a[5]||(a[5]=[g("取消")]))),_:1,__:[5]},8,["onClick"]),p(n,{type:"primary",disabled:0===o.selectedProducts.length,onClick:o.handleConfirm},{default:s((()=>[g(" 确定选择 ("+u(o.selectedProducts.length)+") ",1)])),_:1},8,["disabled","onClick"])])),default:s((()=>[y("div",j,[p(Y,{gutter:16,style:{height:"600px"}},{default:s((()=>[p(R,{span:6,class:"category-tree-container"},{default:s((()=>[a[6]||(a[6]=y("div",{class:"category-tree-header"},[y("h4",{class:"tree-title"},"商品分类")],-1)),y("div",K,[p(L,{spinning:o.categoryLoading,size:"small"},{default:s((()=>[p(l,{selectedKeys:o.selectedCategoryKeys,"onUpdate:selectedKeys":a[0]||(a[0]=e=>o.selectedCategoryKeys=e),"tree-data":o.categoryTreeData,"field-names":{children:"children",title:"title",key:"key"},"show-line":!0,"show-icon":!1,"default-expand-all":!0,class:"product-category-tree",onSelect:o.handleCategorySelect},{title:s((({title:e})=>[y("span",G,u(e),1)])),_:1},8,["selectedKeys","tree-data","onSelect"])])),_:1},8,["spinning"])])])),_:1,__:[6]}),p(R,{span:18,class:"product-content-container"},{default:s((()=>[y("div",O,[p(Y,{gutter:16},{default:s((()=>[p(R,{span:10},{default:s((()=>[p(H,{value:o.searchParams.searchText,"onUpdate:value":a[1]||(a[1]=e=>o.searchParams.searchText=e),placeholder:"商品名称、编码",allowClear:"",onPressEnter:o.loadProducts},{prefix:s((()=>[p(N,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"])])),_:1}),p(R,{span:8},{default:s((()=>[p(q,{value:o.searchParams.pricingType,"onUpdate:value":a[2]||(a[2]=e=>o.searchParams.pricingType=e),placeholder:"计价类型",allowClear:"",onChange:o.loadProducts},{default:s((()=>[p(J,{value:"NORMAL"},{default:s((()=>a[7]||(a[7]=[g("普通")]))),_:1,__:[7]}),p(J,{value:"WEIGHT"},{default:s((()=>a[8]||(a[8]=[g("称重")]))),_:1,__:[8]}),p(J,{value:"PIECE"},{default:s((()=>a[9]||(a[9]=[g("计件")]))),_:1,__:[9]}),p(J,{value:"VARIABLE"},{default:s((()=>a[10]||(a[10]=[g("变价")]))),_:1,__:[10]})])),_:1},8,["value","onChange"])])),_:1}),p(R,{span:6},{default:s((()=>[p(X,null,{default:s((()=>[p(n,{type:"primary",onClick:o.handleSearch},{default:s((()=>a[11]||(a[11]=[g("搜索")]))),_:1,__:[11]},8,["onClick"]),p(n,{onClick:o.handleReset},{default:s((()=>a[12]||(a[12]=[g("重置")]))),_:1,__:[12]},8,["onClick"])])),_:1})])),_:1})])),_:1})]),y("div",U,[p(Z,{columns:o.columns,"data-source":o.products,loading:o.loading,pagination:!1,"row-selection":o.rowSelection,"row-key":e=>e.productId,scroll:{y:400},size:"small",bordered:""},{bodyCell:s((({column:e,record:a})=>["productInfo"===e.key?(i(),h("div",Q,[y("div",B,u(a.productName),1),y("div",M,[y("span",F,u(a.productCode),1),a.specification?(i(),h("span",V,u(a.specification),1)):f("",!0)])])):f("",!0),"categoryName"===e.key?(i(),d($,{key:1,color:"blue"},{default:s((()=>[g(u(a.categoryName),1)])),_:2},1024)):f("",!0),"pricingType"===e.key?(i(),d($,{key:2,color:o.getPricingTypeColor(a.pricingType)},{default:s((()=>[g(u(o.getPricingTypeName(a.pricingType)),1)])),_:2},1032,["color"])):f("",!0),"retailPrice"===e.key?(i(),h("span",W,"¥"+u(o.formatAmount(a.retailPrice)),1)):f("",!0),"stockQuantity"===e.key?(i(),h("span",{key:4,class:v(o.getStockClass(a.stockQuantity))},u(a.stockQuantity||0)+" "+u(a.unit),3)):f("",!0)])),_:1},8,["columns","data-source","loading","row-selection","row-key"]),y("div",D,[p(ee,{current:o.pagination.current,"onUpdate:current":a[3]||(a[3]=e=>o.pagination.current=e),"page-size":o.pagination.pageSize,"onUpdate:pageSize":a[4]||(a[4]=e=>o.pagination.pageSize=e),total:o.pagination.total,"show-size-changer":o.pagination.showSizeChanger,"show-quick-jumper":o.pagination.showQuickJumper,"show-total":o.pagination.showTotal,"page-size-options":["10","20","50","100"],size:"small",onChange:o.handlePageChange,onShowSizeChange:o.handlePageSizeChange},null,8,["current","page-size","total","show-size-changer","show-quick-jumper","show-total","onChange","onShowSizeChange"])])])])),_:1})])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-155bc2a7"]]))}}}));
