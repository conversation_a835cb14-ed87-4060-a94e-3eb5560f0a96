import Request from '@/utils/request/request-util';

/**
 * 后台管理 -租户管理 - 功能包管理api
 *
 */
export class PackageApi {
  /**
   * 添加
   * @param {*} params
   * @returns
   */
  static add(params) {
    return Request.post('/tenantPackage/add', params);
  }

  /**
   * 编辑
   * @param {*} params
   * @returns
   */
  static edit(params) {
    return Request.post('/tenantPackage/edit', params);
  }
  /**
   * 删除单个
   * @param {*} params
   * @returns
   */
  static delete(params) {
    return Request.post('/tenantPackage/delete', params);
  }
  /**
   * 详情
   * @param {*} params
   * @returns
   */
  static detail(params) {
    return Request.getAndLoadData('/tenantPackage/detail', params);
  }
  /**
   * 列表
   * @param {*} params
   * @returns
   */
  static list(params) {
    return Request.getAndLoadData('/tenantPackage/list', params);
  }
  /**
   * 获取功能包绑定的权限列表
   * @param {*} params
   * @returns
   */
  static getPackageAuth(params) {
    return Request.getAndLoadData('/tenantPackage/getPackageAuth', params);
  }
  /**
   * 设置功能包绑定权限
   * @param {*} params
   * @returns
   */
  static setPackagePermission(params) {
    return Request.post('/tenantPackage/setPackagePermission', params);
  }
  /**
   * 刷新租户
   * @param {*} params
   * @returns
   */
  static refreshPackageTenantRoles(params) {
    return Request.post('/tenantPackage/refreshPackageTenantRoles', params);
  }
}
