<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-config</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>config-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--字典相关的api-->
        <!--配置分类需要调用字典相关的接口-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>dict-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--参数校验模块-->
        <!--用在控制器，参数校验-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>validator-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--系统配置表的缓存，使用redis还是map，Redis用在将系统部署在分布式环境-->
        <!--默认使用Map，如果分布式环境，则在项目依赖中切换-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>config-sdk-map</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>config-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

        <!--数据库sdk-->
        <!--数据库dao框架-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

</project>
