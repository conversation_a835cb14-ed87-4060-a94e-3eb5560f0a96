System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./manager-add-edit-legacy-a05969b5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./manager-form-legacy-a74e7199.js","./index-legacy-198191c1.js","./ThemeTemplateApi-legacy-4a2544d6.js","./FileApi-legacy-f85a3060.js"],(function(e,a){"use strict";var t,l,n,s,i,o,c,d,u,r,h,m,y,g,v,f,p,k,x,w,b,C,I;return{setters:[e=>{t=e._},e=>{l=e.r,n=e.o,s=e.k,i=e.a,o=e.c,c=e.b,d=e.d,u=e.w,r=e.g,h=e.t,m=e.h,y=e.f,g=e.M,v=e.E,f=e.cf,p=e.m,k=e.n,x=e.B,w=e.I,b=e.l,C=e.a9},e=>{I=e.default},null,null,null,null,null,null,null],execute:function(){const a={class:"guns-layout"},_={class:"guns-layout-content"},j={class:"guns-layout"},T={class:"guns-layout-content-application"},S={class:"content-mian"},N={class:"content-mian-header"},z={class:"header-content"},A={class:"header-content-left"},E={class:"header-content-right"},F={class:"content-mian-body"},B={class:"table-content"},M=["onClick"];e("default",{__name:"index",setup(e){const L=l([{key:"index",title:"序号",width:60,align:"center",isShow:!0,hideInSetting:!0},{title:"主题名称",dataIndex:"themeName",isShow:!0},{title:"主题模板",isShow:!0,dataIndex:"templateName"},{title:"创建时间",isShow:!0,dataIndex:"createTime"},{title:"启用状态",isShow:!0,dataIndex:"statusFlag"},{key:"action",title:"操作",width:100,isShow:!0}]),R=l(null),P=l({themeName:""}),U=l(null),Y=l(!1);n((()=>{}));const D=()=>{R.value.reload()},G=e=>{U.value=e,Y.value=!0};return(e,l)=>{const n=k,H=s("plus-outlined"),O=x,Q=w,q=b,J=C,K=t;return i(),o("div",a,[c("div",_,[c("div",j,[c("div",T,[c("div",S,[c("div",N,[c("div",z,[c("div",A,[d(n,{size:16})]),c("div",E,[d(n,{size:16},{default:u((()=>[d(O,{type:"primary",class:"border-radius",onClick:l[0]||(l[0]=e=>G())},{default:u((()=>[d(H),l[3]||(l[3]=r("新建"))])),_:1,__:[3]})])),_:1})])])]),c("div",F,[c("div",B,[d(K,{columns:L.value,where:P.value,rowId:"themeId",ref_key:"tableRef",ref:R,rowSelection:!1,url:"/sysTheme/findPage",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"THMEM_MANAGER_TABLE"},{toolLeft:u((()=>[d(q,{value:P.value.themeName,"onUpdate:value":l[1]||(l[1]=e=>P.value.themeName=e),placeholder:"主题名称（回车搜索）",onPressEnter:D,class:"search-input",bordered:!1},{prefix:u((()=>[d(Q,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:u((({column:e,record:a})=>["themeName"==e.dataIndex?(i(),o("a",{key:0,onClick:e=>G(a)},h(a.themeName),9,M)):m("",!0),"statusFlag"===e.dataIndex?(i(),y(J,{key:1,checked:"Y"===a.statusFlag,onChange:e=>(async(e,a)=>{const t=a.themeId,l=e?"Y":"N",n=await f.updateThemeStatus({themeId:t});p.success(n.message),a.statusFlag=l,D()})(e,a)},null,8,["checked","onChange"])):m("",!0),"action"==e.key?(i(),y(n,{key:2,size:16},{default:u((()=>[d(Q,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>G(a)},null,8,["onClick"]),d(Q,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{g.confirm({title:"提示",content:"确定要删除选中的主题吗?",icon:d(v),maskClosable:!0,onOk:async()=>{const a=await f.del({themeId:e.themeId});p.success(a.message),D()}})})(a)},null,8,["onClick"])])),_:2},1024)):m("",!0)])),_:1},8,["columns","where"])])])])])])]),Y.value?(i(),y(I,{key:0,visible:Y.value,"onUpdate:visible":l[2]||(l[2]=e=>Y.value=e),data:U.value,onDone:D},null,8,["visible","data"])):m("",!0)])}}})}}}));
