<template>
  <div class="inventory-alert-config-container">
    <!-- 页面标题 -->
    <a-page-header
      title="库存预警配置"
      sub-title="配置库存预警系统的全局参数和通知设置"
    />

    <!-- 配置表单 -->
    <a-card :bordered="false" class="config-card">
      <a-form
        ref="formRef"
        :model="configData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 12 }"
        @finish="handleSubmit"
      >
        <!-- 基础配置 -->
        <a-divider orientation="left">基础配置</a-divider>
        
        <a-form-item label="启用预警检查" name="enableAlert">
          <a-switch
            v-model:checked="configData.enableAlert"
            checked-children="启用"
            un-checked-children="禁用"
          />
          <div class="form-item-desc">
            控制是否启用自动预警检查功能
          </div>
        </a-form-item>

        <a-form-item label="全局检查频率" name="globalCheckFrequency">
          <a-input-number
            v-model:value="configData.globalCheckFrequency"
            :min="1"
            :max="1440"
            style="width: 200px"
            addon-after="分钟"
          />
          <div class="form-item-desc">
            全局预警检查频率，单位：分钟（1-1440分钟）
          </div>
        </a-form-item>

        <a-form-item label="最大检查商品数" name="maxCheckProducts">
          <a-input-number
            v-model:value="configData.maxCheckProducts"
            :min="100"
            :max="10000"
            style="width: 200px"
            addon-after="个"
          />
          <div class="form-item-desc">
            单次检查的最大商品数量，避免系统负载过高
          </div>
        </a-form-item>

        <a-form-item label="预警记录保留天数" name="alertRecordRetentionDays">
          <a-input-number
            v-model:value="configData.alertRecordRetentionDays"
            :min="7"
            :max="365"
            style="width: 200px"
            addon-after="天"
          />
          <div class="form-item-desc">
            预警记录在系统中的保留天数，超期自动清理
          </div>
        </a-form-item>

        <!-- 通知配置 -->
        <a-divider orientation="left">通知配置</a-divider>

        <a-form-item label="启用邮件通知" name="enableEmailNotify">
          <a-switch
            v-model:checked="configData.enableEmailNotify"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>

        <template v-if="configData.enableEmailNotify">
          <a-form-item label="SMTP服务器" name="smtpServer">
            <a-input
              v-model:value="configData.smtpServer"
              placeholder="请输入SMTP服务器地址"
            />
          </a-form-item>

          <a-form-item label="SMTP端口" name="smtpPort">
            <a-input-number
              v-model:value="configData.smtpPort"
              :min="1"
              :max="65535"
              style="width: 200px"
            />
          </a-form-item>

          <a-form-item label="发件人邮箱" name="senderEmail">
            <a-input
              v-model:value="configData.senderEmail"
              placeholder="请输入发件人邮箱"
            />
          </a-form-item>

          <a-form-item label="邮箱密码" name="emailPassword">
            <a-input-password
              v-model:value="configData.emailPassword"
              placeholder="请输入邮箱密码或授权码"
            />
          </a-form-item>

          <a-form-item label="默认收件人" name="defaultRecipients">
            <a-select
              v-model:value="configData.defaultRecipients"
              mode="tags"
              placeholder="请输入收件人邮箱，支持多个"
              style="width: 100%"
            />
            <div class="form-item-desc">
              默认的预警通知收件人，可输入多个邮箱地址
            </div>
          </a-form-item>
        </template>

        <a-form-item label="启用短信通知" name="enableSmsNotify">
          <a-switch
            v-model:checked="configData.enableSmsNotify"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>

        <template v-if="configData.enableSmsNotify">
          <a-form-item label="短信服务商" name="smsProvider">
            <a-select
              v-model:value="configData.smsProvider"
              placeholder="请选择短信服务商"
            >
              <a-select-option value="ALIYUN">阿里云</a-select-option>
              <a-select-option value="TENCENT">腾讯云</a-select-option>
              <a-select-option value="HUAWEI">华为云</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="AccessKey" name="smsAccessKey">
            <a-input
              v-model:value="configData.smsAccessKey"
              placeholder="请输入AccessKey"
            />
          </a-form-item>

          <a-form-item label="SecretKey" name="smsSecretKey">
            <a-input-password
              v-model:value="configData.smsSecretKey"
              placeholder="请输入SecretKey"
            />
          </a-form-item>

          <a-form-item label="短信模板ID" name="smsTemplateId">
            <a-input
              v-model:value="configData.smsTemplateId"
              placeholder="请输入短信模板ID"
            />
          </a-form-item>

          <a-form-item label="默认接收手机号" name="defaultPhones">
            <a-select
              v-model:value="configData.defaultPhones"
              mode="tags"
              placeholder="请输入手机号，支持多个"
              style="width: 100%"
            />
            <div class="form-item-desc">
              默认的预警通知接收手机号，可输入多个
            </div>
          </a-form-item>
        </template>

        <!-- 高级配置 -->
        <a-divider orientation="left">高级配置</a-divider>

        <a-form-item label="预警去重时间" name="alertDeduplicationTime">
          <a-input-number
            v-model:value="configData.alertDeduplicationTime"
            :min="1"
            :max="1440"
            style="width: 200px"
            addon-after="分钟"
          />
          <div class="form-item-desc">
            相同商品的预警去重时间，避免频繁发送重复预警
          </div>
        </a-form-item>

        <a-form-item label="批量处理大小" name="batchProcessSize">
          <a-input-number
            v-model:value="configData.batchProcessSize"
            :min="10"
            :max="1000"
            style="width: 200px"
            addon-after="条"
          />
          <div class="form-item-desc">
            批量处理预警记录的单次处理数量
          </div>
        </a-form-item>

        <a-form-item label="启用调试模式" name="enableDebugMode">
          <a-switch
            v-model:checked="configData.enableDebugMode"
            checked-children="启用"
            un-checked-children="禁用"
          />
          <div class="form-item-desc">
            启用后会记录详细的调试日志，便于问题排查
          </div>
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item :wrapper-col="{ offset: 6, span: 12 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitLoading">
              保存配置
            </a-button>
            <a-button @click="handleReset">
              重置
            </a-button>
            <a-button @click="handleTestNotify">
              测试通知
            </a-button>
            <a-button @click="handleExportConfig">
              导出配置
            </a-button>
            <a-button @click="handleImportConfig">
              导入配置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 测试通知弹窗 -->
    <a-modal
      v-model:visible="testNotifyVisible"
      title="测试通知"
      @ok="handleTestNotifyConfirm"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="通知类型">
          <a-checkbox-group v-model:value="testNotifyTypes">
            <a-checkbox value="EMAIL" :disabled="!configData.enableEmailNotify">
              邮件通知
            </a-checkbox>
            <a-checkbox value="SMS" :disabled="!configData.enableSmsNotify">
              短信通知
            </a-checkbox>
            <a-checkbox value="SYSTEM">系统消息</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="测试消息">
          <a-textarea
            v-model:value="testMessage"
            placeholder="请输入测试消息内容"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入配置文件 -->
    <input
      ref="importFileRef"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleImportFileChange"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryAlertApi } from '@/api/erp/inventoryAlert';

export default {
  name: 'InventoryAlertConfigIndex',
  setup() {
    const formRef = ref();
    const importFileRef = ref();
    const submitLoading = ref(false);
    const testNotifyVisible = ref(false);
    const testNotifyTypes = ref(['SYSTEM']);
    const testMessage = ref('这是一条测试预警通知消息');

    const configData = reactive({
      // 基础配置
      enableAlert: true,
      globalCheckFrequency: 30,
      maxCheckProducts: 1000,
      alertRecordRetentionDays: 90,
      
      // 邮件通知配置
      enableEmailNotify: false,
      smtpServer: '',
      smtpPort: 587,
      senderEmail: '',
      emailPassword: '',
      defaultRecipients: [],
      
      // 短信通知配置
      enableSmsNotify: false,
      smsProvider: '',
      smsAccessKey: '',
      smsSecretKey: '',
      smsTemplateId: '',
      defaultPhones: [],
      
      // 高级配置
      alertDeduplicationTime: 60,
      batchProcessSize: 100,
      enableDebugMode: false
    });

    const rules = reactive({
      globalCheckFrequency: [
        { required: true, message: '请输入全局检查频率', trigger: 'blur' },
        { type: 'number', min: 1, max: 1440, message: '检查频率必须在1-1440分钟之间', trigger: 'blur' }
      ],
      maxCheckProducts: [
        { required: true, message: '请输入最大检查商品数', trigger: 'blur' },
        { type: 'number', min: 100, max: 10000, message: '最大检查商品数必须在100-10000之间', trigger: 'blur' }
      ],
      alertRecordRetentionDays: [
        { required: true, message: '请输入预警记录保留天数', trigger: 'blur' },
        { type: 'number', min: 7, max: 365, message: '保留天数必须在7-365天之间', trigger: 'blur' }
      ],
      smtpServer: [
        {
          validator: (rule, value) => {
            if (configData.enableEmailNotify && !value) {
              return Promise.reject('请输入SMTP服务器地址');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ],
      senderEmail: [
        {
          validator: (rule, value) => {
            if (configData.enableEmailNotify && !value) {
              return Promise.reject('请输入发件人邮箱');
            }
            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              return Promise.reject('请输入正确的邮箱格式');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ]
    });

    // 获取配置
    const fetchConfig = async () => {
      try {
        const response = await InventoryAlertApi.getConfig();
        Object.assign(configData, response.data);
      } catch (error) {
        console.error('获取配置失败:', error);
        message.error('获取配置失败');
      }
    };

    // 保存配置
    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        submitLoading.value = true;
        await InventoryAlertApi.updateConfig(configData);
        message.success('配置保存成功');
      } catch (error) {
        console.error('保存配置失败:', error);
        message.error('保存配置失败');
      } finally {
        submitLoading.value = false;
      }
    };

    // 重置配置
    const handleReset = () => {
      fetchConfig();
    };

    // 测试通知
    const handleTestNotify = () => {
      testNotifyVisible.value = true;
    };

    // 确认测试通知
    const handleTestNotifyConfirm = async () => {
      try {
        await InventoryAlertApi.testNotify({
          types: testNotifyTypes.value,
          message: testMessage.value
        });
        message.success('测试通知发送成功');
        testNotifyVisible.value = false;
      } catch (error) {
        console.error('测试通知失败:', error);
        message.error('测试通知失败');
      }
    };

    // 导出配置
    const handleExportConfig = () => {
      try {
        const blob = new Blob([JSON.stringify(configData, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `库存预警配置_${new Date().getTime()}.json`;
        link.click();
        URL.revokeObjectURL(url);
        message.success('配置导出成功');
      } catch (error) {
        console.error('导出配置失败:', error);
        message.error('导出配置失败');
      }
    };

    // 导入配置
    const handleImportConfig = () => {
      importFileRef.value.click();
    };

    // 处理导入文件
    const handleImportFileChange = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedConfig = JSON.parse(e.target.result);
          Object.assign(configData, importedConfig);
          message.success('配置导入成功');
        } catch (error) {
          console.error('导入配置失败:', error);
          message.error('配置文件格式错误');
        }
      };
      reader.readAsText(file);
      
      // 清空文件输入
      event.target.value = '';
    };

    onMounted(() => {
      fetchConfig();
    });

    return {
      formRef,
      importFileRef,
      submitLoading,
      testNotifyVisible,
      testNotifyTypes,
      testMessage,
      configData,
      rules,
      handleSubmit,
      handleReset,
      handleTestNotify,
      handleTestNotifyConfirm,
      handleExportConfig,
      handleImportConfig,
      handleImportFileChange
    };
  }
};
</script>

<style scoped>
.inventory-alert-config-container {
  padding: 16px;
}

.config-card {
  margin-top: 16px;
}

.form-item-desc {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}
</style>
