import Request from '@/utils/request/request-util';

/**
 * api认证 -外部应用api
 *
 */
export class ExternalAppApi {
    /**
   * 分页
   * @param {*} params
   * @returns
   */
  static findPage(params) {
    return Request.getAndLoadData('/apiClient/page', params);
  }

  /**
   * 添加
   * @param {*} params
   * @returns
   */
  static add(params) {
    return Request.post('/apiClient/add', params);
  }
  /**
   * 编辑
   * @param {*} params
   * @returns
   */
  static edit(params) {
    return Request.post('/apiClient/edit', params);
  }
  /**
   * 删除单个
   * @param {*} params
   * @returns
   */
  static delete(params) {
    return Request.post('/apiClient/delete', params);
  }
  /**
   * 删除批量
   * @param {*} params
   * @returns
   */
  static batchDelete(params) {
    return Request.post('/apiClient/batchDelete', params);
  }
  /**
   * 详情
   * @param {*} params
   * @returns
   */
  static detail(params) {
    return Request.getAndLoadData('/apiClient/detail', params);
  }
  /**
   * 列表
   * @param {*} params
   * @returns
   */
  static list(params) {
    return Request.getAndLoadData('/apiClient/list', params);
  }

  /**
   * 修改应用状态
   * @param {*} params
   * @returns
   */
  static changeStatus(params) {
    return Request.post('/apiClient/changeStatus', params);
  }

  /**
   * 生成一个随机的公钥私钥对
   * @param {*} params
   * @returns
   */
  static randomRsaKey(params) {
    return Request.getAndLoadData('/apiClient/randomRsaKey', params);
  }
}