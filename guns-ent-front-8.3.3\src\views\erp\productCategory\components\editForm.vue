<template>
  <a-modal
    title="编辑产品分类"
    :visible="visible"
    :confirm-loading="loading"
    :width="800"
    @ok="save"
    @cancel="updateVisible(false)"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ md: { span: 6 }, sm: { span: 24 } }"
      :wrapper-col="{ md: { span: 18 }, sm: { span: 24 } }"
    >
      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="分类编码" name="categoryCode">
            <a-input
              v-model:value="form.categoryCode"
              placeholder="请输入分类编码"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="分类名称" name="categoryName">
            <a-input
              v-model:value="form.categoryName"
              placeholder="请输入分类名称"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="父级分类" name="parentId">
            <a-tree-select
              v-model:value="form.parentId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="categoryTreeData"
              placeholder="请选择父级分类"
              tree-default-expand-all
              :field-names="{ children: 'children', title: 'title', key: 'key', value: 'value' }"
              allow-clear
              show-search
              :filter-tree-node="filterTreeNode"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="分类层级" name="categoryLevel">
            <a-input 
              v-model:value="categoryLevelText" 
              placeholder="根据父级分类自动设置"
              readonly
              style="background-color: #f5f5f5;"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :md="12" :sm="24">
          <a-form-item label="排序号" name="sortOrder">
            <a-input-number
              v-model:value="form.sortOrder"
              placeholder="请输入排序号"
              :min="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :md="12" :sm="24">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option value="Y">启用</a-select-option>
              <a-select-option value="N">停用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item 
            label="备注" 
            name="remark"
            :label-col="{ md: { span: 3 }, sm: { span: 24 } }"
            :wrapper-col="{ md: { span: 21 }, sm: { span: 24 } }"
          >
            <a-textarea
              v-model:value="form.remark"
              placeholder="请输入备注"
              :rows="3"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { ProductCategoryApi } from '../api/productCategoryApi'

export default {
  name: 'ProductCategoryEditForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref()
    const loading = ref(false)
    const categoryTreeData = ref([])
    const categoryLevelText = ref('1级 - 一级分类')

    const form = reactive({
      categoryId: null,
      categoryCode: '',
      categoryName: '',
      parentId: undefined,
      categoryLevel: 1,
      sortOrder: 0,
      status: 'Y',
      remark: ''
    })

    const rules = {
      categoryCode: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
      categoryName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
      categoryLevel: [{ required: true, message: '请选择分类层级', trigger: 'change' }],
      status: [{ required: true, message: '请选择状态', trigger: 'change' }]
    }

    const updateVisible = (value) => {
      emit('update:visible', value)
      if (!value) {
        resetForm()
      }
    }

    const resetForm = () => {
      formRef.value?.resetFields()
      Object.assign(form, {
        categoryId: null,
        categoryCode: '',
        categoryName: '',
        parentId: undefined,
        categoryLevel: 1,
        sortOrder: 0,
        status: 'Y',
        remark: ''
      })
      categoryLevelText.value = '1级 - 一级分类'
    }

    // 层级名称映射
    const getLevelText = (level) => {
      const levelMap = {
        1: '1级 - 一级分类',
        2: '2级 - 二级分类',
        3: '3级 - 三级分类',
        4: '4级 - 四级分类',
        5: '5级 - 五级分类'
      }
      return levelMap[level] || `${level}级 - 未知`
    }

    // 根据父级分类计算层级
    const calculateCategoryLevel = (parentId) => {
      if (!parentId) {
        return 1 // 没有父级，默认为一级分类
      }
      
      // 在树数据中查找父级分类
      const findParentLevel = (nodes, targetId) => {
        for (const node of nodes) {
          if (node.categoryId === targetId) {
            return node.categoryLevel || 1
          }
          if (node.children && node.children.length > 0) {
            const found = findParentLevel(node.children, targetId)
            if (found) return found
          }
        }
        return null
      }
      
      const parentLevel = findParentLevel(categoryTreeData.value, parentId)
      return parentLevel ? Math.min(parentLevel + 1, 5) : 1 // 最大5级
    }

    // 更新分类层级显示
    const updateCategoryLevel = () => {
      const level = calculateCategoryLevel(form.parentId)
      form.categoryLevel = level
      categoryLevelText.value = getLevelText(level)
    }

    // 树选择器搜索过滤
    const filterTreeNode = (inputValue, treeNode) => {
      return treeNode.title && treeNode.title.toLowerCase().includes(inputValue.toLowerCase())
    }

    const loadCategoryTree = async () => {
      try {
        const res = await ProductCategoryApi.findTree()
        let treeData = res || []
        
        // 过滤空白节点和无效数据，并补充树选择器需要的字段
        treeData = filterValidNodes(treeData)
        
        categoryTreeData.value = treeData
      } catch (error) {
        console.error('加载分类树失败:', error)
        categoryTreeData.value = []
      }
    }

    // 过滤有效节点，移除空白或无效数据，并补充树选择器需要的字段
    const filterValidNodes = (nodes) => {
      if (!Array.isArray(nodes)) return []
      
      return nodes.filter(node => {
        // 确保节点有必要的字段
        if (!node || !node.categoryId || !node.categoryName) {
          return false
        }
        
        // 补充树选择器需要的字段
        node.title = node.categoryName
        node.key = String(node.categoryId)
        node.value = String(node.categoryId)
        
        // 递归处理子节点
        if (node.children && Array.isArray(node.children)) {
          node.children = filterValidNodes(node.children)
        }
        
        return true
      })
    }

    const save = async () => {
      try {
        await formRef.value.validate()
        loading.value = true

        await ProductCategoryApi.edit(form)
        message.success('编辑成功')
        emit('ok')
        updateVisible(false)
      } catch (error) {
        console.error('编辑分类失败:', error)
        message.error('编辑失败')
      } finally {
        loading.value = false
      }
    }

    const edit = async (record) => {
      Object.assign(form, record)
      updateCategoryLevel()
    }

    // 监听props.data变化，用于编辑时填充表单
    watch(() => props.data, (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        Object.assign(form, newData)
        // 数据填充后更新层级显示
        updateCategoryLevel()
      }
    }, { immediate: true })

    // 监听visible变化，显示时加载分类树
    watch(() => props.visible, (visible) => {
      if (visible) {
        loadCategoryTree()
      }
    })

    // 监听父级分类变化，自动更新层级
    watch(() => form.parentId, () => {
      updateCategoryLevel()
    })

    return {
      formRef,
      loading,
      form,
      rules,
      categoryTreeData,
      categoryLevelText,
      updateVisible,
      save,
      edit,
      filterTreeNode
    }
  }
}
</script>

<style lang="less" scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
