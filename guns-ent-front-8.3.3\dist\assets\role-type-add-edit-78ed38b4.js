import{r as s,s as P,o as S,a as x,f as k,w as n,d as o,m as R,l as U,u as B,as as O,y as j,H as q,M as L}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{R as m}from"./RoleTypeApi-abe10d8b.js";const H={__name:"role-type-add-edit",props:{visible:Boolean,data:Object,roleType:Number,categoryParentId:String,companyData:Object},emits:["update:visible","done"],setup(f,{emit:g}){const l=f,p=g,u=s(!1),d=s(!1),a=s({categoryType:l.roleType,categoryParentId:l.categoryParentId,fldSort:100}),_=P({roleCategoryName:[{required:!0,message:"\u8BF7\u8F93\u5165\u89D2\u8272\u5206\u7C7B\u540D\u79F0",type:"string",trigger:"blur"}],categoryParentId:[{required:!0,message:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7236\u7EA7",type:"string",trigger:"blur"}],fldSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u89D2\u8272\u5206\u7C7B\u6392\u5E8F",type:"number",trigger:"blur"}]}),y=s(null),v=s([]);S(async()=>{var t;l.data?(d.value=!0,a.value=Object.assign({},l.data)):(d.value=!1,l.roleType==20&&(a.value.companyId=(t=l.companyData)==null?void 0:t.companyId)),b()});const b=async()=>{var e;const t=await m.treeList({categoryType:l.roleType,ignoreCategoryId:(e=a.value)==null?void 0:e.id});v.value=[{id:"-1",roleCategoryName:"\u6839\u8282\u70B9",children:t.data}]},i=t=>{p("update:visible",t)},w=async()=>{await y.value.validate(),u.value=!0;let t=null;d.value?t=m.edit(a.value):t=m.add(a.value),t.then(async e=>{u.value=!1,R.success(e.message),i(!1),p("done")}).catch(()=>{u.value=!1})};return(t,e)=>{const C=U,c=B,I=O,N=j,T=q,h=L;return x(),k(h,{width:524,maskClosable:!1,visible:l.visible,"confirm-loading":u.value,forceRender:!0,title:d.value?"\u7F16\u8F91\u89D2\u8272\u5206\u7C7B":"\u65B0\u5EFA\u89D2\u8272\u5206\u7C7B","body-style":{paddingBottom:"8px"},"onUpdate:visible":i,onOk:w,onClose:e[3]||(e[3]=r=>i(!1))},{default:n(()=>[o(T,{ref_key:"formRef",ref:y,model:a.value,rules:_,layout:"vertical"},{default:n(()=>[o(c,{label:"\u89D2\u8272\u5206\u7C7B\u540D\u79F0:",name:"roleCategoryName"},{default:n(()=>[o(C,{value:a.value.roleCategoryName,"onUpdate:value":e[0]||(e[0]=r=>a.value.roleCategoryName=r),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u5206\u7C7B\u540D\u79F0"},null,8,["value"])]),_:1}),o(c,{label:"\u6240\u5C5E\u7236\u7EA7:",name:"categoryParentId"},{default:n(()=>[o(I,{value:a.value.categoryParentId,"onUpdate:value":e[1]||(e[1]=r=>a.value.categoryParentId=r),style:{width:"100%"},showSearch:"","tree-data":v.value,treeNodeFilterProp:"roleCategoryName","dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"\u8BF7\u9009\u62E9\u6240\u5C5E\u7236\u7EA7",fieldNames:{children:"children",label:"roleCategoryName",key:"id",value:"id"},"allow-clear":"","tree-default-expand-all":""},null,8,["value","tree-data"])]),_:1}),o(c,{label:"\u89D2\u8272\u5206\u7C7B\u6392\u5E8F:",name:"fldSort"},{default:n(()=>[o(N,{value:a.value.fldSort,"onUpdate:value":e[2]||(e[2]=r=>a.value.fldSort=r),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u5206\u7C7B\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["visible","confirm-loading","title"])}}};export{H as default};
