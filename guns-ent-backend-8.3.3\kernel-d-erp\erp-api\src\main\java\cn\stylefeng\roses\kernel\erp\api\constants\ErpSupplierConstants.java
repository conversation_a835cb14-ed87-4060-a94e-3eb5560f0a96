package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * 供应商模块常量
 *
 * <AUTHOR>
 * @since 2025/07/20 10:00
 */
public interface ErpSupplierConstants {

    /**
     * 供应商模块名称
     */
    String SUPPLIER_MODULE_NAME = "erp-supplier";

    /**
     * 供应商模块前缀
     */
    String SUPPLIER_MODULE_PREFIX = "ERP_SUPPLIER";

    /**
     * 供应商类型：企业
     */
    String SUPPLIER_TYPE_ENTERPRISE = "ENTERPRISE";

    /**
     * 供应商类型：个体
     */
    String SUPPLIER_TYPE_INDIVIDUAL = "INDIVIDUAL";

    /**
     * 供应商状态：正常
     */
    String SUPPLIER_STATUS_ACTIVE = "ACTIVE";

    /**
     * 供应商状态：停用
     */
    String SUPPLIER_STATUS_INACTIVE = "INACTIVE";

    /**
     * 供应商状态：黑名单
     */
    String SUPPLIER_STATUS_BLACKLIST = "BLACKLIST";

    /**
     * 信用等级：优秀
     */
    String CREDIT_LEVEL_A = "A";

    /**
     * 信用等级：良好
     */
    String CREDIT_LEVEL_B = "B";

    /**
     * 信用等级：一般
     */
    String CREDIT_LEVEL_C = "C";

    /**
     * 信用等级：较差
     */
    String CREDIT_LEVEL_D = "D";

    /**
     * 默认信用等级
     */
    String DEFAULT_CREDIT_LEVEL = CREDIT_LEVEL_C;

    /**
     * 默认供应商状态
     */
    String DEFAULT_SUPPLIER_STATUS = SUPPLIER_STATUS_ACTIVE;

    /**
     * 默认供应商类型
     */
    String DEFAULT_SUPPLIER_TYPE = SUPPLIER_TYPE_ENTERPRISE;

    /**
     * 经营方式：购销
     */
    String BUSINESS_MODE_PURCHASE_SALE = "PURCHASE_SALE";

    /**
     * 经营方式：联营
     */
    String BUSINESS_MODE_JOINT_VENTURE = "JOINT_VENTURE";

    /**
     * 经营方式：代销
     */
    String BUSINESS_MODE_CONSIGNMENT = "CONSIGNMENT";

    /**
     * 默认经营方式
     */
    String DEFAULT_BUSINESS_MODE = BUSINESS_MODE_PURCHASE_SALE;

}
