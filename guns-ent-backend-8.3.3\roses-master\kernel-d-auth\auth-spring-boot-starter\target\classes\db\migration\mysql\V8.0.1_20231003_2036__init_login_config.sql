INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709185484166533121, '密码最大重试次数', 'SYS_LOGIN_MAX_ERROR_LOGIN_COUNT', '5', 'Y', '登录账号密码登录最大的错误次数，超过此次数则冻结账号', 1, 'auth_config', 'N', '2023-10-03 20:35:49', 1339550467939639299, '2023-10-03 20:35:58', 1339550467939639299);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709186904454336514, '密码策略：口令最小长度', 'SYS_LOGIN_MIN_PASSWORD_LENGTH', '6', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 20:41:28', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709189854337699842, '密码策略：最少特殊符号数量', 'SYS_LOGIN_PASSWORD_MIN_SPECIAL_SYMBOL_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 20:53:11', 1339550467939639299, '2023-10-03 21:19:56', 1339550467939639299);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709196660577406977, '密码策略：最少大写字母数量', 'SYS_LOGIN_PASSWORD_MIN_UPPER_CASE_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 21:20:14', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709196708086288385, '密码策略：最少小写字母数量', 'SYS_LOGIN_PASSWORD_MIN_LOWER_CASE_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 21:20:25', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709196753862922241, '密码策略：最少数字符号的数量', 'SYS_LOGIN_PASSWORD_MIN_NUMBER_COUNT', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 21:20:36', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709207802066505730, '密码策略：最少多久更新一次密码，单位天', 'SYS_LOGIN_PASSWORD_MIN_UPDATE_DAYS', '180', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 22:04:30', 1339550467939639299, NULL, NULL);
INSERT INTO `sys_config`(`config_id`, `config_name`, `config_code`, `config_value`, `sys_flag`, `remark`, `status_flag`, `group_code`, `del_flag`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1709207873369673730, '密码策略：修改密码时，不能和历史密码重复的次数', 'SYS_LOGIN_PASSWORD_MIN_CANT_REPEAT_TIMES', '0', 'Y', NULL, 1, 'auth_config', 'N', '2023-10-03 22:04:47', 1339550467939639299, '2023-10-03 22:05:50', 1339550467939639299);