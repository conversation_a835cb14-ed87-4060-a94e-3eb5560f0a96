package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 手机的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 23:07
 */
public class MobileValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(originValue);
        }

        Pattern pattern = null;
        Matcher matcher = null;
        boolean result = false;

        // 验证手机号
        pattern = Pattern.compile("^[1][3,4,5,7,8][0-9]{9}$");
        matcher = pattern.matcher(originValue);
        result = matcher.matches();

        if (result) {
            return new ExcelLineParseResult(originValue);
        } else {
            return new ExcelLineParseResult(false, originValue, originValue, "手机号格式不正确");
        }
    }

}
