import{_ as z,r as R,X as V,a as _,f as v,w as n,b as A,d as a,t as l,g as r,h as y,c as k,a2 as q,F as T,Y as G,U as P,Z as Q,a0 as W,a4 as K,v as Y,G as j,I as J,B as X,i as Z,a5 as $,M as tt}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{I as O}from"./InventoryHistoryApi-416494e3.js";const et={name:"InventoryDetail",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","showHistory"],setup(C,{emit:S}){const t=R(!1),o=R([]),x=[{title:"\u64CD\u4F5C\u7C7B\u578B",key:"operationType",width:100},{title:"\u6570\u91CF\u53D8\u5316",key:"quantity",width:120,align:"right"},{title:"\u64CD\u4F5C\u524D",key:"beforeStock",width:100,align:"right"},{title:"\u64CD\u4F5C\u540E",key:"afterStock",width:100,align:"right"},{title:"\u64CD\u4F5C\u65F6\u95F4",key:"operationTime",width:160},{title:"\u64CD\u4F5C\u4EBA",key:"operatorName",width:100},{title:"\u5907\u6CE8",key:"remark",width:150,ellipsis:!0}];V(()=>C.visible,e=>{e&&C.data.productId&&w()});const w=async()=>{t.value=!0;try{const e=await O.productHistory({productId:C.data.productId,pageSize:3});if(e&&e.success!==!1){let d=(Array.isArray(e)?e:e.data||[]).map(u=>({...u,quantity:u.quantityChange,beforeStock:u.stockBefore,afterStock:u.stockAfter,operatorName:u.operationUserName||u.operatorName||"\u672A\u77E5\u64CD\u4F5C\u5458",operationTime:b(u.operationTime)}));o.value=d.slice(0,3)}else o.value=[]}catch(e){o.value=[]}finally{t.value=!1}},s=e=>({NORMAL:"\u666E\u901A",WEIGHT:"\u79F0\u91CD",PIECE:"\u8BA1\u4EF6",VARIABLE:"\u53D8\u4EF7"})[e]||e,f=e=>({NORMAL:"blue",WEIGHT:"orange",PIECE:"green",VARIABLE:"purple"})[e]||"default",N=e=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"})[e]||"default",m=(e,i)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return i||"\u4E2A"}},p=(e,i)=>{if(!e)return"0";const d=i==="WEIGHT"?3:0;return parseFloat(e).toFixed(d)},h=e=>e?parseFloat(e).toFixed(2):"0.00",b=e=>{if(!e)return"-";const i=typeof e=="string"?new Date(e):e;if(isNaN(i.getTime()))return e;const d=i.getFullYear(),u=String(i.getMonth()+1).padStart(2,"0"),M=String(i.getDate()).padStart(2,"0"),D=String(i.getHours()).padStart(2,"0"),B=String(i.getMinutes()).padStart(2,"0"),L=String(i.getSeconds()).padStart(2,"0");return"".concat(d,"-").concat(u,"-").concat(M," ").concat(D,":").concat(B,":").concat(L)};return{historyLoading:t,recentHistory:o,historyColumns:x,getPricingTypeName:s,getPricingTypeColor:f,getBusinessModeColor:N,getStockUnit:m,formatStock:p,formatAmount:h,formatDateTime:b,getStockValueStyle:(e,i)=>{const d=parseFloat(e)||0,u=parseFloat(i)||0;return d<=0?{color:"#ff4d4f"}:d<=u?{color:"#faad14"}:{color:"#52c41a"}},getStockStatusName:e=>({NORMAL:"\u5E93\u5B58\u6B63\u5E38",WARNING:"\u5E93\u5B58\u9884\u8B66",OUT_OF_STOCK:"\u5E93\u5B58\u4E0D\u8DB3"})[e]||e,getStockStatusColor:e=>({NORMAL:"green",WARNING:"orange",OUT_OF_STOCK:"red"})[e]||"default",getStockStatusDescription:(e,i,d)=>{const u=parseFloat(i)||0,M=parseFloat(d)||0;switch(e){case"WARNING":return"\u5F53\u524D\u5E93\u5B58 ".concat(u," \u5DF2\u4F4E\u4E8E\u9884\u8B66\u503C ").concat(M,"\uFF0C\u5EFA\u8BAE\u53CA\u65F6\u8865\u8D27");case"OUT_OF_STOCK":return"\u5F53\u524D\u5E93\u5B58\u4E3A0\uFF0C\u8BF7\u5C3D\u5FEB\u8865\u8D27";case"NORMAL":default:return"\u5E93\u5B58\u5145\u8DB3\uFF0C\u65E0\u9700\u8865\u8D27"}},getOperationTypeName:e=>O.getOperationTypeName(e),getOperationTypeColor:e=>O.getOperationTypeColor(e),formatQuantityChange:(e,i)=>O.formatQuantityChange(e,i),getQuantityChangeClass:(e,i)=>{const d=parseFloat(e)||0;switch(i){case"IN":return"quantity-increase";case"OUT":case"SALE":return"quantity-decrease";case"ADJUST":return d>=0?"quantity-increase":"quantity-decrease";case"SET_ALERT":return"";default:return""}},showAllHistory:()=>{S("update:visible",!1),S("showHistory",C.data)},handleCancel:()=>{S("update:visible",!1)}}}},at={class:"inventory-detail-content"},ot={class:"product-name"},nt={class:"stock-status-info"},it={class:"stock-status-desc"},lt={key:0,class:"empty-history"};function rt(C,S,t,o,x,w){const s=G,f=P,N=Q,m=W,p=K,h=Y,b=j,I=J,E=X,H=Z,U=$,F=tt;return _(),v(F,{visible:t.visible,title:"\u5E93\u5B58\u8BE6\u60C5",width:1e3,footer:null,onCancel:o.handleCancel},{default:n(()=>[A("div",at,[a(m,{title:"\u5546\u54C1\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[a(N,{column:3,bordered:"",size:"small"},{default:n(()=>[a(s,{label:"\u5546\u54C1\u540D\u79F0"},{default:n(()=>[A("span",ot,l(t.data.productName),1)]),_:1}),a(s,{label:"\u5546\u54C1\u7F16\u7801"},{default:n(()=>[r(l(t.data.productCode),1)]),_:1}),a(s,{label:"\u6761\u5F62\u7801"},{default:n(()=>[r(l(t.data.barcode||"-"),1)]),_:1}),a(s,{label:"\u5546\u54C1\u5206\u7C7B"},{default:n(()=>[r(l(t.data.categoryName),1)]),_:1}),a(s,{label:"\u8BA1\u4EF7\u7C7B\u578B"},{default:n(()=>[a(f,{color:o.getPricingTypeColor(t.data.pricingType)},{default:n(()=>[r(l(o.getPricingTypeName(t.data.pricingType)),1)]),_:1},8,["color"])]),_:1}),a(s,{label:"\u5546\u54C1\u5355\u4F4D"},{default:n(()=>[r(l(o.getStockUnit(t.data.pricingType,t.data.unit)),1)]),_:1}),a(s,{label:"\u89C4\u683C"},{default:n(()=>[r(l(t.data.specification||"-"),1)]),_:1}),a(s,{label:"\u96F6\u552E\u4EF7"},{default:n(()=>[r(" \xA5"+l(o.formatAmount(t.data.retailPrice)),1)]),_:1}),a(s,{label:"\u5546\u54C1\u72B6\u6001"},{default:n(()=>[a(f,{color:t.data.status==="Y"?"green":"red"},{default:n(()=>[r(l(t.data.status==="Y"?"\u542F\u7528":"\u7981\u7528"),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),a(m,{title:"\u4F9B\u5E94\u5546\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[a(N,{column:2,bordered:"",size:"small"},{default:n(()=>[a(s,{label:"\u4F9B\u5E94\u5546\u540D\u79F0"},{default:n(()=>[r(l(t.data.supplierName),1)]),_:1}),a(s,{label:"\u4F9B\u5E94\u5546\u7F16\u7801"},{default:n(()=>[r(l(t.data.supplierCode),1)]),_:1}),a(s,{label:"\u7ECF\u8425\u65B9\u5F0F"},{default:n(()=>[a(f,{color:o.getBusinessModeColor(t.data.businessMode)},{default:n(()=>[r(l(t.data.businessModeName),1)]),_:1},8,["color"])]),_:1}),a(s,{label:"\u8054\u7CFB\u7535\u8BDD"},{default:n(()=>[r(l(t.data.supplierPhone||"-"),1)]),_:1})]),_:1})]),_:1}),a(m,{title:"\u5E93\u5B58\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[a(b,{gutter:16},{default:n(()=>[a(h,{span:6},{default:n(()=>[a(p,{title:"\u5F53\u524D\u5E93\u5B58",value:o.formatStock(t.data.currentStock,t.data.pricingType),suffix:o.getStockUnit(t.data.pricingType,t.data.unit),"value-style":o.getStockValueStyle(t.data.currentStock,t.data.minStock)},null,8,["value","suffix","value-style"])]),_:1}),a(h,{span:6},{default:n(()=>[a(p,{title:"\u9884\u8B66\u503C",value:o.formatStock(t.data.minStock,t.data.pricingType),suffix:o.getStockUnit(t.data.pricingType,t.data.unit),"value-style":{color:"#8c8c8c"}},null,8,["value","suffix"])]),_:1}),a(h,{span:6},{default:n(()=>[a(p,{title:"\u5E73\u5747\u6210\u672C",value:t.data.avgCost,prefix:"\xA5",precision:2,"value-style":{color:"#1890ff"}},null,8,["value"])]),_:1}),a(h,{span:6},{default:n(()=>[a(p,{title:"\u5E93\u5B58\u4EF7\u503C",value:t.data.totalValue,prefix:"\xA5",precision:2,"value-style":{color:"#52c41a"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(m,{title:"\u5E93\u5B58\u72B6\u6001",size:"small",style:{"margin-bottom":"16px"}},{default:n(()=>[A("div",nt,[a(f,{color:o.getStockStatusColor(t.data.stockStatus),style:{"font-size":"14px",padding:"8px 16px"}},{default:n(()=>[t.data.stockStatus==="WARNING"?(_(),v(I,{key:0,iconClass:"icon-opt-jinggao",style:{"margin-right":"8px"}})):y("",!0),t.data.stockStatus==="OUT_OF_STOCK"?(_(),v(I,{key:1,iconClass:"icon-opt-quehuo",style:{"margin-right":"8px"}})):y("",!0),r(" "+l(o.getStockStatusName(t.data.stockStatus)),1)]),_:1},8,["color"]),A("span",it,l(o.getStockStatusDescription(t.data.stockStatus,t.data.currentStock,t.data.minStock)),1)])]),_:1}),a(m,{title:"\u6700\u8FD1\u5E93\u5B58\u53D8\u52A8",size:"small"},{extra:n(()=>[a(E,{type:"link",size:"small",onClick:o.showAllHistory},{default:n(()=>S[0]||(S[0]=[r(" \u67E5\u770B\u5168\u90E8\u5386\u53F2 ")])),_:1,__:[0]},8,["onClick"])]),default:n(()=>[a(H,{columns:o.historyColumns,"data-source":o.recentHistory,pagination:!1,size:"small",loading:o.historyLoading},{bodyCell:n(({column:g,record:c})=>[g.key==="operationType"?(_(),v(f,{key:0,color:o.getOperationTypeColor(c.operationType)},{default:n(()=>[r(l(o.getOperationTypeName(c.operationType)),1)]),_:2},1032,["color"])):y("",!0),g.key==="quantity"?(_(),k("span",{key:1,class:q(o.getQuantityChangeClass(c.quantityChange,c.operationType))},l(o.formatQuantityChange(c.quantityChange,c.operationType))+" "+l(o.getStockUnit(t.data.pricingType,t.data.unit)),3)):y("",!0),g.key==="beforeStock"?(_(),k(T,{key:2},[r(l(o.formatStock(c.stockBefore,t.data.pricingType))+" "+l(o.getStockUnit(t.data.pricingType,t.data.unit)),1)],64)):y("",!0),g.key==="afterStock"?(_(),k(T,{key:3},[r(l(o.formatStock(c.stockAfter,t.data.pricingType))+" "+l(o.getStockUnit(t.data.pricingType,t.data.unit)),1)],64)):y("",!0),g.key==="operationTime"?(_(),k(T,{key:4},[r(l(c.operationTime),1)],64)):y("",!0),g.key==="operatorName"?(_(),k(T,{key:5},[r(l(c.operatorName||c.operationUserName||"-"),1)],64)):y("",!0),g.key==="remark"?(_(),k(T,{key:6},[r(l(c.remark||"-"),1)],64)):y("",!0)]),_:1},8,["columns","data-source","loading"]),!o.recentHistory||o.recentHistory.length===0?(_(),k("div",lt,[a(U,{description:"\u6682\u65E0\u5E93\u5B58\u53D8\u52A8\u8BB0\u5F55"})])):y("",!0)]),_:1})])]),_:1},8,["visible","onCancel"])}const ht=z(et,[["render",rt],["__scopeId","data-v-6542a158"]]);export{ht as default};
