import{o as S,r as o,X as N,a as _,c as y,d as c,ah as O,w as h,f as x,h as V,l as k,bf as B,a0 as U}from"./index-18a1ea24.js";/* empty css              */import{_ as F}from"./index-d5fce12b.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const j={class:"wh100"},J={__name:"index",props:{value:{type:[String,Array],default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},width:{type:String,default:"100%"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null},normal:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(p,{emit:n}){const l=p,d=n;S(()=>{l.value?t.value=l.normal?l.value:JSON.parse(l.value):t.value=[],r()});const t=o([]),s=o(""),u=o(!1),i=()=>{u.value=!0},v=e=>{t.value=e==null?void 0:e.map(a=>({id:a.id,name:a.name,type:a.type})),r(),f()},r=()=>{s.value=t.value.map(e=>{let a="";return e.type=="1"&&(a="(\u516C\u53F8)"),e.type=="2"&&(a="(\u90E8\u95E8)"),e.type=="3"&&(a="(\u4EBA\u5458)"),e.name+a}).join("\uFF1B")},f=()=>{let e=[...t.value];l.normal||((e==null?void 0:e.length)==0?e="":e=JSON.stringify(e)),d("update:value",e),d("onChange",l.record),b()},b=async()=>{var e;!l.normal&&((e=l.formRef)!=null&&e.validateFields)&&await l.formRef.validateFields([l.record.fieldCode])};return N(()=>l.value,e=>{l.value?t.value=l.normal?t.value:JSON.parse(l.value):t.value=[],r()},{deep:!0}),(e,a)=>{const g=k,C=F,w=B;return _(),y("div",j,[c(g,{value:s.value,"onUpdate:value":a[0]||(a[0]=m=>s.value=m),disabled:l.readonly||l.disabled,class:"w-full",style:O({width:l.width}),placeholder:p.placeholder,onFocus:i},null,8,["value","disabled","style","placeholder"]),c(w,null,{default:h(()=>[u.value?(_(),x(C,{key:0,visible:u.value,"onUpdate:visible":a[1]||(a[1]=m=>u.value=m),list:t.value,title:"\u673A\u6784\u4EBA\u5458\u9009\u62E9",onDone:v},null,8,["visible","list"])):V("",!0)]),_:1})])}}},R={class:"guns-body guns-body-card"},q={__name:"index",setup(p){const n=o(""),l=o(!1),d=o(!1),t=o("\u8BF7\u9009\u62E9"),s=()=>{console.log(n.value)};return(u,i)=>{const v=J,r=U;return _(),y("div",R,[c(r,{title:"\u673A\u6784\u4EBA\u5458\u9009\u62E9(input)",bordered:!1},{default:h(()=>[c(v,{value:n.value,"onUpdate:value":i[0]||(i[0]=f=>n.value=f),disabled:l.value,readonly:d.value,placeholder:t.value,onOnChange:s,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{q as default};
