System.register(["./index-legacy-ee1db0c7.js","./sso-client-form-legacy-b76cb089.js","./FileApi-legacy-f85a3060.js"],(function(e,t){"use strict";var a,l,s,i,n,o,d,u,c,r,v;return{setters:[e=>{a=e.R,l=e.r,s=e.o,i=e.a,n=e.f,o=e.w,d=e.d,u=e.m,c=e.M},e=>{r=e.default},e=>{v=e.a}],execute:function(){class t{static findPage(e){return a.getAndLoadData("/ssoClient/page",e)}static add(e){return a.post("/ssoClient/add",e)}static edit(e){return a.post("/ssoClient/edit",e)}static delete(e){return a.post("/ssoClient/delete",e)}static batchDelete(e){return a.post("/ssoClient/batchDelete",e)}static detail(e){return a.getAndLoadData("/ssoClient/detail",e)}static updateStatus(e){return a.post("/ssoClient/updateStatus",e)}}e("S",t);const f=e("_",{__name:"sso-client-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const f=e,g=a,p=l(!1),m=l(!1),b=l({clientSort:1e3,loginPageType:2,unifiedLogoutFlag:"Y"}),y=l(null);s((()=>{f.data?(m.value=!0,C()):m.value=!1}));const C=()=>{t.detail({clientId:f.data.clientId}).then((e=>{b.value=Object.assign({},e),b.value.clientLogoFileId&&h(b.value.clientLogoFileId,"clientLogoFileIdFileList")}))},h=(e,t)=>{v.getAntdVInfoBatch({fileIdList:[e]}).then((e=>{y.value[t]=e.data}))},_=e=>{g("update:visible",e)},L=async()=>{y.value.$refs.formRef.validate().then((async e=>{if(e){p.value=!0;let e=null;e=m.value?t.edit(b.value):t.add(b.value),e.then((async e=>{p.value=!1,u.success(e.message),_(!1),g("done")})).catch((()=>{p.value=!1}))}}))};return(e,t)=>{const a=c;return i(),n(a,{width:900,maskClosable:!1,visible:f.visible,"confirm-loading":p.value,forceRender:!0,title:m.value?"编辑第三方业务系统配置":"新增第三方业务系统配置","body-style":{paddingBottom:"8px"},"onUpdate:visible":_,onOk:L,class:"common-modal",onClose:t[1]||(t[1]=e=>_(!1))},{default:o((()=>[d(r,{form:b.value,"onUpdate:form":t[0]||(t[0]=e=>b.value=e),ref_key:"ssoClientFormRef",ref:y},null,8,["form"])])),_:1},8,["visible","confirm-loading","title"])}}}),g=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));e("s",g)}}}));
