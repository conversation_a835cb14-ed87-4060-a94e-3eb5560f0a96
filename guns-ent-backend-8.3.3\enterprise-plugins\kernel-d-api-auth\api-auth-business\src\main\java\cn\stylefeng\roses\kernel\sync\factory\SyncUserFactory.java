package cn.stylefeng.roses.kernel.sync.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.sync.pojo.UserSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户信息创建
 *
 * <AUTHOR>
 * @since 2023/10/30 13:40
 */
public class SyncUserFactory {

    /**
     * 用户信息创建工厂
     *
     * <AUTHOR>
     * @since 2023/10/30 13:40
     */
    public static List<UserSyncVo> createUserVo(List<SysUser> userList) {

        List<UserSyncVo> userSyncVos = new ArrayList<>();

        if (ObjectUtil.isEmpty(userList)) {
            return userSyncVos;
        }

        for (SysUser sysUser : userList) {
            UserSyncVo userSyncVo = new UserSyncVo();
            BeanUtil.copyProperties(sysUser, userSyncVo, CopyOptions.create().ignoreError());

            if (sysUser.getUserId() != null) {
                userSyncVo.setUserId(String.valueOf(sysUser.getUserId()));
            }

            userSyncVos.add(userSyncVo);
        }

        return userSyncVos;
    }

}
