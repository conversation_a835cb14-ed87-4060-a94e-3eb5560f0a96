System.register(["./index-legacy-ee1db0c7.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js","./TenantApi-legacy-40853d2f.js"],(function(e,a){"use strict";var l,t,n,r,i,o,u,s,d,p,m,f,c,g,v,y,_,h,b,w,x,U,j,k,D,C,L,I,N,F,Y,q,T,z,M,A,H,P,E;return{setters:[e=>{l=e._,t=e.s,n=e.bh,r=e.bi,i=e.r,o=e.o,u=e.k,s=e.a,d=e.f,p=e.w,m=e.d,f=e.b,c=e.g,g=e.h,v=e.c,y=e.F,_=e.e,h=e.t,b=e.m,w=e.bk,x=e.cj,U=e.l,j=e.u,k=e.v,D=e.a7,C=e.aa,L=e.al,I=e.z,N=e.A,F=e.B,Y=e.W,q=e.J,T=e.I,z=e.G,M=e.M,A=e.H},null,null,null,e=>{H=e.F,P=e.a},e=>{E=e.T}],execute:function(){var a=document.createElement("style");a.textContent=".company[data-v-b5566309]{border-left:4px solid var(--primary-color);padding-left:10px;margin-bottom:20px}\n",document.head.appendChild(a);const S={style:{"margin-bottom":"10px"}},$=["src"],V={__name:"tenant-form",props:{form:Object,isUpdate:{type:Boolean,default:!1}},setup(e){const a=e,l=t({fileUploadUrl:`${n}${H}?secretFlag=N`,filePreviewUrl:`${n}/sysFileInfo/public/preview?fileId=`,headers:{Authorization:r()}}),V=i(!1),B=i(null),R=i(null),G=i([]),O=t({tenantName:[{required:!0,message:"请输入租户名称",type:"string",trigger:"blur"}],tenantCode:[{required:!0,message:"请输入租户编码",type:"string",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",type:"string",trigger:"blur"}],safePhone:[{required:!0,message:"请输入电话",type:"string",trigger:"blur"}],password:[{required:!0,message:"请输入管理员密码",type:"string",trigger:"blur"}],statusFlag:[{required:!0,message:"请选择状态",type:"number",trigger:"change"}],iconList:[{required:!0,message:"请上传租户logo",type:"array",trigger:"blur"}],activeDate:[{required:!0,message:"请选择租户生效时间",type:"string",trigger:"change"}],expireDate:[{required:!0,message:"请选择到期时间",type:"string",trigger:"change"}]});o((()=>{J()}));const J=()=>{E.tenantPackageList().then((e=>{G.value=e.data}))},K=e=>{const a="image/jpeg"===e.type||"image/jpg"===e.type||"image/png"===e.type||"image/tif"===e.type||"image/jfif"===e.type||"image/webp"===e.type||"image/pjp"===e.type||"image/apng"===e.type||"image/pjpeg"===e.type||"image/avif"===e.type||"image/ico"===e.type||"image/tiff"===e.type||"image/bmp"===e.type||"image/xbm"===e.type||"image/jxl"===e.type||"image/svgz"===e.type||"image/gif"===e.type||"image/svg"===e.type;if(!a)return b.error("只能上传图片!"),w.LIST_IGNORE;const l=e.size/1024/1024<5;return l||b.error("图片大小不能超过5MB!"),a&&l},W=async e=>{B.value=e.url||e.preview||e.thumbUrl,V.value=!0},Q=e=>{let a=e.response?e.response.data.fileId:e.uid;P.download({token:r(),fileId:a})},X=()=>{a.form.companyName=a.form.tenantName},Z=()=>{a.form.tenantLinkList.push({trialFlag:"N",packageId:null,serviceEndTime:""})};return(t,n)=>{const r=U,i=j,o=k,H=u("question-circle-outlined"),P=D,E=C,J=u("plus-outlined"),ee=w,ae=L,le=I,te=N,ne=F,re=u("vxe-column"),ie=Y,oe=q,ue=u("vxe-switch"),se=T,de=u("vxe-table"),pe=z,me=M,fe=A;return s(),d(fe,{ref:"formRef",model:e.form,rules:O,layout:"vertical"},{default:p((()=>[m(pe,{gutter:20},{default:p((()=>[m(o,{span:12},{default:p((()=>[m(i,{label:"租户名称:",name:"tenantName"},{default:p((()=>[m(r,{value:e.form.tenantName,"onUpdate:value":n[0]||(n[0]=a=>e.form.tenantName=a),"allow-clear":"",placeholder:"请输入租户名称",onChange:X},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"租户编码:",name:"tenantCode"},{default:p((()=>[m(r,{value:e.form.tenantCode,"onUpdate:value":n[1]||(n[1]=a=>e.form.tenantCode=a),"allow-clear":"",placeholder:"请输入租户编码"},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{name:"email"},{label:p((()=>[n[15]||(n[15]=f("span",{style:{"margin-right":"10px"}},"邮箱:",-1)),m(P,null,{title:p((()=>n[14]||(n[14]=[c("邮箱用在租户的超级管理员登录，作为账号")]))),default:p((()=>[m(H)])),_:1})])),default:p((()=>[m(r,{value:e.form.email,"onUpdate:value":n[2]||(n[2]=a=>e.form.email=a),"allow-clear":"",placeholder:"请输入邮箱"},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"电话:",name:"safePhone"},{default:p((()=>[m(r,{value:e.form.safePhone,"onUpdate:value":n[3]||(n[3]=a=>e.form.safePhone=a),"allow-clear":"",placeholder:"请输入电话"},null,8,["value"])])),_:1})])),_:1}),a.isUpdate?g("",!0):(s(),d(o,{key:0,span:12},{default:p((()=>[m(i,{name:"password"},{label:p((()=>[n[17]||(n[17]=f("span",{style:{"margin-right":"10px"}},"管理员密码:",-1)),m(P,null,{title:p((()=>n[16]||(n[16]=[c("邮箱用在租户的超级管理员登录，作为密码")]))),default:p((()=>[m(H)])),_:1})])),default:p((()=>[m(E,{value:e.form.password,"onUpdate:value":n[4]||(n[4]=a=>e.form.password=a),"allow-clear":"",placeholder:"请输入管理员密码"},null,8,["value"])])),_:1})])),_:1})),m(o,{span:12},{default:p((()=>[m(i,{label:"租户logo:",name:"iconList"},{default:p((()=>[m(ee,{name:"file",multiple:!1,"file-list":e.form.iconList,"onUpdate:fileList":n[5]||(n[5]=a=>e.form.iconList=a),"default-file-list":e.form.iconList,maxCount:1,action:l.fileUploadUrl,"list-type":"picture-card",headers:l.headers,"before-upload":K,accept:".jpeg,.jpg,.png,.tif,.jfif,.webp,.pjp,.apng,.pjpeg,.avif,.ico,.tiff,.bmp,.xbm,.jxl,.jpeg,.svgz,.gif,.svg",onPreview:W,onDownload:Q,onChange:n[6]||(n[6]=e=>((e,l)=>{"done"===e.file.status?(a.form[l]=[e.file],b.success(`${e.file.name} 图片上传成功`)):"error"===e.file.status&&b.error(`${e.file.name} 图片上传失败`)})(e,"iconList")),showUploadList:{showDownloadIcon:!0}},{default:p((()=>[0==e.form.iconList.length?(s(),d(J,{key:0,style:{"font-size":"28px","font-weight":"200"}})):g("",!0)])),_:1},8,["file-list","default-file-list","action","headers"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"租户生效时间:",name:"activeDate"},{default:p((()=>[m(ae,{value:e.form.activeDate,"onUpdate:value":n[7]||(n[7]=a=>e.form.activeDate=a),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择租户生效时间",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"到期时间:",name:"expireDate"},{default:p((()=>[m(ae,{value:e.form.expireDate,"onUpdate:value":n[8]||(n[8]=a=>e.form.expireDate=a),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择到期时间",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"状态:",name:"statusFlag"},{default:p((()=>[m(te,{value:e.form.statusFlag,"onUpdate:value":n[9]||(n[9]=a=>e.form.statusFlag=a)},{default:p((()=>[m(le,{value:1},{default:p((()=>n[18]||(n[18]=[c("启用")]))),_:1,__:[18]}),m(le,{value:2},{default:p((()=>n[19]||(n[19]=[c("禁用")]))),_:1,__:[19]})])),_:1},8,["value"])])),_:1})])),_:1}),m(o,{span:24},{default:p((()=>n[20]||(n[20]=[f("div",{class:"company"},"开通功能",-1)]))),_:1,__:[20]}),m(o,{span:24},{default:p((()=>[f("div",S,[m(ne,{type:"primary",class:"border-radius",onClick:Z},{default:p((()=>n[21]||(n[21]=[c("+ 添加功能")]))),_:1,__:[21]})]),m(de,{border:"",style:{"margin-bottom":"20px"},"show-overflow":"",data:e.form.tenantLinkList,"row-config":{useKey:!0},"column-config":{resizable:!0},"max-height":"600",ref_key:"xTableRef",ref:R},{default:p((()=>[m(re,{type:"seq",width:"60",title:"序号",align:"center"}),m(re,{field:"packageId",title:"功能包",align:"center"},{default:p((({row:e})=>[m(oe,{value:e.packageId,"onUpdate:value":a=>e.packageId=a,style:{width:"100%","border-radius":"4px","text-align":"left"},placeholder:"请选择功能包"},{default:p((()=>[(s(!0),v(y,null,_(G.value,(e=>(s(),d(ie,{value:e.packageId,key:e.packageId},{default:p((()=>[c(h(e.packageName),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value"])])),_:1}),m(re,{field:"serviceEndTime",title:"到期时间",width:"200",align:"center"},{default:p((({row:e})=>[m(ae,{value:e.serviceEndTime,"onUpdate:value":a=>e.serviceEndTime=a,"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择到期时间",style:{width:"100%","border-radius":"4px"}},null,8,["value","onUpdate:value"])])),_:1}),m(re,{field:"trialFlag",title:"是否试用",width:"100",align:"center"},{default:p((({row:e})=>[m(ue,{modelValue:e.trialFlag,"onUpdate:modelValue":a=>e.trialFlag=a,"open-value":"Y","close-value":"N"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),m(re,{title:"操作",width:"100",align:"center"},{default:p((({row:e})=>[m(se,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:l=>(async e=>{"confirm"===await x.modal.confirm("您确定要删除该数据?")&&R.value.remove(e);const l=R.value.getTableData().tableData;a.form.tenantLinkList=l})(e)},null,8,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1}),m(o,{span:24},{default:p((()=>n[22]||(n[22]=[f("div",{class:"company"},"公司信息",-1)]))),_:1,__:[22]}),m(o,{span:12},{default:p((()=>[m(i,{label:"公司名称:",name:"companyName"},{default:p((()=>[m(r,{value:e.form.companyName,"onUpdate:value":n[10]||(n[10]=a=>e.form.companyName=a),"allow-clear":"",placeholder:"请输入公司名称"},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"统一社会信用代码:",name:"companySocialCode"},{default:p((()=>[m(r,{value:e.form.companySocialCode,"onUpdate:value":n[11]||(n[11]=a=>e.form.companySocialCode=a),"allow-clear":"",placeholder:"请输入统一社会信用代码"},null,8,["value"])])),_:1})])),_:1}),m(o,{span:12},{default:p((()=>[m(i,{label:"公司地址:",name:"companyAddress"},{default:p((()=>[m(r,{value:e.form.companyAddress,"onUpdate:value":n[12]||(n[12]=a=>e.form.companyAddress=a),"allow-clear":"",placeholder:"请输入公司地址"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(me,{visible:V.value,footer:null,onCancel:n[13]||(n[13]=e=>V.value=!1)},{default:p((()=>[f("img",{alt:"example",style:{width:"100%"},src:B.value},null,8,$)])),_:1},8,["visible"])])),_:1},8,["model","rules"])}}};e("default",l(V,[["__scopeId","data-v-b5566309"]]))}}}));
