System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.R}],execute:function(){class e{static findPage(t){return r.postAndLoadData("/erp/inventory/page",t)}static findList(t){return r.getAndLoadData("/erp/inventory/list",t)}static detail(t){return r.getAndLoadData("/erp/inventory/detail",t)}static warningList(t){return r.getAndLoadData("/erp/inventory/warning",t)}static outOfStockList(t){return r.getAndLoadData("/erp/inventory/outOfStock",t)}static inventoryValue(t){return r.getAndLoadData("/erp/inventory/value",t)}static setMinStock(t){return r.post("/erp/inventory/setMinStock",t)}static initInventory(t){return r.post("/erp/inventory/init",t)}static adjustInventory(t){return r.post("/erp/inventory/adjust",t)}static batchAdjustInventory(t){return r.post("/erp/inventory/batchAdjust",t)}static setInventoryAlert(t){return r.post("/erp/inventory/setAlert",t)}static getChangeTypeOptions(){return[{label:"入库",value:"INBOUND"},{label:"出库",value:"OUTBOUND"},{label:"调整",value:"ADJUSTMENT"},{label:"盘点",value:"STOCKTAKING"},{label:"损耗",value:"LOSS"},{label:"退货",value:"RETURN"}]}static getInventoryStatusOptions(){return[{label:"正常",value:"NORMAL"},{label:"预警",value:"WARNING"},{label:"缺货",value:"OUT_OF_STOCK"},{label:"滞销",value:"SLOW_MOVING"},{label:"过期",value:"EXPIRED"}]}static getAdjustReasonOptions(){return[{label:"盘点差异",value:"STOCKTAKING_DIFF"},{label:"系统错误",value:"SYSTEM_ERROR"},{label:"商品损坏",value:"DAMAGED"},{label:"商品过期",value:"EXPIRED"},{label:"其他原因",value:"OTHER"}]}static getChangeTypeName(t){const r=e.getChangeTypeOptions().find((e=>e.value===t));return r?r.label:t}static getInventoryStatusName(t){const r=e.getInventoryStatusOptions().find((e=>e.value===t));return r?r.label:t}static getAdjustReasonName(t){const r=e.getAdjustReasonOptions().find((e=>e.value===t));return r?r.label:t}static getInventoryStatusTagColor(t){switch(t){case"NORMAL":return"green";case"WARNING":return"orange";case"OUT_OF_STOCK":case"EXPIRED":return"red";case"SLOW_MOVING":return"blue";default:return"default"}}static getChangeTypeTagColor(t){switch(t){case"INBOUND":return"green";case"OUTBOUND":return"blue";case"ADJUSTMENT":return"orange";case"STOCKTAKING":return"purple";case"LOSS":return"red";case"RETURN":return"cyan";default:return"default"}}static formatQuantity(t,e){return null==t?"-":`${t}${e||""}`}static formatValue(t){return t?`¥${t.toFixed(2)}`:"¥0.00"}static isStockWarning(t,e){return t<=e}static isOutOfStock(t){return t<=0}static calculateTurnoverRate(t,e){return e&&0!==e?(t/e).toFixed(2):0}static exportInventory(t){return r.downLoad("/erp/inventory/export",t)}static exportInventoryAlerts(t){return r.downLoad("/erp/inventory/exportAlerts",t)}static exportInventoryChanges(t){return r.downLoad("/erp/inventory/exportChanges",t)}}t("I",e)}}}));
