package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * POS系统基础异常类
 *
 * 继承框架的ServiceException，遵循现有异常处理规范
 *
 * <AUTHOR>
 * @since 2025/08/01 21:30
 */
public class PosException extends ServiceException {

    public PosException(AbstractExceptionEnum exception) {
        super(ErpConstants.ERP_MODULE_NAME, exception);
    }

    public PosException(AbstractExceptionEnum exception, Object... params) {
        super(ErpConstants.ERP_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

}
