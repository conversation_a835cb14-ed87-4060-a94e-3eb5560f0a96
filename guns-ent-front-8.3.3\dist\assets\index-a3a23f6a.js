import{r as m,s as v,a as g,c as y,d as r,w as c,g as f,b as i,t as d,B as b,a0 as x}from"./index-18a1ea24.js";/* empty css              */import{_ as $}from"./index-ba83f962.js";const k={class:"guns-body guns-body-card"},B={style:{"margin-top":"12px"}},h={style:{"margin-top":"12px"}},C={style:{"margin-top":"12px"}},w={__name:"index",setup(A){const a=m(!1),l=()=>{a.value=!0},s=v({location:"",address:"",lngAndLat:""}),_=e=>{var t,n,o;console.log(e),s.location="".concat((t=e.city)==null?void 0:t.province,"/").concat((n=e.city)==null?void 0:n.city,"/").concat((o=e.city)==null?void 0:o.district),s.address="".concat(e.name," ").concat(e.address),s.lngAndLat="".concat(e.lng,",").concat(e.lat),a.value=!1};return(e,t)=>{const n=b,o=$,p=x;return g(),y("div",k,[r(p,{title:"\u5730\u56FE\u9009\u62E9",bordered:!1},{default:c(()=>[r(n,{class:"ele-btn-icon",onClick:l},{default:c(()=>t[1]||(t[1]=[f(" \u6253\u5F00\u5730\u56FE\u4F4D\u7F6E\u9009\u62E9\u5668 ")])),_:1,__:[1]}),i("div",B,"\u9009\u62E9\u4F4D\u7F6E: "+d(s.location),1),i("div",h,"\u8BE6\u7EC6\u5730\u5740: "+d(s.address),1),i("div",C,"\u7ECF \u7EAC \u5EA6 : "+d(s.lngAndLat),1),r(o,{"need-city":!0,visible:a.value,"onUpdate:visible":t[0]||(t[0]=u=>a.value=u),"search-type":0,onDone:_},null,8,["visible"])]),_:1})])}}};export{w as default};
