import{s as i,a as c,f as p,w as t,d as e,g as d,B as l,n as u,a0 as m}from"./index-18a1ea24.js";/* empty css              */import{p as f}from"./print-4e42e756.js";const C={__name:"print-this",setup(x){const o=i({horizontal:void 0,margin:void 0,title:""}),a=()=>{f(o)};return(B,n)=>{const s=l,r=u,_=m;return c(),p(_,{title:"\u6253\u5370\u5F53\u524D\u9875\u9762",bordered:!1},{default:t(()=>[e(r,null,{default:t(()=>[e(s,{onClick:a},{default:t(()=>n[0]||(n[0]=[d("\u6253\u5370")])),_:1,__:[0]})]),_:1})]),_:1})}}};export{C as default};
