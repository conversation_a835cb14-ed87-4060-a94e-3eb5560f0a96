<template>
  <!-- 新增编辑 -->
  <a-modal
    :width="900"
    :maskClosable="false"
    :visible="props.visible"
    :confirm-loading="loading"
    :forceRender="true"
    title="租户审批"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
    @close="updateVisible(false)"
  >
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-form-item label="租户生效时间:" name="activeDate">
            <a-date-picker
              v-model:value="form.activeDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择租户生效时间"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="到期时间:" name="expireDate">
            <a-date-picker
              v-model:value="form.expireDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择到期时间"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <div class="company">开通功能</div>
        </a-col>
        <a-col :span="24">
          <div style="margin-bottom: 10px"><a-button type="primary" class="border-radius" @click="addUse">+ 添加功能</a-button></div>
          <vxe-table
            border
            style="margin-bottom: 20px"
            show-overflow
            :data="form.tenantLinkList"
            :row-config="{ useKey: true }"
            :column-config="{ resizable: true }"
            height="300"
            ref="xTableRef"
          >
            <vxe-column type="seq" width="60" title="序号" align="center" />
            <vxe-column field="packageId" title="功能包" align="center">
              <template #default="{ row }">
                <a-select
                  v-model:value="row.packageId"
                  style="width: 100%; border-radius: 4px; text-align: left"
                  placeholder="请选择功能包"
                >
                  <a-select-option :value="item.packageId" v-for="item in useList" :key="item.packageId">{{
                    item.packageName
                  }}</a-select-option>
                </a-select>
              </template>
            </vxe-column>
            <vxe-column field="serviceEndTime" title="到期时间" width="200" align="center">
              <template #default="{ row }">
                <a-date-picker
                  v-model:value="row.serviceEndTime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择到期时间"
                  style="width: 100%; border-radius: 4px"
                />
              </template>
            </vxe-column>
            <vxe-column field="trialFlag" title="是否试用" width="100" align="center">
              <template #default="{ row }">
                <vxe-switch v-model="row.trialFlag" open-value="Y" close-value="N" />
              </template>
            </vxe-column>
            <vxe-column title="操作" width="100" align="center">
              <template #default="{ row }">
                <icon-font iconClass="icon-opt-shanchu" font-size="24px" title="删除" color="#60666b" @click="remove(row)" />
              </template>
            </vxe-column>
          </vxe-table>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup name="TenantApprover">
import { ref, onMounted, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { VXETable } from 'vxe-table';
import { TenantApi } from '@/views/tenant/manage/api/TenantApi';
import { getNowTime, GetNextMonthDay } from '@/utils/common/time-util';

const props = defineProps({
  visible: Boolean,
  // 审批列表
  approverList: Array
});

const emits = defineEmits(['update:visible', 'done']);
// 弹框加载
const loading = ref(false);
// 表单数据
const form = ref({
  tenantIdList: props.approverList,
  activeDate: getNowTime() + ' 00:00:00',
  expireDate: GetNextMonthDay(getNowTime(), 1) + ' 00:00:00',
  tenantLinkList: [] //开通功能
});
// 验证规则
const rules = reactive({
  activeDate: [{ required: true, message: '请选择租户生效时间', type: 'string', trigger: 'change' }],
  expireDate: [{ required: true, message: '请选择到期时间', type: 'string', trigger: 'change' }]
});
// ref
const formRef = ref(null);
const xTableRef = ref(null);
// 功能包列表
const useList = ref([]);

onMounted(() => {
  getUseList();
});

// 获取功能包列表
const getUseList = () => {
  TenantApi.tenantPackageList().then(res => {
    useList.value = res.data;
  });
};

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};

// 点击保存
const save = async () => {
  formRef.value.validate().then(async valid => {
    if (valid) {
      // 修改加载框为正在加载
      loading.value = true;
      TenantApi.auditTenant(form.value)
        .then(async result => {
          // 移除加载框
          loading.value = false;

          // 提示添加成功
          message.success(result.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          emits('done');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};

// 添加功能包
const addUse = () => {
  let obj = {
    trialFlag: 'N',
    packageId: null,
    serviceEndTime: ''
  };
  form.value.tenantLinkList.push(obj);
};

// 删除机构
const remove = async row => {
  // 删除当前行
  const type = await VXETable.modal.confirm('您确定要删除该数据?');
  if (type === 'confirm') {
    const $table = xTableRef.value;
    $table.remove(row);
  }
  const res = xTableRef.value.getTableData().tableData;
  form.value.tenantLinkList = res;
};
</script>

<style scoped lang="less">
.company {
  border-left: 4px solid var(--primary-color);
  padding-left: 10px;
  margin-bottom: 20px;
}
</style>
