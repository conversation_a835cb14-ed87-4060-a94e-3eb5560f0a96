System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js"],(function(n,t){"use strict";var e,o,i,a,l,c,d,s,f,g,r,u,p,m,_,b,v,C,x,L,h,y,w,S,I,k;return{setters:[n=>{e=n._,o=n.aP,i=n.s,a=n.o,l=n.bX,c=n.bY,d=n.bu,s=n.a,f=n.f,g=n.w,r=n.m,u=n.S,p=n.b,m=n.d,_=n.t,b=n.c,v=n.e,C=n.F,x=n.g,L=n.l,h=n.u,y=n.v,w=n.G,S=n.B,I=n.H,k=n.a0},null],execute:function(){var t=document.createElement("style");t.textContent=".content[data-v-a7487e2d]{border:1px solid #ccc;border-radius:5px}.content-top[data-v-a7487e2d]{text-align:center;height:32px;line-height:32px;background:#ccc;margin-bottom:10px}.content-row[data-v-a7487e2d]{margin:20px;text-align:center}.content-item[data-v-a7487e2d]{font-weight:700;padding-bottom:5px;border-bottom:2px solid #eee}.button-item[data-v-a7487e2d]{margin:20px;border-top:2px solid #eee;padding-top:20px}\n",document.head.appendChild(t);const E=o({name:"InitPage",components:{},setup(){let n=i({loading:!0,form:{},configList:[],submitLoading:!1});return a((async()=>{if(await l.getInitConfigFlag())return void c.push("/");n.loading=!1,n.configList=await l.getInitConfigList();let t=window.location.host;-1===window.location.href.indexOf("/guns-devops")&&(t+="/api"),n.configList.initConfigGroupList.forEach((e=>{for(const o of e.configInitItemList)"SYS_SERVER_DEPLOY_HOST"===o.configCode||"WEB_SOCKET_WS_URL"===o.configCode?n.form[o.configCode]=o.configValue.replace("localhost:8080",t):n.form[o.configCode]=o.configValue}))})),{...d(n),onSubmit:async()=>{n.submitLoading=!0;let t={sysConfigs:n.form};l.initConfig(t).then((n=>{r.success(n.message),c.push("/")})).finally((()=>{n.submitLoading=!1}))},onReset:async()=>{n.loading=!0,n.configList=await l.getInitConfigList(),n.loading=!1;for(const t of n.configList)n.form[t.configCode]=t.configValue}}}}),R={class:"guns-body",style:{"background-color":"#fafafa"}},O={class:"content-row"},V={class:"content-row content-item"},D={class:"content-row"},G={class:"button-item"};n("default",e(E,[["render",function(n,t,e,o,i,a){const l=L,c=h,d=y,r=w,E=S,P=I,U=k,Y=u;return s(),f(Y,{spinning:n.loading},{default:g((()=>[p("div",R,[m(U,null,{default:g((()=>[m(r,null,{default:g((()=>[m(d,{span:18,offset:3,class:"content"},{default:g((()=>[t[2]||(t[2]=p("div",{class:"content-top"},[p("h5",null,"首次安装程序")],-1)),m(P,{model:n.form,"label-col":{span:6},"wrapper-col":{span:16}},{default:g((()=>[p("div",O,_(n.configList.description),1),(s(!0),b(C,null,v(n.configList.initConfigGroupList,((t,e)=>(s(),b("div",{key:e+"con"},[p("div",V,_(t.title),1),p("div",D,_(t.description),1),(s(!0),b(C,null,v(t.configInitItemList,((t,e)=>(s(),f(r,{key:e+"init"},{default:g((()=>[m(d,{span:16},{default:g((()=>[m(c,null,{label:g((()=>[x(_(t.configName),1)])),default:g((()=>[m(l,{name:t.configCode,value:n.form[t.configCode],"onUpdate:value":e=>n.form[t.configCode]=e,placeholder:t.configDescription},null,8,["name","value","onUpdate:value","placeholder"])])),_:2},1024)])),_:2},1024),m(d,{span:8,style:{color:"#98999b"}},{default:g((()=>[x(_(t.configDescription),1)])),_:2},1024)])),_:2},1024)))),128))])))),128)),p("div",G,[m(c,{"wrapper-col":{span:14,offset:4}},{default:g((()=>[m(E,{type:"primary",loading:n.submitLoading,onClick:n.onSubmit},{default:g((()=>t[0]||(t[0]=[x("提交")]))),_:1,__:[0]},8,["loading","onClick"]),m(E,{onClick:n.onReset,style:{"margin-left":"10px"}},{default:g((()=>t[1]||(t[1]=[x("重置")]))),_:1,__:[1]},8,["onClick"])])),_:1})])])),_:1},8,["model"])])),_:1,__:[2]})])),_:1})])),_:1})])])),_:1},8,["spinning"])}],["__scopeId","data-v-a7487e2d"]]))}}}));
