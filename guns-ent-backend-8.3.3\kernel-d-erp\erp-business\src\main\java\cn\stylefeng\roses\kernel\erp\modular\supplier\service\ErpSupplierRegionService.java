package cn.stylefeng.roses.kernel.erp.modular.supplier.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierRegionResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;

import java.util.List;

/**
 * 供应商-区域关联Service接口
 *
 * <AUTHOR>
 * @since 2025/07/22 16:00
 */
public interface ErpSupplierRegionService {

    /**
     * 获取供应商关联的区域列表
     *
     * @param erpSupplierRegionRequest 请求参数
     * @return 区域列表
     */
    List<ErpRegionResponse> getSupplierRegions(ErpSupplierRegionRequest erpSupplierRegionRequest);

    /**
     * 更新供应商关联的区域
     *
     * @param erpSupplierRegionRequest 请求参数
     */
    void updateSupplierRegions(ErpSupplierRegionRequest erpSupplierRegionRequest);

    /**
     * 根据区域ID查询关联的供应商
     *
     * @param erpSupplierRegionRequest 请求参数
     * @return 供应商分页列表
     */
    PageResult<ErpSupplierResponse> getSuppliersByRegion(ErpSupplierRegionRequest erpSupplierRegionRequest);

    /**
     * 统计区域关联的供应商数量
     *
     * @param erpSupplierRegionRequest 请求参数
     * @return 供应商数量
     */
    Long countSuppliersByRegion(ErpSupplierRegionRequest erpSupplierRegionRequest);

    void fillSupplierRegionInfo(ErpSupplierResponse response);
}