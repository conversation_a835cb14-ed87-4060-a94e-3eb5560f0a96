# Mysql数据库
spring:
  datasource:
    name: accountDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    # 连接池大小根据实际情况调整
    max-active: 100
    max-pool-prepared-statement-per-connection-size: 100
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848

seata:
  enabled: true
  service:
    vgroup-mapping:
      account-service-seata-service-group: default
    grouplist:
      default: 127.0.0.1:8091
    disable-global-transaction: false