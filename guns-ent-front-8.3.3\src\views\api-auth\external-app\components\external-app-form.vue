<template>
  <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
    <a-row :gutter="20">
      <a-col :span="12">
        <a-form-item label="应用名称:" name="apiClientName">
          <a-input v-model:value="form.apiClientName" allow-clear placeholder="请输入应用名称" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="应用编码:" name="apiClientCode">
          <a-input v-model:value="form.apiClientCode" allow-clear placeholder="请输入应用编码" :disabled="props.isUpdate" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="认证秘钥:" name="apiClientSecret">
          <a-input-group compact style="display: flex">
            <a-input v-model:value="form.apiClientSecret" allow-clear placeholder="请输入认证秘钥" />
            <a-button type="primary" @click="generateApiClientSecret">点击生成</a-button>
          </a-input-group>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="认证秘钥过期时间(秒):" name="apiClientTokenExpiration">
          <a-input-number
            v-model:value="form.apiClientTokenExpiration"
            :min="0"
            style="width: 100%"
            placeholder="请输入认证秘钥过期时间"
            allow-clear
            autocomplete="off"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="状态:" name="apiClientStatus">
          <a-radio-group v-model:value="form.apiClientStatus">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="2">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="排序:" name="apiClientSort">
          <a-input-number
            v-model:value="form.apiClientSort"
            :min="0"
            style="width: 100%"
            placeholder="请输入排序"
            allow-clear
            autocomplete="off"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <div class="card-title">传输加密</div>
      </a-col>
      <a-col :span="24">
        <a-form-item label="公钥:" name="apiPublicKey">
          <a-input-group compact style="display: flex">
            <a-input v-model:value="form.apiPublicKey" allow-clear placeholder="请输入公钥" />
            <a-button type="primary" @click="generateApiPublicKey">点击生成</a-button>
          </a-input-group>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="私钥:" name="apiPrivateKey">
          <a-input v-model:value="form.apiPrivateKey" allow-clear placeholder="请输入私钥" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup name="ExternalAppForm">
import { reactive } from 'vue';
import { uuid } from '@/utils/common/print';
import { ExternalAppApi } from '../api/ExternalAppApi';

const props = defineProps({
  // 表单数据
  form: Object,
  isUpdate: Boolean
});

// 验证规则
const rules = reactive({
  apiClientName: [{ required: true, message: '请输入应用名称', type: 'string', trigger: 'blur' }],
  apiClientCode: [{ required: true, message: '请输入应用编码', type: 'string', trigger: 'blur' }],
  apiClientSecret: [{ required: true, message: '请输入认证秘钥', type: 'string', trigger: 'blur' }],
  apiClientTokenExpiration: [{ required: true, message: '请输入认证秘钥过期时间', type: 'number', trigger: 'blur' }],
  apiClientSort: [{ required: true, message: '请输入排序', type: 'number', trigger: 'blur' }],
  apiClientStatus: [{ required: true, message: '请选择状态', type: 'number', trigger: 'change' }]
});

// 生成16位认证秘钥
const generateApiClientSecret = () => {
  props.form.apiClientSecret = uuid(16);
};

// 生成公钥
const generateApiPublicKey = async () => {
  const res = await ExternalAppApi.randomRsaKey();
  props.form.apiPublicKey = res.publicKey;
  props.form.apiPrivateKey = res.privateKey;
};
</script>

<style scoped lang="less">
.card-title {
  width: 100%;
  border-left: 5px solid;
  border-color: var(--primary-color);
  padding-left: 10px;
  margin-bottom: 20px;
}
</style>
