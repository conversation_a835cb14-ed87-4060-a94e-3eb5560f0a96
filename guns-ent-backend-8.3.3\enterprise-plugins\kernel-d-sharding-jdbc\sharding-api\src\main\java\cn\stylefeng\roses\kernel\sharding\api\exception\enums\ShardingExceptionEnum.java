package cn.stylefeng.roses.kernel.sharding.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.sharding.api.constants.ShardingConstants;
import lombok.Getter;

/**
 * sharding模块的异常枚举
 *
 * <AUTHOR>
 * @date 2021/7/16 16:47
 */
@Getter
public enum ShardingExceptionEnum implements AbstractExceptionEnum {

    /**
     * sharding模块通用异常
     */
    SHARDING_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + ShardingConstants.SHARDING_SERVER_EXCEPTION_STEP_CODE + "01", "sharding模块通用异常");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ShardingExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
