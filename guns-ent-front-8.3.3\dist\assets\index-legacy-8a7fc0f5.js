System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,t){"use strict";var l,a,o,n,d,i,s,r,c,u,h,p,f,g,b,v,x,y,m,w,S,I,k,C,z,B,R,T,K,A,j;return{setters:[e=>{l=e.R,a=e.aB,o=e._,n=e.aC,d=e.r,i=e.s,s=e.aD,r=e.o,c=e.aE,u=e.X,h=e.a,p=e.c,f=e.b,g=e.d,b=e.w,v=e.t,x=e.h,y=e.aF,m=e.f,w=e.at,S=e.aG,I=e.e,k=e.aH,C=e.F,z=e.g,B=e.a2,R=e.aI,T=e.aJ,K=e.aK,A=e.n,j=e.i},null,null,null],execute:function(){var E=document.createElement("style");E.textContent=".table-top[data-v-0d3e11b9]{position:relative;height:100%;display:flex;flex-direction:column}.table-top .table-bottom[data-v-0d3e11b9]{position:absolute;bottom:15px;left:-20px}.table-tool[data-v-0d3e11b9]{min-height:50px;padding:8px 16px;display:flex;height:auto;flex-direction:column;align-items:center;border:1px solid rgba(197,207,209,.4);border-top-right-radius:8px;border-top-left-radius:8px}.table-tool .table-tool-top[data-v-0d3e11b9]{width:100%;height:auto;flex:auto;display:flex;flex-direction:row;align-items:center}.table-tool .table-tool-top .table-tool-top-left[data-v-0d3e11b9],.table-tool .table-tool-top .table-tool-top-right[data-v-0d3e11b9]{display:flex;flex:auto;align-items:center}.table-tool .table-tool-top .table-tool-top-left[data-v-0d3e11b9]{margin-right:10px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.table-tool .table-tool-top .table-tool-top-right[data-v-0d3e11b9]{white-space:nowrap;justify-content:end}.table-tool .table-tool-top .table-tool-top-left-space[data-v-0d3e11b9]{width:100%}.table-tool .table-tool-top .table-tool-top-left-space[data-v-0d3e11b9] .ant-space-item:last-child{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.table-tool .table-tool-bottom[data-v-0d3e11b9]{width:100%;height:auto;display:flex;flex-direction:row;align-items:center}.total-num[data-v-0d3e11b9]{font-size:16px;color:#60666b}[data-v-0d3e11b9] .ant-table{border-left:1px solid rgba(197,207,209,.4);border-right:1px solid rgba(197,207,209,.4);border-bottom:1px solid rgba(197,207,209,.4);border-top:var(--37496551)}[data-v-0d3e11b9] .table{width:100%;flex:auto}[data-v-0d3e11b9] .table ::-webkit-scrollbar{width:10px!important}.table-height-100[data-v-0d3e11b9] .ant-spin-nested-loading{height:100%}.table-height-100[data-v-0d3e11b9] .ant-spin-nested-loading .ant-spin-container,.table-height-100[data-v-0d3e11b9] .ant-spin-nested-loading .ant-table-container{height:100%;display:flex;flex-direction:column}.table-height-100[data-v-0d3e11b9] .ant-spin-nested-loading .ant-table-container .ant-table-body{position:relative;flex:1}.table-height-100[data-v-0d3e11b9] .ant-spin-nested-loading .ant-table-container .ant-table-body table{position:absolute;left:0;top:0;right:0;bottom:0}.table-height-100[data-v-0d3e11b9] .ant-spin-nested-loading .ant-table{flex:1}[data-v-0d3e11b9] .ant-table-body{overflow-y:auto}[data-v-0d3e11b9] .ant-table-bordered .ant-table-container:after{border-right:1.5px solid var(--border-color-split);border-bottom:1.5px solid var(--border-color-split)}[data-v-0d3e11b9] .ant-table-bordered div.ant-table-body:before{background:transparent}.table-fullscreen[data-v-0d3e11b9]{z-index:999;position:fixed;top:0;left:0;width:100vw;height:100vh;overflow:auto;padding:0 0 16px;box-sizing:border-box;background:#fff}.table-fullscreen .ant-table-pagination[data-v-0d3e11b9]{margin-bottom:0;padding:0 16px}@media screen and (max-width: 768px){.table-tool[data-v-0d3e11b9]{justify-content:left;min-height:50px;height:auto!important;flex-wrap:wrap}.table-tool-top-left[data-v-0d3e11b9],.table-tool-top-right[data-v-0d3e11b9]{flex:none!important}.table-tool-top-left[data-v-0d3e11b9]{margin-right:10px}[data-v-0d3e11b9] .ant-table-thead .ant-table-cell-fix-right-first{right:1px!important}}\n",document.head.appendChild(E),e({a:function(e,t,l){const o=P(l);if(o)return o;const n=[];e&&a(e,(e=>{var l;const a=U(e);!a||null!==(l=e.children)&&void 0!==l&&l.length||e.hideInSetting||n.push({id:a,key:e.key,width:e.width||120,dataIndex:e.dataIndex,title:e.title||t,checked:e.checked,fixed:e.fixed,...e})}));const d=n.filter((e=>e.checked)).map((e=>e.id));return{data:n,checkedIds:d,isAllChecked:n.length>0&&n.length===d.length,isIndeterminate:0!==d.length&&n.length!==d.length}},b:O,c:$,d:D,g:_});class N{static getUserConfig(e){return l.getAndLoadData("/sysTableWidth/getUserConfig",e)}static setTableWidth(e){return l.post("/sysTableWidth/setTableWidth",e)}}function $(e){return`${e}Size`}function L(e){const t=[];return e.forEach((e=>{var l;if(null!==(l=e.children)&&void 0!==l&&l.length){const l=L(e.children);l.length&&t.push({...e,checked:!0,children:l})}else t.push({...e,checked:!e.hideInTable})})),t}function _(e,t,l){const a=P(t);return a?O(e,a.data,a.checkedIds,l):L(e)}function O(e,t,l,a){const o=[];return e&&e.forEach((e=>{var n,d;const i=U(e),s=t.find((e=>e.id===i)),r=null!==(n=null==s?void 0:s.fixed)&&void 0!==n?n:e.fixed,c=null===(d=null==s?void 0:s.checked)||void 0===d||d;if(e.children){const n=O(e.children,t,l,a);n.length&&o.push({...e,fixed:r,checked:c,children:n})}else o.push({...e,fixed:r,checked:c})})),a&&o.length&&o.sort(((e,l)=>{var a,o;const n=U(e),d=U(l);let i=t.findIndex((e=>e.id===n)),s=t.findIndex((e=>e.id===d));if(-1===i&&null!==(a=e.children)&&void 0!==a&&a.length){const l=U(e.children[0]);i=t.findIndex((e=>e.id===l))}if(-1===s&&null!==(o=l.children)&&void 0!==o&&o.length){const e=U(l.children[0]);s=t.findIndex((t=>t.id===e))}return-1===i&&e.hideInSetting&&"right"===e.fixed&&(i=t.length),!0===e.fixed||"left"===e.fixed?i-=t.length:("right"===e.fixed||e.hideInSetting&&!e.fixed)&&(i+=t.length),-1===s&&l.hideInSetting&&"right"===l.fixed&&(s=t.length),!0===l.fixed||"left"===l.fixed?s-=t.length:("right"===l.fixed||l.hideInSetting&&!l.fixed)&&(s+=t.length),i-s})),o}function P(e){if(e)try{const t=localStorage.getItem(D(e));if(t){const e=JSON.parse(t);if(Array.isArray(e)){const t=e.filter((e=>e.checked)).map((e=>e.id));return{data:e,checkedIds:t,isAllChecked:e.length>0&&e.length===t.length,isIndeterminate:0!==t.length&&e.length!==t.length}}}}catch(t){}}function D(e){return`${e}Cols`}function U(e){const t=e.key||e.dataIndex;return(Array.isArray(t)?t:[t]).join(".")}e("C",N);const J={key:0,class:"table-tool"},W={class:"table-tool-top"},F={class:"table-tool-top-left"},G={key:0,class:"total-num"},H={class:"table-tool-top-right"},X={class:"table-tool-bottom"},q=Object.assign({name:"GunsTable"},{__name:"index",props:{columns:{type:Array,default:[]},rowId:{type:String,default:"id"},url:{type:String,default:""},methods:{type:String,default:"get"},where:{type:Object,default:{}},isSort:{type:Boolean,default:!1},bordered:{type:Boolean,default:!1},isPage:{type:Boolean,default:!0},isRadio:{type:Boolean,default:!1},pageSize:{type:Number,default:20},scroll:{type:Object,default:{x:"max-content",y:"100%"}},isInit:{type:Boolean,default:!0},rowSelection:{type:Boolean,default:!0},isShowRowSelect:{type:Boolean,default:!1},size:{type:String,default:"small"},checkStrictly:{type:Boolean,default:!1},customData:{type:Function,default:null},dataSource:{type:Array,default:[]},expandIconColumnIndex:{type:Number,default:0},isLoad:{type:[Function,Boolean],default:()=>!0},selection:Array,loading:{type:Boolean,default:!1},defaultExpandedRowKeys:Array,expandedRowKeys:Array,showTool:{type:Boolean,default:!0},showTableTool:{type:Boolean,default:!1},tools:{type:Array,default:()=>["reload","size","columns","fullscreen"]},cacheKey:String,fieldBusinessCode:{type:String,default:""},showToolTotal:{type:Boolean,default:!0},childrenColumnName:{type:String,default:"children"},montageParams:{type:Boolean,default:!1},height100:{type:Boolean,default:!0}},emits:["tableListChange","onSelect","onSelectAll","customRowClick","getTotal","update:selection","expand","size-change","columns-change","fullscreen-change"],setup(e,{expose:a,emit:o}){n((e=>({37496551:L.showTool?0:"1px solid rgba(197, 207, 209, 0.4)"})));const E=R((()=>T((()=>t.import("./table-tool-legacy-5242c02c.js")),void 0))),L=e,O=o,P=d([]),D=d(!1),U=i({current:1,pageSize:L.pageSize,showSizeChanger:!0,showLessItems:!0,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共 ${e} 条`,onShowSizeChange:(e,t)=>de(e,t),onChange:(e,t)=>ue(e),total:0}),q=d([]),M=d(""),Q=d(""),V=d([]),Y=s(),Z=Object.keys(Y),ee=d([]),te=d("small"),le=d(!1),ae=d([]);r((()=>{oe(!0),L.isInit&&ne()}));const oe=async(e=!1)=>{let t=L.columns;if(L.fieldBusinessCode&&e){const e=await N.getUserConfig({fieldBusinessCode:L.fieldBusinessCode});e.tableWidthJson&&(t=JSON.parse(e.tableWidthJson))}ae.value=t?_(t,L.cacheKey,!0):[]},ne=()=>{if(ee.value=[],L.url&&L.isLoad){D.value=!0;let e={...L.where};L.isSort&&(e.sortBy=M.value,e.orderBy=c(Q.value)),L.isPage&&(e.pageNo=U.current||1,e.pageSize=U.pageSize||20);let t="";if(L.montageParams){void 0===e&&(e={});let l="?";for(let t in e)e[t]&&(l=l+t+"="+e[t]+"&");l=l.substring(0,l.length-1),t=`${L.url}${l}`}else t=L.url;l[L.methods](t,L.montageParams?{}:e).then((e=>{var t,l;L.isPage?(L.customData&&(e=L.customData(e)),P.value=e.data.rows,U.total=e.data.totalRows,O("getTotal",e.data.totalRows)):(L.customData&&(P.value=L.customData(e)),P.value=e.data,O("getTotal",null!==(t=null===(l=P.value)||void 0===l?void 0:l.length)&&void 0!==t?t:0))})).finally((()=>D.value=!1))}},de=(e,t)=>{U.pageSize=t,ne()},ie=(e,t)=>{q.value=e;const l=e.map((e=>{const l=t.find((t=>se(t,L.rowId)===e));return null!=l?l:null!=L.selection&&L.selection.length?L.selection.find((t=>se(t,L.rowId)===e)):null})).filter((e=>null!=e));O("update:selection",l)},se=(e,t)=>{if("function"==typeof t)return t(e);if(t){let l=e;return t.split(".").forEach((e=>{l=l?l[e]:null})),l}},re=(e,t,l)=>{V.value=l,O("onSelect",e,t,l)},ce=(e,t,l)=>{O("onSelectAll",e,t,l)},ue=(e,t)=>{e!=U.current&&(U.current=e,ne())},he=(e,t,l)=>{"ascend"==l.order?M.value="asc":"descend"==l.order&&(M.value="desc"),Q.value=l.field,L.isSort&&ne()},pe=()=>{K((()=>{U.current=1,ne()}))},fe=(e,t)=>({onClick:()=>{L.isShowRowSelect&&(L.isRadio?(q.value=[e[L.rowId]],O("onSelect",e,!0,[e])):V.value.find((t=>t[L.rowId]==e[L.rowId]))?(q.value.splice(q.value.findIndex((t=>t===e[L.rowId])),1),V.value.splice(V.value.findIndex((t=>t[L.rowId]===e[L.rowId])),1),O("onSelect",e,!1,V.value)):(q.value.push(e[L.rowId]),V.value.push(e),O("onSelect",e,!0,V.value))),O("customRowClick",e,t)}}),ge=(e,t)=>{O("expand",e,t)},be=e=>{te.value=e,O("size-change",e)},ve=e=>{ae.value=e,O("columns-change",e)},xe=e=>{le.value=e,O("fullscreen-change",e)};return u((()=>L.dataSource),(e=>{L.url||(P.value=e)}),{deep:!0,immediate:!0}),u((()=>L.columns),(e=>{e.some((e=>"checked"in e))?ve(e):oe()}),{deep:!0}),u((()=>L.selection),(e=>{if(!L.isRadio)if(null!=e&&e.length){const t=e.map((e=>se(e,L.rowId)));if(t.length!==q.value.length)q.value=t;else for(let e=0;e<t.length;e++)if(!q.value.includes(t[e]))return void(q.value=t)}else q.value.length&&(q.value=[])}),{immediate:!0,deep:!0}),u((()=>L.loading),(e=>{D.value=e})),u((()=>L.expandedRowKeys),(e=>{ee.value=e}),{deep:!0}),u((()=>L.size),(e=>{var t,l;te.value=null!==(t=null!==(l=function(e){if(e){const t=localStorage.getItem($(e));if(t)return JSON.parse(t)}}(L.cacheKey))&&void 0!==l?l:L.size)&&void 0!==t?t:"small"}),{deep:!0,immediate:!0}),a({reload:pe,list:P,setList:e=>{setTimeout((()=>{P.value=[...e]}),500)},expandedRowKeys:ee,tableLoading:D,selectedRowList:q}),(t,l)=>{var a;const o=A,n=j;return h(),p("div",{class:B(["table-top",{"table-fullscreen":le.value},{"table-height-100":L.height100}])},[L.showTool?(h(),p("div",J,[f("div",W,[f("div",F,[g(o,{size:16,class:"table-tool-top-left-space"},{default:b((()=>{var l;return[e.showToolTotal?(h(),p("span",G,"共 "+v(L.isPage?U.total:null===(l=P.value)||void 0===l?void 0:l.length)+" 个",1)):x("",!0),y(t.$slots,"toolLeft",{},void 0,!0)]})),_:3})]),f("div",H,[g(o,{size:16},{default:b((()=>[y(t.$slots,"toolRight",{},void 0,!0),L.showTableTool?(h(),m(w(E),{key:0,tools:L.tools,size:te.value,cacheKey:e.cacheKey,fullscreen:le.value,onReload:pe,columns:ae.value,fieldBusinessCode:L.fieldBusinessCode,"onUpdate:size":be,"onUpdate:columns":ve,"onUpdate:fullscreen":xe},null,8,["tools","size","cacheKey","fullscreen","columns","fieldBusinessCode"])):x("",!0)])),_:3})])]),f("div",X,[y(t.$slots,"toolBottom",{},void 0,!0)])])):x("",!0),g(n,{"data-source":P.value,"row-key":e.rowId,size:te.value,class:"table",bordered:e.bordered,columns:ae.value.filter(((e,t)=>{if(null!=e&&e.checked)return e})),loading:D.value,defaultExpandedRowKeys:L.defaultExpandedRowKeys,expandedRowKeys:ee.value,"onUpdate:expandedRowKeys":l[0]||(l[0]=e=>ee.value=e),expandIconColumnIndex:L.expandIconColumnIndex,pagination:!!e.isPage&&U,onChange:he,onExpand:ge,"row-selection":e.rowSelection?{type:e.isRadio?"radio":"checkbox",selectedRowKeys:q.value,onChange:ie,onSelect:re,checkStrictly:L.checkStrictly,onSelectAll:ce}:null,scroll:(null===(a=P.value)||void 0===a?void 0:a.length)>0?e.scroll:{y:"100%"},customRow:fe,childrenColumnName:L.childrenColumnName},S({_:2},[I(w(Z),(e=>({name:e,fn:b((l=>["bodyCell"==e?y(t.$slots,e,k({key:0,scope:l},l||{}),(()=>{var e;return["序号"==(null===(e=l.column)||void 0===e?void 0:e.title)?(h(),p(C,{key:0},[z(v(l.index+1),1)],64)):x("",!0)]}),!0):y(t.$slots,e,k({key:1,scope:l},l||{}),void 0,!0)]))})))]),1032,["data-source","row-key","size","bordered","columns","loading","defaultExpandedRowKeys","expandedRowKeys","expandIconColumnIndex","pagination","row-selection","scroll","childrenColumnName"])],2)}}});e("_",o(q,[["__scopeId","data-v-0d3e11b9"]]))}}}));
