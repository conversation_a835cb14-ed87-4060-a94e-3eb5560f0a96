<template>
  <a-modal
    :visible="visible"
    title="选择商品"
    :width="1200"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :disabled="selectedProducts.length === 0" @click="handleConfirm">
        确定选择 ({{ selectedProducts.length }})
      </a-button>
    </template>

    <div class="product-selector-content">
      <a-row :gutter="16" style="height: 600px;">
        <!-- 左侧分类树 -->
        <a-col :span="6" class="category-tree-container">
          <div class="category-tree-header">
            <h4 class="tree-title">商品分类</h4>
          </div>
          <div class="category-tree-content">
            <a-spin :spinning="categoryLoading" size="small">
              <a-tree
                v-model:selectedKeys="selectedCategoryKeys"
                :tree-data="categoryTreeData"
                :field-names="{ children: 'children', title: 'title', key: 'key' }"
                :show-line="true"
                :show-icon="false"
                :default-expand-all="true"
                class="product-category-tree"
                @select="handleCategorySelect"
              >
                <template #title="{ title }">
                  <span class="category-title">{{ title }}</span>
                </template>
              </a-tree>
            </a-spin>
          </div>
        </a-col>

        <!-- 右侧商品区域 -->
        <a-col :span="18" class="product-content-container">
          <!-- 搜索区域 -->
          <div class="search-area">
            <a-row :gutter="16">
              <a-col :span="10">
                <a-input
                  v-model:value="searchParams.searchText"
                  placeholder="商品名称、编码"
                  allowClear
                  @pressEnter="loadProducts"
                >
                  <template #prefix>
                    <icon-font iconClass="icon-opt-search" />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="8">
                <a-select
                  v-model:value="searchParams.pricingType"
                  placeholder="计价类型"
                  allowClear
                  @change="loadProducts"
                >
                  <a-select-option value="NORMAL">普通</a-select-option>
                  <a-select-option value="WEIGHT">称重</a-select-option>
                  <a-select-option value="PIECE">计件</a-select-option>
                  <a-select-option value="VARIABLE">变价</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="handleReset">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>

          <!-- 商品列表 -->
          <div class="product-list">
            <a-table
              :columns="columns"
              :data-source="products"
              :loading="loading"
              :pagination="false"
              :row-selection="rowSelection"
              :row-key="record => record.productId"
              :scroll="{ y: 400 }"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record }">
                <!-- 商品信息 -->
                <template v-if="column.key === 'productInfo'">
                  <div class="product-info">
                    <div class="product-name">{{ record.productName }}</div>
                    <div class="product-details">
                      <span class="product-code">{{ record.productCode }}</span>
                      <span v-if="record.specification" class="product-spec">{{ record.specification }}</span>
                    </div>
                  </div>
                </template>

                <!-- 分类 -->
                <template v-if="column.key === 'categoryName'">
                  <a-tag color="blue">{{ record.categoryName }}</a-tag>
                </template>

                <!-- 计价类型 -->
                <template v-if="column.key === 'pricingType'">
                  <a-tag :color="getPricingTypeColor(record.pricingType)">
                    {{ getPricingTypeName(record.pricingType) }}
                  </a-tag>
                </template>

                <!-- 零售价 -->
                <template v-if="column.key === 'retailPrice'">
                  <span class="price-text">¥{{ formatAmount(record.retailPrice) }}</span>
                </template>

                <!-- 库存 -->
                <template v-if="column.key === 'stockQuantity'">
                  <span :class="getStockClass(record.stockQuantity)">
                    {{ record.stockQuantity || 0 }} {{ record.unit }}
                  </span>
                </template>
              </template>
            </a-table>

            <!-- 分页组件 -->
            <div class="pagination-container">
              <a-pagination
                v-model:current="pagination.current"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                :show-size-changer="pagination.showSizeChanger"
                :show-quick-jumper="pagination.showQuickJumper"
                :show-total="pagination.showTotal"
                :page-size-options="['10', '20', '50', '100']"
                size="small"
                @change="handlePageChange"
                @showSizeChange="handlePageSizeChange"
              />
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { ProductApi } from '@/views/erp/product/api/ProductApi';
import { ProductCategoryApi } from '@/views/erp/productCategory/api/productCategoryApi';

export default {
  name: 'ProductSelectorModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    supplierId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'select'],
  setup(props, { emit }) {
    const loading = ref(false);
    const categoryLoading = ref(false);
    const products = ref([]);
    const categories = ref([]);
    const categoryTreeData = ref([]);
    const selectedProducts = ref([]);
    const selectedCategoryKeys = ref([]);

    // 搜索参数
    const searchParams = reactive({
      searchText: '',
      categoryId: undefined,
      pricingType: undefined,
      supplierId: null
    });

    // 分页参数
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    });

    // 表格列定义
    const columns = [
      {
        title: '商品信息',
        key: 'productInfo',
        width: 250
      },
      {
        title: '分类',
        key: 'categoryName',
        width: 120
      },
      {
        title: '计价类型',
        key: 'pricingType',
        width: 100
      },
      {
        title: '零售价',
        key: 'retailPrice',
        width: 100,
        align: 'right'
      },
      {
        title: '库存',
        key: 'stockQuantity',
        width: 100,
        align: 'right'
      }
    ];

    // 行选择配置
    const rowSelection = computed(() => ({
      selectedRowKeys: selectedProducts.value.map(p => p.productId),
      onChange: (selectedRowKeys, selectedRows) => {
        console.log('行选择变化:', { selectedRowKeys, selectedRows });
        selectedProducts.value = selectedRows;
      },
      getCheckboxProps: (record) => {
        console.log('商品复选框状态检查:', { productId: record.productId, status: record.status });
        return {
          disabled: false // 暂时允许所有商品被选择，用于调试
        };
      }
    }));

    // 监听supplierId变化
    watch(() => props.supplierId, (newSupplierId) => {
      searchParams.supplierId = newSupplierId;
      if (props.visible && newSupplierId) {
        loadProducts();
      }
    });

    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      console.log('弹窗显示状态变化:', visible);
      if (visible) {
        selectedProducts.value = [];
        searchParams.supplierId = props.supplierId;
        console.log('开始加载分类树和商品数据，供应商ID:', props.supplierId);
        loadCategories();
        loadProducts();
      }
    });

    // 监听分类树数据变化
    watch(() => categoryTreeData.value, (newData) => {
      console.log('分类树数据变化:', {
        数据长度: newData.length,
        第一个节点: newData[0],
        完整数据: newData
      });
    }, { deep: true });

    // 加载商品分类树
    const loadCategories = async () => {
      categoryLoading.value = true;
      try {
        console.log('开始加载商品分类树');
        const categoryTree = await ProductCategoryApi.findTree({
          status: 'Y'
        });
        console.log('商品分类树API响应:', categoryTree);

        if (categoryTree && Array.isArray(categoryTree)) {
          // 确保每个节点都有正确的key和title字段
          const processTreeNode = (node) => {
            const processedNode = {
              ...node,
              key: node.key || node.categoryId,
              title: node.title || node.categoryName
            };

            if (node.children && Array.isArray(node.children)) {
              processedNode.children = node.children.map(processTreeNode);
            }

            return processedNode;
          };

          const processedTree = categoryTree.map(processTreeNode);

          // 添加"全部分类"根节点
          const allCategoriesNode = {
            key: 'ALL_CATEGORIES',
            title: '全部分类',
            categoryId: null,
            categoryName: '全部分类',
            children: processedTree,
            productCount: null,
            isLeaf: false
          };

          categoryTreeData.value = [allCategoriesNode];

          // 默认选中"全部分类"
          selectedCategoryKeys.value = ['ALL_CATEGORIES'];

          console.log('商品分类树加载成功，节点数量:', categoryTree.length);
          console.log('原始API数据:', categoryTree);
          console.log('处理后的树数据:', categoryTreeData.value);
        } else {
          console.warn('商品分类树数据格式错误:', categoryTree);
          categoryTreeData.value = [];
        }
      } catch (error) {
        console.error('加载商品分类树失败:', error);
        message.error('加载商品分类失败：' + (error.message || '未知错误'));
        categoryTreeData.value = [];
      } finally {
        categoryLoading.value = false;
      }
    };

    // 处理分类树选择
    const handleCategorySelect = (selectedKeys, info) => {
      console.log('分类树选择:', { selectedKeys, info });

      if (selectedKeys.length > 0) {
        const selectedKey = selectedKeys[0];

        // 如果选择的是"全部分类"，则不设置分类ID筛选
        if (selectedKey === 'ALL_CATEGORIES') {
          searchParams.categoryId = null;
        } else {
          // 从选中的节点获取实际的categoryId
          const selectedNode = info.node;
          searchParams.categoryId = selectedNode.categoryId || selectedKey;
        }

        selectedCategoryKeys.value = selectedKeys;
        console.log('设置分类筛选条件:', searchParams.categoryId);

        // 重置分页并重新加载商品列表
        pagination.current = 1;
        loadProducts();
      }
    };

    // 加载商品列表
    const loadProducts = async () => {
      if (!props.supplierId) {
        products.value = [];
        return;
      }

      loading.value = true;
      try {
        console.log('开始加载商品列表，参数:', {
          supplierId: props.supplierId,
          categoryId: searchParams.categoryId,
          searchText: searchParams.searchText,
          pricingType: searchParams.pricingType,
          pageNo: pagination.current,
          pageSize: pagination.pageSize
        });

        // 构建查询参数
        const queryParams = {
          supplierId: props.supplierId,
          pageNo: pagination.current,
          pageSize: pagination.pageSize
        };

        // 添加可选的筛选条件
        if (searchParams.categoryId) {
          queryParams.categoryId = searchParams.categoryId;
        }
        if (searchParams.searchText) {
          queryParams.searchText = searchParams.searchText;
        }
        if (searchParams.pricingType) {
          queryParams.pricingType = searchParams.pricingType;
        }

        // 调用后端分页接口
        const result = await ProductApi.findPage(queryParams);
        console.log('商品列表API响应:', result);

        if (result && result.rows) {
          products.value = result.rows;
          pagination.total = result.totalRows;
          pagination.current = result.pageNo;
          pagination.pageSize = result.pageSize;

          console.log('商品列表加载成功，总数:', result.totalRows, '当前页:', result.rows.length);
        } else {
          console.warn('商品数据格式错误:', result);
          products.value = [];
          pagination.total = 0;
        }
      } catch (error) {
        console.error('加载商品列表失败:', error);
        message.error('加载商品列表失败：' + (error.message || '未知错误'));
      } finally {
        loading.value = false;
      }
    };

    // 分页变化处理
    const handlePageChange = (page, pageSize) => {
      pagination.current = page;
      pagination.pageSize = pageSize;
      loadProducts();
    };

    // 每页条数变化处理
    const handlePageSizeChange = (current, size) => {
      pagination.current = 1; // 重置到第一页
      pagination.pageSize = size;
      loadProducts();
    };

    // 搜索处理
    const handleSearch = () => {
      pagination.current = 1; // 重置到第一页
      loadProducts();
    };

    // 重置搜索
    const handleReset = () => {
      searchParams.searchText = '';
      searchParams.pricingType = undefined;
      // 不重置categoryId，保持分类筛选
      pagination.current = 1;
      loadProducts();
    };

    // 获取计价类型名称
    const getPricingTypeName = (pricingType) => {
      const typeMap = {
        'NORMAL': '普通',
        'WEIGHT': '称重',
        'PIECE': '计件',
        'VARIABLE': '变价'
      };
      return typeMap[pricingType] || pricingType;
    };

    // 获取计价类型颜色
    const getPricingTypeColor = (pricingType) => {
      const colorMap = {
        'NORMAL': 'blue',
        'WEIGHT': 'orange',
        'PIECE': 'green',
        'VARIABLE': 'purple'
      };
      return colorMap[pricingType] || 'default';
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 获取库存样式类
    const getStockClass = (stockQuantity) => {
      const stock = parseFloat(stockQuantity) || 0;
      if (stock <= 0) return 'stock-danger';
      if (stock <= 10) return 'stock-warning';
      return 'stock-normal';
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 确认选择
    const handleConfirm = () => {
      if (selectedProducts.value.length === 0) {
        message.warning('请选择商品');
        return;
      }
      emit('select', selectedProducts.value);
    };

    return {
      loading,
      categoryLoading,
      products,
      categories,
      categoryTreeData,
      selectedProducts,
      selectedCategoryKeys,
      searchParams,
      pagination,
      columns,
      rowSelection,
      loadProducts,
      handlePageChange,
      handlePageSizeChange,
      handleCategorySelect,
      handleSearch,
      handleReset,
      getPricingTypeName,
      getPricingTypeColor,
      formatAmount,
      getStockClass,
      handleCancel,
      handleConfirm
    };
  }
};
</script>

<style scoped>
.product-selector-content {
  height: 600px;
}

/* 分类树容器样式 */
.category-tree-container {
  height: 100%;
  border-right: 1px solid #f0f0f0;
  padding-right: 16px;
}

.category-tree-header {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.category-tree-header .tree-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.category-tree-content {
  height: calc(100% - 50px);
  overflow-y: auto;
}

.category-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.product-count {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 4px;
}

/* 分类树样式优化 */
.product-category-tree :deep(.ant-tree-treenode) {
  padding: 4px 0;
}

.product-category-tree :deep(.ant-tree-node-content-wrapper) {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.product-category-tree :deep(.ant-tree-node-content-wrapper:hover) {
  background-color: #f5f5f5;
}

.product-category-tree :deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: #e6f7ff;
  color: #1890ff;
}

.product-category-tree :deep(.ant-tree-iconEle) {
  margin-right: 8px;
  color: #8c8c8c;
}

.product-category-tree :deep(.ant-tree-switcher:hover) {
  color: #1890ff;
}

/* 商品内容区域样式 */
.product-content-container {
  height: 600px; /* 明确设置高度 */
  display: flex;
  flex-direction: column;
}

.search-area {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  flex-shrink: 0;
}

.product-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.product-list :deep(.ant-table-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.product-list :deep(.ant-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-list :deep(.ant-table-container) {
  flex: 1;
  overflow: auto;
  min-height: 300px; /* 设置最小高度确保有足够空间显示数据 */
}

.product-list :deep(.ant-table-tbody) {
  overflow-y: auto;
}

/* 分页容器样式 */
.pagination-container {
  flex-shrink: 0;
  margin-top: 16px;
  padding: 12px 16px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-details {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.product-code {
  color: #1890ff;
}

.product-spec {
  color: #52c41a;
}

.price-text {
  font-weight: 500;
  color: #1890ff;
}

.stock-normal {
  color: #52c41a;
}

.stock-warning {
  color: #faad14;
}

.stock-danger {
  color: #ff4d4f;
}

.amount-cell {
  text-align: right;
  font-weight: 500;
}

/* 分类树样式优化 */
:deep(.ant-tree) {
  background: transparent;
}

:deep(.ant-tree .ant-tree-node-content-wrapper) {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

:deep(.ant-tree .ant-tree-node-content-wrapper:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: #e6f7ff;
  color: #1890ff;
}

:deep(.ant-tree .ant-tree-title) {
  font-size: 13px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .category-tree-container {
    display: none;
  }

  .product-content-container {
    span: 24;
  }
}
</style>
