package cn.stylefeng.roses.kernel.websocket.memory.checker;

import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 检测客户端的长连接是否都有效，无效则移除
 *
 * <AUTHOR>
 * @since 2024-01-15 10:25
 */
@Slf4j
public class ConnectionTimeOutChecker extends Thread {

    @Override
    public void run() {
        try {
            while (true) {
                // 20秒检查一次
                sleep(20000);

                // 检查连接情况
                checkConnection();
            }
        } catch (Exception ex) {
        }
    }

    /**
     * 检查连接情况
     *
     * <AUTHOR>
     * @since 2024-01-15 10:26
     */
    protected void checkConnection() {

        WebSocketManagerApi webSocketManagerApi = SpringUtil.getBean(WebSocketManagerApi.class);

        Map<String, WebSocketDTO> identityList = webSocketManagerApi.localWebSocketMap();

        // 遍历所有长连接，超过30秒没有心跳的，移除
        identityList.forEach((identify, webSocketDTO) -> {
            if (System.currentTimeMillis() - webSocketDTO.getLastHeart().getTime() > 30000) {
                webSocketManagerApi.remove(identify);
                log.info("remove websocket by time out:" + identify);
            }
        });
    }

}