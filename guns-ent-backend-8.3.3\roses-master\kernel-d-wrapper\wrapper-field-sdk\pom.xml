<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-wrapper</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>wrapper-field-sdk</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--wrapper模块的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>wrapper-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--字典模块的api-->
        <!--新增加了一个字典编码的转化-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>dict-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--jackson相关基础依赖-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

    </dependencies>

</project>
