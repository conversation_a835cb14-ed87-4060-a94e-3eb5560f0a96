package cn.stylefeng.roses.kernel.erp.modular.pos.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * POS支付记录Mapper接口
 *
 * <AUTHOR>
 * @since 2025/08/01 11:00
 */
public interface PosPaymentMapper extends BaseMapper<PosPayment> {

    /**
     * 根据订单ID列表查询支付记录
     *
     * @param orderIds 订单ID列表
     * @param paymentStatus 支付状态（可选）
     * @return 支付记录列表
     */
    List<PosPayment> findByOrderIds(@Param("orderIds") List<Long> orderIds,
                                   @Param("paymentStatus") String paymentStatus);

    /**
     * 统计指定时间范围内的支付数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @param paymentStatus 支付状态（可选）
     * @return 支付统计数据
     */
    Map<String, Object> getPaymentStatsByDateRange(@Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  @Param("paymentMethod") String paymentMethod,
                                                  @Param("paymentStatus") String paymentStatus);

    /**
     * 查询支付方式统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentStatus 支付状态（可选）
     * @return 支付方式统计数据
     */
    List<Map<String, Object>> getPaymentMethodStatistics(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime,
                                                        @Param("paymentStatus") String paymentStatus);

    /**
     * 查询每日支付趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 每日支付数据
     */
    List<Map<String, Object>> getDailyPaymentTrend(@Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  @Param("paymentMethod") String paymentMethod);

    /**
     * 查询每小时支付趋势
     *
     * @param date 日期
     * @param paymentMethod 支付方式（可选）
     * @return 每小时支付数据
     */
    List<Map<String, Object>> getHourlyPaymentTrend(@Param("date") LocalDateTime date,
                                                   @Param("paymentMethod") String paymentMethod);

    /**
     * 查询失败支付记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 失败支付记录
     */
    List<PosPayment> getFailedPayments(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      @Param("paymentMethod") String paymentMethod);

    /**
     * 查询退款记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 退款记录
     */
    List<PosPayment> getRefundPayments(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      @Param("paymentMethod") String paymentMethod);

    /**
     * 统计订单的支付金额
     *
     * @param orderId 订单ID
     * @param paymentStatus 支付状态
     * @return 支付金额
     */
    BigDecimal sumPaymentAmountByOrder(@Param("orderId") Long orderId,
                                      @Param("paymentStatus") String paymentStatus);

    /**
     * 查询待确认的支付记录
     *
     * @param paymentMethod 支付方式（可选）
     * @param timeoutMinutes 超时分钟数
     * @return 待确认支付记录
     */
    List<PosPayment> getPendingPayments(@Param("paymentMethod") String paymentMethod,
                                       @Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 批量更新支付状态
     *
     * @param paymentIds 支付记录ID列表
     * @param paymentStatus 新的支付状态
     * @param updateTime 更新时间
     * @return 更新数量
     */
    int batchUpdatePaymentStatus(@Param("paymentIds") List<Long> paymentIds,
                                @Param("paymentStatus") String paymentStatus,
                                @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询收银员的支付统计
     *
     * @param cashierIds 收银员ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 收银员支付统计
     */
    List<Map<String, Object>> getCashierPaymentStats(@Param("cashierIds") List<Long> cashierIds,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime,
                                                    @Param("paymentMethod") String paymentMethod);

    /**
     * 查询大额支付记录
     *
     * @param minAmount 最小金额
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 大额支付记录
     */
    List<PosPayment> getLargeAmountPayments(@Param("minAmount") BigDecimal minAmount,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("paymentMethod") String paymentMethod);

    /**
     * 查询异常支付记录（如多次失败、异常金额等）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常支付记录
     */
    List<Map<String, Object>> getAbnormalPayments(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 统计支付成功率
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 支付成功率统计
     */
    Map<String, Object> getPaymentSuccessRate(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("paymentMethod") String paymentMethod);

    /**
     * 查询平均支付金额
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param paymentMethod 支付方式（可选）
     * @return 平均支付金额
     */
    BigDecimal getAveragePaymentAmount(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      @Param("paymentMethod") String paymentMethod);

}