package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 产品分类响应参数
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpProductCategoryResponse extends BaseResponse {

    /**
     * 分类ID
     */
    @ChineseDescription("分类ID")
    private Long categoryId;

    /**
     * 分类编码
     */
    @ChineseDescription("分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @ChineseDescription("分类名称")
    private String categoryName;

    /**
     * 父级分类ID
     */
    @ChineseDescription("父级分类ID")
    private Long parentId;

    /**
     * 父级分类名称
     */
    @ChineseDescription("父级分类名称")
    private String parentName;

    /**
     * 分类层级（1-一级分类，2-二级分类，3-三级分类，4-四级分类，5-五级分类）
     */
    @ChineseDescription("分类层级")
    private Integer categoryLevel;

    /**
     * 分类层级名称
     */
    @ChineseDescription("分类层级名称")
    private String categoryLevelName;

    /**
     * 分类路径（用/分隔，如：1/2/3）
     */
    @ChineseDescription("分类路径")
    private String categoryPath;

    /**
     * 排序号
     */
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 状态名称
     */
    @ChineseDescription("状态名称")
    private String statusName;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 子分类列表（树形结构用）
     */
    @ChineseDescription("子分类列表")
    private List<ErpProductCategoryResponse> children;

    /**
     * 是否为叶子节点（用于树形结构）
     */
    @ChineseDescription("是否为叶子节点")
    private Boolean isLeaf;

    /**
     * 是否有子节点（用于懒加载判断）
     */
    @ChineseDescription("是否有子节点")
    private Boolean hasChildren;

    /**
     * 节点图标（用于前端显示）
     */
    @ChineseDescription("节点图标")
    private String icon;

    /**
     * 节点标题（用于树形显示）
     */
    @ChineseDescription("节点标题")
    private String title;

    /**
     * 节点键值（用于树形选择）
     */
    @ChineseDescription("节点键值")
    private String key;

    /**
     * 节点值（用于树形选择）
     */
    @ChineseDescription("节点值")
    private String value;
    
    /**
     * 关联商品数量
     */
    @ChineseDescription("关联商品数量")
    private Integer productCount;
}
