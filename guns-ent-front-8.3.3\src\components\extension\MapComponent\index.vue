<template>
  <div class="wh100">
    <a-input
      v-model:value="dataValue"
      :disabled="readonly || disabled"
      allowClear
      class="w-full"
      @focus="dataValueClick"
      :placeholder="placeholder"
    />

    <!-- 位置选择弹框 -->
    <MapPicker v-model:visible="showMap" v-if="showMap" @done="closeMap" :need-city="true" />
  </div>
</template>

<script setup name="MapComponent">
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  //是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: {}
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:value', 'onChange']);

// 选中的值
const dataValue = ref('');

// 是否显示选择地址弹框
const showMap = ref(false);

onMounted(() => {
  dataValue.value = props.value;
});

// 关闭地图选中
const closeMap = data => {
  const { name } = data;
  dataValue.value = name;
  dataValueChange();
};

watch(
  () => props.value,
  val => {
    dataValue.value = val;
  },
  { deep: true }
);

// 更改值
const dataValueChange = () => {
  emits('update:value', dataValue.value);
  emits('onChange', props.record);
};
// 点击
const dataValueClick = () => {
  showMap.value = true;
};
</script>

<style></style>
