package cn.stylefeng.roses.ent.mobile.manage.service.impl;

import cn.stylefeng.roses.ent.mobile.manage.pojo.index.MobilePersonInfoRequest;
import cn.stylefeng.roses.ent.mobile.manage.pojo.index.MobileUserIndexInfo;
import cn.stylefeng.roses.ent.mobile.manage.service.MobileIndexService;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.file.api.FileInfoApi;
import cn.stylefeng.roses.kernel.sys.modular.login.pojo.IndexUserOrgInfo;
import cn.stylefeng.roses.kernel.sys.modular.login.service.UserIndexInfoService;
import cn.stylefeng.roses.kernel.sys.modular.message.entity.SysMessage;
import cn.stylefeng.roses.kernel.sys.modular.message.pojo.request.SysMessageRequest;
import cn.stylefeng.roses.kernel.sys.modular.message.service.SysMessageService;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.pojo.request.SysUserRequest;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 移动端首页接口
 *
 * <AUTHOR>
 * @since 2024/3/20 23:12
 */
@Service
public class MobileIndexServiceImpl implements MobileIndexService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private FileInfoApi fileInfoApi;

    @Resource
    private UserIndexInfoService userIndexInfoService;

    @Resource
    private SysMessageService sysMessageService;

    @Override
    public MobileUserIndexInfo getUserIndexInfo() {

        // 初始化返回结果
        MobileUserIndexInfo mobileUserIndexInfo = new MobileUserIndexInfo();

        // 获取当前登录用户
        LoginUser loginUser = LoginContext.me().getLoginUser();

        // 1. 获取用户姓名和头像
        this.fillUserBaseInfo(loginUser.getUserId(), mobileUserIndexInfo);

        // 2. 设置用户的任职信息
        List<IndexUserOrgInfo> indexUserOrgInfos = userIndexInfoService.fillUserOrgInfo(loginUser);
        mobileUserIndexInfo.setUserOrgInfoList(indexUserOrgInfos);

        // 3. 获取系统消息
        List<SysMessage> list = sysMessageService.findList(new SysMessageRequest());
        mobileUserIndexInfo.setSysMessageList(list);

        return mobileUserIndexInfo;
    }

    @Override
    public void updateUserInfo(MobilePersonInfoRequest mobilePersonInfoRequest) {
        SysUserRequest sysUserRequest = new SysUserRequest();
        sysUserRequest.setAvatar(mobilePersonInfoRequest.getAvatar());
        sysUserRequest.setRealName(mobilePersonInfoRequest.getRealName());
        sysUserRequest.setSex(mobilePersonInfoRequest.getSex());
        sysUserRequest.setEmail(mobilePersonInfoRequest.getEmail());
        sysUserService.editInfo(sysUserRequest);
    }

    /**
     * 填充用户的基本姓名和头像信息
     *
     * <AUTHOR>
     * @since 2023/6/18 23:01
     */
    private void fillUserBaseInfo(Long userId, MobileUserIndexInfo mobileUserIndexInfo) {
        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserLambdaQueryWrapper.eq(SysUser::getUserId, userId);
        sysUserLambdaQueryWrapper.select(SysUser::getRealName, SysUser::getAvatar,
                SysUser::getEmployeeNumber, SysUser::getPhone, SysUser::getSex, SysUser::getEmail, SysUser::getAccount, SysUser::getBirthday);
        SysUser sysUser = this.sysUserService.getOne(sysUserLambdaQueryWrapper);
        if (sysUser == null) {
            return;
        }

        mobileUserIndexInfo.setUserId(sysUser.getUserId());
        mobileUserIndexInfo.setRealName(sysUser.getRealName());
        mobileUserIndexInfo.setEmployeeNumber(sysUser.getEmployeeNumber());
        mobileUserIndexInfo.setPhone(sysUser.getPhone());
        mobileUserIndexInfo.setSex(sysUser.getSex());
        mobileUserIndexInfo.setEmail(sysUser.getEmail());
        mobileUserIndexInfo.setAvatar(sysUser.getAvatar());

        // 新增账号和生日返回
        mobileUserIndexInfo.setAccount(sysUser.getAccount());
        mobileUserIndexInfo.setBirthday(sysUser.getBirthday());

        // 获取头像文件id信息，转化为头像URL
        Long avatarFileId = sysUser.getAvatar();
        if (avatarFileId != null) {
            // 获取头像的访问地址
            String fileAuthUrl = null;
            try {
                fileAuthUrl = fileInfoApi.getFileAuthUrl(avatarFileId);
            } catch (Exception e) {
                // 文件不存在
            }
            mobileUserIndexInfo.setAvatarUrl(fileAuthUrl);
        }
    }

}
