package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * POS支付记录实体类
 *
 * <AUTHOR>
 * @since 2025/08/01 10:20
 */
@TableName(value = "pos_payment", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PosPayment extends BaseEntity {

    /**
     * 支付ID
     */
    @TableId(value = "payment_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("支付ID")
    private Long paymentId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    @ChineseDescription("订单ID")
    private Long orderId;

    /**
     * 支付单号
     */
    @TableField("payment_no")
    @ChineseDescription("支付单号")
    private String paymentNo;

    /**
     * 支付方式（CASH-现金，WECHAT-微信，ALIPAY-支付宝，MEMBER-会员卡，CARD-银行卡）
     */
    @TableField("payment_method")
    @ChineseDescription("支付方式")
    private String paymentMethod;

    /**
     * 支付金额
     */
    @TableField("payment_amount")
    @ChineseDescription("支付金额")
    private BigDecimal paymentAmount;

    /**
     * 实收金额（现金支付时使用）
     */
    @TableField("received_amount")
    @ChineseDescription("实收金额")
    private BigDecimal receivedAmount;

    /**
     * 找零金额（现金支付时使用）
     */
    @TableField("change_amount")
    @ChineseDescription("找零金额")
    private BigDecimal changeAmount;

    /**
     * 支付状态（PENDING-待支付，SUCCESS-成功，FAILED-失败，CANCELLED-已取消）
     */
    @TableField("payment_status")
    @ChineseDescription("支付状态")
    private String paymentStatus;

    /**
     * 第三方交易号
     */
    @TableField("transaction_id")
    @ChineseDescription("第三方交易号")
    private String transactionId;

    /**
     * 支付完成时间
     */
    @TableField("payment_time")
    @ChineseDescription("支付完成时间")
    private LocalDateTime paymentTime;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;

}