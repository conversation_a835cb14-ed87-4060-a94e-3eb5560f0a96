package cn.stylefeng.roses.kernel.erp.modular.pos.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PosPaymentRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PosPaymentResponse;
import cn.stylefeng.roses.kernel.erp.modular.pos.service.PosPaymentService;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * POS支付控制器
 * 
 * 提供POS系统的支付处理功能，包括多种支付方式、支付状态管理等
 *
 * <AUTHOR>
 * @since 2025/08/01 17:00
 */
@RestController
@ApiResource(name = "POS支付控制器", requiredPermission = true, requirePermissionCode = "ERP_POS_PAYMENT")
public class PosPaymentController {

    @Resource
    private PosPaymentService posPaymentService;

    /**
     * 处理现金支付
     * 
     * 处理现金支付，包括找零计算
     */
    @PostResource(name = "处理现金支付", path = "/erp/pos/payment/cash")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_CASH")
    public ResponseData<PosPaymentResponse> processCashPayment(@RequestBody @Validated(PosPaymentRequest.processPayment.class) PosPaymentRequest request) {
        // 处理现金支付
        Long paymentId = posPaymentService.processCashPayment(
            request.getOrderId(),
            request.getPaymentAmount(),
            request.getReceivedAmount()
        );
        
        // 查询支付记录
        PosPayment payment = posPaymentService.getPaymentById(paymentId);
        PosPaymentResponse response = convertToPosPaymentResponse(payment);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 处理扫码支付
     * 
     * 处理微信、支付宝等扫码支付
     */
    @PostResource(name = "处理扫码支付", path = "/erp/pos/payment/qrcode")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_QRCODE")
    public ResponseData<PosPaymentResponse> processQrCodePayment(@RequestBody @Validated(PosPaymentRequest.processPayment.class) PosPaymentRequest request) {
        // 处理扫码支付
        Long paymentId = posPaymentService.processQrCodePayment(
            request.getOrderId(),
            request.getPaymentAmount(),
            request.getPaymentMethod()
        );
        
        // 查询支付记录
        PosPayment payment = posPaymentService.getPaymentById(paymentId);
        PosPaymentResponse response = convertToPosPaymentResponse(payment);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 处理会员卡支付
     * 
     * 使用会员卡余额进行支付
     */
    @PostResource(name = "处理会员卡支付", path = "/erp/pos/payment/member")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_MEMBER")
    public ResponseData<PosPaymentResponse> processMemberCardPayment(@RequestBody @Validated(PosPaymentRequest.processPayment.class) PosPaymentRequest request) {
        if (request.getMemberId() == null) {
            throw new IllegalArgumentException("会员ID不能为空");
        }
        
        // 处理会员卡支付
        Long paymentId = posPaymentService.processMemberCardPayment(
            request.getOrderId(),
            request.getPaymentAmount(),
            request.getMemberId()
        );
        
        // 查询支付记录
        PosPayment payment = posPaymentService.getPaymentById(paymentId);
        PosPaymentResponse response = convertToPosPaymentResponse(payment);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 处理银行卡支付
     * 
     * 处理银行卡刷卡支付
     */
    @PostResource(name = "处理银行卡支付", path = "/erp/pos/payment/bankcard")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_BANKCARD")
    public ResponseData<PosPaymentResponse> processBankCardPayment(@RequestBody @Validated(PosPaymentRequest.processPayment.class) PosPaymentRequest request) {
        if (request.getCardNo() == null || request.getCardNo().trim().isEmpty()) {
            throw new IllegalArgumentException("银行卡号不能为空");
        }
        
        // 处理银行卡支付
        Long paymentId = posPaymentService.processBankCardPayment(
            request.getOrderId(),
            request.getPaymentAmount(),
            request.getCardNo()
        );
        
        // 查询支付记录
        PosPayment payment = posPaymentService.getPaymentById(paymentId);
        PosPaymentResponse response = convertToPosPaymentResponse(payment);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 处理混合支付
     * 
     * 支持多种支付方式组合支付
     */
    @PostResource(name = "处理混合支付", path = "/erp/pos/payment/mixed")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_MIXED")
    public ResponseData<List<PosPaymentResponse>> processMixedPayment(@RequestBody PosPaymentRequest request) {
        if (request.getPaymentDetails() == null || request.getPaymentDetails().isEmpty()) {
            throw new IllegalArgumentException("支付详情不能为空");
        }
        
        // 转换支付详情
        List<PosPaymentService.PaymentDetail> paymentDetails = request.getPaymentDetails().stream()
                .map(detail -> {
                    PosPaymentService.PaymentDetail paymentDetail = new PosPaymentService.PaymentDetail(
                        detail.getPaymentMethod(), 
                        detail.getPaymentAmount()
                    );
                    paymentDetail.setReceivedAmount(detail.getReceivedAmount());
                    paymentDetail.setMemberId(detail.getMemberId());
                    paymentDetail.setCardNo(detail.getCardNo());
                    return paymentDetail;
                })
                .collect(Collectors.toList());
        
        // 处理混合支付
        List<Long> paymentIds = posPaymentService.processMixedPayment(request.getOrderId(), paymentDetails);
        
        // 查询支付记录
        List<PosPaymentResponse> responses = paymentIds.stream()
                .map(paymentId -> {
                    PosPayment payment = posPaymentService.getPaymentById(paymentId);
                    return convertToPosPaymentResponse(payment);
                })
                .collect(Collectors.toList());
        
        return new SuccessResponseData<>(responses);
    }

    /**
     * 确认支付成功
     * 
     * 第三方支付回调确认支付成功
     */
    @PostResource(name = "确认支付成功", path = "/erp/pos/payment/confirm")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_CONFIRM")
    public ResponseData<?> confirmPaymentSuccess(@RequestBody @Validated(PosPaymentRequest.confirmPayment.class) PosPaymentRequest request) {
        if (request.getPaymentId() == null) {
            throw new IllegalArgumentException("支付ID不能为空");
        }
        
        posPaymentService.confirmPaymentSuccess(request.getPaymentId(), request.getTransactionId());
        return new SuccessResponseData<>();
    }

    /**
     * 支付失败处理
     * 
     * 处理支付失败的情况
     */
    @PostResource(name = "支付失败处理", path = "/erp/pos/payment/failure")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_FAILURE")
    public ResponseData<?> handlePaymentFailure(@RequestBody @Validated(PosPaymentRequest.paymentFailure.class) PosPaymentRequest request) {
        if (request.getPaymentId() == null) {
            throw new IllegalArgumentException("支付ID不能为空");
        }
        
        posPaymentService.handlePaymentFailure(request.getPaymentId(), request.getFailureReason());
        return new SuccessResponseData<>();
    }

    /**
     * 取消支付
     * 
     * 取消正在进行的支付
     */
    @PostResource(name = "取消支付", path = "/erp/pos/payment/cancel")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_CANCEL")
    public ResponseData<?> cancelPayment(@RequestBody @Validated(PosPaymentRequest.cancelPayment.class) PosPaymentRequest request) {
        if (request.getPaymentId() == null) {
            throw new IllegalArgumentException("支付ID不能为空");
        }
        
        posPaymentService.cancelPayment(request.getPaymentId(), request.getCancelReason());
        return new SuccessResponseData<>();
    }

    /**
     * 退款处理
     * 
     * 处理支付退款
     */
    @PostResource(name = "退款处理", path = "/erp/pos/payment/refund")
    @BizLog(logTypeCode = "ERP_POS_PAYMENT_REFUND")
    public ResponseData<Long> processRefund(@RequestBody @Validated(PosPaymentRequest.processRefund.class) PosPaymentRequest request) {
        if (request.getPaymentId() == null) {
            throw new IllegalArgumentException("支付ID不能为空");
        }
        if (request.getRefundAmount() == null) {
            throw new IllegalArgumentException("退款金额不能为空");
        }
        
        Long refundId = posPaymentService.processRefund(
            request.getPaymentId(),
            request.getRefundAmount(),
            request.getRefundReason()
        );
        
        return new SuccessResponseData<>(refundId);
    }

    /**
     * 计算找零金额
     * 
     * 计算现金支付的找零金额
     */
    @GetResource(name = "计算找零金额", path = "/erp/pos/payment/calculateChange")
    public ResponseData<BigDecimal> calculateChange(PosPaymentRequest request) {
        if (request.getPaymentAmount() == null || request.getReceivedAmount() == null) {
            throw new IllegalArgumentException("支付金额和实收金额不能为空");
        }
        
        BigDecimal changeAmount = posPaymentService.calculateChange(
            request.getPaymentAmount(),
            request.getReceivedAmount()
        );
        
        return new SuccessResponseData<>(changeAmount);
    }

    /**
     * 根据订单ID查询支付记录
     * 
     * 获取订单的所有支付记录
     */
    @GetResource(name = "根据订单查询支付记录", path = "/erp/pos/payment/listByOrder")
    public ResponseData<List<PosPaymentResponse>> getPaymentsByOrderId(PosPaymentRequest request) {
        if (request.getOrderId() == null) {
            throw new IllegalArgumentException("订单ID不能为空");
        }
        
        List<PosPayment> payments = posPaymentService.getPaymentsByOrderId(request.getOrderId());
        
        List<PosPaymentResponse> responses = payments.stream()
                .map(this::convertToPosPaymentResponse)
                .collect(Collectors.toList());
        
        return new SuccessResponseData<>(responses);
    }

    /**
     * 根据支付ID查询支付详情
     * 
     * 获取单个支付记录的详细信息
     */
    @GetResource(name = "查询支付详情", path = "/erp/pos/payment/detail")
    public ResponseData<PosPaymentResponse> getPaymentDetail(PosPaymentRequest request) {
        if (request.getPaymentId() == null) {
            throw new IllegalArgumentException("支付ID不能为空");
        }
        
        PosPayment payment = posPaymentService.getPaymentById(request.getPaymentId());
        if (payment == null) {
            throw new IllegalArgumentException("支付记录不存在");
        }
        
        PosPaymentResponse response = convertToPosPaymentResponse(payment);
        return new SuccessResponseData<>(response);
    }

    /**
     * 根据支付单号查询支付记录
     * 
     * 通过支付单号获取支付记录
     */
    @GetResource(name = "根据支付单号查询支付记录", path = "/erp/pos/payment/detailByNo")
    public ResponseData<PosPaymentResponse> getPaymentDetailByNo(PosPaymentRequest request) {
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new IllegalArgumentException("支付单号不能为空");
        }
        
        PosPayment payment = posPaymentService.getPaymentByNo(request.getPaymentNo());
        if (payment == null) {
            throw new IllegalArgumentException("支付记录不存在");
        }
        
        PosPaymentResponse response = convertToPosPaymentResponse(payment);
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询支付记录
     * 
     * 支持按支付方式、支付状态等条件查询
     */
    @GetResource(name = "分页查询支付记录", path = "/erp/pos/payment/page")
    public ResponseData<PageResult<PosPaymentResponse>> getPaymentPage(PosPaymentRequest request) {
        // 设置默认分页参数
        if (request.getPageNo() == null) {
            request.setPageNo(1);
        }
        if (request.getPageSize() == null) {
            request.setPageSize(20);
        }
        
        // 查询支付分页数据
        PageResult<PosPayment> pageResult = posPaymentService.findPaymentPage(
            request.getPageNo(),
            request.getPageSize(),
            request.getPaymentMethod(),
            request.getPaymentStatus(),
            request.getOrderId()
        );
        
        // 转换为响应格式
        List<PosPaymentResponse> responseList = pageResult.getRows().stream()
                .map(this::convertToPosPaymentResponse)
                .collect(Collectors.toList());
        
        PageResult<PosPaymentResponse> response = new PageResult<>();
        response.setRows(responseList);
        response.setTotalRows(pageResult.getTotalRows());
        response.setTotalPage(pageResult.getTotalPage());
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 校验支付金额
     * 
     * 校验支付金额是否正确
     */
    @GetResource(name = "校验支付金额", path = "/erp/pos/payment/validateAmount")
    public ResponseData<Boolean> validatePaymentAmount(PosPaymentRequest request) {
        if (request.getOrderId() == null || request.getPaymentAmount() == null) {
            throw new IllegalArgumentException("订单ID和支付金额不能为空");
        }
        
        boolean isValid = posPaymentService.validatePaymentAmount(request.getOrderId(), request.getPaymentAmount());
        return new SuccessResponseData<>(isValid);
    }

    /**
     * 校验会员余额
     * 
     * 校验会员余额是否充足
     */
    @GetResource(name = "校验会员余额", path = "/erp/pos/payment/validateMemberBalance")
    public ResponseData<Boolean> validateMemberBalance(PosPaymentRequest request) {
        if (request.getMemberId() == null || request.getPaymentAmount() == null) {
            throw new IllegalArgumentException("会员ID和支付金额不能为空");
        }
        
        boolean isValid = posPaymentService.validateMemberBalance(request.getMemberId(), request.getPaymentAmount());
        return new SuccessResponseData<>(isValid);
    }

    /**
     * 获取订单已支付金额
     * 
     * 获取订单的已支付总金额
     */
    @GetResource(name = "获取订单已支付金额", path = "/erp/pos/payment/paidAmount")
    public ResponseData<BigDecimal> getPaidAmountByOrderId(PosPaymentRequest request) {
        if (request.getOrderId() == null) {
            throw new IllegalArgumentException("订单ID不能为空");
        }
        
        BigDecimal paidAmount = posPaymentService.getPaidAmountByOrderId(request.getOrderId());
        return new SuccessResponseData<>(paidAmount);
    }

    /**
     * 获取订单待支付金额
     * 
     * 获取订单的待支付金额
     */
    @GetResource(name = "获取订单待支付金额", path = "/erp/pos/payment/unpaidAmount")
    public ResponseData<BigDecimal> getUnpaidAmountByOrderId(PosPaymentRequest request) {
        if (request.getOrderId() == null) {
            throw new IllegalArgumentException("订单ID不能为空");
        }
        
        BigDecimal unpaidAmount = posPaymentService.getUnpaidAmountByOrderId(request.getOrderId());
        return new SuccessResponseData<>(unpaidAmount);
    }

    // ==================== 私有转换方法 ====================

    /**
     * 转换支付实体为响应对象
     */
    private PosPaymentResponse convertToPosPaymentResponse(PosPayment payment) {
        PosPaymentResponse response = new PosPaymentResponse();
        BeanUtils.copyProperties(payment, response);
        
        // 设置支付方式名称
        response.setPaymentMethodName(getPaymentMethodName(payment.getPaymentMethod()));
        
        // 设置支付状态名称
        response.setPaymentStatusName(getPaymentStatusName(payment.getPaymentStatus()));
        
        return response;
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentMethodName(String paymentMethod) {
        if (paymentMethod == null) return "";
        switch (paymentMethod) {
            case "CASH": return "现金";
            case "WECHAT": return "微信支付";
            case "ALIPAY": return "支付宝";
            case "MEMBER": return "会员卡";
            case "CARD": return "银行卡";
            default: return paymentMethod;
        }
    }

    /**
     * 获取支付状态名称
     */
    private String getPaymentStatusName(String paymentStatus) {
        if (paymentStatus == null) return "";
        switch (paymentStatus) {
            case "PENDING": return "待支付";
            case "SUCCESS": return "支付成功";
            case "FAILED": return "支付失败";
            case "CANCELLED": return "已取消";
            case "REFUNDED": return "已退款";
            default: return paymentStatus;
        }
    }

}