import{_ as z,s as B,r as _,L as D,o as P,a as g,f as p,w as a,d as e,g as i,c as V,F as A,e as R,b as N,ah as $,t as y,h as j,l as E,u as G,v as H,y as J,z as M,A as W,W as K,J as Q,$ as X,G as Y,H as Z}from"./index-18a1ea24.js";import{_ as h}from"./index-3a0e5c06.js";/* empty css              */import{O as ee}from"./OrgApi-021dd6dd.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */const le={__name:"org-form",props:{form:Object,isUpdate:Boolean,levelNumberList:Array},setup(o){const u=o,x=B({orgName:[{required:!0,message:"\u8BF7\u8F93\u5165\u673A\u6784\u540D\u79F0",type:"string",trigger:"blur"}],orgCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u673A\u6784\u7F16\u7801",type:"string",trigger:"blur"}],orgSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u6392\u5E8F",type:"number",trigger:"blur"}],orgType:[{required:!0,message:"\u8BF7\u9009\u62E9\u673A\u6784\u7C7B\u578B",type:"number",trigger:"change"}],statusFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u673A\u6784\u72B6\u6001",type:"number",trigger:"change"}]}),d=_(!1),v=_({}),c=_([]),O=D(()=>s=>{let l="",m=u.levelNumberList.find(r=>r.value==s.levelNumber);return m&&(l=m.name),l}),L=()=>{u.form.orgParentId&&u.form.parentOrgName?v.value.selectOrgList=[{bizId:u.form.orgParentId,name:u.form.parentOrgName}]:v.value.selectOrgList=[],d.value=!0},w=s=>{s.selectOrgList&&s.selectOrgList.length>0?(u.form.orgParentId=s.selectOrgList[0].bizId,u.form.parentOrgName=s.selectOrgList[0].name):(u.form.orgParentId="",u.form.parentOrgName="")};P(()=>{C()});const C=async()=>{c.value=await ee.organizationLevelList()};return(s,l)=>{const m=E,r=G,n=H,S=J,f=M,b=W,U=K,F=Q,k=X,I=Y,T=h,q=Z;return g(),p(q,{ref:"formRef",model:o.form,rules:x,layout:"vertical"},{default:a(()=>[e(I,{gutter:20},{default:a(()=>[e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u673A\u6784\u540D\u79F0:",name:"orgName"},{default:a(()=>[e(m,{value:o.form.orgName,"onUpdate:value":l[0]||(l[0]=t=>o.form.orgName=t),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u673A\u6784\u7F16\u7801:",name:"orgCode"},{default:a(()=>[e(m,{value:o.form.orgCode,"onUpdate:value":l[1]||(l[1]=t=>o.form.orgCode=t),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u4E0A\u7EA7\u673A\u6784\uFF1A",name:"parentOrgName"},{default:a(()=>[e(m,{value:o.form.parentOrgName,"onUpdate:value":l[2]||(l[2]=t=>o.form.parentOrgName=t),"allow-clear":"",placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u673A\u6784",onFocus:L},null,8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u6392\u5E8F:",name:"orgSort"},{default:a(()=>[e(S,{value:o.form.orgSort,"onUpdate:value":l[3]||(l[3]=t=>o.form.orgSort=t),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u673A\u6784\u7C7B\u578B:",name:"orgType"},{default:a(()=>[e(b,{value:o.form.orgType,"onUpdate:value":l[4]||(l[4]=t=>o.form.orgType=t)},{default:a(()=>[e(f,{value:1},{default:a(()=>l[11]||(l[11]=[i("\u516C\u53F8")])),_:1,__:[11]}),e(f,{value:2},{default:a(()=>l[12]||(l[12]=[i("\u90E8\u95E8")])),_:1,__:[12]})]),_:1},8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u5C42\u7EA7:",name:"levelCode"},{default:a(()=>[e(F,{value:o.form.levelCode,"onUpdate:value":l[5]||(l[5]=t=>o.form.levelCode=t),style:{width:"100%"},placeholder:"\u8BF7\u9009\u62E9\u673A\u6784\u5C42\u7EA7"},{default:a(()=>[(g(!0),V(A,null,R(c.value,t=>(g(),p(U,{value:t.levelCode,key:t.levelCode},{default:a(()=>[N("span",{style:$({color:t.levelColor})},y(t.levelName)+"("+y(O.value(t))+")",5)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u673A\u6784\u72B6\u6001:",name:"statusFlag"},{default:a(()=>[e(b,{value:o.form.statusFlag,"onUpdate:value":l[6]||(l[6]=t=>o.form.statusFlag=t)},{default:a(()=>[e(f,{value:1},{default:a(()=>l[13]||(l[13]=[i("\u542F\u7528")])),_:1,__:[13]}),e(f,{value:2},{default:a(()=>l[14]||(l[14]=[i("\u7981\u7528")])),_:1,__:[14]})]),_:1},8,["value"])]),_:1})]),_:1}),e(n,{span:24},{default:a(()=>l[15]||(l[15]=[N("div",{class:"card-title"},"\u5176\u4ED6\u4FE1\u606F",-1)])),_:1,__:[15]}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u673A\u6784\u7B80\u79F0:",name:"orgShortName"},{default:a(()=>[e(m,{value:o.form.orgShortName,"onUpdate:value":l[7]||(l[7]=t=>o.form.orgShortName=t),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u7B80\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(n,{xs:24,sm:24,md:12},{default:a(()=>[e(r,{label:"\u673A\u6784\u7A0E\u53F7:",name:"taxNo"},{default:a(()=>[e(m,{value:o.form.taxNo,"onUpdate:value":l[8]||(l[8]=t=>o.form.taxNo=t),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u673A\u6784\u7A0E\u53F7"},null,8,["value"])]),_:1})]),_:1}),e(n,{span:24},{default:a(()=>[e(r,{label:"\u5907\u6CE8"},{default:a(()=>[e(k,{value:o.form.remark,"onUpdate:value":l[9]||(l[9]=t=>o.form.remark=t),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:4},null,8,["value"])]),_:1})]),_:1})]),_:1}),d.value?(g(),p(T,{key:0,visible:d.value,"onUpdate:visible":l[10]||(l[10]=t=>d.value=t),title:"\u4E0A\u7EA7\u673A\u6784\u9009\u62E9",data:v.value,showTab:["dept"],onDone:w},null,8,["visible","data"])):j("",!0)]),_:1},8,["model","rules"])}}},ge=z(le,[["__scopeId","data-v-b14ce998"]]);export{ge as default};
