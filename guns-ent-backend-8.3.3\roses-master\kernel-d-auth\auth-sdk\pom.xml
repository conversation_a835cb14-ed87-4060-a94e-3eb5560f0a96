<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-auth</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>auth-sdk</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--auth本模块的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>auth-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--日志api-->
        <!--记录登录日志-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>log-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 多数据源操作的api -->
        <!-- 获取当前登录用户之前需要切数据源 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>ds-container-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--demo api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>demo-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--定时任务的api-->
        <!--用来自动清理过期的登录用户缓存-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>timer-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--安全模块的api-->
        <!--需要用到校验验证码的接口-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--system业务模块的api-->
        <!--登录和鉴权需要用到用户相关的接口-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--jwt模块的api-->
        <!--token用的jwt token-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>jwt-sdk</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--缓存的依赖-->
        <!--session manager可以用redis，可以用内存的-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-api</artifactId>
            <version>${roses.version}</version>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
