package cn.stylefeng.roses.kernel.pay.mapper;

import cn.stylefeng.roses.kernel.pay.api.entity.Order;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderRequest;
import cn.stylefeng.roses.kernel.pay.pojo.response.OrderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    List<OrderVo> customFindList(@Param("page") Page page, @Param("param")OrderRequest request);

}
