System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-dba03026.js","./CommonApi-legacy-1cfbfce8.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var a,u,n,d,t,i,o,v,s,r,c,p,g,y;return{setters:[e=>{a=e.r,u=e.o,n=e.X,d=e.a,t=e.c,i=e.d,o=e.w,v=e.f,s=e.h,r=e.l,c=e.bf,p=e.a0},null,e=>{g=e._},e=>{y=e.C},null,null,null,null,null,null,null],execute:function(){const l={class:"wh100"},m={__name:"index",props:{value:{type:String,default:""},record:{type:Object,default:{}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1}},emits:["update:value","onChange"],setup(e,{emit:p}){const m=e,f=p,b=a(!0),h=a(!1),j=a([]),x=a(""),O=a(!1),w=a({selectOrgList:[]});u((()=>{var e;null!==(e=m.record)&&void 0!==e&&e.itemMultipleChoiceFlag||m.multiple?b.value=!1:b.value=!0,C()}));const C=async()=>{if(m.value)if(b.value){if(h.value)return;const e=await y.getOrgName({orgId:m.value});e.data&&(j.value=[{id:m.value,name:e.data}])}else j.value=m.normal?j.value:JSON.parse(m.value);else j.value=[];h.value=!1,x.value=j.value.map((e=>e.name)).join("；")},_=()=>{var e,l;null!==(e=j.value)&&void 0!==e&&e.length?w.value.selectOrgList=null===(l=j.value)||void 0===l?void 0:l.map((e=>({bizId:e.id,name:e.name}))):w.value.selectOrgList=[],O.value=!0},B=e=>{var l;j.value=null===(l=e.selectOrgList)||void 0===l?void 0:l.map((e=>({id:e.bizId,name:e.name}))),x.value=e.selectOrgList.map((e=>e.name)).join("；"),h.value=!0,F()},F=()=>{let e=j.value.length>0?b.value?j.value[0].id:m.normal?j.value:JSON.stringify(j.value):"";f("update:value",e),f("onChange",m.record),L()},L=async()=>{var e;!m.normal&&null!==(e=m.formRef)&&void 0!==e&&e.validateFields&&await m.formRef.validateFields([m.record.fieldCode])};return n((()=>m.value),(e=>{C()}),{deep:!0}),(a,u)=>{const n=r,p=g,y=c;return d(),t("div",l,[i(n,{value:x.value,"onUpdate:value":u[0]||(u[0]=e=>x.value=e),disabled:m.readonly||m.disabled,class:"w-full",placeholder:e.placeholder,onFocus:_},null,8,["value","disabled","placeholder"]),i(y,null,{default:o((()=>[O.value?(d(),v(p,{key:0,visible:O.value,"onUpdate:visible":u[1]||(u[1]=e=>O.value=e),data:w.value,showTab:["dept"],changeHeight:!0,title:"请选择组织",isRadio:b.value,onDone:B},null,8,["visible","data","isRadio"])):s("",!0)])),_:1})])}}},f={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const l=a(""),u=a(!1),n=a(!1),v=a("请选择"),s=()=>{console.log(l.value)};return(e,a)=>{const r=m,c=p;return d(),t("div",f,[i(c,{title:"机构选择",bordered:!1},{default:o((()=>[i(r,{value:l.value,"onUpdate:value":a[0]||(a[0]=e=>l.value=e),disabled:u.value,readonly:n.value,onOnChange:s,placeholder:v.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])])),_:1})])}}})}}}));
