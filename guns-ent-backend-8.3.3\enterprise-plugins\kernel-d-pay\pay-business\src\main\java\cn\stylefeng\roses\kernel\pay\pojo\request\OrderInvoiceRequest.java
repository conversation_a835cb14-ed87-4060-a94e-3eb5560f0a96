package cn.stylefeng.roses.kernel.pay.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 订单开票记录封装类
 *
 * <AUTHOR>
 * @since 2024/06/21 16:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderInvoiceRequest extends BaseRequest {

    /**
     * 订单发票id
     */
    @NotNull(message = "订单发票id不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("订单发票id")
    private Long orderInvoiceId;

    /**
     * 订单id集合，直接存json形式的集合
     */
    @ChineseDescription("订单id集合，直接存json形式的集合")
    @NotEmpty(message = "订单id集合不能为空", groups = {add.class})
    private List<Long> orderIdList;

    /**
     * 发票抬头
     */
    @ChineseDescription("发票抬头")
    @NotBlank(message = "发票抬头不能为空", groups = {add.class})
    private String invoiceTitle;

    /**
     * 发票纳税人识别号
     */
    @ChineseDescription("发票纳税人识别号")
    @NotBlank(message = "发票纳税人识别号不能为空", groups = {add.class})
    private String taxpayerNo;

    /**
     * 单位地址
     */
    @ChineseDescription("单位地址")
    private String address;

    /**
     * 单位电话
     */
    @ChineseDescription("单位电话")
    private String phone;

    /**
     * 开户行
     */
    @ChineseDescription("开户行")
    private String bankName;

    /**
     * 开户行账号
     */
    @ChineseDescription("开户行账号")
    private String bankAccountNo;

    /**
     * 发送邮箱信息
     */
    @ChineseDescription("发送邮箱信息")
    @NotBlank(message = "发送邮箱信息不能为空", groups = {add.class})
    private String sendEmail;

    /**
     * 备注信息
     */
    @ChineseDescription("备注信息")
    private String description;

}