package cn.stylefeng.roses.kernel.impexp.org.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.sys.api.enums.org.OrgTypeEnum;

/**
 * 机构的类型校验
 *
 * <AUTHOR>
 * @since 2024-02-18 18:50
 */
public class OrgTypeValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(false, originValue, originValue, "机构的类型不能为空");
        }

        if (OrgTypeEnum.COMPANY.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, OrgTypeEnum.COMPANY.getCode());
        } else if (OrgTypeEnum.DEPT.getMessage().equals(originValue)) {
            return new ExcelLineParseResult(true, originValue, OrgTypeEnum.DEPT.getCode());
        } else {
            return new ExcelLineParseResult(false, originValue, originValue, "机构的类型不正确，请填写公司或部门");
        }
    }

}
