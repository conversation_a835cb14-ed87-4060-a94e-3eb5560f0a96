import{_ as R,r,L as g,X as N,a as c,c as d,b as s,d as p,t as _,h as v,I as B,a9 as E,y as w}from"./index-18a1ea24.js";/* empty css              */import{A as F}from"./formatter-5a06da9d.js";import"./constants-2fa70699.js";const I={class:"member-discount-panel"},U={key:0,class:"member-discount"},V={class:"discount-info"},j={class:"discount-text"},z={key:1,class:"points-deduction"},O={class:"deduction-header"},S={key:0,class:"deduction-details"},q={class:"deduction-input"},L={class:"deduction-amount"},T={class:"deduction-rate"},X=Object.assign({name:"MemberDiscount"},{__name:"MemberDiscount",props:{member:{type:Object,required:!0},discountRate:{type:Number,default:0},finalAmount:{type:Number,default:0},pointsExchangeRate:{type:Number,default:100}},emits:["pointsDeductionChange"],setup(u,{expose:x,emit:D}){const t=u,l=D,a=r(!1),o=r(0),i=r(0),y=g(()=>t.member&&t.member.points>0&&t.finalAmount>0),h=g(()=>{if(!t.member)return 0;const n=t.member.points||0,e=Math.floor(t.finalAmount*t.pointsExchangeRate);return Math.min(n,e)}),C=n=>F.formatCurrency(n,{showSymbol:!1}),A=n=>{n?(o.value=Math.min(h.value,1e3),f()):(o.value=0,i.value=0,l("pointsDeductionChange",{points:0,amount:0}))},f=()=>{if(!a.value||o.value<=0){i.value=0,l("pointsDeductionChange",{points:0,amount:0});return}const n=o.value/t.pointsExchangeRate;i.value=n,l("pointsDeductionChange",{points:o.value,amount:n})},b=()=>{a.value=!1,o.value=0,i.value=0};return N(()=>t.member,()=>{b()}),x({resetPointsDeduction:b}),(n,e)=>{const k=B,M=E,P=w;return c(),d("div",I,[u.discountRate>0?(c(),d("div",U,[s("div",V,[p(k,{iconClass:"icon-discount"}),s("span",j," \u4EAB\u53D7 "+_((u.discountRate*100).toFixed(1))+"% \u4F1A\u5458\u6298\u6263 ",1)])])):v("",!0),y.value?(c(),d("div",z,[s("div",O,[e[2]||(e[2]=s("span",{class:"deduction-label"},"\u79EF\u5206\u62B5\u6263",-1)),p(M,{checked:a.value,"onUpdate:checked":e[0]||(e[0]=m=>a.value=m),onChange:A,size:"small"},null,8,["checked"])]),a.value?(c(),d("div",S,[s("div",q,[p(P,{value:o.value,"onUpdate:value":e[1]||(e[1]=m=>o.value=m),min:0,max:h.value,step:100,size:"small",onChange:f,class:"points-input"},null,8,["value","max"]),e[3]||(e[3]=s("span",{class:"points-unit"},"\u79EF\u5206",-1))]),s("div",L," \u53EF\u62B5\u6263: \uFFE5"+_(C(i.value)),1),s("div",T," ("+_(u.pointsExchangeRate)+"\u79EF\u5206 = \uFFE51) ",1)])):v("",!0)])):v("",!0)])}}}),Q=R(X,[["__scopeId","data-v-d46a55e8"]]);export{Q as default};
