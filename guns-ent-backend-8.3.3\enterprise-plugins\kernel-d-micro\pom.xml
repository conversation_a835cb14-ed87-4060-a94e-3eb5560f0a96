<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>enterprise-plugins</artifactId>
        <version>8.3.3</version>
    </parent>

    <artifactId>kernel-d-micro</artifactId>
    <packaging>pom</packaging>

    <name>kernel-d-micro</name>
    <description>微服务模块</description>

    <modules>
        <module>micro-api</module>
        <module>micro-project-feign-consumer</module>
        <module>micro-project-feign-provider</module>
        <module>micro-project-gateway</module>
        <module>micro-project-monitor-server</module>
        <module>micro-project-tran-message</module>
        <module>micro-sdk-core</module>
        <module>micro-sdk-loadbalancer</module>
        <module>micro-seata-demo</module>
        <module>micro-spring-boot-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <!-- spring cloud alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2023.0.1.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--spring cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2023.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--spring boot admin-->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring.boot.admin}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-server-ui</artifactId>
                <version>${spring.boot.admin}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring.boot.admin}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
