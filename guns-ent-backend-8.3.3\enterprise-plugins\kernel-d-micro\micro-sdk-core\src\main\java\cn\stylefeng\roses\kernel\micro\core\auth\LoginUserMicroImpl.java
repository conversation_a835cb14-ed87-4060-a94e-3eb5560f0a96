package cn.stylefeng.roses.kernel.micro.core.auth;

import cn.stylefeng.roses.kernel.auth.api.LoginUserApi;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.auth.api.context.LoginUserHolder;
import cn.stylefeng.roses.kernel.auth.api.exception.AuthException;
import cn.stylefeng.roses.kernel.auth.api.loginuser.CommonLoginUserUtil;
import cn.stylefeng.roses.kernel.auth.api.loginuser.api.LoginUserRemoteApi;
import cn.stylefeng.roses.kernel.auth.api.loginuser.pojo.LoginUserRequest;
import cn.stylefeng.roses.kernel.auth.api.loginuser.pojo.SessionValidateResponse;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.sys.api.pojo.org.CompanyDeptDTO;
import cn.stylefeng.roses.kernel.sys.api.pojo.user.UserInfoDetailDTO;
import cn.stylefeng.roses.kernel.sys.api.remote.OrgInfoRemoteApi;
import cn.stylefeng.roses.kernel.sys.api.remote.UserInfoRemoteApi;

import jakarta.annotation.Resource;
import java.util.function.Consumer;

/**
 * 适用于自定义微服务项目中获取当前登录用户使用
 *
 * <AUTHOR>
 * @date 2021/9/28 17:23
 */
public class LoginUserMicroImpl implements LoginUserApi {

    @Resource
    private LoginUserRemoteApi loginUserRemoteApi;

    @Resource
    private UserInfoRemoteApi userInfoRemoteApi;

    @Resource
    private OrgInfoRemoteApi orgInfoRemoteApi;

    @Override
    public String getToken() {
        return CommonLoginUserUtil.getToken();
    }

    @Override
    public LoginUser getLoginUser() throws AuthException {

        // 先从ThreadLocal中获取
        LoginUser currentUser = LoginUserHolder.get();
        if (currentUser != null) {
            return currentUser;
        }

        // 获取用户的token
        String token = getToken();

        // 获取session中该token对应的用户
        return loginUserRemoteApi.getLoginUserByToken(new LoginUserRequest(token));
    }

    @Override
    public LoginUser getLoginUserNullable() {

        // 先从ThreadLocal中获取
        LoginUser currentUser = LoginUserHolder.get();
        if (currentUser != null) {
            return currentUser;
        }

        // 获取用户的token
        String token = null;
        try {
            token = getToken();
        } catch (Exception e) {
            return null;
        }

        // 获取session中该token对应的用户
        try {
            return loginUserRemoteApi.getLoginUserByToken(new LoginUserRequest(token));
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean getSuperAdminFlag() {
        LoginUser loginUser = getLoginUser();
        UserInfoDetailDTO userDetail = this.userInfoRemoteApi.remoteGetUserDetail(loginUser.getUserId());
        return YesOrNotEnum.Y.getCode().equals(userDetail.getSuperAdminFlag());
    }

    @Override
    public Long getCurrentUserCompanyId() {

        Long currentOrgId = LoginContext.me().getLoginUser().getCurrentOrgId();
        if (currentOrgId == null) {
            return null;
        }

        CompanyDeptDTO orgCompanyInfo = orgInfoRemoteApi.remoteGetOrgCompanyDept(currentOrgId);
        if (orgCompanyInfo == null) {
            return null;
        }

        return orgCompanyInfo.getCompanyId();
    }

    @Override
    public LoginUser switchTo(Long userId) {
        return null;
    }

    @Override
    public void endSwitch() {

    }

    @Override
    public void switchTo(Long userId, Consumer<Long> consumer) {

    }

    @Override
    public boolean hasLogin() {

        // 获取用户的token
        String token = null;
        try {
            token = getToken();
        } catch (Exception e) {
            return false;
        }

        // 获取是否在会话中有
        SessionValidateResponse sessionValidateResponse = loginUserRemoteApi.haveSession(token);
        return sessionValidateResponse.getValidateResult();
    }

}
