cn\stylefeng\roses\kernel\db\mp\dboperator\DbOperatorImpl.class
cn\stylefeng\roses\kernel\db\mp\dbid\CustomDatabaseIdProvider.class
cn\stylefeng\roses\kernel\db\mp\tenant\holder\TenantSwitchHolder.class
cn\stylefeng\roses\kernel\db\mp\datascope\aop\DataScopeAop.class
cn\stylefeng\roses\kernel\db\mp\datascope\annotations\DataScope.class
cn\stylefeng\roses\kernel\db\mp\datascope\ProjectDataScopeHandler.class
cn\stylefeng\roses\kernel\db\mp\injector\CustomInsertBatchSqlInjector.class
cn\stylefeng\roses\kernel\db\mp\fieldfill\CustomMetaObjectHandler.class
cn\stylefeng\roses\kernel\db\mp\datascope\holder\DataScopeHolder.class
cn\stylefeng\roses\kernel\db\mp\datascope\config\DataScopeConfig.class
cn\stylefeng\roses\kernel\db\mp\datascope\UserRoleDataScopeApi.class
cn\stylefeng\roses\kernel\db\mp\tenant\holder\TenantIdHolder.class
cn\stylefeng\roses\kernel\db\mp\tenant\holder\TenantRemoveThreadLocalHolder.class
cn\stylefeng\roses\kernel\db\mp\injector\CustomBaseMapper.class
cn\stylefeng\roses\kernel\db\mp\tenant\ProjectTenantInterceptor.class
