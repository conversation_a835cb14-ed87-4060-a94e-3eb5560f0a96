package cn.stylefeng.roses.kernel.manage.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * API资源接口列表异常相关枚举
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@Getter
public enum ApiEndpointExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    API_ENDPOINT_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "Endpoint查询结果不存在"),

    /**
     * endpoint资源编码不合法
     */
    API_ENDPOINT_NOT_RIGHT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "Endpoint资源编码不合法");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ApiEndpointExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
