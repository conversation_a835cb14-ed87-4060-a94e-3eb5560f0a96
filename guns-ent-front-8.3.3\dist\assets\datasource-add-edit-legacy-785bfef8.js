System.register(["./index-legacy-ee1db0c7.js","./datasource-form-legacy-06d85a2b.js"],(function(e,t){"use strict";var a,o,s,l,d,n,i,r,u,c;return{setters:[e=>{a=e.R,o=e.r,s=e.o,l=e.a,d=e.f,n=e.w,i=e.d,r=e.m,u=e.M},e=>{c=e.default}],execute:function(){class t{static findPage(e){return a.getAndLoadData("/databaseInfo/page",e)}static add(e){return a.post("/databaseInfo/add",e)}static edit(e){return a.post("/databaseInfo/edit",e)}static delete(e){return a.post("/databaseInfo/delete",e)}}e("D",t);const f=e("_",{__name:"datasource-add-edit",props:{visible:<PERSON><PERSON><PERSON>,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const f=e,v=a,b=o(!1),m=o(!1),p=o({positionSort:1e3}),g=o(null);s((()=>{f.data?(m.value=!0,p.value=Object.assign({},f.data)):m.value=!1}));const y=e=>{v("update:visible",e)},_=async()=>{g.value.$refs.formRef.validate().then((async e=>{if(e){b.value=!0;let e=null;e=m.value?t.edit(p.value):t.add(p.value),e.then((async e=>{b.value=!1,r.success(e.message),y(!1),v("done")})).catch((()=>{b.value=!1}))}}))};return(e,t)=>{const a=u;return l(),d(a,{width:700,maskClosable:!1,visible:f.visible,"confirm-loading":b.value,forceRender:!0,title:m.value?"编辑数据源":"新建数据源","body-style":{paddingBottom:"8px",height:"580px",overflowY:"auto"},"onUpdate:visible":y,onOk:_,onClose:t[1]||(t[1]=e=>y(!1))},{default:n((()=>[i(c,{form:p.value,"onUpdate:form":t[0]||(t[0]=e=>p.value=e),ref_key:"datasourceFormRef",ref:g},null,8,["form"])])),_:1},8,["visible","confirm-loading","title"])}}}),v=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));e("d",v)}}}));
