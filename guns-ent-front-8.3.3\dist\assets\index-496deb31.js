import{_ as oe}from"./index-d0cfb2ce.js";import{_ as le}from"./index-02bf6f00.js";import{_ as ne,P as ae,K as se,r as v,L as j,N as re,s as ie,k as P,a as r,c as y,d as t,w as o,b as s,g as i,t as h,h as G,O as T,Q as S,F as q,e as H,f,M as Z,E as $,m as N,U as de,n as ce,B as ue,I as _e,p as me,q as pe,D as he,l as ge,V as ve,W as fe,J as Ce,u as we,v as be,G as xe,H as ye}from"./index-18a1ea24.js";/* empty css              */import{_ as ee}from"./index-bb869875.js";import{C as u}from"./CustomerApi-e856427e.js";import Te from"./CustomerEdit-624cf0f9.js";import Se from"./CustomerDetail-c3a0a49d.js";/* empty css              *//* empty css              *//* empty css              */import"./UniversalTree-6547889b.js";import"./regionApi-2c103d88.js";import"./index-60b48b32.js";/* empty css              *//* empty css              */const ke={name:"CustomerIndex",components:{PlusOutlined:ae,SmallDashOutlined:se,CustomerEdit:Te,CustomerDetail:Se,RegionTree:ee},setup(){const R=v(!1),n=v(!1),D=v(!1),e=v({}),J=v(null),E=v([]),C=v(null),w=j(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),b=j(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),U=j(()=>re()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24}),d=ie({searchText:"",customerType:void 0,customerLevel:void 0,status:void 0,contactPerson:void 0,contactPhone:void 0}),x=u.getCustomerTypeOptions(),B=u.getCustomerLevelOptions(),z=u.getCustomerStatusOptions(),A=[{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"customerCode",title:"\u5BA2\u6237\u7F16\u7801",width:140,ellipsis:!0,isShow:!0},{dataIndex:"customerName",title:"\u5BA2\u6237\u540D\u79F0",width:200,ellipsis:!0,isShow:!0},{dataIndex:"customerShortName",title:"\u5BA2\u6237\u7B80\u79F0",width:150,ellipsis:!0,isShow:!0},{dataIndex:"customerType",title:"\u5BA2\u6237\u7C7B\u578B",width:120,align:"center",isShow:!0},{dataIndex:"customerLevel",title:"\u5BA2\u6237\u7B49\u7EA7",width:120,align:"center",isShow:!0},{dataIndex:"contactPerson",title:"\u8054\u7CFB\u4EBA",width:100,ellipsis:!0,isShow:!0},{dataIndex:"contactPhone",title:"\u8054\u7CFB\u7535\u8BDD",width:120,ellipsis:!0,isShow:!0},{dataIndex:"contactMobile",title:"\u624B\u673A\u53F7\u7801",width:120,ellipsis:!0,isShow:!0},{dataIndex:"status",title:"\u72B6\u6001",width:100,align:"center",isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}],g=()=>{C.value&&C.value.reload()},k=()=>{Object.keys(d).forEach(a=>{d[a]=a==="searchText"?"":void 0}),g()},V=()=>{R.value=!R.value},I=()=>{e.value={},n.value=!0},L=a=>{e.value={...a},n.value=!0},_=a=>{e.value={...a},D.value=!0},m=a=>{Z.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u5BA2\u6237\u5417\uFF1F",icon:t($),maskClosable:!0,onOk:()=>u.delete({customerId:a.customerId}).then(()=>{N.success("\u5220\u9664\u6210\u529F"),g()}).catch(c=>{N.error(c.message||"\u5220\u9664\u5931\u8D25")})})},O=({key:a})=>{a==="1"&&M()},M=()=>{var c;const a=(c=C.value)==null?void 0:c.getSelectedRows();if(!a||a.length===0){N.warning("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u6570\u636E");return}Z.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ".concat(a.length," \u6761\u6570\u636E\u5417\uFF1F"),icon:t($),maskClosable:!0,onOk:()=>{const te=a.map(F=>F.customerId);return u.batchDelete({customerIdList:te}).then(()=>{N.success("\u5220\u9664\u6210\u529F"),g()}).catch(F=>{N.error(F.message||"\u5220\u9664\u5931\u8D25")})}})};return{tableRef:C,regionTreeRef:J,superSearch:R,where:d,columns:A,currentRecord:e,selectedRegionNodes:E,showEdit:n,showDetailModal:D,customerTypeOptions:x,customerLevelOptions:B,statusOptions:z,labelCol:w,wrapperCol:b,spanCol:U,reload:g,clear:k,changeSuperSearch:V,handleRegionTreeSelect:(a,c)=>{console.log("\u533A\u57DF\u6811\u9009\u62E9:",a,c),c&&c.selectedNodes&&c.selectedNodes.length>0?(E.value=c.selectedNodes,d.regionId=a[0]):(E.value=[],d.regionId=void 0),g()},handleRegionTreeLoaded:a=>{console.log("\u533A\u57DF\u6811\u6570\u636E\u52A0\u8F7D\u5B8C\u6210:",a)},openAddModal:I,openEditModal:L,showDetail:_,remove:m,moreClick:O,batchDelete:M,getCustomerTypeName:a=>u.getCustomerTypeName(a),getCustomerLevelName:a=>u.getCustomerLevelName(a),getCustomerStatusName:a=>u.getCustomerStatusName(a),getStatusTagColor:a=>u.getStatusTagColor(a),getCustomerLevelTagColor:a=>u.getCustomerLevelTagColor(a)}}},Ie={class:"guns-layout"},Le={class:"guns-layout-sidebar width-100 p-t-12"},Ne={class:"sidebar-content"},Re={class:"guns-layout-content"},Ee={class:"guns-layout"},Oe={class:"guns-layout-content-application"},Me={class:"content-main"},Pe={class:"content-main-header"},De={class:"header-content"},Ue={class:"header-content-left"},Be={key:0,class:"current-region-info"},ze={class:"header-content-right"},Ae={class:"content-main-body"},Ve={class:"table-content"},Fe={key:0,class:"super-search",style:{"margin-top":"8px"}};function je(R,n,D,e,J,E){const C=ee,w=de,b=ce,U=P("plus-outlined"),d=ue,x=_e,B=me,z=pe,A=P("small-dash-outlined"),g=he,k=ge,V=ve,I=fe,L=Ce,_=we,m=be,O=xe,M=ye,K=le,Q=oe,W=P("customer-edit"),X=P("customer-detail");return r(),y("div",Ie,[t(Q,{width:"292px",cacheKey:"ERP_CUSTOMER_MANAGEMENT"},{content:o(()=>[s("div",Re,[s("div",Ee,[s("div",Oe,[s("div",Me,[s("div",Pe,[s("div",De,[s("div",Ue,[t(b,{size:16},{default:o(()=>[e.selectedRegionNodes.length>0?(r(),y("span",Be,[n[9]||(n[9]=i(" \u5F53\u524D\u533A\u57DF\uFF1A")),t(w,{color:"blue"},{default:o(()=>[i(h(e.selectedRegionNodes[0].regionName),1)]),_:1})])):G("",!0)]),_:1})]),s("div",ze,[t(b,{size:16},{default:o(()=>[t(d,{type:"primary",class:"border-radius",onClick:e.openAddModal},{default:o(()=>[t(U),n[10]||(n[10]=i(" \u65B0\u589E\u5BA2\u6237 "))]),_:1,__:[10]},8,["onClick"]),t(g,null,{overlay:o(()=>[t(z,{onClick:e.moreClick},{default:o(()=>[t(B,{key:"1"},{default:o(()=>[t(x,{iconClass:"icon-opt-shanchu",color:"#60666b"}),n[11]||(n[11]=s("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[11]})]),_:1},8,["onClick"])]),default:o(()=>[t(d,{class:"border-radius"},{default:o(()=>[n[12]||(n[12]=i(" \u66F4\u591A ")),t(A)]),_:1,__:[12]})]),_:1})]),_:1})])])]),s("div",Ae,[s("div",Ve,[t(K,{columns:e.columns,where:e.where,fieldBusinessCode:"ERP_CUSTOMER_TABLE",showTableTool:"",showToolTotal:!1,rowId:"customerId",ref:"tableRef",url:"/erp/customer/page"},{toolLeft:o(()=>[t(k,{value:e.where.searchText,"onUpdate:value":n[0]||(n[0]=l=>e.where.searchText=l),bordered:!1,allowClear:"",placeholder:"\u5BA2\u6237\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:e.reload,style:{width:"240px"},class:"search-input"},{prefix:o(()=>[t(x,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"]),t(V,{type:"vertical",class:"divider"}),s("a",{onClick:n[1]||(n[1]=(...l)=>e.changeSuperSearch&&e.changeSuperSearch(...l))},h(e.superSearch?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:o(()=>[e.superSearch?(r(),y("div",Fe,[t(M,{model:e.where,labelCol:e.labelCol,"wrapper-col":e.wrapperCol},{default:o(()=>[t(O,{gutter:16},{default:o(()=>[t(m,T(S(e.spanCol)),{default:o(()=>[t(_,{label:"\u5BA2\u6237\u7C7B\u578B:"},{default:o(()=>[t(L,{value:e.where.customerType,"onUpdate:value":n[2]||(n[2]=l=>e.where.customerType=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u7C7B\u578B",style:{width:"100%"},allowClear:""},{default:o(()=>[(r(!0),y(q,null,H(e.customerTypeOptions,l=>(r(),f(I,{key:l.value,value:l.value},{default:o(()=>[i(h(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),t(m,T(S(e.spanCol)),{default:o(()=>[t(_,{label:"\u5BA2\u6237\u7B49\u7EA7:"},{default:o(()=>[t(L,{value:e.where.customerLevel,"onUpdate:value":n[3]||(n[3]=l=>e.where.customerLevel=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u7B49\u7EA7",style:{width:"100%"},allowClear:""},{default:o(()=>[(r(!0),y(q,null,H(e.customerLevelOptions,l=>(r(),f(I,{key:l.value,value:l.value},{default:o(()=>[i(h(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),t(m,T(S(e.spanCol)),{default:o(()=>[t(_,{label:"\u72B6\u6001:"},{default:o(()=>[t(L,{value:e.where.status,"onUpdate:value":n[4]||(n[4]=l=>e.where.status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",style:{width:"100%"},allowClear:""},{default:o(()=>[(r(!0),y(q,null,H(e.statusOptions,l=>(r(),f(I,{key:l.value,value:l.value},{default:o(()=>[i(h(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16)]),_:1}),t(O,{gutter:16},{default:o(()=>[t(m,T(S(e.spanCol)),{default:o(()=>[t(_,{label:"\u8054\u7CFB\u4EBA:"},{default:o(()=>[t(k,{value:e.where.contactPerson,"onUpdate:value":n[5]||(n[5]=l=>e.where.contactPerson=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),t(m,T(S(e.spanCol)),{default:o(()=>[t(_,{label:"\u8054\u7CFB\u7535\u8BDD:"},{default:o(()=>[t(k,{value:e.where.contactPhone,"onUpdate:value":n[6]||(n[6]=l=>e.where.contactPhone=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),t(m,T(S(e.spanCol)),{default:o(()=>[t(_,{label:" ",class:"not-label"},{default:o(()=>[t(b,{size:16},{default:o(()=>[t(d,{class:"border-radius",onClick:e.reload,type:"primary"},{default:o(()=>n[13]||(n[13]=[i("\u67E5\u8BE2")])),_:1,__:[13]},8,["onClick"]),t(d,{class:"border-radius",onClick:e.clear},{default:o(()=>n[14]||(n[14]=[i("\u91CD\u7F6E")])),_:1,__:[14]},8,["onClick"])]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])])):G("",!0)]),bodyCell:o(({column:l,record:p})=>[l.dataIndex==="customerType"?(r(),f(w,{key:0},{default:o(()=>[i(h(e.getCustomerTypeName(p.customerType)),1)]),_:2},1024)):l.dataIndex==="customerLevel"?(r(),f(w,{key:1,color:e.getCustomerLevelTagColor(p.customerLevel)},{default:o(()=>[i(h(e.getCustomerLevelName(p.customerLevel)),1)]),_:2},1032,["color"])):l.dataIndex==="status"?(r(),f(w,{key:2,color:e.getStatusTagColor(p.status)},{default:o(()=>[i(h(e.getCustomerStatusName(p.status)),1)]),_:2},1032,["color"])):l.key==="action"?(r(),f(b,{key:3,size:16},{default:o(()=>[t(x,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:Y=>e.openEditModal(p)},null,8,["onClick"]),t(x,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:Y=>e.remove(p)},null,8,["onClick"])]),_:2},1024)):G("",!0)]),_:1},8,["columns","where"])])])])])])])]),default:o(()=>[s("div",Le,[s("div",Ne,[t(C,{ref:"regionTreeRef","show-badge":!0,onTreeSelect:e.handleRegionTreeSelect,onTreeDataLoaded:e.handleRegionTreeLoaded},null,8,["onTreeSelect","onTreeDataLoaded"])])])]),_:1}),t(W,{visible:e.showEdit,"onUpdate:visible":n[7]||(n[7]=l=>e.showEdit=l),data:e.currentRecord,onDone:e.reload},null,8,["visible","data","onDone"]),t(X,{visible:e.showDetailModal,"onUpdate:visible":n[8]||(n[8]=l=>e.showDetailModal=l),data:e.currentRecord},null,8,["visible","data"])])}const at=ne(ke,[["render",je],["__scopeId","data-v-b66e25ba"]]);export{at as default};
