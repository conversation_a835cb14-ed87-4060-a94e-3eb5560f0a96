<template>
  <div class="guns-body guns-body-card">
    <a-card title="省市区级联选择" :bordered="false">
      <CityComponent
        v-model:value="selectValue"
        :disabled="disabled"
        :readonly="readonly"
        :placeholder="placeholder"
        @onChange="onChange"
        normal
      />
    </a-card>
    <a-card title="省市级联选择" :bordered="false">
      <CityComponent
        v-model:value="selectValue1"
        :disabled="disabled"
        :readonly="readonly"
        type="provinceCity"
        :placeholder="placeholder"
        @onChange="onChange"
        normal
      />
    </a-card>
    <a-card title="省选择" :bordered="false">
      <CityComponent
        v-model:value="selectValue2"
        :disabled="disabled"
        :readonly="readonly"
        type="province"
        :placeholder="placeholder"
        @onChange="onChange"
        normal
      />
    </a-card>
    <a-card title="通过code获取名称" :bordered="false">
      {{ selectName }}
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { codeToText } from 'element-china-area-data';

// 选中的值
const selectValue = ref('');
const selectValue1 = ref('');
const selectValue2 = ref('');
const selectName = ref('');
// 是否禁用
const disabled = ref(false);

// 是否只读
const readonly = ref(false);

// 选择提示
const placeholder = ref('请选择');

// 值改变
const onChange = () => {
  console.log(selectValue.value);
  selectName.value = getCodeToText(selectValue.value);
};
const getCodeToText = codeArray => {
  let area = '';
  switch (codeArray.length) {
    case 1:
      area += codeToText[codeArray[0]];
      break;
    case 2:
      area += codeToText[codeArray[0]] + '/' + codeToText[codeArray[1]];
      break;
    case 3:
      area += codeToText[codeArray[0]] + '/' + codeToText[codeArray[1]] + '/' + codeToText[codeArray[2]];
      break;
    default:
      break;
  }
  console.log(area, 1111);
  return area;
};
</script>

<style></style>
