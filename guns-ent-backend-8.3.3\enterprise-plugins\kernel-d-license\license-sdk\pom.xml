<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-license</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>license-sdk</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- license-api -->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>license-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!-- xjar -->
<!--        <dependency>-->
<!--            <groupId>com.github.core-lib</groupId>-->
<!--            <artifactId>xjar</artifactId>-->
<!--            <version>4.0.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-loader</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.apache.commons</groupId>-->
<!--            <artifactId>commons-compress</artifactId>-->
<!--            <version>1.19</version>-->
<!--        </dependency>-->

    </dependencies>

    <!-- 设置 jitpack.io 仓库 -->
<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>jitpack.io</id>-->
<!--            <url>https://jitpack.io</url>-->
<!--        </repository>-->
<!--    </repositories>-->

</project>
