import{r as o,o as g,a as I,f as R,w as _,d as y,m as h,M as k}from"./index-18a1ea24.js";import{I as f,a as x}from"./interface-form-eed8b968.js";const w={__name:"interface-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(v,{emit:m}){const n=v,u=m,l=o(!1),s=o(!1),a=o({}),i=o(null);g(()=>{n.data?(s.value=!0,p()):s.value=!1});const p=()=>{f.detail({apiClientResourceId:n.data.apiClientResourceId}).then(t=>{a.value=Object.assign({},t),i.value.setResourceData(a.value)})},r=t=>{u("update:visible",t)},b=async()=>{i.value.$refs.formRef.validate().then(async t=>{if(t){l.value=!0;let e=null;s.value?e=f.edit(a.value):e=f.add(a.value),e.then(async d=>{l.value=!1,h.success(d.message),r(!1),u("done")}).catch(()=>{l.value=!1})}})};return(t,e)=>{const d=k;return I(),R(d,{width:700,maskClosable:!1,visible:n.visible,"confirm-loading":l.value,forceRender:!0,title:s.value?"\u7F16\u8F91\u63A5\u53E3":"\u65B0\u5EFA\u63A5\u53E3","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":r,onOk:b,onClose:e[1]||(e[1]=c=>r(!1))},{default:_(()=>[y(x,{form:a.value,"onUpdate:form":e[0]||(e[0]=c=>a.value=c),ref_key:"InterfaceRef",ref:i,isUpdate:s.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}};export{w as default};
