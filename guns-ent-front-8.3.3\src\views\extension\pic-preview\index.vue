<template>
  <div class="guns-body guns-body-card">
    <a-card title="图片预览" :bordered="false">
      <a-button @click="previewClick">点击预览</a-button>
    </a-card>

    <PicPreview
      :visible="photoVisible"
      :urlList="bigImgUrlList"
      :currentIndex="currentImgIndex"
      @close="close"
      v-if="photoVisible"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import LogoImg from '@/assets/logo.png';

const bigImgUrlList = ref('');

const currentImgIndex = ref(0);

const photoVisible = ref(false);

//关闭图片预览
const close = () => {
  photoVisible.value = false;
  bigImgUrlList.value = [];
  currentIndex.value = 0;
};

const previewClick = () => {
  bigImgUrlList.value = [LogoImg];
  currentImgIndex.value = 0;
  photoVisible.value = true;
};
</script>

<style></style>
