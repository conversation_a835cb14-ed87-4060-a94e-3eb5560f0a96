package cn.stylefeng.roses.kernel.erp.modular.pos.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment;

import java.math.BigDecimal;
import java.util.List;

/**
 * POS支付服务接口
 *
 * <AUTHOR>
 * @since 2025/08/01 11:00
 */
public interface PosPaymentService {

    /**
     * 处理现金支付
     *
     * @param orderId 订单ID
     * @param paymentAmount 支付金额
     * @param receivedAmount 实收金额
     * @return 支付记录ID
     */
    Long processCashPayment(Long orderId, BigDecimal paymentAmount, BigDecimal receivedAmount);

    /**
     * 处理扫码支付（微信/支付宝）
     *
     * @param orderId 订单ID
     * @param paymentAmount 支付金额
     * @param paymentMethod 支付方式（WECHAT/ALIPAY）
     * @return 支付记录ID
     */
    Long processQrCodePayment(Long orderId, BigDecimal paymentAmount, String paymentMethod);

    /**
     * 处理会员卡支付
     *
     * @param orderId 订单ID
     * @param paymentAmount 支付金额
     * @param memberId 会员ID
     * @return 支付记录ID
     */
    Long processMemberCardPayment(Long orderId, BigDecimal paymentAmount, Long memberId);

    /**
     * 处理银行卡支付
     *
     * @param orderId 订单ID
     * @param paymentAmount 支付金额
     * @param cardNo 银行卡号（脱敏）
     * @return 支付记录ID
     */
    Long processBankCardPayment(Long orderId, BigDecimal paymentAmount, String cardNo);

    /**
     * 处理混合支付（多种支付方式组合）
     *
     * @param orderId 订单ID
     * @param paymentDetails 支付详情列表
     * @return 支付记录ID列表
     */
    List<Long> processMixedPayment(Long orderId, List<PaymentDetail> paymentDetails);

    /**
     * 确认支付成功
     *
     * @param paymentId 支付记录ID
     * @param transactionId 第三方交易号
     */
    void confirmPaymentSuccess(Long paymentId, String transactionId);

    /**
     * 支付失败处理
     *
     * @param paymentId 支付记录ID
     * @param failureReason 失败原因
     */
    void handlePaymentFailure(Long paymentId, String failureReason);

    /**
     * 取消支付
     *
     * @param paymentId 支付记录ID
     * @param reason 取消原因
     */
    void cancelPayment(Long paymentId, String reason);

    /**
     * 退款处理
     *
     * @param paymentId 支付记录ID
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 退款记录ID
     */
    Long processRefund(Long paymentId, BigDecimal refundAmount, String reason);

    /**
     * 计算找零金额
     *
     * @param paymentAmount 应付金额
     * @param receivedAmount 实收金额
     * @return 找零金额
     */
    BigDecimal calculateChange(BigDecimal paymentAmount, BigDecimal receivedAmount);

    /**
     * 根据订单ID查询支付记录
     *
     * @param orderId 订单ID
     * @return 支付记录列表
     */
    List<PosPayment> getPaymentsByOrderId(Long orderId);

    /**
     * 根据支付ID查询支付详情
     *
     * @param paymentId 支付ID
     * @return 支付记录
     */
    PosPayment getPaymentById(Long paymentId);

    /**
     * 根据支付单号查询支付记录
     *
     * @param paymentNo 支付单号
     * @return 支付记录
     */
    PosPayment getPaymentByNo(String paymentNo);

    /**
     * 分页查询支付记录
     *
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param paymentMethod 支付方式（可选）
     * @param paymentStatus 支付状态（可选）
     * @param orderId 订单ID（可选）
     * @return 支付记录分页结果
     */
    PageResult<PosPayment> findPaymentPage(Integer pageNo, Integer pageSize,
                                          String paymentMethod, String paymentStatus, Long orderId);

    /**
     * 生成支付单号
     *
     * @param paymentMethod 支付方式
     * @return 支付单号
     */
    String generatePaymentNo(String paymentMethod);

    /**
     * 校验支付金额
     *
     * @param orderId 订单ID
     * @param paymentAmount 支付金额
     * @return 校验结果
     */
    boolean validatePaymentAmount(Long orderId, BigDecimal paymentAmount);

    /**
     * 校验会员余额
     *
     * @param memberId 会员ID
     * @param paymentAmount 支付金额
     * @return 余额是否充足
     */
    boolean validateMemberBalance(Long memberId, BigDecimal paymentAmount);

    /**
     * 获取订单的已支付金额
     *
     * @param orderId 订单ID
     * @return 已支付金额
     */
    BigDecimal getPaidAmountByOrderId(Long orderId);

    /**
     * 获取订单的待支付金额
     *
     * @param orderId 订单ID
     * @return 待支付金额
     */
    BigDecimal getUnpaidAmountByOrderId(Long orderId);

    /**
     * 支付详情内部类
     */
    class PaymentDetail {
        private String paymentMethod;
        private BigDecimal paymentAmount;
        private BigDecimal receivedAmount;
        private Long memberId;
        private String cardNo;

        // 构造函数
        public PaymentDetail(String paymentMethod, BigDecimal paymentAmount) {
            this.paymentMethod = paymentMethod;
            this.paymentAmount = paymentAmount;
        }

        // Getters and Setters
        public String getPaymentMethod() {
            return paymentMethod;
        }

        public void setPaymentMethod(String paymentMethod) {
            this.paymentMethod = paymentMethod;
        }

        public BigDecimal getPaymentAmount() {
            return paymentAmount;
        }

        public void setPaymentAmount(BigDecimal paymentAmount) {
            this.paymentAmount = paymentAmount;
        }

        public BigDecimal getReceivedAmount() {
            return receivedAmount;
        }

        public void setReceivedAmount(BigDecimal receivedAmount) {
            this.receivedAmount = receivedAmount;
        }

        public Long getMemberId() {
            return memberId;
        }

        public void setMemberId(Long memberId) {
            this.memberId = memberId;
        }

        public String getCardNo() {
            return cardNo;
        }

        public void setCardNo(String cardNo) {
            this.cardNo = cardNo;
        }
    }

}