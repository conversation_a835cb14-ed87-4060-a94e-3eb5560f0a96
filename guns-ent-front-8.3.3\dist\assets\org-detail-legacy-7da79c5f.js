System.register(["./index-legacy-ee1db0c7.js","./OrgApi-legacy-c15eac58.js","./org-add-edit-legacy-63da3c2d.js","./set-approver-legacy-303b6a70.js","./org-form-legacy-6e9e4f75.js","./index-legacy-dba03026.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js","./index-legacy-94a6fc23.js","./index-legacy-45c79de7.js","./index-legacy-16a1b89e.js"],(function(e,a){"use strict";var l,t,n,i,o,s,u,v,r,d,c,g,m,p,b,y,f,h,x,j,w,N,_,k,C,L,S,z;return{setters:[e=>{l=e._,t=e.r,n=e.L,i=e.o,o=e.X,s=e.aK,u=e.a,v=e.f,r=e.w,d=e.b,c=e.t,g=e.c,m=e.h,p=e.g,b=e.aR,y=e.d,f=e.F,h=e.e,x=e.aS,j=e.B,w=e.u,N=e.v,_=e.G,k=e.H,C=e.ch},e=>{L=e.O},e=>{S=e.default},e=>{z=e.default},null,null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent="[data-v-857291ab] .ant-drawer-title{color:#262626;font-size:18px;font-weight:500}.top[data-v-857291ab]{display:flex;align-items:center;justify-content:space-between;height:40px;line-height:40px;margin-bottom:14px}.top .orgName[data-v-857291ab]{margin-right:5px;font-size:22px;font-weight:700;color:#000}.top .short-name[data-v-857291ab]{font-size:18px;color:#a6a6a6}.content[data-v-857291ab]{width:100%;height:calc(100% - 130px);overflow-y:auto;overflow-x:hidden}.content .content-item[data-v-857291ab]{width:100%;height:calc(100% - 100px)}[data-v-857291ab] .ant-form-item-label>label{color:#60666b}[data-v-857291ab] .ant-form-item{color:#60666b}\n",document.head.appendChild(a);const A={class:"top"},O={class:"top-left"},B={class:"orgName"},F={key:0,class:"short-name"},T={class:"top-right"},D={class:"content"},I={class:"content-item"},K={key:0},R={key:1},E={key:2},X={key:3},G={class:"content-item"},H={__name:"org-detail",props:{visible:Boolean,data:Object,isShowApprover:Boolean,levelNumberList:Array},emits:["update:visible","done"],setup(e,{emit:a}){const l=e,H=a,M=t("1"),U=t([{key:"1",name:"基础信息",icon:"icon-tab-jichuxinxi"},{key:"2",name:"审批人",icon:"icon-opt-shenpirenshezhi"}]),q=t({}),J=t(!1),P=t(null),Q=t([{name:"上机机构",value:"parentOrgName"},{name:"机构编码",value:"orgCode"},{name:"机构类型",value:"orgType"},{name:"机构简称",value:"orgShortName"},{name:"机构状态",value:"statusFlag"},{name:"机构层级",value:"levelCode"},{name:"税号",value:"taxNo"},{name:"备注",value:"remark"},{name:"排序",value:"orgSort"}]),V=t([]),W=n((()=>{var e;let a="";if(null!==(e=q.value)&&void 0!==e&&e.levelCode){let e=V.value.find((e=>e.levelCode==q.value.levelCode));if(e){a=e.levelName;let t=l.levelNumberList.find((a=>a.value==e.levelNumber));t&&(a+="("+t.name+")")}}return a}));i((()=>{l.isShowApprover&&(M.value="2"),Z(),Y()})),o((()=>l.data),(e=>{e&&Y()}),{deep:!0});const Y=()=>{L.detail({orgId:l.data.orgId}).then((e=>{q.value=Object.assign({},e)})),s((()=>{P.value.getListData()}))},Z=async()=>{V.value=await L.organizationLevelList()},$=e=>{M.value=e},ee=()=>{"2"==M.value?P.value.getListData():J.value=!0};return(e,a)=>{const t=j,n=w,i=N,o=_,s=k,L=C;return u(),v(L,{width:800,visible:l.visible,title:"机构信息",onClose:a[1]||(a[1]=e=>{H("update:visible",!1)}),isShowTab:!0,activeKey:M.value,tabList:U.value,onTabChange:$},{top:r((()=>[d("div",A,[d("div",O,[d("span",B,c(q.value.orgName),1),q.value.orgShortName?(u(),g("span",F,"("+c(q.value.orgShortName)+")",1)):m("",!0)]),d("div",T,["1"==M.value?(u(),v(t,{key:0,type:"primary",class:"border-radius",onClick:ee},{default:r((()=>a[2]||(a[2]=[p("编辑")]))),_:1,__:[2]})):m("",!0)])])])),default:r((()=>[d("div",D,[b(d("div",I,[y(s,{ref:"formRef",model:q.value,"label-col":{span:6}},{default:r((()=>[y(o,{gutter:16},{default:r((()=>[(u(!0),g(f,null,h(Q.value,((e,a)=>(u(),v(i,{span:12,key:a},{default:r((()=>[y(n,{label:e.name},{default:r((()=>["orgType"==e.value?(u(),g("span",K,c(1==q.value[e.value]?"公司":"部门"),1)):"statusFlag"==e.value?(u(),g("span",R,c(1==q.value[e.value]?"启用":"禁用"),1)):"levelCode"==e.value?(u(),g("span",E,c(W.value),1)):(u(),g("span",X,c(q.value[e.value]),1))])),_:2},1032,["label"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["model"])],512),[[x,"1"==M.value]]),b(d("div",G,[y(z,{ref_key:"setApproverRef",ref:P,data:l.data},null,8,["data"])],512),[[x,"2"==M.value]])]),J.value?(u(),v(S,{key:0,visible:J.value,"onUpdate:visible":a[0]||(a[0]=e=>J.value=e),data:q.value,onDone:Y,levelNumberList:l.levelNumberList},null,8,["visible","data","levelNumberList"])):m("",!0)])),_:1},8,["visible","activeKey","tabList"])}}};e("default",l(H,[["__scopeId","data-v-857291ab"]]))}}}));
