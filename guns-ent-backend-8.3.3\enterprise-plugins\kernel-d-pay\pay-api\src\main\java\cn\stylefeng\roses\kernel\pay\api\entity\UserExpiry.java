package cn.stylefeng.roses.kernel.pay.api.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户商品到期时间实例类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@TableName("shop_user_expiry")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserExpiry extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "expiry_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键id")
    private Long expiryId;

    /**
     * 用户id
     */
    @TableField("user_id")
    @ChineseDescription("用户id")
    private Long userId;

    /**
     * 商品id
     */
    @TableField("goods_id")
    @ChineseDescription("商品id")
    private Long goodsId;

    /**
     * 商品到期时间
     */
    @TableField("expiry_date")
    @ChineseDescription("商品到期时间")
    private Date expiryDate;

    /**
     * 是否删除：Y-删除，N-未删除
     */
    @TableField("del_flag")
    @ChineseDescription("是否删除：Y-删除，N-未删除")
    private String delFlag;

}
