import{_ as ie,b3 as ue,r,L as _e,o as pe,a,f as m,w as c,d as n,b as o,t as h,c as p,F as D,e as P,g as R,h as V,at as ve,a5 as O,m as $,u as me,j as he,T as fe,C as ye,ab as ge,B as Ie,bg as Te,I as be,l as ke,H as Ne,aA as xe}from"./index-18a1ea24.js";import{_ as Ce}from"./index-d0cfb2ce.js";/* empty css              *//* empty css              */import{U as f}from"./UsersApi-ec2041f8.js";const Le={style:{"font-size":"16px"}},we={class:"role-select"},Re={class:"role-select-h"},Ke={class:"role-select-b"},Se={key:0,class:"system-role"},Ue={key:1,class:"system-role"},Fe={key:2},Be={key:1,class:"role-select-box"},Ee={class:"company-tree"},Ae={class:"role-select-content"},De={class:"role-select-item"},Pe={class:"role-select-item-top"},Ve={class:"role-select-item-search"},Oe={class:"role-select-item-bottom"},$e={class:"role-select-item"},Me={class:"role-select-item-search"},je={class:"role-select-item-bottom"},ze={class:"bottom-list"},Ge={key:1},He={class:"selected-name"},Ye={class:"selected-del"},qe={__name:"allocation-role",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(M,{emit:j}){const _=M,z=j,G=ue(),T=r(!1),y=r({roleIdList:[],userId:_.data.userId}),K=r([]),b=r([]),v=r([]),i=r(""),k=r(""),N=r(""),u=r([]),g=r([]),x=r([]),H=_e(()=>G.info.superAdminFlag);pe(()=>{H.value?(i.value=10,q(),Y()):i.value=15,J()});const Y=()=>{f.detail({userId:_.data.userId}).then(s=>{var e;y.value.roleIdList=(e=s.roleIdList)!=null?e:[]})},q=async()=>{K.value=await f.roleList()},J=async()=>{b.value=await f.getUserOrgList({userId:_.data.userId}),C()},C=()=>{var s;u.value=[],g.value=[],i.value!=10&&((s=b.value)==null?void 0:s.length)>0&&(v.value.length==0&&(v.value=[b.value[0].orgId]),i.value==15?Q():i.value==20&&W())},Q=()=>{f.getCompanyBusinessRoleTree({orgId:v.value[0],userId:_.data.userId,searhText:k.value}).then(s=>{x.value=L(s.data)})},W=()=>{f.getCompanyRoleTree({orgId:v.value[0],userId:_.data.userId,searhText:k.value}).then(s=>{x.value=L(s.data)})},L=s=>((s==null?void 0:s.length)>0&&s.forEach(e=>{var t;e.nodeType==1&&(e.disabled=!0),e.checkedFlag&&(u.value.push({...e,children:void 0}),g.value.push(e.roleTreeNodeId)),((t=e.children)==null?void 0:t.length)>0&&(e.children=L(e.children))}),s),S=s=>{z("update:visible",s)},U=()=>{T.value=!0,f.bindRoles(y.value).then(s=>{T.value=!1,$.success(s.message)}).catch(()=>{T.value=!1})},X=()=>{C()},Z=()=>{C()},ee=(s,{checked:e,node:t})=>{e?u.value.find(I=>I.roleTreeNodeId==t.roleTreeNodeId)||(u.value.push({checkedFlag:t.checkedFlag,nodeId:t.nodeId,nodeParentId:t.nodeParentId,nodeType:t.nodeType,roleTreeNodeId:t.roleTreeNodeId,roleTreeNodeName:t.roleTreeNodeName,roleTreeParentNodeId:t.roleTreeParentNodeId,roleType:t.roleType}),B(t,e)):F(t)},F=s=>{var e;u.value.splice(u.value.findIndex(t=>t.roleTreeNodeId===s.roleTreeNodeId),1),g.value=(e=u.value)==null?void 0:e.map(t=>t.roleTreeNodeId),B(s,!1)},B=(s,e)=>{f.changeRoleSelect({checkedFlag:e,orgId:v.value[0],roleId:s.roleTreeNodeId,roleType:s.roleType,userId:_.data.userId}).then(t=>{$.success(t.message)})};return(s,e)=>{const t=me,I=he,se=fe,te=ye,oe=ge,le=Ie,E=Te,w=be,A=ke,ae=O,ne=Ce,ce=Ne,de=xe;return a(),m(de,{width:1200,maskClosable:!1,visible:_.visible,"confirm-loading":T.value,forceRender:!0,title:"\u5206\u914D\u89D2\u8272","body-style":{padding:"8px 24px",height:"75vh",overflowY:"auto"},"onUpdate:visible":S,onOk:U,maxable:"",footer:null,onClose:e[6]||(e[6]=l=>S(!1))},{default:c(()=>[n(ce,{ref:"formRef",model:y.value},{default:c(()=>[n(t,{label:"\u7528\u6237\u59D3\u540D",style:{width:"100%"}},{default:c(()=>[o("span",Le,h(_.data.realName),1)]),_:1}),o("div",we,[o("div",Re,[n(se,{activeKey:i.value,"onUpdate:activeKey":e[0]||(e[0]=l=>i.value=l),class:"devops-tabs",onChange:X},{default:c(()=>[(a(),m(I,{key:10,tab:"\u7CFB\u7EDF\u89D2\u8272"})),(a(),m(I,{key:15,tab:"\u4E1A\u52A1\u89D2\u8272"})),(a(),m(I,{key:20,tab:"\u516C\u53F8\u89D2\u8272"}))]),_:1},8,["activeKey"])]),o("div",Ke,[i.value==10?(a(),m(t,{key:0,label:"\u89D2\u8272\u4FE1\u606F",style:{width:"100%",padding:"16px"}},{default:c(()=>[n(oe,{value:y.value.roleIdList,"onUpdate:value":e[1]||(e[1]=l=>y.value.roleIdList=l)},{default:c(()=>[(a(!0),p(D,null,P(K.value,l=>(a(),m(te,{value:l.roleId,name:"type",key:l.roleId,class:"chexkout"},{default:c(()=>[l.roleType==10?(a(),p("span",Se,[R(h(l.roleName)+" ",1),e[7]||(e[7]=o("span",null,"\uFF08\u7CFB\u7EDF\u89D2\u8272\uFF09",-1))])):l.roleType==15?(a(),p("span",Ue,[R(h(l.roleName)+" ",1),e[8]||(e[8]=o("span",null,"\uFF08\u4E1A\u52A1\u89D2\u8272\uFF09",-1))])):(a(),p("span",Fe,h(l.roleName),1))]),_:2},1032,["value"]))),128))]),_:1},8,["value"]),o("div",null,[n(le,{type:"primary",class:"border-radius",onClick:U},{default:c(()=>e[9]||(e[9]=[R("\u786E\u5B9A")])),_:1,__:[9]})])]),_:1})):V("",!0),i.value!=10?(a(),p("div",Be,[n(ne,{width:"252px",allowCollapse:!1},{content:c(()=>{var l;return[o("div",Ae,[o("div",De,[o("div",Pe,h(i.value==15?"\u4E1A\u52A1\u89D2\u8272":"\u516C\u53F8\u89D2\u8272"),1),o("div",Ve,[n(A,{value:k.value,"onUpdate:value":e[3]||(e[3]=d=>k.value=d),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD\uFF08\u56DE\u8F66\u641C\u7D22\uFF09","allow-clear":""},{prefix:c(()=>[n(w,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),o("div",Oe,[n(E,{"show-icon":!1,checkable:"",checkStrictly:"",onCheck:ee,checkedKeys:g.value,"onUpdate:checkedKeys":e[4]||(e[4]=d=>g.value=d),"tree-data":x.value,fieldNames:{children:"children",title:"roleTreeNodeName",key:"roleTreeNodeId",value:"roleTreeNodeId"}},null,8,["checkedKeys","tree-data"])])]),o("div",$e,[e[10]||(e[10]=o("div",{class:"role-select-item-top"},"\u5DF2\u9009\u62E9",-1)),o("div",Me,[n(A,{value:N.value,"onUpdate:value":e[5]||(e[5]=d=>N.value=d),placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u8BCD\uFF08\u56DE\u8F66\u641C\u7D22\uFF09","allow-clear":""},{prefix:c(()=>[n(w,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),o("div",je,[o("div",ze,[u.value&&u.value.length==0?(a(),m(ae,{key:0,image:ve(O).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(a(),p("div",Ge,[(a(!0),p(D,null,P((l=u.value)==null?void 0:l.filter(d=>d.roleTreeNodeName.includes(N.value)),(d,re)=>(a(),p("div",{class:"list",key:d.roleTreeNodeId},[o("div",He,h(re+1)+". "+h(d.roleTreeNodeName),1),o("div",Ye,[n(w,{iconClass:"icon-opt-shanchu",title:"\u5220\u9664",color:"#000",onClick:Je=>F(d)},null,8,["onClick"])])]))),128))]))])])])])]}),default:c(()=>[o("div",Ee,[n(E,{"show-icon":!1,selectedKeys:v.value,"onUpdate:selectedKeys":e[2]||(e[2]=l=>v.value=l),onSelect:Z,"tree-data":b.value,fieldNames:{children:"children",title:"companyName",key:"orgId",value:"orgId"}},null,8,["selectedKeys","tree-data"])])]),_:1})])):V("",!0)])])]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])}}},ss=ie(qe,[["__scopeId","data-v-f9bec944"]]);export{ss as default};
