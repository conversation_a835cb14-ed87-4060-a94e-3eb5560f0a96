/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.i18n.modular.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.i18n.api.TranslationPersistenceApi;
import cn.stylefeng.roses.kernel.i18n.api.pojo.request.TranslationRequest;
import cn.stylefeng.roses.kernel.i18n.modular.entity.Translation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 多语言表 服务类
 *
 * <AUTHOR>
 * @since 2021/1/24 19:21
 */
public interface TranslationService extends IService<Translation>, TranslationPersistenceApi {

    /**
     * 新增
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/26 12:52
     */
    void add(TranslationRequest translationRequest);

    /**
     * 删除
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/26 12:52
     */
    void del(TranslationRequest translationRequest);

    /**
     * 修改
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/26 12:52
     */
    void edit(TranslationRequest translationRequest);

    /**
     * 查询-详情-根据主键id
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/26 12:52
     */
    Translation detail(TranslationRequest translationRequest);

    /**
     * 查询-列表-按实体对象
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/26 12:52
     */
    List<Translation> findList(TranslationRequest translationRequest);

    /**
     * 查询-列表-分页-按实体对象
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/26 12:52
     */
    PageResult<Translation> findPage(TranslationRequest translationRequest);

    /**
     * 删除语种
     *
     * @param translationRequest 参数对象
     * <AUTHOR>
     * @since 2021/1/30 10:00
     */
    void deleteTranLanguage(TranslationRequest translationRequest);

}
