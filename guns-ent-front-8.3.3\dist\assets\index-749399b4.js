import t from"./index-807cd0f5.js";import{a as r,f as o}from"./index-18a1ea24.js";import"./list-51145fbc.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              */import"./SysDictTypeApi-1ce2cbe7.js";import"./detail-d04228d8.js";const E=Object.assign({name:"AuditSecure"},{__name:"index",setup(i){return(e,m)=>(r(),o(t,{apiUrl:"/auditLog/getSecurityLog",fieldBusinessCode:"SYSTEM_AUDIT_SECURE"}))}});export{E as default};
