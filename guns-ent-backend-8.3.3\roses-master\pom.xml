<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.javaguns.roses</groupId>
    <artifactId>roses-kernel</artifactId>
    <version>8.3.3</version>

    <packaging>pom</packaging>

    <name>roses-kernel</name>
    <description>Roses是开源项目Guns的核心包</description>

    <parent>
        <groupId>com.javaguns</groupId>
        <artifactId>javaguns-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modules>
        <!--规则模块，开发规则-->
        <module>kernel-a-rule</module>

        <!--认证和鉴权模块-->
        <module>kernel-d-auth</module>

        <!--缓存模块-->
        <module>kernel-d-cache</module>

        <!--系统配置表-->
        <module>kernel-d-config</module>

        <!--dao框架-->
        <module>kernel-d-db</module>

        <!--多数据源模块-->
        <module>kernel-d-ds-container</module>

        <!--邮件发送模块-->
        <module>kernel-d-email</module>

        <!--业务事件模块-->
        <module>kernel-d-event</module>

        <!--文件操作模块-->
        <module>kernel-d-file</module>

        <!--动态代码执行-->
        <module>kernel-d-groovy</module>

        <!--多语言模块-->
        <module>kernel-d-i18n</module>

        <!--jwt模块，用于token校验-->
        <module>kernel-d-jwt</module>

        <!--日志记录模块-->
        <module>kernel-d-log</module>

        <!--office模块-->
        <module>kernel-d-office</module>

        <!--拼音工具模块-->
        <module>kernel-d-pinyin</module>

        <!--资源扫描模块-->
        <module>kernel-d-scanner</module>

        <!--安全策略模块-->
        <module>kernel-d-security</module>

        <!--sms模块-->
        <module>kernel-d-sms</module>

        <!--定时任务模块-->
        <module>kernel-d-timer</module>

        <!--树节点操作-->
        <module>kernel-d-tree</module>

        <!--参数校验模块-->
        <module>kernel-d-validator</module>

        <!--wrapper包装模块-->
        <module>kernel-d-wrapper</module>

        <!--系统监控模块-->
        <module>kernel-o-monitor</module>

        <!--演示环境-->
        <module>kernel-s-demo</module>

        <!--字典业务模块-->
        <module>kernel-s-dict</module>

        <!--分组业务-->
        <module>kernel-s-group</module>

        <!--业务点击统计-->
        <module>kernel-s-stat</module>

        <!--系统管理基础业务-->
        <module>kernel-s-system</module>

        <!--用户收藏模块-->
        <module>kernel-s-user-favorite</module>

    </modules>

    <properties>
        <java.version>17</java.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <roses.version>8.3.3</roses.version>
        <hutool.version>5.8.29</hutool.version>
        <fastjson.version>2.0.52</fastjson.version>
        <lombok.versin>1.18.34</lombok.versin>
        <mp.version>3.5.7</mp.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <druid.version>1.2.22</druid.version>
        <jwt.version>0.9.1</jwt.version>
        <java.mail.version>1.4.7</java.mail.version>
        <aliyun.oss.version>3.8.0</aliyun.oss.version>
        <qcloud.cos.version>5.6.23</qcloud.cos.version>
        <qcloud.commons.logging.version>1.1.3</qcloud.commons.logging.version>
        <aliyun.sms.sdk.core>4.4.6</aliyun.sms.sdk.core>
        <aliyun.sms.sdk.ecs>4.17.6</aliyun.sms.sdk.ecs>
        <qingyun.oss.version>2.5.1</qingyun.oss.version>
        <qcloud.sms>3.1.57</qcloud.sms>
        <elasticsearch.version>7.9.2</elasticsearch.version>
        <aws.sdk.version>1.11.106</aws.sdk.version>
        <minio.version>3.0.10</minio.version>
        <rocketmq.version>4.5.2</rocketmq.version>
        <easy.captcha>1.6.2</easy.captcha>
        <guns.groovy.version>3.0.22</guns.groovy.version>
        <oshi.version>5.7.1</oshi.version>
        <beetl.version>3.3.2.RELEASE</beetl.version>
        <getty.version>1.4.9</getty.version>
        <aliyun.java.sdk.core>3.0.0</aliyun.java.sdk.core>
        <aliyun.java.sdk.dm>3.1.0</aliyun.java.sdk.dm>
        <activation.version>1.1.1</activation.version>
        <easy.excel.version>3.2.1</easy.excel.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <openfeign.version>2.2.6.RELEASE</openfeign.version>
        <bcprov.version>1.78.1</bcprov.version>
        <flyway.version>7.1.1</flyway.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!--工具类-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--json解析-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.versin}</version>
            </dependency>

            <!--mybatis-plus dao框架-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mp.version}</version>
            </dependency>

            <!--数据库驱动-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <!--数据库连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!--jwt token-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!--java邮件发送-->
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${java.mail.version}</version>
            </dependency>

            <!--阿里云上传文件客户端-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>

            <!--青云的OSS-->
            <dependency>
                <groupId>com.yunify</groupId>
                <artifactId>qingstor.sdk.java</artifactId>
                <version>${qingyun.oss.version}</version>
            </dependency>

            <!--腾讯云上传文件客户端，用的时候手动引入-->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${qcloud.cos.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${qcloud.commons.logging.version}</version>
            </dependency>

            <!--阿里云短信发送的sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun.sms.sdk.core}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ecs</artifactId>
                <version>${aliyun.sms.sdk.ecs}</version>
            </dependency>

            <!--腾讯云短信sdk-->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${qcloud.sms}</version>
            </dependency>

            <!--MinIo客户端sdk-->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>${aws.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- EasyCaptcha图形验证码 -->
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${easy.captcha}</version>
            </dependency>

            <!-- groovy动态代码执行 -->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>${guns.groovy.version}</version>
                <type>pom</type>
            </dependency>

            <!--硬件信息获取-->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <release>17</release>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--推送到中央仓库用-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-javadoc-plugin</artifactId>-->
<!--                <version>2.9.1</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>jar</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <additionalparam>-Xdoclint:none</additionalparam>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-gpg-plugin</artifactId>-->
<!--                <version>3.2.7</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>sign-artifacts</id>-->
<!--                        <phase>verify</phase>-->
<!--                        <goals>-->
<!--                            <goal>sign</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.sonatype.central</groupId>-->
<!--                <artifactId>central-publishing-maven-plugin</artifactId>-->
<!--                <version>0.6.0</version>-->
<!--                <extensions>true</extensions>-->
<!--                <configuration>-->
<!--                    <publishingServerId>central</publishingServerId>-->
<!--                </configuration>-->
<!--            </plugin>-->

        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <developers>
        <developer>
            <name>fengshuonan</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <scm>
        <connection>scm:*************:stylefeng/roses.git</connection>
        <developerConnection>scm:*************:stylefeng/roses.git</developerConnection>
        <url>https://gitee.com/stylefeng/roses</url>
    </scm>

</project>
