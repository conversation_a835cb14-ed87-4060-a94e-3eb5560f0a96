package cn.stylefeng.roses.kernel.websocket.api.encoder;

import com.alibaba.fastjson.JSON;
import jakarta.websocket.EncodeException;
import jakarta.websocket.Encoder;

/**
 * 基于fastjson的对象encoder，返回json数据
 *
 * <AUTHOR>
 * @since 2024/9/30 10:49
 */
public class FastjsonObjectEncoder implements Encoder.Text<Object> {

    @Override
    public String encode(Object myObject) throws EncodeException {
        try {
            return JSON.toJSONString(myObject);
        } catch (Exception e) {
            throw new EncodeException(myObject, "Object encoding failed", e);
        }
    }

}
