package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 供应商统计信息响应参数
 *
 * <AUTHOR>
 * @since 2025/07/28 16:00
 */
@Data
public class SupplierStatsResponse {

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 经营方式
     */
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 关联商品数量
     */
    @ChineseDescription("关联商品数量")
    private Long productCount;

    /**
     * 总采购金额
     */
    @ChineseDescription("总采购金额")
    private BigDecimal totalPurchaseAmount;

    /**
     * 平均采购价格
     */
    @ChineseDescription("平均采购价格")
    private BigDecimal avgPurchasePrice;

    /**
     * 最后采购日期
     */
    @ChineseDescription("最后采购日期")
    private LocalDate lastPurchaseDate;

}