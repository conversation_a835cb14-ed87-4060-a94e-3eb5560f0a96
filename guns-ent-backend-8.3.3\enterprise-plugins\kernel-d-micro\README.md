[TOC]

# 1. 单体项目改造微服务步骤

打开Guns主项目，这个过程需要手动一步步执行。

## 1.1 改造pom.xml文件，将内存缓存改为Redis缓存

找到原有的系统配置和缓存模块，如下：

![](.README_images/c7cdbbdc.png)

将他们改为新的Redis配置：

```xml
<!--缓存，默认增加内存缓存-->
<dependency>
    <groupId>com.javaguns.roses</groupId>
    <artifactId>cache-spring-boot-starter-redis</artifactId>
    <version>${roses.kernel.version}</version>
</dependency>
```

## 1.2 引入微服务核心包

引入之前请确保微服务核心包已经上传到公司的仓库，或者使用`mvn install`命令安装到本地仓库。

```xml
<!-- 微服务模块 -->
<dependency>
    <groupId>com.javaguns.ent</groupId>
    <artifactId>micro-spring-boot-starter</artifactId>
    <version>${roses.kernel.version}</version>
</dependency>
```

## 1.3 application.yml中scanner开启urlWithAppCode开关

````yml
scanner:
  open: true
  urlWithAppCode: true

# nacos配置
spring:
  cloud:
    nacos:
      discovery:
        enabled: true
        register-enabled: true
        watch-delay: 1000

# feign远程调用配置
feign:
  sentinel:
    enabled: true
  client:
    config:
      # 全局配置
      default:
        # NONE不记录任何日志--BASIC仅请求方法URL,状态码执行时间等--HEADERS在BASIC基础上记录header等--FULL记录所有
        loggerLevel: full
        connectTimeout: 500  #连接超时时间
        readTimeout: 5000    #连接超时时间
        errorDecoder: cn.stylefeng.roses.kernel.micro.core.feign.GunsFeignErrorDecoder
        requestInterceptors:
          - cn.stylefeng.roses.kernel.micro.core.feign.GunsFeignHeaderProcessInterceptor
  httpclient:
    # 让feign使用apache httpclient做请求；而不是默认的urlConnection
    enabled: true
    # feign的最大连接数
    max-connections: 200
    # feign单个路径的最大连接数
    max-connections-per-route: 50

# actuator配置，给spring boot admin监控用
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

````

## 1.4 application-local.yml增加nacos和sentinel控制台配置

这里使用的是local profile，如过是dev或者prod环境，需要在对应的配置文件中增加即可。

```yml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0

  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    sentinel:
      transport:
        dashboard: localhost:8999
```

## 1.5 剔除两个权限拦截器

微服务版本鉴权放在网关了，所以剔除掉原有项目的两个拦截器

SpringMvcConfiguration中配置addInterceptors方法，删除两个拦截器的配置

剔除后的配置如下：

![](.README_images/764694f5.png)

`TokenAndPermissionInterceptor`类上去除@Component注解，别把这个类放在Spring容器中了

## 1.6 前端配置修改

vite.config.js文件中，修改rewrite如下，让原有的/api请求，转发到/guns请求，因为经过了网关。

![](.README_images/faff8b8d.png)

## 1.7 更改前端访问后端的端口

vue前端项目中，修改.env.development文件中VITE_API_URL=http://localhost:82

这样就可以让前端访问后端的接口经过网关了

## 1.8 注意事项（重要）

现在最新的vue3版本，当第一次使用系统，会自动初始化系统需要的秘钥，jwt秘钥会随机生成，从新生成后，可能导致和网关的jwt秘钥不一致

当项目自动初始化秘钥完毕后，请执行如下语句，查看一下jwt秘钥是多少：

```sql
select config_value from sys_config where config_code = 'SYS_AUTH_JWT_SECRET' 
```

然后将查询结果，设置到micro-project-gateway网关项目中resources/application-local.yml的`jwt.jwtSecret`的配置，然后重启一下网关就行

**网关上因为有认证功能，所以要校验jwt token的正确性，所以需要用到jwt secret。**

# 2. 微服务开资源扫描

如果新的微服务要开启资源扫描，需要往新的微服务中加入scanner依赖

可以参考`micro-project-feign-consumer`这个项目，有相关的配置

```xml
<!--资源扫描-->
<dependency>
    <groupId>com.javaguns.roses</groupId>
    <artifactId>scanner-spring-boot-starter</artifactId>
    <version>${roses.version}</version>
</dependency>
```

业务模块新增consumer

```java
import cn.stylefeng.roses.kernel.scanner.api.ResourceReportApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 资源汇报的消费者
 *
 * <AUTHOR>
 * @date 2021/8/26 16:31
 */
@FeignClient(name = "guns")
public interface ResourceReportConsumer extends ResourceReportApi {

}
```