package cn.stylefeng.roses.ent.mobile.manage.service.impl;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.stylefeng.roses.ent.mobile.manage.factory.AddressBookFactory;
import cn.stylefeng.roses.ent.mobile.manage.mapper.OrgStatMapper;
import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.*;
import cn.stylefeng.roses.ent.mobile.manage.props.InviteJoinProperties;
import cn.stylefeng.roses.ent.mobile.manage.service.AddressBookService;
import cn.stylefeng.roses.ent.mobile.manage.service.CommonSelectBusinessService;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.db.mp.datascope.UserRoleDataScopeApi;
import cn.stylefeng.roses.kernel.db.mp.datascope.config.DataScopeConfig;
import cn.stylefeng.roses.kernel.db.mp.datascope.holder.DataScopeHolder;
import cn.stylefeng.roses.kernel.rule.enums.permission.DataScopeTypeEnum;
import cn.stylefeng.roses.kernel.rule.util.HttpServletUtil;
import cn.stylefeng.roses.kernel.sys.api.pojo.org.CompanyDeptDTO;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import cn.stylefeng.roses.kernel.sys.modular.position.entity.HrPosition;
import cn.stylefeng.roses.kernel.sys.modular.position.service.HrPositionService;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserOrgService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通讯录业务实现
 *
 * <AUTHOR>
 * @since 2024/3/21 22:28
 */
@Service
public class AddressBookServiceImpl implements AddressBookService {

    @Resource
    private HrOrganizationService hrOrganizationService;

    @Resource
    private SysUserOrgService sysUserOrgService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private UserRoleDataScopeApi userRoleDataScopeApi;

    @Resource
    private HrPositionService hrPositionService;

    @Resource
    private OrgStatMapper orgStatMapper;

    @Resource
    private CommonSelectBusinessService commonSelectBusinessService;

    @Resource
    private InviteJoinProperties inviteJoinProperties;

    @Override
    public List<AddressBookItem> getAddressBookItemList(AddressBookRequest addressBookRequest) {

        // 获取用户数据范围配置
        DataScopeConfig userRoleDataScopeConfig = userRoleDataScopeApi.getUserPointDataScopeConfig(DataScopeTypeEnum.COMPANY_WITH_CHILD);
        List<HrOrganization> orgList;
        List<SysUserOrg> sysUserOrgList;
        List<OrgUserStatTotal> orgStatList = null;

        try {
            DataScopeHolder.set(userRoleDataScopeConfig);

            // 1. 查询指定机构下子一级的所有公司和部门
            orgList = this.commonSelectBusinessService.getOrgList(addressBookRequest.getOrgId());

            // 1.1 获取机构的人员统计信息
            if (ObjectUtil.isNotEmpty(orgList)) {
                userRoleDataScopeConfig.setOrgIdFieldName("o.org_id");
                orgStatList = orgStatMapper.getOrgStatTotalList(orgList.stream().map(HrOrganization::getOrgId).collect(Collectors.toList()));
            }

            // 2. 查询这个机构下的所有人员和人员的职位
            sysUserOrgList = this.commonSelectBusinessService.getPersonAndPositionList(addressBookRequest.getOrgId());
        } finally {
            DataScopeHolder.remove();
        }

        // 人员信息为空，则直接返回机构列表
        if (ObjectUtil.isEmpty(sysUserOrgList)) {
            return AddressBookFactory.createAddressBookItemList(orgList, orgStatList, null, null, null);
        }

        // 3. 根据人员id，查询到所有的人员信息，包含头像，姓名，是否是管理员，用户状态
        List<SysUser> sysUserList = this.commonSelectBusinessService.getUserDetailList(addressBookRequest.getSearchText(), sysUserOrgList);

        // 4. 获取这些人员的职位信息
        List<HrPosition> positionList = this.getPositionDetailList(sysUserOrgList);

        // 组装返回的数据
        return AddressBookFactory.createAddressBookItemList(orgList, orgStatList, sysUserOrgList, sysUserList, positionList);
    }

    @Override
    public AddressBookUserDetail getUserInfo(AddressBookUserRequest addressBookUserRequest) {

        // 查询具体用户信息
        SysUserOrg userOrgInfo = sysUserOrgService.getUserOrgInfo(addressBookUserRequest.getUserId(), addressBookUserRequest.getOrgId());

        // 获取机构对应的公司和部门信息
        CompanyDeptDTO companyDeptInfo = hrOrganizationService.getCompanyDeptInfo(addressBookUserRequest.getOrgId());

        // 获取职位信息
        String positionName = hrPositionService.getPositionName(userOrgInfo.getPositionId());

        // 获取用户的基本信息
        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserLambdaQueryWrapper.eq(SysUser::getUserId, addressBookUserRequest.getUserId());
        sysUserLambdaQueryWrapper.select(SysUser::getAvatar, SysUser::getRealName, SysUser::getSex, SysUser::getEmail, SysUser::getPhone, SysUser::getEmployeeNumber);
        SysUser sysUser = this.sysUserService.getOne(sysUserLambdaQueryWrapper);

        // 组装返回的数据
        return AddressBookFactory.createUserDetail(sysUser, companyDeptInfo, positionName);
    }

    @Override
    public void getInviteQrCodePicture(QrCodeCreateRequest qrCodeCreateRequest) {
        // 获取输出流
        HttpServletResponse response = HttpServletUtil.getResponse();

        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            QrCodeUtil.generate(this.getInviteUrl(qrCodeCreateRequest), 300, 300, ImgUtil.IMAGE_TYPE_JPG, outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            IoUtil.close(outputStream);
        }
    }

    @Override
    public String getInviteUrl(QrCodeCreateRequest qrCodeCreateRequest) {
        // 获取前端的访问的url
        String frontUrl = inviteJoinProperties.getFrontUrl();

        // 拼接上邀请人的用户id，和邀请加入的部门
        return frontUrl + "?userId=" + LoginContext.me().getLoginUser().getUserId() + "&orgId=" + qrCodeCreateRequest.getOrgId();
    }

    /**
     * 获取职位信息的详情
     *
     * <AUTHOR>
     * @since 2024-03-29 11:46
     */
    private List<HrPosition> getPositionDetailList(List<SysUserOrg> sysUserOrgList) {

        // 获取所有职位id列表
        List<Long> positionIdList = sysUserOrgList.stream().map(SysUserOrg::getPositionId).collect(Collectors.toList());

        LambdaQueryWrapper<HrPosition> positionWrapper = new LambdaQueryWrapper<>();
        positionWrapper.in(HrPosition::getPositionId, positionIdList);
        positionWrapper.select(HrPosition::getPositionId, HrPosition::getPositionName);
        return hrPositionService.list(positionWrapper);
    }

}
