System.register(["./index-legacy-cbae9bf3.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./interface-form-legacy-85db84ba.js","./interface-add-edit-legacy-91dc169c.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,l){"use strict";var a,t,n,i,s,o,c,d,u,r,v,h,f,p,y,_,C,g,b,x,k,w,m,I,j,R,S;return{setters:[e=>{a=e._},e=>{t=e.C,n=e._},e=>{i=e.r,s=e.o,o=e.k,c=e.a,d=e.c,u=e.b,r=e.d,v=e.w,h=e.g,f=e.t,p=e.h,y=e.f,_=e.M,C=e.E,g=e.m,b=e.I,x=e.l,k=e.B,w=e.n,m=e.p,I=e.q,j=e.D},e=>{R=e.I},e=>{S=e.default},null,null,null],execute:function(){const l={class:"guns-layout"},T={class:"guns-layout-content"},z={class:"guns-layout"},A={class:"guns-layout-content-application"},B={class:"content-mian"},E={class:"content-mian-header"},N={class:"header-content"},U={class:"header-content-left"},D={class:"header-content-right"},L={class:"content-mian-body"},O={class:"table-content"},J=["onClick"];e("default",Object.assign({name:"ApiAuthInterface"},{__name:"index",setup(e){const M=i([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>P.value.tableIndex+e},{dataIndex:"resourceName",title:"接口名称",ellipsis:!0,width:150,isShow:!0},{dataIndex:"resourceCode",title:"接口编码",width:150,isShow:!0},{dataIndex:"url",title:"请求路径",width:150,isShow:!0},{dataIndex:"httpMethod",title:"请求方式",width:100,isShow:!0},{dataIndex:"createTime",title:"创建时间",width:150,isShow:!0},{key:"action",title:"操作",fixed:"right",width:100,isShow:!0}]),P=i(null),W=i({searchText:""}),q=i(!1),F=i(null),H=i(!1),G=i("API_AUTH_INTERFACE");s((()=>{K()}));const K=()=>{t.getUserConfig({fieldBusinessCode:G.value}).then((e=>{e.tableWidthJson&&(M.value=JSON.parse(e.tableWidthJson))}))},Q=({key:e})=>{"1"==e?q.value=!0:"2"==e&&Z()},V=()=>{P.value.reload()},X=()=>{W.value.searchText="",V()},Y=e=>{F.value=e,H.value=!0},Z=()=>{if(P.value.selectedRowList&&0==P.value.selectedRowList.length)return g.warning("请选择需要删除的接口");_.confirm({title:"提示",content:"确定要删除选中的接口吗?",icon:r(C),maskClosable:!0,onOk:async()=>{const e=await R.batchDelete({apiClientResourceIdList:P.value.selectedRowList});g.success(e.message),V()}})};return(e,t)=>{const i=b,s=x,K=k,Z=w,$=o("plus-outlined"),ee=m,le=I,ae=o("small-dash-outlined"),te=j,ne=n,ie=a;return c(),d("div",l,[u("div",T,[u("div",z,[u("div",A,[u("div",B,[u("div",E,[u("div",N,[u("div",U,[r(Z,{size:16},{default:v((()=>[r(s,{value:W.value.searchText,"onUpdate:value":t[0]||(t[0]=e=>W.value.searchText=e),placeholder:"接口编码（回车搜索）",onPressEnter:V,class:"search-input"},{prefix:v((()=>[r(i,{iconClass:"icon-opt-search"})])),_:1},8,["value"]),r(K,{class:"border-radius",onClick:X},{default:v((()=>t[5]||(t[5]=[h("重置")]))),_:1,__:[5]})])),_:1})]),u("div",D,[r(Z,{size:16},{default:v((()=>[r(K,{type:"primary",class:"border-radius",onClick:t[1]||(t[1]=e=>Y())},{default:v((()=>[r($),t[6]||(t[6]=h("新建"))])),_:1,__:[6]}),r(te,null,{overlay:v((()=>[r(le,{onClick:Q},{default:v((()=>[r(ee,{key:"1"},{default:v((()=>[r(i,{iconClass:"icon-opt-zidingyilie",color:"#60666b"}),t[7]||(t[7]=u("span",null,"自定义列",-1))])),_:1,__:[7]}),u("div",null,[r(ee,{key:"2"},{default:v((()=>[r(i,{iconClass:"icon-opt-shanchu",color:"#60666b"}),t[8]||(t[8]=u("span",null,"批量删除",-1))])),_:1,__:[8]})])])),_:1})])),default:v((()=>[r(K,{class:"border-radius"},{default:v((()=>[t[9]||(t[9]=h(" 更多 ")),r(ae)])),_:1,__:[9]})])),_:1})])),_:1})])])]),u("div",L,[u("div",O,[r(ne,{columns:M.value,where:W.value,rowId:"apiClientResourceId",ref_key:"tableRef",ref:P,url:"/apiEndpoint/page"},{bodyCell:v((({column:e,record:l})=>["resourceName"==e.dataIndex?(c(),d("a",{key:0,onClick:e=>Y(l)},f(l.resourceName),9,J)):p("",!0),"action"==e.key?(c(),y(Z,{key:1,size:16},{default:v((()=>[r(i,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>Y(l)},null,8,["onClick"]),r(i,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{_.confirm({title:"提示",content:"确定要删除选中的接口吗?",icon:r(C),maskClosable:!0,onOk:async()=>{const l=await R.delete({apiClientResourceId:e.apiClientResourceId});g.success(l.message),V()}})})(l)},null,8,["onClick"])])),_:2},1024)):p("",!0)])),_:1},8,["columns","where"])])])])])])]),q.value?(c(),y(ie,{key:0,visible:q.value,"onUpdate:visible":t[2]||(t[2]=e=>q.value=e),data:M.value,onDone:t[3]||(t[3]=e=>M.value=e),fieldBusinessCode:G.value},null,8,["visible","data","fieldBusinessCode"])):p("",!0),H.value?(c(),y(S,{key:1,visible:H.value,"onUpdate:visible":t[4]||(t[4]=e=>H.value=e),data:F.value,onDone:V},null,8,["visible","data"])):p("",!0)])}}}))}}}));
