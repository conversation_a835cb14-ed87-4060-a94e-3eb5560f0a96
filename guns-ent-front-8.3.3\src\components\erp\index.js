
/**
 * ERP组件统一导出
 *
 * <AUTHOR>
 * @since 2025/07/30
 */

import SupplierSelector from './SupplierSelector.vue';
import ProductSelector from './ProductSelector.vue';

// 导出所有组件
export {
  SupplierSelector,
  ProductSelector
};

// 默认导出
export default {
  SupplierSelector,
  ProductSelector
};

// Vue插件安装函数
export const install = (app) => {
  app.component('SupplierSelector', SupplierSelector);
  app.component('ProductSelector', ProductSelector);
};