/*
 * Copyright [2020-2030] [https://www.stylefeng.cn]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Guns采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Guns源码头部的版权声明。
 * 3.请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://gitee.com/stylefeng/guns
 * 5.在修改包名，模块名称，项目代码等时，请注明软件出处 https://gitee.com/stylefeng/guns
 * 6.若您的项目无法满足以上几点，可申请商业授权
 */
package cn.stylefeng.roses.kernel.config.modular.controller;

import cn.stylefeng.roses.kernel.config.modular.pojo.newconfig.StorageConfig;
import cn.stylefeng.roses.kernel.config.modular.service.NewConfigService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新配置设置-文件存储配置
 *
 * <AUTHOR>
 * @since 2024/8/31 16:34
 */
@RestController
@ApiResource(name = "新配置设置-文件存储配置", requiredPermission = true, requirePermissionCode = SysConfigTypeController.SYS_CONFIG)
public class NewFileConfigController {

    @Resource
    private NewConfigService newConfigService;

    /**
     * 获取文件存储配置
     *
     * <AUTHOR>
     * @since 2024/8/31 17:05
     */
    @GetResource(name = "获取文件存储配置", path = "/new/sysConfig/getFileConfig")
    public ResponseData<StorageConfig> getFileConfig() {
        StorageConfig storageConfig = newConfigService.getStorageConfig();
        return new SuccessResponseData<>(storageConfig);
    }

    /**
     * 更新文件配置
     *
     * <AUTHOR>
     * @since 2024/8/31 18:00
     */
    @PostResource(name = "更新文件配置", path = "/new/sysConfig/updateFileConfig")
    public ResponseData<?> updateFileConfig(@RequestBody @Validated StorageConfig storageConfig) {
        newConfigService.updateFileConfig(storageConfig);
        return new SuccessResponseData<>();
    }

}


