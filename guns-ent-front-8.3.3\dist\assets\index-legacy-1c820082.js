System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./package-add-edit-legacy-9e2fc0ac.js","./package-tree-legacy-22410275.js","./index-legacy-94a6fc23.js"],(function(e,t){"use strict";var a,n,c,d,l,o,i,s,u,h,r,p,v,g,k,m,y,b,f,x,w,_;return{setters:[e=>{a=e._,n=e.r,c=e.a,d=e.c,l=e.b,o=e.d,i=e.w,s=e.g,u=e.F,h=e.e,r=e.t,p=e.f,v=e.h,g=e.aR,k=e.aS,m=e.m,y=e.C,b=e.i,f=e.a5,x=e.S},null,null,null,e=>{w=e.P},e=>{_=e.default},null],execute:function(){var t=document.createElement("style");t.textContent=".permission[data-v-be3357bc]{width:100%;height:100%;overflow-y:auto}.right-empty[data-v-be3357bc]{padding-top:350px}.use-content[data-v-be3357bc]{width:100%;height:100%}.use-content .content-header[data-v-be3357bc]{height:22px}.use-content .content-bottom[data-v-be3357bc]{width:100%;margin-top:20px;overflow-y:auto;height:calc(100% - 32px)}.use-content .content-bottom .bottom-item[data-v-be3357bc]{margin-bottom:20px}.use-content .content-bottom .bottom-item .bottom-item-name .title[data-v-be3357bc]{font-size:16px;font-weight:700;margin-right:10px}\n",document.head.appendChild(t);const C={class:"guns-layout"},I={class:"guns-layout-sidebar p-t-12 bgColor",style:{width:"292px"}},P={class:"sidebar-content"},j={class:"guns-layout-content",style:{width:"calc(100% - 292px)"}},L={class:"guns-layout"},U={class:"guns-layout-content-application"},S={class:"content-mian"},A={class:"content-mian-body"},E={class:"table-content"},N={key:0,class:"use-content"},K={class:"content-header"},R={class:"content-bottom"},T={class:"bottom-item-name"},z={class:"title"},F={class:"table"};e("default",a(Object.assign({name:"TenantPackage"},{__name:"index",setup(e){const t=n(""),a=n(null),O=n(!1),q=n([]),B=n([{title:"页面",width:200,dataIndex:"page"},{title:"功能",dataIndex:"use"}]),D=e=>{t.value=e,e?G():a.value=null},G=()=>{q.value=[],O.value=!0,w.getPackageAuth({packageId:t.value}).then((e=>{H(e.appPermissionList),a.value=e})).finally((()=>O.value=!1))},H=e=>{(null==e?void 0:e.length)>0&&e.forEach((e=>{var t;(null==e||null===(t=e.children)||void 0===t?void 0:t.length)>0&&(q.value.push(e.nodeId),H(e.children))}))},J=(e,t,n,c,d,l)=>{t.checked=e.target.checked;const o=M([l]);c&&V(e.target.checked,n?t[n]:[t]),l&&(o.find((e=>0==e.checked))?l.checked=!1:l.checked=!0),d&&(a.value.appPermissionList.find((e=>0==e.checked))?a.value.checked=!1:a.value.checked=!0),Q(t)},M=e=>{const t=[];return e.forEach((function e(a){null!=a&&a.leafFlag&&(t.push(a),null!=a&&a.functionList&&Array.isArray(a.functionList)&&t.push(...a.functionList)),null!=a&&a.children&&Array.isArray(a.children)&&a.children.forEach(e)})),t},Q=e=>{O.value=!0;let a={checked:e.checked,packageId:t.value,nodeId:e.nodeId?e.nodeId:"",permissionNodeType:e.permissionNodeType};w.setPackagePermission(a).then((e=>{m.success(e.message)})).finally((()=>O.value=!1))},V=(e,t)=>{t&&t.length>0&&t.forEach((t=>{t.checked=e,t.children&&t.children.length>0&&V(e,t.children)}))};return(e,n)=>{const m=y,w=b,G=f,H=x;return c(),d("div",C,[l("div",I,[l("div",P,[o(_,{onTreeSelect:D})])]),l("div",j,[l("div",L,[l("div",U,[l("div",S,[l("div",A,[l("div",E,[o(H,{spinning:O.value,delay:100},{default:i((()=>[t.value&&a.value?(c(),d("div",N,[l("div",K,[o(m,{checked:a.value.checked,"onUpdate:checked":n[0]||(n[0]=e=>a.value.checked=e),onClick:n[1]||(n[1]=e=>((e,t)=>{J(e,t,"appPermissionList",!0)})(e,a.value))},{default:i((()=>n[3]||(n[3]=[s("所有权限")]))),_:1,__:[3]},8,["checked"])]),l("div",R,[(c(!0),d(u,null,h(a.value.appPermissionList,((e,t)=>(c(),d("div",{class:"bottom-item",key:t},[l("div",T,[l("span",z,"应用："+r(e.nodeName),1),o(m,{checked:e.checked,"onUpdate:checked":t=>e.checked=t,onClick:t=>((e,t)=>{J(e,t,"",!0,!0)})(t,e)},{default:i((()=>n[4]||(n[4]=[s("全选")]))),_:2,__:[4]},1032,["checked","onUpdate:checked","onClick"])]),l("div",F,[o(w,{dataSource:e.children,columns:B.value,expandedRowKeys:q.value,"onUpdate:expandedRowKeys":n[2]||(n[2]=e=>q.value=e),pagination:!1,checkStrictly:!0,rowKey:"nodeId",bordered:"",size:"small"},{bodyCell:i((({column:t,record:a})=>["page"===t.dataIndex?(c(),p(m,{key:0,checked:a.checked,"onUpdate:checked":e=>a.checked=e,onChange:t=>((e,t,a)=>{J(e,t,"",!0,!0,a)})(t,a,e)},{default:i((()=>[s(r(a.nodeName),1)])),_:2},1032,["checked","onUpdate:checked","onChange"])):"use"===t.dataIndex?(c(!0),d(u,{key:1},h(a.functionList,(t=>(c(),p(m,{checked:t.checked,"onUpdate:checked":e=>t.checked=e,onChange:a=>((e,t,a)=>{J(e,t,"",!1,!0,a)})(a,t,e),key:t.nodeId},{default:i((()=>[s(r(t.nodeName),1)])),_:2},1032,["checked","onUpdate:checked","onChange"])))),128)):v("",!0)])),_:2},1032,["dataSource","columns","expandedRowKeys"])])])))),128))])])):v("",!0),g(o(G,{class:"right-empty"},null,512),[[k,!t.value&&!a.value]])])),_:1},8,["spinning"])])])])])])])])}}}),[["__scopeId","data-v-be3357bc"]]))}}}));
