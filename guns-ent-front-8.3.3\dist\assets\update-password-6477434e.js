import{_ as x,H as i,r as B,s as c,a as F,c as U,d as a,w as o,aH as d,at as p,g as j,bj as k,bJ as q,m,bP as C,bQ as I,aa as A,u as H,B as N}from"./index-18a1ea24.js";const V={class:"form"},h={__name:"update-password",setup(E){const w=i.useForm,_=B(!1),s=c({password:"",newPassword:"",password2:""}),f=c({password:[{required:!0,type:"string",message:"\u8BF7\u8F93\u5165\u65E7\u5BC6\u7801",trigger:"blur"}],newPassword:[{required:!0,type:"string",message:"\u8BF7\u8F93\u5165\u65B0\u5BC6\u7801",trigger:"blur"}],password2:[{required:!0,type:"string",trigger:"blur",validator:async(t,e)=>e?e!==s.newPassword?Promise.reject("\u4E24\u6B21\u8F93\u5165\u5BC6\u7801\u4E0D\u4E00\u81F4"):Promise.resolve():Promise.reject("\u8BF7\u518D\u6B21\u8F93\u5165\u65B0\u5BC6\u7801")}]}),{resetFields:g,validate:v,validateInfos:l}=w(s,f),b=()=>{v().then(()=>{_.value=!0;let t=k(s);delete t.password2,q.updatePassword(t).then(async e=>{e.success?(m.success("\u4FEE\u6539\u6210\u529F\uFF01"),g(),await C.logout(),I()):m.error(e.message)}).finally(()=>{_.value=!1})})};return(t,e)=>{const u=A,n=H,P=N,y=i;return F(),U("div",V,[a(y,{"label-col":{sm:{span:6}},"wrapper-col":{sm:{span:18}}},{default:o(()=>[a(n,d({label:"\u65E7\u5BC6\u7801"},p(l).password),{default:o(()=>[a(u,{value:s.password,"onUpdate:value":e[0]||(e[0]=r=>s.password=r),placeholder:"\u8BF7\u8F93\u5165\u65E7\u5BC6\u7801"},null,8,["value"])]),_:1},16),a(n,d({label:"\u65B0\u5BC6\u7801"},p(l).newPassword),{default:o(()=>[a(u,{value:s.newPassword,"onUpdate:value":e[1]||(e[1]=r=>s.newPassword=r),placeholder:"\u8BF7\u8F93\u5165\u65B0\u5BC6\u7801"},null,8,["value"])]),_:1},16),a(n,d({label:"\u91CD\u590D\u5BC6\u7801"},p(l).password2),{default:o(()=>[a(u,{value:s.password2,"onUpdate:value":e[2]||(e[2]=r=>s.password2=r),placeholder:"\u8BF7\u518D\u6B21\u8F93\u5165\u65B0\u5BC6\u7801"},null,8,["value"])]),_:1},16),a(n,{label:" ",class:"save-btn"},{default:o(()=>[a(P,{type:"primary","html-type":"submit",onClick:b},{default:o(()=>e[3]||(e[3]=[j("\u4FDD\u5B58")])),_:1,__:[3]})]),_:1})]),_:1})])}}},L=x(h,[["__scopeId","data-v-97e30c71"]]);export{L as default};
