import{r as l,o as b,a as _,f as g,w as y,d as h,m as k,M as x}from"./index-18a1ea24.js";import U from"./attr-form-2d967afb.js";import{T as m}from"./ThemeTemplateFieldApi-b2a7ece4.js";/* empty css              */const F={__name:"attr-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(p,{emit:v}){const i=p,d=v,s=l(!1),a=l(!1),t=l({positionSort:1e3}),f=l(null);b(()=>{i.data?(a.value=!0,t.value=Object.assign({},i.data)):a.value=!1});const n=o=>{d("update:visible",o)},c=async()=>{f.value.$refs.formRef.validate().then(async o=>{if(o){s.value=!0;let e=null;a.value?e=m.edit(t.value):e=m.add(t.value),e.then(async r=>{s.value=!1,k.success(r.message),n(!1),d("done")}).catch(()=>{s.value=!1})}})};return(o,e)=>{const r=x;return _(),g(r,{width:700,maskClosable:!1,visible:i.visible,"confirm-loading":s.value,forceRender:!0,title:a.value?"\u7F16\u8F91\u5C5E\u6027":"\u65B0\u5EFA\u5C5E\u6027","body-style":{paddingBottom:"8px",height:"600px",overflowY:"auto"},"onUpdate:visible":n,onOk:c,onClose:e[1]||(e[1]=u=>n(!1))},{default:y(()=>[h(U,{form:t.value,"onUpdate:form":e[0]||(e[0]=u=>t.value=u),ref_key:"attrFormRef",ref:f,isUpdate:a.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}};export{F as default};
