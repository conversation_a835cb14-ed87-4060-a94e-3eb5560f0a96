package cn.stylefeng.roses.kernel.impexp.user.datavalis;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

/**
 * 长整型的校验
 *
 * <AUTHOR>
 * @since 2024/2/6 21:54
 */
public class BigintValidator implements BaseValidator {

    @Override
    public ExcelLineParseResult getValidateResult(String originValue) {

        if (ObjectUtil.isEmpty(originValue)) {
            return new ExcelLineParseResult(originValue);
        }

        try {
            Long.valueOf(originValue);
            return new ExcelLineParseResult(true, originValue, originValue);
        } catch (NumberFormatException e) {
            return new ExcelLineParseResult(false, originValue, originValue, "非正确的长整型值");
        }
    }

}
