package cn.stylefeng.roses.ent.mobile.invite.factory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.ent.mobile.invite.entity.SysInviteUser;
import cn.stylefeng.roses.ent.mobile.invite.enums.InviteStatusEnum;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteUserRequest;
import cn.stylefeng.roses.kernel.sys.api.enums.message.MessageTypeEnum;
import cn.stylefeng.roses.kernel.sys.api.enums.message.PriorityLevelEnum;
import cn.stylefeng.roses.kernel.sys.api.pojo.message.MessageSendDTO;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import java.util.HashMap;

/**
 * 邀请用户的创建工厂
 *
 * <AUTHOR>
 * @since 2024-04-08 19:35
 */
public class InviteUserFactory {

    /**
     * 邀请用户加入的消息类型
     */
    private static final String MESSAGE_TYPE = "INVITE_USER";

    /**
     * 创建邀请用户
     *
     * <AUTHOR>
     * @since 2024-04-08 19:36
     */
    public static SysInviteUser createInviteUser(SysInviteUserRequest sysInviteUserRequest) {
        SysInviteUser sysInviteUser = new SysInviteUser();

        sysInviteUser.setInviteUserId(IdWorker.getId());

        // 设置状态待审核
        sysInviteUser.setInviteStatus(InviteStatusEnum.WAIT.getCode());

        sysInviteUser.setOrgId(sysInviteUserRequest.getOrgId());

        sysInviteUser.setFromUserId(sysInviteUserRequest.getFromUserId());

        sysInviteUser.setRealName(sysInviteUserRequest.getRealName());

        sysInviteUser.setPhoneNumber(sysInviteUserRequest.getPhoneNumber());

        sysInviteUser.setPhoneValidateNumber(sysInviteUserRequest.getPhoneValidateNumber());

        sysInviteUser.setApplyReason(sysInviteUserRequest.getApplyReason());

        return sysInviteUser;
    }

    /**
     * 创建给管理员发送的邀请用户的消息通知
     *
     * <AUTHOR>
     * @since 2024-04-09 14:02
     */
    public static MessageSendDTO createInviteUserMessage(SysInviteUser sysInviteUser, String companyName, String deptName) {

        MessageSendDTO messageSendDTO = new MessageSendDTO();

        // 设置消息的接收人，这里默认写为管理员
        messageSendDTO.setUserIdList(CollectionUtil.set(false, 1339550467939639299L));

        // 设置消息标题
        String title = "成员加入申请";
        messageSendDTO.setMessageTitle(title);

        // 设置消息内容
        String contentTemplate = "【】申请加入【】-【】";
        messageSendDTO.setMessageContent(StrUtil.format(contentTemplate, sysInviteUser.getRealName(), companyName, deptName));

        // 设置通知类型：带url的跳转链接
        messageSendDTO.setMessageType(MessageTypeEnum.NORMAL.getCode());

        // 设置通知的级别
        messageSendDTO.setPriorityLevel(PriorityLevelEnum.MIDDLE.getCode());

        // 设置业务类型
        messageSendDTO.setBusinessType(MESSAGE_TYPE);

        // 设置业务的具体字段内容
        HashMap<String, Object> detail = new HashMap<>();
        detail.put("inviteUserId", sysInviteUser.getInviteUserId());
        messageSendDTO.setBusinessDetail(detail);

        return messageSendDTO;
    }

}
