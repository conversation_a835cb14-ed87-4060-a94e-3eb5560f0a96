package cn.stylefeng.roses.ent.mobile.invite.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

/**
 * 邀请信息
 *
 * <AUTHOR>
 * @since 2024-04-08 18:28
 */
@Data
public class SysInviteDetail {

    /**
     * 邀请人的用户id
     */
    @ChineseDescription("邀请人的用户id")
    private Long inviteUserId;

    /**
     * 邀请人的姓名
     */
    @ChineseDescription("邀请人的姓名")
    private String inviteUserName;

    /**
     * 邀请人的头像地址
     */
    @ChineseDescription("邀请人的头像地址")
    private String avatarFileUrl;

    /**
     * 邀请加入的公司名称
     */
    @ChineseDescription("邀请加入的公司名称")
    private String companyName;

    /**
     * 邀请加入的部门名称
     */
    @ChineseDescription("邀请加入的部门名称")
    private String deptName;

}
