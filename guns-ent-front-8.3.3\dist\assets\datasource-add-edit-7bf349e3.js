import{R as d,r as n,o as g,a as _,f as y,w as R,d as k,m as x,M as O}from"./index-18a1ea24.js";import S from"./datasource-form-af174b20.js";class v{static findPage(e){return d.getAndLoadData("/databaseInfo/page",e)}static add(e){return d.post("/databaseInfo/add",e)}static edit(e){return d.post("/databaseInfo/edit",e)}static delete(e){return d.post("/databaseInfo/delete",e)}}const h={__name:"datasource-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(f,{emit:e}){const i=f,c=e,s=n(!1),o=n(!1),t=n({positionSort:1e3}),m=n(null);g(()=>{i.data?(o.value=!0,t.value=Object.assign({},i.data)):o.value=!1});const r=l=>{c("update:visible",l)},b=async()=>{m.value.$refs.formRef.validate().then(async l=>{if(l){s.value=!0;let a=null;o.value?a=v.edit(t.value):a=v.add(t.value),a.then(async u=>{s.value=!1,x.success(u.message),r(!1),c("done")}).catch(()=>{s.value=!1})}})};return(l,a)=>{const u=O;return _(),y(u,{width:700,maskClosable:!1,visible:i.visible,"confirm-loading":s.value,forceRender:!0,title:o.value?"\u7F16\u8F91\u6570\u636E\u6E90":"\u65B0\u5EFA\u6570\u636E\u6E90","body-style":{paddingBottom:"8px",height:"580px",overflowY:"auto"},"onUpdate:visible":r,onOk:b,onClose:a[1]||(a[1]=p=>r(!1))},{default:R(()=>[k(S,{form:t.value,"onUpdate:form":a[0]||(a[0]=p=>t.value=p),ref_key:"datasourceFormRef",ref:m},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}},B=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));export{v as D,h as _,B as d};
