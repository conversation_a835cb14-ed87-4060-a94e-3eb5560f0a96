System.register(["./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-f13ba0d3.js","./SupplierApi-legacy-234ddfc1.js","./SupplierAdd-legacy-30f91103.js","./SupplierEdit-legacy-bbb865a4.js","./SupplierDetail-legacy-3ca18eaf.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./UniversalTree-legacy-6dcdf778.js","./regionApi-legacy-73888494.js","./index-legacy-0d30ef09.js","./index-legacy-94a6fc23.js","./index-legacy-e24582b9.js"],(function(e,l){"use strict";var a,t,o,d,i,s,n,r,u,c,p,g,f,h,v,b,x,w,y,m,C,_,S,k,T,I,L,<PERSON>,<PERSON>,R,D,j,E,O,P,U,A,B,z,F,G,K,J,Q,q,H;return{setters:[e=>{a=e._},e=>{t=e._},e=>{o=e._,d=e.P,i=e.K,s=e.r,n=e.L,r=e.N,u=e.s,c=e.k,p=e.a,g=e.c,f=e.d,h=e.w,v=e.b,b=e.g,x=e.t,w=e.h,y=e.O,m=e.Q,C=e.F,_=e.e,S=e.f,k=e.M,T=e.E,I=e.m,L=e.U,M=e.n,N=e.B,R=e.I,D=e.p,j=e.q,E=e.D,O=e.l,P=e.V,U=e.W,A=e.J,B=e.u,z=e.v,F=e.G,G=e.H},null,e=>{K=e._},e=>{J=e.S},e=>{Q=e.default},e=>{q=e.default},e=>{H=e.default},null,null,null,null,null,null,null,null],execute:function(){var l=document.createElement("style");l.textContent=".guns-layout .table-toolbar[data-v-536cf5f0]{margin-bottom:16px;padding:16px 0;border-bottom:1px solid #f0f0f0}.guns-layout .table-toolbar .toolbar-left[data-v-536cf5f0]{display:flex;align-items:center;gap:16px}.guns-layout .table-toolbar .toolbar-left .search-input .ant-input[data-v-536cf5f0]{border-radius:6px}.guns-layout .table-toolbar .toolbar-left a[data-v-536cf5f0]{color:#1890ff;cursor:pointer}.guns-layout .table-toolbar .toolbar-left a[data-v-536cf5f0]:hover{color:#40a9ff}.guns-layout .advanced-search[data-v-536cf5f0]{padding:16px;background-color:#fafafa;border-radius:6px;margin-bottom:16px}.guns-layout .advanced-search .ant-form-item[data-v-536cf5f0]{margin-bottom:0}.guns-layout[data-v-536cf5f0]{height:100%;width:100%;margin:0;padding:0}.guns-layout-content[data-v-536cf5f0],.guns-layout-content-application[data-v-536cf5f0]{width:100%;height:100%;display:flex;flex-direction:column;margin:0;padding:0;box-sizing:border-box}.sidebar-content[data-v-536cf5f0]{height:100%;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08)}.content-main[data-v-536cf5f0]{width:100%;height:100%;display:flex;flex-direction:column;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08);margin:0;padding:0;box-sizing:border-box}.content-main-header[data-v-536cf5f0]{padding:16px 24px;border-bottom:1px solid #f0f0f0;background:#fafafa;border-radius:6px 6px 0 0}.header-content[data-v-536cf5f0]{display:flex;justify-content:space-between;align-items:center}.current-region-info[data-v-536cf5f0]{font-size:14px;color:#666}.content-main-body[data-v-536cf5f0]{flex:1;padding:16px 24px;overflow:auto;width:100%;box-sizing:border-box}.table-content[data-v-536cf5f0]{width:100%;height:100%;display:flex;flex-direction:column;box-sizing:border-box}[data-v-536cf5f0] .guns-split-panel-body{width:100%!important;flex:1!important;overflow:hidden!important}[data-v-536cf5f0] .guns-split-panel{width:100%!important}.text-muted[data-v-536cf5f0]{color:#999;font-style:italic}\n",document.head.appendChild(l);const V={class:"guns-layout"},W={class:"guns-layout-sidebar width-100 p-t-12"},$={class:"sidebar-content"},X={class:"guns-layout-content"},Y={class:"guns-layout"},Z={class:"guns-layout-content-application"},ee={class:"content-main"},le={class:"content-main-header"},ae={class:"header-content"},te={class:"header-content-left"},oe={key:0,class:"current-region-info"},de={class:"header-content-right"},ie={class:"content-main-body"},se={class:"table-content"},ne={key:0,class:"super-search",style:{"margin-top":"8px"}},re={key:0},ue={key:1,class:"text-muted"};e("default",o({name:"SupplierIndex",components:{PlusOutlined:d,SmallDashOutlined:i,SupplierAdd:Q,SupplierEdit:q,SupplierDetail:H,RegionTree:K},setup(){const e=s(!1),l=s(!1),a=s(!1),t=s(!1),o=s({}),d=s(null),i=s([]),c=s(null),p=s([]),g=s(!1),h=n((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),v=n((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),b=n((()=>r()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),x=u({searchText:"",supplierType:void 0,businessMode:void 0,status:void 0,creditLevel:void 0,contactPerson:void 0,contactPhone:void 0,regionId:void 0}),w=J.getSupplierTypeOptions(),y=J.getBusinessModeOptions(),m=J.getSupplierStatusOptions(),C=J.getCreditLevelOptions(),_=()=>{c.value&&c.value.reload()},S=()=>{var e;const l=null===(e=c.value)||void 0===e?void 0:e.getSelectedRows();l&&0!==l.length?k.confirm({title:"提示",content:`确定要删除选中的 ${l.length} 条数据吗？`,icon:f(T),maskClosable:!0,onOk:()=>{const e=l.map((e=>e.supplierId));return J.batchDelete({supplierIdList:e}).then((()=>{I.success("删除成功"),_()})).catch((e=>{I.error(e.message||"删除失败")}))}}):I.warning("请选择要删除的数据")};return{tableRef:c,regionTreeRef:d,superSearch:e,where:x,columns:[{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"supplierCode",title:"供应商编码",width:140,ellipsis:!0,isShow:!0},{dataIndex:"supplierName",title:"供应商名称",width:200,ellipsis:!0,isShow:!0},{dataIndex:"supplierShortName",title:"供应商简称",width:150,ellipsis:!0,isShow:!0},{dataIndex:"supplierType",title:"供应商类型",width:120,align:"center",isShow:!0},{dataIndex:"businessMode",title:"经营方式",width:120,align:"center",isShow:!0},{dataIndex:"salesDeduction",title:"销售扣点",width:100,align:"center",isShow:!0},{dataIndex:"contactPerson",title:"联系人",width:100,ellipsis:!0,isShow:!0},{dataIndex:"contactPhone",title:"联系电话",width:120,ellipsis:!0,isShow:!0},{dataIndex:"creditLevel",title:"信用等级",width:100,align:"center",isShow:!0},{dataIndex:"status",title:"状态",width:100,align:"center",isShow:!0},{dataIndex:"createTime",title:"创建时间",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}],currentRecord:o,selectedRegionNodes:i,showEdit:a,showDetailModal:t,supplierTypeOptions:w,businessModeOptions:y,statusOptions:m,creditLevelOptions:C,labelCol:h,wrapperCol:v,spanCol:b,selectedRegionIds:p,treeCollapsed:g,showAdd:l,reload:_,clear:()=>{Object.keys(x).forEach((e=>{x[e]="searchText"===e?"":void 0})),p.value=[],i.value=[],d.value&&d.value.clearSelection(),_()},changeSuperSearch:()=>{e.value=!e.value},handleRegionTreeSelect:(e,l)=>{console.log("区域树选择:",e,l),p.value=e,l&&l.selectedNodes&&l.selectedNodes.length>0?(i.value=l.selectedNodes,x.regionId=e[0]):(i.value=[],x.regionId=void 0),_()},handleRegionTreeLoaded:e=>{console.log("区域树数据加载完成:",e)},clearRegionFilter:()=>{p.value=[],i.value=[],x.regionId=void 0,d.value&&d.value.clearSelection(),_()},openAddModal:()=>{console.log("点击新增供应商按钮"),l.value=!0,console.log("新增弹窗状态已设置为true:",l.value)},openEditModal:e=>{o.value={...e},a.value=!0},showDetail:e=>{o.value={...e},t.value=!0},remove:e=>{k.confirm({title:"提示",content:"确定要删除该供应商吗？",icon:f(T),maskClosable:!0,onOk:()=>J.delete({supplierId:e.supplierId}).then((()=>{I.success("删除成功"),_()})).catch((e=>{I.error(e.message||"删除失败")}))})},moreClick:({key:e})=>{"1"===e&&S()},batchDelete:S,getSupplierTypeName:e=>J.getSupplierTypeName(e),getBusinessModeName:e=>J.getBusinessModeName(e),getSupplierStatusName:e=>J.getSupplierStatusName(e),getCreditLevelName:e=>J.getCreditLevelName(e),getStatusTagColor:e=>J.getStatusTagColor(e),getBusinessModeTagColor:e=>J.getBusinessModeTagColor(e),getCreditLevelTagColor:e=>J.getCreditLevelTagColor(e)}}},[["render",function(e,l,o,d,i,s){const n=K,r=L,u=M,k=c("plus-outlined"),T=N,I=R,J=D,Q=j,q=c("small-dash-outlined"),H=E,ce=O,pe=P,ge=U,fe=A,he=B,ve=z,be=F,xe=G,we=t,ye=a,me=c("supplier-add"),Ce=c("supplier-edit"),_e=c("supplier-detail");return p(),g("div",V,[f(ye,{width:"292px",cacheKey:"ERP_SUPPLIER_MANAGEMENT"},{content:h((()=>[v("div",X,[v("div",Y,[v("div",Z,[v("div",ee,[v("div",le,[v("div",ae,[v("div",te,[f(u,{size:16},{default:h((()=>[d.selectedRegionNodes.length>0?(p(),g("span",oe,[l[11]||(l[11]=b(" 当前区域：")),f(r,{color:"blue"},{default:h((()=>[b(x(d.selectedRegionNodes[0].regionName),1)])),_:1})])):w("",!0)])),_:1})]),v("div",de,[f(u,{size:16},{default:h((()=>[f(T,{type:"primary",class:"border-radius",onClick:d.openAddModal},{default:h((()=>[f(k),l[12]||(l[12]=b(" 新增供应商 "))])),_:1,__:[12]},8,["onClick"]),f(H,null,{overlay:h((()=>[f(Q,{onClick:d.moreClick},{default:h((()=>[f(J,{key:"1"},{default:h((()=>[f(I,{iconClass:"icon-opt-shanchu",color:"#60666b"}),l[13]||(l[13]=v("span",null,"批量删除",-1))])),_:1,__:[13]})])),_:1},8,["onClick"])])),default:h((()=>[f(T,{class:"border-radius"},{default:h((()=>[l[14]||(l[14]=b(" 更多 ")),f(q)])),_:1,__:[14]})])),_:1})])),_:1})])])]),v("div",ie,[v("div",se,[f(we,{columns:d.columns,where:d.where,fieldBusinessCode:"ERP_SUPPLIER_TABLE",showTableTool:"",showToolTotal:!1,rowId:"supplierId",ref:"tableRef",url:"/erp/supplier/page"},{toolLeft:h((()=>[f(ce,{value:d.where.searchText,"onUpdate:value":l[0]||(l[0]=e=>d.where.searchText=e),bordered:!1,allowClear:"",placeholder:"供应商名称、编码（回车搜索）",onPressEnter:d.reload,style:{width:"240px"},class:"search-input"},{prefix:h((()=>[f(I,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),f(pe,{type:"vertical",class:"divider"}),v("a",{onClick:l[1]||(l[1]=(...e)=>d.changeSuperSearch&&d.changeSuperSearch(...e))},x(d.superSearch?"收起":"高级筛选"),1)])),toolBottom:h((()=>[d.superSearch?(p(),g("div",ne,[f(xe,{model:d.where,labelCol:d.labelCol,"wrapper-col":d.wrapperCol},{default:h((()=>[f(be,{gutter:16},{default:h((()=>[f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:"供应商类型:"},{default:h((()=>[f(fe,{value:d.where.supplierType,"onUpdate:value":l[2]||(l[2]=e=>d.where.supplierType=e),placeholder:"请选择供应商类型",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),g(C,null,_(d.supplierTypeOptions,(e=>(p(),S(ge,{key:e.value,value:e.value},{default:h((()=>[b(x(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:"经营方式:"},{default:h((()=>[f(fe,{value:d.where.businessMode,"onUpdate:value":l[3]||(l[3]=e=>d.where.businessMode=e),placeholder:"请选择经营方式",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),g(C,null,_(d.businessModeOptions,(e=>(p(),S(ge,{key:e.value,value:e.value},{default:h((()=>[b(x(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:"状态:"},{default:h((()=>[f(fe,{value:d.where.status,"onUpdate:value":l[4]||(l[4]=e=>d.where.status=e),placeholder:"请选择状态",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),g(C,null,_(d.statusOptions,(e=>(p(),S(ge,{key:e.value,value:e.value},{default:h((()=>[b(x(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16)])),_:1}),f(be,{gutter:16},{default:h((()=>[f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:"信用等级:"},{default:h((()=>[f(fe,{value:d.where.creditLevel,"onUpdate:value":l[5]||(l[5]=e=>d.where.creditLevel=e),placeholder:"请选择信用等级",style:{width:"100%"},allowClear:""},{default:h((()=>[(p(!0),g(C,null,_(d.creditLevelOptions,(e=>(p(),S(ge,{key:e.value,value:e.value},{default:h((()=>[b(x(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},16),f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:"联系人:"},{default:h((()=>[f(ce,{value:d.where.contactPerson,"onUpdate:value":l[6]||(l[6]=e=>d.where.contactPerson=e),placeholder:"请输入联系人",allowClear:""},null,8,["value"])])),_:1})])),_:1},16),f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:"联系电话:"},{default:h((()=>[f(ce,{value:d.where.contactPhone,"onUpdate:value":l[7]||(l[7]=e=>d.where.contactPhone=e),placeholder:"请输入联系电话",allowClear:""},null,8,["value"])])),_:1})])),_:1},16)])),_:1}),f(be,{gutter:16},{default:h((()=>[f(ve,y(m(d.spanCol)),{default:h((()=>[f(he,{label:" ",class:"not-label"},{default:h((()=>[f(u,{size:16},{default:h((()=>[f(T,{class:"border-radius",onClick:d.reload,type:"primary"},{default:h((()=>l[15]||(l[15]=[b("查询")]))),_:1,__:[15]},8,["onClick"]),f(T,{class:"border-radius",onClick:d.clear},{default:h((()=>l[16]||(l[16]=[b("重置")]))),_:1,__:[16]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):w("",!0)])),bodyCell:h((({column:e,record:l})=>["supplierType"===e.dataIndex?(p(),S(r,{key:0},{default:h((()=>[b(x(d.getSupplierTypeName(l.supplierType)),1)])),_:2},1024)):"businessMode"===e.dataIndex?(p(),S(r,{key:1,color:d.getBusinessModeTagColor(l.businessMode)},{default:h((()=>[b(x(d.getBusinessModeName(l.businessMode)),1)])),_:2},1032,["color"])):"salesDeduction"===e.dataIndex?(p(),g(C,{key:2},[null!==l.salesDeduction&&void 0!==l.salesDeduction?(p(),g("span",re,x(l.salesDeduction)+"% ",1)):(p(),g("span",ue,"-"))],64)):"status"===e.dataIndex?(p(),S(r,{key:3,color:d.getStatusTagColor(l.status)},{default:h((()=>[b(x(d.getSupplierStatusName(l.status)),1)])),_:2},1032,["color"])):"creditLevel"===e.dataIndex?(p(),S(r,{key:4,color:d.getCreditLevelTagColor(l.creditLevel)},{default:h((()=>[b(x(d.getCreditLevelName(l.creditLevel)),1)])),_:2},1032,["color"])):"action"===e.key?(p(),S(u,{key:5,size:16},{default:h((()=>[f(I,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>d.openEditModal(l)},null,8,["onClick"]),f(I,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>d.remove(l)},null,8,["onClick"])])),_:2},1024)):w("",!0)])),_:1},8,["columns","where"])])])])])])])])),default:h((()=>[v("div",W,[v("div",$,[f(n,{ref:"regionTreeRef","show-badge":!0,onTreeSelect:d.handleRegionTreeSelect,onTreeDataLoaded:d.handleRegionTreeLoaded},null,8,["onTreeSelect","onTreeDataLoaded"])])])])),_:1}),f(me,{visible:d.showAdd,"onUpdate:visible":l[8]||(l[8]=e=>d.showAdd=e),onDone:d.reload},null,8,["visible","onDone"]),f(Ce,{visible:d.showEdit,"onUpdate:visible":l[9]||(l[9]=e=>d.showEdit=e),data:d.currentRecord,onDone:d.reload},null,8,["visible","data","onDone"]),f(_e,{visible:d.showDetailModal,"onUpdate:visible":l[10]||(l[10]=e=>d.showDetailModal=e),data:d.currentRecord},null,8,["visible","data"])])}],["__scopeId","data-v-536cf5f0"]]))}}}));
