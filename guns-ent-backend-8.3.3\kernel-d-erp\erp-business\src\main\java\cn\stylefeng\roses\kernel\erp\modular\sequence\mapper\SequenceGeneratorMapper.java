package cn.stylefeng.roses.kernel.erp.modular.sequence.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.SequenceGenerator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 序列号生成器Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
public interface SequenceGeneratorMapper extends BaseMapper<SequenceGenerator> {

    /**
     * 根据序列名称查询序列生成器
     *
     * @param sequenceName 序列名称
     * @return 序列生成器信息
     */
    @Select("SELECT * FROM erp_sequence_generator WHERE sequence_name = #{sequenceName}")
    SequenceGenerator getBySequenceName(@Param("sequenceName") String sequenceName);

    /**
     * 获取下一个序列值（原子操作）
     *
     * @param sequenceName 序列名称
     * @return 更新的行数
     */
    @Update("UPDATE erp_sequence_generator SET current_value = current_value + step WHERE sequence_name = #{sequenceName}")
    int getNextValue(@Param("sequenceName") String sequenceName);

    /**
     * 重置序列值
     *
     * @param sequenceName 序列名称
     * @param resetValue 重置值
     * @param resetDate 重置日期
     * @return 更新的行数
     */
    @Update("UPDATE erp_sequence_generator SET current_value = #{resetValue}, last_reset_date = #{resetDate} WHERE sequence_name = #{sequenceName}")
    int resetSequence(@Param("sequenceName") String sequenceName, @Param("resetValue") Long resetValue, @Param("resetDate") String resetDate);

}
