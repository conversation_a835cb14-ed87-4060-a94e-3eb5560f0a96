<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.ent.saas.modular.auth.mapper.TenantLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink">
		<id column="tenant_link_id" property="tenantLinkId" />
		<result column="tenant_id" property="tenantId" />
		<result column="package_id" property="packageId" />
		<result column="service_end_time" property="serviceEndTime" />
		<result column="trial_flag" property="trialFlag" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		tenant_link_id,tenant_id,package_id,service_end_time,trial_flag,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.ent.saas.modular.auth.pojo.response.TenantLinkVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		ent_tenant_link tbl
		WHERE
		<where>
        <if test="param.tenantLinkId != null and param.tenantLinkId != ''">
            and tbl.tenant_link_id like concat('%',#{param.tenantLinkId},'%')
        </if>
        <if test="param.tenantId != null and param.tenantId != ''">
            and tbl.tenant_id like concat('%',#{param.tenantId},'%')
        </if>
        <if test="param.packageId != null and param.packageId != ''">
            and tbl.package_id like concat('%',#{param.packageId},'%')
        </if>
        <if test="param.serviceEndTime != null and param.serviceEndTime != ''">
            and tbl.service_end_time like concat('%',#{param.serviceEndTime},'%')
        </if>
        <if test="param.trialFlag != null and param.trialFlag != ''">
            and tbl.trial_flag like concat('%',#{param.trialFlag},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
