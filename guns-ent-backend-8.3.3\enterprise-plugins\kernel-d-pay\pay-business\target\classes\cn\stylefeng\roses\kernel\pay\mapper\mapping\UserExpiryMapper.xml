<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.pay.mapper.UserExpiryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.pay.api.entity.UserExpiry">
		<id column="expiry_id" property="expiryId" />
		<result column="user_id" property="userId" />
		<result column="goods_id" property="goodsId" />
		<result column="expiry_date" property="expiryDate" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		expiry_id,user_id,goods_id,expiry_date,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.pay.pojo.response.UserExpiryVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		shop_user_expiry tbl
		WHERE
		<where>
        <if test="param.expiryId != null and param.expiryId != ''">
            and tbl.expiry_id like concat('%',#{param.expiryId},'%')
        </if>
        <if test="param.userId != null and param.userId != ''">
            and tbl.user_id like concat('%',#{param.userId},'%')
        </if>
        <if test="param.goodsId != null and param.goodsId != ''">
            and tbl.goods_id like concat('%',#{param.goodsId},'%')
        </if>
        <if test="param.expiryDate != null and param.expiryDate != ''">
            and tbl.expiry_date like concat('%',#{param.expiryDate},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
