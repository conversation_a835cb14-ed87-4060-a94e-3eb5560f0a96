package cn.stylefeng.roses.kernel.ca.server.modular.sso.controller;

import cn.stylefeng.roses.kernel.ca.api.SsoLoginCodeServiceApi;
import cn.stylefeng.roses.kernel.ca.api.SsoService;
import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoDetectionRequest;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoLoginCodeRequest;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoLoginRequest;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoLogoutRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 用户统一认证中心的主控制器
 *
 * <AUTHOR>
 * @date 2021/1/21 13:36
 */
@Controller
@ApiResource(name = "SSO单点登录控制器")
public class SsoController {

    @Resource
    private SsoService ssoService;

    @Resource
    private SsoLoginCodeServiceApi ssoLoginCodeServiceApi;

    /**
     * 单点登录认证检测
     *
     * <AUTHOR>
     * @date 2020/10/29 16:35
     */
    @GetResource(name = "单点登录认证检测", path = "/sso/detection", requiredLogin = false)
    public String detection(@Valid SsoDetectionRequest ssoDetectionRequest) {
        return CaServerConstants.REDIRECT_PREFIX + ssoService.detection(ssoDetectionRequest);
    }

    /**
     * 单点登录，校验账号密码是否正确，获取ssoLoginCode，获取ssoLoginCode后，可以直接单点登录成功
     *
     * <AUTHOR>
     * @date 2021/1/27 16:59
     */
    @PostResource(name = "单点登录获取ssoLoginCode", path = "/sso/getLoginCode", requiredLogin = false)
    @ResponseBody
    public ResponseData<String> getSsoLoginCode(@RequestBody @Valid SsoLoginCodeRequest ssoLoginCodeRequest) {
        String ssoLoginCode = ssoLoginCodeServiceApi.createSsoLoginCode(ssoLoginCodeRequest);
        return new SuccessResponseData<>(ssoLoginCode);
    }

    /**
     * 通过ssoLoginCode进行单点登录
     *
     * <AUTHOR>
     * @date 2021/1/28 12:00
     */
    @GetResource(name = "通过ssoLoginCode进行单点登录", path = "/sso/activateByLoginCode", requiredLogin = false)
    public String activateByLoginCode(@Valid SsoLoginRequest ssoLoginRequest) {
        return CaServerConstants.REDIRECT_PREFIX + ssoService.activateByLoginCode(ssoLoginRequest);
    }

    /**
     * SSO退出登录
     *
     * <AUTHOR>
     * @date 2021/1/28 12:00
     */
    @GetResource(name = "SSO退出登录", path = "/sso/logout", requiredLogin = false)
    public String logout(@Valid SsoLogoutRequest ssoLogoutRequest) {
        return CaServerConstants.REDIRECT_PREFIX + ssoService.ssoLogout(ssoLogoutRequest);
    }

}
