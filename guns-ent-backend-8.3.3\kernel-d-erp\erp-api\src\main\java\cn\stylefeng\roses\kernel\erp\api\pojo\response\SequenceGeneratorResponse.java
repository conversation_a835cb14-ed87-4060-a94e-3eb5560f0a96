package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 序列号生成器响应参数
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
@Data
public class SequenceGeneratorResponse {

    /**
     * 主键ID
     */
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 序列名称
     */
    @ChineseDescription("序列名称")
    private String sequenceName;

    /**
     * 前缀
     */
    @ChineseDescription("前缀")
    private String prefix;

    /**
     * 当前值
     */
    @ChineseDescription("当前值")
    private Long currentValue;

    /**
     * 步长
     */
    @ChineseDescription("步长")
    private Integer step;

    /**
     * 日期格式（如：yyyyMMdd）
     */
    @ChineseDescription("日期格式")
    private String dateFormat;

    /**
     * 重置类型：NONE(不重置)、DAILY(每日)、MONTHLY(每月)、YEARLY(每年)
     */
    @ChineseDescription("重置类型")
    private String resetType;

    /**
     * 最后重置日期
     */
    @ChineseDescription("最后重置日期")
    private LocalDate lastResetDate;

    /**
     * 创建时间
     */
    @ChineseDescription("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ChineseDescription("更新时间")
    private LocalDateTime updateTime;

    /**
     * 重置类型描述
     */
    @ChineseDescription("重置类型描述")
    private String resetTypeDesc;

}
