package cn.stylefeng.roses.ent.mobile.invite.controller;

import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteDetailRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteUserRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteDetail;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteUserVo;
import cn.stylefeng.roses.ent.mobile.invite.service.SysInviteUserService;
import cn.stylefeng.roses.ent.mobile.manage.pojo.config.SendPhoneCodeRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 邀请用户控制器
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
@RestController
@ApiResource(name = "邀请用户")
public class SysInviteUserController {

    @Resource
    private SysInviteUserService sysInviteUserService;

    /**
     * 获取邀请加入界面的邀请人信息详情
     *
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    @GetResource(name = "获取邀请加入界面的邀请人信息详情", path = "/sysInviteUser/getInviteDetail", requiredLogin = false)
    public ResponseData<SysInviteDetail> getInviteDetail(@Validated SysInviteDetailRequest sysInviteDetailRequest) {
        return new SuccessResponseData<>(sysInviteUserService.getInviteDetail(sysInviteDetailRequest));
    }

    /**
     * 邀请加入界面，发送短信验证码
     *
     * <AUTHOR>
     * @since 2024-04-08 19:39
     */
    @PostResource(name = "邀请加入界面，发送短信验证码", path = "/sysInviteUser/sendPhoneValidateCode")
    public ResponseData<?> sendPhoneValidateCode(@RequestBody @Validated SendPhoneCodeRequest changePhoneRequest) {
        sysInviteUserService.sendPhoneValidateCode(changePhoneRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 邀请加入-提交申请
     *
     * <AUTHOR>
     * @since 2024-04-08 19:13
     */
    @PostResource(name = "邀请加入-提交申请", path = "/sysInviteUser/submitApply")
    public ResponseData<?> submitApply(@RequestBody @Validated(SysInviteUserRequest.add.class) SysInviteUserRequest sysInviteUserRequest) {
        sysInviteUserService.submitApply(sysInviteUserRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 获取新成员申请列表
     *
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    @GetResource(name = "获取新成员申请列表", path = "/sysInviteUser/getApplyUserList")
    public ResponseData<List<SysInviteUserVo>> getApplyUserList(SysInviteUserRequest sysInviteUserRequest) {
        return new SuccessResponseData<>(sysInviteUserService.findList(sysInviteUserRequest));
    }

    /**
     * 同意申请
     *
     * <AUTHOR>
     * @since 2024-04-08 19:13
     */
    @PostResource(name = "同意申请", path = "/sysInviteUser/agreeApply")
    public ResponseData<?> agreeApply(@RequestBody @Validated(SysInviteUserRequest.agree.class) SysInviteUserRequest sysInviteUserRequest) {
        sysInviteUserService.agreeApply(sysInviteUserRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 拒绝申请
     *
     * <AUTHOR>
     * @since 2024-04-08 19:13
     */
    @PostResource(name = "拒绝申请", path = "/sysInviteUser/disAgreeApply")
    public ResponseData<?> disAgreeApply(@RequestBody @Validated(SysInviteUserRequest.disAgree.class) SysInviteUserRequest sysInviteUserRequest) {
        sysInviteUserService.disAgreeApply(sysInviteUserRequest);
        return new SuccessResponseData<>();
    }

}
