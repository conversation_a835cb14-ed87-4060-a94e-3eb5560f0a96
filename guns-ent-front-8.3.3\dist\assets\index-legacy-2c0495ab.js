System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./FileApi-legacy-f85a3060.js"],(function(e,l){"use strict";var t,a,o,d,r,i,n,u,s;return{setters:[e=>{t=e.r,a=e.bh,o=e.k,d=e.a,r=e.c,i=e.d,n=e.w,u=e.a0},null,e=>{s=e.a}],execute:function(){const l={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const c=t("undo redo clear h bold italic strikethrough quote ul ol table hr link image code"),m=t("preview sync-scroll fullscreen"),f=t(""),g=t(`${a}/sysFileInfo/public/preview?fileId=`),b=async(e,l,t)=>{const a=new FormData;a.append("file",t[0]);let o=await s.commonUpload("N",a);l({url:g.value+o.data.fileId,desc:o.data.fileOriginName})};return(e,t)=>{const a=o("v-md-editor"),s=u,g=o("v-md-preview");return d(),r("div",l,[i(s,{title:"markwodn编辑器-编辑",bordered:!1},{default:n((()=>[i(a,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e),autofocus:!0,height:"400px",width:"100%",ref:"editor","left-toolbar":c.value,"right-toolbar":m.value,"disabled-menus":[],mode:"edit",onUploadImage:b},null,8,["modelValue","left-toolbar","right-toolbar"])])),_:1}),i(s,{title:"markwodn编辑器-预览",bordered:!1},{default:n((()=>[i(g,{text:f.value,height:"500px"},null,8,["text"])])),_:1})])}}})}}}));
