System.register(["./index-legacy-ee1db0c7.js","./dict-type-form-legacy-04ad38f9.js"],(function(e,t){"use strict";var a,i,d,l,s,n,o,c,r,u,p;return{setters:[e=>{a=e.R,i=e.r,d=e.o,l=e.cb,s=e.a,n=e.f,o=e.w,c=e.d,r=e.m,u=e.M},e=>{p=e.default}],execute:function(){class t{static add(e){return a.post("/dictType/add",e)}static edit(e){return a.post("/dictType/edit",e)}static delete(e){return a.post("/dictType/delete",e)}static detail(e){return a.getAndLoadData("/dictType/detail",e)}static list(e){return a.getAndLoadData("/dictType/list",e)}}e("S",t);const v=e("_",{__name:"dict-type-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const v=e,y=a,f=i(!1),m=i(!1),b=i({statusFlag:1,dictTypeClass:1}),g=i(null);d((async()=>{v.data?(m.value=!0,T()):(b.value.dictTypeSort=await l("SYSTEM_BASE_DICT_TYPE"),m.value=!1)}));const T=()=>{t.detail({dictTypeId:v.data.dictTypeId}).then((e=>{b.value=Object.assign({},e)}))},_=e=>{y("update:visible",e)},S=async()=>{g.value.$refs.formRef.validate().then((async e=>{if(e){f.value=!0;let e=null;e=m.value?t.edit(b.value):t.add(b.value),e.then((async e=>{f.value=!1,r.success(e.message),_(!1),y("done")})).catch((()=>{f.value=!1}))}}))};return(e,t)=>{const a=u;return s(),n(a,{width:700,maskClosable:!1,visible:v.visible,"confirm-loading":f.value,forceRender:!0,title:m.value?"编辑字典类型":"新建字典类型","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":_,onOk:S,onClose:t[1]||(t[1]=e=>_(!1))},{default:o((()=>[c(p,{form:b.value,"onUpdate:form":t[0]||(t[0]=e=>b.value=e),ref_key:"typeFormRef",ref:g,isUpdate:m.value},null,8,["form","isUpdate"])])),_:1},8,["visible","confirm-loading","title"])}}}),y=Object.freeze(Object.defineProperty({__proto__:null,default:v},Symbol.toStringTag,{value:"Module"}));e("d",y)}}}));
