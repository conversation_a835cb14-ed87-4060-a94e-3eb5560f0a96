package cn.stylefeng.roses.kernel.sync.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户同步信息
 *
 * <AUTHOR>
 * @since 2023/10/30 13:38
 */
@Data
public class UserSyncVo {

    /**
     * 主键
     */
    @ChineseDescription("主键")
    private String userId;

    /**
     * 姓名
     */
    @ChineseDescription("姓名")
    private String realName;

    /**
     * 昵称
     */
    @ChineseDescription("昵称")
    private String nickName;

    /**
     * 账号
     */
    @ChineseDescription("账号")
    private String account;

    /**
     * 生日
     */
    @ChineseDescription("生日")
    private Date birthday;

    /**
     * 性别：M-男，F-女
     */
    @ChineseDescription("性别：M-男，F-女")
    private String sex;

    /**
     * 邮箱
     */
    @ChineseDescription("邮箱")
    private String email;

    /**
     * 手机
     */
    @ChineseDescription("手机")
    private String phone;

    /**
     * 电话
     */
    @ChineseDescription("电话")
    private String tel;

    /**
     * 是否是超级管理员：Y-是，N-否
     */
    @ChineseDescription("是否是超级管理员：Y-是，N-否")
    private String superAdminFlag;

    /**
     * 状态：1-正常，2-冻结
     */
    @ChineseDescription("状态：1-正常，2-冻结")
    private Integer statusFlag;

    /**
     * 用户的排序
     */
    @ChineseDescription("用户的排序")
    private BigDecimal userSort;

}
