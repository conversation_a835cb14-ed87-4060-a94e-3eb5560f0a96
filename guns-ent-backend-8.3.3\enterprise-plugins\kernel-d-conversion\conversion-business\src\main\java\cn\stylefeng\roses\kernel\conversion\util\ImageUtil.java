package cn.stylefeng.roses.kernel.conversion.util;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.font.LineMetrics;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 图片处理工具
 *
 * <AUTHOR>
 * @date 2021/11/1 11:19
 */
public class ImageUtil {

    /**
     * 批量给原目录加水印，生成的新图片会在源图片的基础上加上.dist.png
     *
     * @param originDirectory 原目录
     * @param watermarkLogo   水印图片路径
     * <AUTHOR>
     * @date 2021/11/1 14:21
     */
    public static void batchAddWatermark(String originDirectory, String watermarkLogo) {
        List<File> files = FileUtil.loopFiles(originDirectory);
        for (File file : files) {
            if (file.getName().endsWith(".png")) {

                // 图片路径
                String absolutePath = file.getAbsolutePath();

                // 图片加水印
                ImgUtil.pressImage(
                        FileUtil.file(absolutePath),
                        FileUtil.file(absolutePath.replace(".png", ".dist.png")),
                        // 水印图片
                        ImgUtil.read(FileUtil.getInputStream(watermarkLogo)),
                        // x坐标修正值。 默认在中间，偏移量相对于中间偏移
                        0,
                        // y坐标修正值。 默认在中间，偏移量相对于中间偏移
                        0,
                        0.4f
                );
            }
        }
    }

    /**
     * 创建背景透明的文字图片
     *
     * @param str       文本字符串
     * @param width     图片宽度
     * @param height    图片高度
     * @param font      设置字体
     * @param fontColor 字体颜色
     * @param alpha     文字透明度，值从0.0f-1.0f，依次变得不透明
     * @date 2021/11/1 14:30
     */
    public static BufferedImage createImageWithText(String str, int width, int height, Font font, Color fontColor, float alpha) {
        BufferedImage textImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = textImage.createGraphics();

        // 设置背景透明
        textImage = g2.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
        g2.dispose();
        g2 = textImage.createGraphics();

        // 开启文字抗锯齿
        g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        // 设置字体
        g2.setFont(font);
        // 设置字体颜色
        g2.setColor(fontColor);
        // 设置透明度:1.0f为透明度 ，值从0-1.0，依次变得不透明
        g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
        // 计算字体位置：上下左右居中
        FontRenderContext context = g2.getFontRenderContext();
        LineMetrics lineMetrics = font.getLineMetrics(str, context);
        FontMetrics fontMetrics = g2.getFontMetrics(font);
        float offset = (float) (width - fontMetrics.stringWidth(str)) / 2;
        float y = (height + lineMetrics.getAscent() - lineMetrics.getDescent() - lineMetrics.getLeading()) / 2;
        // 绘图
        g2.drawString(str, (int) offset, (int) y);
        // 释放资源
        g2.dispose();
        return textImage;
    }

    /**
     * 将文字写在透明图片上
     *
     * @param destImgPath 输出图片的最终路径
     * @param imgType     图片类型，例如png
     * @param str         文本字符串
     * @param width       图片宽度
     * @param height      图片高度
     * @param font        设置字体
     * @param fontColor   字体颜色
     * @param alpha       文字透明度，值从0.0f-1.0f，依次变得不透明
     * <AUTHOR>
     * @date 2021/11/1 14:38
     */
    public static void createImageWithText(String destImgPath, String imgType, String str, int width, int height, Font font, Color fontColor, float alpha) {
        BufferedImage image = ImageUtil.createImageWithText(str, width, height, font, fontColor, alpha);
        try {
            ImageIO.write(image, imgType, new File(destImgPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入本地图片到缓冲区
     *
     * @param imgName 图片名称
     * <AUTHOR>
     * @date 2021/11/1 14:30
     */
    private static BufferedImage loadImageLocal(String imgName) {
        try {
            return ImageIO.read(new File(imgName));
        } catch (IOException e) {
            System.out.println(e.getMessage());
        }
        return null;
    }

}
