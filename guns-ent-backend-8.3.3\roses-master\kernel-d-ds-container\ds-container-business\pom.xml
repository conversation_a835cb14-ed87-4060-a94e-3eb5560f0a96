<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-ds-container</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ds-container-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--数据源容器sdk-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>ds-container-sdk</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--定时任务的api-->
        <!--定时轮询数据源的状态，更新到库中-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>timer-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库sdk-->
        <!--数据库dao框架-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--分组相关业务-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>group-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
