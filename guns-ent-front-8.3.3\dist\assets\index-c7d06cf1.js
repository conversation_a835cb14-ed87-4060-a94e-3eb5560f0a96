import{R as x,_ as w,r as _,o as T,a as l,c as g,b as n,d as v,w as c,F as V,e as z,f as A,g as F,t as D,h as k,m as P,C as j,i as M,S as q,j as O,T as $}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */import{E as G}from"./ExternalAppApi-19e996ee.js";class I{static getBindResult(o){return x.getAndLoadData("/apiClientAuth/getBindResult",o)}static bind(o){return x.post("/apiClientAuth/bind",o)}}const H={class:"guns-layout"},J={class:"guns-layout-content"},Q={class:"guns-layout"},W={class:"guns-layout-content-application"},X={class:"content-mian"},Y={class:"content-mian-body"},Z={class:"table-content"},ee={key:0,class:"right"},te={class:"right-bottom"},ae={key:0,class:"use-content"},ne={class:"content-header"},se={class:"content-bottom"},oe={class:"table"},ie=Object.assign({name:"Authorization"},{__name:"index",setup(B){const o=_([]),i=_(""),d=_(!1),s=_(null),L=_([{title:"",width:60,dataIndex:"page"},{title:"\u63A5\u53E3\u540D\u79F0",align:"center",dataIndex:"resourceName"},{title:"\u63A5\u53E3\u7F16\u7801",align:"center",dataIndex:"resourceCode"},{title:"\u63A5\u53E3\u8DEF\u5F84",dataIndex:"url"},{title:"\u8BF7\u6C42\u65B9\u5F0F",align:"center",dataIndex:"httpMethod"}]);T(()=>{N()});const N=()=>{G.list().then(t=>{o.value=t,t&&t.length&&(i.value=t[0].apiClientId,f())})},f=()=>{d.value=!0,I.getBindResult({apiClientId:i.value}).then(t=>{s.value=t}).finally(()=>d.value=!1)},E=t=>{f()},R=(t,e)=>{y(t.target.checked,e.apiEndpointCheckedFlagList),b(t.target.checked,e,"1")},K=(t,e)=>{e.apiCheckedFlag=t.target.checked,s.value.apiEndpointCheckedFlagList.find(a=>a.apiCheckedFlag==!1)?s.value.totalAuthCheckedFlag=!1:s.value.totalAuthCheckedFlag=!0,b(t.target.checked,e,"2")},b=(t,e,a)=>{d.value=!0;let p={checkedFlag:t,selectType:a,apiClientId:i.value};a=="2"&&(p.resourceCode=e.resourceCode),I.bind(p).then(m=>{P.success(m.message)}).finally(()=>d.value=!1)},y=(t,e)=>{e&&e.length>0&&e.forEach(a=>{a.apiCheckedFlag=t,a.children&&a.children.length>0&&y(t,a.children)})};return(t,e)=>{const a=j,p=M,m=q,S=O,U=$;return l(),g("div",H,[n("div",J,[n("div",Q,[n("div",W,[n("div",X,[n("div",Y,[n("div",Z,[v(U,{activeKey:i.value,"onUpdate:activeKey":e[2]||(e[2]=r=>i.value=r),"tab-position":"left",animated:"",onChange:E,class:"left-tab"},{default:c(()=>[(l(!0),g(V,null,z(o.value,r=>(l(),A(S,{key:r.apiClientId,tab:r.apiClientName},{default:c(()=>[i.value?(l(),g("div",ee,[v(m,{spinning:d.value,delay:100},{default:c(()=>[n("div",te,[s.value?(l(),g("div",ae,[n("div",ne,[v(a,{checked:s.value.totalAuthCheckedFlag,"onUpdate:checked":e[0]||(e[0]=u=>s.value.totalAuthCheckedFlag=u),onClick:e[1]||(e[1]=u=>R(u,s.value))},{default:c(()=>e[3]||(e[3]=[F("\u6240\u6709\u63A5\u53E3\u6743\u9650")])),_:1,__:[3]},8,["checked"])]),n("div",se,[n("div",oe,[v(p,{dataSource:s.value.apiEndpointCheckedFlagList,columns:L.value,pagination:!1,rowKey:"apiClientResourceId",bordered:"",size:"small",childrenColumnName:"other"},{bodyCell:c(({column:u,record:h})=>[u.dataIndex==="page"?(l(),A(a,{key:0,checked:h.apiCheckedFlag,"onUpdate:checked":C=>h.apiCheckedFlag=C,onChange:C=>K(C,h)},{default:c(()=>[F(D(h.nodeName),1)]),_:2},1032,["checked","onUpdate:checked","onChange"])):k("",!0)]),_:1},8,["dataSource","columns"])])])])):k("",!0)])]),_:1},8,["spinning"])])):k("",!0)]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])])])])])])])])}}}),pe=w(ie,[["__scopeId","data-v-8b847a37"]]);export{pe as default};
