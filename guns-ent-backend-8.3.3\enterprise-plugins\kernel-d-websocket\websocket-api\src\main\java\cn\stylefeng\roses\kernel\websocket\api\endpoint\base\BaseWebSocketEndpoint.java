package cn.stylefeng.roses.kernel.websocket.api.endpoint.base;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.auth.api.SessionManagerApi;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import cn.stylefeng.roses.kernel.websocket.api.constants.WebsocketConstants;
import cn.stylefeng.roses.kernel.websocket.api.pojo.WebSocketDTO;
import cn.stylefeng.roses.kernel.websocket.api.utils.WebSocketUtil;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * WebSocket基础端点类
 *
 * <AUTHOR>
 * @since 2024/1/14 23:13
 */
@Slf4j
public abstract class BaseWebSocketEndpoint {

    /**
     * 创建用户连接，有新websocket连接进入时候调用
     *
     * @param identifier   用户token，唯一标识
     * @param session      websocket会话
     * @param randomString 随机字符串，用来区分同一个用户的多个连接
     * <AUTHOR>
     * @since 2024/1/15 0:03
     */
    public void connect(Session session, String identifier, String randomString) {

        if (StrUtil.isBlank(identifier)) {
            return;
        }

        // 通过token获取，用户的账号信息
        SessionManagerApi sessionManagerApi = SpringUtil.getBean(SessionManagerApi.class);
        LoginUser loginUser = sessionManagerApi.getSession(identifier);
        if (loginUser == null) {
            log.error("websocket连接失败，token无效！token = {}", identifier);
            return;
        }

        // 重新设置客户端标识，设置为用户token + 随机数
        identifier = identifier + WebsocketConstants.IDENTIFIER_CONCAT + randomString;

        // 获取websocket管理器
        WebSocketManagerApi websocketManager = getWebSocketManager();

        // 创建websocket对象，将当前连接加入到websocket容器中
        WebSocketDTO webSocket = new WebSocketDTO();
        webSocket.setSession(session);
        webSocket.setUserAccount(loginUser.getAccount());
        webSocket.setUserId(loginUser.getUserId());
        webSocket.setIdentifier(identifier);
        webSocket.setLastHeart(new Date());
        websocketManager.put(identifier, webSocket);
    }

    /**
     * 接收到websocket客户端的消息时候调用
     *
     * @param identifier 用户token，唯一标识
     * @param message    消息内容
     * @param session    websocket会话
     * <AUTHOR>
     * @since 2024/1/15 0:09
     */
    public void receiveMessage(String identifier, String message, Session session) {

        // 获取websocket管理器
        WebSocketManagerApi webSocketManager = getWebSocketManager();

        // 心跳监测，收到ping消息，发送一个pong消息，并更新心跳时间
        if (webSocketManager.pingFlag(identifier, message)) {
            String pong = webSocketManager.getPongContent(identifier, message);
            WebSocketUtil.sendMessageAsync(session, pong);
            WebSocketDTO webSocket = webSocketManager.get(identifier);
            if (webSocket != null) {
                webSocket.setLastHeart(new Date());
            }
            return;
        }

        // 收到其他消息的时候，发布消息事件
        webSocketManager.onMessage(identifier, message);
    }

    /**
     * websocket连接关闭时候调用
     *
     * <AUTHOR>
     * @since 2024/1/15 0:13
     */
    public void disconnect(String identifier) {
        getWebSocketManager().remove(identifier);
    }

    /**
     * 获取容器中的websocket管理器
     *
     * <AUTHOR>
     * @since 2024/1/15 0:04
     */
    protected WebSocketManagerApi getWebSocketManager() {
        return SpringUtil.getBean(WebsocketConstants.WEBSOCKET_MANAGER_NAME);
    }

}
