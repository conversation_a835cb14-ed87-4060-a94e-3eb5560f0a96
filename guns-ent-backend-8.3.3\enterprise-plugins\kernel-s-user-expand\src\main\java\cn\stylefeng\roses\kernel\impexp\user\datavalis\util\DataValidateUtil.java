package cn.stylefeng.roses.kernel.impexp.user.datavalis.util;

import cn.stylefeng.roses.kernel.impexp.user.datavalis.base.BaseValidator;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;

/**
 * 数据校验的工具类
 *
 * <AUTHOR>
 * @since 2024/2/6 22:02
 */
public class DataValidateUtil {

    /**
     * 根据请求参数的校验器，综合汇总成一个结果
     *
     * <AUTHOR>
     * @since 2024/2/6 22:04
     */
    public static ExcelLineParseResult combineValidateResult(String originValue, BaseValidator... baseValidators) {

        ExcelLineParseResult finalResult = null;
        StringBuilder finalMessage = new StringBuilder();

        for (BaseValidator baseValidator : baseValidators) {

            ExcelLineParseResult validateResult = baseValidator.getValidateResult(originValue);
            finalResult = validateResult;

            if (!validateResult.getValidateResult()) {
                finalMessage.append(validateResult.getErrorMessage()).append(";");
            }
        }

        if (finalResult != null) {
            finalResult.setErrorMessage(finalMessage.toString());
        }

        return finalResult;
    }

}
