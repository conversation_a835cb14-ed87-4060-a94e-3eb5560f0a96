<template>
  <div class="box bgColor box-shadow">
    <!-- 搜索框-->
    <div class="search" style="display: flex">
      <a-input
        v-model:value="searchText"
        placeholder="功能包名称，回车搜索"
        allow-clear
        @pressEnter="getTreeData"
        @change="searchTextChange"
      >
        <template #prefix>
          <icon-font iconClass="icon-opt-search" />
        </template>
      </a-input>
      <a-button type="primary" style="margin-left: 10px" class="border-radius" @click="editPackage()"><plus-outlined /></a-button>
    </div>
    <div class="tree-content">
      <a-spin tip="Loading..." :spinning="apiLoading" :delay="100">
        <div v-show="treeData && treeData.length > 0" class="left-tree">
          <a-directory-tree
            :show-icon="true"
            v-model:selectedKeys="currentSelectKeys"
            v-model:expandedKeys="expandedKeys"
            @select="selectNode"
            :tree-data="treeData"
            :fieldNames="{ children: 'children', title: 'packageName', key: 'packageId', value: 'packageId' }"
          >
            <!-- 图标 -->
            <template #icon="data">
              <icon-font icon-class="icon-opt-shangchuandaoshujuku" color="#43505e" fontSize="24px" />
            </template>
            <!-- 标题 -->
            <template #title="data">
              <!-- 需要显示编辑图标 -->
              <span class="tree-edit">
                <span class="edit-title" :title="data.packageName"> {{ data.packageName }}</span>
                <span class="edit-icon">
                  <a-space>
                    <icon-font iconClass="icon-opt-tianjia" color="var(--primary-color)" @click.stop="editPackage(data)" />
                    <redo-outlined title="刷新" style="color: var(--primary-color)" @click.stop="refresh(data)" />
                    <icon-font iconClass="icon-opt-shanchu" color="red" @click.stop="deletePackage(data)" />
                  </a-space>
                </span>
              </span>
            </template>
          </a-directory-tree>
        </div>
        <a-empty v-show="treeData && treeData.length == 0" class="empty" />
      </a-spin>
    </div>

    <!-- 新增编辑弹框 -->
    <PackageAddEdit v-model:visible="showEdit" v-if="showEdit" :data="current" @done="getTreeData" />
  </div>
</template>

<script setup name="PackageTree">
import { ref, createVNode, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue/es';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import IconFont from '@/components/common/IconFont/index.vue';
import { PackageApi } from '../api/PackageApi';
import PackageAddEdit from './package-add-edit.vue';

const props = defineProps({});

const emits = defineEmits(['treeSelect', 'treeData', 'deletePackage']);

// 组织机构名称
const searchText = ref('');
// 加载状态
const apiLoading = ref(false);
// 组织机构树
const treeData = ref([]);
// 当前选中节点
const currentSelectKeys = ref([]);
// 当前展开的节点
const expandedKeys = ref([]);
// 是否显示新增编辑弹框
const showEdit = ref(false);
// 当前行数据
const current = ref(null);

onMounted(() => {
  getTreeData(true);
});

// 搜索组织机构树
const getTreeData = (flag = false) => {
  apiLoading.value = true;
  PackageApi.list({ searchText: searchText.value })
    .then(res => {
      treeData.value = res;
      if (flag && res?.length > 0) {
        currentSelectKeys.value = [res[0].packageId];
        emits('treeSelect', res[0].packageId);
      }
    })
    .finally(() => (apiLoading.value = false));
};

// 选中节点
const selectNode = (selectedKeys, metadata) => {
  emits('treeSelect', metadata.node.packageId);
};

// 搜索值变化
const searchTextChange = () => {
  treeLoadKeys.value = [];
  if (!searchText.value) getTreeData();
};

// 新建编辑包
const editPackage = data => {
  current.value = data;
  showEdit.value = true;
};

// 删除包
const deletePackage = data => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除吗?',
    icon: createVNode(ExclamationCircleOutlined),
    maskClosable: true,
    onOk: () => {
      apiLoading.value = true;
      PackageApi.delete({ packageId: data.packageId })
        .then(res => {
          message.success(res.message);
          getTreeData();
          if (currentSelectKeys.value?.length > 0 && currentSelectKeys.value[0] == data.packageId) {
            currentSelectKeys.value = [];
            emits('treeSelect', '');
          }
        })
        .finally(() => (apiLoading.value = false));
    }
  });
};

// 刷新
const refresh = data => {
  apiLoading.value = true;
  PackageApi.refreshPackageTenantRoles({ packageId: data.packageId })
    .then(res => {
      message.success(res.message);
    })
    .finally(() => {
      apiLoading.value = false;
    });
};

defineExpose({
  currentSelectKeys
});
</script>

<style scoped lang="less">
:deep(.ant-tree) {
  background-color: #fff !important;
}
.tree-content {
  height: calc(100% - 60px) !important;
}
.empty {
  margin-top: 300px !important;
}

@import url('@/styles/commonTree.less');
:deep(.ant-tree-switcher) {
  display: none;
}
.edit-icon {
  width: 60px !important;
}
.tree-edit:hover .edit-title {
  width: calc(100% - 70px) !important;
}
</style>
