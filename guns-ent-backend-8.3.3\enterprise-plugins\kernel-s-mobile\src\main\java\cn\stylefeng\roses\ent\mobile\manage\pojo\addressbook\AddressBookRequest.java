package cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通讯录查询的条件
 *
 * <AUTHOR>
 * @since 2024/3/21 22:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddressBookRequest extends BaseRequest {

    /**
     * 组织机构id
     */
    @ChineseDescription("组织机构id")
    @NotNull(message = "组织机构id不能为空")
    private Long orgId;

}
