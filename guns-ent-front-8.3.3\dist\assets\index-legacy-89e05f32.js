System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js"],(function(e,t){"use strict";var n,s,l,a,i,o,c,r,d,u,g,h,x,y,f;return{setters:[e=>{n=e._},e=>{s=e.R,l=e.r,a=e.o,i=e.a,o=e.c,c=e.b,r=e.d,d=e.w,u=e.f,g=e.h,h=e.m,x=e.I,y=e.l,f=e.n},null,null,null],execute:function(){class t{static getOnlineUserList(e){return s.getAndLoadData("/getOnlineUserList",e)}static offlineUser(e){return s.post("/offlineUser",e)}}const v={class:"guns-layout"},w={class:"guns-layout-content"},p={class:"guns-layout"},m={class:"guns-layout-content-application"},I={class:"content-mian"},b={class:"content-mian-body"},k={class:"table-content"};e("default",Object.assign({name:"Online"},{__name:"index",setup(e){const s=l([{key:"index",title:"序号",width:60,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"realName",title:"姓名",ellipsis:!0,isShow:!0},{dataIndex:"account",title:"账号",isShow:!0},{dataIndex:"loginTime",title:"登录时间",ellipsis:!0,isShow:!0},{dataIndex:"loginIp",title:"登录IP",isShow:!0},{key:"action",title:"操作",width:60,isShow:!0}]),T=l(null),L=l({searchText:""});a((()=>{}));const S=()=>{T.value.reload()},U=e=>(e.data=e.data.onlineUserList,e);return(e,l)=>{const a=x,_=y,j=f,C=n;return i(),o("div",v,[c("div",w,[c("div",p,[l[1]||(l[1]=c("div",{class:"guns-layout-content-header"},"在线用户",-1)),c("div",m,[c("div",I,[c("div",b,[c("div",k,[r(C,{columns:s.value,where:L.value,rowId:"userId",ref_key:"tableRef",ref:T,isPage:!1,customData:U,url:"/getOnlineUserList",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"ONLINE_TABLE"},{toolLeft:d((()=>[r(_,{value:L.value.searchText,"onUpdate:value":l[0]||(l[0]=e=>L.value.searchText=e),placeholder:"用户账号（回车搜索）",bordered:!1,onPressEnter:S,class:"search-input"},{prefix:d((()=>[r(a,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:d((({column:e,record:n})=>["action"==e.key?(i(),u(j,{key:0,size:16},{default:d((()=>[r(a,{iconClass:"icon-opt-tixiaxian",color:"#60666b","font-size":"24px",title:"踢下线",onClick:e=>(e=>{t.offlineUser({token:e.token}).then((e=>{h.success(e.message),S()}))})(n)},null,8,["onClick"])])),_:2},1024)):g("",!0)])),_:1},8,["columns","where"])])])])])])])])}}}))}}}));
