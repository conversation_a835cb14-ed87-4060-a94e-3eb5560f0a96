System.register(["./index-legacy-ee1db0c7.js"],(function(e,l){"use strict";var t,a,r,u,o,m,p,f,d,n,s,c,_;return{setters:[e=>{t=e.s,a=e.a,r=e.f,u=e.w,o=e.d,m=e.g,p=e.l,f=e.u,d=e.v,n=e.W,s=e.J,c=e.G,_=e.H}],execute:function(){e("default",{__name:"template-form",props:{form:Object,isUpdate:Boolean},setup(e){const l=t({templateName:[{required:!0,message:"请输入模板名称",type:"string",trigger:"blur"}],templateCode:[{required:!0,message:"请输入模板编码",type:"string",trigger:"blur "}],templateType:[{required:!0,message:"请输入模板类型",type:"number",trigger:"blur"}]});return(t,g)=>{const i=p,v=f,y=d,b=n,w=s,C=c,N=_;return a(),r(N,{ref:"formRef",model:e.form,rules:l,layout:"vertical"},{default:u((()=>[o(C,{gutter:20},{default:u((()=>[o(y,{span:24},{default:u((()=>[o(v,{label:"模板名称:",name:"templateName"},{default:u((()=>[o(i,{value:e.form.templateName,"onUpdate:value":g[0]||(g[0]=l=>e.form.templateName=l),placeholder:"请输入模板名称","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),o(y,{span:24},{default:u((()=>[o(v,{label:"模板编码:",name:"templateCode"},{default:u((()=>[o(i,{value:e.form.templateCode,"onUpdate:value":g[1]||(g[1]=l=>e.form.templateCode=l),placeholder:"请输入模板编码","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),o(y,{span:24},{default:u((()=>[o(v,{label:"模板类型:",name:"templateType"},{default:u((()=>[o(w,{value:e.form.templateType,"onUpdate:value":g[2]||(g[2]=l=>e.form.templateType=l),placeholder:"请选择模板类型","allow-clear":"",autocomplete:"off"},{default:u((()=>[o(b,{value:1},{default:u((()=>g[3]||(g[3]=[m("系统类型")]))),_:1,__:[3]}),o(b,{value:2},{default:u((()=>g[4]||(g[4]=[m("业务类型")]))),_:1,__:[4]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
