package cn.stylefeng.roses.kernel.impexp.org.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.impexp.org.datavalis.OrgTypeValidator;
import cn.stylefeng.roses.kernel.impexp.org.datavalis.StatusValidator;
import cn.stylefeng.roses.kernel.impexp.org.pojo.OrgExcelImportPreview;
import cn.stylefeng.roses.kernel.impexp.org.pojo.base.OrgExcelImportParse;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.BigintValidator;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.NotNullValidator;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.SortValidator;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.util.DataValidateUtil;
import cn.stylefeng.roses.kernel.impexp.user.enums.OperateTypeEnums;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织机构导入预览
 *
 * <AUTHOR>
 * @since 2024-02-18 18:20
 */
public class OrgImportValidateFactory {

    /**
     * 创建校验结果
     *
     * <AUTHOR>
     * @since 2024-02-18 18:20
     */
    public static List<OrgExcelImportPreview> createValidateResult(List<OrgExcelImportParse> orgExcelImportParseList) {

        // 定义返回结果
        List<OrgExcelImportPreview> orgExcelImportPreviews = new ArrayList<>();

        if (ObjectUtil.isEmpty(orgExcelImportParseList)) {
            return orgExcelImportPreviews;
        }

        // 逐个解析字段，将字段转化为是否正确的结果
        for (OrgExcelImportParse parseItem : orgExcelImportParseList) {

            OrgExcelImportPreview orgExcelImportPreview = new OrgExcelImportPreview();

            // 如果是demo开头的则直接跳过
            if ("demo".equals(parseItem.getNumber())) {
                continue;
            }

            // 解析机构id
            ExcelLineParseResult orgIdResult = DataValidateUtil.combineValidateResult(parseItem.getOrgId(), new BigintValidator());
            orgExcelImportPreview.setOrgId(orgIdResult);

            // 解析机构名称
            orgExcelImportPreview.setOrgName(new ExcelLineParseResult(parseItem.getOrgName()));

            // 解析机构简称
            orgExcelImportPreview.setOrgShortName(new ExcelLineParseResult(parseItem.getOrgShortName()));

            // 解析父级机构
            orgExcelImportPreview.setParentOrgName(new ExcelLineParseResult(parseItem.getParentOrgName()));

            // 解析机构编号
            orgExcelImportPreview.setOrgCode(new ExcelLineParseResult(parseItem.getOrgCode()));

            // 解析排序
            ExcelLineParseResult sortResult = DataValidateUtil.combineValidateResult(parseItem.getOrgSort(), new SortValidator());
            orgExcelImportPreview.setOrgSort(sortResult);

            // 解析机构的状态
            ExcelLineParseResult statusResult = DataValidateUtil.combineValidateResult(parseItem.getStatusFlag(), new StatusValidator());
            orgExcelImportPreview.setStatusFlag(statusResult);

            // 解析公司的类型
            ExcelLineParseResult orgTypeResult = DataValidateUtil.combineValidateResult(parseItem.getOrgType(), new OrgTypeValidator());
            orgExcelImportPreview.setOrgType(orgTypeResult);

            // 解析税号
            orgExcelImportPreview.setTaxNo(new ExcelLineParseResult(parseItem.getTaxNo()));

            // 解析描述
            orgExcelImportPreview.setRemark(new ExcelLineParseResult(parseItem.getRemark()));

            // 解析主数据系统机构id
            orgExcelImportPreview.setMasterOrgId(new ExcelLineParseResult(parseItem.getMasterOrgId()));

            // 设置操作类型
            if (ObjectUtil.isNotEmpty(parseItem.getOrgId())) {
                orgExcelImportPreview.setOperateType(new ExcelLineParseResult(true, OperateTypeEnums.EDIT.getMessage(), OperateTypeEnums.EDIT.getCode()));
            } else {
                orgExcelImportPreview.setOperateType(new ExcelLineParseResult(true, OperateTypeEnums.ADD.getMessage(), OperateTypeEnums.ADD.getCode()));
            }

            orgExcelImportPreviews.add(orgExcelImportPreview);
        }

        // 填充机构名称重复的标识
        fillOrgNameRepeatFlag(orgExcelImportPreviews, orgExcelImportParseList);

        // 填充机构编码不能重复的标识
        fillOrgCodeRepeatFlag(orgExcelImportPreviews, orgExcelImportParseList);

        // 分析父级机构是否填写正确
        fillParentOrgNameFlag(orgExcelImportPreviews, orgExcelImportParseList);

        return orgExcelImportPreviews;
    }

    /**
     * 填充机构名称重复的标识
     *
     * <AUTHOR>
     * @since 2024-02-18 18:55
     */
    public static void fillOrgNameRepeatFlag(List<OrgExcelImportPreview> orgExcelImportPreviews, List<OrgExcelImportParse> originValues) {

        for (OrgExcelImportPreview orgExcelImportPreview : orgExcelImportPreviews) {

            // 针对新增的机构，1.名称不能为空，2.名称不能在本次导入的过程中重复
            if (OperateTypeEnums.ADD.getCode().equals(orgExcelImportPreview.getOperateType().getSubmitValue())) {

                // 1. 名称不能为空
                ExcelLineParseResult orgNameResult = DataValidateUtil.combineValidateResult(orgExcelImportPreview.getOrgName().getValue(), new NotNullValidator());
                if (!orgNameResult.getValidateResult()) {
                    orgExcelImportPreview.setOrgName(orgNameResult);
                    continue;
                }

                // 2. 名称不能在本次导入的过程中重复
                long count = originValues.stream().filter(i -> orgExcelImportPreview.getOrgName().getValue().equals(i.getOrgName())).count();
                if (count > 1) {
                    orgExcelImportPreview.setOrgName(
                            new ExcelLineParseResult(false, orgExcelImportPreview.getOrgName().getValue(), orgExcelImportPreview.getOrgName().getValue(), "机构名称在本次导入过程中存在重复"));
                }
            }

            // 针对修改的机构，1.名称可以为空，2.名称不能再本次导入过程中重复
            else if (OperateTypeEnums.EDIT.getCode().equals(orgExcelImportPreview.getOperateType().getSubmitValue())) {

                // 1. 名称为空，直接进行下一个
                if (ObjectUtil.isEmpty(orgExcelImportPreview.getOrgName().getValue())) {
                    continue;
                }

                // 2. 名称不能在本次导入过程中重复
                long count = originValues.stream().filter(i -> orgExcelImportPreview.getOrgName().getValue().equals(i.getOrgName())).count();
                if (count > 1) {
                    orgExcelImportPreview.setOrgName(
                            new ExcelLineParseResult(false, orgExcelImportPreview.getOrgName().getValue(), orgExcelImportPreview.getOrgName().getValue(), "机构名称在本次导入过程中存在重复"));
                }
            }
        }
    }

    /**
     * 填充机构编码重复的标识
     *
     * <AUTHOR>
     * @since 2024-02-18 19:42
     */
    private static void fillOrgCodeRepeatFlag(List<OrgExcelImportPreview> orgExcelImportPreviews, List<OrgExcelImportParse> originValues) {

        HrOrganizationService hrOrganizationService = SpringUtil.getBean(HrOrganizationService.class);

        // 获取这批机构在库中存在的情况，key是组织机构编码，value是这个编码对应的组织机构id
        Map<String, Long> orgCodeAndOrgIdMap = new HashMap<>();

        // 获取这批机构的信息
        Set<String> thisOrgCodeList = originValues.stream().map(OrgExcelImportParse::getOrgCode).collect(Collectors.toSet());
        if (ObjectUtil.isNotEmpty(thisOrgCodeList)) {
            LambdaQueryWrapper<HrOrganization> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(HrOrganization::getOrgCode, thisOrgCodeList);
            queryWrapper.select(HrOrganization::getOrgCode, HrOrganization::getOrgId);
            List<HrOrganization> existedInDbOrgList = hrOrganizationService.list(queryWrapper);
            for (HrOrganization existedInDbOrg : existedInDbOrgList) {
                orgCodeAndOrgIdMap.put(existedInDbOrg.getOrgCode(), existedInDbOrg.getOrgId());
            }
        }

        for (OrgExcelImportPreview orgExcelImportPreview : orgExcelImportPreviews) {

            // 1. 针对新增的机构，1.编码不能为空，2.编码不能在库中存在，3.编码不能和本次导入的数据重复
            if (OperateTypeEnums.ADD.getCode().equals(orgExcelImportPreview.getOperateType().getSubmitValue())) {

                // 1. 编码不能为空
                ExcelLineParseResult orgCodeResult = DataValidateUtil.combineValidateResult(orgExcelImportPreview.getOrgCode().getValue(), new NotNullValidator());
                if (!orgCodeResult.getValidateResult()) {
                    orgExcelImportPreview.setOrgCode(orgCodeResult);
                    continue;
                }

                // 2. 编码不能在库中存在
                if (orgCodeAndOrgIdMap.containsKey(orgExcelImportPreview.getOrgCode().getValue())) {
                    orgExcelImportPreview.setOrgCode(
                            new ExcelLineParseResult(false, orgExcelImportPreview.getOrgCode().getValue(), orgExcelImportPreview.getOrgCode().getValue(), "机构编码在库中已存在，请更换编码！"));
                    continue;
                }

                // 3. 编码不能和本次导入的数据重复
                long count = originValues.stream().filter(i -> orgExcelImportPreview.getOrgCode().getValue().equals(i.getOrgCode())).count();
                if (count > 1) {
                    orgExcelImportPreview.setOrgCode(
                            new ExcelLineParseResult(false, orgExcelImportPreview.getOrgCode().getValue(), orgExcelImportPreview.getOrgCode().getValue(), "机构编码在本次导入的数据中重复"));
                }
            }

            // 2. 针对修改的机构，1.编码可以为空，2.编码不能在库中存在，3.编码不能和本次导入的数据重复
            else if (OperateTypeEnums.EDIT.getCode().equals(orgExcelImportPreview.getOperateType().getSubmitValue())) {

                // 1. 编码为空，直接进行下一个
                if (ObjectUtil.isEmpty(orgExcelImportPreview.getOrgCode().getValue())) {
                    continue;
                }

                // 2. 编码不能在库中存在
                String currentCode = orgExcelImportPreview.getOrgCode().getValue();
                if (orgCodeAndOrgIdMap.containsKey(currentCode)) {
                    String dbOrgCodeOrgId = orgCodeAndOrgIdMap.get(currentCode).toString();
                    if (!dbOrgCodeOrgId.equals(orgExcelImportPreview.getOrgId().getSubmitValue())) {
                        orgExcelImportPreview.setOrgCode(
                                new ExcelLineParseResult(false, orgExcelImportPreview.getOrgCode().getValue(), orgExcelImportPreview.getOrgCode().getValue(), "机构编码在库中已存在，请更换编码"));
                        continue;
                    }
                }

                // 3. 编码不能和本次导入的数据重复
                long count = originValues.stream().filter(i -> orgExcelImportPreview.getOrgCode().getValue().equals(i.getOrgCode())).count();
                if (count > 1) {
                    orgExcelImportPreview.setOrgCode(
                            new ExcelLineParseResult(false, orgExcelImportPreview.getOrgCode().getValue(), orgExcelImportPreview.getOrgCode().getValue(), "机构编码在本次导入的数据中重复"));
                }
            }
        }
    }

    /**
     * 分析父级机构是否填写正确
     *
     * <AUTHOR>
     * @since 2024/2/18 22:34
     */
    private static void fillParentOrgNameFlag(List<OrgExcelImportPreview> orgExcelImportPreviews, List<OrgExcelImportParse> originValues) {

        for (OrgExcelImportPreview orgExcelImportPreview : orgExcelImportPreviews) {

            // 针对新增和修改的机构，1. 父级名称可以为空，2. 父级名称必须在本次导入的数据中存在
            // 1. 父级名称可以为空，为空说明直接在确认阶段设置指定父级
            if (ObjectUtil.isEmpty(orgExcelImportPreview.getParentOrgName().getValue())) {
                continue;
            }

            // 2. 父级名称必须在本次导入的数据中存在
            long count = originValues.stream().filter(i -> orgExcelImportPreview.getParentOrgName().getValue().equals(i.getOrgName())).count();
            if (count == 0) {
                orgExcelImportPreview.setParentOrgName(
                        new ExcelLineParseResult(false, orgExcelImportPreview.getParentOrgName().getValue(), orgExcelImportPreview.getParentOrgName().getValue(),
                                "父级机构名称在本次导入列表中不存在，请检查父级名称是否正确"));
            }
        }

    }

}
