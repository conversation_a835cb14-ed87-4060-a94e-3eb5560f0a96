import Request from '@/utils/request/request-util';

/**
 * 产品分类管理API
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
export class ProductCategoryApi {

  /**
   * 分页查询产品分类列表
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async findPage(parameter) {
    return await Request.getAndLoadData('/erp/productCategory/page', parameter);
  }

  /**
   * 查询产品分类列表
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async findList(parameter) {
    return await Request.getAndLoadData('/erp/productCategory/list', parameter);
  }

  /**
   * 查询产品分类树形结构
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async findTree(parameter) {
    return await Request.getAndLoadData('/erp/productCategory/tree', parameter);
  }

  /**
   * 查询产品分类树形结构（懒加载）
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async findTreeWithLazy(parameter) {
    return await Request.getAndLoadData('/erp/productCategory/treeWithLazy', parameter);
  }

  /**
   * 查询产品分类树形结构（用于选择器）
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async findTreeForSelector(parameter) {
    return await Request.getAndLoadData('/erp/productCategory/treeForSelector', parameter);
  }

  /**
   * 批量删除产品分类
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async batchDelete(parameter) {
    return await Request.post('/erp/productCategory/batchDelete', parameter);
  }

  /**
   * 新增产品分类
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async add(parameter) {
    return await Request.post('/erp/productCategory/add', parameter);
  }

  /**
   * 编辑产品分类
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async edit(parameter) {
    return await Request.post('/erp/productCategory/edit', parameter);
  }

  /**
   * 删除产品分类
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async delete(parameter) {
    return await Request.post('/erp/productCategory/delete', parameter);
  }

  /**
   * 查看产品分类详情
   *
   * <AUTHOR>
   * @date 2025/07/21 21:00
   */
  static async detail(parameter) {
    return await Request.getAndLoadData('/erp/productCategory/detail', parameter);
  }
}

// 为了向后兼容，导出函数形式的API
export const getProductCategoryPage = ProductCategoryApi.findPage;
export const getProductCategoryList = ProductCategoryApi.findList;
export const getProductCategoryTree = ProductCategoryApi.findTree;
export const getProductCategoryTreeWithLazy = ProductCategoryApi.findTreeWithLazy;
export const getProductCategoryTreeForSelector = ProductCategoryApi.findTreeForSelector;
export const addProductCategory = ProductCategoryApi.add;
export const editProductCategory = ProductCategoryApi.edit;
export const deleteProductCategory = ProductCategoryApi.delete;
export const getProductCategoryDetail = ProductCategoryApi.detail;
export const batchDeleteProductCategory = ProductCategoryApi.batchDelete;
