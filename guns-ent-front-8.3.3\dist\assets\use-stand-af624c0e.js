import{_ as F,r as v,o as P,k as u,a as f,f as b,w as a,b as p,t as G,d as o,g as E,h as H,m as y,M as I,E as J,I as K,l as Q,B as W,n as X}from"./index-18a1ea24.js";import{M as x}from"./MenuApi-47485f5a.js";const Y={class:"stand-box"},Z={class:"stand-header"},ee={class:"search"},te={class:"table"},oe={__name:"use-stand",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup($,{emit:w}){const r=$,N=w,i=v(!1),_=v(""),C=v([]),d=v(null);P(()=>{r.data&&c()});const c=()=>{i.value=!0,x.optionList({menuId:r.data.menuId,searchText:_.value}).then(t=>{C.value=t}).finally(()=>i.value=!1)},k=t=>{N("update:visible",t)},R=()=>{_.value="",c()},B=async()=>{let t={optionName:"",optionCode:"",menuId:r.data.menuId};C.value.push(t);const e=d.value;e&&await e.setEditRow(t)},z=t=>{const e=d.value;if(e)return e.isEditByRow(t)},O=async t=>{const e=d.value;e&&await e.setEditRow(t)},U=t=>{t.menuOptionId?x.optionEdit(t).then(s=>{y.success(s.message),c()}):x.optionAdd(t).then(s=>{y.success(s.message),c()});const e=d.value;e&&e.clearEdit()},M=async t=>{const e=d.value;e&&(await e.clearEdit(),await e.revertData(t))},S=t=>{I.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5417?",icon:o(J),maskClosable:!0,onOk:()=>{i.value=!0,x.optionDelete({menuOptionId:t.menuOptionId}).then(e=>{y.success(e.message),c()}).finally(()=>i.value=!1)}})};return(t,e)=>{const s=K,T=Q,h=W,g=X,A=u("plus-outlined"),m=u("vxe-column"),V=u("vxe-input"),D=u("save-outlined"),j=u("close-outlined"),L=u("vxe-table"),q=I;return f(),b(q,{width:800,maskClosable:!1,visible:r.visible,"confirm-loading":i.value,forceRender:!0,title:"\u529F\u80FD\u7EF4\u62A4",footer:null,"body-style":{paddingBottom:"8px"},"onUpdate:visible":k,onClose:e[1]||(e[1]=n=>k(!1))},{default:a(()=>[p("div",Y,[p("div",Z,[e[2]||(e[2]=p("span",{class:"title"},"\u5F53\u524D\u83DC\u5355\uFF1A",-1)),p("span",null,G(r.data.menuName),1)]),p("div",ee,[o(g,{size:16},{default:a(()=>[o(T,{value:_.value,"onUpdate:value":e[0]||(e[0]=n=>_.value=n),placeholder:"\u83DC\u5355\u540D\u79F0\u3001\u83DC\u5355\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:c,class:"search-input"},{prefix:a(()=>[o(s,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),o(h,{class:"border-radius",onClick:R},{default:a(()=>e[3]||(e[3]=[E("\u91CD\u7F6E")])),_:1,__:[3]})]),_:1}),o(h,{type:"primary",class:"border-radius",onClick:B},{default:a(()=>[o(A),e[4]||(e[4]=E("\u65B0\u5EFA"))]),_:1,__:[4]})]),p("div",te,[o(L,{border:"","show-overflow":"","keep-source":"",ref_key:"xTableRef",ref:d,loading:i.value,data:C.value,height:"500","edit-config":{trigger:"manual",mode:"row",autoClear:!1,showStatus:!0}},{default:a(()=>[o(m,{type:"seq",width:"60",title:"\u5E8F\u53F7",align:"center"}),o(m,{field:"optionName",title:"\u529F\u80FD\u540D\u79F0","edit-render":{},align:"center"},{edit:a(({row:n})=>[o(V,{modelValue:n.optionName,"onUpdate:modelValue":l=>n.optionName=l,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(m,{field:"optionCode",title:"\u529F\u80FD\u7F16\u7801","edit-render":{},align:"center"},{edit:a(({row:n})=>[o(V,{modelValue:n.optionCode,"onUpdate:modelValue":l=>n.optionCode=l,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(m,{title:"\u64CD\u4F5C",align:"center"},{default:a(({row:n})=>[z(n)?(f(),b(g,{key:0,size:16},{default:a(()=>[o(D,{onClick:l=>U(n),style:{"font-size":"20px"},title:"\u4FDD\u5B58"},null,8,["onClick"]),o(j,{onClick:l=>M(n),style:{"font-size":"20px"},title:"\u53D6\u6D88"},null,8,["onClick"])]),_:2},1024)):(f(),b(g,{key:1,size:16},{default:a(()=>[o(s,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:l=>O(n)},null,8,["onClick"]),n.menuOptionId?(f(),b(s,{key:0,iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:l=>S(n)},null,8,["onClick"])):H("",!0)]),_:2},1024))]),_:1})]),_:1},8,["loading","data"])])])]),_:1},8,["visible","confirm-loading"])}}},se=F(oe,[["__scopeId","data-v-2432af0a"]]);export{se as default};
