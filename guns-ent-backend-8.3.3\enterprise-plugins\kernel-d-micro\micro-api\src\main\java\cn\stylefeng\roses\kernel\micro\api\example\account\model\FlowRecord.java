package cn.stylefeng.roses.kernel.micro.api.example.account.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 流水记录
 * </p>
 *
 * <AUTHOR>
 * @since 2018-05-05
 */
@TableName("flow_record")
@Data
public class FlowRecord extends Model<FlowRecord> {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 流水名称
     */
    private String name;
    /**
     * 总价
     */
    private BigDecimal sum;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

}
