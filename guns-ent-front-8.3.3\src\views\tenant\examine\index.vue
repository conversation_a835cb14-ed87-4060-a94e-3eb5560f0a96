<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <div class="content-mian-header">
              <div class="header-content">
                <div class="header-content-left">
                  <a-space :size="16">
                    <a-input
                      v-model:value="where.searchText"
                      placeholder="租户名称、租户编码（回车搜索）"
                      @pressEnter="reload"
                      class="search-input"
                    >
                      <template #prefix>
                        <icon-font iconClass="icon-opt-search" />
                      </template>
                    </a-input>
                  </a-space>
                </div>
                <div class="header-content-right">
                  <a-space :size="16">
                    <a-button type="primary" class="border-radius" @click="batchApprover">批量审批</a-button>
                  </a-space>
                </div>
              </div>
            </div>
            <div class="content-mian-body">
              <div class="table-content">
                <common-table :columns="columns" :where="where" rowId="tenantId" ref="tableRef" url="/tenant/page">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex == 'tenantName'">
                      <a @click="openDetail(record)">{{ record.tenantName }}</a>
                    </template>
                    <!-- 图标 -->
                    <template v-if="column.dataIndex == 'tenantLogoWrapper'">
                      <img :src="record.tenantLogoWrapper" alt="" class="appIconWrapper" />
                    </template>
                    <!-- 操作 -->
                    <template v-if="column.key == 'action'">
                      <a-space :size="16">
                        <icon-font
                          iconClass="icon-opt-shenpirenshezhi"
                          font-size="24px"
                          title="审批"
                          color="#60666b"
                          @click="approver(record)"
                        />
                        <icon-font
                          iconClass="icon-opt-shanchu"
                          font-size="24px"
                          title="删除"
                          color="#60666b"
                          @click="remove(record)"
                        />
                      </a-space>
                    </template>
                  </template>
                </common-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审批弹框 -->
    <TenantApprover v-model:visible="showApprover" v-if="showApprover" :approverList="approverList" @done="reload" />
    <!-- 租户详情 -->
    <TenantDetail v-model:visible="showDetail" v-if="showDetail" :data="current" @done="reload" />
  </div>
</template>

<script setup name="TenantExamine">
import { ref, createVNode, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue/es';
import TenantDetail from '@/views/tenant/manage/components/tenant-detail.vue';
import TenantApprover from './components/tenant-approver.vue';
import { TenantApi } from '@/views/tenant/manage/api/TenantApi';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

defineOptions({
  name: 'TenantExamine',
})

// 表格配置
const columns = ref([
  {
    key: 'index',
    title: '序号',
    width: 48,
    align: 'center',
    isShow: true,
    hideInSetting: true,
    customRender: ({ index }) => tableRef.value.tableIndex + index
  },
  {
    dataIndex: 'tenantName',
    title: '租户名称',
    ellipsis: true,
    width: 200,
    isShow: true
  },
  {
    dataIndex: 'tenantCode',
    title: '租户编码',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'tenantLogoWrapper',
    title: '租户logo',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'companyName',
    title: '公司',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'email',
    title: '申请人邮箱',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'safePhone',
    title: '申请人电话',
    width: 100,
    isShow: true
  },
  {
    dataIndex: 'createTime',
    title: '申请时间',
    width: 100,
    isShow: true
  },
  {
    key: 'action',
    title: '操作',
    width: 80,
    isShow: true
  }
]);
// ref
const tableRef = ref(null);

// 搜索条件
const where = ref({
  searchText: '',
  // 未激活
  activeFlag: 'N'
});
// 当前行数据
const current = ref(null);
// 是否显示详情
const showDetail = ref(false);
// 是否显示审批弹框
const showApprover = ref(false);
// 审批列表
const approverList = ref([]);

onMounted(() => {});

// 点击搜索
const reload = () => {
  tableRef.value.reload();
};

// 详情点击
const openDetail = record => {
  current.value = record;
  showDetail.value = true;
};

// 删除单个
const remove = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选中的租户吗?',
    icon: createVNode(ExclamationCircleOutlined),
    maskClosable: true,
    onOk: async () => {
      const res = await TenantApi.delete({ tenantId: record.tenantId });
      message.success(res.message);
      reload();
    }
  });
};

// 批量审批
const batchApprover = () => {
  if (tableRef.value.selectedRowList && tableRef.value.selectedRowList.length == 0) {
    return message.warning('请选择需要审批的租户');
  }
  approverList.value = tableRef.value.selectedRowList;
  showApprover.value = true;
};

// 单个审批
const approver = record => {
  approverList.value = [record.tenantId];
  showApprover.value = true;
};
</script>

<style scoped lang="less">
.appIconWrapper {
  width: 22px;
  height: 22px;
}
</style>
