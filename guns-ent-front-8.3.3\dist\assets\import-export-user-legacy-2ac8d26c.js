System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-8a7fc0f5.js","./index-legacy-198191c1.js","./UsersApi-legacy-88b5f949.js","./index-legacy-53580278.js","./index-legacy-efb51034.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js"],(function(e,a){"use strict";var t,l,i,o,n,s,r,d,c,p,u,m,v,g,x,b,y,f,h,w,I,k,N,S,_,z,C,j,U,F,O,L,T,E;return{setters:[e=>{t=e._,l=e.r,i=e.s,o=e.k,n=e.a,s=e.f,r=e.w,d=e.aR,c=e.d,p=e.b,u=e.c,m=e.F,v=e.e,g=e.a2,x=e.t,b=e.g,y=e.aS,f=e.h,h=e.m,w=e.bj,I=e.B,k=e.I,N=e.bk,S=e.S,_=e.l,z=e.u,C=e.a9,j=e.H,U=e.v,F=e.a7,O=e.G},e=>{L=e._},e=>{T=e._},null,e=>{E=e.U},null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".import-box[data-v-a760c48e]{display:flex;flex-direction:column}.import-header[data-v-a760c48e]{width:100%;height:80px;display:flex;align-items:center}.import-body[data-v-a760c48e]{flex:auto}.import-back[data-v-a760c48e]{border:#a6a6a6 solid 1px}.import-title[data-v-a760c48e]{color:#505050;font-size:20px;margin-left:10px}.import-tabs[data-v-a760c48e]{width:100%;display:flex}.import-tab-item[data-v-a760c48e]{width:215px;height:59px;display:flex;align-items:center;justify-content:center;background-color:#fff;border-radius:8px;border:#a6a6a6 solid 1px;box-shadow:0 0 10px rgba(0,0,0,.5);margin-right:40px;color:#505050;font-size:16px;font-weight:700;cursor:pointer}.import-tab-item[data-v-a760c48e]:hover{box-shadow:0 0 9px 2px rgba(42,130,228,.65)}.import-tab-item .icon-font-span[data-v-a760c48e]{margin-right:10px}.import-active[data-v-a760c48e]{box-shadow:0 0 9px 2px rgba(42,130,228,.65)}.import-download-template[data-v-a760c48e]{margin:39px 0}.template-title[data-v-a760c48e]{color:#505050;font-size:20px;margin-bottom:24px}.loading[data-v-a760c48e]{margin-left:10px}.export-content[data-v-a760c48e]{margin:39px 0}.import-body-toop[data-v-a760c48e]{color:#a6a6a6;font-size:14px;margin-bottom:20px}.import-table[data-v-a760c48e]{width:100%;height:calc(100% - 180px);margin-top:10px}[data-v-a760c48e] .ant-table-bordered div.ant-table-body:before{width:0px!important}\n",document.head.appendChild(a);const R={class:"import-header"},P={class:"import-body"},D={class:"import-tabs"},V=["onClick"],q={class:"import-content"},A={class:"import-download-template"},B={class:"import-download-template"},G={class:"export-content"},M={class:"import-header"},H={class:"import-body"},J={class:"import-table"},K={key:0,style:{color:"#43cf7c"}},Q={key:1,style:{color:"#ff8d1a"}},W={key:2,style:{color:"red"}},X={key:0},Y={key:0,style:{color:"red"}};e("default",t({__name:"import-export-user",emits:["back","backReload"],setup(e,{emit:a}){const t=a,Z=l([{name:"导入用户",type:"import"},{name:"导出用户",type:"export"}]),$=l("import"),ee=l(!1),ae=l(!1),te=l({containSubOrg:!1}),le=i({orgName:[{required:!0,message:"请选择机构",type:"string",trigger:"change"}],containSubOrg:[{required:!0,message:"包含子公司",type:"boolean",trigger:"change"}]}),ie=l(null),oe=l(null),ne=l({}),se=l("export"),re=l(!1),de=l(!1),ce=l({selectOrgList:[],selectPositionList:[]}),pe=l([]),ue=l(!1),me=l(0),ve=l([{key:"index",title:"序号",width:60,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>e+1},{title:"操作类型",isShow:!0,width:120,align:"center",dataIndex:"operateType"},{title:"属性机构",isShow:!0,width:150,dataIndex:"orgName"},{title:"职位",isShow:!0,width:150,dataIndex:"positionName"},{title:"姓名",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"realName"},{title:"昵称",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"nickName"},{title:"账号",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"account"},{title:"密码",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"password"},{title:"生日",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"birthday"},{title:"性别",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"sex"},{title:"邮箱",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"email"},{title:"手机",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"phone"},{title:"电话",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"tel"},{title:"状态",isShow:!0,width:120,align:"center",ellipsis:!0,dataIndex:"statusFlag"},{title:"排序",isShow:!0,width:120,align:"center",ellipsis:!0,fixed:"right",dataIndex:"userSort"},{title:"工号",isShow:!0,width:120,align:"center",ellipsis:!0,fixed:"right",dataIndex:"employeeNumber"}]),ge=()=>{t("back")},xe=async()=>{await E.getExcelTemplate()},be=e=>{ee.value=!0;let a=new FormData;a.append("file",e.file),E.uploadAndGetPreviewData(a).then((e=>{"00000"==e.code&&(pe.value=e.data.previewData,ue.value=e.data.totalSuccess,h.success(e.message),ae.value=!0)})).finally((()=>{ee.value=!1}))},ye=(e,a,t)=>{if(se.value=e,ce.value.selectOrgList=[],"export"==e){const{orgName:e,orgId:a}=te.value;e&&a&&(ce.value.selectOrgList=[{bizId:a,name:e}])}else if("import"==e){const{orgName:e,orgId:a}=ne.value;e&&a&&(ce.value.selectOrgList=[{bizId:a,name:e}])}else{me.value=t;const{orgName:e,orgId:l}=a;e&&l&&(ce.value.selectOrgList=[{bizId:l,name:e}])}re.value=!0},fe=e=>{const{bizId:a,name:t}=e.selectOrgList[0]||{bizId:"",name:""};"export"==se.value?(te.value.orgId=a,te.value.orgName=t):"import"==se.value?(ne.value.orgId=a,ne.value.orgName=t,pe.value.forEach((e=>{e.orgId=a,e.orgName=t}))):(pe.value[me.value].orgName=t,pe.value[me.value].orgId=a)},he=(e,a,t)=>{if(se.value=e,ce.value.selectPositionList=[],"all"==e){const{positionId:e,positionName:a}=ne.value;e&&a&&(ce.value.selectPositionList=[{bizId:e,name:a}])}else if("row"==e){me.value=t;const{positionId:e,positionName:l}=a;e&&l&&(ce.value.selectPositionList=[{bizId:e,name:l}])}de.value=!0},we=e=>{const{bizId:a,name:t}=e.selectPositionList[0]||{bizId:"",name:""};"all"==se.value?(ne.value.positionId=a,ne.value.positionName=t,pe.value.forEach((e=>{e.positionId=a,e.positionName=t}))):"row"==se.value&&(pe.value[me.value].positionName=t,pe.value[me.value].positionId=a)},Ie=async()=>{await ie.value.validate(),await E.ExportUser(te.value)},ke=()=>{ae.value=!1},Ne=()=>{let e=w(pe.value);e.forEach((e=>{Object.keys(e).forEach((a=>{var t;["orgId","orgName","positionId","positionName"].includes(a)||(e[a]=null===(t=e[a])||void 0===t?void 0:t.submitValue)}))})),E.ensureImport(e).then((e=>{h.success(e.message),ae.value=!1,t("backReload")}))};return(e,a)=>{const t=o("left-outlined"),l=I,i=k,h=N,w=S,E=_,se=z,me=C,Se=j,_e=U,ze=o("exclamation-circle-outlined"),Ce=F,je=T,Ue=L,Fe=O;return n(),s(Fe,{class:"guns-layout-content"},{default:r((()=>[d(c(_e,{span:24,class:"bg-white padding10 import-box"},{default:r((()=>[p("div",R,[c(l,{shape:"circle",class:"import-back",onClick:ge},{icon:r((()=>[c(t)])),_:1}),a[9]||(a[9]=p("span",{class:"import-title"},"导入导出用户",-1))]),p("div",P,[p("div",D,[(n(!0),u(m,null,v(Z.value,(e=>(n(),u("div",{class:g(["import-tab-item",{"import-active":$.value==e.type}]),key:e.type,onClick:a=>{var t;(t=e).type!=$.value&&($.value=t.type)}},["import"==e.type?(n(),s(i,{key:0,iconClass:"icon-opt-import-user",title:"导入",fontSize:"33px",color:"var(--primary-color)"})):(n(),s(i,{key:1,iconClass:"icon-opt-export-user",title:"导出",fontSize:"33px",color:"var(--primary-color)"})),p("span",null,x(e.name),1)],10,V)))),128))]),d(p("div",q,[p("div",A,[a[11]||(a[11]=p("div",{class:"template-title"},"Excel模板下载",-1)),c(l,{type:"primary",class:"border-radius flex",onClick:xe},{default:r((()=>[c(i,{iconClass:"icon-opt-xiazai",title:"下载",fontSize:"20px",color:"#fffff"}),a[10]||(a[10]=b(" 下载模板"))])),_:1,__:[10]})]),p("div",B,[a[13]||(a[13]=p("div",{class:"template-title"},"上传Excel",-1)),c(h,{name:"file",customRequest:be,showUploadList:!1},{default:r((()=>[c(l,{type:"primary",class:"border-radius flex",disabled:ee.value},{default:r((()=>[c(i,{fontSize:"20px",iconClass:"icon-opt-shangchuan",color:"#fffff"}),a[12]||(a[12]=b("上传文件"))])),_:1,__:[12]},8,["disabled"])])),_:1}),c(w,{spinning:ee.value,delay:100,class:"loading"},null,8,["spinning"])])],512),[[y,"import"==$.value]]),d(p("div",G,[a[15]||(a[15]=p("div",{class:"template-title"},"导出用户筛选",-1)),c(Se,{model:te.value,rules:le,layout:"inline",ref_key:"formRef",ref:ie},{default:r((()=>[c(se,{label:"机构筛选：",name:"orgName"},{default:r((()=>[c(E,{value:te.value.orgName,"onUpdate:value":a[0]||(a[0]=e=>te.value.orgName=e),placeholder:"请选择机构",onFocus:a[1]||(a[1]=e=>ye("export"))},null,8,["value"])])),_:1}),c(se,{label:"包含子公司：",name:"containSubOrg"},{default:r((()=>[c(me,{checked:te.value.containSubOrg,"onUpdate:checked":a[2]||(a[2]=e=>te.value.containSubOrg=e)},null,8,["checked"])])),_:1})])),_:1},8,["model","rules"]),c(l,{type:"primary",class:"border-radius flex",style:{"margin-top":"24px"},onClick:Ie},{default:r((()=>[c(i,{iconClass:"icon-opt-daochu",fontSize:"20px",color:"#fff"}),a[14]||(a[14]=b(" 立即导出"))])),_:1,__:[14]})],512),[[y,"export"==$.value]])])])),_:1},512),[[y,!ae.value]]),d(c(_e,{span:24,class:"bg-white padding10 import-box"},{default:r((()=>[p("div",M,[c(l,{shape:"circle",class:"import-back",onClick:ke},{icon:r((()=>[c(t)])),_:1}),a[16]||(a[16]=p("span",{class:"import-title"},"导入结果预览",-1))]),p("div",H,[a[18]||(a[18]=p("pre",{class:"import-body-toop"},"导入成功后，您需要选择这批导入用户的所属机构。\n如果是新增的人员，可以批量设置所属机构，在批量设置后也可以进行手动单个处理所属机构，直接在表格中操作即可。\n如果是修改的人员，默认设置的所属机构不会覆盖该用户已有的机构。\n如确认数据没问题，请点击确认导入。",-1)),c(Se,{model:ne.value,layout:"inline",ref:"importFormRef"},{default:r((()=>[c(se,{label:"批量设置所属机构：",name:"orgName"},{default:r((()=>[c(E,{value:ne.value.orgName,"onUpdate:value":a[3]||(a[3]=e=>ne.value.orgName=e),placeholder:"请选择机构",onFocus:a[4]||(a[4]=e=>ye("import"))},null,8,["value"])])),_:1}),c(se,{label:"批量设置员工职位：",name:"positionName"},{default:r((()=>[c(E,{value:ne.value.positionName,"onUpdate:value":a[5]||(a[5]=e=>ne.value.positionName=e),placeholder:"请选择职位",onFocus:a[6]||(a[6]=e=>he("all"))},null,8,["value"])])),_:1}),c(se,{label:""},{default:r((()=>[ue.value?(n(),s(l,{key:0,type:"primary",class:"border-radius",onClick:Ne},{default:r((()=>a[17]||(a[17]=[b("确认导入")]))),_:1,__:[17]})):f("",!0)])),_:1})])),_:1},8,["model"]),p("div",J,[c(je,{columns:ve.value,bordered:"",ref_key:"tableRef",ref:oe,rowSelection:!1,isPage:!1,dataSource:pe.value},{bodyCell:r((({column:e,record:a,index:t})=>["operateType"==e.dataIndex?(n(),u(m,{key:0},[1==a.operateType.submitValue?(n(),u("span",K,x(a.operateType.value),1)):f("",!0),2==a.operateType.submitValue?(n(),u("span",Q,x(a.operateType.value),1)):f("",!0),3==a.operateType.submitValue?(n(),u("span",W,x(a.operateType.value),1)):f("",!0)],64)):f("",!0),["realName","nickName","account","password","birthday","sex","email","phone","tel","statusFlag","userSort","employeeNumber"].includes(e.dataIndex)?(n(),u(m,{key:1},[a[e.dataIndex].validateResult?(n(),u("span",X,x(a[e.dataIndex].value),1)):(n(),s(Ce,{key:1},{title:r((()=>[b(x(a[e.dataIndex].errorMessage),1)])),default:r((()=>[a[e.dataIndex].value?(n(),u("span",Y,x(a[e.dataIndex].value),1)):(n(),s(ze,{key:1,style:{color:"red"}}))])),_:2},1024))],64)):f("",!0),"orgName"==e.dataIndex?(n(),s(E,{key:2,value:a.orgName,"onUpdate:value":e=>a.orgName=e,onFocus:e=>ye("preview",a,t)},null,8,["value","onUpdate:value","onFocus"])):f("",!0),"positionName"==e.dataIndex?(n(),s(E,{key:3,value:a.positionName,"onUpdate:value":e=>a.positionName=e,onFocus:e=>he("row",a,t)},null,8,["value","onUpdate:value","onFocus"])):f("",!0)])),_:1},8,["columns","dataSource"])])])])),_:1},512),[[y,ae.value]]),re.value?(n(),s(Ue,{key:0,visible:re.value,"onUpdate:visible":a[7]||(a[7]=e=>re.value=e),title:"选择机构",data:ce.value,showTab:["dept"],onDone:fe},null,8,["visible","data"])):f("",!0),de.value?(n(),s(Ue,{key:1,visible:de.value,"onUpdate:visible":a[8]||(a[8]=e=>de.value=e),title:"选择职位",data:ce.value,showTab:["position"],onDone:we},null,8,["visible","data"])):f("",!0)])),_:1})}}},[["__scopeId","data-v-a760c48e"]]))}}}));
