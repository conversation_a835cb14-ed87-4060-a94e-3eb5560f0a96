System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var r,a,u,o;return{setters:[e=>{r=e.am,a=e.r,u=e.L,o=e.m}],execute:function(){e("u",r("pos",(()=>{const e=a([]),t=a(0),r=a(0),c=a(0),n=a(null),l=a(0),s=a(0),i=a("idle"),v=a(""),d=a(0),m=a(0),p=a([]),y=a(1),h=a([]),g=a(null),S=a([]),f=a(""),A=u((()=>e.value.reduce(((e,t)=>e+t.quantity),0))),I=u((()=>e.value.length>0)),P=u((()=>I.value&&"processing"!==i.value)),D=u((()=>null!==n.value)),C=u((()=>p.value.length>0)),O=t=>{try{const r=e.value.findIndex((e=>e.productId===t));if(r>-1){const t=e.value.splice(r,1)[0];b(),o.success(`已移除 ${t.productName}`)}}catch(r){console.error("移除商品失败:",r),o.error("移除商品失败，请重试")}},N=()=>{try{e.value=[],t.value=0,r.value=0,c.value=0,s.value=0,o.success("购物车已清空")}catch(a){console.error("清空购物车失败:",a),o.error("清空购物车失败")}},b=()=>{try{t.value=e.value.reduce(((e,t)=>e+t.totalPrice),0);let a=0;D.value&&l.value>0&&(a=t.value*(l.value/100)),r.value=a,c.value=Math.max(0,t.value-r.value-s.value)}catch(a){console.error("计算总金额失败:",a)}},M=()=>{try{n.value=null,l.value=0,s.value=0,b(),o.success("已取消会员绑定")}catch(e){console.error("清除会员信息失败:",e)}},w=a(100),_=()=>{i.value="idle",v.value="",d.value=0,m.value=0},R=(e=24)=>{try{const t=Date.now()-60*e*60*1e3,r=p.value.length;p.value=p.value.filter((e=>new Date(e.suspendTime).getTime()>t));const a=r-p.value.length;return a>0&&($(),o.info(`已清理 ${a} 个过期挂单`)),a}catch(t){return console.error("清理过期挂单失败:",t),0}},$=()=>{try{const e={suspendedOrders:p.value,suspendCounter:y.value,timestamp:Date.now()};localStorage.setItem("pos_suspended_orders",JSON.stringify(e))}catch(e){console.error("保存挂单数据到本地存储失败:",e)}},x=()=>{try{const a=localStorage.getItem("pos_current_cart");if(a){const u=JSON.parse(a),i=Date.now()-36e5;u.timestamp&&u.timestamp>i?(e.value=u.cartItems||[],t.value=u.totalAmount||0,r.value=u.discountAmount||0,c.value=u.finalAmount||0,n.value=u.currentMember||null,l.value=u.memberDiscountRate||0,s.value=u.pointsDeductionAmount||0,e.value.length>0&&o.info("已恢复上次未完成的订单")):localStorage.removeItem("pos_current_cart")}}catch(a){console.error("从本地存储恢复购物车失败:",a),localStorage.removeItem("pos_current_cart")}},J=()=>{try{localStorage.removeItem("pos_current_cart")}catch(e){console.error("清除本地购物车数据失败:",e)}},T=e=>"number"!=typeof e?"0.00":e.toFixed(2);return{cartItems:e,totalAmount:t,discountAmount:r,finalAmount:c,currentMember:n,memberDiscountRate:l,pointsDeductionAmount:s,pointsExchangeRate:w,paymentStatus:i,selectedPaymentMethod:v,receivedAmount:d,changeAmount:m,suspendedOrders:p,categories:h,selectedCategory:g,products:S,searchKeyword:f,cartItemCount:A,hasCartItems:I,canCheckout:P,hasMember:D,hasSuspendedOrders:C,addToCart:(t,r=1)=>{try{if(t.stock<r)return o.warning(`商品 ${t.productName} 库存不足`),!1;const a=e.value.find((e=>e.productId===t.productId));if(a){const e=a.quantity+r;if(t.stock<e)return o.warning(`商品 ${t.productName} 库存不足`),!1;a.quantity=e,a.totalPrice=a.unitPrice*e}else e.value.push({productId:t.productId,productName:t.productName,productCode:t.productCode,unitPrice:t.price||t.retailPrice||t.unitPrice,quantity:r,totalPrice:(t.price||t.retailPrice||t.unitPrice)*r,unit:t.unit,pricingType:t.pricingType,retailPrice:t.retailPrice,piecePrice:t.piecePrice,referencePrice:t.referencePrice,imageUrl:t.imageUrl});return b(),o.success(`已添加 ${t.productName} 到购物车`),!0}catch(a){return console.error("添加商品到购物车失败:",a),o.error("添加商品失败，请重试"),!1}},removeFromCart:O,updateQuantity:(t,r)=>{try{if(r<=0)return void O(t);const a=e.value.find((e=>e.productId===t));a&&(a.quantity=r,a.totalPrice=a.unitPrice*r,b())}catch(a){console.error("更新商品数量失败:",a),o.error("更新数量失败，请重试")}},clearCart:N,calculateTotal:b,setCurrentMember:e=>{try{n.value=e,e&&e.discountRate?l.value=e.discountRate:l.value=0,b(),e&&o.success(`已绑定会员：${e.memberName}`)}catch(t){console.error("设置会员信息失败:",t),o.error("设置会员信息失败")}},clearCurrentMember:M,setPointsDeduction:(e,a)=>{try{a<0&&(a=0);const u=t.value-r.value;a>u&&(a=u),s.value=a,n.value&&(n.value.usedPoints=e),b()}catch(u){console.error("设置积分抵扣失败:",u),o.error("设置积分抵扣失败")}},applyMemberDiscount:async e=>{try{if(!e||t.value<=0)return;n.value&&n.value.discountRate&&(l.value=n.value.discountRate),b()}catch(r){console.error("应用会员折扣失败:",r)}},clearMemberDiscount:()=>{try{l.value=0,s.value=0,b()}catch(e){console.error("清除会员折扣失败:",e)}},setPaymentMethod:e=>{v.value=e,"CASH"!==e&&(d.value=0,m.value=0)},setReceivedAmount:e=>{try{d.value=e,m.value=Math.max(0,e-c.value)}catch(t){console.error("设置实收金额失败:",t)}},startPayment:()=>{i.value="processing"},paymentSuccess:()=>{i.value="success",o.success("支付成功")},paymentFailed:(e="支付失败")=>{i.value="failed",o.error(e)},resetPaymentStatus:_,completeOrder:()=>{try{N(),M(),_(),o.success("订单完成")}catch(e){console.error("完成订单失败:",e)}},suspendCurrentOrder:(a="")=>{try{if(!I.value)return o.warning("购物车为空，无法挂单"),!1;const u=`SUSPEND-${Date.now()}-${y.value.toString().padStart(3,"0")}`;y.value++;const i={suspendId:Date.now(),suspendNo:u,suspendTime:(new Date).toISOString(),remark:a,orderData:{cartItems:JSON.parse(JSON.stringify(e.value)),totalAmount:t.value,discountAmount:r.value,finalAmount:c.value,currentMember:n.value?JSON.parse(JSON.stringify(n.value)):null,memberDiscountRate:l.value,pointsDeductionAmount:s.value}};return p.value.push(i),$(),N(),M(),o.success(`订单已挂起，挂单号：${u}`),!0}catch(u){return console.error("挂单失败:",u),o.error("挂单失败，请重试"),!1}},resumeSuspendedOrder:a=>{try{const u=p.value.find((e=>e.suspendId===a));if(!u)return o.error("挂单不存在"),!1;I.value&&o.warning("当前购物车将被清空");const i=u.orderData;e.value=i.cartItems||[],t.value=i.totalAmount||0,r.value=i.discountAmount||0,c.value=i.finalAmount||0,i.currentMember&&(n.value=i.currentMember,l.value=i.memberDiscountRate||0,s.value=i.pointsDeductionAmount||0);const v=p.value.findIndex((e=>e.suspendId===a));return v>-1&&(p.value.splice(v,1),$()),o.success(`已恢复挂单：${u.suspendNo}`),!0}catch(u){return console.error("恢复挂单失败:",u),o.error("恢复挂单失败，请重试"),!1}},deleteSuspendedOrder:e=>{try{const t=p.value.findIndex((t=>t.suspendId===e));if(t>-1){const e=p.value.splice(t,1)[0];return $(),o.success(`已删除挂单：${e.suspendNo}`),!0}return!1}catch(t){return console.error("删除挂单失败:",t),o.error("删除挂单失败"),!1}},clearExpiredSuspendedOrders:R,saveCartToLocal:()=>{try{const a={cartItems:e.value,totalAmount:t.value,discountAmount:r.value,finalAmount:c.value,currentMember:n.value,memberDiscountRate:l.value,pointsDeductionAmount:s.value,timestamp:Date.now()};localStorage.setItem("pos_current_cart",JSON.stringify(a))}catch(a){console.error("保存购物车到本地存储失败:",a)}},loadCartFromLocal:x,clearCartFromLocal:J,setCategories:e=>{h.value=e||[]},setSelectedCategory:e=>{g.value=e},setProducts:e=>{S.value=e||[]},setSearchKeyword:e=>{f.value=e||""},formatAmount:T,getCartSummary:()=>({itemCount:A.value,totalAmount:T(t.value),discountAmount:T(r.value),finalAmount:T(c.value),hasItems:I.value,canCheckout:P.value}),resetAllState:()=>{try{N(),M(),_(),h.value=[],g.value=null,S.value=[],f.value="",J(),o.success("状态已重置")}catch(e){console.error("重置状态失败:",e)}},initializeStore:()=>{try{(()=>{try{const e=localStorage.getItem("pos_suspended_orders");if(e){const t=JSON.parse(e);p.value=t.suspendedOrders||[],y.value=t.suspendCounter||1,R()}}catch(e){console.error("从本地存储加载挂单数据失败:",e),p.value=[],y.value=1}})(),x(),console.log("POS Store 初始化完成")}catch(e){console.error("POS Store 初始化失败:",e)}}}})))}}}));
