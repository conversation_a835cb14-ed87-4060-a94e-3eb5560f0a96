package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.erp.api.constants.ErpConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * POS系统异常枚举
 *
 * <AUTHOR>
 * @since 2025/08/01 10:00
 */
@Getter
public enum ErpPosExceptionEnum implements AbstractExceptionEnum {

    /**
     * 订单不存在
     */
    ORDER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "81", "订单不存在"),

    /**
     * 订单状态不正确
     */
    ORDER_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "82", "订单状态不正确"),

    /**
     * 订单已支付，无法修改
     */
    ORDER_ALREADY_PAID(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "83", "订单已支付，无法修改"),

    /**
     * 订单金额不正确
     */
    ORDER_AMOUNT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "84", "订单金额不正确"),

    /**
     * 支付金额不足
     */
    PAYMENT_AMOUNT_INSUFFICIENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "85", "支付金额不足"),

    /**
     * 支付方式不支持
     */
    PAYMENT_METHOD_NOT_SUPPORTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "86", "支付方式不支持"),

    /**
     * 支付失败
     */
    PAYMENT_FAILED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "87", "支付失败"),

    /**
     * 退款金额超过订单金额
     */
    REFUND_AMOUNT_EXCEED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "88", "退款金额超过订单金额"),

    /**
     * 挂单不存在
     */
    SUSPEND_ORDER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "89", "挂单不存在"),

    /**
     * 挂单已恢复，无法重复操作
     */
    SUSPEND_ORDER_ALREADY_RESTORED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "90", "挂单已恢复，无法重复操作"),

    /**
     * 商品库存不足
     */
    PRODUCT_STOCK_INSUFFICIENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "91", "商品库存不足"),

    /**
     * 商品不存在或已下架
     */
    PRODUCT_NOT_AVAILABLE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "92", "商品不存在或已下架"),

    /**
     * 会员不存在
     */
    MEMBER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "93", "会员不存在"),

    /**
     * 会员积分不足
     */
    MEMBER_POINTS_INSUFFICIENT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "94", "会员积分不足"),

    /**
     * 会员卡已过期
     */
    MEMBER_CARD_EXPIRED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "95", "会员卡已过期"),

    /**
     * 购物车为空
     */
    CART_IS_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "96", "购物车为空"),

    /**
     * 商品数量必须大于0
     */
    PRODUCT_QUANTITY_INVALID(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "97", "商品数量必须大于0"),

    /**
     * 折扣率不正确
     */
    DISCOUNT_RATE_INVALID(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "98", "折扣率不正确，应在0-1之间"),

    /**
     * 操作失败，请重试
     */
    OPERATION_FAILED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "99", "操作失败，请重试"),

    /**
     * 退款失败
     */
    REFUND_FAILED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "A1", "退款失败"),

    /**
     * 退款金额无效
     */
    REFUND_AMOUNT_INVALID(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "A2", "退款金额无效");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ErpPosExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
