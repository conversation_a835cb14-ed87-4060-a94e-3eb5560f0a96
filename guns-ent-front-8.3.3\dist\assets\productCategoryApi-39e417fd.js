import{R as a}from"./index-18a1ea24.js";class e{static async findPage(t){return await a.getAndLoadData("/erp/productCategory/page",t)}static async findList(t){return await a.getAndLoadData("/erp/productCategory/list",t)}static async findTree(t){return await a.getAndLoadData("/erp/productCategory/tree",t)}static async findTreeWithLazy(t){return await a.getAndLoadData("/erp/productCategory/treeWithLazy",t)}static async findTreeForSelector(t){return await a.getAndLoadData("/erp/productCategory/treeForSelector",t)}static async batchDelete(t){return await a.post("/erp/productCategory/batchDelete",t)}static async add(t){return await a.post("/erp/productCategory/add",t)}static async edit(t){return await a.post("/erp/productCategory/edit",t)}static async delete(t){return await a.post("/erp/productCategory/delete",t)}static async detail(t){return await a.getAndLoadData("/erp/productCategory/detail",t)}}const d=e.findTree;export{e as P,d as g};
