import{_ as q}from"./index-02bf6f00.js";import{_ as G,r as u,b3 as H,L as J,o as K,k as y,bv as Q,a as i,c as v,b as o,d as t,w as n,aR as f,f as w,g as E,t as X,h as p,M as T,E as A,m as g,n as Y,B as Z,I as ee,p as te,q as se,D as oe,l as ne}from"./index-18a1ea24.js";import{A as I}from"./AppApi-4e70edf8.js";import ae from"./app-add-edit-76cf409d.js";/* empty css              *//* empty css              *//* empty css              */import"./app-form-ae67ea2d.js";/* empty css              *//* empty css              */import"./FileApi-418f4d35.js";const le={class:"guns-layout"},ie={class:"guns-layout-content"},ce={class:"guns-layout"},de={class:"guns-layout-content-application"},ue={class:"content-mian"},pe={class:"content-mian-header"},re={class:"header-content"},_e={class:"header-content-left"},me={class:"header-content-right"},he={class:"content-mian-body"},ve={class:"table-content"},fe=["onClick"],we=["src"],ge=Object.assign({name:"AuthRole"},{__name:"index",setup(be){const D=u([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"appName",title:"\u5E94\u7528\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"appCode",title:"\u5E94\u7528\u7F16\u7801",width:100,isShow:!0},{dataIndex:"appIconWrapper",title:"\u5E94\u7528\u56FE\u6807",width:100,isShow:!0},{dataIndex:"statusFlag",title:"\u72B6\u6001",width:100,isShow:!0},{dataIndex:"appSort",title:"\u6392\u5E8F",width:100,isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:150,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}]),c=u(null),b=u({searchText:""}),C=u(null),r=u(!1),P=H(),L=J(()=>!P.authorities.find(s=>s=="UPDATE_USER_STATUS"));K(()=>{});const B=({key:s})=>{s=="1"&&R()},_=()=>{c.value.reload()},x=s=>{C.value=s,r.value=!0},F=s=>{T.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u5E94\u7528\u5417?",icon:t(A),maskClosable:!0,onOk:async()=>{const e=await I.delete({appId:s.appId});g.success(e.message),_()}})},R=()=>{if(c.value.selectedRowList&&c.value.selectedRowList.length==0)return g.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u5E94\u7528");T.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u5E94\u7528\u5417?",icon:t(A),maskClosable:!0,onOk:async()=>{const s=await I.batchDelete({appIdList:c.value.selectedRowList});g.success(s.message),_()}})},U=s=>{I.updateStatus({appId:s.appId,statusFlag:s.statusFlag}).then(e=>{g.success(e.message)})};return(s,e)=>{const k=Y,V=y("plus-outlined"),S=Z,m=ee,N=te,$=se,z=y("small-dash-outlined"),M=oe,O=ne,W=y("vxe-switch"),j=q,h=Q("permission");return i(),v("div",le,[o("div",ie,[o("div",ce,[o("div",de,[o("div",ue,[o("div",pe,[o("div",re,[o("div",_e,[t(k,{size:16})]),o("div",me,[t(k,{size:16},{default:n(()=>[f((i(),w(S,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=a=>x())},{default:n(()=>[t(V),e[3]||(e[3]=E("\u65B0\u5EFA"))]),_:1,__:[3]})),[[h,["ADD_APP"]]]),t(M,null,{overlay:n(()=>[t($,{onClick:B},{default:n(()=>[f((i(),v("div",null,[t(N,{key:"1"},{default:n(()=>[t(m,{iconClass:"icon-opt-shanchu",color:"#60666b"}),e[4]||(e[4]=o("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[4]})])),[[h,["DELETE_APP"]]])]),_:1})]),default:n(()=>[t(S,{class:"border-radius"},{default:n(()=>[e[5]||(e[5]=E(" \u66F4\u591A ")),t(z)]),_:1,__:[5]})]),_:1})]),_:1})])])]),o("div",he,[o("div",ve,[t(j,{columns:D.value,where:b.value,showToolTotal:!1,showTableTool:"",rowId:"appId",ref_key:"tableRef",ref:c,url:"/sysApp/page",fieldBusinessCode:"APP_TABLE"},{toolLeft:n(()=>[t(O,{value:b.value.searchText,"onUpdate:value":e[1]||(e[1]=a=>b.value.searchText=a),placeholder:"\u5E94\u7528\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:_,bordered:!1,style:{width:"240px"},class:"search-input"},{prefix:n(()=>[t(m,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:n(({column:a,record:l})=>[a.dataIndex=="appName"?(i(),v("a",{key:0,onClick:d=>x(l)},X(l.appName),9,fe)):p("",!0),a.dataIndex=="appIconWrapper"?(i(),v("img",{key:1,src:l.appIconWrapper,alt:"",class:"appIconWrapper"},null,8,we)):p("",!0),a.dataIndex=="statusFlag"?(i(),w(W,{key:2,modelValue:l.statusFlag,"onUpdate:modelValue":d=>l.statusFlag=d,"open-value":1,"close-value":2,onChange:d=>U(l),disabled:L.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])):p("",!0),a.key=="action"?(i(),w(k,{key:3,size:16},{default:n(()=>[f(t(m,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:d=>x(l)},null,8,["onClick"]),[[h,["EDIT_APP"]]]),f(t(m,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:d=>F(l)},null,8,["onClick"]),[[h,["DELETE_APP"]]])]),_:2},1024)):p("",!0)]),_:1},8,["columns","where"])])])])])])]),r.value?(i(),w(ae,{key:0,visible:r.value,"onUpdate:visible":e[2]||(e[2]=a=>r.value=a),data:C.value,onDone:_},null,8,["visible","data"])):p("",!0)])}}}),Le=G(ge,[["__scopeId","data-v-ecda94dc"]]);export{Le as default};
