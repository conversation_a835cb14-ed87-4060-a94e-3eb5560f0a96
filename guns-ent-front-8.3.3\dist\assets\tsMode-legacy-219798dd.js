!function(){function e(e,t,i){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}System.register(["./index-legacy-db773591.js","./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js"],(function(t,i){"use strict";var s,r;return{setters:[e=>{s=e.t,r=e.m},null,null],execute:function(){t({flattenDiagnosticMessageText:h,getJavaScriptWorker:function(){return new Promise(((e,t)=>{if(!T)return t("JavaScript not registered!");e(T)}))},getTypeScriptWorker:function(){return new Promise(((e,t)=>{if(!L)return t("TypeScript not registered!");e(L)}))},setupJavaScript:function(e){T=E(e,"javascript")},setupTypeScript:function(e){L=E(e,"typescript")}});
/*!-----------------------------------------------------------------------------
       * Copyright (c) Microsoft Corporation. All rights reserved.
       * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
       * Released under the MIT license
       * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
       *-----------------------------------------------------------------------------*/
var i,n,a=Object.defineProperty,o=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,c=Object.prototype.hasOwnProperty,d=(e,t,i,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of l(t))c.call(e,r)||r===i||a(e,r,{get:()=>t[r],enumerable:!(s=o(t,r))||s.enumerable});return e},u=(e,t,i)=>(((e,t,i)=>{t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i})(e,"symbol"!=typeof t?t+"":t,i),i),g={};d(g,i=r,"default"),n&&d(n,i,"default");var p=t("WorkerManager",class{constructor(t,i){e(this,"_configChangeListener",void 0),e(this,"_updateExtraLibsToken",void 0),e(this,"_extraLibsChangeListener",void 0),e(this,"_worker",void 0),e(this,"_client",void 0),this._modeId=t,this._defaults=i,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange((()=>this._stopWorker())),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange((()=>this._updateExtraLibs()))}dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}async _updateExtraLibs(){if(!this._worker)return;const e=++this._updateExtraLibsToken,t=await this._worker.getProxy();this._updateExtraLibsToken===e&&t.updateExtraLibs(this._defaults.getExtraLibs())}_getClient(){return this._client||(this._client=(async()=>(this._worker=g.editor.createWebWorker({moduleId:"vs/language/typescript/tsWorker",label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}}),this._defaults.getEagerModelSync()?await this._worker.withSyncedResources(g.editor.getModels().filter((e=>e.getLanguageId()===this._modeId)).map((e=>e.uri))):await this._worker.getProxy()))()),this._client}async getLanguageServiceWorker(...e){const t=await this._getClient();return this._worker&&await this._worker.withSyncedResources(e),t}}),m={};function h(e,t,i=0){if("string"==typeof e)return e;if(void 0===e)return"";let s="";if(i){s+=t;for(let e=0;e<i;e++)s+="  "}if(s+=e.messageText,i++,e.next)for(const r of e.next)s+=h(r,t,i);return s}function b(e){return e?e.map((e=>e.text)).join(""):""}m["lib.d.ts"]=!0,m["lib.decorators.d.ts"]=!0,m["lib.decorators.legacy.d.ts"]=!0,m["lib.dom.d.ts"]=!0,m["lib.dom.iterable.d.ts"]=!0,m["lib.es2015.collection.d.ts"]=!0,m["lib.es2015.core.d.ts"]=!0,m["lib.es2015.d.ts"]=!0,m["lib.es2015.generator.d.ts"]=!0,m["lib.es2015.iterable.d.ts"]=!0,m["lib.es2015.promise.d.ts"]=!0,m["lib.es2015.proxy.d.ts"]=!0,m["lib.es2015.reflect.d.ts"]=!0,m["lib.es2015.symbol.d.ts"]=!0,m["lib.es2015.symbol.wellknown.d.ts"]=!0,m["lib.es2016.array.include.d.ts"]=!0,m["lib.es2016.d.ts"]=!0,m["lib.es2016.full.d.ts"]=!0,m["lib.es2017.d.ts"]=!0,m["lib.es2017.full.d.ts"]=!0,m["lib.es2017.intl.d.ts"]=!0,m["lib.es2017.object.d.ts"]=!0,m["lib.es2017.sharedmemory.d.ts"]=!0,m["lib.es2017.string.d.ts"]=!0,m["lib.es2017.typedarrays.d.ts"]=!0,m["lib.es2018.asyncgenerator.d.ts"]=!0,m["lib.es2018.asynciterable.d.ts"]=!0,m["lib.es2018.d.ts"]=!0,m["lib.es2018.full.d.ts"]=!0,m["lib.es2018.intl.d.ts"]=!0,m["lib.es2018.promise.d.ts"]=!0,m["lib.es2018.regexp.d.ts"]=!0,m["lib.es2019.array.d.ts"]=!0,m["lib.es2019.d.ts"]=!0,m["lib.es2019.full.d.ts"]=!0,m["lib.es2019.intl.d.ts"]=!0,m["lib.es2019.object.d.ts"]=!0,m["lib.es2019.string.d.ts"]=!0,m["lib.es2019.symbol.d.ts"]=!0,m["lib.es2020.bigint.d.ts"]=!0,m["lib.es2020.d.ts"]=!0,m["lib.es2020.date.d.ts"]=!0,m["lib.es2020.full.d.ts"]=!0,m["lib.es2020.intl.d.ts"]=!0,m["lib.es2020.number.d.ts"]=!0,m["lib.es2020.promise.d.ts"]=!0,m["lib.es2020.sharedmemory.d.ts"]=!0,m["lib.es2020.string.d.ts"]=!0,m["lib.es2020.symbol.wellknown.d.ts"]=!0,m["lib.es2021.d.ts"]=!0,m["lib.es2021.full.d.ts"]=!0,m["lib.es2021.intl.d.ts"]=!0,m["lib.es2021.promise.d.ts"]=!0,m["lib.es2021.string.d.ts"]=!0,m["lib.es2021.weakref.d.ts"]=!0,m["lib.es2022.array.d.ts"]=!0,m["lib.es2022.d.ts"]=!0,m["lib.es2022.error.d.ts"]=!0,m["lib.es2022.full.d.ts"]=!0,m["lib.es2022.intl.d.ts"]=!0,m["lib.es2022.object.d.ts"]=!0,m["lib.es2022.regexp.d.ts"]=!0,m["lib.es2022.sharedmemory.d.ts"]=!0,m["lib.es2022.string.d.ts"]=!0,m["lib.es2023.array.d.ts"]=!0,m["lib.es2023.d.ts"]=!0,m["lib.es2023.full.d.ts"]=!0,m["lib.es5.d.ts"]=!0,m["lib.es6.d.ts"]=!0,m["lib.esnext.d.ts"]=!0,m["lib.esnext.full.d.ts"]=!0,m["lib.esnext.intl.d.ts"]=!0,m["lib.scripthost.d.ts"]=!0,m["lib.webworker.d.ts"]=!0,m["lib.webworker.importscripts.d.ts"]=!0,m["lib.webworker.iterable.d.ts"]=!0;var f=t("Adapter",class{constructor(e){this._worker=e}_textSpanToRange(e,t){let i=e.getPositionAt(t.start),s=e.getPositionAt(t.start+t.length),{lineNumber:r,column:n}=i,{lineNumber:a,column:o}=s;return{startLineNumber:r,startColumn:n,endLineNumber:a,endColumn:o}}}),y=t("LibFiles",class{constructor(t){e(this,"_libFiles",void 0),e(this,"_hasFetchedLibFiles",void 0),e(this,"_fetchLibFilesPromise",void 0),this._worker=t,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}isLibFile(e){return!!e&&(0===e.path.indexOf("/lib.")&&!!m[e.path.slice(1)])}getOrCreateModel(e){const t=g.Uri.parse(e),i=g.editor.getModel(t);if(i)return i;if(this.isLibFile(t)&&this._hasFetchedLibFiles)return g.editor.createModel(this._libFiles[t.path.slice(1)],"typescript",t);const r=s.getExtraLibs()[e];return r?g.editor.createModel(r.content,"typescript",t):null}_containsLibFile(e){for(let t of e)if(this.isLibFile(t))return!0;return!1}async fetchLibFilesIfNecessary(e){this._containsLibFile(e)&&await this._fetchLibFiles()}_fetchLibFiles(){return this._fetchLibFilesPromise||(this._fetchLibFilesPromise=this._worker().then((e=>e.getLibFiles())).then((e=>{this._hasFetchedLibFiles=!0,this._libFiles=e}))),this._fetchLibFilesPromise}}),_=t("DiagnosticsAdapter",class extends f{constructor(t,i,s,r){super(r),e(this,"_disposables",[]),e(this,"_listener",Object.create(null)),this._libFiles=t,this._defaults=i,this._selector=s;const n=e=>{if(e.getLanguageId()!==s)return;const t=()=>{const{onlyVisible:t}=this._defaults.getDiagnosticsOptions();t?e.isAttachedToEditor()&&this._doValidate(e):this._doValidate(e)};let i;const r=e.onDidChangeContent((()=>{clearTimeout(i),i=window.setTimeout(t,500)})),n=e.onDidChangeAttached((()=>{const{onlyVisible:i}=this._defaults.getDiagnosticsOptions();i&&(e.isAttachedToEditor()?t():g.editor.setModelMarkers(e,this._selector,[]))}));this._listener[e.uri.toString()]={dispose(){r.dispose(),n.dispose(),clearTimeout(i)}},t()},a=e=>{g.editor.setModelMarkers(e,this._selector,[]);const t=e.uri.toString();this._listener[t]&&(this._listener[t].dispose(),delete this._listener[t])};this._disposables.push(g.editor.onDidCreateModel((e=>n(e)))),this._disposables.push(g.editor.onWillDisposeModel(a)),this._disposables.push(g.editor.onDidChangeModelLanguage((e=>{a(e.model),n(e.model)}))),this._disposables.push({dispose(){for(const e of g.editor.getModels())a(e)}});const o=()=>{for(const e of g.editor.getModels())a(e),n(e)};this._disposables.push(this._defaults.onDidChange(o)),this._disposables.push(this._defaults.onDidExtraLibsChange(o)),g.editor.getModels().forEach((e=>n(e)))}dispose(){this._disposables.forEach((e=>e&&e.dispose())),this._disposables=[]}async _doValidate(e){const t=await this._worker(e.uri);if(e.isDisposed())return;const i=[],{noSyntaxValidation:s,noSemanticValidation:r,noSuggestionDiagnostics:n}=this._defaults.getDiagnosticsOptions();s||i.push(t.getSyntacticDiagnostics(e.uri.toString())),r||i.push(t.getSemanticDiagnostics(e.uri.toString())),n||i.push(t.getSuggestionDiagnostics(e.uri.toString()));const a=await Promise.all(i);if(!a||e.isDisposed())return;const o=a.reduce(((e,t)=>t.concat(e)),[]).filter((e=>-1===(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(e.code))),l=o.map((e=>e.relatedInformation||[])).reduce(((e,t)=>t.concat(e)),[]).map((e=>e.file?g.Uri.parse(e.file.fileName):null));await this._libFiles.fetchLibFilesIfNecessary(l),e.isDisposed()||g.editor.setModelMarkers(e,this._selector,o.map((t=>this._convertDiagnostics(e,t))))}_convertDiagnostics(e,t){const i=t.start||0,s=t.length||1,{lineNumber:r,column:n}=e.getPositionAt(i),{lineNumber:a,column:o}=e.getPositionAt(i+s),l=[];return t.reportsUnnecessary&&l.push(g.MarkerTag.Unnecessary),t.reportsDeprecated&&l.push(g.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(t.category),startLineNumber:r,startColumn:n,endLineNumber:a,endColumn:o,message:h(t.messageText,"\n"),code:t.code.toString(),tags:l,relatedInformation:this._convertRelatedInformation(e,t.relatedInformation)}}_convertRelatedInformation(e,t){if(!t)return[];const i=[];return t.forEach((t=>{let s=e;if(t.file&&(s=this._libFiles.getOrCreateModel(t.file.fileName)),!s)return;const r=t.start||0,n=t.length||1,{lineNumber:a,column:o}=s.getPositionAt(r),{lineNumber:l,column:c}=s.getPositionAt(r+n);i.push({resource:s.uri,startLineNumber:a,startColumn:o,endLineNumber:l,endColumn:c,message:h(t.messageText,"\n")})})),i}_tsDiagnosticCategoryToMarkerSeverity(e){switch(e){case 1:return g.MarkerSeverity.Error;case 3:return g.MarkerSeverity.Info;case 0:return g.MarkerSeverity.Warning;case 2:return g.MarkerSeverity.Hint}return g.MarkerSeverity.Info}}),w=t("SuggestAdapter",class extends f{get triggerCharacters(){return["."]}async provideCompletionItems(e,t,i,s){const r=e.getWordUntilPosition(t),n=new g.Range(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),a=e.uri,o=e.getOffsetAt(t),l=await this._worker(a);if(e.isDisposed())return;const c=await l.getCompletionsAtPosition(a.toString(),o);if(!c||e.isDisposed())return;return{suggestions:c.entries.map((i=>{let s=n;if(i.replacementSpan){const t=e.getPositionAt(i.replacementSpan.start),r=e.getPositionAt(i.replacementSpan.start+i.replacementSpan.length);s=new g.Range(t.lineNumber,t.column,r.lineNumber,r.column)}const r=[];return void 0!==i.kindModifiers&&-1!==i.kindModifiers.indexOf("deprecated")&&r.push(g.languages.CompletionItemTag.Deprecated),{uri:a,position:t,offset:o,range:s,label:i.name,insertText:i.name,sortText:i.sortText,kind:w.convertKind(i.kind),tags:r}}))}}async resolveCompletionItem(e,t){const i=e,s=i.uri,r=i.position,n=i.offset,a=await this._worker(s),o=await a.getCompletionEntryDetails(s.toString(),n,i.label);return o?{uri:s,position:r,label:o.name,kind:w.convertKind(o.kind),detail:b(o.displayParts),documentation:{value:w.createDocumentationString(o)}}:i}static convertKind(e){switch(e){case A.primitiveType:case A.keyword:return g.languages.CompletionItemKind.Keyword;case A.variable:case A.localVariable:return g.languages.CompletionItemKind.Variable;case A.memberVariable:case A.memberGetAccessor:case A.memberSetAccessor:return g.languages.CompletionItemKind.Field;case A.function:case A.memberFunction:case A.constructSignature:case A.callSignature:case A.indexSignature:return g.languages.CompletionItemKind.Function;case A.enum:return g.languages.CompletionItemKind.Enum;case A.module:return g.languages.CompletionItemKind.Module;case A.class:return g.languages.CompletionItemKind.Class;case A.interface:return g.languages.CompletionItemKind.Interface;case A.warning:return g.languages.CompletionItemKind.File}return g.languages.CompletionItemKind.Property}static createDocumentationString(e){let t=b(e.documentation);if(e.tags)for(const i of e.tags)t+=`\n\n${S(i)}`;return t}});function S(e){let t=`*@${e.name}*`;if("param"===e.name&&e.text){const[i,...s]=e.text;t+=`\`${i.text}\``,s.length>0&&(t+=` — ${s.map((e=>e.text)).join(" ")}`)}else Array.isArray(e.text)?t+=` — ${e.text.map((e=>e.text)).join(" ")}`:e.text&&(t+=` — ${e.text}`);return t}var v=t("SignatureHelpAdapter",class extends f{constructor(...t){super(...t),e(this,"signatureHelpTriggerCharacters",["(",","])}static _toSignatureHelpTriggerReason(e){switch(e.triggerKind){case g.languages.SignatureHelpTriggerKind.TriggerCharacter:return e.triggerCharacter?e.isRetrigger?{kind:"retrigger",triggerCharacter:e.triggerCharacter}:{kind:"characterTyped",triggerCharacter:e.triggerCharacter}:{kind:"invoked"};case g.languages.SignatureHelpTriggerKind.ContentChange:return e.isRetrigger?{kind:"retrigger"}:{kind:"invoked"};case g.languages.SignatureHelpTriggerKind.Invoke:default:return{kind:"invoked"}}}async provideSignatureHelp(e,t,i,s){const r=e.uri,n=e.getOffsetAt(t),a=await this._worker(r);if(e.isDisposed())return;const o=await a.getSignatureHelpItems(r.toString(),n,{triggerReason:v._toSignatureHelpTriggerReason(s)});if(!o||e.isDisposed())return;const l={activeSignature:o.selectedItemIndex,activeParameter:o.argumentIndex,signatures:[]};return o.items.forEach((e=>{const t={label:"",parameters:[]};t.documentation={value:b(e.documentation)},t.label+=b(e.prefixDisplayParts),e.parameters.forEach(((i,s,r)=>{const n=b(i.displayParts),a={label:n,documentation:{value:b(i.documentation)}};t.label+=n,t.parameters.push(a),s<r.length-1&&(t.label+=b(e.separatorDisplayParts))})),t.label+=b(e.suffixDisplayParts),l.signatures.push(t)})),{value:l,dispose(){}}}}),x=t("QuickInfoAdapter",class extends f{async provideHover(e,t,i){const s=e.uri,r=e.getOffsetAt(t),n=await this._worker(s);if(e.isDisposed())return;const a=await n.getQuickInfoAtPosition(s.toString(),r);if(!a||e.isDisposed())return;const o=b(a.documentation),l=a.tags?a.tags.map((e=>S(e))).join("  \n\n"):"",c=b(a.displayParts);return{range:this._textSpanToRange(e,a.textSpan),contents:[{value:"```typescript\n"+c+"\n```\n"},{value:o+(l?"\n\n"+l:"")}]}}}),k=t("DocumentHighlightAdapter",class extends f{async provideDocumentHighlights(e,t,i){const s=e.uri,r=e.getOffsetAt(t),n=await this._worker(s);if(e.isDisposed())return;const a=await n.getDocumentHighlights(s.toString(),r,[s.toString()]);return a&&!e.isDisposed()?a.flatMap((t=>t.highlightSpans.map((t=>({range:this._textSpanToRange(e,t.textSpan),kind:"writtenReference"===t.kind?g.languages.DocumentHighlightKind.Write:g.languages.DocumentHighlightKind.Text}))))):void 0}}),C=t("DefinitionAdapter",class extends f{constructor(e,t){super(t),this._libFiles=e}async provideDefinition(e,t,i){const s=e.uri,r=e.getOffsetAt(t),n=await this._worker(s);if(e.isDisposed())return;const a=await n.getDefinitionAtPosition(s.toString(),r);if(!a||e.isDisposed())return;if(await this._libFiles.fetchLibFilesIfNecessary(a.map((e=>g.Uri.parse(e.fileName)))),e.isDisposed())return;const o=[];for(let l of a){const e=this._libFiles.getOrCreateModel(l.fileName);e&&o.push({uri:e.uri,range:this._textSpanToRange(e,l.textSpan)})}return o}}),D=t("ReferenceAdapter",class extends f{constructor(e,t){super(t),this._libFiles=e}async provideReferences(e,t,i,s){const r=e.uri,n=e.getOffsetAt(t),a=await this._worker(r);if(e.isDisposed())return;const o=await a.getReferencesAtPosition(r.toString(),n);if(!o||e.isDisposed())return;if(await this._libFiles.fetchLibFilesIfNecessary(o.map((e=>g.Uri.parse(e.fileName)))),e.isDisposed())return;const l=[];for(let c of o){const e=this._libFiles.getOrCreateModel(c.fileName);e&&l.push({uri:e.uri,range:this._textSpanToRange(e,c.textSpan)})}return l}}),F=t("OutlineAdapter",class extends f{async provideDocumentSymbols(e,t){const i=e.uri,s=await this._worker(i);if(e.isDisposed())return;const r=await s.getNavigationTree(i.toString());if(!r||e.isDisposed())return;const n=(t,i)=>{var s;return{name:t.text,detail:"",kind:I[t.kind]||g.languages.SymbolKind.Variable,range:this._textSpanToRange(e,t.spans[0]),selectionRange:this._textSpanToRange(e,t.spans[0]),tags:[],children:null===(s=t.childItems)||void 0===s?void 0:s.map((e=>n(e,t.text))),containerName:i}};return r.childItems?r.childItems.map((e=>n(e))):[]}}),A=t("Kind",class{});u(A,"unknown",""),u(A,"keyword","keyword"),u(A,"script","script"),u(A,"module","module"),u(A,"class","class"),u(A,"interface","interface"),u(A,"type","type"),u(A,"enum","enum"),u(A,"variable","var"),u(A,"localVariable","local var"),u(A,"function","function"),u(A,"localFunction","local function"),u(A,"memberFunction","method"),u(A,"memberGetAccessor","getter"),u(A,"memberSetAccessor","setter"),u(A,"memberVariable","property"),u(A,"constructorImplementation","constructor"),u(A,"callSignature","call"),u(A,"indexSignature","index"),u(A,"constructSignature","construct"),u(A,"parameter","parameter"),u(A,"typeParameter","type parameter"),u(A,"primitiveType","primitive type"),u(A,"label","label"),u(A,"alias","alias"),u(A,"const","const"),u(A,"let","let"),u(A,"warning","warning");var I=Object.create(null);I[A.module]=g.languages.SymbolKind.Module,I[A.class]=g.languages.SymbolKind.Class,I[A.enum]=g.languages.SymbolKind.Enum,I[A.interface]=g.languages.SymbolKind.Interface,I[A.memberFunction]=g.languages.SymbolKind.Method,I[A.memberVariable]=g.languages.SymbolKind.Property,I[A.memberGetAccessor]=g.languages.SymbolKind.Property,I[A.memberSetAccessor]=g.languages.SymbolKind.Property,I[A.variable]=g.languages.SymbolKind.Variable,I[A.const]=g.languages.SymbolKind.Variable,I[A.localVariable]=g.languages.SymbolKind.Variable,I[A.variable]=g.languages.SymbolKind.Variable,I[A.function]=g.languages.SymbolKind.Function,I[A.localFunction]=g.languages.SymbolKind.Function;var T,L,P=t("FormatHelper",class extends f{static _convertOptions(e){return{ConvertTabsToSpaces:e.insertSpaces,TabSize:e.tabSize,IndentSize:e.tabSize,IndentStyle:2,NewLineCharacter:"\n",InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(e,t){return{text:t.newText,range:this._textSpanToRange(e,t.span)}}}),O=t("FormatAdapter",class extends P{constructor(...t){super(...t),e(this,"canFormatMultipleRanges",!1)}async provideDocumentRangeFormattingEdits(e,t,i,s){const r=e.uri,n=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),a=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=await this._worker(r);if(e.isDisposed())return;const l=await o.getFormattingEditsForRange(r.toString(),n,a,P._convertOptions(i));return l&&!e.isDisposed()?l.map((t=>this._convertTextChanges(e,t))):void 0}}),N=t("FormatOnTypeAdapter",class extends P{get autoFormatTriggerCharacters(){return[";","}","\n"]}async provideOnTypeFormattingEdits(e,t,i,s,r){const n=e.uri,a=e.getOffsetAt(t),o=await this._worker(n);if(e.isDisposed())return;const l=await o.getFormattingEditsAfterKeystroke(n.toString(),a,i,P._convertOptions(s));return l&&!e.isDisposed()?l.map((t=>this._convertTextChanges(e,t))):void 0}}),M=t("CodeActionAdaptor",class extends P{async provideCodeActions(e,t,i,s){const r=e.uri,n=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),a=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=P._convertOptions(e.getOptions()),l=i.markers.filter((e=>e.code)).map((e=>e.code)).map(Number),c=await this._worker(r);if(e.isDisposed())return;const d=await c.getCodeFixesAtPosition(r.toString(),n,a,l,o);if(!d||e.isDisposed())return{actions:[],dispose:()=>{}};return{actions:d.filter((e=>0===e.changes.filter((e=>e.isNewFile)).length)).map((t=>this._tsCodeFixActionToMonacoCodeAction(e,i,t))),dispose:()=>{}}}_tsCodeFixActionToMonacoCodeAction(e,t,i){const s=[];for(const r of i.changes)for(const t of r.textChanges)s.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,t.span),text:t.newText}});return{title:i.description,edit:{edits:s},diagnostics:t.markers,kind:"quickfix"}}}),K=t("RenameAdapter",class extends f{constructor(e,t){super(t),this._libFiles=e}async provideRenameEdits(e,t,i,s){const r=e.uri,n=r.toString(),a=e.getOffsetAt(t),o=await this._worker(r);if(e.isDisposed())return;const l=await o.getRenameInfo(n,a,{allowRenameOfImportPath:!1});if(!1===l.canRename)return{edits:[],rejectReason:l.localizedErrorMessage};if(void 0!==l.fileToRename)throw new Error("Renaming files is not supported.");const c=await o.findRenameLocations(n,a,!1,!1,!1);if(!c||e.isDisposed())return;const d=[];for(const u of c){const e=this._libFiles.getOrCreateModel(u.fileName);if(!e)throw new Error(`Unknown file ${u.fileName}.`);d.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,u.textSpan),text:i}})}return{edits:d}}}),R=t("InlayHintsAdapter",class extends f{async provideInlayHints(e,t,i){const s=e.uri,r=s.toString(),n=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),a=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=await this._worker(s);if(e.isDisposed())return null;return{hints:(await o.provideInlayHints(r,n,a)).map((t=>({...t,label:t.text,position:e.getPositionAt(t.position),kind:this._convertHintKind(t.kind)}))),dispose:()=>{}}}_convertHintKind(e){return"Parameter"===e?g.languages.InlayHintKind.Parameter:g.languages.InlayHintKind.Type}});function E(e,t){const i=[],s=new p(t,e),r=(...e)=>s.getLanguageServiceWorker(...e),n=new y(r);return function(){const{modeConfiguration:s}=e;!function(e){for(;e.length;)e.pop().dispose()}(i),s.completionItems&&i.push(g.languages.registerCompletionItemProvider(t,new w(r))),s.signatureHelp&&i.push(g.languages.registerSignatureHelpProvider(t,new v(r))),s.hovers&&i.push(g.languages.registerHoverProvider(t,new x(r))),s.documentHighlights&&i.push(g.languages.registerDocumentHighlightProvider(t,new k(r))),s.definitions&&i.push(g.languages.registerDefinitionProvider(t,new C(n,r))),s.references&&i.push(g.languages.registerReferenceProvider(t,new D(n,r))),s.documentSymbols&&i.push(g.languages.registerDocumentSymbolProvider(t,new F(r))),s.rename&&i.push(g.languages.registerRenameProvider(t,new K(n,r))),s.documentRangeFormattingEdits&&i.push(g.languages.registerDocumentRangeFormattingEditProvider(t,new O(r))),s.onTypeFormattingEdits&&i.push(g.languages.registerOnTypeFormattingEditProvider(t,new N(r))),s.codeActions&&i.push(g.languages.registerCodeActionProvider(t,new M(r))),s.inlayHints&&i.push(g.languages.registerInlayHintsProvider(t,new R(r))),s.diagnostics&&i.push(new _(n,e,t,r))}(),r}}}}))}();
