package cn.stylefeng.roses.kernel.micro.starter;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.codec.Encoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.text.SimpleDateFormat;

/**
 * Sentinel配置
 *
 * <AUTHOR>
 * @date 2020/2/11 21:20
 */
@Configuration
public class FeignAutoConfiguration {

    /**
     * feign的编码配置
     *
     * <AUTHOR>
     * @date 2021/8/26 17:39
     */
    @Bean
    public Encoder feignEncoder() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        ObjectFactory<HttpMessageConverters> httpMessageConvertersHttpMessageConverter = () -> new HttpMessageConverters(mappingJackson2HttpMessageConverter);
        return new SpringEncoder(httpMessageConvertersHttpMessageConverter);
    }

}
