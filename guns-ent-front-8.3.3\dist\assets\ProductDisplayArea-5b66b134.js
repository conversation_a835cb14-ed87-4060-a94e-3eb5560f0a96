import{_ as U,r as m,L as $,a as n,c as l,b as a,d as o,w as c,F as f,e as C,f as j,at as k,ac as M,g as p,I as J,t as g,h as y,b1 as W,aM as q,m as b,j as G,T as H,l as Q,W as R,J as X,B as Y,a5 as Z,S as ee}from"./index-18a1ea24.js";const te={class:"product-display-area"},se={class:"category-section"},ae={class:"category-tabs"},oe={class:"product-filter-section"},ne={class:"filter-row"},le={class:"search-wrapper"},ce={class:"filter-item"},ie={class:"product-grid-section"},de={class:"grid-container"},re=["onClick"],ue={class:"product-card"},_e={class:"product-image"},pe=["src","alt"],ve={key:1,class:"image-placeholder"},me={class:"product-info"},ge=["title"],ye={class:"price-info"},he={class:"current-price"},fe={key:0,class:"stock-info"},Ce={class:"product-actions"},ke={key:0,class:"empty-state"},be={key:1,class:"loading-state"},we=Object.assign({name:"ProductDisplayArea"},{__name:"ProductDisplayArea",props:{categories:{type:Array,default:()=>[]},products:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["productAdd","productSelect","categoryChange","search","filterChange"],setup(d,{emit:w}){const N=d,r=w,_=m("all"),i=m(""),h=m("");$(()=>{let e=N.products;if(_.value!=="all"&&(e=e.filter(t=>t.categoryId===_.value)),i.value){const t=i.value.toLowerCase();e=e.filter(u=>u.productName.toLowerCase().includes(t)||u.productCode&&u.productCode.toLowerCase().includes(t))}return e});const S=e=>(e||0).toFixed(2),x=e=>{_.value=e,r("categoryChange",e)},A=()=>{r("search",i.value)},I=e=>{i.value=e.target.value,r("search",i.value)},P=e=>{r("filterChange",{type:"price",value:e})},B=e=>{r("productSelect",e)},F=e=>{if(e.stock<=0){b.warning("\u5546\u54C1\u5DF2\u7F3A\u8D27");return}r("productAdd",e),b.success("\u5DF2\u6DFB\u52A0 ".concat(e.productName," \u5230\u8D2D\u7269\u8F66"))},D=e=>{e.target.style.display="none"};return(e,t)=>{const u=G,L=H,T=Q,v=R,E=X,K=Y,O=Z,V=ee;return n(),l("div",te,[a("div",se,[a("div",ae,[o(L,{activeKey:_.value,"onUpdate:activeKey":t[0]||(t[0]=s=>_.value=s),type:"card",size:"small",onTabClick:x,class:"pos-category-tabs"},{default:c(()=>[o(u,{key:"all",tab:"\u5168\u90E8\u5546\u54C1"}),(n(!0),l(f,null,C(d.categories,s=>(n(),j(u,{key:s.categoryId,tab:s.categoryName},null,8,["tab"]))),128))]),_:1},8,["activeKey"])])]),a("div",oe,[a("div",ne,[a("div",le,[o(T,{value:i.value,"onUpdate:value":t[1]||(t[1]=s=>i.value=s),placeholder:"\u641C\u7D22\u5546\u54C1\u540D\u79F0\u3001\u7F16\u7801\u3001\u6761\u5F62\u7801",class:"search-input",onPressEnter:A,onChange:I,allowClear:""},{prefix:c(()=>[o(k(M))]),_:1},8,["value"])]),a("div",ce,[o(E,{value:h.value,"onUpdate:value":t[2]||(t[2]=s=>h.value=s),placeholder:"\u9009\u62E9\u4EF7\u683C\u8303\u56F4",class:"filter-select",onChange:P,allowClear:""},{default:c(()=>[o(v,{value:"0-50"},{default:c(()=>t[3]||(t[3]=[p("0-50\u5143")])),_:1,__:[3]}),o(v,{value:"50-100"},{default:c(()=>t[4]||(t[4]=[p("50-100\u5143")])),_:1,__:[4]}),o(v,{value:"100-200"},{default:c(()=>t[5]||(t[5]=[p("100-200\u5143")])),_:1,__:[5]}),o(v,{value:"200+"},{default:c(()=>t[6]||(t[6]=[p("200\u5143\u4EE5\u4E0A")])),_:1,__:[6]})]),_:1},8,["value"])])])]),a("div",ie,[a("div",de,[(n(!0),l(f,null,C(d.products,s=>(n(),l("div",{key:s.productId,class:"product-item",onClick:z=>B(s)},[a("div",ue,[a("div",_e,[s.image?(n(),l("img",{key:0,src:s.image,alt:s.productName,onError:D},null,40,pe)):(n(),l("div",ve,[o(J,{iconClass:"icon-product"})]))]),a("div",me,[a("div",{class:"product-name",title:s.productName},g(s.productName),9,ge),a("div",ye,[a("div",he," \xA5"+g(S(s.price)),1)]),s.stock!==void 0?(n(),l("div",fe," \u5E93\u5B58: "+g(s.stock),1)):y("",!0)]),a("div",Ce,[o(K,{type:"primary",size:"small",disabled:s.stock<=0,onClick:q(z=>F(s),["stop"]),class:"add-btn"},{icon:c(()=>[o(k(W))]),default:c(()=>[t[7]||(t[7]=p(" \u52A0\u5165\u8D2D\u7269\u8F66 "))]),_:2,__:[7]},1032,["disabled","onClick"])])])],8,re))),128))]),!d.loading&&d.products.length===0?(n(),l("div",ke,[o(O,{description:"\u6682\u65E0\u5546\u54C1"})])):y("",!0),d.loading?(n(),l("div",be,[o(V,{size:"large"}),t[8]||(t[8]=a("div",{class:"loading-text"},"\u6B63\u5728\u52A0\u8F7D\u5546\u54C1...",-1))])):y("",!0)])])}}}),Se=U(we,[["__scopeId","data-v-d6652b4a"]]);export{Se as default};
