import{_ as P,r as h,L as y,X as I,o as N,a as i,f as m,w as v,c as C,F as E,e as B,b as f,t as d,g as V,h as w,m as x,U as A,W as M,J as O}from"./index-18a1ea24.js";/* empty css              */import{P as F}from"./ProductApi-52d42f8e.js";const R={name:"ProductSelector",props:{value:{type:[String,Number],default:void 0},placeholder:{type:String,default:"\u8BF7\u9009\u62E9\u5546\u54C1"},allowClear:{type:<PERSON>olean,default:!0},disabled:{type:Boolean,default:!1},filter:{type:Object,default:()=>({})}},emits:["update:value","change"],setup(o,{emit:r}){const s=h(!1),a=h([]),u=h(""),_=y({get:()=>o.value,set:e=>{r("update:value",e)}}),c=y(()=>{let e=a.value;if(o.filter.businessModeList&&o.filter.businessModeList.length>0&&(e=e.filter(n=>o.filter.businessModeList.includes(n.businessMode))),o.filter.pricingType&&(e=e.filter(n=>n.pricingType===o.filter.pricingType)),u.value){const n=u.value.toLowerCase();e=e.filter(l=>l.productName.toLowerCase().includes(n)||l.productCode.toLowerCase().includes(n)||l.barcode&&l.barcode.toLowerCase().includes(n))}return e}),g=async()=>{s.value=!0;try{const e=await F.findList({status:"Y",pageSize:1e3});e.success&&(a.value=e.data||[])}catch(e){console.error("\u52A0\u8F7D\u5546\u54C1\u5217\u8868\u5931\u8D25:",e),x.error("\u52A0\u8F7D\u5546\u54C1\u5217\u8868\u5931\u8D25")}finally{s.value=!1}},p=e=>{u.value=e},t=(e,n)=>{const l=a.value.find(T=>T.productId===e);r("change",e,l)},S=()=>{r("change",void 0,null)},b=e=>{switch(e){case"NORMAL":return"blue";case"WEIGHT":return"orange";case"PIECE":return"green";case"VARIABLE":return"purple";default:return"default"}},k=(e,n)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return n||"\u4E2A"}},L=(e,n)=>{if(!e)return"0";const l=n==="WEIGHT"?3:0;return parseFloat(e).toFixed(l)};return I(()=>o.filter,()=>{o.value&&c.value.length>0&&(c.value.find(n=>n.productId===o.value)||(_.value=void 0,r("change",void 0,null)))},{deep:!0}),N(()=>{g()}),{loading:s,selectedValue:_,filteredProducts:c,handleSearch:p,handleChange:t,handleClear:S,getPricingTypeColor:b,getStockUnit:k,formatStock:L}}},U={class:"product-option"},W={class:"product-name"},G={class:"product-info"},H={class:"product-code"},z={key:1,class:"product-stock"};function j(o,r,s,a,u,_){const c=A,g=M,p=O;return i(),m(p,{value:a.selectedValue,"onUpdate:value":r[0]||(r[0]=t=>a.selectedValue=t),placeholder:s.placeholder,loading:a.loading,allowClear:s.allowClear,disabled:s.disabled,showSearch:"",filterOption:!1,onSearch:a.handleSearch,onChange:a.handleChange,onClear:a.handleClear,style:{width:"100%"}},{default:v(()=>[(i(!0),C(E,null,B(a.filteredProducts,t=>(i(),m(g,{key:t.productId,value:t.productId,title:"".concat(t.productName," (").concat(t.productCode,")")},{default:v(()=>[f("div",U,[f("div",W,d(t.productName),1),f("div",G,[f("span",H,d(t.productCode),1),t.pricingTypeName?(i(),m(c,{key:0,size:"small",color:a.getPricingTypeColor(t.pricingType)},{default:v(()=>[V(d(t.pricingTypeName),1)]),_:2},1032,["color"])):w("",!0),t.currentStock!==void 0?(i(),C("span",z," \u5E93\u5B58: "+d(a.formatStock(t.currentStock,t.pricingType))+" "+d(a.getStockUnit(t.pricingType,t.unit)),1)):w("",!0)])])]),_:2},1032,["value","title"]))),128))]),_:1},8,["value","placeholder","loading","allowClear","disabled","onSearch","onChange","onClear"])}const X=P(R,[["render",j],["__scopeId","data-v-daa82fff"]]);export{X as _};
