import{r as o,o as x,X as V,a as h,c as y,d as m,ah as k,w as g,f as N,h as O,l as R,bf as F,a0 as J}from"./index-18a1ea24.js";/* empty css              */import{_ as L}from"./index-3a0e5c06.js";import{C as j}from"./CommonApi-27ae49e3.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const D={class:"wh100"},I={__name:"index",props:{value:{type:[String,Array],default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},isJson:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},width:{type:String,default:"100%"},readonly:{type:Boolean,default:!1},formRef:{type:Object,default:null}},emits:["update:value","onChange"],setup(f,{emit:r}){const e=f,d=r,n=o(!0),c=o(!1),l=o([]),u=o(""),i=o(!1),p=o({selectUserList:[]});x(()=>{var a;(a=e.record)!=null&&a.itemMultipleChoiceFlag||e.multiple?n.value=!1:n.value=!0,v()});const v=async()=>{if(e.isJson)if(e.value)if(n.value){if(c.value)return;const a=await j.getUserName({userId:e.value});a.data&&(l.value=[{id:e.value,name:a.data}])}else l.value=e.normal?l.value:JSON.parse(e.value);else l.value=[];else l.value=e.value?e.value:[];c.value=!1,u.value=l.value.map(a=>a.name).join("\uFF1B")},b=()=>{var a,t;(a=l.value)!=null&&a.length?p.value.selectUserList=(t=l.value)==null?void 0:t.map(s=>({bizId:s.id,name:s.name})):p.value.selectUserList=[],i.value=!0},C=a=>{var t;l.value=(t=a.selectUserList)==null?void 0:t.map(s=>({id:s.bizId,name:s.name})),u.value=a.selectUserList.map(s=>s.name).join("\uFF1B"),c.value=!0,w()},w=()=>{if(e.isJson){let a=l.value.length>0?n.value?l.value[0].id:e.normal?l.value:JSON.stringify(l.value):"";d("update:value",a),d("onChange",e.record),S()}else d("update:value",l.value)},S=async()=>{var a;!e.normal&&((a=e.formRef)!=null&&a.validateFields)&&await e.formRef.validateFields([e.record.fieldCode])};return V(()=>e.value,a=>{v()},{deep:!0}),(a,t)=>{const s=R,U=L,B=F;return h(),y("div",D,[m(s,{value:u.value,"onUpdate:value":t[0]||(t[0]=_=>u.value=_),disabled:e.readonly||e.disabled,class:"w-full",style:k({width:e.width}),placeholder:f.placeholder,onFocus:b},null,8,["value","disabled","style","placeholder"]),m(B,null,{default:g(()=>[i.value?(h(),N(U,{key:0,visible:i.value,"onUpdate:visible":t[1]||(t[1]=_=>i.value=_),data:p.value,showTab:["user"],changeHeight:!0,title:"\u4EBA\u5458\u9009\u62E9",isRadio:n.value,onDone:C},null,8,["visible","data","isRadio"])):O("",!0)]),_:1})])}}},z={class:"guns-body guns-body-card"},Q={__name:"index",setup(f){const r=o(""),e=o(!1),d=o(!1),n=o("\u8BF7\u9009\u62E9"),c=()=>{console.log(r.value)};return(l,u)=>{const i=I,p=J;return h(),y("div",z,[m(p,{title:"\u4EBA\u5458\u9009\u62E9",bordered:!1},{default:g(()=>[m(i,{value:r.value,"onUpdate:value":u[0]||(u[0]=v=>r.value=v),disabled:e.value,readonly:d.value,onOnChange:c,placeholder:n.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{Q as default};
