package cn.stylefeng.roses.ent.mobile.manage.mapper;

import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.OrgUserStat;
import cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.OrgUserStatTotal;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用在组织机构统计的mapper
 *
 * <AUTHOR>
 * @since 2024-03-29 12:56
 */
public interface OrgStatMapper {

    /**
     * 获取机构下的人员统计信息
     * <p>
     * 只统计指定机构的子一级机构的人员，不含子集的子集这种
     *
     * <AUTHOR>
     * @since 2024-03-29 12:58
     */
    List<OrgUserStat> getOrgStatList(@Param("orgList") List<Long> orgList);

    /**
     * 用来统计机构下的人员信息，包括机构下的机构的人员
     *
     * <AUTHOR>
     * @since 2024-03-29 14:57
     */
    List<OrgUserStatTotal> getOrgStatTotalList(@Param("orgList") List<Long> orgList);

}
