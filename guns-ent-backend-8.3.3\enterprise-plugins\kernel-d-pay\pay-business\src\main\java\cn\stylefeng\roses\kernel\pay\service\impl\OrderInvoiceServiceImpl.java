package cn.stylefeng.roses.kernel.pay.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import cn.stylefeng.roses.kernel.auth.api.pojo.login.LoginUser;
import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.pay.api.entity.Order;
import cn.stylefeng.roses.kernel.pay.entity.OrderInvoice;
import cn.stylefeng.roses.kernel.pay.enums.OrderInvoiceExceptionEnum;
import cn.stylefeng.roses.kernel.pay.factory.OrderInvoiceFactory;
import cn.stylefeng.roses.kernel.pay.mapper.OrderInvoiceMapper;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderInvoiceRequest;
import cn.stylefeng.roses.kernel.pay.service.OrderInvoiceService;
import cn.stylefeng.roses.kernel.pay.service.OrderService;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单开票记录业务实现层
 *
 * <AUTHOR>
 * @since 2024/06/21 16:49
 */
@Service
public class OrderInvoiceServiceImpl extends ServiceImpl<OrderInvoiceMapper, OrderInvoice> implements OrderInvoiceService {

    @Resource
    private OrderService orderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(OrderInvoiceRequest orderInvoiceRequest) {

        // 1. 获取当前登录用户id
        LoginUser loginUser = LoginContext.me().getLoginUser();
        Long userId = loginUser.getUserId();

        // 2. 获取已经申请过的开票记录中，是否包含了这些订单
        this.validateHaveAlreadyApply(orderInvoiceRequest, userId);

        // 3. 从新计算一下这些请求开票金额的总金额数量，以订单为准
        BigDecimal totalAmount = this.calcOrderTotalAmount(orderInvoiceRequest, userId);

        // 4. 保存开票记录
        OrderInvoice orderInvoice = OrderInvoiceFactory.createOrderInvoice(orderInvoiceRequest, totalAmount);
        this.save(orderInvoice);

        // 5. 将对应的订单状态改为已申请开票
        List<Long> orderIdList = orderInvoiceRequest.getOrderIdList();
        this.orderService.batchUpdateOrderInvoiceStatus(orderIdList, YesOrNotEnum.Y);
    }

    @Override
    public OrderInvoice detail(OrderInvoiceRequest orderInvoiceRequest) {
        return this.queryOrderInvoice(orderInvoiceRequest);
    }

    @Override
    public List<OrderInvoice> findList(OrderInvoiceRequest orderInvoiceRequest) {
        LambdaQueryWrapper<OrderInvoice> wrapper = this.createWrapper(orderInvoiceRequest);

        // 只查询关键字段
        wrapper.select(
                OrderInvoice::getInvoiceTitle,
                OrderInvoice::getTaxpayerNo,
                OrderInvoice::getTotalAmount,
                OrderInvoice::getOrderIdList,
                OrderInvoice::getSendEmail,
                OrderInvoice::getOrderInvoiceStatus,
                BaseEntity::getCreateTime
        );

        return this.list(wrapper);
    }

    /**
     * 获取信息
     *
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    private OrderInvoice queryOrderInvoice(OrderInvoiceRequest orderInvoiceRequest) {
        OrderInvoice orderInvoice = this.getById(orderInvoiceRequest.getOrderInvoiceId());
        if (ObjectUtil.isEmpty(orderInvoice)) {
            throw new ServiceException(OrderInvoiceExceptionEnum.ORDER_INVOICE_NOT_EXISTED);
        }
        return orderInvoice;
    }

    /**
     * 创建查询wrapper
     *
     * <AUTHOR>
     * @since 2024/06/21 16:49
     */
    private LambdaQueryWrapper<OrderInvoice> createWrapper(OrderInvoiceRequest orderInvoiceRequest) {
        LambdaQueryWrapper<OrderInvoice> queryWrapper = new LambdaQueryWrapper<>();

        // 获取当前人的开票记录
        queryWrapper.eq(OrderInvoice::getUserId, LoginContext.me().getLoginUser().getUserId());

        // 倒序排列开票记录
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);

        return queryWrapper;
    }

    /**
     * 校验这些订单是否已经申请过开票
     *
     * <AUTHOR>
     * @since 2024/6/24 16:03
     */
    private void validateHaveAlreadyApply(OrderInvoiceRequest orderInvoiceRequest, Long userId) {
        LambdaQueryWrapper<OrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInvoice::getUserId, userId);
        wrapper.select(OrderInvoice::getOrderIdList);
        List<OrderInvoice> haveInvoiceRecord = this.list(wrapper);
        if (ObjectUtil.isNotEmpty(haveInvoiceRecord)) {
            List<Long> alreadyOrder = haveInvoiceRecord.stream()
                    .flatMap(orderInvoice -> orderInvoice.getOrderIdList().stream())
                    .collect(Collectors.toList());
            if (orderInvoiceRequest.getOrderIdList().stream().anyMatch(alreadyOrder::contains)) {
                throw new ServiceException(OrderInvoiceExceptionEnum.ALREADY_APPLY);
            }
        }
    }

    /**
     * 计算订单的总金额，以真实数据库的订单为准
     *
     * <AUTHOR>
     * @since 2024/6/24 16:04
     */
    private BigDecimal calcOrderTotalAmount(OrderInvoiceRequest orderInvoiceRequest, Long userId) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<Long> orderIdList = orderInvoiceRequest.getOrderIdList();
        for (Long orderId : orderIdList) {
            Order order = orderService.findUserOrder(userId, orderId);
            if (order == null) {
                throw new ServiceException(OrderInvoiceExceptionEnum.CANT_FIND_ORDER);
            }
            BigDecimal payPrice = order.getPayPrice();
            totalAmount = totalAmount.add(payPrice);
        }
        return totalAmount;
    }

}
