System.register(["./index-legacy-ee1db0c7.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./ProductApi-legacy-33feae42.js"],(function(e,a){"use strict";var t,l,r,i,d,u,o,c,f,n,s,p,g,m,b,P;return{setters:[e=>{t=e._,l=e.a,r=e.f,i=e.w,d=e.d,u=e.g,o=e.t,c=e.c,f=e.b,n=e.h,s=e.Y,p=e.U,g=e.Z,m=e.V,b=e.M},null,null,e=>{P=e.P}],execute:function(){const a={name:"ProductDetail",components:{},props:{visible:Boolean,data:{type:Object,default:()=>({})}},emits:["update:visible"],setup:(e,{emit:a})=>({updateVisible:e=>{a("update:visible",e)},getProductStatusName:e=>P.getProductStatusName(e),getStatusTagColor:e=>P.getStatusTagColor(e),getPricingTypeName:e=>P.getPricingTypeName(e),getPricingTypeTagColor:e=>P.getPricingTypeTagColor(e),formatWeight:e=>P.formatWeight(e),formatVolume:e=>P.formatVolume(e),formatShelfLife:e=>P.formatShelfLife(e),formatPrice:e=>{if(!e.pricingType)return"-";switch(e.pricingType){case"NORMAL":return e.retailPrice?P.formatPrice(e.retailPrice,"NORMAL"):"-";case"WEIGHT":return e.unitPrice?P.formatPrice(e.unitPrice,"WEIGHT"):"-";case"PIECE":return e.piecePrice?P.formatPrice(e.piecePrice,"PIECE"):"-";case"VARIABLE":return e.referencePrice?P.formatPrice(e.referencePrice,"VARIABLE"):"-";default:return"-"}}})},_={key:1},y={key:0,style:{padding:"16px",background:"#f5f5f5","border-radius":"6px"}};e("default",t(a,[["render",function(e,a,t,P,T,h){const N=s,v=p,S=g,C=m,L=b;return l(),r(L,{title:"商品详情",width:800,visible:t.visible,footer:null,"onUpdate:visible":P.updateVisible},{default:i((()=>[d(S,{column:2,bordered:""},{default:i((()=>[d(N,{label:"商品编码"},{default:i((()=>[u(o(t.data.productCode),1)])),_:1}),d(N,{label:"商品名称"},{default:i((()=>[u(o(t.data.productName),1)])),_:1}),d(N,{label:"商品简称"},{default:i((()=>[u(o(t.data.productShortName||"-"),1)])),_:1}),d(N,{label:"条形码"},{default:i((()=>[u(o(t.data.barcode||"-"),1)])),_:1}),d(N,{label:"品牌"},{default:i((()=>[u(o(t.data.brand||"-"),1)])),_:1}),d(N,{label:"规格"},{default:i((()=>[u(o(t.data.specification||"-"),1)])),_:1}),d(N,{label:"基本单位"},{default:i((()=>[u(o(t.data.unit),1)])),_:1}),d(N,{label:"状态"},{default:i((()=>[d(v,{color:P.getStatusTagColor(t.data.status)},{default:i((()=>[u(o(P.getProductStatusName(t.data.status)),1)])),_:1},8,["color"])])),_:1}),d(N,{label:"重量"},{default:i((()=>[u(o(P.formatWeight(t.data.weight)),1)])),_:1}),d(N,{label:"体积"},{default:i((()=>[u(o(P.formatVolume(t.data.volume)),1)])),_:1}),d(N,{label:"保质期"},{default:i((()=>[u(o(P.formatShelfLife(t.data.shelfLife)),1)])),_:1}),d(N,{label:"商品分类"},{default:i((()=>[u(o(t.data.categoryName||"-"),1)])),_:1}),d(N,{label:"供应商"},{default:i((()=>[u(o(t.data.supplierName||"-"),1)])),_:1}),d(N,{label:"计价类型"},{default:i((()=>[t.data.pricingType?(l(),r(v,{key:0,color:P.getPricingTypeTagColor(t.data.pricingType)},{default:i((()=>[u(o(P.getPricingTypeName(t.data.pricingType)),1)])),_:1},8,["color"])):(l(),c("span",_,"-"))])),_:1}),d(N,{label:"价格信息"},{default:i((()=>[u(o(P.formatPrice(t.data)),1)])),_:1}),d(N,{label:"存储条件",span:2},{default:i((()=>[u(o(t.data.storageCondition||"-"),1)])),_:1}),d(N,{label:"备注",span:2},{default:i((()=>[u(o(t.data.remark||"-"),1)])),_:1}),d(N,{label:"创建时间"},{default:i((()=>[u(o(t.data.createTime),1)])),_:1}),d(N,{label:"更新时间"},{default:i((()=>[u(o(t.data.updateTime),1)])),_:1})])),_:1}),d(C,{orientation:"left"},{default:i((()=>a[0]||(a[0]=[u("库存信息")]))),_:1,__:[0]}),t.data.productId?(l(),c("div",y,a[1]||(a[1]=[f("p",null,"库存信息功能开发中...",-1)]))):n("",!0)])),_:1},8,["visible","onUpdate:visible"])}]]))}}}));
