package cn.stylefeng.roses.kernel.secret.modular.service;

import cn.stylefeng.roses.kernel.auth.api.TempSecretApi;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.secret.modular.entity.SysUserSecretKey;
import cn.stylefeng.roses.kernel.secret.modular.pojo.SysUserSecretKeyRequest;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户临时秘钥 服务类
 *
 * <AUTHOR>
 * @date 2022/03/22 11:33
 */
public interface SysUserSecretKeyService extends IService<SysUserSecretKey>, TempSecretApi {

    /**
     * 新增
     *
     * @param sysUserSecretKeyRequest 请求参数
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    void add(SysUserSecretKeyRequest sysUserSecretKeyRequest);

    /**
     * 删除
     *
     * @param sysUserSecretKeyRequest 请求参数
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    void del(SysUserSecretKeyRequest sysUserSecretKeyRequest);

    /**
     * 获取列表（带分页）
     *
     * @param sysUserSecretKeyRequest 请求参数
     * @return PageResult<SysUserSecretKey>   返回结果
     * <AUTHOR>
     * @date 2022/03/22 11:33
     */
    PageResult<SysUserSecretKey> findPage(SysUserSecretKeyRequest sysUserSecretKeyRequest);

    /**
     * 根据用户姓名或者账号查询用户列表
     * <p>
     * 用在新增秘钥时候绑定用户
     *
     * <AUTHOR>
     * @date 2022/3/22 15:57
     */
    List<SysUser> getUserList(String condition);

}
