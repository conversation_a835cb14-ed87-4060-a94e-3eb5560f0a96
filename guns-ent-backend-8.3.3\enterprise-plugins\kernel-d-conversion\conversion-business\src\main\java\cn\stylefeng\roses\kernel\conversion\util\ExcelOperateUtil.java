package cn.stylefeng.roses.kernel.conversion.util;

import com.aspose.cells.*;

/**
 * 一些通用Excel操作的工具类
 *
 * <AUTHOR>
 * @since 2025/2/11 11:36
 */
public class ExcelOperateUtil {

    /**
     * 给sheet插入数据
     *
     * @param sheet    表格
     * @param beginRow 从第几行开始，从1开始
     * @param beginCol 从第几列开始，从1开始
     * @param dataList 数据
     * <AUTHOR>
     * @since 2025/2/11 10:55
     */
    public static void setSheetData(Worksheet sheet, int beginRow, int beginCol, String[] dataList) {
        // 设置标题行的起始单元格位置（第7行，第1列）
        // 注意：Aspose.Cells 的行和列是从 0 开始的
        int startRow = beginRow - 1;
        int startColumn = beginCol - 1;

        // 循环插入标题
        for (int i = 0; i < dataList.length; i++) {
            sheet.getCells().get(startRow, startColumn + i).setValue(dataList[i]);
        }
    }

    /**
     * 设置指定单元格格式居中
     *
     * @param cells    单元格集合
     * @param cellName 单元格名称
     * <AUTHOR>
     * @since 2025/2/11 11:31
     */
    public static void setCellTextCenter(Cells cells, String cellName) {
        Style a1Style = cells.get(cellName).getStyle();
        a1Style.setVerticalAlignment(TextAlignmentType.CENTER);
        a1Style.setHorizontalAlignment(TextAlignmentType.CENTER);
        cells.get(cellName).setStyle(a1Style);
    }

    /**
     * 设置指定sheet的范围，设置border线
     *
     * <AUTHOR>
     * @since 2025/2/11 11:10
     */
    public static void setRangeBorder(Worksheet sheet, String rangeScope) {
        // 设置表单数据区域，加上边框线
        Range range = sheet.getCells().createRange(rangeScope);
        Style style = sheet.getWorkbook().createStyle();
        style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());

        // 应用样式到整个范围
        StyleFlag styleFlag = new StyleFlag();
        styleFlag.setBorders(true);
        range.applyStyle(style, styleFlag);
    }

}
