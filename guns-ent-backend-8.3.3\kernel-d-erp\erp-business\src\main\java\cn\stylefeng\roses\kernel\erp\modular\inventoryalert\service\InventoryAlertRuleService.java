package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRuleRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryAlertRuleResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 库存预警规则服务接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
public interface InventoryAlertRuleService extends IService<InventoryAlertRule> {

    /**
     * 新增预警规则
     *
     * @param request 请求参数
     */
    void add(InventoryAlertRuleRequest request);

    /**
     * 编辑预警规则
     *
     * @param request 请求参数
     */
    void edit(InventoryAlertRuleRequest request);

    /**
     * 删除预警规则
     *
     * @param request 请求参数
     */
    void del(InventoryAlertRuleRequest request);

    /**
     * 批量删除预警规则
     *
     * @param request 请求参数
     */
    void batchDelete(InventoryAlertRuleRequest request);

    /**
     * 查询预警规则详情
     *
     * @param request 请求参数
     * @return 预警规则详情
     */
    InventoryAlertRuleResponse detail(InventoryAlertRuleRequest request);

    /**
     * 分页查询预警规则
     *
     * @param request 请求参数
     * @return 分页结果
     */
    PageResult<InventoryAlertRuleResponse> findPage(InventoryAlertRuleRequest request);

    /**
     * 更新规则状态
     *
     * @param request 请求参数
     */
    void updateStatus(InventoryAlertRuleRequest request);

    /**
     * 测试预警规则
     *
     * @param request 请求参数
     * @return 测试结果
     */
    List<Object> testRule(InventoryAlertRuleRequest request);

    /**
     * 查询启用的预警规则
     *
     * @return 启用的预警规则列表
     */
    List<InventoryAlertRule> findEnabledRules();

    /**
     * 根据商品ID查询预警规则
     *
     * @param productId 商品ID
     * @return 预警规则列表
     */
    List<InventoryAlertRule> findByProductId(Long productId);

    /**
     * 根据分类ID查询预警规则
     *
     * @param categoryId 分类ID
     * @return 预警规则列表
     */
    List<InventoryAlertRule> findByCategoryId(Long categoryId);

    /**
     * 验证规则参数
     *
     * @param request 请求参数
     */
    void validateRuleParams(InventoryAlertRuleRequest request);

    /**
     * 检查规则名称是否重复
     *
     * @param ruleName  规则名称
     * @param excludeId 排除的ID
     * @return 是否重复
     */
    boolean isRuleNameDuplicate(String ruleName, Long excludeId);
}
