<template>
  <div class="guns-body guns-body-card">
    <a-card title="机构人员选择" :bordered="false">
      <a-button class="ele-btn-icon" @click="openSelect"> 选择机构人员 </a-button>
      <div style="margin-top: 12px">选择数据: {{ dataValue.map(item => item.name).join(',') }}</div>
      <OrgUserSelect
        v-model:visible="visible"
        v-if="visible"
        :list="dataValue"
        title="机构人员选择"
        @done="closeSelection"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const visible = ref(false);

// 选中的数据
const dataValue = ref([]);

/* 打开机构人员选择 */
const openSelect = () => {
  visible.value = true;
};

/* 机构人员选择的回调 */
const closeSelection = data => {
  dataValue.value = data;
  visible.value = false;
};
</script>

<style></style>
