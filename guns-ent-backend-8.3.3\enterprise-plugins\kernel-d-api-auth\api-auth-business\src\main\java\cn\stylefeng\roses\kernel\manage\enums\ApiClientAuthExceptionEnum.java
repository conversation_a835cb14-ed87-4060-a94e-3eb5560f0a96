package cn.stylefeng.roses.kernel.manage.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * API客户端和资源绑定关系异常相关枚举
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@Getter
public enum ApiClientAuthExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    API_CLIENT_AUTH_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "查询结果不存在"),

    /**
     * 资源编码不能为空
     */
    RES_CODE_NOT_NULL(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10002", "资源编码不能为空");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ApiClientAuthExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
