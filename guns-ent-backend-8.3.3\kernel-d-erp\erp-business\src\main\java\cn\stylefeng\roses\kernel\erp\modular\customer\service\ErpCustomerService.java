package cn.stylefeng.roses.kernel.erp.modular.customer.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpCustomerRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;

import java.util.List;

/**
 * 客户主档案Service接口
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
public interface ErpCustomerService {

    /**
     * 新增客户
     */
    void add(ErpCustomerRequest erpCustomerRequest);

    /**
     * 删除客户
     */
    void del(ErpCustomerRequest erpCustomerRequest);

    /**
     * 批量删除客户
     */
    void batchDelete(ErpCustomerRequest erpCustomerRequest);

    /**
     * 编辑客户
     */
    void edit(ErpCustomerRequest erpCustomerRequest);

    /**
     * 查询客户详情
     */
    ErpCustomerResponse detail(ErpCustomerRequest erpCustomerRequest);

    /**
     * 分页查询客户列表
     */
    PageResult<ErpCustomerResponse> findPage(ErpCustomerRequest erpCustomerRequest);

    /**
     * 查询客户列表
     */
    List<ErpCustomerResponse> findList(ErpCustomerRequest erpCustomerRequest);

    /**
     * 更新客户状态
     */
    void updateStatus(ErpCustomerRequest erpCustomerRequest);

    /**
     * 校验客户编码是否重复
     */
    boolean validateCustomerCodeRepeat(String customerCode, Long customerId);

    /**
     * 校验客户是否可以删除
     */
    boolean validateCanDelete(Long customerId);

    /**
     * 校验客户是否可以停用
     */
    boolean validateCanInactive(Long customerId);

}
