<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.sys.modular.role.mapper.RoleCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.sys.modular.role.entity.RoleCategory">
		<id column="id" property="id" />
		<result column="category_parent_id" property="categoryParentId" />
		<result column="category_pids" property="categoryPids" />
		<result column="role_category_name" property="roleCategoryName" />
		<result column="category_type" property="categoryType" />
		<result column="fld_sort" property="fldSort" />
		<result column="del_flag" property="delFlag" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="tenant_id" property="tenantId" />
	</resultMap>

	<sql id="Base_Column_List">
		id,category_parent_id,category_pids,role_category_name,category_type,fld_sort,del_flag,create_time,create_user,update_time,update_user,tenant_id
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.sys.modular.role.pojo.response.RoleCategoryVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		sys_role_category tbl
		WHERE
		<where>
        <if test="param.id != null and param.id != ''">
            and tbl.id like concat('%',#{param.id},'%')
        </if>
        <if test="param.categoryParentId != null and param.categoryParentId != ''">
            and tbl.category_parent_id like concat('%',#{param.categoryParentId},'%')
        </if>
        <if test="param.categoryPids != null and param.categoryPids != ''">
            and tbl.category_pids like concat('%',#{param.categoryPids},'%')
        </if>
        <if test="param.roleCategoryName != null and param.roleCategoryName != ''">
            and tbl.role_category_name like concat('%',#{param.roleCategoryName},'%')
        </if>
        <if test="param.categoryType != null and param.categoryType != ''">
            and tbl.category_type like concat('%',#{param.categoryType},'%')
        </if>
        <if test="param.fldSort != null and param.fldSort != ''">
            and tbl.fld_sort like concat('%',#{param.fldSort},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.tenantId != null and param.tenantId != ''">
            and tbl.tenant_id like concat('%',#{param.tenantId},'%')
        </if>
		</where>
	</select>

</mapper>
