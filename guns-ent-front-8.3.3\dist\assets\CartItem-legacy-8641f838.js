System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./formatter-legacy-97503ee9.js","./constants-legacy-2a31d63c.js"],(function(t,e){"use strict";var i,a,n,o,d,s,r,m,l,c,f,p,u,b,g,y,v,x;return{setters:[t=>{i=t._,a=t.a,n=t.c,o=t.b,d=t.g,s=t.t,r=t.h,m=t.d,l=t.w,c=t.at,f=t.aX,p=t.a$,u=t.P,b=t.a2,g=t.B,y=t.y},null,t=>{v=t.A},t=>{x=t.P}],execute:function(){var e=document.createElement("style");e.textContent=".cart-item[data-v-03fa96fb]{padding:12px;border:1px solid #f0f0f0;border-radius:6px;margin-bottom:8px;background:#fff;transition:all .2s ease;position:relative}.cart-item[data-v-03fa96fb]:hover{border-color:#d9d9d9;box-shadow:0 2px 4px rgba(0,0,0,.1)}.cart-item.loading[data-v-03fa96fb]{opacity:.7;pointer-events:none}.item-row[data-v-03fa96fb]{display:flex;align-items:center;justify-content:space-between;margin-bottom:8px}.item-row[data-v-03fa96fb]:last-child{margin-bottom:0}.item-row-1[data-v-03fa96fb]{align-items:flex-start}.item-name[data-v-03fa96fb]{flex:1;font-size:14px;font-weight:500;color:#262626;line-height:1.4;margin-right:8px;word-break:break-word}.item-specs[data-v-03fa96fb]{font-size:12px;color:#8c8c8c;font-weight:400}.remove-btn[data-v-03fa96fb]{flex-shrink:0;width:28px;height:28px;padding:0;display:flex;align-items:center;justify-content:center}.item-row-2[data-v-03fa96fb]{align-items:center}.item-unit-price[data-v-03fa96fb]{font-size:13px;color:#595959}.quantity-control[data-v-03fa96fb]{display:flex;align-items:center;gap:4px}.quantity-btn[data-v-03fa96fb]{width:24px;height:24px;padding:0;display:flex;align-items:center;justify-content:center;border-radius:4px}.quantity-input[data-v-03fa96fb]{width:60px;text-align:center}.quantity-input[data-v-03fa96fb] .ant-input-number-input{text-align:center;padding:0 4px}.item-row-3[data-v-03fa96fb]{align-items:center}.item-code[data-v-03fa96fb]{font-size:12px;color:#8c8c8c;font-family:Courier New,monospace}.item-total[data-v-03fa96fb]{font-size:15px;font-weight:600;color:#f5222d}.item-image[data-v-03fa96fb]{position:absolute;top:8px;right:8px;width:40px;height:40px;border-radius:4px;overflow:hidden;background:#f5f5f5}.item-image img[data-v-03fa96fb]{width:100%;height:100%;object-fit:cover}@media (max-width: 480px){.cart-item[data-v-03fa96fb]{padding:8px}.item-name[data-v-03fa96fb]{font-size:13px}.quantity-input[data-v-03fa96fb]{width:50px}.quantity-btn[data-v-03fa96fb]{width:20px;height:20px}}\n",document.head.appendChild(e);const h={class:"item-row item-row-1"},w=["title"],q={key:0,class:"item-specs"},k={class:"item-row item-row-2"},C={class:"item-unit-price"},j={class:"quantity-control"},z={class:"item-row item-row-3"},_={key:0,class:"item-code"},I={class:"item-total"},E={key:0,class:"item-image"},T=["src","alt"],P=Object.assign({name:"CartItem"},{__name:"CartItem",props:{item:{type:Object,required:!0,validator:t=>t&&"string"==typeof t.id&&"string"==typeof t.name&&"number"==typeof t.price&&"number"==typeof t.quantity&&"number"==typeof t.subtotal},loading:{type:Boolean,default:!1}},emits:["update-quantity","remove-item"],setup(t,{emit:e}){const i=t,P=e,B=t=>v.formatCurrency(t,{showSymbol:!1}),G=()=>i.item.pricingType===x.WEIGHT?.001:1,H=()=>{if(i.item.quantity<=1)return;const t=G(),e=Math.max(t,i.item.quantity-t);P("update-quantity",i.item.id,e)},O=()=>{const t=G(),e=i.item.quantity+t;P("update-quantity",i.item.id,e)},S=t=>{t&&t>0&&P("update-quantity",i.item.id,t)},W=t=>{const e=parseFloat(t.target.value);e&&e>0&&P("update-quantity",i.item.id,e)},A=()=>{P("remove-item",i.item.id)};return(e,v)=>{const P=g,F=y;return a(),n("div",{class:b(["cart-item",{loading:t.loading}])},[o("div",h,[o("div",{class:"item-name",title:t.item.name},[d(s(t.item.name)+" ",1),t.item.specifications?(a(),n("span",q," ("+s(t.item.specifications)+") ",1)):r("",!0)],8,w),m(P,{type:"primary",size:"small",danger:"",onClick:A,class:"remove-btn",title:"删除商品",loading:t.loading},{icon:l((()=>[m(c(f))])),_:1},8,["loading"])]),o("div",k,[o("div",C," ¥"+s(B(t.item.price))+"/"+s(t.item.unit||"件"),1),o("div",j,[m(P,{size:"small",onClick:H,disabled:t.item.quantity<=1||t.loading,class:"quantity-btn"},{icon:l((()=>[m(c(p))])),_:1},8,["disabled"]),m(F,{value:t.item.quantity,min:1,max:9999,precision:i.item.pricingType===x.WEIGHT?3:0,step:G(),size:"small",class:"quantity-input",disabled:t.loading,onChange:S,onPressEnter:W},null,8,["value","precision","step","disabled"]),m(P,{size:"small",onClick:O,disabled:t.loading,class:"quantity-btn"},{icon:l((()=>[m(c(u))])),_:1},8,["disabled"])])]),o("div",z,[t.item.barcode?(a(),n("div",_," 条码: "+s(t.item.barcode),1)):r("",!0),o("div",I," ¥"+s(B(t.item.subtotal)),1)]),t.item.image?(a(),n("div",E,[o("img",{src:t.item.image,alt:t.item.name},null,8,T)])):r("",!0)],2)}}});t("default",i(P,[["__scopeId","data-v-03fa96fb"]]))}}}));
