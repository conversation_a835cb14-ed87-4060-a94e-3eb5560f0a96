package cn.stylefeng.roses.kernel.erp.modular.inventory.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryHistoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse;

import java.util.List;

/**
 * 库存历史Service接口
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
public interface InventoryHistoryService {

    /**
     * 分页查询库存历史列表
     */
    PageResult<InventoryHistoryResponse> findPage(InventoryHistoryQueryRequest inventoryHistoryQueryRequest);

    /**
     * 查询库存历史列表
     */
    List<InventoryHistoryResponse> findList(InventoryHistoryQueryRequest inventoryHistoryQueryRequest);

    /**
     * 查询商品库存历史
     */
    List<InventoryHistoryResponse> findProductHistory(Long productId);

    /**
     * 查询库存历史详情
     */
    InventoryHistoryResponse detail(Long historyId);

    /**
     * 按操作类型统计库存变动
     */
    List<InventoryHistoryResponse> findStatistics(InventoryHistoryQueryRequest inventoryHistoryQueryRequest);

    /**
     * 记录库存历史（内部方法，供其他模块调用）
     */
    void recordInventoryHistory(Long productId, String operationType, java.math.BigDecimal quantityChange, 
                               java.math.BigDecimal stockBefore, java.math.BigDecimal stockAfter, 
                               Long referenceId, String referenceType, String remark);

}