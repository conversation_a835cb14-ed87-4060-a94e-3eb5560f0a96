package cn.stylefeng.roses.kernel.erp.modular.region.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;

import java.util.List;

/**
 * 区域管理Service接口
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
public interface ErpRegionService {

    /**
     * 新增区域
     */
    void add(ErpRegionRequest erpRegionRequest);

    /**
     * 删除区域
     */
    void del(ErpRegionRequest erpRegionRequest);

    /**
     * 编辑区域
     */
    void edit(ErpRegionRequest erpRegionRequest);

    /**
     * 查询区域详情
     */
    ErpRegionResponse detail(ErpRegionRequest erpRegionRequest);

    /**
     * 分页查询区域列表
     */
    PageResult<ErpRegionResponse> findPage(ErpRegionRequest erpRegionRequest);

    /**
     * 查询区域列表
     */
    List<ErpRegionResponse> findList(ErpRegionRequest erpRegionRequest);

    /**
     * 查询区域树形结构
     */
    List<ErpRegionResponse> findTree(ErpRegionRequest erpRegionRequest);

    /**
     * 更新区域状态
     */
    void updateStatus(ErpRegionRequest erpRegionRequest);

    /**
     * 校验区域编码是否重复
     */
    boolean validateRegionCodeRepeat(String regionCode, Long regionId);

    /**
     * 按父级ID分页查询子区域列表
     */
    PageResult<ErpRegionResponse> findPageByParent(ErpRegionRequest erpRegionRequest);

    /**
     * 查询区域树形结构（支持懒加载）
     */
    List<ErpRegionResponse> findTreeWithLazy(ErpRegionRequest erpRegionRequest);

    /**
     * 批量删除区域
     */
    void batchDelete(ErpRegionRequest erpRegionRequest);

    /**
     * 获取区域选择器数据
     */
    List<ErpRegionResponse> findSelector(ErpRegionRequest erpRegionRequest);
    
    /**
     * 获取区域关联的供应商和客户数量
     */
    ErpRegionResponse getRegionRelationCount(ErpRegionRequest erpRegionRequest);

}
