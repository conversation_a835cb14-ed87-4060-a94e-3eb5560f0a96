import{_ as le}from"./index-d0cfb2ce.js";import{_ as ne}from"./index-02bf6f00.js";import{_ as ae,P as se,K as ie,r as g,L as W,N as re,s as de,k as R,a as s,c as v,d as t,w as o,b as i,g as r,t as p,h as K,O as y,Q as x,F as D,e as U,f as C,M as $,E as ee,m as O,U as ue,n as ce,B as pe,I as _e,p as me,q as ge,D as ve,l as he,V as fe,W as we,J as Ce,u as be,v as Se,G as ye,H as xe}from"./index-18a1ea24.js";/* empty css              */import{_ as te}from"./index-bb869875.js";import{S as u}from"./SupplierApi-6b9315dd.js";import Te from"./SupplierAdd-e75cc367.js";import ke from"./SupplierEdit-dbecced4.js";import Ie from"./SupplierDetail-08f1c975.js";/* empty css              *//* empty css              *//* empty css              */import"./UniversalTree-6547889b.js";import"./regionApi-2c103d88.js";import"./index-60b48b32.js";/* empty css              *//* empty css              */const Ne={name:"SupplierIndex",components:{PlusOutlined:se,SmallDashOutlined:ie,SupplierAdd:Te,SupplierEdit:ke,SupplierDetail:Ie,RegionTree:te},setup(){const P=g(!1),n=g(!1),A=g(!1),e=g(!1),E=g({}),T=g(null),S=g([]),_=g(null),h=g([]),z=g(!1),k=W(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),I=W(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),F=W(()=>re()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24}),b=de({searchText:"",supplierType:void 0,businessMode:void 0,status:void 0,creditLevel:void 0,contactPerson:void 0,contactPhone:void 0,regionId:void 0}),V=u.getSupplierTypeOptions(),j=u.getBusinessModeOptions(),M=u.getSupplierStatusOptions(),G=u.getCreditLevelOptions(),N=[{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"supplierCode",title:"\u4F9B\u5E94\u5546\u7F16\u7801",width:140,ellipsis:!0,isShow:!0},{dataIndex:"supplierName",title:"\u4F9B\u5E94\u5546\u540D\u79F0",width:200,ellipsis:!0,isShow:!0},{dataIndex:"supplierShortName",title:"\u4F9B\u5E94\u5546\u7B80\u79F0",width:150,ellipsis:!0,isShow:!0},{dataIndex:"supplierType",title:"\u4F9B\u5E94\u5546\u7C7B\u578B",width:120,align:"center",isShow:!0},{dataIndex:"businessMode",title:"\u7ECF\u8425\u65B9\u5F0F",width:120,align:"center",isShow:!0},{dataIndex:"salesDeduction",title:"\u9500\u552E\u6263\u70B9",width:100,align:"center",isShow:!0},{dataIndex:"contactPerson",title:"\u8054\u7CFB\u4EBA",width:100,ellipsis:!0,isShow:!0},{dataIndex:"contactPhone",title:"\u8054\u7CFB\u7535\u8BDD",width:120,ellipsis:!0,isShow:!0},{dataIndex:"creditLevel",title:"\u4FE1\u7528\u7B49\u7EA7",width:100,align:"center",isShow:!0},{dataIndex:"status",title:"\u72B6\u6001",width:100,align:"center",isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}],c=()=>{_.value&&_.value.reload()},f=()=>{Object.keys(b).forEach(a=>{b[a]=a==="searchText"?"":void 0}),h.value=[],S.value=[],T.value&&T.value.clearSelection(),c()},w=()=>{P.value=!P.value},L=()=>{console.log("\u70B9\u51FB\u65B0\u589E\u4F9B\u5E94\u5546\u6309\u94AE"),n.value=!0,console.log("\u65B0\u589E\u5F39\u7A97\u72B6\u6001\u5DF2\u8BBE\u7F6E\u4E3Atrue:",n.value)},q=a=>{E.value={...a},A.value=!0},H=a=>{E.value={...a},e.value=!0},J=a=>{$.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u4F9B\u5E94\u5546\u5417\uFF1F",icon:t(ee),maskClosable:!0,onOk:()=>u.delete({supplierId:a.supplierId}).then(()=>{O.success("\u5220\u9664\u6210\u529F"),c()}).catch(m=>{O.error(m.message||"\u5220\u9664\u5931\u8D25")})})},B=()=>{var m;const a=(m=_.value)==null?void 0:m.getSelectedRows();if(!a||a.length===0){O.warning("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u6570\u636E");return}$.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ".concat(a.length," \u6761\u6570\u636E\u5417\uFF1F"),icon:t(ee),maskClosable:!0,onOk:()=>{const oe=a.map(Q=>Q.supplierId);return u.batchDelete({supplierIdList:oe}).then(()=>{O.success("\u5220\u9664\u6210\u529F"),c()}).catch(Q=>{O.error(Q.message||"\u5220\u9664\u5931\u8D25")})}})};return{tableRef:_,regionTreeRef:T,superSearch:P,where:b,columns:N,currentRecord:E,selectedRegionNodes:S,showEdit:A,showDetailModal:e,supplierTypeOptions:V,businessModeOptions:j,statusOptions:M,creditLevelOptions:G,labelCol:k,wrapperCol:I,spanCol:F,selectedRegionIds:h,treeCollapsed:z,showAdd:n,reload:c,clear:f,changeSuperSearch:w,handleRegionTreeSelect:(a,m)=>{console.log("\u533A\u57DF\u6811\u9009\u62E9:",a,m),h.value=a,m&&m.selectedNodes&&m.selectedNodes.length>0?(S.value=m.selectedNodes,b.regionId=a[0]):(S.value=[],b.regionId=void 0),c()},handleRegionTreeLoaded:a=>{console.log("\u533A\u57DF\u6811\u6570\u636E\u52A0\u8F7D\u5B8C\u6210:",a)},clearRegionFilter:()=>{h.value=[],S.value=[],b.regionId=void 0,T.value&&T.value.clearSelection(),c()},openAddModal:L,openEditModal:q,showDetail:H,remove:J,moreClick:({key:a})=>{a==="1"&&B()},batchDelete:B,getSupplierTypeName:a=>u.getSupplierTypeName(a),getBusinessModeName:a=>u.getBusinessModeName(a),getSupplierStatusName:a=>u.getSupplierStatusName(a),getCreditLevelName:a=>u.getCreditLevelName(a),getStatusTagColor:a=>u.getStatusTagColor(a),getBusinessModeTagColor:a=>u.getBusinessModeTagColor(a),getCreditLevelTagColor:a=>u.getCreditLevelTagColor(a)}}},Me={class:"guns-layout"},Le={class:"guns-layout-sidebar width-100 p-t-12"},Re={class:"sidebar-content"},De={class:"guns-layout-content"},Oe={class:"guns-layout"},Pe={class:"guns-layout-content-application"},Ee={class:"content-main"},Be={class:"content-main-header"},Ue={class:"header-content"},Ae={class:"header-content-left"},ze={key:0,class:"current-region-info"},Fe={class:"header-content-right"},Ve={class:"content-main-body"},je={class:"table-content"},Ge={key:0,class:"super-search",style:{"margin-top":"8px"}},qe={key:0},He={key:1,class:"text-muted"};function Je(P,n,A,e,E,T){const S=te,_=ue,h=ce,z=R("plus-outlined"),k=pe,I=_e,F=me,b=ge,V=R("small-dash-outlined"),j=ve,M=he,G=fe,N=we,c=Ce,f=be,w=Se,L=ye,q=xe,H=ne,J=le,B=R("supplier-add"),X=R("supplier-edit"),Y=R("supplier-detail");return s(),v("div",Me,[t(J,{width:"292px",cacheKey:"ERP_SUPPLIER_MANAGEMENT"},{content:o(()=>[i("div",De,[i("div",Oe,[i("div",Pe,[i("div",Ee,[i("div",Be,[i("div",Ue,[i("div",Ae,[t(h,{size:16},{default:o(()=>[e.selectedRegionNodes.length>0?(s(),v("span",ze,[n[11]||(n[11]=r(" \u5F53\u524D\u533A\u57DF\uFF1A")),t(_,{color:"blue"},{default:o(()=>[r(p(e.selectedRegionNodes[0].regionName),1)]),_:1})])):K("",!0)]),_:1})]),i("div",Fe,[t(h,{size:16},{default:o(()=>[t(k,{type:"primary",class:"border-radius",onClick:e.openAddModal},{default:o(()=>[t(z),n[12]||(n[12]=r(" \u65B0\u589E\u4F9B\u5E94\u5546 "))]),_:1,__:[12]},8,["onClick"]),t(j,null,{overlay:o(()=>[t(b,{onClick:e.moreClick},{default:o(()=>[t(F,{key:"1"},{default:o(()=>[t(I,{iconClass:"icon-opt-shanchu",color:"#60666b"}),n[13]||(n[13]=i("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[13]})]),_:1},8,["onClick"])]),default:o(()=>[t(k,{class:"border-radius"},{default:o(()=>[n[14]||(n[14]=r(" \u66F4\u591A ")),t(V)]),_:1,__:[14]})]),_:1})]),_:1})])])]),i("div",Ve,[i("div",je,[t(H,{columns:e.columns,where:e.where,fieldBusinessCode:"ERP_SUPPLIER_TABLE",showTableTool:"",showToolTotal:!1,rowId:"supplierId",ref:"tableRef",url:"/erp/supplier/page"},{toolLeft:o(()=>[t(M,{value:e.where.searchText,"onUpdate:value":n[0]||(n[0]=l=>e.where.searchText=l),bordered:!1,allowClear:"",placeholder:"\u4F9B\u5E94\u5546\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:e.reload,style:{width:"240px"},class:"search-input"},{prefix:o(()=>[t(I,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"]),t(G,{type:"vertical",class:"divider"}),i("a",{onClick:n[1]||(n[1]=(...l)=>e.changeSuperSearch&&e.changeSuperSearch(...l))},p(e.superSearch?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:o(()=>[e.superSearch?(s(),v("div",Ge,[t(q,{model:e.where,labelCol:e.labelCol,"wrapper-col":e.wrapperCol},{default:o(()=>[t(L,{gutter:16},{default:o(()=>[t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:"\u4F9B\u5E94\u5546\u7C7B\u578B:"},{default:o(()=>[t(c,{value:e.where.supplierType,"onUpdate:value":n[2]||(n[2]=l=>e.where.supplierType=l),placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546\u7C7B\u578B",style:{width:"100%"},allowClear:""},{default:o(()=>[(s(!0),v(D,null,U(e.supplierTypeOptions,l=>(s(),C(N,{key:l.value,value:l.value},{default:o(()=>[r(p(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:"\u7ECF\u8425\u65B9\u5F0F:"},{default:o(()=>[t(c,{value:e.where.businessMode,"onUpdate:value":n[3]||(n[3]=l=>e.where.businessMode=l),placeholder:"\u8BF7\u9009\u62E9\u7ECF\u8425\u65B9\u5F0F",style:{width:"100%"},allowClear:""},{default:o(()=>[(s(!0),v(D,null,U(e.businessModeOptions,l=>(s(),C(N,{key:l.value,value:l.value},{default:o(()=>[r(p(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:"\u72B6\u6001:"},{default:o(()=>[t(c,{value:e.where.status,"onUpdate:value":n[4]||(n[4]=l=>e.where.status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",style:{width:"100%"},allowClear:""},{default:o(()=>[(s(!0),v(D,null,U(e.statusOptions,l=>(s(),C(N,{key:l.value,value:l.value},{default:o(()=>[r(p(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16)]),_:1}),t(L,{gutter:16},{default:o(()=>[t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:"\u4FE1\u7528\u7B49\u7EA7:"},{default:o(()=>[t(c,{value:e.where.creditLevel,"onUpdate:value":n[5]||(n[5]=l=>e.where.creditLevel=l),placeholder:"\u8BF7\u9009\u62E9\u4FE1\u7528\u7B49\u7EA7",style:{width:"100%"},allowClear:""},{default:o(()=>[(s(!0),v(D,null,U(e.creditLevelOptions,l=>(s(),C(N,{key:l.value,value:l.value},{default:o(()=>[r(p(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:"\u8054\u7CFB\u4EBA:"},{default:o(()=>[t(M,{value:e.where.contactPerson,"onUpdate:value":n[6]||(n[6]=l=>e.where.contactPerson=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:"\u8054\u7CFB\u7535\u8BDD:"},{default:o(()=>[t(M,{value:e.where.contactPhone,"onUpdate:value":n[7]||(n[7]=l=>e.where.contactPhone=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD",allowClear:""},null,8,["value"])]),_:1})]),_:1},16)]),_:1}),t(L,{gutter:16},{default:o(()=>[t(w,y(x(e.spanCol)),{default:o(()=>[t(f,{label:" ",class:"not-label"},{default:o(()=>[t(h,{size:16},{default:o(()=>[t(k,{class:"border-radius",onClick:e.reload,type:"primary"},{default:o(()=>n[15]||(n[15]=[r("\u67E5\u8BE2")])),_:1,__:[15]},8,["onClick"]),t(k,{class:"border-radius",onClick:e.clear},{default:o(()=>n[16]||(n[16]=[r("\u91CD\u7F6E")])),_:1,__:[16]},8,["onClick"])]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])])):K("",!0)]),bodyCell:o(({column:l,record:d})=>[l.dataIndex==="supplierType"?(s(),C(_,{key:0},{default:o(()=>[r(p(e.getSupplierTypeName(d.supplierType)),1)]),_:2},1024)):l.dataIndex==="businessMode"?(s(),C(_,{key:1,color:e.getBusinessModeTagColor(d.businessMode)},{default:o(()=>[r(p(e.getBusinessModeName(d.businessMode)),1)]),_:2},1032,["color"])):l.dataIndex==="salesDeduction"?(s(),v(D,{key:2},[d.salesDeduction!==null&&d.salesDeduction!==void 0?(s(),v("span",qe,p(d.salesDeduction)+"% ",1)):(s(),v("span",He,"-"))],64)):l.dataIndex==="status"?(s(),C(_,{key:3,color:e.getStatusTagColor(d.status)},{default:o(()=>[r(p(e.getSupplierStatusName(d.status)),1)]),_:2},1032,["color"])):l.dataIndex==="creditLevel"?(s(),C(_,{key:4,color:e.getCreditLevelTagColor(d.creditLevel)},{default:o(()=>[r(p(e.getCreditLevelName(d.creditLevel)),1)]),_:2},1032,["color"])):l.key==="action"?(s(),C(h,{key:5,size:16},{default:o(()=>[t(I,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:Z=>e.openEditModal(d)},null,8,["onClick"]),t(I,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:Z=>e.remove(d)},null,8,["onClick"])]),_:2},1024)):K("",!0)]),_:1},8,["columns","where"])])])])])])])]),default:o(()=>[i("div",Le,[i("div",Re,[t(S,{ref:"regionTreeRef","show-badge":!0,onTreeSelect:e.handleRegionTreeSelect,onTreeDataLoaded:e.handleRegionTreeLoaded},null,8,["onTreeSelect","onTreeDataLoaded"])])])]),_:1}),t(B,{visible:e.showAdd,"onUpdate:visible":n[8]||(n[8]=l=>e.showAdd=l),onDone:e.reload},null,8,["visible","onDone"]),t(X,{visible:e.showEdit,"onUpdate:visible":n[9]||(n[9]=l=>e.showEdit=l),data:e.currentRecord,onDone:e.reload},null,8,["visible","data","onDone"]),t(Y,{visible:e.showDetailModal,"onUpdate:visible":n[10]||(n[10]=l=>e.showDetailModal=l),data:e.currentRecord},null,8,["visible","data"])])}const vt=ae(Ne,[["render",Je],["__scopeId","data-v-536cf5f0"]]);export{vt as default};
