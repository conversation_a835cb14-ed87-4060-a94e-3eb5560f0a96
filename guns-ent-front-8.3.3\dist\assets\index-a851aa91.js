import{_ as lt}from"./index-02bf6f00.js";import{_ as st,K as it,a1 as rt,r as y,L as J,N as ct,s as dt,o as ut,k as w,a as p,c as g,b as i,d as e,w as a,g as d,t as u,O as M,Q as b,h as m,f as U,a2 as _t,m as Z,n as pt,I as mt,p as ft,q as kt,B as vt,D as St,l as yt,V as gt,u as ht,v as Ct,W as wt,J as Mt,G as bt,H as It,U as Nt}from"./index-18a1ea24.js";/* empty css              */import{_ as X}from"./SupplierSelector-e8033f79.js";import{I as Y}from"./InventoryApi-271c380c.js";import Tt from"./InventoryDetail-1515dba8.js";import xt from"./InventoryHistory-0dc84751.js";import Ot from"./InventoryAdjustModal-63b05e6b.js";import At from"./SetMinStockModal-64975e1f.js";import Et from"./InventoryStatisticsModal-2c510c14.js";/* empty css              *//* empty css              *//* empty css              */import"./SupplierApi-6b9315dd.js";/* empty css              *//* empty css              *//* empty css              */import"./InventoryHistoryApi-416494e3.js";/* empty css              *//* empty css              */import"./ProductSelector-22648d1a.js";import"./ProductApi-52d42f8e.js";import"./productCategoryApi-39e417fd.js";const Rt={name:"InventoryIndex",components:{SmallDashOutlined:it,DownOutlined:rt,SupplierSelector:X,InventoryDetail:Tt,InventoryHistory:xt,InventoryAdjustModal:Ot,SetMinStockModal:At,InventoryStatisticsModal:Et},setup(){const x=y(!1),o=y(!1),O=y(!1),t=y(!1),A=y(!1),H=y(!1),c=y({}),f=y(null),E=J(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),L=J(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),F=J(()=>ct()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24}),_=dt({searchText:"",productName:"",productCode:"",supplierId:void 0,pricingType:void 0,stockStatus:void 0,businessModeList:["PURCHASE_SALE","CONSIGNMENT"]}),P=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:250,fixed:"left"},{title:"\u4F9B\u5E94\u5546",key:"supplierInfo",width:180},{title:"\u8BA1\u4EF7\u7C7B\u578B",key:"pricingType",width:100},{title:"\u5F53\u524D\u5E93\u5B58",key:"currentStock",width:120,align:"right"},{title:"\u9884\u8B66\u503C",key:"minStock",width:100,align:"right"},{title:"\u5E93\u5B58\u4EF7\u503C",key:"totalValue",width:120,align:"right"},{title:"\u5E93\u5B58\u72B6\u6001",key:"stockStatus",width:100},{title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updateTime",key:"updateTime",width:160},{title:"\u64CD\u4F5C",key:"action",width:200,fixed:"right"}],I=()=>{x.value=!x.value},h=()=>{f.value.reload()},v=()=>{_.searchText="",_.productName="",_.productCode="",_.supplierId=void 0,_.pricingType=void 0,_.stockStatus=void 0,h()},S=n=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"})[n]||"default",B=n=>({NORMAL:"\u666E\u901A",WEIGHT:"\u79F0\u91CD",PIECE:"\u8BA1\u4EF6",VARIABLE:"\u53D8\u4EF7"})[n]||n,k=n=>({NORMAL:"blue",WEIGHT:"orange",PIECE:"green",VARIABLE:"purple"})[n]||"default",R=(n,r)=>{const C=parseFloat(n)||0,at=parseFloat(r)||0;return C<=0?"stock-danger":C<=at?"stock-warning":"stock-normal"},D=(n,r)=>{switch(n){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return r||"\u4E2A"}},V=(n,r)=>{if(!n)return"0";const C=r==="WEIGHT"?3:0;return parseFloat(n).toFixed(C)},N=n=>n?parseFloat(n).toFixed(2):"0.00",G=n=>({NORMAL:"\u6B63\u5E38",WARNING:"\u9884\u8B66",OUT_OF_STOCK:"\u7F3A\u8D27",FROZEN:"\u51BB\u7ED3"})[n]||"\u672A\u77E5",j=n=>({NORMAL:"green",WARNING:"orange",OUT_OF_STOCK:"red",FROZEN:"blue"})[n]||"default",z=n=>{if(n.stockStatus)return n.stockStatus;const r=parseFloat(n.currentStock||0),C=parseFloat(n.minStock||0);return r<=0?"OUT_OF_STOCK":C>0&&r<=C?"WARNING":"NORMAL"},W=n=>{c.value={...n},A.value=!0},K=n=>{c.value={...n},o.value=!0},q=n=>{c.value={...n},O.value=!0},l=n=>{c.value={...n},o.value=!1,O.value=!0},s=n=>{c.value={...n},t.value=!0},T=()=>{c.value={},t.value=!0},$=({key:n})=>{n==="1"?Q():n==="2"&&(H.value=!0)},Q=()=>{try{Y.exportInventory(_),Z.success("\u5BFC\u51FA\u6210\u529F")}catch(n){Z.error("\u5BFC\u51FA\u5931\u8D25\uFF1A"+(n.message||"\u672A\u77E5\u9519\u8BEF"))}},tt=async n=>{try{c.value={...n},Z.info("\u5E93\u5B58\u8C03\u62E8\u529F\u80FD\u5F85\u5B9E\u73B0")}catch(r){throw r}},ot=async n=>{try{await Y.initStock({productId:n.productId}),h()}catch(r){throw r}},et=()=>{t.value=!1,h()},nt=()=>{A.value=!1,h()};return ut(()=>{}),{superSearch:x,showDetailModal:o,showHistoryModal:O,showAdjustModal:t,showSetMinStockModal:A,showStatisticsModal:H,currentRecord:c,tableRef:f,labelCol:E,wrapperCol:L,spanCol:F,where:_,columns:P,changeSuperSearch:I,reload:h,reset:v,getBusinessModeColor:S,getPricingTypeName:B,getPricingTypeColor:k,getStockClass:R,getStockUnit:D,formatStock:V,formatAmount:N,getStockStatusName:G,getStockStatusColor:j,calculateStockStatus:z,showDetail:K,showHistory:q,handleShowHistoryFromDetail:l,adjustStock:s,transferStock:tt,initStock:ot,openAdjustModal:T,openSetMinStockModal:W,moreClick:$,exportData:Q,handleAdjustOk:et,handleSetMinStockOk:nt}}},Ut={class:"guns-layout"},Ht={class:"guns-layout-content"},Lt={class:"guns-layout"},Ft={class:"guns-layout-content-application"},Pt={class:"content-mian"},Bt={class:"content-mian-header"},Dt={class:"header-content"},Vt={class:"header-content-left"},Gt={class:"header-content-right"},jt={class:"content-mian-body"},zt={class:"table-content"},Wt={key:0,class:"super-search",style:{"margin-top":"8px"}},Kt={key:0,class:"product-info"},qt={class:"product-name"},Jt=["onClick"],Zt={class:"product-details"},Qt={class:"product-code"},Yt={key:0,class:"product-barcode"},Xt={key:1},$t={class:"supplier-name"},to={key:3,class:"stock-info"},oo={class:"stock-unit"},eo={key:4,class:"min-stock"},no={key:5,class:"total-value"};function ao(x,o,O,t,A,H){const c=pt,f=mt,E=ft,L=kt,F=w("small-dash-outlined"),_=vt,P=St,I=yt,h=gt,v=ht,S=Ct,B=X,k=wt,R=Mt,D=bt,V=It,N=Nt,G=lt,j=w("inventory-detail"),z=w("inventory-history"),W=w("inventory-adjust-modal"),K=w("set-min-stock-modal"),q=w("inventory-statistics-modal");return p(),g("div",Ut,[i("div",Ht,[i("div",Lt,[i("div",Ft,[i("div",Pt,[i("div",Bt,[i("div",Dt,[i("div",Vt,[e(c,{size:16})]),i("div",Gt,[e(c,{size:16},{default:a(()=>[e(P,null,{overlay:a(()=>[e(L,{onClick:t.moreClick},{default:a(()=>[e(E,{key:"1"},{default:a(()=>[e(f,{iconClass:"icon-opt-daochu",color:"#60666b"}),o[12]||(o[12]=i("span",null,"\u5BFC\u51FA\u6570\u636E",-1))]),_:1,__:[12]}),e(E,{key:"2"},{default:a(()=>[e(f,{iconClass:"icon-opt-tongji",color:"#60666b"}),o[13]||(o[13]=i("span",null,"\u5E93\u5B58\u7EDF\u8BA1",-1))]),_:1,__:[13]})]),_:1},8,["onClick"])]),default:a(()=>[e(_,{class:"border-radius"},{default:a(()=>[o[14]||(o[14]=d(" \u66F4\u591A ")),e(F)]),_:1,__:[14]})]),_:1})]),_:1})])])]),i("div",jt,[i("div",zt,[e(G,{columns:t.columns,where:t.where,fieldBusinessCode:"ERP_INVENTORY_TABLE",showTableTool:"",showToolTotal:!1,rowId:"productId",ref:"tableRef",url:"/erp/inventory/page",methods:"post"},{toolLeft:a(()=>[e(I,{value:t.where.searchText,"onUpdate:value":o[0]||(o[0]=l=>t.where.searchText=l),placeholder:"\u5546\u54C1\u540D\u79F0\u3001\u7F16\u7801\u3001\u6761\u5F62\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:t.reload,bordered:!1,class:"search-input"},{prefix:a(()=>[e(f,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"]),e(h,{type:"vertical",class:"divider"}),i("a",{onClick:o[1]||(o[1]=(...l)=>t.changeSuperSearch&&t.changeSuperSearch(...l))},u(t.superSearch?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:a(()=>[t.superSearch?(p(),g("div",Wt,[e(V,{model:t.where,labelCol:t.labelCol,"wrapper-col":t.wrapperCol},{default:a(()=>[e(D,{gutter:16},{default:a(()=>[e(S,M(b(t.spanCol)),{default:a(()=>[e(v,{label:"\u5546\u54C1\u540D\u79F0:"},{default:a(()=>[e(I,{value:t.where.productName,"onUpdate:value":o[2]||(o[2]=l=>t.where.productName=l),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),e(S,M(b(t.spanCol)),{default:a(()=>[e(v,{label:"\u5546\u54C1\u7F16\u7801:"},{default:a(()=>[e(I,{value:t.where.productCode,"onUpdate:value":o[3]||(o[3]=l=>t.where.productCode=l),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u7F16\u7801",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),e(S,M(b(t.spanCol)),{default:a(()=>[e(v,{label:"\u4F9B\u5E94\u5546:"},{default:a(()=>[e(B,{value:t.where.supplierId,"onUpdate:value":o[4]||(o[4]=l=>t.where.supplierId=l),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),e(S,M(b(t.spanCol)),{default:a(()=>[e(v,{label:"\u8BA1\u4EF7\u7C7B\u578B:"},{default:a(()=>[e(R,{value:t.where.pricingType,"onUpdate:value":o[5]||(o[5]=l=>t.where.pricingType=l),placeholder:"\u8BF7\u9009\u62E9\u8BA1\u4EF7\u7C7B\u578B",allowClear:""},{default:a(()=>[e(k,{value:"NORMAL"},{default:a(()=>o[15]||(o[15]=[d("\u666E\u901A")])),_:1,__:[15]}),e(k,{value:"WEIGHT"},{default:a(()=>o[16]||(o[16]=[d("\u79F0\u91CD")])),_:1,__:[16]}),e(k,{value:"PIECE"},{default:a(()=>o[17]||(o[17]=[d("\u8BA1\u4EF6")])),_:1,__:[17]}),e(k,{value:"VARIABLE"},{default:a(()=>o[18]||(o[18]=[d("\u53D8\u4EF7")])),_:1,__:[18]})]),_:1},8,["value"])]),_:1})]),_:1},16),e(S,M(b(t.spanCol)),{default:a(()=>[e(v,{label:"\u5E93\u5B58\u72B6\u6001:"},{default:a(()=>[e(R,{value:t.where.stockStatus,"onUpdate:value":o[6]||(o[6]=l=>t.where.stockStatus=l),placeholder:"\u8BF7\u9009\u62E9\u5E93\u5B58\u72B6\u6001",allowClear:""},{default:a(()=>[e(k,{value:"NORMAL"},{default:a(()=>o[19]||(o[19]=[d("\u6B63\u5E38")])),_:1,__:[19]}),e(k,{value:"WARNING"},{default:a(()=>o[20]||(o[20]=[d("\u9884\u8B66")])),_:1,__:[20]}),e(k,{value:"OUT_OF_STOCK"},{default:a(()=>o[21]||(o[21]=[d("\u7F3A\u8D27")])),_:1,__:[21]})]),_:1},8,["value"])]),_:1})]),_:1},16),e(S,M(b(t.spanCol)),{default:a(()=>[e(v,{label:" ",colon:!1},{default:a(()=>[e(c,null,{default:a(()=>[e(_,{type:"primary",onClick:t.reload},{default:a(()=>o[22]||(o[22]=[d("\u641C\u7D22")])),_:1,__:[22]},8,["onClick"]),e(_,{onClick:t.reset},{default:a(()=>o[23]||(o[23]=[d("\u91CD\u7F6E")])),_:1,__:[23]},8,["onClick"])]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])])):m("",!0)]),bodyCell:a(({column:l,record:s})=>[l.key==="productInfo"?(p(),g("div",Kt,[i("div",qt,[i("a",{onClick:T=>t.showDetail(s),class:"table-link"},u(s.productName),9,Jt)]),i("div",Zt,[i("span",Qt,u(s.productCode),1),s.barcode?(p(),g("span",Yt,u(s.barcode),1)):m("",!0)])])):m("",!0),l.key==="supplierInfo"?(p(),g("div",Xt,[i("div",$t,u(s.supplierName),1),s.businessModeName?(p(),U(N,{key:0,size:"small",color:t.getBusinessModeColor(s.businessMode)},{default:a(()=>[d(u(s.businessModeName),1)]),_:2},1032,["color"])):m("",!0)])):m("",!0),l.key==="pricingType"?(p(),U(N,{key:2,color:t.getPricingTypeColor(s.pricingType)},{default:a(()=>[d(u(t.getPricingTypeName(s.pricingType)),1)]),_:2},1032,["color"])):m("",!0),l.key==="currentStock"?(p(),g("div",to,[i("span",{class:_t(t.getStockClass(s.currentStock,s.minStock))},u(t.formatStock(s.currentStock,s.pricingType)),3),i("span",oo,u(t.getStockUnit(s.pricingType,s.unit)),1)])):m("",!0),l.key==="minStock"?(p(),g("span",eo,u(t.formatStock(s.minStock,s.pricingType))+" "+u(t.getStockUnit(s.pricingType,s.unit)),1)):m("",!0),l.key==="totalValue"?(p(),g("span",no,"\xA5"+u(t.formatAmount(s.totalValue)),1)):m("",!0),l.key==="stockStatus"?(p(),U(N,{key:6,color:t.getStockStatusColor(t.calculateStockStatus(s))},{default:a(()=>[d(u(t.getStockStatusName(t.calculateStockStatus(s))),1)]),_:2},1032,["color"])):m("",!0),l.key==="action"?(p(),U(c,{key:7,size:16},{default:a(()=>[e(f,{iconClass:"icon-opt-xiangqing","font-size":"24px",title:"\u67E5\u770B\u8BE6\u60C5",color:"#60666b",onClick:T=>t.showDetail(s)},null,8,["onClick"]),e(f,{iconClass:"icon-opt-shezhi","font-size":"24px",title:"\u8BBE\u7F6E\u9884\u8B66\u503C",color:"#60666b",onClick:T=>t.openSetMinStockModal(s)},null,8,["onClick"]),e(f,{iconClass:"icon-opt-xiangqing","font-size":"24px",title:"\u67E5\u770B\u5386\u53F2",color:"#60666b",onClick:T=>t.showHistory(s)},null,8,["onClick"])]),_:2},1024)):m("",!0)]),_:1},8,["columns","where"])])])])])])]),e(j,{visible:t.showDetailModal,"onUpdate:visible":o[7]||(o[7]=l=>t.showDetailModal=l),data:t.currentRecord,onShowHistory:t.handleShowHistoryFromDetail},null,8,["visible","data","onShowHistory"]),e(z,{visible:t.showHistoryModal,"onUpdate:visible":o[8]||(o[8]=l=>t.showHistoryModal=l),data:t.currentRecord},null,8,["visible","data"]),e(W,{visible:t.showAdjustModal,"onUpdate:visible":o[9]||(o[9]=l=>t.showAdjustModal=l),data:t.currentRecord,onOk:t.handleAdjustOk},null,8,["visible","data","onOk"]),e(K,{visible:t.showSetMinStockModal,"onUpdate:visible":o[10]||(o[10]=l=>t.showSetMinStockModal=l),data:t.currentRecord,onOk:t.handleSetMinStockOk},null,8,["visible","data","onOk"]),e(q,{visible:t.showStatisticsModal,"onUpdate:visible":o[11]||(o[11]=l=>t.showStatisticsModal=l)},null,8,["visible"])])}const xo=st(Rt,[["render",ao],["__scopeId","data-v-aaa7dfda"]]);export{xo as default};
