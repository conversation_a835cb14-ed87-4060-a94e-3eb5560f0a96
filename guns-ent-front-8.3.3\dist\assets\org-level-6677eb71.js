import{r as m,o as E,a as n,f as s,w as r,d as p,c as F,F as M,e as R,g as $,t as b,h as u,b as T,m as _,I as D,W as J,J as P,l as W,ci as q,n as G,M as H}from"./index-18a1ea24.js";import{_ as K}from"./index-02bf6f00.js";import{O as g}from"./OrgApi-021dd6dd.js";/* empty css              *//* empty css              *//* empty css              */const le={__name:"org-level",props:{visible:Boolean,levelNumberList:Array},emits:["update:visible","done"],setup(N,{emit:x}){const f=N,C=x,d=m(!1),o=m([]),y=m([{title:"\u5C42\u7EA7\u7EA7\u522B",width:100,dataIndex:"levelNumber",customCell:l=>({class:"cell-hover"})},{title:"\u5C42\u7EA7\u540D\u79F0",width:100,dataIndex:"levelName",customCell:l=>({class:"cell-hoverr"})},{title:"\u5C42\u7EA7\u7F16\u7801",width:100,dataIndex:"levelCode",customCell:l=>({class:"cell-hoverr"})},{title:"\u989C\u8272",width:100,dataIndex:"levelColor",customCell:l=>({class:"cell-hoverr"})},{title:"\u64CD\u4F5C",width:60,align:"center",dataIndex:"action"}]);E(()=>{k()});const k=async()=>{o.value=await g.organizationLevelList()},w=()=>{let l={levelNumber:void 0,levelName:"",levelCode:"",levelColor:"fff"};o.value.push(l)},I=l=>{o.value=o.value.filter((a,c)=>c!==l)},v=l=>{C("update:visible",l)},U=async()=>{if(o.value.length==0)return _.warning("\u5C42\u7EA7\u4E0D\u80FD\u4E3A\u7A7A");if(o.value.some(a=>a.levelNumber===void 0||a.levelName===""||a.levelCode===""||a.levelColor===""))return _.warning("\u5B58\u5728 undefined \u6216\u8005\u7A7A\u5B57\u7B26\u4E32");d.value=!0,g.updateTotal({levelList:o.value}).then(a=>{_.success(a.message),v(!1),C("done")}).finally(()=>d.value=!1)};return(l,a)=>{const c=D,L=J,B=P,h=W,O=q,S=G,V=K,z=H;return n(),s(z,{width:762,maskClosable:!1,visible:f.visible,"confirm-loading":d.value,forceRender:!0,title:"\u5C42\u7EA7\u7EF4\u62A4","body-style":{paddingBottom:"8px"},"onUpdate:visible":v,onOk:U,onClose:a[0]||(a[0]=i=>v(!1))},{default:r(()=>[p(V,{columns:y.value,ref:"tableRef",dataSource:o.value,loading:d.value,size:"small",rowSelection:!1,height100:!1,isPage:!1},{toolRight:r(()=>[p(c,{iconClass:"icon-opt-tianjia","font-size":"24px",title:"\u65B0\u589E",color:"#60666b",onClick:w})]),bodyCell:r(({column:i,record:t,index:A})=>[i.dataIndex=="levelNumber"?(n(),s(B,{key:0,value:t.levelNumber,"onUpdate:value":e=>t.levelNumber=e,bordered:!1,showArrow:!1,style:{width:"100%"},placeholder:"\u8BF7\u9009\u62E9"},{default:r(()=>[(n(!0),F(M,null,R(f.levelNumberList,e=>(n(),s(L,{value:e.value,key:e.value},{default:r(()=>[$(b(e.name),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value"])):u("",!0),i.dataIndex=="levelName"?(n(),s(h,{key:1,value:t.levelName,"onUpdate:value":e=>t.levelName=e,bordered:!1,placeholder:"\u8BF7\u8F93\u5165"},null,8,["value","onUpdate:value"])):u("",!0),i.dataIndex=="levelCode"?(n(),s(h,{key:2,value:t.levelCode,"onUpdate:value":e=>t.levelCode=e,bordered:!1,placeholder:"\u8BF7\u8F93\u5165"},null,8,["value","onUpdate:value"])):u("",!0),i.dataIndex=="levelColor"?(n(),s(S,{key:3},{default:r(()=>[p(O,{value:t.levelColor,"onUpdate:value":e=>t.levelColor=e},null,8,["value","onUpdate:value"]),T("span",null,b(t.levelColor),1)]),_:2},1024)):u("",!0),i.dataIndex=="action"?(n(),s(c,{key:4,iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:e=>I(A)},null,8,["onClick"])):u("",!0)]),_:1},8,["columns","dataSource","loading"])]),_:1},8,["visible","confirm-loading"])}}};export{le as default};
