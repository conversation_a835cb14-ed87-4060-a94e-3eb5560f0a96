package cn.stylefeng.roses.kernel.websocket.redis.action;


import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import com.alibaba.fastjson.JSONObject;

/**
 * 订阅Redis的消息，删除某个用户的socket
 *
 * <AUTHOR>
 * @since 2024-01-15 15:24
 */
public interface Action {

    /**
     * Redis通道中消息的属性名，标识用户token
     */
    String IDENTIFIER = "identifier";

    /**
     * Redis通道中消息的属性名，标识用户id
     */
    String USER_ID = "userId";

    /**
     * Redis通道中消息的属性名，具体的消息内容
     */
    String MESSAGE = "message";

    /**
     * Redis通道中消息的属性名，具体的消息内容
     */
    String ACTION = "action";

    void doMessage(WebSocketManagerApi manager, JSONObject object);

}
