<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryHistoryMapper">

    <!-- 分页查询库存历史记录 -->
    <select id="selectInventoryHistoryPage" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse">
        SELECT
            ih.id,
            ih.product_id,
            ih.operation_type,
            ih.quantity_change,
            ih.stock_before,
            ih.stock_after,
            ih.reference_id,
            ih.reference_type,
            ih.operation_time,
            ih.operation_user,
            CONCAT('操作员', COALESCE(ih.operation_user, '')) as operation_user_name,
            ih.remark,
            p.product_code,
            p.product_name,
            p.product_short_name,
            p.barcode,
            p.unit,
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            CASE 
                WHEN ih.operation_type = 'IN' THEN '入库'
                WHEN ih.operation_type = 'OUT' THEN '出库'
                WHEN ih.operation_type = 'ADJUST' THEN '调整'
                WHEN ih.operation_type = 'SALE' THEN '销售'
                ELSE ih.operation_type
            END as operation_type_name,
            CASE 
                WHEN ih.reference_type = 'PURCHASE_ORDER' THEN '采购入库单'
                WHEN ih.reference_type = 'SALE_ORDER' THEN '销售单'
                WHEN ih.reference_type = 'ADJUST_ORDER' THEN '调整单'
                ELSE ih.reference_type
            END as reference_type_name,
            CASE 
                WHEN ih.reference_type = 'PURCHASE_ORDER' THEN po.order_no
                ELSE CONCAT(ih.reference_type, '-', ih.reference_id)
            END as reference_no
        FROM
            erp_inventory_history ih
        LEFT JOIN
            erp_product p ON ih.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_purchase_order po ON ih.reference_type = 'PURCHASE_ORDER' AND ih.reference_id = po.id
        <where>
            <if test="request.productId != null">
                AND ih.product_id = #{request.productId}
            </if>
            <if test="request.productCode != null and request.productCode != ''">
                AND p.product_code LIKE CONCAT('%', #{request.productCode}, '%')
            </if>
            <if test="request.productName != null and request.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{request.productName}, '%')
            </if>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.supplierName != null and request.supplierName != ''">
                AND s.supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
            </if>
            <if test="request.operationType != null and request.operationType != ''">
                AND ih.operation_type = #{request.operationType}
            </if>
            <if test="request.operationTypeList != null and request.operationTypeList.size() > 0">
                AND ih.operation_type IN
                <foreach collection="request.operationTypeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="request.referenceType != null and request.referenceType != ''">
                AND ih.reference_type = #{request.referenceType}
            </if>
            <if test="request.referenceTypeList != null and request.referenceTypeList.size() > 0">
                AND ih.reference_type IN
                <foreach collection="request.referenceTypeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="request.referenceId != null">
                AND ih.reference_id = #{request.referenceId}
            </if>
            <if test="request.startTime != null">
                AND ih.operation_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null">
                AND ih.operation_time &lt;= #{request.endTime}
            </if>
            <if test="request.operationUser != null">
                AND ih.operation_user = #{request.operationUser}
            </if>
        </where>
        ORDER BY ih.operation_time DESC
    </select>

    <!-- 根据商品ID查询库存历史记录 -->
    <select id="selectByProductId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse">
        SELECT
            ih.id,
            ih.product_id,
            ih.operation_type,
            ih.quantity_change,
            ih.stock_before,
            ih.stock_after,
            ih.reference_id,
            ih.reference_type,
            ih.operation_time,
            ih.operation_user,
            CONCAT('操作员', COALESCE(ih.operation_user, '')) as operation_user_name,
            ih.remark,
            p.product_code,
            p.product_name,
            p.product_short_name,
            p.barcode,
            p.unit,
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            CASE 
                WHEN ih.operation_type = 'IN' THEN '入库'
                WHEN ih.operation_type = 'OUT' THEN '出库'
                WHEN ih.operation_type = 'ADJUST' THEN '调整'
                WHEN ih.operation_type = 'SALE' THEN '销售'
                ELSE ih.operation_type
            END as operation_type_name,
            CASE 
                WHEN ih.reference_type = 'PURCHASE_ORDER' THEN '采购入库单'
                WHEN ih.reference_type = 'SALE_ORDER' THEN '销售单'
                WHEN ih.reference_type = 'ADJUST_ORDER' THEN '调整单'
                ELSE ih.reference_type
            END as reference_type_name,
            CASE 
                WHEN ih.reference_type = 'PURCHASE_ORDER' THEN po.order_no
                ELSE CONCAT(ih.reference_type, '-', ih.reference_id)
            END as reference_no
        FROM
            erp_inventory_history ih
        LEFT JOIN
            erp_product p ON ih.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        LEFT JOIN
            erp_purchase_order po ON ih.reference_type = 'PURCHASE_ORDER' AND ih.reference_id = po.id
        WHERE
            ih.product_id = #{productId}
        ORDER BY ih.operation_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据关联单据查询库存历史记录 -->
    <select id="selectByReference" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse">
        SELECT
            ih.id,
            ih.product_id,
            ih.operation_type,
            ih.quantity_change,
            ih.stock_before,
            ih.stock_after,
            ih.reference_id,
            ih.reference_type,
            ih.operation_time,
            ih.operation_user,
            CONCAT('操作员', COALESCE(ih.operation_user, '')) as operation_user_name,
            ih.remark,
            p.product_code,
            p.product_name,
            p.product_short_name,
            p.barcode,
            p.unit,
            s.supplier_id,
            s.supplier_code,
            s.supplier_name,
            CASE 
                WHEN ih.operation_type = 'IN' THEN '入库'
                WHEN ih.operation_type = 'OUT' THEN '出库'
                WHEN ih.operation_type = 'ADJUST' THEN '调整'
                WHEN ih.operation_type = 'SALE' THEN '销售'
                ELSE ih.operation_type
            END as operation_type_name,
            CASE 
                WHEN ih.reference_type = 'PURCHASE_ORDER' THEN '采购入库单'
                WHEN ih.reference_type = 'SALE_ORDER' THEN '销售单'
                WHEN ih.reference_type = 'ADJUST_ORDER' THEN '调整单'
                ELSE ih.reference_type
            END as reference_type_name
        FROM
            erp_inventory_history ih
        LEFT JOIN
            erp_product p ON ih.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        WHERE
            ih.reference_type = #{referenceType}
            AND ih.reference_id = #{referenceId}
        ORDER BY ih.operation_time DESC
    </select>

    <!-- 统计商品的库存变动总量 -->
    <select id="getTotalQuantityChange" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(ih.quantity_change), 0)
        FROM
            erp_inventory_history ih
        WHERE
            ih.product_id = #{productId}
            <if test="operationType != null and operationType != ''">
                AND ih.operation_type = #{operationType}
            </if>
            <if test="startTime != null">
                AND ih.operation_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ih.operation_time &lt;= #{endTime}
            </if>
    </select>

    <!-- 统计供应商的库存变动记录数量 -->
    <select id="countBySupplier" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            erp_inventory_history ih
        LEFT JOIN
            erp_product p ON ih.product_id = p.product_id
        WHERE
            p.supplier_id = #{supplierId}
            <if test="operationType != null and operationType != ''">
                AND ih.operation_type = #{operationType}
            </if>
            <if test="startTime != null">
                AND ih.operation_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ih.operation_time &lt;= #{endTime}
            </if>
    </select>

    <!-- 根据操作类型统计库存变动 -->
    <select id="getOperationTypeStats" resultType="cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryHistoryMapper$OperationTypeStats">
        SELECT
            ih.operation_type,
            CASE 
                WHEN ih.operation_type = 'IN' THEN '入库'
                WHEN ih.operation_type = 'OUT' THEN '出库'
                WHEN ih.operation_type = 'ADJUST' THEN '调整'
                WHEN ih.operation_type = 'SALE' THEN '销售'
                ELSE ih.operation_type
            END as operation_type_name,
            COUNT(1) as count,
            COALESCE(SUM(ih.quantity_change), 0) as total_quantity_change
        FROM
            erp_inventory_history ih
        LEFT JOIN
            erp_product p ON ih.product_id = p.product_id
        LEFT JOIN
            erp_supplier s ON p.supplier_id = s.supplier_id
        <where>
            <if test="request.productId != null">
                AND ih.product_id = #{request.productId}
            </if>
            <if test="request.supplierId != null">
                AND s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.startTime != null">
                AND ih.operation_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null">
                AND ih.operation_time &lt;= #{request.endTime}
            </if>
        </where>
        GROUP BY ih.operation_type
        ORDER BY count DESC
    </select>

    <!-- 获取商品的入库历史统计 -->
    <select id="getProductInboundStats" resultType="cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryHistoryMapper$ProductInboundStats">
        SELECT
            COUNT(1) as total_inbound_count,
            COALESCE(SUM(ih.quantity_change), 0) as total_inbound_quantity,
            COALESCE(AVG(ih.quantity_change), 0) as avg_inbound_quantity,
            MAX(ih.operation_time) as last_inbound_time
        FROM
            erp_inventory_history ih
        WHERE
            ih.product_id = #{productId}
            AND ih.operation_type = 'IN'
            <if test="startTime != null">
                AND ih.operation_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ih.operation_time &lt;= #{endTime}
            </if>
    </select>

    <!-- 获取商品的出库历史统计 -->
    <select id="getProductOutboundStats" resultType="cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryHistoryMapper$ProductOutboundStats">
        SELECT
            COUNT(1) as total_outbound_count,
            COALESCE(SUM(ABS(ih.quantity_change)), 0) as total_outbound_quantity,
            COALESCE(AVG(ABS(ih.quantity_change)), 0) as avg_outbound_quantity,
            MAX(ih.operation_time) as last_outbound_time
        FROM
            erp_inventory_history ih
        WHERE
            ih.product_id = #{productId}
            AND ih.operation_type IN ('OUT', 'SALE')
            <if test="startTime != null">
                AND ih.operation_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ih.operation_time &lt;= #{endTime}
            </if>
    </select>

    <!-- 批量插入库存历史记录 -->
    <insert id="batchInsertHistory">
        INSERT INTO erp_inventory_history (
            product_id,
            operation_type,
            quantity_change,
            stock_before,
            stock_after,
            reference_id,
            reference_type,
            operation_time,
            operation_user,
            remark,
            tenant_id,
            create_time,
            create_user,
            update_time,
            update_user
        ) VALUES
        <foreach collection="historyList" item="history" separator=",">
            (
                #{history.productId},
                #{history.operationType},
                #{history.quantityChange},
                #{history.stockBefore},
                #{history.stockAfter},
                #{history.referenceId},
                #{history.referenceType},
                #{history.operationTime},
                #{history.operationUser},
                #{history.remark},
                #{history.tenantId},
                #{history.createTime},
                #{history.createUser},
                #{history.updateTime},
                #{history.updateUser}
            )
        </foreach>
    </insert>

</mapper>