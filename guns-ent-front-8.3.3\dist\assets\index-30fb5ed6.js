import{_ as Q}from"./index-d0cfb2ce.js";import{_ as W}from"./index-02bf6f00.js";import{_ as X,P as Z,K as $,r as k,L as z,N as ee,s as te,o as oe,k as S,a as s,c as x,d as n,w as t,b as d,g as l,t as R,h as U,O as V,Q as G,F as Y,f as p,M as j,E as q,m as K,U as ne,n as le,B as ae,I as se,p as re,q as de,D as ie,l as _e,V as ce,W as ue,J as me,u as pe,v as ge,G as fe,H as ye}from"./index-18a1ea24.js";/* empty css              */import{P as H}from"./productCategoryApi-39e417fd.js";import ve from"./product-category-tree-7fb797dc.js";import Ce from"./addForm-96d993e5.js";/* empty css              *//* empty css              *//* empty css              */import"./UniversalTree-6547889b.js";/* empty css              *//* empty css              */const we={name:"ErpProductCategory",components:{PlusOutlined:Z,SmallDashOutlined:$,productCategoryTree:ve,addForm:Ce},setup(){const E=k(!1),e=k(!1),T=k({}),o=k(null),f=k(null),y=k(),O=z(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),_=z(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),v=z(()=>ee()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24}),c=te({parentId:void 0,searchText:"",status:void 0,categoryLevel:void 0}),C=[{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"categoryCode",title:"\u5206\u7C7B\u7F16\u7801",width:140,ellipsis:!0,isShow:!0},{dataIndex:"categoryName",title:"\u5206\u7C7B\u540D\u79F0",width:160,ellipsis:!0,isShow:!0},{dataIndex:"parentName",title:"\u7236\u7EA7\u5206\u7C7B",width:140,ellipsis:!0,isShow:!0},{dataIndex:"categoryLevel",title:"\u5206\u7C7B\u5C42\u7EA7",width:100,align:"center",isShow:!0},{dataIndex:"sortOrder",title:"\u6392\u5E8F\u53F7",width:80,align:"center",isShow:!0},{dataIndex:"status",title:"\u72B6\u6001",width:80,align:"center",isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}],i=async()=>{f.value.reload()},A=()=>{c.searchText="",c.status=void 0,c.categoryLevel=void 0,c.parentId=void 0,y.value.currentSelectKeys=[],o.value=null,i()},N=()=>{E.value=!E.value},P=(a,g)=>{if(a.length>0){const w=g.selectedNodes[0];o.value=w,c.parentId=a[0],i()}else o.value=null,c.parentId=void 0,i()},D=a=>{a&&(o.value=a,c.parentId=a.categoryId),m()},M=a=>{b(a)},B=a=>{i()},m=()=>{T.value={parentId:c.parentId,categoryLevel:o.value?o.value.categoryLevel+1:1},e.value=!0},b=a=>{T.value={...a},e.value=!0},h=async a=>{j.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u5206\u7C7B\u5417?",icon:n(q),maskClosable:!0,onOk:async()=>{var w;const g=await H.delete({categoryId:a.categoryId});K.success(g.message),i(),(w=y.value)==null||w.reload()}})},I=({key:a})=>{a=="1"&&L()},L=()=>{if(f.value.selectedRowList&&f.value.selectedRowList.length==0)return K.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u5206\u7C7B");j.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u5206\u7C7B\u5417?",icon:n(q),maskClosable:!0,onOk:async()=>{var g;const a=await H.batchDelete({categoryIds:f.value.selectedRowList});K.success(a.message),i(),(g=y.value)==null||g.reload()}})},F=()=>{var a;i(),(a=y.value)==null||a.reload()};return oe(()=>{}),{tableRef:f,categoryTreeRef:y,advanced:E,where:c,columns:C,currentRecord:T,currentCategoryInfo:o,showEdit:e,labelCol:O,wrapperCol:_,spanCol:v,reload:i,clear:A,toggleAdvanced:N,onTreeSelect:P,onAddCategory:D,onEditCategory:M,onDeleteCategory:B,openAddModal:m,openEditModal:b,deleteRecord:h,moreClick:I,batchDelete:L,handleFormOk:F}}},ke={class:"guns-layout"},xe={class:"guns-layout-sidebar width-100 p-t-12"},be={class:"sidebar-content"},he={class:"guns-layout-content"},Ie={class:"guns-layout"},Ee={class:"guns-layout-content-application"},Te={class:"content-main"},Le={class:"content-main-header"},Se={class:"header-content"},Re={class:"header-content-left"},Oe={key:0,class:"current-category-info"},Ae={class:"header-content-right"},Ne={class:"content-main-body"},Pe={class:"table-content"},De={key:0,class:"super-search",style:{"margin-top":"8px"}};function Me(E,e,T,o,f,y){const O=S("product-category-tree"),_=ne,v=le,c=S("plus-outlined"),C=ae,i=se,A=re,N=de,P=S("small-dash-outlined"),D=ie,M=_e,B=ce,m=ue,b=me,h=pe,I=ge,L=fe,F=ye,a=W,g=Q,w=S("addForm");return s(),x("div",ke,[n(g,{width:"292px",cacheKey:"ERP_PRODUCT_CATEGORY_MANAGEMENT"},{content:t(()=>[d("div",he,[d("div",Ie,[d("div",Ee,[d("div",Te,[d("div",Le,[d("div",Se,[d("div",Re,[n(v,{size:16},{default:t(()=>[o.currentCategoryInfo?(s(),x("span",Oe,[e[6]||(e[6]=l(" \u5F53\u524D\u5206\u7C7B\uFF1A")),n(_,{color:"blue"},{default:t(()=>[l(R(o.currentCategoryInfo.categoryName),1)]),_:1})])):U("",!0)]),_:1})]),d("div",Ae,[n(v,{size:16},{default:t(()=>[n(C,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=r=>o.openAddModal())},{default:t(()=>[n(c),e[7]||(e[7]=l(" \u65B0\u589E\u5B50\u5206\u7C7B "))]),_:1,__:[7]}),n(D,null,{overlay:t(()=>[n(N,{onClick:o.moreClick},{default:t(()=>[n(A,{key:"1"},{default:t(()=>[n(i,{iconClass:"icon-opt-shanchu",color:"#60666b"}),e[8]||(e[8]=d("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[8]})]),_:1},8,["onClick"])]),default:t(()=>[n(C,{class:"border-radius"},{default:t(()=>[e[9]||(e[9]=l(" \u66F4\u591A ")),n(P)]),_:1,__:[9]})]),_:1})]),_:1})])])]),d("div",Ne,[d("div",Pe,[n(a,{columns:o.columns,where:o.where,fieldBusinessCode:"ERP_PRODUCT_CATEGORY_TABLE",showTableTool:"",showToolTotal:!1,rowId:"categoryId",ref:"tableRef",url:"/erp/productCategory/page"},{toolLeft:t(()=>[n(M,{value:o.where.searchText,"onUpdate:value":e[1]||(e[1]=r=>o.where.searchText=r),placeholder:"\u5206\u7C7B\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",allowClear:"",onPressEnter:o.reload,style:{width:"240px"},class:"search-input",bordered:!1},{prefix:t(()=>[n(i,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"]),n(B,{type:"vertical",class:"divider"}),d("a",{onClick:e[2]||(e[2]=(...r)=>o.toggleAdvanced&&o.toggleAdvanced(...r))},R(o.advanced?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:t(()=>[o.advanced?(s(),x("div",De,[n(F,{model:o.where,labelCol:o.labelCol,"wrapper-col":o.wrapperCol},{default:t(()=>[n(L,{gutter:16},{default:t(()=>[n(I,V(G(o.spanCol)),{default:t(()=>[n(h,{label:"\u72B6\u6001:"},{default:t(()=>[n(b,{value:o.where.status,"onUpdate:value":e[3]||(e[3]=r=>o.where.status=r),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",style:{width:"100%"},allowClear:""},{default:t(()=>[n(m,{value:"Y"},{default:t(()=>e[10]||(e[10]=[l("\u542F\u7528")])),_:1,__:[10]}),n(m,{value:"N"},{default:t(()=>e[11]||(e[11]=[l("\u505C\u7528")])),_:1,__:[11]})]),_:1},8,["value"])]),_:1})]),_:1},16),n(I,V(G(o.spanCol)),{default:t(()=>[n(h,{label:"\u5206\u7C7B\u5C42\u7EA7:"},{default:t(()=>[n(b,{value:o.where.categoryLevel,"onUpdate:value":e[4]||(e[4]=r=>o.where.categoryLevel=r),placeholder:"\u8BF7\u9009\u62E9\u5C42\u7EA7",style:{width:"100%"},allowClear:""},{default:t(()=>[n(m,{value:1},{default:t(()=>e[12]||(e[12]=[l("\u4E00\u7EA7\u5206\u7C7B")])),_:1,__:[12]}),n(m,{value:2},{default:t(()=>e[13]||(e[13]=[l("\u4E8C\u7EA7\u5206\u7C7B")])),_:1,__:[13]}),n(m,{value:3},{default:t(()=>e[14]||(e[14]=[l("\u4E09\u7EA7\u5206\u7C7B")])),_:1,__:[14]}),n(m,{value:4},{default:t(()=>e[15]||(e[15]=[l("\u56DB\u7EA7\u5206\u7C7B")])),_:1,__:[15]}),n(m,{value:5},{default:t(()=>e[16]||(e[16]=[l("\u4E94\u7EA7\u5206\u7C7B")])),_:1,__:[16]})]),_:1},8,["value"])]),_:1})]),_:1},16),n(I,V(G(o.spanCol)),{default:t(()=>[n(h,{label:" ",class:"not-label"},{default:t(()=>[n(v,{size:16},{default:t(()=>[n(C,{class:"border-radius",onClick:o.reload,type:"primary"},{default:t(()=>e[17]||(e[17]=[l("\u67E5\u8BE2")])),_:1,__:[17]},8,["onClick"]),n(C,{class:"border-radius",onClick:o.clear},{default:t(()=>e[18]||(e[18]=[l("\u91CD\u7F6E")])),_:1,__:[18]},8,["onClick"])]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])])):U("",!0)]),bodyCell:t(({column:r,record:u})=>[r.dataIndex==="categoryLevel"?(s(),x(Y,{key:0},[u.categoryLevel===1?(s(),p(_,{key:0,color:"red"},{default:t(()=>e[19]||(e[19]=[l("\u4E00\u7EA7\u5206\u7C7B")])),_:1,__:[19]})):u.categoryLevel===2?(s(),p(_,{key:1,color:"orange"},{default:t(()=>e[20]||(e[20]=[l("\u4E8C\u7EA7\u5206\u7C7B")])),_:1,__:[20]})):u.categoryLevel===3?(s(),p(_,{key:2,color:"yellow"},{default:t(()=>e[21]||(e[21]=[l("\u4E09\u7EA7\u5206\u7C7B")])),_:1,__:[21]})):u.categoryLevel===4?(s(),p(_,{key:3,color:"green"},{default:t(()=>e[22]||(e[22]=[l("\u56DB\u7EA7\u5206\u7C7B")])),_:1,__:[22]})):u.categoryLevel===5?(s(),p(_,{key:4,color:"blue"},{default:t(()=>e[23]||(e[23]=[l("\u4E94\u7EA7\u5206\u7C7B")])),_:1,__:[23]})):(s(),p(_,{key:5,color:"default"},{default:t(()=>e[24]||(e[24]=[l("\u672A\u77E5")])),_:1,__:[24]}))],64)):r.dataIndex==="status"?(s(),x(Y,{key:1},[u.status==="Y"?(s(),p(_,{key:0,color:"green"},{default:t(()=>e[25]||(e[25]=[l("\u542F\u7528")])),_:1,__:[25]})):u.status==="N"?(s(),p(_,{key:1,color:"red"},{default:t(()=>e[26]||(e[26]=[l("\u505C\u7528")])),_:1,__:[26]})):(s(),p(_,{key:2,color:"default"},{default:t(()=>[l(R(u.status),1)]),_:2},1024))],64)):r.dataIndex==="categoryName"?(s(),x(Y,{key:2},[l(R(u.categoryName),1)],64)):r.key==="action"?(s(),p(v,{key:3,size:16},{default:t(()=>[n(i,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:J=>o.openEditModal(u)},null,8,["onClick"]),n(i,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:J=>o.deleteRecord(u)},null,8,["onClick"])]),_:2},1024)):U("",!0)]),_:1},8,["columns","where"])])])])])])])]),default:t(()=>[d("div",xe,[d("div",be,[n(O,{ref:"categoryTreeRef",isShowEditIcon:!0,onTreeSelect:o.onTreeSelect,onAddCategory:o.onAddCategory,onEditCategory:o.onEditCategory,onDeleteCategory:o.onDeleteCategory},null,8,["onTreeSelect","onAddCategory","onEditCategory","onDeleteCategory"])])])]),_:1}),n(w,{visible:o.showEdit,"onUpdate:visible":e[5]||(e[5]=r=>o.showEdit=r),data:o.currentRecord,onOk:o.handleFormOk},null,8,["visible","data","onOk"])])}const We=X(we,[["render",Me],["__scopeId","data-v-9cbaa05f"]]);export{We as default};
