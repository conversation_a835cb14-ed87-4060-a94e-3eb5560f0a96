package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * POS商品搜索请求参数
 *
 * <AUTHOR>
 * @since 2025/08/01 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PosProductSearchRequest extends BaseRequest {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品分类ID
     */
    @ChineseDescription("商品分类ID")
    private Long categoryId;

    /**
     * 搜索关键词（商品名称、编码、条形码）
     */
    @ChineseDescription("搜索关键词")
    private String keyword;

    /**
     * 商品状态（ACTIVE-正常，INACTIVE-停用）
     */
    @ChineseDescription("商品状态")
    private String status;

    /**
     * 是否只显示有库存的商品
     */
    @ChineseDescription("是否只显示有库存的商品")
    private Boolean onlyInStock;

    /**
     * 计价类型（NORMAL-普通，WEIGHT-计重，PIECE-计件，VARIABLE-不定价）
     */
    @ChineseDescription("计价类型")
    private String pricingType;

}