package cn.stylefeng.roses.kernel.erp.modular.supplier.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpSupplierRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpSupplierResponse;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierRegionService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 供应商-区域关联控制器
 *
 * <AUTHOR>
 * @since 2025/07/22 16:30
 */
@RestController
@ApiResource(name = "供应商-区域关联")
public class ErpSupplierRegionController {

    @Resource
    private ErpSupplierRegionService erpSupplierRegionService;

    /**
     * 获取供应商关联的区域列表
     */
    @GetResource(name = "获取供应商关联的区域列表", path = "/erp/supplierRegion/getSupplierRegions")
    public ResponseData<List<ErpRegionResponse>> getSupplierRegions(
            @Validated(ErpSupplierRegionRequest.getSupplierRegions.class) ErpSupplierRegionRequest erpSupplierRegionRequest) {
        List<ErpRegionResponse> result = erpSupplierRegionService.getSupplierRegions(erpSupplierRegionRequest);
        return new SuccessResponseData<>(result);
    }

    /**
     * 更新供应商关联的区域
     */
    @PostResource(name = "更新供应商关联的区域", path = "/erp/supplierRegion/updateSupplierRegions")
    public ResponseData<Void> updateSupplierRegions(
            @RequestBody @Validated(ErpSupplierRegionRequest.updateSupplierRegions.class) ErpSupplierRegionRequest erpSupplierRegionRequest) {
        erpSupplierRegionService.updateSupplierRegions(erpSupplierRegionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 根据区域ID查询关联的供应商
     */
    @GetResource(name = "根据区域ID查询关联的供应商", path = "/erp/supplierRegion/getSuppliersByRegion")
    public ResponseData<PageResult<ErpSupplierResponse>> getSuppliersByRegion(
            @Validated(ErpSupplierRegionRequest.getSuppliersByRegion.class) ErpSupplierRegionRequest erpSupplierRegionRequest) {
        PageResult<ErpSupplierResponse> result = erpSupplierRegionService.getSuppliersByRegion(erpSupplierRegionRequest);
        return new SuccessResponseData<>(result);
    }

    /**
     * 统计区域关联的供应商数量
     */
    @GetResource(name = "统计区域关联的供应商数量", path = "/erp/supplierRegion/countSuppliersByRegion")
    public ResponseData<Long> countSuppliersByRegion(
            @Validated(ErpSupplierRegionRequest.countSuppliersByRegion.class) ErpSupplierRegionRequest erpSupplierRegionRequest) {
        Long count = erpSupplierRegionService.countSuppliersByRegion(erpSupplierRegionRequest);
        return new SuccessResponseData<>(count);
    }
}