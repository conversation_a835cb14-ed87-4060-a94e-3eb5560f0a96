package cn.stylefeng.roses.kernel.erp.modular.pos.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosSuspendedOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * POS挂单Mapper接口
 *
 * <AUTHOR>
 * @since 2025/08/01 11:30
 */
public interface PosSuspendedOrderMapper extends BaseMapper<PosSuspendedOrder> {

    /**
     * 根据收银员ID和状态查询挂单
     *
     * @param cashierId 收银员ID
     * @param status 状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 挂单列表
     */
    List<PosSuspendedOrder> findByCashierAndStatus(@Param("cashierId") Long cashierId,
                                                  @Param("status") String status,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查询即将过期的挂单
     *
     * @param beforeMinutes 过期前多少分钟
     * @param status 状态
     * @return 即将过期的挂单列表
     */
    List<PosSuspendedOrder> findExpiringSoon(@Param("beforeMinutes") Integer beforeMinutes,
                                            @Param("status") String status);

    /**
     * 查询已过期但状态未更新的挂单
     *
     * @param currentTime 当前时间
     * @param status 当前状态
     * @return 已过期的挂单列表
     */
    List<PosSuspendedOrder> findExpiredButNotUpdated(@Param("currentTime") LocalDateTime currentTime,
                                                    @Param("status") String status);

    /**
     * 统计收银员的挂单数据
     *
     * @param cashierId 收银员ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 挂单统计数据
     */
    Map<String, Object> getSuspendOrderStatsByCashier(@Param("cashierId") Long cashierId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计各状态的挂单数量
     *
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 状态统计数据
     */
    List<Map<String, Object>> getSuspendOrderStatusStats(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询挂单时长统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 挂单时长统计
     */
    List<Map<String, Object>> getSuspendOrderDurationStats(@Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询收银员挂单排行
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderBy 排序字段（count-按数量，duration-按时长）
     * @param limit 限制数量
     * @return 收银员挂单排行
     */
    List<Map<String, Object>> getCashierSuspendOrderRanking(@Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime,
                                                           @Param("orderBy") String orderBy,
                                                           @Param("limit") Integer limit);

    /**
     * 查询每日挂单趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 状态（可选）
     * @return 每日挂单数据
     */
    List<Map<String, Object>> getDailySuspendOrderTrend(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime,
                                                       @Param("status") String status);

    /**
     * 查询每小时挂单趋势
     *
     * @param date 日期
     * @param status 状态（可选）
     * @return 每小时挂单数据
     */
    List<Map<String, Object>> getHourlySuspendOrderTrend(@Param("date") LocalDateTime date,
                                                        @Param("status") String status);

    /**
     * 批量更新过期挂单状态
     *
     * @param currentTime 当前时间
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @return 更新数量
     */
    int batchUpdateExpiredStatus(@Param("currentTime") LocalDateTime currentTime,
                                @Param("oldStatus") String oldStatus,
                                @Param("newStatus") String newStatus);

    /**
     * 删除指定时间之前的挂单记录
     *
     * @param beforeTime 指定时间
     * @param status 状态（可选，只删除指定状态的记录）
     * @return 删除数量
     */
    int deleteOldSuspendOrders(@Param("beforeTime") LocalDateTime beforeTime,
                              @Param("status") String status);

    /**
     * 查询长时间未处理的挂单
     *
     * @param hours 小时数
     * @param status 状态
     * @return 长时间未处理的挂单列表
     */
    List<PosSuspendedOrder> findLongTimeSuspendOrders(@Param("hours") Integer hours,
                                                     @Param("status") String status);

    /**
     * 统计挂单恢复率
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 恢复率统计
     */
    Map<String, Object> getSuspendOrderResumeRate(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询挂单数据大小统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据大小统计
     */
    List<Map<String, Object>> getSuspendOrderDataSizeStats(@Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询异常挂单（如数据过大、格式错误等）
     *
     * @param maxDataSize 最大数据大小（字节）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常挂单列表
     */
    List<PosSuspendedOrder> findAbnormalSuspendOrders(@Param("maxDataSize") Integer maxDataSize,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

}