System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./print-legacy-bf2789b6.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./OrgApi-legacy-c15eac58.js"],(function(e,l){"use strict";var a,r,t,s,u,n,d,c,o,i,m,f,p,g,y,v,_,x,b,I;return{setters:[e=>{a=e._,r=e.s,t=e.r,s=e.k,u=e.a,n=e.f,d=e.w,c=e.d,o=e.g,i=e.h,m=e.l,f=e.u,p=e.v,g=e.B,y=e.x,v=e.al,_=e.G,x=e.H},e=>{b=e._},null,null,e=>{I=e.u},null,null,null,null,null,null],execute:function(){var l=document.createElement("style");l.textContent=".row[data-v-ec680b0a]{display:flex;flex-direction:row}\n",document.head.appendChild(l),e("default",a({__name:"sys-user-secret-key-form",props:{form:Object},setup(e,{expose:l}){const a=e,h=r({secretKeyName:[{required:!0,message:"请输入秘钥名称",type:"string",trigger:"blur"}],userIdName:[{required:!0,message:"请选择所属人",type:"string",trigger:"blur"}],secretKey:[{required:!0,message:"请输入秘钥值，经过MD5加密",type:"string",trigger:"blur"}],secretOnceFlag:[{required:!0,message:"请选择秘钥是否使用一次",type:"string",trigger:"change1"}]}),j=t({}),N=t(!1),w=t(""),K=e=>{const l=e.selectUserList[0]||{bizId:"",name:""};"userId"===w.value&&(a.form.userId=l.bizId,a.form.userIdName=l.name)},U=()=>{a.form.secretKey=I(16)};return l({}),(l,r)=>{const t=m,I=f,D=p,F=g,O=y,E=v,Y=s("vxe-switch"),k=_,q=b,M=x;return u(),n(M,{ref:"formRef",model:e.form,rules:h,layout:"vertical"},{default:d((()=>[c(k,{gutter:20},{default:d((()=>[c(D,{span:12},{default:d((()=>[c(I,{label:"秘钥名称:",name:"secretKeyName"},{default:d((()=>[c(t,{value:e.form.secretKeyName,"onUpdate:value":r[0]||(r[0]=l=>e.form.secretKeyName=l),"allow-clear":"",placeholder:"请输入秘钥名称"},null,8,["value"])])),_:1})])),_:1}),c(D,{span:12},{default:d((()=>[c(I,{label:"秘钥所属人:",name:"userIdName"},{default:d((()=>[c(t,{value:e.form.userIdName,"onUpdate:value":r[1]||(r[1]=l=>e.form.userIdName=l),placeholder:"请选择所属人",onFocus:r[2]||(r[2]=e=>(()=>{w.value="userId";const{userId:e,userIdName:l}=a.form;j.value.selectUserList=[{bizId:e,name:l}],N.value=!0})())},null,8,["value"])])),_:1})])),_:1}),c(D,{span:24},{default:d((()=>[c(I,{label:"临时秘钥值:",name:"secretKey"},{default:d((()=>[c(O,{compact:"",style:{display:"flex"}},{default:d((()=>[c(t,{value:e.form.secretKey,"onUpdate:value":r[3]||(r[3]=l=>e.form.secretKey=l),"allow-clear":"",placeholder:"请输入秘钥值，经过MD5加密"},null,8,["value"]),c(F,{type:"primary",onClick:U},{default:d((()=>r[7]||(r[7]=[o("点击生成")]))),_:1,__:[7]})])),_:1})])),_:1})])),_:1}),c(D,{span:24},{default:d((()=>[c(I,{label:"密码过期时间:",name:"secretExpirationTime"},{default:d((()=>[c(E,{value:e.form.secretExpirationTime,"onUpdate:value":r[4]||(r[4]=l=>e.form.secretExpirationTime=l),"value-format":"YYYY-MM-DD",placeholder:"请选择秘钥过期时间",style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),c(D,{span:24},{default:d((()=>[c(I,{label:"是否为一次性秘钥:",name:"secretOnceFlag",class:"row"},{default:d((()=>[c(Y,{modelValue:e.form.secretOnceFlag,"onUpdate:modelValue":r[5]||(r[5]=l=>e.form.secretOnceFlag=l),"open-value":"Y","close-value":"N",style:{"margin-top":"-3px"}},null,8,["modelValue"])])),_:1})])),_:1}),c(D,{span:24},{default:d((()=>r[8]||(r[8]=[o(" 特殊说明：秘钥生成后会加密存储到数据库，无法修改和查看，请记录好秘钥 ")]))),_:1,__:[8]})])),_:1}),N.value?(u(),n(q,{key:0,visible:N.value,"onUpdate:visible":r[6]||(r[6]=e=>N.value=e),data:j.value,showTab:["user"],changeHeight:!0,title:"人员选择",onDone:K},null,8,["visible","data"])):i("",!0)])),_:1},8,["model","rules"])}}},[["__scopeId","data-v-ec680b0a"]]))}}}));
