package cn.stylefeng.roses.kernel.impexp.user.exceptions;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 用户导入出现的异常
 *
 * <AUTHOR>
 * @since 2024-02-04 16:05
 */
@Getter
public enum UserImportExceptionEnum implements AbstractExceptionEnum {

    /**
     * 解析excel出错
     */
    PARSE_EXCEL_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "10001", "解析excel出错");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    UserImportExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
