package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.erp.api.constants.ErpConstants;
import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 商品模块异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
@Getter
public enum ErpProductExceptionEnum implements AbstractExceptionEnum {

    /**
     * 商品不存在
     */
    PRODUCT_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "21", "商品不存在"),

    /**
     * 商品编码重复
     */
    PRODUCT_CODE_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "22", "商品编码重复"),

    /**
     * 商品名称重复
     */
    PRODUCT_NAME_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "23", "商品名称重复"),

    /**
     * 条形码重复
     */
    PRODUCT_BARCODE_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "24", "条形码重复"),

    /**
     * 商品状态不正确
     */
    PRODUCT_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "25", "商品状态不正确"),

    /**
     * 商品已停用，无法操作
     */
    PRODUCT_INACTIVE_CANNOT_OPERATE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "26", "商品已停用，无法操作"),

    /**
     * 商品已停产，无法操作
     */
    PRODUCT_DISCONTINUED_CANNOT_OPERATE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "27", "商品已停产，无法操作"),

    /**
     * 商品存在关联业务数据，无法删除
     */
    PRODUCT_HAS_BUSINESS_DATA_CANNOT_DELETE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "28", "商品存在关联业务数据，无法删除"),

    /**
     * 商品存在关联业务数据，无法停用
     */
    PRODUCT_HAS_BUSINESS_DATA_CANNOT_INACTIVE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "29", "商品存在关联业务数据，无法停用"),

    /**
     * 商品分类不存在
     */
    PRODUCT_CATEGORY_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "30", "商品分类不存在"),

    /**
     * 商品重量格式不正确
     */
    PRODUCT_WEIGHT_FORMAT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "31", "商品重量格式不正确"),

    /**
     * 商品体积格式不正确
     */
    PRODUCT_VOLUME_FORMAT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "32", "商品体积格式不正确"),

    /**
     * 商品保质期格式不正确
     */
    PRODUCT_SHELF_LIFE_FORMAT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "33", "商品保质期格式不正确"),

    /**
     * 商品ID列表为空
     */
    PRODUCT_ID_LIST_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "34", "商品ID列表为空"),



    /**
     * 计价类型不正确
     */
    PRODUCT_PRICING_TYPE_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "36", "计价类型不正确"),

    /**
     * 普通商品必须设置零售价格
     */
    PRODUCT_RETAIL_PRICE_REQUIRED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "37", "普通商品必须设置零售价格"),

    /**
     * 计重商品必须设置单位价格
     */
    PRODUCT_UNIT_PRICE_REQUIRED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "38", "计重商品必须设置单位价格"),

    /**
     * 计件商品必须设置单份价格
     */
    PRODUCT_PIECE_PRICE_REQUIRED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "39", "计件商品必须设置单份价格"),

    /**
     * 参考价格格式不正确
     */
    PRODUCT_REFERENCE_PRICE_FORMAT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "40", "参考价格格式不正确"),

    /**
     * 商品分类不能为空
     */
    PRODUCT_CATEGORY_REQUIRED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "41", "商品分类不能为空"),

    /**
     * 商品分类已停用
     */
    PRODUCT_CATEGORY_DISABLED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + ErpConstants.ERP_EXCEPTION_STEP_CODE + "42", "商品分类已停用");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ErpProductExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}