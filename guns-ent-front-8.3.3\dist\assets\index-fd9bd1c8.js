import{ay as q,_ as Y,L as ee,bc as R,a as G,c as J,r as te,d as D,w as ae,a0 as ne}from"./index-18a1ea24.js";/* empty css              */var re={exports:{}};(function(b){var m=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{},y=function(g){var x=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,L=0,w={},i={manual:g.Prism&&g.Prism.manual,disableWorkerMessageHandler:g.Prism&&g.Prism.disableWorkerMessageHandler,util:{encode:function t(e){return e instanceof h?new h(e.type,t(e.content),e.alias):Array.isArray(e)?e.map(t):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(t){return Object.prototype.toString.call(t).slice(8,-1)},objId:function(t){return t.__id||Object.defineProperty(t,"__id",{value:++L}),t.__id},clone:function t(e,n){var a,r;switch(n=n||{},i.util.type(e)){case"Object":if(r=i.util.objId(e),n[r])return n[r];for(var o in a={},n[r]=a,e)e.hasOwnProperty(o)&&(a[o]=t(e[o],n));return a;case"Array":return r=i.util.objId(e),n[r]?n[r]:(a=[],n[r]=a,e.forEach(function(s,l){a[l]=t(s,n)}),a);default:return e}},getLanguage:function(t){for(;t;){var e=x.exec(t.className);if(e)return e[1].toLowerCase();t=t.parentElement}return"none"},setLanguage:function(t,e){t.className=t.className.replace(RegExp(x,"gi"),""),t.classList.add("language-"+e)},currentScript:function(){if(typeof document>"u")return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(a){var t=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(a.stack)||[])[1];if(t){var e=document.getElementsByTagName("script");for(var n in e)if(e[n].src==t)return e[n]}return null}},isActive:function(t,e,n){for(var a="no-"+e;t;){var r=t.classList;if(r.contains(e))return!0;if(r.contains(a))return!1;t=t.parentElement}return!!n}},languages:{plain:w,plaintext:w,text:w,txt:w,extend:function(t,e){var n=i.util.clone(i.languages[t]);for(var a in e)n[a]=e[a];return n},insertBefore:function(t,e,n,a){var r=(a=a||i.languages)[t],o={};for(var s in r)if(r.hasOwnProperty(s)){if(s==e)for(var l in n)n.hasOwnProperty(l)&&(o[l]=n[l]);n.hasOwnProperty(s)||(o[s]=r[s])}var c=a[t];return a[t]=o,i.languages.DFS(i.languages,function(u,$){$===c&&u!=t&&(this[u]=o)}),o},DFS:function t(e,n,a,r){r=r||{};var o=i.util.objId;for(var s in e)if(e.hasOwnProperty(s)){n.call(e,s,e[s],a||s);var l=e[s],c=i.util.type(l);c!=="Object"||r[o(l)]?c!=="Array"||r[o(l)]||(r[o(l)]=!0,t(l,n,s,r)):(r[o(l)]=!0,t(l,n,null,r))}}},plugins:{},highlightAll:function(t,e){i.highlightAllUnder(document,t,e)},highlightAllUnder:function(t,e,n){var a={callback:n,container:t,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};i.hooks.run("before-highlightall",a),a.elements=Array.prototype.slice.apply(a.container.querySelectorAll(a.selector)),i.hooks.run("before-all-elements-highlight",a);for(var r,o=0;r=a.elements[o++];)i.highlightElement(r,e===!0,a.callback)},highlightElement:function(t,e,n){var a=i.util.getLanguage(t),r=i.languages[a];i.util.setLanguage(t,a);var o=t.parentElement;o&&o.nodeName.toLowerCase()==="pre"&&i.util.setLanguage(o,a);var s={element:t,language:a,grammar:r,code:t.textContent};function l(u){s.highlightedCode=u,i.hooks.run("before-insert",s),s.element.innerHTML=s.highlightedCode,i.hooks.run("after-highlight",s),i.hooks.run("complete",s),n&&n.call(s.element)}if(i.hooks.run("before-sanity-check",s),(o=s.element.parentElement)&&o.nodeName.toLowerCase()==="pre"&&!o.hasAttribute("tabindex")&&o.setAttribute("tabindex","0"),!s.code)return i.hooks.run("complete",s),void(n&&n.call(s.element));if(i.hooks.run("before-highlight",s),s.grammar)if(e&&g.Worker){var c=new Worker(i.filename);c.onmessage=function(u){l(u.data)},c.postMessage(JSON.stringify({language:s.language,code:s.code,immediateClose:!0}))}else l(i.highlight(s.code,s.grammar,s.language));else l(i.util.encode(s.code))},highlight:function(t,e,n){var a={code:t,grammar:e,language:n};if(i.hooks.run("before-tokenize",a),!a.grammar)throw new Error('The language "'+a.language+'" has no grammar.');return a.tokens=i.tokenize(a.code,a.grammar),i.hooks.run("after-tokenize",a),h.stringify(i.util.encode(a.tokens),a.language)},tokenize:function(t,e){var n=e.rest;if(n){for(var a in n)e[a]=n[a];delete e.rest}var r=new U;return A(r,r.head,t),T(t,r,e,r.head,0),function(o){for(var s=[],l=o.head.next;l!==o.tail;)s.push(l.value),l=l.next;return s}(r)},hooks:{all:{},add:function(t,e){var n=i.hooks.all;n[t]=n[t]||[],n[t].push(e)},run:function(t,e){var n=i.hooks.all[t];if(n&&n.length)for(var a,r=0;a=n[r++];)a(e)}},Token:h};function h(t,e,n,a){this.type=t,this.content=e,this.alias=n,this.length=0|(a||"").length}function M(t,e,n,a){t.lastIndex=e;var r=t.exec(n);if(r&&a&&r[1]){var o=r[1].length;r.index+=o,r[0]=r[0].slice(o)}return r}function T(t,e,n,a,r,o){for(var s in n)if(n.hasOwnProperty(s)&&n[s]){var l=n[s];l=Array.isArray(l)?l:[l];for(var c=0;c<l.length;++c){if(o&&o.cause==s+","+c)return;var u=l[c],$=u.inside,N=!!u.lookbehind,H=!!u.greedy,X=u.alias;if(H&&!u.pattern.global){var K=u.pattern.toString().match(/[imsuy]*$/)[0];u.pattern=RegExp(u.pattern.source,K+"g")}for(var I=u.pattern||u,d=a.next,p=r;d!==e.tail&&!(o&&p>=o.reach);p+=d.value.length,d=d.next){var F=d.value;if(e.length>t.length)return;if(!(F instanceof h)){var f,P=1;if(H){if(!(f=M(I,p,t,N))||f.index>=t.length)break;var S=f.index,Q=f.index+f[0].length,v=p;for(v+=d.value.length;S>=v;)v+=(d=d.next).value.length;if(p=v-=d.value.length,d.value instanceof h)continue;for(var _=d;_!==e.tail&&(v<Q||typeof _.value=="string");_=_.next)P++,v+=_.value.length;P--,F=t.slice(p,v),f.index-=p}else if(!(f=M(I,0,F,N)))continue;S=f.index;var j=f[0],z=F.slice(0,S),W=F.slice(S+j.length),B=p+F.length;o&&B>o.reach&&(o.reach=B);var E=d.prev;if(z&&(E=A(e,E,z),p+=z.length),V(e,E,P),d=A(e,E,new h(s,$?i.tokenize(j,$):j,X,j)),W&&A(e,d,W),P>1){var C={cause:s+","+c,reach:B};T(t,e,n,d.prev,p,C),o&&C.reach>o.reach&&(o.reach=C.reach)}}}}}}function U(){var t={value:null,prev:null,next:null},e={value:null,prev:t,next:null};t.next=e,this.head=t,this.tail=e,this.length=0}function A(t,e,n){var a=e.next,r={value:n,prev:e,next:a};return e.next=r,a.prev=r,t.length++,r}function V(t,e,n){for(var a=e.next,r=0;r<n&&a!==t.tail;r++)a=a.next;e.next=a,a.prev=e,t.length-=r}if(g.Prism=i,h.stringify=function t(e,n){if(typeof e=="string")return e;if(Array.isArray(e)){var a="";return e.forEach(function(c){a+=t(c,n)}),a}var r={type:e.type,content:t(e.content,n),tag:"span",classes:["token",e.type],attributes:{},language:n},o=e.alias;o&&(Array.isArray(o)?Array.prototype.push.apply(r.classes,o):r.classes.push(o)),i.hooks.run("wrap",r);var s="";for(var l in r.attributes)s+=" "+l+'="'+(r.attributes[l]||"").replace(/"/g,"&quot;")+'"';return"<"+r.tag+' class="'+r.classes.join(" ")+'"'+s+">"+r.content+"</"+r.tag+">"},!g.document)return g.addEventListener&&(i.disableWorkerMessageHandler||g.addEventListener("message",function(t){var e=JSON.parse(t.data),n=e.language,a=e.code,r=e.immediateClose;g.postMessage(i.highlight(a,i.languages[n],n)),r&&g.close()},!1)),i;var k=i.util.currentScript();function O(){i.manual||i.highlightAll()}if(k&&(i.filename=k.src,k.hasAttribute("data-manual")&&(i.manual=!0)),!i.manual){var Z=document.readyState;Z==="loading"||Z==="interactive"&&k&&k.defer?document.addEventListener("DOMContentLoaded",O):window.requestAnimationFrame?window.requestAnimationFrame(O):window.setTimeout(O,16)}return i}(m);b.exports&&(b.exports=y),typeof q<"u"&&(q.Prism=y)})(re);Prism.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/};Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp("(^|[^\\w$])(?:NaN|Infinity|0[bB][01]+(?:_[01]+)*n?|0[oO][0-7]+(?:_[0-7]+)*n?|0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?|\\d+(?:_\\d+)*n|(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?)(?![\\w$])"),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp("((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/(?:(?:\\[(?:[^\\]\\\\\r\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\r\n])+/[dgimyus]{0,7}|(?:\\[(?:[^[\\]\\\\\r\n]|\\\\.|\\[(?:[^[\\]\\\\\r\n]|\\\\.|\\[(?:[^[\\]\\\\\r\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\r\n])+/[dgimyus]{0,7}v[dgimyus]{0,7})(?=(?:\\s|/\\*(?:[^*]|\\*(?!/))*\\*/)*(?:$|[\r\n,.;:})\\]]|//))"),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute("on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)","javascript")),Prism.languages.js=Prism.languages.javascript;const ie=["innerHTML"],se={__name:"index",props:{code:{type:String,default:""},language:{type:String,default:"javascript"}},setup(b){const m=b,y=ee(()=>R.highlight(m.code,R.languages[m.language],m.language));return(g,x)=>(G(),J("div",{innerHTML:y.value,class:"code-highlight"},null,8,ie))}},oe=Y(se,[["__scopeId","data-v-3822345f"]]),le={class:"guns-body guns-body-card"},de={__name:"index",setup(b){const m=te('{"dimensions":["product","data1","data2"],"source":[{"product":"Mon","data1":120,"data2":130},{"product":"Tue","data1":200,"data2":130},{"product":"Wed","data1":150,"data2":312},{"product":"Thu","data1":80,"data2":268},{"product":"Fri","data1":70,"data2":155},{"product":"Sat","data1":110,"data2":117},{"product":"Sun","data1":130,"data2":160}]}');return(y,g)=>{const x=oe,L=ne;return G(),J("div",le,[D(L,{title:"\u4EE3\u7801\u9884\u89C8",bordered:!1},{default:ae(()=>[D(x,{code:m.value,height:"480px",style:{width:"400px"}},null,8,["code"])]),_:1})])}}};export{de as default};
