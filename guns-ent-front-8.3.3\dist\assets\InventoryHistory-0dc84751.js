import{_ as J,r as H,s as L,X as W,m as V,a as s,f as F,w as r,b as R,d as l,t as i,g as _,a2 as D,c as u,F as h,e as G,h as k,Y as j,Z as X,a0 as Z,W as K,J as $,u as ee,a6 as te,l as ae,B as ne,n as oe,H as re,U as ie,a7 as se,i as le,M as ce}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{I as S}from"./InventoryHistoryApi-416494e3.js";const de={name:"InventoryHistory",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(v,{emit:y}){const o=H(!1),a=H([]),x=H([]),g=L({productId:null,operationType:void 0,operatorName:"",startTime:void 0,endTime:void 0}),d=L({current:1,pageSize:20,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"\u5171 ".concat(e," \u6761\u8BB0\u5F55")}),I=S.getOperationTypeOptions(),N=[{title:"\u64CD\u4F5C\u7C7B\u578B",key:"operationType",width:100,fixed:"left"},{title:"\u6570\u91CF\u53D8\u5316",key:"quantity",width:120,align:"right"},{title:"\u64CD\u4F5C\u524D\u5E93\u5B58",key:"beforeStock",width:120,align:"right"},{title:"\u64CD\u4F5C\u540E\u5E93\u5B58",key:"afterStock",width:120,align:"right"},{title:"\u5173\u8054\u5355\u636E",key:"referenceType",width:120},{title:"\u64CD\u4F5C\u65F6\u95F4",key:"operationTime",width:160},{title:"\u64CD\u4F5C\u4EBA",key:"operatorName",width:100},{title:"\u5907\u6CE8",key:"remark",width:200,ellipsis:!0}];W(()=>v.visible,e=>{e&&v.data.productId&&(g.productId=v.data.productId,b(),C())});const C=async()=>{o.value=!0;try{const e={...g,pageNo:d.current,pageSize:d.pageSize},t=await S.findPage(e);if(t&&t.success!==!1){let p=[],f=0;t.rows?(p=t.rows,f=t.totalRows||0):t.data&&t.data.rows?(p=t.data.rows,f=t.data.totalRows||0):t.data&&t.data.records?(p=t.data.records,f=t.data.total||0):Array.isArray(t.data)?(p=t.data,f=p.length):Array.isArray(t)&&(p=t,f=p.length),a.value=p.map(m=>({...m,quantity:m.quantityChange,beforeStock:m.stockBefore,afterStock:m.stockAfter,operatorName:m.operationUserName||m.operatorName||(m.operationUser?"\u64CD\u4F5C\u5458".concat(m.operationUser):"\u7CFB\u7EDF\u64CD\u4F5C"),operationTime:E(m.operationTime)})),d.total=f,t.pageNo?(d.current=t.pageNo,d.pageSize=t.pageSize):t.data&&t.data.pageNo&&(d.current=t.data.pageNo,d.pageSize=t.data.pageSize)}else a.value=[],d.total=0}catch(e){V.error("\u52A0\u8F7D\u5E93\u5B58\u5386\u53F2\u5931\u8D25\uFF1A"+(e.message||"\u672A\u77E5\u9519\u8BEF")),a.value=[],d.total=0}finally{o.value=!1}},O=e=>{d.current=e.current,d.pageSize=e.pageSize,C()},T=e=>{e&&e.length===2?(g.startTime=e[0],g.endTime=e[1]):(g.startTime=void 0,g.endTime=void 0),C()},b=()=>{g.operationType=void 0,g.operatorName="",g.startTime=void 0,g.endTime=void 0,x.value=[],d.current=1},U=()=>{try{S.exportHistory({...g,productId:v.data.productId}),V.success("\u5BFC\u51FA\u6210\u529F")}catch(e){V.error("\u5BFC\u51FA\u5931\u8D25\uFF1A"+(e.message||"\u672A\u77E5\u9519\u8BEF"))}},w=(e,t)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return t||"\u4E2A"}},A=(e,t)=>{if(!e)return"0";const p=t==="WEIGHT"?3:0;return parseFloat(e).toFixed(p)},z=e=>e?parseFloat(e).toFixed(2):"0.00",E=e=>{if(!e)return"-";const t=typeof e=="string"?new Date(e):e;if(isNaN(t.getTime()))return e;const p=t.getFullYear(),f=String(t.getMonth()+1).padStart(2,"0"),m=String(t.getDate()).padStart(2,"0"),Q=String(t.getHours()).padStart(2,"0"),P=String(t.getMinutes()).padStart(2,"0"),Y=String(t.getSeconds()).padStart(2,"0");return"".concat(p,"-").concat(f,"-").concat(m," ").concat(Q,":").concat(P,":").concat(Y)};return{loading:o,historyList:a,dateRange:x,searchForm:g,pagination:d,operationTypeOptions:I,columns:N,loadHistory:C,handleTableChange:O,onDateRangeChange:T,resetSearch:b,exportHistory:U,getStockUnit:w,formatStock:A,formatAmount:z,getStockClass:(e,t)=>{const p=parseFloat(e)||0,f=parseFloat(t)||0;return p<=0?"stock-danger":p<=f?"stock-warning":"stock-normal"},getOperationTypeName:e=>S.getOperationTypeName(e),getOperationTypeColor:e=>S.getOperationTypeColor(e),formatQuantityChange:(e,t)=>S.formatQuantityChange(e,t),getQuantityChangeClass:(e,t)=>{const p=parseFloat(e)||0;switch(t){case"IN":return"quantity-increase";case"OUT":case"SALE":return"quantity-decrease";case"ADJUST":return p>=0?"quantity-increase":"quantity-decrease";case"SET_ALERT":return"";default:return""}},formatValueChange:e=>{const t=parseFloat(e)||0;return t>=0?"+\xA5".concat(t.toFixed(2)):"-\xA5".concat(Math.abs(t).toFixed(2))},getValueChangeClass:e=>(parseFloat(e)||0)>=0?"value-increase":"value-decrease",getReferenceTypeName:e=>({PURCHASE_ORDER:"\u91C7\u8D2D\u5165\u5E93\u5355",SALE_ORDER:"\u9500\u552E\u5355",ADJUST_ORDER:"\u8C03\u6574\u5355",MANUAL:"\u624B\u52A8\u64CD\u4F5C"})[e]||e,handleCancel:()=>{y("update:visible",!1)}}}},ue={class:"inventory-history-content"},pe={class:"product-name"},ye={class:"record-count"},ge={key:0},_e={key:1},me={key:1},fe={key:0},ke={key:1},he={key:2},Ce={key:1};function Te(v,y,o,a,x,g){const d=j,I=X,N=Z,C=K,O=$,T=ee,b=te,U=ae,w=ne,A=oe,z=re,E=ie,M=se,q=le,B=ce;return s(),F(B,{visible:o.visible,title:"\u5E93\u5B58\u53D8\u52A8\u5386\u53F2",width:1200,footer:null,onCancel:a.handleCancel},{default:r(()=>[R("div",ue,[l(N,{title:"\u5546\u54C1\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:r(()=>[l(I,{column:4,bordered:"",size:"small"},{default:r(()=>[l(d,{label:"\u5546\u54C1\u540D\u79F0"},{default:r(()=>[R("span",pe,i(o.data.productName),1)]),_:1}),l(d,{label:"\u5546\u54C1\u7F16\u7801"},{default:r(()=>[_(i(o.data.productCode),1)]),_:1}),l(d,{label:"\u5F53\u524D\u5E93\u5B58"},{default:r(()=>[R("span",{class:D(a.getStockClass(o.data.currentStock,o.data.minStock))},i(a.formatStock(o.data.currentStock,o.data.pricingType))+" "+i(a.getStockUnit(o.data.pricingType,o.data.unit)),3)]),_:1}),l(d,{label:"\u9884\u8B66\u503C"},{default:r(()=>[_(i(a.formatStock(o.data.minStock,o.data.pricingType))+" "+i(a.getStockUnit(o.data.pricingType,o.data.unit)),1)]),_:1})]),_:1})]),_:1}),l(N,{title:"\u7B5B\u9009\u6761\u4EF6",size:"small",style:{"margin-bottom":"16px"}},{default:r(()=>[l(z,{layout:"inline",model:a.searchForm},{default:r(()=>[l(T,{label:"\u64CD\u4F5C\u7C7B\u578B"},{default:r(()=>[l(O,{value:a.searchForm.operationType,"onUpdate:value":y[0]||(y[0]=c=>a.searchForm.operationType=c),placeholder:"\u8BF7\u9009\u62E9\u64CD\u4F5C\u7C7B\u578B",allowClear:"",style:{width:"120px"},onChange:a.loadHistory},{default:r(()=>[(s(!0),u(h,null,G(a.operationTypeOptions,c=>(s(),F(C,{key:c.value,value:c.value},{default:r(()=>[_(i(c.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange"])]),_:1}),l(T,{label:"\u64CD\u4F5C\u65F6\u95F4"},{default:r(()=>[l(b,{value:a.dateRange,"onUpdate:value":y[1]||(y[1]=c=>a.dateRange=c),style:{width:"240px"},placeholder:["\u5F00\u59CB\u65F6\u95F4","\u7ED3\u675F\u65F6\u95F4"],format:"YYYY-MM-DD",onChange:a.onDateRangeChange},null,8,["value","onChange"])]),_:1}),l(T,{label:"\u64CD\u4F5C\u4EBA"},{default:r(()=>[l(U,{value:a.searchForm.operatorName,"onUpdate:value":y[2]||(y[2]=c=>a.searchForm.operatorName=c),placeholder:"\u8BF7\u8F93\u5165\u64CD\u4F5C\u4EBA",allowClear:"",style:{width:"120px"},onPressEnter:a.loadHistory},null,8,["value","onPressEnter"])]),_:1}),l(T,null,{default:r(()=>[l(A,null,{default:r(()=>[l(w,{type:"primary",onClick:a.loadHistory},{default:r(()=>y[3]||(y[3]=[_("\u641C\u7D22")])),_:1,__:[3]},8,["onClick"]),l(w,{onClick:a.resetSearch},{default:r(()=>y[4]||(y[4]=[_("\u91CD\u7F6E")])),_:1,__:[4]},8,["onClick"]),l(w,{onClick:a.exportHistory},{default:r(()=>y[5]||(y[5]=[_("\u5BFC\u51FA")])),_:1,__:[5]},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(N,{title:"\u53D8\u52A8\u8BB0\u5F55",size:"small"},{extra:r(()=>[l(A,null,{default:r(()=>[R("span",ye,"\u5171 "+i(a.pagination.total)+" \u6761\u8BB0\u5F55",1)]),_:1})]),default:r(()=>[l(q,{columns:a.columns,"data-source":a.historyList,pagination:a.pagination,loading:a.loading,size:"small",bordered:"","row-key":"id",onChange:a.handleTableChange},{bodyCell:r(({column:c,record:n})=>[c.key==="operationType"?(s(),F(E,{key:0,color:a.getOperationTypeColor(n.operationType)},{default:r(()=>[_(i(a.getOperationTypeName(n.operationType)),1)]),_:2},1032,["color"])):k("",!0),c.key==="quantity"?(s(),u("span",{key:1,class:D(a.getQuantityChangeClass(n.quantityChange,n.operationType))},i(a.formatQuantityChange(n.quantityChange,n.operationType))+" "+i(a.getStockUnit(o.data.pricingType,o.data.unit)),3)):k("",!0),c.key==="beforeStock"?(s(),u(h,{key:2},[_(i(a.formatStock(n.stockBefore,o.data.pricingType))+" "+i(a.getStockUnit(o.data.pricingType,o.data.unit)),1)],64)):k("",!0),c.key==="afterStock"?(s(),u(h,{key:3},[_(i(a.formatStock(n.stockAfter,o.data.pricingType))+" "+i(a.getStockUnit(o.data.pricingType,o.data.unit)),1)],64)):k("",!0),c.key==="unitCost"?(s(),u(h,{key:4},[n.unitCost?(s(),u("span",ge,"\xA5"+i(a.formatAmount(n.unitCost)),1)):(s(),u("span",_e,"-"))],64)):k("",!0),c.key==="totalValueChange"?(s(),u(h,{key:5},[n.totalValueChange?(s(),u("span",{key:0,class:D(a.getValueChangeClass(n.totalValueChange))},i(a.formatValueChange(n.totalValueChange)),3)):(s(),u("span",me,"-"))],64)):k("",!0),c.key==="referenceType"?(s(),u(h,{key:6},[n.referenceTypeName?(s(),u("span",fe,i(n.referenceTypeName),1)):n.referenceType?(s(),u("span",ke,i(a.getReferenceTypeName(n.referenceType)),1)):(s(),u("span",he,"-"))],64)):k("",!0),c.key==="operationTime"?(s(),u(h,{key:7},[_(i(n.operationTime),1)],64)):k("",!0),c.key==="operatorName"?(s(),u(h,{key:8},[_(i(n.operationUserName||n.operatorName||"-"),1)],64)):k("",!0),c.key==="remark"?(s(),u(h,{key:9},[n.remark&&n.remark.length>20?(s(),F(M,{key:0,title:n.remark},{default:r(()=>[_(i(n.remark.substring(0,20))+"... ",1)]),_:2},1032,["title"])):(s(),u("span",Ce,i(n.remark||"-"),1))],64)):k("",!0)]),_:1},8,["columns","data-source","pagination","loading","onChange"])]),_:1})])]),_:1},8,["visible","onCancel"])}const Ee=J(de,[["render",Te],["__scopeId","data-v-49676f49"]]);export{Ee as default};
