package cn.stylefeng.roses.kernel.pay.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品信息封装类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsRequest extends BaseRequest {

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空", groups = {detail.class})
    @ChineseDescription("商品id")
    private Long goodsId;

}