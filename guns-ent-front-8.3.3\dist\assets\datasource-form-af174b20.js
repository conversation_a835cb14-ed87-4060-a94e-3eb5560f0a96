import{_ as C,s as w,r as B,a as d,f as i,w as t,d as e,g as u,c as k,F as L,e as N,t as f,l as O,u as S,v as J,W as R,J as x,U as F,a7 as T,G as E,H as V}from"./index-18a1ea24.js";/* empty css              */const A={__name:"datasource-form",props:{form:Object},setup(r){const _=r,p=w({dbName:[{required:!0,message:"\u8BF7\u8F93\u5165\u6570\u636E\u6E90\u540D\u79F0",type:"string",trigger:"blur"}],jdbcDriver:[{required:!0,message:"\u8BF7\u9009\u62E9\u6570\u636E\u6E90\u9A71\u52A8",type:"string",trigger:"blur"}],username:[{required:!0,message:"\u8BF7\u8F93\u5165jdbc\u8D26\u53F7",type:"string",trigger:"blur"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165jdbc\u5BC6\u7801",type:"string",trigger:"blur"}],jdbcUrl:[{required:!0,message:"\u8BF7\u8F93\u5165url",type:"string",trigger:"blur"}]}),v=B([{title:"******************************************************************************************************************************************************************************************************",name:"mysql"},{title:"****************************************",name:"oracle"},{title:"******************************************************",name:"sql server"},{title:"*************************************",name:"postgre sql"}]),g=c=>{_.form.jdbcUrl=c};return(c,l)=>{const s=O,o=S,n=J,m=R,b=x,j=F,D=T,U=E,q=V;return d(),i(q,{ref:"formRef",model:r.form,rules:p,layout:"vertical"},{default:t(()=>[e(U,{gutter:20},{default:t(()=>[e(n,{span:24},{default:t(()=>[e(o,{label:"\u6570\u636E\u5E93\u540D\u79F0:",name:"dbName"},{default:t(()=>[e(s,{value:r.form.dbName,"onUpdate:value":l[0]||(l[0]=a=>r.form.dbName=a),placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u5E93\u540D\u79F0","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(n,{span:24},{default:t(()=>[e(o,{label:"JDBC\u9A71\u52A8:",name:"jdbcDriver"},{default:t(()=>[e(b,{value:r.form.jdbcDriver,"onUpdate:value":l[1]||(l[1]=a=>r.form.jdbcDriver=a),defaultActiveFirstOption:""},{default:t(()=>[e(m,{value:"com.mysql.cj.jdbc.Driver"},{default:t(()=>l[6]||(l[6]=[u("Mysql")])),_:1,__:[6]}),e(m,{value:"oracle.jdbc.OracleDriver"},{default:t(()=>l[7]||(l[7]=[u("Oracle")])),_:1,__:[7]}),e(m,{value:"net.sourceforge.jtds.jdbc.Driver"},{default:t(()=>l[8]||(l[8]=[u("Sql Server")])),_:1,__:[8]}),e(m,{value:"org.postgresql.Driver"},{default:t(()=>l[9]||(l[9]=[u("Postgre SQL")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1}),e(n,{span:24},{default:t(()=>[e(o,{label:"JDBC\u8D26\u53F7:",name:"username"},{default:t(()=>[e(s,{value:r.form.username,"onUpdate:value":l[2]||(l[2]=a=>r.form.username=a),placeholder:"\u8BF7\u8F93\u5165JDBC\u8D26\u53F7","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(n,{span:24},{default:t(()=>[e(o,{label:"JDBC\u5BC6\u7801:",name:"password"},{default:t(()=>[e(s,{value:r.form.password,"onUpdate:value":l[3]||(l[3]=a=>r.form.password=a),placeholder:"\u8BF7\u8F93\u5165JDBC\u5BC6\u7801","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(n,{span:24},{default:t(()=>[e(o,{label:"JDBC URL:",name:"jdbcUrl"},{default:t(()=>[e(s,{value:r.form.jdbcUrl,"onUpdate:value":l[4]||(l[4]=a=>r.form.jdbcUrl=a),placeholder:"\u8BF7\u8F93\u5165URL","allow-clear":"",autocomplete:"off"},null,8,["value"]),(d(!0),k(L,null,N(v.value,(a,y)=>(d(),i(D,{key:y+"url"},{title:t(()=>[u(f(a.title),1)]),default:t(()=>[e(j,{class:"urltag",onClick:I=>g(a.title)},{default:t(()=>[u(f(a.name),1)]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:1}),e(n,{span:24},{default:t(()=>[e(o,{label:"\u6570\u636E\u6E90\u5907\u6CE8:",name:"remarks"},{default:t(()=>[e(s,{value:r.form.remarks,"onUpdate:value":l[5]||(l[5]=a=>r.form.remarks=a),placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u6E90\u5907\u6CE8","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}},z=C(A,[["__scopeId","data-v-03841e2b"]]);export{z as default};
