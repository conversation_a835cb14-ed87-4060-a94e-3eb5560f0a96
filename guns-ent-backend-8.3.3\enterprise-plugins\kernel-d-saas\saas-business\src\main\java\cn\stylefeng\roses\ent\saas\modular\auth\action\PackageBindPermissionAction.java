package cn.stylefeng.roses.ent.saas.modular.auth.action;


import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.kernel.sys.api.enums.PermissionNodeTypeEnum;

/**
 * 功能包绑定权限的操作
 *
 * <AUTHOR>
 * @since 2024/1/22 0:19
 */
public interface PackageBindPermissionAction {

    /**
     * 获取操作的类型，有4种节点类型
     *
     * <AUTHOR>
     * @since 2024/1/22 0:19
     */
    PermissionNodeTypeEnum getPackageBindPermissionNodeType();

    /**
     * 进行功能包绑定权限限制的过程，执行绑定的操作
     *
     * <AUTHOR>
     * @since 2024/1/22 0:20
     */
    void doPackageBindPermissionAction(PackageBindPermissionRequest packageBindPermissionRequest);

}
