package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 区域响应参数
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpRegionResponse extends BaseResponse {

    /**
     * 区域ID
     */
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 区域编码
     */
    @ChineseDescription("区域编码")
    private String regionCode;

    /**
     * 区域名称
     */
    @ChineseDescription("区域名称")
    private String regionName;

    /**
     * 父级区域ID
     */
    @ChineseDescription("父级区域ID")
    private Long parentId;

    /**
     * 父级区域名称
     */
    @ChineseDescription("父级区域名称")
    private String parentName;

    /**
     * 区域层级（1-国家，2-省，3-市，4-区县，5-商圈）
     */
    @ChineseDescription("区域层级")
    private Integer regionLevel;

    /**
     * 区域层级名称
     */
    @ChineseDescription("区域层级名称")
    private String regionLevelName;

    /**
     * 区域路径（用/分隔，如：1/2/3）
     */
    @ChineseDescription("区域路径")
    private String regionPath;

    /**
     * 排序号
     */
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 状态名称
     */
    @ChineseDescription("状态名称")
    private String statusName;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 子区域列表（树形结构用）
     */
    @ChineseDescription("子区域列表")
    private List<ErpRegionResponse> children;

    /**
     * 是否为叶子节点（用于树形结构）
     */
    @ChineseDescription("是否为叶子节点")
    private Boolean isLeaf;

    /**
     * 是否有子节点（用于懒加载判断）
     */
    @ChineseDescription("是否有子节点")
    private Boolean hasChildren;

    /**
     * 节点图标（用于前端显示）
     */
    @ChineseDescription("节点图标")
    private String icon;

    /**
     * 节点标题（用于树形显示）
     */
    @ChineseDescription("节点标题")
    private String title;

    /**
     * 节点键值（用于树形选择）
     */
    @ChineseDescription("节点键值")
    private String key;

    /**
     * 节点值（用于树形选择）
     */
    @ChineseDescription("节点值")
    private String value;
    
    /**
     * 关联的供应商数量
     */
    @ChineseDescription("关联的供应商数量")
    private Long supplierCount;
    
    /**
     * 关联的客户数量
     */
    @ChineseDescription("关联的客户数量")
    private Long customerCount;
    
    /**
     * 供应商-区域关联表中的数量
     */
    @ChineseDescription("供应商-区域关联表中的数量")
    private Long supplierRegionCount;
    
    /**
     * 客户-区域关联表中的数量
     */
    @ChineseDescription("客户-区域关联表中的数量")
    private Long customerRegionCount;
    
    /**
     * 总关联数量
     */
    @ChineseDescription("总关联数量")
    private Long totalRelationCount;


}
