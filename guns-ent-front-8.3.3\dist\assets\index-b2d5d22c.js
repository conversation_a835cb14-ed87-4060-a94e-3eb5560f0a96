import{_ as Q,r as p,o as X,k as D,bv as Y,a,c as u,b as l,d as o,w as s,f as _,aR as T,g as E,a2 as Z,t as ee,h as c,F as oe,at as te,aI as ne,aJ as se,M as R,E as L,m as w,j as ae,T as le,n as ie,B as re,I as ce,p as de,q as ue,D as _e,l as pe}from"./index-18a1ea24.js";import{_ as me}from"./index-d0cfb2ce.js";import{_ as ye}from"./index-02bf6f00.js";import{_ as ve,R as S}from"./role-add-edit-c7b3b09c.js";/* empty css              *//* empty css              *//* empty css              */import"./role-form-f8e04d4e.js";import"./index-3a0e5c06.js";/* empty css              */import"./OrgApi-021dd6dd.js";/* empty css              *//* empty css              */import"./RoleTypeApi-abe10d8b.js";const he={class:"guns-layout"},fe={class:"guns-layout-content"},Te={class:"guns-layout"},Ce={class:"guns-layout-content-application"},be={class:"content-mian"},we={class:"content-mian-header"},ge={class:"header-content"},ke={class:"header-content-left"},Ie={class:"header-content-right"},xe={class:"content-mian-body"},De={class:"table-content"},Ee=["onClick"],Re={key:0},Le={key:1},Se={key:2},Oe=Object.assign({name:"AuthRole"},{__name:"index",setup(ze){const O=ne(()=>se(()=>import("./role-tree-b3df7459.js"),["assets/role-tree-b3df7459.js","assets/index-18a1ea24.js","assets/index-747cb573.css","assets/index-3a0e5c06.js","assets/index-d0cfb2ce.js","assets/index-c7a076d8.css","assets/index-02bf6f00.js","assets/index-62f54e5b.css","assets/index-3a72e44e.css","assets/index-7acfe497.css","assets/index-27dc9b3a.css","assets/OrgApi-021dd6dd.js","assets/index-20a27405.css","assets/index-9785b617.css","assets/RoleTypeApi-abe10d8b.js","assets/role-tree-a095d7e7.css"])),z=p([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"roleName",title:"\u89D2\u8272\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"roleType",title:"\u89D2\u8272\u7C7B\u578B",width:100,isShow:!0},{dataIndex:"roleCompanyIdWrapper",title:"\u89D2\u8272\u6240\u5C5E\u516C\u53F8",width:100,isShow:!0},{dataIndex:"roleCode",title:"\u89D2\u8272\u7F16\u7801",width:100,isShow:!0},{dataIndex:"roleSort",title:"\u6392\u5E8F",width:100,isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:150,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}]),m=p(null),e=p({searchText:"",roleType:"",roleCategoryId:void 0}),g=p(null),y=p(!1),k=p(null);X(()=>{});const B=({key:t})=>{t=="1"&&j()},A=t=>{e.value.roleCategoryId=t.id,r()},N=t=>{e.value.roleType==20&&(k.value=t,e.value.roleCompanyId=t==null?void 0:t.companyId,r())},$=t=>{e.value.roleCategoryId==t.id&&(e.value.roleCategoryId=void 0,r())},r=()=>{m.value.reload()},V=()=>{e.value.roleCategoryId=void 0,e.value.roleCompanyId=void 0,e.value.roleType!=20&&r()},C=t=>{g.value=t,y.value=!0},M=t=>{R.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u89D2\u8272\u5417?",icon:o(L),maskClosable:!0,onOk:async()=>{const n=await S.delete({roleId:t.roleId});w.success(n.message),r()}})},j=()=>{if(m.value.selectedRowList&&m.value.selectedRowList.length==0)return w.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u89D2\u8272");R.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u89D2\u8272\u5417?",icon:o(L),maskClosable:!0,onOk:async()=>{const t=await S.batchDelete({roleIdList:m.value.selectedRowList});w.success(t.message),r()}})};return(t,n)=>{const v=ae,F=le,b=ie,K=D("plus-outlined"),I=re,h=ce,P=de,U=ue,q=D("small-dash-outlined"),G=_e,J=pe,W=ye,H=me,f=Y("permission");return a(),u("div",he,[l("div",fe,[l("div",Te,[l("div",Ce,[l("div",be,[l("div",we,[l("div",ge,[l("div",ke,[o(b,{size:16},{default:s(()=>[o(F,{activeKey:e.value.roleType,"onUpdate:activeKey":n[0]||(n[0]=i=>e.value.roleType=i),class:"devops-tabs",onChange:V},{default:s(()=>[o(v,{key:"",tab:"\u5168\u90E8"}),(a(),_(v,{key:10,tab:"\u7CFB\u7EDF\u89D2\u8272"})),(a(),_(v,{key:15,tab:"\u4E1A\u52A1\u89D2\u8272"})),(a(),_(v,{key:20,tab:"\u516C\u53F8\u89D2\u8272"}))]),_:1},8,["activeKey"])]),_:1})]),l("div",Ie,[o(b,{size:16},{default:s(()=>[T((a(),_(I,{type:"primary",class:"border-radius",onClick:n[1]||(n[1]=i=>C())},{default:s(()=>[o(K),n[4]||(n[4]=E("\u65B0\u5EFA"))]),_:1,__:[4]})),[[f,["ADD_ROLE"]]]),o(G,null,{overlay:s(()=>[o(U,{onClick:B},{default:s(()=>[T((a(),u("div",null,[o(P,{key:"1"},{default:s(()=>[o(h,{iconClass:"icon-opt-shanchu",color:"#60666b"}),n[5]||(n[5]=l("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[5]})])),[[f,["EDIT_ROLE"]]])]),_:1})]),default:s(()=>[o(I,{class:"border-radius"},{default:s(()=>[n[6]||(n[6]=E(" \u66F4\u591A ")),o(q)]),_:1,__:[6]})]),_:1})]),_:1})])])]),l("div",xe,[l("div",De,[o(H,{width:["",10].includes(e.value.roleType)?"0px":"292px",allowCollapse:!["",10].includes(e.value.roleType),resizable:!["",10].includes(e.value.roleType)},{content:s(()=>[l("div",{class:Z(["role-table",{"no-radius":[15,20].includes(e.value.roleType)}])},[o(W,{columns:z.value,where:e.value,showToolTotal:!1,showTableTool:"",rowId:"roleId",ref_key:"tableRef",ref:m,url:"/sysRole/page",fieldBusinessCode:"ROLE_TABLE"},{toolLeft:s(()=>[o(J,{value:e.value.searchText,"onUpdate:value":n[2]||(n[2]=i=>e.value.searchText=i),placeholder:"\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:r,class:"search-input",bordered:!1},{prefix:s(()=>[o(h,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:s(({column:i,record:d})=>[i.dataIndex=="roleName"?(a(),u("a",{key:0,onClick:x=>C(d)},ee(d.roleName),9,Ee)):c("",!0),i.dataIndex=="roleType"?(a(),u(oe,{key:1},[d.roleType==10?(a(),u("span",Re,"\u7CFB\u7EDF\u89D2\u8272")):c("",!0),d.roleType==15?(a(),u("span",Le,"\u4E1A\u52A1\u89D2\u8272")):c("",!0),d.roleType==20?(a(),u("span",Se,"\u516C\u53F8\u89D2\u8272")):c("",!0)],64)):c("",!0),i.key=="action"?(a(),_(b,{key:2,size:16},{default:s(()=>[T(o(h,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:x=>C(d)},null,8,["onClick"]),[[f,["DELETE_ROLE"]]]),T(o(h,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:x=>M(d)},null,8,["onClick"]),[[f,["EDIT_ROLE"]]])]),_:2},1024)):c("",!0)]),_:1},8,["columns","where"])],2)]),default:s(()=>[[15,20].includes(e.value.roleType)?(a(),_(te(O),{key:0,roleType:e.value.roleType,onTreeSelect:A,onDelRoleType:$,onGetCompanyData:N},null,8,["roleType"])):c("",!0)]),_:1},8,["width","allowCollapse","resizable"])])])])])])]),y.value?(a(),_(ve,{key:0,visible:y.value,"onUpdate:visible":n[3]||(n[3]=i=>y.value=i),roleType:e.value.roleType,roleCategoryId:e.value.roleCategoryId,data:g.value,onDone:r,companyData:k.value},null,8,["visible","roleType","roleCategoryId","data","companyData"])):c("",!0)])}}}),We=Q(Oe,[["__scopeId","data-v-48741f7b"]]);export{We as default};
