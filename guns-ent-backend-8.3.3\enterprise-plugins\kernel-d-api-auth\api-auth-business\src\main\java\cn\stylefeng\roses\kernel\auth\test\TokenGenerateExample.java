package cn.stylefeng.roses.kernel.auth.test;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.stylefeng.roses.kernel.auth.pojo.AuthTokenRequest;

import java.util.Date;

/**
 * 生成token的测试类
 *
 * <AUTHOR>
 * @since 2023/10/26 11:20
 */
public class TokenGenerateExample {

    public static void main(String[] args) {

        // 获取用户的秘钥
        String secret = "cufqwoo9ar843gxj";
        System.out.println(secret);

        AuthTokenRequest authTokenRequest = new AuthTokenRequest();

        // 创建时间戳
        authTokenRequest.setTimestamp(DateUtil.format(new Date(), "yyyyMMddHHmmss"));

        // 生成签名
        authTokenRequest.setApiClientSecretEncode(SecureUtil.md5(secret + authTokenRequest.getTimestamp()));

        // 设置客户端编码
        authTokenRequest.setApiClientCode("jd");

        System.out.println(authTokenRequest);
    }

}
