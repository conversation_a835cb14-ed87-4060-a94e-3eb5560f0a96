package cn.stylefeng.roses.ent.mobile.manage.controller;

import cn.stylefeng.roses.ent.mobile.manage.pojo.index.MobilePersonInfoRequest;
import cn.stylefeng.roses.ent.mobile.manage.pojo.index.MobileUserIndexInfo;
import cn.stylefeng.roses.ent.mobile.manage.service.MobileIndexService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 移动端首页的接口
 *
 * <AUTHOR>
 * @since 2024/3/20 21:29
 */
@RestController
@ApiResource(name = "移动端首页")
public class MobileIndexController {

    @Resource
    private MobileIndexService mobileIndexService;

    /**
     * 移动端-获取首页信息
     *
     * <AUTHOR>
     * @since 2024/3/20 23:10
     */
    @GetResource(name = "移动端-获取首页信息", path = "/mobile/index/getUserInfo")
    public ResponseData<MobileUserIndexInfo> getUserInfo() {
        MobileUserIndexInfo mobileUserIndexInfo = mobileIndexService.getUserIndexInfo();
        return new SuccessResponseData<>(mobileUserIndexInfo);
    }

    /**
     * 移动端-更新个人信息
     *
     * <AUTHOR>
     * @since 2024/3/21 21:17
     */
    @PostResource(name = "移动端-更新个人信息", path = "/mobile/index/updateInfo")
    public ResponseData<?> updateInfo(@Validated @RequestBody MobilePersonInfoRequest mobilePersonInfoRequest) {
        mobileIndexService.updateUserInfo(mobilePersonInfoRequest);
        return new SuccessResponseData<>();
    }


}
