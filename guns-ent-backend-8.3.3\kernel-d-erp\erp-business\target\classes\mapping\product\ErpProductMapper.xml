<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.product.mapper.ErpProductMapper">

    <!-- 商品基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct">
        <id column="product_id" property="productId"/>
        <result column="product_code" property="productCode"/>
        <result column="product_name" property="productName"/>
        <result column="product_short_name" property="productShortName"/>
        <result column="barcode" property="barcode"/>
        <result column="category_id" property="categoryId"/>
        <result column="brand" property="brand"/>
        <result column="specification" property="specification"/>
        <result column="unit" property="unit"/>
        <result column="weight" property="weight"/>
        <result column="volume" property="volume"/>
        <result column="shelf_life" property="shelfLife"/>
        <result column="storage_condition" property="storageCondition"/>
        <result column="status" property="status"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="pricing_type" property="pricingType"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="piece_price" property="piecePrice"/>
        <result column="reference_price" property="referencePrice"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 商品供应商关联结果映射 -->
    <resultMap id="ProductWithSupplierResultMap" type="cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct" extends="BaseResultMap">
        <association property="supplier" javaType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier">
            <id column="supplier_id" property="supplierId"/>
            <result column="supplier_name" property="supplierName"/>
            <result column="supplier_code" property="supplierCode"/>
            <result column="business_mode" property="businessMode"/>
            <result column="sales_deduction" property="salesDeduction"/>
        </association>
    </resultMap>

    <!-- 商品统计信息结果映射 -->
    <resultMap id="ProductStatsResultMap" type="cn.stylefeng.roses.kernel.erp.api.pojo.response.ProductStatsResponse">
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_code" property="productCode"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="pricing_type" property="pricingType"/>
        <result column="current_stock" property="currentStock"/>
        <result column="total_purchase_quantity" property="totalPurchaseQuantity"/>
        <result column="total_purchase_amount" property="totalPurchaseAmount"/>
        <result column="avg_purchase_price" property="avgPurchasePrice"/>
        <result column="last_purchase_date" property="lastPurchaseDate"/>
    </resultMap>

    <!-- 根据供应商ID查询商品 -->
    <select id="findBySupplier" resultMap="BaseResultMap">
        SELECT *
        FROM erp_product
        WHERE supplier_id = #{supplierId}
          AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND pricing_type = #{pricingType}
        </if>
        ORDER BY product_name
    </select>

    <!-- 根据供应商ID列表查询商品 -->
    <select id="findBySupplierIds" resultMap="BaseResultMap">
        SELECT *
        FROM erp_product
        WHERE supplier_id IN
        <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
            #{supplierId}
        </foreach>
        AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND pricing_type = #{pricingType}
        </if>
        ORDER BY supplier_id, product_name
    </select>

    <!-- 根据计价类型查询商品 -->
    <select id="findByPricingType" resultMap="BaseResultMap">
        SELECT *
        FROM erp_product
        WHERE pricing_type = #{pricingType}
          AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="supplierId != null">
            AND supplier_id = #{supplierId}
        </if>
        ORDER BY product_name
    </select>

    <!-- 查询商品及其供应商信息 -->
    <select id="findProductsWithSupplier" resultMap="ProductWithSupplierResultMap">
        SELECT 
            p.*,
            s.supplier_name,
            s.supplier_code,
            s.business_mode,
            s.sales_deduction
        FROM erp_product p
        LEFT JOIN erp_supplier s ON p.supplier_id = s.supplier_id AND s.del_flag = 'N'
        WHERE p.del_flag = 'N'
        <if test="supplierId != null">
            AND p.supplier_id = #{supplierId}
        </if>
        <if test="businessMode != null and businessMode != ''">
            AND s.business_mode = #{businessMode}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND p.pricing_type = #{pricingType}
        </if>
        <if test="status != null and status != ''">
            AND p.status = #{status}
        </if>
        ORDER BY s.supplier_name, p.product_name
    </select>

    <!-- 根据价格范围查询商品 -->
    <select id="findByPriceRange" resultMap="BaseResultMap">
        SELECT *
        FROM erp_product
        WHERE del_flag = 'N'
        <choose>
            <when test="pricingType == 'NORMAL'">
                AND pricing_type = 'NORMAL'
                <if test="minPrice != null">
                    AND retail_price >= #{minPrice}
                </if>
                <if test="maxPrice != null">
                    AND retail_price &lt;= #{maxPrice}
                </if>
            </when>
            <when test="pricingType == 'WEIGHT'">
                AND pricing_type = 'WEIGHT'
                <if test="minPrice != null">
                    AND unit_price >= #{minPrice}
                </if>
                <if test="maxPrice != null">
                    AND unit_price &lt;= #{maxPrice}
                </if>
            </when>
            <when test="pricingType == 'PIECE'">
                AND pricing_type = 'PIECE'
                <if test="minPrice != null">
                    AND piece_price >= #{minPrice}
                </if>
                <if test="maxPrice != null">
                    AND piece_price &lt;= #{maxPrice}
                </if>
            </when>
            <when test="pricingType == 'VARIABLE'">
                AND pricing_type = 'VARIABLE'
                <if test="minPrice != null">
                    AND reference_price >= #{minPrice}
                </if>
                <if test="maxPrice != null">
                    AND reference_price &lt;= #{maxPrice}
                </if>
            </when>
            <otherwise>
                AND (
                    (pricing_type = 'NORMAL' 
                     <if test="minPrice != null">AND retail_price >= #{minPrice}</if>
                     <if test="maxPrice != null">AND retail_price &lt;= #{maxPrice}</if>)
                    OR (pricing_type = 'WEIGHT' 
                        <if test="minPrice != null">AND unit_price >= #{minPrice}</if>
                        <if test="maxPrice != null">AND unit_price &lt;= #{maxPrice}</if>)
                    OR (pricing_type = 'PIECE' 
                        <if test="minPrice != null">AND piece_price >= #{minPrice}</if>
                        <if test="maxPrice != null">AND piece_price &lt;= #{maxPrice}</if>)
                    OR (pricing_type = 'VARIABLE' 
                        <if test="minPrice != null">AND reference_price >= #{minPrice}</if>
                        <if test="maxPrice != null">AND reference_price &lt;= #{maxPrice}</if>)
                )
            </otherwise>
        </choose>
        <if test="supplierId != null">
            AND supplier_id = #{supplierId}
        </if>
        ORDER BY 
            CASE pricing_type
                WHEN 'NORMAL' THEN retail_price
                WHEN 'WEIGHT' THEN unit_price
                WHEN 'PIECE' THEN piece_price
                WHEN 'VARIABLE' THEN reference_price
                ELSE 0
            END,
            product_name
    </select>

    <!-- 模糊搜索商品 -->
    <select id="searchProducts" resultMap="BaseResultMap">
        SELECT *
        FROM erp_product
        WHERE del_flag = 'N'
        <if test="keyword != null and keyword != ''">
            AND (
                product_code LIKE CONCAT('%', #{keyword}, '%')
                OR product_name LIKE CONCAT('%', #{keyword}, '%')
                OR product_short_name LIKE CONCAT('%', #{keyword}, '%')
                OR barcode LIKE CONCAT('%', #{keyword}, '%')
                OR brand LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="supplierId != null">
            AND supplier_id = #{supplierId}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND pricing_type = #{pricingType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY 
            CASE WHEN product_code = #{keyword} THEN 1 ELSE 2 END,
            CASE WHEN product_name = #{keyword} THEN 1 ELSE 2 END,
            CASE WHEN barcode = #{keyword} THEN 1 ELSE 2 END,
            product_name
    </select>

    <!-- 获取商品统计信息 -->
    <select id="getProductStats" resultMap="ProductStatsResultMap">
        SELECT 
            p.product_id,
            p.product_name,
            p.product_code,
            p.supplier_id,
            s.supplier_name,
            p.pricing_type,
            COALESCE(i.current_stock, 0) as current_stock,
            COALESCE(SUM(pod.quantity), 0) as total_purchase_quantity,
            COALESCE(SUM(pod.total_price), 0) as total_purchase_amount,
            COALESCE(AVG(pod.unit_price), 0) as avg_purchase_price,
            MAX(po.order_date) as last_purchase_date
        FROM erp_product p
        LEFT JOIN erp_supplier s ON p.supplier_id = s.supplier_id AND s.del_flag = 'N'
        LEFT JOIN erp_inventory i ON p.product_id = i.product_id
        LEFT JOIN erp_purchase_order_detail pod ON p.product_id = pod.product_id
        LEFT JOIN erp_purchase_order po ON pod.order_id = po.id AND po.status = 'COMPLETED'
        WHERE p.del_flag = 'N'
        <if test="productId != null">
            AND p.product_id = #{productId}
        </if>
        <if test="supplierId != null">
            AND p.supplier_id = #{supplierId}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND p.pricing_type = #{pricingType}
        </if>
        GROUP BY p.product_id, p.product_name, p.product_code, p.supplier_id, s.supplier_name, p.pricing_type, i.current_stock
        ORDER BY p.product_name
    </select>

    <!-- 统计供应商下的商品数量（按计价类型分组） -->
    <select id="countProductsBySupplierAndPricingType" resultType="java.util.Map">
        SELECT 
            pricing_type,
            COUNT(1) as count
        FROM erp_product
        WHERE supplier_id = #{supplierId}
          AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        GROUP BY pricing_type
        ORDER BY pricing_type
    </select>

    <!-- 批量更新商品供应商 -->
    <update id="batchUpdateSupplier">
        UPDATE erp_product 
        SET supplier_id = #{newSupplierId},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE product_id IN
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 批量更新商品计价类型 -->
    <update id="batchUpdatePricingType">
        UPDATE erp_product 
        SET pricing_type = #{pricingType},
            <choose>
                <when test="pricingType == 'NORMAL'">
                    retail_price = #{price},
                    unit_price = NULL,
                    piece_price = NULL,
                    reference_price = NULL,
                </when>
                <when test="pricingType == 'WEIGHT'">
                    retail_price = NULL,
                    unit_price = #{price},
                    piece_price = NULL,
                    reference_price = NULL,
                </when>
                <when test="pricingType == 'PIECE'">
                    retail_price = NULL,
                    unit_price = NULL,
                    piece_price = #{price},
                    reference_price = NULL,
                </when>
                <when test="pricingType == 'VARIABLE'">
                    retail_price = NULL,
                    unit_price = NULL,
                    piece_price = NULL,
                    reference_price = #{price},
                </when>
            </choose>
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE product_id IN
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 查询没有设置供应商的商品 -->
    <select id="findProductsWithoutSupplier" resultMap="BaseResultMap">
        SELECT *
        FROM erp_product
        WHERE supplier_id IS NULL
          AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="pricingType != null and pricingType != ''">
            AND pricing_type = #{pricingType}
        </if>
        ORDER BY product_name
    </select>

</mapper>