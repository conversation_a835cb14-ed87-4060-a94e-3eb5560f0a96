package cn.stylefeng.roses.ent.mobile.manage.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.mobile.manage.enums.AddressBookTypeEnum;
import cn.stylefeng.roses.ent.mobile.manage.pojo.common.OrgUserItem;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用机构和用户列表创建工厂
 *
 * <AUTHOR>
 * @since 2024-04-02 15:11
 */
public class CommonOrgUserFactory {

    /**
     * 创建机构和用户列表
     *
     * <AUTHOR>
     * @since 2024-04-02 15:11
     */
    public static List<OrgUserItem> createOrgUserList(List<HrOrganization> orgList, List<SysUser> sysUserList) {

        List<OrgUserItem> orgUserItems = new ArrayList<>();

        // 1. 先添加组织信息
        if (ObjectUtil.isNotEmpty(orgList)) {
            for (HrOrganization hrOrganization : orgList) {
                OrgUserItem orgItem = createOrgItem(hrOrganization);
                orgUserItems.add(orgItem);
            }
        }

        // 2. 再添加人员信息
        if (ObjectUtil.isNotEmpty(sysUserList)) {
            for (SysUser sysUser : sysUserList) {
                OrgUserItem orgUserItem = new OrgUserItem();
                orgUserItem.setItemType(AddressBookTypeEnum.USER.getCode());
                orgUserItem.setAvatar(sysUser.getAvatar());
                orgUserItem.setUserId(sysUser.getUserId());
                orgUserItem.setRealName(sysUser.getRealName());
                orgUserItems.add(orgUserItem);
            }
        }

        return orgUserItems;
    }

    /**
     * 填充机构信息
     *
     * <AUTHOR>
     * @since 2024-04-02 15:13
     */
    private static OrgUserItem createOrgItem(HrOrganization hrOrganization) {
        OrgUserItem orgUserItem = new OrgUserItem();
        orgUserItem.setItemType(hrOrganization.getOrgType());
        orgUserItem.setOrgId(hrOrganization.getOrgId());
        orgUserItem.setOrgName(hrOrganization.getOrgName());
        return orgUserItem;
    }

}
