System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./regionApi-legacy-73888494.js"],(function(e,r){"use strict";var a,l,t,n,o,i,d,s,u,m,g,f,c,v,p,h,y,_,b,w,I;return{setters:[e=>{a=e._,l=e.r,t=e.s,n=e.a,o=e.f,i=e.w,d=e.d,s=e.g,u=e.m,m=e.l,g=e.u,f=e.v,c=e.G,v=e.as,p=e.y,h=e.W,y=e.J,_=e.$,b=e.H,w=e.M},null,null,e=>{I=e.R}],execute:function(){const r={name:"RegionEditForm",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","ok"],setup(e,{emit:r}){const a=l(),n=l(!1),o=l([]),i=l("1级 - 国家"),d=t({regionId:null,regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),s=e=>{r("update:visible",e),e||m()},m=()=>{var e;null===(e=a.value)||void 0===e||e.resetFields(),Object.assign(d,{regionId:null,regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),g()},g=()=>{const e=(e=>{if(!e)return 1;const r=(e,a)=>{for(const l of e){if(l.regionId===a)return l.regionLevel||1;if(l.children&&l.children.length>0){const e=r(l.children,a);if(e)return e}}return null},a=r(o.value,targetId);return a?Math.min(a+1,5):1})(d.parentId);d.regionLevel=e,i.value=(e=>({1:"1级 - 国家",2:"2级 - 省",3:"3级 - 市",4:"4级 - 区县",5:"5级 - 商圈"}[e]||`${e}级 - 未知`))(e)},f=async()=>{try{let e=await I.findTree()||[];e=c(e),o.value=e}catch(e){console.error("加载区域树失败:",e),o.value=[]}},c=e=>Array.isArray(e)?e.filter((e=>!!(e&&e.regionId&&e.regionName)&&(e.title=e.regionName,e.key=String(e.regionId),e.value=String(e.regionId),e.children&&Array.isArray(e.children)&&(e.children=c(e.children)),!0))):[];return watch((()=>e.data),(e=>{e&&Object.keys(e).length>0&&(Object.assign(d,e),g())}),{immediate:!0}),watch((()=>e.visible),(e=>{e&&f()})),watch((()=>d.parentId),(()=>{g()})),{formRef:a,loading:n,form:d,rules:{regionCode:[{required:!0,message:"请输入区域编码",trigger:"blur"}],regionName:[{required:!0,message:"请输入区域名称",trigger:"blur"}],regionLevel:[{required:!0,message:"请选择区域层级",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},regionTreeData:o,regionLevelText:i,updateVisible:s,save:async()=>{try{await a.value.validate(),n.value=!0,await I.edit(d),u.success("编辑成功"),r("ok"),s(!1)}catch(e){console.error("编辑区域失败:",e),u.error("编辑失败")}finally{n.value=!1}},edit:async e=>{try{await f();const r=await I.detail({regionId:e.regionId});Object.assign(d,r)}catch(r){console.error("获取区域详情失败:",r),u.error("获取详情失败")}},filterTreeNode:(e,r)=>r.title&&r.title.toLowerCase().includes(e.toLowerCase())}}};e("default",a(r,[["render",function(e,r,a,l,t,u){const I=m,k=g,x=f,L=c,N=v,O=p,C=h,U=y,j=_,T=b,A=w;return n(),o(A,{title:"编辑区域",width:800,visible:a.visible,"confirm-loading":l.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":l.updateVisible,onOk:l.save},{default:i((()=>[d(T,{ref:"formRef",model:l.form,rules:l.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:i((()=>[d(L,{gutter:16},{default:i((()=>[d(x,{md:12,sm:24},{default:i((()=>[d(k,{label:"区域编码",name:"regionCode"},{default:i((()=>[d(I,{value:l.form.regionCode,"onUpdate:value":r[0]||(r[0]=e=>l.form.regionCode=e),placeholder:"请输入区域编码"},null,8,["value"])])),_:1})])),_:1}),d(x,{md:12,sm:24},{default:i((()=>[d(k,{label:"区域名称",name:"regionName"},{default:i((()=>[d(I,{value:l.form.regionName,"onUpdate:value":r[1]||(r[1]=e=>l.form.regionName=e),placeholder:"请输入区域名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(L,{gutter:16},{default:i((()=>[d(x,{md:12,sm:24},{default:i((()=>[d(k,{label:"父级区域",name:"parentId"},{default:i((()=>[d(N,{value:l.form.parentId,"onUpdate:value":r[2]||(r[2]=e=>l.form.parentId=e),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":l.regionTreeData,placeholder:"请选择父级区域","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":l.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])])),_:1})])),_:1}),d(x,{md:12,sm:24},{default:i((()=>[d(k,{label:"区域层级",name:"regionLevel"},{default:i((()=>[d(I,{value:l.regionLevelText,"onUpdate:value":r[3]||(r[3]=e=>l.regionLevelText=e),placeholder:"根据父级区域自动设置",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),d(L,{gutter:16},{default:i((()=>[d(x,{md:12,sm:24},{default:i((()=>[d(k,{label:"排序号",name:"sortOrder"},{default:i((()=>[d(O,{value:l.form.sortOrder,"onUpdate:value":r[4]||(r[4]=e=>l.form.sortOrder=e),min:0,max:9999,style:{width:"100%"},placeholder:"请输入排序号"},null,8,["value"])])),_:1})])),_:1}),d(x,{md:12,sm:24},{default:i((()=>[d(k,{label:"状态",name:"status"},{default:i((()=>[d(U,{value:l.form.status,"onUpdate:value":r[5]||(r[5]=e=>l.form.status=e),placeholder:"请选择状态"},{default:i((()=>[d(C,{value:"Y"},{default:i((()=>r[7]||(r[7]=[s("启用")]))),_:1,__:[7]}),d(C,{value:"N"},{default:i((()=>r[8]||(r[8]=[s("停用")]))),_:1,__:[8]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),d(L,{gutter:16},{default:i((()=>[d(x,{span:24},{default:i((()=>[d(k,{label:"备注",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:i((()=>[d(j,{value:l.form.remark,"onUpdate:value":r[6]||(r[6]=e=>l.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading","onUpdate:visible","onOk"])}]]))}}}));
