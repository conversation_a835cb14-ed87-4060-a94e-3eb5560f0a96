package cn.stylefeng.roses.kernel.secret.modular.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户临时秘钥封装类
 *
 * <AUTHOR>
 * @date 2022/03/22 11:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysUserSecretKeyRequest extends BaseRequest {

    /**
     * 秘钥id
     */
    @NotNull(message = "秘钥id不能为空", groups = {delete.class})
    @ChineseDescription("秘钥id")
    private Long userSecretKeyId;

    /**
     * 秘钥名称
     */
    @NotBlank(message = "秘钥名称不能为空", groups = {add.class})
    @ChineseDescription("秘钥名称")
    private String secretKeyName;

    /**
     * 所属用户id
     */
    @NotNull(message = "所属用户id不能为空", groups = {add.class})
    @ChineseDescription("所属用户id")
    private Long userId;

    /**
     * 秘钥值
     */
    @NotBlank(message = "秘钥值不能为空", groups = {add.class})
    @ChineseDescription("秘钥值")
    private String secretKey;

    /**
     * 秘钥过期时间，不填则为永久
     */
    @ChineseDescription("秘钥过期时间")
    private String secretExpirationTime;

    /**
     * 秘钥是否使用一次后删除：Y-是，N-否
     */
    @ChineseDescription("秘钥是否使用一次后删除：Y-是，N-否")
    @NotBlank(message = "是否为一次性秘钥不能为空", groups = {add.class})
    private String secretOnceFlag;

    /**
     * 用户查询条件
     */
    @ChineseDescription("用户查询条件，用户账号或姓名")
    @NotBlank(message = "用户查询条件为空，请输入用户账号或姓名", groups = {queryUser.class})
    private String condition;

    public @interface queryUser {
    }

}
