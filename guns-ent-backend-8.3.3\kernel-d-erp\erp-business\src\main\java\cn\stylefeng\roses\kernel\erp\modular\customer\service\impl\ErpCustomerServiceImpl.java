package cn.stylefeng.roses.kernel.erp.modular.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpCustomerConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpCustomerExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpCustomer;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpCustomerRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpCustomerResponse;
import cn.stylefeng.roses.kernel.erp.modular.customer.mapper.ErpCustomerMapper;
import cn.stylefeng.roses.kernel.erp.modular.customer.service.ErpCustomerService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户主档案Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
@Service
public class ErpCustomerServiceImpl extends ServiceImpl<ErpCustomerMapper, ErpCustomer> implements ErpCustomerService {

    @Resource
    private ErpCustomerMapper erpCustomerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ErpCustomerRequest erpCustomerRequest) {
        ErpCustomer erpCustomer = new ErpCustomer();
        BeanUtil.copyProperties(erpCustomerRequest, erpCustomer);

        // 校验客户编码是否重复
        if (this.validateCustomerCodeRepeat(erpCustomer.getCustomerCode(), null)) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_CODE_REPEAT);
        }

        // 设置默认值
        if (StrUtil.isBlank(erpCustomer.getCustomerType())) {
            erpCustomer.setCustomerType(ErpCustomerConstants.DEFAULT_CUSTOMER_TYPE);
        }
        if (StrUtil.isBlank(erpCustomer.getCustomerLevel())) {
            erpCustomer.setCustomerLevel(ErpCustomerConstants.DEFAULT_CUSTOMER_LEVEL);
        }
        if (StrUtil.isBlank(erpCustomer.getStatus())) {
            erpCustomer.setStatus(ErpCustomerConstants.DEFAULT_CUSTOMER_STATUS);
        }
        if (ObjectUtil.isNull(erpCustomer.getPaymentTerms())) {
            erpCustomer.setPaymentTerms(ErpCustomerConstants.DEFAULT_PAYMENT_TERMS);
        }
        if (ObjectUtil.isNull(erpCustomer.getCreditLimit())) {
            erpCustomer.setCreditLimit(BigDecimal.ZERO);
        }
        if (ObjectUtil.isNull(erpCustomer.getUsedCredit())) {
            erpCustomer.setUsedCredit(BigDecimal.ZERO);
        }

        // 校验参数
        this.validateCustomerParams(erpCustomer);

        this.save(erpCustomer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(ErpCustomerRequest erpCustomerRequest) {
        ErpCustomer erpCustomer = this.queryCustomer(erpCustomerRequest);

        // 校验是否可以删除
        if (!this.validateCanDelete(erpCustomer.getCustomerId())) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_HAS_BUSINESS_DATA_CANNOT_DELETE);
        }

        this.removeById(erpCustomer.getCustomerId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ErpCustomerRequest erpCustomerRequest) {
        List<Long> customerIdList = erpCustomerRequest.getCustomerIdList();
        if (ObjectUtil.isEmpty(customerIdList)) {
            return;
        }

        // 校验每个客户是否可以删除
        for (Long customerId : customerIdList) {
            if (!this.validateCanDelete(customerId)) {
                throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_HAS_BUSINESS_DATA_CANNOT_DELETE);
            }
        }

        this.removeBatchByIds(customerIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ErpCustomerRequest erpCustomerRequest) {
        ErpCustomer erpCustomer = this.queryCustomer(erpCustomerRequest);

        // 校验客户编码是否重复（排除自己）
        if (this.validateCustomerCodeRepeat(erpCustomerRequest.getCustomerCode(), erpCustomer.getCustomerId())) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_CODE_REPEAT);
        }

        BeanUtil.copyProperties(erpCustomerRequest, erpCustomer);

        // 校验参数
        this.validateCustomerParams(erpCustomer);

        this.updateById(erpCustomer);
    }

    @Override
    public ErpCustomerResponse detail(ErpCustomerRequest erpCustomerRequest) {
        ErpCustomer erpCustomer = this.queryCustomer(erpCustomerRequest);
        ErpCustomerResponse response = new ErpCustomerResponse();
        BeanUtil.copyProperties(erpCustomer, response);

        // 填充扩展信息
        this.fillCustomerExtInfo(response);

        return response;
    }

    @Override
    public PageResult<ErpCustomerResponse> findPage(ErpCustomerRequest erpCustomerRequest) {
        LambdaQueryWrapper<ErpCustomer> wrapper = this.createWrapper(erpCustomerRequest);
        Page<ErpCustomer> page = this.page(PageFactory.defaultPage(), wrapper);

        List<ErpCustomerResponse> responseList = page.getRecords().stream().map(customer -> {
            ErpCustomerResponse response = new ErpCustomerResponse();
            BeanUtil.copyProperties(customer, response);
            this.fillCustomerExtInfo(response);
            return response;
        }).collect(Collectors.toList());

        return PageResultFactory.createPageResult(responseList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public List<ErpCustomerResponse> findList(ErpCustomerRequest erpCustomerRequest) {
        LambdaQueryWrapper<ErpCustomer> wrapper = this.createWrapper(erpCustomerRequest);
        List<ErpCustomer> customerList = this.list(wrapper);

        return customerList.stream().map(customer -> {
            ErpCustomerResponse response = new ErpCustomerResponse();
            BeanUtil.copyProperties(customer, response);
            this.fillCustomerExtInfo(response);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(ErpCustomerRequest erpCustomerRequest) {
        ErpCustomer erpCustomer = this.queryCustomer(erpCustomerRequest);

        // 如果要停用，校验是否可以停用
        if (ErpCustomerConstants.CUSTOMER_STATUS_INACTIVE.equals(erpCustomerRequest.getStatus()) ||
            ErpCustomerConstants.CUSTOMER_STATUS_FROZEN.equals(erpCustomerRequest.getStatus())) {
            if (!this.validateCanInactive(erpCustomer.getCustomerId())) {
                throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_HAS_BUSINESS_DATA_CANNOT_INACTIVE);
            }
        }

        // 校验状态参数
        this.validateStatus(erpCustomerRequest.getStatus());

        LambdaUpdateWrapper<ErpCustomer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ErpCustomer::getCustomerId, erpCustomer.getCustomerId())
                .set(ErpCustomer::getStatus, erpCustomerRequest.getStatus());

        this.update(updateWrapper);
    }

    @Override
    public boolean validateCustomerCodeRepeat(String customerCode, Long customerId) {
        LambdaQueryWrapper<ErpCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpCustomer::getCustomerCode, customerCode);
        if (ObjectUtil.isNotNull(customerId)) {
            wrapper.ne(ErpCustomer::getCustomerId, customerId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean validateCanDelete(Long customerId) {
        // TODO: 这里需要检查客户是否有关联的业务数据
        // 例如：销售订单、出库单等
        // 暂时返回true，后续根据业务需要完善
        return true;
    }

    @Override
    public boolean validateCanInactive(Long customerId) {
        // TODO: 这里需要检查客户是否有未完成的业务数据
        // 例如：未完成的销售订单等
        // 暂时返回true，后续根据业务需要完善
        return true;
    }

    /**
     * 根据主键查询客户
     */
    private ErpCustomer queryCustomer(ErpCustomerRequest erpCustomerRequest) {
        ErpCustomer erpCustomer = this.getById(erpCustomerRequest.getCustomerId());
        if (ObjectUtil.isNull(erpCustomer)) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_NOT_EXIST);
        }
        return erpCustomer;
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<ErpCustomer> createWrapper(ErpCustomerRequest erpCustomerRequest) {
        LambdaQueryWrapper<ErpCustomer> wrapper = new LambdaQueryWrapper<>();

        // 客户编码
        if (StrUtil.isNotBlank(erpCustomerRequest.getCustomerCode())) {
            wrapper.like(ErpCustomer::getCustomerCode, erpCustomerRequest.getCustomerCode());
        }

        // 客户名称
        if (StrUtil.isNotBlank(erpCustomerRequest.getCustomerName())) {
            wrapper.like(ErpCustomer::getCustomerName, erpCustomerRequest.getCustomerName());
        }

        // 客户类型
        if (StrUtil.isNotBlank(erpCustomerRequest.getCustomerType())) {
            wrapper.eq(ErpCustomer::getCustomerType, erpCustomerRequest.getCustomerType());
        }

        // 客户等级
        if (StrUtil.isNotBlank(erpCustomerRequest.getCustomerLevel())) {
            wrapper.eq(ErpCustomer::getCustomerLevel, erpCustomerRequest.getCustomerLevel());
        }

        // 所属区域
        if (ObjectUtil.isNotNull(erpCustomerRequest.getRegionId())) {
            wrapper.eq(ErpCustomer::getRegionId, erpCustomerRequest.getRegionId());
        }

        // 状态
        if (StrUtil.isNotBlank(erpCustomerRequest.getStatus())) {
            wrapper.eq(ErpCustomer::getStatus, erpCustomerRequest.getStatus());
        }

        // 联系人
        if (StrUtil.isNotBlank(erpCustomerRequest.getContactPerson())) {
            wrapper.like(ErpCustomer::getContactPerson, erpCustomerRequest.getContactPerson());
        }

        // 联系电话
        if (StrUtil.isNotBlank(erpCustomerRequest.getContactPhone())) {
            wrapper.like(ErpCustomer::getContactPhone, erpCustomerRequest.getContactPhone());
        }

        // 手机号码
        if (StrUtil.isNotBlank(erpCustomerRequest.getContactMobile())) {
            wrapper.like(ErpCustomer::getContactMobile, erpCustomerRequest.getContactMobile());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(ErpCustomer::getCreateTime);

        return wrapper;
    }

    /**
     * 校验客户参数
     */
    private void validateCustomerParams(ErpCustomer erpCustomer) {
        // 校验客户类型
        this.validateCustomerType(erpCustomer.getCustomerType());

        // 校验客户等级
        this.validateCustomerLevel(erpCustomer.getCustomerLevel());

        // 校验状态
        this.validateStatus(erpCustomer.getStatus());

        // 校验信用额度
        if (ObjectUtil.isNotNull(erpCustomer.getCreditLimit()) && 
            ObjectUtil.isNotNull(erpCustomer.getUsedCredit()) &&
            erpCustomer.getCreditLimit().compareTo(erpCustomer.getUsedCredit()) < 0) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_CREDIT_LIMIT_ERROR);
        }

        // 校验账期天数
        if (ObjectUtil.isNotNull(erpCustomer.getPaymentTerms()) && erpCustomer.getPaymentTerms() < 0) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_PAYMENT_TERMS_ERROR);
        }
    }

    /**
     * 校验客户类型
     */
    private void validateCustomerType(String customerType) {
        if (!ErpCustomerConstants.CUSTOMER_TYPE_ENTERPRISE.equals(customerType) &&
                !ErpCustomerConstants.CUSTOMER_TYPE_INDIVIDUAL.equals(customerType) &&
                !ErpCustomerConstants.CUSTOMER_TYPE_RETAIL.equals(customerType)) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_TYPE_ERROR);
        }
    }

    /**
     * 校验客户等级
     */
    private void validateCustomerLevel(String customerLevel) {
        if (!ErpCustomerConstants.CUSTOMER_LEVEL_DIAMOND.equals(customerLevel) &&
                !ErpCustomerConstants.CUSTOMER_LEVEL_GOLD.equals(customerLevel) &&
                !ErpCustomerConstants.CUSTOMER_LEVEL_SILVER.equals(customerLevel) &&
                !ErpCustomerConstants.CUSTOMER_LEVEL_BRONZE.equals(customerLevel)) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_LEVEL_ERROR);
        }
    }

    /**
     * 校验状态
     */
    private void validateStatus(String status) {
        if (!ErpCustomerConstants.CUSTOMER_STATUS_ACTIVE.equals(status) &&
                !ErpCustomerConstants.CUSTOMER_STATUS_INACTIVE.equals(status) &&
                !ErpCustomerConstants.CUSTOMER_STATUS_FROZEN.equals(status)) {
            throw new ServiceException(ErpCustomerExceptionEnum.CUSTOMER_STATUS_ERROR);
        }
    }

    /**
     * 填充客户扩展信息
     */
    private void fillCustomerExtInfo(ErpCustomerResponse response) {
        // 填充客户类型名称
        if (ErpCustomerConstants.CUSTOMER_TYPE_ENTERPRISE.equals(response.getCustomerType())) {
            response.setCustomerTypeName("企业");
        } else if (ErpCustomerConstants.CUSTOMER_TYPE_INDIVIDUAL.equals(response.getCustomerType())) {
            response.setCustomerTypeName("个人");
        } else if (ErpCustomerConstants.CUSTOMER_TYPE_RETAIL.equals(response.getCustomerType())) {
            response.setCustomerTypeName("零售");
        }

        // 填充客户等级名称
        if (ErpCustomerConstants.CUSTOMER_LEVEL_DIAMOND.equals(response.getCustomerLevel())) {
            response.setCustomerLevelName("钻石");
        } else if (ErpCustomerConstants.CUSTOMER_LEVEL_GOLD.equals(response.getCustomerLevel())) {
            response.setCustomerLevelName("黄金");
        } else if (ErpCustomerConstants.CUSTOMER_LEVEL_SILVER.equals(response.getCustomerLevel())) {
            response.setCustomerLevelName("白银");
        } else if (ErpCustomerConstants.CUSTOMER_LEVEL_BRONZE.equals(response.getCustomerLevel())) {
            response.setCustomerLevelName("青铜");
        }

        // 填充状态名称
        if (ErpCustomerConstants.CUSTOMER_STATUS_ACTIVE.equals(response.getStatus())) {
            response.setStatusName("正常");
        } else if (ErpCustomerConstants.CUSTOMER_STATUS_INACTIVE.equals(response.getStatus())) {
            response.setStatusName("停用");
        } else if (ErpCustomerConstants.CUSTOMER_STATUS_FROZEN.equals(response.getStatus())) {
            response.setStatusName("冻结");
        }

        // 计算可用额度
        if (ObjectUtil.isNotNull(response.getCreditLimit()) && ObjectUtil.isNotNull(response.getUsedCredit())) {
            response.setAvailableCredit(response.getCreditLimit().subtract(response.getUsedCredit()));
        }
    }

}
