System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js"],(function(e,l){"use strict";var a,r,u,t,n,o,d,s,c,i;return{setters:[e=>{a=e.r,r=e.o,u=e.X,t=e.a,n=e.f,o=e.a6,d=e.c,s=e.d,c=e.w,i=e.a0},null,null,null],execute:function(){const l={__name:"index",props:{value:{type:[String,Array],default:""},valueFormat:{type:String,default:"YYYY-MM-DD"},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},firstPlaceholder:{type:String,default:"请选择开始日期"},secondPlaceholder:{type:String,default:"请选择结束日期"},readonly:{type:Boolean,default:!1},normal:{type:Boolean,default:!1}},emits:["update:value","onBlur","onClick","onChange"],setup(e,{emit:l}){const d=e,s=l,c=a([]);r((()=>{i()})),u((()=>d.value),(e=>{i()}),{deep:!0});const i=()=>{d.value?c.value=d.normal?d.value:JSON.parse(d.value):c.value=[]},v=()=>{let e=d.normal?c.value:JSON.stringify(c.value);s("update:value",e),s("onChange",d.record)},f=()=>{s("onClick",d.record)},y=()=>{s("onBlur",d.record)};return(l,a)=>{const r=o;return t(),n(r,{value:c.value,"onUpdate:value":a[0]||(a[0]=e=>c.value=e),"value-format":e.valueFormat,disabled:e.readonly||e.disabled,allowClear:"","show-time":"",onChange:v,onClick:f,onBlur:y,class:"w-full",placeholder:[e.firstPlaceholder,e.secondPlaceholder]},null,8,["value","value-format","disabled","placeholder"])}}},v={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const r=a(null),u=a("YYYY-MM-DD"),n=a(!1),o=a(!1),f=a("请选择");return(e,a)=>{const y=l,p=i;return t(),d("div",v,[s(p,{title:"日期范围选择",bordered:!1},{default:c((()=>[s(y,{value:r.value,"onUpdate:value":a[0]||(a[0]=e=>r.value=e),valueFormat:u.value,disabled:n.value,firstPlaceholder:f.value,secondPlaceholder:f.value,readonly:o.value},null,8,["value","valueFormat","disabled","firstPlaceholder","secondPlaceholder","readonly"])])),_:1})])}}})}}}));
