System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-b540c599.js","./HomeApi-legacy-e471d9ef.js"],(function(e,a){"use strict";var t,l,n,i,o,d,c,u,s,r,p,v,h,f,m,b,x,g,y,w,k,I,_,C,j,E,L,U,z;return{setters:[e=>{t=e._,l=e.b2,n=e.r,i=e.o,o=e.bG,d=e.bj,c=e.X,u=e.a,s=e.c,r=e.b,p=e.d,v=e.I,h=e.w,f=e.F,m=e.e,b=e.t,x=e.f,g=e.g,y=e.m,w=e.a5,k=e.S,I=e.bH,_=e.A,C=e.a7,j=e.l,E=e.aO,L=e.U,U=e.M},null,null,e=>{z=e.H}],execute:function(){var a=document.createElement("style");a.textContent=".card[data-v-bb990669]{width:100%;height:100%;background-color:#fff;box-shadow:0 0 6px rgba(204,204,204,.5);border-radius:4px;padding:10px}.card-header[data-v-bb990669]{width:100%;height:50px;border-bottom:1px #999999 solid;display:flex;align-items:center;justify-content:space-between}.card-header-title[data-v-bb990669]{font-size:18px;font-weight:500}.card-body[data-v-bb990669]{width:100%;height:calc(100% - 50px)}.app-list[data-v-bb990669]{width:100%;height:100%;display:flex;flex-wrap:wrap;align-content:flex-start;overflow-x:hidden;overflow-y:auto}.app-list-item[data-v-bb990669]{cursor:pointer;min-width:80px;height:50%;display:flex;flex-direction:column;justify-content:center;align-items:center}.app-item-title[data-v-bb990669]{font-size:14px;color:#535353}.common-function[data-v-bb990669]{width:100%;height:100%;display:flex;flex-direction:column}.common-function-header[data-v-bb990669]{display:flex;justify-content:space-between;align-items:center}.common-function-header-left[data-v-bb990669] .ant-radio-button-wrapper{height:40px;line-height:40px}.common-function-header-right[data-v-bb990669]{width:220px;line-height:40px;height:40px}.common-function-list[data-v-bb990669]{width:100%;height:500px;overflow-x:hidden;overflow-y:auto;border:1px #d9d9d9 solid;margin-top:10px}.common-function-list[data-v-bb990669] .ant-tree{margin-top:10px}.common-function-check-list[data-v-bb990669]{margin-top:10px;font-size:16px;color:#505050}\n",document.head.appendChild(a);const A={class:"card"},O={class:"card-header"},H={class:"card-body"},K={class:"app-list"},F={key:0,style:{width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},N=["onClick"],S={class:"app-item-icon"},G={class:"app-item-title"},M={class:"common-function"},R={class:"common-function-header"},X={class:"common-function-list"},q={class:"common-function-check-list"};e("default",t({__name:"common-functions",setup(e){const a=l(),t=n([]),B=n(!1),D=n(!1),J=n(!1),P=n([]),Q=n(""),T=n(""),V=n({}),W=n({}),Y=n(!0),Z=n([]),$=n([]),ee={children:"children",label:"title",key:"menuId",value:"menuId"},ae=()=>{J.value=!0;let e=d(Z.value).map((e=>e.menuId));z.updateUserAppList({menuIdList:e}).then((e=>{e.success&&(y.success(e.message),D.value=!1,ie())})).finally((()=>{J.value=!1}))},te=()=>{let e=P.value.filter((e=>e.appId===Q.value));1===e.length&&($.value=le(e[0].menuList))},le=e=>(e.map((e=>(e.key=e.menuId,e.children&&(e.children=le(e.children)),e))),e),ne=(e,a)=>{for(const t of e){if(t.menuId===a)return t;if(t.children){const e=ne(t.children,a);if(e)return e}}return null},ie=async()=>{B.value=!0;let e=await z.getUserAppList().finally((()=>{B.value=!1}));t.value=e},oe=(e,a,t)=>{for(let l=0;l<a.length;l++){let n=a[l];n.title.indexOf(e)>-1&&t.push(n.key),n.children&&oe(e,n.children,t)}return t};i((async()=>{ie();let e=await o();if(P.value=e.userAppInfoList,P.value.length>0){Q.value=P.value[0].appId;let e=P.value.filter((e=>e.appId===Q.value));1===e.length&&($.value=le(e[0].menuList))}P.value.forEach((e=>{let a=[];t.value.forEach((t=>{let l=ne(e.menuList,t.menuId);l&&a.push(l.menuId)})),V.value[e.appId]=a})),Z.value=d(t.value)})),c((()=>V.value),(e=>{let a=[];P.value.forEach((t=>{e[t.appId].forEach((e=>{let l=ne(t.menuList,e);l&&a.push(l)}))})),Z.value=a}),{deep:!0});const de=e=>{W.value[Q.value]=e,Y.value=!1};return c((()=>T.value),(e=>{W.value[Q.value]=oe(e,$.value,[]),T.value=e,Y.value=!0})),(e,l)=>{const n=w,i=k,o=I,c=_,y=v,z=C,le=j,ne=E,ie=L,oe=U;return u(),s("div",A,[r("div",O,[l[6]||(l[6]=r("div",{class:"card-header-title"},[r("span",null,"常用功能")],-1)),r("div",{class:"user-info-card-header-icon",onClick:l[0]||(l[0]=e=>D.value=!0)},[p(v,{"font-size":"34px",color:"#2a82e4","icon-class":"icon-opt-shezhi","font-weight":"bold"})])]),r("div",H,[p(i,{spinning:B.value},{default:h((()=>[r("div",K,[0===t.value.length?(u(),s("div",F,[p(n)])):(u(!0),s(f,{key:1},m(t.value,((e,t)=>(u(),s("div",{class:"app-list-item",key:t,onClick:t=>(e=>{const{href:t}=a.resolve({path:e.menuRouter});window.open(t,"_blank")})(e)},[r("div",S,[p(v,{"font-size":"36px",color:"#2a82e4","icon-class":e.menuIcon},null,8,["icon-class"])]),r("div",G,[r("span",null,b(e.menuName),1)])],8,N)))),128))])])),_:1},8,["spinning"])]),p(oe,{width:760,visible:D.value,"onUpdate:visible":l[4]||(l[4]=e=>D.value=e),title:"选择常用功能",onCancel:l[5]||(l[5]=e=>{return a=!1,void(D.value=a);var a}),"confirm-loading":J.value,onOk:ae},{default:h((()=>[r("div",M,[r("div",R,[p(c,{class:"common-function-header-left",value:Q.value,"onUpdate:value":l[1]||(l[1]=e=>Q.value=e),onChange:te},{default:h((()=>[(u(!0),s(f,null,m(P.value,(e=>(u(),x(o,{style:{"min-width":"120px","text-align":"center"},key:e.appId,value:e.appId},{default:h((()=>[g(b(e.appName),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"]),p(le,{value:T.value,"onUpdate:value":l[2]||(l[2]=e=>T.value=e),class:"common-function-header-right","allow-clear":"",placeholder:"菜单名称（回车搜索）"},{suffix:h((()=>[p(z,{title:"Extra information"},{default:h((()=>[p(y,{iconClass:"icon-opt-search"})])),_:1})])),_:1},8,["value"])]),r("div",X,[p(ne,{onExpand:de,"expanded-keys":W.value[Q.value],"auto-expand-parent":Y.value,checkedKeys:V.value[Q.value],"onUpdate:checkedKeys":l[3]||(l[3]=e=>V.value[Q.value]=e),checkable:"","field-names":ee,"tree-data":$.value},{title:h((({title:e})=>[g(b(e),1)])),_:1},8,["expanded-keys","auto-expand-parent","checkedKeys","tree-data"])]),r("div",q,[l[7]||(l[7]=g(" 已选： ")),(u(!0),s(f,null,m(Z.value,(e=>(u(),x(ie,{key:e.menuId,color:"processing",closable:"",onClose:a=>(e=>{let a=d(V.value),t={};Object.keys(a).forEach((l=>{const n=a[l];let i=n.findIndex((a=>a===e.menuId));i>-1&&n.splice(i,1),t[l]=n})),V.value=t})(e)},{default:h((()=>[g(b(e.title),1)])),_:2},1032,["onClose"])))),128))])])])),_:1},8,["visible","confirm-loading"])])}}},[["__scopeId","data-v-bb990669"]]))}}}));
