package cn.stylefeng.roses.kernel.sync.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.sync.pojo.PositionSyncVo;
import cn.stylefeng.roses.kernel.sys.modular.position.entity.HrPosition;

import java.util.ArrayList;
import java.util.List;

/**
 * 职位信息创建
 *
 * <AUTHOR>
 * @since 2023/10/30 11:42
 */
public class SyncPositionFactory {

    /**
     * 创建职位信息
     *
     * <AUTHOR>
     * @since 2023/10/30 11:43
     */
    public static List<PositionSyncVo> createPositionVo(List<HrPosition> positionList) {

        List<PositionSyncVo> positionSyncVos = new ArrayList<>();

        if (ObjectUtil.isEmpty(positionList)) {
            return positionSyncVos;
        }

        for (HrPosition hrPosition : positionList) {
            PositionSyncVo positionSyncVo = new PositionSyncVo();
            BeanUtil.copyProperties(hrPosition, positionSyncVo, CopyOptions.create().ignoreError());

            if (hrPosition.getPositionId() != null) {
                positionSyncVo.setPositionId(String.valueOf(hrPosition.getPositionId()));
            }

            positionSyncVos.add(positionSyncVo);
        }

        return positionSyncVos;
    }

}
