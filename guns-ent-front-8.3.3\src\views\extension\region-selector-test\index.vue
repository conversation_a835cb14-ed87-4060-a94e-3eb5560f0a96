<template>
  <div class="guns-body guns-body-card">
    <a-card title="区域选择组件测试" :bordered="false">
      <a-space direction="vertical" size="large" style="width: 100%">
        
        <!-- 基本多选测试 -->
        <div>
          <h3>基本多选模式（修复后测试）</h3>
          <region-selector
            v-model="multiSelectValue"
            placeholder="请选择多个区域"
            @change="handleMultiChange"
            style="width: 400px"
          />
          <div style="margin-top: 8px">
            <strong>选中值:</strong> {{ JSON.stringify(multiSelectValue) }}
          </div>
          <div style="margin-top: 4px">
            <strong>选中区域:</strong> {{ multiSelectRegions.map(r => r.regionName).join(', ') }}
          </div>
          <div style="margin-top: 4px; color: #666; font-size: 12px;">
            测试要点：1. 树节点名称是否正确显示 2. 勾选后是否保持选中状态 3. 数据是否正确更新
          </div>
        </div>

        <!-- 单选模式测试 -->
        <div>
          <h3>单选模式</h3>
          <region-selector 
            v-model="singleSelectValue" 
            :multiple="false"
            placeholder="请选择一个区域"
            @change="handleSingleChange"
            style="width: 400px"
          />
          <div style="margin-top: 8px">
            <strong>选中值:</strong> {{ singleSelectValue }}
          </div>
          <div style="margin-top: 4px">
            <strong>选中区域:</strong> {{ singleSelectRegion?.regionName || '无' }}
          </div>
        </div>

        <!-- 禁用搜索测试 -->
        <div>
          <h3>禁用搜索功能</h3>
          <region-selector 
            v-model="noSearchValue" 
            :showSearch="false"
            placeholder="无搜索功能的区域选择"
            style="width: 400px"
          />
        </div>

        <!-- 只读状态测试 -->
        <div>
          <h3>只读状态</h3>
          <region-selector 
            v-model="readonlyValue" 
            readonly
            placeholder="只读状态"
            style="width: 400px"
          />
          <a-button @click="setReadonlyValue" style="margin-left: 8px">设置值</a-button>
        </div>

        <!-- 禁用状态测试 -->
        <div>
          <h3>禁用状态</h3>
          <region-selector 
            v-model="disabledValue" 
            disabled
            placeholder="禁用状态"
            style="width: 400px"
          />
        </div>

        <!-- 带操作按钮测试 -->
        <div>
          <h3>带操作按钮</h3>
          <region-selector 
            v-model="actionButtonValue" 
            :showActionButtons="true"
            placeholder="带确定/取消按钮"
            @change="handleActionButtonChange"
            style="width: 400px"
          />
        </div>

        <!-- 组件方法测试 -->
        <div>
          <h3>组件方法测试</h3>
          <region-selector 
            ref="regionSelectorRef"
            v-model="methodTestValue" 
            placeholder="方法测试"
            style="width: 400px"
          />
          <div style="margin-top: 8px">
            <a-space>
              <a-button @click="clearSelection">清空选择</a-button>
              <a-button @click="getSelectedRegions">获取选中区域</a-button>
              <a-button @click="reloadData">重新加载数据</a-button>
            </a-space>
          </div>
        </div>

        <!-- 表单集成测试 -->
        <div>
          <h3>表单集成测试</h3>
          <a-form :model="formData" layout="vertical" style="width: 400px">
            <a-form-item label="供应商关联区域" name="supplierRegions">
              <region-selector 
                v-model="formData.supplierRegions" 
                placeholder="请选择供应商关联的区域"
              />
            </a-form-item>
            <a-form-item label="客户关联区域" name="customerRegions">
              <region-selector 
                v-model="formData.customerRegions" 
                placeholder="请选择客户关联的区域"
              />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="submitForm">提交表单</a-button>
                <a-button @click="resetForm">重置表单</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>

        <!-- 问题修复验证测试 -->
        <div>
          <h3>问题修复验证测试</h3>
          <region-selector
            v-model="fixTestValue"
            placeholder="验证修复效果"
            @change="handleFixTestChange"
            style="width: 400px"
          />
          <div style="margin-top: 8px">
            <strong>当前选中:</strong> {{ JSON.stringify(fixTestValue) }}
          </div>
          <div style="margin-top: 4px">
            <strong>选中区域:</strong> {{ fixTestRegions.map(r => r.regionName).join(', ') }}
          </div>
          <div style="margin-top: 8px">
            <a-space>
              <a-button @click="setTestData">设置测试数据</a-button>
              <a-button @click="clearTestData">清空数据</a-button>
            </a-space>
          </div>
          <div style="margin-top: 8px; padding: 8px; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 4px;">
            <h4 style="margin: 0 0 8px 0; color: #0369a1;">修复验证要点：</h4>
            <ul style="margin: 0; padding-left: 20px; color: #0369a1;">
              <li>树节点名称是否正确显示（不再是空白）</li>
              <li>勾选节点后是否保持选中状态（不会自动取消）</li>
              <li>数据回显是否正常工作</li>
              <li>多选操作是否流畅</li>
            </ul>
          </div>
        </div>

        <!-- 事件监听测试 -->
        <div>
          <h3>事件监听测试</h3>
          <region-selector
            v-model="eventTestValue"
            placeholder="事件测试"
            @change="handleEventChange"
            @search="handleEventSearch"
            @clear="handleEventClear"
            style="width: 400px"
          />
          <div style="margin-top: 8px">
            <h4>事件日志:</h4>
            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #d9d9d9; padding: 8px; background: #fafafa;">
              <div v-for="(log, index) in eventLogs" :key="index" style="margin-bottom: 4px;">
                <span style="color: #666;">{{ log.time }}</span> -
                <span style="color: #1890ff;">{{ log.event }}</span>:
                {{ log.data }}
              </div>
            </div>
          </div>
        </div>

      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import RegionSelector from '@/components/common/RegionSelector/index.vue'

// 基本多选
const multiSelectValue = ref([])
const multiSelectRegions = ref([])

// 单选
const singleSelectValue = ref(null)
const singleSelectRegion = ref(null)

// 其他测试值
const noSearchValue = ref([])
const readonlyValue = ref([])
const disabledValue = ref([])
const actionButtonValue = ref([])
const methodTestValue = ref([])
const eventTestValue = ref([])

// 问题修复验证测试
const fixTestValue = ref([])
const fixTestRegions = ref([])

// 表单数据
const formData = reactive({
  supplierRegions: [],
  customerRegions: []
})

// 事件日志
const eventLogs = ref([])

// 组件引用
const regionSelectorRef = ref()

// 事件处理
const handleMultiChange = (value, regions) => {
  multiSelectRegions.value = regions
  console.log('多选变化:', value, regions)
}

const handleSingleChange = (value, region) => {
  singleSelectRegion.value = Array.isArray(region) ? region[0] : region
  console.log('单选变化:', value, region)
}

const handleActionButtonChange = (value, regions) => {
  console.log('带按钮选择变化:', value, regions)
  message.success(`选择了 ${regions.length} 个区域`)
}

const handleEventChange = (value, regions) => {
  addEventLog('change', `值: ${JSON.stringify(value)}, 区域数量: ${regions.length}`)
}

const handleEventSearch = (searchText) => {
  addEventLog('search', `搜索文本: ${searchText}`)
}

const handleEventClear = () => {
  addEventLog('clear', '清空选择')
}

// 添加事件日志
const addEventLog = (event, data) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  eventLogs.value.unshift({ time, event, data })
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 组件方法测试
const clearSelection = () => {
  regionSelectorRef.value?.clearSelection()
  message.info('已清空选择')
}

const getSelectedRegions = () => {
  const selected = regionSelectorRef.value?.getSelectedRegions()
  console.log('当前选中的区域:', selected)
  message.info(`当前选中 ${selected?.length || 0} 个区域`)
}

const reloadData = () => {
  regionSelectorRef.value?.reloadData()
  message.info('正在重新加载数据...')
}

// 设置只读值
const setReadonlyValue = () => {
  readonlyValue.value = ['1', '2'] // 假设的区域ID
  message.info('已设置只读值')
}

// 表单操作
const submitForm = () => {
  console.log('表单数据:', formData)
  message.success('表单提交成功')
}

const resetForm = () => {
  formData.supplierRegions = []
  formData.customerRegions = []
  message.info('表单已重置')
}

// 问题修复验证测试相关方法
const handleFixTestChange = (value, regions) => {
  fixTestRegions.value = regions
  console.log('修复测试变化:', value, regions)
  addEventLog('fix-test-change', `选中 ${regions.length} 个区域`)
}

const setTestData = () => {
  // 设置一些测试数据（使用字符串类型的ID）
  fixTestValue.value = ['1947278275361439745', '1947278275361439746']
  message.info('已设置测试数据，验证数据回显是否正常')
}

const clearTestData = () => {
  fixTestValue.value = []
  fixTestRegions.value = []
  message.info('已清空测试数据')
}
</script>

<style scoped>
h3 {
  color: #1890ff;
  margin-bottom: 16px;
}

h4 {
  margin-bottom: 8px;
  font-size: 14px;
}
</style>
