package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存历史实体类
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@TableName(value = "erp_inventory_history", autoResultMap = true)
@Data
public class InventoryHistory  {

    /**
     * 历史ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ChineseDescription("历史ID")
    private Long id;

    /**
     * 商品ID
     */
    @TableField("product_id")
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 操作类型：IN(入库)、OUT(出库)、ADJUST(调整)、SALE(销售)
     */
    @TableField("operation_type")
    @ChineseDescription("操作类型")
    private String operationType;

    /**
     * 数量变化
     */
    @TableField("quantity_change")
    @ChineseDescription("数量变化")
    private BigDecimal quantityChange;

    /**
     * 操作前库存
     */
    @TableField("stock_before")
    @ChineseDescription("操作前库存")
    private BigDecimal stockBefore;

    /**
     * 操作后库存
     */
    @TableField("stock_after")
    @ChineseDescription("操作后库存")
    private BigDecimal stockAfter;

    /**
     * 关联单据ID
     */
    @TableField("reference_id")
    @ChineseDescription("关联单据ID")
    private Long referenceId;

    /**
     * 关联单据类型：PURCHASE_ORDER(采购入库单)、SALE_ORDER(销售单)、ADJUST_ORDER(调整单)
     */
    @TableField("reference_type")
    @ChineseDescription("关联单据类型")
    private String referenceType;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    @ChineseDescription("操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作人员ID
     */
    @TableField("operation_user")
    @ChineseDescription("操作人员ID")
    private Long operationUser;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 关联的商品信息（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的商品信息")
    private ErpProduct product;

    /**
     * 关联的供应商信息（非数据库字段）
     */
    @TableField(exist = false)
    @ChineseDescription("关联的供应商信息")
    private ErpSupplier supplier;

}