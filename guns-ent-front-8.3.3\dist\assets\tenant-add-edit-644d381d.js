import{r as l,o as U,a as w,f as B,w as D,d as M,m as R,M as T}from"./index-18a1ea24.js";import A from"./tenant-form-a280149b.js";import{T as f}from"./TenantApi-e23e3174.js";import{g as k,G as C}from"./time-util-d1d9a3df.js";import{a as N}from"./FileApi-418f4d35.js";/* empty css              *//* empty css              *//* empty css              */const H={__name:"tenant-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(y,{emit:_}){const d=y,m=_,i=l(!1),n=l(!1),e=l({iconList:[],statusFlag:1,activeDate:k()+" 00:00:00",expireDate:C(k(),1)+" 00:00:00",tenantLinkList:[]}),c=l(null);U(()=>{d.data?(n.value=!0,h()):n.value=!1});const h=()=>{f.detail({tenantId:d.data.tenantId}).then(a=>{e.value=Object.assign({},a),e.value.iconList=[],x("iconList",a.tenantLogo),e.value.tenantLinkList||(e.value.tenantLinkList=[])})},x=(a,t)=>{N.getAntdVInfo({fileId:t}).then(s=>{s.uid=t,e.value[a]=[s]})},r=a=>{m("update:visible",a)},F=async()=>{c.value.$refs.formRef.validate().then(async a=>{var t,s,o,v,p,L,g,b;if(a){(t=e.value.iconList)!=null&&t.length&&(e.value.tenantLogo=(v=(o=(s=e.value.iconList[0])==null?void 0:s.response)==null?void 0:o.data)!=null&&v.fileId?(g=(L=(p=e.value.iconList[0])==null?void 0:p.response)==null?void 0:L.data)==null?void 0:g.fileId:(b=e.value.iconList[0])==null?void 0:b.uid),i.value=!0;let u=null;n.value?u=f.edit(e.value):u=f.add(e.value),u.then(async I=>{i.value=!1,R.success(I.message),r(!1),m("done")}).catch(()=>{i.value=!1})}})};return(a,t)=>{const s=T;return w(),B(s,{width:900,maskClosable:!1,visible:d.visible,"confirm-loading":i.value,forceRender:!0,title:n.value?"\u7F16\u8F91\u79DF\u6237":"\u65B0\u5EFA\u79DF\u6237","body-style":{paddingBottom:"8px"},"onUpdate:visible":r,onOk:F,class:"common-modal",onClose:t[1]||(t[1]=o=>r(!1))},{default:D(()=>[M(A,{form:e.value,"onUpdate:form":t[0]||(t[0]=o=>e.value=o),ref_key:"tenantFormRef",ref:c,isUpdate:n.value},null,8,["form","isUpdate"])]),_:1},8,["visible","confirm-loading","title"])}}};export{H as default};
