package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 客户请求参数
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpCustomerRequest extends BaseRequest {

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = {edit.class, delete.class, detail.class, updateStatus.class})
    @ChineseDescription("客户ID")
    private Long customerId;

    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空", groups = {add.class, edit.class})
    @ChineseDescription("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("客户名称")
    private String customerName;

    /**
     * 客户简称
     */
    @ChineseDescription("客户简称")
    private String customerShortName;

    /**
     * 客户类型（ENTERPRISE-企业，INDIVIDUAL-个人，RETAIL-零售）
     */
    @NotBlank(message = "客户类型不能为空", groups = {add.class, edit.class})
    @ChineseDescription("客户类型")
    private String customerType;

    /**
     * 客户等级（DIAMOND-钻石，GOLD-黄金，SILVER-白银，BRONZE-青铜）
     */
    @ChineseDescription("客户等级")
    private String customerLevel;

    /**
     * 所属区域ID
     */
    @ChineseDescription("所属区域ID")
    private Long regionId;

    /**
     * 联系人
     */
    @ChineseDescription("联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @ChineseDescription("联系电话")
    private String contactPhone;

    /**
     * 手机号码
     */
    @ChineseDescription("手机号码")
    private String contactMobile;

    /**
     * 邮箱地址
     */
    @ChineseDescription("邮箱地址")
    private String contactEmail;

    /**
     * 联系地址
     */
    @ChineseDescription("联系地址")
    private String contactAddress;

    /**
     * 营业执照号
     */
    @ChineseDescription("营业执照号")
    private String businessLicenseNo;

    /**
     * 税务登记号
     */
    @ChineseDescription("税务登记号")
    private String taxNo;

    /**
     * 开户银行
     */
    @ChineseDescription("开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @ChineseDescription("银行账号")
    private String bankAccount;

    /**
     * 信用额度
     */
    @ChineseDescription("信用额度")
    private BigDecimal creditLimit;

    /**
     * 已用额度
     */
    @ChineseDescription("已用额度")
    private BigDecimal usedCredit;

    /**
     * 账期天数
     */
    @ChineseDescription("账期天数")
    private Integer paymentTerms;

    /**
     * 状态（ACTIVE-正常，INACTIVE-停用，FROZEN-冻结）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 客户ID列表（批量操作用）
     */
    @ChineseDescription("客户ID列表")
    private List<Long> customerIdList;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：更新状态
     */
    public @interface updateStatus {
    }

}
