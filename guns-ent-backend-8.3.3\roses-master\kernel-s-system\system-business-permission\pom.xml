<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-s-system</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>system-business-permission</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--ids解析工具-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>kernel-d-tree</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--获取拖拽验证码图片-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>security-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--日志相关api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>log-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 数据源容器 -->
        <!-- 用来获取当前的数据源的类型 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>ds-container-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- 事件模块 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>event-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--参数校验模块的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>validator-api-table-unique</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--文件api-->
        <!--应用图片相关的操作-->
        <!--获取登录用户的头像url拼接-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>file-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--基础核心业务api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源缓存-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-api</artifactId>
            <version>${roses.version}</version>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <optional>true</optional>
        </dependency>

        <!--数据库sdk-->
        <!--数据库dao框架-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

</project>
