import{_ as P,r as F,L as k,X as A,a as h,c as y,b as s,d as t,I as C,g as o,t as d,F as V,e as I,w as l,h as w,at as E,ae as L,m as O,y as $,B as j}from"./index-18a1ea24.js";/* empty css              */const D={class:"cash-payment"},M={class:"cash-input-section"},R={class:"input-group"},S={class:"input-label"},T={class:"payment-info"},U={class:"info-item"},X={class:"info-value"},G={class:"info-item"},H={class:"info-value received"},J={class:"info-item"},K={class:"info-value"},Q={class:"quick-amounts"},W={class:"quick-title"},Y={class:"amount-buttons"},Z={key:0,class:"change-display"},nn={class:"change-header"},sn={class:"change-content"},en={class:"change-amount"},tn={class:"change-value"},an={class:"cash-actions"},on=Object.assign({name:"CashPayment"},{__name:"CashPayment",props:{paymentAmount:{type:Number,required:!0},initialAmount:{type:Number,default:0},loading:{type:Boolean,default:!1}},emits:["confirm-payment","cancel-payment","amount-change"],setup(m,{emit:x}){const r=m,p=x,a=F(0),q=[10,20,50,100,200,500],c=k(()=>Math.max(0,a.value-r.paymentAmount)),f=k(()=>a.value>=r.paymentAmount),_=e=>(e||0).toFixed(2),v=e=>{a.value=e,g(e)},z=()=>{a.value=0,g(0)},g=e=>{p("amount-change",{receivedAmount:e,changeAmount:c.value,canConfirm:f.value})},b=()=>{if(!f.value){O.warning("\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u5B9E\u6536\u91D1\u989D");return}p("confirm-payment",{receivedAmount:a.value,changeAmount:c.value})},B=()=>{p("cancel-payment")};return A(()=>r.initialAmount,e=>{e>0&&v(e)},{immediate:!0}),A(()=>r.paymentAmount,e=>{a.value<e&&v(e)}),(e,n)=>{const N=$,u=j;return h(),y("div",D,[s("div",M,[s("div",R,[s("div",S,[t(C,{iconClass:"icon-cash"}),n[2]||(n[2]=o(" \u5B9E\u6536\u91D1\u989D "))]),t(N,{value:a.value,"onUpdate:value":n[0]||(n[0]=i=>a.value=i),min:0,precision:2,step:.01,size:"large",placeholder:"\u8BF7\u8F93\u5165\u5B9E\u6536\u91D1\u989D",class:"cash-input",onChange:g,onPressEnter:b},null,8,["value"])]),s("div",T,[s("div",U,[n[3]||(n[3]=s("span",{class:"info-label"},"\u5E94\u4ED8\u91D1\u989D:",-1)),s("span",X,"\xA5"+d(_(m.paymentAmount)),1)]),s("div",G,[n[4]||(n[4]=s("span",{class:"info-label"},"\u5B9E\u6536\u91D1\u989D:",-1)),s("span",H,"\xA5"+d(_(a.value)),1)]),s("div",J,[n[5]||(n[5]=s("span",{class:"info-label"},"\u627E\u96F6\u91D1\u989D:",-1)),s("span",K,"\xA5"+d(_(c.value)),1)])])]),s("div",Q,[s("div",W,[t(C,{iconClass:"icon-quick"}),n[6]||(n[6]=o(" \u5FEB\u6377\u91D1\u989D "))]),s("div",Y,[(h(),y(V,null,I(q,i=>t(u,{key:i,size:"small",onClick:ln=>v(i),class:"quick-amount-btn"},{default:l(()=>[o(" \xA5"+d(i),1)]),_:2},1032,["onClick"])),64)),t(u,{size:"small",onClick:n[1]||(n[1]=i=>v(m.paymentAmount)),class:"quick-amount-btn exact"},{default:l(()=>n[7]||(n[7]=[o(" \u521A\u597D ")])),_:1,__:[7]}),t(u,{size:"small",onClick:z,class:"quick-amount-btn clear"},{default:l(()=>n[8]||(n[8]=[o(" \u6E05\u7A7A ")])),_:1,__:[8]})])]),c.value>0?(h(),y("div",Z,[s("div",nn,[t(C,{iconClass:"icon-change"}),n[9]||(n[9]=o(" \u627E\u96F6\u4FE1\u606F "))]),s("div",sn,[s("div",en,[n[10]||(n[10]=s("span",{class:"change-label"},"\u627E\u96F6\u91D1\u989D:",-1)),s("span",tn,"\xA5"+d(_(c.value)),1)])])])):w("",!0),s("div",an,[t(u,{size:"large",onClick:B,class:"action-btn cancel"},{default:l(()=>n[11]||(n[11]=[o(" \u53D6\u6D88 ")])),_:1,__:[11]}),t(u,{type:"primary",size:"large",onClick:b,disabled:!f.value,loading:m.loading,class:"action-btn confirm"},{icon:l(()=>[t(E(L))]),default:l(()=>[n[12]||(n[12]=o(" \u786E\u8BA4\u6536\u6B3E "))]),_:1,__:[12]},8,["disabled","loading"])])])}}}),dn=P(on,[["__scopeId","data-v-c905453f"]]);export{dn as default};
