package cn.stylefeng.roses.ent.saas.modular.auth.service;

import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 租户-功能包服务类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
public interface TenantLinkService extends IService<TenantLink> {

    /**
     * 新增租户-功能包
     *
     * @param tenantLinkRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void add(TenantLinkRequest tenantLinkRequest);

    /**
     * 删除租户-功能包
     *
     * @param tenantLinkRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void del(TenantLinkRequest tenantLinkRequest);

    /**
     * 批量删除租户-功能包
     *
     * @param tenantLinkRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void batchDelete(TenantLinkRequest tenantLinkRequest);

    /**
     * 编辑租户-功能包
     *
     * @param tenantLinkRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    void edit(TenantLinkRequest tenantLinkRequest);

    /**
     * 查询详情租户-功能包
     *
     * @param tenantLinkRequest 请求参数
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    TenantLink detail(TenantLinkRequest tenantLinkRequest);

    /**
     * 获取租户-功能包列表
     *
     * @param tenantLinkRequest 请求参数
     * @return List<TenantLink>  返回结果
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    List<TenantLink> findList(TenantLinkRequest tenantLinkRequest);

    /**
     * 获取租户-功能包分页列表
     *
     * @param tenantLinkRequest 请求参数
     * @return PageResult<TenantLink>   返回结果
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    PageResult<TenantLink> findPage(TenantLinkRequest tenantLinkRequest);

    /**
     * 根据功能包id，删除功能包绑定的租户关联信息
     *
     * <AUTHOR>
     * @since 2024-01-21 16:21
     */
    void removeByPackageId(Long packageId);

    /**
     * 删除租户绑定的功能包信息
     *
     * <AUTHOR>
     * @since 2024-01-21 16:21
     */
    void removeByTenantId(Long tenantId);

    /**
     * 更新租户和功能包的关联关系
     *
     * <AUTHOR>
     * @since 2024-01-21 18:02
     */
    void updateTenantAuthPackageLink(Long tenantId, List<TenantLink> tenantLinkList);

    /**
     * 获取功能包绑定的租户id集合
     *
     * <AUTHOR>
     * @since 2024-01-23 12:41
     */
    List<Long> getPackageTenantIdList(Long packageId);

    /**
     * 获取租户绑定的功能包id集合
     *
     * <AUTHOR>
     * @since 2024-01-23 12:55
     */
    List<Long> getTenantPackageIdList(Long tenantId);

}
