<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="1.775" tests="14" errors="10" skipped="0" failures="2">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\target\test-classes;D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\target\classes;D:\JavaWorking\Maven\repo\com\javaguns\erp-api\8.3.3\erp-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-api\8.3.3\db-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-api\8.3.3\config-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.7\mybatis-plus-spring-boot3-starter-3.5.7.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus\3.5.7\mybatis-plus-3.5.7.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-core\3.5.7\mybatis-plus-core-3.5.7.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-extension\3.5.7\mybatis-plus-extension-3.5.7.jar;D:\JavaWorking\Maven\repo\org\mybatis\mybatis\3.5.16\mybatis-3.5.16.jar;D:\JavaWorking\Maven\repo\com\github\jsqlparser\jsqlparser\4.9\jsqlparser-4.9.jar;D:\JavaWorking\Maven\repo\org\mybatis\mybatis-spring\3.0.3\mybatis-spring-3.0.3.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.7\mybatis-plus-spring-boot-autoconfigure-3.5.7.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-jdbc\3.2.10\spring-boot-starter-jdbc-3.2.10.jar;D:\JavaWorking\Maven\repo\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-jdbc\6.1.13\spring-jdbc-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-tx\6.1.13\spring-tx-6.1.13.jar;D:\JavaWorking\Maven\repo\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;D:\JavaWorking\Maven\repo\com\alibaba\druid\1.2.22\druid-1.2.22.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\validator-api\8.3.3\validator-api-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-validation\3.2.10\spring-boot-starter-validation-3.2.10.jar;D:\JavaWorking\Maven\repo\org\apache\tomcat\embed\tomcat-embed-el\10.1.30\tomcat-embed-el-10.1.30.jar;D:\JavaWorking\Maven\repo\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\JavaWorking\Maven\repo\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\JavaWorking\Maven\repo\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\JavaWorking\Maven\repo\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-spring-boot-starter\8.3.3\system-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-business-hr\8.3.3\system-business-hr-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\kernel-d-tree\8.3.3\kernel-d-tree-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-api\8.3.3\ds-container-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\event-spring-boot-starter\8.3.3\event-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\event-sdk\8.3.3\event-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\event-api\8.3.3\event-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-api\8.3.3\system-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-api\8.3.3\dict-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-api\8.3.3\file-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\scanner-api\8.3.3\scanner-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-api\8.3.3\log-api-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-web\3.2.10\spring-boot-starter-web-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-json\3.2.10\spring-boot-starter-json-3.2.10.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-tomcat\3.2.10\spring-boot-starter-tomcat-3.2.10.jar;D:\JavaWorking\Maven\repo\org\apache\tomcat\embed\tomcat-embed-core\10.1.30\tomcat-embed-core-10.1.30.jar;D:\JavaWorking\Maven\repo\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.30\tomcat-embed-websocket-10.1.30.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-webmvc\6.1.13\spring-webmvc-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-expression\6.1.13\spring-expression-6.1.13.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\validator-api-table-unique\8.3.3\validator-api-table-unique-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-business-permission\8.3.3\system-business-permission-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-api\8.3.3\security-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\cache-api\8.3.3\cache-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-business-portal\8.3.3\system-business-portal-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-api\8.3.3\wrapper-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-spring-boot-starter\8.3.3\config-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-business\8.3.3\config-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-sdk-redis\8.3.3\config-sdk-redis-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-sdk-map\8.3.3\config-sdk-map-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\auth-spring-boot-starter\8.3.3\auth-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\auth-sdk\8.3.3\auth-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\demo-api\8.3.3\demo-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-api\8.3.3\timer-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\jwt-sdk\8.3.3\jwt-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\jwt-api\8.3.3\jwt-api-8.3.3.jar;D:\JavaWorking\Maven\repo\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;D:\JavaWorking\Maven\repo\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\JavaWorking\Maven\repo\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\validator-spring-boot-starter\8.3.3\validator-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-spring-boot-starter\8.3.3\security-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-xss\8.3.3\security-sdk-xss-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-clear-threadlocal\8.3.3\security-sdk-clear-threadlocal-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-captcha\8.3.3\security-sdk-captcha-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-count\8.3.3\security-sdk-count-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-request-encrypt-and-decode\8.3.3\security-sdk-request-encrypt-and-decode-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-guomi\8.3.3\security-sdk-guomi-8.3.3.jar;D:\JavaWorking\Maven\repo\org\bouncycastle\bcprov-jdk15to18\1.78.1\bcprov-jdk15to18-1.78.1.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-black-white\8.3.3\security-sdk-black-white-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-spring-boot-starter\8.3.3\file-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-business\8.3.3\file-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-sdk-local\8.3.3\file-sdk-local-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-spring-boot-starter\8.3.3\dict-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-business\8.3.3\dict-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\pinyin-spring-boot-starter\8.3.3\pinyin-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\pinyin-sdk-pinyin4j\8.3.3\pinyin-sdk-pinyin4j-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\pinyin-api\8.3.3\pinyin-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\belerweb\pinyin4j\2.5.0\pinyin4j-2.5.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-city-business\8.3.3\dict-city-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-spring-boot-starter\8.3.3\log-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business\8.3.3\log-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business-security\8.3.3\log-business-security-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business-requestapi\8.3.3\log-business-requestapi-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business-login-log\8.3.3\log-business-login-log-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-spring-boot-starter\8.3.3\timer-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-business\8.3.3\timer-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-sdk-hutool\8.3.3\timer-sdk-hutool-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-spring-boot-starter\8.3.3\wrapper-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-sdk\8.3.3\wrapper-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-field-sdk\8.3.3\wrapper-field-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-spring-boot-starter\8.3.3\ds-container-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-business\8.3.3\ds-container-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-sdk\8.3.3\ds-container-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\group-spring-boot-starter\8.3.3\group-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\group-business\8.3.3\group-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\group-api\8.3.3\group-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\monitor-spring-boot-starter\8.3.3\monitor-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\monitor-business-system-info\8.3.3\monitor-business-system-info-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\monitor-api\8.3.3\monitor-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\github\oshi\oshi-core\5.7.1\oshi-core-5.7.1.jar;D:\JavaWorking\Maven\repo\net\java\dev\jna\jna\5.8.0\jna-5.8.0.jar;D:\JavaWorking\Maven\repo\net\java\dev\jna\jna-platform\5.8.0\jna-platform-5.8.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\stat-spring-boot-starter\8.3.3\stat-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\stat-business\8.3.3\stat-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\stat-api\8.3.3\stat-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\kernel-a-rule\8.3.3\kernel-a-rule-8.3.3.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-annotation\3.5.7\mybatis-plus-annotation-3.5.7.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;D:\JavaWorking\Maven\repo\cn\hutool\hutool-all\5.8.29\hutool-all-5.8.29.jar;D:\JavaWorking\Maven\repo\org\projectlombok\lombok\1.18.34\lombok-1.18.34.jar;D:\JavaWorking\Maven\repo\com\alibaba\fastjson\2.0.52\fastjson-2.0.52.jar;D:\JavaWorking\Maven\repo\com\alibaba\fastjson2\fastjson2-extension\2.0.52\fastjson2-extension-2.0.52.jar;D:\JavaWorking\Maven\repo\com\alibaba\fastjson2\fastjson2\2.0.52\fastjson2-2.0.52.jar;D:\JavaWorking\Maven\repo\com\github\whvcse\easy-captcha\1.6.2\easy-captcha-1.6.2.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-spring-boot-starter\8.3.3\db-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-sdk-mp\8.3.3\db-sdk-mp-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\auth-api\8.3.3\auth-api-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-aop\3.2.10\spring-boot-starter-aop-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-aop\6.1.13\spring-aop-6.1.13.jar;D:\JavaWorking\Maven\repo\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-sdk-flyway\8.3.3\db-sdk-flyway-8.3.3.jar;D:\JavaWorking\Maven\repo\org\flywaydb\flyway-core\7.1.1\flyway-core-7.1.1.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-configuration-processor\3.2.10\spring-boot-configuration-processor-3.2.10.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\scanner-spring-boot-starter\8.3.3\scanner-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\scanner-sdk-scanner\8.3.3\scanner-sdk-scanner-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-web\6.1.13\spring-web-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-beans\6.1.13\spring-beans-6.1.13.jar;D:\JavaWorking\Maven\repo\io\micrometer\micrometer-observation\1.12.10\micrometer-observation-1.12.10.jar;D:\JavaWorking\Maven\repo\io\micrometer\micrometer-commons\1.12.10\micrometer-commons-1.12.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-test\3.2.10\spring-boot-starter-test-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter\3.2.10\spring-boot-starter-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot\3.2.10\spring-boot-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-context\6.1.13\spring-context-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-autoconfigure\3.2.10\spring-boot-autoconfigure-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-logging\3.2.10\spring-boot-starter-logging-3.2.10.jar;D:\JavaWorking\Maven\repo\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;D:\JavaWorking\Maven\repo\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;D:\JavaWorking\Maven\repo\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\JavaWorking\Maven\repo\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\JavaWorking\Maven\repo\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;D:\JavaWorking\Maven\repo\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\JavaWorking\Maven\repo\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-test\3.2.10\spring-boot-test-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-test-autoconfigure\3.2.10\spring-boot-test-autoconfigure-3.2.10.jar;D:\JavaWorking\Maven\repo\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;D:\JavaWorking\Maven\repo\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\JavaWorking\Maven\repo\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\JavaWorking\Maven\repo\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\JavaWorking\Maven\repo\net\minidev\json-smart\2.5.1\json-smart-2.5.1.jar;D:\JavaWorking\Maven\repo\net\minidev\accessors-smart\2.5.1\accessors-smart-2.5.1.jar;D:\JavaWorking\Maven\repo\org\ow2\asm\asm\9.6\asm-9.6.jar;D:\JavaWorking\Maven\repo\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\JavaWorking\Maven\repo\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;D:\JavaWorking\Maven\repo\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;D:\JavaWorking\Maven\repo\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter\5.10.3\junit-jupiter-5.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter-api\5.10.3\junit-jupiter-api-5.10.3.jar;D:\JavaWorking\Maven\repo\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter-params\5.10.3\junit-jupiter-params-5.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter-engine\5.10.3\junit-jupiter-engine-5.10.3.jar;D:\JavaWorking\Maven\repo\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;D:\JavaWorking\Maven\repo\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;D:\JavaWorking\Maven\repo\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\JavaWorking\Maven\repo\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\JavaWorking\Maven\repo\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;D:\JavaWorking\Maven\repo\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-core\6.1.13\spring-core-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-jcl\6.1.13\spring-jcl-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-test\6.1.13\spring-test-6.1.13.jar;D:\JavaWorking\Maven\repo\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\JavaWorking\Maven\repo\com\h2database\h2\2.2.224\h2-2.2.224.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite\1.10.3\junit-platform-suite-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite-api\1.10.3\junit-platform-suite-api-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-commons\1.10.3\junit-platform-commons-1.10.3.jar;D:\JavaWorking\Maven\repo\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite-engine\1.10.3\junit-platform-suite-engine-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-engine\1.10.3\junit-platform-engine-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite-commons\1.10.3\junit-platform-suite-commons-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-launcher\1.10.3\junit-platform-launcher-1.10.3.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\MyProgramFiles\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire11522093120363434428\surefirebooter-20250802173507301_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire11522093120363434428 2025-08-02T17-35-07_157-jvmRun1 surefire-20250802173507301_1tmp surefire_0-20250802173507301_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="UnifiedProductQueryServiceTest"/>
    <property name="surefire.test.class.path" value="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\target\test-classes;D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\target\classes;D:\JavaWorking\Maven\repo\com\javaguns\erp-api\8.3.3\erp-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-api\8.3.3\db-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-api\8.3.3\config-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.7\mybatis-plus-spring-boot3-starter-3.5.7.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus\3.5.7\mybatis-plus-3.5.7.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-core\3.5.7\mybatis-plus-core-3.5.7.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-extension\3.5.7\mybatis-plus-extension-3.5.7.jar;D:\JavaWorking\Maven\repo\org\mybatis\mybatis\3.5.16\mybatis-3.5.16.jar;D:\JavaWorking\Maven\repo\com\github\jsqlparser\jsqlparser\4.9\jsqlparser-4.9.jar;D:\JavaWorking\Maven\repo\org\mybatis\mybatis-spring\3.0.3\mybatis-spring-3.0.3.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.7\mybatis-plus-spring-boot-autoconfigure-3.5.7.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-jdbc\3.2.10\spring-boot-starter-jdbc-3.2.10.jar;D:\JavaWorking\Maven\repo\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-jdbc\6.1.13\spring-jdbc-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-tx\6.1.13\spring-tx-6.1.13.jar;D:\JavaWorking\Maven\repo\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;D:\JavaWorking\Maven\repo\com\alibaba\druid\1.2.22\druid-1.2.22.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\validator-api\8.3.3\validator-api-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-validation\3.2.10\spring-boot-starter-validation-3.2.10.jar;D:\JavaWorking\Maven\repo\org\apache\tomcat\embed\tomcat-embed-el\10.1.30\tomcat-embed-el-10.1.30.jar;D:\JavaWorking\Maven\repo\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\JavaWorking\Maven\repo\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\JavaWorking\Maven\repo\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\JavaWorking\Maven\repo\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-spring-boot-starter\8.3.3\system-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-business-hr\8.3.3\system-business-hr-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\kernel-d-tree\8.3.3\kernel-d-tree-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-api\8.3.3\ds-container-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\event-spring-boot-starter\8.3.3\event-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\event-sdk\8.3.3\event-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\event-api\8.3.3\event-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-api\8.3.3\system-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-api\8.3.3\dict-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-api\8.3.3\file-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\scanner-api\8.3.3\scanner-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-api\8.3.3\log-api-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-web\3.2.10\spring-boot-starter-web-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-json\3.2.10\spring-boot-starter-json-3.2.10.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-tomcat\3.2.10\spring-boot-starter-tomcat-3.2.10.jar;D:\JavaWorking\Maven\repo\org\apache\tomcat\embed\tomcat-embed-core\10.1.30\tomcat-embed-core-10.1.30.jar;D:\JavaWorking\Maven\repo\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.30\tomcat-embed-websocket-10.1.30.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-webmvc\6.1.13\spring-webmvc-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-expression\6.1.13\spring-expression-6.1.13.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\validator-api-table-unique\8.3.3\validator-api-table-unique-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-business-permission\8.3.3\system-business-permission-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-api\8.3.3\security-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\cache-api\8.3.3\cache-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\system-business-portal\8.3.3\system-business-portal-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-api\8.3.3\wrapper-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-spring-boot-starter\8.3.3\config-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-business\8.3.3\config-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-sdk-redis\8.3.3\config-sdk-redis-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\config-sdk-map\8.3.3\config-sdk-map-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\auth-spring-boot-starter\8.3.3\auth-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\auth-sdk\8.3.3\auth-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\demo-api\8.3.3\demo-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-api\8.3.3\timer-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\jwt-sdk\8.3.3\jwt-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\jwt-api\8.3.3\jwt-api-8.3.3.jar;D:\JavaWorking\Maven\repo\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;D:\JavaWorking\Maven\repo\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\JavaWorking\Maven\repo\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\validator-spring-boot-starter\8.3.3\validator-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-spring-boot-starter\8.3.3\security-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-xss\8.3.3\security-sdk-xss-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-clear-threadlocal\8.3.3\security-sdk-clear-threadlocal-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-captcha\8.3.3\security-sdk-captcha-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-count\8.3.3\security-sdk-count-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-request-encrypt-and-decode\8.3.3\security-sdk-request-encrypt-and-decode-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-guomi\8.3.3\security-sdk-guomi-8.3.3.jar;D:\JavaWorking\Maven\repo\org\bouncycastle\bcprov-jdk15to18\1.78.1\bcprov-jdk15to18-1.78.1.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\security-sdk-black-white\8.3.3\security-sdk-black-white-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-spring-boot-starter\8.3.3\file-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-business\8.3.3\file-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\file-sdk-local\8.3.3\file-sdk-local-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-spring-boot-starter\8.3.3\dict-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-business\8.3.3\dict-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\pinyin-spring-boot-starter\8.3.3\pinyin-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\pinyin-sdk-pinyin4j\8.3.3\pinyin-sdk-pinyin4j-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\pinyin-api\8.3.3\pinyin-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\belerweb\pinyin4j\2.5.0\pinyin4j-2.5.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\dict-city-business\8.3.3\dict-city-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-spring-boot-starter\8.3.3\log-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business\8.3.3\log-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business-security\8.3.3\log-business-security-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business-requestapi\8.3.3\log-business-requestapi-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\log-business-login-log\8.3.3\log-business-login-log-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-spring-boot-starter\8.3.3\timer-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-business\8.3.3\timer-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\timer-sdk-hutool\8.3.3\timer-sdk-hutool-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-spring-boot-starter\8.3.3\wrapper-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-sdk\8.3.3\wrapper-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\wrapper-field-sdk\8.3.3\wrapper-field-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-spring-boot-starter\8.3.3\ds-container-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-business\8.3.3\ds-container-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\ds-container-sdk\8.3.3\ds-container-sdk-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\group-spring-boot-starter\8.3.3\group-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\group-business\8.3.3\group-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\group-api\8.3.3\group-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\monitor-spring-boot-starter\8.3.3\monitor-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\monitor-business-system-info\8.3.3\monitor-business-system-info-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\monitor-api\8.3.3\monitor-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\github\oshi\oshi-core\5.7.1\oshi-core-5.7.1.jar;D:\JavaWorking\Maven\repo\net\java\dev\jna\jna\5.8.0\jna-5.8.0.jar;D:\JavaWorking\Maven\repo\net\java\dev\jna\jna-platform\5.8.0\jna-platform-5.8.0.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\stat-spring-boot-starter\8.3.3\stat-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\stat-business\8.3.3\stat-business-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\stat-api\8.3.3\stat-api-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\kernel-a-rule\8.3.3\kernel-a-rule-8.3.3.jar;D:\JavaWorking\Maven\repo\com\baomidou\mybatis-plus-annotation\3.5.7\mybatis-plus-annotation-3.5.7.jar;D:\JavaWorking\Maven\repo\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;D:\JavaWorking\Maven\repo\cn\hutool\hutool-all\5.8.29\hutool-all-5.8.29.jar;D:\JavaWorking\Maven\repo\org\projectlombok\lombok\1.18.34\lombok-1.18.34.jar;D:\JavaWorking\Maven\repo\com\alibaba\fastjson\2.0.52\fastjson-2.0.52.jar;D:\JavaWorking\Maven\repo\com\alibaba\fastjson2\fastjson2-extension\2.0.52\fastjson2-extension-2.0.52.jar;D:\JavaWorking\Maven\repo\com\alibaba\fastjson2\fastjson2\2.0.52\fastjson2-2.0.52.jar;D:\JavaWorking\Maven\repo\com\github\whvcse\easy-captcha\1.6.2\easy-captcha-1.6.2.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-spring-boot-starter\8.3.3\db-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-sdk-mp\8.3.3\db-sdk-mp-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\auth-api\8.3.3\auth-api-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-aop\3.2.10\spring-boot-starter-aop-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-aop\6.1.13\spring-aop-6.1.13.jar;D:\JavaWorking\Maven\repo\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\db-sdk-flyway\8.3.3\db-sdk-flyway-8.3.3.jar;D:\JavaWorking\Maven\repo\org\flywaydb\flyway-core\7.1.1\flyway-core-7.1.1.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-configuration-processor\3.2.10\spring-boot-configuration-processor-3.2.10.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\scanner-spring-boot-starter\8.3.3\scanner-spring-boot-starter-8.3.3.jar;D:\JavaWorking\Maven\repo\com\javaguns\roses\scanner-sdk-scanner\8.3.3\scanner-sdk-scanner-8.3.3.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-web\6.1.13\spring-web-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-beans\6.1.13\spring-beans-6.1.13.jar;D:\JavaWorking\Maven\repo\io\micrometer\micrometer-observation\1.12.10\micrometer-observation-1.12.10.jar;D:\JavaWorking\Maven\repo\io\micrometer\micrometer-commons\1.12.10\micrometer-commons-1.12.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-test\3.2.10\spring-boot-starter-test-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter\3.2.10\spring-boot-starter-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot\3.2.10\spring-boot-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-context\6.1.13\spring-context-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-autoconfigure\3.2.10\spring-boot-autoconfigure-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-starter-logging\3.2.10\spring-boot-starter-logging-3.2.10.jar;D:\JavaWorking\Maven\repo\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;D:\JavaWorking\Maven\repo\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;D:\JavaWorking\Maven\repo\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\JavaWorking\Maven\repo\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\JavaWorking\Maven\repo\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;D:\JavaWorking\Maven\repo\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\JavaWorking\Maven\repo\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-test\3.2.10\spring-boot-test-3.2.10.jar;D:\JavaWorking\Maven\repo\org\springframework\boot\spring-boot-test-autoconfigure\3.2.10\spring-boot-test-autoconfigure-3.2.10.jar;D:\JavaWorking\Maven\repo\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;D:\JavaWorking\Maven\repo\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\JavaWorking\Maven\repo\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\JavaWorking\Maven\repo\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\JavaWorking\Maven\repo\net\minidev\json-smart\2.5.1\json-smart-2.5.1.jar;D:\JavaWorking\Maven\repo\net\minidev\accessors-smart\2.5.1\accessors-smart-2.5.1.jar;D:\JavaWorking\Maven\repo\org\ow2\asm\asm\9.6\asm-9.6.jar;D:\JavaWorking\Maven\repo\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\JavaWorking\Maven\repo\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;D:\JavaWorking\Maven\repo\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;D:\JavaWorking\Maven\repo\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter\5.10.3\junit-jupiter-5.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter-api\5.10.3\junit-jupiter-api-5.10.3.jar;D:\JavaWorking\Maven\repo\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter-params\5.10.3\junit-jupiter-params-5.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\jupiter\junit-jupiter-engine\5.10.3\junit-jupiter-engine-5.10.3.jar;D:\JavaWorking\Maven\repo\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;D:\JavaWorking\Maven\repo\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;D:\JavaWorking\Maven\repo\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\JavaWorking\Maven\repo\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\JavaWorking\Maven\repo\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;D:\JavaWorking\Maven\repo\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-core\6.1.13\spring-core-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-jcl\6.1.13\spring-jcl-6.1.13.jar;D:\JavaWorking\Maven\repo\org\springframework\spring-test\6.1.13\spring-test-6.1.13.jar;D:\JavaWorking\Maven\repo\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\JavaWorking\Maven\repo\com\h2database\h2\2.2.224\h2-2.2.224.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite\1.10.3\junit-platform-suite-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite-api\1.10.3\junit-platform-suite-api-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-commons\1.10.3\junit-platform-commons-1.10.3.jar;D:\JavaWorking\Maven\repo\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite-engine\1.10.3\junit-platform-suite-engine-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-engine\1.10.3\junit-platform-engine-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-suite-commons\1.10.3\junit-platform-suite-commons-1.10.3.jar;D:\JavaWorking\Maven\repo\org\junit\platform\junit-platform-launcher\1.10.3\junit-platform-launcher-1.10.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\MyProgramFiles\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire11522093120363434428\surefirebooter-20250802173507301_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.8+9-LTS-211"/>
    <property name="user.name" value="15763"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\JavaWorking\Maven\repo"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.8"/>
    <property name="user.dir" value="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\MyProgramFiles\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;D:\MyProgramFiles\Microsoft VS Code\bin;D:\MyProgramFiles\nvm;D:\MyProgramFiles\nodejs;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\MyProgramFiles\Git\cmd;D:\MyProgramFiles\Tencent\微信web开发者工具\dll;D:\JavaWorking\Maven\apache-maven-3.9.10\bin;D:\MyProgramFiles\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\MyProgramFiles\nvm;D:\MyProgramFiles\nodejs;;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\MyProgramFiles\Ollama;D:\MyProgramFiles\Windsurf\bin;D:\MyProgramFiles\cursor\resources\app\bin;D:\MyProgramFiles\Kiro\bin;D:\MyProgramFiles\JetBrains\IntelliJ IDEA 2025.1.3\bin;;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.8+9-LTS-211"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testCategoryQuery" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="1.631">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeCategoryQuery(UnifiedProductQueryServiceImpl.java:127)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:95)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testCategoryQuery(UnifiedProductQueryServiceTest.java:102)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:09.977 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:1:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testAllQuery" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.006">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testAllQuery(UnifiedProductQueryServiceTest.java:171)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.039 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:all:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testBrandFilter" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.005">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testBrandFilter(UnifiedProductQueryServiceTest.java:267)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.046 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:all:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testParameterValidation" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.013">
    <failure message="Unexpected exception type thrown, expected: &lt;java.lang.IllegalArgumentException&gt; but was: &lt;java.lang.NullPointerException&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <java.lang.IllegalArgumentException> but was: <java.lang.NullPointerException>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testParameterValidation(UnifiedProductQueryServiceTest.java:213)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.erp.api.pojo.request.UnifiedProductQueryRequest$QueryMode.ordinal()" because "this.queryMode" is null
	at cn.stylefeng.roses.kernel.erp.api.pojo.request.UnifiedProductQueryRequest.validate(UnifiedProductQueryRequest.java:201)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.lambda$testParameterValidation$0(UnifiedProductQueryServiceTest.java:214)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more
]]></failure>
  </testcase>
  <testcase name="testSearchQuery" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.009">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)&quot; because &quot;this.cacheOperatorApi&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.getSearchSuggestions(UnifiedProductQueryServiceImpl.java:385)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeSearchQuery(UnifiedProductQueryServiceImpl.java:157)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:97)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSearchQuery(UnifiedProductQueryServiceTest.java:129)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.066 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:search:kw:686145:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testSearchSuggestions" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.011">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)&quot; because &quot;this.cacheOperatorApi&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.getSearchSuggestions(UnifiedProductQueryServiceImpl.java:385)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSearchSuggestions(UnifiedProductQueryServiceTest.java:189)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
  </testcase>
  <testcase name="testCacheClear" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.005">
    <failure message="Unexpected exception thrown: java.lang.NullPointerException: Cannot invoke &quot;cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.remove(String[])&quot; because &quot;this.cacheOperatorApi&quot; is null" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: Unexpected exception thrown: java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.remove(String[])" because "this.cacheOperatorApi" is null
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:152)
	at org.junit.jupiter.api.AssertDoesNotThrow.createAssertionFailedError(AssertDoesNotThrow.java:84)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:53)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:36)
	at org.junit.jupiter.api.Assertions.assertDoesNotThrow(Assertions.java:3168)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testCacheClear(UnifiedProductQueryServiceTest.java:327)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.remove(String[])" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.clearQueryCache(UnifiedProductQueryServiceImpl.java:460)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.lambda$testCacheClear$5(UnifiedProductQueryServiceTest.java:328)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:49)
	... 6 more
]]></failure>
  </testcase>
  <testcase name="testCombinedQuery" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.009">
    <system-out><![CDATA[17:35:10.095 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:combined:cat:1:kw:686145:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
17:35:10.095 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 缓存结果失败: pos:product:query:combined:cat:1:kw:686145:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.put(String, Object, java.lang.Long)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testCacheWarmup" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.007">
    <system-out><![CDATA[17:35:10.105 [main] INFO cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 开始预热商品查询缓存
17:35:10.106 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:1:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
17:35:10.107 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 预热分类 1 的商品缓存失败: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
17:35:10.107 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:2:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 预热分类 2 的商品缓存失败: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:3:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 预热分类 3 的商品缓存失败: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:4:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 预热分类 4 的商品缓存失败: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:5:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
17:35:10.108 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 预热分类 5 的商品缓存失败: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
17:35:10.110 [main] INFO cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 商品查询缓存预热完成
]]></system-out>
  </testcase>
  <testcase name="testHotSearchKeywords" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.003">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)&quot; because &quot;this.cacheOperatorApi&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.getHotSearchKeywords(UnifiedProductQueryServiceImpl.java:412)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testHotSearchKeywords(UnifiedProductQueryServiceTest.java:199)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
  </testcase>
  <testcase name="testSortingOptions" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.007">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testSortingOptions(UnifiedProductQueryServiceTest.java:302)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.119 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:all:page:1:size:20:sort:PRICE:ASC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testPerformanceMetrics" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.005">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeCategoryQuery(UnifiedProductQueryServiceImpl.java:127)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:95)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPerformanceMetrics(UnifiedProductQueryServiceTest.java:347)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.127 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:category:cat:1:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testPriceRangeFilter" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.003">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPriceRangeFilter(UnifiedProductQueryServiceTest.java:250)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.131 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:all:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
  <testcase name="testPricingTypeFilter" classname="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" time="0.004">
    <error message="Cannot invoke &quot;cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()&quot; because &quot;pageResult&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult.getRows()" because "pageResult" is null
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.buildResponseFromPageResult(UnifiedProductQueryServiceImpl.java:283)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeAllQuery(UnifiedProductQueryServiceImpl.java:209)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.executeQuery(UnifiedProductQueryServiceImpl.java:101)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl.queryProducts(UnifiedProductQueryServiceImpl.java:72)
	at cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest.testPricingTypeFilter(UnifiedProductQueryServiceTest.java:284)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[17:35:10.137 [main] WARN cn.stylefeng.roses.kernel.erp.modular.product.service.impl.UnifiedProductQueryServiceImpl -- 获取缓存失败: pos:product:query:all:pt:WEIGHT:page:1:size:20:sort:DEFAULT:DESC, 错误: Cannot invoke "cn.stylefeng.roses.kernel.cache.api.CacheOperatorApi.get(String)" because "this.cacheOperatorApi" is null
]]></system-out>
  </testcase>
</testsuite>