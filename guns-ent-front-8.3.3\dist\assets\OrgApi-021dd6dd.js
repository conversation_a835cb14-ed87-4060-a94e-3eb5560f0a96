import{R as e}from"./index-18a1ea24.js";class o{static tree(t){return e.post("/common/org/tree",t)}static findPage(t){return e.getAndLoadData("/hrOrganization/page",t)}static add(t){return e.post("/hrOrganization/add",t)}static edit(t){return e.post("/hrOrganization/edit",t)}static delete(t){return e.post("/hrOrganization/delete",t)}static batchDelete(t){return e.post("/hrOrganization/batchDelete",t)}static detail(t){return e.getAndLoadData("/hrOrganization/detail",t)}static exportOrg(t){return e.downLoad("/org/exportOrg",t)}static getExcelTemplate(t){return e.downLoad("/orgImport/getExcelTemplate",t)}static uploadAndGetPreviewData(t){return e.post("/orgImport/uploadAndGetPreviewData",t)}static ensureImport(t){return e.post("/orgImport/ensureImport",t)}static organizationLevelList(t){return e.getAndLoadData("/organizationLevel/list",t)}static updateTotal(t){return e.post("/organizationLevel/updateTotal",t)}}export{o as O};
