System.register(["./index-legacy-ee1db0c7.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-e24582b9.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js"],(function(e,a){"use strict";var t,l,d,i,s,r,o,n,u,p,c,g,b,y,f,v,m,x,_,h,T,C,I,N,k,S;return{setters:[e=>{t=e._,l=e.r,d=e.X,i=e.m,s=e.a,r=e.f,o=e.w,n=e.d,u=e.g,p=e.t,c=e.c,g=e.F,b=e.e,y=e.b,f=e.h,v=e.Y,m=e.U,x=e.S,_=e.Z,h=e.j,T=e.n,C=e.i,I=e.a5,N=e.T,k=e.M},null,null,null,null,null,e=>{S=e.S}],execute:function(){var a=document.createElement("style");a.textContent=".region-tags[data-v-16ea259b]{display:flex;flex-wrap:wrap;gap:8px;align-items:center}.empty-regions[data-v-16ea259b]{display:flex;align-items:center;gap:8px}.empty-text[data-v-16ea259b],.text-muted[data-v-16ea259b]{color:#999;font-style:italic}.products-section[data-v-16ea259b]{margin-top:16px}.products-header[data-v-16ea259b]{margin-bottom:16px;padding-bottom:8px;border-bottom:1px solid #f0f0f0}.products-title[data-v-16ea259b]{font-weight:500;font-size:16px;color:#262626}.products-content[data-v-16ea259b]{min-height:200px}.empty-products[data-v-16ea259b]{display:flex;justify-content:center;align-items:center;min-height:200px}[data-v-16ea259b] .ant-table-tbody>tr>td{padding:8px 12px}[data-v-16ea259b] .ant-table-thead>tr>th{padding:8px 12px;background-color:#fafafa}[data-v-16ea259b] .ant-tabs-content-holder{padding:16px 0}[data-v-16ea259b] .ant-tabs-tab{padding:8px 16px}\n",document.head.appendChild(a);const L={name:"SupplierDetail",props:{visible:Boolean,data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{emit:a}){const t=l(!1),s=l([]),r=l([]),o=l(!1);d((()=>e.visible),(async a=>{var t;a&&null!==(t=e.data)&&void 0!==t&&t.supplierId?await Promise.all([n(),u()]):(s.value=[],r.value=[])}));const n=async()=>{var a;if(null!==(a=e.data)&&void 0!==a&&a.supplierId){t.value=!0;try{const a=await S.getSupplierRegions({supplierId:e.data.supplierId});a&&Array.isArray(a)?s.value=a:s.value=[]}catch(l){console.error("获取供应商关联区域失败:",l),i.error("获取供应商关联区域失败"),s.value=[]}finally{t.value=!1}}},u=async()=>{var a;if(null!==(a=e.data)&&void 0!==a&&a.supplierId){o.value=!0;try{const a=await S.getSupplierProducts({supplierId:e.data.supplierId});a&&Array.isArray(a)?r.value=a:r.value=[]}catch(t){console.error("获取供应商关联商品失败:",t),i.error("获取供应商关联商品失败"),r.value=[]}finally{o.value=!1}}};return{loading:t,regionList:s,supplierProducts:r,productsLoading:o,productColumns:[{title:"商品编码",dataIndex:"productCode",width:120,ellipsis:!0},{title:"商品名称",dataIndex:"productName",width:200,ellipsis:!0},{title:"计价类型",dataIndex:"pricingType",width:100,align:"center"},{title:"零售价格",dataIndex:"retailPrice",width:100,align:"right"},{title:"状态",dataIndex:"status",width:80,align:"center"},{title:"创建时间",dataIndex:"createTime",width:140,ellipsis:!0}],updateVisible:e=>{a("update:visible",e)},getSupplierTypeName:e=>S.getSupplierTypeName(e),getBusinessModeName:e=>S.getBusinessModeName(e),getSupplierStatusName:e=>S.getSupplierStatusName(e),getCreditLevelName:e=>S.getCreditLevelName(e),getStatusTagColor:e=>S.getStatusTagColor(e),getBusinessModeTagColor:e=>S.getBusinessModeTagColor(e),getCreditLevelTagColor:e=>S.getCreditLevelTagColor(e),getPricingTypeName:e=>({NORMAL:"普通商品",WEIGHT:"计重商品",PIECE:"计件商品",VARIABLE:"不定价商品"}[e]||e),getPricingTypeColor:e=>({NORMAL:"blue",WEIGHT:"green",PIECE:"orange",VARIABLE:"purple"}[e]||"default")}}},P={key:0},w={key:1,class:"text-muted"},A={key:0},E={key:1,class:"region-tags"},M={key:2,class:"empty-regions"},j={class:"products-section"},B={class:"products-header"},V={class:"products-content"},R={key:0},D={key:0},z={key:1,class:"text-muted"},O={key:1,class:"empty-products"};e("default",t(L,[["render",function(e,a,t,l,d,i){const S=v,L=m,U=x,F=_,G=h,H=T,W=C,Q=I,X=N,Y=k;return s(),r(Y,{title:"供应商详情",width:1e3,visible:t.visible,footer:null,"onUpdate:visible":l.updateVisible},{default:o((()=>[n(X,{"default-active-key":"basic",type:"card"},{default:o((()=>[n(G,{key:"basic",tab:"基本信息"},{default:o((()=>[n(F,{column:2,bordered:""},{default:o((()=>[n(S,{label:"供应商编码"},{default:o((()=>[u(p(t.data.supplierCode),1)])),_:1}),n(S,{label:"供应商名称"},{default:o((()=>[u(p(t.data.supplierName),1)])),_:1}),n(S,{label:"供应商简称"},{default:o((()=>[u(p(t.data.supplierShortName||"-"),1)])),_:1}),n(S,{label:"供应商类型"},{default:o((()=>[n(L,null,{default:o((()=>[u(p(l.getSupplierTypeName(t.data.supplierType)),1)])),_:1})])),_:1}),n(S,{label:"经营方式"},{default:o((()=>[n(L,{color:l.getBusinessModeTagColor(t.data.businessMode)},{default:o((()=>[u(p(l.getBusinessModeName(t.data.businessMode)),1)])),_:1},8,["color"])])),_:1}),n(S,{label:"销售扣点"},{default:o((()=>[null!==t.data.salesDeduction&&void 0!==t.data.salesDeduction?(s(),c("span",P,p(t.data.salesDeduction)+"% ",1)):(s(),c("span",w,"-"))])),_:1}),n(S,{label:"联系人"},{default:o((()=>[u(p(t.data.contactPerson||"-"),1)])),_:1}),n(S,{label:"联系电话"},{default:o((()=>[u(p(t.data.contactPhone||"-"),1)])),_:1}),n(S,{label:"手机号码"},{default:o((()=>[u(p(t.data.contactMobile||"-"),1)])),_:1}),n(S,{label:"邮箱地址"},{default:o((()=>[u(p(t.data.contactEmail||"-"),1)])),_:1}),n(S,{label:"联系地址",span:2},{default:o((()=>[u(p(t.data.contactAddress||"-"),1)])),_:1}),n(S,{label:"营业执照号"},{default:o((()=>[u(p(t.data.businessLicenseNo||"-"),1)])),_:1}),n(S,{label:"税务登记号"},{default:o((()=>[u(p(t.data.taxNo||"-"),1)])),_:1}),n(S,{label:"开户银行"},{default:o((()=>[u(p(t.data.bankName||"-"),1)])),_:1}),n(S,{label:"银行账号"},{default:o((()=>[u(p(t.data.bankAccount||"-"),1)])),_:1}),n(S,{label:"信用等级"},{default:o((()=>[n(L,{color:l.getCreditLevelTagColor(t.data.creditLevel)},{default:o((()=>[u(p(l.getCreditLevelName(t.data.creditLevel)),1)])),_:1},8,["color"])])),_:1}),n(S,{label:"状态"},{default:o((()=>[n(L,{color:l.getStatusTagColor(t.data.status)},{default:o((()=>[u(p(l.getSupplierStatusName(t.data.status)),1)])),_:1},8,["color"])])),_:1}),n(S,{label:"关联区域",span:2},{default:o((()=>[l.loading?(s(),c("div",A,[n(U,{size:"small"})])):l.regionList.length>0?(s(),c("div",E,[(s(!0),c(g,null,b(l.regionList,(e=>(s(),r(L,{key:e.regionId,color:"blue"},{default:o((()=>[u(p(e.regionName),1)])),_:2},1024)))),128))])):(s(),c("div",M,a[0]||(a[0]=[y("span",{class:"empty-text"},"暂无关联区域",-1)])))])),_:1}),n(S,{label:"备注",span:2},{default:o((()=>[u(p(t.data.remark||"-"),1)])),_:1}),n(S,{label:"创建时间"},{default:o((()=>[u(p(t.data.createTime),1)])),_:1}),n(S,{label:"更新时间"},{default:o((()=>[u(p(t.data.updateTime),1)])),_:1})])),_:1})])),_:1}),n(G,{key:"products",tab:"关联商品"},{default:o((()=>[y("div",j,[y("div",B,[n(H,null,{default:o((()=>[a[1]||(a[1]=y("span",{class:"products-title"},"关联商品列表",-1)),l.supplierProducts.length>0?(s(),r(L,{key:0,color:"blue"},{default:o((()=>[u(" 共 "+p(l.supplierProducts.length)+" 个商品 ",1)])),_:1})):f("",!0)])),_:1,__:[1]})]),y("div",V,[n(U,{spinning:l.productsLoading},{default:o((()=>[l.supplierProducts.length>0?(s(),c("div",R,[n(W,{columns:l.productColumns,"data-source":l.supplierProducts,pagination:!1,size:"small",scroll:{y:300}},{bodyCell:o((({column:e,record:a})=>["pricingType"===e.dataIndex?(s(),r(L,{key:0,color:l.getPricingTypeColor(a.pricingType)},{default:o((()=>[u(p(l.getPricingTypeName(a.pricingType)),1)])),_:2},1032,["color"])):"retailPrice"===e.dataIndex?(s(),c(g,{key:1},[a.retailPrice?(s(),c("span",D,"￥"+p(a.retailPrice),1)):(s(),c("span",z,"-"))],64)):"status"===e.dataIndex?(s(),r(L,{key:2,color:"ACTIVE"===a.status?"green":"red"},{default:o((()=>[u(p("ACTIVE"===a.status?"正常":"停用"),1)])),_:2},1032,["color"])):f("",!0)])),_:1},8,["columns","data-source"])])):(s(),c("div",O,[n(Q,{description:"暂无关联商品"})]))])),_:1},8,["spinning"])])])])),_:1})])),_:1})])),_:1},8,["visible","onUpdate:visible"])}],["__scopeId","data-v-16ea259b"]]))}}}));
