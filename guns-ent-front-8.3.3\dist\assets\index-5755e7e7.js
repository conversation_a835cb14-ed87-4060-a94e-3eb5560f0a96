import{r as a,bh as f,k as n,a as v,c as h,d as l,w as s,a0 as b}from"./index-18a1ea24.js";/* empty css              */import{a as g}from"./FileApi-418f4d35.js";const w={class:"guns-body guns-body-card"},B={__name:"index",setup(x){const i=a("undo redo clear h bold italic strikethrough quote ul ol table hr link image code"),c=a("preview sync-scroll fullscreen"),r=a(""),m=a("".concat(f,"/sysFileInfo/public/preview?fileId=")),u=async(p,o,d)=>{const e=new FormData;e.append("file",d[0]);let t=await g.commonUpload("N",e);o({url:m.value+t.data.fileId,desc:t.data.fileOriginName})};return(p,o)=>{const d=n("v-md-editor"),e=b,t=n("v-md-preview");return v(),h("div",w,[l(e,{title:"markwodn\u7F16\u8F91\u5668-\u7F16\u8F91",bordered:!1},{default:s(()=>[l(d,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=_=>r.value=_),autofocus:!0,height:"400px",width:"100%",ref:"editor","left-toolbar":i.value,"right-toolbar":c.value,"disabled-menus":[],mode:"edit",onUploadImage:u},null,8,["modelValue","left-toolbar","right-toolbar"])]),_:1}),l(e,{title:"markwodn\u7F16\u8F91\u5668-\u9884\u89C8",bordered:!1},{default:s(()=>[l(t,{text:r.value,height:"500px"},null,8,["text"])]),_:1})])}}};export{B as default};
