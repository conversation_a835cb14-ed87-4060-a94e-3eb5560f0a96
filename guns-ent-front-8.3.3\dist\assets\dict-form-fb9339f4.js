import{s as p,a as _,f as b,w as t,d as e,at as y,as as N,g as i,l as w,u as x,v as S,y as U,z as C,A as F,G as I,H as q}from"./index-18a1ea24.js";/* empty css              */const k={__name:"dict-form",props:{form:Object,isUpdate:<PERSON><PERSON><PERSON>,dictList:Array},setup(l){const n=l,s=p({dictName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5B57\u5178\u540D\u79F0",type:"string",trigger:"blur"}],dictCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5B57\u5178\u7F16\u7801",type:"string",trigger:"blur"}],dictSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u6392\u5E8F",type:"number",trigger:"blur"}],statusFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u5B57\u5178\u72B6\u6001",type:"number",trigger:"change"}],sysFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u662F\u5426\u662F\u7CFB\u7EDF\u914D\u7F6E",type:"string",trigger:"change"}],dictParentId:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u5B57\u5178",type:"string",trigger:"change"}]});return(h,a)=>{const u=w,o=x,r=S,f=U,m=C,c=F,g=I,v=q;return _(),b(v,{ref:"formRef",model:l.form,rules:s,layout:"vertical"},{default:t(()=>[e(g,{gutter:20},{default:t(()=>[e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u6240\u5C5E\u5B57\u5178\u7C7B\u578B:",name:"dictTypeName"},{default:t(()=>[e(u,{value:l.form.dictTypeName,"onUpdate:value":a[0]||(a[0]=d=>l.form.dictTypeName=d),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u6240\u5C5E\u5B57\u5178\u7C7B\u578B",disabled:""},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u5B57\u5178\u540D\u79F0:",name:"dictName"},{default:t(()=>[e(u,{value:l.form.dictName,"onUpdate:value":a[1]||(a[1]=d=>l.form.dictName=d),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u5B57\u5178\u7F16\u7801(\u5B57\u5178\u503C):",name:"dictCode"},{default:t(()=>[e(u,{value:l.form.dictCode,"onUpdate:value":a[2]||(a[2]=d=>l.form.dictCode=d),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u7F16\u7801",disabled:n.isUpdate},null,8,["value","disabled"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u4E0A\u7EA7\u5B57\u5178:",name:"dictParentId"},{default:t(()=>[e(y(N),{value:l.form.dictParentId,"onUpdate:value":a[3]||(a[3]=d=>l.form.dictParentId=d),style:{width:"100%"},showSearch:"","tree-data":n.dictList,treeNodeFilterProp:"dictName","dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u5B57\u5178",fieldNames:{children:"children",label:"dictName",key:"dictId",value:"dictId"},"allow-clear":"",disabled:n.isUpdate,"tree-default-expand-all":""},null,8,["value","tree-data","disabled"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u6392\u5E8F:",name:"dictSort"},{default:t(()=>[e(f,{value:l.form.dictSort,"onUpdate:value":a[4]||(a[4]=d=>l.form.dictSort=d),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u5B57\u5178\u7B80\u79F0:",name:"dictShortName"},{default:t(()=>[e(u,{value:l.form.dictShortName,"onUpdate:value":a[5]||(a[5]=d=>l.form.dictShortName=d),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u7B80\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u5B57\u5178\u7B80\u8981\u7F16\u7801:",name:"dictShortCode"},{default:t(()=>[e(u,{value:l.form.dictShortCode,"onUpdate:value":a[6]||(a[6]=d=>l.form.dictShortCode=d),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u7B80\u8981\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:t(()=>[e(o,{label:"\u5B57\u5178\u72B6\u6001:",name:"statusFlag"},{default:t(()=>[e(c,{value:l.form.statusFlag,"onUpdate:value":a[7]||(a[7]=d=>l.form.statusFlag=d)},{default:t(()=>[e(m,{value:1},{default:t(()=>a[8]||(a[8]=[i("\u542F\u7528")])),_:1,__:[8]}),e(m,{value:2},{default:t(()=>a[9]||(a[9]=[i("\u7981\u7528")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{k as default};
