import{_ as v,a as u,c as d,b as t,d as s,I as e,g as n,t as c,h as g,w as m,m as f,V as b,B as k}from"./index-18a1ea24.js";import{A as w}from"./formatter-5a06da9d.js";import"./constants-2fa70699.js";const x={class:"cart-summary"},B={class:"amount-details"},A={class:"amount-row"},S={class:"amount-label"},V={class:"amount-value"},N={key:0,class:"amount-row discount-row"},j={class:"amount-label"},I={class:"amount-value discount-value"},O={class:"total-amount"},q={class:"total-row"},F={class:"total-label"},z={class:"total-value"},D={class:"action-buttons"},E=Object.assign({name:"CartSummary"},{__name:"CartSummary",props:{summary:{type:Object,required:!0},total:{type:Object,required:!0},canCheckout:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["checkout"],setup(o,{emit:r}){const _=o,p=r,i=l=>w.formatCurrency(l||0),y=()=>{if(!_.canCheckout){f.warning("\u8D2D\u7269\u8F66\u4E3A\u7A7A\u6216\u5546\u54C1\u4FE1\u606F\u6709\u8BEF\uFF0C\u65E0\u6CD5\u7ED3\u8D26");return}p("checkout")};return(l,a)=>{const h=b,C=k;return u(),d("div",x,[t("div",B,[t("div",A,[t("span",S,[s(e,{iconClass:"icon-shopping"}),n(" \u5546\u54C1\u603B\u8BA1\uFF08"+c(o.summary.itemCount)+"\u4EF6\uFF09 ",1)]),t("span",V,c(i(o.total.totalAmount)),1)]),o.total.discountAmount>0?(u(),d("div",N,[t("span",j,[s(e,{iconClass:"icon-discount"}),a[0]||(a[0]=n(" \u4F18\u60E0\u6298\u6263 "))]),t("span",I,"-"+c(i(o.total.discountAmount)),1)])):g("",!0)]),s(h,{style:{margin:"12px 0"}}),t("div",O,[t("div",q,[t("span",F,[s(e,{iconClass:"icon-money"}),a[1]||(a[1]=n(" \u5E94\u4ED8\u603B\u989D "))]),t("span",z,c(i(o.total.payableAmount)),1)])]),t("div",D,[s(C,{size:"large",type:"primary",loading:o.loading,disabled:!o.canCheckout,onClick:y},{icon:m(()=>[s(e,{iconClass:"icon-pay"})]),default:m(()=>[a[2]||(a[2]=n(" \u7ACB\u5373\u7ED3\u8D26 "))]),_:1,__:[2]},8,["loading","disabled"])])])}}}),H=v(E,[["__scopeId","data-v-9e23e82d"]]);export{H as default};
