<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-validator</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>validator-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--参数校验api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>validator-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--参数校验-校验table内编码唯一的-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>validator-api-table-unique</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

</project>
