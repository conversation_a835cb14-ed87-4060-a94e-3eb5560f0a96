package cn.stylefeng.roses.kernel.conversion.util;

import cn.hutool.core.io.FileUtil;
import com.aspose.cells.*;

import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStream;

/**
 * Excel相关转化
 *
 * <AUTHOR>
 * @date 2021/8/26 15:07
 */
public class ExcelUtil {

    /**
     * excel转pdf
     *
     * @param inputFile  原有xls或xlsx文件路径
     * @param outputFile 输出的pdf文件路径
     * <AUTHOR>
     * @date 2021/8/26 14:35
     */
    public static void toPdf(String inputFile, String outputFile) {
        try {
            // 加载Excel文档
            Workbook wb = new Workbook(inputFile);

            // 设置转换选项
            PdfSaveOptions options = new PdfSaveOptions();
            options.setOnePagePerSheet(true);

            // 保存为PDF格式
            wb.save(outputFile, options);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * excel转化为pdf，读入字节，输出到流中
     *
     * <AUTHOR>
     * @since 2023/10/20 22:48
     */
    public static void toPdfByBytes(byte[] inputFileBytes, OutputStream outputStream) {
        try {
            // 加载Excel文档
            Workbook wb = new Workbook(new ByteArrayInputStream(inputFileBytes));

            // 设置转换选项
            PdfSaveOptions options = new PdfSaveOptions();
            options.setOnePagePerSheet(true);

            // 保存到输出流
            wb.save(outputStream, options);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Excel转PDF（指定Sheet）
     *
     * @param inputFile   原有xls或xlsx文件路径
     * @param outputFile  输出的pdf文件路径
     * @param sheetNumber 从0开始的sheet
     * <AUTHOR>
     * @date 2021/8/26 14:35
     */
    public static void toPdfSheet(String inputFile, String outputFile, int sheetNumber) {
        try {
            // 加载Excel文档
            Workbook wb = new Workbook(inputFile);

            // 获取指定工作表
            com.aspose.cells.Worksheet sheet = wb.getWorksheets().get(sheetNumber);

            // 创建一个新的工作簿，只包含指定的工作表
            Workbook singleSheetWorkbook = new Workbook();
            singleSheetWorkbook.getWorksheets().clear();
            singleSheetWorkbook.getWorksheets().add(sheet.getName());

            // 复制指定的工作表内容到新的工作簿中
            Worksheet newSheet = singleSheetWorkbook.getWorksheets().get(0);
            newSheet.copy(sheet);

            // 设置转换选项
            PdfSaveOptions options = new PdfSaveOptions();
            options.setOnePagePerSheet(true);

            // 保存为PDF格式
            singleSheetWorkbook.save(outputFile, options);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * excel转图片
     *
     * @param inputFile  原有xls或xlsx文件路径
     * @param outputFile 输出的pdf文件路径
     * <AUTHOR>
     * @date 2021/8/26 14:35
     */
    public static void toImage(String inputFile, String outputFile) {
        try {
            // 加载Excel文档
            Workbook wb = new Workbook(inputFile);

            // 获取第一个工作表
            com.aspose.cells.Worksheet sheet = wb.getWorksheets().get(0);

            // 设置图像选项
            ImageOrPrintOptions imgOptions = new ImageOrPrintOptions();
            imgOptions.setOnePagePerSheet(true);

            // 渲染并保存为图片
            SheetRender sr = new SheetRender(sheet, imgOptions);
            sr.toImage(0, outputFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws FileNotFoundException {
        ExcelUtil.toPdf("C:\\Users\\<USER>\\Pictures\\test.xlsx", "C:\\Users\\<USER>\\Pictures\\1.pdf");
        ExcelUtil.toPdfByBytes(FileUtil.readBytes("C:\\Users\\<USER>\\Pictures\\test.xlsx"), new FileOutputStream("C:\\Users\\<USER>\\Pictures\\2.pdf"));
        ExcelUtil.toPdfSheet("C:\\Users\\<USER>\\Pictures\\test.xlsx", "C:\\Users\\<USER>\\Pictures\\3.pdf", 1);
        ExcelUtil.toImage("C:\\Users\\<USER>\\Pictures\\test.xlsx", "C:\\Users\\<USER>\\Pictures\\4.png");
    }

}
