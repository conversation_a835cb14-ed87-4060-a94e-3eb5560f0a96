import Request from '@/utils/request/request-util';

/**
 * 单带登录配置-API接口
 *
 * <AUTHOR>
 * @date 2023/11/05 18:00
 */
export class SsoClientApi {

  /**
   * 获取分页列表
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static findPage(params) {
    return Request.getAndLoadData('/ssoClient/page', params);
  }

  /**
   * 添加单带登录配置
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static add(params) {
    return Request.post('/ssoClient/add', params);
  }

  /**
   * 编辑单带登录配置
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static edit(params) {
    return Request.post('/ssoClient/edit', params);
  }

  /**
   * 删除单个单带登录配置
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static delete(params) {
    return Request.post('/ssoClient/delete', params);
  }

  /**
   * 删除批量单带登录配置
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static batchDelete(params) {
    return Request.post('/ssoClient/batchDelete', params);
  }

  /**
   * 单带登录配置详情
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static detail(params) {
    return Request.getAndLoadData('/ssoClient/detail', params);
  }

  /**
   * 更新单点状态
   *
   * <AUTHOR>
   * @date 2023/11/05 18:00
   */
  static updateStatus(params) {
    return Request.post('/ssoClient/updateStatus', params);
  }

}