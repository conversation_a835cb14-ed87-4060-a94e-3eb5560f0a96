package cn.stylefeng.roses.kernel.erp.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 采购入库单模块异常枚举
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@Getter
public enum PurchaseOrderExceptionEnum implements AbstractExceptionEnum {

    /**
     * 采购入库单不存在
     */
    PURCHASE_ORDER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20001", "采购入库单不存在"),

    /**
     * 采购入库单号已存在
     */
    PURCHASE_ORDER_NO_REPEAT(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20002", "采购入库单号已存在"),

    /**
     * 采购入库单状态错误
     */
    PURCHASE_ORDER_STATUS_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20003", "采购入库单状态错误，只能为：草稿、已确认、已完成"),

    /**
     * 采购入库单明细不存在
     */
    PURCHASE_ORDER_DETAIL_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20004", "采购入库单明细不存在"),

    /**
     * 采购入库单明细列表为空
     */
    PURCHASE_ORDER_DETAIL_LIST_EMPTY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20005", "采购入库单明细列表为空"),

    /**
     * 供应商不存在
     */
    SUPPLIER_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20006", "供应商不存在"),

    /**
     * 商品不存在
     */
    PRODUCT_NOT_EXIST(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20007", "商品不存在"),

    /**
     * 联营供应商不允许创建采购入库单
     */
    JOINT_VENTURE_SUPPLIER_CANNOT_PURCHASE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20008", "联营供应商不允许创建采购入库单"),

    /**
     * 采购入库单已确认，无法修改
     */
    PURCHASE_ORDER_CONFIRMED_CANNOT_MODIFY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20009", "采购入库单已确认，无法修改"),

    /**
     * 采购入库单已完成，无法修改
     */
    PURCHASE_ORDER_COMPLETED_CANNOT_MODIFY(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20010", "采购入库单已完成，无法修改"),

    /**
     * 采购入库单未确认，无法入库
     */
    PURCHASE_ORDER_NOT_CONFIRMED_CANNOT_RECEIVE(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20011", "采购入库单未确认，无法入库"),

    /**
     * 采购入库单已入库，无法重复入库
     */
    PURCHASE_ORDER_ALREADY_RECEIVED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20012", "采购入库单已入库，无法重复入库"),

    /**
     * 商品数量必须大于0
     */
    PRODUCT_QUANTITY_MUST_GREATER_THAN_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20013", "商品数量必须大于0"),

    /**
     * 商品单价必须大于0
     */
    PRODUCT_UNIT_PRICE_MUST_GREATER_THAN_ZERO(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20014", "商品单价必须大于0"),

    /**
     * 付款方式错误
     */
    PAYMENT_METHOD_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20015", "付款方式错误"),

    /**
     * 商品不属于该供应商
     */
    PRODUCT_NOT_BELONG_TO_SUPPLIER(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20016", "商品不属于该供应商"),

    /**
     * 采购入库单总金额计算错误
     */
    PURCHASE_ORDER_TOTAL_AMOUNT_ERROR(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20017", "采购入库单总金额计算错误"),

    /**
     * 库存更新失败
     */
    INVENTORY_UPDATE_FAILED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE + "20018", "库存更新失败");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    PurchaseOrderExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }
}