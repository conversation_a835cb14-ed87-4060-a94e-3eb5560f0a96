import{R as e}from"./index-18a1ea24.js";class s{static add(t){return e.post("/erp/customer/add",t)}static delete(t){return e.post("/erp/customer/delete",t)}static batchDelete(t){return e.post("/erp/customer/batchDelete",t)}static edit(t){return e.post("/erp/customer/edit",t)}static detail(t){return e.getAndLoadData("/erp/customer/detail",t)}static findPage(t){return e.getAndLoadData("/erp/customer/page",t)}static findList(t){return e.getAndLoadData("/erp/customer/list",t)}static updateStatus(t){return e.post("/erp/customer/updateStatus",t)}static validateCode(t){return e.getAndLoadData("/erp/customer/validateCode",t)}static getCustomerTypeOptions(){return[{label:"\u4F01\u4E1A",value:"ENTERPRISE"},{label:"\u4E2A\u4EBA",value:"INDIVIDUAL"},{label:"\u96F6\u552E",value:"RETAIL"}]}static getCustomerLevelOptions(){return[{label:"\u94BB\u77F3",value:"DIAMOND"},{label:"\u9EC4\u91D1",value:"GOLD"},{label:"\u767D\u94F6",value:"SILVER"},{label:"\u9752\u94DC",value:"BRONZE"}]}static getCustomerStatusOptions(){return[{label:"\u6B63\u5E38",value:"ACTIVE"},{label:"\u505C\u7528",value:"INACTIVE"},{label:"\u51BB\u7ED3",value:"FROZEN"}]}static getCustomerTypeName(t){const a=s.getCustomerTypeOptions().find(r=>r.value===t);return a?a.label:t}static getCustomerLevelName(t){const a=s.getCustomerLevelOptions().find(r=>r.value===t);return a?a.label:t}static getCustomerStatusName(t){const a=s.getCustomerStatusOptions().find(r=>r.value===t);return a?a.label:t}static getStatusTagColor(t){switch(t){case"ACTIVE":return"green";case"INACTIVE":return"orange";case"FROZEN":return"red";default:return"default"}}static getCustomerLevelTagColor(t){switch(t){case"DIAMOND":return"purple";case"GOLD":return"gold";case"SILVER":return"cyan";case"BRONZE":return"orange";default:return"default"}}static formatAmount(t){return t?Number(t).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00"}static formatPaymentTerms(t){return t?"".concat(t,"\u5929"):"-"}static updateCustomerRegions(t){return e.post("/erp/customerRegion/updateCustomerRegions",t)}static getCustomerRegions(t){return e.getAndLoadData("/erp/customerRegion/getCustomerRegions",t)}static findPageByRegion(t){return e.getAndLoadData("/erp/customer/pageByRegion",t)}}export{s as C};
