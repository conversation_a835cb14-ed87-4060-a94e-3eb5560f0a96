package cn.stylefeng.roses.sanyuan.modular.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.log.api.pojo.business.SysLogBusinessRequest;
import cn.stylefeng.roses.kernel.log.api.pojo.entity.SysLogBusiness;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.sanyuan.api.constants.SanyuanConstants;
import cn.stylefeng.roses.sanyuan.modular.service.AuditLogService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

/**
 * 审计日志控制器
 *
 * <AUTHOR>
 * @since 2023/10/10 14:12
 */
@RestController
@ApiResource(name = "审计日志控制器")
public class AuditLogController {

    @Resource
    private AuditLogService auditLogService;

    /**
     * 获取日常运维日志列表
     *
     * <AUTHOR>
     * @since 2023/10/10 14:15
     */
    @GetResource(name = "获取日常运维日志", path = "/auditLog/getNormalLog", requiredPermission = true,
            requirePermissionCode = SanyuanConstants.RI_CHANG_YUN_WEI_MENU_CODE)
    public ResponseData<PageResult<SysLogBusiness>> getNormalLog(SysLogBusinessRequest sysLogBusinessRequest) {
        PageResult<SysLogBusiness> page = auditLogService.findNormalLogPage(sysLogBusinessRequest);
        return new SuccessResponseData<>(page);
    }

    /**
     * 获取安全操作日志
     *
     * <AUTHOR>
     * @since 2023/10/10 14:45
     */
    @GetResource(name = "获取安全操作日志", path = "/auditLog/getSecurityLog", requiredPermission = true,
            requirePermissionCode = SanyuanConstants.ANQUANCAOZUO_MENU_CODE)
    public ResponseData<PageResult<SysLogBusiness>> getSecurityLog(SysLogBusinessRequest sysLogBusinessRequest) {
        PageResult<SysLogBusiness> page = auditLogService.getSecurityLog(sysLogBusinessRequest);
        return new SuccessResponseData<>(page);
    }

}
