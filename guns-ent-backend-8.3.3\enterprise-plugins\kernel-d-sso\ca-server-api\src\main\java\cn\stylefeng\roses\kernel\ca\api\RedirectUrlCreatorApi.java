package cn.stylefeng.roses.kernel.ca.api;

/**
 * 单点服务跳转url的组装，为了方便组装url后边的参数
 * <p>
 * 可以组装跳转到客户端回调url，可以组装跳转到登录页面的url
 *
 * <AUTHOR>
 * @date 2021/1/22 9:45
 */
public interface RedirectUrlCreatorApi {

    /**
     * 创建跳转到单点客户端的回调地址的url
     *
     * @param clientId                客户端id
     * @param secretUserInfo          回调需要带的token参数
     * @param originCallbackUrl       单点请求时候的callback url
     * @param clientConfigCallbackUrl 数据库中存的客户端回调url
     * @return 具体redirect的url
     * <AUTHOR>
     * @date 2021/1/22 9:47
     */
    String createClientSsoCallbackUrl(Long clientId, String secretUserInfo, String originCallbackUrl, String clientConfigCallbackUrl);

    /**
     * 创建携带错误码的，sso客户端回调地址
     *
     * @param ssoCallback 回调客户端的url
     * @param errorCode   回调需要带的错误码
     * <AUTHOR>
     * @date 2021/1/22 11:19
     */
    String createClientSsoCallbackErrorUrl(String ssoCallback, String errorCode);

    /**
     * 创建跳转到登录界面的url，并且参数拼接errorCode
     * <p>
     * 具体形式如下：
     * <p>
     * https://login-url?errorCode=xx
     *
     * @param ssoLoginUrlType 登录界面类型
     * @param customLoginUrl  数据库配置的自定义登录url
     * @param errorCode       错误码（为何跳转到登录页）
     * <AUTHOR>
     * @date 2021/1/22 11:19
     */
    String createLoginPageUrl(Integer ssoLoginUrlType, String customLoginUrl, String errorCode);

    /**
     * 创建跳转到错误提示界面的url
     * <p>
     * 具体形式如下：
     * <p>
     * https://error-tip-url?errorCode=xx&refer=xxx
     *
     * @param errorCode 错误码（为何跳转到登录页）
     * <AUTHOR>
     * @date 2021/1/22 11:19
     */
    String createErrorTipUrl(String errorCode);

}
