import{_ as le}from"./index-d0cfb2ce.js";import{_ as ie}from"./index-02bf6f00.js";import{_ as re,P as se,K as de,r as w,L as Z,N as ue,s as ce,k as G,a as i,c as u,d as o,w as t,b as c,g as s,t as f,h as R,O as b,Q as k,F as y,e as $,f as _,M as ee,E as te,m as I,U as _e,n as pe,B as fe,I as me,p as ge,q as he,D as Ce,l as ye,V as ve,u as we,v as be,G as ke,W as Se,J as Te,H as Pe,a_ as Ie}from"./index-18a1ea24.js";import{_ as xe}from"./SupplierSelector-e8033f79.js";/* empty css              */import{T as Ne,U as ne}from"./UniversalTree-6547889b.js";import{P as d}from"./ProductApi-52d42f8e.js";import Ee from"./ProductEdit-68ff2007.js";import Oe from"./ProductDetail-4a98f590.js";import{P as ae}from"./productCategoryApi-39e417fd.js";/* empty css              *//* empty css              *//* empty css              */import"./SupplierApi-6b9315dd.js";/* empty css              */import"./CategorySelector-5d61ae06.js";/* empty css              *//* empty css              */function De(){return new Ne().setDataSource(ae.findTree,ae.findTreeWithLazy,"searchText","parentId").setFieldMapping("categoryId","categoryName","children","hasChildren","level").setDisplayConfig({title:"\u4EA7\u54C1\u5206\u7C7B",showHeader:!0,showSearch:!0,searchPlaceholder:"\u641C\u7D22\u5206\u7C7B\u540D\u79F0",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0}).setInteractionConfig({selectable:!0,expandable:!0,lazyLoad:!1,defaultExpandLevel:1,allowMultiSelect:!1}).enableReadOnlyMode().build()}const Re={name:"ProductWithCategory",components:{PlusOutlined:se,SmallDashOutlined:de,ProductEdit:Ee,ProductDetail:Oe,UniversalTree:ne},setup(){const B=w(!1),a=w(!1),H=w(!1),e=w({}),x=w(null),M=w(""),N=w(null),p=w(null),E=De(),K=Z(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),O=Z(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),S=Z(()=>ue()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24}),m=ce({searchText:"",productCode:void 0,barcode:void 0,brand:void 0,status:void 0,unit:void 0,supplierId:void 0,pricingType:void 0}),W=d.getProductStatusOptions(),j=d.getCommonUnitOptions(),z=d.getPricingTypeOptions(),D=[{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"productCode",title:"\u5546\u54C1\u7F16\u7801",width:140,ellipsis:!0,isShow:!0},{dataIndex:"productName",title:"\u5546\u54C1\u540D\u79F0",width:200,ellipsis:!0,isShow:!0},{dataIndex:"categoryName",title:"\u4EA7\u54C1\u5206\u7C7B",width:200,ellipsis:!0,isShow:!0},{dataIndex:"productShortName",title:"\u5546\u54C1\u7B80\u79F0",width:150,ellipsis:!0,isShow:!0},{dataIndex:"barcode",title:"\u6761\u5F62\u7801",width:120,ellipsis:!0,isShow:!0},{dataIndex:"brand",title:"\u54C1\u724C",width:100,ellipsis:!0,isShow:!0},{dataIndex:"specification",title:"\u89C4\u683C",width:120,ellipsis:!0,isShow:!0},{dataIndex:"unit",title:"\u57FA\u672C\u5355\u4F4D",width:80,align:"center",isShow:!0},{dataIndex:"supplierName",title:"\u4F9B\u5E94\u5546",width:150,ellipsis:!0,isShow:!0},{dataIndex:"pricingType",title:"\u8BA1\u4EF7\u7C7B\u578B",width:100,align:"center",isShow:!0},{dataIndex:"retailPrice",title:"\u4EF7\u683C",width:120,align:"right",isShow:!0},{dataIndex:"status",title:"\u72B6\u6001",width:100,align:"center",isShow:!0},{dataIndex:"createTime",title:"\u521B\u5EFA\u65F6\u95F4",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:150,isShow:!0}],v=()=>{p.value&&p.value.reload()},g=()=>{Object.keys(m).forEach(n=>{m[n]=n==="searchText"?"":void 0}),N.value=null,M.value="",x.value&&x.value.clearSelection(),v()},h=()=>{B.value=!B.value},A=()=>{e.value={},a.value=!0},L=n=>{e.value={...n},a.value=!0},U=n=>{e.value={...n},H.value=!0},q=n=>{ee.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u5546\u54C1\u5417\uFF1F",icon:o(te),maskClosable:!0,onOk:()=>d.delete({productId:n.productId}).then(()=>{I.success("\u5220\u9664\u6210\u529F"),v()}).catch(C=>{I.error(C.message||"\u5220\u9664\u5931\u8D25")})})},J=(n,C)=>{const P=d.getProductStatusName(C);ee.confirm({title:"\u63D0\u793A",content:'\u786E\u5B9A\u8981\u5C06\u5546\u54C1\u72B6\u6001\u66F4\u65B0\u4E3A"'.concat(P,'"\u5417\uFF1F'),icon:o(te),maskClosable:!0,onOk:()=>d.updateStatus({productId:n.productId,status:C}).then(()=>{I.success("\u72B6\u6001\u66F4\u65B0\u6210\u529F"),v()}).catch(V=>{I.error(V.message||"\u72B6\u6001\u66F4\u65B0\u5931\u8D25")})})},Q=n=>{if(console.log("\u5206\u7C7B\u6811\u9009\u62E9:",n),n.keys&&n.keys.length>0){const C=n.keys[0],P=n.nodes[0];N.value=C,M.value=(P==null?void 0:P.categoryName)||"",m.categoryId=C}else N.value=null,M.value="",m.categoryId=void 0;v()},X=n=>{N.value=n,x.value&&x.value.setSelectedKeys([String(n)]),m.categoryId=n,v()},Y=({key:n})=>{n==="1"&&F()},F=()=>{var C;const n=(C=p.value)==null?void 0:C.getSelectedRows();if(!n||n.length===0){I.warning("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u6570\u636E");return}ee.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ".concat(n.length," \u6761\u6570\u636E\u5417\uFF1F"),icon:o(te),maskClosable:!0,onOk:()=>{const P=n.map(V=>V.productId);return d.batchDelete({productIdList:P}).then(()=>{I.success("\u5220\u9664\u6210\u529F"),v()}).catch(V=>{I.error(V.message||"\u5220\u9664\u5931\u8D25")})}})};return{tableRef:p,categoryTreeRef:x,treeConfig:E,superSearch:B,where:m,columns:D,currentRecord:e,currentCategoryName:M,showEdit:a,showDetailModal:H,statusOptions:W,unitOptions:j,pricingTypeOptions:z,labelCol:K,wrapperCol:O,spanCol:S,reload:v,clear:g,changeSuperSearch:h,handleCategorySelect:Q,handleCategoryTagClick:X,openAddModal:A,openEditModal:L,showDetail:U,remove:q,moreClick:Y,batchDelete:F,updateStatus:J,getProductStatusName:n=>d.getProductStatusName(n),getStatusTagColor:n=>d.getStatusTagColor(n),getPricingTypeName:n=>d.getPricingTypeName(n),getPricingTypeTagColor:n=>d.getPricingTypeTagColor(n),formatWeight:n=>d.formatWeight(n),formatVolume:n=>d.formatVolume(n),formatShelfLife:n=>d.formatShelfLife(n),formatPrice:n=>{if(!n.pricingType)return"-";switch(n.pricingType){case"NORMAL":return n.retailPrice?d.formatPrice(n.retailPrice,"NORMAL"):"-";case"WEIGHT":return n.unitPrice?d.formatPrice(n.unitPrice,"WEIGHT"):"-";case"PIECE":return n.piecePrice?d.formatPrice(n.piecePrice,"PIECE"):"-";case"VARIABLE":return n.referencePrice?d.formatPrice(n.referencePrice,"VARIABLE"):"-";default:return"-"}}}}},Me={class:"guns-layout"},Ae={class:"guns-layout-sidebar width-100 p-t-12"},Le={class:"sidebar-content"},Ue={class:"guns-layout-content"},Ve={class:"guns-layout"},Be={class:"guns-layout-content-application"},We={class:"content-main"},ze={class:"content-main-header"},Fe={class:"header-content"},Ge={class:"header-content-left"},He={key:0,class:"current-category-info"},Ke={class:"header-content-right"},je={class:"content-main-body"},qe={class:"table-content"},Je={key:0,class:"super-search",style:{"margin-top":"8px"}},Qe={key:0},Xe={key:0};function Ye(B,a,H,e,x,M){const N=ne,p=_e,E=pe,K=G("plus-outlined"),O=fe,S=me,m=ge,W=he,j=G("small-dash-outlined"),z=Ce,D=ye,v=ve,g=we,h=be,A=ke,L=Se,U=Te,q=xe,J=Pe,Q=Ie,X=ie,Y=le,F=G("product-edit"),oe=G("product-detail");return i(),u("div",Me,[o(Y,{width:"292px",cacheKey:"ERP_PRODUCT_MANAGEMENT"},{content:t(()=>[c("div",Ue,[c("div",Ve,[c("div",Be,[c("div",We,[c("div",ze,[c("div",Fe,[c("div",Ge,[o(E,{size:16},{default:t(()=>[e.currentCategoryName?(i(),u("span",He,[a[11]||(a[11]=s(" \u5F53\u524D\u5206\u7C7B\uFF1A")),o(p,{color:"blue"},{default:t(()=>[s(f(e.currentCategoryName),1)]),_:1})])):R("",!0)]),_:1})]),c("div",Ke,[o(E,{size:16},{default:t(()=>[o(O,{type:"primary",class:"border-radius",onClick:e.openAddModal},{default:t(()=>[o(K),a[12]||(a[12]=s(" \u65B0\u589E\u5546\u54C1 "))]),_:1,__:[12]},8,["onClick"]),o(z,null,{overlay:t(()=>[o(W,{onClick:e.moreClick},{default:t(()=>[o(m,{key:"1"},{default:t(()=>[o(S,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[13]||(a[13]=c("span",null,"\u6279\u91CF\u5220\u9664",-1))]),_:1,__:[13]})]),_:1},8,["onClick"])]),default:t(()=>[o(O,{class:"border-radius"},{default:t(()=>[a[14]||(a[14]=s(" \u66F4\u591A ")),o(j)]),_:1,__:[14]})]),_:1})]),_:1})])])]),c("div",je,[c("div",qe,[o(X,{columns:e.columns,where:e.where,fieldBusinessCode:"ERP_PRODUCT_TABLE",showTableTool:"",showToolTotal:!1,rowId:"productId",ref:"tableRef",url:"/erp/product/page"},{toolLeft:t(()=>[o(D,{value:e.where.searchText,"onUpdate:value":a[0]||(a[0]=l=>e.where.searchText=l),bordered:!1,allowClear:"",placeholder:"\u5546\u54C1\u540D\u79F0\u3001\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:e.reload,style:{width:"240px"},class:"search-input"},{prefix:t(()=>[o(S,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"]),o(v,{type:"vertical",class:"divider"}),c("a",{onClick:a[1]||(a[1]=(...l)=>e.changeSuperSearch&&e.changeSuperSearch(...l))},f(e.superSearch?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:t(()=>[e.superSearch?(i(),u("div",Je,[o(J,{model:e.where,labelCol:e.labelCol,"wrapper-col":e.wrapperCol},{default:t(()=>[o(A,{gutter:16},{default:t(()=>[o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u5546\u54C1\u7F16\u7801:"},{default:t(()=>[o(D,{value:e.where.productCode,"onUpdate:value":a[2]||(a[2]=l=>e.where.productCode=l),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u7F16\u7801",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u6761\u5F62\u7801:"},{default:t(()=>[o(D,{value:e.where.barcode,"onUpdate:value":a[3]||(a[3]=l=>e.where.barcode=l),placeholder:"\u8BF7\u8F93\u5165\u6761\u5F62\u7801",allowClear:""},null,8,["value"])]),_:1})]),_:1},16),o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u54C1\u724C:"},{default:t(()=>[o(D,{value:e.where.brand,"onUpdate:value":a[4]||(a[4]=l=>e.where.brand=l),placeholder:"\u8BF7\u8F93\u5165\u54C1\u724C",allowClear:""},null,8,["value"])]),_:1})]),_:1},16)]),_:1}),o(A,{gutter:16},{default:t(()=>[o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u72B6\u6001:"},{default:t(()=>[o(U,{value:e.where.status,"onUpdate:value":a[5]||(a[5]=l=>e.where.status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",style:{width:"100%"},allowClear:""},{default:t(()=>[(i(!0),u(y,null,$(e.statusOptions,l=>(i(),_(L,{key:l.value,value:l.value},{default:t(()=>[s(f(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u57FA\u672C\u5355\u4F4D:"},{default:t(()=>[o(U,{value:e.where.unit,"onUpdate:value":a[6]||(a[6]=l=>e.where.unit=l),placeholder:"\u8BF7\u9009\u62E9\u57FA\u672C\u5355\u4F4D",style:{width:"100%"},allowClear:""},{default:t(()=>[(i(!0),u(y,null,$(e.unitOptions,l=>(i(),_(L,{key:l.value,value:l.value},{default:t(()=>[s(f(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16)]),_:1}),o(A,{gutter:16},{default:t(()=>[o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u4F9B\u5E94\u5546:"},{default:t(()=>[o(q,{value:e.where.supplierId,"onUpdate:value":a[7]||(a[7]=l=>e.where.supplierId=l),placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",allowClear:!0},null,8,["value"])]),_:1})]),_:1},16),o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:"\u8BA1\u4EF7\u7C7B\u578B:"},{default:t(()=>[o(U,{value:e.where.pricingType,"onUpdate:value":a[8]||(a[8]=l=>e.where.pricingType=l),placeholder:"\u8BF7\u9009\u62E9\u8BA1\u4EF7\u7C7B\u578B",style:{width:"100%"},allowClear:""},{default:t(()=>[(i(!0),u(y,null,$(e.pricingTypeOptions,l=>(i(),_(L,{key:l.value,value:l.value},{default:t(()=>[s(f(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},16),o(h,b(k(e.spanCol)),{default:t(()=>[o(g,{label:" ",class:"not-label"},{default:t(()=>[o(E,{size:16},{default:t(()=>[o(O,{class:"border-radius",onClick:e.reload,type:"primary"},{default:t(()=>a[15]||(a[15]=[s("\u67E5\u8BE2")])),_:1,__:[15]},8,["onClick"]),o(O,{class:"border-radius",onClick:e.clear},{default:t(()=>a[16]||(a[16]=[s("\u91CD\u7F6E")])),_:1,__:[16]},8,["onClick"])]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])])):R("",!0)]),bodyCell:t(({column:l,record:r})=>[l.dataIndex==="status"?(i(),_(p,{key:0,color:e.getStatusTagColor(r.status)},{default:t(()=>[s(f(e.getProductStatusName(r.status)),1)]),_:2},1032,["color"])):l.dataIndex==="categoryName"?(i(),u(y,{key:1},[r.categoryName?(i(),_(p,{key:0,color:"blue",onClick:T=>e.handleCategoryTagClick(r.categoryId)},{default:t(()=>[s(f(r.categoryName),1)]),_:2},1032,["onClick"])):(i(),_(p,{key:1,color:"default"},{default:t(()=>a[17]||(a[17]=[s("\u672A\u5206\u7C7B")])),_:1,__:[17]}))],64)):l.dataIndex==="weight"?(i(),u(y,{key:2},[s(f(e.formatWeight(r.weight)),1)],64)):l.dataIndex==="volume"?(i(),u(y,{key:3},[s(f(e.formatVolume(r.volume)),1)],64)):l.dataIndex==="shelfLife"?(i(),u(y,{key:4},[s(f(e.formatShelfLife(r.shelfLife)),1)],64)):l.dataIndex==="supplierName"?(i(),u(y,{key:5},[r.supplierName?(i(),u("span",Qe,f(r.supplierName),1)):(i(),_(p,{key:1,color:"default"},{default:t(()=>a[18]||(a[18]=[s("\u672A\u8BBE\u7F6E")])),_:1,__:[18]}))],64)):l.dataIndex==="pricingType"?(i(),u(y,{key:6},[r.pricingType?(i(),_(p,{key:0,color:e.getPricingTypeTagColor(r.pricingType)},{default:t(()=>[s(f(e.getPricingTypeName(r.pricingType)),1)]),_:2},1032,["color"])):(i(),_(p,{key:1,color:"default"},{default:t(()=>a[19]||(a[19]=[s("\u672A\u8BBE\u7F6E")])),_:1,__:[19]}))],64)):l.dataIndex==="retailPrice"?(i(),u(y,{key:7},[r.retailPrice||r.unitPrice||r.piecePrice||r.referencePrice?(i(),u("span",Xe,f(e.formatPrice(r)),1)):(i(),_(p,{key:1,color:"default"},{default:t(()=>a[20]||(a[20]=[s("\u672A\u8BBE\u7F6E")])),_:1,__:[20]}))],64)):l.key==="action"?(i(),_(E,{key:8,size:16},{default:t(()=>[o(S,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:T=>e.openEditModal(r)},null,8,["onClick"]),o(S,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:T=>e.remove(r)},null,8,["onClick"]),o(z,null,{overlay:t(()=>[o(W,null,{default:t(()=>[r.status==="ACTIVE"?(i(),_(m,{key:0,onClick:T=>e.updateStatus(r,"INACTIVE")},{default:t(()=>a[21]||(a[21]=[s(" \u505C\u7528 ")])),_:2,__:[21]},1032,["onClick"])):R("",!0),r.status==="INACTIVE"?(i(),_(m,{key:1,onClick:T=>e.updateStatus(r,"ACTIVE")},{default:t(()=>a[22]||(a[22]=[s(" \u542F\u7528 ")])),_:2,__:[22]},1032,["onClick"])):R("",!0),r.status!=="DISCONTINUED"?(i(),_(m,{key:2,onClick:T=>e.updateStatus(r,"DISCONTINUED")},{default:t(()=>a[23]||(a[23]=[s(" \u505C\u4EA7 ")])),_:2,__:[23]},1032,["onClick"])):R("",!0),o(Q),o(m,{onClick:T=>e.showDetail(r)},{default:t(()=>a[24]||(a[24]=[s(" \u8BE6\u60C5 ")])),_:2,__:[24]},1032,["onClick"])]),_:2},1024)]),default:t(()=>[o(S,{iconClass:"icon-opt-gengduo","font-size":"24px",title:"\u66F4\u591A",color:"#60666b"})]),_:2},1024)]),_:2},1024)):R("",!0)]),_:1},8,["columns","where"])])])])])])])]),default:t(()=>[c("div",Ae,[c("div",Le,[o(N,{ref:"categoryTreeRef","data-source":e.treeConfig.dataSource,"field-mapping":e.treeConfig.fieldMapping,"display-config":e.treeConfig.displayConfig,"interaction-config":e.treeConfig.interactionConfig,"action-config":e.treeConfig.actionConfig,onSelect:e.handleCategorySelect},null,8,["data-source","field-mapping","display-config","interaction-config","action-config","onSelect"])])])]),_:1}),o(F,{visible:e.showEdit,"onUpdate:visible":a[9]||(a[9]=l=>e.showEdit=l),data:e.currentRecord,onDone:e.reload},null,8,["visible","data","onDone"]),o(oe,{visible:e.showDetailModal,"onUpdate:visible":a[10]||(a[10]=l=>e.showDetailModal=l),data:e.currentRecord},null,8,["visible","data"])])}const vt=re(Re,[["render",Ye],["__scopeId","data-v-fb1fe0bb"]]);export{vt as default};
