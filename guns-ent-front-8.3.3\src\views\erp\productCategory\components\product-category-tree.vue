<template>
  <universal-tree
    ref="universalTreeRef"
    :data-source="dataSourceConfig"
    :field-mapping="fieldMappingConfig"
    :display-config="displayConfigValue"
    :interaction-config="interactionConfig"
    :action-config="actionConfigValue"
    @select="handleSelect"
    @expand="handleExpand"
    @search="handleSearch"
    @add="handleAdd"
    @edit="handleEdit"
    @delete="handleDelete"
    @load="handleLoad"
    @loadError="handleLoadError"
  />
</template>

<script setup>
import { ref, computed, createVNode } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { ProductCategoryApi } from '../api/productCategoryApi'
import UniversalTree from '@/components/UniversalTree/UniversalTree.vue'

// 定义组件名称
defineOptions({
  name: 'ProductCategoryTree'
})

// 定义Props
const props = defineProps({
  isShowEditIcon: {
    type: Boolean,
    default: false
  },
  isSetWidth: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['treeSelect', 'addCategory', 'editCategory', 'deleteCategory'])

// 组件引用
const universalTreeRef = ref()

// 数据源配置
const dataSourceConfig = {
  api: ProductCategoryApi.findTree,
  lazyLoadApi: ProductCategoryApi.findTreeWithLazy,
  searchParam: 'searchText',
  parentIdParam: 'parentId'
}

// 字段映射配置
const fieldMappingConfig = {
  key: 'categoryId',
  title: 'categoryName',
  children: 'children',
  hasChildren: 'hasChildren',
  level: 'categoryLevel'
}

// 显示配置（响应式）
const displayConfigValue = computed(() => ({
  title: '产品分类',
  showHeader: props.isSetWidth,
  showSearch: true,
  searchPlaceholder: '请输入分类名称，回车搜索',
  showAddButton: props.isShowEditIcon,
  showEditIcons: props.isShowEditIcon,
  showIcon: false,
  isSetWidth: props.isSetWidth
}))

// 交互配置
const interactionConfig = {
  selectable: true,
  expandable: true,
  lazyLoad: true,
  defaultExpandLevel: 2,
  allowMultiSelect: false
}

// 操作配置（响应式）
const actionConfigValue = computed(() => ({
  allowAdd: props.isShowEditIcon,
  allowEdit: props.isShowEditIcon,
  allowDelete: props.isShowEditIcon
}))

// 事件处理方法
const handleSelect = (selectInfo) => {
  const { keys, nodes } = selectInfo
  emit('treeSelect', keys, { selectedNodes: nodes })
}

const handleExpand = (expandedKeys) => {
  // 可以在这里处理展开事件，如果需要的话
}

const handleSearch = (searchText) => {
  // 搜索事件已经在UniversalTree内部处理
}

const handleAdd = (parentNode) => {
  emit('addCategory', parentNode)
}

const handleEdit = (node) => {
  emit('editCategory', node)
}

const handleDelete = (node) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${node.categoryName}"吗？`,
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      return ProductCategoryApi.delete({ categoryId: node.categoryId })
        .then(() => {
          message.success('删除成功')
          emit('deleteCategory', node)
          // 刷新树数据
          reload()
        })
        .catch(error => {
          console.error('删除分类失败:', error)
          message.error('删除失败')
        })
    }
  })
}

const handleLoad = (data) => {
  // 数据加载成功事件
}

const handleLoadError = (error) => {
  console.error('产品分类树数据加载失败:', error)
}

// 暴露给父组件的方法
const reload = () => {
  universalTreeRef.value?.reload()
}

const getSelectedNodes = () => {
  return universalTreeRef.value?.getSelectedNodes()
}

const setSelectedKeys = (keys) => {
  universalTreeRef.value?.setSelectedKeys(keys)
}

// 为了向后兼容，保留原有的方法名和属性
const reloadCategoryTreeData = reload
const currentSelectKeys = computed(() => universalTreeRef.value?.getSelectedNodes() || [])

// 暴露方法给父组件
defineExpose({
  reload,
  reloadCategoryTreeData,
  getSelectedNodes,
  setSelectedKeys,
  currentSelectKeys
})
</script>

<style scoped lang="less">
// 样式已经在UniversalTree组件中统一处理
</style>
