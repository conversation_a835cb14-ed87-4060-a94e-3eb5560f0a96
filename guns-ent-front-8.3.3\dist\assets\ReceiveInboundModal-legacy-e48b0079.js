System.register(["./index-legacy-ee1db0c7.js","./index-legacy-510bfbb8.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-94a6fc23.js","./index-legacy-45c79de7.js","./index-legacy-e24582b9.js","./PurchaseApi-legacy-77810512.js"],(function(e,t){"use strict";var a,l,i,n,d,o,r,u,c,s,f,v,m,p,y,g,h,_,k,A,Q,x,b,C,w,R,T,L,D,F,z,U,M,P;return{setters:[e=>{a=e._,l=e.r,i=e.s,n=e.X,d=e.L,o=e.a,r=e.f,u=e.w,c=e.d,s=e.g,f=e.b,v=e.t,m=e.c,p=e.h,y=e.F,g=e.a2,h=e.m,_=e.B,k=e.a3,A=e.Y,Q=e.Z,x=e.a0,b=e.W,C=e.J,w=e.u,R=e.v,T=e.l,L=e.y,D=e.G,F=e.H,z=e.i,U=e.a4,M=e.M},null,null,null,null,null,null,null,e=>{P=e.P}],execute:function(){var t=document.createElement("style");t.textContent=".receive-inbound-content[data-v-dd8968e8]{max-height:70vh;overflow-y:auto}.order-no[data-v-dd8968e8]{font-weight:500;color:#1890ff}.product-info[data-v-dd8968e8]{text-align:left}.product-name[data-v-dd8968e8]{font-weight:500;color:#262626;margin-bottom:4px}.product-details[data-v-dd8968e8]{font-size:12px;color:#8c8c8c}.product-code[data-v-dd8968e8]{margin-right:8px}.product-spec[data-v-dd8968e8]{margin-left:8px}.receive-amount[data-v-dd8968e8]{font-weight:500;color:#1890ff}.diff-positive[data-v-dd8968e8]{color:#52c41a;font-weight:500}.diff-negative[data-v-dd8968e8]{color:#ff4d4f;font-weight:500}.diff-zero[data-v-dd8968e8]{color:#8c8c8c}.ant-statistic[data-v-dd8968e8]{text-align:center}.ant-statistic-title[data-v-dd8968e8]{color:#8c8c8c;font-size:14px}.ant-statistic-content[data-v-dd8968e8]{color:#262626;font-size:20px;font-weight:500}\n",document.head.appendChild(t);const j={name:"ReceiveInboundModal",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:t}){const a=l(null),o=l(null),r=l(!1),u=i({id:null,paymentMethod:"",paymentAccount:"",actualTotalAmount:null,receiveRemark:"",detailList:[]});n((()=>e.data),(e=>{e&&e.id&&(u.id=e.id,u.paymentMethod="",u.paymentAccount="",u.actualTotalAmount=e.totalAmount||null,u.receiveRemark="",u.detailList=(e.detailList||[]).map((e=>({...e,orderQuantity:e.quantity,receiveQuantity:e.quantity,diffQuantity:0,receiveAmount:e.totalPrice,remark:""}))))}),{immediate:!0});const c=d((()=>u.detailList?u.detailList.length:0)),s=d((()=>u.detailList&&0!==u.detailList.length?u.detailList.reduce(((e,t)=>e+(parseFloat(t.orderQuantity)||0)),0):0)),f=d((()=>u.detailList&&0!==u.detailList.length?u.detailList.reduce(((e,t)=>e+(parseFloat(t.receiveQuantity)||0)),0):0)),v=d((()=>u.detailList&&0!==u.detailList.length?u.detailList.reduce(((e,t)=>e+(parseFloat(t.receiveAmount)||0)),0):0));return{formRef:a,receiveFormRef:o,loading:r,receiveData:u,rules:{},receiveRules:{paymentMethod:[{required:!0,message:"请选择付款方式",trigger:"change"}]},columns:[{title:"商品信息",key:"productInfo",width:200,fixed:"left"},{title:"订单数量",key:"orderQuantity",width:100,align:"center"},{title:"入库数量",key:"receiveQuantity",width:120,align:"center"},{title:"差异数量",key:"diffQuantity",width:100,align:"center"},{title:"单价",key:"unitPrice",width:100,align:"right"},{title:"入库金额",key:"receiveAmount",width:120,align:"right"},{title:"备注",key:"remark",width:150}],productCount:c,orderTotalQuantity:s,receiveTotalQuantity:f,receiveTotalAmount:v,getPrecision:e=>"WEIGHT"===e?3:0,getStep:e=>"WEIGHT"===e?.001:1,getQuantityUnit:e=>{switch(e.pricingType){case"WEIGHT":return"kg";case"PIECE":return"件";default:return e.unit||"个"}},formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",formatDiffQuantity:e=>{const t=parseFloat(e)||0;return t>=0?`+${t}`:`${t}`},getDiffQuantityClass:e=>{const t=parseFloat(e)||0;return t>0?"diff-positive":t<0?"diff-negative":"diff-zero"},onReceiveQuantityChange:(e,t)=>{const a=parseFloat(e.orderQuantity)||0,l=parseFloat(e.receiveQuantity)||0,i=parseFloat(e.unitPrice)||0;e.diffQuantity=l-a,e.receiveAmount=l*i},handleCancel:()=>{t("update:visible",!1)},handleReceive:()=>{o.value.validate().then((()=>a.value.validate())).then((()=>{u.id?(r.value=!0,P.receive({id:u.id,paymentMethod:u.paymentMethod,paymentAccount:u.paymentAccount,actualTotalAmount:u.actualTotalAmount,receiveRemark:u.receiveRemark}).then((()=>{h.success("入库成功"),t("ok")})).catch((e=>{h.error("入库失败："+(e.message||"未知错误"))})).finally((()=>{r.value=!1}))):h.error("入库单信息不完整")})).catch((()=>{h.error("请完善入库信息")}))}}}},I={class:"receive-inbound-content"},E={class:"order-no"},H={key:0,class:"product-info"},N={class:"product-name"},S={class:"product-details"},W={class:"product-code"},q={key:0,class:"product-spec"},B={key:5,class:"receive-amount"};e("default",a(j,[["render",function(e,t,a,l,i,n){const d=_,h=k,P=A,j=Q,G=x,Y=b,$=C,J=w,K=R,O=T,X=L,Z=D,V=F,ee=z,te=U,ae=M;return o(),r(ae,{visible:a.visible,title:"执行入库操作",width:1e3,maskClosable:!1,onCancel:l.handleCancel},{footer:u((()=>[c(d,{onClick:l.handleCancel},{default:u((()=>t[4]||(t[4]=[s("取消")]))),_:1,__:[4]},8,["onClick"]),c(d,{type:"primary",loading:l.loading,onClick:l.handleReceive},{default:u((()=>t[5]||(t[5]=[s(" 确认入库 ")]))),_:1,__:[5]},8,["loading","onClick"])])),default:u((()=>[f("div",I,[c(h,{message:"入库操作提醒",description:"执行入库操作后，商品库存将会增加，入库单状态变为已完成，请仔细核对入库数量。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}}),c(G,{title:"入库单信息",size:"small",style:{"margin-bottom":"16px"}},{default:u((()=>[c(j,{column:3,bordered:"",size:"small"},{default:u((()=>[c(P,{label:"入库单号"},{default:u((()=>[f("span",E,v(a.data.orderNo),1)])),_:1}),c(P,{label:"供应商"},{default:u((()=>[s(v(a.data.supplierName),1)])),_:1}),c(P,{label:"订单日期"},{default:u((()=>[s(v(a.data.orderDate),1)])),_:1})])),_:1})])),_:1}),c(G,{title:"入库信息",size:"small",style:{"margin-bottom":"16px"}},{default:u((()=>[c(V,{ref:"receiveFormRef",model:l.receiveData,rules:l.receiveRules,"label-col":{span:6},"wrapper-col":{span:18}},{default:u((()=>[c(Z,{gutter:16},{default:u((()=>[c(K,{span:12},{default:u((()=>[c(J,{label:"付款方式",name:"paymentMethod"},{default:u((()=>[c($,{value:l.receiveData.paymentMethod,"onUpdate:value":t[0]||(t[0]=e=>l.receiveData.paymentMethod=e),placeholder:"请选择付款方式"},{default:u((()=>[c(Y,{value:"CASH"},{default:u((()=>t[6]||(t[6]=[s("现金")]))),_:1,__:[6]}),c(Y,{value:"BANK_TRANSFER"},{default:u((()=>t[7]||(t[7]=[s("银行转账")]))),_:1,__:[7]}),c(Y,{value:"ALIPAY"},{default:u((()=>t[8]||(t[8]=[s("支付宝")]))),_:1,__:[8]}),c(Y,{value:"WECHAT"},{default:u((()=>t[9]||(t[9]=[s("微信支付")]))),_:1,__:[9]}),c(Y,{value:"CREDIT"},{default:u((()=>t[10]||(t[10]=[s("赊账")]))),_:1,__:[10]})])),_:1},8,["value"])])),_:1})])),_:1}),c(K,{span:12},{default:u((()=>[c(J,{label:"付款账户",name:"paymentAccount"},{default:u((()=>[c(O,{value:l.receiveData.paymentAccount,"onUpdate:value":t[1]||(t[1]=e=>l.receiveData.paymentAccount=e),placeholder:"请输入付款账户"},null,8,["value"])])),_:1})])),_:1}),c(K,{span:12},{default:u((()=>[c(J,{label:"实际总金额",name:"actualTotalAmount"},{default:u((()=>[c(X,{value:l.receiveData.actualTotalAmount,"onUpdate:value":t[2]||(t[2]=e=>l.receiveData.actualTotalAmount=e),min:0,precision:2,style:{width:"100%"},placeholder:"请输入实际总金额"},{addonBefore:u((()=>t[11]||(t[11]=[s("¥")]))),_:1},8,["value"])])),_:1})])),_:1}),c(K,{span:12},{default:u((()=>[c(J,{label:"入库备注",name:"receiveRemark"},{default:u((()=>[c(O,{value:l.receiveData.receiveRemark,"onUpdate:value":t[3]||(t[3]=e=>l.receiveData.receiveRemark=e),placeholder:"请输入入库备注"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1}),c(G,{title:"入库明细",size:"small",style:{"margin-bottom":"16px"}},{default:u((()=>[c(V,{ref:"formRef",model:l.receiveData,rules:l.rules},{default:u((()=>[c(ee,{columns:l.columns,"data-source":l.receiveData.detailList,pagination:!1,size:"small",bordered:"",scroll:{y:400}},{bodyCell:u((({column:e,record:t,index:a})=>["productInfo"===e.key?(o(),m("div",H,[f("div",N,v(t.productName),1),f("div",S,[f("span",W,v(t.productCode),1),t.specification?(o(),m("span",q,v(t.specification),1)):p("",!0)])])):p("",!0),"orderQuantity"===e.key?(o(),m(y,{key:1},[s(v(t.orderQuantity)+" "+v(l.getQuantityUnit(t)),1)],64)):p("",!0),"receiveQuantity"===e.key?(o(),r(J,{key:2,name:["detailList",a,"receiveQuantity"],rules:[{required:!0,message:"请输入入库数量"},{type:"number",min:0,message:"入库数量不能小于0"}],style:{"margin-bottom":"0"}},{default:u((()=>[c(X,{value:t.receiveQuantity,"onUpdate:value":e=>t.receiveQuantity=e,min:0,precision:l.getPrecision(t.pricingType),step:l.getStep(t.pricingType),style:{width:"100%"},onChange:e=>l.onReceiveQuantityChange(t,a)},{addonAfter:u((()=>[s(v(l.getQuantityUnit(t)),1)])),_:2},1032,["value","onUpdate:value","precision","step","onChange"])])),_:2},1032,["name"])):p("",!0),"diffQuantity"===e.key?(o(),m("span",{key:3,class:g(l.getDiffQuantityClass(t.diffQuantity))},v(l.formatDiffQuantity(t.diffQuantity))+" "+v(l.getQuantityUnit(t)),3)):p("",!0),"unitPrice"===e.key?(o(),m(y,{key:4},[s(" ¥"+v(l.formatAmount(t.unitPrice)),1)],64)):p("",!0),"receiveAmount"===e.key?(o(),m("span",B,"¥"+v(l.formatAmount(t.receiveAmount)),1)):p("",!0),"remark"===e.key?(o(),r(O,{key:6,value:t.remark,"onUpdate:value":e=>t.remark=e,placeholder:"入库备注",size:"small"},null,8,["value","onUpdate:value"])):p("",!0)])),_:1},8,["columns","data-source"])])),_:1},8,["model","rules"])])),_:1}),c(G,{title:"入库汇总",size:"small"},{default:u((()=>[c(Z,{gutter:16},{default:u((()=>[c(K,{span:6},{default:u((()=>[c(te,{title:"商品种类",value:l.productCount,suffix:"种"},null,8,["value"])])),_:1}),c(K,{span:6},{default:u((()=>[c(te,{title:"订单总量",value:l.orderTotalQuantity,suffix:"件"},null,8,["value"])])),_:1}),c(K,{span:6},{default:u((()=>[c(te,{title:"入库总量",value:l.receiveTotalQuantity,suffix:"件"},null,8,["value"])])),_:1}),c(K,{span:6},{default:u((()=>[c(te,{title:"入库金额",value:l.receiveTotalAmount,prefix:"¥",precision:2},null,8,["value"])])),_:1})])),_:1})])),_:1})])])),_:1},8,["visible","onCancel"])}],["__scopeId","data-v-dd8968e8"]]))}}}));
