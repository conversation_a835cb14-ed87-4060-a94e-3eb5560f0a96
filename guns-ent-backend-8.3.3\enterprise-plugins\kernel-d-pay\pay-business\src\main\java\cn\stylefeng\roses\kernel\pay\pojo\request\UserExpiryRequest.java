package cn.stylefeng.roses.kernel.pay.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户商品到期时间封装类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserExpiryRequest extends BaseRequest {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {edit.class, delete.class})
    @ChineseDescription("主键id")
    private Long expiryId;
    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("用户id")
    private Long userId;
    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空", groups = {add.class, edit.class})
    @ChineseDescription("商品id")
    private Long goodsId;
    /**
     * 商品到期时间
     */
    @NotNull(message = "商品到期时间不能为空", groups = {add.class, edit.class})
    @ChineseDescription("商品到期时间")
	private String expiryDate;

    /**
     * 批量删除用的id集合
     */
    @NotNull(message = "批量删除id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("批量删除用的id集合")
    private List<Long> batchDeleteIdList;

}