import{_ as z,r as i,L as E,o as $,X as G,aK as H,a as o,f as d,w as r,b as u,t as c,c as _,h as y,g as M,aR as k,d as f,F as U,e as X,aS as L,B as q,u as J,v as P,G as Q,H as W,ch as Y}from"./index-18a1ea24.js";import{O as C}from"./OrgApi-021dd6dd.js";import Z from"./org-add-edit-1a7fc69a.js";import ee from"./set-approver-dcab71b1.js";import"./org-form-47675b1b.js";import"./index-3a0e5c06.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */const te={class:"top"},ae={class:"top-left"},oe={class:"orgName"},se={key:0,class:"short-name"},le={class:"top-right"},ne={class:"content"},ie={class:"content-item"},re={key:0},ue={key:1},ve={key:2},ce={key:3},_e={class:"content-item"},me={__name:"org-detail",props:{visible:Boolean,data:Object,isShowApprover:Boolean,levelNumberList:Array},emits:["update:visible","done"],setup(w,{emit:S}){const l=w,x=S,n=i("1"),D=i([{key:"1",name:"\u57FA\u7840\u4FE1\u606F",icon:"icon-tab-jichuxinxi"},{key:"2",name:"\u5BA1\u6279\u4EBA",icon:"icon-opt-shenpirenshezhi"}]),t=i({}),p=i(!1),g=i(null),B=i([{name:"\u4E0A\u673A\u673A\u6784",value:"parentOrgName"},{name:"\u673A\u6784\u7F16\u7801",value:"orgCode"},{name:"\u673A\u6784\u7C7B\u578B",value:"orgType"},{name:"\u673A\u6784\u7B80\u79F0",value:"orgShortName"},{name:"\u673A\u6784\u72B6\u6001",value:"statusFlag"},{name:"\u673A\u6784\u5C42\u7EA7",value:"levelCode"},{name:"\u7A0E\u53F7",value:"taxNo"},{name:"\u5907\u6CE8",value:"remark"},{name:"\u6392\u5E8F",value:"orgSort"}]),N=i([]),A=E(()=>{var s;let e="";if((s=t.value)!=null&&s.levelCode){let m=N.value.find(v=>v.levelCode==t.value.levelCode);if(m){e=m.levelName;let v=l.levelNumberList.find(h=>h.value==m.levelNumber);v&&(e+="("+v.name+")")}}return e});$(()=>{l.isShowApprover&&(n.value="2"),O(),b()}),G(()=>l.data,e=>{e&&b()},{deep:!0});const b=()=>{C.detail({orgId:l.data.orgId}).then(e=>{t.value=Object.assign({},e)}),H(()=>{g.value.getListData()})},O=async()=>{N.value=await C.organizationLevelList()},T=e=>{x("update:visible",e)},F=e=>{n.value=e},V=()=>{n.value=="2"?g.value.getListData():p.value=!0};return(e,s)=>{const m=q,v=J,h=P,K=Q,R=W,j=Y;return o(),d(j,{width:800,visible:l.visible,title:"\u673A\u6784\u4FE1\u606F",onClose:s[1]||(s[1]=a=>T(!1)),isShowTab:!0,activeKey:n.value,tabList:D.value,onTabChange:F},{top:r(()=>[u("div",te,[u("div",ae,[u("span",oe,c(t.value.orgName),1),t.value.orgShortName?(o(),_("span",se,"("+c(t.value.orgShortName)+")",1)):y("",!0)]),u("div",le,[n.value=="1"?(o(),d(m,{key:0,type:"primary",class:"border-radius",onClick:V},{default:r(()=>s[2]||(s[2]=[M("\u7F16\u8F91")])),_:1,__:[2]})):y("",!0)])])]),default:r(()=>[u("div",ne,[k(u("div",ie,[f(R,{ref:"formRef",model:t.value,"label-col":{span:6}},{default:r(()=>[f(K,{gutter:16},{default:r(()=>[(o(!0),_(U,null,X(B.value,(a,I)=>(o(),d(h,{span:12,key:I},{default:r(()=>[f(v,{label:a.name},{default:r(()=>[a.value=="orgType"?(o(),_("span",re,c(t.value[a.value]==1?"\u516C\u53F8":"\u90E8\u95E8"),1)):a.value=="statusFlag"?(o(),_("span",ue,c(t.value[a.value]==1?"\u542F\u7528":"\u7981\u7528"),1)):a.value=="levelCode"?(o(),_("span",ve,c(A.value),1)):(o(),_("span",ce,c(t.value[a.value]),1))]),_:2},1032,["label"])]),_:2},1024))),128))]),_:1})]),_:1},8,["model"])],512),[[L,n.value=="1"]]),k(u("div",_e,[f(ee,{ref_key:"setApproverRef",ref:g,data:l.data},null,8,["data"])],512),[[L,n.value=="2"]])]),p.value?(o(),d(Z,{key:0,visible:p.value,"onUpdate:visible":s[0]||(s[0]=a=>p.value=a),data:t.value,onDone:b,levelNumberList:l.levelNumberList},null,8,["visible","data","levelNumberList"])):y("",!0)]),_:1},8,["visible","activeKey","tabList"])}}},Be=z(me,[["__scopeId","data-v-857291ab"]]);export{Be as default};
