import{_ as O,P as E,r as I,s as U,ag as w,L as v,X as F,k as P,a as A,f as j,w as l,d as t,g as k,t as B,m as g,B as T,l as z,u as R,v as V,al as G,G as H,$ as Q,a0 as X,a4 as J,H as K,M as W}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{_ as q}from"./SupplierSelector-e8033f79.js";import{P as L}from"./PurchaseApi-38cc3d1a.js";import Z from"./InboundDetailTable-342420f3.js";import $ from"./ProductSelectorModal-01685223.js";import"./SupplierApi-6b9315dd.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./ProductApi-52d42f8e.js";import"./productCategoryApi-39e417fd.js";const ee={name:"InboundForm",components:{PlusOutlined:E,SupplierSelector:q,InboundDetailTable:Z,ProductSelectorModal:$},props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(c,{emit:n}){const s=I(null),e=I(!1),b=I(!1),r=U({id:null,orderNo:"",supplierId:null,orderDate:w().format("YYYY-MM-DD"),remark:"",detailList:[]}),m={supplierId:[{required:!0,message:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",trigger:"change"}],orderDate:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BA2\u5355\u65E5\u671F",trigger:"change"}]},d=v(()=>!!(c.data&&c.data.id)),u=v(()=>r.detailList?r.detailList.length:0),i=v(()=>!r.detailList||r.detailList.length===0?0:r.detailList.reduce((a,o)=>a+(parseFloat(o.quantity)||0),0)),p=v(()=>!r.detailList||r.detailList.length===0?0:r.detailList.reduce((a,o)=>{const M=parseFloat(o.quantity)||0,h=parseFloat(o.unitPrice)||0;return a+M*h},0)),D=v(()=>i.value===0?0:p.value/i.value);F(()=>c.data,a=>{a&&Object.keys(a).length>0&&Object.assign(r,{id:a.id,orderNo:a.orderNo,supplierId:a.supplierId,orderDate:a.orderDate,remark:a.remark,detailList:a.detailList||[]})},{immediate:!0}),F(()=>c.visible,async a=>{a&&(d.value||(_(),await y()))});const _=()=>{Object.assign(r,{id:null,orderNo:"",supplierId:null,orderDate:w().format("YYYY-MM-DD"),remark:"",detailList:[]}),s.value&&s.value.clearValidate()},y=async()=>{try{const a=await L.generateOrderNo();a.success&&(r.orderNo=a.data)}catch(a){console.error("\u751F\u6210\u5165\u5E93\u5355\u53F7\u5931\u8D25:",a)}};return{formRef:s,loading:e,showProductSelector:b,formData:r,rules:m,isEdit:d,productCount:u,totalQuantity:i,totalAmount:p,averagePrice:D,onSupplierChange:a=>{r.detailList=[]},onDetailChange:a=>{r.detailList=a},addProduct:()=>{if(!r.supplierId){g.warning("\u8BF7\u5148\u9009\u62E9\u4F9B\u5E94\u5546");return}b.value=!0},onProductSelect:a=>{a.forEach(o=>{r.detailList.find(h=>h.productId===o.productId)||r.detailList.push({productId:o.productId,productCode:o.productCode,productName:o.productName,specification:o.specification,unit:o.unit,pricingType:o.pricingType,quantity:1,unitPrice:o.retailPrice||0,totalPrice:o.retailPrice||0,remark:""})}),b.value=!1},handleCancel:()=>{n("update:visible",!1)},handleSubmit:()=>{s.value.validate().then(async()=>{if(!r.detailList||r.detailList.length===0){g.warning("\u8BF7\u6DFB\u52A0\u5546\u54C1\u660E\u7EC6");return}e.value=!0;try{const a={...r,totalAmount:p.value};d.value?(await L.edit(a),g.success("\u66F4\u65B0\u6210\u529F")):(await L.add(a),g.success("\u4FDD\u5B58\u6210\u529F")),n("ok")}catch(a){g.error((d.value?"\u66F4\u65B0":"\u4FDD\u5B58")+"\u5931\u8D25\uFF1A"+(a.message||"\u672A\u77E5\u9519\u8BEF"))}finally{e.value=!1}})}}}};function te(c,n,s,e,b,r){const m=T,d=z,u=R,i=V,p=q,D=G,_=H,y=Q,C=X,S=P("plus-outlined"),x=P("inbound-detail-table"),f=J,N=K,Y=P("product-selector-modal"),a=W;return A(),j(a,{visible:s.visible,title:e.isEdit?"\u7F16\u8F91\u5165\u5E93\u5355":"\u65B0\u5EFA\u5165\u5E93\u5355",width:1200,maskClosable:!1,onCancel:e.handleCancel},{footer:l(()=>[t(m,{onClick:e.handleCancel},{default:l(()=>n[6]||(n[6]=[k("\u53D6\u6D88")])),_:1,__:[6]},8,["onClick"]),t(m,{type:"primary",loading:e.loading,onClick:e.handleSubmit},{default:l(()=>[k(B(e.isEdit?"\u66F4\u65B0":"\u4FDD\u5B58"),1)]),_:1},8,["loading","onClick"])]),default:l(()=>[t(N,{ref:"formRef",model:e.formData,rules:e.rules,layout:"vertical"},{default:l(()=>[t(C,{title:"\u57FA\u672C\u4FE1\u606F",size:"small",style:{"margin-bottom":"16px"}},{default:l(()=>[t(_,{gutter:16},{default:l(()=>[t(i,{span:8},{default:l(()=>[t(u,{label:"\u5165\u5E93\u5355\u53F7",name:"orderNo"},{default:l(()=>[t(d,{value:e.formData.orderNo,"onUpdate:value":n[0]||(n[0]=o=>e.formData.orderNo=o),placeholder:"\u7CFB\u7EDF\u81EA\u52A8\u751F\u6210",disabled:""},null,8,["value"])]),_:1})]),_:1}),t(i,{span:8},{default:l(()=>[t(u,{label:"\u4F9B\u5E94\u5546",name:"supplierId",required:""},{default:l(()=>[t(p,{value:e.formData.supplierId,"onUpdate:value":n[1]||(n[1]=o=>e.formData.supplierId=o),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},onChange:e.onSupplierChange},null,8,["value","onChange"])]),_:1})]),_:1}),t(i,{span:8},{default:l(()=>[t(u,{label:"\u8BA2\u5355\u65E5\u671F",name:"orderDate",required:""},{default:l(()=>[t(D,{value:e.formData.orderDate,"onUpdate:value":n[2]||(n[2]=o=>e.formData.orderDate=o),style:{width:"100%"},format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD"},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(_,{gutter:16},{default:l(()=>[t(i,{span:24},{default:l(()=>[t(u,{label:"\u5907\u6CE8",name:"remark"},{default:l(()=>[t(y,{value:e.formData.remark,"onUpdate:value":n[3]||(n[3]=o=>e.formData.remark=o),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8\u4FE1\u606F",rows:3,maxlength:500,showCount:""},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1}),t(C,{title:"\u5546\u54C1\u660E\u7EC6",size:"small",style:{"margin-bottom":"16px"}},{extra:l(()=>[t(m,{type:"primary",size:"small",onClick:e.addProduct,disabled:!e.formData.supplierId},{default:l(()=>[t(S),n[7]||(n[7]=k(" \u6DFB\u52A0\u5546\u54C1 "))]),_:1,__:[7]},8,["onClick","disabled"])]),default:l(()=>[t(x,{value:e.formData.detailList,"onUpdate:value":n[4]||(n[4]=o=>e.formData.detailList=o),"supplier-id":e.formData.supplierId,onChange:e.onDetailChange},null,8,["value","supplier-id","onChange"])]),_:1}),t(C,{title:"\u6C47\u603B\u4FE1\u606F",size:"small"},{default:l(()=>[t(_,{gutter:16},{default:l(()=>[t(i,{span:6},{default:l(()=>[t(f,{title:"\u5546\u54C1\u79CD\u7C7B",value:e.productCount,suffix:"\u79CD"},null,8,["value"])]),_:1}),t(i,{span:6},{default:l(()=>[t(f,{title:"\u603B\u6570\u91CF",value:e.totalQuantity,suffix:"\u4EF6"},null,8,["value"])]),_:1}),t(i,{span:6},{default:l(()=>[t(f,{title:"\u603B\u91D1\u989D",value:e.totalAmount,prefix:"\xA5",precision:2},null,8,["value"])]),_:1}),t(i,{span:6},{default:l(()=>[t(f,{title:"\u5E73\u5747\u5355\u4EF7",value:e.averagePrice,prefix:"\xA5",precision:2},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),t(Y,{visible:e.showProductSelector,"onUpdate:visible":n[5]||(n[5]=o=>e.showProductSelector=o),"supplier-id":e.formData.supplierId,onSelect:e.onProductSelect},null,8,["visible","supplier-id","onSelect"])]),_:1},8,["visible","title","onCancel"])}const be=O(ee,[["render",te],["__scopeId","data-v-0ca7e8a4"]]);export{be as default};
