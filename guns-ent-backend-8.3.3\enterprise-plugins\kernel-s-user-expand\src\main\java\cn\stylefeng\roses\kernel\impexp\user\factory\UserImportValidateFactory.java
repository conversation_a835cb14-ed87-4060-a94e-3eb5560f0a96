package cn.stylefeng.roses.kernel.impexp.user.factory;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.*;
import cn.stylefeng.roses.kernel.impexp.user.datavalis.util.DataValidateUtil;
import cn.stylefeng.roses.kernel.impexp.user.enums.OperateTypeEnums;
import cn.stylefeng.roses.kernel.impexp.user.pojo.UserExcelImportPreview;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.ExcelLineParseResult;
import cn.stylefeng.roses.kernel.impexp.user.pojo.base.UserExcelImportParse;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户导入的校验创建工厂
 *
 * <AUTHOR>
 * @since 2024-02-04 16:23
 */
public class UserImportValidateFactory {

    /**
     * 创建校验结果
     *
     * <AUTHOR>
     * @since 2024-02-04 16:24
     */
    public static List<UserExcelImportPreview> createValidateResult(List<UserExcelImportParse> userExcelImportParses) {

        // 定义返回结果
        List<UserExcelImportPreview> userExcelImportPreviews = new ArrayList<>();

        if (ObjectUtil.isEmpty(userExcelImportParses)) {
            return userExcelImportPreviews;
        }

        // 逐个解析字段，将字段转化为是否正确的结果
        for (UserExcelImportParse parseItem : userExcelImportParses) {

            UserExcelImportPreview userExcelImportPreview = new UserExcelImportPreview();

            // 如果是demo开头的则直接跳过
            if ("demo".equals(parseItem.getNumber())) {
                continue;
            }

            // 解析userId
            ExcelLineParseResult userIdResult = DataValidateUtil.combineValidateResult(parseItem.getUserId(), new BigintValidator());
            userExcelImportPreview.setUserId(userIdResult);

            // 解析姓名
            ExcelLineParseResult realNameResult = DataValidateUtil.combineValidateResult(parseItem.getRealName(), new NotNullValidator());
            userExcelImportPreview.setRealName(realNameResult);

            // 解析昵称
            userExcelImportPreview.setNickName(new ExcelLineParseResult(parseItem.getNickName()));

            // 解析账号，直接设置值
            userExcelImportPreview.setAccount(new ExcelLineParseResult(parseItem.getAccount()));

            // 解析密码（如果有用户id，可以为空，如果没用户id则为新增用户，新增用户必须填写密码）
            if (ObjectUtil.isEmpty(parseItem.getUserId())) {
                ExcelLineParseResult passwordResult = DataValidateUtil.combineValidateResult(parseItem.getPassword(), new NotNullValidator());
                userExcelImportPreview.setPassword(passwordResult);
            } else {
                userExcelImportPreview.setPassword(new ExcelLineParseResult(parseItem.getPassword()));
            }

            // 解析生日
            ExcelLineParseResult birthdayResult = DataValidateUtil.combineValidateResult(parseItem.getBirthday(), new BirthdayValidator());
            userExcelImportPreview.setBirthday(birthdayResult);

            // 解析性别
            ExcelLineParseResult sexResult = DataValidateUtil.combineValidateResult(parseItem.getSex(), new SexValidator());
            userExcelImportPreview.setSex(sexResult);

            // 更新校验邮箱
            ExcelLineParseResult mailResult = DataValidateUtil.combineValidateResult(parseItem.getEmail(), new EmailValidator());
            userExcelImportPreview.setEmail(mailResult);

            // 更新手机的校验
            ExcelLineParseResult phoneResult = DataValidateUtil.combineValidateResult(parseItem.getPhone(), new MobileValidator());
            userExcelImportPreview.setPhone(phoneResult);

            // 更新固话的校验
            ExcelLineParseResult telResult = DataValidateUtil.combineValidateResult(parseItem.getTel(), new TelValidator());
            userExcelImportPreview.setTel(telResult);

            // 更新状态的校验
            ExcelLineParseResult statusResult = DataValidateUtil.combineValidateResult(parseItem.getStatusFlag(), new UserStatusValidator());
            userExcelImportPreview.setStatusFlag(statusResult);

            // 更新排序的校验
            ExcelLineParseResult sortResult = DataValidateUtil.combineValidateResult(parseItem.getUserSort(), new SortValidator());
            userExcelImportPreview.setUserSort(sortResult);

            // 更新工号的校验
            userExcelImportPreview.setEmployeeNumber(new ExcelLineParseResult(parseItem.getEmployeeNumber()));

            // 更新系统用户外部系统用户id
            userExcelImportPreview.setMasterUserId(new ExcelLineParseResult(parseItem.getMasterUserId()));

            // 设置操作类型
            if (ObjectUtil.isNotEmpty(parseItem.getUserId())) {
                userExcelImportPreview.setOperateType(new ExcelLineParseResult(true, OperateTypeEnums.EDIT.getMessage(), OperateTypeEnums.EDIT.getCode()));
            } else {
                userExcelImportPreview.setOperateType(new ExcelLineParseResult(true, OperateTypeEnums.ADD.getMessage(), OperateTypeEnums.ADD.getCode()));
            }

            userExcelImportPreviews.add(userExcelImportPreview);
        }

        // 填充账号的重复标识
        fillAccountRepeatFlag(userExcelImportPreviews, userExcelImportParses);

        return userExcelImportPreviews;
    }

    /**
     * 填充账号重复标识
     *
     * <AUTHOR>
     * @since 2024/2/14 23:15
     */
    public static void fillAccountRepeatFlag(List<UserExcelImportPreview> list, List<UserExcelImportParse> originValues) {

        SysUserService sysUserService = SpringUtil.getBean(SysUserService.class);

        // 获取这批账号在库中存在的情况，key是账号，value是这个账号对应的用户id
        Map<String, Long> userAccountUserIdMap = new HashMap<>();

        // 获取这批账号信息
        Set<String> thisAccountList = originValues.stream().map(UserExcelImportParse::getAccount).collect(Collectors.toSet());
        if (ObjectUtil.isNotEmpty(thisAccountList)) {
            LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysUserLambdaQueryWrapper.in(SysUser::getAccount, thisAccountList);
            sysUserLambdaQueryWrapper.select(SysUser::getAccount, SysUser::getUserId);
            List<SysUser> existedInDbUsers = sysUserService.list(sysUserLambdaQueryWrapper);
            for (SysUser existedInDbUser : existedInDbUsers) {
                userAccountUserIdMap.put(existedInDbUser.getAccount(), existedInDbUser.getUserId());
            }
        }

        for (UserExcelImportPreview userExcelImportPreview : list) {

            // 针对新增的用户，1.账号不能为空，2.账号不能在库中存在，3.账号不能和本次导入的数据重复
            if (OperateTypeEnums.ADD.getCode().equals(userExcelImportPreview.getOperateType().getSubmitValue())) {
                ExcelLineParseResult accountResult = DataValidateUtil.combineValidateResult(userExcelImportPreview.getAccount().getValue(), new NotNullValidator());
                if (!accountResult.getValidateResult()) {
                    userExcelImportPreview.setAccount(accountResult);
                    continue;
                }

                // 2. 账号不能在库中存在
                if (userAccountUserIdMap.containsKey(userExcelImportPreview.getAccount().getValue())) {
                    userExcelImportPreview.setAccount(
                            new ExcelLineParseResult(false, userExcelImportPreview.getAccount().getValue(), userExcelImportPreview.getAccount().getValue(), "账号在库中已存在"));
                    continue;
                }

                // 3. 账号不能和本次导入的数据重复
                long count = originValues.stream().filter(i -> userExcelImportPreview.getAccount().getValue().equals(i.getAccount())).count();
                if (count > 1) {
                    userExcelImportPreview.setAccount(
                            new ExcelLineParseResult(false, userExcelImportPreview.getAccount().getValue(), userExcelImportPreview.getAccount().getValue(), "账号在本次导入的数据中重复"));
                    continue;
                }
            }

            // 针对修改的用户，1.账号可以为空，2.账号不能在库中存在（排除此userId），3.账号不能和本次导入的数据重复
            if (OperateTypeEnums.EDIT.getCode().equals(userExcelImportPreview.getOperateType().getSubmitValue())) {

                // 1. 账号为空，直接执行下一个
                if (ObjectUtil.isEmpty(userExcelImportPreview.getAccount().getValue())) {
                    continue;
                }

                // 2. 账号不能在数据库中重复
                String currentAccount = userExcelImportPreview.getAccount().getValue();
                if (userAccountUserIdMap.containsKey(currentAccount)) {
                    String dbAccountUserId = userAccountUserIdMap.get(currentAccount).toString();
                    if (!dbAccountUserId.equals(userExcelImportPreview.getUserId().getSubmitValue())) {
                        userExcelImportPreview.setAccount(
                                new ExcelLineParseResult(false, userExcelImportPreview.getAccount().getValue(), userExcelImportPreview.getAccount().getValue(), "账号在库中已存在"));
                        continue;
                    }
                }

                // 3. 账号不能和本次导入的数据重复
                long count = originValues.stream().filter(i -> userExcelImportPreview.getAccount().getValue().equals(i.getAccount())).count();
                if (count > 1) {
                    userExcelImportPreview.setAccount(
                            new ExcelLineParseResult(false, userExcelImportPreview.getAccount().getValue(), userExcelImportPreview.getAccount().getValue(), "账号在本次导入的数据中重复"));
                }
            }
        }
    }

}
