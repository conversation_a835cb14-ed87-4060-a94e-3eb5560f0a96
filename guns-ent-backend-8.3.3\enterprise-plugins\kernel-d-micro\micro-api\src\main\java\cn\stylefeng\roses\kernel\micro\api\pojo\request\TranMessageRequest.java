package cn.stylefeng.roses.kernel.micro.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 消息服务请求实体
 *
 * <AUTHOR>
 * @date 2018-04-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TranMessageRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息内容
     */
    private String messageBody;

    /**
     * 消息数据类型
     */
    private String messageDataType;

    /**
     * 消费队列
     */
    private String topic;

    /**
     * 消息重发次数
     */
    private Integer messageSendTimes;

    /**
     * 是否死亡
     * <p>
     * Y：已死亡
     * N：未死亡
     */
    private String alreadyDead;

    /**
     * 状态
     * <p>
     * WAIT_VERIFY：待确认
     * SENDING：发送中
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 最后修改时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 修改者
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    @Version
    private Long version = 0L;

    /**
     * 版本号
     */
    private Long bizUniqueId;

}
