System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var a;return{setters:[e=>{a=e.R}],execute:function(){class t{static add(e){return a.post("/erp/customer/add",e)}static delete(e){return a.post("/erp/customer/delete",e)}static batchDelete(e){return a.post("/erp/customer/batchDelete",e)}static edit(e){return a.post("/erp/customer/edit",e)}static detail(e){return a.getAndLoadData("/erp/customer/detail",e)}static findPage(e){return a.getAndLoadData("/erp/customer/page",e)}static findList(e){return a.getAndLoadData("/erp/customer/list",e)}static updateStatus(e){return a.post("/erp/customer/updateStatus",e)}static validateCode(e){return a.getAndLoadData("/erp/customer/validateCode",e)}static getCustomerTypeOptions(){return[{label:"企业",value:"ENTERPRISE"},{label:"个人",value:"INDIVIDUAL"},{label:"零售",value:"RETAIL"}]}static getCustomerLevelOptions(){return[{label:"钻石",value:"DIAMOND"},{label:"黄金",value:"GOLD"},{label:"白银",value:"SILVER"},{label:"青铜",value:"BRONZE"}]}static getCustomerStatusOptions(){return[{label:"正常",value:"ACTIVE"},{label:"停用",value:"INACTIVE"},{label:"冻结",value:"FROZEN"}]}static getCustomerTypeName(e){const a=t.getCustomerTypeOptions().find((t=>t.value===e));return a?a.label:e}static getCustomerLevelName(e){const a=t.getCustomerLevelOptions().find((t=>t.value===e));return a?a.label:e}static getCustomerStatusName(e){const a=t.getCustomerStatusOptions().find((t=>t.value===e));return a?a.label:e}static getStatusTagColor(e){switch(e){case"ACTIVE":return"green";case"INACTIVE":return"orange";case"FROZEN":return"red";default:return"default"}}static getCustomerLevelTagColor(e){switch(e){case"DIAMOND":return"purple";case"GOLD":return"gold";case"SILVER":return"cyan";case"BRONZE":return"orange";default:return"default"}}static formatAmount(e){return e?Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00"}static formatPaymentTerms(e){return e?`${e}天`:"-"}static updateCustomerRegions(e){return a.post("/erp/customerRegion/updateCustomerRegions",e)}static getCustomerRegions(e){return a.getAndLoadData("/erp/customerRegion/getCustomerRegions",e)}static findPageByRegion(e){return a.getAndLoadData("/erp/customer/pageByRegion",e)}}e("C",t)}}}));
