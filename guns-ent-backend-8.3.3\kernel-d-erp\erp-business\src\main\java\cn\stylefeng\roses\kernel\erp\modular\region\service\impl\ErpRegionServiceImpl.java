package cn.stylefeng.roses.kernel.erp.modular.region.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpRegionConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpRegionExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpRegion;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpRegionRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpRegionResponse;
import cn.stylefeng.roses.kernel.erp.modular.region.mapper.ErpRegionMapper;
import cn.stylefeng.roses.kernel.erp.modular.region.service.ErpRegionService;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.auth.api.context.LoginContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域管理Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
@Service
public class ErpRegionServiceImpl extends ServiceImpl<ErpRegionMapper, ErpRegion> implements ErpRegionService {

    @Resource
    private ErpRegionMapper erpRegionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = new ErpRegion();
        BeanUtil.copyProperties(erpRegionRequest, erpRegion);

        // 校验区域编码是否重复
        if (this.validateRegionCodeRepeat(erpRegion.getRegionCode(), null)) {
            throw new ServiceException(ErpRegionExceptionEnum.REGION_CODE_REPEAT);
        }

        // 设置默认值
        if (StrUtil.isBlank(erpRegion.getStatus())) {
            erpRegion.setStatus(ErpRegionConstants.DEFAULT_REGION_STATUS);
        }
        if (ObjectUtil.isNull(erpRegion.getSortOrder())) {
            erpRegion.setSortOrder(0);
        }

        // 处理父级区域和层级（不包含路径设置）
        this.processParentAndLevelBeforeSave(erpRegion);

        // 校验参数
        this.validateRegionParams(erpRegion);

        // 保存到数据库（生成regionId）
        this.save(erpRegion);

        // 处理区域路径（保存后，regionId已生成）
        this.processRegionPathAfterSave(erpRegion);

        // 更新区域路径
        this.updateById(erpRegion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = this.queryRegion(erpRegionRequest);

        // 校验是否有子级区域
        if (this.hasChildren(erpRegion.getRegionId())) {
            throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_CHILDREN.getErrorCode(),
                    String.format(ErpRegionExceptionEnum.REGION_HAS_CHILDREN.getUserTip(), erpRegion.getRegionName()));
        }

        // 检查供应商关联
        Long supplierCount = erpRegionMapper.countSupplierByRegionId(erpRegion.getRegionId());
        if (supplierCount > 0) {
            throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DELETE.getErrorCode(),
                    "该区域关联了" + supplierCount + "个供应商，无法删除");
        }

        // 检查客户关联
        Long customerCount = erpRegionMapper.countCustomerByRegionId(erpRegion.getRegionId());
        if (customerCount > 0) {
            throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DELETE.getErrorCode(),
                    "该区域关联了" + customerCount + "个客户，无法删除");
        }

        // 检查供应商-区域关联表
        Long supplierRegionCount = erpRegionMapper.countSupplierRegionByRegionId(erpRegion.getRegionId());
        if (supplierRegionCount > 0) {
            throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DELETE.getErrorCode(),
                    "该区域在供应商-区域关联表中有" + supplierRegionCount + "条记录，无法删除");
        }

        // 检查客户-区域关联表
        Long customerRegionCount = erpRegionMapper.countCustomerRegionByRegionId(erpRegion.getRegionId());
        if (customerRegionCount > 0) {
            throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DELETE.getErrorCode(),
                    "该区域在客户-区域关联表中有" + customerRegionCount + "条记录，无法删除");
        }

        this.removeById(erpRegion.getRegionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = this.queryRegion(erpRegionRequest);

        // 校验区域编码是否重复（排除自己）
        if (this.validateRegionCodeRepeat(erpRegionRequest.getRegionCode(), erpRegion.getRegionId())) {
            throw new ServiceException(ErpRegionExceptionEnum.REGION_CODE_REPEAT);
        }

        // 校验不能选择自己作为父级
        if (ObjectUtil.isNotNull(erpRegionRequest.getParentId()) &&
                erpRegionRequest.getParentId().equals(erpRegion.getRegionId())) {
            throw new ServiceException(ErpRegionExceptionEnum.CANNOT_SELECT_SELF_AS_PARENT);
        }

        BeanUtil.copyProperties(erpRegionRequest, erpRegion);

        // 处理父级区域和层级
        this.processParentAndLevel(erpRegion);

        // 校验参数
        this.validateRegionParams(erpRegion);

        this.updateById(erpRegion);
    }

    @Override
    public ErpRegionResponse detail(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = this.queryRegion(erpRegionRequest);
        ErpRegionResponse response = new ErpRegionResponse();
        BeanUtil.copyProperties(erpRegion, response);

        // 填充扩展信息
        this.fillRegionExtInfo(response);

        return response;
    }

    @Override
    public PageResult<ErpRegionResponse> findPage(ErpRegionRequest erpRegionRequest) {
        LambdaQueryWrapper<ErpRegion> wrapper = this.createWrapper(erpRegionRequest);
        Page<ErpRegion> page = this.page(PageFactory.defaultPage(), wrapper);

        List<ErpRegionResponse> responseList = page.getRecords().stream().map(region -> {
            ErpRegionResponse response = new ErpRegionResponse();
            BeanUtil.copyProperties(region, response);
            this.fillRegionExtInfo(response);
            return response;
        }).collect(Collectors.toList());

        return PageResultFactory.createPageResult(responseList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public List<ErpRegionResponse> findList(ErpRegionRequest erpRegionRequest) {
        LambdaQueryWrapper<ErpRegion> wrapper = this.createWrapper(erpRegionRequest);
        List<ErpRegion> regionList = this.list(wrapper);

        return regionList.stream().map(region -> {
            ErpRegionResponse response = new ErpRegionResponse();
            BeanUtil.copyProperties(region, response);
            this.fillRegionExtInfo(response);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ErpRegionResponse> findTree(ErpRegionRequest erpRegionRequest) {
        // 如果有搜索文本，使用专门的搜索逻辑
        if (ObjectUtil.isNotEmpty(erpRegionRequest.getSearchText())) {
            return findTreeWithSearch(erpRegionRequest.getSearchText());
        }

        // 查询所有区域
        List<ErpRegionResponse> allRegions = this.findList(erpRegionRequest);

        // 构建树形结构，传入null表示查找根节点
        return this.buildTree(allRegions, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = this.queryRegion(erpRegionRequest);

        // 如果要停用，校验是否有关联业务数据
        if (ErpRegionConstants.REGION_STATUS_DISABLE.equals(erpRegionRequest.getStatus())) {
            // 检查供应商关联
            Long supplierCount = erpRegionMapper.countSupplierByRegionId(erpRegion.getRegionId());
            if (supplierCount > 0) {
                throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DISABLE.getErrorCode(),
                        "该区域关联了" + supplierCount + "个供应商，无法停用");
            }

            // 检查客户关联
            Long customerCount = erpRegionMapper.countCustomerByRegionId(erpRegion.getRegionId());
            if (customerCount > 0) {
                throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DISABLE.getErrorCode(),
                        "该区域关联了" + customerCount + "个客户，无法停用");
            }

            // 检查供应商-区域关联表
            Long supplierRegionCount = erpRegionMapper.countSupplierRegionByRegionId(erpRegion.getRegionId());
            if (supplierRegionCount > 0) {
                throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DISABLE.getErrorCode(),
                        "该区域在供应商-区域关联表中有" + supplierRegionCount + "条记录，无法停用");
            }

            // 检查客户-区域关联表
            Long customerRegionCount = erpRegionMapper.countCustomerRegionByRegionId(erpRegion.getRegionId());
            if (customerRegionCount > 0) {
                throw new ServiceException("erp-region-module", ErpRegionExceptionEnum.REGION_HAS_BUSINESS_DATA_CANNOT_DISABLE.getErrorCode(),
                        "该区域在客户-区域关联表中有" + customerRegionCount + "条记录，无法停用");
            }
        }

        // 校验状态参数
        this.validateStatus(erpRegionRequest.getStatus());

        LambdaUpdateWrapper<ErpRegion> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ErpRegion::getRegionId, erpRegion.getRegionId())
                .set(ErpRegion::getStatus, erpRegionRequest.getStatus());

        this.update(updateWrapper);
    }

    @Override
    public boolean validateRegionCodeRepeat(String regionCode, Long regionId) {
        LambdaQueryWrapper<ErpRegion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpRegion::getRegionCode, regionCode);
        if (ObjectUtil.isNotNull(regionId)) {
            wrapper.ne(ErpRegion::getRegionId, regionId);
        }
        return this.count(wrapper) > 0;
    }

    /**
     * 根据主键查询区域
     */
    private ErpRegion queryRegion(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = this.getById(erpRegionRequest.getRegionId());
        if (ObjectUtil.isNull(erpRegion)) {
            throw new ServiceException(ErpRegionExceptionEnum.REGION_NOT_EXIST);
        }
        return erpRegion;
    }

    /**
     * 创建查询条件
     */
    private LambdaQueryWrapper<ErpRegion> createWrapper(ErpRegionRequest erpRegionRequest) {
        LambdaQueryWrapper<ErpRegion> wrapper = new LambdaQueryWrapper<>();

        // 区域编码
        if (StrUtil.isNotBlank(erpRegionRequest.getRegionCode())) {
            wrapper.like(ErpRegion::getRegionCode, erpRegionRequest.getRegionCode());
        }

        // 区域名称
        if (StrUtil.isNotBlank(erpRegionRequest.getRegionName())) {
            wrapper.like(ErpRegion::getRegionName, erpRegionRequest.getRegionName());
        }

        // 父级区域
        if (ObjectUtil.isNotNull(erpRegionRequest.getParentId())) {
            wrapper.eq(ErpRegion::getParentId, erpRegionRequest.getParentId());
        }

        // 区域层级
        if (ObjectUtil.isNotNull(erpRegionRequest.getRegionLevel())) {
            wrapper.eq(ErpRegion::getRegionLevel, erpRegionRequest.getRegionLevel());
        }

        // 状态
        if (StrUtil.isNotBlank(erpRegionRequest.getStatus())) {
            wrapper.eq(ErpRegion::getStatus, erpRegionRequest.getStatus());
        }

        // 只查询未删除的记录
        wrapper.eq(ErpRegion::getDelFlag, YesOrNotEnum.N.getCode());

        // 按排序号和创建时间排序
        wrapper.orderByAsc(ErpRegion::getSortOrder).orderByAsc(ErpRegion::getCreateTime);

        return wrapper;
    }

    /**
     * 处理父级区域和层级（保存前，不设置路径）
     */
    private void processParentAndLevelBeforeSave(ErpRegion erpRegion) {
        if (ObjectUtil.isNull(erpRegion.getParentId()) || erpRegion.getParentId().equals(ErpRegionConstants.ROOT_PARENT_ID)) {
            // 根节点
            erpRegion.setParentId(ErpRegionConstants.ROOT_PARENT_ID);
            erpRegion.setRegionLevel(1);
            // 不设置路径，等保存后再设置
        } else {
            // 子节点
            ErpRegion parentRegion = this.getById(erpRegion.getParentId());
            if (ObjectUtil.isNull(parentRegion)) {
                throw new ServiceException(ErpRegionExceptionEnum.PARENT_REGION_NOT_EXIST);
            }

            erpRegion.setRegionLevel(parentRegion.getRegionLevel() + 1);
            // 不设置路径，等保存后再设置
        }
    }

    /**
     * 处理区域路径（保存后，regionId已生成）
     */
    private void processRegionPathAfterSave(ErpRegion erpRegion) {
        if (ObjectUtil.isNull(erpRegion.getParentId()) || erpRegion.getParentId().equals(ErpRegionConstants.ROOT_PARENT_ID)) {
            // 根节点：路径就是自己的ID
            erpRegion.setRegionPath(erpRegion.getRegionId().toString());
        } else {
            // 子节点：父级路径 + 分隔符 + 自己的ID
            ErpRegion parentRegion = this.getById(erpRegion.getParentId());
            if (ObjectUtil.isNotNull(parentRegion)) {
                erpRegion.setRegionPath(parentRegion.getRegionPath() + ErpRegionConstants.PATH_SEPARATOR + erpRegion.getRegionId());
            } else {
                // 如果父级区域不存在，设置为自己的ID（容错处理）
                erpRegion.setRegionPath(erpRegion.getRegionId().toString());
            }
        }
    }

    /**
     * 处理父级区域和层级（编辑时使用，regionId已存在）
     */
    private void processParentAndLevel(ErpRegion erpRegion) {
        if (ObjectUtil.isNull(erpRegion.getParentId()) || erpRegion.getParentId().equals(ErpRegionConstants.ROOT_PARENT_ID)) {
            // 根节点
            erpRegion.setParentId(ErpRegionConstants.ROOT_PARENT_ID);
            erpRegion.setRegionLevel(1);
            erpRegion.setRegionPath(erpRegion.getRegionId().toString());
        } else {
            // 子节点
            ErpRegion parentRegion = this.getById(erpRegion.getParentId());
            if (ObjectUtil.isNull(parentRegion)) {
                throw new ServiceException(ErpRegionExceptionEnum.PARENT_REGION_NOT_EXIST);
            }

            erpRegion.setRegionLevel(parentRegion.getRegionLevel() + 1);
            erpRegion.setRegionPath(parentRegion.getRegionPath() + ErpRegionConstants.PATH_SEPARATOR + erpRegion.getRegionId());
        }
    }

    /**
     * 校验区域参数
     */
    private void validateRegionParams(ErpRegion erpRegion) {
        // 校验状态
        this.validateStatus(erpRegion.getStatus());

        // 校验层级深度
        if (erpRegion.getRegionLevel() > ErpRegionConstants.MAX_LEVEL_DEPTH) {
            throw new ServiceException(ErpRegionExceptionEnum.REGION_LEVEL_EXCEED_MAX_DEPTH);
        }
    }

    /**
     * 校验状态
     */
    private void validateStatus(String status) {
        if (!ErpRegionConstants.REGION_STATUS_ENABLE.equals(status) &&
                !ErpRegionConstants.REGION_STATUS_DISABLE.equals(status)) {
            throw new ServiceException(ErpRegionExceptionEnum.REGION_STATUS_ERROR);
        }
    }

    /**
     * 检查是否有子级区域
     */
    private boolean hasChildren(Long regionId) {
        LambdaQueryWrapper<ErpRegion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpRegion::getParentId, regionId);
        return this.count(wrapper) > 0;
    }

    /**
     * 检查是否有关联业务数据
     */
    private boolean hasBusinessData(Long regionId) {
        // 检查区域是否有关联的供应商
        Long supplierCount = erpRegionMapper.countSupplierByRegionId(regionId);
        if (supplierCount > 0) {
            return true;
        }

        // 检查区域是否有关联的客户
        Long customerCount = erpRegionMapper.countCustomerByRegionId(regionId);
        if (customerCount > 0) {
            return true;
        }

        // 检查区域是否有关联的供应商-区域关联表
        Long supplierRegionCount = erpRegionMapper.countSupplierRegionByRegionId(regionId);
        if (supplierRegionCount > 0) {
            return true;
        }

        // 检查区域是否有关联的客户-区域关联表
        Long customerRegionCount = erpRegionMapper.countCustomerRegionByRegionId(regionId);
        if (customerRegionCount > 0) {
            return true;
        }

        return false;
    }

    /**
     * 填充区域扩展信息
     */
    private void fillRegionExtInfo(ErpRegionResponse response) {
        // 填充状态名称
        if (ErpRegionConstants.REGION_STATUS_ENABLE.equals(response.getStatus())) {
            response.setStatusName("启用");
        } else if (ErpRegionConstants.REGION_STATUS_DISABLE.equals(response.getStatus())) {
            response.setStatusName("停用");
        }

        // 填充层级名称
        if (ObjectUtil.isNotNull(response.getRegionLevel())) {
            switch (response.getRegionLevel()) {
                case 1:
                    response.setRegionLevelName("国家");
                    break;
                case 2:
                    response.setRegionLevelName("省");
                    break;
                case 3:
                    response.setRegionLevelName("市");
                    break;
                case 4:
                    response.setRegionLevelName("区县");
                    break;
                case 5:
                    response.setRegionLevelName("商圈");
                    break;
                default:
                    response.setRegionLevelName("未知");
                    break;
            }
        }

        // 检查是否有子节点
        response.setHasChildren(this.hasChildren(response.getRegionId()));

        // TODO: 填充父级区域名称
        // 需要查询父级区域获取名称
    }

    /**
     * 构建树形结构
     */
    private List<ErpRegionResponse> buildTree(List<ErpRegionResponse> allRegions, Long parentId) {
        List<ErpRegionResponse> result = new ArrayList<>();

        for (ErpRegionResponse region : allRegions) {
            // 处理根节点：parentId为null或者等于ROOT_PARENT_ID(0)
            boolean isRootMatch = (parentId == null || parentId.equals(ErpRegionConstants.ROOT_PARENT_ID))
                    && (region.getParentId() == null || region.getParentId().equals(ErpRegionConstants.ROOT_PARENT_ID));

            // 处理非根节点
            boolean isChildMatch = parentId != null && !parentId.equals(ErpRegionConstants.ROOT_PARENT_ID)
                    && ObjectUtil.equal(region.getParentId(), parentId);

            if (isRootMatch || isChildMatch) {
                List<ErpRegionResponse> children = buildTree(allRegions, region.getRegionId());
                region.setChildren(children);
                result.add(region);
            }
        }

        return result;
    }

    @Override
    public PageResult<ErpRegionResponse> findPageByParent(ErpRegionRequest erpRegionRequest) {
        LambdaQueryWrapper<ErpRegion> queryWrapper = new LambdaQueryWrapper<>();

        // 按父级ID查询
        if (ObjectUtil.isNotNull(erpRegionRequest.getParentId())) {
            queryWrapper.eq(ErpRegion::getParentId, erpRegionRequest.getParentId());
        } else {
            // 如果没有指定父级ID，查询顶级区域
            queryWrapper.isNull(ErpRegion::getParentId);
        }

        // 搜索条件
        if (ObjectUtil.isNotEmpty(erpRegionRequest.getSearchText())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(ErpRegion::getRegionName, erpRegionRequest.getSearchText())
                    .or()
                    .like(ErpRegion::getRegionCode, erpRegionRequest.getSearchText())
            );
        }

        // 状态筛选
        if (ObjectUtil.isNotEmpty(erpRegionRequest.getStatus())) {
            queryWrapper.eq(ErpRegion::getStatus, erpRegionRequest.getStatus());
        }

        // 层级筛选
        if (ObjectUtil.isNotNull(erpRegionRequest.getRegionLevel())) {
            queryWrapper.eq(ErpRegion::getRegionLevel, erpRegionRequest.getRegionLevel());
        }

        // 只查询未删除的记录
        queryWrapper.eq(ErpRegion::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        queryWrapper.orderByAsc(ErpRegion::getSortOrder);
        queryWrapper.orderByAsc(ErpRegion::getRegionLevel);
        queryWrapper.orderByDesc(ErpRegion::getCreateTime);

        // 分页查询
        Page<ErpRegion> page = this.page(PageFactory.defaultPage(), queryWrapper);

        // 转换为响应对象
        List<ErpRegionResponse> responseList = page.getRecords().stream().map(item -> {
            ErpRegionResponse response = BeanUtil.copyProperties(item, ErpRegionResponse.class);
            // 设置树形结构相关字段
            response.setKey(String.valueOf(item.getRegionId()));
            response.setValue(String.valueOf(item.getRegionId()));
            response.setTitle(item.getRegionName());
            response.setIsLeaf(checkIsLeaf(item.getRegionId()));
            response.setHasChildren(!response.getIsLeaf());
            response.setIcon(getRegionIcon(item.getRegionLevel()));
            return response;
        }).collect(Collectors.toList());

        // 创建分页结果
        return PageResultFactory.createPageResult(responseList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public List<ErpRegionResponse> findTreeWithLazy(ErpRegionRequest erpRegionRequest) {
        LambdaQueryWrapper<ErpRegion> queryWrapper = new LambdaQueryWrapper<>();

        // 如果指定了父级ID，只查询该父级的直接子节点
        if (ObjectUtil.isNotNull(erpRegionRequest.getParentId())) {
            queryWrapper.eq(ErpRegion::getParentId, erpRegionRequest.getParentId());
        } else {
            // 否则查询顶级节点
            queryWrapper.isNull(ErpRegion::getParentId);
        }

        // 搜索条件
        if (ObjectUtil.isNotEmpty(erpRegionRequest.getSearchText())) {
            // 如果有搜索条件，需要查询所有匹配的节点及其路径
            return findTreeWithSearch(erpRegionRequest.getSearchText());
        }

        // 状态筛选
        if (ObjectUtil.isNotNull(erpRegionRequest.getOnlyEnabled()) && erpRegionRequest.getOnlyEnabled()) {
            queryWrapper.eq(ErpRegion::getStatus, YesOrNotEnum.Y.getCode());
        }

        // 只查询未删除的记录
        queryWrapper.eq(ErpRegion::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        queryWrapper.orderByAsc(ErpRegion::getSortOrder);
        queryWrapper.orderByAsc(ErpRegion::getRegionLevel);

        List<ErpRegion> regionList = this.list(queryWrapper);

        // 转换为响应对象
        return regionList.stream().map(item -> {
            ErpRegionResponse response = BeanUtil.copyProperties(item, ErpRegionResponse.class);
            // 设置树形结构相关字段
            response.setKey(String.valueOf(item.getRegionId()));
            response.setValue(String.valueOf(item.getRegionId()));
            response.setTitle(item.getRegionName());
            response.setIsLeaf(checkIsLeaf(item.getRegionId()));
            response.setHasChildren(!response.getIsLeaf());
            response.setIcon(getRegionIcon(item.getRegionLevel()));
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public void batchDelete(ErpRegionRequest erpRegionRequest) {
        List<Long> regionIdList = erpRegionRequest.getRegionIdList();

        if (ObjectUtil.isEmpty(regionIdList)) {
            throw new ServiceException(ErpRegionExceptionEnum.REGION_ID_LIST_EMPTY);
        }

        // 检查是否有子区域
        for (Long regionId : regionIdList) {
            if (hasChildren(regionId)) {
                ErpRegion region = this.getById(regionId);
                throw new ServiceException(ErpRegionExceptionEnum.REGION_HAS_CHILDREN_CANNOT_DELETE);
            }
        }

        // 批量删除（逻辑删除）
        LambdaUpdateWrapper<ErpRegion> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ErpRegion::getRegionId, regionIdList);
        updateWrapper.set(ErpRegion::getDelFlag, YesOrNotEnum.Y.getCode());
        updateWrapper.set(ErpRegion::getUpdateTime, new Date());
        updateWrapper.set(ErpRegion::getUpdateUser, LoginContext.me().getLoginUser().getUserId());

        this.update(updateWrapper);
    }

    @Override
    public List<ErpRegionResponse> findSelector(ErpRegionRequest erpRegionRequest) {
        LambdaQueryWrapper<ErpRegion> queryWrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (ObjectUtil.isNotEmpty(erpRegionRequest.getSearchText())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(ErpRegion::getRegionName, erpRegionRequest.getSearchText())
                    .or()
                    .like(ErpRegion::getRegionCode, erpRegionRequest.getSearchText())
            );
        }

        // 层级筛选
        if (ObjectUtil.isNotNull(erpRegionRequest.getRegionLevel())) {
            queryWrapper.eq(ErpRegion::getRegionLevel, erpRegionRequest.getRegionLevel());
        }

        // 只查询启用状态的记录
        queryWrapper.eq(ErpRegion::getStatus, YesOrNotEnum.Y.getCode());
        queryWrapper.eq(ErpRegion::getDelFlag, YesOrNotEnum.N.getCode());

        // 排序
        queryWrapper.orderByAsc(ErpRegion::getRegionLevel);
        queryWrapper.orderByAsc(ErpRegion::getSortOrder);

        List<ErpRegion> regionList = this.list(queryWrapper);

        // 转换为响应对象
        return regionList.stream().map(item -> {
            ErpRegionResponse response = BeanUtil.copyProperties(item, ErpRegionResponse.class);
            response.setKey(String.valueOf(item.getRegionId()));
            response.setValue(String.valueOf(item.getRegionId()));
            response.setTitle(item.getRegionName());
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 获取区域关联的供应商和客户数量
     *
     * @param erpRegionRequest 区域请求参数
     * @return 区域关联信息
     */
    @Override
    public ErpRegionResponse getRegionRelationCount(ErpRegionRequest erpRegionRequest) {
        ErpRegion erpRegion = this.queryRegion(erpRegionRequest);
        ErpRegionResponse response = new ErpRegionResponse();
        BeanUtil.copyProperties(erpRegion, response);

        // 获取供应商数量
        Long supplierCount = erpRegionMapper.countSupplierByRegionId(erpRegion.getRegionId());
        response.setSupplierCount(supplierCount);

        // 获取客户数量
        Long customerCount = erpRegionMapper.countCustomerByRegionId(erpRegion.getRegionId());
        response.setCustomerCount(customerCount);

        // 获取供应商-区域关联表数量
        Long supplierRegionCount = erpRegionMapper.countSupplierRegionByRegionId(erpRegion.getRegionId());
        response.setSupplierRegionCount(supplierRegionCount);

        // 获取客户-区域关联表数量
        Long customerRegionCount = erpRegionMapper.countCustomerRegionByRegionId(erpRegion.getRegionId());
        response.setCustomerRegionCount(customerRegionCount);

        // 计算总关联数量
        response.setTotalRelationCount(supplierCount + customerCount + supplierRegionCount + customerRegionCount);

        return response;
    }

    /**
     * 检查是否为叶子节点
     */
    private Boolean checkIsLeaf(Long regionId) {
        return !hasChildren(regionId);
    }

    /**
     * 根据区域层级获取图标
     */
    private String getRegionIcon(Integer regionLevel) {
        switch (regionLevel) {
            case 1:
                return "folder";
            case 2:
                return "bank";
            case 3:
                return "home";
            case 4:
                return "environment";
            case 5:
                return "shop";
            default:
                return "folder";
        }
    }

    /**
     * 搜索时查询树形结构
     */
    private List<ErpRegionResponse> findTreeWithSearch(String searchText) {
        LambdaQueryWrapper<ErpRegion> queryWrapper = new LambdaQueryWrapper<>();

        // 搜索匹配的节点
        queryWrapper.and(wrapper -> wrapper
                .like(ErpRegion::getRegionName, searchText)
                .or()
                .like(ErpRegion::getRegionCode, searchText)
        );

        queryWrapper.eq(ErpRegion::getDelFlag, YesOrNotEnum.N.getCode());
        queryWrapper.orderByAsc(ErpRegion::getRegionLevel);
        queryWrapper.orderByAsc(ErpRegion::getSortOrder);

        List<ErpRegion> matchedRegions = this.list(queryWrapper);

        // 获取所有匹配节点的路径上的节点
        Set<Long> pathRegionIds = new HashSet<>();
        for (ErpRegion region : matchedRegions) {
            pathRegionIds.add(region.getRegionId());
            // 添加路径上的所有父节点
            addParentIds(pathRegionIds, region.getRegionPath());
        }

        // 查询路径上的所有节点
        LambdaQueryWrapper<ErpRegion> pathQueryWrapper = new LambdaQueryWrapper<>();
        pathQueryWrapper.in(ErpRegion::getRegionId, pathRegionIds);
        pathQueryWrapper.eq(ErpRegion::getDelFlag, YesOrNotEnum.N.getCode());
        pathQueryWrapper.orderByAsc(ErpRegion::getRegionLevel);
        pathQueryWrapper.orderByAsc(ErpRegion::getSortOrder);

        List<ErpRegion> allPathRegions = this.list(pathQueryWrapper);

        // 转换为响应对象并构建树形结构
        List<ErpRegionResponse> responseList = allPathRegions.stream().map(item -> {
            ErpRegionResponse response = BeanUtil.copyProperties(item, ErpRegionResponse.class);
            response.setKey(String.valueOf(item.getRegionId()));
            response.setValue(String.valueOf(item.getRegionId()));
            response.setTitle(item.getRegionName());
            response.setIsLeaf(checkIsLeaf(item.getRegionId()));
            response.setHasChildren(!response.getIsLeaf());
            response.setIcon(getRegionIcon(item.getRegionLevel()));
            return response;
        }).collect(Collectors.toList());

        return buildTree(responseList, null);
    }

    /**
     * 添加路径上的父节点ID
     */
    private void addParentIds(Set<Long> pathRegionIds, String regionPath) {
        if (ObjectUtil.isNotEmpty(regionPath)) {
            String[] pathIds = regionPath.split("/");
            for (String pathId : pathIds) {
                if (ObjectUtil.isNotEmpty(pathId)) {
                    pathRegionIds.add(Long.valueOf(pathId));
                }
            }
        }
    }

}
