package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * POS订单项实体类
 *
 * <AUTHOR>
 * @since 2025/08/01 10:20
 */
@TableName(value = "pos_order_item", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PosOrderItem extends BaseEntity {

    /**
     * 订单项ID
     */
    @TableId(value = "item_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("订单项ID")
    private Long itemId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    @ChineseDescription("订单ID")
    private Long orderId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品名称
     */
    @TableField("product_name")
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品编码
     */
    @TableField("product_code")
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 单价
     */
    @TableField("unit_price")
    @ChineseDescription("单价")
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    @TableField("quantity")
    @ChineseDescription("数量")
    private BigDecimal quantity;

    /**
     * 小计金额
     */
    @TableField("total_price")
    @ChineseDescription("小计金额")
    private BigDecimal totalPrice;

    /**
     * 单位
     */
    @TableField("unit")
    @ChineseDescription("单位")
    private String unit;

    /**
     * 计价类型（NORMAL-普通，WEIGHT-计重，PIECE-计件，VARIABLE-不定价）
     */
    @TableField("pricing_type")
    @ChineseDescription("计价类型")
    private String pricingType;

}