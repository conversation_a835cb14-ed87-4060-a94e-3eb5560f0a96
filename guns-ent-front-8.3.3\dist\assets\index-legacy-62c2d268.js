System.register(["./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./productCategoryApi-legacy-247b2407.js","./product-category-tree-legacy-396e1e66.js","./addForm-legacy-e747c9ca.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./UniversalTree-legacy-6dcdf778.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js"],(function(e,a){"use strict";var t,l,o,d,n,r,c,i,s,u,g,f,p,y,b,v,h,_,x,m,w,C,k,I,E,T,L,S,R,j,A,O,z,N,D,P,U,F,M,B,G,Y,K,Q;return{setters:[e=>{t=e._},e=>{l=e._},e=>{o=e._,d=e.P,n=e.K,r=e.r,c=e.L,i=e.N,s=e.s,u=e.o,g=e.k,f=e.a,p=e.c,y=e.d,b=e.w,v=e.b,h=e.g,_=e.t,x=e.h,m=e.O,w=e.Q,C=e.F,k=e.f,I=e.M,E=e.E,T=e.m,L=e.U,S=e.n,R=e.B,j=e.I,A=e.p,O=e.q,z=e.D,N=e.l,D=e.V,P=e.W,U=e.J,F=e.u,M=e.v,B=e.G,G=e.H},null,e=>{Y=e.P},e=>{K=e.default},e=>{Q=e.default},null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".guns-layout .table-toolbar[data-v-9cbaa05f]{margin-bottom:16px;padding:16px 0;border-bottom:1px solid #f0f0f0}.guns-layout .table-toolbar .toolbar-left[data-v-9cbaa05f]{display:flex;align-items:center;gap:16px}.guns-layout .table-toolbar .toolbar-left .search-input .ant-input[data-v-9cbaa05f]{border-radius:6px}.guns-layout .table-toolbar .toolbar-left a[data-v-9cbaa05f]{color:#1890ff;cursor:pointer}.guns-layout .table-toolbar .toolbar-left a[data-v-9cbaa05f]:hover{color:#40a9ff}.guns-layout .advanced-search[data-v-9cbaa05f]{padding:16px;background-color:#fafafa;border-radius:6px;margin-bottom:16px}.guns-layout .advanced-search .ant-form-item[data-v-9cbaa05f]{margin-bottom:0}.guns-layout[data-v-9cbaa05f]{height:100%;width:100%;margin:0;padding:0}.guns-layout-content[data-v-9cbaa05f],.guns-layout-content-application[data-v-9cbaa05f]{width:100%;height:100%;display:flex;flex-direction:column;margin:0;padding:0;box-sizing:border-box}.sidebar-content[data-v-9cbaa05f]{height:100%;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08)}.content-main[data-v-9cbaa05f]{width:100%;height:100%;display:flex;flex-direction:column;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,21,41,.08);margin:0;padding:0;box-sizing:border-box}.content-main-header[data-v-9cbaa05f]{padding:16px 24px;border-bottom:1px solid #f0f0f0;background:#fafafa;border-radius:6px 6px 0 0}.header-content[data-v-9cbaa05f]{display:flex;justify-content:space-between;align-items:center}.current-category-info[data-v-9cbaa05f]{font-size:14px;color:#666}.content-main-body[data-v-9cbaa05f]{flex:1;padding:16px 24px;overflow:auto;width:100%;box-sizing:border-box}.table-content[data-v-9cbaa05f]{width:100%;height:100%;display:flex;flex-direction:column;box-sizing:border-box}.table-pagination[data-v-9cbaa05f]{margin-top:16px;text-align:right}.product-category-table[data-v-9cbaa05f],.product-category-table[data-v-9cbaa05f] .ant-table,.product-category-table[data-v-9cbaa05f] .ant-table-container,.product-category-table[data-v-9cbaa05f] .ant-table-content,.product-category-table[data-v-9cbaa05f] .ant-table-body{width:100%!important}[data-v-9cbaa05f] .guns-split-panel-body{width:100%!important;flex:1!important;overflow:hidden!important}[data-v-9cbaa05f] .guns-split-panel{width:100%!important}\n",document.head.appendChild(a);const V={class:"guns-layout"},q={class:"guns-layout-sidebar width-100 p-t-12"},H={class:"sidebar-content"},J={class:"guns-layout-content"},W={class:"guns-layout"},X={class:"guns-layout-content-application"},Z={class:"content-main"},$={class:"content-main-header"},ee={class:"header-content"},ae={class:"header-content-left"},te={key:0,class:"current-category-info"},le={class:"header-content-right"},oe={class:"content-main-body"},de={class:"table-content"},ne={key:0,class:"super-search",style:{"margin-top":"8px"}};e("default",o({name:"ErpProductCategory",components:{PlusOutlined:d,SmallDashOutlined:n,productCategoryTree:K,addForm:Q},setup(){const e=r(!1),a=r(!1),t=r({}),l=r(null),o=r(null),d=r(),n=c((()=>({xxl:7,xl:7,lg:5,md:7,sm:4}))),g=c((()=>({xxl:17,xl:17,lg:19,md:17,sm:20}))),f=c((()=>i()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24})),p=s({parentId:void 0,searchText:"",status:void 0,categoryLevel:void 0}),b=async()=>{o.value.reload()},v=()=>{t.value={parentId:p.parentId,categoryLevel:l.value?l.value.categoryLevel+1:1},a.value=!0},h=e=>{t.value={...e},a.value=!0},_=()=>{if(o.value.selectedRowList&&0==o.value.selectedRowList.length)return T.warning("请选择需要删除的分类");I.confirm({title:"提示",content:"确定要删除选中的分类吗?",icon:y(E),maskClosable:!0,onOk:async()=>{var e;const a=await Y.batchDelete({categoryIds:o.value.selectedRowList});T.success(a.message),b(),null===(e=d.value)||void 0===e||e.reload()}})};return u((()=>{})),{tableRef:o,categoryTreeRef:d,advanced:e,where:p,columns:[{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"categoryCode",title:"分类编码",width:140,ellipsis:!0,isShow:!0},{dataIndex:"categoryName",title:"分类名称",width:160,ellipsis:!0,isShow:!0},{dataIndex:"parentName",title:"父级分类",width:140,ellipsis:!0,isShow:!0},{dataIndex:"categoryLevel",title:"分类层级",width:100,align:"center",isShow:!0},{dataIndex:"sortOrder",title:"排序号",width:80,align:"center",isShow:!0},{dataIndex:"status",title:"状态",width:80,align:"center",isShow:!0},{dataIndex:"createTime",title:"创建时间",width:140,ellipsis:!0,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}],currentRecord:t,currentCategoryInfo:l,showEdit:a,labelCol:n,wrapperCol:g,spanCol:f,reload:b,clear:()=>{p.searchText="",p.status=void 0,p.categoryLevel=void 0,p.parentId=void 0,d.value.currentSelectKeys=[],l.value=null,b()},toggleAdvanced:()=>{e.value=!e.value},onTreeSelect:(e,a)=>{if(e.length>0){const t=a.selectedNodes[0];l.value=t,p.parentId=e[0],b()}else l.value=null,p.parentId=void 0,b()},onAddCategory:e=>{e&&(l.value=e,p.parentId=e.categoryId),v()},onEditCategory:e=>{h(e)},onDeleteCategory:e=>{b()},openAddModal:v,openEditModal:h,deleteRecord:async e=>{I.confirm({title:"提示",content:"确定要删除选中的分类吗?",icon:y(E),maskClosable:!0,onOk:async()=>{var a;const t=await Y.delete({categoryId:e.categoryId});T.success(t.message),b(),null===(a=d.value)||void 0===a||a.reload()}})},moreClick:({key:e})=>{"1"==e&&_()},batchDelete:_,handleFormOk:()=>{var e;b(),null===(e=d.value)||void 0===e||e.reload()}}}},[["render",function(e,a,o,d,n,r){const c=g("product-category-tree"),i=L,s=S,u=g("plus-outlined"),I=R,E=j,T=A,Y=O,K=g("small-dash-outlined"),Q=z,re=N,ce=D,ie=P,se=U,ue=F,ge=M,fe=B,pe=G,ye=l,be=t,ve=g("addForm");return f(),p("div",V,[y(be,{width:"292px",cacheKey:"ERP_PRODUCT_CATEGORY_MANAGEMENT"},{content:b((()=>[v("div",J,[v("div",W,[v("div",X,[v("div",Z,[v("div",$,[v("div",ee,[v("div",ae,[y(s,{size:16},{default:b((()=>[d.currentCategoryInfo?(f(),p("span",te,[a[6]||(a[6]=h(" 当前分类：")),y(i,{color:"blue"},{default:b((()=>[h(_(d.currentCategoryInfo.categoryName),1)])),_:1})])):x("",!0)])),_:1})]),v("div",le,[y(s,{size:16},{default:b((()=>[y(I,{type:"primary",class:"border-radius",onClick:a[0]||(a[0]=e=>d.openAddModal())},{default:b((()=>[y(u),a[7]||(a[7]=h(" 新增子分类 "))])),_:1,__:[7]}),y(Q,null,{overlay:b((()=>[y(Y,{onClick:d.moreClick},{default:b((()=>[y(T,{key:"1"},{default:b((()=>[y(E,{iconClass:"icon-opt-shanchu",color:"#60666b"}),a[8]||(a[8]=v("span",null,"批量删除",-1))])),_:1,__:[8]})])),_:1},8,["onClick"])])),default:b((()=>[y(I,{class:"border-radius"},{default:b((()=>[a[9]||(a[9]=h(" 更多 ")),y(K)])),_:1,__:[9]})])),_:1})])),_:1})])])]),v("div",oe,[v("div",de,[y(ye,{columns:d.columns,where:d.where,fieldBusinessCode:"ERP_PRODUCT_CATEGORY_TABLE",showTableTool:"",showToolTotal:!1,rowId:"categoryId",ref:"tableRef",url:"/erp/productCategory/page"},{toolLeft:b((()=>[y(re,{value:d.where.searchText,"onUpdate:value":a[1]||(a[1]=e=>d.where.searchText=e),placeholder:"分类名称、编码（回车搜索）",allowClear:"",onPressEnter:d.reload,style:{width:"240px"},class:"search-input",bordered:!1},{prefix:b((()=>[y(E,{iconClass:"icon-opt-search"})])),_:1},8,["value","onPressEnter"]),y(ce,{type:"vertical",class:"divider"}),v("a",{onClick:a[2]||(a[2]=(...e)=>d.toggleAdvanced&&d.toggleAdvanced(...e))},_(d.advanced?"收起":"高级筛选"),1)])),toolBottom:b((()=>[d.advanced?(f(),p("div",ne,[y(pe,{model:d.where,labelCol:d.labelCol,"wrapper-col":d.wrapperCol},{default:b((()=>[y(fe,{gutter:16},{default:b((()=>[y(ge,m(w(d.spanCol)),{default:b((()=>[y(ue,{label:"状态:"},{default:b((()=>[y(se,{value:d.where.status,"onUpdate:value":a[3]||(a[3]=e=>d.where.status=e),placeholder:"请选择状态",style:{width:"100%"},allowClear:""},{default:b((()=>[y(ie,{value:"Y"},{default:b((()=>a[10]||(a[10]=[h("启用")]))),_:1,__:[10]}),y(ie,{value:"N"},{default:b((()=>a[11]||(a[11]=[h("停用")]))),_:1,__:[11]})])),_:1},8,["value"])])),_:1})])),_:1},16),y(ge,m(w(d.spanCol)),{default:b((()=>[y(ue,{label:"分类层级:"},{default:b((()=>[y(se,{value:d.where.categoryLevel,"onUpdate:value":a[4]||(a[4]=e=>d.where.categoryLevel=e),placeholder:"请选择层级",style:{width:"100%"},allowClear:""},{default:b((()=>[y(ie,{value:1},{default:b((()=>a[12]||(a[12]=[h("一级分类")]))),_:1,__:[12]}),y(ie,{value:2},{default:b((()=>a[13]||(a[13]=[h("二级分类")]))),_:1,__:[13]}),y(ie,{value:3},{default:b((()=>a[14]||(a[14]=[h("三级分类")]))),_:1,__:[14]}),y(ie,{value:4},{default:b((()=>a[15]||(a[15]=[h("四级分类")]))),_:1,__:[15]}),y(ie,{value:5},{default:b((()=>a[16]||(a[16]=[h("五级分类")]))),_:1,__:[16]})])),_:1},8,["value"])])),_:1})])),_:1},16),y(ge,m(w(d.spanCol)),{default:b((()=>[y(ue,{label:" ",class:"not-label"},{default:b((()=>[y(s,{size:16},{default:b((()=>[y(I,{class:"border-radius",onClick:d.reload,type:"primary"},{default:b((()=>a[17]||(a[17]=[h("查询")]))),_:1,__:[17]},8,["onClick"]),y(I,{class:"border-radius",onClick:d.clear},{default:b((()=>a[18]||(a[18]=[h("重置")]))),_:1,__:[18]},8,["onClick"])])),_:1})])),_:1})])),_:1},16)])),_:1})])),_:1},8,["model","labelCol","wrapper-col"])])):x("",!0)])),bodyCell:b((({column:e,record:t})=>["categoryLevel"===e.dataIndex?(f(),p(C,{key:0},[1===t.categoryLevel?(f(),k(i,{key:0,color:"red"},{default:b((()=>a[19]||(a[19]=[h("一级分类")]))),_:1,__:[19]})):2===t.categoryLevel?(f(),k(i,{key:1,color:"orange"},{default:b((()=>a[20]||(a[20]=[h("二级分类")]))),_:1,__:[20]})):3===t.categoryLevel?(f(),k(i,{key:2,color:"yellow"},{default:b((()=>a[21]||(a[21]=[h("三级分类")]))),_:1,__:[21]})):4===t.categoryLevel?(f(),k(i,{key:3,color:"green"},{default:b((()=>a[22]||(a[22]=[h("四级分类")]))),_:1,__:[22]})):5===t.categoryLevel?(f(),k(i,{key:4,color:"blue"},{default:b((()=>a[23]||(a[23]=[h("五级分类")]))),_:1,__:[23]})):(f(),k(i,{key:5,color:"default"},{default:b((()=>a[24]||(a[24]=[h("未知")]))),_:1,__:[24]}))],64)):"status"===e.dataIndex?(f(),p(C,{key:1},["Y"===t.status?(f(),k(i,{key:0,color:"green"},{default:b((()=>a[25]||(a[25]=[h("启用")]))),_:1,__:[25]})):"N"===t.status?(f(),k(i,{key:1,color:"red"},{default:b((()=>a[26]||(a[26]=[h("停用")]))),_:1,__:[26]})):(f(),k(i,{key:2,color:"default"},{default:b((()=>[h(_(t.status),1)])),_:2},1024))],64)):"categoryName"===e.dataIndex?(f(),p(C,{key:2},[h(_(t.categoryName),1)],64)):"action"===e.key?(f(),k(s,{key:3,size:16},{default:b((()=>[y(E,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>d.openEditModal(t)},null,8,["onClick"]),y(E,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>d.deleteRecord(t)},null,8,["onClick"])])),_:2},1024)):x("",!0)])),_:1},8,["columns","where"])])])])])])])])),default:b((()=>[v("div",q,[v("div",H,[y(c,{ref:"categoryTreeRef",isShowEditIcon:!0,onTreeSelect:d.onTreeSelect,onAddCategory:d.onAddCategory,onEditCategory:d.onEditCategory,onDeleteCategory:d.onDeleteCategory},null,8,["onTreeSelect","onAddCategory","onEditCategory","onDeleteCategory"])])])])),_:1}),y(ve,{visible:d.showEdit,"onUpdate:visible":a[5]||(a[5]=e=>d.showEdit=e),data:d.currentRecord,onOk:d.handleFormOk},null,8,["visible","data","onOk"])])}],["__scopeId","data-v-9cbaa05f"]]))}}}));
