package cn.stylefeng.roses.ent.saas.modular.auth.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户-功能包授权范围实例类
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@TableName("ent_tenant_package_auth")
@Data
@EqualsAndHashCode(callSuper = true)
public class TenantPackageAuth extends BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "package_auth_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键")
    private Long packageAuthId;

    /**
     * 功能包id
     */
    @TableField("package_id")
    @ChineseDescription("功能包id")
    private Long packageId;

    /**
     * 功能包绑定权限类型：1-菜单，2-功能
     */
    @TableField("limit_type")
    @ChineseDescription("功能包绑定权限类型：1-菜单，2-功能")
    private Integer limitType;

    /**
     * 业务id，为菜单id或菜单功能id
     */
    @TableField("business_id")
    @ChineseDescription("业务id，为菜单id或菜单功能id")
    private Long businessId;

}
