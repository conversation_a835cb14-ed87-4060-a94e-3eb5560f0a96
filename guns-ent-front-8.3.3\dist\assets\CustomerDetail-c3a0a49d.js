import{_ as h,r as S,X as N,a as c,f as T,w as t,d as a,g as n,t as s,b as g,c as b,F as k,e as A,m as w,Y as x,U as P,Z as B,M as D}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{C as m}from"./CustomerApi-e856427e.js";const V={name:"CustomerDetail",props:{visible:Boolean,data:{type:Object,default:()=>({})}},emits:["update:visible"],setup(r,{emit:C}){const e=S([]),d=l=>{C("update:visible",l)},_=async l=>{if(!l){e.value=[];return}try{const u=await m.getCustomerRegions({customerId:l});e.value=u||[]}catch(u){console.error("\u83B7\u53D6\u5BA2\u6237\u5173\u8054\u533A\u57DF\u5931\u8D25:",u),w.error("\u83B7\u53D6\u5BA2\u6237\u5173\u8054\u533A\u57DF\u5931\u8D25"),e.value=[]}};return N(()=>{var l;return(l=r.data)==null?void 0:l.customerId},l=>{l&&r.visible&&_(l)},{immediate:!0}),N(()=>r.visible,l=>{var u;l&&((u=r.data)!=null&&u.customerId)&&_(r.data.customerId)}),{regionList:e,updateVisible:d,getCustomerTypeName:l=>m.getCustomerTypeName(l),getCustomerLevelName:l=>m.getCustomerLevelName(l),getCustomerStatusName:l=>m.getCustomerStatusName(l),getStatusTagColor:l=>m.getStatusTagColor(l),getCustomerLevelTagColor:l=>m.getCustomerLevelTagColor(l),formatAmount:l=>m.formatAmount(l),formatPaymentTerms:l=>m.formatPaymentTerms(l)}}},I={style:{color:"#1890ff","font-weight":"bold"}},M={style:{color:"#f5222d","font-weight":"bold"}},U={style:{color:"#52c41a","font-weight":"bold"}},E={key:0,class:"region-tags"},F={key:1};function R(r,C,e,d,_,L){const o=x,i=P,v=B,y=D;return c(),T(y,{title:"\u5BA2\u6237\u8BE6\u60C5",width:800,visible:e.visible,footer:null,"onUpdate:visible":d.updateVisible},{default:t(()=>[a(v,{column:2,bordered:""},{default:t(()=>[a(o,{label:"\u5BA2\u6237\u7F16\u7801"},{default:t(()=>[n(s(e.data.customerCode),1)]),_:1}),a(o,{label:"\u5BA2\u6237\u540D\u79F0"},{default:t(()=>[n(s(e.data.customerName),1)]),_:1}),a(o,{label:"\u5BA2\u6237\u7B80\u79F0"},{default:t(()=>[n(s(e.data.customerShortName||"-"),1)]),_:1}),a(o,{label:"\u5BA2\u6237\u7C7B\u578B"},{default:t(()=>[a(i,null,{default:t(()=>[n(s(d.getCustomerTypeName(e.data.customerType)),1)]),_:1})]),_:1}),a(o,{label:"\u5BA2\u6237\u7B49\u7EA7"},{default:t(()=>[a(i,{color:d.getCustomerLevelTagColor(e.data.customerLevel)},{default:t(()=>[n(s(d.getCustomerLevelName(e.data.customerLevel)),1)]),_:1},8,["color"])]),_:1}),a(o,{label:"\u72B6\u6001"},{default:t(()=>[a(i,{color:d.getStatusTagColor(e.data.status)},{default:t(()=>[n(s(d.getCustomerStatusName(e.data.status)),1)]),_:1},8,["color"])]),_:1}),a(o,{label:"\u8054\u7CFB\u4EBA"},{default:t(()=>[n(s(e.data.contactPerson||"-"),1)]),_:1}),a(o,{label:"\u8054\u7CFB\u7535\u8BDD"},{default:t(()=>[n(s(e.data.contactPhone||"-"),1)]),_:1}),a(o,{label:"\u624B\u673A\u53F7\u7801"},{default:t(()=>[n(s(e.data.contactMobile||"-"),1)]),_:1}),a(o,{label:"\u90AE\u7BB1\u5730\u5740"},{default:t(()=>[n(s(e.data.contactEmail||"-"),1)]),_:1}),a(o,{label:"\u8054\u7CFB\u5730\u5740",span:2},{default:t(()=>[n(s(e.data.contactAddress||"-"),1)]),_:1}),a(o,{label:"\u8425\u4E1A\u6267\u7167\u53F7"},{default:t(()=>[n(s(e.data.businessLicenseNo||"-"),1)]),_:1}),a(o,{label:"\u7A0E\u52A1\u767B\u8BB0\u53F7"},{default:t(()=>[n(s(e.data.taxNo||"-"),1)]),_:1}),a(o,{label:"\u5F00\u6237\u94F6\u884C"},{default:t(()=>[n(s(e.data.bankName||"-"),1)]),_:1}),a(o,{label:"\u94F6\u884C\u8D26\u53F7"},{default:t(()=>[n(s(e.data.bankAccount||"-"),1)]),_:1}),a(o,{label:"\u4FE1\u7528\u989D\u5EA6"},{default:t(()=>[g("span",I,s(d.formatAmount(e.data.creditLimit)),1)]),_:1}),a(o,{label:"\u5DF2\u7528\u989D\u5EA6"},{default:t(()=>[g("span",M,s(d.formatAmount(e.data.usedCredit)),1)]),_:1}),a(o,{label:"\u53EF\u7528\u989D\u5EA6"},{default:t(()=>[g("span",U,s(d.formatAmount(e.data.availableCredit)),1)]),_:1}),a(o,{label:"\u8D26\u671F\u5929\u6570"},{default:t(()=>[n(s(d.formatPaymentTerms(e.data.paymentTerms)),1)]),_:1}),a(o,{label:"\u5173\u8054\u533A\u57DF",span:2},{default:t(()=>[d.regionList&&d.regionList.length>0?(c(),b("div",E,[(c(!0),b(k,null,A(d.regionList,f=>(c(),T(i,{key:f.regionId,color:"blue"},{default:t(()=>[n(s(f.regionName),1)]),_:2},1024))),128))])):(c(),b("span",F,"-"))]),_:1}),a(o,{label:"\u5907\u6CE8",span:2},{default:t(()=>[n(s(e.data.remark||"-"),1)]),_:1}),a(o,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:t(()=>[n(s(e.data.createTime),1)]),_:1}),a(o,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:t(()=>[n(s(e.data.updateTime),1)]),_:1})]),_:1})]),_:1},8,["visible","onUpdate:visible"])}const q=h(V,[["render",R],["__scopeId","data-v-6d7d4582"]]);export{q as default};
