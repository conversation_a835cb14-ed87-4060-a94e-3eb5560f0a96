<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-d-ds-container</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ds-container-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--多数据源业务模块-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>ds-container-business</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
