package cn.stylefeng.roses.kernel.oauth2.api.enums;

import lombok.Getter;

/**
 * OAuth2外部网站的项目秘钥配置，类别标识
 *
 * <AUTHOR>
 * @date 2022/7/1 17:04
 */
@Getter
public enum OAuth2ConfigEnum {

    /**
     * qq统一登录
     */
    QQ("qq", "clientId", "secret"),

    /**
     * 码云
     */
    GITEE("gitee", "clientId", "secret"),

    /**
     * 钉钉
     */
    DING("dingtalk", "clientId", "secret"),

    /**
     * 百度统一登录
     */
    BAIDU("baidu", "clientId", "secret"),

    /**
     * 微博
     */
    WEIBO("weibo", "clientId", "secret"),

    /**
     * CODING
     */
    CODING("coding", "clientId", "secret"),

    /**
     * 开源中国
     */
    OSC("oschina", "clientId", "secret"),

    /**
     * 支付宝
     */
    ALIPAY("alipay", "clientId", "secret"),

    /**
     * 微信统一平台
     */
    WECHAT("wechat", "clientId", "secret"),

    /**
     * 微信公众平台
     */
    WECHAT_MP("wechat-mp", "clientId", "secret");

    private final String code;

    private final String clientId;

    private final String secret;

    OAuth2ConfigEnum(String code, String clientId, String secret) {
        this.code = code;
        this.clientId = clientId;
        this.secret = secret;
    }

}
