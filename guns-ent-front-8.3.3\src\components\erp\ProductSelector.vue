<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :loading="loading"
    :allowClear="allowClear"
    :disabled="disabled"
    showSearch
    :filterOption="false"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear"
    style="width: 100%"
  >
    <a-select-option
      v-for="product in filteredProducts"
      :key="product.productId"
      :value="product.productId"
      :title="`${product.productName} (${product.productCode})`"
    >
      <div class="product-option">
        <div class="product-name">{{ product.productName }}</div>
        <div class="product-info">
          <span class="product-code">{{ product.productCode }}</span>
          <a-tag 
            v-if="product.pricingTypeName" 
            size="small" 
            :color="getPricingTypeColor(product.pricingType)"
          >
            {{ product.pricingTypeName }}
          </a-tag>
          <span v-if="product.currentStock !== undefined" class="product-stock">
            库存: {{ formatStock(product.currentStock, product.pricingType) }}
            {{ getStockUnit(product.pricingType, product.unit) }}
          </span>
        </div>
      </div>
    </a-select-option>
  </a-select>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { ProductApi } from '@/views/erp/product/api/ProductApi';

export default {
  name: 'ProductSelector',
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    placeholder: {
      type: String,
      default: '请选择商品'
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    filter: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const loading = ref(false);
    const products = ref([]);
    const searchKeyword = ref('');

    // 计算属性
    const selectedValue = computed({
      get: () => props.value,
      set: (value) => {
        emit('update:value', value);
      }
    });

    const filteredProducts = computed(() => {
      let result = products.value;

      // 按业务模式过滤
      if (props.filter.businessModeList && props.filter.businessModeList.length > 0) {
        result = result.filter(product => 
          props.filter.businessModeList.includes(product.businessMode)
        );
      }

      // 按计价类型过滤
      if (props.filter.pricingType) {
        result = result.filter(product => 
          product.pricingType === props.filter.pricingType
        );
      }

      // 按搜索关键词过滤
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(product => 
          product.productName.toLowerCase().includes(keyword) ||
          product.productCode.toLowerCase().includes(keyword) ||
          (product.barcode && product.barcode.toLowerCase().includes(keyword))
        );
      }

      return result;
    });

    // 加载商品列表
    const loadProducts = async () => {
      loading.value = true;
      try {
        const response = await ProductApi.findList({
          status: 'Y', // 只加载启用的商品
          pageSize: 1000 // 加载更多商品供选择
        });
        if (response.success) {
          products.value = response.data || [];
        }
      } catch (error) {
        console.error('加载商品列表失败:', error);
        message.error('加载商品列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索处理
    const handleSearch = (value) => {
      searchKeyword.value = value;
    };

    // 选择变化处理
    const handleChange = (value, option) => {
      const product = products.value.find(p => p.productId === value);
      emit('change', value, product);
    };

    // 清空处理
    const handleClear = () => {
      emit('change', undefined, null);
    };

    // 获取计价类型颜色
    const getPricingTypeColor = (pricingType) => {
      switch (pricingType) {
        case 'NORMAL':
          return 'blue';
        case 'WEIGHT':
          return 'orange';
        case 'PIECE':
          return 'green';
        case 'VARIABLE':
          return 'purple';
        default:
          return 'default';
      }
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 监听filter变化，重新过滤数据
    watch(() => props.filter, () => {
      // 过滤条件变化时，如果当前选中的商品不符合新的过滤条件，则清空选择
      if (props.value && filteredProducts.value.length > 0) {
        const currentProduct = filteredProducts.value.find(p => p.productId === props.value);
        if (!currentProduct) {
          selectedValue.value = undefined;
          emit('change', undefined, null);
        }
      }
    }, { deep: true });

    // 组件挂载时加载数据
    onMounted(() => {
      loadProducts();
    });

    return {
      loading,
      selectedValue,
      filteredProducts,
      handleSearch,
      handleChange,
      handleClear,
      getPricingTypeColor,
      getStockUnit,
      formatStock
    };
  }
};
</script>

<style scoped>
.product-option {
  padding: 4px 0;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.product-code {
  font-size: 12px;
  color: #8c8c8c;
}

.product-stock {
  font-size: 12px;
  color: #1890ff;
}

.ant-tag {
  margin: 0;
}
</style>
