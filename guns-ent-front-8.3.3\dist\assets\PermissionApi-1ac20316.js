import{R as t}from"./index-18a1ea24.js";class i{static getRoleCategoryAndRoleTree(e){return t.getAndLoadData("/permission/getRoleCategoryAndRoleTree",e)}static getRoleBindPermission(e){return t.getAndLoadData("/permission/getRoleBindPermission",e)}static getRoleList(e){return t.getAndLoadData("/permission/getRoleList",e)}static updateRoleBindPermission(e){return t.post("/permission/updateRoleBindPermission",e)}static getRoleDataScopePageList(e){return t.getAndLoadData("/roleDataScope/getRoleDataScopePageList",e)}static addRoleDataScope(e){return t.post("/roleDataScope/addRoleDataScope",e)}static editRoleDataScope(e){return t.post("/roleDataScope/editRoleDataScope",e)}static roleDataScopeDelete(e){return t.post("/roleDataScope/delete",e)}static getRoleLimit(e){return t.getAndLoadData("/roleLimit/getRoleLimit",e)}static bindRoleLimit(e){return t.post("/roleLimit/bindRoleLimit",e)}}export{i as P};
