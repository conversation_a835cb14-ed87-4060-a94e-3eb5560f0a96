package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * POS挂单实体类
 *
 * <AUTHOR>
 * @since 2025/08/01 10:20
 */
@TableName(value = "pos_suspended_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PosSuspendedOrder extends BaseEntity {

    /**
     * 挂单ID
     */
    @TableId(value = "suspend_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("挂单ID")
    private Long suspendId;

    /**
     * 挂单号
     */
    @TableField("suspend_no")
    @ChineseDescription("挂单号")
    private String suspendNo;

    /**
     * 收银员ID
     */
    @TableField("cashier_id")
    @ChineseDescription("收银员ID")
    private Long cashierId;

    /**
     * 订单数据JSON
     */
    @TableField("order_data")
    @ChineseDescription("订单数据JSON")
    private String orderData;

    /**
     * 挂单时间
     */
    @TableField("suspend_time")
    @ChineseDescription("挂单时间")
    private LocalDateTime suspendTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    @ChineseDescription("过期时间")
    private LocalDateTime expireTime;

    /**
     * 状态（ACTIVE-有效，RESUMED-已恢复，EXPIRED-已过期）
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;

}