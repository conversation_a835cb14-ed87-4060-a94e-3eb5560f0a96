package cn.stylefeng.roses.kernel.erp.modular.pos.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * POS订单项Mapper接口
 *
 * <AUTHOR>
 * @since 2025/08/01 10:30
 */
public interface PosOrderItemMapper extends BaseMapper<PosOrderItem> {

    /**
     * 根据订单ID列表查询订单项
     *
     * @param orderIds 订单ID列表
     * @return 订单项列表
     */
    List<PosOrderItem> findByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 根据商品ID查询销售记录
     *
     * @param productId 商品ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 销售记录列表
     */
    List<PosOrderItem> findSalesByProduct(@Param("productId") Long productId,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计商品销售数据
     *
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 销售统计数据
     */
    Map<String, Object> getProductSalesStats(@Param("productId") Long productId,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询商品销售排行
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderBy 排序字段（quantity-按数量，amount-按金额）
     * @param limit 限制数量
     * @return 商品销售排行
     */
    List<Map<String, Object>> getProductSalesRanking(@Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime,
                                                    @Param("orderBy") String orderBy,
                                                    @Param("limit") Integer limit);

    /**
     * 查询分类销售统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分类销售统计
     */
    List<Map<String, Object>> getCategorySalesStats(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询商品库存预警（基于销售数据）
     *
     * @param days 统计天数
     * @param threshold 预警阈值
     * @return 预警商品列表
     */
    List<Map<String, Object>> getInventoryAlertBySales(@Param("days") Integer days,
                                                      @Param("threshold") BigDecimal threshold);

    /**
     * 统计订单项数量
     *
     * @param orderId 订单ID
     * @return 订单项数量
     */
    Integer countOrderItems(@Param("orderId") Long orderId);

    /**
     * 计算订单项总金额
     *
     * @param orderId 订单ID
     * @return 总金额
     */
    BigDecimal calculateOrderItemsTotal(@Param("orderId") Long orderId);

    /**
     * 批量更新订单项状态
     *
     * @param orderIds 订单ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("orderIds") List<Long> orderIds,
                         @Param("status") String status);

    /**
     * 查询商品的平均销售价格
     *
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均价格
     */
    BigDecimal getAverageSellingPrice(@Param("productId") Long productId,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询商品销售趋势
     *
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组方式（day-按天，week-按周，month-按月）
     * @return 销售趋势数据
     */
    List<Map<String, Object>> getProductSalesTrend(@Param("productId") Long productId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  @Param("groupBy") String groupBy);

}