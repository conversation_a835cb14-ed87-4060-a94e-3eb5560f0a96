/** 简体中文 */
const localeValues = {
  // 多页签
  tabs: {
    reload: '刷新当前',
    fullscreen: '内容全屏',
    fullscreenExit: '退出全屏',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeOther: '关闭其他',
    closeAll: '关闭全部',
    close: '关闭当前'
  },
  // table工具按钮
  tableTools: {
    refresh: '刷新',
    size: '密度',
    sizeOption: {
      medium: '默认',
      small: '中等',
      mini: '紧凑'
    },
    columns: '列设置',
    columnsOption: {
      columns: '列展示',
      reset: '重置',
      untitled: '无标题',
      fixedLeft: '固定在左侧',
      fixedRight: '固定在右侧'
    },
    fullscreen: '全屏'
  },
  // 文件列表
  fileList: {
    selectAll: '全选',
    selectTips: '已选中 {total} 个文件/文件夹',
    fileName: '文件名',
    fileSize: '大小',
    fileTimestamp: '修改日期'
  },
  // 图片裁剪
  cropper: {
    zoomIn: '放大',
    zoomOut: '缩小',
    rotateLeft: '向左旋转',
    rotateRight: '向右旋转',
    moveLeft: '左移',
    moveRight: '右移',
    moveUp: '上移',
    moveDown: '下移',
    flipX: '左右翻转',
    flipY: '上下翻转',
    reset: '重新开始',
    upload: '选择图片',
    ok: '完成',
    title: '裁剪图片'
  },
  // 地图选择
  map: {
    title: '选择位置',
    placeholder: '输入关键字搜索',
    message: '请点击列表选中位置',
    ok: '确定'
  },
  // 颜色选择
  colorPicker: {
    cancelText: '取消',
    okText: '确定'
  },
  upload: {
    uploading: '上传中',
    exception: '上传失败',
    retry: '重试'
  },
  tour: {
    skip: '跳过',
    prev: '上一步',
    next: '下一步',
    finish: '结束'
  }
};

export default localeValues;
