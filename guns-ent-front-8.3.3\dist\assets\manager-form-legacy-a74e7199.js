System.register(["./index-legacy-ee1db0c7.js","./index-legacy-198191c1.js","./ThemeTemplateApi-legacy-4a2544d6.js","./FileApi-legacy-f85a3060.js"],(function(e,l){"use strict";var t,a,i,d,o,s,u,r,n,f,m,p,c,v,b,g,h,y,_,C,F,T,L,j,U,A;return{setters:[e=>{t=e.r,a=e.bh,i=e.bi,d=e.o,o=e.k,s=e.a,u=e.f,r=e.w,n=e.d,f=e.c,m=e.F,p=e.e,c=e.g,v=e.h,b=e.m,g=e.J,h=e.u,y=e.v,_=e.l,C=e.V,F=e.B,T=e.bk,L=e.G,j=e.H},null,e=>{U=e.T},e=>{A=e.F}],execute:function(){e("default",{__name:"manager-form",props:{form:Object,rules:Object,disabledChangeTemplate:Boolean,tempFileList:Object,templateFields:Array},emits:["getThemeAttributes"],setup(e,{emit:l}){const N=e,k=l,I=t([]),x=t(`${a}${A}?secretFlag=N`),O=t({Authorization:i()});d((()=>{$()}));const $=async()=>{let e=await U.findList();I.value=[];for(let l of e)"Y"===l.statusFlag&&I.value.push({value:l.templateId,label:l.templateName})},w=e=>{k("getThemeAttributes",e)};return(l,t)=>{const a=g,i=h,d=y,U=_,A=C,k=o("CloudUploadOutlined"),$=F,B=T,z=L,G=j;return s(),u(G,{ref:"formRef",model:e.form,rules:N.rules,layout:"vertical"},{default:r((()=>[n(z,{gutter:20},{default:r((()=>[n(d,{span:24},{default:r((()=>[n(i,{label:"主题模板",name:"templateId"},{default:r((()=>[n(a,{disabled:N.disabledChangeTemplate,value:e.form.templateId,"onUpdate:value":t[0]||(t[0]=l=>e.form.templateId=l),placeholder:"请选择主题模板",options:I.value,onChange:w},null,8,["disabled","value","options"])])),_:1})])),_:1}),n(d,{span:24},{default:r((()=>[n(i,{label:"主题名称:",name:"themeName"},{default:r((()=>[n(U,{value:e.form.themeName,"onUpdate:value":t[1]||(t[1]=l=>e.form.themeName=l),placeholder:"请输入主题名称","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),n(A,{style:{"padding-bottom":"24px"}}),(s(!0),f(m,null,p(e.templateFields,(l=>(s(),u(d,{span:24,key:l},{default:r((()=>[n(i,{label:l.fieldName,name:l.fieldCode},{default:r((()=>["string"===l.fieldType?(s(),u(U,{key:0,value:e.form[l.fieldCode],"onUpdate:value":t=>e.form[l.fieldCode]=t},null,8,["value","onUpdate:value"])):"file"===l.fieldType?(s(),u(B,{key:1,name:"file",multiple:!1,action:x.value,headers:O.value,"list-type":"picture","file-list":e.tempFileList[l.fieldCode],"onUpdate:fileList":t=>e.tempFileList[l.fieldCode]=t,onChange:e=>{return t=e,a=l.fieldCode,void("done"===t.file.status?(N.tempFileList[a]=[t.file],b.success(`${t.file.name} 图片上传成功`),N.form[a]=t.file.response.data.fileId):"error"===t.file.status&&b.error(`${t.file.name} 图片上传失败`));var t,a}},{default:r((()=>[n($,{class:"border-radius"},{default:r((()=>[n(k),t[2]||(t[2]=c(" 上传图片 "))])),_:1,__:[2]})])),_:2},1032,["action","headers","file-list","onUpdate:fileList","onChange"])):v("",!0)])),_:2},1032,["label","name"])])),_:2},1024)))),128))])),_:1})])),_:1},8,["model","rules"])}}})}}}));
