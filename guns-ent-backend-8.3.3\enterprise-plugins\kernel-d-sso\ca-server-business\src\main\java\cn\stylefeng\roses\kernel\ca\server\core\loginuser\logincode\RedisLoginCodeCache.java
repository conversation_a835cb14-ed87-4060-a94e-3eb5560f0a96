package cn.stylefeng.roses.kernel.ca.server.core.loginuser.logincode;

import cn.stylefeng.roses.kernel.ca.api.constants.CaServerConstants;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import cn.stylefeng.roses.kernel.cache.redis.AbstractRedisCacheOperator;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 基于redis的loginCode缓存
 * <p>
 * key：    SSO LoginCode，是一个临时的密码校验成功的一个凭证
 * value：  CaLoginUser，单点登录用户信息的一个标识
 *
 * <AUTHOR>
 * @date 2021/1/28 14:25
 */
public class RedisLoginCodeCache extends AbstractRedisCacheOperator<CaLoginUser> {

    public RedisLoginCodeCache(RedisTemplate<String, CaLoginUser> redisTemplate) {
        super(redisTemplate);
    }

    @Override
    public String getCommonKeyPrefix() {
        return CaServerConstants.CA_LOGIN_CODE_CACHE_PREFIX;
    }

}
