System.register(["./index-legacy-53580278.js","./index-legacy-ee1db0c7.js","./security-legacy-e9720905.js","./index-legacy-94a6fc23.js"],(function(e,t){"use strict";var l,n,s,u,a,c,i,d,r,o,_,v,y,S,g,b,T;return{setters:[e=>{l=e._},e=>{n=e.b3,s=e.r,u=e.L,a=e.o,c=e.bv,i=e.a,d=e.c,r=e.d,o=e.w,_=e.b,v=e.g,y=e.aR,S=e.p,g=e.bq,b=e.q},e=>{T=e.default},null],execute:function(){const t={class:"guns-layout"},f={class:"guns-layout-sidebar width-100 p-t-12"},k={class:"sidebar-content"},p={class:"sidebar-content"},A={class:"guns-layout-content"};e("default",Object.assign({name:"BackendSecurity"},{__name:"index",setup(e){const E=n(),m=s(["1"]),C=s([]),I=s(""),O=u((()=>{var e;return null!==(e=E.authorities)&&void 0!==e?e:[]})),j=({key:e})=>{I.value=e},x=()=>{m.value=["1"]};return a((()=>{let e="";C.value=[],O.value.includes("PASSWORD_STRATEGY_CONFIG")?e="1":O.value.includes("BLACK_WHITE_LIST_UPDATE")&&(e="2"),e&&(C.value=[e],j({key:e}))})),(e,n)=>{const s=S,u=g,a=b,E=l,O=c("permission");return i(),d("div",t,[r(E,{resizable:!1},{content:o((()=>[_("div",A,[r(T,{currentMenuSelect:I.value},null,8,["currentMenuSelect"])])])),default:o((()=>[_("div",f,[_("div",k,[_("div",p,[r(a,{selectedKeys:C.value,"onUpdate:selectedKeys":n[0]||(n[0]=e=>C.value=e),class:"sidebar-menu",mode:"inline","open-keys":m.value,onSelect:j,onOpenChange:x},{default:o((()=>[r(u,{key:"1"},{title:o((()=>n[1]||(n[1]=[v("策略配置")]))),default:o((()=>[y((i(),d("div",null,[r(s,{key:"1"},{default:o((()=>n[2]||(n[2]=[v(" 密码策略 ")]))),_:1,__:[2]})])),[[O,["PASSWORD_STRATEGY_CONFIG"]]]),y((i(),d("div",null,[r(s,{key:"2"},{default:o((()=>n[3]||(n[3]=[v(" 黑白名单 ")]))),_:1,__:[3]})])),[[O,["BLACK_WHITE_LIST_UPDATE"]]])])),_:1})])),_:1},8,["selectedKeys","open-keys"])])])])])),_:1})])}}}))}}}));
