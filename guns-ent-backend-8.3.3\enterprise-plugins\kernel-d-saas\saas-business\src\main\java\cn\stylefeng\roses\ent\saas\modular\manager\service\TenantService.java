package cn.stylefeng.roses.ent.saas.modular.manager.service;

import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * 租户信息 服务类
 *
 * <AUTHOR>
 * @date 2023/08/30 17:04
 */
public interface TenantService extends IService<Tenant> {

    /**
     * 新增
     *
     * @param tenantRequest 请求参数
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    void add(TenantRequest tenantRequest);

    /**
     * 删除
     *
     * @param tenantRequest 请求参数
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    void del(TenantRequest tenantRequest);

    /**
     * 编辑
     *
     * @param tenantRequest 请求参数
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    void edit(TenantRequest tenantRequest);

    /**
     * 查询详情
     *
     * @param tenantRequest 请求参数
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    Tenant detail(TenantRequest tenantRequest);

    /**
     * 获取列表
     *
     * @param tenantRequest 请求参数
     * @return List<Tenant>   返回结果
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    List<Tenant> findList(TenantRequest tenantRequest);

    /**
     * 获取列表（带分页）
     *
     * @param tenantRequest 请求参数
     * @return PageResult<Tenant>   返回结果
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    PageResult<Tenant> findPage(TenantRequest tenantRequest);

    /**
     * 校验租户编码是否存在，true-存在，false-不存在
     *
     * <AUTHOR>
     * @since 2023/8/31 0:30
     */
    boolean checkTenantCodeAlreadyExisted(TenantRequest tenantRequest);

    /**
     * 初始化租户信息
     *
     * <AUTHOR>
     * @since 2023/9/7 9:52
     */
    void initTenant(Long tenantId, TenantRequest tenantRequest, String calculatedPassword, String calculatedSalt);

    /**
     * 获取租户的管理员角色
     * <p>
     * 也就是为系统类型的租户角色
     *
     * <AUTHOR>
     * @since 2024-01-23 12:57
     */
    Set<Long> getTenantAdminRoleId(Long tenantId);

}