/**
 * 树配置工厂类
 * 
 * <AUTHOR>
 * @since 2025/01/24
 */

import { 
  RegionTreeConfig, 
  ProductCategoryTreeConfig, 
  createRegionReadOnlyConfig,
  createProductCategoryReadOnlyConfig,
  ConfigPresetType
} from './configs.js'
import { TreeConfigBuilder, deepMergeConfig } from './utils.js'

/**
 * 树配置工厂类
 */
export class TreeConfigFactory {
  /**
   * 创建区域管理树配置
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createRegionConfig(customOptions = {}) {
    return deepMergeConfig(RegionTreeConfig, customOptions)
  }

  /**
   * 创建产品分类管理树配置
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createProductCategoryConfig(customOptions = {}) {
    return deepMergeConfig(ProductCategoryTreeConfig, customOptions)
  }

  /**
   * 创建区域只读树配置
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createRegionReadOnlyConfig(customOptions = {}) {
    const baseConfig = createRegionReadOnlyConfig()
    return deepMergeConfig(baseConfig, customOptions)
  }

  /**
   * 创建产品分类只读树配置
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createProductCategoryReadOnlyConfig(customOptions = {}) {
    const baseConfig = createProductCategoryReadOnlyConfig()
    return deepMergeConfig(baseConfig, customOptions)
  }

  /**
   * 创建自定义配置
   * @param {Object} options 配置选项
   * @returns {TreeConfig} 配置对象
   */
  static createCustomConfig(options) {
    const builder = new TreeConfigBuilder()
    
    if (options.dataSource) {
      const { api, lazyLoadApi, searchParam, parentIdParam } = options.dataSource
      builder.setDataSource(api, lazyLoadApi, searchParam, parentIdParam)
    }

    if (options.fieldMapping) {
      const { key, title, children, hasChildren, level } = options.fieldMapping
      builder.setFieldMapping(key, title, children, hasChildren, level)
    }

    if (options.displayConfig) {
      builder.setDisplayConfig(options.displayConfig)
    }

    if (options.interactionConfig) {
      builder.setInteractionConfig(options.interactionConfig)
    }

    if (options.actionConfig) {
      builder.setActionConfig(options.actionConfig)
    }

    // 根据模式设置
    if (options.mode === 'readonly') {
      builder.enableReadOnlyMode()
    } else if (options.mode === 'edit') {
      builder.enableEditMode()
    }

    return builder.build()
  }

  /**
   * 根据预设类型创建配置
   * @param {string} presetType 预设类型
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createByPreset(presetType, customOptions = {}) {
    switch (presetType) {
      case ConfigPresetType.REGION_TREE:
        return this.createRegionConfig(customOptions)
      case ConfigPresetType.PRODUCT_CATEGORY_TREE:
        return this.createProductCategoryConfig(customOptions)
      case ConfigPresetType.REGION_READONLY:
        return this.createRegionReadOnlyConfig(customOptions)
      case ConfigPresetType.PRODUCT_CATEGORY_READONLY:
        return this.createProductCategoryReadOnlyConfig(customOptions)
      default:
        throw new Error(`不支持的预设类型: ${presetType}`)
    }
  }

  /**
   * 创建商品管理页面配置（产品分类只读）
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createProductManagementConfig(customOptions = {}) {
    return this.createProductCategoryReadOnlyConfig({
      displayConfig: {
        title: '产品分类',
        showHeader: true,
        searchPlaceholder: '请输入分类名称搜索'
      },
      ...customOptions
    })
  }

  /**
   * 创建客户管理页面配置（区域只读）
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createCustomerManagementConfig(customOptions = {}) {
    return this.createRegionReadOnlyConfig({
      displayConfig: {
        title: '区域筛选',
        showHeader: true,
        searchPlaceholder: '请输入区域名称搜索'
      },
      ...customOptions
    })
  }

  /**
   * 创建供应商管理页面配置（区域只读）
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createSupplierManagementConfig(customOptions = {}) {
    return this.createRegionReadOnlyConfig({
      displayConfig: {
        title: '区域筛选',
        showHeader: true,
        searchPlaceholder: '请输入区域名称搜索'
      },
      ...customOptions
    })
  }

  /**
   * 快速创建只读配置
   * @param {string} baseType 基础类型 ('region' | 'productCategory')
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createReadOnlyConfig(baseType, customOptions = {}) {
    if (baseType === 'region') {
      return this.createRegionReadOnlyConfig(customOptions)
    } else if (baseType === 'productCategory') {
      return this.createProductCategoryReadOnlyConfig(customOptions)
    } else {
      throw new Error(`不支持的基础类型: ${baseType}`)
    }
  }

  /**
   * 快速创建编辑配置
   * @param {string} baseType 基础类型 ('region' | 'productCategory')
   * @param {Object} customOptions 自定义选项
   * @returns {TreeConfig} 配置对象
   */
  static createEditConfig(baseType, customOptions = {}) {
    if (baseType === 'region') {
      return this.createRegionConfig(customOptions)
    } else if (baseType === 'productCategory') {
      return this.createProductCategoryConfig(customOptions)
    } else {
      throw new Error(`不支持的基础类型: ${baseType}`)
    }
  }

  /**
   * 获取所有可用的预设配置
   * @returns {Object} 预设配置映射
   */
  static getAvailablePresets() {
    return {
      [ConfigPresetType.REGION_TREE]: '区域管理树',
      [ConfigPresetType.PRODUCT_CATEGORY_TREE]: '产品分类管理树',
      [ConfigPresetType.REGION_READONLY]: '区域只读树',
      [ConfigPresetType.PRODUCT_CATEGORY_READONLY]: '产品分类只读树'
    }
  }

  /**
   * 验证配置
   * @param {TreeConfig} config 配置对象
   * @returns {ConfigValidationResult} 验证结果
   */
  static validateConfig(config) {
    const errors = []
    const warnings = []

    // 检查必需的配置
    if (!config.dataSource || !config.dataSource.api) {
      errors.push('dataSource.api 是必需的')
    }

    if (!config.fieldMapping) {
      errors.push('fieldMapping 是必需的')
    } else {
      if (!config.fieldMapping.key) {
        errors.push('fieldMapping.key 是必需的')
      }
      if (!config.fieldMapping.title) {
        errors.push('fieldMapping.title 是必需的')
      }
      if (!config.fieldMapping.children) {
        errors.push('fieldMapping.children 是必需的')
      }
    }

    // 检查可选但推荐的配置
    if (!config.displayConfig) {
      warnings.push('建议提供 displayConfig 配置')
    }

    if (!config.interactionConfig) {
      warnings.push('建议提供 interactionConfig 配置')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// 导出便捷方法
export const createRegionTreeConfig = TreeConfigFactory.createRegionConfig
export const createProductCategoryTreeConfig = TreeConfigFactory.createProductCategoryConfig
export const createRegionReadOnlyTreeConfig = TreeConfigFactory.createRegionReadOnlyConfig
export const createProductCategoryReadOnlyTreeConfig = TreeConfigFactory.createProductCategoryReadOnlyConfig
export const createCustomTreeConfig = TreeConfigFactory.createCustomConfig