System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var a;return{setters:[e=>{a=e.R}],execute:function(){class t{static add(e){return a.post("/erp/supplier/add",e)}static delete(e){return a.post("/erp/supplier/delete",e)}static batchDelete(e){return a.post("/erp/supplier/batchDelete",e)}static edit(e){return a.post("/erp/supplier/edit",e)}static detail(e){return a.getAndLoadData("/erp/supplier/detail",e)}static findPage(e){return a.getAndLoadData("/erp/supplier/page",e)}static findList(e){return a.getAndLoadData("/erp/supplier/list",e)}static updateStatus(e){return a.post("/erp/supplier/updateStatus",e)}static validateCode(e){return a.getAndLoadData("/erp/supplier/validateCode",e)}static getSupplierRegions(e){return a.getAndLoadData("/erp/supplierRegion/getSupplierRegions",e)}static updateSupplierRegions(e){return a.post("/erp/supplierRegion/updateSupplierRegions",e)}static findSuppliersByRegion(e){return a.getAndLoadData("/erp/supplierRegion/findSuppliersByRegion",e)}static getSupplierTypeOptions(){return[{label:"企业",value:"ENTERPRISE"},{label:"个体",value:"INDIVIDUAL"}]}static getBusinessModeOptions(){return[{label:"购销",value:"PURCHASE_SALE"},{label:"联营",value:"JOINT_VENTURE"},{label:"代销",value:"CONSIGNMENT"}]}static getSupplierStatusOptions(){return[{label:"正常",value:"ACTIVE"},{label:"停用",value:"INACTIVE"},{label:"黑名单",value:"BLACKLIST"}]}static getCreditLevelOptions(){return[{label:"优秀",value:"A"},{label:"良好",value:"B"},{label:"一般",value:"C"},{label:"较差",value:"D"}]}static getSupplierTypeName(e){const a=t.getSupplierTypeOptions().find((t=>t.value===e));return a?a.label:e}static getBusinessModeName(e){const a=t.getBusinessModeOptions().find((t=>t.value===e));return a?a.label:e}static getSupplierStatusName(e){const a=t.getSupplierStatusOptions().find((t=>t.value===e));return a?a.label:e}static getCreditLevelName(e){const a=t.getCreditLevelOptions().find((t=>t.value===e));return a?a.label:e}static getStatusTagColor(e){switch(e){case"ACTIVE":return"green";case"INACTIVE":return"orange";case"BLACKLIST":return"red";default:return"default"}}static getCreditLevelTagColor(e){switch(e){case"A":return"green";case"B":return"blue";case"C":return"orange";case"D":return"red";default:return"default"}}static getBusinessModeTagColor(e){switch(e){case"PURCHASE_SALE":return"blue";case"JOINT_VENTURE":return"green";case"CONSIGNMENT":return"orange";default:return"default"}}static getSupplierProducts(e){return a.getAndLoadData("/erp/supplier/products",e)}static validateBusinessModeChange(e){return a.getAndLoadData("/erp/supplier/validateBusinessModeChange",e)}}e("S",t)}}}));
