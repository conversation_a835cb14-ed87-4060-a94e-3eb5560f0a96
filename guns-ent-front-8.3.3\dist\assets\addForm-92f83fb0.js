import{_ as A,r as L,s as M,X as I,a as j,f as F,w as l,d as t,g as U,m as R,l as V,u as q,v as B,G as S,as as Y,y as H,W as E,J as G,$ as J,H as P,M as W}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{R as N}from"./regionApi-2c103d88.js";const X={name:"RegionAddForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(m,{emit:n}){const _=L(),a=L(!1),g=L([]),v=L("1\u7EA7 - \u56FD\u5BB6"),o=M({regionId:null,regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),u={regionCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u533A\u57DF\u7F16\u7801",trigger:"blur"}],regionName:[{required:!0,message:"\u8BF7\u8F93\u5165\u533A\u57DF\u540D\u79F0",trigger:"blur"}],regionLevel:[{required:!0,message:"\u8BF7\u9009\u62E9\u533A\u57DF\u5C42\u7EA7",trigger:"change"}],status:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",trigger:"change"}]},s=e=>{n("update:visible",e),e||f()},f=()=>{var e;(e=_.value)==null||e.resetFields(),Object.assign(o,{regionCode:"",regionName:"",parentId:void 0,regionLevel:1,sortOrder:0,status:"Y",remark:""}),c()},p=e=>({1:"1\u7EA7 - \u56FD\u5BB6",2:"2\u7EA7 - \u7701",3:"3\u7EA7 - \u5E02",4:"4\u7EA7 - \u533A\u53BF",5:"5\u7EA7 - \u5546\u5708"})[e]||"".concat(e,"\u7EA7 - \u672A\u77E5"),k=e=>{if(!e)return 1;const r=(O,T)=>{for(const b of O){if(b.regionId===T)return b.regionLevel||1;if(b.children&&b.children.length>0){const C=r(b.children,T);if(C)return C}}return null},d=r(g.value,e);return d?Math.min(d+1,5):1},c=()=>{const e=k(o.parentId);o.regionLevel=e,v.value=p(e)},w=(e,r)=>r.title&&r.title.toLowerCase().includes(e.toLowerCase()),x=async()=>{try{let r=await N.findTree()||[];r=y(r),o.regionId?g.value=h(r,o.regionId):g.value=r}catch(e){console.error("\u52A0\u8F7D\u533A\u57DF\u6811\u5931\u8D25:",e),g.value=[]}},y=e=>Array.isArray(e)?e.filter(r=>!r||!r.regionId||!r.regionName?!1:(r.title=r.regionName,r.key=String(r.regionId),r.value=String(r.regionId),r.children&&Array.isArray(r.children)&&(r.children=y(r.children)),!0)):[],h=(e,r)=>e.filter(d=>d.regionId===r?!1:(d.children&&d.children.length>0&&(d.children=h(d.children,r)),!0)),i=async()=>{try{await _.value.validate(),a.value=!0;const e=!!o.regionId,r=e?N.edit:N.add,d=e?"\u7F16\u8F91\u6210\u529F":"\u65B0\u589E\u6210\u529F",O=e?"\u7F16\u8F91\u5931\u8D25":"\u65B0\u589E\u5931\u8D25";await r(o),R.success(d),n("ok"),s(!1)}catch(e){console.error("\u4FDD\u5B58\u533A\u57DF\u5931\u8D25:",e),R.error("\u4FDD\u5B58\u5931\u8D25")}finally{a.value=!1}};return I(()=>m.data,e=>{e&&Object.keys(e).length>0&&(Object.assign(o,e),e.regionLevel?v.value=p(e.regionLevel):c())},{immediate:!0}),I(()=>m.visible,e=>{e?(x(),m.data&&Object.keys(m.data).length>0&&(Object.assign(o,m.data),setTimeout(()=>{m.data.regionLevel?v.value=p(m.data.regionLevel):c()},100))):f()}),I(()=>o.parentId,()=>{c()}),{formRef:_,loading:a,form:o,rules:u,regionTreeData:g,regionLevelText:v,updateVisible:s,save:i,filterTreeNode:w}}};function z(m,n,_,a,g,v){const o=V,u=q,s=B,f=S,p=Y,k=H,c=E,w=G,x=J,y=P,h=W;return j(),F(h,{title:a.form.regionId?"\u7F16\u8F91\u533A\u57DF":"\u65B0\u589E\u533A\u57DF",width:800,visible:_.visible,"confirm-loading":a.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":a.updateVisible,onOk:a.save},{default:l(()=>[t(y,{ref:"formRef",model:a.form,rules:a.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:l(()=>[t(f,{gutter:16},{default:l(()=>[t(s,{md:12,sm:24},{default:l(()=>[t(u,{label:"\u533A\u57DF\u7F16\u7801",name:"regionCode"},{default:l(()=>[t(o,{value:a.form.regionCode,"onUpdate:value":n[0]||(n[0]=i=>a.form.regionCode=i),placeholder:"\u8BF7\u8F93\u5165\u533A\u57DF\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),t(s,{md:12,sm:24},{default:l(()=>[t(u,{label:"\u533A\u57DF\u540D\u79F0",name:"regionName"},{default:l(()=>[t(o,{value:a.form.regionName,"onUpdate:value":n[1]||(n[1]=i=>a.form.regionName=i),placeholder:"\u8BF7\u8F93\u5165\u533A\u57DF\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(f,{gutter:16},{default:l(()=>[t(s,{md:12,sm:24},{default:l(()=>[t(u,{label:"\u7236\u7EA7\u533A\u57DF",name:"parentId"},{default:l(()=>[t(p,{value:a.form.parentId,"onUpdate:value":n[2]||(n[2]=i=>a.form.parentId=i),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":a.regionTreeData,placeholder:"\u8BF7\u9009\u62E9\u7236\u7EA7\u533A\u57DF","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":a.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])]),_:1})]),_:1}),t(s,{md:12,sm:24},{default:l(()=>[t(u,{label:"\u533A\u57DF\u5C42\u7EA7",name:"regionLevel"},{default:l(()=>[t(o,{value:a.regionLevelText,"onUpdate:value":n[3]||(n[3]=i=>a.regionLevelText=i),placeholder:"\u6839\u636E\u7236\u7EA7\u533A\u57DF\u81EA\u52A8\u8BBE\u7F6E",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(f,{gutter:16},{default:l(()=>[t(s,{md:12,sm:24},{default:l(()=>[t(u,{label:"\u6392\u5E8F\u53F7",name:"sortOrder"},{default:l(()=>[t(k,{value:a.form.sortOrder,"onUpdate:value":n[4]||(n[4]=i=>a.form.sortOrder=i),min:0,max:9999,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F\u53F7"},null,8,["value"])]),_:1})]),_:1}),t(s,{md:12,sm:24},{default:l(()=>[t(u,{label:"\u72B6\u6001",name:"status"},{default:l(()=>[t(w,{value:a.form.status,"onUpdate:value":n[5]||(n[5]=i=>a.form.status=i),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:l(()=>[t(c,{value:"Y"},{default:l(()=>n[7]||(n[7]=[U("\u542F\u7528")])),_:1,__:[7]}),t(c,{value:"N"},{default:l(()=>n[8]||(n[8]=[U("\u505C\u7528")])),_:1,__:[8]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(f,{gutter:16},{default:l(()=>[t(s,{span:24},{default:l(()=>[t(u,{label:"\u5907\u6CE8",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:l(()=>[t(x,{value:a.form.remark,"onUpdate:value":n[6]||(n[6]=i=>a.form.remark=i),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","visible","confirm-loading","onUpdate:visible","onOk"])}const $=A(X,[["render",z]]);export{$ as default};
