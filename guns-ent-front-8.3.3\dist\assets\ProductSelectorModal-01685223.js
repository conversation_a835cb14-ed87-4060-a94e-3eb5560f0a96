import{_ as U,r as f,s as Q,L as K,X as E,m as L,a as C,f as R,w as n,d as s,g as u,t as _,b as p,c as T,h as v,a2 as F,B as W,aO as q,S as H,v as J,I as j,l as D,W as X,J as Y,n as Z,G as $,U as ee,i as te,ba as oe,M as ae}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{P as ne}from"./ProductApi-52d42f8e.js";import{P as le}from"./productCategoryApi-39e417fd.js";const se={name:"ProductSelectorModal",props:{visible:{type:Boolean,default:!1},supplierId:{type:[String,Number],default:null}},emits:["update:visible","select"],setup(y,{emit:o}){const I=f(!1),t=f(!1),P=f([]),M=f([]),d=f([]),h=f([]),S=f([]),l=Q({searchText:"",categoryId:void 0,pricingType:void 0,supplierId:null}),c=Q({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"\u5171 ".concat(e," \u6761\u8BB0\u5F55")}),w=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:250},{title:"\u5206\u7C7B",key:"categoryName",width:120},{title:"\u8BA1\u4EF7\u7C7B\u578B",key:"pricingType",width:100},{title:"\u96F6\u552E\u4EF7",key:"retailPrice",width:100,align:"right"},{title:"\u5E93\u5B58",key:"stockQuantity",width:100,align:"right"}],k=K(()=>({selectedRowKeys:h.value.map(e=>e.productId),onChange:(e,a)=>{console.log("\u884C\u9009\u62E9\u53D8\u5316:",{selectedRowKeys:e,selectedRows:a}),h.value=a},getCheckboxProps:e=>(console.log("\u5546\u54C1\u590D\u9009\u6846\u72B6\u6001\u68C0\u67E5:",{productId:e.productId,status:e.status}),{disabled:!1})}));E(()=>y.supplierId,e=>{l.supplierId=e,y.visible&&e&&g()}),E(()=>y.visible,e=>{console.log("\u5F39\u7A97\u663E\u793A\u72B6\u6001\u53D8\u5316:",e),e&&(h.value=[],l.supplierId=y.supplierId,console.log("\u5F00\u59CB\u52A0\u8F7D\u5206\u7C7B\u6811\u548C\u5546\u54C1\u6570\u636E\uFF0C\u4F9B\u5E94\u5546ID:",y.supplierId),z(),g())}),E(()=>d.value,e=>{console.log("\u5206\u7C7B\u6811\u6570\u636E\u53D8\u5316:",{\u6570\u636E\u957F\u5EA6:e.length,\u7B2C\u4E00\u4E2A\u8282\u70B9:e[0],\u5B8C\u6574\u6570\u636E:e})},{deep:!0});const z=async()=>{t.value=!0;try{console.log("\u5F00\u59CB\u52A0\u8F7D\u5546\u54C1\u5206\u7C7B\u6811");const e=await le.findTree({status:"Y"});if(console.log("\u5546\u54C1\u5206\u7C7B\u6811API\u54CD\u5E94:",e),e&&Array.isArray(e)){const a=m=>{const V={...m,key:m.key||m.categoryId,title:m.title||m.categoryName};return m.children&&Array.isArray(m.children)&&(V.children=m.children.map(a)),V},x={key:"ALL_CATEGORIES",title:"\u5168\u90E8\u5206\u7C7B",categoryId:null,categoryName:"\u5168\u90E8\u5206\u7C7B",children:e.map(a),productCount:null,isLeaf:!1};d.value=[x],S.value=["ALL_CATEGORIES"],console.log("\u5546\u54C1\u5206\u7C7B\u6811\u52A0\u8F7D\u6210\u529F\uFF0C\u8282\u70B9\u6570\u91CF:",e.length),console.log("\u539F\u59CBAPI\u6570\u636E:",e),console.log("\u5904\u7406\u540E\u7684\u6811\u6570\u636E:",d.value)}else console.warn("\u5546\u54C1\u5206\u7C7B\u6811\u6570\u636E\u683C\u5F0F\u9519\u8BEF:",e),d.value=[]}catch(e){console.error("\u52A0\u8F7D\u5546\u54C1\u5206\u7C7B\u6811\u5931\u8D25:",e),L.error("\u52A0\u8F7D\u5546\u54C1\u5206\u7C7B\u5931\u8D25\uFF1A"+(e.message||"\u672A\u77E5\u9519\u8BEF")),d.value=[]}finally{t.value=!1}},A=(e,a)=>{if(console.log("\u5206\u7C7B\u6811\u9009\u62E9:",{selectedKeys:e,info:a}),e.length>0){const b=e[0];if(b==="ALL_CATEGORIES")l.categoryId=null;else{const x=a.node;l.categoryId=x.categoryId||b}S.value=e,console.log("\u8BBE\u7F6E\u5206\u7C7B\u7B5B\u9009\u6761\u4EF6:",l.categoryId),c.current=1,g()}},g=async()=>{if(!y.supplierId){P.value=[];return}I.value=!0;try{console.log("\u5F00\u59CB\u52A0\u8F7D\u5546\u54C1\u5217\u8868\uFF0C\u53C2\u6570:",{supplierId:y.supplierId,categoryId:l.categoryId,searchText:l.searchText,pricingType:l.pricingType,pageNo:c.current,pageSize:c.pageSize});const e={supplierId:y.supplierId,pageNo:c.current,pageSize:c.pageSize};l.categoryId&&(e.categoryId=l.categoryId),l.searchText&&(e.searchText=l.searchText),l.pricingType&&(e.pricingType=l.pricingType);const a=await ne.findPage(e);console.log("\u5546\u54C1\u5217\u8868API\u54CD\u5E94:",a),a&&a.rows?(P.value=a.rows,c.total=a.totalRows,c.current=a.pageNo,c.pageSize=a.pageSize,console.log("\u5546\u54C1\u5217\u8868\u52A0\u8F7D\u6210\u529F\uFF0C\u603B\u6570:",a.totalRows,"\u5F53\u524D\u9875:",a.rows.length)):(console.warn("\u5546\u54C1\u6570\u636E\u683C\u5F0F\u9519\u8BEF:",a),P.value=[],c.total=0)}catch(e){console.error("\u52A0\u8F7D\u5546\u54C1\u5217\u8868\u5931\u8D25:",e),L.error("\u52A0\u8F7D\u5546\u54C1\u5217\u8868\u5931\u8D25\uFF1A"+(e.message||"\u672A\u77E5\u9519\u8BEF"))}finally{I.value=!1}};return{loading:I,categoryLoading:t,products:P,categories:M,categoryTreeData:d,selectedProducts:h,selectedCategoryKeys:S,searchParams:l,pagination:c,columns:w,rowSelection:k,loadProducts:g,handlePageChange:(e,a)=>{c.current=e,c.pageSize=a,g()},handlePageSizeChange:(e,a)=>{c.current=1,c.pageSize=a,g()},handleCategorySelect:A,handleSearch:()=>{c.current=1,g()},handleReset:()=>{l.searchText="",l.pricingType=void 0,c.current=1,g()},getPricingTypeName:e=>({NORMAL:"\u666E\u901A",WEIGHT:"\u79F0\u91CD",PIECE:"\u8BA1\u4EF6",VARIABLE:"\u53D8\u4EF7"})[e]||e,getPricingTypeColor:e=>({NORMAL:"blue",WEIGHT:"orange",PIECE:"green",VARIABLE:"purple"})[e]||"default",formatAmount:e=>e?parseFloat(e).toFixed(2):"0.00",getStockClass:e=>{const a=parseFloat(e)||0;return a<=0?"stock-danger":a<=10?"stock-warning":"stock-normal"},handleCancel:()=>{o("update:visible",!1)},handleConfirm:()=>{if(h.value.length===0){L.warning("\u8BF7\u9009\u62E9\u5546\u54C1");return}o("select",h.value)}}}},re={class:"product-selector-content"},ce={class:"category-tree-content"},ie={class:"category-title"},de={class:"search-area"},ge={class:"product-list"},pe={key:0,class:"product-info"},ue={class:"product-name"},_e={class:"product-details"},ye={class:"product-code"},he={key:0,class:"product-spec"},me={key:3,class:"price-text"},fe={class:"pagination-container"};function Ce(y,o,I,t,P,M){const d=W,h=q,S=H,l=J,c=j,w=D,k=X,z=Y,A=Z,g=$,N=ee,B=te,O=oe,G=ae;return C(),R(G,{visible:I.visible,title:"\u9009\u62E9\u5546\u54C1",width:1200,maskClosable:!1,onCancel:t.handleCancel},{footer:n(()=>[s(d,{onClick:t.handleCancel},{default:n(()=>o[5]||(o[5]=[u("\u53D6\u6D88")])),_:1,__:[5]},8,["onClick"]),s(d,{type:"primary",disabled:t.selectedProducts.length===0,onClick:t.handleConfirm},{default:n(()=>[u(" \u786E\u5B9A\u9009\u62E9 ("+_(t.selectedProducts.length)+") ",1)]),_:1},8,["disabled","onClick"])]),default:n(()=>[p("div",re,[s(g,{gutter:16,style:{height:"600px"}},{default:n(()=>[s(l,{span:6,class:"category-tree-container"},{default:n(()=>[o[6]||(o[6]=p("div",{class:"category-tree-header"},[p("h4",{class:"tree-title"},"\u5546\u54C1\u5206\u7C7B")],-1)),p("div",ce,[s(S,{spinning:t.categoryLoading,size:"small"},{default:n(()=>[s(h,{selectedKeys:t.selectedCategoryKeys,"onUpdate:selectedKeys":o[0]||(o[0]=r=>t.selectedCategoryKeys=r),"tree-data":t.categoryTreeData,"field-names":{children:"children",title:"title",key:"key"},"show-line":!0,"show-icon":!1,"default-expand-all":!0,class:"product-category-tree",onSelect:t.handleCategorySelect},{title:n(({title:r})=>[p("span",ie,_(r),1)]),_:1},8,["selectedKeys","tree-data","onSelect"])]),_:1},8,["spinning"])])]),_:1,__:[6]}),s(l,{span:18,class:"product-content-container"},{default:n(()=>[p("div",de,[s(g,{gutter:16},{default:n(()=>[s(l,{span:10},{default:n(()=>[s(w,{value:t.searchParams.searchText,"onUpdate:value":o[1]||(o[1]=r=>t.searchParams.searchText=r),placeholder:"\u5546\u54C1\u540D\u79F0\u3001\u7F16\u7801",allowClear:"",onPressEnter:t.loadProducts},{prefix:n(()=>[s(c,{iconClass:"icon-opt-search"})]),_:1},8,["value","onPressEnter"])]),_:1}),s(l,{span:8},{default:n(()=>[s(z,{value:t.searchParams.pricingType,"onUpdate:value":o[2]||(o[2]=r=>t.searchParams.pricingType=r),placeholder:"\u8BA1\u4EF7\u7C7B\u578B",allowClear:"",onChange:t.loadProducts},{default:n(()=>[s(k,{value:"NORMAL"},{default:n(()=>o[7]||(o[7]=[u("\u666E\u901A")])),_:1,__:[7]}),s(k,{value:"WEIGHT"},{default:n(()=>o[8]||(o[8]=[u("\u79F0\u91CD")])),_:1,__:[8]}),s(k,{value:"PIECE"},{default:n(()=>o[9]||(o[9]=[u("\u8BA1\u4EF6")])),_:1,__:[9]}),s(k,{value:"VARIABLE"},{default:n(()=>o[10]||(o[10]=[u("\u53D8\u4EF7")])),_:1,__:[10]})]),_:1},8,["value","onChange"])]),_:1}),s(l,{span:6},{default:n(()=>[s(A,null,{default:n(()=>[s(d,{type:"primary",onClick:t.handleSearch},{default:n(()=>o[11]||(o[11]=[u("\u641C\u7D22")])),_:1,__:[11]},8,["onClick"]),s(d,{onClick:t.handleReset},{default:n(()=>o[12]||(o[12]=[u("\u91CD\u7F6E")])),_:1,__:[12]},8,["onClick"])]),_:1})]),_:1})]),_:1})]),p("div",ge,[s(B,{columns:t.columns,"data-source":t.products,loading:t.loading,pagination:!1,"row-selection":t.rowSelection,"row-key":r=>r.productId,scroll:{y:400},size:"small",bordered:""},{bodyCell:n(({column:r,record:i})=>[r.key==="productInfo"?(C(),T("div",pe,[p("div",ue,_(i.productName),1),p("div",_e,[p("span",ye,_(i.productCode),1),i.specification?(C(),T("span",he,_(i.specification),1)):v("",!0)])])):v("",!0),r.key==="categoryName"?(C(),R(N,{key:1,color:"blue"},{default:n(()=>[u(_(i.categoryName),1)]),_:2},1024)):v("",!0),r.key==="pricingType"?(C(),R(N,{key:2,color:t.getPricingTypeColor(i.pricingType)},{default:n(()=>[u(_(t.getPricingTypeName(i.pricingType)),1)]),_:2},1032,["color"])):v("",!0),r.key==="retailPrice"?(C(),T("span",me,"\xA5"+_(t.formatAmount(i.retailPrice)),1)):v("",!0),r.key==="stockQuantity"?(C(),T("span",{key:4,class:F(t.getStockClass(i.stockQuantity))},_(i.stockQuantity||0)+" "+_(i.unit),3)):v("",!0)]),_:1},8,["columns","data-source","loading","row-selection","row-key"]),p("div",fe,[s(O,{current:t.pagination.current,"onUpdate:current":o[3]||(o[3]=r=>t.pagination.current=r),"page-size":t.pagination.pageSize,"onUpdate:pageSize":o[4]||(o[4]=r=>t.pagination.pageSize=r),total:t.pagination.total,"show-size-changer":t.pagination.showSizeChanger,"show-quick-jumper":t.pagination.showQuickJumper,"show-total":t.pagination.showTotal,"page-size-options":["10","20","50","100"],size:"small",onChange:t.handlePageChange,onShowSizeChange:t.handlePageSizeChange},null,8,["current","page-size","total","show-size-changer","show-quick-jumper","show-total","onChange","onShowSizeChange"])])])]),_:1})]),_:1})])]),_:1},8,["visible","onCancel"])}const xe=U(se,[["render",Ce],["__scopeId","data-v-155bc2a7"]]);export{xe as default};
