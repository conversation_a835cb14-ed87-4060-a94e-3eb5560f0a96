import{R as r,_ as H,r as m,a as M,c as x,b as d,d as s,w as o,at as _,ac as A,g as R,aT as k,a5 as W,h as P,m as f,I as U,B as F,aU as L,j as V,T as K,a3 as j}from"./index-18a1ea24.js";import{P as q,a as G}from"./performance-monitor-659a8228.js";import"./constants-2fa70699.js";class B{static createApiWrapper(e,t={}){const{context:u="Member API\u8C03\u7528",showMessage:g=!0,showNotification:i=!1,retryOptions:c={maxRetries:2,retryDelay:1e3}}=t,p=G.measureApiCall(u,e);return q.wrapApiCall(p,{showMessage:g,showNotification:i,context:u,retryOptions:c})}static async getMemberByCardNo(e){const t=()=>r.get("/erp/member/getByCardNo",{cardNo:e});return this.createApiWrapper(t,{context:"\u6839\u636E\u5361\u53F7\u67E5\u8BE2\u4F1A\u5458",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberByPhone(e){const t=()=>r.get("/erp/member/getByPhone",{phone:e});return this.createApiWrapper(t,{context:"\u6839\u636E\u624B\u673A\u53F7\u67E5\u8BE2\u4F1A\u5458",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberByIdCard(e){const t=()=>r.get("/erp/member/getByIdCard",{idCard:e});return this.createApiWrapper(t,{context:"\u6839\u636E\u8EAB\u4EFD\u8BC1\u67E5\u8BE2\u4F1A\u5458",showMessage:!0,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberDetail(e){const t=()=>r.get("/erp/member/detail",{memberId:e});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u4F1A\u5458\u8BE6\u7EC6\u4FE1\u606F",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async searchMembers(e={}){const t=()=>r.get("/erp/member/search",{...e,limit:e.limit||20});return this.createApiWrapper(t,{context:"\u641C\u7D22\u4F1A\u5458",showMessage:!1,retryOptions:{maxRetries:1,retryDelay:300}})()}static async validateMemberPassword(e){const t=()=>r.post("/erp/member/validatePassword",e);return this.createApiWrapper(t,{context:"\u9A8C\u8BC1\u4F1A\u5458\u5BC6\u7801",showMessage:!1,retryOptions:{maxRetries:0}})()}static async calculateMemberDiscount(e){const t=()=>r.post("/erp/member/calculateDiscount",e);return this.createApiWrapper(t,{context:"\u8BA1\u7B97\u4F1A\u5458\u6298\u6263",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:300}})()}static async calculatePointsDeduction(e){const t=()=>r.post("/erp/member/calculatePointsDeduction",e);return this.createApiWrapper(t,{context:"\u8BA1\u7B97\u79EF\u5206\u62B5\u6263",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:300}})()}static async getMemberPoints(e){const t=()=>r.get("/erp/member/points",{memberId:e});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u4F1A\u5458\u79EF\u5206",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async addMemberPoints(e){const t=()=>r.post("/erp/member/addPoints",{...e,operateTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u589E\u52A0\u4F1A\u5458\u79EF\u5206",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async deductMemberPoints(e){const t=()=>r.post("/erp/member/deductPoints",{...e,operateTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u6263\u51CF\u4F1A\u5458\u79EF\u5206",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberPointsHistory(e={}){const t=()=>r.get("/erp/member/pointsHistory",{...e,pageNo:e.pageNo||1,pageSize:e.pageSize||20});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u79EF\u5206\u660E\u7EC6",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberBalance(e){const t=()=>r.get("/erp/member/balance",{memberId:e});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u4F1A\u5458\u4F59\u989D",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async rechargeMemberBalance(e){const t=()=>r.post("/erp/member/recharge",{...e,rechargeTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u4F1A\u5458\u4F59\u989D\u5145\u503C",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async deductMemberBalance(e){const t=()=>r.post("/erp/member/deductBalance",{...e,operateTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u6263\u51CF\u4F1A\u5458\u4F59\u989D",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberBalanceHistory(e={}){const t=()=>r.get("/erp/member/balanceHistory",{...e,pageNo:e.pageNo||1,pageSize:e.pageSize||20});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u4F59\u989D\u660E\u7EC6",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async upgradeMemberLevel(e){const t=()=>r.post("/erp/member/upgradeLevel",{...e,upgradeTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u5347\u7EA7\u4F1A\u5458\u7B49\u7EA7",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberLevelRules(){const e=()=>r.get("/erp/member/levelRules");return this.createApiWrapper(e,{context:"\u83B7\u53D6\u4F1A\u5458\u7B49\u7EA7\u89C4\u5219",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async getMemberConsumptionHistory(e={}){const t=()=>r.get("/erp/member/consumptionHistory",{...e,pageNo:e.pageNo||1,pageSize:e.pageSize||20});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u6D88\u8D39\u8BB0\u5F55",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async getMemberStatistics(e){const t=()=>r.get("/erp/member/statistics",{memberId:e});return this.createApiWrapper(t,{context:"\u83B7\u53D6\u4F1A\u5458\u7EDF\u8BA1",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async freezeMember(e){const t=()=>r.post("/erp/member/freeze",{...e,freezeTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u51BB\u7ED3\u4F1A\u5458\u8D26\u6237",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async unfreezeMember(e){const t=()=>r.post("/erp/member/unfreeze",{...e,unfreezeTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u89E3\u51BB\u4F1A\u5458\u8D26\u6237",showMessage:!0,showNotification:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async getMemberCoupons(e={}){const t=()=>r.get("/erp/member/coupons",e);return this.createApiWrapper(t,{context:"\u83B7\u53D6\u4F1A\u5458\u4F18\u60E0\u5238",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:500}})()}static async issueCouponToMember(e){const t=()=>r.post("/erp/member/issueCoupon",{...e,issueTime:new Date().toISOString()});return this.createApiWrapper(t,{context:"\u53D1\u653E\u4F18\u60E0\u5238",showMessage:!0,retryOptions:{maxRetries:1,retryDelay:1e3}})()}}const J={class:"member-search"},Q={class:"search-methods"},X={class:"card-input"},Y={class:"phone-input"},Z={key:0,class:"no-member-found"},$={key:1,class:"error-message"},ee=Object.assign({name:"MemberSearch"},{__name:"MemberSearch",emits:["memberFound","searchError"],setup(N,{expose:e,emit:t}){const u=t,g=m("card"),i=m(""),c=m(""),p=m(!1),h=m(!1),l=m(""),v=async()=>{if(!i.value.trim()){f.warning("\u8BF7\u8F93\u5165\u4F1A\u5458\u5361\u53F7");return}await b("card",i.value.trim())},w=async()=>{if(!c.value.trim()){f.warning("\u8BF7\u8F93\u5165\u624B\u673A\u53F7");return}await b("phone",c.value.trim())},b=async(C,a)=>{try{p.value=!0,h.value=!0,l.value="";let n;C==="card"?n=await B.getMemberByCardNo(a):n=await B.getMemberByPhone(a),n?(u("memberFound",n),i.value="",c.value="",h.value=!1):f.warning("\u672A\u627E\u5230\u4F1A\u5458\u4FE1\u606F")}catch(n){console.error("\u641C\u7D22\u4F1A\u5458\u5931\u8D25:",n),l.value=n.message||"\u641C\u7D22\u4F1A\u5458\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5",u("searchError",n)}finally{p.value=!1}},I=()=>{l.value=""};return e({resetSearch:()=>{i.value="",c.value="",h.value=!1,l.value="",p.value=!1},searchMember:b}),(C,a)=>{const n=U,D=F,O=L,S=V,z=K,T=W,E=j;return M(),x("div",J,[d("div",Q,[s(z,{activeKey:g.value,"onUpdate:activeKey":a[2]||(a[2]=y=>g.value=y),size:"small",class:"search-tabs"},{default:o(()=>[s(S,{key:"card",tab:"\u4F1A\u5458\u5361"},{default:o(()=>[d("div",X,[s(O,{value:i.value,"onUpdate:value":a[0]||(a[0]=y=>i.value=y),placeholder:"\u8BF7\u8F93\u5165\u4F1A\u5458\u5361\u53F7\u6216\u626B\u63CF\u4F1A\u5458\u5361",size:"large",onSearch:v,onPressEnter:v,loading:p.value,allowClear:"",class:"card-search-input"},{prefix:o(()=>[s(n,{iconClass:"icon-card"})]),enterButton:o(()=>[s(D,{type:"primary"},{icon:o(()=>[s(_(A))]),default:o(()=>[a[3]||(a[3]=R(" \u67E5\u8BE2 "))]),_:1,__:[3]})]),_:1},8,["value","loading"])])]),_:1}),s(S,{key:"phone",tab:"\u624B\u673A\u53F7"},{default:o(()=>[d("div",Y,[s(O,{value:c.value,"onUpdate:value":a[1]||(a[1]=y=>c.value=y),placeholder:"\u8BF7\u8F93\u5165\u4F1A\u5458\u624B\u673A\u53F7",size:"large",onSearch:w,onPressEnter:w,loading:p.value,allowClear:"",class:"phone-search-input"},{prefix:o(()=>[s(_(k))]),enterButton:o(()=>[s(D,{type:"primary"},{icon:o(()=>[s(_(A))]),default:o(()=>[a[4]||(a[4]=R(" \u67E5\u8BE2 "))]),_:1,__:[4]})]),_:1},8,["value","loading"])])]),_:1})]),_:1},8,["activeKey"])]),h.value&&!p.value?(M(),x("div",Z,[s(T,{description:"\u672A\u627E\u5230\u4F1A\u5458\u4FE1\u606F",image:_(W).PRESENTED_IMAGE_SIMPLE},{default:o(()=>a[5]||(a[5]=[d("p",{class:"search-tip"},"\u8BF7\u68C0\u67E5\u4F1A\u5458\u5361\u53F7\u6216\u624B\u673A\u53F7\u662F\u5426\u6B63\u786E",-1)])),_:1,__:[5]},8,["image"])])):P("",!0),l.value?(M(),x("div",$,[s(E,{message:l.value,type:"error","show-icon":"",closable:"",onClose:I},null,8,["message"])])):P("",!0)])}}}),oe=H(ee,[["__scopeId","data-v-c14730c4"]]);export{oe as default};
