import{_ as J,r as g,s as z,o as q,a as l,f as T,w as o,b as v,d as a,c as r,F as h,e as K,g as y,t as i,h as d,a2 as Q,m as B,a6 as X,u as Z,W as $,J as tt,B as et,n as at,H as ot,a0 as nt,a4 as st,v as lt,G as it,U as rt,i as ct,I as ut,a5 as dt,M as _t}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{_ as O}from"./SupplierSelector-e8033f79.js";/* empty css              */import{I as pt}from"./InventoryApi-271c380c.js";import{P as gt}from"./productCategoryApi-39e417fd.js";import"./SupplierApi-6b9315dd.js";const mt={name:"InventoryStatisticsModal",components:{SupplierSelector:O},props:{visible:{type:Boolean,default:!1}},emits:["update:visible"],setup(D,{emit:c}){const C=g(!1),t=g(!1),w=g(!1),L=g([]),N=g([]),u=z({supplierId:void 0,categoryId:void 0,startDate:void 0,endDate:void 0}),b=z({totalProducts:0,totalValue:0,warningProducts:0,outOfStockProducts:0}),I=g([]),V=[{title:"\u5546\u54C1\u5206\u7C7B",key:"categoryName",width:120},{title:"\u5546\u54C1\u6570\u91CF",dataIndex:"productCount",width:80,align:"center"},{title:"\u5E93\u5B58\u4EF7\u503C",key:"totalValue",width:100,align:"right"},{title:"\u9884\u8B66\u6570\u91CF",key:"warningCount",width:80,align:"center"}],k=g([]),E=[{title:"\u4F9B\u5E94\u5546",key:"supplierName",width:150},{title:"\u5546\u54C1\u6570\u91CF",dataIndex:"productCount",width:80,align:"center"},{title:"\u5E93\u5B58\u4EF7\u503C",key:"totalValue",width:100,align:"right"},{title:"\u9884\u8B66\u6570\u91CF",key:"warningCount",width:80,align:"center"}],x=g([]),p=[{title:"\u5546\u54C1\u4FE1\u606F",key:"productInfo",width:200},{title:"\u5468\u8F6C\u7387",key:"turnoverRate",width:100,align:"center"},{title:"\u5E73\u5747\u5E93\u5B58",key:"avgStock",width:100,align:"right"},{title:"\u9500\u552E\u603B\u91CF",key:"totalSales",width:100,align:"right"},{title:"\u5E93\u5B58\u5929\u6570",dataIndex:"stockDays",width:80,align:"center"}],f=async()=>{try{const e=await gt.findList({status:"Y"});e.success&&(N.value=e.data||[])}catch(e){console.error("\u52A0\u8F7D\u5546\u54C1\u5206\u7C7B\u5931\u8D25:",e)}},_=async()=>{await Promise.all([M(),R(),S(),A()])},M=async()=>{try{const e=await pt.inventoryValue(u);e.success&&Object.assign(b,e.data)}catch(e){console.error("\u52A0\u8F7D\u603B\u4F53\u7EDF\u8BA1\u5931\u8D25:",e)}},R=async()=>{C.value=!0;try{I.value=[{categoryName:"\u98DF\u54C1",productCount:25,totalValue:15e3,warningCount:3},{categoryName:"\u996E\u6599",productCount:18,totalValue:8500,warningCount:1},{categoryName:"\u65E5\u7528\u54C1",productCount:32,totalValue:12e3,warningCount:5}]}catch(e){console.error("\u52A0\u8F7D\u5206\u7C7B\u7EDF\u8BA1\u5931\u8D25:",e)}finally{C.value=!1}},S=async()=>{t.value=!0;try{k.value=[{supplierName:"\u4F9B\u5E94\u5546A",businessMode:"PURCHASE_SALE",businessModeName:"\u8D2D\u9500",productCount:20,totalValue:18e3,warningCount:2},{supplierName:"\u4F9B\u5E94\u5546B",businessMode:"CONSIGNMENT",businessModeName:"\u4EE3\u9500",productCount:15,totalValue:12e3,warningCount:3}]}catch(e){console.error("\u52A0\u8F7D\u4F9B\u5E94\u5546\u7EDF\u8BA1\u5931\u8D25:",e)}finally{t.value=!1}},A=async()=>{w.value=!0;try{x.value=[{productName:"\u5546\u54C1A",productCode:"P001",pricingType:"NORMAL",unit:"\u4E2A",turnoverRate:12.5,avgStock:100,totalSales:1250,stockDays:30},{productName:"\u5546\u54C1B",productCode:"P002",pricingType:"WEIGHT",unit:"kg",turnoverRate:8.2,avgStock:50,totalSales:410,stockDays:45}]}catch(e){console.error("\u52A0\u8F7D\u5468\u8F6C\u7387\u7EDF\u8BA1\u5931\u8D25:",e)}finally{w.value=!1}},F=e=>{e&&e.length===2?(u.startDate=e[0],u.endDate=e[1]):(u.startDate=void 0,u.endDate=void 0),_()},P=()=>{try{B.success("\u5BFC\u51FA\u6210\u529F")}catch(e){B.error("\u5BFC\u51FA\u5931\u8D25\uFF1A"+(e.message||"\u672A\u77E5\u9519\u8BEF"))}},s=e=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"})[e]||"default",n=(e,m)=>{switch(e){case"WEIGHT":return"kg";case"PIECE":return"\u4EF6";case"NORMAL":case"VARIABLE":default:return m||"\u4E2A"}},U=(e,m)=>{if(!e)return"0";const j=m==="WEIGHT"?3:0;return parseFloat(e).toFixed(j)},G=e=>e?parseFloat(e).toFixed(2):"0.00",H=e=>e?parseFloat(e).toFixed(1):"0.0",Y=e=>{const m=parseFloat(e)||0;return m>=10?"turnover-high":m>=5?"turnover-medium":"turnover-low"},W=()=>{c("update:visible",!1)};return q(()=>{f(),D.visible&&_()}),{categoryLoading:C,supplierLoading:t,turnoverLoading:w,dateRange:L,categories:N,statisticsForm:u,overallStats:b,categoryStats:I,categoryColumns:V,supplierStats:k,supplierColumns:E,turnoverStats:x,turnoverColumns:p,loadStatistics:_,onDateRangeChange:F,exportStatistics:P,getBusinessModeColor:s,getStockUnit:n,formatStock:U,formatAmount:G,formatTurnoverRate:H,getTurnoverRateClass:Y,handleCancel:W}}},yt={class:"inventory-statistics-content"},ft={key:1,class:"total-value"},vt={key:0,class:"warning-count"},Ct={key:1},kt={key:0},St={class:"supplier-name"},ht={key:1,class:"total-value"},wt={key:0,class:"warning-count"},Nt={key:1},bt={class:"trend-chart-container"},It={class:"chart-placeholder"},xt={key:0,class:"product-info"},Mt={class:"product-name"},Rt={class:"product-code"};function Tt(D,c,C,t,w,L){const N=X,u=Z,b=O,I=$,V=tt,k=et,E=at,x=ot,p=nt,f=st,_=lt,M=it,R=rt,S=ct,A=ut,F=dt,P=_t;return l(),T(P,{visible:C.visible,title:"\u5E93\u5B58\u7EDF\u8BA1\u5206\u6790",width:1200,footer:null,onCancel:t.handleCancel},{default:o(()=>[v("div",yt,[a(p,{title:"\u7EDF\u8BA1\u6761\u4EF6",size:"small",style:{"margin-bottom":"16px"}},{default:o(()=>[a(x,{layout:"inline",model:t.statisticsForm},{default:o(()=>[a(u,{label:"\u7EDF\u8BA1\u65F6\u95F4"},{default:o(()=>[a(N,{value:t.dateRange,"onUpdate:value":c[0]||(c[0]=s=>t.dateRange=s),style:{width:"240px"},placeholder:["\u5F00\u59CB\u65F6\u95F4","\u7ED3\u675F\u65F6\u95F4"],format:"YYYY-MM-DD",onChange:t.onDateRangeChange},null,8,["value","onChange"])]),_:1}),a(u,{label:"\u4F9B\u5E94\u5546"},{default:o(()=>[a(b,{value:t.statisticsForm.supplierId,"onUpdate:value":c[1]||(c[1]=s=>t.statisticsForm.supplierId=s),filter:{businessMode:["PURCHASE_SALE","CONSIGNMENT"]},placeholder:"\u5168\u90E8\u4F9B\u5E94\u5546",allowClear:"",onChange:t.loadStatistics},null,8,["value","onChange"])]),_:1}),a(u,{label:"\u5546\u54C1\u5206\u7C7B"},{default:o(()=>[a(V,{value:t.statisticsForm.categoryId,"onUpdate:value":c[2]||(c[2]=s=>t.statisticsForm.categoryId=s),placeholder:"\u5168\u90E8\u5206\u7C7B",allowClear:"",style:{width:"150px"},onChange:t.loadStatistics},{default:o(()=>[(l(!0),r(h,null,K(t.categories,s=>(l(),T(I,{key:s.categoryId,value:s.categoryId},{default:o(()=>[y(i(s.categoryName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange"])]),_:1}),a(u,null,{default:o(()=>[a(E,null,{default:o(()=>[a(k,{type:"primary",onClick:t.loadStatistics},{default:o(()=>c[3]||(c[3]=[y("\u5237\u65B0\u7EDF\u8BA1")])),_:1,__:[3]},8,["onClick"]),a(k,{onClick:t.exportStatistics},{default:o(()=>c[4]||(c[4]=[y("\u5BFC\u51FA\u62A5\u8868")])),_:1,__:[4]},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(p,{title:"\u603B\u4F53\u7EDF\u8BA1",size:"small",style:{"margin-bottom":"16px"}},{default:o(()=>[a(M,{gutter:16},{default:o(()=>[a(_,{span:6},{default:o(()=>[a(f,{title:"\u5546\u54C1\u603B\u6570",value:t.overallStats.totalProducts,"value-style":{color:"#1890ff"},suffix:"\u79CD"},null,8,["value"])]),_:1}),a(_,{span:6},{default:o(()=>[a(f,{title:"\u5E93\u5B58\u603B\u4EF7\u503C",value:t.overallStats.totalValue,"value-style":{color:"#52c41a"},prefix:"\xA5",precision:2},null,8,["value"])]),_:1}),a(_,{span:6},{default:o(()=>[a(f,{title:"\u9884\u8B66\u5546\u54C1",value:t.overallStats.warningProducts,"value-style":{color:"#faad14"},suffix:"\u79CD"},null,8,["value"])]),_:1}),a(_,{span:6},{default:o(()=>[a(f,{title:"\u7F3A\u8D27\u5546\u54C1",value:t.overallStats.outOfStockProducts,"value-style":{color:"#ff4d4f"},suffix:"\u79CD"},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(M,{gutter:16},{default:o(()=>[a(_,{span:12},{default:o(()=>[a(p,{title:"\u6309\u5206\u7C7B\u7EDF\u8BA1",size:"small",style:{"margin-bottom":"16px"}},{default:o(()=>[a(S,{columns:t.categoryColumns,"data-source":t.categoryStats,pagination:!1,size:"small",loading:t.categoryLoading,scroll:{y:300}},{bodyCell:o(({column:s,record:n})=>[s.key==="categoryName"?(l(),T(R,{key:0,color:"blue"},{default:o(()=>[y(i(n.categoryName),1)]),_:2},1024)):d("",!0),s.key==="totalValue"?(l(),r("span",ft,"\xA5"+i(t.formatAmount(n.totalValue)),1)):d("",!0),s.key==="warningCount"?(l(),r(h,{key:2},[n.warningCount>0?(l(),r("span",vt,i(n.warningCount),1)):(l(),r("span",Ct,"0"))],64)):d("",!0)]),_:1},8,["columns","data-source","loading"])]),_:1})]),_:1}),a(_,{span:12},{default:o(()=>[a(p,{title:"\u6309\u4F9B\u5E94\u5546\u7EDF\u8BA1",size:"small",style:{"margin-bottom":"16px"}},{default:o(()=>[a(S,{columns:t.supplierColumns,"data-source":t.supplierStats,pagination:!1,size:"small",loading:t.supplierLoading,scroll:{y:300}},{bodyCell:o(({column:s,record:n})=>[s.key==="supplierName"?(l(),r("div",kt,[v("div",St,i(n.supplierName),1),n.businessModeName?(l(),T(R,{key:0,size:"small",color:t.getBusinessModeColor(n.businessMode)},{default:o(()=>[y(i(n.businessModeName),1)]),_:2},1032,["color"])):d("",!0)])):d("",!0),s.key==="totalValue"?(l(),r("span",ht,"\xA5"+i(t.formatAmount(n.totalValue)),1)):d("",!0),s.key==="warningCount"?(l(),r(h,{key:2},[n.warningCount>0?(l(),r("span",wt,i(n.warningCount),1)):(l(),r("span",Nt,"0"))],64)):d("",!0)]),_:1},8,["columns","data-source","loading"])]),_:1})]),_:1})]),_:1}),a(p,{title:"\u5E93\u5B58\u53D8\u52A8\u8D8B\u52BF",size:"small",style:{"margin-bottom":"16px"}},{default:o(()=>[v("div",bt,[v("div",It,[a(F,{description:"\u56FE\u8868\u529F\u80FD\u5F85\u5F00\u53D1"},{image:o(()=>[a(A,{iconClass:"icon-opt-tongji",style:{"font-size":"48px",color:"#d9d9d9"}})]),_:1})])])]),_:1}),a(p,{title:"\u5E93\u5B58\u5468\u8F6C\u5206\u6790",size:"small"},{default:o(()=>[a(S,{columns:t.turnoverColumns,"data-source":t.turnoverStats,pagination:{pageSize:10},size:"small",loading:t.turnoverLoading},{bodyCell:o(({column:s,record:n})=>[s.key==="productInfo"?(l(),r("div",xt,[v("div",Mt,i(n.productName),1),v("div",Rt,i(n.productCode),1)])):d("",!0),s.key==="turnoverRate"?(l(),r("span",{key:1,class:Q(t.getTurnoverRateClass(n.turnoverRate))},i(t.formatTurnoverRate(n.turnoverRate)),3)):d("",!0),s.key==="avgStock"?(l(),r(h,{key:2},[y(i(t.formatStock(n.avgStock,n.pricingType))+" "+i(t.getStockUnit(n.pricingType,n.unit)),1)],64)):d("",!0),s.key==="totalSales"?(l(),r(h,{key:3},[y(i(t.formatStock(n.totalSales,n.pricingType))+" "+i(t.getStockUnit(n.pricingType,n.unit)),1)],64)):d("",!0)]),_:1},8,["columns","data-source","loading"])]),_:1})])]),_:1},8,["visible","onCancel"])}const Ht=J(mt,[["render",Tt],["__scopeId","data-v-684aba3f"]]);export{Ht as default};
