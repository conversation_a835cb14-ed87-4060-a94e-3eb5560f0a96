package cn.stylefeng.roses.ent.saas.modular.auth.mapper;

import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.response.TenantLinkVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户-功能包 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
public interface TenantLinkMapper extends BaseMapper<TenantLink> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    List<TenantLinkVo> customFindList(@Param("page") Page page, @Param("param")TenantLinkRequest request);

}
