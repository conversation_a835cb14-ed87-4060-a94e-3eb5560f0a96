<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.pay.mapper.GoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.pay.api.entity.Goods">
		<id column="goods_id" property="goodsId" />
		<result column="goods_name" property="goodsName" />
		<result column="goods_desc" property="goodsDesc" />
		<result column="goods_pic" property="goodsPic" />
		<result column="detail_url" property="detailUrl" />
		<result column="price" property="price" />
		<result column="original_price" property="originalPrice" />
		<result column="expiry_month" property="expiryMonth" />
		<result column="goods_status" property="goodsStatus" />
		<result column="fld_sort" property="fldSort" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		goods_id,goods_name,goods_desc,goods_pic,detail_url,price,original_price,expiry_month,goods_status,fld_sort,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.pay.pojo.response.GoodsVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		shop_goods tbl
		WHERE
		<where>
        <if test="param.goodsId != null and param.goodsId != ''">
            and tbl.goods_id like concat('%',#{param.goodsId},'%')
        </if>
        <if test="param.goodsName != null and param.goodsName != ''">
            and tbl.goods_name like concat('%',#{param.goodsName},'%')
        </if>
        <if test="param.goodsDesc != null and param.goodsDesc != ''">
            and tbl.goods_desc like concat('%',#{param.goodsDesc},'%')
        </if>
        <if test="param.goodsPic != null and param.goodsPic != ''">
            and tbl.goods_pic like concat('%',#{param.goodsPic},'%')
        </if>
        <if test="param.detailUrl != null and param.detailUrl != ''">
            and tbl.detail_url like concat('%',#{param.detailUrl},'%')
        </if>
        <if test="param.price != null and param.price != ''">
            and tbl.price like concat('%',#{param.price},'%')
        </if>
        <if test="param.originalPrice != null and param.originalPrice != ''">
            and tbl.original_price like concat('%',#{param.originalPrice},'%')
        </if>
        <if test="param.expiryMonth != null and param.expiryMonth != ''">
            and tbl.expiry_month like concat('%',#{param.expiryMonth},'%')
        </if>
        <if test="param.goodsStatus != null and param.goodsStatus != ''">
            and tbl.goods_status like concat('%',#{param.goodsStatus},'%')
        </if>
        <if test="param.fldSort != null and param.fldSort != ''">
            and tbl.fld_sort like concat('%',#{param.fldSort},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
