import{_ as Z,b2 as ee,r as i,o as ne,bG as te,bj as N,X as $,a as r,c as f,b as l,d as u,I as F,w as p,F as K,e as B,t as I,f as j,g as b,m as oe,a5 as se,S as ae,bH as le,A as ie,a7 as ce,l as ue,aO as de,U as re,M as pe}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{H as z}from"./HomeApi-75ad5066.js";const _e={class:"card"},ve={class:"card-header"},fe={class:"card-body"},me={class:"app-list"},he={key:0,style:{width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},ge=["onClick"],ye={class:"app-item-icon"},ke={class:"app-item-title"},Ie={class:"common-function"},be={class:"common-function-header"},xe={class:"common-function-list"},Ce={class:"common-function-check-list"},Le={__name:"common-functions",setup(we){const D=ee(),m=i([]),x=i(!1),h=i(!1),C=i(!1),d=i([]),c=i(""),g=i(""),v=i({}),L=i({}),w=i(!0),y=i([]),k=i([]),H={children:"children",label:"title",key:"menuId",value:"menuId"},M=()=>{C.value=!0;let n=N(y.value).map(e=>e.menuId);z.updateUserAppList({menuIdList:n}).then(e=>{e.success&&(oe.success(e.message),h.value=!1,O())}).finally(()=>{C.value=!1})},T=n=>{h.value=n},P=n=>{const{href:e}=D.resolve({path:n.menuRouter});window.open(e,"_blank")},R=()=>{let n=d.value.filter(e=>e.appId===c.value);n.length===1&&(k.value=A(n[0].menuList))},A=n=>(n.map(e=>(e.key=e.menuId,e.children&&(e.children=A(e.children)),e)),n),E=(n,e)=>{for(const o of n){if(o.menuId===e)return o;if(o.children){const a=E(o.children,e);if(a)return a}}return null},G=n=>{let e=N(v.value),o={};Object.keys(e).forEach(a=>{const s=e[a];let _=s.findIndex(U=>U===n.menuId);_>-1&&s.splice(_,1),o[a]=s}),v.value=o},O=async()=>{x.value=!0;let n=await z.getUserAppList().finally(()=>{x.value=!1});m.value=n},S=(n,e,o)=>{for(let a=0;a<e.length;a++){let s=e[a];s.title.indexOf(n)>-1&&o.push(s.key),s.children&&S(n,s.children,o)}return o};ne(async()=>{O();let n=await te();if(d.value=n.userAppInfoList,d.value.length>0){c.value=d.value[0].appId;let e=d.value.filter(o=>o.appId===c.value);e.length===1&&(k.value=A(e[0].menuList))}d.value.forEach(e=>{let o=[];m.value.forEach(a=>{let s=E(e.menuList,a.menuId);s&&o.push(s.menuId)}),v.value[e.appId]=o}),y.value=N(m.value)}),$(()=>v.value,n=>{let e=[];d.value.forEach(o=>{n[o.appId].forEach(s=>{let _=E(o.menuList,s);_&&e.push(_)})}),y.value=e},{deep:!0});const X=n=>{L.value[c.value]=n,w.value=!1};return $(()=>g.value,n=>{L.value[c.value]=S(n,k.value,[]),g.value=n,w.value=!0}),(n,e)=>{const o=se,a=ae,s=le,_=ie,U=F,q=ce,J=ue,Q=de,W=re,Y=pe;return r(),f("div",_e,[l("div",ve,[e[6]||(e[6]=l("div",{class:"card-header-title"},[l("span",null,"\u5E38\u7528\u529F\u80FD")],-1)),l("div",{class:"user-info-card-header-icon",onClick:e[0]||(e[0]=t=>h.value=!0)},[u(F,{"font-size":"34px",color:"#2a82e4","icon-class":"icon-opt-shezhi","font-weight":"bold"})])]),l("div",fe,[u(a,{spinning:x.value},{default:p(()=>[l("div",me,[m.value.length===0?(r(),f("div",he,[u(o)])):(r(!0),f(K,{key:1},B(m.value,(t,V)=>(r(),f("div",{class:"app-list-item",key:V,onClick:Ae=>P(t)},[l("div",ye,[u(F,{"font-size":"36px",color:"#2a82e4","icon-class":t.menuIcon},null,8,["icon-class"])]),l("div",ke,[l("span",null,I(t.menuName),1)])],8,ge))),128))])]),_:1},8,["spinning"])]),u(Y,{width:760,visible:h.value,"onUpdate:visible":e[4]||(e[4]=t=>h.value=t),title:"\u9009\u62E9\u5E38\u7528\u529F\u80FD",onCancel:e[5]||(e[5]=t=>T(!1)),"confirm-loading":C.value,onOk:M},{default:p(()=>[l("div",Ie,[l("div",be,[u(_,{class:"common-function-header-left",value:c.value,"onUpdate:value":e[1]||(e[1]=t=>c.value=t),onChange:R},{default:p(()=>[(r(!0),f(K,null,B(d.value,t=>(r(),j(s,{style:{"min-width":"120px","text-align":"center"},key:t.appId,value:t.appId},{default:p(()=>[b(I(t.appName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"]),u(J,{value:g.value,"onUpdate:value":e[2]||(e[2]=t=>g.value=t),class:"common-function-header-right","allow-clear":"",placeholder:"\u83DC\u5355\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09"},{suffix:p(()=>[u(q,{title:"Extra information"},{default:p(()=>[u(U,{iconClass:"icon-opt-search"})]),_:1})]),_:1},8,["value"])]),l("div",xe,[u(Q,{onExpand:X,"expanded-keys":L.value[c.value],"auto-expand-parent":w.value,checkedKeys:v.value[c.value],"onUpdate:checkedKeys":e[3]||(e[3]=t=>v.value[c.value]=t),checkable:"","field-names":H,"tree-data":k.value},{title:p(({title:t})=>[b(I(t),1)]),_:1},8,["expanded-keys","auto-expand-parent","checkedKeys","tree-data"])]),l("div",Ce,[e[7]||(e[7]=b(" \u5DF2\u9009\uFF1A ")),(r(!0),f(K,null,B(y.value,t=>(r(),j(W,{key:t.menuId,color:"processing",closable:"",onClose:V=>G(t)},{default:p(()=>[b(I(t.title),1)]),_:2},1032,["onClose"]))),128))])])]),_:1},8,["visible","confirm-loading"])])}}},Ke=Z(Le,[["__scopeId","data-v-bb990669"]]);export{Ke as default};
