package cn.stylefeng.roses.kernel.ca.server.modular.manage.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.validator.api.validators.status.StatusValue;
import cn.stylefeng.roses.kernel.validator.api.validators.unique.TableUniqueValue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单点登录客户端封装类
 *
 * <AUTHOR>
 * @since 2023/11/05 09:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SsoClientRequest extends BaseRequest {

    /**
     * 业务应用的id
     */
    @NotNull(message = "业务应用的id不能为空", groups = {edit.class, delete.class, detail.class})
    @ChineseDescription("业务应用的id")
    private Long clientId;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("应用名称")
    @TableUniqueValue(
            message = "应用名称重复，请检查clientName参数",
            groups = {add.class, edit.class},
            tableName = "ent_sso_client",
            columnName = "client_name",
            idFieldName = "client_id",
            excludeLogicDeleteItems = true)
    private String clientName;

    /**
     * 应用图标的文件id
     */
    @ChineseDescription("应用图标的文件id")
    private Long clientLogoFileId;

    /**
     * 登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面
     */
    @NotNull(message = "登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面不能为空", groups = {add.class, edit.class})
    @ChineseDescription("登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面")
    private Integer loginPageType;

    /**
     * 是否统一退出：Y-是，N-否
     */
    @NotBlank(message = "是否统一退出：Y-是，N-否不能为空", groups = {add.class, edit.class})
    @ChineseDescription("是否统一退出：Y-是，N-否")
    private String unifiedLogoutFlag;

    /**
     * 回调业务地址，单点登录到业务端时，跳转到业务端的地址
     */
    @NotBlank(message = "回调业务地址，单点登录到业务端时，跳转到业务端的地址不能为空", groups = {add.class, edit.class})
    @ChineseDescription("回调业务地址，单点登录到业务端时，跳转到业务端的地址")
    private String ssoCallbackUrl;

    /**
     * 退出地址，从认证中心退出后，通知业务端的地址
     */
    @NotBlank(message = "退出地址，从认证中心退出后，通知业务端的地址不能为空", groups = {add.class, edit.class})
    @ChineseDescription("退出地址，从认证中心退出后，通知业务端的地址")
    private String ssoLogoutUrl;

    /**
     * 应用登录的地址，针对自定义登录界面
     */
    @ChineseDescription("应用登录的地址，针对自定义登录界面")
    private String customLoginUrl;

    /**
     * 加密和解密的密钥，针对单点到业务系统的token（对称加密）
     */
    @NotBlank(message = "加密和解密的密钥，针对单点到业务系统的token（对称加密）不能为空", groups = {add.class, edit.class})
    @ChineseDescription("加密和解密的密钥，针对单点到业务系统的token（对称加密）")
    private String caTokenSecret;

    /**
     * 状态：1-启用，2-禁用
     */
    @ChineseDescription("状态：1-启用，2-禁用")
    @StatusValue(message = "状态值不正确，请检查clientStatus参数", groups = updateStatus.class)
    private Integer clientStatus;

    /**
     * 排序码
     */
    @ChineseDescription("排序码")
    private BigDecimal clientSort;

    /**
     * 应用的描述
     */
    @ChineseDescription("应用的描述")
    private String clientDescription;

    /**
     * 乐观锁
     */
    @ChineseDescription("乐观锁")
    private Long versionFlag;

    /**
     * 批量删除用的id集合
     */
    @NotNull(message = "批量删除id集合不能为空", groups = batchDelete.class)
    @ChineseDescription("批量删除用的id集合")
    private List<Long> batchDeleteIdList;

}
