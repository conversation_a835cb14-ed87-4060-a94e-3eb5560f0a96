import{r as o,o as y,X as b,a as p,c as f,d as v,f as C,h as g,l as w,w as x,a0 as k}from"./index-18a1ea24.js";/* empty css              */import{_ as V}from"./index-ba83f962.js";const B={class:"wh100"},M={__name:"index",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},readonly:{type:<PERSON><PERSON>an,default:!1}},emits:["update:value","onChange"],setup(n,{emit:s}){const d=n,u=s,e=o(""),l=o(!1);y(()=>{e.value=d.value});const m=t=>{const{name:a}=t;e.value=a,c()};b(()=>d.value,t=>{e.value=t},{deep:!0});const c=()=>{u("update:value",e.value),u("onChange",d.record)},r=()=>{l.value=!0};return(t,a)=>{const _=w,h=V;return p(),f("div",B,[v(_,{value:e.value,"onUpdate:value":a[0]||(a[0]=i=>e.value=i),disabled:n.readonly||n.disabled,allowClear:"",class:"w-full",onFocus:r,placeholder:n.placeholder},null,8,["value","disabled","placeholder"]),l.value?(p(),C(h,{key:0,visible:l.value,"onUpdate:visible":a[1]||(a[1]=i=>l.value=i),onDone:m,"need-city":!0},null,8,["visible"])):g("",!0)])}}},U={class:"guns-body guns-body-card"},S={__name:"index",setup(n){const s=o(""),d=o(!1),u=o(!1),e=o("\u8BF7\u9009\u62E9"),l=()=>{console.log(s.value)};return(m,c)=>{const r=M,t=k;return p(),f("div",U,[v(t,{title:"\u5730\u56FE\u9009\u62E9(input)",bordered:!1},{default:x(()=>[v(r,{value:s.value,"onUpdate:value":c[0]||(c[0]=a=>s.value=a),disabled:d.value,placeholder:e.value,readonly:u.value,onOnChange:l,style:{width:"300px"}},null,8,["value","disabled","placeholder","readonly"])]),_:1})])}}};export{S as default};
