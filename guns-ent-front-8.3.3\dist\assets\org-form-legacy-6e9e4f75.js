System.register(["./index-legacy-ee1db0c7.js","./index-legacy-dba03026.js","./index-legacy-94a6fc23.js","./OrgApi-legacy-c15eac58.js","./index-legacy-53580278.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./index-legacy-efb51034.js"],(function(e,l){"use strict";var a,r,t,o,u,d,m,n,s,g,f,v,i,c,p,_,b,y,x,h,N,O,w,C,U,j,L,S,F,I;return{setters:[e=>{a=e._,r=e.s,t=e.r,o=e.L,u=e.o,d=e.a,m=e.f,n=e.w,s=e.d,g=e.g,f=e.c,v=e.F,i=e.e,c=e.b,p=e.ah,_=e.t,b=e.h,y=e.l,x=e.u,h=e.v,N=e.y,O=e.z,w=e.A,C=e.W,U=e.J,j=e.$,L=e.G,S=e.H},e=>{F=e._},null,e=>{I=e.O},null,null,null,null,null,null],execute:function(){var l=document.createElement("style");l.textContent=".card-title[data-v-b14ce998]{width:100%;border-left:5px solid;border-color:var(--primary-color);padding-left:10px;margin-bottom:20px}\n",document.head.appendChild(l);const q={__name:"org-form",props:{form:Object,isUpdate:Boolean,levelNumberList:Array},setup(e){const l=e,a=r({orgName:[{required:!0,message:"请输入机构名称",type:"string",trigger:"blur"}],orgCode:[{required:!0,message:"请输入机构编码",type:"string",trigger:"blur"}],orgSort:[{required:!0,message:"请输入排序",type:"number",trigger:"blur"}],orgType:[{required:!0,message:"请选择机构类型",type:"number",trigger:"change"}],statusFlag:[{required:!0,message:"请选择机构状态",type:"number",trigger:"change"}]}),q=t(!1),T=t({}),k=t([]),z=o((()=>e=>{let a="",r=l.levelNumberList.find((l=>l.value==e.levelNumber));return r&&(a=r.name),a})),P=()=>{l.form.orgParentId&&l.form.parentOrgName?T.value.selectOrgList=[{bizId:l.form.orgParentId,name:l.form.parentOrgName}]:T.value.selectOrgList=[],q.value=!0},A=e=>{e.selectOrgList&&e.selectOrgList.length>0?(l.form.orgParentId=e.selectOrgList[0].bizId,l.form.parentOrgName=e.selectOrgList[0].name):(l.form.orgParentId="",l.form.parentOrgName="")};u((()=>{B()}));const B=async()=>{k.value=await I.organizationLevelList()};return(l,r)=>{const t=y,o=x,u=h,I=N,B=O,E=w,D=C,G=U,H=j,J=L,R=F,W=S;return d(),m(W,{ref:"formRef",model:e.form,rules:a,layout:"vertical"},{default:n((()=>[s(J,{gutter:20},{default:n((()=>[s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"机构名称:",name:"orgName"},{default:n((()=>[s(t,{value:e.form.orgName,"onUpdate:value":r[0]||(r[0]=l=>e.form.orgName=l),"allow-clear":"",placeholder:"请输入机构名称"},null,8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"机构编码:",name:"orgCode"},{default:n((()=>[s(t,{value:e.form.orgCode,"onUpdate:value":r[1]||(r[1]=l=>e.form.orgCode=l),"allow-clear":"",placeholder:"请输入机构编码"},null,8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"上级机构：",name:"parentOrgName"},{default:n((()=>[s(t,{value:e.form.parentOrgName,"onUpdate:value":r[2]||(r[2]=l=>e.form.parentOrgName=l),"allow-clear":"",placeholder:"请选择上级机构",onFocus:P},null,8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"排序:",name:"orgSort"},{default:n((()=>[s(I,{value:e.form.orgSort,"onUpdate:value":r[3]||(r[3]=l=>e.form.orgSort=l),min:0,style:{width:"100%"},placeholder:"请输入排序","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"机构类型:",name:"orgType"},{default:n((()=>[s(E,{value:e.form.orgType,"onUpdate:value":r[4]||(r[4]=l=>e.form.orgType=l)},{default:n((()=>[s(B,{value:1},{default:n((()=>r[11]||(r[11]=[g("公司")]))),_:1,__:[11]}),s(B,{value:2},{default:n((()=>r[12]||(r[12]=[g("部门")]))),_:1,__:[12]})])),_:1},8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"层级:",name:"levelCode"},{default:n((()=>[s(G,{value:e.form.levelCode,"onUpdate:value":r[5]||(r[5]=l=>e.form.levelCode=l),style:{width:"100%"},placeholder:"请选择机构层级"},{default:n((()=>[(d(!0),f(v,null,i(k.value,(e=>(d(),m(D,{value:e.levelCode,key:e.levelCode},{default:n((()=>[c("span",{style:p({color:e.levelColor})},_(e.levelName)+"("+_(z.value(e))+")",5)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"机构状态:",name:"statusFlag"},{default:n((()=>[s(E,{value:e.form.statusFlag,"onUpdate:value":r[6]||(r[6]=l=>e.form.statusFlag=l)},{default:n((()=>[s(B,{value:1},{default:n((()=>r[13]||(r[13]=[g("启用")]))),_:1,__:[13]}),s(B,{value:2},{default:n((()=>r[14]||(r[14]=[g("禁用")]))),_:1,__:[14]})])),_:1},8,["value"])])),_:1})])),_:1}),s(u,{span:24},{default:n((()=>r[15]||(r[15]=[c("div",{class:"card-title"},"其他信息",-1)]))),_:1,__:[15]}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"机构简称:",name:"orgShortName"},{default:n((()=>[s(t,{value:e.form.orgShortName,"onUpdate:value":r[7]||(r[7]=l=>e.form.orgShortName=l),"allow-clear":"",placeholder:"请输入机构简称"},null,8,["value"])])),_:1})])),_:1}),s(u,{xs:24,sm:24,md:12},{default:n((()=>[s(o,{label:"机构税号:",name:"taxNo"},{default:n((()=>[s(t,{value:e.form.taxNo,"onUpdate:value":r[8]||(r[8]=l=>e.form.taxNo=l),"allow-clear":"",placeholder:"请输入机构税号"},null,8,["value"])])),_:1})])),_:1}),s(u,{span:24},{default:n((()=>[s(o,{label:"备注"},{default:n((()=>[s(H,{value:e.form.remark,"onUpdate:value":r[9]||(r[9]=l=>e.form.remark=l),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1}),q.value?(d(),m(R,{key:0,visible:q.value,"onUpdate:visible":r[10]||(r[10]=e=>q.value=e),title:"上级机构选择",data:T.value,showTab:["dept"],onDone:A},null,8,["visible","data"])):b("",!0)])),_:1},8,["model","rules"])}}};e("default",a(q,[["__scopeId","data-v-b14ce998"]]))}}}));
