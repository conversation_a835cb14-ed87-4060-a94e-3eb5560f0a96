System.register(["./index-legacy-ee1db0c7.js","./index-legacy-b540c599.js"],(function(e,t){"use strict";var a,n,i,o,r,d,l,s,c,h,p,u,f,v,g,y,m,w,x,C,k,b,E,S,A,L;return{setters:[e=>{a=e._,n=e.r,i=e.L,o=e.o,r=e.a,d=e.c,l=e.at,s=e.b,c=e.t,h=e.f,p=e.P,u=e.h,f=e.d,v=e.w,g=e.I,y=e.aR,m=e.aM,w=e.F,x=e.e,C=e.aS,k=e.m,b=e.l,E=e.n,S=e.aO,A=e.a5,L=e.S},null],execute:function(){var t=document.createElement("style");t.textContent=".left-header[data-v-f00d3699]{height:30px;line-height:30px;display:flex;justify-content:space-between;align-items:center;color:#505050;font-size:14px;font-weight:400;margin-bottom:16px}.left-header .left-header-title[data-v-f00d3699]{color:#60666b;font-size:14px;font-weight:400}.left-header .header-add[data-v-f00d3699]{font-size:14px;cursor:pointer;padding:5px}.left-header .header-add[data-v-f00d3699]:hover{background:#e9f3f8}.search[data-v-f00d3699]{height:36px;border-radius:5px;margin-bottom:16px}.search-input[data-v-f00d3699]{border-radius:4px}.tree-content[data-v-f00d3699]{width:100%;height:calc(100% - 90px);overflow:hidden}[data-v-f00d3699] .ant-spin-container{height:100%}.left-tree[data-v-f00d3699]{height:calc(100% - 10px)!important;overflow-y:auto!important;overflow-x:hidden!important}[data-v-f00d3699]::-webkit-scrollbar{width:12px!important}.tree-edit[data-v-f00d3699],.not-tree-edit[data-v-f00d3699]{width:100%;display:inline-block;position:relative}.tree-edit .edit-title[data-v-f00d3699],.not-tree-edit .edit-title[data-v-f00d3699]{width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tree-edit .edit-icon[data-v-f00d3699],.not-tree-edit .edit-icon[data-v-f00d3699]{display:none;width:40px;position:absolute;right:10px}.tree-edit:hover .edit-icon[data-v-f00d3699],.not-tree-edit:hover .edit-icon[data-v-f00d3699]{display:inline-block}.tree-edit:hover .edit-title[data-v-f00d3699],.not-tree-edit:hover .edit-title[data-v-f00d3699]{width:calc(100% - 50px)}.not-tree-edit:hover .edit-title[data-v-f00d3699]{width:100%}[data-v-f00d3699] .ant-tree .ant-tree-node-content-wrapper{height:38px!important;line-height:38px!important;display:inherit!important}[data-v-f00d3699] .ant-tree-switcher{line-height:38px!important}[data-v-f00d3699] .ant-tree-switcher .ant-tree-switcher-icon{font-size:14px!important}[data-v-f00d3699] .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle{height:38px!important;line-height:38px!important;margin-right:8px}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before{border-radius:4px;background:rgba(207,221,247,.35)!important}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected{color:#0f56d7;font-weight:500}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tree-iconEle .iconfont{color:#0f56d7!important}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before{background:rgba(207,221,247,.35)!important;border-radius:4px}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher{color:#000;font-weight:500}[data-v-f00d3699] .ant-tree-treenode:not(:last-child){margin-bottom:8px}[data-v-f00d3699] .ant-tree-indent-unit{width:10px!important}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode:before{bottom:0!important}[data-v-f00d3699] .ant-tree .ant-tree-treenode{padding:0 12px}[data-v-f00d3699] .guns-table-tool .guns-tool{display:none}.img[data-v-f00d3699]{width:24px;height:22px;margin-top:-4px}.svg-img[data-v-f00d3699]{width:24px;height:22px;margin-top:8px}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode{height:38px!important;line-height:38px!important}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button{display:inline;display:flex;top:0}[data-v-f00d3699] .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tree-button-first{display:inline;display:flex;top:0;margin-right:150px}[data-v-f00d3699] .ant-tree-node-content-wrapper{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 0 0 4px}[data-v-f00d3699] .ant-tree-title{width:calc(100% - 32px)}[data-v-f00d3699] .ant-card-body,[data-v-f00d3699] .ant-spin-nested-loading,[data-v-f00d3699] .ant-spin-container{height:100%}[data-v-f00d3699] .ant-tree{background-color:#fff!important}.edit-icon[data-v-f00d3699]{width:60px!important}.tree-edit:hover .edit-title[data-v-f00d3699]{width:calc(100% - 70px)!important}[data-v-f00d3699] .ant-tree-node-content-wrapper{width:100%!important;display:inline-block!important}[data-v-f00d3699] .ant-tree-title{width:100%!important;display:block!important}[data-v-f00d3699] .ant-tree-treenode{width:100%!important}[data-v-f00d3699] .ant-tree-node-content-wrapper{padding:4px 8px!important;line-height:24px!important}[data-v-f00d3699] .ant-spin-nested-loading,[data-v-f00d3699] .ant-spin-container{height:100%}.empty[data-v-f00d3699]{margin-top:50%}.search .ant-input-affix-wrapper[data-v-f00d3699]{border-radius:4px}.left-header .header-add[data-v-f00d3699]{border-radius:4px;transition:background-color .2s}.left-header .header-add[data-v-f00d3699]:hover{background-color:var(--primary-color-hover, #e9f3f8)}\n",document.head.appendChild(t);const D="DATA_LOAD_ERROR",I="LAZY_LOAD_ERROR",R="SEARCH_ERROR",K="ACTION_ERROR",T="CONFIG_ERROR";class M{static formatTreeData(e,t){return e&&e.length?e.map((e=>({...e,key:String(e[t.key]),title:e[t.title],children:e[t.children]?this.formatTreeData(e[t.children],t):[],isLeaf:t.hasChildren?!e[t.hasChildren]:!e[t.children]||0===e[t.children].length}))):[]}static extractAllIds(e){const t=[],a=e=>{e.forEach((e=>{t.push(e.key),e.children&&e.children.length>0&&a(e.children)}))};return a(e),t}static extractNodeIdsByLevel(e,t,a){const n=[],i=e=>{e.forEach((e=>{const o=e[a.level||"level"]||1;o<=t&&n.push(e.key),e.children&&e.children.length>0&&o<t&&i(e.children)}))};return i(e),n}static setIsLeaf(e,t,a){return e&&e.length?e.map((e=>{if(e[t.key]=String(e[t.key]),t.hasChildren&&void 0!==e[t.hasChildren])e.isLeaf=!e[t.hasChildren];else{const n=e[t.children]&&e[t.children].length>0,i=e[t.level||"level"]||1,o=!!a&&i>=a;e.isLeaf=!n&&o}return e[t.children]&&e[t.children].length>0&&(e[t.children]=this.setIsLeaf(e[t.children],t,a)),e})):[]}static searchTreeNodes(e,t,a){if(!t||!t.trim())return e;const n=t.toLowerCase().trim(),i=[],o=e=>{const t=(e[a.title]||"").toLowerCase().includes(n);let i=[];return e.children&&e.children.length>0&&(i=e.children.map(o).filter(Boolean)),t||i.length>0?{...e,children:i}:null};return e.forEach((e=>{const t=o(e);t&&i.push(t)})),i}static getNodePath(e,t,a){const n=[],i=(e,t,o)=>{for(const r of e){const e=[...o,r];if(String(r[a.key])===String(t))return n.push(...e),!0;if(r.children&&r.children.length>0&&i(r.children,t,e))return!0}return!1};return i(e,t,[]),n}static getParentKeys(e,t,a){return this.getNodePath(e,t,a).slice(0,-1).map((e=>String(e[a.key])))}static expandToNode(e,t,a){return this.getParentKeys(e,t,a)}static validateConfig(e){const t=[],a=[];return e.dataSource&&e.dataSource.api||t.push("dataSource.api 是必需的"),e.fieldMapping?(e.fieldMapping.key||t.push("fieldMapping.key 是必需的"),e.fieldMapping.title||t.push("fieldMapping.title 是必需的"),e.fieldMapping.children||t.push("fieldMapping.children 是必需的")):t.push("fieldMapping 是必需的"),e.displayConfig||a.push("建议提供 displayConfig 配置"),e.interactionConfig||a.push("建议提供 interactionConfig 配置"),{isValid:0===t.length,errors:t,warnings:a}}}class P{static handleError(e,t){switch(e.type){case D:return this.handleDataLoadError(e,t);case I:return this.handleLazyLoadError(e,t);case R:return this.handleSearchError(e,t);case K:return this.handleActionError(e,t);case T:return this.handleConfigError(e,t);default:return this.handleUnknownError(e,t)}}static handleDataLoadError(e,t){return console.error("树数据加载失败:",e),{showRetry:!0,message:"数据加载失败，请重试"}}static handleLazyLoadError(e,t){return console.error("懒加载失败:",e),{showNodeRetry:!0,message:"子节点加载失败"}}static handleSearchError(e,t){return console.error("搜索失败:",e),{clearSearch:!0,message:"搜索失败，请重试"}}static handleActionError(e,t){return console.error("操作失败:",e),{message:e.message||"操作失败"}}static handleConfigError(e,t){return console.error("配置错误:",e),{message:"组件配置错误"}}static handleUnknownError(e,t){return console.error("未知错误:",e),{message:"发生未知错误"}}}e("T",class{constructor(){this.config={dataSource:{api:null,lazyLoadApi:null,searchParam:"searchText",parentIdParam:"parentId"},fieldMapping:{key:"id",title:"name",children:"children",hasChildren:"hasChildren",level:"level"},displayConfig:{title:"",showHeader:!0,showSearch:!0,searchPlaceholder:"请输入关键字搜索",showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0},interactionConfig:{selectable:!0,expandable:!0,lazyLoad:!1,defaultExpandLevel:1,allowMultiSelect:!1},actionConfig:{allowAdd:!1,allowEdit:!1,allowDelete:!1,customActions:[]}}}setDataSource(e,t=null,a="searchText",n="parentId"){return this.config.dataSource={api:e,lazyLoadApi:t,searchParam:a,parentIdParam:n},this}setFieldMapping(e,t,a,n=null,i=null){return this.config.fieldMapping={key:e,title:t,children:a,hasChildren:n,level:i},this}setDisplayConfig(e){return this.config.displayConfig={...this.config.displayConfig,...e},this}setInteractionConfig(e){return this.config.interactionConfig={...this.config.interactionConfig,...e},this}setActionConfig(e){return this.config.actionConfig={...this.config.actionConfig,...e},this}enableReadOnlyMode(){return this.config.displayConfig.showAddButton=!1,this.config.displayConfig.showEditIcons=!1,this.config.actionConfig.allowAdd=!1,this.config.actionConfig.allowEdit=!1,this.config.actionConfig.allowDelete=!1,this}enableEditMode(){return this.config.displayConfig.showAddButton=!0,this.config.displayConfig.showEditIcons=!0,this.config.actionConfig.allowAdd=!0,this.config.actionConfig.allowEdit=!0,this.config.actionConfig.allowDelete=!0,this}build(){const e=M.validateConfig(this.config);return e.isValid||console.warn("配置验证失败:",e.errors),e.warnings.length>0&&console.warn("配置警告:",e.warnings),{...this.config}}});const N={class:"box bgColor box-shadow"},O={key:0,class:"left-header"},_={class:"left-header-title"},z={key:1,class:"search"},j={class:"tree-content"},B={class:"left-tree"},U={key:0,class:"tree-edit"},H=["title"],F={class:"edit-icon"},W={key:1,class:"not-tree-edit"},q=["title"];e("U",a(Object.assign({name:"UniversalTree"},{__name:"UniversalTree",props:{dataSource:{type:Object,required:!0},fieldMapping:{type:Object,required:!0},displayConfig:{type:Object,default:()=>({showHeader:!0,showSearch:!0,showAddButton:!1,showEditIcons:!1,showIcon:!1,isSetWidth:!0})},interactionConfig:{type:Object,default:()=>({selectable:!0,expandable:!0,lazyLoad:!1,defaultExpandLevel:2,allowMultiSelect:!1})},actionConfig:{type:Object,default:()=>({allowAdd:!1,allowEdit:!1,allowDelete:!1,customActions:[]})}},emits:["select","expand","search","add","edit","delete","customAction","load","loadError","nodeClick","nodeDoubleClick"],setup(e,{expose:t,emit:a}){const R=e,K=a,T=n(!1),V=n([]),G=n([]),Y=n([]),Z=n([]),J=n(""),Q=i((()=>({children:R.fieldMapping.children,title:R.fieldMapping.title,key:R.fieldMapping.key,value:R.fieldMapping.key}))),{dataSource:X,fieldMapping:$,displayConfig:ee,interactionConfig:te,actionConfig:ae}=R,ne=async()=>{if(X.api)try{T.value=!0;const e={};J.value&&J.value.trim()&&X.searchParam&&(e[X.searchParam]=J.value.trim());const t=await X.api(e),a=(null==t?void 0:t.data)||t||[],n=M.setIsLeaf(a,$);V.value=M.formatTreeData(n,$),J.value&&J.value.trim()?Y.value=M.extractAllIds(V.value):0===Y.value.length&&te.defaultExpandLevel&&(Y.value=M.extractNodeIdsByLevel(V.value,te.defaultExpandLevel,$)),K("load",V.value)}catch(e){console.error("加载树数据失败:",e),V.value=[];const t={type:D,message:"数据加载失败",originalError:e},a=P.handleError(t,{searchText:J.value});k.error(a.message),K("loadError",e)}finally{T.value=!1}else console.error("数据源API未配置")},ie=async e=>X.lazyLoadApi&&X.parentIdParam?Z.value.includes(e.eventKey)?Promise.resolve():(Z.value.push(e.eventKey),new Promise((t=>{const a={};a[X.parentIdParam]=e.dataRef[$.key],X.lazyLoadApi(a).then((t=>{const a=(null==t?void 0:t.data)||t||[],n=M.setIsLeaf(a,$),i=M.formatTreeData(n,$);e.dataRef&&(e.dataRef[$.children]=i,V.value=[...V.value])})).catch((t=>{console.error("懒加载失败:",t);const a=Z.value.indexOf(e.eventKey);a>-1&&Z.value.splice(a,1);const n={type:I,message:"子节点加载失败",originalError:t},i=P.handleError(n,{nodeKey:e.eventKey});k.error(i.message)})).finally((()=>{t()}))}))):Promise.resolve(),oe=(e,t)=>{G.value=e;const a=(t.selectedNodes||[]).map((e=>e.dataRef||e));K("select",{keys:e,nodes:a,event:t})},re=(e,t)=>{var a;Y.value=e;const n=((null==t?void 0:t.expandedNodes)||[]).map((e=>e.dataRef||e));K("expand",{keys:e,nodes:n,expanded:null==t?void 0:t.expanded,node:(null==t||null===(a=t.node)||void 0===a?void 0:a.dataRef)||(null==t?void 0:t.node),event:t})},de=()=>{var e;const t=(null===(e=J.value)||void 0===e?void 0:e.trim())||"";K("search",{searchText:t,isSearching:!!t,timestamp:Date.now()}),ne()},le=()=>{var e;(null===(e=J.value)||void 0===e?void 0:e.trim())||(K("search",{searchText:"",isSearching:!1,timestamp:Date.now()}),ne())},se=(e=null)=>{K("add",{parentNode:e||null,action:"add",timestamp:Date.now(),context:{isRootAdd:!e,parentKey:e?e[$.key]:null,parentTitle:e?e[$.title]:null}})},ce=e=>{G.value=e},he=e=>{const t=[...new Set([...Y.value,...e])];Y.value=t,K("expand",{keys:t,nodes:[],expanded:!0,programmatic:!0,timestamp:Date.now()})},pe=e=>{const t=M.getParentKeys(V.value,e,$);t.length>0&&he(t)},ue=(e,t)=>{var a;if(!t)return;const n=t.dataRef||t;K("nodeClick",{node:n,event:e,timestamp:Date.now(),context:{nodeKey:n[$.key],nodeTitle:n[$.title],hasChildren:(null===(a=n[$.children])||void 0===a?void 0:a.length)>0,isSelected:G.value.includes(String(n[$.key])),isExpanded:Y.value.includes(String(n[$.key]))}})},fe=(e,t)=>{var a;if(!t)return;const n=t.dataRef||t;K("nodeDoubleClick",{node:n,event:e,timestamp:Date.now(),context:{nodeKey:n[$.key],nodeTitle:n[$.title],hasChildren:(null===(a=n[$.children])||void 0===a?void 0:a.length)>0,isSelected:G.value.includes(String(n[$.key])),isExpanded:Y.value.includes(String(n[$.key]))}})};return t({reload:()=>{ne()},loadTreeData:ne,getSelectedNodes:()=>G.value,setSelectedKeys:ce,clearSelection:()=>{G.value=[],K("select",{keys:[],nodes:[],cleared:!0,programmatic:!0,timestamp:Date.now()})},expandNodes:he,collapseNodes:e=>{const t=Y.value.filter((t=>!e.includes(t)));Y.value=t,K("expand",{keys:t,nodes:[],expanded:!1,programmatic:!0,timestamp:Date.now()})},expandAll:()=>{const e=M.extractAllIds(V.value);Y.value=e,K("expand",{keys:e,nodes:[],expanded:!0,expandAll:!0,programmatic:!0,timestamp:Date.now()})},collapseAll:()=>{Y.value=[],K("expand",{keys:[],nodes:[],expanded:!1,collapseAll:!0,programmatic:!0,timestamp:Date.now()})},expandToNode:pe,selectAndExpandToNode:e=>{pe(e),setTimeout((()=>{ce([String(e)])}),100)},findNodeByKey:e=>{const t=(e,a)=>{for(const n of e){if(String(n[$.key])===String(a))return n;if(n[$.children]&&n[$.children].length>0){const e=t(n[$.children],a);if(e)return e}}return null};return t(V.value,e)},getTreeData:()=>V.value,getExpandedKeys:()=>Y.value,getLoadedKeys:()=>Z.value,getSearchText:()=>J.value,isLoading:()=>T.value}),o((()=>{ne()})),(e,t)=>{var a;const n=b,i=E,o=S,k=A,D=L;return r(),d("div",N,[l(ee).showHeader&&l(ee).isSetWidth?(r(),d("div",O,[s("span",_,c(l(ee).title||""),1),s("span",null,[l(ee).showAddButton&&null!==(a=l(ae))&&void 0!==a&&a.allowAdd?(r(),h(l(p),{key:0,class:"header-add",onClick:t[0]||(t[0]=e=>se()),title:"新增"+(l(ee).title||"节点")},null,8,["title"])):u("",!0)])])):u("",!0),l(ee).showSearch?(r(),d("div",z,[f(n,{value:J.value,"onUpdate:value":t[1]||(t[1]=e=>J.value=e),placeholder:l(ee).searchPlaceholder||"请输入关键词搜索","allow-clear":"",onPressEnter:de,onChange:le},{prefix:v((()=>[f(g,{iconClass:"icon-opt-search"})])),_:1},8,["value","placeholder"])])):u("",!0),s("div",j,[f(D,{tip:"Loading...",spinning:T.value,delay:100},{default:v((()=>[y(s("div",B,[f(o,{selectedKeys:G.value,"onUpdate:selectedKeys":t[2]||(t[2]=e=>G.value=e),expandedKeys:Y.value,"onUpdate:expandedKeys":t[3]||(t[3]=e=>Y.value=e),loadedKeys:Z.value,"onUpdate:loadedKeys":t[4]||(t[4]=e=>Z.value=e),onSelect:oe,onExpand:re,onClick:ue,onDblclick:fe,"load-data":l(te).lazyLoad?ie:void 0,"tree-data":V.value,"show-icon":l(ee).showIcon,multiple:l(te).allowMultiSelect,selectable:l(te).selectable,"field-names":Q.value},{title:v((e=>[l(ee).showEditIcons&&l(ae)?(r(),d("span",U,[s("span",{class:"edit-title",title:e[l($).title]||""},c(e[l($).title]||""),9,H),s("span",F,[f(i,null,{default:v((()=>[l(ae).allowAdd?(r(),h(g,{key:0,iconClass:"icon-opt-tianjia",color:"var(--primary-color)",title:"新增子节点",onClick:m((t=>se(e)),["stop"])},null,8,["onClick"])):u("",!0),l(ae).allowEdit?(r(),h(g,{key:1,iconClass:"icon-opt-bianji",color:"var(--primary-color)",title:"编辑节点",onClick:m((t=>{var a,n;(a=e)?K("edit",{node:a,action:"edit",timestamp:Date.now(),context:{nodeKey:a[$.key],nodeTitle:a[$.title],hasChildren:(null===(n=a[$.children])||void 0===n?void 0:n.length)>0}}):console.warn("编辑操作缺少节点数据")}),["stop"])},null,8,["onClick"])):u("",!0),l(ae).allowDelete?(r(),h(g,{key:2,iconClass:"icon-opt-shanchu",color:"red",title:"删除节点",onClick:m((t=>{var a,n;(a=e)?K("delete",{node:a,action:"delete",timestamp:Date.now(),context:{nodeKey:a[$.key],nodeTitle:a[$.title],hasChildren:(null===(n=a[$.children])||void 0===n?void 0:n.length)>0,isSelected:G.value.includes(String(a[$.key]))}}):console.warn("删除操作缺少节点数据")}),["stop"])},null,8,["onClick"])):u("",!0),(r(!0),d(w,null,x(l(ae).customActions||[],(t=>(r(),h(g,{key:t.key,iconClass:t.icon,color:t.color||"var(--primary-color)",title:t.title||t.key,onClick:m((a=>((e,t)=>{var a,n;if(!e||!t)return void console.warn("自定义操作缺少必要参数");const i=null==ae||null===(a=ae.customActions)||void 0===a?void 0:a.find((t=>t.key===e));K("customAction",{actionKey:e,node:t,action:"custom",timestamp:Date.now(),context:{nodeKey:t[$.key],nodeTitle:t[$.title],actionConfig:i,hasChildren:(null===(n=t[$.children])||void 0===n?void 0:n.length)>0}})})(t.key,e)),["stop"])},null,8,["iconClass","color","title","onClick"])))),128))])),_:2},1024)])])):(r(),d("span",W,[s("span",{class:"edit-title",title:e[l($).title]||""},c(e[l($).title]||""),9,q)]))])),_:1},8,["selectedKeys","expandedKeys","loadedKeys","load-data","tree-data","show-icon","multiple","selectable","field-names"])],512),[[C,V.value&&V.value.length>0]]),y(f(k,{class:"empty"},null,512),[[C,V.value&&0===V.value.length]])])),_:1},8,["spinning"])])])}}}),[["__scopeId","data-v-f00d3699"]]))}}}));
