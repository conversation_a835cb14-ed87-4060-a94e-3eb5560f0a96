package cn.stylefeng.roses.kernel.micro.tran.message.modular.service.impl;

import cn.stylefeng.roses.kernel.micro.api.pojo.TranMessage;
import cn.stylefeng.roses.kernel.micro.tran.message.modular.mapper.TranMessageMapper;
import cn.stylefeng.roses.kernel.micro.tran.message.modular.service.ITranMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-04-16
 */
@Service
public class ReliableMessageServiceImpl extends ServiceImpl<TranMessageMapper, TranMessage> implements ITranMessageService {

}
