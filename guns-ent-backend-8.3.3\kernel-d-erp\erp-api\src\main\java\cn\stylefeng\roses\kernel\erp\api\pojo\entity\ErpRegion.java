package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseBusinessEntity;
import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 区域管理实体类
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
@TableName(value = "erp_region", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ErpRegion extends BaseEntity {

    /**
     * 区域ID
     */
    @TableId(value = "region_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 区域编码
     */
    @TableField("region_code")
    @ChineseDescription("区域编码")
    private String regionCode;

    /**
     * 区域名称
     */
    @TableField("region_name")
    @ChineseDescription("区域名称")
    private String regionName;

    /**
     * 父级区域ID
     */
    @TableField("parent_id")
    @ChineseDescription("父级区域ID")
    private Long parentId;

    /**
     * 区域层级（1-国家，2-省，3-市，4-区县，5-商圈）
     */
    @TableField("region_level")
    @ChineseDescription("区域层级")
    private Integer regionLevel;

    /**
     * 区域路径（用/分隔，如：1/2/3）
     */
    @TableField("region_path")
    @ChineseDescription("区域路径")
    private String regionPath;

    /**
     * 排序号
     */
    @TableField("sort_order")
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @TableField("status")
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    @ChineseDescription("备注")
    private String remark;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ChineseDescription("租户id")
    private Long tenantId;

    /**
     * 删除标记：Y-已删除，N-未删除
     */
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    @ChineseDescription("删除标记：Y-已删除，N-未删除")
    @TableLogic
    private String delFlag;
}
