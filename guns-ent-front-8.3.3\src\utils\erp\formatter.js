/**
 * ERP业务数据格式化工具函数
 * 
 * 提供各种数据格式化功能，包括价格、数量、日期等
 */

import { 
  BUSINESS_MODE_TEXT, 
  PRICING_TYPE_TEXT, 
  PURCHASE_ORDER_STATUS_TEXT,
  INVENTORY_STATUS_TEXT,
  PAYMENT_METHOD_TEXT,
  PRECISION_CONFIG 
} from './constants'

/**
 * 格式化价格显示
 * @param {number|string} price - 价格值
 * @param {number} precision - 精度，默认2位小数
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(price, precision = PRECISION_CONFIG.PRICE) {
  if (price === null || price === undefined || price === '') {
    return '-'
  }
  const numPrice = Number(price)
  if (isNaN(numPrice)) {
    return '-'
  }
  return `¥${numPrice.toFixed(precision)}`
}

/**
 * 格式化数量显示
 * @param {number|string} quantity - 数量值
 * @param {string} pricingType - 计价类型
 * @param {number} precision - 精度，默认3位小数
 * @returns {string} 格式化后的数量字符串
 */
export function formatQuantity(quantity, pricingType = 'NORMAL', precision = PRECISION_CONFIG.QUANTITY) {
  if (quantity === null || quantity === undefined || quantity === '') {
    return '-'
  }
  const numQuantity = Number(quantity)
  if (isNaN(numQuantity)) {
    return '-'
  }
  
  const formattedNum = numQuantity.toFixed(precision)
  
  // 根据计价类型添加单位
  switch (pricingType) {
    case 'WEIGHT':
      return `${formattedNum}kg`
    case 'PIECE':
      return `${formattedNum}份`
    case 'NORMAL':
    case 'VARIABLE':
    default:
      return `${formattedNum}个`
  }
}

/**
 * 格式化百分比显示
 * @param {number|string} percentage - 百分比值
 * @param {number} precision - 精度，默认2位小数
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercentage(percentage, precision = PRECISION_CONFIG.PERCENTAGE) {
  if (percentage === null || percentage === undefined || percentage === '') {
    return '-'
  }
  const numPercentage = Number(percentage)
  if (isNaN(numPercentage)) {
    return '-'
  }
  return `${numPercentage.toFixed(precision)}%`
}

/**
 * 格式化日期显示
 * @param {string|Date} date - 日期值
 * @param {string} format - 格式，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) {
    return '-'
  }
  
  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) {
    return '-'
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD HH:mm:ss':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    case 'YYYY-MM-DD HH:mm':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'MM-DD':
      return `${month}-${day}`
    case 'YYYY-MM-DD':
    default:
      return `${year}-${month}-${day}`
  }
}

/**
 * 格式化供应商经营方式显示
 * @param {string} businessMode - 经营方式枚举值
 * @returns {string} 格式化后的经营方式文本
 */
export function formatBusinessMode(businessMode) {
  return BUSINESS_MODE_TEXT[businessMode] || businessMode || '-'
}

/**
 * 格式化商品计价类型显示
 * @param {string} pricingType - 计价类型枚举值
 * @returns {string} 格式化后的计价类型文本
 */
export function formatPricingType(pricingType) {
  return PRICING_TYPE_TEXT[pricingType] || pricingType || '-'
}

/**
 * 格式化采购入库单状态显示
 * @param {string} status - 状态枚举值
 * @returns {string} 格式化后的状态文本
 */
export function formatPurchaseOrderStatus(status) {
  return PURCHASE_ORDER_STATUS_TEXT[status] || status || '-'
}

/**
 * 格式化库存状态显示
 * @param {string} status - 库存状态枚举值
 * @returns {string} 格式化后的库存状态文本
 */
export function formatInventoryStatus(status) {
  return INVENTORY_STATUS_TEXT[status] || status || '-'
}

/**
 * 格式化付款方式显示
 * @param {string} paymentMethod - 付款方式枚举值
 * @returns {string} 格式化后的付款方式文本
 */
export function formatPaymentMethod(paymentMethod) {
  return PAYMENT_METHOD_TEXT[paymentMethod] || paymentMethod || '-'
}

/**
 * 格式化文件大小显示
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字显示（添加千分位分隔符）
 * @param {number|string} number - 数字值
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(number) {
  if (number === null || number === undefined || number === '') {
    return '-'
  }
  const numValue = Number(number)
  if (isNaN(numValue)) {
    return '-'
  }
  return numValue.toLocaleString()
}

/**
 * 截断文本显示
 * @param {string} text - 原始文本
 * @param {number} maxLength - 最大长度
 * @param {string} suffix - 后缀，默认为'...'
 * @returns {string} 截断后的文本
 */
export function truncateText(text, maxLength = 50, suffix = '...') {
  if (!text || text.length <= maxLength) {
    return text || '-'
  }
  return text.substring(0, maxLength) + suffix
}