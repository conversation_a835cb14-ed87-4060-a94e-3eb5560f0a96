<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <!-- 头部操作区 -->
            <div class="content-mian-header">
              <div class="header-content">
                <div class="header-content-left">
                  <a-space :size="16">
                    <!-- 移除页面标题，保持与职位管理页面一致 -->
                  </a-space>
                </div>
                <div class="header-content-right">
                  <a-space :size="16">
                    <a-dropdown>
                      <template #overlay>
                        <a-menu @click="moreClick">
                          <a-menu-item key="1">
                            <icon-font iconClass="icon-opt-daochu" color="#60666b" />
                            <span>导出数据</span>
                          </a-menu-item>
                          <a-menu-item key="2">
                            <icon-font iconClass="icon-opt-tongji" color="#60666b" />
                            <span>库存统计</span>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button class="border-radius">
                        更多
                        <small-dash-outlined />
                      </a-button>
                    </a-dropdown>
                  </a-space>
                </div>
              </div>
            </div>

            <!-- 主体内容区 -->
            <div class="content-mian-body">
              <div class="table-content">
                <common-table
                  :columns="columns"
                  :where="where"
                  fieldBusinessCode="ERP_INVENTORY_TABLE"
                  showTableTool
                  :showToolTotal="false"
                  rowId="productId"
                  ref="tableRef"
                  url="/erp/inventory/page"
                  methods="post"
                >
                  <template #toolLeft>
                    <a-input
                      v-model:value="where.searchText"
                      placeholder="商品名称、编码、条形码（回车搜索）"
                      @pressEnter="reload"
                      :bordered="false"
                      class="search-input"
                    >
                      <template #prefix>
                        <icon-font iconClass="icon-opt-search" />
                      </template>
                    </a-input>
                    <a-divider type="vertical" class="divider" />
                    <a @click="changeSuperSearch">{{ superSearch ? '收起' : '高级筛选' }}</a>
                  </template>
                  <template #toolBottom>
                    <div class="super-search" style="margin-top: 8px" v-if="superSearch">
                      <a-form :model="where" :labelCol="labelCol" :wrapper-col="wrapperCol">
                        <a-row :gutter="16">
                          <a-col v-bind="spanCol">
                            <a-form-item label="商品名称:">
                              <a-input v-model:value="where.productName" placeholder="请输入商品名称" allowClear />
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="商品编码:">
                              <a-input v-model:value="where.productCode" placeholder="请输入商品编码" allowClear />
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="供应商:">
                              <supplier-selector 
                                v-model:value="where.supplierId" 
                                :filter="{ businessMode: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
                                placeholder="请选择供应商"
                                allowClear
                              />
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="计价类型:">
                              <a-select v-model:value="where.pricingType" placeholder="请选择计价类型" allowClear>
                                <a-select-option value="NORMAL">普通</a-select-option>
                                <a-select-option value="WEIGHT">称重</a-select-option>
                                <a-select-option value="PIECE">计件</a-select-option>
                                <a-select-option value="VARIABLE">变价</a-select-option>
                              </a-select>
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="库存状态:">
                              <a-select v-model:value="where.stockStatus" placeholder="请选择库存状态" allowClear>
                                <a-select-option value="NORMAL">正常</a-select-option>
                                <a-select-option value="WARNING">预警</a-select-option>
                                <a-select-option value="OUT_OF_STOCK">缺货</a-select-option>
                              </a-select>
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label=" " :colon="false">
                              <a-space>
                                <a-button type="primary" @click="reload">搜索</a-button>
                                <a-button @click="reset">重置</a-button>
                              </a-space>
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </a-form>
                    </div>
                  </template>

                  <!-- 表格列自定义渲染 -->
                  <template #bodyCell="{ column, record }">
                    <!-- 商品信息 -->
                    <template v-if="column.key === 'productInfo'">
                      <div class="product-info">
                        <div class="product-name">
                          <a @click="showDetail(record)" class="table-link">{{ record.productName }}</a>
                        </div>
                        <div class="product-details">
                          <span class="product-code">{{ record.productCode }}</span>
                          <span v-if="record.barcode" class="product-barcode">{{ record.barcode }}</span>
                        </div>
                      </div>
                    </template>

                    <!-- 供应商信息 -->
                    <template v-if="column.key === 'supplierInfo'">
                      <div>
                        <div class="supplier-name">{{ record.supplierName }}</div>
                        <a-tag 
                          v-if="record.businessModeName" 
                          size="small" 
                          :color="getBusinessModeColor(record.businessMode)"
                        >
                          {{ record.businessModeName }}
                        </a-tag>
                      </div>
                    </template>

                    <!-- 计价类型 -->
                    <template v-if="column.key === 'pricingType'">
                      <a-tag :color="getPricingTypeColor(record.pricingType)">
                        {{ getPricingTypeName(record.pricingType) }}
                      </a-tag>
                    </template>

                    <!-- 当前库存 -->
                    <template v-if="column.key === 'currentStock'">
                      <div class="stock-info">
                        <span :class="getStockClass(record.currentStock, record.minStock)">
                          {{ formatStock(record.currentStock, record.pricingType) }}
                        </span>
                        <span class="stock-unit">{{ getStockUnit(record.pricingType, record.unit) }}</span>
                      </div>
                    </template>

                    <!-- 预警值 -->
                    <template v-if="column.key === 'minStock'">
                      <span class="min-stock">
                        {{ formatStock(record.minStock, record.pricingType) }}
                        {{ getStockUnit(record.pricingType, record.unit) }}
                      </span>
                    </template>

                    <!-- 库存价值 -->
                    <template v-if="column.key === 'totalValue'">
                      <span class="total-value">¥{{ formatAmount(record.totalValue) }}</span>
                    </template>

                    <!-- 库存状态 -->
                    <template v-if="column.key === 'stockStatus'">
                      <a-tag :color="getStockStatusColor(calculateStockStatus(record))">
                        {{ getStockStatusName(calculateStockStatus(record)) }}
                      </a-tag>
                    </template>

                    <!-- 操作 -->
                    <template v-if="column.key === 'action'">
                      <a-space :size="16">
                        <icon-font
                          iconClass="icon-opt-xiangqing"
                          font-size="24px"
                          title="查看详情"
                          color="#60666b"
                          @click="showDetail(record)"
                        />
                        <icon-font
                          iconClass="icon-opt-shezhi"
                          font-size="24px"
                          title="设置预警值"
                          color="#60666b"
                          @click="openSetMinStockModal(record)"
                        />
                        <icon-font
                          iconClass="icon-opt-xiangqing"
                          font-size="24px"
                          title="查看历史"
                          color="#60666b"
                          @click="showHistory(record)"
                        />
                      </a-space>
                    </template>
                  </template>
                </common-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存详情弹窗 -->
    <inventory-detail
      v-model:visible="showDetailModal"
      :data="currentRecord"
      @showHistory="handleShowHistoryFromDetail"
    />

    <!-- 库存历史弹窗 -->
    <inventory-history
      v-model:visible="showHistoryModal"
      :data="currentRecord"
    />

    <!-- 库存调整弹窗 -->
    <inventory-adjust-modal
      v-model:visible="showAdjustModal"
      :data="currentRecord"
      @ok="handleAdjustOk"
    />

    <!-- 设置预警值弹窗 -->
    <set-min-stock-modal
      v-model:visible="showSetMinStockModal"
      :data="currentRecord"
      @ok="handleSetMinStockOk"
    />

    <!-- 库存统计弹窗 -->
    <inventory-statistics-modal
      v-model:visible="showStatisticsModal"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { 
  SmallDashOutlined, 
  DownOutlined
} from '@ant-design/icons-vue';
import { InventoryApi } from './api/InventoryApi';
import { isMobile } from '@/utils/common/util';
import SupplierSelector from '@/components/erp/SupplierSelector.vue';
import InventoryDetail from './components/InventoryDetail.vue';
import InventoryHistory from './components/InventoryHistory.vue';
import InventoryAdjustModal from './components/InventoryAdjustModal.vue';
import SetMinStockModal from './components/SetMinStockModal.vue';
import InventoryStatisticsModal from './components/InventoryStatisticsModal.vue';

export default {
  name: 'InventoryIndex',
  components: {
    SmallDashOutlined,
    DownOutlined,
    SupplierSelector,
    InventoryDetail,
    InventoryHistory,
    InventoryAdjustModal,
    SetMinStockModal,
    InventoryStatisticsModal
  },
  setup() {
    // 响应式数据
    const superSearch = ref(false);
    const showDetailModal = ref(false);
    const showHistoryModal = ref(false);
    const showAdjustModal = ref(false);
    const showSetMinStockModal = ref(false);
    const showStatisticsModal = ref(false);
    const currentRecord = ref({});
    const tableRef = ref(null);



    // 响应式计算属性
    const labelCol = computed(() => {
      return { xxl: 7, xl: 7, lg: 5, md: 7, sm: 4 };
    });

    const wrapperCol = computed(() => {
      return { xxl: 17, xl: 17, lg: 19, md: 17, sm: 20 };
    });

    const spanCol = computed(() => {
      if (isMobile()) {
        return { xxl: 6, xl: 8, lg: 12, md: 24, sm: 24, xs: 24 };
      }
      return { xxl: 6, xl: 8, lg: 24, md: 24, sm: 24, xs: 24 };
    });

    // 查询参数
    const where = reactive({
      searchText: '',
      productName: '',
      productCode: '',
      supplierId: undefined,
      pricingType: undefined,
      stockStatus: undefined,
      businessModeList: ['PURCHASE_SALE', 'CONSIGNMENT'] // 排除联营商品
    });

    // 表格列定义
    const columns = [
      {
        title: '商品信息',
        key: 'productInfo',
        width: 250,
        fixed: 'left'
      },
      {
        title: '供应商',
        key: 'supplierInfo',
        width: 180
      },
      {
        title: '计价类型',
        key: 'pricingType',
        width: 100
      },
      {
        title: '当前库存',
        key: 'currentStock',
        width: 120,
        align: 'right'
      },
      {
        title: '预警值',
        key: 'minStock',
        width: 100,
        align: 'right'
      },
      {
        title: '库存价值',
        key: 'totalValue',
        width: 120,
        align: 'right'
      },
      {
        title: '库存状态',
        key: 'stockStatus',
        width: 100
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 160
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ];

    // 切换高级搜索
    const changeSuperSearch = () => {
      superSearch.value = !superSearch.value;
    };

    // 重新加载数据
    const reload = () => {
      tableRef.value.reload();
    };

    // 重置搜索条件
    const reset = () => {
      where.searchText = '';
      where.productName = '';
      where.productCode = '';
      where.supplierId = undefined;
      where.pricingType = undefined;
      where.stockStatus = undefined;
      reload();
    };

    // 获取经营方式颜色
    const getBusinessModeColor = (businessMode) => {
      const colorMap = {
        'PURCHASE_SALE': 'blue',
        'JOINT_VENTURE': 'orange',
        'CONSIGNMENT': 'green'
      };
      return colorMap[businessMode] || 'default';
    };

    // 获取计价类型名称
    const getPricingTypeName = (pricingType) => {
      const typeMap = {
        'NORMAL': '普通',
        'WEIGHT': '称重',
        'PIECE': '计件',
        'VARIABLE': '变价'
      };
      return typeMap[pricingType] || pricingType;
    };

    // 获取计价类型颜色
    const getPricingTypeColor = (pricingType) => {
      const colorMap = {
        'NORMAL': 'blue',
        'WEIGHT': 'orange',
        'PIECE': 'green',
        'VARIABLE': 'purple'
      };
      return colorMap[pricingType] || 'default';
    };

    // 获取库存样式类
    const getStockClass = (currentStock, minStock) => {
      const current = parseFloat(currentStock) || 0;
      const min = parseFloat(minStock) || 0;

      if (current <= 0) return 'stock-danger';
      if (current <= min) return 'stock-warning';
      return 'stock-normal';
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 获取库存状态名称
    const getStockStatusName = (status) => {
      const statusMap = {
        'NORMAL': '正常',
        'WARNING': '预警',
        'OUT_OF_STOCK': '缺货',
        'FROZEN': '冻结'
      };
      return statusMap[status] || '未知';
    };

    // 获取库存状态颜色
    const getStockStatusColor = (status) => {
      const colorMap = {
        'NORMAL': 'green',
        'WARNING': 'orange',
        'OUT_OF_STOCK': 'red',
        'FROZEN': 'blue'
      };
      return colorMap[status] || 'default';
    };

    // 计算库存状态（如果后端没有返回stockStatus字段）
    const calculateStockStatus = (record) => {
      if (record.stockStatus) {
        return record.stockStatus;
      }

      const currentStock = parseFloat(record.currentStock || 0);
      const minStock = parseFloat(record.minStock || 0);

      if (currentStock <= 0) {
        return 'OUT_OF_STOCK';
      } else if (minStock > 0 && currentStock <= minStock) {
        return 'WARNING';
      } else {
        return 'NORMAL';
      }
    };

    // 打开设置预警值弹窗
    const openSetMinStockModal = (record) => {
      currentRecord.value = { ...record };
      showSetMinStockModal.value = true;
    };

    // 查看详情
    const showDetail = (record) => {
      currentRecord.value = { ...record };
      showDetailModal.value = true;
    };

    // 查看历史
    const showHistory = (record) => {
      currentRecord.value = { ...record };
      showHistoryModal.value = true;
    };

    // 从详情页跳转到历史记录
    const handleShowHistoryFromDetail = (record) => {
      currentRecord.value = { ...record };
      showDetailModal.value = false; // 关闭详情弹窗
      showHistoryModal.value = true; // 打开历史记录弹窗
    };

    // 库存调整
    const adjustStock = (record) => {
      currentRecord.value = { ...record };
      showAdjustModal.value = true;
    };

    // 打开库存调整弹窗
    const openAdjustModal = () => {
      currentRecord.value = {};
      showAdjustModal.value = true;
    };





    // 更多操作菜单点击
    const moreClick = ({ key }) => {
      if (key === '1') {
        // 导出数据
        exportData();
      } else if (key === '2') {
        // 库存统计
        showStatisticsModal.value = true;
      }
    };



    // 导出数据
    const exportData = () => {
      try {
        InventoryApi.exportInventory(where);
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败：' + (error.message || '未知错误'));
      }
    };

    // 库存调拨
    const transferStock = async (record) => {
      try {
        // 这里可以打开调拨弹窗或调用调拨API
        currentRecord.value = { ...record };
        // showTransferModal.value = true; // 如果有调拨弹窗的话
        message.info('库存调拨功能待实现');
      } catch (error) {
        throw error;
      }
    };

    // 初始化库存
    const initStock = async (record) => {
      try {
        // 这里调用初始化库存的API
        await InventoryApi.initStock({ productId: record.productId });
        reload();
      } catch (error) {
        throw error; // 重新抛出错误，让组件处理
      }
    };



    // 库存调整成功回调
    const handleAdjustOk = () => {
      showAdjustModal.value = false;
      reload();
    };

    // 设置预警值成功回调
    const handleSetMinStockOk = () => {
      showSetMinStockModal.value = false;
      reload();
    };

    // 组件挂载时的操作
    onMounted(() => {
      // 页面初始化
    });

    return {
      superSearch,
      showDetailModal,
      showHistoryModal,
      showAdjustModal,
      showSetMinStockModal,
      showStatisticsModal,
      currentRecord,
      tableRef,
      labelCol,
      wrapperCol,
      spanCol,
      where,
      columns,
      changeSuperSearch,
      reload,
      reset,
      getBusinessModeColor,
      getPricingTypeName,
      getPricingTypeColor,
      getStockClass,
      getStockUnit,
      formatStock,
      formatAmount,
      getStockStatusName,
      getStockStatusColor,
      calculateStockStatus,
      showDetail,
      showHistory,
      handleShowHistoryFromDetail,
      adjustStock,
      transferStock,
      initStock,
      openAdjustModal,
      openSetMinStockModal,
      moreClick,
      exportData,
      handleAdjustOk,
      handleSetMinStockOk
    };
  }
};
</script>

<style scoped>
/* 搜索区域样式 */

/* 搜索区域样式 */
.divider {
  height: 20px;
  margin: 0 12px;
}

/* 商品信息样式 */
.product-info {
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.product-details {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.3;
}

.product-code {
  margin-right: 8px;
  color: #1890ff;
}

.product-barcode {
  margin-left: 8px;
  color: #52c41a;
}

/* 供应商信息样式 */
.supplier-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

/* 库存信息样式 */
.stock-info {
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.stock-unit {
  margin-left: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.stock-normal {
  color: #52c41a;
  font-weight: 500;
}

.stock-warning {
  color: #faad14;
  font-weight: 500;
}

.stock-danger {
  color: #ff4d4f;
  font-weight: 500;
}

.min-stock {
  color: #8c8c8c;
  font-size: 12px;
}

.total-value {
  font-weight: 500;
  color: #1890ff;
  text-align: right;
}

/* 表格链接样式 */
.table-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.table-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 表格行样式优化 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #fafafa !important;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa !important;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f5f5f5;
  padding: 12px 16px;
  vertical-align: middle;
}


</style>
