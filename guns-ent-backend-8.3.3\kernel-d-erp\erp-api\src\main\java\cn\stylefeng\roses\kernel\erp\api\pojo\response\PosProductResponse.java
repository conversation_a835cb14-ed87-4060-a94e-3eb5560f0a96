package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;

/**
 * POS商品响应参数
 *
 * <AUTHOR>
 * @since 2025/08/01 15:00
 */
@Data
public class PosProductResponse {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 条形码
     */
    @ChineseDescription("条形码")
    private String barcode;

    /**
     * 商品分类ID
     */
    @ChineseDescription("商品分类ID")
    private Long categoryId;

    /**
     * 商品分类名称
     */
    @ChineseDescription("商品分类名称")
    private String categoryName;

    /**
     * 品牌
     */
    @ChineseDescription("品牌")
    private String brand;

    /**
     * 规格
     */
    @ChineseDescription("规格")
    private String specification;

    /**
     * 基本单位
     */
    @ChineseDescription("基本单位")
    private String unit;

    /**
     * 计价类型（NORMAL-普通，WEIGHT-计重，PIECE-计件，VARIABLE-不定价）
     */
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 计价类型名称
     */
    @ChineseDescription("计价类型名称")
    private String pricingTypeName;

    /**
     * 零售价格
     */
    @ChineseDescription("零售价格")
    private BigDecimal retailPrice;

    /**
     * 单位价格（计重商品用）
     */
    @ChineseDescription("单位价格")
    private BigDecimal unitPrice;

    /**
     * 单份价格（计件商品用）
     */
    @ChineseDescription("单份价格")
    private BigDecimal piecePrice;

    /**
     * 参考价格（不定价商品用）
     */
    @ChineseDescription("参考价格")
    private BigDecimal referencePrice;

    /**
     * 当前库存数量
     */
    @ChineseDescription("当前库存数量")
    private BigDecimal stockQuantity;

    /**
     * 库存状态（NORMAL-正常，WARNING-预警，OUT_OF_STOCK-缺货）
     */
    @ChineseDescription("库存状态")
    private String stockStatus;

    /**
     * 库存状态名称
     */
    @ChineseDescription("库存状态名称")
    private String stockStatusName;

    /**
     * 商品状态（ACTIVE-正常，INACTIVE-停用）
     */
    @ChineseDescription("商品状态")
    private String status;

    /**
     * 商品状态名称
     */
    @ChineseDescription("商品状态名称")
    private String statusName;

    /**
     * 商品图片URL
     */
    @ChineseDescription("商品图片URL")
    private String imageUrl;

}