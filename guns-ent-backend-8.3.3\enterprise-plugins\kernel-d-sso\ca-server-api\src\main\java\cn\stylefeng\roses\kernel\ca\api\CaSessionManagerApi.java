package cn.stylefeng.roses.kernel.ca.api;

import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;

/**
 * 统一认证中心会话管理
 *
 * <AUTHOR>
 * @date 2021/1/21 14:04
 */
public interface CaSessionManagerApi {

    /**
     * 根据token获取用户的会话信息
     *
     * @param token token会话信息，一般为一个随机字符串，用来做缓存key
     * <AUTHOR>
     * @date 2021/1/21 14:07
     */
    CaLoginUser getCaSession(String token);

    /**
     * 创建CA Session，不创建cookie
     *
     * @param token       token会话信息，一般为一个随机字符串，用来做缓存key
     * @param caLoginUser 当前CA登陆用用户
     * <AUTHOR>
     * @date 2021/1/23 15:24
     */
    void createCaSession(String token, CaLoginUser caLoginUser);

    /**
     * 创建CA Session，同时cookie和缓存会创建用户的信息
     *
     * @param token       token会话信息，一般为一个随机字符串，用来做缓存key
     * @param caLoginUser 当前CA登陆用用户
     * <AUTHOR>
     * @date 2021/1/23 15:24
     */
    void createCaSessionAndCookie(String token, CaLoginUser caLoginUser);

    /**
     * 刷新CA Session的过期时间，cookie过期时间和缓存中的过期时间同时刷新
     *
     * @param token token会话信息，一般为一个随机字符串，用来做缓存key
     * <AUTHOR>
     * @date 2020/10/19 16:50
     */
    void refreshSession(String token);

    /**
     * 销毁会话信息
     *
     * @param token token会话信息，一般为一个随机字符串，用来做缓存key
     * <AUTHOR>
     * @date 2021/2/1 14:24
     */
    void destroySession(String token);

    /**
     * 更新会话内容
     *
     * @param token       token会话信息，一般为一个随机字符串，用来做缓存key
     * @param caLoginUser session
     * <AUTHOR>
     * @date 2021/2/1 14:24
     */
    void updateSession(String token, CaLoginUser caLoginUser);

    /**
     * 强制CA登录方式下线
     *
     * <AUTHOR>
     * @date 2021-4-2 10:39
     */
    void offlineCaByAccountId(Long accountId);

}
