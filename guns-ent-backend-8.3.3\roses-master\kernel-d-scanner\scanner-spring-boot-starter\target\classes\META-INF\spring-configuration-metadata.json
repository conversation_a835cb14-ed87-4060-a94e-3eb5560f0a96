{"groups": [{"name": "devops", "type": "cn.stylefeng.roses.kernel.scanner.api.pojo.devops.DevOpsReportProperties", "sourceType": "cn.stylefeng.roses.kernel.scanner.starter.ProjectResourceAutoConfiguration", "sourceMethod": "devOpsReportProperties()"}, {"name": "scanner", "type": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties", "sourceType": "cn.stylefeng.roses.kernel.scanner.starter.ProjectResourceAutoConfiguration", "sourceMethod": "scannerProperties()"}], "properties": [{"name": "devops.field-metadata-class-path", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.devops.DevOpsReportProperties"}, {"name": "devops.project-interaction-secret-key", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.devops.DevOpsReportProperties"}, {"name": "devops.project-unique-code", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.devops.DevOpsReportProperties"}, {"name": "devops.server-host", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.devops.DevOpsReportProperties"}, {"name": "devops.token-validity-period-seconds", "type": "java.lang.Long", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.devops.DevOpsReportProperties"}, {"name": "scanner.app-code", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}, {"name": "scanner.context-path", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}, {"name": "scanner.entity-scan-package", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}, {"name": "scanner.link-symbol", "type": "java.lang.String", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}, {"name": "scanner.open", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}, {"name": "scanner.url-with-app-code", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}, {"name": "scanner.url-with-context-path", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.stylefeng.roses.kernel.scanner.api.pojo.scanner.ScannerProperties"}], "hints": []}