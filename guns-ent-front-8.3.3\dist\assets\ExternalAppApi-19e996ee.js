import{R as a}from"./index-18a1ea24.js";class n{static findPage(t){return a.getAndLoadData("/apiClient/page",t)}static add(t){return a.post("/apiClient/add",t)}static edit(t){return a.post("/apiClient/edit",t)}static delete(t){return a.post("/apiClient/delete",t)}static batchDelete(t){return a.post("/apiClient/batchDelete",t)}static detail(t){return a.getAndLoadData("/apiClient/detail",t)}static list(t){return a.getAndLoadData("/apiClient/list",t)}static changeStatus(t){return a.post("/apiClient/changeStatus",t)}static randomRsaKey(t){return a.getAndLoadData("/apiClient/randomRsaKey",t)}}export{n as E};
