package cn.stylefeng.roses.ent.mobile.invite.mapper;

import cn.stylefeng.roses.ent.mobile.invite.entity.SysInviteUser;
import cn.stylefeng.roses.ent.mobile.invite.pojo.request.SysInviteUserRequest;
import cn.stylefeng.roses.ent.mobile.invite.pojo.response.SysInviteUserVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邀请用户 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024/04/08 18:11
 */
public interface SysInviteUserMapper extends BaseMapper<SysInviteUser> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2024/04/08 18:11
     */
    List<SysInviteUserVo> customFindList(@Param("page") Page page, @Param("param")SysInviteUserRequest request);

}
