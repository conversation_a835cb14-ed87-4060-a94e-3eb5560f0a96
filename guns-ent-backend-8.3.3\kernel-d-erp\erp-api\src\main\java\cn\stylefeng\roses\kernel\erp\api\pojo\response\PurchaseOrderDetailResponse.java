package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 采购入库单明细响应参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderDetailResponse extends BaseResponse {

    /**
     * 明细ID
     */
    @ChineseDescription("明细ID")
    private Long id;

    /**
     * 入库单ID
     */
    @ChineseDescription("入库单ID")
    private Long orderId;

    /**
     * 入库单号
     */
    @ChineseDescription("入库单号")
    private String orderNo;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品规格
     */
    @ChineseDescription("商品规格")
    private String productSpecification;

    /**
     * 商品单位
     */
    @ChineseDescription("商品单位")
    private String productUnit;

    /**
     * 商品计价类型
     */
    @ChineseDescription("商品计价类型")
    private String pricingType;

    /**
     * 商品计价类型名称
     */
    @ChineseDescription("商品计价类型名称")
    private String pricingTypeName;

    /**
     * 数量
     */
    @ChineseDescription("数量")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @ChineseDescription("单价")
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @ChineseDescription("总价")
    private BigDecimal totalPrice;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

}