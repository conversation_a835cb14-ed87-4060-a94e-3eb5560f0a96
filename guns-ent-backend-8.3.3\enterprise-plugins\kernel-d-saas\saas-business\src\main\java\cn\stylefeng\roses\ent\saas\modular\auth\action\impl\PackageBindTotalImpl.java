package cn.stylefeng.roses.ent.saas.modular.auth.action.impl;

import cn.stylefeng.roses.ent.saas.modular.auth.action.PackageBindPermissionAction;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.kernel.sys.api.entity.SysMenuOptions;
import cn.stylefeng.roses.kernel.sys.api.enums.PermissionNodeTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.menu.entity.SysMenu;
import cn.stylefeng.roses.kernel.sys.modular.menu.service.SysMenuOptionsService;
import cn.stylefeng.roses.kernel.sys.modular.menu.service.SysMenuService;
import cn.stylefeng.roses.kernel.sys.modular.role.enums.RoleLimitTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能包绑定所有的实现
 *
 * <AUTHOR>
 * @since 2024/1/22 0:52
 */
@Service
public class PackageBindTotalImpl implements PackageBindPermissionAction {

    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private SysMenuOptionsService sysMenuOptionsService;

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    @Override
    public PermissionNodeTypeEnum getPackageBindPermissionNodeType() {
        return PermissionNodeTypeEnum.TOTAL;
    }

    @Override
    public void doPackageBindPermissionAction(PackageBindPermissionRequest packageBindPermissionRequest) {
        Long packageId = packageBindPermissionRequest.getPackageId();

        // 先清空所有的角色限制
        LambdaQueryWrapper<TenantPackageAuth> tenantPackageAuthLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tenantPackageAuthLambdaQueryWrapper.eq(TenantPackageAuth::getPackageId, packageId);
        this.tenantPackageAuthService.remove(tenantPackageAuthLambdaQueryWrapper);

        // 如果是选中状态，则绑定所有的选项
        if (packageBindPermissionRequest.getChecked()) {

            // 获取所有的菜单
            List<SysMenu> totalMenus = this.sysMenuService.getTotalMenus();

            // 绑定角色的所有菜单
            List<TenantPackageAuth> tenantPackageAuths = new ArrayList<>();
            for (SysMenu menuItem : totalMenus) {
                TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
                tenantPackageAuth.setPackageId(packageId);
                tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU.getCode());
                tenantPackageAuth.setBusinessId(menuItem.getMenuId());
                tenantPackageAuths.add(tenantPackageAuth);
            }

            // 获取所有的功能
            List<SysMenuOptions> sysMenuOptionsList = sysMenuOptionsService.getTotalMenuOptionsList();

            // 绑定角色的所有菜单功能
            for (SysMenuOptions menuOptionItem : sysMenuOptionsList) {
                TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
                tenantPackageAuth.setPackageId(packageId);
                tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU_OPTIONS.getCode());
                tenantPackageAuth.setBusinessId(menuOptionItem.getMenuOptionId());
                tenantPackageAuths.add(tenantPackageAuth);
            }
            this.tenantPackageAuthService.saveBatch(tenantPackageAuths);
        }
    }

}
