<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosOrderMapper">

    <!-- 根据收银员ID查询订单 -->
    <select id="findByCashier" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder">
        SELECT * FROM pos_order
        WHERE cashier_id = #{cashierId}
        AND del_flag = 'N'
        <if test="orderStatus != null and orderStatus != ''">
            AND order_status = #{orderStatus}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND payment_status = #{paymentStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据会员ID查询订单 -->
    <select id="findByMember" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder">
        SELECT * FROM pos_order
        WHERE member_id = #{memberId}
        AND del_flag = 'N'
        <if test="orderStatus != null and orderStatus != ''">
            AND order_status = #{orderStatus}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据时间范围查询订单 -->
    <select id="findByTimeRange" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder">
        SELECT * FROM pos_order
        WHERE create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        AND del_flag = 'N'
        <if test="cashierId != null">
            AND cashier_id = #{cashierId}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            AND order_status = #{orderStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据订单号查询订单 -->
    <select id="findByOrderNo" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder">
        SELECT * FROM pos_order
        WHERE order_no = #{orderNo}
        AND del_flag = 'N'
    </select>

    <!-- 统计指定时间范围内的订单数量和金额 -->
    <select id="getOrderStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as order_count,
            COALESCE(SUM(total_amount), 0) as total_amount,
            COALESCE(SUM(discount_amount), 0) as discount_amount,
            COALESCE(SUM(final_amount), 0) as final_amount,
            order_status,
            payment_status
        FROM pos_order
        WHERE create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        AND del_flag = 'N'
        <if test="cashierId != null">
            AND cashier_id = #{cashierId}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            AND order_status = #{orderStatus}
        </if>
        GROUP BY order_status, payment_status
    </select>

    <!-- 批量更新订单状态 -->
    <update id="batchUpdateStatus">
        UPDATE pos_order 
        SET order_status = #{orderStatus},
            payment_status = #{paymentStatus},
            update_user = #{updateUser},
            update_time = NOW()
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND del_flag = 'N'
    </update>

</mapper>