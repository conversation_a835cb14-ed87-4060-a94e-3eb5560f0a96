package cn.stylefeng.roses.kernel.ca.server.core.sso;

import cn.hutool.core.bean.BeanUtil;
import cn.stylefeng.roses.kernel.ca.api.CaSessionManagerApi;
import cn.stylefeng.roses.kernel.ca.api.SsoExternalApi;
import cn.stylefeng.roses.kernel.ca.api.business.CaAccountApi;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.AccountInfo;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.request.*;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.response.SsoExternalCreateSessionResponse;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.response.SsoExternalDetectionResponse;
import cn.stylefeng.roses.kernel.ca.api.pojo.sso.CaLoginUser;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 针对于外部单点打通的api
 * <p>
 * 如果以前系统有建设单独的单点，那新业务系统和旧业务系统打通就需要本接口实现
 * <p>
 * 多单点打通的前提是多个单点的的cookie可以在同一个域名下
 *
 * <AUTHOR>
 * @date 2021/2/23 10:58
 */
@Service
public class SsoExternalApiImpl implements SsoExternalApi {

    @Resource
    private CaSessionManagerApi caSessionManagerApi;

    @Resource
    private CaAccountApi caAccountApi;

    @Override
    public SsoExternalDetectionResponse tokenDetection(SsoExternalDetectionRequest ssoExternalDetectionRequest) {

        SsoExternalDetectionResponse ssoExternalDetectionResponse = new SsoExternalDetectionResponse();

        CaLoginUser caLoginUser = caSessionManagerApi.getCaSession(ssoExternalDetectionRequest.getCaToken());
        if (caLoginUser == null) {
            ssoExternalDetectionResponse.setHaveCaSession(false);
        } else {
            ssoExternalDetectionResponse.setHaveCaSession(true);
            ssoExternalDetectionResponse.setCaLoginUser(caLoginUser);
        }

        return ssoExternalDetectionResponse;
    }

    @Override
    public void kickOff(SsoExternalKickOffRequest ssoExternalKickOffRequest) {
        caSessionManagerApi.destroySession(ssoExternalKickOffRequest.getCaToken());
    }

    @Override
    public SsoExternalCreateSessionResponse createSession(SsoExternalCreateSessionRequest ssoExternalCreateSessionRequest) {

        // 通过账号获取账号id和账号名称
        String account = ssoExternalCreateSessionRequest.getAccount();
        AccountInfo accountInfo = caAccountApi.getAccountInfo(account);

        // 创建caToken
        String caToken = IdWorker.get32UUID();

        CaLoginUser caLoginUser = new CaLoginUser();
        BeanUtil.copyProperties(accountInfo, caLoginUser);
        caLoginUser.setCaToken(caToken);

        // 创建单点登录会话
        caSessionManagerApi.createCaSession(caToken, caLoginUser);

        // 响应结果
        SsoExternalCreateSessionResponse ssoExternalCreateSessionResponse = new SsoExternalCreateSessionResponse();
        ssoExternalCreateSessionResponse.setCaToken(caToken);
        return ssoExternalCreateSessionResponse;
    }

    @Override
    public void refreshSession(SsoExternalRefreshSessionRequest ssoExternalRefreshSessionRequest) {

        // 根据caToken获取session
        CaLoginUser caLoginUser = caSessionManagerApi.getCaSession(ssoExternalRefreshSessionRequest.getCaToken());
        if (caLoginUser == null) {
            return;
        }

        // 更新会话中的内容
        caSessionManagerApi.updateSession(ssoExternalRefreshSessionRequest.getCaToken(), caLoginUser);
    }

    @Override
    public void offlineCaByAccountId(SsoExternalAccountRequest ssoExternalAccountRequest) {
        this.caSessionManagerApi.offlineCaByAccountId(ssoExternalAccountRequest.getAccountId());
    }

}
