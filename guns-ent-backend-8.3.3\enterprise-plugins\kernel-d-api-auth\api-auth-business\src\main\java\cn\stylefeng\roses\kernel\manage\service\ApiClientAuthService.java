package cn.stylefeng.roses.kernel.manage.service;

import cn.stylefeng.roses.kernel.manage.entity.ApiClientAuth;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiClientAuthRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.ApiAuthBindResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * API客户端和资源绑定关系服务类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
public interface ApiClientAuthService extends IService<ApiClientAuth> {

    /**
     * 获取api客户端绑定的接口资源的列表
     *
     * <AUTHOR>
     * @since 2023/10/25 15:23
     */
    ApiAuthBindResult getBindResult(ApiClientAuthRequest apiClientAuthRequest);

    /**
     * api客户端和资源绑定
     *
     * <AUTHOR>
     * @since 2023/10/25 18:15
     */
    void bind(ApiClientAuthRequest apiClientAuthRequest);

    /**
     * 校验客户端是否有对应资源的权限
     *
     * <AUTHOR>
     * @since 2023/10/26 15:43
     */
    boolean validateClientAuth(Long clientId, String resCode);

}
