# UniversalTree 组件测试指南

## 测试环境要求

- Node.js 16+
- 项目已安装依赖 (`npm install` 或 `yarn install`)

## 运行测试

### 1. 运行所有UniversalTree相关测试

```bash
# 在项目根目录下运行
npm run test:unit src/components/UniversalTree/__tests__/

# 或者使用 yarn
yarn test:unit src/components/UniversalTree/__tests__/
```

### 2. 运行特定测试文件

```bash
# 运行基本组件测试
npm run test:unit src/components/UniversalTree/__tests__/basic.test.js

# 运行数据适配器基本测试
npm run test:unit src/components/UniversalTree/__tests__/utils-basic.test.js

# 运行多个测试文件
npm run test:unit src/components/UniversalTree/__tests__/basic.test.js src/components/UniversalTree/__tests__/utils-basic.test.js
```

### 3. 运行测试并监听文件变化

```bash
npm run test:unit -- --watch
```

### 4. 运行测试并生成覆盖率报告

```bash
npm run test:unit -- --coverage
```

### 5. 运行特定测试用例

```bash
# 运行包含特定关键词的测试
npm run test:unit -- --grep "基本渲染"

# 运行特定的测试套件
npm run test:unit -- --grep "UniversalTree"
```

## 测试文件说明

### UniversalTree.test.js
- **基本渲染测试**: 验证组件正确渲染
- **数据加载测试**: 验证API调用和数据处理
- **懒加载功能测试**: 验证懒加载机制
- **事件处理测试**: 验证各种事件的触发和处理
- **操作按钮测试**: 验证增删改操作
- **错误处理测试**: 验证错误处理机制
- **暴露方法测试**: 验证组件暴露的公共方法

### utils.test.js
- **TreeDataAdapter 测试**: 数据格式化、搜索、路径获取等
- **TreeErrorHandler 测试**: 各种错误类型的处理
- **配置验证测试**: 配置完整性验证

### configFactory.test.js
- **预设配置测试**: 各种预设配置的创建
- **自定义配置测试**: 自定义配置的创建和合并
- **配置验证测试**: 配置有效性验证

## 测试覆盖的功能

### ✅ 组件渲染
- 基本组件结构渲染
- 条件渲染（头部、搜索框、操作按钮）
- 响应式配置更新

### ✅ 数据处理
- API数据加载
- 数据格式化和转换
- 字段映射处理
- 树结构数据处理

### ✅ 交互功能
- 节点选择和展开
- 搜索功能
- 懒加载机制
- 操作按钮交互

### ✅ 事件系统
- 所有事件的触发和参数传递
- 事件回调处理
- 自定义事件支持

### ✅ 错误处理
- 数据加载错误
- 懒加载错误
- 搜索错误
- 操作错误

### ✅ 配置系统
- 预设配置创建
- 自定义配置合并
- 配置验证

## 常见问题解决

### 1. 测试运行失败

如果测试运行失败，请检查：

```bash
# 确保依赖已安装
npm install

# 检查 Node.js 版本
node --version  # 应该是 16+

# 清理缓存
npm run test:unit -- --clearCache
```

### 2. Mock 相关错误

如果遇到 mock 相关错误，检查：
- `setup.js` 文件中的 mock 配置
- 测试文件中的 mock 导入

### 3. 路径解析错误

确保项目根目录的 `vite.config.js` 中配置了正确的路径别名：

```javascript
resolve: {
  alias: {
    '@': fileURLToPath(new URL('./src', import.meta.url))
  }
}
```

## 测试最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 "应该...当...时" 的格式

### 2. 测试结构
- 使用 `describe` 组织相关测试
- 使用 `beforeEach` 设置测试环境
- 使用 `it` 编写具体测试用例

### 3. 断言
- 使用明确的断言
- 测试预期的行为和结果
- 验证错误情况

### 4. Mock 使用
- 只 mock 必要的依赖
- 使用有意义的 mock 数据
- 验证 mock 函数的调用

## 持续集成

在 CI/CD 流程中运行测试：

```yaml
# GitHub Actions 示例
- name: Run tests
  run: npm run test:unit

- name: Generate coverage report
  run: npm run test:unit -- --coverage

- name: Upload coverage to Codecov
  uses: codecov/codecov-action@v1
```

## 测试报告

测试完成后，你会看到类似的输出：

```
✓ src/components/UniversalTree/__tests__/UniversalTree.test.js (25)
✓ src/components/UniversalTree/__tests__/utils.test.js (20)
✓ src/components/UniversalTree/__tests__/configFactory.test.js (15)

Test Files  3 passed (3)
Tests  60 passed (60)
```

如果启用了覆盖率报告，还会显示代码覆盖率信息。

## 调试测试

如果需要调试测试：

```bash
# 使用 Node.js 调试器
node --inspect-brk ./node_modules/vitest/vitest.mjs run

# 在测试中添加 debugger 语句
it('应该正确处理数据', () => {
  debugger; // 在这里暂停
  // 测试代码...
})
```