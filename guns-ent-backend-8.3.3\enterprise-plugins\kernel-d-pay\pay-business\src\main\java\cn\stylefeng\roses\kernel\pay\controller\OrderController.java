package cn.stylefeng.roses.kernel.pay.controller;

import cn.hutool.core.io.IoUtil;
import cn.stylefeng.roses.kernel.pay.api.entity.Order;
import cn.stylefeng.roses.kernel.pay.api.pojo.OrderPayRequest;
import cn.stylefeng.roses.kernel.pay.pojo.NotifyRequest;
import cn.stylefeng.roses.kernel.pay.pojo.OrderDTO;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderBuyRequest;
import cn.stylefeng.roses.kernel.pay.pojo.request.OrderRequest;
import cn.stylefeng.roses.kernel.pay.service.OrderService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 * 订单控制器
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@RestController
@ApiResource(name = "订单")
public class OrderController {

    @Resource
    private OrderService orderService;

    /**
     * 下单接口，生成订单
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @GetResource(name = "下单接口", path = "/order/buy")
    public ResponseData<Order> buy(@Validated OrderBuyRequest orderBuyRequest) {
        return new SuccessResponseData<>(orderService.createOrder(orderBuyRequest));
    }

    /**
     * 支付界面，生成支付二维码
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @GetResource(name = "支付接口", path = "/order/pay")
    public ResponseData<String> pay(@Validated OrderPayRequest orderPayRequest) {
        return new SuccessResponseData<>(orderService.pay(orderPayRequest));
    }

    /**
     * 聚合支付，支付成功回调
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @ApiResource(name = "聚合支付，支付成功回调", path = "/order/theNotify",
            method = {RequestMethod.GET, RequestMethod.POST}, requiredLogin = false)
    public void mergeNotify(NotifyRequest notifyRequest, HttpServletResponse response) {

        orderService.payNotify(notifyRequest);

        PrintWriter printWriter = null;
        try {
            response.setContentType("text/plain;charset=utf-8");
            printWriter = response.getWriter();
            printWriter.write("SUCCESS");
            printWriter.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(printWriter);
        }
    }

    /**
     * 查询订单状态
     * <p>
     * 用在轮训查询订单状态
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @GetResource(name = "查询订单状态", path = "/order/getOrderStatus")
    public ResponseData<Integer> getOrderStatus(@Validated(OrderRequest.orderStatus.class)
                                                OrderRequest orderRequest) {
        Integer orderStatus = orderService.queryOrderStatus(orderRequest);
        return new SuccessResponseData<>(orderStatus);
    }

    /**
     * 获取订单列表
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @GetResource(name = "获取订单列表", path = "/order/getUserOrderList")
    public ResponseData<List<OrderDTO>> getUserOrderList(OrderRequest orderRequest) {
        return new SuccessResponseData<>(orderService.findUserOrderList(orderRequest));
    }

    /**
     * 取消订单
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @PostResource(name = "取消订单", path = "/order/cancelOrder")
    public ResponseData<?> cancelOrder(@RequestBody @Validated(OrderRequest.detail.class)
                                       OrderRequest orderRequest) {
        orderService.cancelOrder(orderRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除订单
     *
     * <AUTHOR>
     * @date 2021/06/23 22:28
     */
    @PostResource(name = "删除订单", path = "/order/deleteOrder", requiredPermission = false)
    public ResponseData<?> deleteOrder(@RequestBody @Validated(OrderRequest.detail.class) OrderRequest orderRequest) {
        orderService.deleteOrder(orderRequest);
        return new SuccessResponseData<>();
    }

}
