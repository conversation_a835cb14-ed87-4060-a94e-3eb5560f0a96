package cn.stylefeng.roses.kernel.manage.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API客户端和资源绑定关系实例类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@TableName("ent_api_client_auth")
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiClientAuth extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "api_client_resource_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键id")
    private Long apiClientResourceId;

    /**
     * api客户端id
     */
    @TableField("api_client_id")
    @ChineseDescription("api客户端id")
    private Long apiClientId;

    /**
     * 资源编码，与sys_resource表编码对应
     */
    @TableField("resource_code")
    @ChineseDescription("资源编码，与sys_resource表编码对应")
    private String resourceCode;

}
