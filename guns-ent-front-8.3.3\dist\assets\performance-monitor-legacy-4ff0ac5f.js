System.register(["./index-legacy-ee1db0c7.js","./constants-legacy-2a31d63c.js"],(function(e,t){"use strict";var r,n,s,i;return{setters:[e=>{r=e.aJ,n=e.m,s=e.aV},e=>{i=e.N}],execute:function(){e("i",R);const o={NETWORK_ERROR:"NETWORK_ERROR",TIMEOUT_ERROR:"TIMEOUT_ERROR",CONNECTION_ERROR:"CONNECTION_ERROR",INSUFFICIENT_INVENTORY:"INSUFFICIENT_INVENTORY",INVALID_MEMBER:"INVALID_MEMBER",MEMBER_EXPIRED:"MEMBER_EXPIRED",MEMBER_INACTIVE:"MEMBER_INACTIVE",PAYMENT_FAILED:"PAYMENT_FAILED",PAYMENT_TIMEOUT:"PAYMENT_TIMEOUT",PAYMENT_CANCELLED:"PAYMENT_CANCELLED",ORDER_NOT_FOUND:"ORDER_NOT_FOUND",PRODUCT_NOT_FOUND:"PRODUCT_NOT_FOUND",CATEGORY_NOT_FOUND:"CATEGORY_NOT_FOUND",INVALID_QUANTITY:"INVALID_QUANTITY",INVALID_PRICE:"INVALID_PRICE",INVALID_AMOUNT:"INVALID_AMOUNT",INVALID_DISCOUNT:"INVALID_DISCOUNT",INVALID_POINTS:"INVALID_POINTS",EMPTY_CART:"EMPTY_CART",CART_ITEM_LIMIT_EXCEEDED:"CART_ITEM_LIMIT_EXCEEDED",INVALID_BARCODE:"INVALID_BARCODE",INVALID_CARD_NO:"INVALID_CARD_NO",INVALID_PHONE:"INVALID_PHONE",PERMISSION_DENIED:"PERMISSION_DENIED",UNAUTHORIZED:"UNAUTHORIZED",ACCESS_FORBIDDEN:"ACCESS_FORBIDDEN",SYSTEM_ERROR:"SYSTEM_ERROR",SYSTEM_MAINTENANCE:"SYSTEM_MAINTENANCE",DATABASE_ERROR:"DATABASE_ERROR",SERVICE_UNAVAILABLE:"SERVICE_UNAVAILABLE",INVALID_CONFIG:"INVALID_CONFIG",MISSING_CONFIG:"MISSING_CONFIG",FILE_NOT_FOUND:"FILE_NOT_FOUND",FILE_UPLOAD_FAILED:"FILE_UPLOAD_FAILED",FILE_SIZE_EXCEEDED:"FILE_SIZE_EXCEEDED",CACHE_ERROR:"CACHE_ERROR",CACHE_EXPIRED:"CACHE_EXPIRED",CONCURRENT_MODIFICATION:"CONCURRENT_MODIFICATION",RESOURCE_LOCKED:"RESOURCE_LOCKED",BUSINESS_RULE_VIOLATION:"BUSINESS_RULE_VIOLATION",OPERATION_NOT_ALLOWED:"OPERATION_NOT_ALLOWED",TIME_RESTRICTION:"TIME_RESTRICTION",UNKNOWN_ERROR:"UNKNOWN_ERROR"},E={[o.NETWORK_ERROR]:"网络连接失败，请检查网络设置",[o.TIMEOUT_ERROR]:"请求超时，请稍后重试",[o.CONNECTION_ERROR]:"连接服务器失败，请检查网络连接",[o.INSUFFICIENT_INVENTORY]:"商品库存不足",[o.INVALID_MEMBER]:"会员信息无效或不存在",[o.MEMBER_EXPIRED]:"会员卡已过期",[o.MEMBER_INACTIVE]:"会员账户已被冻结或停用",[o.PAYMENT_FAILED]:"支付失败，请重试",[o.PAYMENT_TIMEOUT]:"支付超时，请重新发起支付",[o.PAYMENT_CANCELLED]:"支付已取消",[o.ORDER_NOT_FOUND]:"订单不存在",[o.PRODUCT_NOT_FOUND]:"商品不存在",[o.CATEGORY_NOT_FOUND]:"商品分类不存在",[o.INVALID_QUANTITY]:"商品数量必须大于0",[o.INVALID_PRICE]:"商品价格格式不正确",[o.INVALID_AMOUNT]:"金额格式不正确",[o.INVALID_DISCOUNT]:"折扣金额不能超过商品总价",[o.INVALID_POINTS]:"积分数量格式不正确",[o.EMPTY_CART]:"购物车为空，无法结算",[o.CART_ITEM_LIMIT_EXCEEDED]:"购物车商品数量超过限制",[o.INVALID_BARCODE]:"商品条码格式不正确",[o.INVALID_CARD_NO]:"卡号格式不正确",[o.INVALID_PHONE]:"手机号码格式不正确",[o.PERMISSION_DENIED]:"权限不足，无法执行此操作",[o.UNAUTHORIZED]:"用户未登录或登录已过期",[o.ACCESS_FORBIDDEN]:"访问被拒绝",[o.SYSTEM_ERROR]:"系统错误，请联系管理员",[o.SYSTEM_MAINTENANCE]:"系统正在维护中，请稍后再试",[o.DATABASE_ERROR]:"数据库连接错误",[o.SERVICE_UNAVAILABLE]:"服务暂时不可用",[o.INVALID_CONFIG]:"系统配置错误",[o.MISSING_CONFIG]:"缺少必要的系统配置",[o.FILE_NOT_FOUND]:"文件不存在",[o.FILE_UPLOAD_FAILED]:"文件上传失败",[o.FILE_SIZE_EXCEEDED]:"文件大小超过限制",[o.CACHE_ERROR]:"缓存操作失败",[o.CACHE_EXPIRED]:"缓存已过期",[o.CONCURRENT_MODIFICATION]:"数据已被其他用户修改，请刷新后重试",[o.RESOURCE_LOCKED]:"资源被锁定，请稍后重试",[o.BUSINESS_RULE_VIOLATION]:"违反业务规则",[o.OPERATION_NOT_ALLOWED]:"当前状态下不允许此操作",[o.TIME_RESTRICTION]:"当前时间不允许此操作",[o.UNKNOWN_ERROR]:"未知错误，请重试"},a={LOW:"LOW",MEDIUM:"MEDIUM",HIGH:"HIGH",CRITICAL:"CRITICAL"},c={[o.INVALID_QUANTITY]:a.LOW,[o.INVALID_PRICE]:a.LOW,[o.INVALID_AMOUNT]:a.LOW,[o.INVALID_DISCOUNT]:a.LOW,[o.INVALID_POINTS]:a.LOW,[o.EMPTY_CART]:a.LOW,[o.INVALID_BARCODE]:a.LOW,[o.INVALID_CARD_NO]:a.LOW,[o.INVALID_PHONE]:a.LOW,[o.INSUFFICIENT_INVENTORY]:a.MEDIUM,[o.INVALID_MEMBER]:a.MEDIUM,[o.MEMBER_EXPIRED]:a.MEDIUM,[o.MEMBER_INACTIVE]:a.MEDIUM,[o.PAYMENT_FAILED]:a.MEDIUM,[o.PAYMENT_TIMEOUT]:a.MEDIUM,[o.ORDER_NOT_FOUND]:a.MEDIUM,[o.PRODUCT_NOT_FOUND]:a.MEDIUM,[o.CATEGORY_NOT_FOUND]:a.MEDIUM,[o.CART_ITEM_LIMIT_EXCEEDED]:a.MEDIUM,[o.BUSINESS_RULE_VIOLATION]:a.MEDIUM,[o.OPERATION_NOT_ALLOWED]:a.MEDIUM,[o.TIME_RESTRICTION]:a.MEDIUM,[o.NETWORK_ERROR]:a.HIGH,[o.TIMEOUT_ERROR]:a.HIGH,[o.CONNECTION_ERROR]:a.HIGH,[o.PERMISSION_DENIED]:a.HIGH,[o.UNAUTHORIZED]:a.HIGH,[o.ACCESS_FORBIDDEN]:a.HIGH,[o.INVALID_CONFIG]:a.HIGH,[o.MISSING_CONFIG]:a.HIGH,[o.CONCURRENT_MODIFICATION]:a.HIGH,[o.RESOURCE_LOCKED]:a.HIGH,[o.SYSTEM_ERROR]:a.CRITICAL,[o.SYSTEM_MAINTENANCE]:a.CRITICAL,[o.DATABASE_ERROR]:a.CRITICAL,[o.SERVICE_UNAVAILABLE]:a.CRITICAL,[o.CACHE_ERROR]:a.CRITICAL,[o.UNKNOWN_ERROR]:a.MEDIUM};function I(e,t=""){return t||E[e]||E[o.UNKNOWN_ERROR]}function R(e){return[o.NETWORK_ERROR,o.TIMEOUT_ERROR,o.CONNECTION_ERROR,o.SERVICE_UNAVAILABLE,o.DATABASE_ERROR,o.CACHE_ERROR,o.RESOURCE_LOCKED].includes(e)}function N(e){return![o.SYSTEM_ERROR,o.DATABASE_ERROR,o.CACHE_ERROR,o.INVALID_CONFIG,o.MISSING_CONFIG].includes(e)}e("P",class{static wrapApiCall(e,n={}){const{showMessage:s=!0,showNotification:i=!1,context:o="操作",onError:E,retryOptions:a={}}=n;return async(...n)=>{try{if(a.maxRetries>0){const{RetryHandler:s}=await r((()=>t.import("./retry-handler-legacy-f881a188.js")),void 0);return await s.withRetry((()=>e(...n)),a)}return await e(...n)}catch(c){const e=this.processError(c,o);throw e.userFacing&&(s&&this.showErrorMessage(e),i&&this.showErrorNotification(e,o)),this.logError(e,o),"function"==typeof E&&E(e),e}}}static processError(e,t=""){if(e&&e.type&&e.severity)return e;let r=o.UNKNOWN_ERROR,n="",s={};if(e instanceof Error)n=e.message,s={name:e.name,stack:e.stack},r=this.inferErrorType(e);else if("object"==typeof e&&null!==e)if(e.response){const{status:t,data:i}=e.response;s={status:t,data:i},t>=500?r=o.SYSTEM_ERROR:404===t?r=o.PRODUCT_NOT_FOUND:401===t?r=o.UNAUTHORIZED:403===t?r=o.ACCESS_FORBIDDEN:400===t&&(r=this.inferErrorTypeFromResponse(i)),n=(null==i?void 0:i.message)||e.message||`HTTP ${t} 错误`}else e.code?(r=e.code,n=e.message||"",s=e.details||{}):n=e.message||JSON.stringify(e);else n=String(e);return function(e,t="",r={}){return{type:e,message:t||I(e),severity:(n=e,c[n]||a.MEDIUM),retryable:R(e),userFacing:N(e),timestamp:(new Date).toISOString(),details:r};var n}(r,n,{...s,context:t,originalError:e})}static inferErrorType(e){const t=e.message.toLowerCase();return t.includes("network")||t.includes("网络")?o.NETWORK_ERROR:t.includes("timeout")||t.includes("超时")?o.TIMEOUT_ERROR:t.includes("connection")||t.includes("连接")?o.CONNECTION_ERROR:t.includes("inventory")||t.includes("库存")?o.INSUFFICIENT_INVENTORY:t.includes("member")||t.includes("会员")?o.INVALID_MEMBER:t.includes("payment")||t.includes("支付")?o.PAYMENT_FAILED:t.includes("permission")||t.includes("权限")?o.PERMISSION_DENIED:o.UNKNOWN_ERROR}static inferErrorTypeFromResponse(e){if(!e)return o.UNKNOWN_ERROR;const{code:t,message:r=""}=e;if(t&&Object.values(o).includes(t))return t;const n=r.toLowerCase();return n.includes("quantity")||n.includes("数量")?o.INVALID_QUANTITY:n.includes("price")||n.includes("价格")?o.INVALID_PRICE:n.includes("amount")||n.includes("金额")?o.INVALID_AMOUNT:n.includes("cart")||n.includes("购物车")?o.EMPTY_CART:o.UNKNOWN_ERROR}static showErrorMessage(e){const t=this.getMessageType(e.severity);n[t](e.message,3)}static showErrorNotification(e,t){const r=this.getNotificationType(e.severity);s[r]({message:`${t}失败`,description:e.message,duration:this.getNotificationDuration(e.severity),placement:"topRight"})}static getMessageType(e){return"LOW"===e?"warning":"error"}static getNotificationType(e){return"LOW"===e?"warning":"error"}static getNotificationDuration(e){switch(e){case"LOW":return 3;case"MEDIUM":default:return 5;case"HIGH":return 8;case"CRITICAL":return 0}}static logError(e,t){const r=this.getLogLevel(e.severity),n=`[POS Error] ${t}: ${e.message}`,s={type:e.type,message:e.message,severity:e.severity,context:t,timestamp:e.timestamp,details:e.details};switch(r){case"warn":console.warn(n,s);break;case"error":console.error(n,s);break;default:console.log(n,s)}this.sendToLogService(e,t)}static getLogLevel(e){switch(e){case"LOW":default:return"info";case"MEDIUM":return"warn";case"HIGH":case"CRITICAL":return"error"}}static sendToLogService(e,t){if("HIGH"===e.severity||"CRITICAL"===e.severity)try{window.logService&&"function"==typeof window.logService.captureException&&window.logService.captureException(e,{tags:{context:t,severity:e.severity,type:e.type},extra:e.details})}catch(r){console.warn("发送错误日志失败:",r)}}static createErrorBoundary(e){return(t,r)=>{const s=this.processError(t,`组件 ${e}`);n.error("页面出现错误，请刷新后重试"),this.logError({...s,details:{...s.details,componentStack:r.componentStack,errorBoundary:!0}},`组件 ${e} 错误边界`)}}static globalErrorHandler(e,t="global"){const r=this.processError(e,t);"CRITICAL"===r.severity&&s.error({message:"系统错误",description:"系统遇到严重错误，请刷新页面或联系管理员",duration:0,placement:"topRight"}),this.logError(r,t)}static unhandledRejectionHandler(e){const t=e.reason,r=this.processError(t,"unhandled promise rejection");e.preventDefault(),this.logError(r,"unhandled promise rejection"),r.userFacing&&n.error(r.message)}}),e("a",class{static init(){window.posMetrics||(window.posMetrics=[]),this.setupPerformanceObserver(),this.setupMemoryMonitor(),this.setupVisibilityMonitor()}static measureComponentRender(e,t){const r=performance.now(),n=`${e}-render-start`,s=`${e}-render-end`,i=`${e}-render`;performance.mark(n);try{const o=t();return o&&"function"==typeof o.then?o.then((t=>(this.completeRenderMeasurement(e,r,n,s,i),t))).catch((t=>{throw this.completeRenderMeasurement(e,r,n,s,i,t),t})):(this.completeRenderMeasurement(e,r,n,s,i),o)}catch(o){throw this.completeRenderMeasurement(e,r,n,s,i,o),o}}static completeRenderMeasurement(e,t,r,n,s,o=null){const E=performance.now()-t;performance.mark(n),performance.measure(s,r,n),this.recordMetric("component_render",{component:e,renderTime:E,timestamp:Date.now(),status:o?"error":"success",error:o?o.message:null});const a=i.PERFORMANCE_THRESHOLDS.COMPONENT_RENDER_TIME;E>a&&(console.warn(`组件 ${e} 渲染时间过长: ${E.toFixed(2)}ms (阈值: ${a}ms)`),this.recordMetric("performance_warning",{type:"slow_render",component:e,renderTime:E,threshold:a,timestamp:Date.now()}))}static measureApiCall(e,t){return async(...r)=>{const n=performance.now(),s=`${e}-api-start`,o=`${e}-api-end`,E=`${e}-api`;performance.mark(s);try{const a=await t(...r),c=performance.now()-n;performance.mark(o),performance.measure(E,s,o),this.recordMetric("api_call",{api:e,duration:c,status:"success",timestamp:Date.now(),args:this.sanitizeArgs(r)});const I=i.PERFORMANCE_THRESHOLDS.API_CALL_TIME;return c>I&&(console.warn(`API ${e} 调用时间过长: ${c.toFixed(2)}ms (阈值: ${I}ms)`),this.recordMetric("performance_warning",{type:"slow_api",api:e,duration:c,threshold:I,timestamp:Date.now()})),a}catch(a){const t=performance.now()-n;throw performance.mark(o),performance.measure(E,s,o),this.recordMetric("api_call",{api:e,duration:t,status:"error",error:a.message,timestamp:Date.now(),args:this.sanitizeArgs(r)}),a}}}static sanitizeArgs(e){return e.map((e=>{if("object"==typeof e&&null!==e){const t={...e};return["password","token","cardNo","phone","idCard"].forEach((e=>{t[e]&&(t[e]="***")})),t}return e}))}static recordMetric(e,t){window.posMetrics||(window.posMetrics=[]);const r={type:e,...t,id:this.generateMetricId(),userAgent:navigator.userAgent,url:window.location.href};window.posMetrics.push(r),window.posMetrics.length>1e3&&(window.posMetrics=window.posMetrics.slice(-500)),this.sendMetricToService(r)}static generateMetricId(){return`${Date.now()}-${Math.random().toString(36).substr(2,9)}`}static sendMetricToService(e){if(["performance_warning","api_call","component_render"].includes(e.type))try{window.monitoringService&&"function"==typeof window.monitoringService.track&&window.monitoringService.track("pos_performance",e)}catch(t){console.warn("发送性能指标失败:",t)}}static getMetrics(e={}){if(!window.posMetrics)return[];let t=[...window.posMetrics];return e.type&&(t=t.filter((t=>t.type===e.type))),e.component&&(t=t.filter((t=>t.component===e.component))),e.api&&(t=t.filter((t=>t.api===e.api))),e.startTime&&e.endTime&&(t=t.filter((t=>t.timestamp>=e.startTime&&t.timestamp<=e.endTime))),t}static getPerformanceStats(e){const t=this.getMetrics({type:e});if(0===t.length)return{count:0,average:0,min:0,max:0,p95:0,p99:0};const r=t.map((e=>e.renderTime||e.duration||0)).sort(((e,t)=>e-t)),n=r.reduce(((e,t)=>e+t),0),s=r.length;return{count:s,average:n/s,min:r[0],max:r[s-1],p95:r[Math.floor(.95*s)],p99:r[Math.floor(.99*s)]}}static clearMetrics(){window.posMetrics&&(window.posMetrics=[]),performance.clearMarks&&performance.clearMarks(),performance.clearMeasures&&performance.clearMeasures()}static setupPerformanceObserver(){if("PerformanceObserver"in window)try{new PerformanceObserver((e=>{e.getEntries().forEach((e=>{"navigation"===e.entryType&&this.recordMetric("navigation",{domContentLoaded:e.domContentLoadedEventEnd-e.domContentLoadedEventStart,loadComplete:e.loadEventEnd-e.loadEventStart,firstPaint:e.responseEnd-e.requestStart,timestamp:Date.now()})}))})).observe({entryTypes:["navigation"]}),new PerformanceObserver((e=>{e.getEntries().forEach((e=>{e.duration>1e3&&this.recordMetric("resource_load",{name:e.name,duration:e.duration,size:e.transferSize,timestamp:Date.now()})}))})).observe({entryTypes:["resource"]});const e=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{this.recordMetric("long_task",{duration:e.duration,startTime:e.startTime,timestamp:Date.now()})}))}));"longtask"in PerformanceObserver.supportedEntryTypes&&e.observe({entryTypes:["longtask"]})}catch(e){console.warn("设置性能观察器失败:",e)}}static setupMemoryMonitor(){setInterval((()=>{if(performance.memory){const t={used:performance.memory.usedJSHeapSize,total:performance.memory.totalJSHeapSize,limit:performance.memory.jsHeapSizeLimit},r=t.used/t.limit*100;this.recordMetric("memory_usage",{...t,usagePercent:r,timestamp:Date.now()});const n=i.PERFORMANCE_THRESHOLDS.MEMORY_USAGE_PERCENT;if(r>n&&(console.warn(`内存使用率过高: ${r.toFixed(2)}% (阈值: ${n}%)`),this.recordMetric("performance_warning",{type:"high_memory_usage",usagePercent:r,threshold:n,timestamp:Date.now()}),window.gc&&"function"==typeof window.gc))try{window.gc()}catch(e){console.warn("手动垃圾回收失败:",e)}}}),3e4)}static setupVisibilityMonitor(){let e=Date.now();document.addEventListener("visibilitychange",(()=>{const t=Date.now();if(document.hidden){const r=t-e;this.recordMetric("page_visibility",{event:"hidden",visibleDuration:r,timestamp:t})}else e=t,this.recordMetric("page_visibility",{event:"visible",timestamp:t})}))}static generateReport(e={}){const{timeRange:t=36e5,includeDetails:r=!1}=e,n=Date.now(),s=n-t,i=this.getMetrics({startTime:s,endTime:n}),o={timeRange:{startTime:s,endTime:n},summary:{totalMetrics:i.length,componentRenders:i.filter((e=>"component_render"===e.type)).length,apiCalls:i.filter((e=>"api_call"===e.type)).length,warnings:i.filter((e=>"performance_warning"===e.type)).length},performance:{componentRender:this.getPerformanceStats("component_render"),apiCall:this.getPerformanceStats("api_call")},warnings:i.filter((e=>"performance_warning"===e.type)),memory:this.getLatestMemoryUsage()};return r&&(o.details=i),o}static getLatestMemoryUsage(){const e=this.getMetrics({type:"memory_usage"});return e.length>0?e[e.length-1]:null}static exportMetrics(e="json"){const t=this.getMetrics();if("csv"===e){const e=["type","timestamp","component","api","duration","status"];return[e.join(","),...t.map((t=>e.map((e=>t[e]||"")).join(",")))].join("\n")}return JSON.stringify(t,null,2)}})}}}));
