package cn.stylefeng.roses.kernel.pay.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.pay.api.entity.Goods;
import cn.stylefeng.roses.kernel.pay.enums.GoodsExceptionEnum;
import cn.stylefeng.roses.kernel.pay.mapper.GoodsMapper;
import cn.stylefeng.roses.kernel.pay.pojo.request.GoodsRequest;
import cn.stylefeng.roses.kernel.pay.service.GoodsService;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品信息业务实现层
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@Service
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, Goods> implements GoodsService {

    @Override
    public Goods detail(GoodsRequest goodsRequest) {
        return this.queryGoods(goodsRequest);
    }

    @Override
    public List<Goods> findList(GoodsRequest goodsRequest) {
        LambdaQueryWrapper<Goods> wrapper = this.createWrapper(goodsRequest);
        return this.list(wrapper);
    }

    /**
     * 获取信息
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    private Goods queryGoods(GoodsRequest goodsRequest) {
        Goods goods = this.getById(goodsRequest.getGoodsId());
        if (ObjectUtil.isEmpty(goods)) {
            throw new ServiceException(GoodsExceptionEnum.GOODS_NOT_EXISTED);
        }
        return goods;
    }

    /**
     * 创建查询wrapper
     *
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    private LambdaQueryWrapper<Goods> createWrapper(GoodsRequest goodsRequest) {
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();

        // 按顺序正序排列
        queryWrapper.orderByAsc(Goods::getFldSort);

        // 只查询上架的商品
        queryWrapper.eq(Goods::getGoodsStatus, StatusEnum.ENABLE.getCode());

        if (goodsRequest == null) {
            return queryWrapper;
        }

        // 根据商品id查询
        Long goodsId = goodsRequest.getGoodsId();
        queryWrapper.eq(ObjectUtil.isNotNull(goodsId), Goods::getGoodsId, goodsId);

        // 根据商品名称查询
        String searchText = goodsRequest.getSearchText();
        queryWrapper.like(ObjectUtil.isNotEmpty(searchText), Goods::getGoodsName, searchText);

        return queryWrapper;
    }

}
