import{_ as H,ac as W,ad as X,ae as Z,af as j,r as m,s as G,L as J,o as Q,ag as q,k as g,a as z,c as $,d as l,w as a,g as r,t as T,b as ee,ah as te,f as ae,h as le,m as f,l as oe,u as ne,W as re,J as se,a6 as de,B as ie,n as ce,H as ue,a0 as _e,U as me,ai as pe,i as fe,z as ve,A as ge,$ as he,M as Re}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import{I as M}from"./InventoryAlertRecordApi-19b95203.js";import Se from"./AlertRecordDetail-997a2a22.js";import ye from"./AlertRecordProcess-a80d30be.js";/* empty css              */const Te={name:"InventoryAlertRecordIndex",components:{SearchOutlined:W,ReloadOutlined:X,CheckOutlined:Z,DownloadOutlined:j,AlertRecordDetail:Se,AlertRecordProcess:ye},setup(){const w=m(),e=m(!1),x=m([]),t=m([]),I=m(!1),C=m(!1),h=m(!1),d=m({}),s=m("RESOLVED"),v=m(""),p=G({productName:"",alertType:void 0,alertLevel:void 0,handleStatus:void 0,alertTimeRange:[]}),c=G({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:n=>"\u5171 ".concat(n," \u6761\u8BB0\u5F55")}),u=[{title:"\u5546\u54C1\u540D\u79F0",dataIndex:"productName",key:"productName",width:150},{title:"\u9884\u8B66\u7C7B\u578B",dataIndex:"alertType",key:"alertType",width:100,slots:{customRender:"alertType"}},{title:"\u9884\u8B66\u7EA7\u522B",dataIndex:"alertLevel",key:"alertLevel",width:100,slots:{customRender:"alertLevel"}},{title:"\u5F53\u524D\u5E93\u5B58",dataIndex:"currentStock",key:"currentStock",width:100,slots:{customRender:"currentStock"}},{title:"\u9608\u503C",dataIndex:"thresholdValue",key:"thresholdValue",width:80},{title:"\u9884\u8B66\u6D88\u606F",dataIndex:"alertMessage",key:"alertMessage",width:200,ellipsis:!0},{title:"\u5904\u7406\u72B6\u6001",dataIndex:"handleStatus",key:"handleStatus",width:100,slots:{customRender:"handleStatus"}},{title:"\u9884\u8B66\u65F6\u95F4",dataIndex:"alertTime",key:"alertTime",width:150,slots:{customRender:"alertTime"}},{title:"\u64CD\u4F5C",key:"action",width:150,fixed:"right",slots:{customRender:"action"}}],b={selectedRowKeys:t,onChange:n=>{t.value=n},getCheckboxProps:n=>({disabled:n.handleStatus!=="PENDING"})},R=J(()=>t.value.length>0),i=async()=>{try{e.value=!0;const n={current:c.current,size:c.pageSize,...p};p.alertTimeRange&&p.alertTimeRange.length===2&&(n.startTime=p.alertTimeRange[0].format("YYYY-MM-DD"),n.endTime=p.alertTimeRange[1].format("YYYY-MM-DD"));const _=await M.page(n);x.value=_.data.records,c.total=_.data.total}catch(n){console.error("\u83B7\u53D6\u9884\u8B66\u8BB0\u5F55\u5931\u8D25:",n),f.error("\u83B7\u53D6\u9884\u8B66\u8BB0\u5F55\u5931\u8D25")}finally{e.value=!1}},S=()=>{c.current=1,i()},E=()=>{w.value.resetFields(),c.current=1,i()},N=()=>{i()},y=n=>{c.current=n.current,c.pageSize=n.pageSize,i()},D=n=>{d.value=n,I.value=!0},P=n=>{d.value=n,C.value=!0},k=()=>{C.value=!1,i()},L=async n=>{try{await M.delete({id:n.id}),f.success("\u5220\u9664\u6210\u529F"),i()}catch(_){console.error("\u5220\u9664\u5931\u8D25:",_),f.error("\u5220\u9664\u5931\u8D25")}},V=()=>{if(t.value.length===0){f.warning("\u8BF7\u9009\u62E9\u8981\u5904\u7406\u7684\u8BB0\u5F55");return}h.value=!0},A=async()=>{try{await M.batchHandle({idList:t.value,handleType:s.value,handleRemark:v.value}),f.success("\u6279\u91CF\u5904\u7406\u6210\u529F"),h.value=!1,t.value=[],i()}catch(n){console.error("\u6279\u91CF\u5904\u7406\u5931\u8D25:",n),f.error("\u6279\u91CF\u5904\u7406\u5931\u8D25")}},F=()=>{f.info("\u5BFC\u51FA\u529F\u80FD\u5F00\u53D1\u4E2D...")},Y=n=>({LOW_STOCK:"orange",ZERO_STOCK:"red",OVERSTOCK:"purple",EXPIRY:"volcano"})[n]||"default",o=n=>({LOW_STOCK:"\u5E93\u5B58\u4E0D\u8DB3",ZERO_STOCK:"\u96F6\u5E93\u5B58",OVERSTOCK:"\u5E93\u5B58\u79EF\u538B",EXPIRY:"\u4E34\u671F\u9884\u8B66"})[n]||n,O=n=>({CRITICAL:"red",WARNING:"orange",INFO:"blue"})[n]||"default",K=n=>({CRITICAL:"\u7D27\u6025",WARNING:"\u8B66\u544A",INFO:"\u63D0\u9192"})[n]||n,U=n=>({PENDING:"orange",RESOLVED:"green",IGNORED:"gray"})[n]||"default",B=n=>({PENDING:"\u5F85\u5904\u7406",RESOLVED:"\u5DF2\u89E3\u51B3",IGNORED:"\u5DF2\u5FFD\u7565"})[n]||n;return Q(()=>{i()}),{searchFormRef:w,loading:e,dataSource:x,selectedRowKeys:t,detailVisible:I,processVisible:C,batchProcessVisible:h,currentRecord:d,batchProcessType:s,batchProcessRemark:v,searchForm:p,pagination:c,columns:u,rowSelection:b,hasSelected:R,dayjs:q,handleSearch:S,handleReset:E,handleRefresh:N,handleTableChange:y,handleView:D,handleProcess:P,handleProcessSuccess:k,handleDelete:L,handleBatchProcess:V,handleBatchProcessConfirm:A,handleExport:F,getAlertTypeColor:Y,getAlertTypeText:o,getAlertLevelColor:O,getAlertLevelText:K,getHandleStatusColor:U,getHandleStatusText:B}}},Ce={class:"inventory-alert-record-container"};function be(w,e,x,t,I,C){const h=oe,d=ne,s=re,v=se,p=de,c=g("SearchOutlined"),u=ie,b=g("ReloadOutlined"),R=ce,i=ue,S=_e,E=g("CheckOutlined"),N=g("DownloadOutlined"),y=me,D=pe,P=fe,k=ve,L=ge,V=he,A=Re,F=g("AlertRecordDetail"),Y=g("AlertRecordProcess");return z(),$("div",Ce,[l(S,{bordered:!1,class:"search-card"},{default:a(()=>[l(i,{ref:"searchFormRef",model:t.searchForm,layout:"inline",class:"search-form"},{default:a(()=>[l(d,{label:"\u5546\u54C1\u540D\u79F0",name:"productName"},{default:a(()=>[l(h,{value:t.searchForm.productName,"onUpdate:value":e[0]||(e[0]=o=>t.searchForm.productName=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0","allow-clear":"",onPressEnter:t.handleSearch},null,8,["value","onPressEnter"])]),_:1}),l(d,{label:"\u9884\u8B66\u7C7B\u578B",name:"alertType"},{default:a(()=>[l(v,{value:t.searchForm.alertType,"onUpdate:value":e[1]||(e[1]=o=>t.searchForm.alertType=o),placeholder:"\u8BF7\u9009\u62E9\u9884\u8B66\u7C7B\u578B","allow-clear":"",style:{width:"150px"}},{default:a(()=>[l(s,{value:"LOW_STOCK"},{default:a(()=>e[10]||(e[10]=[r("\u5E93\u5B58\u4E0D\u8DB3")])),_:1,__:[10]}),l(s,{value:"ZERO_STOCK"},{default:a(()=>e[11]||(e[11]=[r("\u96F6\u5E93\u5B58")])),_:1,__:[11]}),l(s,{value:"OVERSTOCK"},{default:a(()=>e[12]||(e[12]=[r("\u5E93\u5B58\u79EF\u538B")])),_:1,__:[12]}),l(s,{value:"EXPIRY"},{default:a(()=>e[13]||(e[13]=[r("\u4E34\u671F\u9884\u8B66")])),_:1,__:[13]})]),_:1},8,["value"])]),_:1}),l(d,{label:"\u9884\u8B66\u7EA7\u522B",name:"alertLevel"},{default:a(()=>[l(v,{value:t.searchForm.alertLevel,"onUpdate:value":e[2]||(e[2]=o=>t.searchForm.alertLevel=o),placeholder:"\u8BF7\u9009\u62E9\u9884\u8B66\u7EA7\u522B","allow-clear":"",style:{width:"120px"}},{default:a(()=>[l(s,{value:"CRITICAL"},{default:a(()=>e[14]||(e[14]=[r("\u7D27\u6025")])),_:1,__:[14]}),l(s,{value:"WARNING"},{default:a(()=>e[15]||(e[15]=[r("\u8B66\u544A")])),_:1,__:[15]}),l(s,{value:"INFO"},{default:a(()=>e[16]||(e[16]=[r("\u63D0\u9192")])),_:1,__:[16]})]),_:1},8,["value"])]),_:1}),l(d,{label:"\u5904\u7406\u72B6\u6001",name:"handleStatus"},{default:a(()=>[l(v,{value:t.searchForm.handleStatus,"onUpdate:value":e[3]||(e[3]=o=>t.searchForm.handleStatus=o),placeholder:"\u8BF7\u9009\u62E9\u5904\u7406\u72B6\u6001","allow-clear":"",style:{width:"120px"}},{default:a(()=>[l(s,{value:"PENDING"},{default:a(()=>e[17]||(e[17]=[r("\u5F85\u5904\u7406")])),_:1,__:[17]}),l(s,{value:"RESOLVED"},{default:a(()=>e[18]||(e[18]=[r("\u5DF2\u89E3\u51B3")])),_:1,__:[18]}),l(s,{value:"IGNORED"},{default:a(()=>e[19]||(e[19]=[r("\u5DF2\u5FFD\u7565")])),_:1,__:[19]})]),_:1},8,["value"])]),_:1}),l(d,{label:"\u9884\u8B66\u65F6\u95F4",name:"alertTimeRange"},{default:a(()=>[l(p,{value:t.searchForm.alertTimeRange,"onUpdate:value":e[4]||(e[4]=o=>t.searchForm.alertTimeRange=o),format:"YYYY-MM-DD",placeholder:["\u5F00\u59CB\u65E5\u671F","\u7ED3\u675F\u65E5\u671F"]},null,8,["value"])]),_:1}),l(d,null,{default:a(()=>[l(R,null,{default:a(()=>[l(u,{type:"primary",onClick:t.handleSearch},{icon:a(()=>[l(c)]),default:a(()=>[e[20]||(e[20]=r(" \u641C\u7D22 "))]),_:1,__:[20]},8,["onClick"]),l(u,{onClick:t.handleReset},{icon:a(()=>[l(b)]),default:a(()=>[e[21]||(e[21]=r(" \u91CD\u7F6E "))]),_:1,__:[21]},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(S,{bordered:!1,class:"action-card"},{default:a(()=>[l(R,null,{default:a(()=>[l(u,{type:"primary",disabled:!t.hasSelected,onClick:t.handleBatchProcess},{icon:a(()=>[l(E)]),default:a(()=>[e[22]||(e[22]=r(" \u6279\u91CF\u5904\u7406 "))]),_:1,__:[22]},8,["disabled","onClick"]),l(u,{onClick:t.handleExport},{icon:a(()=>[l(N)]),default:a(()=>[e[23]||(e[23]=r(" \u5BFC\u51FA "))]),_:1,__:[23]},8,["onClick"]),l(u,{onClick:t.handleRefresh},{icon:a(()=>[l(b)]),default:a(()=>[e[24]||(e[24]=r(" \u5237\u65B0 "))]),_:1,__:[24]},8,["onClick"])]),_:1})]),_:1}),l(S,{bordered:!1,class:"table-card"},{default:a(()=>[l(P,{columns:t.columns,"data-source":t.dataSource,loading:t.loading,pagination:t.pagination,"row-selection":t.rowSelection,scroll:{x:1200},onChange:t.handleTableChange},{alertType:a(({record:o})=>[l(y,{color:t.getAlertTypeColor(o.alertType)},{default:a(()=>[r(T(t.getAlertTypeText(o.alertType)),1)]),_:2},1032,["color"])]),alertLevel:a(({record:o})=>[l(y,{color:t.getAlertLevelColor(o.alertLevel)},{default:a(()=>[r(T(t.getAlertLevelText(o.alertLevel)),1)]),_:2},1032,["color"])]),currentStock:a(({record:o})=>[ee("span",{style:te({color:o.currentStock<=o.thresholdValue?"#f5222d":"#52c41a"})},T(o.currentStock),5)]),handleStatus:a(({record:o})=>[l(y,{color:t.getHandleStatusColor(o.handleStatus)},{default:a(()=>[r(T(t.getHandleStatusText(o.handleStatus)),1)]),_:2},1032,["color"])]),alertTime:a(({record:o})=>[r(T(t.dayjs(o.alertTime).format("YYYY-MM-DD HH:mm:ss")),1)]),action:a(({record:o})=>[l(R,null,{default:a(()=>[l(u,{type:"link",size:"small",onClick:O=>t.handleView(o)},{default:a(()=>e[25]||(e[25]=[r(" \u67E5\u770B ")])),_:2,__:[25]},1032,["onClick"]),o.handleStatus==="PENDING"?(z(),ae(u,{key:0,type:"link",size:"small",onClick:O=>t.handleProcess(o)},{default:a(()=>e[26]||(e[26]=[r(" \u5904\u7406 ")])),_:2,__:[26]},1032,["onClick"])):le("",!0),l(D,{title:"\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u6761\u9884\u8B66\u8BB0\u5F55\u5417\uFF1F",onConfirm:O=>t.handleDelete(o)},{default:a(()=>[l(u,{type:"link",size:"small",danger:""},{default:a(()=>e[27]||(e[27]=[r(" \u5220\u9664 ")])),_:1,__:[27]})]),_:2},1032,["onConfirm"])]),_:2},1024)]),_:1},8,["columns","data-source","loading","pagination","row-selection","onChange"])]),_:1}),l(A,{visible:t.batchProcessVisible,"onUpdate:visible":e[7]||(e[7]=o=>t.batchProcessVisible=o),title:"\u6279\u91CF\u5904\u7406\u9884\u8B66\u8BB0\u5F55",onOk:t.handleBatchProcessConfirm},{default:a(()=>[l(i,{"label-col":{span:6},"wrapper-col":{span:16}},{default:a(()=>[l(d,{label:"\u5904\u7406\u64CD\u4F5C"},{default:a(()=>[l(L,{value:t.batchProcessType,"onUpdate:value":e[5]||(e[5]=o=>t.batchProcessType=o)},{default:a(()=>[l(k,{value:"RESOLVED"},{default:a(()=>e[28]||(e[28]=[r("\u6807\u8BB0\u4E3A\u5DF2\u89E3\u51B3")])),_:1,__:[28]}),l(k,{value:"IGNORED"},{default:a(()=>e[29]||(e[29]=[r("\u6807\u8BB0\u4E3A\u5DF2\u5FFD\u7565")])),_:1,__:[29]})]),_:1},8,["value"])]),_:1}),l(d,{label:"\u5904\u7406\u5907\u6CE8"},{default:a(()=>[l(V,{value:t.batchProcessRemark,"onUpdate:value":e[6]||(e[6]=o=>t.batchProcessRemark=o),placeholder:"\u8BF7\u8F93\u5165\u5904\u7406\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["visible","onOk"]),l(F,{visible:t.detailVisible,"onUpdate:visible":e[8]||(e[8]=o=>t.detailVisible=o),"record-data":t.currentRecord},null,8,["visible","record-data"]),l(Y,{visible:t.processVisible,"onUpdate:visible":e[9]||(e[9]=o=>t.processVisible=o),"record-data":t.currentRecord,onSuccess:t.handleProcessSuccess},null,8,["visible","record-data","onSuccess"])])}const Ye=H(Te,[["render",be],["__scopeId","data-v-ed5e37d3"]]);export{Ye as default};
