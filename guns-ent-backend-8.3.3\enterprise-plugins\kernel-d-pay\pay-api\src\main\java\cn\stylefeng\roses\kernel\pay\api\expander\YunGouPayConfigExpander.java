package cn.stylefeng.roses.kernel.pay.api.expander;


import cn.stylefeng.roses.kernel.config.api.context.ConfigContext;

/**
 * YunGouOS支付配置
 * <p>
 * 提供获取支付相关配置的方法
 *
 * <AUTHOR>
 * @since 2024/6/20 16:52
 */
public class YunGouPayConfigExpander {

    /**
     * 获取微信商户id
     *
     * @return 微信商户id
     */
    public static String getYunGouWxShopId() {
        return ConfigContext.me().getConfigValue("YUNGOU_WX_SHOP_ID", String.class);
    }

    /**
     * 获取支付宝商户id
     *
     * @return 支付宝商户id
     */
    public static String getYunGouAliShopId() {
        return ConfigContext.me().getConfigValue("YUNGOU_ALI_SHOP_ID", String.class);
    }

    /**
     * 获取聚合支付商户id
     *
     * @return 聚合支付商户id
     */
    public static String getYunGouMergePayShopId() {
        return ConfigContext.me().getConfigValue("YUNGOU_MERGE_PAY_SHOP_ID", String.class);
    }

    /**
     * 获取聚合支付的url
     *
     * @return 聚合支付的url
     */
    public static String getYunGouMergePayUrl() {
        return ConfigContext.me().getConfigValue("YUNGOU_MERGE_PAY_URL", String.class);
    }

    /**
     * 获取聚合支付回调地址（异步回调）
     *
     * @return 聚合支付回调地址（异步回调）
     */
    public static String getYunGouMergePayNotifyUrl() {
        return ConfigContext.me().getConfigValue("YUNGOU_MERGE_PAY_NOTIFY_URL", String.class);
    }

    /**
     * 获取聚合支付成功跳转地址
     *
     * @return 聚合支付成功跳转地址
     */
    public static String getYunGouMergePayReturnUrl() {
        return ConfigContext.me().getConfigValue("YUNGOU_MERGE_PAY_RETURN_URL", String.class);
    }

    /**
     * 获取聚合支付秘钥
     *
     * @return 聚合支付秘钥
     */
    public static String getYunGouMergePaySecret() {
        return ConfigContext.me().getConfigValue("YUNGOU_MERGE_PAY_SECRET", String.class);
    }

    /**
     * 获取支付宝商户密钥
     *
     * @return 支付宝商户密钥
     */
    public static String getYunGouPayAliKey() {
        return ConfigContext.me().getConfigValue("YUNGOU_PAY_ALI_KEY", String.class);
    }

    /**
     * 获取微信商户密钥
     *
     * @return 微信商户密钥
     */
    public static String getYunGouPayWxKey() {
        return ConfigContext.me().getConfigValue("YUNGOU_PAY_WX_KEY", String.class);
    }

}