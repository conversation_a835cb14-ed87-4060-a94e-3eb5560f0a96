<template>
  <a-slider v-model:value="dataValue" style="width: 100%;" :disabled="props.readonly || props.disabled" :min="0" :max="100" @change="dataValueChange" />
</template>

<script setup name="GunsProgress">
import { ref, onMounted, watch } from 'vue';

defineOptions({
  name: 'GunsProgress'
});

const props = defineProps({
  value: {
    type: Number,
    default: 0
  },
  record: {
    type: Object,
    default: {}
  },
  //是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:value', 'change', 'onChange']);

// 选中的值
const dataValue = ref(0);

onMounted(() => {
  dataValue.value = props.value;
});

watch(
  () => props.value,
  () => {
    dataValue.value = props.value;
  }
);

// 更改值
const dataValueChange = () => {
  emits('update:value', dataValue.value);
  emits('change', dataValue.value);
  emits('onChange', props.record);
};
</script>

<style></style>
