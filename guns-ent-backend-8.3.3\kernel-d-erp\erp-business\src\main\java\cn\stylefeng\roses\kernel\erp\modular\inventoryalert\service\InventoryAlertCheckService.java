package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.service;

import cn.hutool.core.collection.CollUtil;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertRule;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryAlertRecordRequest;
import cn.stylefeng.roses.kernel.erp.modular.inventory.mapper.InventoryMapper;
import cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper.InventoryAlertRuleMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 库存预警检查服务
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
@Slf4j
@Service
public class InventoryAlertCheckService {

    @Resource
    private InventoryAlertRuleMapper ruleMapper;
    
    @Resource
    private InventoryAlertRecordService recordService;
    
    @Resource
    private InventoryMapper inventoryMapper;

    /**
     * 执行库存预警检查
     */
    @Async
    public void executeAlertCheck() {
        log.info("开始执行库存预警检查");
        
        try {
            // 获取所有启用的预警规则
            List<InventoryAlertRule> rules = ruleMapper.selectEnabledRules();
            
            for (InventoryAlertRule rule : rules) {
                checkRuleAlert(rule);
                // 更新规则的最后检查时间
                ruleMapper.updateLastCheckTime(rule.getId());
            }
            
            log.info("库存预警检查完成，共检查{}条规则", rules.size());
        } catch (Exception e) {
            log.error("库存预警检查异常", e);
        }
    }

    /**
     * 检查单个规则的预警
     */
    private void checkRuleAlert(InventoryAlertRule rule) {
        try {
            switch (rule.getRuleType()) {
                case "LOW_STOCK":
                    checkLowStockAlert(rule);
                    break;
                case "ZERO_STOCK":
                    checkZeroStockAlert(rule);
                    break;
                case "OVERSTOCK":
                    checkOverstockAlert(rule);
                    break;
                case "EXPIRY":
                    checkExpiryAlert(rule);
                    break;
                default:
                    log.warn("未知的预警类型: {}", rule.getRuleType());
            }
        } catch (Exception e) {
            log.error("检查预警规则异常，规则ID：{}", rule.getId(), e);
        }
    }

    /**
     * 检查低库存预警
     */
    private void checkLowStockAlert(InventoryAlertRule rule) {
        List<Inventory> inventories = getInventoriesByRule(rule);
        
        for (Inventory inventory : inventories) {
            if (isLowStock(inventory, rule)) {
                String message = String.format("商品库存不足，当前库存：%s，预警阈值：%s", 
                    inventory.getCurrentStock(), rule.getThresholdValue());
                createAlertRecord(rule, inventory, message);
            }
        }
    }

    /**
     * 检查零库存预警
     */
    private void checkZeroStockAlert(InventoryAlertRule rule) {
        List<Inventory> inventories = getInventoriesByRule(rule);
        
        for (Inventory inventory : inventories) {
            if (inventory.getCurrentStock().compareTo(BigDecimal.ZERO) == 0) {
                String message = String.format("商品零库存，请立即补货");
                createAlertRecord(rule, inventory, message);
            }
        }
    }

    /**
     * 检查库存积压预警
     */
    private void checkOverstockAlert(InventoryAlertRule rule) {
        List<Inventory> inventories = getInventoriesByRule(rule);
        
        for (Inventory inventory : inventories) {
            if (isOverstock(inventory, rule)) {
                String message = String.format("商品库存积压，当前库存：%s，最大库存：%s", 
                    inventory.getCurrentStock(), inventory.getMaxStock());
                createAlertRecord(rule, inventory, message);
            }
        }
    }

    /**
     * 检查临期预警
     */
    private void checkExpiryAlert(InventoryAlertRule rule) {
        // TODO: 实现临期预警检查逻辑
        // 需要根据商品的保质期和入库时间计算是否临期
        log.debug("临期预警检查功能待实现");
    }

    /**
     * 根据规则获取库存数据
     */
    private List<Inventory> getInventoriesByRule(InventoryAlertRule rule) {
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        
        if ("PRODUCT".equals(rule.getTargetType())) {
            wrapper.eq(Inventory::getProductId, rule.getTargetId());
        } else if ("CATEGORY".equals(rule.getTargetType())) {
            // 查询指定分类下的所有商品库存
            List<Long> productIds = getProductIdsByCategory(rule.getTargetId());
            if (CollUtil.isNotEmpty(productIds)) {
                wrapper.in(Inventory::getProductId, productIds);
            } else {
                return CollUtil.newArrayList(); // 返回空列表
            }
        }
        // ALL类型不需要额外条件
        
        return inventoryMapper.selectList(wrapper);
    }

    /**
     * 根据分类ID获取商品ID列表
     */
    private List<Long> getProductIdsByCategory(Long categoryId) {
        // TODO: 实现根据分类ID查询商品ID列表的逻辑
        return CollUtil.newArrayList();
    }

    /**
     * 判断是否为低库存
     */
    private boolean isLowStock(Inventory inventory, InventoryAlertRule rule) {
        BigDecimal currentStock = inventory.getCurrentStock();
        BigDecimal threshold = rule.getThresholdValue();
        String comparisonOperator = rule.getComparisonOperator();
        
        if ("QUANTITY".equals(rule.getThresholdType())) {
            return compareValues(currentStock, threshold, comparisonOperator);
        } else if ("PERCENTAGE".equals(rule.getThresholdType())) {
            BigDecimal minStock = inventory.getMinStock();
            if (minStock != null && minStock.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal percentage = currentStock.divide(minStock, 4, RoundingMode.HALF_UP)
                                                  .multiply(new BigDecimal("100"));
                return compareValues(percentage, threshold, comparisonOperator);
            }
        }
        
        return false;
    }

    /**
     * 判断是否为库存积压
     */
    private boolean isOverstock(Inventory inventory, InventoryAlertRule rule) {
        BigDecimal currentStock = inventory.getCurrentStock();
        BigDecimal maxStock = inventory.getMaxStock();
        
        if (maxStock == null || maxStock.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        BigDecimal threshold = rule.getThresholdValue();
        String comparisonOperator = rule.getComparisonOperator();
        
        if ("PERCENTAGE".equals(rule.getThresholdType())) {
            BigDecimal percentage = currentStock.divide(maxStock, 4, RoundingMode.HALF_UP)
                                              .multiply(new BigDecimal("100"));
            return compareValues(percentage, threshold, comparisonOperator);
        } else if ("QUANTITY".equals(rule.getThresholdType())) {
            BigDecimal overstockThreshold = maxStock.add(threshold);
            return currentStock.compareTo(overstockThreshold) >= 0;
        }
        
        return false;
    }

    /**
     * 比较数值
     */
    private boolean compareValues(BigDecimal value1, BigDecimal value2, String operator) {
        int comparison = value1.compareTo(value2);
        
        switch (operator) {
            case "LTE":
                return comparison <= 0;
            case "LT":
                return comparison < 0;
            case "GTE":
                return comparison >= 0;
            case "GT":
                return comparison > 0;
            case "EQ":
                return comparison == 0;
            default:
                return false;
        }
    }

    /**
     * 创建预警记录
     */
    private void createAlertRecord(InventoryAlertRule rule, Inventory inventory, String message) {
        // 检查是否已存在未处理的相同预警
        if (recordService.hasUnresolvedAlert(rule.getId(), inventory.getProductId())) {
            return;
        }
        
        InventoryAlertRecordRequest request = new InventoryAlertRecordRequest();
        request.setRuleId(rule.getId());
        request.setProductId(inventory.getProductId());
        request.setAlertType(rule.getRuleType());
        request.setAlertLevel(rule.getAlertLevel());
        request.setCurrentStock(inventory.getCurrentStock());
        request.setThresholdValue(rule.getThresholdValue());
        request.setAlertMessage(message);
        request.setStatus("PENDING");
        request.setAlertTime(new Date());
        
        recordService.add(request);
        
        // TODO: 发送通知
        // sendNotification(rule, request);
    }
}
