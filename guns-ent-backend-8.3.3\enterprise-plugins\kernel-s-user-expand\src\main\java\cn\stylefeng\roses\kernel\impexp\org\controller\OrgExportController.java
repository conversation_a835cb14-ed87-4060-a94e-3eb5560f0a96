package cn.stylefeng.roses.kernel.impexp.org.controller;

import cn.stylefeng.roses.kernel.impexp.org.pojo.OrgExportRequest;
import cn.stylefeng.roses.kernel.impexp.org.service.OrgExportService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 组织机构导出的控制器
 *
 * <AUTHOR>
 * @since 2024-02-18 15:39
 */
@RestController
@ApiResource(name = "组织机构导出的控制器")
@Slf4j
public class OrgExportController {

    @Resource
    private OrgExportService orgExportService;

    /**
     * 组织机构导出
     *
     * <AUTHOR>
     * @since 2024-02-18 15:52
     */
    @GetResource(name = "组织机构导出业务", path = "/org/exportOrg")
    public ResponseData<?> exportOrg(OrgExportRequest orgExportRequest) {
        orgExportService.exportOrg(orgExportRequest);
        return new SuccessResponseData<>();
    }

}
