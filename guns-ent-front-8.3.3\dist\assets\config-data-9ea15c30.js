import{R as r,_ as R,r as n,a as w,f as x,w as u,d as _,cg as k,v as I,G as D,S as F}from"./index-18a1ea24.js";/* empty css              */import{T as f}from"./ThemeTemplateFieldApi-b2a7ece4.js";class m{static add(t){return r.post("/sysThemeTemplateRel/add",t)}static del(t){return r.post("/sysThemeTemplateRel/del",t)}}const N={__name:"config-data",setup(v,{expose:t}){const l=n(""),c=n(!1),s=n([]),d=n([]),o=n(null);t({openConfig:a=>{l.value=a,p()}});const p=async()=>{c.value=!0;let a=await f.findNotRelList({templateId:l.value});if(o.value=await f.findRelList({templateId:l.value}),s.value=[],d.value=[],a!=null)for(const e of a)s.value.push({key:e.fieldCode,title:e.fieldName});if(o.value!=null)for(const e of o.value)s.value.push({key:e.fieldCode,title:e.fieldName}),d.value.push(e.fieldCode);c.value=!1},g=async(a,e,i)=>{o.value=a,e==="left"?await m.del({templateId:l.value,fieldCodes:i}):await m.add({templateId:l.value,fieldCodes:i}),p()};return(a,e)=>{const i=k,h=I,C=D,y=F;return w(),x(y,{spinning:c.value},{default:u(()=>[_(C,{class:"myTransfer"},{default:u(()=>[_(h,{span:24},{default:u(()=>[_(i,{"data-source":s.value,titles:["\u672A\u9009","\u5DF2\u9009"],"target-keys":d.value,render:T=>T.title,onChange:g},null,8,["data-source","target-keys","render"])]),_:1})]),_:1})]),_:1},8,["spinning"])}}},S=R(N,[["__scopeId","data-v-5cf457ab"]]);export{S as default};
