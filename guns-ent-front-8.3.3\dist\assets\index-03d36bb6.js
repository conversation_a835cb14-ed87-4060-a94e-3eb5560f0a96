import{_ as H}from"./index-02bf6f00.js";import{r as u,L as b,N as J,o as Q,k as j,a as v,c as I,b as n,d as e,w as t,t as M,aR as K,O as f,Q as g,g as w,aS as X,h as C,f as T,m as N,M as Z,E as ee,n as te,B as ae,I as oe,l as le,V as ne,al as se,u as ue,v as ie,W as re,J as de,G as ce,H as _e}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{O as pe,a as me}from"./operate-log-detail-2f4d0ab7.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */const ve={class:"guns-layout"},fe={class:"guns-layout-content"},ge={class:"guns-layout"},he={class:"guns-layout-content-application"},xe={class:"content-mian"},be={class:"content-mian-header"},we={class:"header-content"},Ce={class:"header-content-left"},De={class:"header-content-right"},ke={class:"content-mian-body"},ye={class:"table-content"},Se={class:"super-search",style:{"margin-top":"8px"}},Ye=["onClick"],Re={__name:"index",setup(Ie){const B=u([{key:"index",title:"\u5E8F\u53F7",width:60,align:"center",isShow:!0,hideInSetting:!0},{title:"\u6267\u884C\u63A5\u53E3",isShow:!0,dataIndex:"requestUrl"},{title:"\u5177\u4F53\u6D88\u606F",isShow:!0,dataIndex:"logContent"},{title:"\u64CD\u4F5C\u7528\u6237",isShow:!0,dataIndex:"userIdWrapper"},{title:"\u516C\u53F8\u540D\u79F0",isShow:!0,dataIndex:"userCurrentOrgIdWrapper"},{title:"\u670D\u52A1\u540D\u79F0",isShow:!0,dataIndex:"appName"},{title:"\u65F6\u95F4",isShow:!0,dataIndex:"createTime"},{title:"\u64CD\u4F5C",key:"action",isShow:!0,width:60}]),D=u(null),o=u({searchText:"",beginDate:"",endDate:"",appName:null}),k=u(null),i=u(!1),r=u(!1),O=b(()=>({xxl:7,xl:7,lg:5,md:7,sm:4})),U=b(()=>({xxl:17,xl:17,lg:19,md:17,sm:20})),d=b(()=>J()?{xxl:6,xl:8,lg:12,md:24,sm:24,xs:24}:{xxl:6,xl:8,lg:24,md:24,sm:24,xs:24});Q(()=>{});const E=()=>{r.value=!r.value},s=()=>{D.value.reload()},L=()=>{o.value.searchText="",o.value.beginDate="",o.value.endDate="",o.value.appName=null,s()},z=()=>{if(!o.value.beginDate||!o.value.endDate||!o.value.appName){N.error("\u6E05\u7A7A\u65E5\u5FD7\u9700\u8981\u586B\u5199\u5F00\u59CB\u65F6\u95F4\uFF0C\u7ED3\u675F\u65F6\u95F4\u4EE5\u53CAapp\u540D\u79F0");return}Z.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u6E05\u7A7A\u6307\u5B9A\u65E5\u671F\u7684\u64CD\u4F5C\u65E5\u5FD7\u5417?",icon:e(ee),maskClosable:!0,onOk:async()=>{const c=await me.delete(o.value);N.success(c.message),s()}})},y=c=>{k.value=c,i.value=!0};return(c,a)=>{const _=te,P=j("delete-outlined"),h=ae,S=oe,R=le,V=ne,Y=se,p=ue,m=ie,q=re,F=de,$=ce,A=_e,W=H;return v(),I("div",ve,[n("div",fe,[n("div",ge,[n("div",he,[n("div",xe,[n("div",be,[n("div",we,[n("div",Ce,[e(_,{size:16})]),n("div",De,[e(_,{size:16},{default:t(()=>[e(h,{danger:"",onClick:a[0]||(a[0]=l=>z())},{icon:t(()=>[e(P)]),default:t(()=>[a[6]||(a[6]=n("span",null,"\u6E05\u7A7A\u65E5\u5FD7",-1))]),_:1,__:[6]})]),_:1})])])]),n("div",ke,[n("div",ye,[e(W,{columns:B.value,where:o.value,rowId:"logId",ref_key:"tableRef",ref:D,rowSelection:!1,url:"/logManager/page",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"OPERATE_LOG_TABLE"},{toolLeft:t(()=>[e(R,{value:o.value.searchText,"onUpdate:value":a[1]||(a[1]=l=>o.value.searchText=l),placeholder:"\u63A5\u53E3\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:s,class:"search-input",bordered:!1},{prefix:t(()=>[e(S,{iconClass:"icon-opt-search"})]),_:1},8,["value"]),e(V,{type:"vertical",class:"divider"}),n("a",{onClick:E},M(r.value?"\u6536\u8D77":"\u9AD8\u7EA7\u7B5B\u9009"),1)]),toolBottom:t(()=>[K(n("div",Se,[e(A,{model:o.value,labelCol:O.value,"wrapper-col":U.value},{default:t(()=>[e($,{gutter:16},{default:t(()=>[e(m,f(g(d.value)),{default:t(()=>[e(p,{label:"\u5F00\u59CB\u65F6\u95F4:"},{default:t(()=>[e(Y,{value:o.value.beginDate,"onUpdate:value":a[2]||(a[2]=l=>o.value.beginDate=l),format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",onChange:s,style:{width:"100%"},placeholder:"\u5F00\u59CB\u65F6\u95F4"},null,8,["value"])]),_:1})]),_:1},16),e(m,f(g(d.value)),{default:t(()=>[e(p,{label:"\u7ED3\u675F\u65F6\u95F4:"},{default:t(()=>[e(Y,{value:o.value.endDate,"onUpdate:value":a[3]||(a[3]=l=>o.value.endDate=l),format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",onChange:s,style:{width:"100%"},placeholder:"\u7ED3\u675F\u65F6\u95F4"},null,8,["value"])]),_:1})]),_:1},16),e(m,f(g(d.value)),{default:t(()=>[e(p,{label:"\u670D\u52A1\u540D\u79F0:"},{default:t(()=>[e(F,{value:o.value.appName,"onUpdate:value":a[4]||(a[4]=l=>o.value.appName=l),style:{width:"100%"},placeholder:"\u670D\u52A1\u540D\u79F0",onChange:s},{default:t(()=>[e(q,{value:"guns"},{default:t(()=>a[7]||(a[7]=[w("guns")])),_:1,__:[7]})]),_:1},8,["value"])]),_:1})]),_:1},16),e(m,f(g(d.value)),{default:t(()=>[e(p,{label:" ",class:"not-label"},{default:t(()=>[e(_,{size:16},{default:t(()=>[e(h,{class:"border-radius",onClick:s,type:"primary"},{default:t(()=>a[8]||(a[8]=[w("\u67E5\u8BE2")])),_:1,__:[8]}),e(h,{class:"border-radius",onClick:L},{default:t(()=>a[9]||(a[9]=[w("\u91CD\u7F6E")])),_:1,__:[9]})]),_:1})]),_:1})]),_:1},16)]),_:1})]),_:1},8,["model","labelCol","wrapper-col"])],512),[[X,r.value]])]),bodyCell:t(({column:l,record:x})=>[l.dataIndex=="requestUrl"?(v(),I("a",{key:0,onClick:G=>y(x)},M(x.requestUrl),9,Ye)):C("",!0),l.key==="action"?(v(),T(_,{key:1},{default:t(()=>[e(S,{iconClass:"icon-opt-xiangqing","font-size":"24px",title:"\u8BE6\u60C5",color:"#60666b",onClick:G=>y(x)},null,8,["onClick"])]),_:2},1024)):C("",!0)]),_:1},8,["columns","where"])])])])])])]),i.value?(v(),T(pe,{key:0,visible:i.value,"onUpdate:visible":a[5]||(a[5]=l=>i.value=l),data:k.value},null,8,["visible","data"])):C("",!0)])}}};export{Re as default};
