package cn.stylefeng.roses.kernel.erp.modular.region.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpRegion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 区域管理Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/20 13:00
 */
public interface ErpRegionMapper extends BaseMapper<ErpRegion> {

    /**
     * 统计指定区域关联的供应商数量
     *
     * @param regionId 区域ID
     * @return 供应商数量
     */
    @Select("SELECT COUNT(1) FROM erp_supplier WHERE region_id = #{regionId} AND del_flag = 'N'")
    Long countSupplierByRegionId(@Param("regionId") Long regionId);

    /**
     * 统计指定区域关联的客户数量
     *
     * @param regionId 区域ID
     * @return 客户数量
     */
    @Select("SELECT COUNT(1) FROM erp_customer WHERE region_id = #{regionId} AND del_flag = 'N'")
    Long countCustomerByRegionId(@Param("regionId") Long regionId);

    /**
     * 统计指定区域在供应商-区域关联表中的数量
     *
     * @param regionId 区域ID
     * @return 关联数量
     */
    @Select("SELECT COUNT(1) FROM erp_supplier_region WHERE region_id = #{regionId}")
    Long countSupplierRegionByRegionId(@Param("regionId") Long regionId);

    /**
     * 统计指定区域在客户-区域关联表中的数量
     *
     * @param regionId 区域ID
     * @return 关联数量
     */
    @Select("SELECT COUNT(1) FROM erp_customer_region WHERE region_id = #{regionId}")
    Long countCustomerRegionByRegionId(@Param("regionId") Long regionId);
}
