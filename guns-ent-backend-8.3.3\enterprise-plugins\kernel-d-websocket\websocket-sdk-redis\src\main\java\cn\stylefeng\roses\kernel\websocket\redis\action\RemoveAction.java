package cn.stylefeng.roses.kernel.websocket.redis.action;


import cn.stylefeng.roses.kernel.websocket.api.WebSocketManagerApi;
import com.alibaba.fastjson.JSONObject;

/**
 * 订阅Redis中的删除会话的消息
 *
 * <AUTHOR>
 * @since 2024-01-15 15:38
 */
public class RemoveAction implements Action {

    @Override
    public void doMessage(WebSocketManagerApi manager, JSONObject object) {
        if (!object.containsKey(IDENTIFIER)) {
            return;
        }

        // 删除具体标识的会话
        String identifier = object.getString(IDENTIFIER);
        manager.remove(identifier);
    }

}
