import{r,L as y,o as B,R as P,bd as D,X as g,a as b,c as _,f as N,at as U,as as F,d as I,w as x,a0 as O}from"./index-18a1ea24.js";/* empty css              *//* empty css              */const V={style:{width:"100%"}},j={__name:"index",props:{value:{type:[String,Array],default:void 0},record:{type:Object,default:{}},formData:{type:Object,default:{}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},readonly:{type:Boolean,default:!1},isDesgin:{type:Boolean,default:!1},normal:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},dataSources:{type:Number,default:1},backendUrl:{type:String,default:""},valueType:{type:Number,default:1},dataList:{type:Array,default:[]}},emits:["update:value","onChange"],setup(n,{emit:h}){const e=n,v=h,o=r([]),u=r(""),m=y(()=>{var a;return!((a=e.record)!=null&&a.itemMultipleChoiceFlag||e.multiple)}),t=r(m.value?"":[]),c=y(()=>{var a,l;return(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.dataFromType?e.record.formItemSelect.dataFromType:e.dataSources?e.dataSources:1}),i=y(()=>{var a,l,d,s;return(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.valueType?(s=(d=e.record)==null?void 0:d.formItemSelect)==null?void 0:s.valueType:e.valueType?e.valueType:1}),p=y(()=>{var a,l,d,s;return(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.backendUrl?(s=(d=e.record)==null?void 0:d.formItemSelect)==null?void 0:s.backendUrl:e.backendUrl?e.backendUrl:""});B(()=>{var a,l;(l=(a=e.record)==null?void 0:a.formItemSelect)!=null&&l.linkFieldCode&&(u.value=e.record.formItemSelect.linkFieldCode),e.dataSources==2&&(o.value=e.dataList),f(),w()});const f=a=>{if(e.isDesgin||c.value!=1||!p.value)return;let l={searchText:a};u.value&&(l[u.value]=e.formData[u.value],l.valueType=i.value),P.get(p.value,l).then(d=>{e.backendUrl?o.value=d.data:e.record.dictList=d.data})},k=D(a=>{f(a)},500),C=(a,l)=>!a||l.name.toLowerCase().indexOf(a.toLowerCase())!==-1,T=()=>{var l;let a="";m.value?a=t.value:a=e.normal?t.value:((l=t.value)==null?void 0:l.length)===0?"":JSON.stringify(t.value),v("update:value",a),v("onChange",e.record)},w=()=>{m.value?t.value=e.value:t.value=e.value?e.normal?e.value:JSON.parse(e.value):[]};return g(()=>e.formData[u.value],a=>{a&&f()},{deep:!0}),g(()=>e.value,a=>{w()},{deep:!0}),g(()=>{var a;return(a=e.record)==null?void 0:a.dictList},a=>{a&&(o.value=a)},{deep:!0,immediate:!0}),(a,l)=>{var s,L;const d=F;return b(),_("div",V,[m.value?(b(),N(d,{key:1,"show-search":"","allow-clear":"",value:t.value,"onUpdate:value":l[1]||(l[1]=S=>t.value=S),style:{width:"100%"},"tree-data":o.value,onChange:T,disabled:n.readonly||n.disabled,placeholder:n.placeholder,multiple:!1,"tree-node-filter-prop":"name",onSearch:U(k),filterTreeNode:c.value==1?!1:C,fieldNames:{children:"children",label:"name",key:i.value==1?"id":"code",value:i.value==1?"id":"code"}},null,8,["value","tree-data","disabled","placeholder","onSearch","filterTreeNode","fieldNames"])):(b(),N(d,{key:0,"show-search":"","allow-clear":"",value:t.value,"onUpdate:value":l[0]||(l[0]=S=>t.value=S),style:{width:"100%"},"tree-data":o.value,onChange:T,"tree-checkable":!0,disabled:n.readonly||n.disabled,placeholder:n.placeholder,multiple:!0,"tree-node-filter-prop":"name",onSearch:U(k),treeCheckStrictly:!((L=(s=e.record)==null?void 0:s.formItemSelect)!=null&&L.treeCheckStrictly),showCheckedStrategy:"SHOW_ALL",filterTreeNode:c.value==1?!1:C,fieldNames:{children:"children",label:"name",key:i.value==1?"id":"code",value:i.value==1?"id":"code"}},null,8,["value","tree-data","disabled","placeholder","onSearch","treeCheckStrictly","filterTreeNode","fieldNames"]))])}}},A={class:"guns-body guns-body-card"},M={__name:"index",setup(n){const h=r(null),e=r(null),v=r(!1),o=r(!1),u=r("\u8BF7\u9009\u62E9"),m=r([{id:"1798280342488690689",name:"\u5317\u4EAC\u603B\u90E8",code:"bj",parentId:"-1",children:[{id:"1798280342488690711",name:"\u5317\u4EAC\u603B\u90E81\u90E8\u95E8",code:"bj1",parentId:"1798280342488690689",children:null,nodeId:"1798280342488690711",nodeParentId:"1798280342488690689"},{id:"1798280342488690720",name:"\u5317\u4EAC\u603B\u90E82\u90E8\u95E8",code:"bj2",parentId:"1798280342488690689",children:null,nodeId:"1798280342488690720",nodeParentId:"1798280342488690689"}],nodeId:"1798280342488690689",nodeParentId:"-1"},{id:"1798280342488690696",name:"\u4E0A\u6D77\u5206\u516C\u53F8",code:"sh",parentId:"-1",children:[{id:"1798280342488690731",name:"\u4E0A\u6D77\u5206\u516C\u53F81\u90E8\u95E8",code:"sh1",parentId:"1798280342488690696",children:null,nodeId:"1798280342488690731",nodeParentId:"1798280342488690696"},{id:"1798280342488690742",name:"\u4E0A\u6D77\u5206\u516C\u53F82\u90E8\u95E8",code:"sh2",parentId:"1798280342488690696",children:null,nodeId:"1798280342488690742",nodeParentId:"1798280342488690696"}],nodeId:"1798280342488690696",nodeParentId:"-1"},{id:"1798280342488690704",name:"\u5E7F\u5DDE\u5206\u516C\u53F8",code:"gz",parentId:"-1",children:[{id:"1798280342488690756",name:"\u5E7F\u5DDE\u5206\u516C\u53F81\u90E8\u95E8",code:"gz1",parentId:"1798280342488690704",children:null,nodeId:"1798280342488690756",nodeParentId:"1798280342488690704"},{id:"1798280342488690769",name:"\u5E7F\u5DDE\u5206\u516C\u53F82\u90E8\u95E8",code:"gz2",parentId:"1798280342488690704",children:null,nodeId:"1798280342488690769",nodeParentId:"1798280342488690704"}],nodeId:"1798280342488690704",nodeParentId:"-1"}]);return(t,c)=>{const i=j,p=O;return b(),_("div",A,[I(p,{title:"\u6811\u5F62\u9009\u62E9-\u81EA\u5B9A\u4E49\u586B\u5145\u6570\u636E",bordered:!1},{default:x(()=>[I(i,{value:h.value,"onUpdate:value":c[0]||(c[0]=f=>h.value=f),disabled:v.value,readonly:o.value,dataList:m.value,dataSources:2,multiple:!1,placeholder:u.value,style:{width:"300px"}},null,8,["value","disabled","readonly","dataList","placeholder"])]),_:1}),I(p,{title:"\u6811\u5F62\u9009\u62E9-\u52A8\u6001\u63A5\u53E3\uFF08\u53EA\u652F\u6301get\uFF09",bordered:!1},{default:x(()=>[I(i,{value:e.value,"onUpdate:value":c[1]||(c[1]=f=>e.value=f),disabled:v.value,readonly:o.value,dataSources:1,backendUrl:"/my/org/treeSelectList",multiple:!1,placeholder:u.value,style:{width:"300px"}},null,8,["value","disabled","readonly","placeholder"])]),_:1})])}}};export{M as default};
