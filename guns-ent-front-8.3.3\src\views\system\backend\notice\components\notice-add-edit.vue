<template>
  <!-- 新增编辑 -->
  <a-modal
    :width="800"
    :maskClosable="false"
    :visible="props.visible"
    :confirm-loading="loading"
    :forceRender="true"
    :title="isUpdate ? '编辑通知公告' : '新建通知公告'"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
    @close="updateVisible(false)"
  >
    <notice-form v-model:form="form" ref="noticeFormRef" />
  </a-modal>
</template>

<script setup name="NoticeAddEdit">
import { ref, onMounted } from 'vue';
import NoticeForm from './notice-form.vue';
import { message } from 'ant-design-vue';
import { NoticeApi } from '../api/NoticeApi';

const props = defineProps({
  visible: Boolean,
  data: Object
});

const emits = defineEmits(['update:visible', 'done']);
// 弹框加载
const loading = ref(false);
// 是否是编辑状态
const isUpdate = ref(false);
// 表单数据
const form = ref({
  priorityLevel: 'high',
  noticeUserScope: {
    pointUserList: [],
    pointOrgList: []
  }
});
// ref
const noticeFormRef = ref(null);

onMounted(() => {
  if (props.data) {
    isUpdate.value = true;
    getDetail();
  } else {
    isUpdate.value = false;
  }
});

// 获取详情
const getDetail = () => {
  NoticeApi.detail({ noticeId: props.data.noticeId }).then(res => {
    form.value = Object.assign({}, res);
    if (!form.value.noticeUserScope.pointOrgList) {
      form.value.noticeUserScope.pointOrgList = [];
    }
    if (!form.value.noticeUserScope.pointUserList) {
      form.value.noticeUserScope.pointUserList = [];
    }
  });
};

// 更改弹框状态
const updateVisible = value => {
  emits('update:visible', value);
};

// 点击保存
const save = async () => {
  noticeFormRef.value.$refs.formRef.validate().then(async valid => {
    if (valid) {
      // 修改加载框为正在加载
      loading.value = true;

      let result = null;

      // 执行编辑或修改
      if (isUpdate.value) {
        result = NoticeApi.edit(form.value);
      } else {
        result = NoticeApi.add(form.value);
      }
      result
        .then(async result => {
          // 移除加载框
          loading.value = false;

          // 提示添加成功
          message.success(result.message);
          // 关闭弹框，通过控制visible的值，传递给父组件
          updateVisible(false);

          // 触发父组件done事件
          emits('done');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<style></style>
