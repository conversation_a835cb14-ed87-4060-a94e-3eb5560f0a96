import{_ as F,r as v,s as j,X as x,a as M,f as V,w as o,d as a,g as h,m as T,l as q,u as P,v as S,G as B,as as Y,y as H,W as R,J as D,$ as E,H as G,M as J}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{P as U}from"./productCategoryApi-39e417fd.js";const W={name:"ProductCategoryEditForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(y,{emit:l}){const m=v(),t=v(!1),f=v([]),g=v("1\u7EA7 - \u4E00\u7EA7\u5206\u7C7B"),s=j({categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),u={categoryCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5206\u7C7B\u7F16\u7801",trigger:"blur"}],categoryName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",trigger:"blur"}],categoryLevel:[{required:!0,message:"\u8BF7\u9009\u62E9\u5206\u7C7B\u5C42\u7EA7",trigger:"change"}],status:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",trigger:"change"}]},d=e=>{l("update:visible",e),e||c()},c=()=>{var e;(e=m.value)==null||e.resetFields(),Object.assign(s,{categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),g.value="1\u7EA7 - \u4E00\u7EA7\u5206\u7C7B"},b=e=>({1:"1\u7EA7 - \u4E00\u7EA7\u5206\u7C7B",2:"2\u7EA7 - \u4E8C\u7EA7\u5206\u7C7B",3:"3\u7EA7 - \u4E09\u7EA7\u5206\u7C7B",4:"4\u7EA7 - \u56DB\u7EA7\u5206\u7C7B",5:"5\u7EA7 - \u4E94\u7EA7\u5206\u7C7B"})[e]||"".concat(e,"\u7EA7 - \u672A\u77E5"),w=e=>{if(!e)return 1;const r=(A,N)=>{for(const _ of A){if(_.categoryId===N)return _.categoryLevel||1;if(_.children&&_.children.length>0){const O=r(_.children,N);if(O)return O}}return null},I=r(f.value,e);return I?Math.min(I+1,5):1},i=()=>{const e=w(s.parentId);s.categoryLevel=e,g.value=b(e)},k=(e,r)=>r.title&&r.title.toLowerCase().includes(e.toLowerCase()),L=async()=>{try{let r=await U.findTree()||[];r=p(r),f.value=r}catch(e){console.error("\u52A0\u8F7D\u5206\u7C7B\u6811\u5931\u8D25:",e),f.value=[]}},p=e=>Array.isArray(e)?e.filter(r=>!r||!r.categoryId||!r.categoryName?!1:(r.title=r.categoryName,r.key=String(r.categoryId),r.value=String(r.categoryId),r.children&&Array.isArray(r.children)&&(r.children=p(r.children)),!0)):[],C=async()=>{try{await m.value.validate(),t.value=!0,await U.edit(s),T.success("\u7F16\u8F91\u6210\u529F"),l("ok"),d(!1)}catch(e){console.error("\u7F16\u8F91\u5206\u7C7B\u5931\u8D25:",e),T.error("\u7F16\u8F91\u5931\u8D25")}finally{t.value=!1}},n=async e=>{Object.assign(s,e),i()};return x(()=>y.data,e=>{e&&Object.keys(e).length>0&&(Object.assign(s,e),i())},{immediate:!0}),x(()=>y.visible,e=>{e&&L()}),x(()=>s.parentId,()=>{i()}),{formRef:m,loading:t,form:s,rules:u,categoryTreeData:f,categoryLevelText:g,updateVisible:d,save:C,edit:n,filterTreeNode:k}}};function X(y,l,m,t,f,g){const s=q,u=P,d=S,c=B,b=Y,w=H,i=R,k=D,L=E,p=G,C=J;return M(),V(C,{title:"\u7F16\u8F91\u4EA7\u54C1\u5206\u7C7B",visible:m.visible,"confirm-loading":t.loading,width:800,onOk:t.save,onCancel:l[7]||(l[7]=n=>t.updateVisible(!1))},{default:o(()=>[a(p,{ref:"formRef",model:t.form,rules:t.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:o(()=>[a(c,{gutter:16},{default:o(()=>[a(d,{md:12,sm:24},{default:o(()=>[a(u,{label:"\u5206\u7C7B\u7F16\u7801",name:"categoryCode"},{default:o(()=>[a(s,{value:t.form.categoryCode,"onUpdate:value":l[0]||(l[0]=n=>t.form.categoryCode=n),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u7F16\u7801","allow-clear":""},null,8,["value"])]),_:1})]),_:1}),a(d,{md:12,sm:24},{default:o(()=>[a(u,{label:"\u5206\u7C7B\u540D\u79F0",name:"categoryName"},{default:o(()=>[a(s,{value:t.form.categoryName,"onUpdate:value":l[1]||(l[1]=n=>t.form.categoryName=n),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0","allow-clear":""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(c,{gutter:16},{default:o(()=>[a(d,{md:12,sm:24},{default:o(()=>[a(u,{label:"\u7236\u7EA7\u5206\u7C7B",name:"parentId"},{default:o(()=>[a(b,{value:t.form.parentId,"onUpdate:value":l[2]||(l[2]=n=>t.form.parentId=n),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":t.categoryTreeData,placeholder:"\u8BF7\u9009\u62E9\u7236\u7EA7\u5206\u7C7B","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":t.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])]),_:1})]),_:1}),a(d,{md:12,sm:24},{default:o(()=>[a(u,{label:"\u5206\u7C7B\u5C42\u7EA7",name:"categoryLevel"},{default:o(()=>[a(s,{value:t.categoryLevelText,"onUpdate:value":l[3]||(l[3]=n=>t.categoryLevelText=n),placeholder:"\u6839\u636E\u7236\u7EA7\u5206\u7C7B\u81EA\u52A8\u8BBE\u7F6E",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(c,{gutter:16},{default:o(()=>[a(d,{md:12,sm:24},{default:o(()=>[a(u,{label:"\u6392\u5E8F\u53F7",name:"sortOrder"},{default:o(()=>[a(w,{value:t.form.sortOrder,"onUpdate:value":l[4]||(l[4]=n=>t.form.sortOrder=n),placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F\u53F7",min:0,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),a(d,{md:12,sm:24},{default:o(()=>[a(u,{label:"\u72B6\u6001",name:"status"},{default:o(()=>[a(k,{value:t.form.status,"onUpdate:value":l[5]||(l[5]=n=>t.form.status=n),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:o(()=>[a(i,{value:"Y"},{default:o(()=>l[8]||(l[8]=[h("\u542F\u7528")])),_:1,__:[8]}),a(i,{value:"N"},{default:o(()=>l[9]||(l[9]=[h("\u505C\u7528")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),a(c,{gutter:16},{default:o(()=>[a(d,{span:24},{default:o(()=>[a(u,{label:"\u5907\u6CE8",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:o(()=>[a(L,{value:t.form.remark,"onUpdate:value":l[6]||(l[6]=n=>t.form.remark=n),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["visible","confirm-loading","onOk"])}const $=F(W,[["render",X],["__scopeId","data-v-e8d947ed"]]);export{$ as default};
