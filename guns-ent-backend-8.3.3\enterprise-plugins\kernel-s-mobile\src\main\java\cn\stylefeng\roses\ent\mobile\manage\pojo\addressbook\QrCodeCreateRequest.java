package cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通讯录二维码的创建
 *
 * <AUTHOR>
 * @since 2024-04-08 16:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QrCodeCreateRequest extends BaseRequest {

    /**
     * 邀请加入的部门id
     */
    @ChineseDescription("邀请加入的部门id")
    @NotNull(message = "邀请加入的部门id不能为空")
    private Long orgId;

}
