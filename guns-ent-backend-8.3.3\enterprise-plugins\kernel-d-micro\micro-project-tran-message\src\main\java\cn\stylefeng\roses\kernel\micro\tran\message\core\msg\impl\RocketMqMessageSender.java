package cn.stylefeng.roses.kernel.micro.tran.message.core.msg.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.micro.api.exception.MicroException;
import cn.stylefeng.roses.kernel.micro.api.exception.enums.MicroExceptionEnum;
import cn.stylefeng.roses.kernel.micro.api.pojo.TranMessage;
import cn.stylefeng.roses.kernel.micro.tran.message.core.msg.MessageSender;
import cn.stylefeng.roses.kernel.micro.tran.message.core.msg.rocket.OrderSource;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * rocketmq发送消息
 *
 * <AUTHOR>
 * @date 2021/5/18 13:53
 */
@Service
public class RocketMqMessageSender implements MessageSender {

    @Resource
    private OrderSource txMsgSource;

    @Override
    public void sendMessage(TranMessage tranMessageDTO) {

        if (tranMessageDTO == null || ObjectUtil.isEmpty(tranMessageDTO.getTopic()) || ObjectUtil.isEmpty(tranMessageDTO.getMessageBody())) {
            throw new MicroException(MicroExceptionEnum.MICRO_EXCEPTION, "请求参数为空");
        }

        // 发送消息
        this.txMsgSource.output().send(MessageBuilder.withPayload(tranMessageDTO.getMessageBody())
                .setHeader("messageId", tranMessageDTO.getMessageId())
                .build()
        );
    }

}
