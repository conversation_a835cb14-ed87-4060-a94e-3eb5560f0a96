import{r as n,a as l,c as m,d as i,w as r,g as v,b as f,t as b,f as g,h as x,B,a0 as k}from"./index-18a1ea24.js";/* empty css              */import{_ as y}from"./index-d5fce12b.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const V={class:"guns-body guns-body-card"},C={style:{"margin-top":"12px"}},T={__name:"index",setup(N){const e=n(!1),o=n([]),c=()=>{e.value=!0},_=s=>{o.value=s,e.value=!1};return(s,t)=>{const p=B,u=y,d=k;return l(),m("div",V,[i(d,{title:"\u673A\u6784\u4EBA\u5458\u9009\u62E9",bordered:!1},{default:r(()=>[i(p,{class:"ele-btn-icon",onClick:c},{default:r(()=>t[1]||(t[1]=[v(" \u9009\u62E9\u673A\u6784\u4EBA\u5458 ")])),_:1,__:[1]}),f("div",C,"\u9009\u62E9\u6570\u636E: "+b(o.value.map(a=>a.name).join(",")),1),e.value?(l(),g(u,{key:0,visible:e.value,"onUpdate:visible":t[0]||(t[0]=a=>e.value=a),list:o.value,title:"\u673A\u6784\u4EBA\u5458\u9009\u62E9",onDone:_},null,8,["visible","list"])):x("",!0)]),_:1})])}}};export{T as default};
