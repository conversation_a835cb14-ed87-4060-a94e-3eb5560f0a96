System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./index-legacy-45c79de7.js","./InventoryApi-legacy-319e6456.js","./InventoryHistoryApi-legacy-e1cc044f.js","./index-legacy-ccd1ec80.js","./index-legacy-8a7fc0f5.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./SupplierSelector-legacy-fba3813b.js","./SupplierApi-legacy-234ddfc1.js","./InventoryDetail-legacy-994a0a92.js","./index-legacy-510bfbb8.js","./index-legacy-e24582b9.js","./InventoryHistory-legacy-6ae6f9d6.js","./index-legacy-c65a6a4e.js","./InventoryAdjustModal-legacy-2a1676ea.js","./index-legacy-94a6fc23.js","./ProductSelector-legacy-b94adcaf.js","./ProductApi-legacy-33feae42.js","./SetMinStockModal-legacy-05f003c9.js","./InventoryStatisticsModal-legacy-541704cd.js","./productCategoryApi-legacy-247b2407.js"],(function(e,t){"use strict";var s,a,n,l,c,o,r,i,u,d,g,y,p,m,v,x,_,f,j,S,L,h;return{setters:[e=>{s=e._,a=e.r,n=e.k,l=e.a,c=e.c,o=e.b,r=e.d,i=e.w,u=e.g,d=e.f,g=e.F,y=e.e,p=e.t,m=e.h,v=e.m,x=e.B,_=e.n,f=e.a0,j=e.U},null,null,e=>{S=e.I},e=>{L=e.I},e=>{h=e.default},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".test-page[data-v-a4c92145]{padding:20px;max-width:1200px;margin:0 auto}h1[data-v-a4c92145]{color:#1890ff;margin-bottom:20px}.ant-card[data-v-a4c92145]{border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,.1)}\n",document.head.appendChild(t);const k={class:"test-page"};e("default",s({name:"InventoryTest",components:{InventoryIndex:h},setup(){const e=a([]),t=a(!1),s=(t,s)=>{e.value.push({success:t,message:s})};return{testResults:e,showMainPage:t,testLoadInventory:async()=>{try{const e=await S.findPage({pageNo:1,pageSize:10,businessModeList:["PURCHASE_SALE","CONSIGNMENT"]});e.success?(s(!0,`加载库存列表成功，共 ${e.data.total} 条记录`),v.success("加载库存列表成功")):(s(!1,`加载库存列表失败: ${e.message}`),v.error("加载库存列表失败"))}catch(e){s(!1,`加载库存列表异常: ${e.message}`),v.error("加载库存列表异常: "+e.message)}},testLoadInventoryValue:async()=>{try{const e=await S.inventoryValue({businessModeList:["PURCHASE_SALE","CONSIGNMENT"]});if(e.success){const t=e.data;s(!0,`加载库存价值统计成功: 总商品${t.totalProducts}种，总价值¥${t.totalValue}`),v.success("加载库存价值统计成功")}else s(!1,`加载库存价值统计失败: ${e.message}`),v.error("加载库存价值统计失败")}catch(e){s(!1,`加载库存价值统计异常: ${e.message}`),v.error("加载库存价值统计异常: "+e.message)}},testLoadWarningList:async()=>{try{const e=await S.warningList({pageNo:1,pageSize:10});e.success?(s(!0,`加载预警商品列表成功，共 ${e.data.total||e.data.length} 条记录`),v.success("加载预警商品列表成功")):(s(!1,`加载预警商品列表失败: ${e.message}`),v.error("加载预警商品列表失败"))}catch(e){s(!1,`加载预警商品列表异常: ${e.message}`),v.error("加载预警商品列表异常: "+e.message)}},testLoadOutOfStockList:async()=>{try{const e=await S.outOfStockList({pageNo:1,pageSize:10});e.success?(s(!0,`加载缺货商品列表成功，共 ${e.data.total||e.data.length} 条记录`),v.success("加载缺货商品列表成功")):(s(!1,`加载缺货商品列表失败: ${e.message}`),v.error("加载缺货商品列表失败"))}catch(e){s(!1,`加载缺货商品列表异常: ${e.message}`),v.error("加载缺货商品列表异常: "+e.message)}},testLoadInventoryHistory:async()=>{try{const e=await L.findPage({pageNo:1,pageSize:10});e.success?(s(!0,`加载库存历史记录成功，共 ${e.data.total} 条记录`),v.success("加载库存历史记录成功")):(s(!1,`加载库存历史记录失败: ${e.message}`),v.error("加载库存历史记录失败"))}catch(e){s(!1,`加载库存历史记录异常: ${e.message}`),v.error("加载库存历史记录异常: "+e.message)}},openMainPage:()=>{t.value=!t.value,v.info(t.value?"显示库存管理主页面":"隐藏库存管理主页面")}}}},[["render",function(e,t,s,a,v,S){const L=x,h=_,C=f,I=j,$=n("inventory-index");return l(),c("div",k,[t[6]||(t[6]=o("h1",null,"库存管理测试页面",-1)),t[7]||(t[7]=o("p",null,"这是一个测试页面，用于验证库存管理功能是否正常工作。",-1)),r(C,{title:"功能测试",style:{"margin-top":"20px"}},{default:i((()=>[r(h,{direction:"vertical",style:{width:"100%"}},{default:i((()=>[r(L,{type:"primary",onClick:a.testLoadInventory},{default:i((()=>t[0]||(t[0]=[u(" 测试加载库存列表 ")]))),_:1,__:[0]},8,["onClick"]),r(L,{onClick:a.testLoadInventoryValue},{default:i((()=>t[1]||(t[1]=[u(" 测试加载库存价值统计 ")]))),_:1,__:[1]},8,["onClick"]),r(L,{onClick:a.testLoadWarningList},{default:i((()=>t[2]||(t[2]=[u(" 测试加载预警商品列表 ")]))),_:1,__:[2]},8,["onClick"]),r(L,{onClick:a.testLoadOutOfStockList},{default:i((()=>t[3]||(t[3]=[u(" 测试加载缺货商品列表 ")]))),_:1,__:[3]},8,["onClick"]),r(L,{onClick:a.testLoadInventoryHistory},{default:i((()=>t[4]||(t[4]=[u(" 测试加载库存历史记录 ")]))),_:1,__:[4]},8,["onClick"]),r(L,{onClick:a.openMainPage},{default:i((()=>t[5]||(t[5]=[u(" 打开库存管理主页面 ")]))),_:1,__:[5]},8,["onClick"])])),_:1})])),_:1}),a.testResults.length>0?(l(),d(C,{key:0,title:"测试结果",style:{"margin-top":"20px"}},{default:i((()=>[(l(!0),c(g,null,y(a.testResults,((e,t)=>(l(),c("div",{key:t,style:{"margin-bottom":"10px"}},[r(I,{color:e.success?"green":"red"},{default:i((()=>[u(p(e.success?"成功":"失败"),1)])),_:2},1032,["color"]),u(" "+p(e.message),1)])))),128))])),_:1})):m("",!0),a.showMainPage?(l(),d($,{key:1})):m("",!0)])}],["__scopeId","data-v-a4c92145"]]))}}}));
