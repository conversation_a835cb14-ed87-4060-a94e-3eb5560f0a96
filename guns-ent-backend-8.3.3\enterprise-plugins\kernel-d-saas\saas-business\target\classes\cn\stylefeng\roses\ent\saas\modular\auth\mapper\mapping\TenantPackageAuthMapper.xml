<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.ent.saas.modular.auth.mapper.TenantPackageAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth">
		<id column="package_auth_id" property="packageAuthId" />
		<result column="package_id" property="packageId" />
		<result column="limit_type" property="limitType" />
		<result column="business_id" property="businessId" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
	</resultMap>

	<sql id="Base_Column_List">
		package_auth_id,package_id,limit_type,business_id,create_time,create_user,update_time,update_user
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.ent.saas.modular.auth.pojo.response.TenantPackageAuthVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		ent_tenant_package_auth tbl
		WHERE
		<where>
        <if test="param.packageAuthId != null and param.packageAuthId != ''">
            and tbl.package_auth_id like concat('%',#{param.packageAuthId},'%')
        </if>
        <if test="param.packageId != null and param.packageId != ''">
            and tbl.package_id like concat('%',#{param.packageId},'%')
        </if>
        <if test="param.limitType != null and param.limitType != ''">
            and tbl.limit_type like concat('%',#{param.limitType},'%')
        </if>
        <if test="param.businessId != null and param.businessId != ''">
            and tbl.business_id like concat('%',#{param.businessId},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
		</where>
	</select>

</mapper>
