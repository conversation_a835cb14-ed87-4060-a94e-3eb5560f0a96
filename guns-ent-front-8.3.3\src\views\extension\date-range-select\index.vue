<template>
  <div class="guns-body guns-body-card">
    <a-card title="日期范围选择" :bordered="false">
      <DateRangeComponent
        v-model:value="selectValue"
        :valueFormat="valueFormat"
        :disabled="disabled"
        :firstPlaceholder="placeholder"
        :secondPlaceholder="placeholder"
        :readonly="readonly"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 选中的值
const selectValue = ref(null);

const valueFormat = ref('YYYY-MM-DD');

// 是否禁用
const disabled = ref(false);

// 是否只读
const readonly = ref(false);

// 选择提示
const placeholder = ref('请选择');
</script>

<style></style>
