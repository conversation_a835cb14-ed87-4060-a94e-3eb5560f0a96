<template>
  <a-modal
    :visible="visible"
    title="确认入库单"
    :width="800"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleConfirm">
        确认入库单
      </a-button>
    </template>

    <div class="confirm-inbound-content">
      <!-- 警告提示 -->
      <a-alert
        message="确认提醒"
        description="确认后入库单将不能再修改，请仔细核对入库单信息。"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />

      <!-- 入库单基本信息 -->
      <a-card title="入库单信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="入库单号">
            <span class="order-no">{{ data.orderNo }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="供应商">
            {{ data.supplierName }}
          </a-descriptions-item>
          <a-descriptions-item label="订单日期">
            {{ data.orderDate }}
          </a-descriptions-item>
          <a-descriptions-item label="总金额">
            <span class="total-amount">¥{{ formatAmount(data.totalAmount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="商品种类">
            {{ productCount }} 种
          </a-descriptions-item>
          <a-descriptions-item label="总数量">
            {{ totalQuantity }} 件
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 商品明细预览 -->
      <a-card title="商品明细" size="small" style="margin-bottom: 16px">
        <a-table
          :columns="columns"
          :data-source="data.detailList || []"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ y: 300 }"
        >
          <template #bodyCell="{ column, record }">
            <!-- 商品信息 -->
            <template v-if="column.key === 'productInfo'">
              <div class="product-info">
                <div class="product-name">{{ record.productName }}</div>
                <div class="product-code">{{ record.productCode }}</div>
              </div>
            </template>

            <!-- 数量 -->
            <template v-if="column.key === 'quantity'">
              {{ record.quantity }} {{ getQuantityUnit(record) }}
            </template>

            <!-- 单价 -->
            <template v-if="column.key === 'unitPrice'">
              ¥{{ formatAmount(record.unitPrice) }}
            </template>

            <!-- 总价 -->
            <template v-if="column.key === 'totalPrice'">
              <span class="total-price">¥{{ formatAmount(record.totalPrice) }}</span>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 确认信息 -->
      <a-card title="确认信息" size="small">
        <a-form ref="formRef" :model="confirmData" :rules="rules" layout="vertical">
          <a-form-item label="确认备注" name="confirmRemark">
            <a-textarea
              v-model:value="confirmData.confirmRemark"
              placeholder="请输入确认备注（可选）"
              :rows="3"
              :maxlength="200"
              showCount
            />
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { PurchaseApi } from '../../api/PurchaseApi';

export default {
  name: 'ConfirmInboundModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const loading = ref(false);

    // 确认数据
    const confirmData = reactive({
      confirmRemark: ''
    });

    // 表单验证规则
    const rules = {
      // 确认备注为可选项，无验证规则
    };

    // 表格列定义
    const columns = [
      {
        title: '商品信息',
        key: 'productInfo',
        width: 200
      },
      {
        title: '数量',
        key: 'quantity',
        width: 100,
        align: 'center'
      },
      {
        title: '单价',
        key: 'unitPrice',
        width: 100,
        align: 'right'
      },
      {
        title: '总价',
        key: 'totalPrice',
        width: 120,
        align: 'right'
      }
    ];

    // 计算属性
    const productCount = computed(() => {
      return props.data.detailList ? props.data.detailList.length : 0;
    });

    const totalQuantity = computed(() => {
      if (!props.data.detailList || props.data.detailList.length === 0) return 0;
      return props.data.detailList.reduce((total, item) => {
        return total + (parseFloat(item.quantity) || 0);
      }, 0);
    });

    // 获取数量单位
    const getQuantityUnit = (record) => {
      switch (record.pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return record.unit || '个';
      }
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 取消
    const handleCancel = () => {
      confirmData.confirmRemark = '';
      emit('update:visible', false);
    };

    // 确认
    const handleConfirm = () => {
      if (!props.data.id) {
        message.error('入库单信息不完整');
        return;
      }

      loading.value = true;
      PurchaseApi.confirm({
        id: props.data.id,
        confirmRemark: confirmData.confirmRemark
      })
        .then(() => {
          message.success('确认成功');
          confirmData.confirmRemark = '';
          emit('ok');
        })
        .catch(error => {
          message.error('确认失败：' + (error.message || '未知错误'));
        })
        .finally(() => {
          loading.value = false;
        });
    };

    return {
      formRef,
      loading,
      confirmData,
      rules,
      columns,
      productCount,
      totalQuantity,
      getQuantityUnit,
      formatAmount,
      handleCancel,
      handleConfirm
    };
  }
};
</script>

<style scoped>
.confirm-inbound-content {
  max-height: 70vh;
  overflow-y: auto;
}

.order-no {
  font-weight: 500;
  color: #1890ff;
}

.total-amount {
  font-weight: 500;
  color: #f5222d;
  font-size: 16px;
}

.product-info {
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-code {
  font-size: 12px;
  color: #8c8c8c;
}

.total-price {
  font-weight: 500;
  color: #1890ff;
}

.ant-alert {
  border-radius: 6px;
}

.ant-card {
  border-radius: 6px;
}

.ant-descriptions-item-label {
  font-weight: 500;
}
</style>
