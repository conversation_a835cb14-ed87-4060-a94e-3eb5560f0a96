import{R as s}from"./regionApi-2c103d88.js";import{U as N}from"./UniversalTree-6547889b.js";import{_ as A,r as b,L as i,a as B,f as W,M as D,d as K,E as M,m as c}from"./index-18a1ea24.js";/* empty css              */const z=Object.assign({name:"RegionTree"},{__name:"region-tree",props:{isShowEditIcon:{type:Boolean,default:!1},isSetWidth:{type:Boolean,default:!0}},emits:["treeSelect","addRegion","editRegion","deleteRegion"],setup(r,{expose:l,emit:h}){const o=r,a=h,n=b(),g={api:s.findTree,lazyLoadApi:s.findTreeWithLazy,searchParam:"searchText",parentIdParam:"parentId"},p={key:"regionId",title:"regionName",children:"children",hasChildren:"hasChildren",level:"regionLevel"},u=i(()=>({title:"\u533A\u57DF\u7BA1\u7406",showHeader:o.isSetWidth,showSearch:!0,searchPlaceholder:"\u8BF7\u8F93\u5165\u533A\u57DF\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22",showAddButton:o.isShowEditIcon,showEditIcons:o.isShowEditIcon,showIcon:!1,isSetWidth:o.isSetWidth})),f={selectable:!0,expandable:!0,lazyLoad:!0,defaultExpandLevel:3,allowMultiSelect:!1},S=i(()=>({allowAdd:o.isShowEditIcon,allowEdit:o.isShowEditIcon,allowDelete:o.isShowEditIcon})),m=e=>{const{keys:t,nodes:k}=e;a("treeSelect",t,{selectedNodes:k})},_=e=>{},y=e=>{},E=e=>{a("addRegion",e)},v=e=>{a("editRegion",e)},w=e=>{D.confirm({title:"\u786E\u8BA4\u5220\u9664",content:'\u786E\u5B9A\u8981\u5220\u9664\u533A\u57DF"'.concat(e.regionName,'"\u5417\uFF1F'),icon:K(M),okText:"\u786E\u5B9A",cancelText:"\u53D6\u6D88",onOk:()=>s.delete({regionId:e.regionId}).then(()=>{c.success("\u5220\u9664\u6210\u529F"),a("deleteRegion",e),d()}).catch(t=>{console.error("\u5220\u9664\u533A\u57DF\u5931\u8D25:",t),c.error("\u5220\u9664\u5931\u8D25")})})},x=e=>{},I=e=>{console.error("\u533A\u57DF\u6811\u6570\u636E\u52A0\u8F7D\u5931\u8D25:",e)},d=()=>{var e;(e=n.value)==null||e.reload()},T=()=>{var e;return(e=n.value)==null?void 0:e.getSelectedNodes()},R=e=>{var t;(t=n.value)==null||t.setSelectedKeys(e)},L=d,C=i(()=>{var e;return((e=n.value)==null?void 0:e.getSelectedNodes())||[]});return l({reload:d,reloadRegionTreeData:L,getSelectedNodes:T,setSelectedKeys:R,currentSelectKeys:C}),(e,t)=>(B(),W(N,{ref_key:"universalTreeRef",ref:n,"data-source":g,"field-mapping":p,"display-config":u.value,"interaction-config":f,"action-config":S.value,onSelect:m,onExpand:_,onSearch:y,onAdd:E,onEdit:v,onDelete:w,onLoad:x,onLoadError:I},null,8,["display-config","action-config"]))}}),j=A(z,[["__scopeId","data-v-d2d1dbc9"]]);export{j as default};
