<template>
  <div class="wh100">
    <a-input
      v-model:value="dataValueName"
      :disabled="props.readonly || props.disabled"
      class="w-full"
      :style="{ width: props.width }"
      :placeholder="placeholder"
      @focus="inputClick"
    />

    <a-form-item-rest>
      <!-- 选择组件 -->
      <OrgUserSelect
        v-model:visible="visibleSelection"
        v-if="visibleSelection"
        :list="dataValue"
        title="机构人员选择"
        @done="closeSelection"
      />
    </a-form-item-rest>
  </div>
</template>

<script setup name="OrgUserComponent">
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  value: {
    type: [String, Array],
    default: ''
  },
  //是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: {}
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  // input宽度
  width: {
    type: String,
    default: '100%'
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  },
  // ref
  formRef: {
    type: Object,
    default: null
  },
  // 是否正常保存，不是转json格式
  normal: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:value', 'onChange']);

onMounted(() => {
  if (props.value) {
    dataValue.value = props.normal ? props.value : JSON.parse(props.value);
  } else {
    dataValue.value = [];
  }
  setDataValueName();
});

// 选中的值
const dataValue = ref([]);
// 名称
const dataValueName = ref('');

// 是否显示选人弹框
const visibleSelection = ref(false);

// 输入框点击
const inputClick = () => {
  visibleSelection.value = true;
};

// 关闭选人弹框
const closeSelection = data => {
  dataValue.value = data?.map(item => {
    return { id: item.id, name: item.name, type: item.type };
  });
  setDataValueName();
  dataValueChange();
};

const setDataValueName = () => {
  dataValueName.value = dataValue.value
    .map(item => {
      let typeName = '';
      if (item.type == '1') {
        typeName = '(公司)';
      }
      if (item.type == '2') {
        typeName = '(部门)';
      }
      if (item.type == '3') {
        typeName = '(人员)';
      }
      return item.name + typeName;
    })
    .join('；');
};

// 更改值
const dataValueChange = () => {
  let valueData = [...dataValue.value];
  if (props.normal) {
  } else {
    if (valueData?.length == 0) {
      valueData = '';
    } else {
      valueData = JSON.stringify(valueData);
    }
  }
  emits('update:value', valueData);
  emits('onChange', props.record);
  checkField();
};

// 校验必填
const checkField = async () => {
  if (!props.normal && props.formRef?.validateFields) {
    await props.formRef.validateFields([props.record.fieldCode]);
  }
};

watch(
  () => props.value,
  val => {
    if (props.value) {
      dataValue.value = props.normal ? dataValue.value : JSON.parse(props.value);
    } else {
      dataValue.value = [];
    }

    setDataValueName();
  },
  { deep: true }
);
</script>

<style></style>
