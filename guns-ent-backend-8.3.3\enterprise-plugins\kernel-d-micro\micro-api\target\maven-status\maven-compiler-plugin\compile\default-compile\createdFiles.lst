cn\stylefeng\roses\kernel\micro\api\exception\enums\GatewayExceptionEnum.class
cn\stylefeng\roses\kernel\micro\api\exception\enums\TranMessageExceptionEnum.class
cn\stylefeng\roses\kernel\micro\api\example\order\GoodsOrderApi.class
cn\stylefeng\roses\kernel\micro\api\pojo\TranMessage.class
cn\stylefeng\roses\kernel\micro\api\FeignExampleApi.class
cn\stylefeng\roses\kernel\micro\api\enums\TranMessageStatusEnum.class
cn\stylefeng\roses\kernel\micro\api\example\order\model\GoodsOrder.class
cn\stylefeng\roses\kernel\micro\api\example\account\model\FlowRecord.class
cn\stylefeng\roses\kernel\micro\api\exception\MicroException.class
cn\stylefeng\roses\kernel\micro\api\constants\MicroConstants.class
cn\stylefeng\roses\kernel\micro\api\pojo\request\TranMessageRequest.class
cn\stylefeng\roses\kernel\micro\api\TranMessageServiceApi.class
cn\stylefeng\roses\kernel\micro\api\example\account\FlowRecordApi.class
cn\stylefeng\roses\kernel\micro\api\exception\enums\FeignExceptionEnum.class
cn\stylefeng\roses\kernel\micro\api\example\order\GoodsFlowParam.class
cn\stylefeng\roses\kernel\micro\api\example\order\enums\OrderStatusEnum.class
cn\stylefeng\roses\kernel\micro\api\exception\enums\MicroExceptionEnum.class
cn\stylefeng\roses\kernel\micro\api\enums\MessageQueueEnum.class
