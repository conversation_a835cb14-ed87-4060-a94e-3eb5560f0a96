System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js"],(function(e,l){"use strict";var a,r,t,u,o,s,d,n,c,m,f,i,v,p,b,g,_,j,D,y,U;return{setters:[e=>{a=e._,r=e.s,t=e.r,u=e.a,o=e.f,s=e.w,d=e.d,n=e.g,c=e.c,m=e.F,f=e.e,i=e.t,v=e.l,p=e.u,b=e.v,g=e.W,_=e.J,j=e.U,D=e.a7,y=e.G,U=e.H},null],execute:function(){var l=document.createElement("style");l.textContent=".urltag[data-v-03841e2b]{cursor:pointer}\n",document.head.appendChild(l),e("default",a({__name:"datasource-form",props:{form:Object},setup(e){const l=e,a=r({dbName:[{required:!0,message:"请输入数据源名称",type:"string",trigger:"blur"}],jdbcDriver:[{required:!0,message:"请选择数据源驱动",type:"string",trigger:"blur"}],username:[{required:!0,message:"请输入jdbc账号",type:"string",trigger:"blur"}],password:[{required:!0,message:"请输入jdbc密码",type:"string",trigger:"blur"}],jdbcUrl:[{required:!0,message:"请输入url",type:"string",trigger:"blur"}]}),q=t([{title:"******************************************************************************************************************************************************************************************************",name:"mysql"},{title:"****************************************",name:"oracle"},{title:"******************************************************",name:"sql server"},{title:"*************************************",name:"postgre sql"}]);return(r,t)=>{const C=v,w=p,h=b,B=g,J=_,L=j,N=D,O=y,R=U;return u(),o(R,{ref:"formRef",model:e.form,rules:a,layout:"vertical"},{default:s((()=>[d(O,{gutter:20},{default:s((()=>[d(h,{span:24},{default:s((()=>[d(w,{label:"数据库名称:",name:"dbName"},{default:s((()=>[d(C,{value:e.form.dbName,"onUpdate:value":t[0]||(t[0]=l=>e.form.dbName=l),placeholder:"请输入数据库名称","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),d(h,{span:24},{default:s((()=>[d(w,{label:"JDBC驱动:",name:"jdbcDriver"},{default:s((()=>[d(J,{value:e.form.jdbcDriver,"onUpdate:value":t[1]||(t[1]=l=>e.form.jdbcDriver=l),defaultActiveFirstOption:""},{default:s((()=>[d(B,{value:"com.mysql.cj.jdbc.Driver"},{default:s((()=>t[6]||(t[6]=[n("Mysql")]))),_:1,__:[6]}),d(B,{value:"oracle.jdbc.OracleDriver"},{default:s((()=>t[7]||(t[7]=[n("Oracle")]))),_:1,__:[7]}),d(B,{value:"net.sourceforge.jtds.jdbc.Driver"},{default:s((()=>t[8]||(t[8]=[n("Sql Server")]))),_:1,__:[8]}),d(B,{value:"org.postgresql.Driver"},{default:s((()=>t[9]||(t[9]=[n("Postgre SQL")]))),_:1,__:[9]})])),_:1},8,["value"])])),_:1})])),_:1}),d(h,{span:24},{default:s((()=>[d(w,{label:"JDBC账号:",name:"username"},{default:s((()=>[d(C,{value:e.form.username,"onUpdate:value":t[2]||(t[2]=l=>e.form.username=l),placeholder:"请输入JDBC账号","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),d(h,{span:24},{default:s((()=>[d(w,{label:"JDBC密码:",name:"password"},{default:s((()=>[d(C,{value:e.form.password,"onUpdate:value":t[3]||(t[3]=l=>e.form.password=l),placeholder:"请输入JDBC密码","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1}),d(h,{span:24},{default:s((()=>[d(w,{label:"JDBC URL:",name:"jdbcUrl"},{default:s((()=>[d(C,{value:e.form.jdbcUrl,"onUpdate:value":t[4]||(t[4]=l=>e.form.jdbcUrl=l),placeholder:"请输入URL","allow-clear":"",autocomplete:"off"},null,8,["value"]),(u(!0),c(m,null,f(q.value,((e,a)=>(u(),o(N,{key:a+"url"},{title:s((()=>[n(i(e.title),1)])),default:s((()=>[d(L,{class:"urltag",onClick:a=>(e=>{l.form.jdbcUrl=e})(e.title)},{default:s((()=>[n(i(e.name),1)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})])),_:1}),d(h,{span:24},{default:s((()=>[d(w,{label:"数据源备注:",name:"remarks"},{default:s((()=>[d(C,{value:e.form.remarks,"onUpdate:value":t[5]||(t[5]=l=>e.form.remarks=l),placeholder:"请输入数据源备注","allow-clear":"",autocomplete:"off"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}},[["__scopeId","data-v-03841e2b"]]))}}}));
