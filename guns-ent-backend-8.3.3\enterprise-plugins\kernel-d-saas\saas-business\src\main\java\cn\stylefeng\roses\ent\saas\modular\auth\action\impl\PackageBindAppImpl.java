package cn.stylefeng.roses.ent.saas.modular.auth.action.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.action.PackageBindPermissionAction;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackageAuth;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.kernel.sys.api.entity.SysMenuOptions;
import cn.stylefeng.roses.kernel.sys.api.enums.PermissionNodeTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.menu.entity.SysMenu;
import cn.stylefeng.roses.kernel.sys.modular.menu.service.SysMenuOptionsService;
import cn.stylefeng.roses.kernel.sys.modular.menu.service.SysMenuService;
import cn.stylefeng.roses.kernel.sys.modular.role.enums.RoleLimitTypeEnum;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleMenuOptionsService;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleMenuService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能包绑定应用的实现
 *
 * <AUTHOR>
 * @since 2024/1/22 0:43
 */
@Service
@SuppressWarnings("all")
public class PackageBindAppImpl implements PackageBindPermissionAction {

    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private SysMenuOptionsService sysMenuOptionsService;

    @Resource
    private SysRoleMenuService sysRoleMenuService;

    @Resource
    private SysRoleMenuOptionsService sysRoleMenuOptionsService;

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    @Override
    public PermissionNodeTypeEnum getPackageBindPermissionNodeType() {
        return PermissionNodeTypeEnum.APP;
    }

    @Override
    public void doPackageBindPermissionAction(PackageBindPermissionRequest packageBindPermissionRequest) {
        Long packageId = packageBindPermissionRequest.getPackageId();
        Long appId = packageBindPermissionRequest.getNodeId();

        // 找到所选应用的对应的所有菜单
        Set<Long> menuIds = this.getAppMenuIds(appId);

        // 菜单为空，则直接返回
        if (ObjectUtil.isEmpty(menuIds)) {
            return;
        }

        // 找到所选应用的对应的所有菜单功能
        List<SysMenuOptions> totalMenuOptions = this.getAppMenuOptions(appId);
        Set<Long> menuOptionIds = totalMenuOptions.stream().map(SysMenuOptions::getMenuOptionId).collect(Collectors.toSet());

        // 组装菜单id和功能id的集合
        List<Long> totalBusinessId = ListUtil.list(false, menuIds);
        if (ObjectUtil.isNotEmpty(menuOptionIds)) {
            totalBusinessId.addAll(menuOptionIds);
        }

        // 删除角色绑定的这些菜单限制，以及菜单功能限制
        LambdaQueryWrapper<TenantPackageAuth> sysRoleLimitLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysRoleLimitLambdaQueryWrapper.eq(TenantPackageAuth::getPackageId, packageId);
        sysRoleLimitLambdaQueryWrapper.in(TenantPackageAuth::getBusinessId, totalBusinessId);
        tenantPackageAuthService.remove(sysRoleLimitLambdaQueryWrapper);

        // 如果是选中了应用，则从新绑定这些菜单和功能
        if (packageBindPermissionRequest.getChecked()) {
            List<TenantPackageAuth> totalRoleLimit = new ArrayList<>();

            // 绑定菜单id
            for (Long menuId : menuIds) {
                TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
                tenantPackageAuth.setPackageId(packageId);
                tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU.getCode());
                tenantPackageAuth.setBusinessId(menuId);
                totalRoleLimit.add(tenantPackageAuth);
            }

            // 绑定菜单功能id
            if (ObjectUtil.isNotEmpty(menuOptionIds)) {
                for (Long optionsId : menuOptionIds) {
                    TenantPackageAuth tenantPackageAuth = new TenantPackageAuth();
                    tenantPackageAuth.setPackageId(packageId);
                    tenantPackageAuth.setLimitType(RoleLimitTypeEnum.MENU_OPTIONS.getCode());
                    tenantPackageAuth.setBusinessId(optionsId);
                    totalRoleLimit.add(tenantPackageAuth);
                }
            }

            this.tenantPackageAuthService.saveBatch(totalRoleLimit);
        }
    }

    /**
     * 获取应用下的所有菜单id
     *
     * <AUTHOR>
     * @since 2023/9/8 15:03
     */
    private Set<Long> getAppMenuIds(Long appId) {
        return this.getAppMenuIds(appId, null);
    }

    /**
     * 获取应用下的所有菜单id
     *
     * <AUTHOR>
     * @since 2023/9/8 15:03
     */
    private Set<Long> getAppMenuIds(Long appId, Set<Long> roleLimitMenuIdsAndOptionIds) {
        LambdaQueryWrapper<SysMenu> menuLambdaQueryWrapper = new LambdaQueryWrapper<>();
        menuLambdaQueryWrapper.eq(SysMenu::getAppId, appId);
        // 如果有范围限制，则查询范围内的菜单
        if (ObjectUtil.isNotEmpty(roleLimitMenuIdsAndOptionIds)) {
            menuLambdaQueryWrapper.in(SysMenu::getMenuId, roleLimitMenuIdsAndOptionIds);
        }
        menuLambdaQueryWrapper.select(SysMenu::getMenuId);
        List<SysMenu> totalMenus = sysMenuService.list(menuLambdaQueryWrapper);
        if (ObjectUtil.isEmpty(totalMenus)) {
            return new HashSet<>();
        }
        return totalMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toSet());
    }

    /**
     * 获取应用下的所有菜单功能
     *
     * <AUTHOR>
     * @since 2023/9/8 15:13
     */
    private List<SysMenuOptions> getAppMenuOptions(Long appId) {
        return this.getAppMenuOptions(appId, null);
    }

    /**
     * 获取应用下的所有菜单功能
     *
     * <AUTHOR>
     * @since 2023/9/8 15:13
     */
    private List<SysMenuOptions> getAppMenuOptions(Long appId, Set<Long> roleLimitMenuIdsAndOptionIds) {
        LambdaQueryWrapper<SysMenuOptions> menuOptionsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        menuOptionsLambdaQueryWrapper.eq(SysMenuOptions::getAppId, appId);
        // 如果有范围限制，则查询范围内的菜单
        if (ObjectUtil.isNotEmpty(roleLimitMenuIdsAndOptionIds)) {
            menuOptionsLambdaQueryWrapper.in(SysMenuOptions::getMenuOptionId, roleLimitMenuIdsAndOptionIds);
        }
        menuOptionsLambdaQueryWrapper.select(SysMenuOptions::getMenuOptionId, SysMenuOptions::getMenuId);
        return sysMenuOptionsService.list(menuOptionsLambdaQueryWrapper);
    }

}
