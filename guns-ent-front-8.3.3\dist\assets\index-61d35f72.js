import{r as o,o as h,X as y,a as f,f as g,a6 as C,c as _,d as v,w as b,a0 as B}from"./index-18a1ea24.js";/* empty css              *//* empty css              *//* empty css              */const k={__name:"index",props:{value:{type:[String,Array],default:""},valueFormat:{type:String,default:"YYYY-MM-DD"},disabled:{type:Boolean,default:!1},record:{type:Object,default:{}},firstPlaceholder:{type:String,default:"\u8BF7\u9009\u62E9\u5F00\u59CB\u65E5\u671F"},secondPlaceholder:{type:String,default:"\u8BF7\u9009\u62E9\u7ED3\u675F\u65E5\u671F"},readonly:{type:Boolean,default:!1},normal:{type:Boolean,default:!1}},emits:["update:value","onBlur","onClick","onChange"],setup(l,{emit:d}){const e=l,t=d,a=o([]);h(()=>{n()}),y(()=>e.value,r=>{n()},{deep:!0});const n=()=>{e.value?a.value=e.normal?e.value:JSON.parse(e.value):a.value=[]},i=()=>{let r=e.normal?a.value:JSON.stringify(a.value);t("update:value",r),t("onChange",e.record)},u=()=>{t("onClick",e.record)},c=()=>{t("onBlur",e.record)};return(r,s)=>{const m=C;return f(),g(m,{value:a.value,"onUpdate:value":s[0]||(s[0]=p=>a.value=p),"value-format":l.valueFormat,disabled:l.readonly||l.disabled,allowClear:"","show-time":"",onChange:i,onClick:u,onBlur:c,class:"w-full",placeholder:[l.firstPlaceholder,l.secondPlaceholder]},null,8,["value","value-format","disabled","placeholder"])}}},P={class:"guns-body guns-body-card"},D={__name:"index",setup(l){const d=o(null),e=o("YYYY-MM-DD"),t=o(!1),a=o(!1),n=o("\u8BF7\u9009\u62E9");return(i,u)=>{const c=k,r=B;return f(),_("div",P,[v(r,{title:"\u65E5\u671F\u8303\u56F4\u9009\u62E9",bordered:!1},{default:b(()=>[v(c,{value:d.value,"onUpdate:value":u[0]||(u[0]=s=>d.value=s),valueFormat:e.value,disabled:t.value,firstPlaceholder:n.value,secondPlaceholder:n.value,readonly:a.value},null,8,["value","valueFormat","disabled","firstPlaceholder","secondPlaceholder","readonly"])]),_:1})])}}};export{D as default};
