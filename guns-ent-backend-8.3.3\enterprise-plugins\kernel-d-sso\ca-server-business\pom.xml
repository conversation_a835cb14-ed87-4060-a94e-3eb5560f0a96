<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-sso</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ca-server-business</artifactId>

    <packaging>jar</packaging>

    <properties>
        <mp.version>3.4.0</mp.version>
    </properties>

    <dependencies>

        <!--sso-server的api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>ca-server-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--sso-server的api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>api-auth-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--定时任务的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>timer-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--system系统管理的api-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--jwt模块的api-->
        <!--token用的jwt token-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>jwt-sdk</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <!--web获取token的操作需要从http header中取-->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <!--缓存的依赖-->
        <!--sso的session可以用redis，可以用内存的-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-api</artifactId>
            <version>${roses.version}</version>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-memory</artifactId>
            <version>${roses.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>cache-sdk-redis</artifactId>
            <version>${roses.version}</version>
            <scope>provided</scope>
        </dependency>

        <!--动态脚本执行-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>groovy-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--系统管理基础业务-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
