System.register(["./index-legacy-cbae9bf3.js","./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./TenantApi-legacy-40853d2f.js","./tenant-detail-legacy-cd0e295d.js","./tenant-add-edit-legacy-fe1aa42f.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./tenant-form-legacy-6fd2a4d3.js","./index-legacy-c65a6a4e.js","./index-legacy-efb51034.js","./index-legacy-198191c1.js","./FileApi-legacy-f85a3060.js","./time-util-legacy-296598c1.js"],(function(e,a){"use strict";var t,l,n,s,i,d,o,c,u,r,v,h,p,g,y,x,m,w,f,b,k,_,C,I,j,S,N,D,T,F;return{setters:[e=>{t=e._},e=>{l=e.C,n=e._},e=>{s=e._,i=e.r,d=e.o,o=e.k,c=e.a,u=e.c,r=e.b,v=e.d,h=e.w,p=e.g,g=e.t,y=e.h,x=e.f,m=e.F,w=e.M,f=e.E,b=e.m,k=e.I,_=e.l,C=e.n,I=e.B,j=e.p,S=e.q,N=e.D},e=>{D=e.T},e=>{T=e.default},e=>{F=e.default},null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".appIconWrapper[data-v-58d50984]{width:22px;height:22px}\n",document.head.appendChild(a);const L={class:"guns-layout"},U={class:"guns-layout-content"},W={class:"guns-layout"},z={class:"guns-layout-content-application"},A={class:"content-mian"},E={class:"content-mian-header"},B={class:"header-content"},O={class:"header-content-left"},R={class:"header-content-right"},M={class:"content-mian-body"},V={class:"table-content"},J=["onClick"],q=["src"],P=Object.assign({name:"TenantManage"},{__name:"index",setup(e){const a=i([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:e})=>s.value.tableIndex+e},{dataIndex:"tenantName",title:"租户名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"tenantCode",title:"租户编码",width:100,isShow:!0},{dataIndex:"tenantLogoWrapper",title:"租户logo",width:100,isShow:!0},{dataIndex:"companyName",title:"公司",width:100,isShow:!0},{dataIndex:"email",title:"申请人邮箱",width:100,isShow:!0},{dataIndex:"safePhone",title:"申请人电话",width:100,isShow:!0},{dataIndex:"statusFlag",title:"状态",width:100,isShow:!0},{dataIndex:"activeDate",title:"租户生效时间",width:100,isShow:!0},{dataIndex:"expireDate",title:"租户到期时间",width:100,isShow:!0},{key:"action",title:"操作",width:80,isShow:!0}]),s=i(null),P=i({searchText:""}),G=i(!1),H=i(null),K=i(!1),Q=i(!1),X=i("TENANT_MANAGEMENT");d((()=>{Y()}));const Y=()=>{l.getUserConfig({fieldBusinessCode:X.value}).then((e=>{e.tableWidthJson&&(a.value=JSON.parse(e.tableWidthJson))}))},Z=({key:e})=>{"1"==e?G.value=!0:"2"==e&&ae()},$=()=>{s.value.reload()},ee=e=>{H.value=e,K.value=!0},ae=()=>{if(s.value.selectedRowList&&0==s.value.selectedRowList.length)return b.warning("请选择需要删除的租户");w.confirm({title:"提示",content:"确定要删除选中的租户吗?",icon:v(f),maskClosable:!0,onOk:async()=>{const e=await D.batchDelete({tenantIdList:s.value.selectedRowList});b.success(e.message),$()}})},te=e=>{let a="";return e&&(a=e.substr(0,10)),a};return(e,l)=>{const i=k,d=_,Y=C,ae=o("plus-outlined"),le=I,ne=j,se=S,ie=o("small-dash-outlined"),de=N,oe=o("vxe-switch"),ce=n,ue=t;return c(),u("div",L,[r("div",U,[r("div",W,[r("div",z,[r("div",A,[r("div",E,[r("div",B,[r("div",O,[v(Y,{size:16},{default:h((()=>[v(d,{value:P.value.searchText,"onUpdate:value":l[0]||(l[0]=e=>P.value.searchText=e),placeholder:"租户名称、租户编码（回车搜索）",onPressEnter:$,class:"search-input"},{prefix:h((()=>[v(i,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),_:1})]),r("div",R,[v(Y,{size:16},{default:h((()=>[v(le,{type:"primary",class:"border-radius",onClick:l[1]||(l[1]=e=>ee())},{default:h((()=>[v(ae),l[6]||(l[6]=p("新建"))])),_:1,__:[6]}),v(de,null,{overlay:h((()=>[v(se,{onClick:Z},{default:h((()=>[v(ne,{key:"1"},{default:h((()=>[v(i,{iconClass:"icon-opt-zidingyilie",color:"#60666b"}),l[7]||(l[7]=r("span",null,"自定义列",-1))])),_:1,__:[7]}),r("div",null,[v(ne,{key:"2"},{default:h((()=>[v(i,{iconClass:"icon-opt-shanchu",color:"#60666b"}),l[8]||(l[8]=r("span",null,"批量删除",-1))])),_:1,__:[8]})])])),_:1})])),default:h((()=>[v(le,{class:"border-radius"},{default:h((()=>[l[9]||(l[9]=p(" 更多 ")),v(ie)])),_:1,__:[9]})])),_:1})])),_:1})])])]),r("div",M,[r("div",V,[v(ce,{columns:a.value,where:P.value,rowId:"tenantId",ref_key:"tableRef",ref:s,url:"/tenant/page"},{bodyCell:h((({column:e,record:a})=>["tenantName"==e.dataIndex?(c(),u("a",{key:0,onClick:e=>(e=>{H.value=e,Q.value=!0})(a)},g(a.tenantName),9,J)):y("",!0),"tenantLogoWrapper"==e.dataIndex?(c(),u("img",{key:1,src:a.tenantLogoWrapper,alt:"",class:"appIconWrapper"},null,8,q)):y("",!0),"statusFlag"==e.dataIndex?(c(),x(oe,{key:2,modelValue:a.statusFlag,"onUpdate:modelValue":e=>a.statusFlag=e,"open-value":1,"close-value":2,onChange:e=>(e=>{D.edit(e).then((e=>{b.success(e.message),$()}))})(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])):y("",!0),"activeDate"==e.dataIndex||"expireDate"==e.dataIndex?(c(),u(m,{key:3},[p(g(te(a[e.dataIndex])),1)],64)):y("",!0),"action"==e.key?(c(),x(Y,{key:4,size:16},{default:h((()=>[v(i,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>ee(a)},null,8,["onClick"]),v(i,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{w.confirm({title:"提示",content:"确定要删除选中的租户吗?",icon:v(f),maskClosable:!0,onOk:async()=>{const a=await D.delete({tenantId:e.tenantId});b.success(a.message),$()}})})(a)},null,8,["onClick"])])),_:2},1024)):y("",!0)])),_:1},8,["columns","where"])])])])])])]),G.value?(c(),x(ue,{key:0,visible:G.value,"onUpdate:visible":l[2]||(l[2]=e=>G.value=e),data:a.value,onDone:l[3]||(l[3]=e=>a.value=e),fieldBusinessCode:X.value},null,8,["visible","data","fieldBusinessCode"])):y("",!0),K.value?(c(),x(F,{key:1,visible:K.value,"onUpdate:visible":l[4]||(l[4]=e=>K.value=e),data:H.value,onDone:$},null,8,["visible","data"])):y("",!0),Q.value?(c(),x(T,{key:2,visible:Q.value,"onUpdate:visible":l[5]||(l[5]=e=>Q.value=e),data:H.value,onDone:$},null,8,["visible","data"])):y("",!0)])}}});e("default",s(P,[["__scopeId","data-v-58d50984"]]))}}}));
