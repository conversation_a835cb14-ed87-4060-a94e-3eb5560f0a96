import{_ as p,a as i,c as r,b as t,d as s,a3 as n}from"./index-18a1ea24.js";import _ from"./index-a03f62e5.js";import"./ProductDisplayArea-5b66b134.js";import"./ShoppingCart-3699cb13.js";import"./pos-8a64eaa6.js";import"./performance-monitor-659a8228.js";import"./constants-2fa70699.js";import"./CartHeader-ed27259f.js";import"./CartItem-343bbd45.js";/* empty css              */import"./formatter-5a06da9d.js";import"./CartSummary-686b7994.js";import"./ToolbarPanel-6eb7f826.js";import"./FunctionButton-592779e0.js";/* empty css               */import"./OrderSuspend-3ebe2459.js";import"./OrderSummary-ec4b928f.js";import"./SuspendedOrderItem-54ab7d24.js";import"./PaymentPanel-5a383353.js";import"./CashPayment-9bc7318e.js";const a={class:"pos-test-page"},m={class:"page-header"},c={class:"debug-info"},d={class:"test-content"},l=Object.assign({name:"POSTest"},{__name:"pos-test",setup(u){return(f,o)=>{const e=n;return i(),r("div",a,[t("div",m,[o[0]||(o[0]=t("h2",null,"POS\u6536\u94F6\u53F0\u6D4B\u8BD5\u9875\u9762",-1)),o[1]||(o[1]=t("p",null,"\u6D4B\u8BD5\u4F18\u5316\u540E\u7684POS\u6536\u94F6\u53F0\u529F\u80FD",-1)),t("div",c,[s(e,{message:"\u8C03\u8BD5\u4FE1\u606F",description:"\u8BF7\u6253\u5F00\u6D4F\u89C8\u5668\u5F00\u53D1\u8005\u5DE5\u5177\u67E5\u770B\u63A7\u5236\u53F0\u8F93\u51FA\uFF0C\u68C0\u67E5API\u8C03\u7528\u548C\u6570\u636E\u52A0\u8F7D\u60C5\u51B5",type:"info","show-icon":"",closable:""})])]),t("div",d,[s(_)])])}}}),q=p(l,[["__scopeId","data-v-5f35db99"]]);export{q as default};
