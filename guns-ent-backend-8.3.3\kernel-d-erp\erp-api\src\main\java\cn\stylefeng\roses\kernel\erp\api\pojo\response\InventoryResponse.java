package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.response.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;

/**
 * 库存响应参数
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryResponse extends BaseResponse {

    /**
     * 库存ID
     */
    @ChineseDescription("库存ID")
    private Long id;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 商品简称
     */
    @ChineseDescription("商品简称")
    private String productShortName;

    /**
     * 条形码
     */
    @ChineseDescription("条形码")
    private String barcode;

    /**
     * 商品规格
     */
    @ChineseDescription("商品规格")
    private String specification;

    /**
     * 基本单位
     */
    @ChineseDescription("基本单位")
    private String unit;

    /**
     * 计价类型
     */
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 计价类型名称
     */
    @ChineseDescription("计价类型名称")
    private String pricingTypeName;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 经营方式
     */
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 经营方式名称
     */
    @ChineseDescription("经营方式名称")
    private String businessModeName;

    /**
     * 当前库存
     */
    @ChineseDescription("当前库存")
    private BigDecimal currentStock;

    /**
     * 最小库存（预警值）
     */
    @ChineseDescription("最小库存")
    private BigDecimal minStock;

    /**
     * 平均成本
     */
    @ChineseDescription("平均成本")
    private BigDecimal avgCost;

    /**
     * 库存总价值
     */
    @ChineseDescription("库存总价值")
    private BigDecimal totalValue;

    /**
     * 最后更新时间
     */
    @ChineseDescription("最后更新时间")
    private Date updateTime;

    /**
     * 是否库存预警
     */
    @ChineseDescription("是否库存预警")
    private Boolean isWarning;

    /**
     * 库存状态：NORMAL(正常)、WARNING(预警)、OUT_OF_STOCK(缺货)
     */
    @ChineseDescription("库存状态")
    private String inventoryStatus;

    /**
     * 库存状态名称
     */
    @ChineseDescription("库存状态名称")
    private String inventoryStatusName;

    /**
     * 商品分类ID
     */
    @ChineseDescription("商品分类ID")
    private Long categoryId;

    /**
     * 商品分类名称
     */
    @ChineseDescription("商品分类名称")
    private String categoryName;

    /**
     * 商品状态
     */
    @ChineseDescription("商品状态")
    private String productStatus;

    /**
     * 商品状态名称
     */
    @ChineseDescription("商品状态名称")
    private String productStatusName;

}