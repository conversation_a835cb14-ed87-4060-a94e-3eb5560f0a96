System.register([],(function(e,t){"use strict";return{execute:function(){class t{static getAlertTypeOptions(){return[{label:"库存不足",value:"LOW_STOCK",color:"orange"},{label:"零库存",value:"ZERO_STOCK",color:"red"},{label:"库存积压",value:"OVERSTOCK",color:"blue"},{label:"临期预警",value:"EXPIRY",color:"purple"}]}static getAlertLevelOptions(){return[{label:"紧急",value:"CRITICAL",color:"red"},{label:"警告",value:"WARNING",color:"orange"},{label:"提醒",value:"INFO",color:"blue"}]}static getTargetTypeOptions(){return[{label:"单个商品",value:"PRODUCT"},{label:"商品分类",value:"CATEGORY"},{label:"全部商品",value:"ALL"}]}static getThresholdTypeOptions(){return[{label:"数量",value:"QUANTITY"},{label:"百分比",value:"PERCENTAGE"},{label:"天数",value:"DAYS"}]}static getComparisonOperatorOptions(){return[{label:"小于等于",value:"LTE"},{label:"小于",value:"LT"},{label:"大于等于",value:"GTE"},{label:"大于",value:"GT"},{label:"等于",value:"EQ"}]}static getNotificationMethodOptions(){return[{label:"系统通知",value:"SYSTEM"},{label:"邮件通知",value:"EMAIL"},{label:"短信通知",value:"SMS"},{label:"微信通知",value:"WECHAT"}]}static getStatusOptions(){return[{label:"待处理",value:"PENDING",color:"orange"},{label:"处理中",value:"PROCESSING",color:"blue"},{label:"已解决",value:"RESOLVED",color:"green"},{label:"已忽略",value:"IGNORED",color:"gray"}]}static getAlertTypeName(e){const l=t.getAlertTypeOptions().find((t=>t.value===e));return l?l.label:e}static getAlertTypeInfo(e){const l=t.getAlertTypeOptions().find((t=>t.value===e));return l?{name:l.label,color:l.color}:{name:e,color:"default"}}static getAlertLevelInfo(e){const l=t.getAlertLevelOptions().find((t=>t.value===e));return l?{name:l.label,color:l.color}:{name:e,color:"default"}}static getStatusInfo(e){const l=t.getStatusOptions().find((t=>t.value===e));return l?{name:l.label,color:l.color}:{name:e,color:"default"}}static getTargetTypeName(e){const l=t.getTargetTypeOptions().find((t=>t.value===e));return l?l.label:e}static getThresholdTypeName(e){const l=t.getThresholdTypeOptions().find((t=>t.value===e));return l?l.label:e}static getComparisonOperatorName(e){const l=t.getComparisonOperatorOptions().find((t=>t.value===e));return l?l.label:e}static formatThresholdValue(e,t){if(null==e)return"-";switch(t){case"PERCENTAGE":return`${e}%`;case"DAYS":return`${e}天`;default:return e.toString()}}static getAlertLevelIcon(e){switch(e){case"CRITICAL":return"exclamation-circle";case"WARNING":return"warning";case"INFO":return"info-circle";default:return"question-circle"}}static getAlertTypeIcon(e){switch(e){case"LOW_STOCK":return"arrow-down";case"ZERO_STOCK":return"stop";case"OVERSTOCK":return"arrow-up";case"EXPIRY":return"clock-circle";default:return"alert"}}static validateRuleConfig(e){const t=[];return"PRODUCT"!==e.targetType&&"CATEGORY"!==e.targetType||e.targetId||t.push("请选择目标对象"),(null==e.thresholdValue||e.thresholdValue<0)&&t.push("阈值必须大于等于0"),"EXPIRY"===e.ruleType&&"DAYS"!==e.thresholdType&&t.push("临期预警的阈值类型必须是天数"),(null==e.checkFrequency||e.checkFrequency<1||e.checkFrequency>1440)&&t.push("检查频率必须在1-1440分钟之间"),t}static generateRuleDescription(e){const l=t.getAlertTypeName(e.ruleType),a=t.getTargetTypeName(e.targetType),r=t.formatThresholdValue(e.thresholdValue,e.thresholdType);return`${a}的${l}预警，当库存${t.getComparisonOperatorName(e.comparisonOperator)}${r}时触发`}}e("I",t)}}}));
