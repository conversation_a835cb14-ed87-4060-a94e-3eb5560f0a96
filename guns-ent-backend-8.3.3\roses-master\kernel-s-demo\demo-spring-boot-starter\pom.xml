<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>kernel-s-demo</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>demo-spring-boot-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--演示模块的业务-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>demo-business</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
