System.register(["./index-legacy-ee1db0c7.js"],(function(e,a){"use strict";var t,r,l,s,o,u,n,i,d,m,c,f,p,g,v,_,y,b,T,h,j;return{setters:[e=>{t=e.R,r=e.s,l=e.r,s=e.o,o=e.a,u=e.f,n=e.w,i=e.d,d=e.c,m=e.F,c=e.e,f=e.g,p=e.t,g=e.l,v=e.u,_=e.v,y=e.W,b=e.J,T=e.$,h=e.G,j=e.H}],execute:function(){class a{static findTimerPage(e){return t.getAndLoadData("/sysTimers/page",e)}static add(e){return t.post("/sysTimers/add",e)}static detail(e){return t.post("/sysTimers/detail",e)}static edit(e){return t.post("/sysTimers/edit",e)}static stop(e){return t.post("/sysTimers/stop",e)}static start(e){return t.post("/sysTimers/start",e)}static delete(e){return t.post("/sysTimers/delete",e)}static getActionClasses(){return t.postAndLoadData("/sysTimers/getActionClasses")}}e("S",a);const w=e("_",{__name:"timer-form",props:{form:Object},setup(e){const t=r({timerName:[{required:!0,message:"请输入任务名称",type:"string",trigger:"blur"}],cron:[{required:!0,message:"请输入cron任务表达式",type:"string",trigger:"blur"}],actionClass:[{required:!0,message:"请选择任务job",type:"string",trigger:"blur"}]}),w=l(!1),C=l([]);return s((()=>{w.value=!0,a.getActionClasses().then((e=>{C.value=e})).finally((()=>w.value=!1))})),(a,r)=>{const l=g,s=v,A=_,S=y,U=b,N=T,k=h,q=j;return o(),u(q,{ref:"formRef",model:e.form,rules:t,layout:"vertical"},{default:n((()=>[i(k,{gutter:20},{default:n((()=>[i(A,{span:24},{default:n((()=>[i(s,{label:"任务名称:",name:"timerName"},{default:n((()=>[i(l,{value:e.form.timerName,"onUpdate:value":r[0]||(r[0]=a=>e.form.timerName=a),"allow-clear":"",placeholder:"请输入任务名称"},null,8,["value"])])),_:1})])),_:1}),i(A,{span:24},{default:n((()=>[i(s,{label:"任务job:",name:"actionClass"},{default:n((()=>[i(U,{loading:w.value,showSearch:"",placeholder:"请选择任务job",value:e.form.actionClass,"onUpdate:value":r[1]||(r[1]=a=>e.form.actionClass=a),"allow-clear":""},{default:n((()=>[(o(!0),d(m,null,c(C.value,(e=>(o(),u(S,{key:e,value:e},{default:n((()=>[f(p(e),1)])),_:2},1032,["value"])))),128))])),_:1},8,["loading","value"])])),_:1})])),_:1}),i(A,{span:24},{default:n((()=>[i(s,{label:"cron:",name:"cron"},{default:n((()=>[i(l,{value:e.form.cron,"onUpdate:value":r[2]||(r[2]=a=>e.form.cron=a),placeholder:"请输入任务cron表达式","allow-clear":""},null,8,["value"])])),_:1})])),_:1}),i(A,{span:24},{default:n((()=>[i(s,{label:"任务参数:",name:"params"},{default:n((()=>[i(l,{value:e.form.params,"onUpdate:value":r[3]||(r[3]=a=>e.form.params=a),placeholder:"请输入任务参数","allow-clear":""},null,8,["value"])])),_:1})])),_:1}),i(A,{span:24},{default:n((()=>[i(s,{label:"备注"},{default:n((()=>[i(N,{value:e.form.remark,"onUpdate:value":r[4]||(r[4]=a=>e.form.remark=a),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}}),C=Object.freeze(Object.defineProperty({__proto__:null,default:w},Symbol.toStringTag,{value:"Module"}));e("t",C)}}}));
