package cn.stylefeng.roses.kernel.micro.tran.message.modular.controller;

import cn.stylefeng.roses.kernel.micro.api.enums.MessageQueueEnum;
import cn.stylefeng.roses.kernel.micro.api.pojo.TranMessage;
import cn.stylefeng.roses.kernel.micro.tran.message.core.msg.MessageSender;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 测试队列
 *
 * <AUTHOR>
 * @date 2020-05-07-1:56 下午
 */
@RestController
public class TestController {

    @Resource
    private MessageSender messageSender;

    /**
     * 测试队列
     *
     * <AUTHOR>
     * @date 2021/5/18 14:10
     */
    @RequestMapping("/testmq")
    @ResponseBody
    public SuccessResponseData test() {

        String messageId = IdWorker.getIdStr();
        String queue = MessageQueueEnum.MAKE_ORDER.name();
        TranMessage reliableMessage = new TranMessage(messageId, "aaaaaaaaa", queue);
        reliableMessage.setBizUniqueId(123L);

        messageSender.sendMessage(reliableMessage);
        return new SuccessResponseData();
    }

}
