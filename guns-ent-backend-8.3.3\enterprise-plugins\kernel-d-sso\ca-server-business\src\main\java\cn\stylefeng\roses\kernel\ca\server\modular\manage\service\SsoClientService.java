package cn.stylefeng.roses.kernel.ca.server.modular.manage.service;

import cn.stylefeng.roses.kernel.ca.server.modular.manage.entity.SsoClient;
import cn.stylefeng.roses.kernel.ca.server.modular.manage.pojo.request.SsoClientRequest;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 单点登录客户端服务类
 *
 * <AUTHOR>
 * @since 2023/11/05 09:28
 */
public interface SsoClientService extends IService<SsoClient> {

    /**
     * 新增单点登录客户端
     *
     * @param ssoClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    void add(SsoClientRequest ssoClientRequest);

    /**
     * 删除单点登录客户端
     *
     * @param ssoClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    void del(SsoClientRequest ssoClientRequest);

    /**
     * 批量删除单点登录客户端
     *
     * @param ssoClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    void batchDelete(SsoClientRequest ssoClientRequest);

    /**
     * 编辑单点登录客户端
     *
     * @param ssoClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    void edit(SsoClientRequest ssoClientRequest);

    /**
     * 查询详情单点登录客户端
     *
     * @param ssoClientRequest 请求参数
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    SsoClient detail(SsoClientRequest ssoClientRequest);

    /**
     * 获取单点登录客户端列表
     *
     * @param ssoClientRequest 请求参数
     * @return List<SsoClient>  返回结果
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    List<SsoClient> findList(SsoClientRequest ssoClientRequest);

    /**
     * 获取单点登录客户端分页列表
     *
     * @param ssoClientRequest 请求参数
     * @return PageResult<SsoClient>   返回结果
     * <AUTHOR>
     * @since 2023/11/05 09:28
     */
    PageResult<SsoClient> findPage(SsoClientRequest ssoClientRequest);

    /**
     * 更新状态
     *
     * <AUTHOR>
     * @since 2023/11/5 11:56
     */
    void updateStatus(SsoClientRequest ssoClientRequest);

}
