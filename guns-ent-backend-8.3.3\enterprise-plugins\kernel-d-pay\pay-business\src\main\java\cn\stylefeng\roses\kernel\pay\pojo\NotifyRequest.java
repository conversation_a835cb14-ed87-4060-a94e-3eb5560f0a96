package cn.stylefeng.roses.kernel.pay.pojo;

import lombok.Data;

/**
 * 支付回调的参数
 *
 * <AUTHOR>
 * @date 2021/6/24 13:56
 */
@Data
public class NotifyRequest {

    /**
     * 支付结果 【1：成功；0：失败】
     */
    private String code;

    /**
     * 系统订单号（YunGouOS系统内单号）
     */
    private String orderNo;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 支付单号（第三方支付单号）
     */
    private String payNo;

    /**
     * 支付金额 单位：元
     */
    private String money;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 支付渠道：alipay，wxpay
     */
    private String payChannel;

    /**
     * 支付成功时间
     */
    private String time;

    /**
     * 附加数据，回调时候原路返回
     */
    private String attach;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 签名（见签名算法文档）
     */
    private String sign;

}
