.sidebar-content {
  .ant-menu-inline {
    border-right: 0;
  }
  .ant-menu {
    --component-background: #fff !important;
    --background-color-light: #fff !important;
  }
  .ant-menu-inline .ant-menu-item::after {
    border-right: 0;
  }
  .ant-menu-submenu-selected {
    color: #999;
  }
  .ant-menu-submenu-title {
    font-size: 14px;
    padding-left: 10px !important;
    color: #60666b;
    &:hover {
      color: #60666b;
    }
  }
  .ant-menu-item-selected {
    color: #0f56d7 !important;
    border-radius: 4px;
    font-weight: 700 !important;
    background: rgba(207, 221, 247, 0.35) !important;
    .iconfont {
      color: #0f56d7 !important;
      font-weight: normal !important;
    }
  }
  .ant-menu-item:active {
    color: #0f56d7 !important;
    border-radius: 4px;
    font-weight: 500 !important;
    background: rgba(207, 221, 247, 0.35) !important;
    .iconfont {
      color: #0f56d7 !important;
    }
  }
  .ant-menu-item:hover {
    background: #f3f3f3;
    border-radius: 4px;
  }
  .ant-menu-item {
    font-weight: 400;
    color: #43505e;
    padding-left: 20px !important;
  }
  .ant-menu-submenu {
    border-top: 1px solid #eee;
  }
  .ant-menu-submenu:first-child {
    border-top: 1px solid #fbfbfb;
  }
  .ant-menu-submenu-title:active {
    background: #fbfbfb !important;
  }
  .ant-menu-submenu-arrow {
    display: none;
  }
}
