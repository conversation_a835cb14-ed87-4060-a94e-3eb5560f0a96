<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_17">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="kernel-a-rule" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:3.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:3.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:6.1.13" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:6.1.13" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:6.1.13" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:6.1.13" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-observation:1.12.10" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-commons:1.12.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:3.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:3.2.10" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.4.14" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.4.14" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:2.0.16" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.21.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.21.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:2.0.16" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:6.1.13" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:6.1.13" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:2.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-annotation:3.5.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.15.4" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.8.29" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.34" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:2.0.52" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.fastjson2:fastjson2-extension:2.0.52" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.fastjson2:fastjson2:2.0.52" level="project" />
    <orderEntry type="library" name="Maven: com.github.whvcse:easy-captcha:1.6.2" level="project" />
  </component>
</module>