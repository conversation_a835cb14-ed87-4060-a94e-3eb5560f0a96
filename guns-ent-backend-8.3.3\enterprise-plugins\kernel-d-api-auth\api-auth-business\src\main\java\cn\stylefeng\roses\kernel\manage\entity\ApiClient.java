package cn.stylefeng.roses.kernel.manage.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseBusinessEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * API客户端实例类
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
@TableName("ent_api_client")
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiClient extends BaseBusinessEntity {

    /**
     * API客户端ID
     */
    @TableId(value = "api_client_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("API客户端ID")
    private Long apiClientId;

    /**
     * API客户端名称
     */
    @TableField("api_client_name")
    @ChineseDescription("API客户端名称")
    private String apiClientName;

    /**
     * API客户端编号
     */
    @TableField("api_client_code")
    @ChineseDescription("API客户端编号")
    private String apiClientCode;

    /**
     * API客户端秘钥明文，用来获取API调用的token
     */
    @TableField("api_client_secret")
    @ChineseDescription("API客户端秘钥明文，用来获取API调用的token")
    private String apiClientSecret;

    /**
     * 公钥，加密数据用
     */
    @TableField("api_public_key")
    @ChineseDescription("公钥，加密数据用")
    private String apiPublicKey;

    /**
     * 私钥，解密数据用
     */
    @TableField("api_private_key")
    @ChineseDescription("私钥，解密数据用")
    private String apiPrivateKey;

    /**
     * token过期时间，单位：秒
     */
    @TableField("api_client_token_expiration")
    @ChineseDescription("token过期时间，单位：秒")
    private Integer apiClientTokenExpiration;

    /**
     * 排序
     */
    @TableField("api_client_sort")
    @ChineseDescription("排序")
    private BigDecimal apiClientSort;

    /**
     * 状态：1-启用，2-禁用
     */
    @TableField("api_client_status")
    @ChineseDescription("状态：1-启用，2-禁用")
    private Integer apiClientStatus;

}
