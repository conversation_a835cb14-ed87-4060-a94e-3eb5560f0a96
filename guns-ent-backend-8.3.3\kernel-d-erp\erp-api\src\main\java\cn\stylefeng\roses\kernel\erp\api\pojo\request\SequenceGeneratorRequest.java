package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 序列号生成器请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SequenceGeneratorRequest extends BaseRequest {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {edit.class, delete.class, detail.class})
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 序列名称
     */
    @NotBlank(message = "序列名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("序列名称")
    private String sequenceName;

    /**
     * 前缀
     */
    @ChineseDescription("前缀")
    private String prefix;

    /**
     * 当前值
     */
    @ChineseDescription("当前值")
    private Long currentValue;

    /**
     * 步长
     */
    @ChineseDescription("步长")
    private Integer step;

    /**
     * 日期格式（如：yyyyMMdd）
     */
    @ChineseDescription("日期格式")
    private String dateFormat;

    /**
     * 重置类型：NONE(不重置)、DAILY(每日)、MONTHLY(每月)、YEARLY(每年)
     */
    @ChineseDescription("重置类型")
    private String resetType;

    /**
     * 最后重置日期
     */
    @ChineseDescription("最后重置日期")
    private LocalDate lastResetDate;

    /**
     * 重置值（用于手动重置）
     */
    @ChineseDescription("重置值")
    private Long resetValue;

}
