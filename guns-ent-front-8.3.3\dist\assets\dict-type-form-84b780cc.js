import{s as T,a as i,f,w as a,d as e,g as r,h as b,l as C,u as x,v as w,z as U,A as N,y as B,$ as q,G as F,H as S}from"./index-18a1ea24.js";/* empty css              */const R={__name:"dict-type-form",props:{form:Object,isUpdate:Boolean},setup(l){const p=l,_=T({dictTypeName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5B57\u5178\u7C7B\u578B\u540D\u79F0",type:"string",trigger:"blur"}],dictTypeCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5B57\u5178\u7C7B\u578B\u7F16\u7801",type:"string",trigger:"blur"}],dictTypeSort:[{required:!0,message:"\u8BF7\u8F93\u5165\u6392\u5E8F",type:"number",trigger:"blur"}],dictTypeClass:[{required:!0,message:"\u8BF7\u9009\u62E9\u5B57\u5178\u7C7B\u578B",type:"number",trigger:"change"}],statusFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u5B57\u5178\u7C7B\u578B\u72B6\u6001",type:"number",trigger:"change"}]});return(k,t)=>{const s=C,o=x,d=w,n=U,m=N,c=B,g=q,y=F,v=S;return i(),f(v,{ref:"formRef",model:l.form,rules:_,layout:"vertical"},{default:a(()=>[e(y,{gutter:20},{default:a(()=>[e(d,{xs:24,sm:24,md:12},{default:a(()=>[e(o,{label:"\u5B57\u5178\u7C7B\u578B\u540D\u79F0:",name:"dictTypeName"},{default:a(()=>[e(s,{value:l.form.dictTypeName,"onUpdate:value":t[0]||(t[0]=u=>l.form.dictTypeName=u),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u7C7B\u578B\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(d,{xs:24,sm:24,md:12},{default:a(()=>[e(o,{label:"\u5B57\u5178\u7C7B\u578B\u7F16\u7801:",name:"dictTypeCode"},{default:a(()=>[e(s,{value:l.form.dictTypeCode,"onUpdate:value":t[1]||(t[1]=u=>l.form.dictTypeCode=u),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u7C7B\u578B\u7F16\u7801",disabled:p.isUpdate},null,8,["value","disabled"])]),_:1})]),_:1}),e(d,{xs:24,sm:24,md:12},{default:a(()=>[e(o,{label:"\u5B57\u5178\u7C7B\u578B:",name:"dictTypeClass"},{default:a(()=>[e(m,{value:l.form.dictTypeClass,"onUpdate:value":t[2]||(t[2]=u=>l.form.dictTypeClass=u)},{default:a(()=>[e(n,{value:1},{default:a(()=>t[7]||(t[7]=[r("\u7CFB\u7EDF\u7C7B\u578B")])),_:1,__:[7]}),e(n,{value:2},{default:a(()=>t[8]||(t[8]=[r("\u4E1A\u52A1\u7C7B\u578B")])),_:1,__:[8]})]),_:1},8,["value"])]),_:1})]),_:1}),e(d,{xs:24,sm:24,md:12},{default:a(()=>[e(o,{label:"\u5B57\u5178\u7C7B\u578B\u72B6\u6001:",name:"statusFlag"},{default:a(()=>[e(m,{value:l.form.statusFlag,"onUpdate:value":t[3]||(t[3]=u=>l.form.statusFlag=u)},{default:a(()=>[e(n,{value:1},{default:a(()=>t[9]||(t[9]=[r("\u542F\u7528")])),_:1,__:[9]}),e(n,{value:2},{default:a(()=>t[10]||(t[10]=[r("\u7981\u7528")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1})]),_:1}),e(d,{xs:24,sm:24,md:12},{default:a(()=>[e(o,{label:"\u6392\u5E8F:",name:"dictTypeSort"},{default:a(()=>[e(c,{value:l.form.dictTypeSort,"onUpdate:value":t[4]||(t[4]=u=>l.form.dictTypeSort=u),min:0,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),l.form.dictTypeClass==2?(i(),f(d,{key:0,xs:24,sm:24,md:12},{default:a(()=>[e(o,{label:"\u4E1A\u52A1\u7F16\u7801:",name:"dictTypeBusCode"},{default:a(()=>[e(s,{value:l.form.dictTypeBusCode,"onUpdate:value":t[5]||(t[5]=u=>l.form.dictTypeBusCode=u),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u4E1A\u52A1\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1})):b("",!0),e(d,{span:24},{default:a(()=>[e(o,{label:"\u5907\u6CE8"},{default:a(()=>[e(g,{value:l.form.dictTypeDesc,"onUpdate:value":t[6]||(t[6]=u=>l.form.dictTypeDesc=u),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:4},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{R as default};
