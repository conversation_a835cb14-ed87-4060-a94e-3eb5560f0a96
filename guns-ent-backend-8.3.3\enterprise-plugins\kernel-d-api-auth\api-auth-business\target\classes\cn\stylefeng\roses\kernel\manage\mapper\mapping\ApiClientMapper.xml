<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.apiauth1.mapper.ApiClientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.manage.entity.ApiClient">
		<id column="api_client_id" property="apiClientId" />
		<result column="api_client_name" property="apiClientName" />
		<result column="api_client_code" property="apiClientCode" />
		<result column="api_client_secret" property="apiClientSecret" />
		<result column="api_public_key" property="apiPublicKey" />
		<result column="api_private_key" property="apiPrivateKey" />
		<result column="api_client_token_expiration" property="apiClientTokenExpiration" />
		<result column="api_client_sort" property="apiClientSort" />
		<result column="api_client_status" property="apiClientStatus" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<sql id="Base_Column_List">
		api_client_id,api_client_name,api_client_code,api_client_secret,api_public_key,api_private_key,api_client_token_expiration,api_client_sort,api_client_status,create_time,create_user,update_time,update_user,del_flag
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.manage.pojo.response.ApiClientVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
        ent_api_client tbl
		WHERE
		<where>
        <if test="param.apiClientId != null and param.apiClientId != ''">
            and tbl.api_client_id like concat('%',#{param.apiClientId},'%')
        </if>
        <if test="param.apiClientName != null and param.apiClientName != ''">
            and tbl.api_client_name like concat('%',#{param.apiClientName},'%')
        </if>
        <if test="param.apiClientCode != null and param.apiClientCode != ''">
            and tbl.api_client_code like concat('%',#{param.apiClientCode},'%')
        </if>
        <if test="param.apiClientSecret != null and param.apiClientSecret != ''">
            and tbl.api_client_secret like concat('%',#{param.apiClientSecret},'%')
        </if>
        <if test="param.apiPublicKey != null and param.apiPublicKey != ''">
            and tbl.api_public_key like concat('%',#{param.apiPublicKey},'%')
        </if>
        <if test="param.apiPrivateKey != null and param.apiPrivateKey != ''">
            and tbl.api_private_key like concat('%',#{param.apiPrivateKey},'%')
        </if>
        <if test="param.apiClientTokenExpiration != null and param.apiClientTokenExpiration != ''">
            and tbl.api_client_token_expiration like concat('%',#{param.apiClientTokenExpiration},'%')
        </if>
        <if test="param.apiClientSort != null and param.apiClientSort != ''">
            and tbl.api_client_sort like concat('%',#{param.apiClientSort},'%')
        </if>
        <if test="param.apiClientStatus != null and param.apiClientStatus != ''">
            and tbl.api_client_status like concat('%',#{param.apiClientStatus},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
        <if test="param.delFlag != null and param.delFlag != ''">
            and tbl.del_flag like concat('%',#{param.delFlag},'%')
        </if>
		</where>
	</select>

</mapper>
