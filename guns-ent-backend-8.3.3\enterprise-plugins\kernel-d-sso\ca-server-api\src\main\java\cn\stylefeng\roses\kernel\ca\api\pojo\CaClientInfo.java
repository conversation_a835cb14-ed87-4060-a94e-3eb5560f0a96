package cn.stylefeng.roses.kernel.ca.api.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

/**
 * 存放客户端token的信息
 *
 * <AUTHOR>
 * @date 2022/5/20 10:35
 */
@Data
public class CaClientInfo {

    /**
     * 业务应用的id
     */
    @ChineseDescription("业务应用的id")
    private Long clientId;

    /**
     * 应用名称
     */
    @ChineseDescription("应用名称")
    private String clientName;

    /**
     * 是否统一退出：Y-是，N-否
     */
    @ChineseDescription("是否统一退出：Y-是，N-否")
    private String unifiedLogoutFlag;

    /**
     * 退出地址，单点退出回调应用地址
     */
    @ChineseDescription("退出地址，单点退出回调应用地址")
    private String ssoLogoutUrl;

}
