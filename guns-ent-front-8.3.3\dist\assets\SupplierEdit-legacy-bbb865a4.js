System.register(["./index-legacy-ee1db0c7.js","./index-legacy-0d30ef09.js","./index-legacy-94a6fc23.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js","./index-legacy-b540c599.js","./regionApi-legacy-73888494.js"],(function(e,l){"use strict";var a,t,n,u,o,d,s,r,i,p,m,c,f,v,g,b,_,h,y,x,N,U,S,C,E,I,M,w,O,A,k,R;return{setters:[e=>{a=e._,t=e.aq,n=e.s,u=e.r,o=e.X,d=e.m,s=e.k,r=e.a,i=e.f,p=e.w,m=e.d,c=e.c,f=e.F,v=e.e,g=e.g,b=e.t,_=e.b,h=e.h,y=e.l,x=e.u,N=e.v,U=e.G,S=e.W,C=e.J,E=e.$,I=e.U,M=e.a7,w=e.y,O=e.H,A=e.M},e=>{k=e.R},null,null,e=>{R=e.S},null,null],execute:function(){var l=document.createElement("style");l.textContent="[data-v-07cd38db] .ant-modal{z-index:1000!important}[data-v-07cd38db] .ant-modal-mask{z-index:999!important}.ant-form-item[data-v-07cd38db]{margin-bottom:16px}.ant-form-item-label[data-v-07cd38db]{font-weight:500}.ant-select-multiple .ant-select-selection-item[data-v-07cd38db]{background:#f6f6f6;border:1px solid #d9d9d9;border-radius:4px}.ant-input[data-v-07cd38db],.ant-select[data-v-07cd38db]{border-radius:4px}@media (max-width: 768px){[data-v-07cd38db] .ant-modal{width:95%!important;margin:10px auto}[data-v-07cd38db] .ant-form-item-label{text-align:left!important}}\n",document.head.appendChild(l);const T={name:"SupplierEdit",components:{RegionSelector:k,QuestionCircleOutlined:t},props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:l}){const a=n({businessMode:"PURCHASE_SALE",salesDeduction:null}),t=u(null),s=u(null),r=u(!1),i=R.getSupplierTypeOptions(),p=R.getSupplierStatusOptions(),m=R.getCreditLevelOptions(),c=n({supplierCode:[{required:!0,message:"请输入供应商编码",trigger:"blur"},{max:50,message:"供应商编码不能超过50个字符",trigger:"blur"}],supplierName:[{required:!0,message:"请输入供应商名称",trigger:"blur"},{max:200,message:"供应商名称不能超过200个字符",trigger:"blur"}],supplierType:[{required:!0,message:"请选择供应商类型",trigger:"change"}],businessMode:[{required:!0,message:"请选择经营方式",trigger:"change"}],contactEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],contactPhone:[{pattern:/^[0-9-()（）\s]+$/,message:"请输入正确的电话号码",trigger:"blur"}],contactMobile:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],salesDeduction:[{validator:(e,l)=>"JOINT_VENTURE"!==a.businessMode&&"CONSIGNMENT"!==a.businessMode||null!=l?null!=l&&(l<0||l>100)?Promise.reject("销售扣点必须在0-100之间"):Promise.resolve():Promise.reject("请输入销售扣点"),trigger:"blur"}]}),f=e=>{l("update:visible",e)};return o((()=>e.visible),(async l=>{var t;if(console.log("SupplierEdit watch visible 变化:",l,"props.data:",e.data),l&&null!==(t=e.data)&&void 0!==t&&t.supplierId){Object.assign(a,e.data),a.businessMode||(a.businessMode="PURCHASE_SALE");try{r.value=!0;const l=await R.getSupplierRegions({supplierId:e.data.supplierId});l&&Array.isArray(l)?a.regionIds=l.map((e=>e.regionId)):a.regionIds=[]}catch(n){console.error("获取供应商关联区域失败:",n),d.error("获取供应商关联区域失败"),a.regionIds=[]}finally{r.value=!1}}}),{immediate:!0}),{form:a,formRef:t,regionSelectorRef:s,loading:r,supplierTypeOptions:i,statusOptions:p,creditLevelOptions:m,rules:c,updateVisible:f,handleBusinessModeChange:e=>{console.log("经营方式变更:",e),"PURCHASE_SALE"===e&&(a.salesDeduction=null)},handleSalesDeductionChange:e=>{console.log("销售扣点变更:",e)},save:async()=>{try{await t.value.validate(),r.value=!0;const e={...a};await R.edit(e),a.supplierId&&await R.updateSupplierRegions({supplierId:a.supplierId,regionIds:a.regionIds||[]}),d.success("编辑供应商成功"),f(!1),l("done")}catch(e){console.error("编辑供应商失败:",e),d.error(e.message||"编辑失败")}finally{r.value=!1}},handleRegionChange:(e,l)=>{console.log("供应商区域选择变化:",e,l),a.regionIds=e}}}},j={style:{display:"flex","align-items":"center",gap:"8px"}},P={style:{display:"flex","justify-content":"space-between","align-items":"center"}},L={style:{display:"flex","justify-content":"space-between","align-items":"center"}},V={style:{display:"flex","justify-content":"space-between","align-items":"center"}};e("default",a(T,[["render",function(e,l,a,t,n,u){const o=y,d=x,R=N,T=U,D=S,q=C,z=E,H=I,J=s("question-circle-outlined"),B=M,G=w,$=k,F=O,Q=A;return r(),i(Q,{visible:a.visible,title:"编辑供应商",width:800,"confirm-loading":t.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":t.updateVisible,onOk:t.save},{default:p((()=>[m(F,{ref:"formRef",model:t.form,rules:t.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:p((()=>[m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"供应商编码",name:"supplierCode"},{default:p((()=>[m(o,{value:t.form.supplierCode,"onUpdate:value":l[0]||(l[0]=e=>t.form.supplierCode=e),placeholder:"请输入供应商编码"},null,8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"供应商名称",name:"supplierName"},{default:p((()=>[m(o,{value:t.form.supplierName,"onUpdate:value":l[1]||(l[1]=e=>t.form.supplierName=e),placeholder:"请输入供应商名称"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"供应商简称",name:"supplierShortName"},{default:p((()=>[m(o,{value:t.form.supplierShortName,"onUpdate:value":l[2]||(l[2]=e=>t.form.supplierShortName=e),placeholder:"请输入供应商简称"},null,8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"供应商类型",name:"supplierType"},{default:p((()=>[m(q,{value:t.form.supplierType,"onUpdate:value":l[3]||(l[3]=e=>t.form.supplierType=e),placeholder:"请选择供应商类型"},{default:p((()=>[(r(!0),c(f,null,v(t.supplierTypeOptions,(e=>(r(),i(D,{key:e.value,value:e.value},{default:p((()=>[g(b(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"联系人",name:"contactPerson"},{default:p((()=>[m(o,{value:t.form.contactPerson,"onUpdate:value":l[4]||(l[4]=e=>t.form.contactPerson=e),placeholder:"请输入联系人"},null,8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"联系电话",name:"contactPhone"},{default:p((()=>[m(o,{value:t.form.contactPhone,"onUpdate:value":l[5]||(l[5]=e=>t.form.contactPhone=e),placeholder:"请输入联系电话"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"手机号码",name:"contactMobile"},{default:p((()=>[m(o,{value:t.form.contactMobile,"onUpdate:value":l[6]||(l[6]=e=>t.form.contactMobile=e),placeholder:"请输入手机号码"},null,8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"邮箱地址",name:"contactEmail"},{default:p((()=>[m(o,{value:t.form.contactEmail,"onUpdate:value":l[7]||(l[7]=e=>t.form.contactEmail=e),placeholder:"请输入邮箱地址"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{span:24},{default:p((()=>[m(d,{label:"联系地址",name:"contactAddress","label-col":{span:3},"wrapper-col":{span:21}},{default:p((()=>[m(z,{value:t.form.contactAddress,"onUpdate:value":l[8]||(l[8]=e=>t.form.contactAddress=e),placeholder:"请输入联系地址",rows:2},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"营业执照号",name:"businessLicenseNo"},{default:p((()=>[m(o,{value:t.form.businessLicenseNo,"onUpdate:value":l[9]||(l[9]=e=>t.form.businessLicenseNo=e),placeholder:"请输入营业执照号"},null,8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"税务登记号",name:"taxNo"},{default:p((()=>[m(o,{value:t.form.taxNo,"onUpdate:value":l[10]||(l[10]=e=>t.form.taxNo=e),placeholder:"请输入税务登记号"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"开户银行",name:"bankName"},{default:p((()=>[m(o,{value:t.form.bankName,"onUpdate:value":l[11]||(l[11]=e=>t.form.bankName=e),placeholder:"请输入开户银行"},null,8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"银行账号",name:"bankAccount"},{default:p((()=>[m(o,{value:t.form.bankAccount,"onUpdate:value":l[12]||(l[12]=e=>t.form.bankAccount=e),placeholder:"请输入银行账号"},null,8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"经营方式",name:"businessMode"},{default:p((()=>[_("div",j,[m(q,{value:t.form.businessMode,"onUpdate:value":l[13]||(l[13]=e=>t.form.businessMode=e),placeholder:"请选择经营方式",onChange:t.handleBusinessModeChange,style:{flex:"1"}},{default:p((()=>[m(D,{value:"PURCHASE_SALE"},{default:p((()=>[_("div",P,[l[20]||(l[20]=_("span",null,"购销",-1)),m(H,{color:"blue",size:"small"},{default:p((()=>l[19]||(l[19]=[g("采购销售")]))),_:1,__:[19]})])])),_:1}),m(D,{value:"JOINT_VENTURE"},{default:p((()=>[_("div",L,[l[22]||(l[22]=_("span",null,"联营",-1)),m(H,{color:"green",size:"small"},{default:p((()=>l[21]||(l[21]=[g("联合经营")]))),_:1,__:[21]})])])),_:1}),m(D,{value:"CONSIGNMENT"},{default:p((()=>[_("div",V,[l[24]||(l[24]=_("span",null,"代销",-1)),m(H,{color:"orange",size:"small"},{default:p((()=>l[23]||(l[23]=[g("代理销售")]))),_:1,__:[23]})])])),_:1})])),_:1},8,["value","onChange"]),m(B,null,{title:p((()=>l[25]||(l[25]=[_("div",null,[_("div",{style:{"margin-bottom":"8px"}},[_("strong",null,"购销："),g("需要采购入库，管理库存，按采购价结算。适用于传统的采购销售模式。")]),_("div",{style:{"margin-bottom":"8px"}},[_("strong",null,"联营："),g("不需要采购入库，不管理库存，商品归属于供应商，按销售扣点结算。适用于品牌专柜等场景。")]),_("div",null,[_("strong",null,"代销："),g("需要采购入库，管理库存，按实际销售扣点结算。适用于代理销售模式。")])],-1)]))),default:p((()=>[m(J,{style:{color:"#1890ff",cursor:"pointer"}})])),_:1})])])),_:1})])),_:1}),"JOINT_VENTURE"===t.form.businessMode||"CONSIGNMENT"===t.form.businessMode?(r(),i(R,{key:0,md:12,sm:24},{default:p((()=>[m(d,{label:"销售扣点",name:"salesDeduction"},{default:p((()=>[m(G,{value:t.form.salesDeduction,"onUpdate:value":l[14]||(l[14]=e=>t.form.salesDeduction=e),min:0,max:100,precision:2,step:.1,placeholder:"请输入销售扣点",style:{width:"100%"}},{addonAfter:p((()=>l[26]||(l[26]=[g("%")]))),_:1},8,["value"])])),_:1})])),_:1})):h("",!0)])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"信用等级",name:"creditLevel"},{default:p((()=>[m(q,{value:t.form.creditLevel,"onUpdate:value":l[15]||(l[15]=e=>t.form.creditLevel=e),placeholder:"请选择信用等级"},{default:p((()=>[(r(!0),c(f,null,v(t.creditLevelOptions,(e=>(r(),i(D,{key:e.value,value:e.value},{default:p((()=>[g(b(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),m(R,{md:12,sm:24},{default:p((()=>[m(d,{label:"状态",name:"status"},{default:p((()=>[m(q,{value:t.form.status,"onUpdate:value":l[16]||(l[16]=e=>t.form.status=e),placeholder:"请选择状态"},{default:p((()=>[(r(!0),c(f,null,v(t.statusOptions,(e=>(r(),i(D,{key:e.value,value:e.value},{default:p((()=>[g(b(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{span:24},{default:p((()=>[m(d,{label:"关联区域",name:"regionIds","label-col":{span:3},"wrapper-col":{span:21}},{default:p((()=>[m($,{ref:"regionSelectorRef",modelValue:t.form.regionIds,"onUpdate:modelValue":l[17]||(l[17]=e=>t.form.regionIds=e),placeholder:"请选择供应商服务的区域",onChange:t.handleRegionChange},null,8,["modelValue","onChange"])])),_:1})])),_:1})])),_:1}),m(T,{gutter:16},{default:p((()=>[m(R,{span:24},{default:p((()=>[m(d,{label:"备注",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:p((()=>[m(z,{value:t.form.remark,"onUpdate:value":l[18]||(l[18]=e=>t.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["visible","confirm-loading","onUpdate:visible","onOk"])}],["__scopeId","data-v-07cd38db"]]))}}}));
