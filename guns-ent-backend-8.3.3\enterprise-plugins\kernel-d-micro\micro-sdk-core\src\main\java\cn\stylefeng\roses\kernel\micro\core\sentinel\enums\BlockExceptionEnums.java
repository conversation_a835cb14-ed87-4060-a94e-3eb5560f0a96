package cn.stylefeng.roses.kernel.micro.core.sentinel.enums;

import cn.stylefeng.roses.kernel.rule.pojo.response.ErrorResponseData;
import lombok.Getter;

/**
 * sentinel流控降解的响应
 *
 * <AUTHOR>
 * @date 2019/9/1 16:01
 */
@Getter
public enum BlockExceptionEnums {

    /**
     * 当前请求被限流
     */
    FLOW_EXCEPTION("450", "当前请求被限流"),

    /**
     * 当前请求被降级
     */
    DEGRADE_EXCEPTION("451", "当前请求被降级"),

    /**
     * 热点参数限流
     */
    PARAM_FLOW_EXCEPTION("452", "热点参数限流"),

    /**
     * 系统规则限流
     */
    SYSTEM_BLOCK_EXCEPTION("453", "系统规则限流"),

    /**
     * 授权规则不通过
     */
    AUTHORITY_EXCEPTION("454", "授权规则不通过");

    private final ErrorResponseData errorResponseData;

    BlockExceptionEnums(String code, String message) {
        this.errorResponseData = new ErrorResponseData(code, message);
    }

}
