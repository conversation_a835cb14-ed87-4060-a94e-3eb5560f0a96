package cn.stylefeng.roses.kernel.ca.server.modular.manage.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseBusinessEntity;
import cn.stylefeng.roses.kernel.file.api.format.FileUrlFormatProcess;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.annotation.SimpleFieldFormat;
import cn.stylefeng.roses.kernel.rule.enums.FormatTypeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 单点登录客户端实例类
 *
 * <AUTHOR>
 * @since 2023/11/05 09:28
 */
@TableName("ent_sso_client")
@Data
@EqualsAndHashCode(callSuper = true)
public class SsoClient extends BaseBusinessEntity {

    /**
     * 业务应用的id
     */
    @TableId(value = "client_id", type = IdType.ASSIGN_ID)
    @ChineseDescription("业务应用的id")
    private Long clientId;

    /**
     * 应用名称
     */
    @TableField("client_name")
    @ChineseDescription("应用名称")
    private String clientName;

    /**
     * 应用图标的文件id
     */
    @TableField("client_logo_file_id")
    @ChineseDescription("应用图标的文件id")
    @SimpleFieldFormat(formatType = FormatTypeEnum.ADD_FIELD, processClass = FileUrlFormatProcess.class)
    private Long clientLogoFileId;

    /**
     * 登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面
     */
    @TableField("login_page_type")
    @ChineseDescription("登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面")
    private Integer loginPageType;

    /**
     * 是否统一退出：Y-是，N-否
     */
    @TableField("unified_logout_flag")
    @ChineseDescription("是否统一退出：Y-是，N-否")
    private String unifiedLogoutFlag;

    /**
     * 回调业务地址，单点登录到业务端时，跳转到业务端的地址
     */
    @TableField("sso_callback_url")
    @ChineseDescription("回调业务地址，单点登录到业务端时，跳转到业务端的地址")
    private String ssoCallbackUrl;

    /**
     * 退出地址，从认证中心退出后，通知业务端的地址
     */
    @TableField("sso_logout_url")
    @ChineseDescription("退出地址，从认证中心退出后，通知业务端的地址")
    private String ssoLogoutUrl;

    /**
     * 应用登录的地址，针对自定义登录界面
     */
    @TableField("custom_login_url")
    @ChineseDescription("应用登录的地址，针对自定义登录界面")
    private String customLoginUrl;

    /**
     * 加密和解密的密钥，针对单点到业务系统的token（对称加密）
     */
    @TableField("ca_token_secret")
    @ChineseDescription("加密和解密的密钥，针对单点到业务系统的token（对称加密）")
    private String caTokenSecret;

    /**
     * 状态：1-启用，2-禁用
     */
    @TableField("client_status")
    @ChineseDescription("状态：1-启用，2-禁用")
    private Integer clientStatus;

    /**
     * 排序码
     */
    @TableField("client_sort")
    @ChineseDescription("排序码")
    private BigDecimal clientSort;

    /**
     * 应用的描述
     */
    @TableField("client_description")
    @ChineseDescription("应用的描述")
    private String clientDescription;

}
