# 区域树组件

区域树组件是一个可复用的Vue 3组件，用于显示和操作区域的树形结构。该组件支持搜索、懒加载、节点选择、展开/折叠等功能，并提供了丰富的自定义选项。

## 功能特点

- 树形结构展示区域数据
- 支持搜索过滤
- 支持节点展开/折叠
- 支持单选和多选模式
- 支持懒加载子节点
- 支持自定义API接口
- 支持节点编辑操作（新增、编辑、删除）
- 支持数量标记显示
- 提供丰富的事件和方法

## 使用方法

### 基本用法

```vue
<template>
  <div style="height: 500px;">
    <region-tree @treeSelect="handleTreeSelect" />
  </div>
</template>

<script setup>
import RegionTree from '@/components/common/RegionTree/index.vue'

const handleTreeSelect = (selectedKeys, metadata) => {
  console.log('选中的节点:', selectedKeys)
  console.log('选中的节点数据:', metadata)
}
</script>
```

### 带编辑功能

```vue
<template>
  <div style="height: 500px;">
    <region-tree 
      :isShowEditIcon="true"
      @treeSelect="handleTreeSelect"
      @addRegion="handleAddRegion"
      @editRegion="handleEditRegion"
      @deleteRegion="handleDeleteRegion"
    />
  </div>
</template>

<script setup>
import RegionTree from '@/components/common/RegionTree/index.vue'
import { message } from 'ant-design-vue'

const handleTreeSelect = (selectedKeys, metadata) => {
  console.log('选中的节点:', selectedKeys)
}

const handleAddRegion = (data) => {
  console.log('添加子区域:', data)
  // 打开添加表单
}

const handleEditRegion = (data) => {
  console.log('编辑区域:', data)
  // 打开编辑表单
}

const handleDeleteRegion = (data) => {
  console.log('删除区域:', data)
  message.success('区域已删除')
}
</script>
```

### 多选模式

```vue
<template>
  <div style="height: 500px;">
    <region-tree 
      :multiple="true"
      @treeSelect="handleTreeSelect"
    />
  </div>
</template>

<script setup>
import RegionTree from '@/components/common/RegionTree/index.vue'

const handleTreeSelect = (selectedKeys, metadata) => {
  console.log('选中的多个节点:', selectedKeys)
}
</script>
```

### 显示数量标记

```vue
<template>
  <div style="height: 500px;">
    <region-tree 
      :showBadge="true"
      :customApi="getRegionsWithCount"
    />
  </div>
</template>

<script setup>
import RegionTree from '@/components/common/RegionTree/index.vue'
import { RegionApi } from '@/views/erp/region/api/regionApi'

// 自定义API，返回带count属性的区域数据
const getRegionsWithCount = async (params) => {
  const response = await RegionApi.findTree(params)
  // 假设后端返回的数据中包含count属性，表示关联的数据数量
  return response
}
</script>
```

### 使用自定义API

```vue
<template>
  <div style="height: 500px;">
    <region-tree 
      :customApi="customTreeApi"
      :customLazyApi="customLazyApi"
      :extraParams="{ type: 'supplier' }"
    />
  </div>
</template>

<script setup>
import RegionTree from '@/components/common/RegionTree/index.vue'
import { SupplierApi } from '@/api/supplierApi'

// 自定义树数据API
const customTreeApi = (params) => {
  return SupplierApi.getRegionTree(params)
}

// 自定义懒加载API
const customLazyApi = (params) => {
  return SupplierApi.getRegionChildren(params)
}
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| isShowEditIcon | Boolean | false | 是否显示编辑图标（新增、编辑、删除） |
| showSearch | Boolean | true | 是否显示搜索框 |
| searchPlaceholder | String | '搜索区域名称' | 搜索框占位符 |
| multiple | Boolean | false | 是否支持多选 |
| selectable | Boolean | true | 是否可选择 |
| defaultExpandAll | Boolean | false | 是否默认展开所有节点 |
| defaultExpandParent | Boolean | true | 是否默认展开父节点 |
| blockNode | Boolean | true | 是否节点占满一行 |
| emptyText | String | '暂无区域数据' | 空数据提示文本 |
| defaultSelectedKeys | Array | [] | 默认选中的节点 |
| defaultExpandedKeys | Array | [] | 默认展开的节点 |
| showBadge | Boolean | false | 是否显示数量标记 |
| customApi | Function | null | 自定义API方法，用于获取树数据 |
| customLazyApi | Function | null | 自定义懒加载API方法 |
| extraParams | Object | {} | 额外的查询参数 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| treeSelect | (selectedKeys, metadata) | 选中节点时触发 |
| addRegion | (nodeData) | 点击添加按钮时触发 |
| editRegion | (nodeData) | 点击编辑按钮时触发 |
| deleteRegion | (nodeData) | 删除节点成功后触发 |
| treeDataLoaded | (treeData) | 树数据加载完成时触发 |
| expandChange | (expandedKeys) | 展开/折叠节点时触发 |

## 组件方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| reloadRegionTreeData | - | - | 重新加载树数据 |
| setSelectedKeys | (keys) | - | 设置选中的节点 |
| setExpandedKeys | (keys) | - | 设置展开的节点 |
| clearSelection | - | - | 清空选中的节点 |
| getTreeData | - | Array | 获取当前树数据 |
| getSelectedNodes | - | Array | 获取当前选中的节点数据 |
| getRegionTreeData | - | - | 重新获取区域树数据 |