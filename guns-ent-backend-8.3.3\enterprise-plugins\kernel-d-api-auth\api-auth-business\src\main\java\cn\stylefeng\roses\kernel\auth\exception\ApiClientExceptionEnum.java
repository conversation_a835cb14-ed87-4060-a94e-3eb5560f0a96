package cn.stylefeng.roses.kernel.auth.exception;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * API客户端异常相关枚举
 *
 * <AUTHOR>
 * @date 2022/03/23 14:02
 */
@Getter
public enum ApiClientExceptionEnum implements AbstractExceptionEnum {

    /**
     * API认证请求时间过期，请从新认证
     */
    API_CLIENT_AUTH_EXPIRED(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "11002", "API登录请求时间过期，请从新调用认证接口"),

    /**
     * API登录失败，秘钥不正确
     */
    API_CLIENT_LOGIN_SECRET_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "11004", "API登录失败，秘钥不正确"),

    /**
     * API登录失败，API客户端被禁用
     */
    API_CLIENT_DISABLED(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "11005", "API登录失败，API客户端被禁用"),

    /**
     * 时间戳格式不对
     */
    TIMESTAMP_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + "11006", "API登录失败，时间戳格式不对");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    ApiClientExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
