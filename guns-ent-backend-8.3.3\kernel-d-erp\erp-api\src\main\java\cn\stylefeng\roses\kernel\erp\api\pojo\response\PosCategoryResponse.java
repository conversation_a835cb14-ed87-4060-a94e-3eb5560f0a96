package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

/**
 * POS商品分类响应参数
 *
 * <AUTHOR>
 * @since 2025/08/01 15:00
 */
@Data
public class PosCategoryResponse {

    /**
     * 分类ID
     */
    @ChineseDescription("分类ID")
    private Long categoryId;

    /**
     * 分类编码
     */
    @ChineseDescription("分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @ChineseDescription("分类名称")
    private String categoryName;

    /**
     * 父级分类ID
     */
    @ChineseDescription("父级分类ID")
    private Long parentId;

    /**
     * 分类层级
     */
    @ChineseDescription("分类层级")
    private Integer categoryLevel;

    /**
     * 排序号
     */
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 关联商品数量
     */
    @ChineseDescription("关联商品数量")
    private Integer productCount;

    /**
     * 分类图标
     */
    @ChineseDescription("分类图标")
    private String icon;

}