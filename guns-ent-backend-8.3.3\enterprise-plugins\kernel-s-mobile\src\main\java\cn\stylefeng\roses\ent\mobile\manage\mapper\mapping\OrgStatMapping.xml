<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.ent.mobile.manage.mapper.OrgStatMapper">

    <select id="getOrgStatList" resultType="cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.OrgUserStat">
        SELECT
        org_id as orgId,
        count(*) as userCount
        FROM
        sys_user_org uo
        WHERE
        org_id IN
        <foreach collection="orgList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY
        org_id
    </select>

    <select id="getOrgStatTotalList" resultType="cn.stylefeng.roses.ent.mobile.manage.pojo.addressbook.OrgUserStatTotal">
        SELECT uo.user_id AS userId,
        uo.org_id AS orgId,
        o.org_pids AS orgPids
        FROM sys_user_org uo
        LEFT JOIN sys_hr_organization o ON uo.org_id = o.org_id
        WHERE uo.org_id IN
        <foreach collection="orgList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        OR
        <foreach collection="orgList" item="item" open="" close="" separator="or">
            o.org_pids LIKE ("%[${item}]%")
        </foreach>
    </select>


</mapper>