ALTER TABLE `ent_tenant`
ADD COLUMN `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码，加密方式为MD5' AFTER `expire_date`,
ADD COLUMN `password_salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码盐' AFTER `password`;

ALTER TABLE `ent_tenant`
ADD COLUMN `active_flag` char(1) NOT NULL DEFAULT 'Y' COMMENT '激活状态：Y-已激活，N-未激活' AFTER `status_flag`;