<template>
  <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
    <a-row :gutter="20">
      <a-col :xs="24" :sm="24" :md="12">
        <a-form-item label="角色名称:" name="roleName">
          <a-input v-model:value="form.roleName" allow-clear placeholder="请输入角色名称" />
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12">
        <a-form-item label="角色编码:" name="roleCode">
          <a-input v-model:value="form.roleCode" allow-clear placeholder="请输入角色编码" />
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12">
        <a-form-item label="排序:" name="roleSort">
          <a-input-number
            v-model:value="form.roleSort"
            :min="0"
            style="width: 100%"
            placeholder="请输入排序"
            allow-clear
            autocomplete="off"
          />
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12">
        <a-form-item label="状态:" name="statusFlag">
          <a-radio-group v-model:value="form.statusFlag">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="2">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12">
        <a-form-item label="角色类型:" name="roleType">
          <a-radio-group v-model:value="form.roleType" @change="roleTypeChange">
            <a-radio :value="10" v-if="props.superAdminFlag">系统角色</a-radio>
            <a-radio :value="15" v-if="props.superAdminFlag">业务角色</a-radio>
            <a-radio :value="20">公司角色</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12" v-if="form.roleType == 20">
        <a-form-item label="所属公司:" name="roleCompanyIdWrapper">
          <a-input
            v-model:value="form.roleCompanyIdWrapper"
            @focus="roleCompanyIdWrapperClick"
            :disabled="!props.superAdminFlag"
            placeholder="请选择所属公司"
          />
        </a-form-item>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12" v-if="form.roleType == 15 || (form.roleType == 20 && form.roleCompanyId)">
        <a-form-item label="角色分类:" name="roleCategoryId">
          <a-tree-select
            v-model:value="form.roleCategoryId"
            style="width: 100%"
            showSearch
            :tree-data="roleTypeList"
            treeNodeFilterProp="roleCategoryName"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择角色分类"
            :fieldNames="{ children: 'children', label: 'roleCategoryName', key: 'id', value: 'id' }"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注">
          <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 选择组件 -->
    <Selection
      v-model:visible="showSelectCompany"
      v-if="showSelectCompany"
      title="选择公司"
      :data="selectData"
      :showTab="['company']"
      @done="closeSelectCompany"
    />
  </a-form>
</template>

<script setup name="RoleForm">
import { reactive, ref, watch } from 'vue';
import { RoleTypeApi } from '../api/RoleTypeApi';

const props = defineProps({
  // 表单数据
  form: Object,
  // 是否是超管
  superAdminFlag: Boolean
});

// 验证规则
const rules = reactive({
  roleName: [{ required: true, message: '请输入角色名称', type: 'string', trigger: 'blur' }],
  roleCode: [{ required: true, message: '请输入角色编码', type: 'string', trigger: 'blur' }],
  roleSort: [{ required: true, message: '请输入排序', type: 'number', trigger: 'blur' }],
  statusFlag: [{ required: true, message: '请选择状态', type: 'number', trigger: 'change' }],
  roleType: [{ required: true, message: '请选择角色类型', type: 'number', trigger: 'change' }],
  roleCategoryId: [{ required: true, message: '请选择角色分类', type: 'string', trigger: 'change' }],
  roleCompanyIdWrapper: [{ required: true, message: '请选择所属公司', type: 'string', trigger: 'change' }]
});

// 是否显示弹框
const showSelectCompany = ref(false);

// 选择的总数据
const selectData = ref({
  selectCompanyList: []
});

// 角色分类列表
const roleTypeList = ref([]);

// 选择公司
const roleCompanyIdWrapperClick = () => {
  selectData.value.selectCompanyList = [{ bizId: props.form.roleCompanyId, name: props.form.roleCompanyIdWrapper }];
  showSelectCompany.value = true;
};

// 关闭选择弹框
const closeSelectCompany = data => {
  const { bizId, name } = data.selectCompanyList[0];
  props.form.roleCompanyId = bizId;
  props.form.roleCompanyIdWrapper = name;
  props.form.roleCategoryId = undefined;
  getRoleTypeList();
};

// 角色类型改变
const roleTypeChange = ({ target }) => {
  props.form.roleCompanyId = null;
  props.form.roleCompanyIdWrapper = null;
  props.form.roleCategoryId = undefined;
};

// 获取角色分类列表
const getRoleTypeList = async () => {
  const res = await RoleTypeApi.treeList({ categoryType: props.form.roleType, companyId: props.form?.roleCompanyId });
  roleTypeList.value = res.data;
};

watch(
  () => props.form?.roleType,
  val => {
    if ([15].includes(val)) {
      getRoleTypeList();
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => [props.form?.roleCompanyId, props.form?.roleType],
  val => {
    if (val[0] && val[1]) {
      getRoleTypeList();
    } else {
      roleTypeList.value = [];
    }
  },
  { deep: true, immediate: true }
);
</script>

<style></style>
