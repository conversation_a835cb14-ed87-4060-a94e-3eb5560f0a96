package cn.stylefeng.roses.ent.saas.modular.manager.controller;

import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.dto.TenantDropdownDTO;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantService;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.saas.api.constants.SaasConstants;
import cn.stylefeng.roses.kernel.saas.api.expander.SaasConfigExpander;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 租户信息控制器
 *
 * <AUTHOR>
 * @date 2023/08/30 17:04
 */
@RestController
@ApiResource(name = "租户信息")
public class TenantController {

    @Resource
    private TenantService tenantService;

    /**
     * 获取租户列表（带分页）
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    @GetResource(name = "获取租户列表（带分页）", path = "/tenant/page", requiredPermission = true, requirePermissionCode = SaasConstants.TENANT_MENU_CODE)
    public ResponseData<PageResult<Tenant>> page(TenantRequest tenantRequest) {
        return new SuccessResponseData<>(tenantService.findPage(tenantRequest));
    }

    /**
     * 添加租户
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    @PostResource(name = "添加租户", path = "/tenant/add", requiredPermission = true, requirePermissionCode = SaasConstants.TENANT_MENU_CODE)
    public ResponseData<Tenant> add(@RequestBody @Validated(TenantRequest.add.class) TenantRequest tenantRequest) {
        tenantService.add(tenantRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    @PostResource(name = "删除", path = "/tenant/delete", requiredPermission = true, requirePermissionCode = SaasConstants.TENANT_MENU_CODE)
    public ResponseData<?> delete(@RequestBody @Validated(TenantRequest.delete.class) TenantRequest tenantRequest) {
        tenantService.del(tenantRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    @PostResource(name = "编辑", path = "/tenant/edit", requiredPermission = true, requirePermissionCode = SaasConstants.TENANT_MENU_CODE)
    public ResponseData<?> edit(@RequestBody @Validated(TenantRequest.edit.class) TenantRequest tenantRequest) {
        tenantService.edit(tenantRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看详情
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    @GetResource(name = "查看详情", path = "/tenant/detail", requiredPermission = true, requirePermissionCode = SaasConstants.TENANT_MENU_CODE)
    public ResponseData<Tenant> detail(@Validated(TenantRequest.detail.class) TenantRequest tenantRequest) {
        return new SuccessResponseData<>(tenantService.detail(tenantRequest));
    }

    /**
     * 检查租户编码是否存在
     *
     * <AUTHOR>
     * @since 2023/8/31 0:25
     */
    @GetResource(name = "检查企业名称是否存在", path = "/tenant/checkTenantCode", requiredLogin = false)
    public ResponseData<Boolean> checkTenantCode(@Validated(TenantRequest.checkTenantCode.class) TenantRequest tenantRequest) {
        boolean flag = tenantService.checkTenantCodeAlreadyExisted(tenantRequest);
        return new SuccessResponseData<>(flag);
    }

    /**
     * 获取租户下拉列表（用来选择租户）
     *
     * <AUTHOR>
     * @since 2023/8/31 0:32
     */
    @GetResource(name = "获取租户下拉列表", path = "/tenant/tenantDropdown", requiredLogin = false)
    public ResponseData<TenantDropdownDTO> tenantDropdown() {

        TenantDropdownDTO tenantDropdownDTO = new TenantDropdownDTO();

        // 获取租户下拉还是手动输入的配置
        Boolean tenantCodeSelectFlag = SaasConfigExpander.getTenantCodeSelectFlag();
        if (tenantCodeSelectFlag) {
            tenantDropdownDTO.setSelectFlag(true);
            List<Tenant> dropDownList = tenantService.findList(new TenantRequest());
            tenantDropdownDTO.setTenantList(dropDownList);
        } else {
            tenantDropdownDTO.setSelectFlag(false);
        }

        return new SuccessResponseData<>(tenantDropdownDTO);
    }

}
