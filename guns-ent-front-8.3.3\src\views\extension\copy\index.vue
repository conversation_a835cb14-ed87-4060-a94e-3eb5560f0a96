<template>
  <div class="guns-body guns-body-card">
    <a-card title="一键复制" :bordered="false">
      <a-button @click="copyData">点击复制</a-button>
      <div>{{ dataValue }}</div>
    </a-card>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import useClipboard from 'vue-clipboard3';

const { toClipboard } = useClipboard();

const dataValue = ref('我是复制的内容')

// 复制
const copyData = async () => {
  try {
    await toClipboard(dataValue.value);
    message.success('复制成功');
  } catch (e) {
    console.error(e);
  }
};
</script>

<style></style>
