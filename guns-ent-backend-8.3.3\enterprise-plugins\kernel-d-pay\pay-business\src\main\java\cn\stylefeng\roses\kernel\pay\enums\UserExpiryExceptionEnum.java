package cn.stylefeng.roses.kernel.pay.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import lombok.Getter;

/**
 * 用户商品到期时间异常相关枚举
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
@Getter
public enum UserExpiryExceptionEnum implements AbstractExceptionEnum {

    /**
     * 查询结果不存在
     */
    USER_EXPIRY_NOT_EXISTED(RuleConstants.USER_OPERATION_ERROR_TYPE_CODE +  "10001", "查询结果不存在");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    UserExpiryExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
