<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>kernel-d-oauth2</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>oauth2-business</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!--外部oauth2api-->
        <dependency>
            <groupId>com.javaguns.ent</groupId>
            <artifactId>oauth2-api</artifactId>
            <version>${guns.ent.version}</version>
        </dependency>

        <!--auth服务-->
        <!--和登录相关的服务，第三方登录时候需要用用户名来登录-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>auth-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!-- oauth2的第三方拓展 -->
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
            <version>${just.auth.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--创建oauth2用户需要创建用户信息，用户角色关联信息，用户机构关联信息-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>system-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--资源api模块-->
        <!--用在资源控制器，资源扫描上-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>scanner-api</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库sdk-->
        <!--数据库dao框架-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-sdk-mp</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--web模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

</project>
