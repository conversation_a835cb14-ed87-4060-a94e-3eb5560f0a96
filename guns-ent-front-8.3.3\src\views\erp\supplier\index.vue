<template>
  <div class="guns-layout">
    <!-- 左右分栏布局 -->
    <guns-split-layout width="292px" cacheKey="ERP_SUPPLIER_MANAGEMENT">
      <!-- 左侧区域树 -->
      <div class="guns-layout-sidebar width-100 p-t-12">
        <div class="sidebar-content">
          <region-tree
            ref="regionTreeRef"
            :show-badge="true"
            @tree-select="handleRegionTreeSelect"
            @tree-data-loaded="handleRegionTreeLoaded"
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <template #content>
        <div class="guns-layout-content">
          <div class="guns-layout">
            <div class="guns-layout-content-application">
              <div class="content-main">
                <!-- 头部操作区 -->
                <div class="content-main-header">
                  <div class="header-content">
                    <div class="header-content-left">
                      <a-space :size="16">
                        <span class="current-region-info" v-if="selectedRegionNodes.length > 0">
                          当前区域：<a-tag color="blue">{{ selectedRegionNodes[0].regionName }}</a-tag>
                        </span>
                      </a-space>
                    </div>
                    <div class="header-content-right">
                      <a-space :size="16">
                        <a-button type="primary" class="border-radius" @click="openAddModal">
                          <plus-outlined />
                          新增供应商
                        </a-button>
                        <a-dropdown>
                          <template #overlay>
                            <a-menu @click="moreClick">
                              <a-menu-item key="1">
                                <icon-font iconClass="icon-opt-shanchu" color="#60666b" />
                                <span>批量删除</span>
                              </a-menu-item>
                            </a-menu>
                          </template>
                          <a-button class="border-radius">
                            更多
                            <small-dash-outlined />
                          </a-button>
                        </a-dropdown>
                      </a-space>
                    </div>
                  </div>
                </div>

                <!-- 主体内容区 -->
                <div class="content-main-body">
                  <div class="table-content">
                    <common-table
                      :columns="columns"
                      :where="where"
                      fieldBusinessCode="ERP_SUPPLIER_TABLE"
                      showTableTool
                      :showToolTotal="false"
                      rowId="supplierId"
                      ref="tableRef"
                      url="/erp/supplier/page"
                    >
                      <template #toolLeft>
                        <a-input
                          v-model:value="where.searchText"
                          :bordered="false"
                          allowClear
                          placeholder="供应商名称、编码（回车搜索）"
                          @pressEnter="reload"
                          style="width: 240px"
                          class="search-input"
                        >
                          <template #prefix>
                            <icon-font iconClass="icon-opt-search" />
                          </template>
                        </a-input>
                        <a-divider type="vertical" class="divider" />
                        <a @click="changeSuperSearch">{{ superSearch ? '收起' : '高级筛选' }} </a>
                      </template>
                      <template #toolBottom>
                        <div class="super-search" style="margin-top: 8px" v-if="superSearch">
                          <a-form :model="where" :labelCol="labelCol" :wrapper-col="wrapperCol">
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label="供应商类型:">
                                  <a-select v-model:value="where.supplierType" placeholder="请选择供应商类型" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in supplierTypeOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="经营方式:">
                                  <a-select v-model:value="where.businessMode" placeholder="请选择经营方式" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in businessModeOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="状态:">
                                  <a-select v-model:value="where.status" placeholder="请选择状态" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                            </a-row>
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label="信用等级:">
                                  <a-select v-model:value="where.creditLevel" placeholder="请选择信用等级" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in creditLevelOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item></a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="联系人:">
                                  <a-input v-model:value="where.contactPerson" placeholder="请输入联系人" allowClear />
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="联系电话:">
                                  <a-input v-model:value="where.contactPhone" placeholder="请输入联系电话" allowClear />
                                </a-form-item>
                              </a-col>
                            </a-row>
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label=" " class="not-label">
                                  <a-space :size="16">
                                    <a-button class="border-radius" @click="reload" type="primary">查询</a-button>
                                    <a-button class="border-radius" @click="clear">重置</a-button>
                                  </a-space>
                                </a-form-item>
                              </a-col>
                            </a-row>
                          </a-form>
                        </div>
                      </template>
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'supplierType'">
                          <a-tag>{{ getSupplierTypeName(record.supplierType) }}</a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'businessMode'">
                          <a-tag :color="getBusinessModeTagColor(record.businessMode)">
                            {{ getBusinessModeName(record.businessMode) }}
                          </a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'salesDeduction'">
                          <span v-if="record.salesDeduction !== null && record.salesDeduction !== undefined">
                            {{ record.salesDeduction }}%
                          </span>
                          <span v-else class="text-muted">-</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'status'">
                          <a-tag :color="getStatusTagColor(record.status)">
                            {{ getSupplierStatusName(record.status) }}
                          </a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'creditLevel'">
                          <a-tag :color="getCreditLevelTagColor(record.creditLevel)">
                            {{ getCreditLevelName(record.creditLevel) }}
                          </a-tag>
                        </template>
                        <template v-else-if="column.key === 'action'">
                          <a-space :size="16">
                            <icon-font
                              iconClass="icon-opt-bianji"
                              font-size="24px"
                              title="编辑"
                              color="#60666b"
                              @click="openEditModal(record)"
                            />
                            <icon-font
                              iconClass="icon-opt-shanchu"
                              font-size="24px"
                              title="删除"
                              color="#60666b"
                              @click="remove(record)"
                            />
                          </a-space>
                        </template>
                      </template>
                    </common-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </guns-split-layout>

    <!-- 新增弹窗 -->
    <supplier-add
      v-model:visible="showAdd"
      @done="reload"
    />

    <!-- 编辑弹窗 -->
    <supplier-edit
      v-model:visible="showEdit"
      :data="currentRecord"
      @done="reload"
    />

    <!-- 详情弹窗 -->
    <supplier-detail
      v-model:visible="showDetailModal"
      :data="currentRecord"
    />
  </div>
</template>

<script>
import { createVNode, reactive, ref, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { 
  PlusOutlined, 
  SmallDashOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { SupplierApi } from './api/SupplierApi';
import SupplierAdd from './components/SupplierAdd.vue';
import SupplierEdit from './components/SupplierEdit.vue';
import SupplierDetail from './components/SupplierDetail.vue';
import RegionTree from '@/components/common/RegionTree/index.vue';
import { isMobile } from '@/utils/common/util';

export default {
  name: 'SupplierIndex',
  components: {
    PlusOutlined,
    SmallDashOutlined,
    SupplierAdd,
    SupplierEdit,
    SupplierDetail,
    RegionTree
  },
  setup() {
    // 响应式数据
    const superSearch = ref(false)
    const showAdd = ref(false)
    const showEdit = ref(false)
    const showDetailModal = ref(false)
    const currentRecord = ref({})
    const regionTreeRef = ref(null)
    const selectedRegionNodes = ref([])
    const tableRef = ref(null)
    const selectedRegionIds = ref([])
    const treeCollapsed = ref(false)

    // 响应式计算属性
    const labelCol = computed(() => {
      return { xxl: 7, xl: 7, lg: 5, md: 7, sm: 4 }
    })

    const wrapperCol = computed(() => {
      return { xxl: 17, xl: 17, lg: 19, md: 17, sm: 20 }
    })

    const spanCol = computed(() => {
      if (isMobile()) {
        return { xxl: 6, xl: 8, lg: 12, md: 24, sm: 24, xs: 24 }
      }
      return { xxl: 6, xl: 8, lg: 24, md: 24, sm: 24, xs: 24 }
    })

    // 查询条件
    const where = reactive({
      searchText: '',
      supplierType: undefined,
      businessMode: undefined,
      status: undefined,
      creditLevel: undefined,
      contactPerson: undefined,
      contactPhone: undefined,
      regionId: undefined // 添加区域筛选参数
    })

    // 选项数据
    const supplierTypeOptions = SupplierApi.getSupplierTypeOptions()
    const businessModeOptions = SupplierApi.getBusinessModeOptions()
    const statusOptions = SupplierApi.getSupplierStatusOptions()
    const creditLevelOptions = SupplierApi.getCreditLevelOptions()

    // 表格列定义
    const columns = [
      {
        key: 'index',
        title: '序号',
        width: 48,
        align: 'center',
        isShow: true,
        hideInSetting: true
      },
      {
        dataIndex: 'supplierCode',
        title: '供应商编码',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'supplierName',
        title: '供应商名称',
        width: 200,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'supplierShortName',
        title: '供应商简称',
        width: 150,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'supplierType',
        title: '供应商类型',
        width: 120,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'businessMode',
        title: '经营方式',
        width: 120,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'salesDeduction',
        title: '销售扣点',
        width: 100,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'contactPerson',
        title: '联系人',
        width: 100,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'contactPhone',
        title: '联系电话',
        width: 120,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'creditLevel',
        title: '信用等级',
        width: 100,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'status',
        title: '状态',
        width: 100,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'createTime',
        title: '创建时间',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        key: 'action',
        title: '操作',
        width: 100,
        isShow: true
      }
    ]

    // 查询数据（使用common-table组件自动处理）
    const reload = () => {
      if (tableRef.value) {
        tableRef.value.reload()
      }
    }

    // 重置查询条件
    const clear = () => {
      Object.keys(where).forEach(key => {
        where[key] = key === 'searchText' ? '' : undefined
      })
      // 清除区域选择状态
      selectedRegionIds.value = []
      selectedRegionNodes.value = []
      if (regionTreeRef.value) {
        regionTreeRef.value.clearSelection()
      }
      reload()
    }

    // 切换高级搜索
    const changeSuperSearch = () => {
      superSearch.value = !superSearch.value;
    };



    // 打开新增弹窗
    const openAddModal = () => {
      console.log('点击新增供应商按钮');
      showAdd.value = true;
      console.log('新增弹窗状态已设置为true:', showAdd.value);
    };

    // 打开编辑弹窗
    const openEditModal = (record) => {
      currentRecord.value = { ...record };
      showEdit.value = true;
    };

    // 显示详情
    const showDetail = (record) => {
      currentRecord.value = { ...record };
      showDetailModal.value = true;
    };

    // 删除
    const remove = (record) => {
      Modal.confirm({
        title: '提示',
        content: '确定要删除该供应商吗？',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: () => {
          return SupplierApi.delete({ supplierId: record.supplierId })
            .then(() => {
              message.success('删除成功');
              reload();
            })
            .catch((e) => {
              message.error(e.message || '删除失败');
            });
        }
      });
    };

    // 批量删除
    const batchDelete = () => {
      const selectedRows = tableRef.value?.getSelectedRows()
      if (!selectedRows || selectedRows.length === 0) {
        message.warning('请选择要删除的数据')
        return
      }

      Modal.confirm({
        title: '提示',
        content: `确定要删除选中的 ${selectedRows.length} 条数据吗？`,
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: () => {
          const supplierIds = selectedRows.map(row => row.supplierId)
          return SupplierApi.batchDelete({ supplierIdList: supplierIds })
            .then(() => {
              message.success('删除成功')
              reload()
            })
            .catch((e) => {
              message.error(e.message || '删除失败')
            })
        }
      })
    }

    // 更多操作点击
    const moreClick = ({ key }) => {
      if (key === '1') {
        batchDelete();
      }
    };

    // 更新状态
    const updateStatus = (record, status) => {
      const statusName = SupplierApi.getSupplierStatusName(status);
      Modal.confirm({
        title: '提示',
        content: `确定要将供应商状态更新为"${statusName}"吗？`,
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: () => {
          return SupplierApi.updateStatus({ supplierId: record.supplierId, status })
            .then(() => {
              message.success('状态更新成功');
              reload();
            })
            .catch((e) => {
              message.error(e.message || '状态更新失败');
            });
        }
      });
    };

    // 获取名称和颜色的方法（使用箭头函数保持this上下文）
    const getSupplierTypeName = (type) => SupplierApi.getSupplierTypeName(type);
    const getBusinessModeName = (businessMode) => SupplierApi.getBusinessModeName(businessMode);
    const getSupplierStatusName = (status) => SupplierApi.getSupplierStatusName(status);
    const getCreditLevelName = (level) => SupplierApi.getCreditLevelName(level);
    const getStatusTagColor = (status) => SupplierApi.getStatusTagColor(status);
    const getBusinessModeTagColor = (businessMode) => SupplierApi.getBusinessModeTagColor(businessMode);
    const getCreditLevelTagColor = (level) => SupplierApi.getCreditLevelTagColor(level);

    // 区域树相关方法
    
    // 切换区域树折叠状态
    const toggleTreeCollapse = () => {
      treeCollapsed.value = !treeCollapsed.value;
    };
    
    // 处理区域树选择事件
    const handleRegionTreeSelect = (selectedKeys, info) => {
      console.log('区域树选择:', selectedKeys, info)
      selectedRegionIds.value = selectedKeys
      
      if (info && info.selectedNodes && info.selectedNodes.length > 0) {
        selectedRegionNodes.value = info.selectedNodes
        // 将区域筛选参数设置到where对象中，使用CommonTable的标准参数处理机制
        where.regionId = selectedKeys[0]
      } else {
        selectedRegionNodes.value = []
        where.regionId = undefined
      }
      
      reload()
    }
    
    // 处理区域树数据加载完成事件
    const handleRegionTreeLoaded = (treeData) => {
      console.log('区域树数据加载完成:', treeData);
    };
    
    // 清除区域筛选
    const clearRegionFilter = () => {
      selectedRegionIds.value = []
      selectedRegionNodes.value = []
      where.regionId = undefined
      
      // 清除树选中状态
      if (regionTreeRef.value) {
        regionTreeRef.value.clearSelection()
      }
      
      reload()
    }

    return {
      tableRef,
      regionTreeRef,
      superSearch,
      where,
      columns,
      currentRecord,
      selectedRegionNodes,
      showEdit,
      showDetailModal,
      supplierTypeOptions,
      businessModeOptions,
      statusOptions,
      creditLevelOptions,
      labelCol,
      wrapperCol,
      spanCol,
      selectedRegionIds,
      treeCollapsed,
      showAdd,
      reload,
      clear,
      changeSuperSearch,
      handleRegionTreeSelect,
      handleRegionTreeLoaded,
      clearRegionFilter,
      openAddModal,
      openEditModal,
      showDetail,
      remove,
      moreClick,
      batchDelete,
      getSupplierTypeName,
      getBusinessModeName,
      getSupplierStatusName,
      getCreditLevelName,
      getStatusTagColor,
      getBusinessModeTagColor,
      getCreditLevelTagColor
    };
  }
};
</script>

<style lang="less" scoped>
.guns-layout {
  .table-toolbar {
    margin-bottom: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-input {
        .ant-input {
          border-radius: 6px;
        }
      }

      a {
        color: #1890ff;
        cursor: pointer;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  .advanced-search {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;

    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

.guns-layout {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.guns-layout-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.guns-layout-content-application {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.sidebar-content {
  height: 100%;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.content-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.content-main-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-region-info {
  font-size: 14px;
  color: #666;
}

.content-main-body {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
  width: 100%;
  box-sizing: border-box;
}

.table-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

// 确保GunsSplitLayout组件的右侧内容区域完全铺满
:deep(.guns-split-panel-body) {
  width: 100% !important;
  flex: 1 !important;
  overflow: hidden !important;
}

:deep(.guns-split-panel) {
  width: 100% !important;
}

.text-muted {
  color: #999;
  font-style: italic;
}
</style>