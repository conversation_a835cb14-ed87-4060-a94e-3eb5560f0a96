package cn.stylefeng.guns.gateway.core.filters;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.guns.gateway.core.consts.GatewayFilterOrdered;
import cn.stylefeng.guns.gateway.modular.cache.BlackListRedisCache;
import cn.stylefeng.guns.gateway.modular.cache.WhiteListRedisCache;
import cn.stylefeng.roses.kernel.micro.api.exception.MicroException;
import cn.stylefeng.roses.kernel.micro.api.exception.enums.GatewayExceptionEnum;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import jakarta.annotation.Resource;
import java.net.InetSocketAddress;
import java.util.Collection;
import java.util.Map;

/**
 * 黑白名单的过滤拦截器
 *
 * <AUTHOR>
 * @since 2024/7/10 23:10
 */
public class BlackWhiteFilter implements GlobalFilter, Ordered {

    @Resource
    private BlackListRedisCache blackListRedisCache;

    @Resource
    private WhiteListRedisCache whiteListRedisCache;

    @Override
    public int getOrder() {
        return GatewayFilterOrdered.BLACK_WHITE_HEADER_ORDER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        // 获取对方请求的ip地址
        InetSocketAddress remoteAddress = exchange.getRequest().getRemoteAddress();
        String hostAddress = remoteAddress.getAddress().getHostAddress();

        // 获取对方去请求的header头信息
        HttpHeaders headers = exchange.getRequest().getHeaders();
        Map<String, String> headerValueMap = headers.toSingleValueMap();

        // 先从header中找到代理的ip
        String[] ruleHeaderNames = {"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR", "Agent-Source-Ip"};
        String ip = "";
        for (String header : ruleHeaderNames) {
            ip = headerValueMap.get(header);
            if (false == NetUtil.isUnknown(ip)) {
                ip = NetUtil.getMultistageReverseProxyIp(ip);
                break;
            }
        }

        // 如果header中找不到代理的ip，那可能是直接访问的ip，获取对方真实ip
        if (StrUtil.isBlank(ip)) {
            ip = hostAddress;
        }

        // 判断ip地址是否在黑名单中，如果在黑名单，则直接打回
        boolean containsBlack = blackListRedisCache.contains(ip);
        if (containsBlack) {
            throw new MicroException(GatewayExceptionEnum.IP_BLACK_LIST_ERROR);
        }

        // 如果白名单没内容，则不执行白名单机制
        Collection<String> allKeys = whiteListRedisCache.getAllKeys();
        if (ObjectUtil.isEmpty(allKeys)) {
            return chain.filter(exchange.mutate().build());
        }

        // 如果白名单里边有内容，则判断当前IP是否在白名单里边，如果不在白名单，则直接返回
        boolean containsWhite = whiteListRedisCache.contains(ip);
        if (!containsWhite) {
            throw new MicroException(GatewayExceptionEnum.IP_WHITE_LIST_ERROR);
        }

        return chain.filter(exchange.mutate().build());
    }

}
