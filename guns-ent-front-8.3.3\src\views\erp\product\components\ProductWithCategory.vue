<template>
  <div class="guns-layout">
    <!-- 左右分栏布局 -->
    <guns-split-layout width="292px" cacheKey="ERP_PRODUCT_MANAGEMENT">
      <!-- 左侧分类树 -->
      <div class="guns-layout-sidebar width-100 p-t-12">
        <div class="sidebar-content">
          <universal-tree 
            ref="categoryTreeRef" 
            :data-source="treeConfig.dataSource"
            :field-mapping="treeConfig.fieldMapping"
            :display-config="treeConfig.displayConfig"
            :interaction-config="treeConfig.interactionConfig"
            :action-config="treeConfig.actionConfig"
            @select="handleCategorySelect" 
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <template #content>
        <div class="guns-layout-content">
          <div class="guns-layout">
            <div class="guns-layout-content-application">
              <div class="content-main">
                <!-- 头部操作区 -->
                <div class="content-main-header">
                  <div class="header-content">
                    <div class="header-content-left">
                      <a-space :size="16">
                        <span class="current-category-info" v-if="currentCategoryName">
                          当前分类：<a-tag color="blue">{{ currentCategoryName }}</a-tag>
                        </span>
                      </a-space>
                    </div>
                    <div class="header-content-right">
                      <a-space :size="16">
                        <a-button type="primary" class="border-radius" @click="openAddModal">
                          <plus-outlined />
                          新增商品
                        </a-button>
                        <a-dropdown>
                          <template #overlay>
                            <a-menu @click="moreClick">
                              <a-menu-item key="1">
                                <icon-font iconClass="icon-opt-shanchu" color="#60666b" />
                                <span>批量删除</span>
                              </a-menu-item>

                            </a-menu>
                          </template>
                          <a-button class="border-radius">
                            更多
                            <small-dash-outlined />
                          </a-button>
                        </a-dropdown>
                      </a-space>
                    </div>
                  </div>
                </div>
                <!-- 主体内容区 -->
                <div class="content-main-body">
                  <div class="table-content">
                    <common-table
                      :columns="columns"
                      :where="where"
                      fieldBusinessCode="ERP_PRODUCT_TABLE"
                      showTableTool
                      :showToolTotal="false"
                      rowId="productId"
                      ref="tableRef"
                      url="/erp/product/page"
                    >
                      <template #toolLeft>
                        <a-input
                          v-model:value="where.searchText"
                          :bordered="false"
                          allowClear
                          placeholder="商品名称、编码（回车搜索）"
                          @pressEnter="reload"
                          style="width: 240px"
                          class="search-input"
                        >
                          <template #prefix>
                            <icon-font iconClass="icon-opt-search" />
                          </template>
                        </a-input>
                        <a-divider type="vertical" class="divider" />
                        <a @click="changeSuperSearch">{{ superSearch ? '收起' : '高级筛选' }} </a>
                      </template>
                      <template #toolBottom>
                        <div class="super-search" style="margin-top: 8px" v-if="superSearch">
                          <a-form :model="where" :labelCol="labelCol" :wrapper-col="wrapperCol">
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label="商品编码:">
                                  <a-input v-model:value="where.productCode" placeholder="请输入商品编码" allowClear />
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="条形码:">
                                  <a-input v-model:value="where.barcode" placeholder="请输入条形码" allowClear />
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="品牌:">
                                  <a-input v-model:value="where.brand" placeholder="请输入品牌" allowClear />
                                </a-form-item>
                              </a-col>
                            </a-row>
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label="状态:">
                                  <a-select v-model:value="where.status" placeholder="请选择状态" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="基本单位:">
                                  <a-select v-model:value="where.unit" placeholder="请选择基本单位" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in unitOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                            </a-row>
                            <a-row :gutter="16">
                              <a-col v-bind="spanCol">
                                <a-form-item label="供应商:">
                                  <supplier-selector
                                    v-model:value="where.supplierId"
                                    placeholder="请选择供应商"
                                    :allowClear="true"
                                  />
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label="计价类型:">
                                  <a-select v-model:value="where.pricingType" placeholder="请选择计价类型" style="width: 100%" allowClear>
                                    <a-select-option v-for="item in pricingTypeOptions" :key="item.value" :value="item.value">
                                      {{ item.label }}
                                    </a-select-option>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-col v-bind="spanCol">
                                <a-form-item label=" " class="not-label">
                                  <a-space :size="16">
                                    <a-button class="border-radius" @click="reload" type="primary">查询</a-button>
                                    <a-button class="border-radius" @click="clear">重置</a-button>
                                  </a-space>
                                </a-form-item>
                              </a-col>
                            </a-row>
                          </a-form>
                        </div>
                      </template>
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'status'">
                          <a-tag :color="getStatusTagColor(record.status)">
                            {{ getProductStatusName(record.status) }}
                          </a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'categoryName'">
                          <template v-if="record.categoryName">
                            <a-tag
                              color="blue"
                              @click="handleCategoryTagClick(record.categoryId)"
                            >
                              {{ record.categoryName }}
                            </a-tag>
                          </template>
                          <a-tag v-else color="default">未分类</a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'weight'">
                          {{ formatWeight(record.weight) }}
                        </template>
                        <template v-else-if="column.dataIndex === 'volume'">
                          {{ formatVolume(record.volume) }}
                        </template>
                        <template v-else-if="column.dataIndex === 'shelfLife'">
                          {{ formatShelfLife(record.shelfLife) }}
                        </template>
                        <template v-else-if="column.dataIndex === 'supplierName'">
                          <span v-if="record.supplierName">{{ record.supplierName }}</span>
                          <a-tag v-else color="default">未设置</a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'pricingType'">
                          <a-tag v-if="record.pricingType" :color="getPricingTypeTagColor(record.pricingType)">
                            {{ getPricingTypeName(record.pricingType) }}
                          </a-tag>
                          <a-tag v-else color="default">未设置</a-tag>
                        </template>
                        <template v-else-if="column.dataIndex === 'retailPrice'">
                          <span v-if="record.retailPrice || record.unitPrice || record.piecePrice || record.referencePrice">
                            {{ formatPrice(record) }}
                          </span>
                          <a-tag v-else color="default">未设置</a-tag>
                        </template>
                        <template v-else-if="column.key === 'action'">
                          <a-space :size="16">
                            <icon-font
                              iconClass="icon-opt-bianji"
                              font-size="24px"
                              title="编辑"
                              color="#60666b"
                              @click="openEditModal(record)"
                            />
                            <icon-font
                              iconClass="icon-opt-shanchu"
                              font-size="24px"
                              title="删除"
                              color="#60666b"
                              @click="remove(record)"
                            />
                            <a-dropdown>
                              <icon-font
                                iconClass="icon-opt-gengduo"
                                font-size="24px"
                                title="更多"
                                color="#60666b"
                              />
                              <template #overlay>
                                <a-menu>
                                  <a-menu-item v-if="record.status === 'ACTIVE'" @click="updateStatus(record, 'INACTIVE')">
                                    停用
                                  </a-menu-item>
                                  <a-menu-item v-if="record.status === 'INACTIVE'" @click="updateStatus(record, 'ACTIVE')">
                                    启用
                                  </a-menu-item>
                                  <a-menu-item v-if="record.status !== 'DISCONTINUED'" @click="updateStatus(record, 'DISCONTINUED')">
                                    停产
                                  </a-menu-item>
                                  <a-menu-divider />
                                  <a-menu-item @click="showDetail(record)">
                                    详情
                                  </a-menu-item>
                                </a-menu>
                              </template>
                            </a-dropdown>
                          </a-space>
                        </template>
                      </template>
                    </common-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </guns-split-layout>

    <!-- 新增/编辑弹窗 -->
    <product-edit
      v-model:visible="showEdit"
      :data="currentRecord"
      @done="reload"
    />

    <!-- 详情弹窗 -->
    <product-detail
      v-model:visible="showDetailModal"
      :data="currentRecord"
    />


  </div>
</template>

<script>
import { createVNode, reactive, ref, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { 
  PlusOutlined, 
  SmallDashOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { ProductApi } from '../api/ProductApi';
import { ProductCategoryApi } from '../../productCategory/api/productCategoryApi.js';
import ProductEdit from './ProductEdit.vue';
import ProductDetail from './ProductDetail.vue';
import UniversalTree from '@/components/UniversalTree/UniversalTree.vue';

// import SupplierSelector from '@/components/erp/SupplierSelector.vue';
import { createProductCategoryReadOnlyTreeConfig } from '@/components/UniversalTree/presets.js';

import { isMobile } from '@/utils/common/util';

export default {
  name: 'ProductWithCategory',
  components: {
    PlusOutlined,
    SmallDashOutlined,
    ProductEdit,
    ProductDetail,
    UniversalTree,

    // SupplierSelector
  },
  setup() {
    // 响应式数据
    const superSearch = ref(false)
    const showEdit = ref(false)
    const showDetailModal = ref(false)

    const currentRecord = ref({})
    const categoryTreeRef = ref(null)
    const currentCategoryName = ref('')
    const currentCategoryId = ref(null)
    const tableRef = ref(null)
    
    // 树配置
    const treeConfig = createProductCategoryReadOnlyTreeConfig()

    // 响应式计算属性
    const labelCol = computed(() => {
      return { xxl: 7, xl: 7, lg: 5, md: 7, sm: 4 }
    })

    const wrapperCol = computed(() => {
      return { xxl: 17, xl: 17, lg: 19, md: 17, sm: 20 }
    })

    const spanCol = computed(() => {
      if (isMobile()) {
        return { xxl: 6, xl: 8, lg: 12, md: 24, sm: 24, xs: 24 }
      }
      return { xxl: 6, xl: 8, lg: 24, md: 24, sm: 24, xs: 24 }
    })



    // 查询条件
    const where = reactive({
      searchText: '',
      productCode: undefined,
      barcode: undefined,
      brand: undefined,
      status: undefined,
      unit: undefined,
      supplierId: undefined,
      pricingType: undefined
    })

    // 选项数据
    const statusOptions = ProductApi.getProductStatusOptions();
    const unitOptions = ProductApi.getCommonUnitOptions();
    const pricingTypeOptions = ProductApi.getPricingTypeOptions();

    // 表格列定义
    const columns = [
      {
        key: 'index',
        title: '序号',
        width: 48,
        align: 'center',
        isShow: true,
        hideInSetting: true
      },
      {
        dataIndex: 'productCode',
        title: '商品编码',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'productName',
        title: '商品名称',
        width: 200,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'categoryName',
        title: '产品分类',
        width: 200,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'productShortName',
        title: '商品简称',
        width: 150,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'barcode',
        title: '条形码',
        width: 120,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'brand',
        title: '品牌',
        width: 100,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'specification',
        title: '规格',
        width: 120,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'unit',
        title: '基本单位',
        width: 80,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'supplierName',
        title: '供应商',
        width: 150,
        ellipsis: true,
        isShow: true
      },
      {
        dataIndex: 'pricingType',
        title: '计价类型',
        width: 100,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'retailPrice',
        title: '价格',
        width: 120,
        align: 'right',
        isShow: true
      },
      {
        dataIndex: 'status',
        title: '状态',
        width: 100,
        align: 'center',
        isShow: true
      },
      {
        dataIndex: 'createTime',
        title: '创建时间',
        width: 140,
        ellipsis: true,
        isShow: true
      },
      {
        key: 'action',
        title: '操作',
        width: 150,
        isShow: true
      }
    ]

    // 查询数据（使用common-table组件自动处理）
    const reload = () => {
      if (tableRef.value) {
        tableRef.value.reload()
      }
    }

    // 重置查询条件
    const clear = () => {
      Object.keys(where).forEach(key => {
        where[key] = key === 'searchText' ? '' : undefined
      })
      currentCategoryId.value = null
      currentCategoryName.value = ''
      if (categoryTreeRef.value) {
        categoryTreeRef.value.clearSelection()
      }
      reload()
    }

    // 切换高级搜索
    const changeSuperSearch = () => {
      superSearch.value = !superSearch.value
    }

    // 打开新增弹窗
    const openAddModal = () => {
      currentRecord.value = {};
      showEdit.value = true;
    };

    // 打开编辑弹窗
    const openEditModal = (record) => {
      currentRecord.value = { ...record };
      showEdit.value = true;
    };

    // 显示详情
    const showDetail = (record) => {
      currentRecord.value = { ...record };
      showDetailModal.value = true;
    };

    // 删除
    const remove = (record) => {
      Modal.confirm({
        title: '提示',
        content: '确定要删除该商品吗？',
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: () => {
          return ProductApi.delete({ productId: record.productId })
            .then(() => {
              message.success('删除成功');
              reload();
            })
            .catch((e) => {
              message.error(e.message || '删除失败');
            });
        }
      });
    };



    // 更新状态
    const updateStatus = (record, status) => {
      const statusName = ProductApi.getProductStatusName(status);
      Modal.confirm({
        title: '提示',
        content: `确定要将商品状态更新为"${statusName}"吗？`,
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: () => {
          return ProductApi.updateStatus({ productId: record.productId, status })
            .then(() => {
              message.success('状态更新成功');
              reload();
            })
            .catch((e) => {
              message.error(e.message || '状态更新失败');
            });
        }
      });
    };
    
    // 处理分类选择
    const handleCategorySelect = (selectInfo) => {
      console.log('分类树选择:', selectInfo)
      
      if (selectInfo.keys && selectInfo.keys.length > 0) {
        const selectedKey = selectInfo.keys[0]
        const selectedNode = selectInfo.nodes[0]
        
        currentCategoryId.value = selectedKey
        currentCategoryName.value = selectedNode?.categoryName || ''
        
        // 添加分类筛选条件到查询参数
        where.categoryId = selectedKey
      } else {
        // 如果取消选择，则显示全部商品
        currentCategoryId.value = null
        currentCategoryName.value = ''
        where.categoryId = undefined
      }
      
      reload()
    }
    
    // 处理分类标签点击
    const handleCategoryTagClick = (categoryId) => {
      currentCategoryId.value = categoryId
      if (categoryTreeRef.value) {
        categoryTreeRef.value.setSelectedKeys([String(categoryId)])
      }
      where.categoryId = categoryId
      reload()
    }
    
    // 更多操作菜单点击
    const moreClick = ({ key }) => {
      if (key === '1') {
        batchDelete()
      }
    }

    // 批量删除
    const batchDelete = () => {
      const selectedRows = tableRef.value?.getSelectedRows()
      if (!selectedRows || selectedRows.length === 0) {
        message.warning('请选择要删除的数据')
        return
      }

      Modal.confirm({
        title: '提示',
        content: `确定要删除选中的 ${selectedRows.length} 条数据吗？`,
        icon: createVNode(ExclamationCircleOutlined),
        maskClosable: true,
        onOk: () => {
          const productIds = selectedRows.map(row => row.productId)
          return ProductApi.batchDelete({ productIdList: productIds })
            .then(() => {
              message.success('删除成功')
              reload()
            })
            .catch((e) => {
              message.error(e.message || '删除失败')
            })
        }
      })
    }

    // 获取名称和颜色的方法
    const getProductStatusName = (status) => ProductApi.getProductStatusName(status)
    const getStatusTagColor = (status) => ProductApi.getStatusTagColor(status)
    const getPricingTypeName = (pricingType) => ProductApi.getPricingTypeName(pricingType)
    const getPricingTypeTagColor = (pricingType) => ProductApi.getPricingTypeTagColor(pricingType)
    const formatWeight = (weight) => ProductApi.formatWeight(weight)
    const formatVolume = (volume) => ProductApi.formatVolume(volume)
    const formatShelfLife = (shelfLife) => ProductApi.formatShelfLife(shelfLife)

    // 格式化价格显示
    const formatPrice = (record) => {
      if (!record.pricingType) return '-'

      switch (record.pricingType) {
        case 'NORMAL':
          return record.retailPrice ? ProductApi.formatPrice(record.retailPrice, 'NORMAL') : '-'
        case 'WEIGHT':
          return record.unitPrice ? ProductApi.formatPrice(record.unitPrice, 'WEIGHT') : '-'
        case 'PIECE':
          return record.piecePrice ? ProductApi.formatPrice(record.piecePrice, 'PIECE') : '-'
        case 'VARIABLE':
          return record.referencePrice ? ProductApi.formatPrice(record.referencePrice, 'VARIABLE') : '-'
        default:
          return '-'
      }
    }

    return {
      tableRef,
      categoryTreeRef,
      treeConfig,
      superSearch,
      where,
      columns,
      currentRecord,
      currentCategoryName,
      showEdit,
      showDetailModal,


      statusOptions,
      unitOptions,
      pricingTypeOptions,
      labelCol,
      wrapperCol,
      spanCol,
      reload,
      clear,
      changeSuperSearch,
      handleCategorySelect,
      handleCategoryTagClick,
      openAddModal,
      openEditModal,
      showDetail,
      remove,
      moreClick,
      batchDelete,
      updateStatus,

      getProductStatusName,
      getStatusTagColor,
      getPricingTypeName,
      getPricingTypeTagColor,
      formatWeight,
      formatVolume,
      formatShelfLife,
      formatPrice
    }
  }
};
</script>

<style lang="less" scoped>
.guns-layout {
  .table-toolbar {
    margin-bottom: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-input {
        .ant-input {
          border-radius: 6px;
        }
      }

      a {
        color: #1890ff;
        cursor: pointer;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  .advanced-search {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;

    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

.guns-layout {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.guns-layout-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.guns-layout-content-application {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.sidebar-content {
  height: 100%;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.content-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.content-main-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-category-info {
  font-size: 14px;
  color: #666;
}

.content-main-body {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
  width: 100%;
  box-sizing: border-box;
}

.table-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

// 确保GunsSplitLayout组件的右侧内容区域完全铺满
:deep(.guns-split-panel-body) {
  width: 100% !important;
  flex: 1 !important;
  overflow: hidden !important;
}

:deep(.guns-split-panel) {
  width: 100% !important;
}
</style>