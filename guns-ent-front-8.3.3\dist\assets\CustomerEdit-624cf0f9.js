import{_ as S,s as E,r as C,X as A,m as k,a as c,f as I,w as a,d as e,c as w,F as L,e as O,g as T,t as R,l as h,u as M,v as P,G as V,W as B,J as j,$ as q,y as F,H as D,M as G}from"./index-18a1ea24.js";import{R as x}from"./index-60b48b32.js";/* empty css              */import{C as v}from"./CustomerApi-e856427e.js";/* empty css              */import"./regionApi-2c103d88.js";const H={name:"CustomerEdit",components:{RegionSelector:x},props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(_,{emit:o}){const u=E({}),t=C(null),b=C(null),p=C(!1),s=C(!1),n=v.getCustomerTypeOptions(),r=v.getCustomerLevelOptions(),m=v.getCustomerStatusOptions(),y=E({customerCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7F16\u7801",trigger:"blur"},{max:50,message:"\u5BA2\u6237\u7F16\u7801\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26",trigger:"blur"}],customerName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BA2\u6237\u540D\u79F0",trigger:"blur"},{max:200,message:"\u5BA2\u6237\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26",trigger:"blur"}],customerType:[{required:!0,message:"\u8BF7\u9009\u62E9\u5BA2\u6237\u7C7B\u578B",trigger:"change"}],contactEmail:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:"blur"}],contactPhone:[{pattern:/^[0-9-()（）\s]+$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u7535\u8BDD\u53F7\u7801",trigger:"blur"}],contactMobile:[{pattern:/^1[3-9]\d{9}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}],creditLimit:[{type:"number",min:0,message:"\u4FE1\u7528\u989D\u5EA6\u4E0D\u80FD\u5C0F\u4E8E0",trigger:"blur"}],paymentTerms:[{type:"number",min:0,message:"\u8D26\u671F\u5929\u6570\u4E0D\u80FD\u5C0F\u4E8E0",trigger:"blur"}]}),g=d=>{o("update:visible",d)},U=async()=>{try{await t.value.validate(),p.value=!0;const d={...u},i=await(s.value?v.edit:v.add)(d),l=s.value?u.customerId:i.data;l&&await v.updateCustomerRegions({customerId:l,regionIds:u.regionIds||[]}),k.success("\u4FDD\u5B58\u6210\u529F"),g(!1),o("done")}catch(d){console.error("\u4FDD\u5B58\u5BA2\u6237\u5931\u8D25:",d),k.error(d.message||"\u4FDD\u5B58\u5931\u8D25")}finally{p.value=!1}};return A(()=>_.visible,async d=>{var f;if(d)if((f=_.data)!=null&&f.customerId){s.value=!0,Object.assign(u,_.data);try{p.value=!0;const i=await v.getCustomerRegions({customerId:_.data.customerId});i&&Array.isArray(i)?u.regionIds=i.map(l=>l.regionId):u.regionIds=[]}catch(i){console.error("\u83B7\u53D6\u5BA2\u6237\u5173\u8054\u533A\u57DF\u5931\u8D25:",i),k.error("\u83B7\u53D6\u5BA2\u6237\u5173\u8054\u533A\u57DF\u5931\u8D25"),u.regionIds=[]}finally{p.value=!1}}else s.value=!1,Object.keys(u).forEach(i=>{u[i]=void 0}),u.customerType="ENTERPRISE",u.customerLevel="BRONZE",u.status="ACTIVE",u.paymentTerms=30,u.creditLimit=0,u.regionIds=[],b.value&&b.value.resetState&&b.value.resetState()},{immediate:!0}),{form:u,formRef:t,regionSelectorRef:b,loading:p,isUpdate:s,customerTypeOptions:n,customerLevelOptions:r,statusOptions:m,rules:y,updateVisible:g,save:U,handleRegionChange:(d,f)=>{console.log("\u5BA2\u6237\u533A\u57DF\u9009\u62E9\u53D8\u5316:",d,f),u.regionIds=d}}}};function J(_,o,u,t,b,p){const s=h,n=M,r=P,m=V,y=B,g=j,U=q,N=F,d=x,f=D,i=G;return c(),I(i,{title:t.isUpdate?"\u7F16\u8F91\u5BA2\u6237":"\u65B0\u589E\u5BA2\u6237",width:900,visible:u.visible,"confirm-loading":t.loading,"body-style":{paddingBottom:"8px"},"onUpdate:visible":t.updateVisible,onOk:t.save},{default:a(()=>[e(f,{ref:"formRef",model:t.form,rules:t.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:a(()=>[e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5BA2\u6237\u7F16\u7801",name:"customerCode"},{default:a(()=>[e(s,{value:t.form.customerCode,"onUpdate:value":o[0]||(o[0]=l=>t.form.customerCode=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7F16\u7801"},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5BA2\u6237\u540D\u79F0",name:"customerName"},{default:a(()=>[e(s,{value:t.form.customerName,"onUpdate:value":o[1]||(o[1]=l=>t.form.customerName=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5BA2\u6237\u7B80\u79F0",name:"customerShortName"},{default:a(()=>[e(s,{value:t.form.customerShortName,"onUpdate:value":o[2]||(o[2]=l=>t.form.customerShortName=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7B80\u79F0"},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5BA2\u6237\u7C7B\u578B",name:"customerType"},{default:a(()=>[e(g,{value:t.form.customerType,"onUpdate:value":o[3]||(o[3]=l=>t.form.customerType=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u7C7B\u578B"},{default:a(()=>[(c(!0),w(L,null,O(t.customerTypeOptions,l=>(c(),I(y,{key:l.value,value:l.value},{default:a(()=>[T(R(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5BA2\u6237\u7B49\u7EA7",name:"customerLevel"},{default:a(()=>[e(g,{value:t.form.customerLevel,"onUpdate:value":o[4]||(o[4]=l=>t.form.customerLevel=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u7B49\u7EA7"},{default:a(()=>[(c(!0),w(L,null,O(t.customerLevelOptions,l=>(c(),I(y,{key:l.value,value:l.value},{default:a(()=>[T(R(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u72B6\u6001",name:"status"},{default:a(()=>[e(g,{value:t.form.status,"onUpdate:value":o[5]||(o[5]=l=>t.form.status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:a(()=>[(c(!0),w(L,null,O(t.statusOptions,l=>(c(),I(y,{key:l.value,value:l.value},{default:a(()=>[T(R(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u8054\u7CFB\u4EBA",name:"contactPerson"},{default:a(()=>[e(s,{value:t.form.contactPerson,"onUpdate:value":o[6]||(o[6]=l=>t.form.contactPerson=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA"},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u8054\u7CFB\u7535\u8BDD",name:"contactPhone"},{default:a(()=>[e(s,{value:t.form.contactPhone,"onUpdate:value":o[7]||(o[7]=l=>t.form.contactPhone=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u624B\u673A\u53F7\u7801",name:"contactMobile"},{default:a(()=>[e(s,{value:t.form.contactMobile,"onUpdate:value":o[8]||(o[8]=l=>t.form.contactMobile=l),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u90AE\u7BB1\u5730\u5740",name:"contactEmail"},{default:a(()=>[e(s,{value:t.form.contactEmail,"onUpdate:value":o[9]||(o[9]=l=>t.form.contactEmail=l),placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"\u8054\u7CFB\u5730\u5740",name:"contactAddress","label-col":{span:3},"wrapper-col":{span:21}},{default:a(()=>[e(U,{value:t.form.contactAddress,"onUpdate:value":o[10]||(o[10]=l=>t.form.contactAddress=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u5730\u5740",rows:2},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u8425\u4E1A\u6267\u7167\u53F7",name:"businessLicenseNo"},{default:a(()=>[e(s,{value:t.form.businessLicenseNo,"onUpdate:value":o[11]||(o[11]=l=>t.form.businessLicenseNo=l),placeholder:"\u8BF7\u8F93\u5165\u8425\u4E1A\u6267\u7167\u53F7"},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u7A0E\u52A1\u767B\u8BB0\u53F7",name:"taxNo"},{default:a(()=>[e(s,{value:t.form.taxNo,"onUpdate:value":o[12]||(o[12]=l=>t.form.taxNo=l),placeholder:"\u8BF7\u8F93\u5165\u7A0E\u52A1\u767B\u8BB0\u53F7"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u5F00\u6237\u94F6\u884C",name:"bankName"},{default:a(()=>[e(s,{value:t.form.bankName,"onUpdate:value":o[13]||(o[13]=l=>t.form.bankName=l),placeholder:"\u8BF7\u8F93\u5165\u5F00\u6237\u94F6\u884C"},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u94F6\u884C\u8D26\u53F7",name:"bankAccount"},{default:a(()=>[e(s,{value:t.form.bankAccount,"onUpdate:value":o[14]||(o[14]=l=>t.form.bankAccount=l),placeholder:"\u8BF7\u8F93\u5165\u94F6\u884C\u8D26\u53F7"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u4FE1\u7528\u989D\u5EA6",name:"creditLimit"},{default:a(()=>[e(N,{value:t.form.creditLimit,"onUpdate:value":o[15]||(o[15]=l=>t.form.creditLimit=l),placeholder:"\u8BF7\u8F93\u5165\u4FE1\u7528\u989D\u5EA6",min:0,precision:2,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(r,{md:12,sm:24},{default:a(()=>[e(n,{label:"\u8D26\u671F\u5929\u6570",name:"paymentTerms"},{default:a(()=>[e(N,{value:t.form.paymentTerms,"onUpdate:value":o[16]||(o[16]=l=>t.form.paymentTerms=l),placeholder:"\u8BF7\u8F93\u5165\u8D26\u671F\u5929\u6570",min:0,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"\u5173\u8054\u533A\u57DF",name:"regionIds","label-col":{span:3},"wrapper-col":{span:21}},{default:a(()=>[e(d,{ref:"regionSelectorRef",modelValue:t.form.regionIds,"onUpdate:modelValue":o[17]||(o[17]=l=>t.form.regionIds=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u670D\u52A1\u7684\u533A\u57DF",onChange:t.handleRegionChange},null,8,["modelValue","onChange"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:16},{default:a(()=>[e(r,{span:24},{default:a(()=>[e(n,{label:"\u5907\u6CE8",name:"remark","label-col":{span:3},"wrapper-col":{span:21}},{default:a(()=>[e(U,{value:t.form.remark,"onUpdate:value":o[18]||(o[18]=l=>t.form.remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","visible","confirm-loading","onUpdate:visible","onOk"])}const Y=S(H,[["render",J]]);export{Y as default};
