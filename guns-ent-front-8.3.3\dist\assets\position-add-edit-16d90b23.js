import{R as s,r as n,o as _,cb as g,a as h,f as P,w as y,d as O,m as R,M as S}from"./index-18a1ea24.js";import x from"./position-form-f6d27226.js";class v{static findPage(e){return s.getAndLoadData("/hrPosition/page",e)}static add(e){return s.post("/hrPosition/add",e)}static edit(e){return s.post("/hrPosition/edit",e)}static delete(e){return s.post("/hrPosition/delete",e)}static batchDelete(e){return s.post("/hrPosition/batchDelete",e)}static detail(e){return s.getAndLoadData("/hrPosition/detail",e)}}const M={__name:"position-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(c,{emit:e}){const r=c,f=e,o=n(!1),i=n(!1),a=n({}),p=n(null);_(async()=>{r.data?(i.value=!0,a.value=Object.assign({},r.data)):(a.value.positionSort=await g("SYSTEM_HR_POSITION"),i.value=!1)});const d=l=>{f("update:visible",l)},b=async()=>{p.value.$refs.formRef.validate().then(async l=>{if(l){o.value=!0;let t=null;i.value?t=v.edit(a.value):t=v.add(a.value),t.then(async u=>{o.value=!1,R.success(u.message),d(!1),f("done")}).catch(()=>{o.value=!1})}})};return(l,t)=>{const u=S;return h(),P(u,{width:700,maskClosable:!1,visible:r.visible,"confirm-loading":o.value,forceRender:!0,title:i.value?"\u7F16\u8F91\u804C\u4F4D":"\u65B0\u5EFA\u804C\u4F4D","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":d,onOk:b,onClose:t[1]||(t[1]=m=>d(!1))},{default:y(()=>[O(x,{form:a.value,"onUpdate:form":t[0]||(t[0]=m=>a.value=m),ref_key:"positionFormRef",ref:p},null,8,["form"])]),_:1},8,["visible","confirm-loading","title"])}}},B=Object.freeze(Object.defineProperty({__proto__:null,default:M},Symbol.toStringTag,{value:"Module"}));export{v as P,M as _,B as p};
