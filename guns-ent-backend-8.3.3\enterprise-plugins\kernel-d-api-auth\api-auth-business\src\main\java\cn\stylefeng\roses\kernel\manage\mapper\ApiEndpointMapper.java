package cn.stylefeng.roses.kernel.manage.mapper;

import cn.stylefeng.roses.kernel.manage.entity.ApiEndpoint;
import cn.stylefeng.roses.kernel.manage.pojo.request.ApiEndpointRequest;
import cn.stylefeng.roses.kernel.manage.pojo.response.ApiEndpointVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API资源接口列表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023/10/23 22:57
 */
public interface ApiEndpointMapper extends BaseMapper<ApiEndpoint> {

    /**
     * 获取自定义查询列表
     *
     * <AUTHOR>
     * @since 2023/10/23 22:57
     */
    List<ApiEndpointVo> customFindList(@Param("page") Page page, @Param("param") ApiEndpointRequest request);

}
