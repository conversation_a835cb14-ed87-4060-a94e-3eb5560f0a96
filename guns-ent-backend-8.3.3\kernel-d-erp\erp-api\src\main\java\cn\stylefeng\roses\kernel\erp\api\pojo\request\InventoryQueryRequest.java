package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存查询请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryQueryRequest extends BaseRequest {

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 条形码
     */
    @ChineseDescription("条形码")
    private String barcode;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 经营方式
     */
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 经营方式列表
     */
    @ChineseDescription("经营方式列表")
    private List<String> businessModeList;

    /**
     * 计价类型
     */
    @ChineseDescription("计价类型")
    private String pricingType;

    /**
     * 计价类型列表
     */
    @ChineseDescription("计价类型列表")
    private List<String> pricingTypeList;

    /**
     * 商品分类ID
     */
    @ChineseDescription("商品分类ID")
    private Long categoryId;

    /**
     * 最小当前库存
     */
    @ChineseDescription("最小当前库存")
    private BigDecimal minCurrentStock;

    /**
     * 最大当前库存
     */
    @ChineseDescription("最大当前库存")
    private BigDecimal maxCurrentStock;

    /**
     * 最小库存总价值
     */
    @ChineseDescription("最小库存总价值")
    private BigDecimal minTotalValue;

    /**
     * 最大库存总价值
     */
    @ChineseDescription("最大库存总价值")
    private BigDecimal maxTotalValue;

    /**
     * 是否只显示预警库存
     */
    @ChineseDescription("是否只显示预警库存")
    private Boolean warningOnly;

    /**
     * 是否只显示缺货商品
     */
    @ChineseDescription("是否只显示缺货商品")
    private Boolean outOfStockOnly;

    /**
     * 商品状态
     */
    @ChineseDescription("商品状态")
    private String productStatus;

    /**
     * 商品状态列表
     */
    @ChineseDescription("商品状态列表")
    private List<String> productStatusList;

}