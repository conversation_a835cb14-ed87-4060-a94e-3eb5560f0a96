import Request from '@/utils/request/request-util';

/**
 * 临时秘钥-API接口
 *
 * <AUTHOR>
 * @date 2023/11/02 17:52
 */
export class SysUserSecretKeyApi {

  /**
   * 获取分页列表
   *
   * <AUTHOR>
   * @date 2023/11/02 17:52
   */
  static findPage(params) {
    return Request.getAndLoadData('/sysUserSecretKey/page', params);
  }

  /**
   * 添加临时秘钥
   *
   * <AUTHOR>
   * @date 2023/11/02 17:52
   */
  static add(params) {
    return Request.post('/sysUserSecretKey/add', params);
  }

  /**
   * 编辑临时秘钥
   *
   * <AUTHOR>
   * @date 2023/11/02 17:52
   */
  static edit(params) {
    return Request.post('/sysUserSecretKey/edit', params);
  }

  /**
   * 删除单个临时秘钥
   *
   * <AUTHOR>
   * @date 2023/11/02 17:52
   */
  static delete(params) {
    return Request.post('/sysUserSecretKey/delete', params);
  }

  /**
   * 删除批量临时秘钥
   *
   * <AUTHOR>
   * @date 2023/11/02 17:52
   */
  static batchDelete(params) {
    return Request.post('/sysUserSecretKey/batchDelete', params);
  }

  /**
   * 临时秘钥详情
   *
   * <AUTHOR>
   * @date 2023/11/02 17:52
   */
  static detail(params) {
    return Request.getAndLoadData('/sysUserSecretKey/detail', params);
  }

}