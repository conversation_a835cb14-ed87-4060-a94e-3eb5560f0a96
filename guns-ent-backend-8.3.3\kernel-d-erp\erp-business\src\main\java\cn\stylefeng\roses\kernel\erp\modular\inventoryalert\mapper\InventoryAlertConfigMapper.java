package cn.stylefeng.roses.kernel.erp.modular.inventoryalert.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryAlertConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 库存预警配置Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/30 19:00
 */
public interface InventoryAlertConfigMapper extends BaseMapper<InventoryAlertConfig> {

    /**
     * 根据配置键查询配置值
     *
     * @param configKey 配置键
     * @param tenantId  租户ID
     * @return 配置值
     */
    @Select("SELECT config_value FROM erp_inventory_alert_config " +
            "WHERE config_key = #{configKey} " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if> " +
            "LIMIT 1")
    String selectValueByKey(@Param("configKey") String configKey, @Param("tenantId") Long tenantId);

    /**
     * 检查配置键是否重复
     *
     * @param configKey 配置键
     * @param excludeId 排除的ID（编辑时使用）
     * @param tenantId  租户ID
     * @return 重复数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM erp_inventory_alert_config " +
            "WHERE config_key = #{configKey} " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if> " +
            "<if test='excludeId != null'> AND id != #{excludeId} </if>" +
            "</script>")
    int countByConfigKey(@Param("configKey") String configKey,
                        @Param("excludeId") Long excludeId,
                        @Param("tenantId") Long tenantId);

    /**
     * 查询系统配置列表
     *
     * @param tenantId 租户ID
     * @return 系统配置列表
     */
    @Select("SELECT * FROM erp_inventory_alert_config " +
            "WHERE is_system = 'Y' " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if> " +
            "ORDER BY config_key")
    List<InventoryAlertConfig> selectSystemConfigs(@Param("tenantId") Long tenantId);

    /**
     * 查询用户配置列表
     *
     * @param tenantId 租户ID
     * @return 用户配置列表
     */
    @Select("SELECT * FROM erp_inventory_alert_config " +
            "WHERE is_system = 'N' " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if> " +
            "ORDER BY config_key")
    List<InventoryAlertConfig> selectUserConfigs(@Param("tenantId") Long tenantId);

    /**
     * 根据配置类型查询配置列表
     *
     * @param configType 配置类型
     * @param tenantId   租户ID
     * @return 配置列表
     */
    @Select("SELECT * FROM erp_inventory_alert_config " +
            "WHERE config_type = #{configType} " +
            "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if> " +
            "ORDER BY config_key")
    List<InventoryAlertConfig> selectByConfigType(@Param("configType") String configType,
                                                 @Param("tenantId") Long tenantId);

    /**
     * 批量插入配置
     *
     * @param configs 配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("configs") List<InventoryAlertConfig> configs);

    /**
     * 批量更新配置值
     *
     * @param configs 配置列表
     * @return 更新数量
     */
    int batchUpdateValue(@Param("configs") List<InventoryAlertConfig> configs);
}
