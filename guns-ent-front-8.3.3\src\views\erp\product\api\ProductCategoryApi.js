import Request from '@/utils/request/request-util';

/**
 * 商品分类关联API
 *
 * <AUTHOR>
 * @since 2025/07/23 11:30
 */
export class ProductCategoryApi {
  
  /**
   * 根据分类ID查询商品列表
   * @param {Object} params 包含categoryId的参数
   * @returns {Promise}
   */
  static findProductsByCategoryId(params) {
    return Request.getAndLoadData('/erp/product/listByCategoryId', params);
  }


}

// 为了向后兼容，导出函数形式的API
export const getProductsByCategoryId = ProductCategoryApi.findProductsByCategoryId;