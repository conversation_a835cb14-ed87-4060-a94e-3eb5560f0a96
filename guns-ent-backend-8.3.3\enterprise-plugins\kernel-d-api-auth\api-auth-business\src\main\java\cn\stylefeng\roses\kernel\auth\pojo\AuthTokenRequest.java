package cn.stylefeng.roses.kernel.auth.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 获取api认证token的请求参数
 *
 * <AUTHOR>
 * @since 2023/10/25 22:25
 */
@Data
public class AuthTokenRequest {

    /**
     * API客户端唯一标识
     * <p>
     * 对应api_client表的code
     */
    @ChineseDescription("API客户端唯一标识")
    @NotBlank(message = "API客户端唯一标识不能为空")
    private String apiClientCode;

    /**
     * API客户端认证秘钥编码
     * <p>
     * 具体加密过程：md5（秘钥 + 时间戳（例如：202203231123））
     */
    @ChineseDescription("API客户端认证秘钥编码")
    @NotBlank(message = "API客户端认证秘钥编码不能为空")
    private String apiClientSecretEncode;

    /**
     * 请求时候的时间戳，精确到秒（例如：202203231123）
     */
    @ChineseDescription("时间戳")
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

}
