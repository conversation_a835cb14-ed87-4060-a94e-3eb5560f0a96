<template>
  <a-modal
    :visible="visible"
    :title="isEdit ? '编辑入库单' : '新建入库单'"
    :width="1200"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        {{ isEdit ? '更新' : '保存' }}
      </a-button>
    </template>

    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" style="margin-bottom: 16px">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="入库单号" name="orderNo">
              <a-input v-model:value="formData.orderNo" placeholder="系统自动生成" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商" name="supplierId" required>
              <supplier-selector 
                v-model:value="formData.supplierId" 
                :filter="{ businessMode: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
                @change="onSupplierChange" 
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="订单日期" name="orderDate" required>
              <a-date-picker 
                v-model:value="formData.orderDate" 
                style="width: 100%" 
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="备注" name="remark">
              <a-textarea 
                v-model:value="formData.remark" 
                placeholder="请输入备注信息"
                :rows="3"
                :maxlength="500"
                showCount
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 商品明细 -->
      <a-card title="商品明细" size="small" style="margin-bottom: 16px">
        <template #extra>
          <a-button type="primary" size="small" @click="addProduct" :disabled="!formData.supplierId">
            <plus-outlined />
            添加商品
          </a-button>
        </template>

        <inbound-detail-table 
          v-model:value="formData.detailList" 
          :supplier-id="formData.supplierId"
          @change="onDetailChange"
        />
      </a-card>

      <!-- 汇总信息 -->
      <a-card title="汇总信息" size="small">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="商品种类" :value="productCount" suffix="种" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总数量" :value="totalQuantity" suffix="件" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总金额" :value="totalAmount" prefix="¥" :precision="2" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均单价" :value="averagePrice" prefix="¥" :precision="2" />
          </a-col>
        </a-row>
      </a-card>
    </a-form>

    <!-- 商品选择弹窗 -->
    <product-selector-modal
      v-model:visible="showProductSelector"
      :supplier-id="formData.supplierId"
      @select="onProductSelect"
    />
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { PurchaseApi } from '../../api/PurchaseApi';
import SupplierSelector from '@/components/erp/SupplierSelector.vue';
import InboundDetailTable from './InboundDetailTable.vue';
import ProductSelectorModal from './ProductSelectorModal.vue';
import dayjs from 'dayjs';

export default {
  name: 'InboundForm',
  components: {
    PlusOutlined,
    SupplierSelector,
    InboundDetailTable,
    ProductSelectorModal
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const loading = ref(false);
    const showProductSelector = ref(false);

    // 表单数据
    const formData = reactive({
      id: null,
      orderNo: '',
      supplierId: null,
      orderDate: dayjs().format('YYYY-MM-DD'),
      remark: '',
      detailList: []
    });

    // 表单验证规则
    const rules = {
      supplierId: [
        { required: true, message: '请选择供应商', trigger: 'change' }
      ],
      orderDate: [
        { required: true, message: '请选择订单日期', trigger: 'change' }
      ]
    };

    // 计算属性
    const isEdit = computed(() => {
      return !!(props.data && props.data.id);
    });

    const productCount = computed(() => {
      return formData.detailList ? formData.detailList.length : 0;
    });

    const totalQuantity = computed(() => {
      if (!formData.detailList || formData.detailList.length === 0) return 0;
      return formData.detailList.reduce((total, item) => {
        return total + (parseFloat(item.quantity) || 0);
      }, 0);
    });

    const totalAmount = computed(() => {
      if (!formData.detailList || formData.detailList.length === 0) return 0;
      return formData.detailList.reduce((total, item) => {
        const quantity = parseFloat(item.quantity) || 0;
        const unitPrice = parseFloat(item.unitPrice) || 0;
        return total + (quantity * unitPrice);
      }, 0);
    });

    const averagePrice = computed(() => {
      if (totalQuantity.value === 0) return 0;
      return totalAmount.value / totalQuantity.value;
    });

    // 监听props.data变化，用于编辑时填充表单
    watch(() => props.data, (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        Object.assign(formData, {
          id: newData.id,
          orderNo: newData.orderNo,
          supplierId: newData.supplierId,
          orderDate: newData.orderDate,
          remark: newData.remark,
          detailList: newData.detailList || []
        });
      }
    }, { immediate: true });

    // 监听visible变化，显示时重置表单或生成单号
    watch(() => props.visible, async (visible) => {
      if (visible) {
        if (!isEdit.value) {
          // 新增时重置表单并生成单号
          resetForm();
          await generateOrderNo();
        }
      }
    });

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        id: null,
        orderNo: '',
        supplierId: null,
        orderDate: dayjs().format('YYYY-MM-DD'),
        remark: '',
        detailList: []
      });
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    };

    // 生成入库单号
    const generateOrderNo = async () => {
      try {
        const response = await PurchaseApi.generateOrderNo();
        if (response.success) {
          formData.orderNo = response.data;
        }
      } catch (error) {
        console.error('生成入库单号失败:', error);
      }
    };

    // 供应商变化事件
    const onSupplierChange = (supplierId) => {
      // 清空商品明细
      formData.detailList = [];
    };

    // 明细变化事件
    const onDetailChange = (detailList) => {
      formData.detailList = detailList;
    };

    // 添加商品
    const addProduct = () => {
      if (!formData.supplierId) {
        message.warning('请先选择供应商');
        return;
      }
      showProductSelector.value = true;
    };

    // 商品选择回调
    const onProductSelect = (products) => {
      products.forEach(product => {
        // 检查是否已存在
        const exists = formData.detailList.find(item => item.productId === product.productId);
        if (!exists) {
          formData.detailList.push({
            productId: product.productId,
            productCode: product.productCode,
            productName: product.productName,
            specification: product.specification,
            unit: product.unit,
            pricingType: product.pricingType,
            quantity: 1,
            unitPrice: product.retailPrice || 0,
            totalPrice: product.retailPrice || 0,
            remark: ''
          });
        }
      });
      showProductSelector.value = false;
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 提交
    const handleSubmit = () => {
      formRef.value.validate().then(async () => {
        if (!formData.detailList || formData.detailList.length === 0) {
          message.warning('请添加商品明细');
          return;
        }

        loading.value = true;
        try {
          const submitData = {
            ...formData,
            totalAmount: totalAmount.value
          };

          if (isEdit.value) {
            await PurchaseApi.edit(submitData);
            message.success('更新成功');
          } else {
            await PurchaseApi.add(submitData);
            message.success('保存成功');
          }
          
          emit('ok');
        } catch (error) {
          message.error((isEdit.value ? '更新' : '保存') + '失败：' + (error.message || '未知错误'));
        } finally {
          loading.value = false;
        }
      });
    };

    return {
      formRef,
      loading,
      showProductSelector,
      formData,
      rules,
      isEdit,
      productCount,
      totalQuantity,
      totalAmount,
      averagePrice,
      onSupplierChange,
      onDetailChange,
      addProduct,
      onProductSelect,
      handleCancel,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.ant-card {
  border-radius: 6px;
}

.ant-card-head-title {
  font-weight: 500;
}

.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
}

.ant-statistic-content {
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}
</style>
