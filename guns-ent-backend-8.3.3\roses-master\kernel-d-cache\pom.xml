<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.roses</groupId>
        <artifactId>roses-kernel</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>kernel-d-cache</artifactId>

    <packaging>pom</packaging>

    <modules>
        <module>cache-api</module>
        <module>cache-sdk-memory</module>
        <module>cache-sdk-redis</module>
        <module>cache-spring-boot-starter-memory</module>
        <module>cache-spring-boot-starter-redis</module>
    </modules>

    <dependencies>

        <!-- 开发规则 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>kernel-a-rule</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>

</project>
