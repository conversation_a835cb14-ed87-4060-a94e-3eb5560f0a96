<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.sys.modular.security.mapper.SysUserPasswordRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.sys.modular.security.entity.SysUserPasswordRecord">
		<id column="record_id" property="recordId" />
		<result column="user_id" property="userId" />
		<result column="history_password" property="historyPassword" />
		<result column="history_password_salt" property="historyPasswordSalt" />
		<result column="update_password_time" property="updatePasswordTime" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
	</resultMap>

	<sql id="Base_Column_List">
		record_id,user_id,history_password,history_password_salt,update_password_time,create_time,create_user,update_time,update_user
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.sys.modular.security.pojo.response.SysUserPasswordRecordVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		sys_user_password_record tbl
		WHERE
		<where>
        <if test="param.recordId != null and param.recordId != ''">
            and tbl.record_id like concat('%',#{param.recordId},'%')
        </if>
        <if test="param.userId != null and param.userId != ''">
            and tbl.user_id like concat('%',#{param.userId},'%')
        </if>
        <if test="param.historyPassword != null and param.historyPassword != ''">
            and tbl.history_password like concat('%',#{param.historyPassword},'%')
        </if>
        <if test="param.historyPasswordSalt != null and param.historyPasswordSalt != ''">
            and tbl.history_password_salt like concat('%',#{param.historyPasswordSalt},'%')
        </if>
        <if test="param.updatePasswordTime != null and param.updatePasswordTime != ''">
            and tbl.update_password_time like concat('%',#{param.updatePasswordTime},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
		</where>
	</select>

</mapper>
