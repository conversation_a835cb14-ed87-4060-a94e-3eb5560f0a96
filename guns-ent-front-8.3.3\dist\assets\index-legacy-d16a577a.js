System.register(["./index-legacy-ee1db0c7.js"],(function(t,e){"use strict";var n,r,a,l,o,i,c,s,u;return{setters:[t=>{n=t._,r=t.a,a=t.c,l=t.d,o=t.w,i=t.k,c=t.ck,s=t.g,u=t.B}],execute:function(){var e=document.createElement("style");e.textContent=".ant-result{padding:48px 32px}.ant-result-success .ant-result-icon>.anticon{color:var(--success-color)}.ant-result-error .ant-result-icon>.anticon{color:var(--error-color)}.ant-result-info .ant-result-icon>.anticon{color:var(--info-color)}.ant-result-warning .ant-result-icon>.anticon{color:var(--warning-color)}.ant-result-image{width:250px;height:295px;margin:auto}.ant-result-icon{margin-bottom:24px;text-align:center}.ant-result-icon>.anticon{font-size:72px}.ant-result-title{color:var(--heading-color);font-size:24px;line-height:1.8;text-align:center}.ant-result-subtitle{color:var(--text-color-secondary);font-size:14px;line-height:1.6;text-align:center}.ant-result-extra{margin:24px 0 0;text-align:center}.ant-result-extra>*{margin-right:8px}.ant-result-extra>*:last-child{margin-right:0}.ant-result-content{margin-top:24px;padding:24px 40px;background-color:var(--background-color-light)}.ant-result-rtl{direction:rtl}.ant-result-rtl .ant-result-extra>*{margin-right:0;margin-left:8px}.ant-result-rtl .ant-result-extra>*:last-child{margin-left:0}\n",document.head.appendChild(e);const g={style:{"padding-top":"80px"}};t("default",n({name:"Exception404"},[["render",function(t,e,n,x,d,p){const m=u,h=i("router-link"),f=c;return r(),a("div",g,[l(f,{status:"404",title:"404","sub-title":"抱歉, 你访问的页面不存在."},{extra:o((()=>[l(h,{to:"/"},{default:o((()=>[l(m,{type:"primary"},{default:o((()=>e[0]||(e[0]=[s("返回首页")]))),_:1,__:[0]})])),_:1})])),_:1})])}]]))}}}));
