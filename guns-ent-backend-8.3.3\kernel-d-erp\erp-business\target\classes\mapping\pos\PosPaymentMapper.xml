<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosPaymentMapper">

    <!-- 根据订单ID查询支付记录 -->
    <select id="findByOrderId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment">
        SELECT * FROM pos_payment
        WHERE order_id = #{orderId}
        AND del_flag = 'N'
        ORDER BY create_time DESC
    </select>

    <!-- 根据支付单号查询支付记录 -->
    <select id="findByPaymentNo" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment">
        SELECT * FROM pos_payment
        WHERE payment_no = #{paymentNo}
        AND del_flag = 'N'
    </select>

    <!-- 根据第三方交易号查询支付记录 -->
    <select id="findByTransactionId" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment">
        SELECT * FROM pos_payment
        WHERE transaction_id = #{transactionId}
        AND del_flag = 'N'
    </select>

    <!-- 根据支付方式统计支付金额 -->
    <select id="getPaymentStatsByMethod" resultType="java.util.Map">
        SELECT 
            payment_method,
            COUNT(*) as payment_count,
            SUM(payment_amount) as total_payment_amount,
            AVG(payment_amount) as avg_payment_amount,
            SUM(CASE WHEN received_amount IS NOT NULL THEN received_amount ELSE 0 END) as total_received_amount,
            SUM(CASE WHEN change_amount IS NOT NULL THEN change_amount ELSE 0 END) as total_change_amount
        FROM pos_payment
        WHERE create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        AND del_flag = 'N'
        <if test="paymentMethod != null and paymentMethod != ''">
            AND payment_method = #{paymentMethod}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND payment_status = #{paymentStatus}
        </if>
        GROUP BY payment_method
        ORDER BY total_payment_amount DESC
    </select>

    <!-- 查询指定时间范围内的支付记录 -->
    <select id="findByTimeRange" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosPayment">
        SELECT * FROM pos_payment
        WHERE create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        AND del_flag = 'N'
        <if test="paymentMethod != null and paymentMethod != ''">
            AND payment_method = #{paymentMethod}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND payment_status = #{paymentStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新支付状态 -->
    <update id="batchUpdatePaymentStatus">
        UPDATE pos_payment 
        SET payment_status = #{paymentStatus},
            update_user = #{updateUser},
            update_time = NOW()
        WHERE payment_id IN
        <foreach collection="paymentIds" item="paymentId" open="(" separator="," close=")">
            #{paymentId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 统计各支付方式的使用情况 -->
    <select id="getPaymentMethodStats" resultType="java.util.Map">
        SELECT 
            payment_method,
            COUNT(*) as usage_count,
            SUM(payment_amount) as total_amount,
            AVG(payment_amount) as avg_amount,
            MIN(payment_amount) as min_amount,
            MAX(payment_amount) as max_amount,
            COUNT(CASE WHEN payment_status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN payment_status = 'FAILED' THEN 1 END) as failed_count
        FROM pos_payment
        WHERE create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        AND del_flag = 'N'
        GROUP BY payment_method
        ORDER BY usage_count DESC
    </select>

</mapper>