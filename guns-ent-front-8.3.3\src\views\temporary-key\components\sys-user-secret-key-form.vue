<template>
  <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
    <a-row :gutter="20">
      <a-col :span="12">
        <a-form-item label="秘钥名称:" name="secretKeyName">
          <a-input v-model:value="form.secretKeyName" allow-clear placeholder="请输入秘钥名称" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="秘钥所属人:" name="userIdName">
          <a-input v-model:value="form.userIdName" placeholder="请选择所属人" @focus="userIdClick()" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="临时秘钥值:" name="secretKey">
          <a-input-group compact style="display: flex">
            <a-input v-model:value="form.secretKey" allow-clear placeholder="请输入秘钥值，经过MD5加密" />
            <a-button type="primary" @click="generate">点击生成</a-button>
          </a-input-group>
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="密码过期时间:" name="secretExpirationTime">
          <a-date-picker
            v-model:value="form.secretExpirationTime"
            value-format="YYYY-MM-DD"
            placeholder="请选择秘钥过期时间"
            style="width: 100%"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="是否为一次性秘钥:" name="secretOnceFlag" class="row">
          <vxe-switch v-model="form.secretOnceFlag" open-value="Y" close-value="N" style="margin-top: -3px" />
        </a-form-item>
      </a-col>
      <a-col :span="24"> 特殊说明：秘钥生成后会加密存储到数据库，无法修改和查看，请记录好秘钥 </a-col>
    </a-row>

    <!-- 选择人员 -->
    <Selection
      v-model:visible="commonVisibleUser"
      v-if="commonVisibleUser"
      :data="commonSelectedData"
      :showTab="['user']"
      :changeHeight="true"
      title="人员选择"
      @done="closeSelectUser"
    />
  </a-form>
</template>

<script setup name="SysUserSecretKeyForm">
import { reactive, ref } from 'vue';
import { uuid } from '@/utils/common/print';

const props = defineProps({
  // 表单数据
  form: Object
});

// 验证规则
const rules = reactive({
  secretKeyName: [{ required: true, message: '请输入秘钥名称', type: 'string', trigger: 'blur' }],
  userIdName: [{ required: true, message: '请选择所属人', type: 'string', trigger: 'blur' }],
  secretKey: [{ required: true, message: '请输入秘钥值，经过MD5加密', type: 'string', trigger: 'blur' }],
  secretOnceFlag: [{ required: true, message: '请选择秘钥是否使用一次', type: 'string', trigger: 'change1' }]
});
// 通用选择总数据，用在选人、选部门、选职位的组件上
const commonSelectedData = ref({});

// 是否显示选人弹框
const commonVisibleUser = ref(false);

// 当前人员类型
const selectUserType = ref('');

/**
 * 关闭选择人员弹框时候的回调
 */
const closeSelectUser = data => {
  const valueData = data.selectUserList[0] || { bizId: '', name: '' };
  if (selectUserType.value === 'userId') {
    props.form.userId = valueData.bizId;
    props.form.userIdName = valueData.name;
  }
};

/**
 * 人员选择的点击事件
 */
const userIdClick = () => {
  selectUserType.value = 'userId';
  const { userId, userIdName } = props.form;
  commonSelectedData.value.selectUserList = [{ bizId: userId, name: userIdName }];
  commonVisibleUser.value = true;
};

const generate = () => {
  props.form.secretKey = uuid(16);
};

defineExpose({});
</script>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
}
</style>
