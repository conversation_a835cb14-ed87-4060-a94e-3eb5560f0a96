package cn.stylefeng.roses.seata.demo.order.modular.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 订单
 *
 * <AUTHOR>
 * @date 2021/08/29 10:33
 */
@TableName("order_tbl")
@Data
public class OrderTbl {

    /**
     * 订单id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 下单人用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 商品编码
     */
    @TableField("commodity_code")
    private String commodityCode;

    /**
     * 商品数量
     */
    @TableField("count")
    private Integer count;

    /**
     * 商品总金额
     */
    @TableField("money")
    private Integer money;

}