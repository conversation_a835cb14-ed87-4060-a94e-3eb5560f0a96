System.register(["./index-legacy-ee1db0c7.js","./ProductDisplayArea-legacy-98358b54.js","./ShoppingCart-legacy-6e7fc1f1.js","./ToolbarPanel-legacy-6cb73d3a.js","./OrderSuspend-legacy-12d63ded.js","./PaymentPanel-legacy-8712b7c7.js","./pos-legacy-fe4fee5f.js","./performance-monitor-legacy-4ff0ac5f.js","./constants-legacy-2a31d63c.js","./formatter-legacy-97503ee9.js","./common-legacy-b1e2d362.js","./CartHeader-legacy-1c7632ad.js","./CartItem-legacy-8641f838.js","./index-legacy-94a6fc23.js","./CartSummary-legacy-fa651ed6.js","./FunctionButton-legacy-c76ff097.js","./OrderSummary-legacy-e7630f7e.js","./SuspendedOrderItem-legacy-38b299a9.js","./CashPayment-legacy-2c3f7c6c.js"],(function(e,t){"use strict";var a,r,n,s,o,c,i,l,d,u,p,m,y,v,h,g,f,b,x,C,<PERSON>,w,P,A,O,S,D,M,k,R,E,N,U,j,T,_,W,F,L,H,q,z,$,B,Q,G;return{setters:[e=>{a=e.R,r=e.r,n=e.L,s=e.s,o=e.m,c=e.M,i=e.b2,l=e.b3,d=e.o,u=e.aL,p=e._,m=e.a,y=e.c,v=e.b,h=e.d,g=e.I,f=e.t,b=e.at,x=e.w,C=e.aq,I=e.g,w=e.f,P=e.b4,A=e.b5,O=e.ad,S=e.b6,D=e.h,M=e.b7,k=e.a2,R=e.B,E=e.a7,N=e.n},e=>{U=e.default},e=>{j=e.C,T=e.P,_=e.u,W=e.S},e=>{F=e.default},e=>{L=e.default},e=>{H=e.default},e=>{q=e.u},e=>{z=e.a,$=e.P},e=>{B=e.a,Q=e.b},e=>{G=e.A},null,null,null,null,null,null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".pos-main[data-v-3ccb510b]{height:100vh;display:flex;flex-direction:column;background:#f5f5f5;overflow:hidden}.pos-main.fullscreen[data-v-3ccb510b]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999}.pos-toolbar[data-v-3ccb510b]{display:flex;justify-content:space-between;align-items:center;padding:12px 20px;background:#fff;border-bottom:1px solid #e8e8e8;box-shadow:0 2px 8px rgba(0,0,0,.06);flex-shrink:0}.toolbar-left[data-v-3ccb510b]{display:flex;align-items:center;gap:24px}.pos-logo[data-v-3ccb510b]{display:flex;align-items:center;gap:8px;font-size:18px;font-weight:600;color:#1890ff}.logo-text[data-v-3ccb510b]{font-size:16px}.cashier-info[data-v-3ccb510b]{display:flex;align-items:center;gap:16px;font-size:14px;color:#666}.pos-content[data-v-3ccb510b]{flex:1;display:flex;gap:16px;padding:16px;overflow:hidden;min-height:0}.pos-left[data-v-3ccb510b]{flex:3;min-width:0;display:flex;flex-direction:column}.pos-center[data-v-3ccb510b]{flex:2;display:flex;flex-direction:column;min-width:0}.cart-section[data-v-3ccb510b]{flex:1;min-height:0;display:flex;flex-direction:column}.pos-right[data-v-3ccb510b]{width:120px;flex-shrink:0}.pos-card[data-v-3ccb510b]{background:#fff;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,.06);overflow:hidden;display:flex;flex-direction:column}.pos-statusbar[data-v-3ccb510b]{display:flex;justify-content:space-between;align-items:center;padding:8px 20px;background:#fff;border-top:1px solid #e8e8e8;font-size:12px;color:#666;flex-shrink:0}.status-left[data-v-3ccb510b],.status-right[data-v-3ccb510b]{display:flex;align-items:center;gap:16px}.status-item[data-v-3ccb510b]{display:flex;align-items:center;gap:4px}.help-content[data-v-3ccb510b]{padding:16px 0}.help-section[data-v-3ccb510b]{margin-bottom:24px}.help-section h4[data-v-3ccb510b]{margin:0 0 12px;font-size:14px;font-weight:600;color:#262626}.help-item[data-v-3ccb510b]{display:flex;align-items:center;gap:12px;margin-bottom:8px}.help-item .key[data-v-3ccb510b]{display:inline-block;padding:2px 8px;background:#f5f5f5;border:1px solid #d9d9d9;border-radius:4px;font-family:monospace;font-size:12px;min-width:60px;text-align:center}.help-item .desc[data-v-3ccb510b]{font-size:13px;color:#595959}@media (max-width: 1200px){.pos-content[data-v-3ccb510b]{gap:12px;padding:12px}.pos-right[data-v-3ccb510b]{width:100px}}@media (max-width: 768px){.pos-content[data-v-3ccb510b]{flex-direction:column}.pos-left[data-v-3ccb510b],.pos-center[data-v-3ccb510b],.pos-right[data-v-3ccb510b]{flex:none;width:100%}.pos-right[data-v-3ccb510b]{height:80px}}\n",document.head.appendChild(t);class K{static createApiWrapper(e,t={}){const{context:a="Payment API调用",showMessage:r=!0,showNotification:n=!0,retryOptions:s={maxRetries:0}}=t,o=z.measureApiCall(a,e);return $.wrapApiCall(o,{showMessage:r,showNotification:n,context:a,retryOptions:s})}static async processCashPayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/cash",{...e,paymentMethod:B.CASH,paymentTime:(new Date).toISOString()})),{context:"处理现金支付",retryOptions:{maxRetries:0}})()}static async processQrCodePayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/qrcode",{...e,paymentTime:(new Date).toISOString()})),{context:"处理扫码支付",retryOptions:{maxRetries:0}})()}static async queryQrCodePaymentStatus(e){return this.createApiWrapper((()=>a.get("/erp/pos/payment/qrcode/status",e)),{context:"查询扫码支付状态",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:3,retryDelay:2e3}})()}static async processMemberCardPayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/member",{...e,paymentMethod:B.MEMBER,paymentTime:(new Date).toISOString()})),{context:"处理会员卡支付",retryOptions:{maxRetries:0}})()}static async processPointsPayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/points",{...e,paymentMethod:B.POINTS,paymentTime:(new Date).toISOString()})),{context:"处理积分支付",retryOptions:{maxRetries:0}})()}static async processBankCardPayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/bankcard",{...e,paymentMethod:B.CARD,paymentTime:(new Date).toISOString()})),{context:"处理银行卡支付",retryOptions:{maxRetries:0}})()}static async processComboPayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/combo",{...e,paymentTime:(new Date).toISOString()})),{context:"处理组合支付",retryOptions:{maxRetries:0}})()}static async confirmPaymentSuccess(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/confirm",{...e,confirmTime:e.confirmTime||(new Date).toISOString()})),{context:"确认支付成功",retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async cancelPayment(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/cancel",{...e,cancelTime:(new Date).toISOString()})),{context:"取消支付",retryOptions:{maxRetries:1,retryDelay:1e3}})()}static async requestRefund(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/refund",{...e,refundTime:(new Date).toISOString()})),{context:"申请退款",retryOptions:{maxRetries:1,retryDelay:2e3}})()}static async queryRefundStatus(e){return this.createApiWrapper((()=>a.get("/erp/pos/payment/refund/status",e)),{context:"查询退款状态",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:3,retryDelay:2e3}})()}static async getPaymentRecords(e={}){return this.createApiWrapper((()=>a.get("/erp/pos/payment/records",e)),{context:"获取支付记录",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async getPaymentStatistics(e={}){return this.createApiWrapper((()=>a.get("/erp/pos/payment/statistics",e)),{context:"获取支付统计",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async validatePaymentPassword(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/validatePassword",e)),{context:"验证支付密码",showMessage:!1,retryOptions:{maxRetries:0}})()}static async getPaymentConfig(e={}){return this.createApiWrapper((()=>a.get("/erp/pos/payment/config",e)),{context:"获取支付配置",showMessage:!1,showNotification:!1,retryOptions:{maxRetries:2,retryDelay:1e3}})()}static async testPaymentConnection(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/test",e)),{context:"测试支付连接",retryOptions:{maxRetries:1,retryDelay:3e3}})()}static async syncPaymentStatus(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/sync",e)),{context:"同步支付状态",showMessage:!1,retryOptions:{maxRetries:2,retryDelay:2e3}})()}static async batchProcessPayments(e){return this.createApiWrapper((()=>a.post("/erp/pos/payment/batch",{...e,processTime:(new Date).toISOString()})),{context:"批量处理支付",retryOptions:{maxRetries:1,retryDelay:3e3}})()}}function V(){const e=i(),t=q(),a=l(),{addItem:p,updateQuantity:m,removeItem:y,clearCart:v,recalculateTotal:h}=_(),{processPayment:g,resetPaymentStatus:f}=function(){const e=q(),t=r(Q.UNPAID),a=r(""),i=r(0),l=r(0),d=n((()=>a.value===B.CASH?j.calculateChange(l.value,i.value):0)),u=s({success:!1,paymentId:"",transactionId:"",message:"",timestamp:null}),p=r(!1),m=r({enabledMethods:[],limits:{},settings:{}}),y=n((()=>m.value.enabledMethods.map((e=>({value:e,label:f(e),icon:b(e),enabled:!0,limit:m.value.limits[e]}))))),v=n((()=>i.value>0&&a.value&&t.value===Q.UNPAID&&!p.value)),h=async(t,a=30)=>{let r=0;return new Promise((n=>{const s=async()=>{try{r++;const c=await K.queryQrCodePaymentStatus({paymentId:t,outTradeNo:`${e.currentOrderId}_${t}`});if("SUCCESS"===c.status)return void n(!0);if("FAILED"===c.status||"CANCELLED"===c.status)return void n(!1);if(r>=a)return o.warning("支付超时，请检查支付状态"),void n(!1);setTimeout(s,2e3)}catch(c){console.error("查询支付状态失败:",c),r>=a?n(!1):setTimeout(s,2e3)}};s()}))},g=async()=>{try{var t;const a=await K.getPaymentConfig({storeId:null===(t=e.currentStore)||void 0===t?void 0:t.id});m.value={enabledMethods:a.enabledMethods||Object.values(B),limits:a.limits||{},settings:a.settings||{}}}catch(a){console.error("加载支付配置失败:",a),m.value={enabledMethods:Object.values(B),limits:{},settings:{}}}},f=e=>({[B.CASH]:"现金",[B.WECHAT]:"微信支付",[B.ALIPAY]:"支付宝",[B.MEMBER]:"会员卡",[B.CARD]:"银行卡",[B.POINTS]:"积分支付"}[e]||e),b=e=>({[B.CASH]:"money-collect",[B.WECHAT]:"wechat",[B.ALIPAY]:"alipay",[B.MEMBER]:"credit-card",[B.CARD]:"bank",[B.POINTS]:"gift"}[e]||"pay-circle");return{paymentStatus:t,selectedPaymentMethod:a,paymentAmount:i,receivedAmount:l,changeAmount:d,paymentResult:u,loading:p,paymentConfig:m,availablePaymentMethods:y,canPay:v,initializePayment:async(e,r={})=>{try{const r=T.validatePaymentAmount(e);return r.isValid?(i.value=e,t.value=Q.UNPAID,a.value="",l.value=0,Object.assign(u,{success:!1,paymentId:"",transactionId:"",message:"",timestamp:null}),await g(),!0):(o.error(r.message),!1)}catch(n){return console.error("初始化支付失败:",n),o.error("初始化支付失败，请重试"),!1}},selectPaymentMethod:e=>{if(!m.value.enabledMethods.includes(e))return o.error("该支付方式不可用"),!1;const t=m.value.limits[e];return t&&t.max&&i.value>t.max?(o.error(`${f(e)}单笔限额为${t.max}元`),!1):(a.value=e,e===B.CASH&&(l.value=i.value),!0)},processCashPayment:async(a={})=>{try{var r;p.value=!0,t.value=Q.PROCESSING;const n={orderId:a.orderId||e.currentOrderId,paymentAmount:i.value,receivedAmount:l.value,changeAmount:d.value,cashierId:a.cashierId||(null===(r=e.currentUser)||void 0===r?void 0:r.id)},s=T.validateCashPayment(n);if(!s.isValid)return o.error(s.message),t.value=Q.UNPAID,!1;const c=await K.processCashPayment(n);return c.success?(Object.assign(u,{success:!0,paymentId:c.paymentId,transactionId:c.transactionId,message:"现金支付成功",timestamp:(new Date).toISOString()}),t.value=Q.PAID,o.success("现金支付成功"),!0):(t.value=Q.UNPAID,o.error(c.message||"现金支付失败"),!1)}catch(n){return console.error("现金支付失败:",n),t.value=Q.UNPAID,o.error("现金支付失败，请重试"),!1}finally{p.value=!1}},processQrCodePayment:async(r={})=>{try{var n;p.value=!0,t.value=Q.PROCESSING;const s={orderId:r.orderId||e.currentOrderId,paymentAmount:i.value,paymentMethod:a.value,qrCode:r.qrCode||"",cashierId:r.cashierId||(null===(n=e.currentUser)||void 0===n?void 0:n.id)},c=T.validateQrCodePayment(s);if(!c.isValid)return o.error(c.message),t.value=Q.UNPAID,!1;const l=await K.processQrCodePayment(s);return l.success?await h(l.paymentId)?(Object.assign(u,{success:!0,paymentId:l.paymentId,transactionId:l.transactionId,message:`${f(a.value)}支付成功`,timestamp:(new Date).toISOString()}),t.value=Q.PAID,o.success(`${f(a.value)}支付成功`),!0):(t.value=Q.UNPAID,o.error("支付失败或已取消"),!1):(t.value=Q.UNPAID,o.error(l.message||"发起支付失败"),!1)}catch(s){return console.error("扫码支付失败:",s),t.value=Q.UNPAID,o.error("扫码支付失败，请重试"),!1}finally{p.value=!1}},processMemberCardPayment:async(a={})=>{try{var r;p.value=!0,t.value=Q.PROCESSING;const n={orderId:a.orderId||e.currentOrderId,paymentAmount:i.value,memberId:a.memberId,memberCardNo:a.memberCardNo,cashierId:a.cashierId||(null===(r=e.currentUser)||void 0===r?void 0:r.id)};if(!n.memberId||!n.memberCardNo)return o.error("请先选择会员"),t.value=Q.UNPAID,!1;const s=await K.processMemberCardPayment(n);return s.success?(Object.assign(u,{success:!0,paymentId:s.paymentId,transactionId:s.transactionId,message:"会员卡支付成功",timestamp:(new Date).toISOString()}),t.value=Q.PAID,o.success(`会员卡支付成功，余额：${s.remainingBalance}元`),!0):(t.value=Q.UNPAID,o.error(s.message||"会员卡支付失败"),!1)}catch(n){return console.error("会员卡支付失败:",n),t.value=Q.UNPAID,o.error("会员卡支付失败，请重试"),!1}finally{p.value=!1}},processComboPayment:async(a,r={})=>{try{var n;p.value=!0,t.value=Q.PROCESSING;const s=a.reduce(((e,t)=>e+t.amount),0);if(Math.abs(s-i.value)>.01)return o.error("组合支付金额不匹配"),t.value=Q.UNPAID,!1;const c={orderId:r.orderId||e.currentOrderId,totalAmount:i.value,paymentMethods:a,cashierId:r.cashierId||(null===(n=e.currentUser)||void 0===n?void 0:n.id)},l=await K.processComboPayment(c);return l.success?(Object.assign(u,{success:!0,paymentId:l.paymentId,transactionId:l.transactionId,message:"组合支付成功",timestamp:(new Date).toISOString()}),t.value=Q.PAID,o.success("组合支付成功"),!0):(t.value=Q.UNPAID,o.error(l.message||"组合支付失败"),!1)}catch(s){return console.error("组合支付失败:",s),t.value=Q.UNPAID,o.error("组合支付失败，请重试"),!1}finally{p.value=!1}},cancelPayment:async(r="用户取消")=>{try{var n;return u.paymentId&&await K.cancelPayment({paymentId:u.paymentId,cancelReason:r,cancelBy:null===(n=e.currentUser)||void 0===n?void 0:n.id}),t.value=Q.UNPAID,a.value="",l.value=0,Object.assign(u,{success:!1,paymentId:"",transactionId:"",message:"",timestamp:null}),o.info("支付已取消"),!0}catch(s){return console.error("取消支付失败:",s),o.error("取消支付失败"),!1}},requestRefund:async t=>{try{var a;p.value=!0;const r={paymentId:t.paymentId||u.paymentId,refundAmount:t.refundAmount||i.value,refundReason:t.refundReason||"客户要求退款",refundBy:t.refundBy||(null===(a=e.currentUser)||void 0===a?void 0:a.id)},n=await K.requestRefund(r);return n.success?(o.success("退款申请已提交"),!0):(o.error(n.message||"退款申请失败"),!1)}catch(r){return console.error("申请退款失败:",r),o.error("申请退款失败，请重试"),!1}finally{p.value=!1}},validatePaymentPassword:async t=>{try{var a;return(await K.validatePaymentPassword({password:t,cashierId:null===(a=e.currentUser)||void 0===a?void 0:a.id})).valid}catch(r){return console.error("验证支付密码失败:",r),!1}},showPaymentConfirmDialog:(e={})=>new Promise((t=>{c.confirm({title:e.title||"确认支付",content:e.content||`确认支付 ${i.value} 元？`,okText:"确认支付",cancelText:"取消",onOk:()=>t(!0),onCancel:()=>t(!1)})})),getPaymentMethodLabel:f,getPaymentMethodIcon:b,formatChangeDenominations:e=>j.calculateChangeDenominations(e)}}();q();const{suspendOrder:b,resumeOrder:x,deleteOrder:C}=function(){const e=q();return{suspendOrder:async t=>{try{return await e.suspendCurrentOrder(t)}catch(a){throw console.error("挂起订单失败:",a),a}},resumeOrder:async t=>{try{return await e.resumeSuspendedOrder(t)}catch(a){throw console.error("恢复订单失败:",a),a}},deleteOrder:async t=>{try{return await e.deleteSuspendedOrder(t),!0}catch(a){throw console.error("删除订单失败:",a),a}},createOrder:async t=>{try{return await e.createOrder(t)}catch(a){throw console.error("创建订单失败:",a),a}},getOrderList:async(t={})=>{try{return await e.getOrderList(t)}catch(a){throw console.error("获取订单列表失败:",a),a}}}}(),I=r(!1),w=r(!1),P=r(!1),A=r(!1),O=r(""),S=r(""),D=r(null),M=n((()=>a.userInfo||{})),k=n((()=>t.getCartSummary())),R=n((()=>t.hasMember)),E=n((()=>t.currentMember)),N=n((()=>t.suspendedOrders.length)),U=n((()=>t.hasCartItems)),W=n((()=>t.canCheckout)),F=n((()=>({itemCount:k.value.itemCount,totalAmount:k.value.totalAmount,discountAmount:k.value.discountAmount,pointsDeductionAmount:k.value.pointsDeductionAmount,finalAmount:k.value.finalAmount,items:t.cartItems,member:E.value}))),L=()=>{const e=new Date;O.value=e.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),S.value=e.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},H=async e=>{try{await p(e,1),o.success(`已添加 ${e.name}`)}catch(t){console.error("添加商品失败:",t),o.error("添加商品失败: "+t.message)}};return d((()=>{L(),D.value=setInterval(L,1e3);const e=()=>{I.value=!!document.fullscreenElement};return document.addEventListener("fullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e)}})),u((()=>{D.value&&clearInterval(D.value)})),{currentUser:M,currentTime:O,currentDate:S,isFullscreen:I,showHelpModal:w,showPaymentPanel:P,showSuspendedOrdersDrawer:A,cartSummary:k,hasMember:R,currentMember:E,suspendedOrdersCount:N,hasCartItems:U,canCheckout:W,orderInfo:F,toggleFullscreen:()=>{document.fullscreenElement?(document.exitFullscreen(),I.value=!1):(document.documentElement.requestFullscreen(),I.value=!0)},showHelp:()=>{w.value=!0},resetAll:()=>{t.resetAllState(),f(),o.success("已重置所有状态")},logout:()=>{a.logout(),e.push("/login")},formatAmount:e=>G.formatCurrency(e||0),handleProductAdd:H,handleProductSelect:e=>{H(e)},handleCartChange:e=>{const{type:t,itemId:a,quantity:r,item:n}=e;try{switch(t){case"update":m(a,r);break;case"remove":y(a),o.success(`已移除 ${(null==n?void 0:n.name)||"商品"}`);break;case"add":p(n,r)}}catch(s){console.error("购物车操作失败:",s),o.error("操作失败: "+s.message)}},handleCartClear:()=>{v(),o.success("已清空购物车")},handleCheckout:()=>{W.value?U.value?P.value=!0:o.warning("购物车为空，请先添加商品"):o.warning("购物车为空或正在处理中，无法结账")},handleShowSuspendedOrders:()=>{A.value=!0},handleMemberManagement:()=>{o.info("会员管理功能")},handleOrderSuspended:async e=>{try{await b(e),o.success("订单已挂起"),v()}catch(t){console.error("挂起订单失败:",t),o.error("挂起订单失败: "+t.message)}},handleOrderResumed:async e=>{try{const a=await x(e);o.success("订单已恢复"),A.value=!1,a&&a.cartItems&&t.restoreCartFromData(a)}catch(a){console.error("恢复订单失败:",a),o.error("恢复订单失败: "+a.message)}},handleOrderDeleted:async e=>{try{await C(e),o.success("订单已删除")}catch(t){console.error("删除订单失败:",t),o.error("删除订单失败: "+t.message)}},handlePaymentSuccess:async e=>{try{o.success("支付成功！"),v(),P.value=!1,f(),console.log("支付成功:",e)}catch(t){console.error("处理支付成功失败:",t),o.error("处理支付结果失败")}},handlePaymentCancel:()=>{P.value=!1,f(),o.info("已取消支付")}}}const Y={class:"pos-toolbar"},X={class:"toolbar-left"},J={class:"pos-logo"},Z={class:"cashier-info"},ee={class:"cashier-name"},te={class:"work-time"},ae={class:"toolbar-right"},re={class:"pos-content"},ne={class:"pos-left pos-card"},se={class:"pos-center"},oe={class:"cart-section pos-card"},ce={class:"pos-right"},ie={class:"pos-statusbar"},le={class:"status-left"},de={class:"status-item"},ue={class:"status-item"},pe={key:0,class:"status-item"},me={class:"status-right"},ye={class:"status-item"},ve={class:"status-item"};e("default",p(Object.assign({name:"PosIndex"},{__name:"index",setup(e){const{currentUser:t,currentTime:a,currentDate:r,isFullscreen:n,showHelpModal:s,showPaymentPanel:o,showSuspendedOrdersDrawer:i,cartSummary:l,hasMember:p,currentMember:j,suspendedOrdersCount:T,orderInfo:_,toggleFullscreen:q,showHelp:z,resetAll:$,logout:B,formatAmount:Q,handleProductAdd:G,handleProductSelect:K,handleCartChange:he,handleCartClear:ge,handleCheckout:fe,handleShowSuspendedOrders:be,handleMemberManagement:xe,handleOrderSuspended:Ce,handleOrderResumed:Ie,handleOrderDeleted:we,handlePaymentSuccess:Pe,handlePaymentCancel:Ae}=V();return function(){const e=e=>{if("INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName){if("F1"===e.key)return e.preventDefault(),void document.dispatchEvent(new CustomEvent("pos:showHelp"));if("F11"===e.key)return e.preventDefault(),void document.dispatchEvent(new CustomEvent("pos:toggleFullscreen"));if(e.ctrlKey&&"r"===e.key)return e.preventDefault(),void document.dispatchEvent(new CustomEvent("pos:resetAll"));if("Escape"!==e.key){if("Enter"===e.key&&e.ctrlKey)return e.preventDefault(),void document.dispatchEvent(new CustomEvent("pos:confirm"));if("Delete"!==e.key)return e.key>="0"&&e.key<="9"&&e.altKey?(e.preventDefault(),void document.dispatchEvent(new CustomEvent("pos:numberInput",{detail:{number:e.key}}))):void 0;document.dispatchEvent(new CustomEvent("pos:delete"))}else document.dispatchEvent(new CustomEvent("pos:cancel"))}};d((()=>{document.addEventListener("keydown",e)})),u((()=>{document.removeEventListener("keydown",e)}))}(),(e,d)=>{const u=R,V=E,Oe=N,Se=c;return m(),y("div",{class:k(["pos-main",{fullscreen:b(n)}])},[v("div",Y,[v("div",X,[v("div",J,[h(g,{iconClass:"icon-pos"}),d[3]||(d[3]=v("span",{class:"logo-text"},"POS收银系统",-1))]),v("div",Z,[v("span",ee,"收银员: "+f(b(t).realName),1),v("span",te,f(b(a)),1)])]),v("div",ae,[h(Oe,null,{default:x((()=>[h(V,{title:"快捷键: F1"},{default:x((()=>[h(u,{onClick:b(z),class:"toolbar-btn"},{icon:x((()=>[h(b(C))])),default:x((()=>[d[4]||(d[4]=I(" 帮助 "))])),_:1,__:[4]},8,["onClick"])])),_:1}),h(V,{title:"快捷键: F11"},{default:x((()=>[h(u,{onClick:b(q),class:"toolbar-btn"},{icon:x((()=>[b(n)?(m(),w(b(A),{key:1})):(m(),w(b(P),{key:0}))])),default:x((()=>[I(" "+f(b(n)?"退出全屏":"全屏"),1)])),_:1},8,["onClick"])])),_:1}),h(V,{title:"快捷键: Ctrl+R"},{default:x((()=>[h(u,{onClick:b($),class:"toolbar-btn"},{icon:x((()=>[h(b(O))])),default:x((()=>[d[5]||(d[5]=I(" 重置 "))])),_:1,__:[5]},8,["onClick"])])),_:1}),h(u,{onClick:b(B),type:"primary",danger:"",class:"logout-btn"},{icon:x((()=>[h(b(S))])),default:x((()=>[d[6]||(d[6]=I(" 退出登录 "))])),_:1,__:[6]},8,["onClick"])])),_:1})])]),v("div",re,[v("div",ne,[h(U,{onProductAdd:b(G),onProductSelect:b(K)},null,8,["onProductAdd","onProductSelect"])]),v("div",se,[v("div",oe,[h(W,{onCartChange:b(he),onCartClear:b(ge),onCheckout:b(fe)},null,8,["onCartChange","onCartClear","onCheckout"])])]),v("div",ce,[h(F,{"suspended-orders-count":b(T),onShowSuspendedOrders:b(be),onMemberManagement:b(xe)},null,8,["suspended-orders-count","onShowSuspendedOrders","onMemberManagement"])])]),v("div",ie,[v("div",le,[v("span",de,[h(g,{iconClass:"icon-cart"}),I(" 购物车: "+f(b(l).itemCount)+"件 ",1)]),v("span",ue,[h(g,{iconClass:"icon-money"}),I(" 金额: "+f(b(Q)(b(l).finalAmount)),1)]),b(p)?(m(),y("span",pe,[h(g,{iconClass:"icon-member"}),I(" 会员: "+f(b(j).memberName),1)])):D("",!0)]),v("div",me,[v("span",ye,[h(g,{iconClass:"icon-suspend"}),I(" 挂单: "+f(b(T))+"个 ",1)]),v("span",ve,[h(g,{iconClass:"icon-time"}),I(" "+f(b(r)),1)])])]),h(L,{visible:b(i),"onUpdate:visible":d[0]||(d[0]=e=>M(i)?i.value=e:null),onOrderSuspended:b(Ce),onOrderResumed:b(Ie),onOrderDeleted:b(we)},null,8,["visible","onOrderSuspended","onOrderResumed","onOrderDeleted"]),h(H,{visible:b(o),"onUpdate:visible":d[1]||(d[1]=e=>M(o)?o.value=e:null),"order-info":b(_),"member-info":b(j),onPaymentSuccess:b(Pe),onPaymentCancel:b(Ae)},null,8,["visible","order-info","member-info","onPaymentSuccess","onPaymentCancel"]),h(Se,{open:b(s),"onUpdate:open":d[2]||(d[2]=e=>M(s)?s.value=e:null),title:"快捷键帮助",footer:null,width:"600px",class:"help-modal"},{default:x((()=>d[7]||(d[7]=[v("div",{class:"help-content"},[v("div",{class:"help-section"},[v("h4",null,"基本操作"),v("div",{class:"help-item"},[v("span",{class:"key"},"F1"),v("span",{class:"desc"},"显示帮助")]),v("div",{class:"help-item"},[v("span",{class:"key"},"F11"),v("span",{class:"desc"},"切换全屏")]),v("div",{class:"help-item"},[v("span",{class:"key"},"Ctrl+R"),v("span",{class:"desc"},"重置所有状态")])])],-1)]))),_:1,__:[7]},8,["open"])],2)}}}),[["__scopeId","data-v-3ccb510b"]]))}}}));
