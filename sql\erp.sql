/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50722
 Source Host           : localhost:3306
 Source Schema         : guns

 Target Server Type    : MySQL
 Target Server Version : 50722
 File Encoding         : 65001

 Date: 02/08/2025 15:12:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for erp_customer
-- ----------------------------
DROP TABLE IF EXISTS `erp_customer`;
CREATE TABLE `erp_customer`  (
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户编码',
  `customer_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户名称',
  `customer_short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户简称',
  `customer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ENTERPRISE' COMMENT '客户类型（ENTERPRISE-企业，INDIVIDUAL-个人，RETAIL-零售）',
  `customer_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'BRONZE' COMMENT '客户等级（DIAMOND-钻石，GOLD-黄金，SILVER-白银，BRONZE-青铜）',
  `region_id` bigint(20) NULL DEFAULT NULL COMMENT '所属区域ID',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号码',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `contact_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系地址',
  `business_license_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `tax_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税务登记号',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '银行账号',
  `credit_limit` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '信用额度',
  `used_credit` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '已用额度',
  `payment_terms` int(11) NULL DEFAULT 30 COMMENT '账期天数',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE' COMMENT '状态（ACTIVE-正常，INACTIVE-停用，FROZEN-冻结）',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`customer_id`) USING BTREE,
  UNIQUE INDEX `uk_customer_code`(`customer_code`) USING BTREE,
  INDEX `idx_customer_name`(`customer_name`) USING BTREE,
  INDEX `idx_region_id`(`region_id`) USING BTREE,
  INDEX `idx_customer_level`(`customer_level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_customer_level_status`(`customer_level`, `status`, `del_flag`) USING BTREE,
  INDEX `idx_erp_customer_tenant_id`(`tenant_id`) USING BTREE,
  CONSTRAINT `fk_customer_region` FOREIGN KEY (`region_id`) REFERENCES `erp_region` (`region_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客户主档案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_customer
-- ----------------------------
INSERT INTO `erp_customer` VALUES (2001, 'CUS001', '北京华联超市连锁有限公司', '北京华联', 'ENTERPRISE', 'VIP', 201, '张采购', '010-66666666', '***********', '<EMAIL>', '北京市海淀区学院路30号', '91110108111111111A', '110108111111111', '中国银行北京分行', '1111111111111111111', 1000000.00, 0.00, 30, 'ACTIVE', 'VIP大客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2002, 'CUS002', '上海永辉超市股份有限公司', '上海永辉', 'ENTERPRISE', 'A', 204, '李经理', '021-55555555', '13900139002', '<EMAIL>', '上海市浦东新区世纪大道1000号', '91310115222222222B', '310115222222222', '工商银行上海分行', '2222222222222222222', 800000.00, 0.00, 45, 'ACTIVE', 'A级客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2003, 'CUS003', '深圳万科物业服务有限公司', '深圳万科物业', 'ENTERPRISE', 'A', 303, '王主管', '0755-44444444', '13700137003', '<EMAIL>', '深圳市南山区深南大道9988号', '91440300333333333C', '440300333333333', '招商银行深圳分行', '3333333333333333333', 600000.00, 0.00, 30, 'ACTIVE', '物业服务公司', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2004, 'CUS004', '杭州阿里巴巴网络技术有限公司', '阿里巴巴', 'ENTERPRISE', 'VIP', 304, '陈总监', '0571-33333333', '13600136004', '<EMAIL>', '杭州市西湖区文三路969号', '91330106444444444D', '330106444444444', '建设银行杭州分行', '4444444444444444444', 2000000.00, 0.00, 60, 'ACTIVE', '互联网巨头', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2005, 'CUS005', '广州美的集团股份有限公司', '美的集团', 'ENTERPRISE', 'VIP', 205, '刘总', '020-22222222', '13500135005', '<EMAIL>', '广州市番禺区美的大道6号', '91440106555555555E', '440106555555555', '农业银行广州分行', '5555555555555555555', 1500000.00, 0.00, 45, 'ACTIVE', '家电制造商', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2006, 'CUS006', '宁波方太厨具有限公司', '方太厨具', 'ENTERPRISE', 'B', 208, '赵经理', '0574-11111111', '13400134006', '<EMAIL>', '宁波市杭州湾新区滨海二路', '91330205666666666F', '330205666666666', '交通银行宁波分行', '6666666666666666666', 400000.00, 0.00, 30, 'ACTIVE', '厨具制造商', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2007, 'CUS007', '张三', '张三', 'INDIVIDUAL', 'C', 201, '张三', '010-77777777', '13300133007', '<EMAIL>', '北京市朝阳区建国路88号', NULL, NULL, '中国银行', '7777777777777777777', 50000.00, 0.00, 15, 'ACTIVE', '个人客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2008, 'CUS008', '李四', '李四', 'INDIVIDUAL', 'C', 204, '李四', '021-88888888', '13200132008', '<EMAIL>', '上海市黄浦区南京路100号', NULL, NULL, '工商银行', '8888888888888888888', 30000.00, 0.00, 15, 'ACTIVE', '个人客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2009, 'CUS009', '王五', '王五', 'INDIVIDUAL', 'B', 206, '王五', '0755-99999999', '13100131009', '<EMAIL>', '深圳市福田区华强北路200号', NULL, NULL, '招商银行', '9999999999999999999', 80000.00, 0.00, 20, 'ACTIVE', '高端个人客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2010, 'CUS010', '赵六', '赵六', 'INDIVIDUAL', 'C', 207, '赵六', '0571-00000000', '13000130010', '<EMAIL>', '杭州市西湖区西湖大道300号', NULL, NULL, '建设银行', '0000000000000000000', 25000.00, 0.00, 15, 'ACTIVE', '个人客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2011, 'CUS011', '测试停用客户', '测试停用', 'ENTERPRISE', 'D', 202, '测试', '010-00000000', '13000000000', '<EMAIL>', '测试地址', '91110000000000000G', '110000000000000', '测试银行', '1111111111111111110', 0.00, 0.00, 0, 'INACTIVE', '测试用停用客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);
INSERT INTO `erp_customer` VALUES (2012, 'CUS012', '黑名单客户', '黑名单', 'ENTERPRISE', 'D', 203, '黑名单', '021-00000000', '13100000000', '<EMAIL>', '黑名单地址', '91310000000000000H', '310000000000000', '黑名单银行', '2222222222222222220', 0.00, 0.00, 0, 'BLACKLIST', '黑名单客户', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:03', 1, 'N', 1);

-- ----------------------------
-- Table structure for erp_customer_region
-- ----------------------------
DROP TABLE IF EXISTS `erp_customer_region`;
CREATE TABLE `erp_customer_region`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `region_id` bigint(20) NOT NULL COMMENT '区域ID',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_erp_customer_region_customer_region`(`customer_id`, `region_id`) USING BTREE COMMENT '客户-区域唯一索引',
  INDEX `idx_erp_customer_region_region_id`(`region_id`) USING BTREE COMMENT '区域ID索引',
  INDEX `idx_erp_customer_region_customer_id`(`customer_id`) USING BTREE COMMENT '客户ID索引',
  CONSTRAINT `fk_customer_region_customer` FOREIGN KEY (`customer_id`) REFERENCES `erp_customer` (`customer_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_customer_region_region` FOREIGN KEY (`region_id`) REFERENCES `erp_region` (`region_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客户-区域关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for erp_inventory
-- ----------------------------
DROP TABLE IF EXISTS `erp_inventory`;
CREATE TABLE `erp_inventory`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `current_stock` decimal(10, 3) NOT NULL DEFAULT 0.000 COMMENT '当前库存',
  `min_stock` decimal(10, 3) NOT NULL DEFAULT 0.000 COMMENT '最小库存（预警值）',
  `max_stock` decimal(10, 3) NULL DEFAULT NULL COMMENT '最大库存',
  `avg_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '平均成本',
  `total_value` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '库存总价值',
  `last_in_time` datetime(0) NULL DEFAULT NULL COMMENT '最后入库时间',
  `last_out_time` datetime(0) NULL DEFAULT NULL COMMENT '最后出库时间',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_product_id`(`product_id`) USING BTREE,
  INDEX `idx_current_stock`(`current_stock`) USING BTREE,
  INDEX `idx_min_stock`(`min_stock`) USING BTREE,
  INDEX `idx_stock_warning`(`current_stock`, `min_stock`) USING BTREE,
  INDEX `idx_stock_alert_check`(`current_stock`, `min_stock`, `max_stock`) USING BTREE COMMENT '库存预警检查索引',
  INDEX `idx_last_in_out_time`(`last_in_time`, `last_out_time`) USING BTREE COMMENT '最后进出库时间索引',
  INDEX `idx_erp_inventory_tenant_id`(`tenant_id`) USING BTREE,
  CONSTRAINT `fk_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `erp_product` (`product_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1950761032456605699 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_inventory
-- ----------------------------
INSERT INTO `erp_inventory` VALUES (1, 3001, 211.000, 20.000, 500.000, 8.58, 1810.38, NULL, NULL, '2025-07-27 23:19:03', 1, '2025-07-27 00:00:00', 1339550467939639299, NULL);
INSERT INTO `erp_inventory` VALUES (2, 3002, 101.000, 10.000, 200.000, 19.90, 2009.90, NULL, NULL, '2025-07-27 23:19:03', 1, '2025-07-27 00:00:00', 1339550467939639299, NULL);
INSERT INTO `erp_inventory` VALUES (3, 3004, 80.000, 80.000, 300.000, 12.00, 960.00, NULL, NULL, '2025-07-27 23:19:03', 1, '2025-08-01 09:58:41', NULL, NULL);
INSERT INTO `erp_inventory` VALUES (4, 3005, 411.000, 50.000, 1000.000, 2.82, 1159.02, NULL, NULL, '2025-07-27 23:19:03', 1, '2025-07-27 00:00:00', 1339550467939639299, NULL);
INSERT INTO `erp_inventory` VALUES (5, 3006, 30.000, 5.000, 100.000, 10.50, 315.00, NULL, NULL, '2025-07-27 23:19:03', 1, '2025-07-27 23:19:03', NULL, NULL);
INSERT INTO `erp_inventory` VALUES (1950761021610135554, 1950457803923525633, 12.000, 2.000, 10.000, 100.00, 1200.00, NULL, NULL, '2025-07-31 11:30:46', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761022478356481, 1950162198944841729, 12.000, 1.000, 11.000, 11.00, 132.00, NULL, NULL, '2025-07-31 11:30:46', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761023350771713, 3009, 2.000, 5.000, 10.000, 10.00, 20.00, NULL, NULL, '2025-07-31 11:30:46', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761024202215426, 3010, 2.000, 5.000, 10.000, 10.00, 20.00, NULL, NULL, '2025-07-31 11:30:46', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761024986550274, 3011, 2.000, 5.000, 10.000, 10.00, 20.00, NULL, NULL, '2025-07-31 11:30:46', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761025837993986, 3012, 2.000, 5.000, 10.000, 22.00, 44.00, NULL, NULL, '2025-07-31 11:30:47', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761026689437698, 3013, 2.000, 5.000, 10.000, 10.00, 20.00, NULL, NULL, '2025-07-31 11:30:47', 1339550467939639299, '2025-07-31 00:00:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761027473772545, 3014, 1.000, 5.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:47', 1339550467939639299, '2025-07-31 15:52:02', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761028325216257, 3015, 1.000, 1.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:47', 1339550467939639299, '2025-07-31 15:52:05', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761029147299841, 3016, 1.000, 1.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:47', 1339550467939639299, '2025-07-31 15:52:05', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761029994549250, 3017, 0.000, 0.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:48', 1339550467939639299, '2025-08-01 09:57:55', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761030791467010, 3018, 0.000, 1.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:48', 1339550467939639299, '2025-07-31 15:55:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761031621939202, 3019, 0.000, 1.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:48', 1339550467939639299, '2025-07-31 15:55:00', 1339550467939639299, 1);
INSERT INTO `erp_inventory` VALUES (1950761032456605698, 3020, 0.000, 1.000, 10.000, 10.00, 10.00, NULL, NULL, '2025-07-31 11:30:48', 1339550467939639299, '2025-07-31 15:55:00', 1339550467939639299, 1);

-- ----------------------------
-- Table structure for erp_inventory_alert_config
-- ----------------------------
DROP TABLE IF EXISTS `erp_inventory_alert_config`;
CREATE TABLE `erp_inventory_alert_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `config_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'STRING' COMMENT '配置类型：STRING(字符串)、NUMBER(数字)、BOOLEAN(布尔)、JSON(JSON对象)',
  `is_system` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '是否系统配置（Y-系统，N-用户）',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key_tenant`(`config_key`, `tenant_id`) USING BTREE,
  INDEX `idx_config_type`(`config_type`) USING BTREE,
  INDEX `idx_is_system`(`is_system`) USING BTREE,
  INDEX `idx_erp_inventory_alert_config_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存预警配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_inventory_alert_config
-- ----------------------------
INSERT INTO `erp_inventory_alert_config` VALUES (1, 'alert.email.enabled', 'true', '是否启用邮件通知', 'BOOLEAN', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);
INSERT INTO `erp_inventory_alert_config` VALUES (2, 'alert.email.smtp.host', 'smtp.163.com', '邮件SMTP服务器地址', 'STRING', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);
INSERT INTO `erp_inventory_alert_config` VALUES (3, 'alert.email.smtp.port', '587', '邮件SMTP端口', 'NUMBER', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);
INSERT INTO `erp_inventory_alert_config` VALUES (4, 'alert.sms.enabled', 'false', '是否启用短信通知', 'BOOLEAN', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);
INSERT INTO `erp_inventory_alert_config` VALUES (5, 'alert.wechat.enabled', 'false', '是否启用微信通知', 'BOOLEAN', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);
INSERT INTO `erp_inventory_alert_config` VALUES (6, 'alert.auto.resolve.days', '7', '预警自动解决天数', 'NUMBER', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);
INSERT INTO `erp_inventory_alert_config` VALUES (7, 'alert.batch.size', '100', '批量处理预警数量', 'NUMBER', 'Y', '2025-07-30 19:00:00', 1, '2025-07-30 19:00:00', 1, 1);

-- ----------------------------
-- Table structure for erp_inventory_alert_record
-- ----------------------------
DROP TABLE IF EXISTS `erp_inventory_alert_record`;
CREATE TABLE `erp_inventory_alert_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '预警记录ID',
  `rule_id` bigint(20) NOT NULL COMMENT '预警规则ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `alert_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)',
  `alert_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)',
  `current_stock` decimal(10, 3) NOT NULL COMMENT '当前库存数量',
  `threshold_value` decimal(10, 3) NOT NULL COMMENT '触发阈值',
  `alert_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警消息',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING' COMMENT '处理状态：PENDING(待处理)、PROCESSING(处理中)、RESOLVED(已解决)、IGNORED(已忽略)',
  `suggested_action` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '建议操作',
  `suggested_quantity` decimal(10, 3) NULL DEFAULT NULL COMMENT '建议补货数量',
  `suggested_supplier_id` bigint(20) NULL DEFAULT NULL COMMENT '建议供应商ID',
  `notification_sent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '是否已发送通知（Y-已发送，N-未发送）',
  `notification_methods` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '已发送的通知方式',
  `notification_time` datetime(0) NULL DEFAULT NULL COMMENT '通知发送时间',
  `handler_user` bigint(20) NULL DEFAULT NULL COMMENT '处理人ID',
  `handle_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
  `handle_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '处理备注',
  `alert_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '预警时间',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_rule_id`(`rule_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_alert_type`(`alert_type`) USING BTREE,
  INDEX `idx_alert_level`(`alert_level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_alert_time`(`alert_time`) USING BTREE,
  INDEX `idx_product_status`(`product_id`, `status`) USING BTREE,
  INDEX `idx_alert_level_status`(`alert_level`, `status`, `alert_time`) USING BTREE,
  INDEX `idx_erp_inventory_alert_record_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `fk_alert_record_supplier`(`suggested_supplier_id`) USING BTREE,
  CONSTRAINT `fk_alert_record_product` FOREIGN KEY (`product_id`) REFERENCES `erp_product` (`product_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_alert_record_rule` FOREIGN KEY (`rule_id`) REFERENCES `erp_inventory_alert_rule` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_alert_record_supplier` FOREIGN KEY (`suggested_supplier_id`) REFERENCES `erp_supplier` (`supplier_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存预警记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for erp_inventory_alert_rule
-- ----------------------------
DROP TABLE IF EXISTS `erp_inventory_alert_rule`;
CREATE TABLE `erp_inventory_alert_rule`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '预警规则ID',
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名称',
  `rule_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警类型：LOW_STOCK(库存不足)、EXPIRY(临期预警)、OVERSTOCK(库存积压)、ZERO_STOCK(零库存)',
  `target_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRODUCT' COMMENT '目标类型：PRODUCT(单个商品)、CATEGORY(商品分类)、ALL(全部商品)',
  `target_id` bigint(20) NULL DEFAULT NULL COMMENT '目标ID（商品ID或分类ID，ALL类型时为NULL）',
  `alert_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'WARNING' COMMENT '预警级别：CRITICAL(紧急)、WARNING(警告)、INFO(提醒)',
  `threshold_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '阈值类型：QUANTITY(数量)、PERCENTAGE(百分比)、DAYS(天数)',
  `threshold_value` decimal(10, 3) NOT NULL COMMENT '阈值（根据阈值类型确定含义）',
  `comparison_operator` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'LTE' COMMENT '比较操作符：LTE(小于等于)、LT(小于)、GTE(大于等于)、GT(大于)、EQ(等于)',
  `is_enabled` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y' COMMENT '是否启用（Y-启用，N-停用）',
  `notification_methods` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'SYSTEM' COMMENT '通知方式：SYSTEM(系统通知)、EMAIL(邮件)、SMS(短信)、WECHAT(微信)，多个用逗号分隔',
  `notification_users` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '通知用户ID列表，用逗号分隔',
  `check_frequency` int(11) NOT NULL DEFAULT 60 COMMENT '检查频率（分钟）',
  `last_check_time` datetime(0) NULL DEFAULT NULL COMMENT '最后检查时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_rule_type`(`rule_type`) USING BTREE,
  INDEX `idx_target_type_id`(`target_type`, `target_id`) USING BTREE,
  INDEX `idx_alert_level`(`alert_level`) USING BTREE,
  INDEX `idx_is_enabled`(`is_enabled`) USING BTREE,
  INDEX `idx_check_frequency`(`check_frequency`, `last_check_time`) USING BTREE,
  INDEX `idx_erp_inventory_alert_rule_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存预警规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_inventory_alert_rule
-- ----------------------------
INSERT INTO `erp_inventory_alert_rule` VALUES (1, '低库存预警-全部商品', 'LOW_STOCK', 'ALL', NULL, 'WARNING', 'QUANTITY', 10.000, 'LTE', 'N', 'SYSTEM,EMAIL', '1,1339550467939639299', 30, '2025-07-31 09:32:00', '当商品库存低于10个时发出预警', '2025-07-30 19:00:00', 1, '2025-07-31 10:08:58', 1339550467939639299, 'N', 1);
INSERT INTO `erp_inventory_alert_rule` VALUES (2, '零库存紧急预警', 'ZERO_STOCK', 'ALL', NULL, 'CRITICAL', 'QUANTITY', 0.000, 'EQ', 'N', 'SYSTEM,EMAIL,SMS', '1,1339550467939639299', 15, '2025-07-31 09:32:00', '商品零库存时立即发出紧急预警', '2025-07-30 19:00:00', 1, '2025-07-31 10:08:58', 1339550467939639299, 'N', 1);
INSERT INTO `erp_inventory_alert_rule` VALUES (3, '食品临期预警', 'EXPIRY', 'CATEGORY', 100, 'WARNING', 'DAYS', 30.000, 'LTE', 'N', 'SYSTEM,EMAIL', '1,1339550467939639299', 60, '2025-07-31 09:32:00', '食品类商品距离过期30天内发出预警', '2025-07-30 19:00:00', 1, '2025-07-31 10:08:59', 1339550467939639299, 'N', 1);
INSERT INTO `erp_inventory_alert_rule` VALUES (4, '库存积压预警', 'OVERSTOCK', 'ALL', NULL, 'INFO', 'PERCENTAGE', 150.000, 'GTE', 'N', 'SYSTEM', '1,1339550467939639299', 120, '2025-07-31 09:32:00', '库存超过最大库存150%时发出积压预警', '2025-07-30 19:00:00', 1, '2025-07-31 10:08:59', 1339550467939639299, 'N', 1);

-- ----------------------------
-- Table structure for erp_inventory_history
-- ----------------------------
DROP TABLE IF EXISTS `erp_inventory_history`;
CREATE TABLE `erp_inventory_history`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：IN(入库)、OUT(出库)、ADJUST(调整)、INIT(初始化)',
  `quantity_change` decimal(10, 3) NOT NULL COMMENT '数量变化（正数为增加，负数为减少）',
  `stock_before` decimal(10, 3) NOT NULL COMMENT '操作前库存',
  `stock_after` decimal(10, 3) NOT NULL COMMENT '操作后库存',
  `unit_cost` decimal(10, 2) NULL DEFAULT NULL COMMENT '单位成本',
  `reference_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联单据类型：PURCHASE(采购入库)、SALE(销售出库)、ADJUST(库存调整)',
  `reference_id` bigint(20) NULL DEFAULT NULL COMMENT '关联单据ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `operation_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operation_user` bigint(20) NULL DEFAULT NULL COMMENT '操作人ID',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_operation_time`(`operation_time`) USING BTREE,
  INDEX `idx_reference`(`reference_type`, `reference_id`) USING BTREE,
  INDEX `idx_product_operation`(`product_id`, `operation_type`, `operation_time`) USING BTREE,
  INDEX `idx_erp_inventory_history_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_inventory_history_product_time`(`product_id`, `operation_time`) USING BTREE,
  INDEX `idx_inventory_history_operation_time`(`operation_time`) USING BTREE,
  INDEX `idx_inventory_history_reference`(`reference_type`, `reference_id`) USING BTREE,
  INDEX `idx_inventory_history_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_inventory_history_product_type_time`(`product_id`, `operation_type`, `operation_time`) USING BTREE,
  CONSTRAINT `fk_inventory_history_product` FOREIGN KEY (`product_id`) REFERENCES `erp_product` (`product_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1951100239104569347 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_inventory_history
-- ----------------------------
INSERT INTO `erp_inventory_history` VALUES (1, 3001, 'INIT', 100.000, 0.000, 100.000, 8.50, NULL, NULL, '初始化库存', '2025-07-27 23:19:03', 1, NULL);
INSERT INTO `erp_inventory_history` VALUES (2, 3002, 'INIT', 50.000, 0.000, 50.000, 20.00, NULL, NULL, '初始化库存', '2025-07-27 23:19:03', 1, NULL);
INSERT INTO `erp_inventory_history` VALUES (3, 3004, 'INIT', 80.000, 0.000, 80.000, 12.00, NULL, NULL, '初始化库存', '2025-07-27 23:19:03', 1, NULL);
INSERT INTO `erp_inventory_history` VALUES (4, 3005, 'INIT', 200.000, 0.000, 200.000, 2.80, NULL, NULL, '初始化库存', '2025-07-27 23:19:03', 1, NULL);
INSERT INTO `erp_inventory_history` VALUES (5, 3006, 'INIT', 30.000, 0.000, 30.000, 10.50, NULL, NULL, '初始化库存', '2025-07-27 23:19:03', 1, NULL);
INSERT INTO `erp_inventory_history` VALUES (1950761020175683586, 3001, 'IN', 100.000, 100.000, 200.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:45', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761020695777281, 3002, 'IN', 50.000, 50.000, 100.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:45', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761021144567810, 3005, 'IN', 200.000, 200.000, 400.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:45', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761021987622913, 1950457803923525633, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:46', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761022885203969, 1950162198944841729, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:46', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761023673733121, 3009, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:46', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761024529371138, 3010, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:46', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761025380814850, 3011, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:47', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761026165149697, 3012, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:47', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761027012399105, 3013, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:47', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761027876425729, 3014, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:47', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761028681732097, 3015, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:47', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761029516398593, 3016, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:47', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761030351065090, 3017, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:48', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761031152177154, 3018, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:48', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761031995232258, 3019, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:48', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950761032813121538, 3020, 'IN', 1.000, 0.000, 1.000, NULL, 'PURCHASE_ORDER', 1, '采购入库', '2025-07-31 11:30:48', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950826299480178690, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950826574693629953, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827730354405377, 1950457803923525633, 'IN', 11.000, 1.000, 12.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:50', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827730761252866, 1950162198944841729, 'IN', 11.000, 1.000, 12.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:50', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827731256180737, 3001, 'IN', 11.000, 200.000, 211.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:50', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827731717554177, 3002, 'IN', 1.000, 100.000, 101.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827732124401666, 3005, 'IN', 11.000, 400.000, 411.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827732585775105, 3009, 'IN', 1.000, 1.000, 2.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827733030371330, 3010, 'IN', 1.000, 1.000, 2.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827733491744769, 3011, 'IN', 1.000, 1.000, 2.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827733906980865, 3012, 'IN', 1.000, 1.000, 2.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188481, 3013, 'IN', 1.000, 1.000, 2.000, NULL, 'PURCHASE_ORDER', 1950827091939393537, '采购入库', '2025-07-31 15:55:51', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188482, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188483, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188484, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188485, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188486, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188487, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188488, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188489, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188490, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188491, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188492, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188493, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188494, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188495, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188496, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188497, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188498, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188499, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188500, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188501, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188502, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188503, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188504, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188505, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188506, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188507, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188508, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188509, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188510, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188511, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188512, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188513, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188514, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188515, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188516, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188517, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188518, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188519, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188520, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188521, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188522, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188523, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188524, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188525, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188526, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188527, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188528, 1950457803923525633, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=2，最大库存=10', '2025-07-31 15:50:09', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1950827734343188529, 1950162198944841729, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=1，最大库存=11', '2025-07-31 15:51:15', 1339550467939639299, 1);
INSERT INTO `erp_inventory_history` VALUES (1951100045138980865, 3017, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=0', '2025-08-01 09:57:55', NULL, 1);
INSERT INTO `erp_inventory_history` VALUES (1951100239104569346, 3004, 'SET_ALERT', 0.000, 0.000, 0.000, NULL, 'MANUAL', NULL, '设置库存预警值：最小库存=80', '2025-08-01 09:58:41', NULL, 1);

-- ----------------------------
-- Table structure for erp_product
-- ----------------------------
DROP TABLE IF EXISTS `erp_product`;
CREATE TABLE `erp_product`  (
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品编码',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `product_short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品简称',
  `barcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '条形码',
  `category_id` bigint(20) NOT NULL COMMENT '商品分类ID',
  `supplier_id` bigint(20) NULL DEFAULT NULL COMMENT '供应商ID',
  `pricing_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NORMAL' COMMENT '计价类型：NORMAL(普通)、WEIGHT(计重)、PIECE(计件)、VARIABLE(不定价)',
  `retail_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '零售价格（普通商品用）',
  `unit_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '单位价格（计重商品用，每公斤价格）',
  `piece_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '单份价格（计件商品用）',
  `reference_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '参考价格（不定价商品用）',
  `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌',
  `specification` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '个' COMMENT '基本单位',
  `weight` decimal(10, 3) NULL DEFAULT NULL COMMENT '重量（kg）',
  `volume` decimal(10, 3) NULL DEFAULT NULL COMMENT '体积（立方米）',
  `shelf_life` int(11) NULL DEFAULT NULL COMMENT '保质期（天）',
  `storage_condition` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '存储条件',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE' COMMENT '状态（ACTIVE-正常，INACTIVE-停用，DISCONTINUED-停产）',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`product_id`) USING BTREE,
  UNIQUE INDEX `uk_product_code`(`product_code`) USING BTREE,
  UNIQUE INDEX `uk_barcode`(`barcode`) USING BTREE,
  INDEX `idx_product_name`(`product_name`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_brand`(`brand`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_product_category_status`(`category_id`, `status`, `del_flag`) USING BTREE,
  INDEX `idx_erp_product_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_supplier_id`(`supplier_id`) USING BTREE,
  INDEX `idx_pricing_type`(`pricing_type`) USING BTREE,
  INDEX `idx_supplier_pricing`(`supplier_id`, `pricing_type`, `del_flag`) USING BTREE,
  INDEX `idx_shelf_life_status`(`shelf_life`, `status`, `del_flag`) USING BTREE COMMENT '保质期状态索引',
  INDEX `idx_erp_product_supplier`(`supplier_id`) USING BTREE,
  INDEX `idx_erp_product_code`(`product_code`) USING BTREE,
  INDEX `idx_erp_product_name`(`product_name`) USING BTREE,
  CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `erp_product_category` (`category_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_product_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `erp_supplier` (`supplier_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品主档案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_product
-- ----------------------------
INSERT INTO `erp_product` VALUES (3001, 'PRD001', '奥利奥原味夹心饼干', '奥利奥饼干', '6901668001234', 300, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '奥利奥', '116g/包', '包', 0.116, 0.001, 365, '常温干燥保存', 'ACTIVE', '经典原味夹心饼干', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1339550467939639299, 'N', 1);
INSERT INTO `erp_product` VALUES (3002, 'PRD002', '康师傅红烧牛肉面', '康师傅牛肉面', '6901668002345', 300, 1001, 'WEIGHT', NULL, 25.00, NULL, NULL, '康师傅', '105g/桶', '桶', 0.105, 0.002, 180, '常温保存', 'ACTIVE', '经典红烧牛肉味方便面', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1339550467939639299, 'N', 1);
INSERT INTO `erp_product` VALUES (3003, 'PRD003', '德芙丝滑牛奶巧克力', '德芙巧克力', '6901668003456', 301, 1002, 'PIECE', NULL, NULL, 8.50, NULL, '德芙', '43g/块', '块', 0.043, 0.001, 365, '阴凉干燥处保存', 'ACTIVE', '丝滑香浓牛奶巧克力', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1339550467939639299, 'N', 1);
INSERT INTO `erp_product` VALUES (3004, 'PRD004', '旺仔牛奶', '旺仔牛奶', '6901668004567', 302, 1003, 'VARIABLE', NULL, NULL, NULL, 15.00, '旺旺', '125ml/罐', '罐', 0.145, 0.000, 365, '常温保存', 'ACTIVE', '经典儿童牛奶饮品', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3005, 'PRD005', '农夫山泉天然水', '农夫山泉', '6901668005678', 302, 1001, 'NORMAL', 3.50, NULL, NULL, NULL, '农夫山泉', '550ml/瓶', '瓶', 0.550, 0.001, 730, '常温保存', 'ACTIVE', '天然弱碱性水', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3006, 'PRD006', '立顿红茶包', '立顿红茶', '6901668006789', 303, 1004, 'NORMAL', 12.80, NULL, NULL, NULL, '立顿', '2g×25包/盒', '盒', 0.050, 0.001, 1095, '干燥通风处保存', 'ACTIVE', '经典英式红茶', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3007, 'PRD007', 'iPhone 15 Pro Max', 'iPhone 15 Pro', '1234567890123', 304, 1002, 'NORMAL', 8999.00, NULL, NULL, NULL, '苹果', '256GB 深空黑色', '台', 0.221, 0.000, NULL, '常温干燥保存', 'ACTIVE', '苹果最新旗舰手机', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3008, 'PRD008', '华为Mate 60 Pro', '华为Mate60', '2345678901234', 304, 1002, 'NORMAL', 6999.00, NULL, NULL, NULL, '华为', '512GB 雅川青', '台', 0.225, 0.000, NULL, '常温干燥保存', 'ACTIVE', '华为旗舰手机', '2025-07-21 10:43:01', 1, '2025-07-30 09:38:23', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3009, 'PRD009', '小米14 Ultra', '小米14 Ultra', '3456789012345', 304, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '小米', '512GB 钛金属黑', '台', 0.224, 0.000, NULL, '常温干燥保存', 'ACTIVE', '小米影像旗舰', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:06', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3010, 'PRD010', 'MacBook Pro 16英寸', 'MacBook Pro 16', '4567890123456', 305, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '苹果', 'M3 Max 36GB 1TB', '台', 2.140, 0.002, NULL, '常温干燥保存', 'ACTIVE', '苹果专业笔记本', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:06', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3011, 'PRD011', '联想ThinkPad X1 Carbon', 'ThinkPad X1', '5678901234567', 305, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '联想', '14英寸 i7 16GB 512GB', '台', 1.120, 0.002, NULL, '常温干燥保存', 'ACTIVE', '商务轻薄笔记本', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:06', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3012, 'PRD012', '戴尔XPS 13', '戴尔XPS 13', '6789012345678', 305, 1001, 'NORMAL', 22.00, NULL, NULL, NULL, '戴尔', '13.4英寸 i5 16GB 512GB', '台', 1.290, 0.002, NULL, '常温干燥保存', 'ACTIVE', '超轻薄笔记本', '2025-07-21 10:43:01', 1, '2025-07-30 09:39:47', 1339550467939639299, 'N', 1);
INSERT INTO `erp_product` VALUES (3013, 'PRD013', '测试停产商品', '测试停产', '7890123456789', 300, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '测试品牌', '测试规格', '个', 0.100, 0.001, 30, '测试存储', 'DISCONTINUED', '测试用停产商品', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3014, 'PRD014', '测试停用商品', '测试停用', '8901234567890', 301, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '测试品牌', '测试规格', '个', 0.200, 0.002, 60, '测试存储', 'INACTIVE', '测试用停用商品', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3015, 'PRD015', '手工制作陶瓷茶具套装', '手工陶瓷茶具', NULL, 103, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '景德镇', '一壶四杯', '套', 1.500, 0.005, NULL, '防震包装', 'ACTIVE', '手工制作陶瓷茶具', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3016, 'PRD016', '定制实木书桌', '实木书桌', NULL, 103, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '自制', '120cm×60cm×75cm', '张', 25.000, 0.540, NULL, '防潮防虫', 'ACTIVE', '定制实木办公书桌', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3017, 'PRD017', 'A4复印纸', 'A4纸', '9012345678901', 103, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '得力', '70g 500张/包', '包', 2.500, 0.003, NULL, '防潮保存', 'ACTIVE', '办公用复印纸', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3018, 'PRD018', '圆珠笔', '圆珠笔', '0123456789012', 103, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '晨光', '0.7mm 蓝色', '支', 0.010, 0.000, NULL, '常温保存', 'ACTIVE', '办公用圆珠笔', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3019, 'PRD019', '双开门冰箱', '双开门冰箱', '1357924680135', 103, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '海尔', '452L 风冷无霜', '台', 85.000, 1.200, NULL, '直立运输', 'ACTIVE', '大容量双开门冰箱', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (3020, 'PRD020', '三人沙发', '三人沙发', '2468013579246', 103, 1001, 'NORMAL', 10.00, NULL, NULL, NULL, '顾家家居', '2.1m×0.9m×0.8m 真皮', '张', 45.000, 1.512, NULL, '防潮防晒', 'ACTIVE', '真皮三人沙发', '2025-07-21 10:43:01', 1, '2025-07-31 10:36:10', 1, 'N', 1);
INSERT INTO `erp_product` VALUES (1950162198944841729, 'aa', 'aaa', NULL, NULL, 200, 1001, 'NORMAL', 11.00, NULL, NULL, NULL, NULL, NULL, '个', NULL, NULL, NULL, NULL, 'ACTIVE', NULL, '2025-07-29 19:51:15', 1339550467939639299, '2025-07-30 09:38:23', NULL, 'N', 1);
INSERT INTO `erp_product` VALUES (1950457803923525633, 'bbb', 'bbb', NULL, NULL, 1948770530597306369, 1001, 'NORMAL', 100.00, NULL, NULL, NULL, NULL, NULL, '个', NULL, NULL, NULL, NULL, 'ACTIVE', NULL, '2025-07-30 15:25:53', 1339550467939639299, '2025-07-30 15:25:52', NULL, 'N', 1);

-- ----------------------------
-- Table structure for erp_product_category
-- ----------------------------
DROP TABLE IF EXISTS `erp_product_category`;
CREATE TABLE `erp_product_category`  (
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类编码',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父级分类ID',
  `category_level` int(11) NOT NULL DEFAULT 1 COMMENT '分类层级（1-大类，2-中类，3-小类）',
  `category_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类路径（用/分隔，如：1/2/3）',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序号',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y' COMMENT '状态（Y-启用，N-停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`category_id`) USING BTREE,
  UNIQUE INDEX `uk_category_code`(`category_code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_category_level`(`category_level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_erp_product_category_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_product_category
-- ----------------------------
INSERT INTO `erp_product_category` VALUES (100, '11', '食物', 0, 1, '100', 0, 'Y', '', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1339550467939639299, 'N', 1);
INSERT INTO `erp_product_category` VALUES (101, 'ELECTRONICS', '电子产品', 0, 1, '101', 2, 'Y', '电子产品大类', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (102, 'CLOTHING', '服装鞋帽', 0, 1, '102', 3, 'Y', '服装鞋帽大类', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (103, 'HOME', '家居用品', 0, 1, '103', 4, 'Y', '家居用品大类', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (200, 'FOOD-SNACK', '休闲食品', 100, 2, '100/200', 1, 'Y', '休闲零食', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (201, 'FOOD-DRINK', '饮料', 100, 2, '100/201', 2, 'Y', '各类饮料', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (202, 'ELECTRONICS-PHONE', '手机通讯', 101, 2, '101/202', 1, 'Y', '手机及配件', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (203, 'ELECTRONICS-COMPUTER', '电脑数码', 101, 2, '101/203', 2, 'Y', '电脑及数码产品', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (204, 'CLOTHING-MEN', '男装', 102, 2, '102/204', 1, 'Y', '男士服装', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (205, 'CLOTHING-WOMEN', '女装', 102, 2, '102/205', 2, 'Y', '女士服装', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (300, 'FOOD-SNACK-BISCUIT', '饼干糕点', 200, 3, '100/200/300', 1, 'Y', '饼干糕点类', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (301, 'FOOD-SNACK-CANDY', '糖果巧克力', 200, 3, '100/200/301', 2, 'Y', '糖果巧克力类', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (302, 'FOOD-DRINK-JUICE', '果汁饮料', 201, 3, '100/201/302', 1, 'Y', '果汁类饮料', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (303, 'FOOD-DRINK-TEA', '茶饮', 201, 3, '100/201/303', 2, 'Y', '茶类饮品', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (304, 'ELECTRONICS-PHONE-SMARTPHONE', '智能手机', 202, 3, '101/202/304', 1, 'Y', '智能手机', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (305, 'ELECTRONICS-COMPUTER-LAPTOP', '笔记本电脑', 203, 3, '101/203/305', 1, 'Y', '笔记本电脑', '2025-07-21 10:43:01', 1, '2025-07-25 23:41:34', 1, 'N', 1);
INSERT INTO `erp_product_category` VALUES (1947465432428466177, '1ew', '1111', 0, 1, '1947465432428466177', 0, 'Y', '', '2025-07-22 09:15:16', 1339550467939639299, '2025-07-25 23:41:34', 1339550467939639299, 'Y', 1);
INSERT INTO `erp_product_category` VALUES (1947470097861857281, '123', '12', 100, 2, '100/1947470097861857281', 0, 'Y', '', '2025-07-22 09:33:48', 1339550467939639299, '2025-07-25 23:41:34', 1339550467939639299, 'Y', 1);
INSERT INTO `erp_product_category` VALUES (1947473543662948353, 'aaa', 'aaaaaa', 305, 4, '101/203/305/1947473543662948353', 0, 'Y', '', '2025-07-22 09:47:30', 1339550467939639299, '2025-07-29 20:00:23', 1339550467939639299, 'Y', 1);
INSERT INTO `erp_product_category` VALUES (1948770530597306369, 'Basd', '啊啊啊', 0, 1, '1948770530597306369', 0, 'Y', '', '2025-07-25 23:41:16', 1339550467939639299, '2025-07-25 23:41:16', 1339550467939639299, 'N', 1);

-- ----------------------------
-- Table structure for erp_product_category_relation
-- ----------------------------
DROP TABLE IF EXISTS `erp_product_category_relation`;
CREATE TABLE `erp_product_category_relation`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_erp_product_category_relation`(`product_id`, `category_id`) USING BTREE COMMENT '商品-分类唯一索引',
  INDEX `idx_erp_product_category_relation_category_id`(`category_id`) USING BTREE COMMENT '分类ID索引',
  INDEX `idx_erp_product_category_relation_product_id`(`product_id`) USING BTREE COMMENT '商品ID索引',
  CONSTRAINT `fk_product_category_relation_category` FOREIGN KEY (`category_id`) REFERENCES `erp_product_category` (`category_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_product_category_relation_product` FOREIGN KEY (`product_id`) REFERENCES `erp_product` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品-产品分类关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_product_category_relation
-- ----------------------------
INSERT INTO `erp_product_category_relation` VALUES (1948037344602939394, 3001, 102, '2025-07-23 23:07:50', 1339550467939639299);
INSERT INTO `erp_product_category_relation` VALUES (1948037344602939395, 3001, 202, '2025-07-23 23:07:50', 1339550467939639299);
INSERT INTO `erp_product_category_relation` VALUES (1948037432314224641, 3002, 100, '2025-07-23 23:08:11', 1339550467939639299);
INSERT INTO `erp_product_category_relation` VALUES (1948037432314224642, 3002, 103, '2025-07-23 23:08:11', 1339550467939639299);
INSERT INTO `erp_product_category_relation` VALUES (1948037432314224643, 3002, 203, '2025-07-23 23:08:11', 1339550467939639299);
INSERT INTO `erp_product_category_relation` VALUES (1948037462542573570, 3003, 204, '2025-07-23 23:08:19', 1339550467939639299);

-- ----------------------------
-- Table structure for erp_purchase_order
-- ----------------------------
DROP TABLE IF EXISTS `erp_purchase_order`;
CREATE TABLE `erp_purchase_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '入库单ID',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '入库单号',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'DRAFT' COMMENT '状态：DRAFT(草稿)、CONFIRMED(已确认)、COMPLETED(已完成)、CANCELLED(已取消)',
  `total_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `payment_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '付款方式：CASH(现金)、BANK_TRANSFER(银行转账)、CREDIT(赊账)',
  `payment_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '付款账户',
  `order_date` date NOT NULL COMMENT '订单日期',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no`) USING BTREE,
  INDEX `idx_supplier_id`(`supplier_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_order_date`(`order_date`) USING BTREE,
  INDEX `idx_supplier_status`(`supplier_id`, `status`, `del_flag`) USING BTREE,
  INDEX `idx_erp_purchase_order_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_erp_purchase_order_no`(`order_no`) USING BTREE,
  CONSTRAINT `fk_purchase_order_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `erp_supplier` (`supplier_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1950827091939393538 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购入库单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_purchase_order
-- ----------------------------
INSERT INTO `erp_purchase_order` VALUES (1, 'PO20250727001', 1001, 'COMPLETED', 2653.00, 'CASH', '1', '2025-07-27', '1', '2025-07-27 23:19:03', 1, '2025-07-31 11:30:45', 1339550467939639299, 'N', NULL);
INSERT INTO `erp_purchase_order` VALUES (2, 'PO20250727002', 1003, 'COMPLETED', 1800.00, 'CASH', '111', '2025-07-27', '22', '2025-07-27 23:19:03', 1, '2025-07-31 09:46:18', NULL, 'N', NULL);
INSERT INTO `erp_purchase_order` VALUES (3, 'PO20250727003', 1004, 'COMPLETED', 960.00, 'CREDIT', NULL, '2025-07-26', '测试采购入库单3', '2025-07-27 23:19:03', 1, '2025-07-27 23:19:03', NULL, 'N', NULL);
INSERT INTO `erp_purchase_order` VALUES (1950443447382970369, 'PO202507300013', 1001, 'CONFIRMED', 110.00, 'CASH', NULL, '2025-07-30', '', '2025-07-30 14:28:50', 1339550467939639299, '2025-07-31 10:39:54', NULL, 'N', 1);
INSERT INTO `erp_purchase_order` VALUES (1950467759791935490, 'PO202507300032', 1001, 'COMPLETED', 848.00, 'CASH', '现金1', '2025-07-30', '111', '2025-07-30 16:05:27', 1339550467939639299, '2025-07-31 10:37:09', 1339550467939639299, 'N', 1);
INSERT INTO `erp_purchase_order` VALUES (1950761254817632257, 'PO202507310003', 1001, 'CONFIRMED', 296.50, 'CASH', NULL, '2025-07-31', '12', '2025-07-31 11:31:41', 1339550467939639299, '2025-07-31 11:36:37', 1339550467939639299, 'N', 1);
INSERT INTO `erp_purchase_order` VALUES (1950802364369850370, 'PO202507310005', 1002, 'DRAFT', 16008.00, 'CASH', NULL, '2025-07-31', '', '2025-07-31 14:15:02', 1339550467939639299, '2025-07-31 14:15:02', NULL, 'N', 1);
INSERT INTO `erp_purchase_order` VALUES (1950827091939393537, 'PO202507310008', 1001, 'COMPLETED', 1441.50, 'CASH', '11', '2025-07-31', '11', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:55:50', NULL, 'N', 1);

-- ----------------------------
-- Table structure for erp_purchase_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `erp_purchase_order_detail`;
CREATE TABLE `erp_purchase_order_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` bigint(20) NOT NULL COMMENT '入库单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `quantity` decimal(10, 3) NOT NULL COMMENT '数量',
  `unit_price` decimal(10, 2) NOT NULL COMMENT '单价',
  `total_price` decimal(12, 2) NOT NULL COMMENT '总价',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_order_product`(`order_id`, `product_id`) USING BTREE,
  INDEX `idx_erp_purchase_order_detail_tenant_id`(`tenant_id`) USING BTREE,
  CONSTRAINT `fk_purchase_detail_order` FOREIGN KEY (`order_id`) REFERENCES `erp_purchase_order` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_purchase_detail_product` FOREIGN KEY (`product_id`) REFERENCES `erp_product` (`product_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1950827092161691650 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购入库单明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_purchase_order_detail
-- ----------------------------
INSERT INTO `erp_purchase_order_detail` VALUES (1, 1, 3001, 100.000, 8.50, 850.00, NULL, '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (2, 1, 3002, 50.000, 20.00, 1000.00, NULL, '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (3, 1, 3005, 200.000, 2.80, 560.00, NULL, '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (4, 2, 3004, 80.000, 12.00, 960.00, NULL, '2025-07-27 23:19:03', 1, '2025-07-30 14:23:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (5, 2, 3006, 30.000, 10.50, 315.00, NULL, '2025-07-27 23:19:03', 1, '2025-07-30 14:23:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (6, 3, 3006, 80.000, 12.00, 960.00, NULL, '2025-07-27 23:19:03', 1, '2025-07-30 14:23:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950443447445884930, 1950443447382970369, 3001, 10.000, 10.00, 100.00, '', '2025-07-30 14:28:50', 1339550467939639299, '2025-07-30 14:28:50', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950443447445884931, 1950443447382970369, 3002, 1.000, 10.00, 10.00, '', '2025-07-30 14:28:50', 1339550467939639299, '2025-07-30 14:28:50', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950467759854850049, 1950467759791935490, 3001, 11.000, 10.00, 110.00, '11', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950467759854850050, 1950467759791935490, 3002, 11.000, 10.00, 110.00, '1222', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950467759921958913, 1950467759791935490, 3005, 110.000, 3.50, 385.00, '', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747351895359489, 1950467759791935490, 1950457803923525633, 1.000, 100.00, 100.00, '', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747351920525314, 1950467759791935490, 1950162198944841729, 1.000, 11.00, 11.00, '', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747351933108225, 1950467759791935490, 3009, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747351949885441, 1950467759791935490, 3010, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747351966662658, 1950467759791935490, 3011, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:26', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747351983439873, 1950467759791935490, 3012, 1.000, 22.00, 22.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352000217089, 1950467759791935490, 3013, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352012800002, 1950467759791935490, 3014, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352029577218, 1950467759791935490, 3015, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352046354433, 1950467759791935490, 3016, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352063131650, 1950467759791935490, 3017, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352079908866, 1950467759791935490, 3018, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352096686082, 1950467759791935490, 3019, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950747352109268993, 1950467759791935490, 3020, 1.000, 10.00, 10.00, '', '2025-07-31 10:36:27', 1339550467939639299, '2025-07-31 10:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594295136258, 1, 1950457803923525633, 1.000, 100.00, 100.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594366439425, 1, 1950162198944841729, 1.000, 11.00, 11.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594366439426, 1, 3009, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594366439427, 1, 3010, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594425159682, 1, 3011, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594425159683, 1, 3012, 1.000, 22.00, 22.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594425159684, 1, 3013, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594425159685, 1, 3014, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594492268545, 1, 3015, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594492268546, 1, 3016, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594492268547, 1, 3017, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594492268548, 1, 3018, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594555183105, 1, 3019, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950758594555183106, 1, 3020, 1.000, 10.00, 10.00, '', '2025-07-31 11:21:07', 1339550467939639299, '2025-07-31 11:21:06', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254855380993, 1950761254817632257, 1950457803923525633, 1.000, 100.00, 100.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254867963905, 1950761254817632257, 1950162198944841729, 1.000, 11.00, 11.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254884741122, 1950761254817632257, 3001, 1.000, 10.00, 10.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254901518337, 1950761254817632257, 3002, 11.000, 10.00, 110.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254914101249, 1950761254817632257, 3005, 1.000, 3.50, 3.50, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254926684162, 1950761254817632257, 3009, 1.000, 10.00, 10.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254939267074, 1950761254817632257, 3010, 1.000, 10.00, 10.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254956044290, 1950761254817632257, 3011, 1.000, 10.00, 10.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254968627202, 1950761254817632257, 3012, 1.000, 22.00, 22.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950761254985404418, 1950761254817632257, 3013, 1.000, 10.00, 10.00, '', '2025-07-31 11:36:27', 1339550467939639299, '2025-07-31 11:36:26', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950802364424376322, 1950802364369850370, 3003, 1.000, 10.00, 10.00, '', '2025-07-31 14:15:02', 1339550467939639299, '2025-07-31 14:15:02', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950802364449542145, 1950802364369850370, 3007, 1.000, 8999.00, 8999.00, '', '2025-07-31 14:15:02', 1339550467939639299, '2025-07-31 14:15:02', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950802364462125057, 1950802364369850370, 3008, 1.000, 6999.00, 6999.00, '', '2025-07-31 14:15:03', 1339550467939639299, '2025-07-31 14:15:02', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092023279618, 1950827091939393537, 1950457803923525633, 11.000, 100.00, 1100.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092048445442, 1950827091939393537, 1950162198944841729, 11.000, 11.00, 121.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092061028354, 1950827091939393537, 3001, 11.000, 10.00, 110.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092077805570, 1950827091939393537, 3002, 1.000, 10.00, 10.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092094582785, 1950827091939393537, 3005, 11.000, 3.50, 38.50, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092107165697, 1950827091939393537, 3009, 1.000, 10.00, 10.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092123942913, 1950827091939393537, 3010, 1.000, 10.00, 10.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092136525826, 1950827091939393537, 3011, 1.000, 10.00, 10.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092153303042, 1950827091939393537, 3012, 1.000, 22.00, 22.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);
INSERT INTO `erp_purchase_order_detail` VALUES (1950827092161691649, 1950827091939393537, 3013, 1.000, 10.00, 10.00, '', '2025-07-31 15:53:18', 1339550467939639299, '2025-07-31 15:53:18', NULL, 1);

-- ----------------------------
-- Table structure for erp_region
-- ----------------------------
DROP TABLE IF EXISTS `erp_region`;
CREATE TABLE `erp_region`  (
  `region_id` bigint(20) NOT NULL COMMENT '区域ID',
  `region_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域编码',
  `region_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域名称',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父级区域ID',
  `region_level` int(11) NOT NULL DEFAULT 1 COMMENT '区域层级（1-国家，2-省，3-市，4-区县，5-商圈）',
  `region_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域路径（用/分隔，如：1/2/3）',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序号',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y' COMMENT '状态（Y-启用，N-停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`region_id`) USING BTREE,
  UNIQUE INDEX `uk_region_code`(`region_code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_region_level`(`region_level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_erp_region_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '区域管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_region
-- ----------------------------
INSERT INTO `erp_region` VALUES (100, 'CHN', '中国', NULL, 1, '100', 1, 'Y', '中华人民共和国', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (101, 'CHN-BJ', '北京市', 100, 2, '100/101', 1, 'Y', '首都直辖市', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1339550467939639299, 'N', 1);
INSERT INTO `erp_region` VALUES (102, 'CHN-SH', '上海市', 100, 2, '100/102', 2, 'Y', '经济中心直辖市', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (103, 'CHN-GD', '广东省', 100, 2, '100/103', 3, 'Y', '经济发达省份', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (104, 'CHN-ZJ', '浙江省', 100, 2, '100/104', 4, 'Y', '长三角核心省份', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (105, 'CHN-JS', '江苏省', 100, 2, '100/105', 5, 'Y', '制造业强省', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (106, 'CHN-SD', '山东省', 100, 2, '100/106', 6, 'Y', '人口大省', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (201, 'CHN-BJ-HD', '海淀区', 101, 3, '100/101/201', 1, 'Y', '科技创新中心', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (202, 'CHN-BJ-CY', '朝阳区', 101, 3, '100/101/202', 2, 'Y', '商务中心区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (203, 'CHN-SH-HP', '黄浦区', 102, 3, '100/102/203', 1, 'Y', '上海市中心', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (204, 'CHN-SH-PD', '浦东新区', 102, 3, '100/102/204', 2, 'Y', '金融贸易区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (205, 'CHN-GD-GZ', '广州市', 103, 3, '100/103/205', 1, 'Y', '省会城市', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (206, 'CHN-GD-SZ', '深圳市', 103, 3, '100/103/206', 2, 'Y', '特区城市', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (207, 'CHN-ZJ-HZ', '杭州市', 104, 3, '100/104/207', 1, 'Y', '电商之都', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (208, 'CHN-ZJ-NB', '宁波市', 104, 3, '100/104/208', 2, 'Y', '港口城市', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (301, 'CHN-BJ-HD-ZGC', '中关村', 201, 4, '100/101/201/301', 1, 'Y', '科技园区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (302, 'CHN-SH-PD-LJZ', '陆家嘴', 204, 4, '100/102/204/302', 1, 'Y', '金融中心', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (303, 'CHN-GD-SZ-NSD', '南山区', 206, 4, '100/103/206/303', 1, 'Y', '高新技术区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (304, 'CHN-ZJ-HZ-XH', '西湖区', 207, 4, '100/104/207/304', 1, 'Y', '风景名胜区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (401, 'CHN-BJ-HD-ZGC-SW', '中关村软件园', 301, 5, '100/101/201/301/401', 1, 'Y', '软件产业园', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (402, 'CHN-SH-PD-LJZ-JR', '陆家嘴金融城', 302, 5, '100/102/204/302/402', 1, 'Y', '金融商务区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (403, 'CHN-GD-SZ-NSD-KJ', '南山科技园', 303, 5, '100/103/206/303/403', 1, 'Y', '高科技园区', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (404, 'CHN-ZJ-HZ-XH-WC', '文创园区', 304, 5, '100/104/207/304/404', 1, 'Y', '文化创意产业园', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:37', 1, 'N', 1);
INSERT INTO `erp_region` VALUES (1947278275361439745, 'mg', '美国', 0, 1, '1947278275361439745', 0, 'Y', '', '2025-07-21 20:51:34', 1339550467939639299, '2025-07-25 23:42:37', 1339550467939639299, 'N', 1);
INSERT INTO `erp_region` VALUES (1947278468978900994, 'mgjz', '美国jz', 106, 3, '100/106/1947278468978900994', 0, 'Y', '11111', '2025-07-21 20:52:20', 1339550467939639299, '2025-07-25 23:42:37', 1339550467939639299, 'N', 1);
INSERT INTO `erp_region` VALUES (1948999425414213633, 'A', '区域A', 0, 1, '1948999425414213633', 0, 'Y', '', '2025-07-26 14:50:48', 1943145071222202370, '2025-07-26 14:50:48', 1943145071222202370, 'N', 1943145071222202369);
INSERT INTO `erp_region` VALUES (1948999468665876481, 'b', '区域B', 0, 1, '1948999468665876481', 0, 'Y', '', '2025-07-26 14:50:59', 1943145071222202370, '2025-07-26 14:50:59', 1943145071222202370, 'N', 1943145071222202369);

-- ----------------------------
-- Table structure for erp_sequence_generator
-- ----------------------------
DROP TABLE IF EXISTS `erp_sequence_generator`;
CREATE TABLE `erp_sequence_generator`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sequence_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '序列名称',
  `prefix` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '前缀',
  `current_value` bigint(20) NOT NULL DEFAULT 1 COMMENT '当前值',
  `step` int(11) NOT NULL DEFAULT 1 COMMENT '步长',
  `date_format` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '日期格式（如：yyyyMMdd）',
  `reset_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NONE' COMMENT '重置类型：NONE(不重置)、DAILY(每日)、MONTHLY(每月)、YEARLY(每年)',
  `last_reset_date` date NULL DEFAULT NULL COMMENT '最后重置日期',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_sequence_name`(`sequence_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '序列号生成器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_sequence_generator
-- ----------------------------
INSERT INTO `erp_sequence_generator` VALUES (1, 'PURCHASE_ORDER', 'PO', 8, 1, 'yyyyMMdd', 'DAILY', '2025-07-31', '2025-07-27 23:18:55', '2025-07-31 15:53:17');

-- ----------------------------
-- Table structure for erp_supplier
-- ----------------------------
DROP TABLE IF EXISTS `erp_supplier`;
CREATE TABLE `erp_supplier`  (
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `supplier_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '供应商编码',
  `supplier_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '供应商名称',
  `supplier_short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商简称',
  `supplier_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ENTERPRISE' COMMENT '供应商类型（ENTERPRISE-企业，INDIVIDUAL-个体）',
  `region_id` bigint(20) NULL DEFAULT NULL COMMENT '所属区域ID',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号码',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `contact_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系地址',
  `business_license_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `tax_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税务登记号',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '银行账号',
  `credit_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'C' COMMENT '信用等级（A-优秀，B-良好，C-一般，D-较差）',
  `business_mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PURCHASE_SALE' COMMENT '经营方式：PURCHASE_SALE(购销)、JOINT_VENTURE(联营)、CONSIGNMENT(代销)',
  `sales_deduction` decimal(5, 2) NULL DEFAULT NULL COMMENT '销售扣点（联营和代销使用，百分比）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE' COMMENT '状态（ACTIVE-正常，INACTIVE-停用，BLACKLIST-黑名单）',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`supplier_id`) USING BTREE,
  UNIQUE INDEX `uk_supplier_code`(`supplier_code`) USING BTREE,
  INDEX `idx_supplier_name`(`supplier_name`) USING BTREE,
  INDEX `idx_region_id`(`region_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_credit_level`(`credit_level`) USING BTREE,
  INDEX `idx_supplier_region_status`(`region_id`, `status`, `del_flag`) USING BTREE,
  INDEX `idx_erp_supplier_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_business_mode`(`business_mode`) USING BTREE,
  INDEX `idx_erp_supplier_code`(`supplier_code`) USING BTREE,
  INDEX `idx_erp_supplier_name`(`supplier_name`) USING BTREE,
  CONSTRAINT `fk_supplier_region` FOREIGN KEY (`region_id`) REFERENCES `erp_region` (`region_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '供应商主档案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_supplier
-- ----------------------------
INSERT INTO `erp_supplier` VALUES (1001, 'SUP001', '北京科技食品有限公司', '北京科技食品', 'ENTERPRISE', 201, '张经理', '010-88888888', '***********', '<EMAIL>', '北京市海淀区中关村大街1号', '91110108123456789X', '110108123456789', '中国银行北京分行', '1234567890123456789', 'A', 'PURCHASE_SALE', NULL, 'ACTIVE', '优质食品供应商', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1339550467939639299, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1002, 'SUP002', '上海浦东电子科技股份有限公司', '上海浦东电子', 'ENTERPRISE', 204, '李总监', '021-66666666', '13900139002', '<EMAIL>', '上海市浦东新区陆家嘴环路888号', '91310115987654321A', '310115987654321', '工商银行上海分行', '9876543210987654321', 'A', 'CONSIGNMENT', 15.00, 'ACTIVE', '电子产品制造商', '2025-07-21 10:43:01', 1, '2025-07-31 10:11:57', 1339550467939639299, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1003, 'SUP003', '深圳南山服装贸易有限公司', '深圳南山服装', 'ENTERPRISE', 303, '王主管', '0755-77777777', '13700137003', '<EMAIL>', '深圳市南山区科技园南区', '91440300456789123B', '440300456789123', '招商银行深圳分行', '4567891234567891234', 'B', 'CONSIGNMENT', 20.00, 'ACTIVE', '服装贸易公司', '2025-07-21 10:43:01', 1, '2025-07-27 21:45:10', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1004, 'SUP004', '杭州西湖家居用品有限公司', '杭州西湖家居', 'ENTERPRISE', 304, '陈经理', '0571-55555555', '13600136004', '<EMAIL>', '杭州市西湖区文三路100号', '91330106789123456C', '330106789123456', '建设银行杭州分行', '7891234567891234567', 'B', 'PURCHASE_SALE', NULL, 'ACTIVE', '家居用品供应商', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1339550467939639299, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1005, 'SUP005', '广州天河食品批发有限公司', '广州天河食品', 'ENTERPRISE', 205, '刘总', '020-44444444', '13500135005', '<EMAIL>', '广州市天河区珠江新城', '91440106321654987D', '440106321654987', '农业银行广州分行', '3216549873216549873', 'A', 'JOINT_VENTURE', 12.50, 'ACTIVE', '食品批发商', '2025-07-21 10:43:01', 1, '2025-07-27 21:45:10', 1339550467939639299, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1006, 'SUP006', '宁波港口贸易有限公司', '宁波港口贸易', 'ENTERPRISE', 208, '赵经理', '0574-33333333', '13400134006', '<EMAIL>', '宁波市江北区港口大道200号', '91330205654321789E', '330205654321789', '交通银行宁波分行', '6543217896543217896', 'C', 'PURCHASE_SALE', NULL, 'ACTIVE', '港口贸易公司', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1007, 'SUP007', '老王果蔬批发部', '老王果蔬', 'INDIVIDUAL', 205, '王老板', '020-22222222', '13300133007', '<EMAIL>', '广州市白云区江夏批发市场', NULL, NULL, '农村信用社', '1111222233334444', 'C', 'PURCHASE_SALE', NULL, 'ACTIVE', '个体果蔬批发', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1008, 'SUP008', '小李电子配件店', '小李电子', 'INDIVIDUAL', 206, '李老板', '0755-11111111', '13200132008', '<EMAIL>', '深圳市福田区华强北电子市场', NULL, NULL, '邮政储蓄银行', '2222333344445555', 'C', 'PURCHASE_SALE', NULL, 'ACTIVE', '个体电子配件', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1009, 'SUP009', '张师傅手工艺品工作室', '张师傅工艺品', 'INDIVIDUAL', 207, '张师傅', '0571-99999999', '13100131009', '<EMAIL>', '杭州市拱墅区手工艺品街', NULL, NULL, '杭州银行', '3333444455556666', 'B', 'PURCHASE_SALE', NULL, 'ACTIVE', '手工艺品制作', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1010, 'SUP010', '刘阿姨特色小食店', '刘阿姨小食', 'INDIVIDUAL', 201, '刘阿姨', '010-77777777', '13000130010', '<EMAIL>', '北京市朝阳区美食街88号', NULL, NULL, '北京银行', '4444555566667777', 'C', 'PURCHASE_SALE', NULL, 'ACTIVE', '特色小食制作', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1011, 'SUP011', '测试停用供应商', '测试停用', 'ENTERPRISE', 202, '测试', '010-00000000', '13000000000', '<EMAIL>', '测试地址', '91110000000000000F', '110000000000000', '测试银行', '0000000000000000000', 'D', 'PURCHASE_SALE', NULL, 'INACTIVE', '测试用停用供应商', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);
INSERT INTO `erp_supplier` VALUES (1012, 'SUP012', '黑名单供应商', '黑名单', 'ENTERPRISE', 203, '黑名单', '021-00000000', '13100000000', '<EMAIL>', '黑名单地址', '91310000000000000G', '310000000000000', '黑名单银行', '1111111111111111111', 'D', 'PURCHASE_SALE', NULL, 'BLACKLIST', '黑名单供应商', '2025-07-21 10:43:01', 1, '2025-07-25 23:42:22', 1, 'N', 1);

-- ----------------------------
-- Table structure for erp_supplier_region
-- ----------------------------
DROP TABLE IF EXISTS `erp_supplier_region`;
CREATE TABLE `erp_supplier_region`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `region_id` bigint(20) NOT NULL COMMENT '区域ID',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_erp_supplier_region_supplier_region`(`supplier_id`, `region_id`) USING BTREE COMMENT '供应商-区域唯一索引',
  INDEX `idx_erp_supplier_region_region_id`(`region_id`) USING BTREE COMMENT '区域ID索引',
  INDEX `idx_erp_supplier_region_supplier_id`(`supplier_id`) USING BTREE COMMENT '供应商ID索引',
  CONSTRAINT `fk_supplier_region_region` FOREIGN KEY (`region_id`) REFERENCES `erp_region` (`region_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_supplier_region_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `erp_supplier` (`supplier_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '供应商-区域关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of erp_supplier_region
-- ----------------------------
INSERT INTO `erp_supplier_region` VALUES (1947646909749436418, 1001, 201, '2025-07-22 21:16:23', 1339550467939639299);
INSERT INTO `erp_supplier_region` VALUES (1947646909749436419, 1001, 301, '2025-07-22 21:16:23', 1339550467939639299);
INSERT INTO `erp_supplier_region` VALUES (1947646909749436420, 1001, 202, '2025-07-22 21:16:23', 1339550467939639299);
INSERT INTO `erp_supplier_region` VALUES (1947659913815678978, 1004, 401, '2025-07-22 22:08:04', 1339550467939639299);
INSERT INTO `erp_supplier_region` VALUES (1948334953750077442, 1005, 303, '2025-07-24 18:50:26', 1339550467939639299);
INSERT INTO `erp_supplier_region` VALUES (1948334953817186305, 1005, 304, '2025-07-24 18:50:26', 1339550467939639299);

-- ----------------------------
-- Table structure for pos_order
-- ----------------------------
DROP TABLE IF EXISTS `pos_order`;
CREATE TABLE `pos_order`  (
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `member_id` bigint(20) NULL DEFAULT NULL COMMENT '会员ID',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '折扣金额',
  `final_amount` decimal(10, 2) NOT NULL COMMENT '实付金额',
  `order_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING' COMMENT '订单状态（PENDING-待支付，PAID-已支付，CANCELLED-已取消，REFUNDED-已退款）',
  `payment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'UNPAID' COMMENT '支付状态（UNPAID-未支付，PAID-已支付，PARTIAL-部分支付，REFUNDED-已退款）',
  `payment_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式（CASH-现金，WECHAT-微信，ALIPAY-支付宝，MEMBER-会员卡，CARD-银行卡）',
  `cashier_id` bigint(20) NOT NULL COMMENT '收银员ID',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`order_id`) USING BTREE,
  UNIQUE INDEX `uk_pos_order_no`(`order_no`) USING BTREE,
  INDEX `idx_pos_order_member_id`(`member_id`) USING BTREE,
  INDEX `idx_pos_order_create_time`(`create_time`) USING BTREE,
  INDEX `idx_pos_order_cashier_id`(`cashier_id`) USING BTREE,
  INDEX `idx_pos_order_status`(`order_status`, `payment_status`) USING BTREE,
  INDEX `idx_pos_order_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'POS订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pos_order_item
-- ----------------------------
DROP TABLE IF EXISTS `pos_order_item`;
CREATE TABLE `pos_order_item`  (
  `item_id` bigint(20) NOT NULL COMMENT '订单项ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品编码',
  `unit_price` decimal(10, 2) NOT NULL COMMENT '单价',
  `quantity` decimal(10, 3) NOT NULL COMMENT '数量',
  `total_price` decimal(10, 2) NOT NULL COMMENT '小计金额',
  `unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单位',
  `pricing_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '计价类型（NORMAL-普通，WEIGHT-计重，PIECE-计件，VARIABLE-不定价）',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`item_id`) USING BTREE,
  INDEX `idx_pos_order_item_order_id`(`order_id`) USING BTREE,
  INDEX `idx_pos_order_item_product_id`(`product_id`) USING BTREE,
  CONSTRAINT `fk_pos_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `pos_order` (`order_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'POS订单项表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pos_payment
-- ----------------------------
DROP TABLE IF EXISTS `pos_payment`;
CREATE TABLE `pos_payment`  (
  `payment_id` bigint(20) NOT NULL COMMENT '支付ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `payment_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付单号',
  `payment_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式（CASH-现金，WECHAT-微信，ALIPAY-支付宝，MEMBER-会员卡，CARD-银行卡）',
  `payment_amount` decimal(10, 2) NOT NULL COMMENT '支付金额',
  `received_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实收金额（现金支付时使用）',
  `change_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '找零金额（现金支付时使用）',
  `payment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING' COMMENT '支付状态（PENDING-待支付，SUCCESS-成功，FAILED-失败，CANCELLED-已取消）',
  `transaction_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '第三方交易号',
  `payment_time` datetime(0) NULL DEFAULT NULL COMMENT '支付完成时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`payment_id`) USING BTREE,
  UNIQUE INDEX `uk_pos_payment_no`(`payment_no`) USING BTREE,
  INDEX `idx_pos_payment_order_id`(`order_id`) USING BTREE,
  INDEX `idx_pos_payment_time`(`payment_time`) USING BTREE,
  INDEX `idx_pos_payment_status`(`payment_status`) USING BTREE,
  INDEX `idx_pos_payment_tenant_id`(`tenant_id`) USING BTREE,
  CONSTRAINT `fk_pos_payment_order` FOREIGN KEY (`order_id`) REFERENCES `pos_order` (`order_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'POS支付记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pos_suspended_order
-- ----------------------------
DROP TABLE IF EXISTS `pos_suspended_order`;
CREATE TABLE `pos_suspended_order`  (
  `suspend_id` bigint(20) NOT NULL COMMENT '挂单ID',
  `suspend_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '挂单号',
  `cashier_id` bigint(20) NOT NULL COMMENT '收银员ID',
  `order_data` json NOT NULL COMMENT '订单数据JSON',
  `suspend_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '挂单时间',
  `expire_time` datetime(0) NOT NULL COMMENT '过期时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE' COMMENT '状态（ACTIVE-有效，RESUMED-已恢复，EXPIRED-已过期）',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'N' COMMENT '删除标志（Y-已删除，N-未删除）',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`suspend_id`) USING BTREE,
  UNIQUE INDEX `uk_pos_suspend_no`(`suspend_no`) USING BTREE,
  INDEX `idx_pos_suspend_cashier_id`(`cashier_id`) USING BTREE,
  INDEX `idx_pos_suspend_time`(`suspend_time`) USING BTREE,
  INDEX `idx_pos_suspend_status`(`status`) USING BTREE,
  INDEX `idx_pos_suspend_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'POS挂单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Triggers structure for table erp_supplier
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_supplier_status_change`;
delimiter ;;
CREATE TRIGGER `tr_supplier_status_change` AFTER UPDATE ON `erp_supplier` FOR EACH ROW BEGIN
    -- 当供应商状态变为停用或黑名单时，自动终止相关经营方式
    IF NEW.status IN ('INACTIVE', 'BLACKLIST') AND OLD.status = 'ACTIVE' THEN
        UPDATE erp_supplier_business_mode 
        SET status = 'TERMINATED', 
            update_time = NOW(), 
            update_user = NEW.update_user
        WHERE supplier_id = NEW.supplier_id 
        AND status = 'ACTIVE' 
        AND del_flag = 'N';
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
