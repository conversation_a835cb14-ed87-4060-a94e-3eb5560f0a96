import{r as s,o as _,X as v,a as p,f as m,bw as f,c as g,d as i,w as b,a0 as y}from"./index-18a1ea24.js";/* empty css              */const h=Object.assign({name:"GunsProgress"},{__name:"index",props:{value:{type:Number,default:0},record:{type:Object,default:{}},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:value","change","onChange"],setup(r,{emit:n}){const e=r,l=n,a=s(0);_(()=>{a.value=e.value}),v(()=>e.value,()=>{a.value=e.value});const c=()=>{l("update:value",a.value),l("change",a.value),l("onChange",e.record)};return(o,t)=>{const d=f;return p(),m(d,{value:a.value,"onUpdate:value":t[0]||(t[0]=u=>a.value=u),style:{width:"100%"},disabled:e.readonly||e.disabled,min:0,max:100,onChange:c},null,8,["value","disabled"])}}}),x={class:"guns-body guns-body-card"},B={__name:"index",setup(r){const n=s(15),e=s(!1),l=s(!1),a=()=>{console.log(n.value)};return(c,o)=>{const t=h,d=y;return p(),g("div",x,[i(d,{title:"\u8FDB\u5EA6\u6761",bordered:!1},{default:b(()=>[i(t,{value:n.value,"onUpdate:value":o[0]||(o[0]=u=>n.value=u),disabled:e.value,readonly:l.value,onOnChange:a,style:{width:"300px"}},null,8,["value","disabled","readonly"])]),_:1})])}}};export{B as default};
