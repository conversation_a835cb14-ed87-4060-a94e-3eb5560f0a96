<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.javaguns.ent</groupId>
        <artifactId>micro-seata-demo</artifactId>
        <version>8.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>storage-service</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <!-- 数据库操作 -->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

        <!--数据库驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>

        <!--系统配置-->
        <dependency>
            <groupId>com.javaguns.roses</groupId>
            <artifactId>config-spring-boot-starter</artifactId>
            <version>${roses.version}</version>
        </dependency>

    </dependencies>


</project>
