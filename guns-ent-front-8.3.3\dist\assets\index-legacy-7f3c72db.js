System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./datasource-add-edit-legacy-785bfef8.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./datasource-form-legacy-06d85a2b.js"],(function(e,l){"use strict";var t,a,s,i,n,d,o,c,u,r,h,v,y,f,b,w,g,x,m,p,_,k,I,j,S;return{setters:[e=>{t=e._},e=>{a=e.r,s=e.o,i=e.k,n=e.a,d=e.c,o=e.b,c=e.d,u=e.w,r=e.g,h=e.F,v=e.f,y=e.t,f=e.h,b=e.M,w=e.E,g=e.m,x=e.n,m=e.B,p=e.I,_=e.l,k=e.U,I=e.a7},null,e=>{j=e._,S=e.D},null,null,null,null],execute:function(){const l={class:"guns-layout"},C={class:"guns-layout-content"},T={class:"guns-layout"},D={class:"guns-layout-content-application"},F={class:"content-mian"},U={class:"content-mian-header"},B={class:"header-content"},E={class:"header-content-left"},N={class:"header-content-right"},z={class:"content-mian-body"},A={class:"table-content"};e("default",Object.assign({name:"Position"},{__name:"index",setup(e){const O=a([{key:"index",title:"序号",width:60,align:"center",isShow:!0,fixed:"left"},{dataIndex:"dbName",title:"数据库名称",ellipsis:!0,fixed:"left",width:100,isShow:!0},{dataIndex:"jdbcDriver",title:"驱动类型",width:100,ellipsis:!0,isShow:!0},{dataIndex:"username",title:"账号",ellipsis:!0,width:100,isShow:!0},{dataIndex:"jdbcUrl",title:"jdbc的url",ellipsis:!0,width:100,isShow:!0},{dataIndex:"statusFlag",title:"状态",width:60,isShow:!0},{dataIndex:"remarks",title:"备注",ellipsis:!0,width:150,isShow:!0},{key:"action",title:"操作",fixed:"right",width:60,isShow:!0}]),L=a(null),P=a({dbName:""}),R=a(null),M=a(!1);s((()=>{}));const q=()=>{L.value.reload()};return(e,a)=>{const s=x,G=i("plus-outlined"),H=m,J=p,K=_,Q=k,V=I,W=t;return n(),d("div",l,[o("div",C,[o("div",T,[o("div",D,[o("div",F,[o("div",U,[o("div",B,[o("div",E,[c(s,{size:16})]),o("div",N,[c(s,{size:16},{default:u((()=>[c(H,{type:"primary",class:"border-radius",onClick:a[0]||(a[0]=e=>{return R.value=l,void(M.value=!0);var l})},{default:u((()=>[c(G),a[3]||(a[3]=r("新建"))])),_:1,__:[3]})])),_:1})])])]),o("div",z,[o("div",A,[c(W,{columns:O.value,where:P.value,rowId:"dbId",ref_key:"tableRef",ref:L,rowSelection:!1,url:"/databaseInfo/page",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"DATASOURCE_TABLE"},{toolLeft:u((()=>[c(K,{value:P.value.dbName,"onUpdate:value":a[1]||(a[1]=e=>P.value.dbName=e),placeholder:"数据源名称（回车搜索）",onPressEnter:q,class:"search-input",bordered:!1},{prefix:u((()=>[c(J,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:u((({column:e,record:l})=>["statusFlag"===e.dataIndex?(n(),d(h,{key:0},[1===l.statusFlag?(n(),v(Q,{key:0,color:"success"},{default:u((()=>a[4]||(a[4]=[r("正常")]))),_:1,__:[4]})):(n(),v(Q,{key:1,color:"error"},{default:u((()=>[c(V,null,{title:u((()=>[r(y(l.errorDescription),1)])),default:u((()=>[a[5]||(a[5]=r(" 连接错误 "))])),_:2,__:[5]},1024)])),_:2},1024))],64)):f("",!0),"action"==e.key?(n(),v(s,{key:1},{default:u((()=>[c(J,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{b.confirm({title:"提示",content:"确定要删除选中的多数据源吗?",icon:c(w),maskClosable:!0,onOk:async()=>{const l=await S.delete({dbId:e.dbId});g.success(l.message),q()}})})(l)},null,8,["onClick"])])),_:2},1024)):f("",!0)])),_:1},8,["columns","where"])])])])])])]),M.value?(n(),v(j,{key:0,visible:M.value,"onUpdate:visible":a[2]||(a[2]=e=>M.value=e),data:R.value,onDone:q},null,8,["visible","data"])):f("",!0)])}}}))}}}));
