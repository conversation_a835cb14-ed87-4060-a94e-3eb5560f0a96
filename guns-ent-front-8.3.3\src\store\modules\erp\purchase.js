/**
 * 采购入库状态管理
 * 
 * 管理采购入库相关的状态和操作，包括：
 * - 采购入库单列表管理
 * - 当前采购入库单信息
 * - 采购入库单明细管理
 * - 搜索和筛选状态
 * - CRUD操作
 * - 入库确认和执行
 * - 统计查询
 */
import { defineStore } from 'pinia';
import { PurchaseApi } from '@/views/erp/purchase/api/PurchaseApi';
import { message } from 'ant-design-vue';

export const usePurchaseStore = defineStore({
  id: 'purchase',
  state: () => ({
    // 采购入库单列表数据
    purchaseOrders: [],
    // 当前选中的采购入库单
    currentOrder: null,
    // 当前采购入库单的明细列表
    orderDetails: [],
    // 加载状态
    loading: false,
    // 明细加载状态
    detailsLoading: false,
    // 搜索查询条件
    searchQuery: '',
    // 供应商筛选
    supplierFilter: '',
    // 状态筛选
    statusFilter: '',
    // 采购类型筛选
    typeFilter: '',
    // 日期范围筛选
    dateRange: [],
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    // 统计信息
    statistics: {
      totalAmount: 0,
      totalOrders: 0,
      pendingOrders: 0,
      confirmedOrders: 0,
      inboundOrders: 0
    },
    // 统计加载状态
    statisticsLoading: false
  }),
  
  getters: {
    /**
     * 获取筛选后的采购入库单列表
     */
    filteredOrders: (state) => {
      let filtered = [...state.purchaseOrders];
      
      // 按搜索条件筛选
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(order => 
          order.orderNo?.toLowerCase().includes(query) ||
          order.supplierName?.toLowerCase().includes(query) ||
          order.remark?.toLowerCase().includes(query)
        );
      }
      
      // 按供应商筛选
      if (state.supplierFilter) {
        filtered = filtered.filter(order => 
          order.supplierId === state.supplierFilter
        );
      }
      
      // 按状态筛选
      if (state.statusFilter) {
        filtered = filtered.filter(order => 
          order.status === state.statusFilter
        );
      }
      
      // 按采购类型筛选
      if (state.typeFilter) {
        filtered = filtered.filter(order => 
          order.purchaseType === state.typeFilter
        );
      }
      
      // 按日期范围筛选
      if (state.dateRange && state.dateRange.length === 2) {
        const [startDate, endDate] = state.dateRange;
        filtered = filtered.filter(order => {
          const orderDate = new Date(order.orderDate);
          return orderDate >= startDate && orderDate <= endDate;
        });
      }
      
      return filtered;
    },
    
    /**
     * 获取待处理的采购入库单列表
     */
    pendingOrders: (state) => {
      return state.purchaseOrders.filter(order => order.status === 'PENDING');
    },
    
    /**
     * 获取已确认的采购入库单列表
     */
    confirmedOrders: (state) => {
      return state.purchaseOrders.filter(order => order.status === 'CONFIRMED');
    },
    
    /**
     * 获取已入库的采购入库单列表
     */
    inboundOrders: (state) => {
      return state.purchaseOrders.filter(order => order.status === 'INBOUND');
    },
    
    /**
     * 获取状态统计
     */
    statusStats: (state) => {
      const stats = {
        PENDING: 0,
        CONFIRMED: 0,
        INBOUND: 0,
        CANCELLED: 0
      };
      
      state.purchaseOrders.forEach(order => {
        if (stats.hasOwnProperty(order.status)) {
          stats[order.status]++;
        }
      });
      
      return stats;
    },
    
    /**
     * 获取当前采购入库单的总金额
     */
    currentOrderTotalAmount: (state) => {
      if (!state.orderDetails || state.orderDetails.length === 0) return 0;
      return PurchaseApi.calculateTotalAmount(state.orderDetails);
    },
    
    /**
     * 获取当前采购入库单的总数量
     */
    currentOrderTotalQuantity: (state) => {
      if (!state.orderDetails || state.orderDetails.length === 0) return 0;
      return PurchaseApi.calculateTotalQuantity(state.orderDetails);
    }
  },
  
  actions: {
    /**
     * 获取采购入库单列表（分页）
     */
    async fetchPurchaseOrders(params = {}) {
      this.loading = true;
      try {
        const queryParams = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          orderNo: this.searchQuery || undefined,
          supplierId: this.supplierFilter || undefined,
          status: this.statusFilter || undefined,
          purchaseType: this.typeFilter || undefined,
          startDate: this.dateRange && this.dateRange[0] ? this.dateRange[0].format('YYYY-MM-DD') : undefined,
          endDate: this.dateRange && this.dateRange[1] ? this.dateRange[1].format('YYYY-MM-DD') : undefined,
          ...params
        };
        
        const result = await PurchaseApi.findPage(queryParams);
        
        if (result && result.rows) {
          this.purchaseOrders = result.rows;
          this.pagination.total = result.totalRows;
          this.pagination.current = result.pageNo;
          this.pagination.pageSize = result.pageSize;
        }
        
        return result;
      } catch (error) {
        console.error('获取采购入库单列表失败:', error);
        message.error('获取采购入库单列表失败');
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 获取所有采购入库单列表（不分页）
     */
    async fetchAllPurchaseOrders(params = {}) {
      try {
        const result = await PurchaseApi.findList(params);
        
        if (result) {
          return result;
        }
        
        return [];
      } catch (error) {
        console.error('获取所有采购入库单列表失败:', error);
        message.error('获取所有采购入库单列表失败');
        throw error;
      }
    },
    
    /**
     * 创建采购入库单
     */
    async createPurchaseOrder(orderData) {
      try {
        const result = await PurchaseApi.add(orderData);
        
        if (result) {
          message.success('采购入库单创建成功');
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
        }
        
        return result;
      } catch (error) {
        console.error('创建采购入库单失败:', error);
        message.error('创建采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 更新采购入库单
     */
    async updatePurchaseOrder(orderData) {
      try {
        const result = await PurchaseApi.edit(orderData);
        
        if (result) {
          message.success('采购入库单更新成功');
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
          
          // 如果更新的是当前采购入库单，更新当前采购入库单信息
          if (this.currentOrder && this.currentOrder.purchaseId === orderData.purchaseId) {
            await this.fetchPurchaseOrderDetail(orderData.purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('更新采购入库单失败:', error);
        message.error('更新采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 删除采购入库单
     */
    async deletePurchaseOrder(purchaseId) {
      try {
        const result = await PurchaseApi.delete({ purchaseId });
        
        if (result) {
          message.success('采购入库单删除成功');
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
          
          // 如果删除的是当前采购入库单，清空当前采购入库单
          if (this.currentOrder && this.currentOrder.purchaseId === purchaseId) {
            this.currentOrder = null;
            this.orderDetails = [];
          }
        }
        
        return result;
      } catch (error) {
        console.error('删除采购入库单失败:', error);
        message.error('删除采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 批量删除采购入库单
     */
    async batchDeletePurchaseOrders(purchaseIds) {
      try {
        const result = await PurchaseApi.batchDelete({ purchaseIdList: purchaseIds });
        
        if (result) {
          message.success(`成功删除 ${purchaseIds.length} 个采购入库单`);
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
          
          // 如果删除的包含当前采购入库单，清空当前采购入库单
          if (this.currentOrder && purchaseIds.includes(this.currentOrder.purchaseId)) {
            this.currentOrder = null;
            this.orderDetails = [];
          }
        }
        
        return result;
      } catch (error) {
        console.error('批量删除采购入库单失败:', error);
        message.error('批量删除采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 获取采购入库单详情
     */
    async fetchPurchaseOrderDetail(purchaseId) {
      try {
        const result = await PurchaseApi.detail({ purchaseId });
        
        if (result) {
          this.currentOrder = result;
          // 同时获取明细列表
          await this.fetchOrderDetails(purchaseId);
        }
        
        return result;
      } catch (error) {
        console.error('获取采购入库单详情失败:', error);
        message.error('获取采购入库单详情失败');
        throw error;
      }
    },
    
    /**
     * 获取采购入库单明细列表
     */
    async fetchOrderDetails(purchaseId) {
      this.detailsLoading = true;
      try {
        const result = await PurchaseApi.getPurchaseItems({ purchaseId });
        
        if (result) {
          this.orderDetails = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取采购入库单明细失败:', error);
        message.error('获取采购入库单明细失败');
        throw error;
      } finally {
        this.detailsLoading = false;
      }
    },
    
    /**
     * 确认采购入库单
     */
    async confirmPurchaseOrder(purchaseId, confirmData = {}) {
      try {
        const result = await PurchaseApi.confirm({ 
          purchaseId, 
          ...confirmData 
        });
        
        if (result) {
          message.success('采购入库单确认成功');
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
          
          // 如果确认的是当前采购入库单，更新当前采购入库单信息
          if (this.currentOrder && this.currentOrder.purchaseId === purchaseId) {
            await this.fetchPurchaseOrderDetail(purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('确认采购入库单失败:', error);
        message.error('确认采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 执行入库操作
     */
    async executeInbound(purchaseId, inboundData = {}) {
      try {
        const result = await PurchaseApi.executeInbound({ 
          purchaseId, 
          ...inboundData 
        });
        
        if (result) {
          message.success('入库操作执行成功');
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
          
          // 如果入库的是当前采购入库单，更新当前采购入库单信息
          if (this.currentOrder && this.currentOrder.purchaseId === purchaseId) {
            await this.fetchPurchaseOrderDetail(purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('执行入库操作失败:', error);
        message.error('执行入库操作失败');
        throw error;
      }
    },
    
    /**
     * 取消采购入库单
     */
    async cancelPurchaseOrder(purchaseId, cancelReason = '') {
      try {
        const result = await PurchaseApi.cancel({ 
          purchaseId, 
          cancelReason 
        });
        
        if (result) {
          message.success('采购入库单取消成功');
          // 重新获取采购入库单列表
          await this.fetchPurchaseOrders();
          
          // 如果取消的是当前采购入库单，更新当前采购入库单信息
          if (this.currentOrder && this.currentOrder.purchaseId === purchaseId) {
            await this.fetchPurchaseOrderDetail(purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('取消采购入库单失败:', error);
        message.error('取消采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 校验采购入库单是否可以确认
     */
    async validateConfirm(purchaseId) {
      try {
        const result = await PurchaseApi.validateConfirm({ purchaseId });
        return result;
      } catch (error) {
        console.error('校验确认操作失败:', error);
        throw error;
      }
    },
    
    /**
     * 校验采购入库单是否可以入库
     */
    async validateInbound(purchaseId) {
      try {
        const result = await PurchaseApi.validateInbound({ purchaseId });
        return result;
      } catch (error) {
        console.error('校验入库操作失败:', error);
        throw error;
      }
    },
    
    /**
     * 新增采购入库单明细
     */
    async addOrderDetail(itemData) {
      try {
        const result = await PurchaseApi.addPurchaseItem(itemData);
        
        if (result) {
          message.success('明细添加成功');
          // 重新获取明细列表
          if (this.currentOrder) {
            await this.fetchOrderDetails(this.currentOrder.purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('添加明细失败:', error);
        message.error('添加明细失败');
        throw error;
      }
    },
    
    /**
     * 编辑采购入库单明细
     */
    async updateOrderDetail(itemData) {
      try {
        const result = await PurchaseApi.editPurchaseItem(itemData);
        
        if (result) {
          message.success('明细更新成功');
          // 重新获取明细列表
          if (this.currentOrder) {
            await this.fetchOrderDetails(this.currentOrder.purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('更新明细失败:', error);
        message.error('更新明细失败');
        throw error;
      }
    },
    
    /**
     * 删除采购入库单明细
     */
    async deleteOrderDetail(itemId) {
      try {
        const result = await PurchaseApi.deletePurchaseItem({ itemId });
        
        if (result) {
          message.success('明细删除成功');
          // 重新获取明细列表
          if (this.currentOrder) {
            await this.fetchOrderDetails(this.currentOrder.purchaseId);
          }
        }
        
        return result;
      } catch (error) {
        console.error('删除明细失败:', error);
        message.error('删除明细失败');
        throw error;
      }
    },
    
    /**
     * 批量导入明细
     */
    async batchImportDetails(purchaseId, items) {
      try {
        const result = await PurchaseApi.batchImportItems({ 
          purchaseId, 
          items 
        });
        
        if (result) {
          message.success('明细导入成功');
          // 重新获取明细列表
          await this.fetchOrderDetails(purchaseId);
        }
        
        return result;
      } catch (error) {
        console.error('批量导入明细失败:', error);
        message.error('批量导入明细失败');
        throw error;
      }
    },
    
    /**
     * 获取采购统计信息
     */
    async fetchStatistics(params = {}) {
      this.statisticsLoading = true;
      try {
        const result = await PurchaseApi.getPurchaseStatistics(params);
        
        if (result) {
          this.statistics = result;
        }
        
        return result;
      } catch (error) {
        console.error('获取采购统计信息失败:', error);
        message.error('获取采购统计信息失败');
        throw error;
      } finally {
        this.statisticsLoading = false;
      }
    },
    
    /**
     * 导出采购入库单
     */
    async exportPurchaseOrders(params = {}) {
      try {
        await PurchaseApi.exportPurchase(params);
        message.success('导出成功');
      } catch (error) {
        console.error('导出采购入库单失败:', error);
        message.error('导出采购入库单失败');
        throw error;
      }
    },
    
    /**
     * 设置搜索条件
     */
    setSearchQuery(query) {
      this.searchQuery = query;
    },
    
    /**
     * 设置供应商筛选
     */
    setSupplierFilter(supplierId) {
      this.supplierFilter = supplierId;
    },
    
    /**
     * 设置状态筛选
     */
    setStatusFilter(status) {
      this.statusFilter = status;
    },
    
    /**
     * 设置采购类型筛选
     */
    setTypeFilter(type) {
      this.typeFilter = type;
    },
    
    /**
     * 设置日期范围筛选
     */
    setDateRange(dateRange) {
      this.dateRange = dateRange;
    },
    
    /**
     * 设置分页信息
     */
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    
    /**
     * 设置当前采购入库单
     */
    setCurrentOrder(order) {
      this.currentOrder = order;
    },
    
    /**
     * 清空当前采购入库单
     */
    clearCurrentOrder() {
      this.currentOrder = null;
      this.orderDetails = [];
    },
    
    /**
     * 清空搜索和筛选条件
     */
    clearFilters() {
      this.searchQuery = '';
      this.supplierFilter = '';
      this.statusFilter = '';
      this.typeFilter = '';
      this.dateRange = [];
    },
    
    /**
     * 重置分页到第一页
     */
    resetPagination() {
      this.pagination.current = 1;
    },
    
    /**
     * 刷新采购入库单列表
     */
    async refreshPurchaseOrders() {
      await this.fetchPurchaseOrders();
    },
    
    /**
     * 刷新当前采购入库单详情
     */
    async refreshCurrentOrder() {
      if (this.currentOrder) {
        await this.fetchPurchaseOrderDetail(this.currentOrder.purchaseId);
      }
    }
  }
});