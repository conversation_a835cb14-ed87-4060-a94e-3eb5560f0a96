package cn.stylefeng.roses.kernel.pay.api.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 业务订单的创建需要的参数
 *
 * <AUTHOR>
 * @since 2024/6/9 14:32
 */
@Data
public class BusinessOrder {

    /**
     * 商品id，可以直接传递业务的id
     */
    @ChineseDescription("商品id，可以直接传递业务的id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String goodsName;

    /**
     * 商品原价
     */
    @ChineseDescription("商品原价")
    private BigDecimal originalPrice;

    /**
     * 实付金额
     */
    @ChineseDescription("实付金额")
    private BigDecimal payPrice;

    /**
     * 业务数据的参数
     */
    @ChineseDescription("业务数据的参数")
    private Map<String, Object> businessParams;

    /**
     * 支付成功后回调的业务处理类
     */
    @ChineseDescription("支付成功后回调的业务处理类")
    private String businessProcessClass;

}