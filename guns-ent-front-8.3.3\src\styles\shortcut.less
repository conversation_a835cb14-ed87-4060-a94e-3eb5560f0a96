.bgColor {
  background: #fff;
}

.box {
  height: 100%;
  width: 100%;
  overflow: hidden;
  border-radius: 5px;
}

.ten-height {
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}

.bg-white {
  background: #fff;
  border-radius: 5px;
}

.padding10 {
  padding: 10px;
}

.border-radius {
  border-radius: 4px;
}

.box-shadow {
  box-shadow: 0 0 6px hsla(0, 0%, 80%, 0.5);
}

.height100 {
  height: 100%;
}

// 禁止内容选中
.isSelected {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.f-s-20 {
  font-size: 20px;
}

.f-s-24 {
  font-size: 24px;
}

.p-t-12 {
  padding-top: 12px;
}
.p-t-15 {
  padding-top: 15px;
}
.flex {
  display: flex;
}
