# v7企业版所有插件

## 插件说明

| 插件属性                        | 说明                    |
|:----------------------------|:----------------------|
| _kernel-d-dm-adapter        | 达梦数据源插件               |
| _kernel-d-obfuscator        | jar包源码混淆工具            |
| _kernel-d-oracle-adapter    | oracle数据源适配           |
| _kernel-d-pgsql-adapter     | pgsql数据源适配            |
| _kernel-d-sqlserver-adapter | sql server数据源适配       |
| kernel-d-conversion         | office转化插件            |
| kernel-d-license            | 项目license加密机制，以及jar加密 |
| kernel-d-micro              | 微服务适配插件               |
| kernel-d-oauth2             | oauth2登录              |
| kernel-d-pay                | 支付模块                  |
| kernel-d-report             | 报表插件                  |
| kernel-d-saas               | SaaS多租户模块             |
| kernel-d-sharding-jdbc      | 数据库读写分离插件             |
| kernel-d-sso                | 单点登录统一认证中心            |
| kernel-d-workflow           | 工作流模块                 |
| kernel-s-ent-integration    | 快速集成企业版               |

## 使用说明

打开本项目，在根目录执行`mvn clean install`安装到本地仓库jar包