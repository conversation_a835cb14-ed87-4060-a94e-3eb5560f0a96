<template>
  <div class="guns-layout">
    <div class="guns-layout-content">
      <div class="guns-layout">
        <div class="guns-layout-content-application">
          <div class="content-mian">
            <!-- 头部操作区 -->
            <div class="content-mian-header">
              <div class="header-content">
                <div class="header-content-left">
                  <a-space :size="16" />
                </div>
                <div class="header-content-right">
                  <a-space :size="16">
                    <a-button type="primary" class="border-radius" @click="openAddModal">
                      <plus-outlined />
                      新建入库单
                    </a-button>
                    <a-dropdown>
                      <template #overlay>
                        <a-menu @click="moreClick">
                          <a-menu-item key="1">
                            <icon-font iconClass="icon-opt-shanchu" color="#60666b" />
                            <span>批量删除</span>
                          </a-menu-item>
                          <a-menu-item key="2">
                            <icon-font iconClass="icon-opt-daochu" color="#60666b" />
                            <span>导出数据</span>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button class="border-radius">
                        更多
                        <small-dash-outlined />
                      </a-button>
                    </a-dropdown>
                  </a-space>
                </div>
              </div>
            </div>

            <!-- 主体内容区 -->
            <div class="content-mian-body">
              <div class="table-content">
                <common-table
                  :columns="columns"
                  :where="where"
                  fieldBusinessCode="ERP_PURCHASE_INBOUND_TABLE"
                  showTableTool
                  :showToolTotal="false"
                  rowId="id"
                  ref="tableRef"
                  url="/purchase/order/page"
                >
                  <template #toolLeft>
                    <a-input
                      v-model:value="where.searchText"
                      placeholder="入库单号、供应商名称（回车搜索）"
                      @pressEnter="reload"
                      :bordered="false"
                      class="search-input"
                    >
                      <template #prefix>
                        <icon-font iconClass="icon-opt-search" />
                      </template>
                    </a-input>
                    <a-divider type="vertical" class="divider" />
                    <a @click="changeSuperSearch">{{ superSearch ? '收起' : '高级筛选' }}</a>
                  </template>
                  <template #toolBottom>
                    <div class="super-search" style="margin-top: 8px" v-if="superSearch">
                      <a-form :model="where" :labelCol="labelCol" :wrapper-col="wrapperCol">
                        <a-row :gutter="16">
                          <a-col v-bind="spanCol">
                            <a-form-item label="入库单号:">
                              <a-input v-model:value="where.orderNo" placeholder="请输入入库单号" allowClear />
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="供应商:">
                              <a-input v-model:value="where.supplierName" placeholder="请输入供应商名称" allowClear />
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="状态:">
                              <a-select v-model:value="where.status" placeholder="请选择状态" allowClear>
                                <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                                  {{ item.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label="订单日期:">
                              <a-range-picker
                                v-model:value="dateRange"
                                style="width: 100%"
                                :placeholder="['开始日期', '结束日期']"
                                format="YYYY-MM-DD"
                                allowClear
                              />
                            </a-form-item>
                          </a-col>
                          <a-col v-bind="spanCol">
                            <a-form-item label=" " :colon="false">
                              <a-space>
                                <a-button type="primary" @click="reload">搜索</a-button>
                                <a-button @click="reset">重置</a-button>
                              </a-space>
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </a-form>
                    </div>
                  </template>

                  <!-- 表格列自定义渲染 -->
                  <template #bodyCell="{ column, record }">
                    <!-- 入库单号 -->
                    <template v-if="column.key === 'orderNo'">
                      <a @click="showDetail(record)" class="table-link">{{ record.orderNo }}</a>
                    </template>

                    <!-- 供应商信息 -->
                    <template v-if="column.key === 'supplierInfo'">
                      <div>
                        <div class="supplier-name">{{ record.supplierName }}</div>
                        <div class="supplier-code text-muted">{{ record.supplierCode }}</div>
                      </div>
                    </template>

                    <!-- 状态 -->
                    <template v-if="column.key === 'status'">
                      <a-tag :color="getStatusTagColor(record.status)">
                        {{ record.statusName || getStatusName(record.status) }}
                      </a-tag>
                    </template>

                    <!-- 总金额 -->
                    <template v-if="column.key === 'totalAmount'">
                      <span class="amount-text">{{ formatAmount(record.totalAmount) }}</span>
                    </template>

                    <!-- 操作 -->
                    <template v-if="column.key === 'action'">
                      <a-space :size="16">
                        <!-- 查看详情 - 使用现有的详情图标 -->
                        <icon-font
                          iconClass="icon-opt-xiangqing"
                          font-size="24px"
                          title="查看"
                          color="#60666b"
                          @click="showDetail(record)"
                        />
                        <!-- 编辑 - 使用现有的编辑图标 -->
                        <icon-font
                          v-if="record.status === 'DRAFT'"
                          iconClass="icon-opt-bianji"
                          font-size="24px"
                          title="编辑"
                          color="#60666b"
                          @click="editRecord(record)"
                        />
                        <!-- 确认 - 使用同意图标 -->
                        <icon-font
                          v-if="record.status === 'DRAFT'"
                          iconClass="icon-opt-tongyi"
                          font-size="24px"
                          title="确认"
                          color="#60666b"
                          @click="confirmRecord(record)"
                        />
                        <!-- 入库 - 使用添加图标 -->
                        <icon-font
                          v-if="record.status === 'CONFIRMED'"
                          iconClass="icon-opt-tianjia"
                          font-size="24px"
                          title="入库"
                          color="#60666b"
                          @click="receiveRecord(record)"
                        />
                        <!-- 删除 - 使用现有的删除图标 -->
                        <icon-font
                          v-if="record.status === 'DRAFT'"
                          iconClass="icon-opt-shanchu"
                          font-size="24px"
                          title="删除"
                          color="#60666b"
                          @click="deleteRecord(record)"
                        />
                      </a-space>
                    </template>
                  </template>
                </common-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <inbound-form
      v-model:visible="showEdit"
      :data="currentRecord"
      @ok="handleFormOk"
    />

    <!-- 详情弹窗 -->
    <inbound-detail
      v-model:visible="showDetailModal"
      :data="currentRecord"
    />

    <!-- 确认入库弹窗 -->
    <confirm-inbound-modal
      v-model:visible="showConfirmModal"
      :data="currentRecord"
      @ok="handleConfirmOk"
    />

    <!-- 入库操作弹窗 -->
    <receive-inbound-modal
      v-model:visible="showReceiveModal"
      :data="currentRecord"
      @ok="handleReceiveOk"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { 
  PlusOutlined, 
  SmallDashOutlined, 
  DownOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons-vue';
import { PurchaseApi } from '../api/PurchaseApi';
import { isMobile } from '@/utils/common/util';
import InboundForm from './components/InboundForm.vue';
import InboundDetail from './components/InboundDetail.vue';
import ConfirmInboundModal from './components/ConfirmInboundModal.vue';
import ReceiveInboundModal from './components/ReceiveInboundModal.vue';

export default {
  name: 'PurchaseInboundIndex',
  components: {
    PlusOutlined,
    SmallDashOutlined,
    DownOutlined,
    InboundForm,
    InboundDetail,
    ConfirmInboundModal,
    ReceiveInboundModal
  },
  setup() {
    // 响应式数据
    const superSearch = ref(false);
    const showEdit = ref(false);
    const showDetailModal = ref(false);
    const showConfirmModal = ref(false);
    const showReceiveModal = ref(false);
    const currentRecord = ref({});
    const tableRef = ref(null);
    const dateRange = ref([]);

    // 状态选项
    const statusOptions = PurchaseApi.getPurchaseStatusOptions();

    // 响应式计算属性
    const labelCol = computed(() => {
      return { xxl: 7, xl: 7, lg: 5, md: 7, sm: 4 };
    });

    const wrapperCol = computed(() => {
      return { xxl: 17, xl: 17, lg: 19, md: 17, sm: 20 };
    });

    const spanCol = computed(() => {
      if (isMobile()) {
        return { xxl: 6, xl: 8, lg: 12, md: 24, sm: 24, xs: 24 };
      }
      return { xxl: 6, xl: 8, lg: 24, md: 24, sm: 24, xs: 24 };
    });

    // 查询参数
    const where = reactive({
      searchText: '',
      orderNo: '',
      supplierName: '',
      status: undefined,
      startDate: undefined,
      endDate: undefined
    });

    // 表格列定义
    const columns = [
      {
        title: '入库单号',
        dataIndex: 'orderNo',
        key: 'orderNo',
        width: 160,
        fixed: 'left'
      },
      {
        title: '供应商',
        key: 'supplierInfo',
        width: 200
      },
      {
        title: '状态',
        key: 'status',
        width: 100
      },
      {
        title: '总金额',
        key: 'totalAmount',
        width: 120,
        align: 'right'
      },
      {
        title: '订单日期',
        dataIndex: 'orderDate',
        key: 'orderDate',
        width: 120
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 160
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ];

    // 切换高级搜索
    const changeSuperSearch = () => {
      superSearch.value = !superSearch.value;
    };

    // 重新加载数据
    const reload = () => {
      // 处理日期范围
      if (dateRange.value && dateRange.value.length === 2) {
        where.startDate = dateRange.value[0];
        where.endDate = dateRange.value[1];
      } else {
        where.startDate = undefined;
        where.endDate = undefined;
      }
      tableRef.value.reload();
    };

    // 重置搜索条件
    const reset = () => {
      where.searchText = '';
      where.orderNo = '';
      where.supplierName = '';
      where.status = undefined;
      where.startDate = undefined;
      where.endDate = undefined;
      dateRange.value = [];
      reload();
    };

    // 获取状态名称
    const getStatusName = (status) => {
      return PurchaseApi.getPurchaseStatusName(status);
    };

    // 获取状态标签颜色
    const getStatusTagColor = (status) => {
      return PurchaseApi.getStatusTagColor(status);
    };

    // 格式化金额
    const formatAmount = (amount) => {
      return PurchaseApi.formatAmount(amount);
    };

    // 删除入库单
    const deleteRecord = async (record) => {
      Modal.confirm({
        title: '删除入库单',
        content: `确定要删除入库单"${record.orderNo}"吗？删除后无法恢复。`,
        okText: '确定删除',
        cancelText: '取消',
        okType: 'danger',
        icon: createVNode(ExclamationCircleOutlined),
        onOk: async () => {
          try {
            await PurchaseApi.delete({ id: record.id });
            message.success('删除成功');
            reload();
          } catch (error) {
            message.error('删除失败：' + (error.message || '未知错误'));
          }
        }
      });
    };

    // 打开新增弹窗
    const openAddModal = () => {
      currentRecord.value = {};
      showEdit.value = true;
    };

    // 编辑记录
    const editRecord = async (record) => {
      try {
        console.log('开始加载入库单详情用于编辑，ID:', record.id);

        // 调用详情API获取完整数据
        const detailData = await PurchaseApi.detail({ id: record.id });
        console.log('编辑入库单详情API响应:', detailData);

        if (detailData) {
          currentRecord.value = detailData;
          showEdit.value = true;
        } else {
          message.error('获取入库单详情失败，无法编辑');
        }
      } catch (error) {
        console.error('加载入库单详情失败:', error);
        message.error('加载入库单详情失败：' + (error.message || '未知错误'));
      }
    };

    // 查看详情
    const showDetail = async (record) => {
      try {
        console.log('开始加载入库单详情，ID:', record.id);

        // 调用详情API获取完整数据
        const detailData = await PurchaseApi.detail({ id: record.id });
        console.log('入库单详情API响应:', detailData);

        if (detailData) {
          currentRecord.value = detailData;
          showDetailModal.value = true;
        } else {
          message.error('获取入库单详情失败');
        }
      } catch (error) {
        console.error('加载入库单详情失败:', error);
        message.error('加载入库单详情失败：' + (error.message || '未知错误'));
      }
    };

    // 确认入库单
    const confirmRecord = async (record) => {
      try {
        console.log('开始加载入库单详情用于确认，ID:', record.id);

        // 调用详情API获取完整数据
        const detailData = await PurchaseApi.detail({ id: record.id });
        console.log('确认入库单详情API响应:', detailData);

        if (detailData) {
          currentRecord.value = detailData;
          showConfirmModal.value = true;
        } else {
          message.error('获取入库单详情失败，无法确认');
        }
      } catch (error) {
        console.error('加载入库单详情失败:', error);
        message.error('加载入库单详情失败：' + (error.message || '未知错误'));
      }
    };

    // 执行入库操作
    const receiveRecord = async (record) => {
      try {
        console.log('开始加载入库单详情用于入库操作，ID:', record.id);

        // 调用详情API获取完整数据
        const detailData = await PurchaseApi.detail({ id: record.id });
        console.log('入库操作详情API响应:', detailData);

        if (detailData) {
          currentRecord.value = detailData;
          showReceiveModal.value = true;
        } else {
          message.error('获取入库单详情失败，无法执行入库操作');
        }
      } catch (error) {
        console.error('加载入库单详情失败:', error);
        message.error('加载入库单详情失败：' + (error.message || '未知错误'));
      }
    };



    // 更多操作菜单点击
    const moreClick = ({ key }) => {
      if (key === '1') {
        // 批量删除
        batchDelete();
      } else if (key === '2') {
        // 导出数据
        exportData();
      }
    };



    // 批量删除
    const batchDelete = () => {
      if (!tableRef.value.selectedRowList || tableRef.value.selectedRowList.length === 0) {
        message.warning('请选择要删除的记录');
        return;
      }

      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${tableRef.value.selectedRowList.length} 条记录吗？`,
        icon: createVNode(ExclamationCircleOutlined),
        onOk: async () => {
          try {
            // 这里需要实现批量删除API
            message.success('批量删除成功');
            reload();
          } catch (error) {
            message.error('批量删除失败：' + (error.message || '未知错误'));
          }
        }
      });
    };

    // 导出数据
    const exportData = () => {
      try {
        PurchaseApi.exportPurchase(where);
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败：' + (error.message || '未知错误'));
      }
    };

    // 表单操作成功回调
    const handleFormOk = () => {
      showEdit.value = false;
      reload();
    };

    // 确认操作成功回调
    const handleConfirmOk = () => {
      showConfirmModal.value = false;
      reload();
    };

    // 入库操作成功回调
    const handleReceiveOk = () => {
      showReceiveModal.value = false;
      reload();
    };

    // 组件挂载时的操作
    onMounted(() => {
      // 初始化加载数据
    });

    return {
      superSearch,
      showEdit,
      showDetailModal,
      showConfirmModal,
      showReceiveModal,
      currentRecord,
      tableRef,
      dateRange,
      statusOptions,
      labelCol,
      wrapperCol,
      spanCol,
      where,
      columns,
      changeSuperSearch,
      reload,
      reset,
      getStatusName,
      getStatusTagColor,
      formatAmount,
      openAddModal,
      editRecord,
      showDetail,
      confirmRecord,
      receiveRecord,
      deleteRecord,
      moreClick,

      batchDelete,
      exportData,
      handleFormOk,
      handleConfirmOk,
      handleReceiveOk
    };
  }
};
</script>

<style scoped lang="less">
/* 搜索区域样式 */
.divider {
  height: 20px;
  margin: 0 12px;
}

/* 供应商信息样式 */
.supplier-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.supplier-code {
  font-size: 12px;
  color: #8c8c8c;
}

/* 金额样式 */
.amount-text {
  font-weight: 500;
  color: #1890ff;
}

/* 表格链接样式 */
.table-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.table-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 辅助文本样式 */
.text-muted {
  color: #8c8c8c;
  font-size: 12px;
}

/* 表格行样式优化 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #fafafa !important;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa !important;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f5f5f5;
  padding: 12px 16px;
  vertical-align: middle;
}


</style>
