<template>
  <div class="box bgColor box-shadow">
    <!-- 头部标题和新增按钮 -->
    <div class="left-header" v-if="displayConfig.showHeader && displayConfig.isSetWidth">
      <span class="left-header-title">{{ displayConfig.title || '' }}</span>
      <span>
        <plus-outlined 
          class="header-add" 
          v-if="displayConfig.showAddButton && actionConfig?.allowAdd" 
          @click="handleAdd()" 
          :title="'新增' + (displayConfig.title || '节点')"
        />
      </span>
    </div>

    <!-- 搜索框 -->
    <div class="search" v-if="displayConfig.showSearch">
      <a-input
        v-model:value="searchText"
        :placeholder="displayConfig.searchPlaceholder || '请输入关键词搜索'"
        allow-clear
        @pressEnter="handleSearch"
        @change="handleSearchChange"
      >
        <template #prefix>
          <icon-font iconClass="icon-opt-search" />
        </template>
      </a-input>
    </div>

    <!-- 树结构内容 -->
    <div class="tree-content">
      <a-spin tip="Loading..." :spinning="loading" :delay="100">
        <div v-show="treeData && treeData.length > 0" class="left-tree">
          <a-tree
            v-model:selectedKeys="selectedKeys"
            v-model:expandedKeys="expandedKeys"
            v-model:loadedKeys="loadedKeys"
            @select="handleSelect"
            @expand="handleExpand"
            @click="handleNodeClick"
            @dblclick="handleNodeDoubleClick"
            :load-data="interactionConfig.lazyLoad ? handleLoadData : undefined"
            :tree-data="treeData"
            :show-icon="displayConfig.showIcon"
            :multiple="interactionConfig.allowMultiSelect"
            :selectable="interactionConfig.selectable"
            :field-names="antdFieldNames"
          >
            <!-- 自定义节点标题 -->
            <template #title="nodeData">
              <!-- 显示编辑图标模式 -->
              <span class="tree-edit" v-if="displayConfig.showEditIcons && actionConfig">
                <span class="edit-title" :title="nodeData[fieldMapping.title] || ''">
                  {{ nodeData[fieldMapping.title] || '' }}
                </span>
                <span class="edit-icon">
                  <a-space>
                    <!-- 新增按钮 -->
                    <icon-font
                      v-if="actionConfig.allowAdd"
                      iconClass="icon-opt-tianjia"
                      color="var(--primary-color)"
                      title="新增子节点"
                      @click.stop="handleAdd(nodeData)"
                    />
                    <!-- 编辑按钮 -->
                    <icon-font
                      v-if="actionConfig.allowEdit"
                      iconClass="icon-opt-bianji"
                      color="var(--primary-color)"
                      title="编辑节点"
                      @click.stop="handleEdit(nodeData)"
                    />
                    <!-- 删除按钮 -->
                    <icon-font
                      v-if="actionConfig.allowDelete"
                      iconClass="icon-opt-shanchu"
                      color="red"
                      title="删除节点"
                      @click.stop="handleDelete(nodeData)"
                    />
                    <!-- 自定义操作按钮 -->
                    <icon-font
                      v-for="action in (actionConfig.customActions || [])"
                      :key="action.key"
                      :iconClass="action.icon"
                      :color="action.color || 'var(--primary-color)'"
                      :title="action.title || action.key"
                      @click.stop="handleCustomAction(action.key, nodeData)"
                    />
                  </a-space>
                </span>
              </span>
              <!-- 只读模式 -->
              <span class="not-tree-edit" v-else>
                <span class="edit-title" :title="nodeData[fieldMapping.title] || ''">
                  {{ nodeData[fieldMapping.title] || '' }}
                </span>
              </span>
            </template>
          </a-tree>
        </div>
        <a-empty v-show="treeData && treeData.length === 0" class="empty" />
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import IconFont from '@/components/common/IconFont/index.vue'
import { TreeErrorType } from './types.js'
import { TreeDataAdapter, TreeErrorHandler } from './utils.js'

// 定义组件名称
defineOptions({
  name: 'UniversalTree'
})

// 定义Props
const props = defineProps({
  dataSource: {
    type: Object,
    required: true
  },
  fieldMapping: {
    type: Object,
    required: true
  },
  displayConfig: {
    type: Object,
    default: () => ({
      showHeader: true,
      showSearch: true,
      showAddButton: false,
      showEditIcons: false,
      showIcon: false,
      isSetWidth: true
    })
  },
  interactionConfig: {
    type: Object,
    default: () => ({
      selectable: true,
      expandable: true,
      lazyLoad: false,
      defaultExpandLevel: 2,
      allowMultiSelect: false
    })
  },
  actionConfig: {
    type: Object,
    default: () => ({
      allowAdd: false,
      allowEdit: false,
      allowDelete: false,
      customActions: []
    })
  }
})

// 定义事件
const emit = defineEmits([
  'select',        // 节点选择事件
  'expand',        // 节点展开/收起事件
  'search',        // 搜索事件
  'add',           // 新增节点事件
  'edit',          // 编辑节点事件
  'delete',        // 删除节点事件
  'customAction',  // 自定义操作事件
  'load',          // 数据加载完成事件
  'loadError',     // 数据加载错误事件
  'nodeClick',     // 节点点击事件（可选）
  'nodeDoubleClick' // 节点双击事件（可选）
])

// 响应式数据
const loading = ref(false)
const treeData = ref([])
const selectedKeys = ref([])
const expandedKeys = ref([])
const loadedKeys = ref([])
const searchText = ref('')

// 计算属性 - Ant Design Tree组件的字段映射
const antdFieldNames = computed(() => ({
  children: props.fieldMapping.children,
  title: props.fieldMapping.title,
  key: props.fieldMapping.key,
  value: props.fieldMapping.key
}))

// 解构配置对象以便在模板中使用
const { dataSource, fieldMapping, displayConfig, interactionConfig, actionConfig } = props

/**
 * 加载树数据
 */
const loadTreeData = async () => {
  if (!dataSource.api) {
    console.error('数据源API未配置')
    return
  }
  
  try {
    loading.value = true
    const params = {}
    
    // 添加搜索参数
    if (searchText.value && searchText.value.trim() && dataSource.searchParam) {
      params[dataSource.searchParam] = searchText.value.trim()
    }
    
    const response = await dataSource.api(params)
    const rawData = response?.data || response || []
    
    // 处理数据格式
    const processedData = TreeDataAdapter.setIsLeaf(rawData, fieldMapping)
    treeData.value = TreeDataAdapter.formatTreeData(processedData, fieldMapping)
    
    // 设置默认展开节点
    if (searchText.value && searchText.value.trim()) {
      // 搜索时展开所有匹配的节点
      expandedKeys.value = TreeDataAdapter.extractAllIds(treeData.value)
    } else if (expandedKeys.value.length === 0 && interactionConfig.defaultExpandLevel) {
      // 默认展开指定层级
      expandedKeys.value = TreeDataAdapter.extractNodeIdsByLevel(
        treeData.value, 
        interactionConfig.defaultExpandLevel, 
        fieldMapping
      )
    }
    
    emit('load', treeData.value)
  } catch (error) {
    console.error('加载树数据失败:', error)
    treeData.value = [] // 确保在错误时清空数据
    const treeError = {
      type: TreeErrorType.DATA_LOAD_ERROR,
      message: '数据加载失败',
      originalError: error
    }
    
    const errorResult = TreeErrorHandler.handleError(treeError, { searchText: searchText.value })
    message.error(errorResult.message)
    emit('loadError', error)
  } finally {
    loading.value = false
  }
}

/**
 * 懒加载节点数据
 */
const handleLoadData = async (treeNode) => {
  if (!dataSource.lazyLoadApi || !dataSource.parentIdParam) {
    return Promise.resolve()
  }
  
  // 避免重复加载
  if (loadedKeys.value.includes(treeNode.eventKey)) {
    return Promise.resolve()
  }
  
  // 设置已加载的节点
  loadedKeys.value.push(treeNode.eventKey)
  
  return new Promise((resolve) => {
    const params = {}
    params[dataSource.parentIdParam] = treeNode.dataRef[fieldMapping.key]
    
    dataSource.lazyLoadApi(params)
      .then((response) => {
        const rawData = response?.data || response || []
        const processedData = TreeDataAdapter.setIsLeaf(rawData, fieldMapping)
        const formattedData = TreeDataAdapter.formatTreeData(processedData, fieldMapping)
        
        // 确保节点数据存在
        if (treeNode.dataRef) {
          treeNode.dataRef[fieldMapping.children] = formattedData
          // 重新赋值树数据以触发更新
          treeData.value = [...treeData.value]
        }
      })
      .catch((error) => {
        console.error('懒加载失败:', error)
        // 从已加载列表中移除失败的节点，允许重试
        const index = loadedKeys.value.indexOf(treeNode.eventKey)
        if (index > -1) {
          loadedKeys.value.splice(index, 1)
        }
        
        const treeError = {
          type: TreeErrorType.LAZY_LOAD_ERROR,
          message: '子节点加载失败',
          originalError: error
        }
        
        const errorResult = TreeErrorHandler.handleError(treeError, { nodeKey: treeNode.eventKey })
        message.error(errorResult.message)
      })
      .finally(() => {
        resolve()
      })
  })
}

/**
 * 处理节点选择
 */
const handleSelect = (keys, info) => {
  selectedKeys.value = keys
  const selectedNodes = info.selectedNodes || []
  const selectedData = selectedNodes.map(node => node.dataRef || node)
  
  // 触发选择事件，传递选中的keys和完整的节点数据
  emit('select', {
    keys,
    nodes: selectedData,
    event: info
  })
}

/**
 * 处理节点展开
 */
const handleExpand = (keys, info) => {
  expandedKeys.value = keys
  const expandedNodes = info?.expandedNodes || []
  const expandedData = expandedNodes.map(node => node.dataRef || node)
  
  // 触发展开事件，传递展开的keys和节点数据
  emit('expand', {
    keys,
    nodes: expandedData,
    expanded: info?.expanded,
    node: info?.node?.dataRef || info?.node,
    event: info
  })
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  const searchValue = searchText.value?.trim() || ''
  
  // 触发搜索事件，传递搜索文本和搜索状态
  emit('search', {
    searchText: searchValue,
    isSearching: !!searchValue,
    timestamp: Date.now()
  })
  
  loadTreeData()
}

/**
 * 处理搜索文本变化
 */
const handleSearchChange = () => {
  const searchValue = searchText.value?.trim() || ''
  
  // 如果搜索文本为空，重新加载数据
  if (!searchValue) {
    emit('search', {
      searchText: '',
      isSearching: false,
      timestamp: Date.now()
    })
    loadTreeData()
  }
}

/**
 * 处理新增操作
 */
const handleAdd = (parentNode = null) => {
  // 触发新增事件，传递父节点信息和操作类型
  emit('add', {
    parentNode: parentNode || null,
    action: 'add',
    timestamp: Date.now(),
    context: {
      isRootAdd: !parentNode,
      parentKey: parentNode ? parentNode[fieldMapping.key] : null,
      parentTitle: parentNode ? parentNode[fieldMapping.title] : null
    }
  })
}

/**
 * 处理编辑操作
 */
const handleEdit = (node) => {
  if (!node) {
    console.warn('编辑操作缺少节点数据')
    return
  }
  
  // 触发编辑事件，传递节点信息和操作类型
  emit('edit', {
    node,
    action: 'edit',
    timestamp: Date.now(),
    context: {
      nodeKey: node[fieldMapping.key],
      nodeTitle: node[fieldMapping.title],
      hasChildren: node[fieldMapping.children]?.length > 0
    }
  })
}

/**
 * 处理删除操作
 */
const handleDelete = (node) => {
  if (!node) {
    console.warn('删除操作缺少节点数据')
    return
  }
  
  // 触发删除事件，传递节点信息和操作类型
  emit('delete', {
    node,
    action: 'delete',
    timestamp: Date.now(),
    context: {
      nodeKey: node[fieldMapping.key],
      nodeTitle: node[fieldMapping.title],
      hasChildren: node[fieldMapping.children]?.length > 0,
      isSelected: selectedKeys.value.includes(String(node[fieldMapping.key]))
    }
  })
}

/**
 * 处理自定义操作
 */
const handleCustomAction = (actionKey, node) => {
  if (!actionKey || !node) {
    console.warn('自定义操作缺少必要参数')
    return
  }
  
  // 查找自定义操作配置
  const customAction = actionConfig?.customActions?.find(action => action.key === actionKey)
  
  // 触发自定义操作事件，传递操作信息和节点数据
  emit('customAction', {
    actionKey,
    node,
    action: 'custom',
    timestamp: Date.now(),
    context: {
      nodeKey: node[fieldMapping.key],
      nodeTitle: node[fieldMapping.title],
      actionConfig: customAction,
      hasChildren: node[fieldMapping.children]?.length > 0
    }
  })
}

/**
 * 刷新树数据（对外暴露的方法）
 */
const reload = () => {
  loadTreeData()
}

/**
 * 获取选中的节点数据（对外暴露的方法）
 */
const getSelectedNodes = () => {
  return selectedKeys.value
}

/**
 * 设置选中的节点（对外暴露的方法）
 */
const setSelectedKeys = (keys) => {
  selectedKeys.value = keys
}

/**
 * 展开指定节点（对外暴露的方法）
 */
const expandNodes = (keys) => {
  const newExpandedKeys = [...new Set([...expandedKeys.value, ...keys])]
  expandedKeys.value = newExpandedKeys
  
  // 触发展开事件
  emit('expand', {
    keys: newExpandedKeys,
    nodes: [],
    expanded: true,
    programmatic: true,
    timestamp: Date.now()
  })
}

/**
 * 收起指定节点（对外暴露的方法）
 */
const collapseNodes = (keys) => {
  const newExpandedKeys = expandedKeys.value.filter(key => !keys.includes(key))
  expandedKeys.value = newExpandedKeys
  
  // 触发展开事件
  emit('expand', {
    keys: newExpandedKeys,
    nodes: [],
    expanded: false,
    programmatic: true,
    timestamp: Date.now()
  })
}

/**
 * 清空选择（对外暴露的方法）
 */
const clearSelection = () => {
  selectedKeys.value = []
  
  // 触发选择事件
  emit('select', {
    keys: [],
    nodes: [],
    cleared: true,
    programmatic: true,
    timestamp: Date.now()
  })
}

/**
 * 展开所有节点（对外暴露的方法）
 */
const expandAll = () => {
  const allKeys = TreeDataAdapter.extractAllIds(treeData.value)
  expandedKeys.value = allKeys
  
  // 触发展开事件
  emit('expand', {
    keys: allKeys,
    nodes: [],
    expanded: true,
    expandAll: true,
    programmatic: true,
    timestamp: Date.now()
  })
}

/**
 * 收起所有节点（对外暴露的方法）
 */
const collapseAll = () => {
  expandedKeys.value = []
  
  // 触发展开事件
  emit('expand', {
    keys: [],
    nodes: [],
    expanded: false,
    collapseAll: true,
    programmatic: true,
    timestamp: Date.now()
  })
}

/**
 * 根据节点key查找节点数据（对外暴露的方法）
 */
const findNodeByKey = (key) => {
  const findNode = (nodes, targetKey) => {
    for (const node of nodes) {
      if (String(node[fieldMapping.key]) === String(targetKey)) {
        return node
      }
      if (node[fieldMapping.children] && node[fieldMapping.children].length > 0) {
        const found = findNode(node[fieldMapping.children], targetKey)
        if (found) return found
      }
    }
    return null
  }
  
  return findNode(treeData.value, key)
}

/**
 * 展开到指定节点（对外暴露的方法）
 */
const expandToNode = (key) => {
  const parentKeys = TreeDataAdapter.getParentKeys(treeData.value, key, fieldMapping)
  if (parentKeys.length > 0) {
    expandNodes(parentKeys)
  }
}

/**
 * 选择并展开到指定节点（对外暴露的方法）
 */
const selectAndExpandToNode = (key) => {
  // 先展开到节点
  expandToNode(key)
  
  // 然后选择节点
  setTimeout(() => {
    setSelectedKeys([String(key)])
  }, 100)
}

/**
 * 处理节点点击事件
 */
const handleNodeClick = (e, node) => {
  if (!node) return
  
  const nodeData = node.dataRef || node
  
  // 触发节点点击事件
  emit('nodeClick', {
    node: nodeData,
    event: e,
    timestamp: Date.now(),
    context: {
      nodeKey: nodeData[fieldMapping.key],
      nodeTitle: nodeData[fieldMapping.title],
      hasChildren: nodeData[fieldMapping.children]?.length > 0,
      isSelected: selectedKeys.value.includes(String(nodeData[fieldMapping.key])),
      isExpanded: expandedKeys.value.includes(String(nodeData[fieldMapping.key]))
    }
  })
}

/**
 * 处理节点双击事件
 */
const handleNodeDoubleClick = (e, node) => {
  if (!node) return
  
  const nodeData = node.dataRef || node
  
  // 触发节点双击事件
  emit('nodeDoubleClick', {
    node: nodeData,
    event: e,
    timestamp: Date.now(),
    context: {
      nodeKey: nodeData[fieldMapping.key],
      nodeTitle: nodeData[fieldMapping.title],
      hasChildren: nodeData[fieldMapping.children]?.length > 0,
      isSelected: selectedKeys.value.includes(String(nodeData[fieldMapping.key])),
      isExpanded: expandedKeys.value.includes(String(nodeData[fieldMapping.key]))
    }
  })
}

// 暴露方法给父组件
defineExpose({
  // 数据操作
  reload,
  loadTreeData,
  
  // 选择操作
  getSelectedNodes,
  setSelectedKeys,
  clearSelection,
  
  // 展开操作
  expandNodes,
  collapseNodes,
  expandAll,
  collapseAll,
  expandToNode,
  selectAndExpandToNode,
  
  // 查找操作
  findNodeByKey,
  
  // 状态获取
  getTreeData: () => treeData.value,
  getExpandedKeys: () => expandedKeys.value,
  getLoadedKeys: () => loadedKeys.value,
  getSearchText: () => searchText.value,
  isLoading: () => loading.value
})

// 组件挂载时加载数据
onMounted(() => {
  loadTreeData()
})
</script>

<style scoped lang="less">
@import url('@/styles/commonTree.less');

// 确保树组件背景色一致
:deep(.ant-tree) {
  background-color: #fff !important;
}

// 编辑图标区域宽度
.edit-icon {
  width: 60px !important;
}

// 悬停时调整标题宽度以显示操作按钮
.tree-edit:hover .edit-title {
  width: calc(100% - 70px) !important;
}

// 修复 a-tree 组件的节点内容宽度问题
:deep(.ant-tree-node-content-wrapper) {
  width: 100% !important;
  display: inline-block !important;
}

:deep(.ant-tree-title) {
  width: 100% !important;
  display: block !important;
}

// 确保树节点占满整个宽度
:deep(.ant-tree-treenode) {
  width: 100% !important;
}

// 调整树节点的内边距，与commonTree.less保持一致
:deep(.ant-tree-node-content-wrapper) {
  padding: 4px 8px !important;
  line-height: 24px !important;
}

// 加载状态样式优化
:deep(.ant-spin-nested-loading) {
  height: 100%;
}

:deep(.ant-spin-container) {
  height: 100%;
}

// 空状态样式
.empty {
  margin-top: 50%;
}

// 搜索框样式优化
.search {
  .ant-input-affix-wrapper {
    border-radius: 4px;
  }
}

// 头部样式优化
.left-header {
  .header-add {
    border-radius: 4px;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: var(--primary-color-hover, #e9f3f8);
    }
  }
}
</style>