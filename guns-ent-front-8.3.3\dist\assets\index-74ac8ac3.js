import{_ as B}from"./index-02bf6f00.js";import{_ as D,r as s,o as R,a as l,c as f,b as t,d as a,w as c,g as E,t as A,h as u,f as x,M as V,E as W,m as b,I as M,l as O,n as U,B as $}from"./index-18a1ea24.js";import F from"./tenant-detail-3a81cf05.js";import P from"./tenant-approver-095a52de.js";import{T as j}from"./TenantApi-e23e3174.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              *//* empty css              */import"./time-util-d1d9a3df.js";const q={class:"guns-layout"},G={class:"guns-layout-content"},H={class:"guns-layout"},J={class:"guns-layout-content-application"},K={class:"content-mian"},Q={class:"content-mian-header"},X={class:"header-content"},Y={class:"header-content-left"},Z={class:"header-content-right"},ee={class:"content-mian-body"},te={class:"table-content"},ne=["onClick"],ae=["src"],oe=Object.assign({name:"TenantExamine"},{__name:"index",setup(se){const k=s([{key:"index",title:"\u5E8F\u53F7",width:48,align:"center",isShow:!0,hideInSetting:!0,customRender:({index:o})=>i.value.tableIndex+o},{dataIndex:"tenantName",title:"\u79DF\u6237\u540D\u79F0",ellipsis:!0,width:200,isShow:!0},{dataIndex:"tenantCode",title:"\u79DF\u6237\u7F16\u7801",width:100,isShow:!0},{dataIndex:"tenantLogoWrapper",title:"\u79DF\u6237logo",width:100,isShow:!0},{dataIndex:"companyName",title:"\u516C\u53F8",width:100,isShow:!0},{dataIndex:"email",title:"\u7533\u8BF7\u4EBA\u90AE\u7BB1",width:100,isShow:!0},{dataIndex:"safePhone",title:"\u7533\u8BF7\u4EBA\u7535\u8BDD",width:100,isShow:!0},{dataIndex:"createTime",title:"\u7533\u8BF7\u65F6\u95F4",width:100,isShow:!0},{key:"action",title:"\u64CD\u4F5C",width:80,isShow:!0}]),i=s(null),v=s({searchText:"",activeFlag:"N"}),g=s(null),p=s(!1),r=s(!1),m=s([]);R(()=>{});const _=()=>{i.value.reload()},I=o=>{g.value=o,p.value=!0},C=o=>{V.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u79DF\u6237\u5417?",icon:a(W),maskClosable:!0,onOk:async()=>{const e=await j.delete({tenantId:o.tenantId});b.success(e.message),_()}})},S=()=>{if(i.value.selectedRowList&&i.value.selectedRowList.length==0)return b.warning("\u8BF7\u9009\u62E9\u9700\u8981\u5BA1\u6279\u7684\u79DF\u6237");m.value=i.value.selectedRowList,r.value=!0},T=o=>{m.value=[o.tenantId],r.value=!0};return(o,e)=>{const h=M,L=O,w=U,N=$,z=B;return l(),f("div",q,[t("div",G,[t("div",H,[t("div",J,[t("div",K,[t("div",Q,[t("div",X,[t("div",Y,[a(w,{size:16},{default:c(()=>[a(L,{value:v.value.searchText,"onUpdate:value":e[0]||(e[0]=n=>v.value.searchText=n),placeholder:"\u79DF\u6237\u540D\u79F0\u3001\u79DF\u6237\u7F16\u7801\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:_,class:"search-input"},{prefix:c(()=>[a(h,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),_:1})]),t("div",Z,[a(w,{size:16},{default:c(()=>[a(N,{type:"primary",class:"border-radius",onClick:S},{default:c(()=>e[3]||(e[3]=[E("\u6279\u91CF\u5BA1\u6279")])),_:1,__:[3]})]),_:1})])])]),t("div",ee,[t("div",te,[a(z,{columns:k.value,where:v.value,rowId:"tenantId",ref_key:"tableRef",ref:i,url:"/tenant/page"},{bodyCell:c(({column:n,record:d})=>[n.dataIndex=="tenantName"?(l(),f("a",{key:0,onClick:y=>I(d)},A(d.tenantName),9,ne)):u("",!0),n.dataIndex=="tenantLogoWrapper"?(l(),f("img",{key:1,src:d.tenantLogoWrapper,alt:"",class:"appIconWrapper"},null,8,ae)):u("",!0),n.key=="action"?(l(),x(w,{key:2,size:16},{default:c(()=>[a(h,{iconClass:"icon-opt-shenpirenshezhi","font-size":"24px",title:"\u5BA1\u6279",color:"#60666b",onClick:y=>T(d)},null,8,["onClick"]),a(h,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:y=>C(d)},null,8,["onClick"])]),_:2},1024)):u("",!0)]),_:1},8,["columns","where"])])])])])])]),r.value?(l(),x(P,{key:0,visible:r.value,"onUpdate:visible":e[1]||(e[1]=n=>r.value=n),approverList:m.value,onDone:_},null,8,["visible","approverList"])):u("",!0),p.value?(l(),x(F,{key:1,visible:p.value,"onUpdate:visible":e[2]||(e[2]=n=>p.value=n),data:g.value,onDone:_},null,8,["visible","data"])):u("",!0)])}}}),we=D(oe,[["__scopeId","data-v-60537850"]]);export{we as default};
