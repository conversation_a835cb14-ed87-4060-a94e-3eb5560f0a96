package cn.stylefeng.roses.kernel.pay.service;

import cn.stylefeng.roses.kernel.pay.api.entity.Goods;
import cn.stylefeng.roses.kernel.pay.pojo.request.GoodsRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商品信息服务类
 *
 * <AUTHOR>
 * @since 2024/05/25 23:47
 */
public interface GoodsService extends IService<Goods> {

    /**
     * 查询详情商品信息
     *
     * @param goodsRequest 请求参数
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    Goods detail(GoodsRequest goodsRequest);

    /**
     * 获取商品信息列表
     *
     * @param goodsRequest 请求参数
     * @return List<Goods>  返回结果
     * <AUTHOR>
     * @since 2024/05/25 23:47
     */
    List<Goods> findList(GoodsRequest goodsRequest);

}
