package cn.stylefeng.roses.ent.saas.modular.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantLink;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.PackageAuthInfo;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantLinkRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantLinkService;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.ent.saas.modular.manager.entity.Tenant;
import cn.stylefeng.roses.ent.saas.modular.manager.enums.TenantExceptionEnum;
import cn.stylefeng.roses.ent.saas.modular.manager.factory.TenantAuthLinkFactory;
import cn.stylefeng.roses.ent.saas.modular.manager.factory.TenantFactory;
import cn.stylefeng.roses.ent.saas.modular.manager.factory.TenantLinkFactory;
import cn.stylefeng.roses.ent.saas.modular.manager.mapper.TenantMapper;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.dto.MenuAndOptionsDTO;
import cn.stylefeng.roses.ent.saas.modular.manager.pojo.request.TenantRequest;
import cn.stylefeng.roses.ent.saas.modular.manager.service.TenantService;
import cn.stylefeng.roses.kernel.auth.api.TenantCodeGetApi;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantIdHolder;
import cn.stylefeng.roses.kernel.db.mp.tenant.holder.TenantSwitchHolder;
import cn.stylefeng.roses.kernel.rule.enums.StatusEnum;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import cn.stylefeng.roses.kernel.sys.api.enums.role.RoleTypeEnum;
import cn.stylefeng.roses.kernel.sys.api.expander.TenantConfigExpander;
import cn.stylefeng.roses.kernel.sys.modular.org.entity.HrOrganization;
import cn.stylefeng.roses.kernel.sys.modular.org.service.HrOrganizationService;
import cn.stylefeng.roses.kernel.sys.modular.position.entity.HrPosition;
import cn.stylefeng.roses.kernel.sys.modular.position.service.HrPositionService;
import cn.stylefeng.roses.kernel.sys.modular.role.entity.SysRole;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleLimitService;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleMenuOptionsService;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleMenuService;
import cn.stylefeng.roses.kernel.sys.modular.role.service.SysRoleService;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserOrg;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUserRole;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserOrgService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserRoleService;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 租户信息业务实现层
 *
 * <AUTHOR>
 * @date 2023/08/30 17:04
 */
@Service
public class TenantServiceImpl extends ServiceImpl<TenantMapper, Tenant> implements TenantService, TenantCodeGetApi {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private HrOrganizationService hrOrganizationService;

    @Resource
    private HrPositionService hrPositionService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysUserRoleService sysUserRoleService;

    @Resource
    private SysUserOrgService sysUserOrgService;

    @Resource
    private SysRoleMenuService sysRoleMenuService;

    @Resource
    private SysRoleMenuOptionsService sysRoleMenuOptionsService;

    @Resource
    private SysRoleLimitService roleLimitService;

    @Resource
    private TenantLinkService tenantLinkService;

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(TenantRequest tenantRequest) {
        Tenant tenant = new Tenant();
        BeanUtil.copyProperties(tenantRequest, tenant);

        // 手动新增租户时候，默认设置是激活的
        tenant.setActiveFlag(YesOrNotEnum.Y.getCode());

        // 保存租户信息
        this.save(tenant);

        // 初始化租户
        this.initTenant(tenant.getTenantId(), tenantRequest, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(TenantRequest tenantRequest) {

        // 不能删除根租户
        if (TenantConfigExpander.getDefaultRootTenantId().equals(tenantRequest.getTenantId())) {
            throw new ServiceException(TenantExceptionEnum.CANNOT_DELETE_ROOT_TENANT);
        }

        Tenant tenant = this.queryTenant(tenantRequest);
        this.removeById(tenant.getTenantId());

        // 删除租户关联的功能包信息
        tenantLinkService.removeByTenantId(tenant.getTenantId());

        try {
            // 设定租户id为指定租户id
            TenantIdHolder.set(tenant.getTenantId());

            // 删除租户的关联的业务信息
            this.deleteTenantTotal(tenant.getTenantId());

        } finally {
            // 移除tenantId临时缓存
            TenantIdHolder.remove();
        }
    }

    @Override
    public void edit(TenantRequest tenantRequest) {

        // 不能修改根租户
        if (TenantConfigExpander.getDefaultRootTenantId().equals(tenantRequest.getTenantId())) {
            throw new ServiceException(TenantExceptionEnum.CANNOT_EDIT_ROOT_TENANT);
        }

        // 不能修改租户编码
        tenantRequest.setTenantCode(null);

        Tenant tenant = this.queryTenant(tenantRequest);
        BeanUtil.copyProperties(tenantRequest, tenant);
        this.updateById(tenant);

        List<TenantLink> tenantPackageLink = TenantAuthLinkFactory.createTenantPackageLink(tenant.getTenantId(), tenantRequest);

        if (ObjectUtil.isNotEmpty(tenantPackageLink)) {

            // 修改租户的功能包绑定
            this.tenantLinkService.updateTenantAuthPackageLink(tenantRequest.getTenantId(), tenantPackageLink);

            // 更新租户的相关角色的绑定的权限
            this.tenantPackageAuthService.updateTenantPackageRole(tenantRequest.getTenantId(), tenantPackageLink.stream().map(TenantLink::getPackageId).collect(Collectors.toList()));
        }
    }

    @Override
    public Tenant detail(TenantRequest tenantRequest) {

        LambdaQueryWrapper<Tenant> tenantLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tenantLambdaQueryWrapper.eq(Tenant::getTenantId, tenantRequest.getTenantId());
        tenantLambdaQueryWrapper.select(Tenant::getTenantId, Tenant::getTenantName, Tenant::getTenantCode, Tenant::getEmail, Tenant::getSafePhone, Tenant::getActiveDate, Tenant::getExpireDate,
                BaseEntity::getCreateTime, BaseEntity::getUpdateTime, Tenant::getTenantLogo, Tenant::getStatusFlag, Tenant::getCompanyName, Tenant::getCompanyAddress, Tenant::getCompanySocialCode);
        Tenant tenant = this.getOne(tenantLambdaQueryWrapper, false);

        try {
            // 设置临时租户
            TenantIdHolder.set(tenant.getTenantId());

            // 携带租户的统计信息
            Long userCount = sysUserService.count();
            tenant.setTenantUserCount(userCount);

            // 鞋带租户的组织机构统计信息
            long orgCount = hrOrganizationService.count();
            tenant.setTenantOrgCount(orgCount);
        } finally {
            // 移除临时租户
            TenantIdHolder.remove();
        }

        // 设置租户的功能包列表
        TenantLinkRequest tenantLinkRequest = new TenantLinkRequest();
        tenantLinkRequest.setTenantId(tenant.getTenantId());
        List<TenantLink> list = tenantLinkService.findList(tenantLinkRequest);
        tenant.setTenantLinkList(list);

        return tenant;
    }

    @Override
    public PageResult<Tenant> findPage(TenantRequest tenantRequest) {
        LambdaQueryWrapper<Tenant> wrapper = createWrapper(tenantRequest);

        // 只查询列表需要的字段
        wrapper.select(Tenant::getTenantId, Tenant::getTenantName, Tenant::getTenantCode, Tenant::getTenantLogo, Tenant::getCompanyName, Tenant::getEmail, Tenant::getSafePhone, Tenant::getStatusFlag,
                Tenant::getActiveDate, Tenant::getExpireDate, BaseEntity::getCreateTime);

        Page<Tenant> sysRolePage = this.page(PageFactory.defaultPage(), wrapper);
        return PageResultFactory.createPageResult(sysRolePage);
    }

    @Override
    public boolean checkTenantCodeAlreadyExisted(TenantRequest tenantRequest) {
        LambdaQueryWrapper<Tenant> tenantLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tenantLambdaQueryWrapper.eq(Tenant::getTenantCode, tenantRequest.getTenantCode());

        if (ObjectUtil.isNotEmpty(tenantRequest.getTenantId())) {
            tenantLambdaQueryWrapper.ne(Tenant::getTenantId, tenantRequest.getTenantId());
        }

        return this.count(tenantLambdaQueryWrapper) > 0;
    }

    @Override
    public void initTenant(Long tenantId, TenantRequest tenantRequest, String calculatedPassword, String calculatedSalt) {

        try {
            // 设定租户id为新生成的租户id
            TenantIdHolder.set(tenantId);

            // 初始化租户
            initTenantPure(tenantId, tenantRequest, calculatedPassword, calculatedSalt);

        } finally {
            // 移除tenantId临时缓存
            TenantIdHolder.remove();
        }
    }

    @Override
    public Set<Long> getTenantAdminRoleId(Long tenantId) {

        LambdaQueryWrapper<SysRole> sysRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysRoleLambdaQueryWrapper.eq(SysRole::getRoleType, RoleTypeEnum.SYSTEM_ROLE.getCode());
        sysRoleLambdaQueryWrapper.eq(SysRole::getStatusFlag, StatusEnum.ENABLE.getCode());
        sysRoleLambdaQueryWrapper.eq(SysRole::getTenantId, tenantId);

        try {
            // 临时关闭租户
            TenantSwitchHolder.set(false);

            List<SysRole> list = sysRoleService.list(sysRoleLambdaQueryWrapper);

            if (ObjectUtil.isEmpty(list)) {
                return new HashSet<>();
            } else {
                return list.stream().map(SysRole::getRoleId).collect(Collectors.toSet());
            }
        } finally {
            TenantSwitchHolder.remove();
        }
    }

    @Override
    public List<Tenant> findList(TenantRequest tenantRequest) {
        LambdaQueryWrapper<Tenant> wrapper = this.createWrapper(tenantRequest);

        // 只查询编码和名称
        wrapper.select(Tenant::getTenantId, Tenant::getTenantCode, Tenant::getTenantName);

        return this.list(wrapper);
    }

    @Override
    public Long getTenantIdByCode(String tenantCode) {

        // 如果租户编码为空，那么返回默认的租户id
        if (ObjectUtil.isEmpty(tenantCode)) {
            return TenantConfigExpander.getDefaultRootTenantId();
        }

        // 查询租户id，这里只查询启用并且激活的租户，禁用租户的返回租户不存在
        LambdaQueryWrapper<Tenant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tenant::getTenantCode, tenantCode);
        queryWrapper.eq(Tenant::getStatusFlag, StatusEnum.ENABLE.getCode());
        queryWrapper.eq(Tenant::getActiveFlag, YesOrNotEnum.Y.getCode());
        queryWrapper.select(Tenant::getTenantId);
        Tenant tenant = this.getOne(queryWrapper);

        if (tenant != null) {
            return tenant.getTenantId();
        }

        throw new ServiceException(TenantExceptionEnum.TENANT_EXISTED);
    }

    /**
     * 获取信息
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    private Tenant queryTenant(TenantRequest tenantRequest) {
        Tenant tenant = this.getById(tenantRequest.getTenantId());
        if (ObjectUtil.isEmpty(tenant)) {
            throw new ServiceException(TenantExceptionEnum.TENANT_NOT_EXISTED);
        }
        return tenant;
    }

    /**
     * 创建查询wrapper
     *
     * <AUTHOR>
     * @date 2023/08/30 17:04
     */
    private LambdaQueryWrapper<Tenant> createWrapper(TenantRequest tenantRequest) {
        LambdaQueryWrapper<Tenant> queryWrapper = new LambdaQueryWrapper<>();

        // 如果没传递激活状态，默认查询激活的
        if (ObjectUtil.isEmpty(tenantRequest.getActiveFlag())) {
            queryWrapper.eq(Tenant::getActiveFlag, YesOrNotEnum.Y.getCode());
        } else {
            queryWrapper.eq(Tenant::getActiveFlag, tenantRequest.getActiveFlag());
        }

        // 根据租户编码，租户名称，公司名称模糊查询
        String searchText = tenantRequest.getSearchText();
        if (ObjectUtil.isNotEmpty(searchText)) {
            queryWrapper.nested(i -> {
                i.like(Tenant::getTenantCode, searchText).or().like(Tenant::getTenantName, searchText).or().like(Tenant::getCompanyName, searchText);
            });
        }

        return queryWrapper;
    }

    /**
     * 删除租户信息
     *
     * <AUTHOR>
     * @since 2024/1/21 21:16
     */
    private void deleteTenantTotal(Long tenantId) {

        // 删除租户下的用户信息
        this.sysUserService.remove(new LambdaQueryWrapper<>());

        // 删除租户下的组织机构信息
        this.hrOrganizationService.remove(new LambdaQueryWrapper<>());

        // 删除租户下的职务信息
        this.hrPositionService.remove(new LambdaQueryWrapper<>());

        // 删除租户下的角色信息
        this.sysRoleService.remove(new LambdaQueryWrapper<>());
    }

    /**
     * 初始化租户，一般在此方法外边套一层修改租户id的方法
     *
     * <AUTHOR>
     * @since 2024-02-23 13:57
     */
    private void initTenantPure(Long tenantId, TenantRequest tenantRequest, String calculatedPassword, String calculatedSalt) {
        // 初始化租户的管理员用户
        SysUser tenantAdminUser = TenantFactory.createTenantAdminUser(tenantRequest, calculatedPassword, calculatedSalt);

        // 初始化租户的总公司
        HrOrganization tenantHrOrganization = TenantFactory.createTenantHrOrganization(tenantRequest);

        // 初始化租户的默认职务
        HrPosition tenantPosition = TenantFactory.createTenantPosition();

        // 设置用户关联公司信息
        SysUserOrg sysUserOrg = TenantLinkFactory.createUserOrgLink(tenantAdminUser, tenantHrOrganization, tenantPosition);

        // 初始化租户的角色
        SysRole tenantRole = TenantFactory.createTenantRole(tenantRequest, tenantHrOrganization);
        SysRole baseTenantRole = TenantFactory.createBaseTenantRole(tenantRequest, tenantHrOrganization);

        // 创建用户和角色的关联信息
        SysUserRole userRoleLink = TenantLinkFactory.createUserRoleLink(tenantAdminUser, tenantRole);

        // 初始化租户和功能的绑定
        List<TenantLink> tenantPackageLink = TenantAuthLinkFactory.createTenantPackageLink(tenantId, tenantRequest);

        // 保存租户的超级用户，租户的根组织机构，租户的职务信息和用户和机构关联
        this.sysUserService.save(tenantAdminUser);
        this.hrOrganizationService.save(tenantHrOrganization);
        this.hrPositionService.save(tenantPosition);
        this.sysUserOrgService.save(sysUserOrg);

        // 保存租住的超级角色和用户角色的关联
        this.sysRoleService.save(tenantRole);
        this.sysRoleService.save(baseTenantRole);
        this.sysUserRoleService.save(userRoleLink);

        // 保存租户的功能包关联信息
        if (ObjectUtil.isNotEmpty(tenantPackageLink)) {
            this.tenantLinkService.updateTenantAuthPackageLink(tenantId, tenantPackageLink);

            // 获取功能包对应的菜单id和菜单功能id
            List<Long> packageIdList = tenantPackageLink.stream().map(TenantLink::getPackageId).collect(Collectors.toList());
            PackageAuthInfo packageAuthInfo = this.tenantPackageAuthService.getPackageAuthInfo(packageIdList);

            // 创建角色绑定的菜单和菜单功能，同时，限制角色的权限范围
            MenuAndOptionsDTO tenantMenuAndOptions = TenantLinkFactory.getTenantMenuAndOptions(tenantRole.getRoleId(), packageAuthInfo);
            if (ObjectUtil.isNotEmpty(tenantMenuAndOptions.getSysRoleMenuList())) {
                this.sysRoleMenuService.saveBatch(tenantMenuAndOptions.getSysRoleMenuList());
            }
            if (ObjectUtil.isNotEmpty(tenantMenuAndOptions.getSysRoleMenuOptionsList())) {
                this.sysRoleMenuOptionsService.saveBatch(tenantMenuAndOptions.getSysRoleMenuOptionsList());
            }
            if (ObjectUtil.isNotEmpty(tenantMenuAndOptions.getRoleLimitList())) {
                this.roleLimitService.saveBatch(tenantMenuAndOptions.getRoleLimitList());
            }
        }
    }

}