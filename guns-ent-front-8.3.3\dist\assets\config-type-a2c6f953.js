import{_ as $,r as a,o as j,k as V,a as h,c as L,b as s,d as n,w as d,aR as g,t as O,aM as C,aS as x,f as z,h as A,M as F,E as P,m as R,I as q,l as G,n as H,bg as J,a5 as Q,S as W}from"./index-18a1ea24.js";/* empty css              */import{S as k,_ as X}from"./config-type-add-edit-4711a1ae.js";import"./config-type-form-be70389a.js";/* empty css              */const Y={class:"box bgColor box-shadow"},Z={class:"left-header"},ee={class:"search"},te={class:"tree-content"},ne={class:"left-tree"},se={class:"tree-edit"},oe=["title"],ae={class:"edit-icon"},le={__name:"config-type",emits:["treeSelect","defaultSelect"],setup(ie,{expose:S,emit:b}){const v=b,u=a(""),c=a(!1),l=a([]),p=a([]),m=a([]),r=a(!1),f=a(null);j(()=>{i(!0)});const i=(o=!1)=>{c.value=!0,k.list({searchText:u.value}).then(e=>{l.value=e,o&&e&&e.length>0&&(p.value=[e[0].code],v("defaultSelect",e[0].code))}).finally(()=>c.value=!1)},w=(o,e)=>{v("treeSelect",o,e)},K=()=>{u.value||i()},y=o=>{f.value=o,r.value=!0},T=o=>{F.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u5417?",icon:n(P),maskClosable:!0,onOk:()=>{c.value=!0,k.delete({configTypeId:o.id}).then(e=>{R.success(e.message),i()}).finally(()=>c.value=!1)}})};return S({currentSelectKeys:p,getTreeData:i}),(o,e)=>{const E=V("plus-outlined"),_=q,D=G,I=H,M=J,N=Q,B=W;return h(),L("div",Y,[s("div",Z,[e[5]||(e[5]=s("span",{class:"left-header-title"},"\u914D\u7F6E\u5206\u7C7B",-1)),s("span",null,[n(E,{class:"header-add",onClick:e[0]||(e[0]=t=>y())})])]),s("div",ee,[n(D,{value:u.value,"onUpdate:value":e[1]||(e[1]=t=>u.value=t),placeholder:"\u8BF7\u8F93\u5165\u914D\u7F6E\u540D\u79F0\uFF0C\u56DE\u8F66\u641C\u7D22","allow-clear":"",onPressEnter:i,onChange:K},{prefix:d(()=>[n(_,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),s("div",te,[n(B,{tip:"Loading...",spinning:c.value,delay:100},{default:d(()=>[g(s("div",ne,[n(M,{"show-icon":!0,selectedKeys:p.value,"onUpdate:selectedKeys":e[2]||(e[2]=t=>p.value=t),expandedKeys:m.value,"onUpdate:expandedKeys":e[3]||(e[3]=t=>m.value=t),onSelect:w,"tree-data":l.value,fieldNames:{children:"children",title:"name",key:"code",value:"code"}},{icon:d(()=>[n(_,{iconClass:"icon-tree-wenjianjia",color:"#43505e",fontSize:"24px"})]),title:d(t=>[s("span",se,[s("span",{class:"edit-title",title:t.name},O(t.name),9,oe),s("span",ae,[n(I,null,{default:d(()=>[n(_,{iconClass:"icon-opt-bianji",color:"var(--primary-color)",onClick:C(U=>y(t),["stop"])},null,8,["onClick"]),n(_,{iconClass:"icon-opt-shanchu",color:"red",onClick:C(U=>T(t),["stop"])},null,8,["onClick"])]),_:2},1024)])])]),_:1},8,["selectedKeys","expandedKeys","tree-data"])],512),[[x,l.value&&l.value.length>0]]),g(n(N,{class:"empty"},null,512),[[x,l.value&&l.value.length==0]])]),_:1},8,["spinning"])]),r.value?(h(),z(X,{key:0,visible:r.value,"onUpdate:visible":e[4]||(e[4]=t=>r.value=t),data:f.value,onDone:i},null,8,["visible","data"])):A("",!0)])}}},_e=$(le,[["__scopeId","data-v-32175c5f"]]);export{_e as default};
