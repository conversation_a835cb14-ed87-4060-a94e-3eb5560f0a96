import{r as c,bh as F,bi as w,o as x,k as O,a as m,f as i,w as n,d as o,c as V,F as $,e as j,g as E,h as P,m as _,J as R,u as z,v as G,l as H,V as J,B as M,bk as S,G as X,H as Y}from"./index-18a1ea24.js";/* empty css              */import{T as q}from"./ThemeTemplateApi-ac0cf8e8.js";import{F as D}from"./FileApi-418f4d35.js";const ee={__name:"manager-form",props:{form:Object,rules:Object,disabledChangeTemplate:Boolean,tempFileList:Object,templateFields:Array},emits:["getThemeAttributes"],setup(a,{emit:g}){const u=a,b=g,r=c([]),h=c("".concat(F).concat(D,"?secretFlag=N")),v=c({Authorization:w()});x(()=>{C()});const C=async()=>{let l=await q.findList();r.value=[];for(let t of l)t.statusFlag==="Y"&&r.value.push({value:t.templateId,label:t.templateName})},T=(l,t)=>{l.file.status==="done"?(u.tempFileList[t]=[l.file],_.success("".concat(l.file.name," \u56FE\u7247\u4E0A\u4F20\u6210\u529F")),u.form[t]=l.file.response.data.fileId):l.file.status==="error"&&_.error("".concat(l.file.name," \u56FE\u7247\u4E0A\u4F20\u5931\u8D25"))},U=l=>{b("getThemeAttributes",l)};return(l,t)=>{const y=R,d=z,f=G,p=H,k=J,L=O("CloudUploadOutlined"),N=M,A=S,I=X,B=Y;return m(),i(B,{ref:"formRef",model:a.form,rules:u.rules,layout:"vertical"},{default:n(()=>[o(I,{gutter:20},{default:n(()=>[o(f,{span:24},{default:n(()=>[o(d,{label:"\u4E3B\u9898\u6A21\u677F",name:"templateId"},{default:n(()=>[o(y,{disabled:u.disabledChangeTemplate,value:a.form.templateId,"onUpdate:value":t[0]||(t[0]=e=>a.form.templateId=e),placeholder:"\u8BF7\u9009\u62E9\u4E3B\u9898\u6A21\u677F",options:r.value,onChange:U},null,8,["disabled","value","options"])]),_:1})]),_:1}),o(f,{span:24},{default:n(()=>[o(d,{label:"\u4E3B\u9898\u540D\u79F0:",name:"themeName"},{default:n(()=>[o(p,{value:a.form.themeName,"onUpdate:value":t[1]||(t[1]=e=>a.form.themeName=e),placeholder:"\u8BF7\u8F93\u5165\u4E3B\u9898\u540D\u79F0","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),o(k,{style:{"padding-bottom":"24px"}}),(m(!0),V($,null,j(a.templateFields,e=>(m(),i(f,{span:24,key:e},{default:n(()=>[o(d,{label:e.fieldName,name:e.fieldCode},{default:n(()=>[e.fieldType==="string"?(m(),i(p,{key:0,value:a.form[e.fieldCode],"onUpdate:value":s=>a.form[e.fieldCode]=s},null,8,["value","onUpdate:value"])):e.fieldType==="file"?(m(),i(A,{key:1,name:"file",multiple:!1,action:h.value,headers:v.value,"list-type":"picture","file-list":a.tempFileList[e.fieldCode],"onUpdate:fileList":s=>a.tempFileList[e.fieldCode]=s,onChange:s=>T(s,e.fieldCode)},{default:n(()=>[o(N,{class:"border-radius"},{default:n(()=>[o(L),t[2]||(t[2]=E(" \u4E0A\u4F20\u56FE\u7247 "))]),_:1,__:[2]})]),_:2},1032,["action","headers","file-list","onUpdate:fileList","onChange"])):P("",!0)]),_:2},1032,["label","name"])]),_:2},1024))),128))]),_:1})]),_:1},8,["model","rules"])}}};export{ee as default};
