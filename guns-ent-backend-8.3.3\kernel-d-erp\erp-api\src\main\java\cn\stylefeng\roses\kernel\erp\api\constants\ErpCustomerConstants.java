package cn.stylefeng.roses.kernel.erp.api.constants;

/**
 * 客户模块常量
 *
 * <AUTHOR>
 * @since 2025/07/20 12:00
 */
public interface ErpCustomerConstants {

    /**
     * 客户模块名称
     */
    String CUSTOMER_MODULE_NAME = "erp-customer";

    /**
     * 客户模块前缀
     */
    String CUSTOMER_MODULE_PREFIX = "ERP_CUSTOMER";

    /**
     * 客户类型：企业
     */
    String CUSTOMER_TYPE_ENTERPRISE = "ENTERPRISE";

    /**
     * 客户类型：个人
     */
    String CUSTOMER_TYPE_INDIVIDUAL = "INDIVIDUAL";

    /**
     * 客户类型：零售
     */
    String CUSTOMER_TYPE_RETAIL = "RETAIL";

    /**
     * 客户等级：钻石
     */
    String CUSTOMER_LEVEL_DIAMOND = "DIAMOND";

    /**
     * 客户等级：黄金
     */
    String CUSTOMER_LEVEL_GOLD = "GOLD";

    /**
     * 客户等级：白银
     */
    String CUSTOMER_LEVEL_SILVER = "SILVER";

    /**
     * 客户等级：青铜
     */
    String CUSTOMER_LEVEL_BRONZE = "BRONZE";

    /**
     * 客户状态：正常
     */
    String CUSTOMER_STATUS_ACTIVE = "ACTIVE";

    /**
     * 客户状态：停用
     */
    String CUSTOMER_STATUS_INACTIVE = "INACTIVE";

    /**
     * 客户状态：冻结
     */
    String CUSTOMER_STATUS_FROZEN = "FROZEN";

    /**
     * 默认客户类型
     */
    String DEFAULT_CUSTOMER_TYPE = CUSTOMER_TYPE_ENTERPRISE;

    /**
     * 默认客户等级
     */
    String DEFAULT_CUSTOMER_LEVEL = CUSTOMER_LEVEL_BRONZE;

    /**
     * 默认客户状态
     */
    String DEFAULT_CUSTOMER_STATUS = CUSTOMER_STATUS_ACTIVE;

    /**
     * 默认账期天数
     */
    Integer DEFAULT_PAYMENT_TERMS = 30;

}
