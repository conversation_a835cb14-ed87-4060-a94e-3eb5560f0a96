/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50722
 Source Host           : localhost:3306
 Source Schema         : guns

 Target Server Type    : MySQL
 Target Server Version : 50722
 File Encoding         : 65001

 Date: 24/07/2025 16:16:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ent_api_client
-- ----------------------------
DROP TABLE IF EXISTS `ent_api_client`;
CREATE TABLE `ent_api_client`  (
  `api_client_id` bigint(20) NOT NULL COMMENT 'API客户端ID',
  `api_client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API客户端名称',
  `api_client_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API客户端编号',
  `api_client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API客户端秘钥明文，用来获取API调用的token',
  `api_public_key` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公钥，加密数据用',
  `api_private_key` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '私钥，解密数据用',
  `api_client_token_expiration` int(11) NOT NULL COMMENT 'token过期时间，单位：秒',
  `api_client_sort` decimal(10, 2) NULL DEFAULT NULL COMMENT '排序',
  `api_client_status` tinyint(4) NOT NULL COMMENT '状态：1-启用，2-禁用',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '删除标记：Y-已删除，N-未删除',
  PRIMARY KEY (`api_client_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API客户端' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ent_api_client_auth
-- ----------------------------
DROP TABLE IF EXISTS `ent_api_client_auth`;
CREATE TABLE `ent_api_client_auth`  (
  `api_client_resource_id` bigint(20) NOT NULL COMMENT '主键id',
  `api_client_id` bigint(20) NOT NULL COMMENT 'api客户端id',
  `resource_code` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源编码，与sys_resource表编码对应',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`api_client_resource_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API客户端和资源绑定关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ent_api_endpoint
-- ----------------------------
DROP TABLE IF EXISTS `ent_api_endpoint`;
CREATE TABLE `ent_api_endpoint`  (
  `api_client_resource_id` bigint(20) NOT NULL COMMENT '主键id',
  `resource_code` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源编码，与sys_resource表编码对应',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`api_client_resource_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'API资源接口列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ent_sso_client
-- ----------------------------
DROP TABLE IF EXISTS `ent_sso_client`;
CREATE TABLE `ent_sso_client`  (
  `client_id` bigint(20) NOT NULL COMMENT '业务应用的id',
  `client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `client_logo_file_id` bigint(20) NULL DEFAULT NULL COMMENT '应用图标的文件id',
  `login_page_type` tinyint(4) NOT NULL DEFAULT 2 COMMENT '登录地址的类型：1-应用自定义登录界面，2-使用CA服务统一登录界面',
  `unified_logout_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Y' COMMENT '是否统一退出：Y-是，N-否',
  `sso_callback_url` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回调业务地址，单点登录到业务端时，跳转到业务端的地址',
  `sso_logout_url` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退出地址，从认证中心退出后，通知业务端的地址',
  `custom_login_url` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用登录的地址，针对自定义登录界面',
  `ca_token_secret` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加密和解密的密钥，针对单点到业务系统的token（对称加密）',
  `client_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `client_sort` decimal(11, 2) NULL DEFAULT 999.00 COMMENT '排序码',
  `client_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用的描述',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除：Y-被删除，N-未删除',
  PRIMARY KEY (`client_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '单点登录客户端' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ent_tenant
-- ----------------------------
DROP TABLE IF EXISTS `ent_tenant`;
CREATE TABLE `ent_tenant`  (
  `tenant_id` bigint(20) NOT NULL COMMENT '主键id',
  `tenant_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户唯一标识',
  `tenant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户名称',
  `tenant_logo` bigint(20) NULL DEFAULT NULL COMMENT '租户logo，存储文件id',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司地址',
  `company_social_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注册邮箱',
  `safe_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '安全手机（注册时的手机号）',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `active_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Y' COMMENT '激活状态：Y-已激活，N-未激活',
  `active_date` datetime(0) NULL DEFAULT NULL COMMENT '租户开通时间',
  `expire_date` datetime(0) NULL DEFAULT NULL COMMENT '租户到期时间',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码，加密方式为MD5',
  `password_salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码盐',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`tenant_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ent_tenant
-- ----------------------------
INSERT INTO `ent_tenant` VALUES (1, 'root', '根管理端', 1479753047148322818, NULL, NULL, NULL, '<EMAIL>', '1001', 1, 'Y', '2023-01-01 00:00:00', '2099-01-01 00:00:00', '', NULL, NULL, 0, 'N', NULL, NULL, NULL, NULL);
INSERT INTO `ent_tenant` VALUES (1943145071222202369, 'z1', '租户1', 1945474278380011521, '租户1', NULL, NULL, '<EMAIL>', '17611112333', 1, 'Y', '2025-07-10 00:00:00', '2025-08-10 00:00:00', '123456', NULL, NULL, 7, 'N', '2025-07-10 11:07:42', 1339550467939639299, '2025-07-16 21:23:10', 1339550467939639299);
INSERT INTO `ent_tenant` VALUES (1945474242082504705, 'z2', '租户2', 1945474215238959105, '租户2', NULL, NULL, '<EMAIL>', '17611112322', 1, 'Y', '2025-07-16 00:00:00', '2025-08-16 00:00:00', '123456', NULL, NULL, 0, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL);

-- ----------------------------
-- Table structure for ent_tenant_link
-- ----------------------------
DROP TABLE IF EXISTS `ent_tenant_link`;
CREATE TABLE `ent_tenant_link`  (
  `tenant_link_id` bigint(20) NOT NULL COMMENT '主键id',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  `package_id` bigint(20) NOT NULL COMMENT '功能包id',
  `service_end_time` datetime(0) NULL DEFAULT NULL COMMENT '服务结束时间，空则是长期',
  `trial_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否为试用：Y-试用，N-非试用',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  PRIMARY KEY (`tenant_link_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户和功能包的关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ent_tenant_link
-- ----------------------------
INSERT INTO `ent_tenant_link` VALUES (1943564670883340290, 1943145071222202369, 1943145305201451010, '2025-07-13 14:54:54', 'N', '2025-07-11 14:55:02', 1339550467939639299, NULL, NULL, 'Y');
INSERT INTO `ent_tenant_link` VALUES (1943568438433697793, 1943145071222202369, 1749698250385616898, NULL, 'Y', '2025-07-11 15:10:00', 1339550467939639299, NULL, NULL, 'Y');
INSERT INTO `ent_tenant_link` VALUES (1943568663302918146, 1943145071222202369, 1749698214931165185, NULL, 'N', '2025-07-11 15:10:54', 1339550467939639299, NULL, NULL, 'Y');
INSERT INTO `ent_tenant_link` VALUES (1945465382076694529, 1943145071222202369, 1749698214931165185, NULL, 'N', '2025-07-16 20:47:47', 1339550467939639299, NULL, NULL, 'Y');
INSERT INTO `ent_tenant_link` VALUES (1945474242338357250, 1945474242082504705, 1749698214931165185, NULL, 'N', '2025-07-16 21:22:59', 1339550467939639299, NULL, NULL, 'N');
INSERT INTO `ent_tenant_link` VALUES (1945474285862649857, 1943145071222202369, 1749698214931165185, NULL, 'N', '2025-07-16 21:23:10', 1339550467939639299, NULL, NULL, 'N');

-- ----------------------------
-- Table structure for ent_tenant_package
-- ----------------------------
DROP TABLE IF EXISTS `ent_tenant_package`;
CREATE TABLE `ent_tenant_package`  (
  `package_id` bigint(20) NOT NULL COMMENT '主键id',
  `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能包名称',
  `package_price` decimal(10, 2) NOT NULL COMMENT '功能包价格',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  PRIMARY KEY (`package_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户-功能包' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ent_tenant_package
-- ----------------------------
INSERT INTO `ent_tenant_package` VALUES (1749698214931165185, '基础系统管理', 0.00, 0, '2024-01-23 15:38:57', 1339550467939639299, NULL, NULL, 'N');
INSERT INTO `ent_tenant_package` VALUES (1749698250385616898, 'API管理', 1000.00, 0, '2024-01-23 15:39:05', 1339550467939639299, NULL, NULL, 'N');
INSERT INTO `ent_tenant_package` VALUES (1943145305201451010, '基础1', 100.00, 0, '2025-07-10 11:08:37', 1339550467939639299, '2025-07-11 15:06:31', 1339550467939639299, 'Y');

-- ----------------------------
-- Table structure for ent_tenant_package_auth
-- ----------------------------
DROP TABLE IF EXISTS `ent_tenant_package_auth`;
CREATE TABLE `ent_tenant_package_auth`  (
  `package_auth_id` bigint(20) NOT NULL COMMENT '主键',
  `package_id` bigint(20) NOT NULL COMMENT '功能包id',
  `limit_type` tinyint(4) NOT NULL COMMENT '功能包绑定权限类型：1-菜单，2-功能',
  `business_id` bigint(20) NOT NULL COMMENT '业务id，为菜单id或菜单功能id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`package_auth_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户-功能包授权范围' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ent_tenant_package_auth
-- ----------------------------
INSERT INTO `ent_tenant_package_auth` VALUES (1749698263543148545, 1749698214931165185, 1, 1671407539607171073, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698263543148546, 1749698214931165185, 2, 1671416717948006401, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698263543148547, 1749698214931165185, 2, 1671416755763851265, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698263543148548, 1749698214931165185, 2, 1677205540070064129, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698263543148550, 1749698214931165185, 2, 1677205870040154114, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698263543148551, 1749698214931165185, 2, 1677205994816503809, '2024-01-23 15:39:09', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698299991650305, 1749698214931165185, 2, 1677205784526684162, '2024-01-23 15:39:17', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698316001308674, 1749698214931165185, 1, 1671407615163363330, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698316001308675, 1749698214931165185, 2, 1675495204221640706, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698316001308676, 1749698214931165185, 2, 1677213466805501954, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698316001308677, 1749698214931165185, 2, 1677213504298385410, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698316001308678, 1749698214931165185, 2, 1677213572741038081, '2024-01-23 15:39:21', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698345969610753, 1749698214931165185, 1, 1671407652933070850, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698345969610754, 1749698214931165185, 2, 1677212372381564929, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698345969610755, 1749698214931165185, 2, 1677212407240425474, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698345969610756, 1749698214931165185, 2, 1677212448021643265, '2024-01-23 15:39:28', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698388319498242, 1749698214931165185, 1, 1671406619464953857, '2024-01-23 15:39:38', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698388319498243, 1749698214931165185, 1, 1671407186899759106, '2024-01-23 15:39:38', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698388319498244, 1749698214931165185, 2, 1677199976008040449, '2024-01-23 15:39:38', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698424969326593, 1749698250385616898, 1, 1718826580437897217, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698424969326594, 1749698250385616898, 1, 1718826709295304705, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698425036435457, 1749698250385616898, 1, 1718826323209621506, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);
INSERT INTO `ent_tenant_package_auth` VALUES (1749698425036435458, 1749698250385616898, 1, 1718824432245411841, '2024-01-23 15:39:47', 1339550467939639299, NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
