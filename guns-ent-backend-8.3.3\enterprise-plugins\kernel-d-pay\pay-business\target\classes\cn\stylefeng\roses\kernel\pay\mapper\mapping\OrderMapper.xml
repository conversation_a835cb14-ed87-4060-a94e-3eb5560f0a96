<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.stylefeng.roses.kernel.pay.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.stylefeng.roses.kernel.pay.api.entity.Order">
		<id column="order_id" property="orderId" />
		<result column="order_number" property="orderNumber" />
		<result column="user_id" property="userId" />
		<result column="goods_id" property="goodsId" />
		<result column="goods_name" property="goodsName" />
		<result column="original_price" property="originalPrice" />
		<result column="pay_price" property="payPrice" />
		<result column="state" property="state" />
		<result column="pay_time" property="payTime" />
		<result column="order_no" property="orderNo" />
		<result column="pay_no" property="payNo" />
		<result column="pay_channel" property="payChannel" />
		<result column="sign" property="sign" />
		<result column="open_id" property="openId" />
		<result column="create_time" property="createTime" />
		<result column="create_user" property="createUser" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
	</resultMap>

	<sql id="Base_Column_List">
		order_id,order_number,user_id,goods_id,goods_name,original_price,pay_price,state,pay_time,order_no,pay_no,pay_channel,sign,open_id,create_time,create_user,update_time,update_user
	</sql>

    <resultMap id="customResultMap" type="cn.stylefeng.roses.kernel.pay.pojo.response.OrderVo" extends="BaseResultMap">
    </resultMap>

	<select id="customFindList" resultMap="customResultMap">
		SELECT
		*
        FROM
		shop_order tbl
		WHERE
		<where>
        <if test="param.orderId != null and param.orderId != ''">
            and tbl.order_id like concat('%',#{param.orderId},'%')
        </if>
        <if test="param.orderNumber != null and param.orderNumber != ''">
            and tbl.order_number like concat('%',#{param.orderNumber},'%')
        </if>
        <if test="param.userId != null and param.userId != ''">
            and tbl.user_id like concat('%',#{param.userId},'%')
        </if>
        <if test="param.goodsId != null and param.goodsId != ''">
            and tbl.goods_id like concat('%',#{param.goodsId},'%')
        </if>
        <if test="param.goodsName != null and param.goodsName != ''">
            and tbl.goods_name like concat('%',#{param.goodsName},'%')
        </if>
        <if test="param.originalPrice != null and param.originalPrice != ''">
            and tbl.original_price like concat('%',#{param.originalPrice},'%')
        </if>
        <if test="param.payPrice != null and param.payPrice != ''">
            and tbl.pay_price like concat('%',#{param.payPrice},'%')
        </if>
        <if test="param.state != null and param.state != ''">
            and tbl.state like concat('%',#{param.state},'%')
        </if>
        <if test="param.payTime != null and param.payTime != ''">
            and tbl.pay_time like concat('%',#{param.payTime},'%')
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and tbl.order_no like concat('%',#{param.orderNo},'%')
        </if>
        <if test="param.payNo != null and param.payNo != ''">
            and tbl.pay_no like concat('%',#{param.payNo},'%')
        </if>
        <if test="param.payChannel != null and param.payChannel != ''">
            and tbl.pay_channel like concat('%',#{param.payChannel},'%')
        </if>
        <if test="param.sign != null and param.sign != ''">
            and tbl.sign like concat('%',#{param.sign},'%')
        </if>
        <if test="param.openId != null and param.openId != ''">
            and tbl.open_id like concat('%',#{param.openId},'%')
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            and tbl.create_time like concat('%',#{param.createTime},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            and tbl.create_user like concat('%',#{param.createUser},'%')
        </if>
        <if test="param.updateTime != null and param.updateTime != ''">
            and tbl.update_time like concat('%',#{param.updateTime},'%')
        </if>
        <if test="param.updateUser != null and param.updateUser != ''">
            and tbl.update_user like concat('%',#{param.updateUser},'%')
        </if>
		</where>
	</select>

</mapper>
