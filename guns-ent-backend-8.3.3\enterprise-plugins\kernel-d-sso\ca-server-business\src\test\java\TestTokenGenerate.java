import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

import java.util.Date;

public class TestTokenGenerate {

    public static void main(String[] args) {

        // 公钥（API应用后台管理界面生成）
        String publicSecret = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDatI4d7mKvqzbKU4hveGoddEsb6M0697IuRlV9c2D01cHPvWMKHRmJcEZZv0g9hnPxi23E6LKnOoZdrBVkAHMDz3fTsT3R6nA68jmV1zPZPt3VRVfPzr2o6g16sBzDZJkUwyFxcoHul6YyAincd2NFWNGkt6aRC6+ilLH3t/aO2wIDAQAB";

        // API应用秘钥（API应用后台管理界面生成）
        String appSecret = "2ze2o13F7JkuEwySXRbb4AZhJb7S4zXY";

        // 获取当前时间
        String dateTime = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        System.out.println("当前时间为：" + dateTime);

        // 将app秘钥和时间拼接并进行md5加密
        String md5 = SecureUtil.md5(appSecret + dateTime);

        // 生成加密字符串，用来进行生成token
        RSA rsa = new RSA(null, publicSecret);
        String base64 = rsa.encryptBase64(md5, KeyType.PublicKey);
        System.out.println("生成的秘钥参数如下：" + base64);
    }

}
