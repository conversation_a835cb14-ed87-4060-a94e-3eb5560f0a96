CREATE TABLE `sys_invite_user` (
  `invite_user_id` bigint NOT NULL COMMENT '主键',
  `org_id` bigint NOT NULL COMMENT '邀请机构id',
  `from_user_id` bigint NOT NULL COMMENT '来自谁的邀请',
  `real_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '真实姓名',
  `phone_number` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户手机号',
  `phone_validate_number` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号验证码',
  `apply_reason` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请加入理由',
  `invite_status` int DEFAULT NULL COMMENT '审核状态：10-待审核，20-通过，30-拒绝',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否删除：Y-删除，N-未删除',
  PRIMARY KEY (`invite_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='邀请用户';