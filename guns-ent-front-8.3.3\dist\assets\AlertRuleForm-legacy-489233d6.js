System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js"],(function(e,a){"use strict";var l,t,r,o,n,u,d,i,s,m,h,c,p,f,g,v,_,T,y,b,D,E,C,O,L,A;return{setters:[e=>{l=e._,t=e.r,r=e.L,o=e.X,n=e.s,u=e.a,d=e.f,i=e.w,s=e.d,m=e.g,h=e.c,c=e.F,p=e.e,f=e.t,g=e.h,v=e.b,_=e.l,T=e.u,y=e.W,b=e.J,D=e.as,E=e.y,C=e.a9,O=e.$,L=e.H,A=e.M},null,null],execute:function(){var a=document.createElement("style");a.textContent=".ant-form-item-explain[data-v-f1f58ff9]{color:#999;font-size:12px;margin-top:4px}\n",document.head.appendChild(a);const k={name:"AlertRuleForm",props:{visible:{type:Boolean,default:!1},formData:{type:Object,default:()=>({})},productOptions:{type:Array,default:()=>[]},categoryOptions:{type:Array,default:()=>[]}},emits:["update:visible","submit","cancel"],setup(e,{emit:a}){const l=t(),u=t(!1),d=t({id:null,ruleName:"",alertType:"",targetType:"ALL",targetId:null,alertLevel:"WARNING",thresholdType:"QUANTITY",thresholdValue:null,comparisonOperator:"LTE",isEnabled:"Y",notificationMethods:["SYSTEM"],notificationUsers:[],checkFrequency:30,remark:""}),i=r((()=>d.value.id?"编辑预警规则":"新增预警规则"));o((()=>e.formData),(e=>{e?Object.assign(d.value,{id:e.id||null,ruleName:e.ruleName||"",alertType:e.alertType||"",targetType:e.targetType||"ALL",targetId:e.targetId||null,alertLevel:e.alertLevel||"WARNING",thresholdType:e.thresholdType||"QUANTITY",thresholdValue:e.thresholdValue||null,comparisonOperator:e.comparisonOperator||"LTE",isEnabled:e.isEnabled||"Y",notificationMethods:e.notificationMethods||["SYSTEM"],notificationUsers:e.notificationUsers||[],checkFrequency:e.checkFrequency||30,remark:e.remark||""}):Object.assign(d.value,{id:null,ruleName:"",alertType:"",targetType:"ALL",targetId:null,alertLevel:"WARNING",thresholdType:"QUANTITY",thresholdValue:null,comparisonOperator:"LTE",isEnabled:"Y",notificationMethods:["SYSTEM"],notificationUsers:[],checkFrequency:30,remark:""})}),{immediate:!0,deep:!0});const s=n({ruleName:[{required:!0,message:"请输入规则名称",trigger:"blur"},{max:100,message:"规则名称不能超过100个字符",trigger:"blur"}],alertType:[{required:!0,message:"请选择预警类型",trigger:"change"}],targetType:[{required:!0,message:"请选择目标类型",trigger:"change"}],targetId:[{validator:(e,a)=>"ALL"===d.value.targetType||a?Promise.resolve():Promise.reject("请选择目标"),trigger:"change"}],alertLevel:[{required:!0,message:"请选择预警级别",trigger:"change"}],thresholdType:[{required:!0,message:"请选择阈值类型",trigger:"change"}],thresholdValue:[{required:!0,message:"请输入阈值",trigger:"blur"},{type:"number",min:0,message:"阈值不能小于0",trigger:"blur"}],comparisonOperator:[{required:!0,message:"请选择比较操作符",trigger:"change"}],checkFrequency:[{required:!0,message:"请输入检查频率",trigger:"blur"},{type:"number",min:1,max:1440,message:"检查频率必须在1-1440分钟之间",trigger:"blur"}]});return{formRef:l,formData:d,confirmLoading:u,formTitle:i,rules:s,handleSubmit:async()=>{try{await l.value.validate(),u.value=!0,a("submit",d.value)}catch(e){console.error("表单验证失败:",e)}finally{u.value=!1}},handleCancel:()=>{a("update:visible",!1),a("cancel")},handleAlertTypeChange:e=>{"ZERO_STOCK"===e?(d.value.thresholdType="QUANTITY",d.value.thresholdValue=0,d.value.comparisonOperator="EQ"):"EXPIRY"===e&&(d.value.thresholdType="DAYS",d.value.comparisonOperator="LTE")},handleTargetTypeChange:e=>{d.value.targetId=null},handleThresholdTypeChange:e=>{d.value.thresholdValue=null},filterOption:(e,a)=>a.children.toLowerCase().indexOf(e.toLowerCase())>=0}}},I={class:"ant-form-item-explain"},N={key:0},U={key:1},R={key:2};e("default",l(k,[["render",function(e,a,l,t,r,o){const n=_,k=T,Y=y,x=b,q=D,S=E,G=C,F=O,V=L,w=A;return u(),d(w,{title:t.formTitle,visible:l.visible,width:800,"confirm-loading":t.confirmLoading,onOk:t.handleSubmit,onCancel:t.handleCancel},{default:i((()=>[s(V,{ref:"formRef",model:t.formData,rules:t.rules,"label-col":{span:6},"wrapper-col":{span:16}},{default:i((()=>[s(k,{label:"规则名称",name:"ruleName"},{default:i((()=>[s(n,{value:t.formData.ruleName,"onUpdate:value":a[0]||(a[0]=e=>t.formData.ruleName=e),placeholder:"请输入规则名称",maxlength:100},null,8,["value"])])),_:1}),s(k,{label:"预警类型",name:"alertType"},{default:i((()=>[s(x,{value:t.formData.alertType,"onUpdate:value":a[1]||(a[1]=e=>t.formData.alertType=e),placeholder:"请选择预警类型",onChange:t.handleAlertTypeChange},{default:i((()=>[s(Y,{value:"LOW_STOCK"},{default:i((()=>a[12]||(a[12]=[m("库存不足")]))),_:1,__:[12]}),s(Y,{value:"ZERO_STOCK"},{default:i((()=>a[13]||(a[13]=[m("零库存")]))),_:1,__:[13]}),s(Y,{value:"OVERSTOCK"},{default:i((()=>a[14]||(a[14]=[m("库存积压")]))),_:1,__:[14]}),s(Y,{value:"EXPIRY"},{default:i((()=>a[15]||(a[15]=[m("临期预警")]))),_:1,__:[15]})])),_:1},8,["value","onChange"])])),_:1}),s(k,{label:"目标类型",name:"targetType"},{default:i((()=>[s(x,{value:t.formData.targetType,"onUpdate:value":a[2]||(a[2]=e=>t.formData.targetType=e),placeholder:"请选择目标类型",onChange:t.handleTargetTypeChange},{default:i((()=>[s(Y,{value:"ALL"},{default:i((()=>a[16]||(a[16]=[m("全部商品")]))),_:1,__:[16]}),s(Y,{value:"PRODUCT"},{default:i((()=>a[17]||(a[17]=[m("指定商品")]))),_:1,__:[17]}),s(Y,{value:"CATEGORY"},{default:i((()=>a[18]||(a[18]=[m("商品分类")]))),_:1,__:[18]})])),_:1},8,["value","onChange"])])),_:1}),"PRODUCT"===t.formData.targetType?(u(),d(k,{key:0,label:"目标商品",name:"targetId"},{default:i((()=>[s(x,{value:t.formData.targetId,"onUpdate:value":a[3]||(a[3]=e=>t.formData.targetId=e),placeholder:"请选择商品","show-search":"","filter-option":t.filterOption},{default:i((()=>[(u(!0),h(c,null,p(l.productOptions,(e=>(u(),d(Y,{key:e.id,value:e.id},{default:i((()=>[m(f(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","filter-option"])])),_:1})):g("",!0),"CATEGORY"===t.formData.targetType?(u(),d(k,{key:1,label:"目标分类",name:"targetId"},{default:i((()=>[s(q,{value:t.formData.targetId,"onUpdate:value":a[4]||(a[4]=e=>t.formData.targetId=e),placeholder:"请选择商品分类","tree-data":l.categoryOptions,"field-names":{label:"name",value:"id",children:"children"}},null,8,["value","tree-data"])])),_:1})):g("",!0),s(k,{label:"预警级别",name:"alertLevel"},{default:i((()=>[s(x,{value:t.formData.alertLevel,"onUpdate:value":a[5]||(a[5]=e=>t.formData.alertLevel=e),placeholder:"请选择预警级别"},{default:i((()=>[s(Y,{value:"CRITICAL"},{default:i((()=>a[19]||(a[19]=[m("紧急")]))),_:1,__:[19]}),s(Y,{value:"WARNING"},{default:i((()=>a[20]||(a[20]=[m("警告")]))),_:1,__:[20]}),s(Y,{value:"INFO"},{default:i((()=>a[21]||(a[21]=[m("提醒")]))),_:1,__:[21]})])),_:1},8,["value"])])),_:1}),s(k,{label:"阈值类型",name:"thresholdType"},{default:i((()=>[s(x,{value:t.formData.thresholdType,"onUpdate:value":a[6]||(a[6]=e=>t.formData.thresholdType=e),placeholder:"请选择阈值类型",onChange:t.handleThresholdTypeChange},{default:i((()=>[s(Y,{value:"QUANTITY"},{default:i((()=>a[22]||(a[22]=[m("数量")]))),_:1,__:[22]}),s(Y,{value:"PERCENTAGE"},{default:i((()=>a[23]||(a[23]=[m("百分比")]))),_:1,__:[23]}),s(Y,{value:"DAYS"},{default:i((()=>a[24]||(a[24]=[m("天数")]))),_:1,__:[24]})])),_:1},8,["value","onChange"])])),_:1}),s(k,{label:"阈值",name:"thresholdValue"},{default:i((()=>[s(S,{value:t.formData.thresholdValue,"onUpdate:value":a[7]||(a[7]=e=>t.formData.thresholdValue=e),min:0,precision:"PERCENTAGE"===t.formData.thresholdType?2:0,max:"PERCENTAGE"===t.formData.thresholdType?100:void 0,style:{width:"100%"},placeholder:"请输入阈值"},null,8,["value","precision","max"]),v("div",I,["QUANTITY"===t.formData.thresholdType?(u(),h("span",N," 当库存数量小于等于此值时触发预警 ")):"PERCENTAGE"===t.formData.thresholdType?(u(),h("span",U," 当库存数量占最大库存的百分比小于等于此值时触发预警 ")):"DAYS"===t.formData.thresholdType?(u(),h("span",R," 当商品距离过期天数小于等于此值时触发预警 ")):g("",!0)])])),_:1}),s(k,{label:"比较操作符",name:"comparisonOperator"},{default:i((()=>[s(x,{value:t.formData.comparisonOperator,"onUpdate:value":a[8]||(a[8]=e=>t.formData.comparisonOperator=e),placeholder:"请选择比较操作符"},{default:i((()=>[s(Y,{value:"LTE"},{default:i((()=>a[25]||(a[25]=[m("小于等于 (≤)")]))),_:1,__:[25]}),s(Y,{value:"LT"},{default:i((()=>a[26]||(a[26]=[m("小于 (<)")]))),_:1,__:[26]}),s(Y,{value:"GTE"},{default:i((()=>a[27]||(a[27]=[m("大于等于 (≥)")]))),_:1,__:[27]}),s(Y,{value:"GT"},{default:i((()=>a[28]||(a[28]=[m("大于 (>)")]))),_:1,__:[28]}),s(Y,{value:"EQ"},{default:i((()=>a[29]||(a[29]=[m("等于 (=)")]))),_:1,__:[29]})])),_:1},8,["value"])])),_:1}),s(k,{label:"检查频率",name:"checkFrequency"},{default:i((()=>[s(S,{value:t.formData.checkFrequency,"onUpdate:value":a[9]||(a[9]=e=>t.formData.checkFrequency=e),min:1,max:1440,style:{width:"100%"},placeholder:"请输入检查频率（分钟）"},null,8,["value"]),a[30]||(a[30]=v("div",{class:"ant-form-item-explain"}," 检查频率，单位：分钟（1-1440分钟） ",-1))])),_:1,__:[30]}),s(k,{label:"是否启用",name:"isEnabled"},{default:i((()=>[s(G,{checked:t.formData.isEnabled,"onUpdate:checked":a[10]||(a[10]=e=>t.formData.isEnabled=e),"checked-children":"启用","un-checked-children":"禁用"},null,8,["checked"])])),_:1}),s(k,{label:"备注",name:"remark"},{default:i((()=>[s(F,{value:t.formData.remark,"onUpdate:value":a[11]||(a[11]=e=>t.formData.remark=e),placeholder:"请输入备注信息",rows:3,maxlength:500},null,8,["value"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","visible","confirm-loading","onOk","onCancel"])}],["__scopeId","data-v-f1f58ff9"]]))}}}));
