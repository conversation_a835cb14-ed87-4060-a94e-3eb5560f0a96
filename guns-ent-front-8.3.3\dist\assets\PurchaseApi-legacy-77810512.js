System.register(["./index-legacy-ee1db0c7.js"],(function(e,t){"use strict";var r;return{setters:[e=>{r=e.R}],execute:function(){class t{static add(e){return r.post("/purchase/order/add",e)}static delete(e){return r.post("/purchase/order/delete",e)}static edit(e){return r.post("/purchase/order/edit",e)}static detail(e){return r.getAndLoadData("/purchase/order/detail",e)}static findPage(e){return r.getAndLoadData("/purchase/order/page",e)}static findList(e){return r.getAndLoadData("/purchase/order/list",e)}static confirm(e){return r.post("/purchase/order/confirm",e)}static receive(e){return r.post("/purchase/order/receive",e)}static generateOrderNo(){return r.getAndLoadData("/purchase/order/generateOrderNo")}static validateSupplierBusinessMode(e){return r.getAndLoadData("/purchase/order/validateSupplierBusinessMode",e)}static cancel(e){return r.post("/erp/purchase/cancel",e)}static getPurchaseStatusOptions(){return[{label:"草稿",value:"DRAFT"},{label:"已确认",value:"CONFIRMED"},{label:"已完成",value:"COMPLETED"}]}static getPurchaseTypeOptions(){return[{label:"普通采购",value:"NORMAL"},{label:"紧急采购",value:"URGENT"},{label:"补货采购",value:"REPLENISHMENT"}]}static getPurchaseStatusName(e){const r=t.getPurchaseStatusOptions().find((t=>t.value===e));return r?r.label:e}static getPurchaseTypeName(e){const r=t.getPurchaseTypeOptions().find((t=>t.value===e));return r?r.label:e}static getStatusTagColor(e){switch(e){case"DRAFT":return"orange";case"CONFIRMED":return"blue";case"COMPLETED":return"green";default:return"default"}}static getPurchaseTypeTagColor(e){switch(e){case"NORMAL":return"blue";case"URGENT":return"red";case"REPLENISHMENT":return"green";default:return"default"}}static validateConfirm(e){return r.getAndLoadData("/erp/purchase/validateConfirm",e)}static validateInbound(e){return r.getAndLoadData("/erp/purchase/validateInbound",e)}static getPurchaseItems(e){return r.getAndLoadData("/erp/purchase/items",e)}static addPurchaseItem(e){return r.post("/erp/purchase/addItem",e)}static editPurchaseItem(e){return r.post("/erp/purchase/editItem",e)}static deletePurchaseItem(e){return r.post("/erp/purchase/deleteItem",e)}static batchImportItems(e){return r.post("/erp/purchase/batchImportItems",e)}static exportPurchase(e){return r.downLoad("/erp/purchase/export",e)}static getPurchaseStatistics(e){return r.getAndLoadData("/erp/purchase/statistics",e)}static formatAmount(e){return e?parseFloat(e).toFixed(2):"0.00"}static formatQuantity(e,t){return e?`${e}${t||""}`:"-"}static calculateTotalAmount(e){return e&&0!==e.length?e.reduce(((e,t)=>e+t.quantity*t.unitPrice),0):0}static calculateTotalQuantity(e){return e&&0!==e.length?e.reduce(((e,t)=>e+t.quantity),0):0}}e("P",t)}}}));
