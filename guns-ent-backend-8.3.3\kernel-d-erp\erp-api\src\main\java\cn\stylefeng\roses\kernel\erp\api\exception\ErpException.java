package cn.stylefeng.roses.kernel.erp.api.exception;

import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;

/**
 * ERP模块的异常
 *
 * <AUTHOR>
 * @since 2025/08/01 10:00
 */
public class ErpException extends ServiceException {

    public ErpException(AbstractExceptionEnum exception) {
        super(ErpConstants.ERP_MODULE_NAME, exception);
    }

    public ErpException(AbstractExceptionEnum exception, Object... params) {
        super(ErpConstants.ERP_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

}
