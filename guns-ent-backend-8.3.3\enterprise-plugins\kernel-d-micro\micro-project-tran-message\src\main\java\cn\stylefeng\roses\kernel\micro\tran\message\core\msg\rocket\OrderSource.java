package cn.stylefeng.roses.kernel.micro.tran.message.core.msg.rocket;

import org.springframework.messaging.MessageChannel;

/**
 * 用于接收特定分布式事务消息的
 * <p>
 * 每个单独的业务占用一个通道
 *
 * <AUTHOR>
 * @date 2021/5/18 13:51
 */
public interface OrderSource {

    /**
     * Name of the output channel.
     */
    String OUTPUT = "order-output";

    /**
     * @return output channel
     */
    MessageChannel output();

}
