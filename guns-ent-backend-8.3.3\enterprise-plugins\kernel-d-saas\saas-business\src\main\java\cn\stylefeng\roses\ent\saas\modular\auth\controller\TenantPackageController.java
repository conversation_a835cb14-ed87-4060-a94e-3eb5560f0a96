package cn.stylefeng.roses.ent.saas.modular.auth.controller;

import cn.stylefeng.roses.ent.saas.modular.auth.entity.TenantPackage;
import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.TenantPackageRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageService;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 租户-功能包控制器
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@RestController
@ApiResource(name = "租户-功能包")
public class TenantPackageController {

    @Resource
    private TenantPackageService tenantPackageService;

    /**
     * 添加租户-功能包
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    @PostResource(name = "添加租户-功能包", path = "/tenantPackage/add")
    public ResponseData<TenantPackage> add(@RequestBody @Validated(TenantPackageRequest.add.class) TenantPackageRequest tenantPackageRequest) {
        tenantPackageService.add(tenantPackageRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 删除租户-功能包
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    @PostResource(name = "删除租户-功能包", path = "/tenantPackage/delete")
    public ResponseData<?> delete(@RequestBody @Validated(TenantPackageRequest.delete.class) TenantPackageRequest tenantPackageRequest) {
        tenantPackageService.del(tenantPackageRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 编辑租户-功能包
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    @PostResource(name = "编辑租户-功能包", path = "/tenantPackage/edit")
    public ResponseData<?> edit(@RequestBody @Validated(TenantPackageRequest.edit.class) TenantPackageRequest tenantPackageRequest) {
        tenantPackageService.edit(tenantPackageRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 查看租户-功能包详情
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    @GetResource(name = "查看租户-功能包详情", path = "/tenantPackage/detail")
    public ResponseData<TenantPackage> detail(@Validated(TenantPackageRequest.detail.class) TenantPackageRequest tenantPackageRequest) {
        return new SuccessResponseData<>(tenantPackageService.detail(tenantPackageRequest));
    }

    /**
     * 获取租户-功能包列表
     *
     * <AUTHOR>
     * @since 2024/01/21 15:55
     */
    @GetResource(name = "获取租户-功能包列表", path = "/tenantPackage/list")
    public ResponseData<List<TenantPackage>> list(TenantPackageRequest tenantPackageRequest) {
        return new SuccessResponseData<>(tenantPackageService.findList(tenantPackageRequest));
    }

}
