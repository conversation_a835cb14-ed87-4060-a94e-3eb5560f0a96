package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 产品分类请求参数
 *
 * <AUTHOR>
 * @since 2025/07/21 21:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpProductCategoryRequest extends BaseRequest {

    /**
     * 分类ID
     */
    @ChineseDescription("分类ID")
    private Long categoryId;

    /**
     * 分类编码
     */
    @NotBlank(message = "分类编码不能为空", groups = {add.class, edit.class})
    @ChineseDescription("分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空", groups = {add.class, edit.class})
    @ChineseDescription("分类名称")
    private String categoryName;

    /**
     * 父级分类ID
     */
    @ChineseDescription("父级分类ID")
    private Long parentId;

    /**
     * 分类层级（1-一级分类，2-二级分类，3-三级分类，4-四级分类，5-五级分类）
     */
    @ChineseDescription("分类层级")
    private Integer categoryLevel;

    /**
     * 分类路径（用/分隔，如：1/2/3）
     */
    @ChineseDescription("分类路径")
    private String categoryPath;

    /**
     * 排序号
     */
    @ChineseDescription("排序号")
    private Integer sortOrder;

    /**
     * 状态（Y-启用，N-停用）
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 备注
     */
    @ChineseDescription("备注")
    private String remark;

    /**
     * 搜索文本
     */
    @ChineseDescription("搜索文本")
    private String searchText;

    /**
     * 只查询启用状态的分类
     */
    @ChineseDescription("只查询启用状态的分类")
    private Boolean onlyEnabled;

    /**
     * 分类ID列表（用于批量操作）
     */
    @ChineseDescription("分类ID列表")
    private List<String> categoryIds;
}
