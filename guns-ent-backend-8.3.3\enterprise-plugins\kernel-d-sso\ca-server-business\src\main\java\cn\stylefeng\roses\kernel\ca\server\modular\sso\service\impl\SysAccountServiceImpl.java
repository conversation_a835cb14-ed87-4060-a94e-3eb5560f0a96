package cn.stylefeng.roses.kernel.ca.server.modular.sso.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.stylefeng.roses.kernel.ca.api.business.CaAccountApi;
import cn.stylefeng.roses.kernel.ca.api.pojo.external.AccountInfo;
import cn.stylefeng.roses.kernel.ca.server.modular.sso.service.SysAccountService;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.sys.modular.user.entity.SysUser;
import cn.stylefeng.roses.kernel.sys.modular.user.service.SysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户账号信息 服务实现类
 *
 * <AUTHOR>
 * @date 2020/12/04 16:17
 */
@Service
public class SysAccountServiceImpl implements SysAccountService, CaAccountApi {

    @Resource
    private SysUserService sysUserService;

    @Override
    public AccountInfo getAccountInfo(String account) {
        LambdaQueryWrapper<SysUser> sysAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysAccountLambdaQueryWrapper.eq(SysUser::getAccount, account);
        sysAccountLambdaQueryWrapper.eq(SysUser::getDelFlag, YesOrNotEnum.N.getCode());
        SysUser sysUser = this.sysUserService.getOne(sysAccountLambdaQueryWrapper);

        // 查询到账号信息转换为AccountInfo类型后返回，否则返回null
        if (ObjectUtil.isNotEmpty(sysUser)) {
            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setAccount(sysUser.getAccount());
            accountInfo.setUserId(sysUser.getUserId());
            accountInfo.setRealName(sysUser.getRealName());
            return accountInfo;
        } else {
            return null;
        }

    }
}
