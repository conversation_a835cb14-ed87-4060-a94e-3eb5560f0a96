System.register(["./index-legacy-ee1db0c7.js"],(function(e,l){"use strict";var a,r,u,o,t,f,d,s,n,m,g,i,c,_;return{setters:[e=>{a=e.s,r=e.a,u=e.f,o=e.w,t=e.d,f=e.g,d=e.l,s=e.u,n=e.v,m=e.z,g=e.A,i=e.$,c=e.G,_=e.H}],execute:function(){e("default",{__name:"config-form",props:{form:Object,isUpdate:Boolean},setup(e){const l=e,v=a({configName:[{required:!0,message:"请输入配置名称",type:"string",trigger:"blur"}],configCode:[{required:!0,message:"请输入配置编码",type:"string",trigger:"blur"}],sysFlag:[{required:!0,message:"请选择是否是系统配置",type:"string",trigger:"change"}]});return(a,p)=>{const b=d,y=s,U=n,h=m,w=g,x=i,N=c,C=_;return r(),u(C,{ref:"formRef",model:e.form,rules:v,layout:"vertical"},{default:o((()=>[t(N,{gutter:20},{default:o((()=>[t(U,{xs:24,sm:24,md:12},{default:o((()=>[t(y,{label:"配置名称:",name:"configName"},{default:o((()=>[t(b,{value:e.form.configName,"onUpdate:value":p[0]||(p[0]=l=>e.form.configName=l),"allow-clear":"",placeholder:"请输入配置名称"},null,8,["value"])])),_:1})])),_:1}),t(U,{xs:24,sm:24,md:12},{default:o((()=>[t(y,{label:"配置编码:",name:"configCode"},{default:o((()=>[t(b,{value:e.form.configCode,"onUpdate:value":p[1]||(p[1]=l=>e.form.configCode=l),"allow-clear":"",placeholder:"请输入配置编码",disabled:l.isUpdate},null,8,["value","disabled"])])),_:1})])),_:1}),t(U,{xs:24,sm:24,md:12},{default:o((()=>[t(y,{label:"是否是系统配置:",name:"sysFlag"},{default:o((()=>[t(w,{value:e.form.sysFlag,"onUpdate:value":p[2]||(p[2]=l=>e.form.sysFlag=l)},{default:o((()=>[t(h,{value:"Y"},{default:o((()=>p[5]||(p[5]=[f("是")]))),_:1,__:[5]}),t(h,{value:"N"},{default:o((()=>p[6]||(p[6]=[f("否")]))),_:1,__:[6]})])),_:1},8,["value"])])),_:1})])),_:1}),t(U,{span:24},{default:o((()=>[t(y,{label:"配置值"},{default:o((()=>[t(x,{value:e.form.configValue,"onUpdate:value":p[3]||(p[3]=l=>e.form.configValue=l),placeholder:"请输入配置值",rows:4},null,8,["value"])])),_:1})])),_:1}),t(U,{span:24},{default:o((()=>[t(y,{label:"备注"},{default:o((()=>[t(x,{value:e.form.remark,"onUpdate:value":p[4]||(p[4]=l=>e.form.remark=l),placeholder:"请输入备注",rows:4},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])}}})}}}));
