<template>
  <a-modal
    :visible="visible"
    title="库存统计分析"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="inventory-statistics-content">
      <!-- 统计时间范围 -->
      <a-card title="统计条件" size="small" style="margin-bottom: 16px">
        <a-form layout="inline" :model="statisticsForm">
          <a-form-item label="统计时间">
            <a-range-picker
              v-model:value="dateRange"
              style="width: 240px"
              :placeholder="['开始时间', '结束时间']"
              format="YYYY-MM-DD"
              @change="onDateRangeChange"
            />
          </a-form-item>
          <a-form-item label="供应商">
            <supplier-selector 
              v-model:value="statisticsForm.supplierId" 
              :filter="{ businessMode: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
              placeholder="全部供应商"
              allowClear
              @change="loadStatistics"
            />
          </a-form-item>
          <a-form-item label="商品分类">
            <a-select 
              v-model:value="statisticsForm.categoryId" 
              placeholder="全部分类" 
              allowClear
              style="width: 150px"
              @change="loadStatistics"
            >
              <a-select-option v-for="category in categories" :key="category.categoryId" :value="category.categoryId">
                {{ category.categoryName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="loadStatistics">刷新统计</a-button>
              <a-button @click="exportStatistics">导出报表</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 总体统计 -->
      <a-card title="总体统计" size="small" style="margin-bottom: 16px">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="商品总数"
              :value="overallStats.totalProducts"
              :value-style="{ color: '#1890ff' }"
              suffix="种"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="库存总价值"
              :value="overallStats.totalValue"
              :value-style="{ color: '#52c41a' }"
              prefix="¥"
              :precision="2"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="预警商品"
              :value="overallStats.warningProducts"
              :value-style="{ color: '#faad14' }"
              suffix="种"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="缺货商品"
              :value="overallStats.outOfStockProducts"
              :value-style="{ color: '#ff4d4f' }"
              suffix="种"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 分类统计 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="按分类统计" size="small" style="margin-bottom: 16px">
            <a-table
              :columns="categoryColumns"
              :data-source="categoryStats"
              :pagination="false"
              size="small"
              :loading="categoryLoading"
              :scroll="{ y: 300 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'categoryName'">
                  <a-tag color="blue">{{ record.categoryName }}</a-tag>
                </template>
                <template v-if="column.key === 'totalValue'">
                  <span class="total-value">¥{{ formatAmount(record.totalValue) }}</span>
                </template>
                <template v-if="column.key === 'warningCount'">
                  <span v-if="record.warningCount > 0" class="warning-count">{{ record.warningCount }}</span>
                  <span v-else>0</span>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="按供应商统计" size="small" style="margin-bottom: 16px">
            <a-table
              :columns="supplierColumns"
              :data-source="supplierStats"
              :pagination="false"
              size="small"
              :loading="supplierLoading"
              :scroll="{ y: 300 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'supplierName'">
                  <div>
                    <div class="supplier-name">{{ record.supplierName }}</div>
                    <a-tag 
                      v-if="record.businessModeName" 
                      size="small" 
                      :color="getBusinessModeColor(record.businessMode)"
                    >
                      {{ record.businessModeName }}
                    </a-tag>
                  </div>
                </template>
                <template v-if="column.key === 'totalValue'">
                  <span class="total-value">¥{{ formatAmount(record.totalValue) }}</span>
                </template>
                <template v-if="column.key === 'warningCount'">
                  <span v-if="record.warningCount > 0" class="warning-count">{{ record.warningCount }}</span>
                  <span v-else>0</span>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>

      <!-- 库存变动趋势 -->
      <a-card title="库存变动趋势" size="small" style="margin-bottom: 16px">
        <div class="trend-chart-container">
          <!-- 这里可以集成图表组件，如 ECharts -->
          <div class="chart-placeholder">
            <a-empty description="图表功能待开发">
              <template #image>
                <icon-font iconClass="icon-opt-tongji" style="font-size: 48px; color: #d9d9d9;" />
              </template>
            </a-empty>
          </div>
        </div>
      </a-card>

      <!-- 库存周转分析 -->
      <a-card title="库存周转分析" size="small">
        <a-table
          :columns="turnoverColumns"
          :data-source="turnoverStats"
          :pagination="{ pageSize: 10 }"
          size="small"
          :loading="turnoverLoading"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'productInfo'">
              <div class="product-info">
                <div class="product-name">{{ record.productName }}</div>
                <div class="product-code">{{ record.productCode }}</div>
              </div>
            </template>
            <template v-if="column.key === 'turnoverRate'">
              <span :class="getTurnoverRateClass(record.turnoverRate)">
                {{ formatTurnoverRate(record.turnoverRate) }}
              </span>
            </template>
            <template v-if="column.key === 'avgStock'">
              {{ formatStock(record.avgStock, record.pricingType) }}
              {{ getStockUnit(record.pricingType, record.unit) }}
            </template>
            <template v-if="column.key === 'totalSales'">
              {{ formatStock(record.totalSales, record.pricingType) }}
              {{ getStockUnit(record.pricingType, record.unit) }}
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryApi } from '../api/InventoryApi';
import { InventoryHistoryApi } from '../api/InventoryHistoryApi';
import { ProductCategoryApi } from '@/views/erp/productCategory/api/productCategoryApi';
import SupplierSelector from '@/components/erp/SupplierSelector.vue';

export default {
  name: 'InventoryStatisticsModal',
  components: {
    SupplierSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const categoryLoading = ref(false);
    const supplierLoading = ref(false);
    const turnoverLoading = ref(false);
    const dateRange = ref([]);
    const categories = ref([]);

    // 统计表单
    const statisticsForm = reactive({
      supplierId: undefined,
      categoryId: undefined,
      startDate: undefined,
      endDate: undefined
    });

    // 总体统计数据
    const overallStats = reactive({
      totalProducts: 0,
      totalValue: 0,
      warningProducts: 0,
      outOfStockProducts: 0
    });

    // 分类统计数据
    const categoryStats = ref([]);
    const categoryColumns = [
      { title: '商品分类', key: 'categoryName', width: 120 },
      { title: '商品数量', dataIndex: 'productCount', width: 80, align: 'center' },
      { title: '库存价值', key: 'totalValue', width: 100, align: 'right' },
      { title: '预警数量', key: 'warningCount', width: 80, align: 'center' }
    ];

    // 供应商统计数据
    const supplierStats = ref([]);
    const supplierColumns = [
      { title: '供应商', key: 'supplierName', width: 150 },
      { title: '商品数量', dataIndex: 'productCount', width: 80, align: 'center' },
      { title: '库存价值', key: 'totalValue', width: 100, align: 'right' },
      { title: '预警数量', key: 'warningCount', width: 80, align: 'center' }
    ];

    // 周转率统计数据
    const turnoverStats = ref([]);
    const turnoverColumns = [
      { title: '商品信息', key: 'productInfo', width: 200 },
      { title: '周转率', key: 'turnoverRate', width: 100, align: 'center' },
      { title: '平均库存', key: 'avgStock', width: 100, align: 'right' },
      { title: '销售总量', key: 'totalSales', width: 100, align: 'right' },
      { title: '库存天数', dataIndex: 'stockDays', width: 80, align: 'center' }
    ];

    // 加载商品分类
    const loadCategories = async () => {
      try {
        const response = await ProductCategoryApi.findList({ status: 'Y' });
        if (response.success) {
          categories.value = response.data || [];
        }
      } catch (error) {
        console.error('加载商品分类失败:', error);
      }
    };

    // 加载统计数据
    const loadStatistics = async () => {
      await Promise.all([
        loadOverallStats(),
        loadCategoryStats(),
        loadSupplierStats(),
        loadTurnoverStats()
      ]);
    };

    // 加载总体统计
    const loadOverallStats = async () => {
      try {
        const response = await InventoryApi.inventoryValue(statisticsForm);
        if (response.success) {
          Object.assign(overallStats, response.data);
        }
      } catch (error) {
        console.error('加载总体统计失败:', error);
      }
    };

    // 加载分类统计
    const loadCategoryStats = async () => {
      categoryLoading.value = true;
      try {
        // 这里需要实现分类统计API
        // const response = await InventoryApi.categoryStatistics(statisticsForm);
        // categoryStats.value = response.data || [];
        
        // 模拟数据
        categoryStats.value = [
          { categoryName: '食品', productCount: 25, totalValue: 15000, warningCount: 3 },
          { categoryName: '饮料', productCount: 18, totalValue: 8500, warningCount: 1 },
          { categoryName: '日用品', productCount: 32, totalValue: 12000, warningCount: 5 }
        ];
      } catch (error) {
        console.error('加载分类统计失败:', error);
      } finally {
        categoryLoading.value = false;
      }
    };

    // 加载供应商统计
    const loadSupplierStats = async () => {
      supplierLoading.value = true;
      try {
        // 这里需要实现供应商统计API
        // const response = await InventoryApi.supplierStatistics(statisticsForm);
        // supplierStats.value = response.data || [];
        
        // 模拟数据
        supplierStats.value = [
          { supplierName: '供应商A', businessMode: 'PURCHASE_SALE', businessModeName: '购销', productCount: 20, totalValue: 18000, warningCount: 2 },
          { supplierName: '供应商B', businessMode: 'CONSIGNMENT', businessModeName: '代销', productCount: 15, totalValue: 12000, warningCount: 3 }
        ];
      } catch (error) {
        console.error('加载供应商统计失败:', error);
      } finally {
        supplierLoading.value = false;
      }
    };

    // 加载周转率统计
    const loadTurnoverStats = async () => {
      turnoverLoading.value = true;
      try {
        // 这里需要实现周转率统计API
        // const response = await InventoryApi.turnoverStatistics(statisticsForm);
        // turnoverStats.value = response.data || [];
        
        // 模拟数据
        turnoverStats.value = [
          { productName: '商品A', productCode: 'P001', pricingType: 'NORMAL', unit: '个', turnoverRate: 12.5, avgStock: 100, totalSales: 1250, stockDays: 30 },
          { productName: '商品B', productCode: 'P002', pricingType: 'WEIGHT', unit: 'kg', turnoverRate: 8.2, avgStock: 50, totalSales: 410, stockDays: 45 }
        ];
      } catch (error) {
        console.error('加载周转率统计失败:', error);
      } finally {
        turnoverLoading.value = false;
      }
    };

    // 日期范围变化
    const onDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        statisticsForm.startDate = dates[0];
        statisticsForm.endDate = dates[1];
      } else {
        statisticsForm.startDate = undefined;
        statisticsForm.endDate = undefined;
      }
      loadStatistics();
    };

    // 导出统计报表
    const exportStatistics = () => {
      try {
        // 这里需要实现导出统计报表API
        // InventoryApi.exportStatistics(statisticsForm);
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败：' + (error.message || '未知错误'));
      }
    };

    // 获取经营方式颜色
    const getBusinessModeColor = (businessMode) => {
      const colorMap = {
        'PURCHASE_SALE': 'blue',
        'JOINT_VENTURE': 'orange',
        'CONSIGNMENT': 'green'
      };
      return colorMap[businessMode] || 'default';
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 格式化周转率
    const formatTurnoverRate = (rate) => {
      if (!rate) return '0.0';
      return parseFloat(rate).toFixed(1);
    };

    // 获取周转率样式类
    const getTurnoverRateClass = (rate) => {
      const turnoverRate = parseFloat(rate) || 0;
      if (turnoverRate >= 10) return 'turnover-high';
      if (turnoverRate >= 5) return 'turnover-medium';
      return 'turnover-low';
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories();
      if (props.visible) {
        loadStatistics();
      }
    });

    return {
      categoryLoading,
      supplierLoading,
      turnoverLoading,
      dateRange,
      categories,
      statisticsForm,
      overallStats,
      categoryStats,
      categoryColumns,
      supplierStats,
      supplierColumns,
      turnoverStats,
      turnoverColumns,
      loadStatistics,
      onDateRangeChange,
      exportStatistics,
      getBusinessModeColor,
      getStockUnit,
      formatStock,
      formatAmount,
      formatTurnoverRate,
      getTurnoverRateClass,
      handleCancel
    };
  }
};
</script>

<style scoped>
.inventory-statistics-content {
  max-height: 70vh;
  overflow-y: auto;
}

.supplier-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-info {
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-code {
  font-size: 12px;
  color: #8c8c8c;
}

.total-value {
  font-weight: 500;
  color: #1890ff;
}

.warning-count {
  color: #faad14;
  font-weight: 500;
}

.turnover-high {
  color: #52c41a;
  font-weight: 500;
}

.turnover-medium {
  color: #faad14;
  font-weight: 500;
}

.turnover-low {
  color: #ff4d4f;
  font-weight: 500;
}

.trend-chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #8c8c8c;
}

.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
}

.ant-statistic-content {
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}
</style>
