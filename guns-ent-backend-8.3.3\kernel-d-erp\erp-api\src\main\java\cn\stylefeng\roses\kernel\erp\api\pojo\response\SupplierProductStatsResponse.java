package cn.stylefeng.roses.kernel.erp.api.pojo.response;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 供应商商品统计响应类
 *
 * <AUTHOR>
 * @since 2025/07/28 10:00
 */
@Data
public class SupplierProductStatsResponse {

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 商品ID
     */
    @ChineseDescription("商品ID")
    private Long productId;

    /**
     * 商品编码
     */
    @ChineseDescription("商品编码")
    private String productCode;

    /**
     * 商品名称
     */
    @ChineseDescription("商品名称")
    private String productName;

    /**
     * 总数量
     */
    @ChineseDescription("总数量")
    private BigDecimal totalQuantity;

    /**
     * 总金额
     */
    @ChineseDescription("总金额")
    private BigDecimal totalAmount;

    /**
     * 采购次数
     */
    @ChineseDescription("采购次数")
    private Long purchaseCount;

    /**
     * 平均价格
     */
    @ChineseDescription("平均价格")
    private BigDecimal avgPrice;

    /**
     * 最后采购日期
     */
    @ChineseDescription("最后采购日期")
    private LocalDate lastPurchaseDate;

}