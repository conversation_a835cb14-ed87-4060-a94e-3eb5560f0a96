package cn.stylefeng.roses.kernel.ca.api;

import cn.stylefeng.roses.kernel.ca.api.pojo.SsoTokenBuild;

/**
 * 用户信息加密和解密api
 *
 * <AUTHOR>
 * @date 2021/1/21 19:14
 */
public interface CaLoginUserEncryptApi {

    /**
     * 将用户信息序列化为json，并进行加密
     *
     * @param ssoTokenBuild ca登陆用户
     * @return 加密后的字符串
     * <AUTHOR>
     * @date 2021/1/21 19:15
     */
    String encrypt(SsoTokenBuild ssoTokenBuild);

    /**
     * 将加密的字符串进行解密，解密出json，再反序列化为loginUser实体
     *
     * @param encryptStr 加密的字符串
     * @return ca登陆用户
     * <AUTHOR>
     * @date 2021/1/21 16:48
     */
    SsoTokenBuild decrypt(String encryptStr);

}
