System.register(["./index-legacy-ee1db0c7.js","./pos-legacy-fe4fee5f.js","./MemberSearch-legacy-cbaf8360.js","./MemberSelector-legacy-68857f68.js","./MemberInfo-legacy-8e547239.js","./MemberDiscount-legacy-fe983216.js","./common-legacy-b1e2d362.js","./performance-monitor-legacy-4ff0ac5f.js","./constants-legacy-2a31d63c.js","./index-legacy-efb51034.js","./formatter-legacy-97503ee9.js","./index-legacy-94a6fc23.js"],(function(e,r){"use strict";var t,o,a,n,m,i,l,d,s,c,b,f,p,g,x,u,h,v,y,w,k;return{setters:[e=>{t=e._,o=e.r,a=e.L,n=e.X,m=e.a,i=e.c,l=e.b,d=e.d,s=e.g,c=e.f,b=e.w,f=e.at,p=e.au,g=e.h,x=e.I,u=e.B},e=>{h=e.u},e=>{v=e.default},e=>{y=e.default},e=>{w=e.default},e=>{k=e.default},null,null,null,null,null,null],execute:function(){var r=document.createElement("style");r.textContent='.member-modal .ant-modal-content{border-radius:12px;overflow:hidden}.member-modal .ant-modal-header{background:linear-gradient(135deg,#722ed1 0%,#9254de 100%);border-bottom:none;padding:20px 24px}.member-modal .ant-modal-title{color:#fff;font-size:18px;font-weight:600}.member-modal .ant-modal-close{color:#fff;opacity:.8}.member-modal .ant-modal-close:hover{opacity:1}.member-modal .ant-modal-body{padding:24px}.member-search{margin-bottom:24px}.member-search-title{font-size:16px;font-weight:600;color:#262626;margin-bottom:16px}.member-search-input{width:100%;height:48px;font-size:16px;border-radius:8px}.member-search-tabs{margin-top:16px}.member-search-tabs .ant-tabs-tab{font-size:14px;font-weight:500}.member-card{background:linear-gradient(135deg,#f0f9ff 0%,#e6f7ff 100%);border:1px solid #e6f7ff;border-radius:12px;padding:20px;margin-bottom:24px;position:relative;overflow:hidden}.member-card:before{content:"";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,#722ed1 0%,#1890ff 100%)}.member-header{display:flex;align-items:center;justify-content:space-between;margin-bottom:16px}.member-avatar{width:60px;height:60px;border-radius:50%;background:linear-gradient(135deg,#722ed1 0%,#9254de 100%);display:flex;align-items:center;justify-content:center;color:#fff;font-size:24px;font-weight:600;margin-right:16px}.member-basic-info{flex:1}.member-name{font-size:18px;font-weight:600;color:#262626;margin-bottom:4px}.member-phone{font-size:14px;color:#8c8c8c;font-family:Courier New,monospace}.member-level{background:linear-gradient(135deg,#722ed1 0%,#9254de 100%);color:#fff;padding:4px 12px;border-radius:16px;font-size:12px;font-weight:500}.member-details{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:16px}.member-detail-item{text-align:center;padding:12px;background:rgba(255,255,255,.6);border-radius:8px;backdrop-filter:blur(4px)}.member-detail-label{font-size:12px;color:#8c8c8c;margin-bottom:4px}.member-detail-value{font-size:16px;font-weight:600;color:#262626}.member-points{color:#722ed1}.member-balance{color:#1890ff}.member-discount{color:#52c41a}.member-list{max-height:400px;overflow-y:auto}.member-list-item{display:flex;align-items:center;padding:12px;border:1px solid #f0f0f0;border-radius:8px;margin-bottom:8px;cursor:pointer;transition:all .3s ease;background:#fff}.member-list-item:hover{border-color:#722ed1;background:#f9f0ff}.member-list-item.selected{border-color:#722ed1;background:#f0e6ff}.member-list-avatar{width:40px;height:40px;border-radius:50%;background:linear-gradient(135deg,#722ed1 0%,#9254de 100%);display:flex;align-items:center;justify-content:center;color:#fff;font-size:16px;font-weight:600;margin-right:12px}.member-list-info{flex:1}.member-list-name{font-size:14px;font-weight:600;color:#262626;margin-bottom:2px}.member-list-phone{font-size:12px;color:#8c8c8c;font-family:Courier New,monospace}.member-list-level{background:#722ed1;color:#fff;padding:2px 6px;border-radius:10px;font-size:10px;font-weight:500}.member-actions{display:flex;gap:12px;margin-top:24px}.member-cancel-btn{flex:1;height:48px;font-size:16px;font-weight:500;border-radius:8px}.member-select-btn{flex:2;height:48px;font-size:16px;font-weight:600;border-radius:8px;background:linear-gradient(135deg,#722ed1 0%,#9254de 100%);border:none;color:#fff;transition:all .3s ease}.member-select-btn:hover:not(:disabled){background:linear-gradient(135deg,#531dab 0%,#722ed1 100%);color:#fff;transform:translateY(-1px);box-shadow:0 4px 12px rgba(114,46,209,.3)}.member-select-btn:disabled{background:#f5f5f5;color:#bfbfbf;cursor:not-allowed;transform:none;box-shadow:none}.member-empty{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;color:#8c8c8c}.member-empty .ant-empty-description{color:#bfbfbf;font-size:14px;margin-top:16px}@media (max-width: 768px){.member-modal .ant-modal-body,.member-card{padding:16px}.member-header{flex-direction:column;align-items:flex-start;gap:12px}.member-avatar{width:50px;height:50px;font-size:20px;margin-right:0}.member-details{grid-template-columns:repeat(2,1fr);gap:12px}.member-actions{flex-direction:column}.member-cancel-btn,.member-select-btn{width:100%}}@media (max-width: 480px){.member-modal .ant-modal-header{padding:16px 20px}.member-modal .ant-modal-body{padding:12px}.member-search-input{height:44px;font-size:14px}.member-card{padding:12px}.member-details{grid-template-columns:1fr}.member-list-item{padding:8px}.member-list-avatar{width:36px;height:36px;font-size:14px}}@media (hover: none){.member-list-item:hover{border-color:#f0f0f0;background:#fff}.member-list-item.selected:hover{border-color:#722ed1;background:#f0e6ff}.member-select-btn:hover:not(:disabled){transform:none;box-shadow:none}}@media (prefers-reduced-motion: reduce){.member-list-item,.member-select-btn{transition:none}.member-select-btn:hover:not(:disabled){transform:none}}.member-panel[data-v-de4206f4]{height:100%;display:flex;flex-direction:column}.member-header[data-v-de4206f4]{display:flex;align-items:center;justify-content:space-between;padding:16px 20px 12px;border-bottom:1px solid #f0f0f0}.member-title[data-v-de4206f4]{margin:0;font-size:16px;font-weight:600;color:#262626;display:flex;align-items:center;gap:8px}.clear-btn[data-v-de4206f4]{color:#ff4d4f;transition:all .3s}.clear-btn[data-v-de4206f4]:hover:not(:disabled){color:#ff7875;background:#fff2f0}.member-content[data-v-de4206f4]{flex:1;padding:0 20px;overflow-y:auto}\n',document.head.appendChild(r);const z={class:"member-panel pos-card"},j={class:"member-header"},C={class:"member-title"},M={key:0,class:"member-content"},_={key:1,class:"member-content"},D=Object.assign({name:"MemberPanel"},{__name:"MemberPanel",emits:["memberChange","memberSelect","memberClear"],setup(e,{expose:r,emit:t}){const D=t,R=o(null),S=o(null),P=o(null),E=h(),A=a((()=>E.currentMember)),I=a((()=>E.memberDiscountRate)),N=a((()=>E.finalAmount)),B=a((()=>E.pointsExchangeRate||100)),F=e=>{T(e),S.value&&S.value.addToRecentMembers(e)},L=e=>{console.error("会员搜索错误:",e)},O=e=>{T(e)},T=e=>{E.setCurrentMember(e),R.value&&R.value.resetSearch(),P.value&&P.value.resetPointsDeduction(),D("memberChange",e),D("memberSelect",e),console.log("选择会员:",e.memberName)},X=()=>{E.clearCurrentMember(),P.value&&P.value.resetPointsDeduction(),D("memberChange",null),D("memberClear"),console.log("清除会员")},Y=({points:e,amount:r})=>{E.setPointsDeduction(e,r)};return n((()=>A.value),(e=>{e?E.applyMemberDiscount(e.memberId):E.clearMemberDiscount()}),{deep:!0}),r({selectMember:T,clearMember:X}),(e,r)=>{const t=x,o=u;return m(),i("div",z,[l("div",j,[l("h3",C,[d(t,{iconClass:"icon-member"}),r[0]||(r[0]=s(" 会员管理 "))]),A.value?(m(),c(o,{key:0,type:"text",size:"small",onClick:X,class:"clear-btn"},{icon:b((()=>[d(f(p))])),default:b((()=>[r[1]||(r[1]=s(" 清除 "))])),_:1,__:[1]})):g("",!0)]),A.value?g("",!0):(m(),i("div",M,[d(f(v),{ref_key:"memberSearchRef",ref:R,onMemberFound:F,onSearchError:L},null,512),d(f(y),{ref_key:"memberSelectorRef",ref:S,onMemberSelect:O},null,512)])),A.value?(m(),i("div",_,[d(f(w),{member:A.value},null,8,["member"]),d(f(k),{ref_key:"memberDiscountRef",ref:P,member:A.value,discountRate:I.value,finalAmount:N.value,pointsExchangeRate:B.value,onPointsDeductionChange:Y},null,8,["member","discountRate","finalAmount","pointsExchangeRate"])])):g("",!0)])}}});e("default",t(D,[["__scopeId","data-v-de4206f4"]]))}}}));
