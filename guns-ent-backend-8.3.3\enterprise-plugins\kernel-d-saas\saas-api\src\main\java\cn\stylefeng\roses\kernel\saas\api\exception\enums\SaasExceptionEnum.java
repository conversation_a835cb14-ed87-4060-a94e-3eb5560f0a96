package cn.stylefeng.roses.kernel.saas.api.exception.enums;

import cn.stylefeng.roses.kernel.rule.constants.RuleConstants;
import cn.stylefeng.roses.kernel.rule.exception.AbstractExceptionEnum;
import cn.stylefeng.roses.kernel.saas.api.constants.SaasConstants;
import lombok.Getter;

/**
 * SaaS异常枚举
 *
 * <AUTHOR>
 * @date 2021/2/18 13:59
 */
@Getter
public enum SaasExceptionEnum implements AbstractExceptionEnum {

    /**
     * 租户sql执行错误
     */
    SQL_RUN_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + SaasConstants.SAAS_SERVER_EXCEPTION_STEP_CODE + "01", "租户sql执行错误，具体原因：{}"),

    /**
     * 创建租户数据库错误
     */
    TENANT_DB_CREATE_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + SaasConstants.SAAS_SERVER_EXCEPTION_STEP_CODE + "02", "创建租户数据库错误，具体原因：{}"),

    /**
     * 租户不存在
     */
    TENANT_NOT_EXIST(RuleConstants.BUSINESS_ERROR_TYPE_CODE + SaasConstants.SAAS_SERVER_EXCEPTION_STEP_CODE + "03", "租户不存在"),

    /**
     * 获取租户编码错误
     */
    CALC_TENANT_CODE_ERROR(RuleConstants.BUSINESS_ERROR_TYPE_CODE + SaasConstants.SAAS_SERVER_EXCEPTION_STEP_CODE + "04", "获取租户编码错误，具体信息:{}");

    /**
     * 错误编码
     */
    private final String errorCode;

    /**
     * 提示用户信息
     */
    private final String userTip;

    SaasExceptionEnum(String errorCode, String userTip) {
        this.errorCode = errorCode;
        this.userTip = userTip;
    }

}
