/**
 * Copyright 2018-2020 stylefeng & fengshuonan (<EMAIL>)
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.stylefeng.roses.kernel.micro.core.feign;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.micro.api.exception.MicroException;
import cn.stylefeng.roses.kernel.micro.api.exception.enums.FeignExceptionEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * feign错误解码器（为了feign接收到错误的返回）
 *
 * <AUTHOR>
 * @date 2018/4/20 23:14
 */
@Slf4j
public class GunsFeignErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {

        // 1. 响应为空的情况
        if (response == null || response.body() == null) {
            return new MicroException(FeignExceptionEnum.REMOTE_SERVICE_ERROR, "未知异常");
        }

        // 2. 解析出来ResponseBody内容，如果body解析异常则直接抛出MicroException
        String responseBody;
        try {
            if (response.status() == 404) {
                return new MicroException(FeignExceptionEnum.REMOTE_SERVICE_NULL);
            }
            responseBody = IoUtil.read(response.body().asInputStream(), "UTF-8");
        } catch (IOException e) {
            return new MicroException(FeignExceptionEnum.REMOTE_IO_ERROR);
        }

        // 3. 解析出来ResponseBody后，用json反序列化
        JSONObject jsonObject = JSON.parseObject(responseBody);
        if (log.isDebugEnabled()) {
            log.debug("FeignErrorDecoder收到错误响应结果：" + responseBody);
        }

        // 4. 获取有效信息
        String message = jsonObject.getString("message");
        if (StrUtil.isEmpty(message)) {
            message = "未知错误";
        }

        return new MicroException(FeignExceptionEnum.REMOTE_SERVICE_ERROR, message);
    }

}