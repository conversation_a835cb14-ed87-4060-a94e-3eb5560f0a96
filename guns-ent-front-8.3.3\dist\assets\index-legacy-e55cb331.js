System.register(["./index-legacy-ee1db0c7.js","./index-legacy-45c79de7.js","./logo-legacy-1fd31909.js"],(function(e,t){"use strict";var i,n,o,r,a,s,l,h,c,u,d,m,v,g,f,p,b,w;return{setters:[e=>{i=e.r,n=e.o,o=e.aK,r=e.a,a=e.c,s=e.aR,l=e.aS,h=e.b,c=e.F,u=e.e,d=e.h,m=e.d,v=e.w,g=e.g,f=e.f,p=e.B,b=e.a0},null,e=>{w=e._}],execute:function(){var t=document.createElement("style");
/*!
       * Viewer.js v1.10.4
       * https://fengyuanchen.github.io/viewerjs
       *
       * Copyright 2015-present <PERSON>
       * Released under the MIT license
       *
       * Date: 2022-02-13T08:40:00.127Z
       */
function y(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function x(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?y(Object(i),!0).forEach((function(t){z(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):y(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function A(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function z(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}t.textContent='/*!\n * Viewer.js v1.10.4\n * https://fengyuanchen.github.io/viewerjs\n *\n * Copyright 2015-present Chen Fengyuan\n * Released under the MIT license\n *\n * Date: 2022-02-13T08:39:57.620Z\n */.viewer-zoom-in:before,.viewer-zoom-out:before,.viewer-one-to-one:before,.viewer-reset:before,.viewer-prev:before,.viewer-play:before,.viewer-next:before,.viewer-rotate-left:before,.viewer-rotate-right:before,.viewer-flip-horizontal:before,.viewer-flip-vertical:before,.viewer-fullscreen:before,.viewer-fullscreen-exit:before,.viewer-close:before{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARgAAAAUCAYAAABWOyJDAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAABx0RVh0U29mdHdhcmUAQWRvYmUgRmlyZXdvcmtzIENTNui8sowAAAQPSURBVHic7Zs/iFxVFMa/0U2UaJGksUgnIVhYxVhpjDbZCBmLdAYECxsRFBTUamcXUiSNncgKQbSxsxH8gzAP3FU2jY0kKKJNiiiIghFlccnP4p3nPCdv3p9778vsLOcHB2bfveeb7955c3jvvNkBIMdxnD64a94GHMfZu3iBcRynN7zAOI7TG15gHCeeNUkr8zaxG2lbYDYsdgMbktBsP03jdQwljSXdtBhLOmtjowC9Mg9L+knSlcD8TNKpSA9lBpK2JF2VdDSR5n5J64m0qli399hNFMUlpshQii5jbXTbHGviB0nLNeNDSd9VO4A2UdB2fp+x0eCnaXxWXGA2X0au/3HgN9P4LFCjIANOJdrLr0zzZ+BEpNYDwKbpnQMeAw4m8HjQtM6Z9qa917zPQwFr3M5KgA6J5rTJCdFZJj9/lyvGhsDvwFNVuV2MhhjrK6b9bFiE+j1r87eBl4HDwCF7/U/k+ofAX5b/EXBv5JoLMuILzf3Ap6Z3EzgdqHMCuF7hcQf4HDgeoHnccncqdK/TvSDWffFXI/exICY/xZyqc6XLWF1UFZna4gJ7q8BsRvgd2/xXpo6P+D9dfT7PpECtA3cnWPM0GXGFZh/wgWltA+cDNC7X+AP4GzjZQe+k5dRxuYPeiuXU7e1qwLpDz7dFjXKRaSwuMLvAlG8zZlG+YmiK1HoFqT7wP2z+4Q45TfEGcMt01xLoNZEBTwRqD4BLpnMLeC1A41UmVxsXgXeBayV/Wx20rpTyrpnWRft7p6O/FdqzGrDukPNtkaMoMo3FBdBSQMOnYBCReyf05s126fU9ytfX98+mY54Kxnp7S9K3kj6U9KYdG0h6UdLbkh7poFXMfUnSOyVvL0h6VtIXHbS6nOP+s/Zm9mvyXW1uuC9ohZ72E9uDmXWLJOB1GxsH+DxPftsB8B6wlGDN02TAkxG6+4D3TWsbeC5CS8CDFce+AW500LhhOW2020TRjK3b21HEmgti9m0RonxbdMZeVzV+/4tF3cBpP7E9mKHNL5q8h5g0eYsCMQz0epq8gQrwMXAgcs0FGXGFRcB9wCemF9PkbYqM/Bas7fxLwNeJPdTdpo4itQti8lPMqTpXuozVRVXPpbHI3KkNTB1NfkL81j2mvhDp91HgV9MKuRIqrykj3WPq4rHyL+axj8/qGPmTqi6F9YDlHOvJU6oYcTsh/TYSzWmTE6JT19CtLTJt32D6CmHe0eQn1O8z5AXgT4sx4Vcu0/EQecMydB8z0hUWkTd2t4CrwNEePqMBcAR4mrBbwyXLPWJa8zrXmmLEhNBmfpkuY2102xxrih+pb+ieAb6vGhuA97UcJ5KR8gZ77K+99xxeYBzH6Q3/Z0fHcXrDC4zjOL3hBcZxnN74F+zlvXFWXF9PAAAAAElFTkSuQmCC);background-repeat:no-repeat;background-size:280px;color:transparent;display:block;font-size:0;height:20px;line-height:0;width:20px}.viewer-zoom-in:before{background-position:0 0;content:"Zoom In"}.viewer-zoom-out:before{background-position:-20px 0;content:"Zoom Out"}.viewer-one-to-one:before{background-position:-40px 0;content:"One to One"}.viewer-reset:before{background-position:-60px 0;content:"Reset"}.viewer-prev:before{background-position:-80px 0;content:"Previous"}.viewer-play:before{background-position:-100px 0;content:"Play"}.viewer-next:before{background-position:-120px 0;content:"Next"}.viewer-rotate-left:before{background-position:-140px 0;content:"Rotate Left"}.viewer-rotate-right:before{background-position:-160px 0;content:"Rotate Right"}.viewer-flip-horizontal:before{background-position:-180px 0;content:"Flip Horizontal"}.viewer-flip-vertical:before{background-position:-200px 0;content:"Flip Vertical"}.viewer-fullscreen:before{background-position:-220px 0;content:"Enter Full Screen"}.viewer-fullscreen-exit:before{background-position:-240px 0;content:"Exit Full Screen"}.viewer-close:before{background-position:-260px 0;content:"Close"}.viewer-container{bottom:0;direction:ltr;font-size:0;left:0;line-height:0;overflow:hidden;position:absolute;right:0;-webkit-tap-highlight-color:transparent;top:0;-ms-touch-action:none;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.viewer-container::-moz-selection,.viewer-container *::-moz-selection{background-color:transparent}.viewer-container::selection,.viewer-container *::selection{background-color:transparent}.viewer-container:focus{outline:0}.viewer-container img{display:block;height:auto;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}.viewer-canvas{bottom:0;left:0;overflow:hidden;position:absolute;right:0;top:0}.viewer-canvas>img{height:auto;margin:15px auto;max-width:90%!important;width:auto}.viewer-footer{bottom:0;left:0;overflow:hidden;position:absolute;right:0;text-align:center}.viewer-navbar{background-color:rgba(0,0,0,.5);overflow:hidden}.viewer-list{box-sizing:content-box;height:50px;margin:0;overflow:hidden;padding:1px 0}.viewer-list>li{color:transparent;cursor:pointer;float:left;font-size:0;height:50px;line-height:0;opacity:.5;overflow:hidden;transition:opacity .15s;width:30px}.viewer-list>li:focus,.viewer-list>li:hover{opacity:.75}.viewer-list>li:focus{outline:0}.viewer-list>li+li{margin-left:1px}.viewer-list>.viewer-loading{position:relative}.viewer-list>.viewer-loading:after{border-width:2px;height:20px;margin-left:-10px;margin-top:-10px;width:20px}.viewer-list>.viewer-active,.viewer-list>.viewer-active:focus,.viewer-list>.viewer-active:hover{opacity:1}.viewer-player{background-color:#000;bottom:0;cursor:none;display:none;left:0;position:absolute;right:0;top:0;z-index:1}.viewer-player>img{left:0;position:absolute;top:0}.viewer-toolbar>ul{display:inline-block;margin:0 auto 5px;overflow:hidden;padding:6px 3px}.viewer-toolbar>ul>li{background-color:rgba(0,0,0,.5);border-radius:50%;cursor:pointer;float:left;height:24px;overflow:hidden;transition:background-color .15s;width:24px}.viewer-toolbar>ul>li:focus,.viewer-toolbar>ul>li:hover{background-color:rgba(0,0,0,.8)}.viewer-toolbar>ul>li:focus{box-shadow:0 0 3px #fff;outline:0;position:relative;z-index:1}.viewer-toolbar>ul>li:before{margin:2px}.viewer-toolbar>ul>li+li{margin-left:1px}.viewer-toolbar>ul>.viewer-small{height:18px;margin-bottom:3px;margin-top:3px;width:18px}.viewer-toolbar>ul>.viewer-small:before{margin:-1px}.viewer-toolbar>ul>.viewer-large{height:30px;margin-bottom:-3px;margin-top:-3px;width:30px}.viewer-toolbar>ul>.viewer-large:before{margin:5px}.viewer-tooltip{background-color:rgba(0,0,0,.8);border-radius:10px;color:#fff;display:none;font-size:12px;height:20px;left:50%;line-height:20px;margin-left:-25px;margin-top:-10px;position:absolute;text-align:center;top:50%;width:50px}.viewer-title{color:#ccc;display:inline-block;font-size:12px;line-height:1.2;margin:0 5% 5px;max-width:90%;opacity:.8;overflow:hidden;text-overflow:ellipsis;transition:opacity .15s;white-space:nowrap}.viewer-title:hover{opacity:1}.viewer-button{background-color:rgba(0,0,0,.5);border-radius:50%;cursor:pointer;height:80px;overflow:hidden;position:absolute;right:-40px;top:-40px;transition:background-color .15s;width:80px}.viewer-button:focus,.viewer-button:hover{background-color:rgba(0,0,0,.8)}.viewer-button:focus{box-shadow:0 0 3px #fff;outline:0}.viewer-button:before{bottom:15px;left:15px;position:absolute}.viewer-fixed{position:fixed}.viewer-open{overflow:hidden}.viewer-show{display:block}.viewer-hide{display:none}.viewer-backdrop{background-color:rgba(0,0,0,.5)}.viewer-invisible{visibility:hidden}.viewer-move{cursor:move;cursor:-webkit-grab;cursor:grab}.viewer-fade{opacity:0}.viewer-in{opacity:1}.viewer-transition{transition:all .3s}@-webkit-keyframes viewer-spinner{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes viewer-spinner{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.viewer-loading:after{-webkit-animation:viewer-spinner 1s linear infinite;animation:viewer-spinner 1s linear infinite;border:4px solid rgba(255,255,255,10%);border-left-color:rgba(255,255,255,.5);border-radius:50%;content:"";display:inline-block;height:40px;left:50%;margin-left:-20px;margin-top:-20px;position:absolute;top:50%;width:40px;z-index:1}@media (max-width: 767px){.viewer-hide-xs-down{display:none}}@media (max-width: 991px){.viewer-hide-sm-down{display:none}}@media (max-width: 1199px){.viewer-hide-md-down{display:none}}.viewer-transition{transition:all .1s!important}\n',document.head.appendChild(t);var T={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},D="undefined"!=typeof window&&void 0!==window.document,E=D?window:{},C=!(!D||!E.document.documentElement)&&"ontouchstart"in E.document.documentElement,I=!!D&&"PointerEvent"in E,S="viewer",F="move",L="switch",O="zoom",R="".concat(S,"-active"),X="".concat(S,"-close"),N="".concat(S,"-fade"),M="".concat(S,"-fixed"),P="".concat(S,"-fullscreen"),Y="".concat(S,"-fullscreen-exit"),B="".concat(S,"-hide"),j="".concat(S,"-hide-md-down"),q="".concat(S,"-hide-sm-down"),W="".concat(S,"-hide-xs-down"),H="".concat(S,"-in"),U="".concat(S,"-invisible"),V="".concat(S,"-loading"),K="".concat(S,"-move"),Z="".concat(S,"-open"),G="".concat(S,"-show"),Q="".concat(S,"-transition"),J="click",_="dblclick",$="dragstart",ee="focusin",te="keydown",ie="load",ne="error",oe=I?"pointerdown":C?"touchstart":"mousedown",re=I?"pointermove":C?"touchmove":"mousemove",ae=I?"pointerup pointercancel":C?"touchend touchcancel":"mouseup",se="resize",le="transitionend",he="wheel",ce="ready",ue="show",de="shown",me="hide",ve="hidden",ge="view",fe="viewed",pe="move",be="moved",we="rotate",ye="rotated",xe="scale",ke="scaled",Ae="zoom",ze="zoomed",Te="play",De="stop",Ee="".concat(S,"Action"),Ce=/\s\s*/,Ie=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function Se(e){return"string"==typeof e}var Fe=Number.isNaN||E.isNaN;function Le(e){return"number"==typeof e&&!Fe(e)}function Oe(e){return void 0===e}function Re(e){return"object"===k(e)&&null!==e}var Xe=Object.prototype.hasOwnProperty;function Ne(e){if(!Re(e))return!1;try{var t=e.constructor,i=t.prototype;return t&&i&&Xe.call(i,"isPrototypeOf")}catch(n){return!1}}function Me(e){return"function"==typeof e}function Pe(e,t){if(e&&Me(t))if(Array.isArray(e)||Le(e.length)){var i,n=e.length;for(i=0;i<n&&!1!==t.call(e,e[i],i,e);i+=1);}else Re(e)&&Object.keys(e).forEach((function(i){t.call(e,e[i],i,e)}));return e}var Ye=Object.assign||function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return Re(e)&&i.length>0&&i.forEach((function(t){Re(t)&&Object.keys(t).forEach((function(i){e[i]=t[i]}))})),e},Be=/^(?:width|height|left|top|marginLeft|marginTop)$/;function je(e,t){var i=e.style;Pe(t,(function(e,t){Be.test(t)&&Le(e)&&(e+="px"),i[t]=e}))}function qe(e,t){return!(!e||!t)&&(e.classList?e.classList.contains(t):e.className.indexOf(t)>-1)}function We(e,t){if(e&&t)if(Le(e.length))Pe(e,(function(e){We(e,t)}));else if(e.classList)e.classList.add(t);else{var i=e.className.trim();i?i.indexOf(t)<0&&(e.className="".concat(i," ").concat(t)):e.className=t}}function He(e,t){e&&t&&(Le(e.length)?Pe(e,(function(e){He(e,t)})):e.classList?e.classList.remove(t):e.className.indexOf(t)>=0&&(e.className=e.className.replace(t,"")))}function Ue(e,t,i){t&&(Le(e.length)?Pe(e,(function(e){Ue(e,t,i)})):i?We(e,t):He(e,t))}var Ve=/([a-z\d])([A-Z])/g;function Ke(e){return e.replace(Ve,"$1-$2").toLowerCase()}function Ze(e,t){return Re(e[t])?e[t]:e.dataset?e.dataset[t]:e.getAttribute("data-".concat(Ke(t)))}function Ge(e,t,i){Re(i)?e[t]=i:e.dataset?e.dataset[t]=i:e.setAttribute("data-".concat(Ke(t)),i)}var Qe=function(){var e=!1;if(D){var t=!1,i=function(){},n=Object.defineProperty({},"once",{get:function(){return e=!0,t},set:function(e){t=e}});E.addEventListener("test",i,n),E.removeEventListener("test",i,n)}return e}();function Je(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i;t.trim().split(Ce).forEach((function(t){if(!Qe){var r=e.listeners;r&&r[t]&&r[t][i]&&(o=r[t][i],delete r[t][i],0===Object.keys(r[t]).length&&delete r[t],0===Object.keys(r).length&&delete e.listeners)}e.removeEventListener(t,o,n)}))}function _e(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i;t.trim().split(Ce).forEach((function(t){if(n.once&&!Qe){var r=e.listeners,a=void 0===r?{}:r;o=function(){delete a[t][i],e.removeEventListener(t,o,n);for(var r=arguments.length,s=new Array(r),l=0;l<r;l++)s[l]=arguments[l];i.apply(e,s)},a[t]||(a[t]={}),a[t][i]&&e.removeEventListener(t,a[t][i],n),a[t][i]=o,e.listeners=a}e.addEventListener(t,o,n)}))}function $e(e,t,i,n){var o;return Me(Event)&&Me(CustomEvent)?o=new CustomEvent(t,x({bubbles:!0,cancelable:!0,detail:i},n)):(o=document.createEvent("CustomEvent")).initCustomEvent(t,!0,!0,i),e.dispatchEvent(o)}function et(e){var t=e.rotate,i=e.scaleX,n=e.scaleY,o=e.translateX,r=e.translateY,a=[];Le(o)&&0!==o&&a.push("translateX(".concat(o,"px)")),Le(r)&&0!==r&&a.push("translateY(".concat(r,"px)")),Le(t)&&0!==t&&a.push("rotate(".concat(t,"deg)")),Le(i)&&1!==i&&a.push("scaleX(".concat(i,")")),Le(n)&&1!==n&&a.push("scaleY(".concat(n,")"));var s=a.length?a.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}var tt=E.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(E.navigator.userAgent);function it(e,t,i){var n=document.createElement("img");if(e.naturalWidth&&!tt)return i(e.naturalWidth,e.naturalHeight),n;var o=document.body||document.documentElement;return n.onload=function(){i(n.width,n.height),tt||o.removeChild(n)},Pe(t.inheritedAttributes,(function(t){var i=e.getAttribute(t);null!==i&&n.setAttribute(t,i)})),n.src=e.src,tt||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n)),n}function nt(e){switch(e){case 2:return W;case 3:return q;case 4:return j;default:return""}}function ot(e,t){var i=e.pageX,n=e.pageY,o={endX:i,endY:n};return t?o:x({timeStamp:Date.now(),startX:i,startY:n},o)}var rt,at={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var e=this.element.ownerDocument,t=e.body||e.documentElement;this.body=t,this.scrollbarWidth=window.innerWidth-e.documentElement.clientWidth,this.initialBodyPaddingRight=t.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(t).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var e,t=this.options,i=this.parent;t.inline&&(e={width:Math.max(i.offsetWidth,t.minWidth),height:Math.max(i.offsetHeight,t.minHeight)},this.parentData=e),!this.fulled&&e||(e=this.containerData),this.viewerData=Ye({},e)},renderViewer:function(){this.options.inline&&!this.fulled&&je(this.viewer,this.viewerData)},initList:function(){var e=this,t=this.element,i=this.options,n=this.list,o=[];n.innerHTML="",Pe(this.images,(function(t,r){var a=t.src,s=t.alt||function(e){return Se(e)?decodeURIComponent(e.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}(a),l=e.getImageURL(t);if(a||l){var h=document.createElement("li"),c=document.createElement("img");Pe(i.inheritedAttributes,(function(e){var i=t.getAttribute(e);null!==i&&c.setAttribute(e,i)})),c.src=a||l,c.alt=s,c.setAttribute("data-original-url",l||a),h.setAttribute("data-index",r),h.setAttribute("data-viewer-action","view"),h.setAttribute("role","button"),i.keyboard&&h.setAttribute("tabindex",0),h.appendChild(c),n.appendChild(h),o.push(h)}})),this.items=o,Pe(o,(function(t){var n,o,r=t.firstElementChild;Ge(r,"filled",!0),i.loading&&We(t,V),_e(r,ie,n=function(n){Je(r,ne,o),i.loading&&He(t,V),e.loadImage(n)},{once:!0}),_e(r,ne,o=function(){Je(r,ie,n),i.loading&&He(t,V)},{once:!0})})),i.transition&&_e(t,fe,(function(){We(n,Q)}),{once:!0})},renderList:function(){var e=this.index,t=this.items[e];if(t){var i=t.nextElementSibling,n=parseInt(window.getComputedStyle(i||t).marginLeft,10),o=t.offsetWidth,r=o+n;je(this.list,Ye({width:r*this.length-n},et({translateX:(this.viewerData.width-o)/2-r*e})))}},resetList:function(){var e=this.list;e.innerHTML="",He(e,Q),je(e,et({translateX:0}))},initImage:function(e){var t,i=this,n=this.options,o=this.image,r=this.viewerData,a=this.footer.offsetHeight,s=r.width,l=Math.max(r.height-a,a),h=this.imageData||{};this.imageInitializing={abort:function(){t.onload=null}},t=it(o,n,(function(t,o){var r=t/o,a=s,c=l;i.imageInitializing=!1,l*r>s?c=s/r:a=l*r,a=Math.min(.9*a,t),c=Math.min(.9*c,o);var u=(s-a)/2,d=(l-c)/2,m={left:u,top:d,x:u,y:d,width:a,height:c,oldRatio:1,ratio:a/t,aspectRatio:r,naturalWidth:t,naturalHeight:o},v=Ye({},m);n.rotatable&&(m.rotate=h.rotate||0,v.rotate=0),n.scalable&&(m.scaleX=h.scaleX||1,m.scaleY=h.scaleY||1,v.scaleX=1,v.scaleY=1),i.imageData=m,i.initialImageData=v,e&&e()}))},renderImage:function(e){var t=this,i=this.image,n=this.imageData;if(je(i,Ye({width:n.width,height:n.height,marginLeft:n.x,marginTop:n.y},et(n))),e)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&qe(i,Q)){var o=function(){t.imageRendering=!1,e()};this.imageRendering={abort:function(){Je(i,le,o)}},_e(i,le,o,{once:!0})}else e()},resetImage:function(){if(this.viewing||this.viewed){var e=this.image;this.viewing&&this.viewing.abort(),e.parentNode.removeChild(e),this.image=null}}},st={bind:function(){var e=this.options,t=this.viewer,i=this.canvas,n=this.element.ownerDocument;_e(t,J,this.onClick=this.click.bind(this)),_e(t,$,this.onDragStart=this.dragstart.bind(this)),_e(i,oe,this.onPointerDown=this.pointerdown.bind(this)),_e(n,re,this.onPointerMove=this.pointermove.bind(this)),_e(n,ae,this.onPointerUp=this.pointerup.bind(this)),_e(n,te,this.onKeyDown=this.keydown.bind(this)),_e(window,se,this.onResize=this.resize.bind(this)),e.zoomable&&e.zoomOnWheel&&_e(t,he,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleOnDblclick&&_e(i,_,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var e=this.options,t=this.viewer,i=this.canvas,n=this.element.ownerDocument;Je(t,J,this.onClick),Je(t,$,this.onDragStart),Je(i,oe,this.onPointerDown),Je(n,re,this.onPointerMove),Je(n,ae,this.onPointerUp),Je(n,te,this.onKeyDown),Je(window,se,this.onResize),e.zoomable&&e.zoomOnWheel&&Je(t,he,this.onWheel,{passive:!1,capture:!0}),e.toggleOnDblclick&&Je(i,_,this.onDblclick)}},lt={click:function(e){var t=this.options,i=this.imageData,n=e.target,o=Ze(n,Ee);switch(o||"img"!==n.localName||"li"!==n.parentElement.localName||(o=Ze(n=n.parentElement,Ee)),C&&e.isTrusted&&n===this.canvas&&clearTimeout(this.clickCanvasTimeout),o){case"mix":this.played?this.stop():t.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.hide();break;case"view":this.view(Ze(n,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(t.loop);break;case"play":this.play(t.fullscreen);break;case"next":this.next(t.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-i.scaleX||-1);break;case"flip-vertical":this.scaleY(-i.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(e){e.preventDefault(),this.viewed&&e.target===this.image&&(C&&e.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(e.isTrusted?e:e.detail&&e.detail.originalEvent))},load:function(){var e=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var t=this.element,i=this.options,n=this.image,o=this.index,r=this.viewerData;He(n,U),i.loading&&He(this.canvas,V),n.style.cssText="height:0;"+"margin-left:".concat(r.width/2,"px;")+"margin-top:".concat(r.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage((function(){Ue(n,K,i.movable),Ue(n,Q,i.transition),e.renderImage((function(){e.viewed=!0,e.viewing=!1,Me(i.viewed)&&_e(t,fe,i.viewed,{once:!0}),$e(t,fe,{originalImage:e.images[o],index:o,image:n},{cancelable:!1})}))}))},loadImage:function(e){var t=e.target,i=t.parentNode,n=i.offsetWidth||30,o=i.offsetHeight||50,r=!!Ze(t,"filled");it(t,this.options,(function(e,i){var a=e/i,s=n,l=o;o*a>n?r?s=o*a:l=n/a:r?l=n/a:s=o*a,je(t,Ye({width:s,height:l},et({translateX:(n-s)/2,translateY:(o-l)/2})))}))},keydown:function(e){var t=this.options;if(t.keyboard){var i=e.keyCode||e.which||e.charCode;if(13===i&&this.viewer.contains(e.target)&&this.click(e),this.fulled)switch(i){case 27:this.played?this.stop():t.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.prev(t.loop);break;case 38:e.preventDefault(),this.zoom(t.zoomRatio,!0);break;case 39:this.next(t.loop);break;case 40:e.preventDefault(),this.zoom(-t.zoomRatio,!0);break;case 48:case 49:e.ctrlKey&&(e.preventDefault(),this.toggle())}}},dragstart:function(e){"img"===e.target.localName&&e.preventDefault()},pointerdown:function(e){var t=this.options,i=this.pointers,n=e.buttons,o=e.button;if(!(!this.viewed||this.showing||this.viewing||this.hiding||("mousedown"===e.type||"pointerdown"===e.type&&"mouse"===e.pointerType)&&(Le(n)&&1!==n||Le(o)&&0!==o||e.ctrlKey))){e.preventDefault(),e.changedTouches?Pe(e.changedTouches,(function(e){i[e.identifier]=ot(e)})):i[e.pointerId||0]=ot(e);var r=!!t.movable&&F;t.zoomOnTouch&&t.zoomable&&Object.keys(i).length>1?r=O:t.slideOnTouch&&("touch"===e.pointerType||"touchstart"===e.type)&&this.isSwitchable()&&(r=L),!t.transition||r!==F&&r!==O||He(this.image,Q),this.action=r}},pointermove:function(e){var t=this.pointers,i=this.action;this.viewed&&i&&(e.preventDefault(),e.changedTouches?Pe(e.changedTouches,(function(e){Ye(t[e.identifier]||{},ot(e,!0))})):Ye(t[e.pointerId||0]||{},ot(e,!0)),this.change(e))},pointerup:function(e){var t,i=this,n=this.options,o=this.action,r=this.pointers;e.changedTouches?Pe(e.changedTouches,(function(e){t=r[e.identifier],delete r[e.identifier]})):(t=r[e.pointerId||0],delete r[e.pointerId||0]),o&&(e.preventDefault(),!n.transition||o!==F&&o!==O||We(this.image,Q),this.action=!1,C&&o!==O&&t&&Date.now()-t.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),n.toggleOnDblclick&&this.viewed&&e.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout((function(){$e(i.image,_,{originalEvent:e})}),50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout((function(){i.imageClicked=!1}),500)):(this.imageClicked=!1,n.backdrop&&"static"!==n.backdrop&&e.target===this.canvas&&(this.clickCanvasTimeout=setTimeout((function(){$e(i.canvas,J,{originalEvent:e})}),50)))))},resize:function(){var e=this;if(this.isShown&&!this.hiding&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){e.renderImage()})),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement))return void this.stop();Pe(this.player.getElementsByTagName("img"),(function(t){_e(t,ie,e.loadImage.bind(e),{once:!0}),$e(t,ie)}))}},wheel:function(e){var t=this;if(this.viewed&&(e.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout((function(){t.wheeling=!1}),50);var i=Number(this.options.zoomRatio)||.1,n=1;e.deltaY?n=e.deltaY>0?1:-1:e.wheelDelta?n=-e.wheelDelta/120:e.detail&&(n=e.detail>0?1:-1),this.zoom(-n*i,!0,e)}}},ht={show:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.element,i=this.options;if(i.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(e),this;if(Me(i.show)&&_e(t,ue,i.show,{once:!0}),!1===$e(t,ue)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n=this.viewer;if(He(n,B),n.setAttribute("role","dialog"),n.setAttribute("aria-labelledby",this.title.id),n.setAttribute("aria-modal",!0),n.removeAttribute("aria-hidden"),i.transition&&!e){var o=this.shown.bind(this);this.transitioning={abort:function(){Je(n,le,o),He(n,H)}},We(n,Q),n.initialOffsetWidth=n.offsetWidth,_e(n,le,o,{once:!0}),We(n,H)}else We(n,H),this.shown();return this},hide:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.element,n=this.options;if(n.inline||this.hiding||!this.isShown&&!this.showing)return this;if(Me(n.hide)&&_e(i,me,n.hide,{once:!0}),!1===$e(i,me))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var o=this.viewer,r=this.image,a=function(){He(o,H),e.hidden()};if(n.transition&&!t){var s=function t(i){i&&i.target===o&&(Je(o,le,t),e.hidden())},l=function(){qe(o,Q)?(_e(o,le,s),He(o,H)):a()};this.transitioning={abort:function(){e.viewed&&qe(r,Q)?Je(r,le,l):qe(o,Q)&&Je(o,le,s)}},this.viewed&&qe(r,Q)?(_e(r,le,l,{once:!0}),this.zoomTo(0,!1,null,!0)):l()}else a();return this},view:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.initialViewIndex;if(t=Number(t)||0,this.hiding||this.played||t<0||t>=this.length||this.viewed&&t===this.index)return this;if(!this.isShown)return this.index=t,this.show();this.viewing&&this.viewing.abort();var i=this.element,n=this.options,o=this.title,r=this.canvas,a=this.items[t],s=a.querySelector("img"),l=Ze(s,"originalUrl"),h=s.getAttribute("alt"),c=document.createElement("img");if(Pe(n.inheritedAttributes,(function(e){var t=s.getAttribute(e);null!==t&&c.setAttribute(e,t)})),c.src=l,c.alt=h,Me(n.view)&&_e(i,ge,n.view,{once:!0}),!1===$e(i,ge,{originalImage:this.images[t],index:t,image:c})||!this.isShown||this.hiding||this.played)return this;var u=this.items[this.index];u&&(He(u,R),u.removeAttribute("aria-selected")),We(a,R),a.setAttribute("aria-selected",!0),n.focus&&a.focus(),this.image=c,this.viewed=!1,this.index=t,this.imageData={},We(c,U),n.loading&&We(r,V),r.innerHTML="",r.appendChild(c),this.renderList(),o.innerHTML="";var d,m,v=function(){var t,i=e.imageData,r=Array.isArray(n.title)?n.title[1]:n.title;o.innerHTML=Se(t=Me(r)?r.call(e,c,i):"".concat(h," (").concat(i.naturalWidth," × ").concat(i.naturalHeight,")"))?t.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):t};return _e(i,fe,v,{once:!0}),this.viewing={abort:function(){Je(i,fe,v),c.complete?e.imageRendering?e.imageRendering.abort():e.imageInitializing&&e.imageInitializing.abort():(c.src="",Je(c,ie,d),e.timeout&&clearTimeout(e.timeout))}},c.complete?this.load():(_e(c,ie,d=function(){Je(c,ne,m),e.load()},{once:!0}),_e(c,ne,m=function(){Je(c,ie,d),e.timeout&&(clearTimeout(e.timeout),e.timeout=!1),He(c,U),n.loading&&He(e.canvas,V)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){He(c,U),e.timeout=!1}),1e3)),this},prev:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.index-1;return t<0&&(t=e?this.length-1:0),this.view(t),this},next:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.length-1,i=this.index+1;return i>t&&(i=e?0:t),this.view(i),this},move:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=this.imageData;return this.moveTo(Oe(e)?e:i.x+Number(e),Oe(t)?t:i.y+Number(t)),this},moveTo:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=this.element,r=this.options,a=this.imageData;if(e=Number(e),i=Number(i),this.viewed&&!this.played&&r.movable){var s=a.x,l=a.y,h=!1;if(Le(e)?h=!0:e=s,Le(i)?h=!0:i=l,h){if(Me(r.move)&&_e(o,pe,r.move,{once:!0}),!1===$e(o,pe,{x:e,y:i,oldX:s,oldY:l,originalEvent:n}))return this;a.x=e,a.y=i,a.left=e,a.top=i,this.moving=!0,this.renderImage((function(){t.moving=!1,Me(r.moved)&&_e(o,be,r.moved,{once:!0}),$e(o,be,{x:e,y:i,oldX:s,oldY:l,originalEvent:n},{cancelable:!1})}))}}return this},rotate:function(e){return this.rotateTo((this.imageData.rotate||0)+Number(e)),this},rotateTo:function(e){var t=this,i=this.element,n=this.options,o=this.imageData;if(Le(e=Number(e))&&this.viewed&&!this.played&&n.rotatable){var r=o.rotate;if(Me(n.rotate)&&_e(i,we,n.rotate,{once:!0}),!1===$e(i,we,{degree:e,oldDegree:r}))return this;o.rotate=e,this.rotating=!0,this.renderImage((function(){t.rotating=!1,Me(n.rotated)&&_e(i,ye,n.rotated,{once:!0}),$e(i,ye,{degree:e,oldDegree:r},{cancelable:!1})}))}return this},scaleX:function(e){return this.scale(e,this.imageData.scaleY),this},scaleY:function(e){return this.scale(this.imageData.scaleX,e),this},scale:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=this.element,o=this.options,r=this.imageData;if(e=Number(e),i=Number(i),this.viewed&&!this.played&&o.scalable){var a=r.scaleX,s=r.scaleY,l=!1;if(Le(e)?l=!0:e=a,Le(i)?l=!0:i=s,l){if(Me(o.scale)&&_e(n,xe,o.scale,{once:!0}),!1===$e(n,xe,{scaleX:e,scaleY:i,oldScaleX:a,oldScaleY:s}))return this;r.scaleX=e,r.scaleY=i,this.scaling=!0,this.renderImage((function(){t.scaling=!1,Me(o.scaled)&&_e(n,ke,o.scaled,{once:!0}),$e(n,ke,{scaleX:e,scaleY:i,oldScaleX:a,oldScaleY:s},{cancelable:!1})}))}}return this},zoom:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=this.imageData;return e=(e=Number(e))<0?1/(1-e):1+e,this.zoomTo(n.width*e/n.naturalWidth,t,i),this},zoomTo:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=this.element,a=this.options,s=this.pointers,l=this.imageData,h=l.x,c=l.y,u=l.width,d=l.height,m=l.naturalWidth,v=l.naturalHeight;if(Le(e=Math.max(0,e))&&this.viewed&&!this.played&&(o||a.zoomable)){if(!o){var g=Math.max(.01,a.minZoomRatio),f=Math.min(100,a.maxZoomRatio);e=Math.min(Math.max(e,g),f)}if(n)switch(n.type){case"wheel":a.zoomRatio>=.055&&e>.95&&e<1.05&&(e=1);break;case"pointermove":case"touchmove":case"mousemove":e>.99&&e<1.01&&(e=1)}var p=m*e,b=v*e,w=p-u,y=b-d,x=l.ratio;if(Me(a.zoom)&&_e(r,Ae,a.zoom,{once:!0}),!1===$e(r,Ae,{ratio:e,oldRatio:x,originalEvent:n}))return this;if(this.zooming=!0,n){var k=function(e){var t=e.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}(this.viewer),A=s&&Object.keys(s).length>0?function(e){var t=0,i=0,n=0;return Pe(e,(function(e){var o=e.startX,r=e.startY;t+=o,i+=r,n+=1})),{pageX:t/=n,pageY:i/=n}}(s):{pageX:n.pageX,pageY:n.pageY};l.x-=w*((A.pageX-k.left-h)/u),l.y-=y*((A.pageY-k.top-c)/d)}else l.x-=w/2,l.y-=y/2;l.left=l.x,l.top=l.y,l.width=p,l.height=b,l.oldRatio=x,l.ratio=e,this.renderImage((function(){t.zooming=!1,Me(a.zoomed)&&_e(r,ze,a.zoomed,{once:!0}),$e(r,ze,{ratio:e,oldRatio:x,originalEvent:n},{cancelable:!1})})),i&&this.tooltip()}return this},play:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var i=this.element,n=this.options;if(Me(n.play)&&_e(i,Te,n.play,{once:!0}),!1===$e(i,Te))return this;var o=this.player,r=this.loadImage.bind(this),a=[],s=0,l=0;if(this.played=!0,this.onLoadWhenPlay=r,t&&this.requestFullscreen(t),We(o,G),Pe(this.items,(function(e,t){var i=e.querySelector("img"),h=document.createElement("img");h.src=Ze(i,"originalUrl"),h.alt=i.getAttribute("alt"),h.referrerPolicy=i.referrerPolicy,s+=1,We(h,N),Ue(h,Q,n.transition),qe(e,R)&&(We(h,H),l=t),a.push(h),_e(h,ie,r,{once:!0}),o.appendChild(h)})),Le(n.interval)&&n.interval>0){var h=function t(){e.playing=setTimeout((function(){He(a[l],H),We(a[l=(l+=1)<s?l:0],H),t()}),n.interval)};s>1&&h()}return this},stop:function(){var e=this;if(!this.played)return this;var t=this.element,i=this.options;if(Me(i.stop)&&_e(t,De,i.stop,{once:!0}),!1===$e(t,De))return this;var n=this.player;return this.played=!1,clearTimeout(this.playing),Pe(n.getElementsByTagName("img"),(function(t){Je(t,ie,e.onLoadWhenPlay)})),He(n,G),n.innerHTML="",this.exitFullscreen(),this},full:function(){var e=this,t=this.options,i=this.viewer,n=this.image,o=this.list;return!this.isShown||this.played||this.fulled||!t.inline||(this.fulled=!0,this.open(),We(this.button,Y),t.transition&&(He(o,Q),this.viewed&&He(n,Q)),We(i,M),i.setAttribute("role","dialog"),i.setAttribute("aria-labelledby",this.title.id),i.setAttribute("aria-modal",!0),i.removeAttribute("style"),je(i,{zIndex:t.zIndex}),t.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=Ye({},this.containerData),this.renderList(),this.viewed&&this.initImage((function(){e.renderImage((function(){t.transition&&setTimeout((function(){We(n,Q),We(o,Q)}),0)}))}))),this},exit:function(){var e=this,t=this.options,i=this.viewer,n=this.image,o=this.list;return this.isShown&&!this.played&&this.fulled&&t.inline?(this.fulled=!1,this.close(),He(this.button,Y),t.transition&&(He(o,Q),this.viewed&&He(n,Q)),t.focus&&this.clearEnforceFocus(),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),He(i,M),je(i,{zIndex:t.zIndexInline}),this.viewerData=Ye({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){e.renderImage((function(){t.transition&&setTimeout((function(){We(n,Q),We(o,Q)}),0)}))})),this):this},tooltip:function(){var e=this,t=this.options,i=this.tooltipBox,n=this.imageData;return this.viewed&&!this.played&&t.tooltip?(i.textContent="".concat(Math.round(100*n.ratio),"%"),this.tooltipping?clearTimeout(this.tooltipping):t.transition?(this.fading&&$e(i,le),We(i,G),We(i,N),We(i,Q),i.removeAttribute("aria-hidden"),i.initialOffsetWidth=i.offsetWidth,We(i,H)):(We(i,G),i.removeAttribute("aria-hidden")),this.tooltipping=setTimeout((function(){t.transition?(_e(i,le,(function(){He(i,G),He(i,N),He(i,Q),i.setAttribute("aria-hidden",!0),e.fading=!1}),{once:!0}),He(i,H),e.fading=!0):(He(i,G),i.setAttribute("aria-hidden",!0)),e.tooltipping=!1}),1e3),this):this},toggle:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return 1===this.imageData.ratio?this.zoomTo(this.imageData.oldRatio,!0,e):this.zoomTo(1,!0,e),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=Ye({},this.initialImageData),this.renderImage()),this},update:function(){var e=this,t=this.element,i=this.options,n=this.isImg;if(n&&!t.parentNode)return this.destroy();var o=[];if(Pe(n?[t]:t.querySelectorAll("img"),(function(t){Me(i.filter)?i.filter.call(e,t)&&o.push(t):e.getImageURL(t)&&o.push(t)})),!o.length)return this;if(this.images=o,this.length=o.length,this.ready){var r=[];if(Pe(this.items,(function(e,t){var i=e.querySelector("img"),n=o[t];n&&i&&n.src===i.src&&n.alt===i.alt||r.push(t)})),je(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var a=r.indexOf(this.index);if(a>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-a,this.length-1),0));else{var s=this.items[this.index];We(s,R),s.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var e=this.element,t=this.options;return e[S]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),t.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):t.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),t.inline||Je(e,J,this.onStart),e[S]=void 0,this):this}},ct={getImageURL:function(e){var t=this.options.url;return t=Se(t)?e.getAttribute(t):Me(t)?t.call(this,e):""},enforceFocus:function(){var e=this;this.clearEnforceFocus(),_e(document,ee,this.onFocusin=function(t){var i=e.viewer,n=t.target;n===document||n===i||i.contains(n)||null!==n.getAttribute("tabindex")&&"true"===n.getAttribute("aria-modal")||i.focus()})},clearEnforceFocus:function(){this.onFocusin&&(Je(document,ee,this.onFocusin),this.onFocusin=null)},open:function(){var e=this.body;We(e,Z),e.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px")},close:function(){var e=this.body;He(e,Z),e.style.paddingRight=this.initialBodyPaddingRight},shown:function(){var e=this.element,t=this.options,i=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,t.focus&&(i.focus(),this.enforceFocus()),Me(t.shown)&&_e(e,de,t.shown,{once:!0}),!1!==$e(e,de)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var e=this.element,t=this.options,i=this.viewer;t.fucus&&this.clearEnforceFocus(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),We(i,B),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),i.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(Me(t.hidden)&&_e(e,ve,t.hidden,{once:!0}),$e(e,ve,null,{cancelable:!1}))},requestFullscreen:function(e){var t=this.element.ownerDocument;if(this.fulled&&!(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)){var i=t.documentElement;i.requestFullscreen?Ne(e)?i.requestFullscreen(e):i.requestFullscreen():i.webkitRequestFullscreen?i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):i.mozRequestFullScreen?i.mozRequestFullScreen():i.msRequestFullscreen&&i.msRequestFullscreen()}},exitFullscreen:function(){var e=this.element.ownerDocument;this.fulled&&(e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement)&&(e.exitFullscreen?e.exitFullscreen():e.webkitExitFullscreen?e.webkitExitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.msExitFullscreen&&e.msExitFullscreen())},change:function(e){var t=this.options,i=this.pointers,n=i[Object.keys(i)[0]];if(n){var o=n.endX-n.startX,r=n.endY-n.startY;switch(this.action){case F:this.move(o,r,e);break;case O:this.zoom(function(e){var t=x({},e),i=[];return Pe(e,(function(e,n){delete t[n],Pe(t,(function(t){var n=Math.abs(e.startX-t.startX),o=Math.abs(e.startY-t.startY),r=Math.abs(e.endX-t.endX),a=Math.abs(e.endY-t.endY),s=Math.sqrt(n*n+o*o),l=(Math.sqrt(r*r+a*a)-s)/s;i.push(l)}))})),i.sort((function(e,t){return Math.abs(e)<Math.abs(t)})),i[0]}(i),!1,e);break;case L:this.action="switched";var a=Math.abs(o);a>1&&a>Math.abs(r)&&(this.pointers={},o>1?this.prev(t.loop):o<-1&&this.next(t.loop))}Pe(i,(function(e){e.startX=e.endX,e.startY=e.endY}))}},isSwitchable:function(){var e=this.imageData,t=this.viewerData;return this.length>1&&e.x>=0&&e.y>=0&&e.width<=t.width&&e.height<=t.height}},ut=E.Viewer,dt=(rt=-1,function(){return rt+=1}),mt=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!t||1!==t.nodeType)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=Ye({},T,Ne(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.id=dt(),this.init()}var t,i,n;return t=e,i=[{key:"init",value:function(){var e=this,t=this.element,i=this.options;if(!t[S]){t[S]=this,i.focus&&!i.keyboard&&(i.focus=!1);var n="img"===t.localName,o=[];if(Pe(n?[t]:t.querySelectorAll("img"),(function(t){Me(i.filter)?i.filter.call(e,t)&&o.push(t):e.getImageURL(t)&&o.push(t)})),this.isImg=n,this.length=o.length,this.images=o,this.initBody(),Oe(document.createElement(S).style.transition)&&(i.transition=!1),i.inline){var r=0,a=function(){var t;(r+=1)===e.length&&(e.initializing=!1,e.delaying={abort:function(){clearTimeout(t)}},t=setTimeout((function(){e.delaying=!1,e.build()}),0))};this.initializing={abort:function(){Pe(o,(function(e){e.complete||Je(e,ie,a)}))}},Pe(o,(function(e){e.complete?a():_e(e,ie,a,{once:!0})}))}else _e(t,J,this.onStart=function(t){var n=t.target;"img"!==n.localName||Me(i.filter)&&!i.filter.call(e,n)||e.view(e.images.indexOf(n))})}}},{key:"build",value:function(){if(!this.ready){var e=this.element,t=this.options,i=e.parentNode,n=document.createElement("div");n.innerHTML='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>';var o=n.querySelector(".".concat(S,"-container")),r=o.querySelector(".".concat(S,"-title")),a=o.querySelector(".".concat(S,"-toolbar")),s=o.querySelector(".".concat(S,"-navbar")),l=o.querySelector(".".concat(S,"-button")),h=o.querySelector(".".concat(S,"-canvas"));if(this.parent=i,this.viewer=o,this.title=r,this.toolbar=a,this.navbar=s,this.button=l,this.canvas=h,this.footer=o.querySelector(".".concat(S,"-footer")),this.tooltipBox=o.querySelector(".".concat(S,"-tooltip")),this.player=o.querySelector(".".concat(S,"-player")),this.list=o.querySelector(".".concat(S,"-list")),o.id="".concat(S).concat(this.id),r.id="".concat(S,"Title").concat(this.id),We(r,t.title?nt(Array.isArray(t.title)?t.title[0]:t.title):B),We(s,t.navbar?nt(t.navbar):B),Ue(l,B,!t.button),t.keyboard&&l.setAttribute("tabindex",0),t.backdrop&&(We(o,"".concat(S,"-backdrop")),t.inline||"static"===t.backdrop||Ge(h,Ee,"hide")),Se(t.className)&&t.className&&t.className.split(Ce).forEach((function(e){We(o,e)})),t.toolbar){var c=document.createElement("ul"),u=Ne(t.toolbar),d=Ie.slice(0,3),m=Ie.slice(7,9),v=Ie.slice(9);u||We(a,nt(t.toolbar)),Pe(u?t.toolbar:Ie,(function(e,i){var n=u&&Ne(e),o=u?Ke(i):e,r=n&&!Oe(e.show)?e.show:e;if(r&&(t.zoomable||-1===d.indexOf(o))&&(t.rotatable||-1===m.indexOf(o))&&(t.scalable||-1===v.indexOf(o))){var a=n&&!Oe(e.size)?e.size:e,s=n&&!Oe(e.click)?e.click:e,l=document.createElement("li");t.keyboard&&l.setAttribute("tabindex",0),l.setAttribute("role","button"),We(l,"".concat(S,"-").concat(o)),Me(s)||Ge(l,Ee,o),Le(r)&&We(l,nt(r)),-1!==["small","large"].indexOf(a)?We(l,"".concat(S,"-").concat(a)):"play"===o&&We(l,"".concat(S,"-large")),Me(s)&&_e(l,J,s),c.appendChild(l)}})),a.appendChild(c)}else We(a,B);if(!t.rotatable){var g=a.querySelectorAll('li[class*="rotate"]');We(g,U),Pe(g,(function(e){a.appendChild(e)}))}if(t.inline)We(l,P),je(o,{zIndex:t.zIndexInline}),"static"===window.getComputedStyle(i).position&&je(i,{position:"relative"}),i.insertBefore(o,e.nextSibling);else{We(l,X),We(o,M),We(o,N),We(o,B),je(o,{zIndex:t.zIndex});var f=t.container;Se(f)&&(f=e.ownerDocument.querySelector(f)),f||(f=this.body),f.appendChild(o)}t.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,Me(t.ready)&&_e(e,ce,t.ready,{once:!0}),!1!==$e(e,ce)?this.ready&&t.inline&&this.view(this.index):this.ready=!1}}}],n=[{key:"noConflict",value:function(){return window.Viewer=ut,e}},{key:"setDefaults",value:function(e){Ye(T,Ne(e)&&e)}}],i&&A(t.prototype,i),n&&A(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();Ye(mt.prototype,at,st,lt,ht,ct);const vt={key:0},gt={id:"viewerjs"},ft=["src"],pt={__name:"index",props:{url:{type:String,default:""},visible:{type:Boolean,default:!1},urlList:Array,currentIndex:{type:Number,default:0}},emits:["close"],setup(e,{emit:t}){const m=e,v=t,g=i([]);n((()=>{m.url?g.value.push(m.url):m.urlList&&(g.value=m.urlList),g.value.length>0&&f()}));const f=()=>{o((()=>{const e=document.getElementById("viewerjs"),t=new mt(e,{title:!1,navbar:!0,hide:()=>{t.destroy(),v("close",!1)}});t.view(m.currentIndex),t.show()}))};return(t,i)=>e.visible?(r(),a("div",vt,[s(h("div",gt,[(r(!0),a(c,null,u(g.value,((e,t)=>(r(),a("img",{src:e,key:t},null,8,ft)))),128))],512),[[l,!1]])])):d("",!0)}},bt={class:"guns-body guns-body-card"};e("default",{__name:"index",setup(e){const t=i(""),n=i(0),o=i(!1),s=()=>{o.value=!1,t.value=[],currentIndex.value=0},l=()=>{t.value=[w],n.value=0,o.value=!0};return(e,i)=>{const h=p,c=b,u=pt;return r(),a("div",bt,[m(c,{title:"图片预览",bordered:!1},{default:v((()=>[m(h,{onClick:l},{default:v((()=>i[0]||(i[0]=[g("点击预览")]))),_:1,__:[0]})])),_:1}),o.value?(r(),f(u,{key:0,visible:o.value,urlList:t.value,currentIndex:n.value,onClose:s},null,8,["visible","urlList","currentIndex"])):d("",!0)])}}})}}}));
