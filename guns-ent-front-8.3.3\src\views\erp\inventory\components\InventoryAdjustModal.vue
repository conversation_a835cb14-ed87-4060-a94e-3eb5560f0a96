<template>
  <a-modal
    :visible="visible"
    title="库存调整"
    :width="800"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确认调整
      </a-button>
    </template>

    <div class="inventory-adjust-content">
      <!-- 调整提示 -->
      <a-alert
        message="库存调整提醒"
        description="库存调整将直接影响商品库存数量和价值，请谨慎操作。调整后的库存变化将记录在库存历史中。"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />

      <!-- 商品信息 -->
      <a-card v-if="data.productId" title="商品信息" size="small" style="margin-bottom: 16px">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="商品名称">
            <span class="product-name">{{ data.productName }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="商品编码">
            {{ data.productCode }}
          </a-descriptions-item>
          <a-descriptions-item label="当前库存">
            <span :class="getStockClass(data.currentStock, data.minStock)">
              {{ formatStock(data.currentStock, data.pricingType) }}
              {{ getStockUnit(data.pricingType, data.unit) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="库存价值">
            ¥{{ formatAmount(data.totalValue) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 调整表单 -->
      <a-card title="调整信息" size="small">
        <a-form ref="formRef" :model="adjustForm" :rules="rules" layout="vertical">
          <!-- 商品选择（批量调整时显示） -->
          <a-form-item v-if="!data.productId" label="选择商品" name="productId" required>
            <product-selector 
              v-model:value="adjustForm.productId" 
              :filter="{ businessModeList: ['PURCHASE_SALE', 'CONSIGNMENT'] }"
              @change="onProductChange"
            />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="调整类型" name="adjustType" required>
                <a-select v-model:value="adjustForm.adjustType" placeholder="请选择调整类型">
                  <a-select-option value="INCREASE">增加库存</a-select-option>
                  <a-select-option value="DECREASE">减少库存</a-select-option>
                  <a-select-option value="SET">设置库存</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调整数量" name="adjustQuantity" required>
                <a-input-number
                  v-model:value="adjustForm.adjustQuantity"
                  :min="getMinQuantity()"
                  :precision="getPrecision()"
                  :step="getStep()"
                  style="width: 100%"
                  :placeholder="getQuantityPlaceholder()"
                >
                  <template #addonAfter>
                    {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
                  </template>
                </a-input-number>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="调整原因" name="adjustReason" required>
                <a-select v-model:value="adjustForm.adjustReason" placeholder="请选择调整原因">
                  <a-select-option value="STOCKTAKING">盘点调整</a-select-option>
                  <a-select-option value="DAMAGE">商品损坏</a-select-option>
                  <a-select-option value="LOSS">商品丢失</a-select-option>
                  <a-select-option value="EXPIRE">商品过期</a-select-option>
                  <a-select-option value="RETURN">退货入库</a-select-option>
                  <a-select-option value="OTHER">其他原因</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="单位成本" name="unitCost">
                <a-input-number
                  v-model:value="adjustForm.unitCost"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                  placeholder="请输入单位成本（可选）"
                >
                  <template #addonBefore>¥</template>
                </a-input-number>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="调整备注" name="remark">
            <a-textarea
              v-model:value="adjustForm.remark"
              placeholder="请输入调整备注"
              :rows="3"
              :maxlength="200"
              showCount
            />
          </a-form-item>
        </a-form>

        <!-- 调整预览 -->
        <a-card v-if="adjustPreview.show" title="调整预览" size="small" style="margin-top: 16px; background: #fafafa;">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="调整前库存">
              {{ formatStock(adjustPreview.beforeStock, selectedProduct.pricingType) }}
              {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
            </a-descriptions-item>
            <a-descriptions-item label="调整后库存">
              <span :class="getStockClass(adjustPreview.afterStock, selectedProduct.minStock)">
                {{ formatStock(adjustPreview.afterStock, selectedProduct.pricingType) }}
                {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="数量变化">
              <span :class="getQuantityChangeClass(adjustPreview.quantityChange)">
                {{ formatQuantityChange(adjustPreview.quantityChange) }}
                {{ getStockUnit(selectedProduct.pricingType, selectedProduct.unit) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="价值变化">
              <span v-if="adjustPreview.valueChange" :class="getValueChangeClass(adjustPreview.valueChange)">
                {{ formatValueChange(adjustPreview.valueChange) }}
              </span>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { InventoryApi } from '../api/InventoryApi';
import ProductSelector from '@/components/erp/ProductSelector.vue';

export default {
  name: 'InventoryAdjustModal',
  components: {
    ProductSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'ok'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const loading = ref(false);
    const selectedProduct = ref({});

    // 调整表单
    const adjustForm = reactive({
      productId: null,
      adjustType: 'INCREASE',
      adjustQuantity: null,
      adjustReason: 'STOCKTAKING',
      unitCost: null,
      remark: ''
    });

    // 调整预览
    const adjustPreview = reactive({
      show: false,
      beforeStock: 0,
      afterStock: 0,
      quantityChange: 0,
      valueChange: 0
    });

    // 表单验证规则
    const rules = {
      productId: [
        { required: true, message: '请选择商品', trigger: 'change' }
      ],
      adjustType: [
        { required: true, message: '请选择调整类型', trigger: 'change' }
      ],
      adjustQuantity: [
        { required: true, message: '请输入调整数量', trigger: 'blur' },
        { type: 'number', min: 0.001, message: '调整数量必须大于0', trigger: 'blur' }
      ],
      adjustReason: [
        { required: true, message: '请选择调整原因', trigger: 'change' }
      ]
    };

    // 监听props.data变化
    watch(() => props.data, (newData) => {
      if (newData && newData.productId) {
        selectedProduct.value = { ...newData };
        adjustForm.productId = newData.productId;
        updatePreview();
      }
    }, { immediate: true });

    // 监听表单变化，更新预览
    watch([
      () => adjustForm.adjustType,
      () => adjustForm.adjustQuantity,
      () => adjustForm.unitCost
    ], () => {
      updatePreview();
    });

    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      if (visible) {
        if (!props.data.productId) {
          resetForm();
        }
      }
    });

    // 重置表单
    const resetForm = () => {
      Object.assign(adjustForm, {
        productId: null,
        adjustType: 'INCREASE',
        adjustQuantity: null,
        adjustReason: 'STOCKTAKING',
        unitCost: null,
        remark: ''
      });
      selectedProduct.value = {};
      adjustPreview.show = false;
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    };

    // 商品变化事件
    const onProductChange = (productId, product) => {
      if (product) {
        selectedProduct.value = { ...product };
        updatePreview();
      } else {
        selectedProduct.value = {};
        adjustPreview.show = false;
      }
    };

    // 更新调整预览
    const updatePreview = () => {
      if (!selectedProduct.value.productId || !adjustForm.adjustQuantity) {
        adjustPreview.show = false;
        return;
      }

      const currentStock = parseFloat(selectedProduct.value.currentStock) || 0;
      const adjustQuantity = parseFloat(adjustForm.adjustQuantity) || 0;
      const unitCost = parseFloat(adjustForm.unitCost) || 0;

      let afterStock = currentStock;
      let quantityChange = 0;

      switch (adjustForm.adjustType) {
        case 'INCREASE':
          afterStock = currentStock + adjustQuantity;
          quantityChange = adjustQuantity;
          break;
        case 'DECREASE':
          afterStock = Math.max(0, currentStock - adjustQuantity);
          quantityChange = -adjustQuantity;
          break;
        case 'SET':
          afterStock = adjustQuantity;
          quantityChange = adjustQuantity - currentStock;
          break;
      }

      adjustPreview.show = true;
      adjustPreview.beforeStock = currentStock;
      adjustPreview.afterStock = afterStock;
      adjustPreview.quantityChange = quantityChange;
      adjustPreview.valueChange = unitCost > 0 ? quantityChange * unitCost : 0;
    };

    // 获取最小数量
    const getMinQuantity = () => {
      if (adjustForm.adjustType === 'DECREASE') {
        return 0.001;
      }
      return 0.001;
    };

    // 获取精度
    const getPrecision = () => {
      return selectedProduct.value.pricingType === 'WEIGHT' ? 3 : 0;
    };

    // 获取步长
    const getStep = () => {
      return selectedProduct.value.pricingType === 'WEIGHT' ? 0.001 : 1;
    };

    // 获取数量占位符
    const getQuantityPlaceholder = () => {
      switch (adjustForm.adjustType) {
        case 'INCREASE':
          return '请输入增加数量';
        case 'DECREASE':
          return '请输入减少数量';
        case 'SET':
          return '请输入设置数量';
        default:
          return '请输入数量';
      }
    };

    // 获取库存单位
    const getStockUnit = (pricingType, unit) => {
      switch (pricingType) {
        case 'WEIGHT':
          return 'kg';
        case 'PIECE':
          return '件';
        case 'NORMAL':
        case 'VARIABLE':
        default:
          return unit || '个';
      }
    };

    // 格式化库存数量
    const formatStock = (stock, pricingType) => {
      if (!stock) return '0';
      const precision = pricingType === 'WEIGHT' ? 3 : 0;
      return parseFloat(stock).toFixed(precision);
    };

    // 格式化金额
    const formatAmount = (amount) => {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    };

    // 获取库存样式类
    const getStockClass = (currentStock, minStock) => {
      const current = parseFloat(currentStock) || 0;
      const min = parseFloat(minStock) || 0;
      
      if (current <= 0) return 'stock-danger';
      if (current <= min) return 'stock-warning';
      return 'stock-normal';
    };

    // 格式化数量变化
    const formatQuantityChange = (quantityChange) => {
      const change = parseFloat(quantityChange) || 0;
      return change >= 0 ? `+${change}` : `${change}`;
    };

    // 获取数量变化样式类
    const getQuantityChangeClass = (quantityChange) => {
      const change = parseFloat(quantityChange) || 0;
      return change >= 0 ? 'quantity-increase' : 'quantity-decrease';
    };

    // 格式化价值变化
    const formatValueChange = (valueChange) => {
      const value = parseFloat(valueChange) || 0;
      return value >= 0 ? `+¥${value.toFixed(2)}` : `-¥${Math.abs(value).toFixed(2)}`;
    };

    // 获取价值变化样式类
    const getValueChangeClass = (valueChange) => {
      const value = parseFloat(valueChange) || 0;
      return value >= 0 ? 'value-increase' : 'value-decrease';
    };

    // 取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 提交
    const handleSubmit = () => {
      formRef.value.validate().then(async () => {
        loading.value = true;
        try {
          const submitData = {
            productId: adjustForm.productId,
            adjustType: adjustForm.adjustType,
            adjustQuantity: adjustForm.adjustQuantity,
            adjustReason: adjustForm.adjustReason,
            unitCost: adjustForm.unitCost,
            remark: adjustForm.remark
          };

          // 这里调用库存调整API
          // await InventoryApi.adjustStock(submitData);
          
          message.success('库存调整成功');
          emit('ok');
        } catch (error) {
          message.error('库存调整失败：' + (error.message || '未知错误'));
        } finally {
          loading.value = false;
        }
      });
    };

    return {
      formRef,
      loading,
      selectedProduct,
      adjustForm,
      adjustPreview,
      rules,
      onProductChange,
      getMinQuantity,
      getPrecision,
      getStep,
      getQuantityPlaceholder,
      getStockUnit,
      formatStock,
      formatAmount,
      getStockClass,
      formatQuantityChange,
      getQuantityChangeClass,
      formatValueChange,
      getValueChangeClass,
      handleCancel,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.inventory-adjust-content {
  max-height: 70vh;
  overflow-y: auto;
}

.product-name {
  font-weight: 500;
  color: #1890ff;
}

.stock-normal {
  color: #52c41a;
  font-weight: 500;
}

.stock-warning {
  color: #faad14;
  font-weight: 500;
}

.stock-danger {
  color: #ff4d4f;
  font-weight: 500;
}

.quantity-increase {
  color: #52c41a;
  font-weight: 500;
}

.quantity-decrease {
  color: #ff4d4f;
  font-weight: 500;
}

.value-increase {
  color: #52c41a;
  font-weight: 500;
}

.value-decrease {
  color: #ff4d4f;
  font-weight: 500;
}
</style>
