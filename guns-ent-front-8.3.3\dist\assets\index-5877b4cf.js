import{_ as R,aP as D,s as N,o as O,bX as d,bY as y,bu as $,a as f,f as w,w as n,m as G,S as P,b as a,d as e,t as r,c as g,e as S,F as h,g as _,l as T,u as U,v as Y,G as H,B as W,H as x,a0 as A}from"./index-18a1ea24.js";/* empty css              */const K=D({name:"InitPage",components:{},setup(){let o=N({loading:!0,form:{},configList:[],submitLoading:!1});O(async()=>{if(await d.getInitConfigFlag()){y.push("/");return}o.loading=!1,o.configList=await d.getInitConfigList();let i=window.location.host;window.location.href.indexOf("/guns-devops")===-1&&(i=i+"/api"),o.configList.initConfigGroupList.forEach(C=>{for(const t of C.configInitItemList)t.configCode==="SYS_SERVER_DEPLOY_HOST"||t.configCode==="WEB_SOCKET_WS_URL"?o.form[t.configCode]=t.configValue.replace("localhost:8080",i):o.form[t.configCode]=t.configValue})});const s=async()=>{o.submitLoading=!0;let l={sysConfigs:o.form};d.initConfig(l).then(i=>{G.success(i.message),y.push("/")}).finally(()=>{o.submitLoading=!1})},m=async()=>{o.loading=!0,o.configList=await d.getInitConfigList(),o.loading=!1;for(const l of o.configList)o.form[l.configCode]=l.configValue};return{...$(o),onSubmit:s,onReset:m}}}),M={class:"guns-body",style:{"background-color":"#fafafa"}},X={class:"content-row"},j={class:"content-row content-item"},q={class:"content-row"},z={class:"button-item"};function J(o,s,m,l,i,C){const t=T,L=U,u=Y,b=H,v=W,k=x,I=A,V=P;return f(),w(V,{spinning:o.loading},{default:n(()=>[a("div",M,[e(I,null,{default:n(()=>[e(b,null,{default:n(()=>[e(u,{span:18,offset:3,class:"content"},{default:n(()=>[s[2]||(s[2]=a("div",{class:"content-top"},[a("h5",null,"\u9996\u6B21\u5B89\u88C5\u7A0B\u5E8F")],-1)),e(k,{model:o.form,"label-col":{span:6},"wrapper-col":{span:16}},{default:n(()=>[a("div",X,r(o.configList.description),1),(f(!0),g(h,null,S(o.configList.initConfigGroupList,(p,B)=>(f(),g("div",{key:B+"con"},[a("div",j,r(p.title),1),a("div",q,r(p.description),1),(f(!0),g(h,null,S(p.configInitItemList,(c,E)=>(f(),w(b,{key:E+"init"},{default:n(()=>[e(u,{span:16},{default:n(()=>[e(L,null,{label:n(()=>[_(r(c.configName),1)]),default:n(()=>[e(t,{name:c.configCode,value:o.form[c.configCode],"onUpdate:value":F=>o.form[c.configCode]=F,placeholder:c.configDescription},null,8,["name","value","onUpdate:value","placeholder"])]),_:2},1024)]),_:2},1024),e(u,{span:8,style:{color:"#98999b"}},{default:n(()=>[_(r(c.configDescription),1)]),_:2},1024)]),_:2},1024))),128))]))),128)),a("div",z,[e(L,{"wrapper-col":{span:14,offset:4}},{default:n(()=>[e(v,{type:"primary",loading:o.submitLoading,onClick:o.onSubmit},{default:n(()=>s[0]||(s[0]=[_("\u63D0\u4EA4")])),_:1,__:[0]},8,["loading","onClick"]),e(v,{onClick:o.onReset,style:{"margin-left":"10px"}},{default:n(()=>s[1]||(s[1]=[_("\u91CD\u7F6E")])),_:1,__:[1]},8,["onClick"])]),_:1})])]),_:1},8,["model"])]),_:1,__:[2]})]),_:1})]),_:1})])]),_:1},8,["spinning"])}const oo=R(K,[["render",J],["__scopeId","data-v-a7487e2d"]]);export{oo as default};
