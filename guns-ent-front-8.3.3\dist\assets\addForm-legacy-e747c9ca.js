System.register(["./index-legacy-ee1db0c7.js","./index-legacy-94a6fc23.js","./index-legacy-9a185ac3.js","./productCategoryApi-legacy-247b2407.js"],(function(e,a){"use strict";var t,r,l,o,d,n,u,c,s,i,m,g,f,v,y,p,h,_,b,I,w,k;return{setters:[e=>{t=e._,r=e.r,l=e.s,o=e.X,d=e.a,n=e.f,u=e.w,c=e.d,s=e.g,i=e.m,m=e.l,g=e.u,f=e.v,v=e.G,y=e.as,p=e.y,h=e.W,_=e.J,b=e.$,I=e.H,w=e.M},null,null,e=>{k=e.P}],execute:function(){var a=document.createElement("style");a.textContent=".ant-form-item[data-v-459c9ad7]{margin-bottom:16px}\n",document.head.appendChild(a);const L={name:"ProductCategoryAddForm",props:{visible:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:visible","ok"],setup(e,{emit:a}){const t=r(),d=r(!1),n=r([]),u=r("1级 - 一级分类"),c=l({categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),s=e=>{a("update:visible",e),e||m()},m=()=>{var e;null===(e=t.value)||void 0===e||e.resetFields(),Object.assign(c,{categoryId:null,categoryCode:"",categoryName:"",parentId:void 0,categoryLevel:1,sortOrder:0,status:"Y",remark:""}),u.value="1级 - 一级分类"},g=e=>({1:"1级 - 一级分类",2:"2级 - 二级分类",3:"3级 - 三级分类",4:"4级 - 四级分类",5:"5级 - 五级分类"}[e]||`${e}级 - 未知`),f=()=>{const e=(e=>{if(!e)return 1;const a=(e,t)=>{for(const r of e){if(r.categoryId===t)return r.categoryLevel||1;if(r.children&&r.children.length>0){const e=a(r.children,t);if(e)return e}}return null},t=a(n.value,e);return t?Math.min(t+1,5):1})(c.parentId);c.categoryLevel=e,u.value=g(e)},v=e=>Array.isArray(e)?e.filter((e=>!!(e&&e.categoryId&&e.categoryName)&&(e.title=e.categoryName,e.key=String(e.categoryId),e.value=String(e.categoryId),e.children&&Array.isArray(e.children)&&(e.children=v(e.children)),!0))):[],y=(e,a)=>e.filter((e=>e.categoryId!==a&&(e.children&&e.children.length>0&&(e.children=y(e.children,a)),!0)));return o((()=>e.data),(e=>{e&&Object.keys(e).length>0&&(Object.assign(c,e),e.categoryLevel?u.value=g(e.categoryLevel):f())}),{immediate:!0}),o((()=>e.visible),(a=>{a?((async()=>{try{let e=await k.findTree()||[];e=v(e),c.categoryId?n.value=y(e,c.categoryId):n.value=e}catch(e){console.error("加载分类树失败:",e),n.value=[]}})(),e.data&&Object.keys(e.data).length>0&&(Object.assign(c,e.data),setTimeout((()=>{e.data.categoryLevel?u.value=g(e.data.categoryLevel):f()}),100))):m()})),o((()=>c.parentId),(()=>{f()})),{formRef:t,loading:d,form:c,rules:{categoryCode:[{required:!0,message:"请输入分类编码",trigger:"blur"}],categoryName:[{required:!0,message:"请输入分类名称",trigger:"blur"}],categoryLevel:[{required:!0,message:"请选择分类层级",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},categoryTreeData:n,categoryLevelText:u,updateVisible:s,save:async()=>{try{await t.value.validate(),d.value=!0;const e=!!c.categoryId,r=e?k.edit:k.add,l=e?"编辑成功":"新增成功";await r(c),i.success(l),a("ok"),s(!1)}catch(e){console.error("保存分类失败:",e),i.error("保存失败")}finally{d.value=!1}},filterTreeNode:(e,a)=>a.title&&a.title.toLowerCase().includes(e.toLowerCase())}}};e("default",t(L,[["render",function(e,a,t,r,l,o){const i=m,k=g,L=f,C=v,O=y,x=p,N=h,j=_,T=b,U=I,A=w;return d(),n(A,{title:r.form.categoryId?"编辑产品分类":"新增产品分类",visible:t.visible,"confirm-loading":r.loading,width:800,onOk:r.save,onCancel:a[7]||(a[7]=e=>r.updateVisible(!1))},{default:u((()=>[c(U,{ref:"formRef",model:r.form,rules:r.rules,"label-col":{md:{span:6},sm:{span:24}},"wrapper-col":{md:{span:18},sm:{span:24}}},{default:u((()=>[c(C,{gutter:16},{default:u((()=>[c(L,{md:12,sm:24},{default:u((()=>[c(k,{label:"分类编码",name:"categoryCode"},{default:u((()=>[c(i,{value:r.form.categoryCode,"onUpdate:value":a[0]||(a[0]=e=>r.form.categoryCode=e),placeholder:"请输入分类编码","allow-clear":""},null,8,["value"])])),_:1})])),_:1}),c(L,{md:12,sm:24},{default:u((()=>[c(k,{label:"分类名称",name:"categoryName"},{default:u((()=>[c(i,{value:r.form.categoryName,"onUpdate:value":a[1]||(a[1]=e=>r.form.categoryName=e),placeholder:"请输入分类名称","allow-clear":""},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(C,{gutter:16},{default:u((()=>[c(L,{md:12,sm:24},{default:u((()=>[c(k,{label:"父级分类",name:"parentId"},{default:u((()=>[c(O,{value:r.form.parentId,"onUpdate:value":a[2]||(a[2]=e=>r.form.parentId=e),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":r.categoryTreeData,placeholder:"请选择父级分类","tree-default-expand-all":"","field-names":{children:"children",title:"title",key:"key",value:"value"},"allow-clear":"","show-search":"","filter-tree-node":r.filterTreeNode},null,8,["value","tree-data","filter-tree-node"])])),_:1})])),_:1}),c(L,{md:12,sm:24},{default:u((()=>[c(k,{label:"分类层级",name:"categoryLevel"},{default:u((()=>[c(i,{value:r.categoryLevelText,"onUpdate:value":a[3]||(a[3]=e=>r.categoryLevelText=e),placeholder:"根据父级分类自动设置",readonly:"",style:{"background-color":"#f5f5f5"}},null,8,["value"])])),_:1})])),_:1})])),_:1}),c(C,{gutter:16},{default:u((()=>[c(L,{md:12,sm:24},{default:u((()=>[c(k,{label:"排序号",name:"sortOrder"},{default:u((()=>[c(x,{value:r.form.sortOrder,"onUpdate:value":a[4]||(a[4]=e=>r.form.sortOrder=e),placeholder:"请输入排序号",min:0,style:{width:"100%"}},null,8,["value"])])),_:1})])),_:1}),c(L,{md:12,sm:24},{default:u((()=>[c(k,{label:"状态",name:"status"},{default:u((()=>[c(j,{value:r.form.status,"onUpdate:value":a[5]||(a[5]=e=>r.form.status=e),placeholder:"请选择状态"},{default:u((()=>[c(N,{value:"Y"},{default:u((()=>a[8]||(a[8]=[s("启用")]))),_:1,__:[8]}),c(N,{value:"N"},{default:u((()=>a[9]||(a[9]=[s("停用")]))),_:1,__:[9]})])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),c(C,{gutter:16},{default:u((()=>[c(L,{span:24},{default:u((()=>[c(k,{label:"备注",name:"remark","label-col":{md:{span:3},sm:{span:24}},"wrapper-col":{md:{span:21},sm:{span:24}}},{default:u((()=>[c(T,{value:r.form.remark,"onUpdate:value":a[6]||(a[6]=e=>r.form.remark=e),placeholder:"请输入备注",rows:3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","visible","confirm-loading","onOk"])}],["__scopeId","data-v-459c9ad7"]]))}}}));
