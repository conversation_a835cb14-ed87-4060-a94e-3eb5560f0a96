import{_ as i,bv as n,a as m,f as _,w as e,b as p,d as t,aR as l,v as d,G as c}from"./index-18a1ea24.js";import x from"./userinfo-6478800e.js";import r from"./company-overview-e3f00ba3.js";import u from"./operation-records-a37611f8.js";import f from"./common-functions-ec28eefa.js";import{H as g}from"./index-5300631d.js";/* empty css              *//* empty css              */import"./HomeApi-75ad5066.js";/* empty css              *//* empty css              */const y={class:"main"},v={__name:"index",setup(h){return(b,w)=>{const o=d,s=c,a=n("permission");return m(),_(g,null,{default:e(()=>[p("div",y,[t(s,{gutter:10,style:{"margin-bottom":"10px"}},{default:e(()=>[t(o,{xs:24,sm:24,md:12,lg:12,xl:12,style:{height:"300px","margin-bottom":"10px"}},{default:e(()=>[t(f)]),_:1}),t(o,{xs:24,sm:24,md:12,lg:12,xl:12,style:{height:"300px"}},{default:e(()=>[t(x)]),_:1})]),_:1}),l((m(),_(s,{gutter:10,style:{"margin-bottom":"10px"}},{default:e(()=>[t(o,{xs:24,sm:24,md:12,lg:12,xl:12,style:{"margin-bottom":"10px"}},{default:e(()=>[t(r,{type:"system"})]),_:1}),t(o,{xs:24,sm:24,md:12,lg:12,xl:12},{default:e(()=>[t(r,{type:"current"})]),_:1})]),_:1})),[[a,["COMPANY_STAT_INFO"]]]),t(s,{gutter:10},{default:e(()=>[t(o,{xs:24,sm:24,md:12,lg:12,xl:12,style:{height:"300px","margin-bottom":"10px"}},{default:e(()=>[t(u)]),_:1}),t(o,{xs:24,sm:24,md:12,lg:12,xl:12})]),_:1})])]),_:1})}}},T=i(v,[["__scopeId","data-v-e69d5200"]]);export{T as default};
