import{s as c,a as i,f as g,w as t,d as e,g as p,l as v,u as b,v as y,W as w,J as N,G as C,H as T}from"./index-18a1ea24.js";const q={__name:"template-form",props:{form:Object,isUpdate:<PERSON><PERSON><PERSON>},setup(l){const s=c({templateName:[{required:!0,message:"\u8BF7\u8F93\u5165\u6A21\u677F\u540D\u79F0",type:"string",trigger:"blur"}],templateCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u6A21\u677F\u7F16\u7801",type:"string",trigger:"blur "}],templateType:[{required:!0,message:"\u8BF7\u8F93\u5165\u6A21\u677F\u7C7B\u578B",type:"number",trigger:"blur"}]});return(x,a)=>{const u=v,n=b,m=y,r=w,_=N,f=C,d=T;return i(),g(d,{ref:"formRef",model:l.form,rules:s,layout:"vertical"},{default:t(()=>[e(f,{gutter:20},{default:t(()=>[e(m,{span:24},{default:t(()=>[e(n,{label:"\u6A21\u677F\u540D\u79F0:",name:"templateName"},{default:t(()=>[e(u,{value:l.form.templateName,"onUpdate:value":a[0]||(a[0]=o=>l.form.templateName=o),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u540D\u79F0","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(m,{span:24},{default:t(()=>[e(n,{label:"\u6A21\u677F\u7F16\u7801:",name:"templateCode"},{default:t(()=>[e(u,{value:l.form.templateCode,"onUpdate:value":a[1]||(a[1]=o=>l.form.templateCode=o),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u7F16\u7801","allow-clear":"",autocomplete:"off"},null,8,["value"])]),_:1})]),_:1}),e(m,{span:24},{default:t(()=>[e(n,{label:"\u6A21\u677F\u7C7B\u578B:",name:"templateType"},{default:t(()=>[e(_,{value:l.form.templateType,"onUpdate:value":a[2]||(a[2]=o=>l.form.templateType=o),placeholder:"\u8BF7\u9009\u62E9\u6A21\u677F\u7C7B\u578B","allow-clear":"",autocomplete:"off"},{default:t(()=>[e(r,{value:1},{default:t(()=>a[3]||(a[3]=[p("\u7CFB\u7EDF\u7C7B\u578B")])),_:1,__:[3]}),e(r,{value:2},{default:t(()=>a[4]||(a[4]=[p("\u4E1A\u52A1\u7C7B\u578B")])),_:1,__:[4]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{q as default};
