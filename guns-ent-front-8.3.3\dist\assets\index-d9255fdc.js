import{_ as A}from"./index-d0cfb2ce.js";import{b3 as C,r as u,L as E,o as K,bv as h,a as _,c as l,d as n,w as s,b as a,g as i,aR as y,p as B,bq as D,q as I}from"./index-18a1ea24.js";import O from"./security-158fdae3.js";/* empty css              */const x={class:"guns-layout"},L={class:"guns-layout-sidebar width-100 p-t-12"},M={class:"sidebar-content"},N={class:"sidebar-content"},R={class:"guns-layout-content"},V=Object.assign({name:"BackendSecurity"},{__name:"index",setup(w){const S=C(),c=u(["1"]),o=u([]),r=u(""),d=E(()=>{var e;return(e=S.authorities)!=null?e:[]}),p=({key:e})=>{r.value=e},f=()=>{c.value=["1"]};return K(()=>{let e="";o.value=[],d.value.includes("PASSWORD_STRATEGY_CONFIG")?e="1":d.value.includes("BLACK_WHITE_LIST_UPDATE")&&(e="2"),e&&(o.value=[e],p({key:e}))}),(e,t)=>{const m=B,g=D,b=I,T=A,v=h("permission");return _(),l("div",x,[n(T,{resizable:!1},{content:s(()=>[a("div",R,[n(O,{currentMenuSelect:r.value},null,8,["currentMenuSelect"])])]),default:s(()=>[a("div",L,[a("div",M,[a("div",N,[n(b,{selectedKeys:o.value,"onUpdate:selectedKeys":t[0]||(t[0]=k=>o.value=k),class:"sidebar-menu",mode:"inline","open-keys":c.value,onSelect:p,onOpenChange:f},{default:s(()=>[n(g,{key:"1"},{title:s(()=>t[1]||(t[1]=[i("\u7B56\u7565\u914D\u7F6E")])),default:s(()=>[y((_(),l("div",null,[n(m,{key:"1"},{default:s(()=>t[2]||(t[2]=[i(" \u5BC6\u7801\u7B56\u7565 ")])),_:1,__:[2]})])),[[v,["PASSWORD_STRATEGY_CONFIG"]]]),y((_(),l("div",null,[n(m,{key:"2"},{default:s(()=>t[3]||(t[3]=[i(" \u9ED1\u767D\u540D\u5355 ")])),_:1,__:[3]})])),[[v,["BLACK_WHITE_LIST_UPDATE"]]])]),_:1})]),_:1},8,["selectedKeys","open-keys"])])])])]),_:1})])}}});export{V as default};
