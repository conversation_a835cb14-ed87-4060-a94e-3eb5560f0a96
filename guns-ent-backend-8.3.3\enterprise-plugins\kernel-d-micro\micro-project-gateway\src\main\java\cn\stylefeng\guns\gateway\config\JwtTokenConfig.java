package cn.stylefeng.guns.gateway.config;

import cn.stylefeng.roses.kernel.jwt.JwtTokenOperator;
import cn.stylefeng.roses.kernel.jwt.api.JwtApi;
import cn.stylefeng.roses.kernel.jwt.api.pojo.config.JwtConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * jwt工具的配置
 *
 * <AUTHOR>
 * @date 2021/5/13 21:33
 */
@Configuration
public class JwtTokenConfig {

    /**
     * jwt操作工具类的配置
     *
     * <AUTHOR>
     * @date 2020/12/1 14:40
     */
    @Bean
    public JwtApi jwtApi() {
        return new JwtTokenOperator(jwtConfig());
    }

    /**
     * 获取jwt配置
     *
     * <AUTHOR>
     * @date 2021/5/13 21:34
     */
    @Bean
    @ConfigurationProperties(prefix = "jwt")
    public JwtConfig jwtConfig() {
        return new JwtConfig();
    }

}
