package cn.stylefeng.roses.kernel.websocket.api.endpoint;


import jakarta.websocket.HandshakeResponse;
import jakarta.websocket.server.HandshakeRequest;
import jakarta.websocket.server.ServerEndpointConfig;

/**
 * WebSocket握手阶段获取与当前WebSocket会话关联的HTTP会话（`HttpSession`）并将其存储起来。
 * <p>
 * WebSocket协议本身是独立于HTTP协议的，但在建立WebSocket连接时，通常会首先通过 HTTP 请求进行一次“握手”，然后才升级到 WebSocket 协议。
 * <p>
 * 在这个握手过程中，可以访问到一些HTTP特有的信息，比如HTTP会话。
 *
 * <AUTHOR>
 * @since 2024/1/10 22:19
 */
public class GetHttpSessionConfigurator extends ServerEndpointConfig.Configurator {

    @Override
    public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
        // clean
        super.modifyHandshake(sec, request, response);
    }

}
