# SaaS 租户设计架构

## 1. 系统概述

JavaGuns Enterprise 采用了基于数据库共享、应用隔离的 SaaS 多租户架构模式。系统通过租户标识（tenant_id）实现数据隔离，通过功能包（package）机制实现功能权限控制，为不同租户提供定制化的服务体验。

### 1.1 架构特点

- **共享数据库模式**：所有租户共享同一个数据库实例，通过租户ID进行数据隔离
- **应用级隔离**：在应用层面实现租户间的数据和功能隔离
- **功能包机制**：通过功能包（Package）控制租户可访问的功能模块
- **统一认证**：支持租户独立的用户体系和权限管理
- **弹性扩展**：支持租户功能的动态开通和关闭

### 1.2 技术栈

- **后端框架**：Spring Boot 3.2.10 + MyBatis-Plus
- **数据库**：MySQL 5.7+
- **前端框架**：Vue 3.5.11 + Ant Design Vue 3.2.17
- **租户模块**：kernel-d-saas（企业级插件）

## 2. SaaS 租户架构设计

### 2.1 数据库表结构设计

#### 2.1.1 租户主表 (ent_tenant)

```sql
CREATE TABLE `ent_tenant` (
  `tenant_id` bigint(20) NOT NULL COMMENT '主键id',
  `tenant_code` varchar(64) NOT NULL COMMENT '租户唯一标识',
  `tenant_name` varchar(255) NOT NULL COMMENT '租户名称',
  `tenant_logo` bigint(20) NULL DEFAULT NULL COMMENT '租户logo，存储文件id',
  `company_name` varchar(255) NULL DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(255) NULL DEFAULT NULL COMMENT '公司地址',
  `company_social_code` varchar(255) NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `email` varchar(255) NOT NULL COMMENT '注册邮箱',
  `safe_phone` varchar(20) NOT NULL COMMENT '安全手机（注册时的手机号）',
  `status_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `active_flag` char(1) NOT NULL DEFAULT 'Y' COMMENT '激活状态：Y-已激活，N-未激活',
  `active_date` datetime(0) NULL DEFAULT NULL COMMENT '租户开通时间',
  `expire_date` datetime(0) NULL DEFAULT NULL COMMENT '租户到期时间',
  `password` varchar(100) NOT NULL COMMENT '密码，加密方式为MD5',
  `password_salt` varchar(10) NULL DEFAULT NULL COMMENT '密码盐',
  `expand_field` json NULL COMMENT '拓展字段',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `del_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`tenant_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '租户信息';
```

**主要字段说明**：
- `tenant_code`: 租户唯一标识，用于租户识别和路由
- `status_flag`: 租户状态控制，支持启用/禁用
- `active_flag`: 激活状态，控制租户是否可用
- `expire_date`: 租户到期时间，支持租户生命周期管理
- `expand_field`: JSON扩展字段，支持租户个性化配置

#### 2.1.2 功能包表 (ent_tenant_package)

```sql
CREATE TABLE `ent_tenant_package` (
  `package_id` bigint(20) NOT NULL COMMENT '主键id',
  `package_name` varchar(255) NOT NULL COMMENT '功能包名称',
  `package_price` decimal(10, 2) NOT NULL COMMENT '功能包价格',
  `version_flag` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  PRIMARY KEY (`package_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '租户-功能包';
```

#### 2.1.3 租户功能包关联表 (ent_tenant_link)

```sql
CREATE TABLE `ent_tenant_link` (
  `tenant_link_id` bigint(20) NOT NULL COMMENT '主键id',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  `package_id` bigint(20) NOT NULL COMMENT '功能包id',
  `service_end_time` datetime(0) NULL DEFAULT NULL COMMENT '服务结束时间，空则是长期',
  `trial_flag` char(1) NOT NULL COMMENT '是否为试用：Y-试用，N-非试用',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否删除，Y-被删除，N-未删除',
  PRIMARY KEY (`tenant_link_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '租户和功能包的关联';
```

#### 2.1.4 功能包权限授权表 (ent_tenant_package_auth)

```sql
CREATE TABLE `ent_tenant_package_auth` (
  `package_auth_id` bigint(20) NOT NULL COMMENT '主键',
  `package_id` bigint(20) NOT NULL COMMENT '功能包id',
  `limit_type` tinyint(4) NOT NULL COMMENT '功能包绑定权限类型：1-菜单，2-功能',
  `business_id` bigint(20) NOT NULL COMMENT '业务id，为菜单id或菜单功能id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`package_auth_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '租户-功能包授权范围';
```

### 2.2 租户身份识别和路由机制

#### 2.2.1 租户识别方式

系统支持多种租户识别方式：

1. **URL路径识别**：通过URL中的租户标识进行识别
   ```
   https://domain.com/{tenant_code}/login
   ```

2. **子域名识别**：通过子域名进行租户识别
   ```
   https://{tenant_code}.domain.com/login
   ```

3. **Header识别**：通过HTTP Header传递租户标识
   ```
   X-Tenant-Code: tenant_code
   ```

4. **登录界面选择**：用户在登录时选择或输入租户编码

#### 2.2.2 租户上下文管理

系统通过租户上下文（TenantContext）管理当前请求的租户信息：

```java
public class TenantContext {
    private static final ThreadLocal<String> TENANT_CODE = new ThreadLocal<>();
    
    public static void setTenantCode(String tenantCode) {
        TENANT_CODE.set(tenantCode);
    }
    
    public static String getTenantCode() {
        return TENANT_CODE.get();
    }
    
    public static void clear() {
        TENANT_CODE.remove();
    }
}
```

### 2.3 数据隔离实现方式

#### 2.3.1 数据库层面隔离

系统采用共享数据库模式，通过以下方式实现数据隔离：

1. **租户字段添加**：在需要隔离的表中添加 `tenant_id` 字段
2. **SQL自动拦截**：通过MyBatis-Plus插件自动在SQL中添加租户条件
3. **数据权限过滤**：结合现有的数据权限系统进行双重过滤

#### 2.3.2 应用层面隔离

1. **服务层隔离**：在Service层自动注入租户条件
2. **缓存隔离**：缓存Key中包含租户标识
3. **文件存储隔离**：文件存储路径包含租户标识

### 2.4 安全机制

#### 2.4.1 数据安全

1. **强制租户过滤**：所有数据查询必须包含租户条件
2. **跨租户访问防护**：防止租户间数据泄露
3. **敏感数据加密**：租户敏感信息采用AES加密存储

#### 2.4.2 访问安全

1. **租户状态检查**：访问前检查租户状态和有效期
2. **功能权限验证**：基于功能包进行权限验证
3. **API访问限制**：限制租户API访问频率和范围

## 3. 租户管理功能

### 3.1 租户创建和配置流程

#### 3.1.1 租户注册流程

1. **注册申请**：用户填写租户基本信息
2. **邮箱验证**：发送验证邮件确认邮箱有效性
3. **审核流程**：管理员审核租户申请
4. **账户激活**：审核通过后激活租户账户
5. **功能包分配**：为租户分配初始功能包

#### 3.1.2 租户配置管理

```java
@RestController
@ApiResource(name = "租户管理")
public class TenantController {
    
    @PostResource(name = "创建租户", path = "/tenant/add")
    @ApiResource(name = "创建租户", requiredPermission = true, requirePermissionCode = "TENANT_ADD")
    public ResponseData add(@RequestBody TenantRequest request) {
        tenantService.add(request);
        return new SuccessResponseData();
    }
    
    @PostResource(name = "更新租户", path = "/tenant/edit")
    @ApiResource(name = "更新租户", requiredPermission = true, requirePermissionCode = "TENANT_EDIT")
    public ResponseData edit(@RequestBody TenantRequest request) {
        tenantService.edit(request);
        return new SuccessResponseData();
    }
}
```

### 3.2 租户权限和资源限制机制

#### 3.2.1 功能包权限控制

系统通过功能包机制实现租户功能权限控制：

1. **功能包定义**：定义不同的功能包和包含的功能
2. **权限绑定**：将菜单和功能权限绑定到功能包
3. **租户授权**：为租户分配相应的功能包
4. **动态控制**：支持功能包的动态开通和关闭

#### 3.2.2 资源限制机制

1. **存储限制**：限制租户文件存储空间
2. **用户数限制**：限制租户用户数量
3. **API调用限制**：限制租户API调用频率
4. **并发限制**：限制租户并发访问数

### 3.3 租户数据备份和恢复策略

#### 3.3.1 数据备份策略

1. **定时备份**：定期备份租户数据
2. **增量备份**：支持增量数据备份
3. **分租户备份**：按租户维度进行数据备份
4. **多地备份**：支持异地备份存储

#### 3.3.2 数据恢复策略

1. **快速恢复**：支持租户数据快速恢复
2. **选择性恢复**：支持指定时间点的数据恢复
3. **验证机制**：恢复后进行数据完整性验证
4. **回滚机制**：支持恢复操作的回滚

## 4. 新功能适配指南

### 4.1 租户隔离要求

#### 4.1.1 开发原则

1. **租户优先**：所有业务功能都要考虑租户隔离
2. **数据安全**：确保租户间数据不会泄露
3. **性能考虑**：租户隔离不应显著影响系统性能
4. **扩展性**：设计要考虑租户数量的扩展

#### 4.1.2 必须遵循的规范

1. **数据表设计**：新表必须包含 `tenant_id` 字段
2. **查询条件**：所有查询必须包含租户条件
3. **缓存设计**：缓存Key必须包含租户标识
4. **文件存储**：文件路径必须包含租户标识

### 4.2 数据库设计租户字段规范

#### 4.2.1 标准字段定义

```sql
-- 租户ID字段标准定义
`tenant_id` bigint(20) NOT NULL DEFAULT 1 COMMENT '租户ID',

-- 添加租户索引
KEY `idx_tenant_id` (`tenant_id`),

-- 复合索引示例（租户ID + 业务字段）
KEY `idx_tenant_business` (`tenant_id`, `business_id`),
```

#### 4.2.2 表结构设计示例

```sql
-- 新业务表设计示例
CREATE TABLE `business_table` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL DEFAULT 1 COMMENT '租户ID',
  `business_field1` varchar(100) NOT NULL COMMENT '业务字段1',
  `business_field2` varchar(200) NULL DEFAULT NULL COMMENT '业务字段2',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人ID',
  `del_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标志',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE,
  KEY `idx_tenant_status` (`tenant_id`, `status`) USING BTREE
) ENGINE = InnoDB COMMENT = '业务表';
```

### 4.3 API 接口设计的租户适配模式

#### 4.3.1 Controller层适配

```java
@RestController
@ApiResource(name = "业务管理", requiredPermission = true)
public class BusinessController {
    
    @Resource
    private BusinessService businessService;
    
    @PostResource(name = "新增业务", path = "/business/add")
    @ApiResource(name = "新增业务", requiredPermission = true, requirePermissionCode = "BUSINESS_ADD")
    public ResponseData add(@RequestBody BusinessRequest request) {
        // 自动注入租户ID
        businessService.add(request);
        return new SuccessResponseData();
    }
    
    @GetResource(name = "查询业务", path = "/business/page")
    @ApiResource(name = "查询业务", requiredPermission = true, requirePermissionCode = "BUSINESS_QUERY")
    public ResponseData page(BusinessRequest request) {
        // 查询时自动添加租户条件
        PageResult<BusinessResponse> result = businessService.findPage(request);
        return new SuccessResponseData(result);
    }
}
```

#### 4.3.2 Service层适配

```java
@Service
public class BusinessServiceImpl implements BusinessService {
    
    @Resource
    private BusinessMapper businessMapper;
    
    @Override
    @TenantFilter  // 租户过滤注解
    public void add(BusinessRequest request) {
        Business business = new Business();
        BeanUtil.copyProperties(request, business);
        
        // 自动设置租户ID
        business.setTenantId(TenantContext.getTenantCode());
        
        businessMapper.insert(business);
    }
    
    @Override
    @TenantFilter  // 租户过滤注解
    public PageResult<BusinessResponse> findPage(BusinessRequest request) {
        // MyBatis-Plus会自动添加租户条件
        LambdaQueryWrapper<Business> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Business::getStatus, StatusEnum.ENABLE.getCode());
        
        Page<Business> page = businessMapper.selectPage(PageFactory.defaultPage(), wrapper);
        return PageResultFactory.createPageResult(page);
    }
}
```

#### 4.3.3 Mapper层适配

```java
@Mapper
public interface BusinessMapper extends BaseMapper<Business> {
    
    /**
     * 自定义查询方法
     * 注意：自定义SQL也需要添加租户条件
     */
    @Select("SELECT * FROM business_table WHERE tenant_id = #{tenantId} AND status = #{status}")
    List<Business> selectByTenantAndStatus(@Param("tenantId") Long tenantId, @Param("status") Integer status);
}
```

### 4.4 前端界面的租户上下文处理

#### 4.4.1 租户信息管理

```javascript
// 租户上下文管理
export const useTenantStore = defineStore('tenant', {
  state: () => ({
    tenantInfo: null,
    tenantCode: null,
    tenantName: null
  }),
  
  actions: {
    // 设置租户信息
    setTenantInfo(tenantInfo) {
      this.tenantInfo = tenantInfo;
      this.tenantCode = tenantInfo.tenantCode;
      this.tenantName = tenantInfo.tenantName;
      
      // 存储到本地存储
      localStorage.setItem('tenant_info', JSON.stringify(tenantInfo));
    },
    
    // 获取租户信息
    getTenantInfo() {
      if (!this.tenantInfo) {
        const stored = localStorage.getItem('tenant_info');
        if (stored) {
          this.setTenantInfo(JSON.parse(stored));
        }
      }
      return this.tenantInfo;
    },
    
    // 清除租户信息
    clearTenantInfo() {
      this.tenantInfo = null;
      this.tenantCode = null;
      this.tenantName = null;
      localStorage.removeItem('tenant_info');
    }
  }
});
```

#### 4.4.2 HTTP请求拦截器

```javascript
// 请求拦截器 - 自动添加租户标识
axios.interceptors.request.use(
  config => {
    const tenantStore = useTenantStore();
    const tenantCode = tenantStore.tenantCode;
    
    if (tenantCode) {
      // 方式1：通过Header传递
      config.headers['X-Tenant-Code'] = tenantCode;
      
      // 方式2：通过URL参数传递
      // config.params = { ...config.params, tenantCode };
    }
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
```

#### 4.4.3 租户选择组件

```vue
<template>
  <div class="tenant-selector">
    <a-select
      v-model:value="selectedTenant"
      placeholder="请选择租户"
      style="width: 200px"
      @change="handleTenantChange">
      <a-select-option
        v-for="tenant in tenantList"
        :key="tenant.tenantCode"
        :value="tenant.tenantCode">
        {{ tenant.tenantName }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useTenantStore } from '@/stores/tenant';
import { TenantApi } from '@/api/tenant';

const tenantStore = useTenantStore();
const tenantList = ref([]);
const selectedTenant = ref(null);

onMounted(async () => {
  // 加载租户列表
  await loadTenantList();
  
  // 设置当前租户
  const currentTenant = tenantStore.getTenantInfo();
  if (currentTenant) {
    selectedTenant.value = currentTenant.tenantCode;
  }
});

const loadTenantList = async () => {
  try {
    const result = await TenantApi.getTenantList();
    tenantList.value = result.data;
  } catch (error) {
    console.error('加载租户列表失败:', error);
  }
};

const handleTenantChange = (tenantCode) => {
  const tenant = tenantList.value.find(t => t.tenantCode === tenantCode);
  if (tenant) {
    tenantStore.setTenantInfo(tenant);
    // 刷新页面或重新加载数据
    window.location.reload();
  }
};
</script>
```

## 5. 最佳实践和注意事项

### 5.1 常见的租户适配错误和避免方法

#### 5.1.1 数据泄露风险

**错误示例**：
```java
// 错误：没有添加租户条件的查询
@Override
public List<Business> getAllBusiness() {
    return businessMapper.selectList(null); // 危险！会查询所有租户数据
}
```

**正确做法**：
```java
// 正确：添加租户条件的查询
@Override
@TenantFilter
public List<Business> getAllBusiness() {
    LambdaQueryWrapper<Business> wrapper = new LambdaQueryWrapper<>();
    // MyBatis-Plus插件会自动添加租户条件
    return businessMapper.selectList(wrapper);
}
```

#### 5.1.2 缓存污染问题

**错误示例**：
```java
// 错误：缓存Key没有包含租户标识
@Cacheable(value = "business", key = "#businessId")
public Business getBusinessById(Long businessId) {
    return businessMapper.selectById(businessId);
}
```

**正确做法**：
```java
// 正确：缓存Key包含租户标识
@Cacheable(value = "business", key = "#tenantId + ':' + #businessId")
public Business getBusinessById(Long tenantId, Long businessId) {
    return businessMapper.selectById(businessId);
}
```

#### 5.1.3 文件存储混乱

**错误示例**：
```java
// 错误：文件路径没有租户隔离
String filePath = "/uploads/" + fileName;
```

**正确做法**：
```java
// 正确：文件路径包含租户标识
String tenantCode = TenantContext.getTenantCode();
String filePath = "/uploads/" + tenantCode + "/" + fileName;
```

### 5.2 性能优化建议

#### 5.2.1 数据库优化

1. **索引优化**：
   ```sql
   -- 为租户相关查询添加复合索引
   CREATE INDEX idx_tenant_status_time ON business_table(tenant_id, status, create_time);
   ```

2. **分区表设计**：
   ```sql
   -- 按租户ID进行表分区（适用于大租户场景）
   CREATE TABLE business_table (
       -- 字段定义
   ) PARTITION BY HASH(tenant_id) PARTITIONS 10;
   ```

3. **查询优化**：
   - 租户条件放在WHERE子句的第一位
   - 避免跨租户的JOIN查询
   - 使用租户相关的复合索引

#### 5.2.2 缓存优化

1. **分层缓存**：
   ```java
   // L1缓存：租户级别缓存
   @Cacheable(value = "tenant:" + tenantId, key = "#key")
   
   // L2缓存：全局缓存（谨慎使用）
   @Cacheable(value = "global", key = "#key")
   ```

2. **缓存预热**：
   ```java
   // 租户登录时预热常用数据
   @EventListener
   public void onTenantLogin(TenantLoginEvent event) {
       preloadTenantData(event.getTenantId());
   }
   ```

#### 5.2.3 连接池优化

```yaml
# 数据库连接池配置
spring:
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 5.3 安全性考虑

#### 5.3.1 数据访问安全

1. **强制租户过滤**：
   ```java
   @Component
   public class TenantDataInterceptor implements Interceptor {
       @Override
       public Object intercept(Invocation invocation) throws Throwable {
           // 强制检查SQL是否包含租户条件
           MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
           if (needTenantFilter(ms)) {
               // 自动添加租户条件
               addTenantCondition(invocation);
           }
           return invocation.proceed();
       }
   }
   ```

2. **跨租户访问检测**：
   ```java
   @Aspect
   @Component
   public class TenantSecurityAspect {
       
       @Around("@annotation(TenantSecure)")
       public Object checkTenantAccess(ProceedingJoinPoint point) throws Throwable {
           // 检查是否存在跨租户访问
           validateTenantAccess(point.getArgs());
           return point.proceed();
       }
   }
   ```

#### 5.3.2 API安全防护

1. **租户状态验证**：
   ```java
   @Component
   public class TenantStatusFilter implements Filter {
       @Override
       public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
           String tenantCode = getTenantCode(request);
           if (!isTenantActive(tenantCode)) {
               throw new TenantInactiveException("租户已停用");
           }
           chain.doFilter(request, response);
       }
   }
   ```

2. **API访问限制**：
   ```java
   @Component
   public class TenantRateLimiter {
       
       @RateLimiter(key = "tenant:#{tenantId}", rate = 100, duration = 60)
       public void checkApiLimit(Long tenantId) {
           // 检查租户API调用频率
       }
   }
   ```

#### 5.3.3 数据加密存储

```java
@Component
public class TenantDataEncryption {
    
    // 租户敏感数据加密
    public String encryptSensitiveData(String data, String tenantCode) {
        String key = generateTenantKey(tenantCode);
        return AESUtil.encrypt(data, key);
    }
    
    // 租户敏感数据解密
    public String decryptSensitiveData(String encryptedData, String tenantCode) {
        String key = generateTenantKey(tenantCode);
        return AESUtil.decrypt(encryptedData, key);
    }
}
```

### 5.4 监控和运维

#### 5.4.1 租户监控指标

1. **性能监控**：
   - 租户请求响应时间
   - 租户数据库连接数
   - 租户缓存命中率
   - 租户API调用频率

2. **资源监控**：
   - 租户存储空间使用量
   - 租户用户数量
   - 租户并发连接数
   - 租户功能使用情况

#### 5.4.2 日志记录

```java
@Aspect
@Component
public class TenantAuditAspect {
    
    @AfterReturning("@annotation(TenantAudit)")
    public void auditTenantOperation(JoinPoint point) {
        String tenantCode = TenantContext.getTenantCode();
        String operation = point.getSignature().getName();
        Object[] args = point.getArgs();
        
        // 记录租户操作日志
        TenantAuditLog log = new TenantAuditLog();
        log.setTenantCode(tenantCode);
        log.setOperation(operation);
        log.setParameters(JSON.toJSONString(args));
        log.setOperateTime(new Date());
        
        auditLogService.save(log);
    }
}
```

## 6. 系统配置

### 6.1 租户相关配置项

```properties
# 多租户开关
SYS_TENANT_OPEN=true

# 默认根租户ID
DEFAULT_ROOT_TENANT_ID=1

# 租户编码选择方式（true-下拉选择，false-手动输入）
SAAS_TENANT_CODE_SELECT_FLAG=true

# 租户会话超时时间（秒）
TENANT_SESSION_TIMEOUT=3600

# 租户数据缓存时间（秒）
TENANT_DATA_CACHE_TIMEOUT=1800
```

### 6.2 Maven依赖配置

```xml
<!-- SaaS租户模块依赖 -->
<dependency>
    <groupId>cn.stylefeng.roses</groupId>
    <artifactId>saas-spring-boot-starter</artifactId>
    <version>${roses.kernel.version}</version>
</dependency>
```

### 6.3 Spring Boot自动配置

```java
@Configuration
@ConditionalOnProperty(name = "SYS_TENANT_OPEN", havingValue = "true")
public class SaasAutoConfiguration {
    
    @Bean
    public TenantInterceptor tenantInterceptor() {
        return new TenantInterceptor();
    }
    
    @Bean
    public TenantDataSourceInterceptor tenantDataSourceInterceptor() {
        return new TenantDataSourceInterceptor();
    }
}
```

## 7. 总结

JavaGuns Enterprise 的 SaaS 租户架构采用了共享数据库、应用隔离的设计模式，通过租户标识实现数据隔离，通过功能包机制实现功能权限控制。该架构具有以下优势：

### 7.1 架构优势

1. **成本效益**：共享基础设施，降低运维成本
2. **快速部署**：新租户可以快速开通和配置
3. **统一管理**：集中管理所有租户的数据和配置
4. **弹性扩展**：支持租户数量和功能的弹性扩展
5. **安全隔离**：确保租户间数据和功能的安全隔离

### 7.2 适用场景

1. **中小型SaaS应用**：租户数量适中，数据量可控
2. **企业内部多部门系统**：需要数据隔离的内部系统
3. **快速原型开发**：需要快速搭建多租户原型
4. **成本敏感项目**：对基础设施成本有严格要求的项目

### 7.3 发展建议

1. **监控完善**：建立完善的租户监控和告警机制
2. **性能优化**：持续优化数据库查询和缓存策略
3. **安全加强**：加强租户间的安全隔离和访问控制
4. **功能扩展**：根据业务需求扩展租户管理功能

通过遵循本文档的设计原则和最佳实践，开发人员可以正确地为新功能添加租户支持，确保系统的安全性、性能和可维护性。