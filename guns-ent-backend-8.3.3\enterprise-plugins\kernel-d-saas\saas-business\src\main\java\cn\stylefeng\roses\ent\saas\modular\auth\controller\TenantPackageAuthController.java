package cn.stylefeng.roses.ent.saas.modular.auth.controller;

import cn.stylefeng.roses.ent.saas.modular.auth.pojo.request.PackageBindPermissionRequest;
import cn.stylefeng.roses.ent.saas.modular.auth.service.TenantPackageAuthService;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import cn.stylefeng.roses.kernel.sys.api.pojo.role.response.RoleBindPermissionResponse;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租户-功能包授权范围控制器
 *
 * <AUTHOR>
 * @since 2024/01/21 15:55
 */
@RestController
@ApiResource(name = "租户-功能包授权范围")
public class TenantPackageAuthController {

    @Resource
    private TenantPackageAuthService tenantPackageAuthService;

    /**
     * 获取功能包绑定的权限列表
     *
     * <AUTHOR>
     * @since 2024/1/21 23:41
     */
    @GetResource(name = "获取功能包绑定的权限列表", path = "/tenantPackage/getPackageAuth")
    public ResponseData<RoleBindPermissionResponse> getPackageAuth(@Validated(BaseRequest.detail.class) PackageBindPermissionRequest packageBindPermissionRequest) {
        RoleBindPermissionResponse roleBindPermissionResponse = tenantPackageAuthService.getPackageAuth(packageBindPermissionRequest);
        return new SuccessResponseData<>(roleBindPermissionResponse);
    }

    /**
     * 设置功能包绑定权限
     *
     * <AUTHOR>
     * @since 2024/1/21 23:57
     */
    @PostResource(name = "设置功能包绑定权限", path = "/tenantPackage/setPackagePermission")
    public ResponseData<?> setPackagePermission(
            @RequestBody @Validated(PackageBindPermissionRequest.packageBindPermission.class) PackageBindPermissionRequest packageBindPermissionRequest) {
        tenantPackageAuthService.updatePackageBindPermission(packageBindPermissionRequest);
        return new SuccessResponseData<>();
    }

    /**
     * 刷新关联功能包的租户，从新赋予角色的权限和权限范围
     *
     * <AUTHOR>
     * @since 2024/1/21 23:57
     */
    @PostResource(name = "刷新关联功能包的租户，从新赋予角色的权限和权限范围", path = "/tenantPackage/refreshPackageTenantRoles")
    public ResponseData<?> refreshPackageTenantRoles(@RequestBody @Validated(BaseRequest.detail.class) PackageBindPermissionRequest packageBindPermissionRequest) {
        this.tenantPackageAuthService.refreshPackageTenantRoles(packageBindPermissionRequest);
        return new SuccessResponseData<>();
    }

}
