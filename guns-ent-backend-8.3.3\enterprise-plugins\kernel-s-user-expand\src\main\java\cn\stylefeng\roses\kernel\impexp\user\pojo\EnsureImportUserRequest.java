package cn.stylefeng.roses.kernel.impexp.user.pojo;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 确认导入用户数据
 *
 * <AUTHOR>
 * @since 2024/2/10 17:23
 */
@Data
public class EnsureImportUserRequest {

    /**
     * 业务操作类型：1-新增，2-修改，3-删除
     */
    @ChineseDescription("业务操作类型：1-新增，2-修改，3-删除")
    private Integer operateType;

    /**
     * 主键
     */
    @ChineseDescription("主键")
    private Long userId;

    /**
     * 姓名
     */
    @ChineseDescription("姓名")
    private String realName;

    /**
     * 昵称
     */
    @ChineseDescription("昵称")
    private String nickName;

    /**
     * 账号
     */
    @ChineseDescription("账号")
    private String account;

    /**
     * 密码
     */
    @ChineseDescription("密码")
    private String password;

    /**
     * 生日，格式YYYY-MM-DD
     */
    @ChineseDescription("生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 性别：M-男，F-女
     */
    @ChineseDescription("性别：M-男，F-女")
    private String sex;

    /**
     * 邮箱
     */
    @ChineseDescription("邮箱")
    private String email;

    /**
     * 手机
     */
    @ChineseDescription("手机")
    private String phone;

    /**
     * 电话
     */
    @ChineseDescription("电话")
    private String tel;

    /**
     * 状态：1-正常，2-冻结
     */
    @ChineseDescription("状态：1-正常，2-冻结")
    private Integer statusFlag;

    /**
     * 用户的排序
     */
    @ChineseDescription("用户的排序")
    private BigDecimal userSort;

    /**
     * 用户工号
     */
    @ChineseDescription("用户工号")
    private String employeeNumber;

    /**
     * 对接外部主数据的用户id
     */
    @ChineseDescription("对接外部主数据的用户id")
    private String masterUserId;

    /**
     * 组织机构id
     */
    @ChineseDescription("组织机构id")
    private Long orgId;

    /**
     * 职位id
     */
    @ChineseDescription("职位id")
    private Long positionId;

}
