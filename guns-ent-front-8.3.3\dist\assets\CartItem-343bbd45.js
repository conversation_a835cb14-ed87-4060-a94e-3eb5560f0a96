import{_ as k,a as m,c as l,b as n,g as x,t as o,h as u,d as s,w as y,at as g,aX as P,a$ as B,P as Q,a2 as T,B as z,y as E}from"./index-18a1ea24.js";/* empty css              */import{A as N}from"./formatter-5a06da9d.js";import{P as b}from"./constants-2fa70699.js";const O={class:"item-row item-row-1"},S=["title"],V={key:0,class:"item-specs"},D={class:"item-row item-row-2"},G={class:"item-unit-price"},j={class:"quantity-control"},A={class:"item-row item-row-3"},F={key:0,class:"item-code"},H={class:"item-total"},M={key:0,class:"item-image"},R=["src","alt"],W=Object.assign({name:"CartItem"},{__name:"CartItem",props:{item:{type:Object,required:!0,validator:t=>t&&typeof t.id=="string"&&typeof t.name=="string"&&typeof t.price=="number"&&typeof t.quantity=="number"&&typeof t.subtotal=="number"},loading:{type:Boolean,default:!1}},emits:["update-quantity","remove-item"],setup(t,{emit:h}){const i=t,c=h,f=e=>N.formatCurrency(e,{showSymbol:!1}),v=()=>i.item.pricingType===b.WEIGHT?3:0,d=()=>i.item.pricingType===b.WEIGHT?.001:1,_=()=>{if(i.item.quantity<=1)return;const e=d(),a=Math.max(e,i.item.quantity-e);c("update-quantity",i.item.id,a)},p=()=>{const e=d(),a=i.item.quantity+e;c("update-quantity",i.item.id,a)},q=e=>{e&&e>0&&c("update-quantity",i.item.id,e)},C=e=>{const a=parseFloat(e.target.value);a&&a>0&&c("update-quantity",i.item.id,a)},w=()=>{c("remove-item",i.item.id)};return(e,a)=>{const r=z,I=E;return m(),l("div",{class:T(["cart-item",{loading:t.loading}])},[n("div",O,[n("div",{class:"item-name",title:t.item.name},[x(o(t.item.name)+" ",1),t.item.specifications?(m(),l("span",V," ("+o(t.item.specifications)+") ",1)):u("",!0)],8,S),s(r,{type:"primary",size:"small",danger:"",onClick:w,class:"remove-btn",title:"\u5220\u9664\u5546\u54C1",loading:t.loading},{icon:y(()=>[s(g(P))]),_:1},8,["loading"])]),n("div",D,[n("div",G," \xA5"+o(f(t.item.price))+"/"+o(t.item.unit||"\u4EF6"),1),n("div",j,[s(r,{size:"small",onClick:_,disabled:t.item.quantity<=1||t.loading,class:"quantity-btn"},{icon:y(()=>[s(g(B))]),_:1},8,["disabled"]),s(I,{value:t.item.quantity,min:1,max:9999,precision:v(),step:d(),size:"small",class:"quantity-input",disabled:t.loading,onChange:q,onPressEnter:C},null,8,["value","precision","step","disabled"]),s(r,{size:"small",onClick:p,disabled:t.loading,class:"quantity-btn"},{icon:y(()=>[s(g(Q))]),_:1},8,["disabled"])])]),n("div",A,[t.item.barcode?(m(),l("div",F," \u6761\u7801: "+o(t.item.barcode),1)):u("",!0),n("div",H," \xA5"+o(f(t.item.subtotal)),1)]),t.item.image?(m(),l("div",M,[n("img",{src:t.item.image,alt:t.item.name},null,8,R)])):u("",!0)],2)}}}),K=k(W,[["__scopeId","data-v-03fa96fb"]]);export{K as default};
