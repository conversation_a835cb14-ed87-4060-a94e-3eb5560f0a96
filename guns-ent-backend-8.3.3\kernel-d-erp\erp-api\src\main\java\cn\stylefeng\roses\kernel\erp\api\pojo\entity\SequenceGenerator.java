package cn.stylefeng.roses.kernel.erp.api.pojo.entity;

import cn.stylefeng.roses.kernel.db.api.pojo.entity.BaseEntity;
import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 序列号生成器实体类
 *
 * <AUTHOR>
 * @since 2025/07/27 17:00
 */
@TableName(value = "erp_sequence_generator", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SequenceGenerator extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 序列名称
     */
    @TableField("sequence_name")
    @ChineseDescription("序列名称")
    private String sequenceName;

    /**
     * 前缀
     */
    @TableField("prefix")
    @ChineseDescription("前缀")
    private String prefix;

    /**
     * 当前值
     */
    @TableField("current_value")
    @ChineseDescription("当前值")
    private Long currentValue;

    /**
     * 步长
     */
    @TableField("step")
    @ChineseDescription("步长")
    private Integer step;

    /**
     * 日期格式（如：yyyyMMdd）
     */
    @TableField("date_format")
    @ChineseDescription("日期格式")
    private String dateFormat;

    /**
     * 重置类型：NONE(不重置)、DAILY(每日)、MONTHLY(每月)、YEARLY(每年)
     */
    @TableField("reset_type")
    @ChineseDescription("重置类型")
    private String resetType;

    /**
     * 最后重置日期
     */
    @TableField("last_reset_date")
    @ChineseDescription("最后重置日期")
    private LocalDate lastResetDate;

}
