package cn.stylefeng.roses.kernel.ca.api.business;

import cn.stylefeng.roses.kernel.ca.api.pojo.sso.request.SsoLoginCodeRequest;

/**
 * 用户密码校验的api
 *
 * <AUTHOR>
 * @date 2021/1/27 17:16
 */
public interface CaValidatePasswordApi {

    /**
     * 校验用户密码是否正确
     * <p>
     * 如果校验成功，返回用户信息，如果校验失败，返回null
     *
     * @param ssoLoginCodeRequest 账号密码信息
     * @return 用户id
     * <AUTHOR>
     * @date 2021/1/27 17:17
     */
    Long validatePassword(SsoLoginCodeRequest ssoLoginCodeRequest);

}
