import{_ as j,r as P,o as K,a as y,f,w as I,b as i,d as N,t as T,m as M,I as V,bg as z,a5 as O,M as U}from"./index-18a1ea24.js";/* empty css              */import{S}from"./SysDictApi-f53c1202.js";const A={class:"box-shadow menu-item"},E={class:"left-header"},F={class:"app-item-right"},G={class:"app-item-name"},L={class:"menu-tree"},R={class:"tree-edit"},Y=["title"],$={__name:"update-structure",props:{visible:Boolean,dictTypeId:String,dictTypeName:String},emits:["update:visible","done"],setup(b,{emit:w}){const h=b,x=w,m=P(!1),s=P([]);K(()=>{h.dictTypeId&&D()});const D=()=>{S.tree({dictTypeId:h.dictTypeId}).then(e=>{s.value=e})},v=e=>{x("update:visible",e)},k=()=>{s.value&&s.value.length>0&&(m.value=!0,S.updateDictTree({totalDictStructure:s.value}).then(e=>{M.success(e.message),x("done"),v(!1)}).finally(()=>m.value=!1))},B=e=>{const c=e.node.eventKey,p=e.dragNode.eventKey,_=e.node.pos.split("-"),u=e.dropPosition-Number(_[_.length-1]),a=(t,d,r)=>{t.forEach((l,g,C)=>{if(l.dictId===d)return r(l,g,C);if(l.children)return a(l.children,d,r)})},n=[...s.value];let o={};if(a(n,p,(t,d,r)=>{r.splice(d,1),o=t}),!e.dropToGap)o.dictParentId=e.node.dictId,o.nodeParentId=e.node.dictId,a(n,c,t=>{t.children=t.children||[],t.children.push(o)});else if((e.node.children||[]).length>0&&e.node.expanded&&u===1)o.dictParentId=e.node.dictId,o.nodeParentId=e.node.dictId,a(n,c,t=>{t.children=t.children||[],t.children.unshift(o)});else{o.dictParentId=e.node.nodeParentId,o.nodeParentId=e.node.nodeParentId;let t=[],d=0;a(n,c,(r,l,g)=>{t=g,d=l}),u===-1?t.splice(d,0,o):t.splice(d+1,0,o)}s.value=[...n]};return(e,c)=>{const p=V,_=z,u=O,a=U;return y(),f(a,{width:500,maskClosable:!1,visible:h.visible,"confirm-loading":m.value,forceRender:!0,style:{top:"40px"},title:"\u4FEE\u6539\u5B57\u5178\u4E0A\u4E0B\u7ED3\u6784","body-style":{paddingBottom:"8px",height:"600px",overflowY:"hidden"},"onUpdate:visible":v,onOk:k,onClose:c[0]||(c[0]=n=>v(!1))},{default:I(()=>[i("div",A,[i("div",E,[N(p,{iconClass:"icon-tree-wenjianjia",color:"var(--primary-color)","font-size":"44px",style:{margin:"0 10px"}}),i("div",F,[i("div",G,T(b.dictTypeName),1)])]),i("div",L,[s.value&&s.value.length>0?(y(),f(_,{key:0,draggable:"",onDrop:B,"show-icon":!0,"tree-data":s.value,fieldNames:{children:"children",title:"dictName",key:"dictId",value:"dictId"}},{icon:I(()=>[N(p,{iconClass:"icon-tab-jichuxinxi",color:"#43505e","font-size":"24px"})]),title:I(n=>[i("span",R,[i("span",{class:"edit-title",title:n.dictName},T(n.dictName),9,Y)])]),_:1},8,["tree-data"])):(y(),f(u,{key:1,class:"empty"}))])])]),_:1},8,["visible","confirm-loading"])}}},Q=j($,[["__scopeId","data-v-f271e1fb"]]);export{Q as default};
