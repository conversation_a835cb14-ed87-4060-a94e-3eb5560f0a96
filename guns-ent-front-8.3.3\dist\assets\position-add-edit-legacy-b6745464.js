System.register(["./index-legacy-ee1db0c7.js","./position-form-legacy-98b77b53.js"],(function(e,t){"use strict";var a,i,o,s,n,l,r,d,u,c,v;return{setters:[e=>{a=e.R,i=e.r,o=e.o,s=e.cb,n=e.a,l=e.f,r=e.w,d=e.d,u=e.m,c=e.M},e=>{v=e.default}],execute:function(){class t{static findPage(e){return a.getAndLoadData("/hrPosition/page",e)}static add(e){return a.post("/hrPosition/add",e)}static edit(e){return a.post("/hrPosition/edit",e)}static delete(e){return a.post("/hrPosition/delete",e)}static batchDelete(e){return a.post("/hrPosition/batchDelete",e)}static detail(e){return a.getAndLoadData("/hrPosition/detail",e)}}e("P",t);const f=e("_",{__name:"position-add-edit",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(e,{emit:a}){const f=e,p=a,b=i(!1),m=i(!1),g=i({}),h=i(null);o((async()=>{f.data?(m.value=!0,g.value=Object.assign({},f.data)):(g.value.positionSort=await s("SYSTEM_HR_POSITION"),m.value=!1)}));const y=e=>{p("update:visible",e)},_=async()=>{h.value.$refs.formRef.validate().then((async e=>{if(e){b.value=!0;let e=null;e=m.value?t.edit(g.value):t.add(g.value),e.then((async e=>{b.value=!1,u.success(e.message),y(!1),p("done")})).catch((()=>{b.value=!1}))}}))};return(e,t)=>{const a=c;return n(),l(a,{width:700,maskClosable:!1,visible:f.visible,"confirm-loading":b.value,forceRender:!0,title:m.value?"编辑职位":"新建职位","body-style":{paddingBottom:"8px",height:"500px",overflowY:"auto"},"onUpdate:visible":y,onOk:_,onClose:t[1]||(t[1]=e=>y(!1))},{default:r((()=>[d(v,{form:g.value,"onUpdate:form":t[0]||(t[0]=e=>g.value=e),ref_key:"positionFormRef",ref:h},null,8,["form"])])),_:1},8,["visible","confirm-loading","title"])}}}),p=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));e("p",p)}}}));
