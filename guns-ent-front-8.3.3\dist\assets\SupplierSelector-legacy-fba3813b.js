System.register(["./index-legacy-ee1db0c7.js","./index-legacy-efb51034.js","./SupplierApi-legacy-234ddfc1.js"],(function(e,a){"use strict";var l,t,s,i,n,o,r,d,u,c,p,h,f,v,g,m,b,y,S,C;return{setters:[e=>{l=e.r,t=e._,s=e.L,i=e.X,n=e.o,o=e.aK,r=e.a,d=e.f,u=e.w,c=e.c,p=e.F,h=e.e,f=e.b,v=e.t,g=e.d,m=e.g,b=e.U,y=e.W,S=e.J},null,e=>{C=e.S}],execute:function(){var a=document.createElement("style");a.textContent=".supplier-option[data-v-8bd89d78]{display:flex;justify-content:space-between;align-items:center}.supplier-name[data-v-8bd89d78]{flex:1;margin-right:8px}.business-mode-tag[data-v-8bd89d78]{margin-left:auto}[data-v-8bd89d78] .ant-select-item-option-content{width:100%}\n",document.head.appendChild(a);const N={data:l([]),loading:l(!1),lastFetchTime:0,cacheTimeout:3e5,loadPromise:null,async fetchSuppliers(e=!1){const a=Date.now();return!e&&this.data.value.length>0&&a-this.lastFetchTime<this.cacheTimeout?this.data.value:(this.loadPromise||(this.loadPromise=(async()=>{try{this.loading.value=!0;const e=await C.findList({status:"ACTIVE"});return this.data.value=e||[],this.lastFetchTime=a,this.data.value}catch(e){throw console.error("加载供应商列表失败:",e),e}finally{this.loading.value=!1,this.loadPromise=null}})()),this.loadPromise)},clearCache(){this.data.value=[],this.lastFetchTime=0,this.loadPromise=null}},w={name:"SupplierSelector",props:{value:{type:[String,Number],default:void 0},allowClear:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},filter:{type:Object,default:()=>({})}},emits:["update:value","change"],setup(e,{emit:a}){const t=l(!1),r=l([]),d=l(""),u=l(!1),c=s({get:()=>e.value,set:e=>{a("update:value",e)}}),p=s((()=>{let a=r.value;if(e.filter.businessMode){const l=Array.isArray(e.filter.businessMode)?e.filter.businessMode:[e.filter.businessMode];a=a.filter((e=>l.includes(e.businessMode)))}if(e.filter.includeJointVenture||(a=a.filter((e=>"JOINT_VENTURE"!==e.businessMode))),d.value){const e=d.value.toLowerCase();a=a.filter((a=>a.supplierName.toLowerCase().includes(e)||a.supplierCode.toLowerCase().includes(e)))}return a})),h=async()=>{if(!t.value)try{t.value=!0;const e=await N.fetchSuppliers();r.value=e||[],u.value=!0}catch(e){console.error("加载供应商列表失败:",e),r.value=[]}finally{t.value=!1}};return i((()=>e.filter),((e,a)=>{u.value&&JSON.stringify(e)!==JSON.stringify(a)&&h()}),{deep:!0}),n((()=>{o((()=>{h()}))})),{loading:t,selectedValue:c,filteredSuppliers:p,getBusinessModeName:e=>({PURCHASE_SALE:"购销",JOINT_VENTURE:"联营",CONSIGNMENT:"代销"}[e]||e),getBusinessModeColor:e=>({PURCHASE_SALE:"blue",JOINT_VENTURE:"orange",CONSIGNMENT:"green"}[e]||"default"),filterOption:(e,a)=>!0,handleChange:(e,l)=>{const t=r.value.find((a=>a.supplierId===e));a("change",e,t)},handleSearch:e=>{d.value=e},refreshSuppliers:async()=>{try{t.value=!0;const e=await N.fetchSuppliers(!0);r.value=e||[]}catch(e){console.error("刷新供应商列表失败:",e)}finally{t.value=!1}}}}},T={class:"supplier-option"},E={class:"supplier-name"};e("_",t(w,[["render",function(e,a,l,t,s,i){const n=b,o=y,C=S;return r(),d(C,{value:t.selectedValue,"onUpdate:value":a[0]||(a[0]=e=>t.selectedValue=e),placeholder:"请选择供应商",loading:t.loading,allowClear:l.allowClear,disabled:l.disabled,"show-search":"","filter-option":t.filterOption,onChange:t.handleChange,onSearch:t.handleSearch},{default:u((()=>[(r(!0),c(p,null,h(t.filteredSuppliers,(e=>(r(),d(o,{key:e.supplierId,value:e.supplierId,disabled:e.disabled},{default:u((()=>[f("div",T,[f("span",E,v(e.supplierName),1),g(n,{color:t.getBusinessModeColor(e.businessMode),size:"small",class:"business-mode-tag"},{default:u((()=>[m(v(t.getBusinessModeName(e.businessMode)),1)])),_:2},1032,["color"])])])),_:2},1032,["value","disabled"])))),128))])),_:1},8,["value","loading","allowClear","disabled","filter-option","onChange","onSearch"])}],["__scopeId","data-v-8bd89d78"]]))}}}));
