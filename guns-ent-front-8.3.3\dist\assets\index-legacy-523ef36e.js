System.register(["./index-legacy-8a7fc0f5.js","./index-legacy-ee1db0c7.js","./position-add-edit-legacy-b6745464.js","./index-legacy-5b978ff2.js","./index-legacy-16f295ac.js","./index-legacy-b540c599.js","./position-form-legacy-98b77b53.js","./index-legacy-94a6fc23.js"],(function(e,t){"use strict";var l,i,s,n,a,o,c,d,u,r,h,v,p,y,w,m,_,f,I,g,k,x,b,C,T,S,O;return{setters:[e=>{l=e._},e=>{i=e.r,s=e.o,n=e.k,a=e.bv,o=e.a,c=e.c,d=e.b,u=e.d,r=e.w,h=e.aR,v=e.f,p=e.g,y=e.t,w=e.h,m=e.M,_=e.E,f=e.m,I=e.n,g=e.B,k=e.I,x=e.p,b=e.q,C=e.D,T=e.l},e=>{S=e._,O=e.P},null,null,null,null,null],execute:function(){const t={class:"guns-layout"},j={class:"guns-layout-content"},E={class:"guns-layout"},P={class:"guns-layout-content-application"},D={class:"content-mian"},L={class:"content-mian-header"},N={class:"header-content"},z={class:"header-content-left"},R={class:"header-content-right"},B={class:"content-mian-body"},A={class:"table-content"},U=["onClick"];e("default",Object.assign({name:"Position"},{__name:"index",setup(e){const q=i([{key:"index",title:"序号",width:48,align:"center",isShow:!0,hideInSetting:!0},{dataIndex:"positionName",title:"职位名称",ellipsis:!0,width:200,isShow:!0},{dataIndex:"positionCode",title:"职位编码",width:100,isShow:!0},{dataIndex:"remark",title:"备注",ellipsis:!0,width:200,isShow:!0},{dataIndex:"positionSort",title:"排序",width:100,isShow:!0},{dataIndex:"createTime",title:"创建时间",width:150,isShow:!0},{key:"action",title:"操作",width:100,isShow:!0}]),M=i(null),F=i({searchText:""}),G=i(null),H=i(!1);s((()=>{}));const J=({key:e})=>{"1"==e&&V()},K=()=>{M.value.reload()},Q=e=>{G.value=e,H.value=!0},V=()=>{if(M.value.selectedRowList&&0==M.value.selectedRowList.length)return f.warning("请选择需要删除的职位");m.confirm({title:"提示",content:"确定要删除选中的职位吗?",icon:u(_),maskClosable:!0,onOk:async()=>{const e=await O.batchDelete({positionIdList:M.value.selectedRowList});f.success(e.message),K()}})};return(e,i)=>{const s=I,V=n("plus-outlined"),W=g,X=k,Y=x,Z=b,$=n("small-dash-outlined"),ee=C,te=T,le=l,ie=a("permission");return o(),c("div",t,[d("div",j,[d("div",E,[d("div",P,[d("div",D,[d("div",L,[d("div",N,[d("div",z,[u(s,{size:16})]),d("div",R,[u(s,{size:16},{default:r((()=>[h((o(),v(W,{type:"primary",class:"border-radius",onClick:i[0]||(i[0]=e=>Q())},{default:r((()=>[u(V),i[3]||(i[3]=p("新建"))])),_:1,__:[3]})),[[ie,["ADD_POSITION"]]]),u(ee,null,{overlay:r((()=>[u(Z,{onClick:J},{default:r((()=>[h((o(),c("div",null,[u(Y,{key:"1"},{default:r((()=>[u(X,{iconClass:"icon-opt-shanchu",color:"#60666b"}),i[4]||(i[4]=d("span",null,"批量删除",-1))])),_:1,__:[4]})])),[[ie,["DELETE_POSITION"]]])])),_:1})])),default:r((()=>[u(W,{class:"border-radius"},{default:r((()=>[i[5]||(i[5]=p(" 更多 ")),u($)])),_:1,__:[5]})])),_:1})])),_:1})])])]),d("div",B,[d("div",A,[u(le,{columns:q.value,where:F.value,showTableTool:"",showToolTotal:!1,rowId:"positionId",ref_key:"tableRef",ref:M,fieldBusinessCode:"POSITION_TABLE",url:"/hrPosition/page"},{toolLeft:r((()=>[u(te,{value:F.value.searchText,"onUpdate:value":i[1]||(i[1]=e=>F.value.searchText=e),placeholder:"名称、编码（回车搜索）",onPressEnter:K,bordered:!1,class:"search-input"},{prefix:r((()=>[u(X,{iconClass:"icon-opt-search"})])),_:1},8,["value"])])),bodyCell:r((({column:e,record:t})=>["positionName"==e.dataIndex?(o(),c("a",{key:0,onClick:e=>Q(t)},y(t.positionName),9,U)):w("",!0),"action"==e.key?(o(),v(s,{key:1,size:16},{default:r((()=>[h(u(X,{iconClass:"icon-opt-bianji","font-size":"24px",title:"编辑",color:"#60666b",onClick:e=>Q(t)},null,8,["onClick"]),[[ie,["EDIT_POSITION"]]]),h(u(X,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"删除",color:"#60666b",onClick:e=>(e=>{m.confirm({title:"提示",content:"确定要删除选中的职位吗?",icon:u(_),maskClosable:!0,onOk:async()=>{const t=await O.delete({positionId:e.positionId});f.success(t.message),K()}})})(t)},null,8,["onClick"]),[[ie,["DELETE_POSITION"]]])])),_:2},1024)):w("",!0)])),_:1},8,["columns","where"])])])])])])]),H.value?(o(),v(S,{key:0,visible:H.value,"onUpdate:visible":i[2]||(i[2]=e=>H.value=e),data:G.value,onDone:K},null,8,["visible","data"])):w("",!0)])}}}))}}}));
