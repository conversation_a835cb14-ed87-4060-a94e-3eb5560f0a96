<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.stylefeng.roses.kernel.erp.modular.pos.mapper.PosSuspendedOrderMapper">

    <!-- 根据收银员ID查询挂单 -->
    <select id="findByCashier" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosSuspendedOrder">
        SELECT * FROM pos_suspended_order
        WHERE cashier_id = #{cashierId}
        AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY suspend_time DESC
    </select>

    <!-- 根据挂单号查询挂单 -->
    <select id="findBySuspendNo" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosSuspendedOrder">
        SELECT * FROM pos_suspended_order
        WHERE suspend_no = #{suspendNo}
        AND del_flag = 'N'
    </select>

    <!-- 查询过期的挂单 -->
    <select id="findExpiredOrders" resultType="cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosSuspendedOrder">
        SELECT * FROM pos_suspended_order
        WHERE expire_time &lt; #{currentTime}
        AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="status == null or status == ''">
            AND status = 'ACTIVE'
        </if>
        ORDER BY expire_time ASC
    </select>

    <!-- 批量更新挂单状态 -->
    <update id="batchUpdateStatus">
        UPDATE pos_suspended_order 
        SET status = #{status},
            update_user = #{updateUser},
            update_time = NOW()
        WHERE suspend_id IN
        <foreach collection="suspendIds" item="suspendId" open="(" separator="," close=")">
            #{suspendId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 清理过期挂单 -->
    <update id="cleanExpiredOrders">
        UPDATE pos_suspended_order 
        SET status = 'EXPIRED',
            update_user = #{updateUser},
            update_time = NOW()
        WHERE expire_time &lt; #{expireTime}
        AND status = 'ACTIVE'
        AND del_flag = 'N'
    </update>

    <!-- 统计收银员的挂单数量 -->
    <select id="countByCashier" resultType="int">
        SELECT COUNT(*) FROM pos_suspended_order
        WHERE cashier_id = #{cashierId}
        AND del_flag = 'N'
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

</mapper>