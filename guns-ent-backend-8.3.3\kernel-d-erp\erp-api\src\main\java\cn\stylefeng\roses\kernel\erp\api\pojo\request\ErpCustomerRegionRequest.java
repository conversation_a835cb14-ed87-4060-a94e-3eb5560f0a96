package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 客户-区域关联请求参数
 *
 * <AUTHOR>
 * @since 2025/07/22 17:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpCustomerRegionRequest extends BaseRequest {

    /**
     * 主键ID
     */
    @ChineseDescription("主键ID")
    private Long id;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = {add.class, edit.class, delete.class, detail.class, getCustomerRegions.class, updateCustomerRegions.class})
    @ChineseDescription("客户ID")
    private Long customerId;

    /**
     * 区域ID
     */
    @NotNull(message = "区域ID不能为空", groups = {getCustomersByRegion.class, countCustomersByRegion.class})
    @ChineseDescription("区域ID")
    private Long regionId;

    /**
     * 区域ID列表
     */
    @NotEmpty(message = "区域ID列表不能为空", groups = {updateCustomerRegions.class})
    @ChineseDescription("区域ID列表")
    private List<Long> regionIds;

    /**
     * 是否包含子区域
     */
    @ChineseDescription("是否包含子区域")
    private Boolean includeChildRegions = true;

    /**
     * 客户编码
     */
    @ChineseDescription("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ChineseDescription("客户名称")
    private String customerName;

    /**
     * 客户类型
     */
    @ChineseDescription("客户类型")
    private String customerType;

    /**
     * 客户等级
     */
    @ChineseDescription("客户等级")
    private String customerLevel;

    /**
     * 客户状态
     */
    @ChineseDescription("客户状态")
    private String status;

    /**
     * 参数校验分组：新增
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

    /**
     * 参数校验分组：获取客户关联区域
     */
    public @interface getCustomerRegions {
    }

    /**
     * 参数校验分组：更新客户关联区域
     */
    public @interface updateCustomerRegions {
    }

    /**
     * 参数校验分组：根据区域ID查询客户
     */
    public @interface getCustomersByRegion {
    }

    /**
     * 参数校验分组：统计区域关联客户数量
     */
    public @interface countCustomersByRegion {
    }
}