import{_ as F}from"./index-02bf6f00.js";import{r as c,o as M,k as z,a as d,c as C,b as t,d as s,w as l,g as A,t as $,h as m,f as k,M as V,E as R,cf as b,m as x,n as D,B as L,I as O,l as P,a9 as U}from"./index-18a1ea24.js";import Y from"./manager-add-edit-d37d4de2.js";/* empty css              *//* empty css              *//* empty css              */import"./manager-form-ed3d923e.js";/* empty css              */import"./ThemeTemplateApi-ac0cf8e8.js";import"./FileApi-418f4d35.js";const j={class:"guns-layout"},G={class:"guns-layout-content"},H={class:"guns-layout"},q={class:"guns-layout-content-application"},J={class:"content-mian"},K={class:"content-mian-header"},Q={class:"header-content"},W={class:"header-content-left"},X={class:"header-content-right"},Z={class:"content-mian-body"},ee={class:"table-content"},te=["onClick"],me={__name:"index",setup(se){const I=c([{key:"index",title:"\u5E8F\u53F7",width:60,align:"center",isShow:!0,hideInSetting:!0},{title:"\u4E3B\u9898\u540D\u79F0",dataIndex:"themeName",isShow:!0},{title:"\u4E3B\u9898\u6A21\u677F",isShow:!0,dataIndex:"templateName"},{title:"\u521B\u5EFA\u65F6\u95F4",isShow:!0,dataIndex:"createTime"},{title:"\u542F\u7528\u72B6\u6001",isShow:!0,dataIndex:"statusFlag"},{key:"action",title:"\u64CD\u4F5C",width:100,isShow:!0}]),w=c(null),p=c({themeName:""}),y=c(null),u=c(!1);M(()=>{});const r=()=>{w.value.reload()},h=n=>{y.value=n,u.value=!0},N=n=>{V.confirm({title:"\u63D0\u793A",content:"\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u4E3B\u9898\u5417?",icon:s(R),maskClosable:!0,onOk:async()=>{const e=await b.del({themeId:n.themeId});x.success(e.message),r()}})},S=async(n,e)=>{const i=e.themeId,v=n?"Y":"N",f=await b.updateThemeStatus({themeId:i});x.success(f.message),e.statusFlag=v,r()};return(n,e)=>{const i=D,v=z("plus-outlined"),f=L,g=O,T=P,E=U,B=F;return d(),C("div",j,[t("div",G,[t("div",H,[t("div",q,[t("div",J,[t("div",K,[t("div",Q,[t("div",W,[s(i,{size:16})]),t("div",X,[s(i,{size:16},{default:l(()=>[s(f,{type:"primary",class:"border-radius",onClick:e[0]||(e[0]=o=>h())},{default:l(()=>[s(v),e[3]||(e[3]=A("\u65B0\u5EFA"))]),_:1,__:[3]})]),_:1})])])]),t("div",Z,[t("div",ee,[s(B,{columns:I.value,where:p.value,rowId:"themeId",ref_key:"tableRef",ref:w,rowSelection:!1,url:"/sysTheme/findPage",showTableTool:"",showToolTotal:!1,fieldBusinessCode:"THMEM_MANAGER_TABLE"},{toolLeft:l(()=>[s(T,{value:p.value.themeName,"onUpdate:value":e[1]||(e[1]=o=>p.value.themeName=o),placeholder:"\u4E3B\u9898\u540D\u79F0\uFF08\u56DE\u8F66\u641C\u7D22\uFF09",onPressEnter:r,class:"search-input",bordered:!1},{prefix:l(()=>[s(g,{iconClass:"icon-opt-search"})]),_:1},8,["value"])]),bodyCell:l(({column:o,record:a})=>[o.dataIndex=="themeName"?(d(),C("a",{key:0,onClick:_=>h(a)},$(a.themeName),9,te)):m("",!0),o.dataIndex==="statusFlag"?(d(),k(E,{key:1,checked:a.statusFlag==="Y",onChange:_=>S(_,a)},null,8,["checked","onChange"])):m("",!0),o.key=="action"?(d(),k(i,{key:2,size:16},{default:l(()=>[s(g,{iconClass:"icon-opt-bianji","font-size":"24px",title:"\u7F16\u8F91",color:"#60666b",onClick:_=>h(a)},null,8,["onClick"]),s(g,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:_=>N(a)},null,8,["onClick"])]),_:2},1024)):m("",!0)]),_:1},8,["columns","where"])])])])])])]),u.value?(d(),k(Y,{key:0,visible:u.value,"onUpdate:visible":e[2]||(e[2]=o=>u.value=o),data:y.value,onDone:r},null,8,["visible","data"])):m("",!0)])}}};export{me as default};
