package cn.stylefeng.roses.kernel.erp.api.pojo.request;

import cn.stylefeng.roses.kernel.rule.annotation.ChineseDescription;
import cn.stylefeng.roses.kernel.rule.pojo.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单查询请求参数
 *
 * <AUTHOR>
 * @since 2025/07/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderQueryRequest extends BaseRequest {

    /**
     * 入库单号
     */
    @ChineseDescription("入库单号")
    private String orderNo;

    /**
     * 供应商ID
     */
    @ChineseDescription("供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ChineseDescription("供应商名称")
    private String supplierName;

    /**
     * 供应商编码
     */
    @ChineseDescription("供应商编码")
    private String supplierCode;

    /**
     * 状态
     */
    @ChineseDescription("状态")
    private String status;

    /**
     * 状态列表
     */
    @ChineseDescription("状态列表")
    private List<String> statusList;

    /**
     * 经营方式
     */
    @ChineseDescription("经营方式")
    private String businessMode;

    /**
     * 经营方式列表
     */
    @ChineseDescription("经营方式列表")
    private List<String> businessModeList;

    /**
     * 最小总金额
     */
    @ChineseDescription("最小总金额")
    private BigDecimal minTotalAmount;

    /**
     * 最大总金额
     */
    @ChineseDescription("最大总金额")
    private BigDecimal maxTotalAmount;

    /**
     * 开始日期
     */
    @ChineseDescription("开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ChineseDescription("结束日期")
    private LocalDate endDate;

    /**
     * 付款方式
     */
    @ChineseDescription("付款方式")
    private String paymentMethod;

    /**
     * 付款方式列表
     */
    @ChineseDescription("付款方式列表")
    private List<String> paymentMethodList;

    /**
     * 创建人
     */
    @ChineseDescription("创建人")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @ChineseDescription("创建人姓名")
    private String createUserName;

}