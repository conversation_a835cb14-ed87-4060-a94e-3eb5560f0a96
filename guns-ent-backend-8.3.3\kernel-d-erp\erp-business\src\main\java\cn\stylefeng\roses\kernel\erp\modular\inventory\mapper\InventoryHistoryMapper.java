package cn.stylefeng.roses.kernel.erp.modular.inventory.mapper;

import cn.stylefeng.roses.kernel.erp.api.pojo.entity.InventoryHistory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.InventoryHistoryQueryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.InventoryHistoryResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存历史Mapper接口
 *
 * <AUTHOR>
 * @since 2025/07/27 16:00
 */
public interface InventoryHistoryMapper extends BaseMapper<InventoryHistory> {

    /**
     * 分页查询库存历史记录
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 库存历史记录列表
     */
    Page<InventoryHistoryResponse> selectInventoryHistoryPage(Page<InventoryHistoryResponse> page, @Param("request") InventoryHistoryQueryRequest request);

    /**
     * 根据商品ID查询库存历史记录
     *
     * @param productId 商品ID
     * @param limit 限制数量
     * @return 库存历史记录列表
     */
    List<InventoryHistoryResponse> selectByProductId(@Param("productId") Long productId, @Param("limit") Integer limit);

    /**
     * 根据关联单据查询库存历史记录
     *
     * @param referenceType 关联单据类型
     * @param referenceId 关联单据ID
     * @return 库存历史记录列表
     */
    List<InventoryHistoryResponse> selectByReference(@Param("referenceType") String referenceType, @Param("referenceId") Long referenceId);

    /**
     * 统计商品的库存变动总量
     *
     * @param productId 商品ID
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 变动总量
     */
    BigDecimal getTotalQuantityChange(@Param("productId") Long productId, 
                                     @Param("operationType") String operationType,
                                     @Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计供应商的库存变动记录数量
     *
     * @param supplierId 供应商ID
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数量
     */
    Long countBySupplier(@Param("supplierId") Long supplierId, 
                        @Param("operationType") String operationType,
                        @Param("startTime") LocalDateTime startTime, 
                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取商品的最新库存历史记录
     *
     * @param productId 商品ID
     * @return 最新库存历史记录
     */
    @Select("SELECT * FROM erp_inventory_history WHERE product_id = #{productId} ORDER BY operation_time DESC LIMIT 1")
    InventoryHistory getLatestByProductId(@Param("productId") Long productId);

    /**
     * 根据操作类型统计库存变动
     *
     * @param request 查询条件
     * @return 操作类型统计列表
     */
    List<OperationTypeStats> getOperationTypeStats(@Param("request") InventoryHistoryQueryRequest request);

    /**
     * 获取商品的入库历史统计
     *
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 入库统计信息
     */
    ProductInboundStats getProductInboundStats(@Param("productId") Long productId,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取商品的出库历史统计
     *
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 出库统计信息
     */
    ProductOutboundStats getProductOutboundStats(@Param("productId") Long productId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 批量插入库存历史记录
     *
     * @param historyList 库存历史记录列表
     * @return 插入数量
     */
    Integer batchInsertHistory(@Param("historyList") List<InventoryHistory> historyList);

    /**
     * 操作类型统计结果
     */
    class OperationTypeStats {
        private String operationType;
        private String operationTypeName;
        private Long count;
        private BigDecimal totalQuantityChange;

        // getters and setters
        public String getOperationType() { return operationType; }
        public void setOperationType(String operationType) { this.operationType = operationType; }
        public String getOperationTypeName() { return operationTypeName; }
        public void setOperationTypeName(String operationTypeName) { this.operationTypeName = operationTypeName; }
        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
        public BigDecimal getTotalQuantityChange() { return totalQuantityChange; }
        public void setTotalQuantityChange(BigDecimal totalQuantityChange) { this.totalQuantityChange = totalQuantityChange; }
    }

    /**
     * 商品入库统计结果
     */
    class ProductInboundStats {
        private Long totalInboundCount;
        private BigDecimal totalInboundQuantity;
        private BigDecimal avgInboundQuantity;
        private LocalDateTime lastInboundTime;

        // getters and setters
        public Long getTotalInboundCount() { return totalInboundCount; }
        public void setTotalInboundCount(Long totalInboundCount) { this.totalInboundCount = totalInboundCount; }
        public BigDecimal getTotalInboundQuantity() { return totalInboundQuantity; }
        public void setTotalInboundQuantity(BigDecimal totalInboundQuantity) { this.totalInboundQuantity = totalInboundQuantity; }
        public BigDecimal getAvgInboundQuantity() { return avgInboundQuantity; }
        public void setAvgInboundQuantity(BigDecimal avgInboundQuantity) { this.avgInboundQuantity = avgInboundQuantity; }
        public LocalDateTime getLastInboundTime() { return lastInboundTime; }
        public void setLastInboundTime(LocalDateTime lastInboundTime) { this.lastInboundTime = lastInboundTime; }
    }

    /**
     * 商品出库统计结果
     */
    class ProductOutboundStats {
        private Long totalOutboundCount;
        private BigDecimal totalOutboundQuantity;
        private BigDecimal avgOutboundQuantity;
        private LocalDateTime lastOutboundTime;

        // getters and setters
        public Long getTotalOutboundCount() { return totalOutboundCount; }
        public void setTotalOutboundCount(Long totalOutboundCount) { this.totalOutboundCount = totalOutboundCount; }
        public BigDecimal getTotalOutboundQuantity() { return totalOutboundQuantity; }
        public void setTotalOutboundQuantity(BigDecimal totalOutboundQuantity) { this.totalOutboundQuantity = totalOutboundQuantity; }
        public BigDecimal getAvgOutboundQuantity() { return avgOutboundQuantity; }
        public void setAvgOutboundQuantity(BigDecimal avgOutboundQuantity) { this.avgOutboundQuantity = avgOutboundQuantity; }
        public LocalDateTime getLastOutboundTime() { return lastOutboundTime; }
        public void setLastOutboundTime(LocalDateTime lastOutboundTime) { this.lastOutboundTime = lastOutboundTime; }
    }

}